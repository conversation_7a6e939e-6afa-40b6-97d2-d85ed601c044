#!/usr/bin/env python3
"""
Run Modern system WITHOUT regime cache to test performance improvement.
This forces regime calculations every 60s instead of using hourly cache.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import logging

# Set up logging to see what's happening
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    print("="*60)
    print("MODERN SYSTEM TEST - NO REGIME CACHE")
    print("="*60)
    print("\nTHIS TEST WILL:")
    print("1. Force regime updates (should calculate, not load from cache)")
    print("2. Still trade HOURLY (not every minute)")
    print("3. Use CURRENT regime at trade time (not stale cached regime)")
    print("-"*60)
    
    # Import backtest runner
    from scripts.run_modern_backtest import main as run_modern_backtest
    
    # Monkey-patch to disable cache
    print("\n🔧 Patching ModernBacktestEngine to disable cache...")
    
    from hyperliquid_bot.modern import backtester_engine
    original_init = backtester_engine.ModernBacktestEngine.__init__
    
    def patched_init(self, config, start_date, end_date, data_dir=None, use_regime_cache=True):
        print(f"   [PATCH] Forcing use_regime_cache=False (was {use_regime_cache})")
        # Force cache to be disabled
        original_init(self, config, start_date, end_date, data_dir, use_regime_cache=False)
    
    backtester_engine.ModernBacktestEngine.__init__ = patched_init
    
    print("✅ Patch applied - regime cache disabled")
    print("\n🚀 Running Modern backtest without cache...")
    print("   Note: This will be SLOWER as it calculates regimes in real-time")
    print("   But should show BETTER PERFORMANCE due to fresh regime data")
    print("")
    
    # Add regime update counter
    update_count = [0]  # Use list to allow modification in nested function
    
    # Patch compute_regime_live to count calls
    from hyperliquid_bot.modern import continuous_detector_v2
    if hasattr(continuous_detector_v2, 'ModernContinuousGMSDetector'):
        original_compute = continuous_detector_v2.ModernContinuousGMSDetector.compute_regime_live
        
        def patched_compute(self, data):
            update_count[0] += 1
            if update_count[0] % 100 == 0:
                print(f"   [REGIME UPDATE #{update_count[0]}]")
            return original_compute(self, data)
        
        continuous_detector_v2.ModernContinuousGMSDetector.compute_regime_live = patched_compute
    
    try:
        # Run the backtest
        run_modern_backtest()
        
        print(f"\n📊 DIAGNOSTIC INFO:")
        print(f"   Total regime updates: {update_count[0]}")
        print(f"   Expected for 1 week: ~10,080 (60 updates/hour * 24 hours * 7 days)")
        print(f"   If much less, regime updates aren't happening every minute!")
        
    except Exception as e:
        print(f"\n❌ Error during backtest: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*60)
    print("ANALYSIS:")
    print("="*60)
    print("\nCompare results with cached version:")
    print("- Cached version: +41.78% annual ROI")
    print("- This version: Check 2024_backtest_results.json")
    print("\nIf performance improved, hourly cache was the problem!")

if __name__ == "__main__":
    main()