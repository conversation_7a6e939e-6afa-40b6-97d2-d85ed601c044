# Regime and Trade Direction Investigation Report

Generated: 2025-07-15 02:01:37

## Data Sources

- **Signals File:** backtest_signals_20250715_015329.parquet
- **Trades File:** backtest_trades_20250715_015329.json
- **Total Signal Samples:** 8708
- **Total Trades:** 183

## 1. Regime Diversity Analysis

## 2. Trade Direction Analysis

### Trade Direction Distribution

- **Long trades:** 183 (100.0%)

### Long/Short Bias Analysis

- **Long percentage:** 100.0%
- **Short percentage:** 0.0%
- **Is long-only:** True
- **Significant bias:** True

🚨 **CRITICAL FINDING:** Strategy appears to be LONG-ONLY!

This suggests a potential bug preventing short position generation.

## Conclusions and Recommendations

### ❌ Regime Diversity Issue

- **No BEAR regimes detected** - this indicates a problem with regime detection
- The system may not be properly identifying bearish market conditions
- Check GMS threshold calibration and state mapping configuration

### 🚨 CRITICAL: Long-Only Bug

**IMMEDIATE ACTION REQUIRED:**
1. Check TF-v3 strategy logic for short signal generation
2. Verify EMA crossover detection (fast crossing below slow)
3. Ensure BEAR regime mapping is working correctly
4. Review signal filtering and regime gating logic

**Potential causes:**
- Bug in EMA crossover logic preventing short signals
- BEAR regime not being detected or mapped correctly
- Signal filtering rejecting all short position signals
- Position sizing logic preventing short trades

