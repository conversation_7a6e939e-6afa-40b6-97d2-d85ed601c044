#!/usr/bin/env python3
"""
Test the enhanced modern system for full year 2024.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import logging
from datetime import datetime

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtest_engine import RobustBacktestEngine

# Set up logging - less verbose
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """Run full year test."""
    print("\n" + "="*80)
    print("ENHANCED MODERN SYSTEM - FULL 2024 BACKTEST")
    print("="*80)
    
    # Load config
    config_path = Path(__file__).parent / "configs/overrides/modern_system_v2_adjusted_thresholds.yaml"
    config = load_config(str(config_path))
    
    # Show configuration
    print("\nConfiguration Summary:")
    print(f"  Detector: enhanced (relaxed Weak trend trading)")
    print(f"  Quality threshold: 0.45")
    print(f"  Momentum thresholds: {config.regime.gms_mom_weak_thresh} / {config.regime.gms_mom_strong_thresh}")
    print(f"  Spread thresholds: {config.regime.gms_spread_mean_low_thresh} / {config.regime.gms_spread_std_high_thresh}")
    print(f"  Allowed regimes: Strong & Weak trends (Bull/Bear)")
    
    # Full year test
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 12, 31, 23, 59, 59)
    
    print(f"\nTest Period: {start_date.date()} to {end_date.date()} (full year)")
    print("-" * 80)
    
    try:
        # Create engine
        engine = RobustBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date,
            use_regime_cache=False,
            strict=False
        )
        
        print("\n✅ Engine initialized successfully")
        print("Running full year backtest... (this may take a few minutes)")
        
        # Run the backtest
        results = engine.run_backtest()
        
        print("\n✅ Backtest completed")
        
        # Analyze results
        print("\n" + "="*80)
        print("FULL YEAR 2024 RESULTS:")
        print("="*80)
        
        print(f"\nTrade Statistics:")
        print(f"  Total Trades: {results.get('total_trades', 0)}")
        print(f"  Winning Trades: {results.get('winning_trades', 0)}")
        print(f"  Losing Trades: {results.get('losing_trades', 0)}")
        print(f"  Win Rate: {results.get('win_rate', 0):.1%}")
        print(f"  Average Trade: {results.get('average_return', 0):.2%}")
        
        print(f"\nPerformance:")
        print(f"  Total Return: {results.get('total_return', 0):.2%}")
        print(f"  Sharpe Ratio: {results.get('sharpe_ratio', 0):.2f}")
        print(f"  Max Drawdown: {results.get('max_drawdown', 0):.2%}")
        
        # Monthly breakdown
        if results.get('total_trades', 0) > 0:
            print(f"\nMonthly Average:")
            print(f"  Trades/month: {results.get('total_trades', 0) / 12:.1f}")
            print(f"  Return/month: {results.get('total_return', 0) / 12:.2%}")
        
        # Comparison
        print("\n" + "="*80)
        print("PERFORMANCE COMPARISON:")
        print("="*80)
        
        print(f"\nEnhanced Modern System (2024):")
        print(f"  Total Trades: {results.get('total_trades', 0)}")
        print(f"  Total Return: {results.get('total_return', 0):.2%}")
        print(f"  Sharpe Ratio: {results.get('sharpe_ratio', 0):.2f}")
        
        print(f"\nLegacy System Target (2024):")
        print(f"  Total Trades: 180")
        print(f"  Total Return: +215%")
        
        # Performance assessment
        trade_ratio = results.get('total_trades', 0) / 180 * 100
        return_ratio = results.get('total_return', 0) / 215 * 100
        
        print(f"\nPerformance vs Target:")
        print(f"  Trade Count: {trade_ratio:.1f}% of target")
        print(f"  Returns: {return_ratio:.1f}% of target")
        
    except Exception as e:
        print(f"\n❌ ERROR: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()