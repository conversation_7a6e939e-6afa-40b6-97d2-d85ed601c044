# ==============================================================================
# Global Configuration
# ==============================================================================
is_backtest: true                 # Global flag indicating if running in backtest mode

# ==============================================================================
# Data & Cache Configuration
# ==============================================================================
data_paths:
  l2_data_root: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw2" # Contains YYYYMMDD_raw2.parquet
  raw_l2_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/l2_raw" # Raw L2 data directory
  feature_1s_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s" # 1-second feature data
  ohlcv_base_path: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled_l2" # Base path for OHLC bars
  log_dir: "/Users/<USER>/Desktop/trading_bot_/logs"
  require_ohlcv_volume: false # Set to false for now

# ==============================================================================
# Data Providers Configuration
# ==============================================================================
data_providers:
  fear_greed:
    enabled: false # Re-enabled after fixing timezone issues
    # cache_path: null # Optional: Path to cache file (future use)

# ==============================================================================
# Cache Configuration
# ==============================================================================
cache:
  l2_cache_max_size: 24 # Max daily L2 files to keep in memory (if applicable)

# ==============================================================================
# Timeframe Setting
# ==============================================================================
timeframe: "1h" # Or "4h" - Set the desired timeframe for the run

# ==============================================================================
# Backtest & Simulation Configuration
# ==============================================================================
backtest:
  period_preset: '2024'            # Options: 'full', 'YYYYQX', 'YYYY', 'custom'
  custom_start_date: "2025-03-02"  # 22-day test period for R-113
  custom_end_date: "2025-03-22"    # 22-day test period for R-113

simulation:
  latency_seconds: 0.5             # Simulated order latency
  max_impact_levels: 5             # L2 levels assumed fillable without extra penalty
  force_taker_execution: True      # Set to False to enable maker attempt
  attempt_maker_orders: False      # Set to True to attempt maker fills
  maker_placement_type: 'best_passive'
  maker_time_buckets_seconds: [5, 30, 120]
  maker_fill_probabilities: [0.07, 0.13, 0.10]

# ==============================================================================
# Portfolio Configuration
# ==============================================================================
portfolio:
  # --- Risk Controls ---
  initial_balance: 10000       # Initial balance for backtests (in USD)
  risk_per_trade: 0.02         # Risk % of balance per trade (2%)
  max_leverage: 10.0           # Global maximum leverage cap
  asset_max_leverage: 50.0     # Asset-specific max leverage
  margin_mode: cross           # Margin mode: 'cross' or 'isolated'
  max_hold_time_hours: 24      # Maximum time to hold a position
  
  # --- Optional Guards ---
  max_notional: 0              # 0 = no explicit ceiling
  min_trade_size: 0.001        # Minimum trade size in BTC
  leverage_guard_pct: 0        # 0 = disabled
  margin_buffer_pct: 0.05      # Margin buffer percentage (5%)

# ==============================================================================
# Costs Configuration
# ==============================================================================
costs:
  funding_rate: 0.0001        # Default funding rate (per period)
  funding_hours: 8            # Funding interval
  taker_fee: 0.000315         # Taker fee rate (0.0315%)
  maker_fee: 0.00009          # Maker fee rate (0.009%)
  l2_penalty_factor: 1.005    # Slippage penalty factor (0.5%)

# ==============================================================================
# Strategy Selection & Configuration
# ==============================================================================
strategies:
  # --- Strategy Activation ---
  use_tf_v2: True            # Legacy trend following strategy (currently active)
  use_mean_reversion: False  # Mean reversion strategy (inactive per PRD)
  use_mean_variance: False   # Mean variance strategy (inactive per PRD) 
  use_obi_scalper: False     # Order Book Imbalance scalper strategy (inactive)
  use_tf_v3: False           # Modern trend following strategy (v3 - inactive)
  
  # --- Trend Following (TF-v2) Configuration ---
  tf_warmup_bars: "auto"     # Warm-up period: "auto" (calculates based on indicators) or integer
  tf_use_obi_filter: False   # Whether to use Order Book Imbalance filter for TF signals
  tf_use_funding_filter: False # Whether to use funding rate filter for TF signals
  
  # --- OBI Scalper Configuration ---
  obi_scalper_active_in_all_regimes: False # Whether OBI scalper works in all market regimes
  gms_activate_obi_scalper_in_chop: True   # Activate OBI scalper during CHOP regime
  scalper_risk_pct: 0.5      # Risk percentage for scalper strategy (0.5% of balance)
  scalper_stop_ticks: 5       # Stop loss in ticks for scalper
  
  # --- OBI Scalper Strategy Detailed Configuration ---
  OBIScalperStrategy:
    enabled: true             # Master enable flag for OBI scalper
    defaults:
      vol_veto_threshold: 0.002      # Volatility threshold above which scalper is disabled (0.2%)
      spread_veto_threshold: 0.0001  # Spread threshold above which scalper is disabled
      obi_l1_3_trigger: 0.50        # OBI level 1-3 trigger threshold (50% imbalance)
      tp_ticks: 7                    # Take profit in ticks
      sl_ticks: 5                    # Stop loss in ticks  
      timeout_seconds: 30            # Maximum time to hold scalper position (seconds)
      allowed_gms_states: ["CHOP", "BULL", "BEAR"] # GMS states where scalper is active
      zero_sign_eps: 0.001           # Epsilon for zero sign detection
      tick_size: 0.01                # Minimum price movement (BTC: $0.01)

# ==============================================================================
# Market Regime Detection Configuration
# CRITICAL: detector_type must match gms.detector_type for proper mode selection
# ==============================================================================
regime:
  # --- General Settings ---
  use_filter: True           # Enable regime-based strategy filtering
  detector_type: 'granular_microstructure'  # MUST match gms.detector_type below!
                            # Options: 'rule_based', 'continuous_gms', 'granular_microstructure'
  use_strict_strategy_filtering: True # Only allow strategies in compatible regimes
  
  # --- Rule-Based Detector Settings (only if detector_type = 'rule_based') ---
  use_enhanced_detection: True    # Use enhanced rule-based detection logic
  use_chop_filter: True          # Filter out chop/ranging periods
  use_chop_index_for_chop: False # Use Chop Index (vs BBW) for chop detection
  use_bbw_for_chop_detection: False # Use Bollinger Band Width for chop detection
  pause_in_chop: False           # Whether to pause all trading during chop
  
  # --- GMS Configuration Flags ---
  gms_use_adx_confirmation: False    # Use ADX to confirm trend strength
  gms_use_funding_confirmation: False # Use funding rate for regime confirmation
  
  # --- State Mapping Configuration ---
  gms_use_three_state_mapping: True      # Map 8 GMS states to 3 states (BULL/BEAR/CHOP)
  gms_state_mapping_file: 'configs/gms_state_mapping.yaml' # State mapping file
  map_weak_bear_to_bear: false           # Whether Weak_Bear_Trend maps to BEAR (vs CHOP)
  
  # --- Dynamic Risk Adjustment (currently disabled) ---
  dynamic_risk_adjustment: false         # Enable regime-based risk scaling
  chop_risk_factor: 0.5                 # Risk multiplier during chop (50% of normal)
  chop_leverage_factor: 0.5             # Leverage multiplier during chop
  strong_trend_risk_factor: 0.8         # Risk multiplier during strong trends
  strong_trend_leverage_factor: 0.7     # Leverage multiplier during strong trends
  weak_trend_risk_scale: 0.8            # Risk multiplier during weak trends
  
  # --- Fallback Thresholds (used if detector-specific values not found) ---
  gms_vol_thresh_mode: 'fixed'           # Volatility threshold mode: 'fixed' or 'adaptive'
  gms_vol_high_thresh: 0.0092           # High volatility threshold (0.92% in decimal ATR)
  gms_vol_low_thresh: 0.0055            # Low volatility threshold (0.55% in decimal ATR)
                                        # IMPORTANT: Scaled down 100x to match corrected ATR units
  gms_vol_high_percentile: 75           # High volatility percentile (for adaptive mode)
  gms_vol_low_percentile: 25            # Low volatility percentile (for adaptive mode)
  gms_mom_strong_thresh: 100.0          # Strong momentum threshold (MA slope)
  gms_mom_weak_thresh: 50.0             # Weak momentum threshold (MA slope)
  gms_spread_std_high_thresh: 0.000050  # High spread volatility threshold
  gms_spread_mean_low_thresh: 0.000045  # Low (tight) spread threshold
  
  # --- Detector-Specific Configurations ---
  granular_microstructure:  # Legacy hourly mode configuration
    # Volatility Thresholds (CRITICAL: scaled down 100x for corrected decimal ATR)
    gms_vol_high_thresh: 0.0092         # High volatility: 0.92% (was 92% before ATR fix)
    gms_vol_low_thresh: 0.0055          # Low volatility: 0.55% (was 55% before ATR fix)
    # Momentum Thresholds (MA slope-based)
    gms_mom_strong_thresh: 100.0        # Strong momentum threshold
    gms_mom_weak_thresh: 50.0           # Weak momentum threshold
    # Spread Thresholds
    gms_spread_std_high_thresh: 0.000050 # High spread volatility (5 basis points)
    gms_spread_mean_low_thresh: 0.000045 # Tight spread threshold (4.5 basis points)
    # Operational Settings
    cadence_sec: 3600                   # Update frequency: 3600s (1 hour)
    output_states: 8                    # Number of output states (before collapse)
    state_collapse_map_file: 'configs/gms_state_mapping.yaml' # State mapping file
    use_four_state_mapping: false       # Use 3-state (vs 4-state) mapping
    risk_suppressed_notional_frac: 0.25 # Risk suppression: 25% of ATR-adjusted notional
    risk_suppressed_pnl_atr_mult: 1.5   # Risk suppression: 1.5x ATR PnL threshold
    skip_l2_raw_processing_if_1h_features_exist: true # Performance optimization
  
  continuous_gms:  # Modern 60-second mode configuration
    # Volatility Thresholds - Phase 1 Conservative Tuning
    gms_vol_high_thresh: 0.03           # High volatility: 3% (more aggressive than legacy)
    gms_vol_low_thresh: 0.01            # Low volatility: 1% (more aggressive than legacy)
    # Momentum Thresholds (more sensitive for 60s updates)
    gms_mom_strong_thresh: 2.5          # Strong momentum (lower than legacy for 60s)
    gms_mom_weak_thresh: 0.5            # Weak momentum (lower than legacy for 60s)
    # Spread Thresholds (wider for continuous mode)
    gms_spread_std_high_thresh: 0.0005  # High spread volatility (50 basis points)
    gms_spread_mean_low_thresh: 0.0001  # Tight spread threshold (1 basis point)
    # Operational Settings
    cadence_sec: 60                     # Update frequency: 60s (1 minute)
    output_states: 8                    # Number of output states (before collapse)
    state_collapse_map_file: 'configs/gms_state_mapping.yaml' # State mapping file
    use_four_state_mapping: false       # Use 3-state (vs 4-state) mapping
    risk_suppressed_notional_frac: 0.25 # Risk suppression: 25% of ATR-adjusted notional
    risk_suppressed_pnl_atr_mult: 1.5   # Risk suppression: 1.5x ATR PnL threshold
  
  # --- Advanced GMS Filters (null = disabled) ---
  # Funding Rate Filters
  gms_funding_extreme_positive_thresh: 0.001    # Extreme positive funding threshold (0.1%)
  gms_funding_extreme_negative_thresh: -0.001   # Extreme negative funding threshold (-0.1%)
  
  # Weak Trend Filters
  gms_filter_allow_weak_bull_trend: True        # Allow weak bull trend trading
  gms_filter_allow_weak_bear_trend: True       # Allow weak bear trend trading
  gms_weak_bull_risk_scale: 0.25               # Risk scaling for weak bull trends (25%)
  gms_weak_bear_risk_scale: 0.25               # Risk scaling for weak bear trends (25%)
  
  # Advanced Microstructure Filters (experimental - mostly disabled)
  gms_obi_zscore_threshold: null               # OBI Z-score filter threshold
  gms_spread_percentile_gate: null             # Spread percentile gating
  gms_depth_slope_thin_limit: null             # Depth slope thin liquidity limit
  gms_depth_skew_thresh: null                  # Depth skew threshold
  gms_spread_trend_lookback: null              # Spread trend analysis lookback
  gms_adaptive_obi_base: null                  # Adaptive OBI baseline
  gms_confirmation_bars: null                  # Signal confirmation bars
  
  # Spread Analysis Settings
  gms_tight_spread_fallback_percentile: null   # Fallback percentile for tight spreads
  gms_tight_spread_percentile_window: 24       # Window for tight spread percentile (hours)
  gms_spread_mean_thresh_mode: 'fixed'         # Spread mean threshold mode: 'fixed'/'adaptive'
  gms_spread_std_thresh_mode: 'fixed'          # Spread std threshold mode: 'fixed'/'adaptive'
  gms_spread_mean_low_percentile: 0.25         # Low spread percentile (25th)
  gms_spread_std_high_percentile: 0.75         # High spread volatility percentile (75th)
  gms_spread_percentile_window: null           # Spread percentile calculation window

# ==============================================================================
# GMS Detector Configuration (UNIFIED)
# CRITICAL: detector_type MUST match regime.detector_type for proper mode selection
# ==============================================================================
gms:
  # --- Mode Selection (CRITICAL) ---
  detector_type: 'granular_microstructure'  # MUST match regime.detector_type above!
                                            # This ensures legacy mode with scaled thresholds
  
  # --- Basic Operational Settings ---
  cadence_sec: 60                      # Base update frequency (overridden by mode)
  output_states: 8                     # Number of regime states before collapse
  state_collapse_map_file: 'configs/gms_state_mapping.yaml' # State mapping configuration
  use_four_state_mapping: false        # Use 3-state (BULL/BEAR/CHOP) vs 4-state mapping
  
  # --- Risk Suppression (continuous mode only) ---
  risk_suppressed_notional_frac: 0.25  # Suppress risk when position > 25% of ATR-notional
  risk_suppressed_pnl_atr_mult: 1.5    # Suppress risk when PnL > 1.5x ATR threshold
  
  # --- Adaptive Thresholds (continuous mode only) ---
  # WARNING: Setting to true for legacy mode can cause severe overfitting!
  # Legacy mode should ALWAYS use fixed thresholds to avoid look-ahead bias
  auto_thresholds: true                 # Enable adaptive thresholds (ONLY for continuous mode)
                                       # Legacy mode ignores this and uses fixed thresholds
  percentile_window_sec: 86400          # 24-hour window for percentile calculation
  vol_low_pct: 0.001                   # Low volatility percentile (0.1%)
  vol_high_pct: 0.50                   # High volatility percentile (50%)
  mom_low_pct: 0.001                   # Low momentum percentile (0.1%)
  mom_high_pct: 0.50                   # High momentum percentile (50%)
  min_history_rows: 100                # Minimum data points before adaptive kicks in
  priming_hours: 24                    # Hours of data needed for priming
  
  # --- Market Bias System (experimental - currently disabled) ---
  market_bias:
    enabled: false                      # Master enable for market bias adjustments
    use_three_state_mapping: true       # Use 3-state vs 8-state for bias calculation
    # Leverage Factors (affect margin requirements)
    bull_leverage_factor: 1.0           # Leverage multiplier during bull markets (100%)
    bear_leverage_factor: 1.0           # Leverage multiplier during bear markets (100%)
    chop_leverage_factor: 1.0           # Leverage multiplier during chop markets (100%)
    # Risk Factors (affect position size)
    bull_risk_factor: 1.2               # Risk multiplier during bull markets (120%)
    bear_risk_factor: 0.8               # Risk multiplier during bear markets (80%)
    chop_risk_factor: 0.5               # Risk multiplier during chop markets (50%)
    # Direction Bias (requires direction field in regime output)
    bull_long_bias: 1.0                 # Long bias during bull markets
    bull_short_bias: 1.0                # Short bias during bull markets
    bear_long_bias: 1.0                 # Long bias during bear markets
    bear_short_bias: 1.0                # Short bias during bear markets

# ==============================================================================
# Microstructure Feature Configuration
# Controls order book and spread-based features
# ==============================================================================
microstructure:
  # --- Feature Calculation Settings ---
  depth_levels: 5                      # Number of order book levels to analyze (1-5)
  obi_levels: null                     # OBI levels override (null = use depth_levels)
  obi_smoothing_window: 8              # Rolling window for OBI smoothing (bars)
  obi_smoothing_type: 'sma'            # OBI smoothing method: 'sma', 'ema', 'ewm'
  obi_zscore_window: null              # Z-score normalization window (null = disabled)
  depth_levels_for_calc: null          # Override depth levels for calculations
  spread_rolling_window: 24            # Rolling window for spread statistics (hours)
  spread_metric_to_roll: 'relative'    # Spread metric to roll: 'relative', 'absolute'
  allow_nan_micro_depth: true          # Continue processing with missing depth data
  
  # --- GMS OBI Confirmation Thresholds ---
  gms_obi_strong_confirm_thresh: 0.20  # Strong OBI confirmation threshold (20% imbalance)
  gms_obi_weak_confirm_thresh: 0.11    # Weak OBI confirmation threshold (11% imbalance)
  
  # --- TF Strategy Filter Thresholds ---
  tf_filter_obi_threshold_long: 0.1    # OBI threshold for long entries (10% buy bias)
  tf_filter_obi_threshold_short: -0.1  # OBI threshold for short entries (10% sell bias)
  tf_filter_funding_threshold_long: -0.0005  # Funding rate threshold for longs (-0.05%)
  tf_filter_funding_threshold_short: 0.0005  # Funding rate threshold for shorts (+0.05%)

# ==============================================================================
# Indicator Parameters
# Technical indicator settings for signal generation
# ==============================================================================
indicators:
  require_volume_for_signals: false    # Whether volume data is required for signals
  
  # --- General / Rule-Based Detector Indicators ---
  adx_period: 14                       # ADX calculation period (bars)
  adx_threshold: 30.0                  # ADX threshold for trend detection
  high_volatility_adx_threshold: 30.0  # ADX threshold for high volatility trends
  low_forecast_threshold: 1.0          # Minimum forecast magnitude for trend
  volatility_thresh_percent: 0.005     # Volatility threshold (0.5%)
  bbw_thresh: 0.03                     # Bollinger Band Width threshold (3%)
  chop_index_period: 14                # Chop Index calculation period
  chop_index_high_thresh: 61.8         # Chop Index high threshold (choppy market)
  chop_index_low_thresh: 38.2          # Chop Index low threshold (trending market)
  min_leverage: 1.0                    # Minimum allowed leverage
  
  # --- Trend Following (TF-v2) Indicators ---
  tf_ewma_fast: 20                     # Fast EWMA period for trend detection
  tf_ewma_medium: 32                   # Medium EWMA period (optional)
  use_tf_medium_ewma: False            # Whether to use medium EWMA in calculations
  tf_ewma_slow: 50                     # Slow EWMA period for trend detection
  tf_atr_period: 20                    # ATR period for stop/target calculation
  tf_atr_stop_mult: 2.0                # ATR multiplier for stop loss (2x ATR)
  tf_atr_target_mult: 4.0              # ATR multiplier for take profit (4x ATR)
  tf_leverage_base: 5.0                # Base leverage for TF strategy
  tf_max_entry_volatility_pct: 0.01    # Max volatility for TF entries (1%)
  
  # --- Mean Reversion Strategy (Inactive) ---
  mr_ema_period: 20                    # EMA period for mean reversion baseline
  mr_keltner_mult: 2.0                 # Keltner Channel multiplier
  mr_rsi_period: 14                    # RSI calculation period
  mr_rsi_oversold: 30.0                # RSI oversold threshold
  mr_rsi_overbought: 70.0              # RSI overbought threshold
  mr_atr_period: 20                    # ATR period for MR stops
  mr_atr_stop_mult: 1.5                # ATR multiplier for MR stop loss
  mr_leverage_base: 5.0                # Base leverage for MR strategy
  mr_obi_long_threshold: -0.2          # OBI threshold for MR long entries
  mr_obi_short_threshold: 0.2          # OBI threshold for MR short entries
  mr_require_obi_filter: True          # Require OBI confirmation for MR trades
  
  # --- Mean Variance Strategy (Inactive) ---
  mv_edge_ema_period: 10               # Edge calculation EMA period
  mv_volatility_period: 20             # Volatility calculation period
  mv_min_edge_threshold: 0.0005        # Minimum edge for MV entry (0.05%)
  mv_min_exit_edge_threshold: 0.0      # Minimum edge for MV exit
  mv_min_kelly: 0.05                   # Minimum Kelly fraction (5%)
  mv_max_kelly: 0.25                   # Maximum Kelly fraction (25%)
  mv_leverage_base: 3.0                # Base leverage for MV strategy
  mv_atr_stop_mult: 2.5                # ATR multiplier for MV stop loss
  mv_atr_target_mult: 3.0              # ATR multiplier for MV take profit
  mv_atr_period: 20                    # ATR period for MV calculations
  mv_obi_long_threshold: -0.3          # OBI threshold for MV long entries
  mv_obi_short_threshold: 0.3          # OBI threshold for MV short entries
  mv_require_obi_filter: True          # Require OBI confirmation for MV trades
  
  # --- GMS Detector Specific Indicators ---
  gms_roc_period: 5                    # Rate of Change period for momentum
  gms_ma_slope_period: 30              # Moving average period for slope calculation
  gms_atr_percent_period: 14           # ATR period for volatility percentage

# ==============================================================================
# Analysis Configuration
# ==============================================================================
analysis:
  analyze_trades_after_backtest: True          # Generate trade analysis after backtest

# ==============================================================================
# ETL Pipeline Configuration
# ==============================================================================
etl:
  l20_to_1s:                                   # L2 order book to 1-second features
    chunk_sec: 3600                            # Process in 1-hour chunks
    rollup_method: "median"                     # Aggregation method: median/mean/last

# ==============================================================================
# Scheduler Configuration
# ==============================================================================
scheduler:
  etl_enabled: false                           # Enable scheduled ETL processing
  etl_poll_sec: 300                            # ETL polling interval (5 minutes)

# ==============================================================================
# TF-v3 Strategy Configuration (Modern Trend Following)
# ==============================================================================
tf_v3:
  enabled: false                               # TF-v3 strategy enabled (currently inactive)
  # Technical Indicators
  ema_fast: 20                                 # Fast EMA period for trend detection
  ema_slow: 50                                 # Slow EMA period for trend detection
  atr_period: 14                               # ATR period for volatility measurement
  atr_trail_k: 3.0                             # ATR multiplier for trailing stop (3x)
  # Risk Management
  max_trade_life_h: 24                         # Maximum trade duration (24 hours)
  risk_frac: 0.25                              # Risk fraction per trade (25% of balance)
  max_notional: 25000                          # Maximum notional position size ($25K)
  # GMS Integration
  gms_max_age_sec: 300                         # Maximum age of GMS signal (5 minutes)
  atr_fallback_pct: 0.01                       # Fallback ATR percentage (1%)
  trail_eps: 0.01                              # Trailing stop epsilon

# ==============================================================================
# Visualization Configuration
# ==============================================================================
visualization:
  require_volume: false                        # Require volume data for charts
  panels:                                      # Chart panel configuration
    show_ma_slope: true                        # Show MA slope indicator panel
    show_obi: true                             # Show Order Book Imbalance panel
    show_fear_greed: false                     # Show Fear & Greed Index panel
  appearance:                                  # Chart appearance settings
    price_panel_ratio: 4                       # Price panel height ratio
    indicator_panel_ratio: 1                   # Indicator panel height ratio
    max_points: 5000                           # Maximum data points to display