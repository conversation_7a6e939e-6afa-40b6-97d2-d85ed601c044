# Conservative GMS State Mapping for Modern System
# Maps 8 GMS states to 3 strategy states (BULL/BEAR/CHOP)
# CRITICAL: This mapping is calibrated for conservative Bear regime detection

state_map:
  Strong_Bull_Trend: 'BULL'      # Strong bullish momentum with confirmation
  Weak_Bull_Trend: 'BULL'        # Weaker bullish momentum - still tradeable
  High_Vol_Range: 'CHOP'         # High volatility ranging - NO TRADING
  Low_Vol_Range: 'CHOP'          # Low volatility ranging - NO TRADING
  Uncertain: 'CHOP'              # Conflicting signals - NO TRADING
  Unknown: 'CHOP'                # Missing data or error state - NO TRADING
  Weak_Bear_Trend: 'BEAR'        # FIXED: Weak bearish now maps to BEAR (was CHOP)
  Strong_Bear_Trend: 'BEAR'      # Strong bearish momentum with confirmation
  TIGHT_SPREAD: 'CHOP'           # Tight spread conditions - NO TRADING

# Mapping rationale:
# - Strong trends (Bull/Bear) are clearly tradeable
# - Weak_Bear_Trend now maps to BEAR instead of CHOP to enable short trades
# - This should increase Bear regime detection and enable proper short opportunities
# - All ranging/uncertain states remain CHOP for safety