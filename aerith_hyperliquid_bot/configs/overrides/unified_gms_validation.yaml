# Unified GMS Detector Validation Profile
# Tests the new unified detector with adaptive thresholds for continuous mode
# Generated: May 31, 2025
#
# KEY ADJUSTMENTS FOR MORE TRADES:
# 1. EMA periods: Fast 10->5, Slow 20->12 (reduce NaN issues, better alignment)
# 2. Adaptive thresholds: More permissive percentiles (20/65 for vol, 25/60 for mom)
# 3. Initial thresholds: Lowered to trigger states more easily
# 4. Risk suppression: Raised thresholds (40% notional, 2.5x ATR)
# 5. Priming period: Reduced to 6 hours for faster startup
# 6. ATR trail: Widened to 3.0 to avoid premature stops

# Backtest period - 20 days with available continuous_gms features
backtest:
  period_preset: 'custom'
  custom_start_date: "2025-03-02"
  custom_end_date: "2025-03-22"

# Enable continuous GMS detector with unified implementation
regime:
  # General Settings
  use_filter: True
  detector_type: 'continuous_gms'  # Modern continuous mode with adaptive thresholds
  use_strict_strategy_filtering: True
  
  # GMS Configuration
  gms_use_adx_confirmation: False
  gms_use_funding_confirmation: False
  
  # State Mapping - 3-state system (BULL/BEAR/CHOP)
  gms_use_three_state_mapping: True
  gms_state_mapping_file: 'configs/gms_state_mapping.yaml'
  map_weak_bear_to_bear: false
  
  # Dynamic Risk (disabled for initial validation)
  dynamic_risk_adjustment: false
  
  # Continuous GMS specific configuration - RELAXED thresholds
  continuous_gms:
    # Initial thresholds - more permissive (adaptive will override)
    gms_vol_high_thresh: 0.012          # 1.2% high volatility (lower = more high vol states)
    gms_vol_low_thresh: 0.003           # 0.3% low volatility (lower = more low vol states)
    gms_mom_strong_thresh: 0.5          # Lower strong momentum threshold
    gms_mom_weak_thresh: 0.10           # Lower weak momentum threshold
    gms_spread_std_high_thresh: 0.0005  # 50 basis points (more permissive)
    gms_spread_mean_low_thresh: 0.00002 # 0.2 basis points (tighter spreads)
    # Operational settings
    cadence_sec: 60                     # 60-second updates
    output_states: 8
    state_collapse_map_file: 'configs/gms_state_mapping.yaml'
    use_four_state_mapping: false
    risk_suppressed_notional_frac: 0.40 # Higher threshold before suppression
    risk_suppressed_pnl_atr_mult: 2.5   # Higher threshold for risk suppression

# Strategy configuration - TF-v3 only
strategies:
  use_tf_v2: False
  use_tf_v3: True
  use_mean_reversion: False
  use_mean_variance: False

# GMS configuration with adaptive thresholds ENABLED
gms:
  # CRITICAL: Must match regime.detector_type
  detector_type: 'continuous_gms'
  
  # Basic settings
  cadence_sec: 60
  output_states: 8
  state_collapse_map_file: 'configs/gms_state_mapping.yaml'
  use_four_state_mapping: false
  
  # Risk suppression
  risk_suppressed_notional_frac: 0.30
  risk_suppressed_pnl_atr_mult: 2.0
  
  # ADAPTIVE THRESHOLDS - RELAXED for more trades
  auto_thresholds: true              # Enable adaptive thresholds
  percentile_window_sec: 86400       # 24-hour window (faster adaptation)
  vol_low_pct: 0.20                  # 20th percentile (more permissive)
  vol_high_pct: 0.65                 # 65th percentile (triggers high vol easier)
  mom_low_pct: 0.25                  # 25th percentile (more permissive)
  mom_high_pct: 0.60                 # 60th percentile (triggers strong momentum easier)
  min_history_rows: 30               # Even lower for very fast adaptation
  priming_hours: 6                   # 6 hours priming (faster startup)
  
  # Market bias (disabled for validation)
  market_bias:
    enabled: false

# Microstructure configuration
microstructure:
  depth_levels: 5
  obi_smoothing_window: 8
  obi_smoothing_type: 'sma'
  spread_rolling_window: 24
  spread_metric_to_roll: 'relative'
  allow_nan_micro_depth: true
  
  # GMS OBI thresholds
  gms_obi_strong_confirm_thresh: 0.15  # 15% imbalance for strong confirmation
  gms_obi_weak_confirm_thresh: 0.08    # 8% imbalance for weak confirmation
  
  # TF filter thresholds
  tf_filter_obi_threshold_long: 0.05   # 5% buy bias for longs
  tf_filter_obi_threshold_short: -0.05 # 5% sell bias for shorts
  tf_filter_funding_threshold_long: -0.0003
  tf_filter_funding_threshold_short: 0.0003

# Indicator parameters
indicators:
  require_volume_for_signals: false
  
  # GMS specific indicators
  gms_roc_period: 5
  gms_ma_slope_period: 20            # Shorter for more responsive signals
  gms_atr_percent_period: 14
  
  # TF indicators (not used but kept for compatibility)
  tf_ewma_fast: 20
  tf_ewma_slow: 50
  tf_atr_period: 20
  tf_atr_stop_mult: 2.0
  tf_atr_target_mult: 4.0
  tf_leverage_base: 5.0
  tf_max_entry_volatility_pct: 0.015  # 1.5% max volatility

# TF-v3 configuration optimized for more trades
tf_v3:
  enabled: true
  # Technical indicators - ADJUSTED for better alignment
  ema_fast: 5               # Much faster for 60s mode to reduce NaN issues
  ema_slow: 12              # Shorter slow EMA for better responsiveness
  atr_period: 14
  atr_trail_k: 3.0          # Slightly wider stop to avoid premature exits
  # Risk management - more aggressive for trade generation
  max_trade_life_h: 96      # 4 days max hold
  risk_frac: 0.15           # 15% risk per trade
  max_notional: 25000       # $25k max position
  # GMS integration
  gms_max_age_sec: 1200     # 20 minutes staleness (more permissive)
  atr_fallback_pct: 0.015   # 1.5% fallback ATR
  trail_eps: 0.01
  disable_ema_alignment: false

# Analysis and reporting
analysis:
  analyze_trades_after_backtest: True

# ETL and scheduler
etl:
  l20_to_1s:
    chunk_sec: 3600
    rollup_method: "median"

scheduler:
  etl_enabled: true
  etl_poll_sec: 300

# Visualization
visualization:
  require_volume: false
  panels:
    show_ma_slope: true
    show_obi: true
    show_fear_greed: false
  appearance:
    price_panel_ratio: 4
    indicator_panel_ratio: 1
    max_points: 5000