# Conservative Modern System Calibration
# Goal: Match legacy conservatism (160-190 trades) while maintaining modern architecture
# Generated: 2025-07-14

# Enable TF-v3 modern strategy
strategies:
  use_tf_v2: False      # Disable legacy strategy  
  use_tf_v3: True       # Enable modern strategy
  use_mean_reversion: False
  use_mean_variance: False
  use_obi_scalper: False

# Switch to continuous GMS for modern pipeline
regime:
  detector_type: 'continuous_gms'  # Use modern continuous mode
  use_filter: True
  use_strict_strategy_filtering: True
  
  # Conservative continuous GMS settings
  continuous_gms:
    # CRITICAL: Much higher momentum thresholds to match legacy conservatism
    # Legacy uses 100.0/50.0, but continuous mode needs scaling adjustment
    # Calibrated to reduce trade frequency from 270 to 160-190
    gms_mom_strong_thresh: 25.0      # Increased from 2.5 (10x increase)
    gms_mom_weak_thresh: 12.5        # Increased from 0.5 (25x increase)
    
    # Keep volatility thresholds reasonable for 60s updates
    gms_vol_high_thresh: 0.04        # Slightly higher than 0.03
    gms_vol_low_thresh: 0.015        # Slightly higher than 0.01
    
    # Maintain 60s updates for tactical responsiveness
    cadence_sec: 60
    output_states: 8
    state_collapse_map_file: 'configs/gms_state_mapping_conservative.yaml'
    use_four_state_mapping: false
    risk_suppressed_notional_frac: 0.25
    risk_suppressed_pnl_atr_mult: 1.5

# Ensure GMS detector matches regime detector
gms:
  detector_type: 'continuous_gms'  # Must match regime.detector_type
  cadence_sec: 60
  output_states: 8
  state_collapse_map_file: 'configs/gms_state_mapping_conservative.yaml'
  use_four_state_mapping: false
  auto_thresholds: false           # Use fixed thresholds to avoid overfitting
  
# TF-v3 strategy configuration for conservative trading
tf_v3:
  enabled: true
  # Technical indicators
  ema_fast: 20
  ema_slow: 50
  atr_period: 14
  atr_trail_k: 3.0
  
  # Conservative risk management
  max_trade_life_h: 24
  risk_frac: 0.02                  # Conservative 2% risk per trade (vs 0.25)
  max_notional: 20000              # Lower max position size
  
  # Regime filtering
  gms_max_age_sec: 300             # 5 minutes max age
  atr_fallback_pct: 0.01
  trail_eps: 0.01
  
  # Conservative regime requirements (Phase 2 enhancements)
  min_regime_confidence: 0.7       # Require high confidence
  min_regime_duration: 5.0         # Require 5 minutes of stability
  require_momentum_confirmation: true
  momentum_lookback_bars: 3
  min_momentum_threshold: 0.002    # 0.2% minimum momentum
  
  # Enable confidence-based position scaling
  enable_confidence_scaling: true
  confidence_scale_factor: 0.8     # Conservative scaling
  
  # Enable regime transition exits
  regime_transition_exits: true

# Analysis settings
analysis:
  analyze_trades_after_backtest: True

# Metadata
calibration_metadata:
  target_trade_frequency: "160-190 trades/year"
  calibration_goal: "Match legacy conservatism with modern pipeline"
  key_changes:
    - "Increased momentum thresholds 10-25x"
    - "Enhanced Bear regime detection" 
    - "Added regime confidence/duration filters"
    - "Reduced risk per trade"
    - "Enabled regime transition exits"
  calibration_date: "2025-07-14"
  notes: |
    Conservative calibration to achieve legacy-like trade frequency while
    maintaining the modern 3-layer architecture:
    - Strategic Layer (1h): Primary regime signals
    - Tactical Layer (1m): Momentum confirmation  
    - Execution Layer (1s): Optimal entry timing