# Override configuration for TF-v3 (new default) backtest
# Date range: March 1-22, 2025
# Modified to ensure proper configuration for TF-v3 with granular_microstructure detector

backtest:
  period_preset: 'custom'
  custom_start_date: "2025-03-01"
  custom_end_date: "2025-03-22"

strategies:
  use_tf_v2: false
  use_tf_v3: true
  use_obi_scalper: false
  use_mean_reversion: false
  use_mean_variance: false

# Portfolio configuration
portfolio:
  max_notional: 1000000  # Increased max notional to allow larger trades
  max_leverage: 100.0    # Set very high leverage limit to allow small positions

# Core configuration
core:
  initial_balance: 10000.0
  risk_per_trade: 0.001   # Very small risk per trade (0.1%)
  max_leverage: 100.0     # Very high leverage
  asset_max_leverage: 100.0

regime:
  detector_type: 'continuous_gms'  # Use continuous_gms detector type
  use_strict_strategy_filtering: false  # Disable strict strategy filtering to allow TF-v3 in all regimes
  gms_use_three_state_mapping: true  # Ensure 3-state mapping is used
  gms_state_mapping_file: 'configs/gms_state_mapping.yaml'  # Explicitly set mapping file

# GMS configuration to ensure 3-state mapping
gms:
  detector_type: 'continuous_gms'  # Match regime.detector_type for consistency
  cadence_sec: 60
  output_states: 3  # Set to 3 to ensure 3-state mapping
  state_collapse_map_file: 'configs/gms_state_mapping.yaml'
  use_four_state_mapping: false
  use_three_state_mapping: true  # Explicitly enable 3-state mapping
  risk_suppressed_notional_frac: 0.25
  risk_suppressed_pnl_atr_mult: 1.5
  gms_max_age_sec: 3600  # Increased from default to be more lenient with staleness

# TF-v3 configuration with 20/50 EMA settings (matching hardcoded values in SignalEngine)
tf_v3:
  enabled: true
  ema_fast: 20  # Match hardcoded value in SignalEngine
  ema_slow: 50  # Match hardcoded value in SignalEngine
  atr_period: 14
  atr_trail_k: 3.0
  max_trade_life_h: 24
  risk_frac: 0.0001  # Extremely small risk fraction (0.01%)
  max_notional: 10   # Very small max notional
  gms_max_age_sec: 3600  # Increased from default to be more lenient with staleness
