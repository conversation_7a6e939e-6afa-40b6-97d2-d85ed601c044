# ==============================================================================
# Modern System Calibrated Configuration
# ==============================================================================
# This configuration contains calibrated thresholds for the modern system
# based on analysis of 1-second feature data patterns.
#
# Key differences from modern_system.yaml:
# - Adjusted thresholds based on actual data distribution
# - Optimized for 100-200 trades per year
# - Balanced regime state distribution
# ==============================================================================

# Extends modern_system.yaml with calibrated settings
extends: "configs/overrides/modern_system.yaml"

# System mode selection
system_mode: "modern"

# ==============================================================================
# Regime Detection Configuration - CALIBRATED
# ==============================================================================
regime:
  # Detector Selection
  detector_type: "continuous_gms"  # Changed back to original name
  use_filter: true
  
  # CRITICAL: 60-second updates for state awareness
  cadence_sec: 60  # NOT 3600!
  
  # Volatility Thresholds - Calibrated for 1s data
  # Based on analysis showing typical ATR range of 0.001-0.03
  gms_vol_high_thresh: 0.012      # ~80th percentile of volatility
  gms_vol_low_thresh: 0.004       # ~20th percentile of volatility
  
  # Momentum Thresholds - Calibrated for 1s features
  # Based on typical momentum range of -100 to +100
  gms_mom_strong_thresh: 50.0     # Strong directional move
  gms_mom_weak_thresh: 20.0       # Moderate directional move
  
  # State mapping - keep 3-state for initial testing
  gms_use_three_state_mapping: true
  
  # Confirmation settings
  gms_use_adx_confirmation: true
  gms_use_funding_confirmation: true
  
  # Minimum confidence for regime validity
  min_regime_confidence: 0.6      # Balanced threshold

# ==============================================================================
# Strategy Configuration
# ==============================================================================
strategies:
  # Strategy Selection
  use_tf_v2: false
  use_tf_v3: true                 # ACTIVE: Modern trend following
  use_mean_reversion: false
  use_obi_scalper: false
  
  # TF-v3 Filters
  tf_use_obi_filter: true
  tf_use_funding_filter: true

# ==============================================================================
# Risk Management - CRITICAL FIX
# ==============================================================================
tf_v3:
  # Position Sizing - MUST MATCH LEGACY
  risk_frac: 0.25                 # 25% risk per trade (NOT 2%!)
  
  # Risk Management Features
  use_dynamic_risk: true
  use_regime_risk_scaling: true
  
  # Regime Risk Multipliers (for 25% base)
  strong_trend_risk_mult: 1.2     # 30% in strong trends
  weak_trend_risk_mult: 1.0       # 25% in weak trends
  range_risk_mult: 0.8            # 20% in ranging markets
  
  # Stop Loss Configuration
  use_dynamic_stops: true
  stop_loss_atr_mult: 1.5         # 1.5x ATR stop
  
  # Take Profit Configuration
  use_dynamic_tp: true
  take_profit_atr_mult: 3.0       # 3x ATR target
  
  # Entry Requirements
  min_regime_confidence: 0.6      # Not too restrictive
  min_regime_duration_minutes: 10 # Regime must be stable for 10 min
  max_regime_changes_1h: 5        # Max 5 state changes in last hour

# ==============================================================================
# Microstructure Configuration - CALIBRATED
# ==============================================================================
microstructure:
  # OBI Settings
  depth_levels: 10
  obi_levels: 10
  
  # OBI Confirmation Thresholds - Calibrated
  gms_obi_strong_confirm_thresh: 0.10   # ~70th percentile
  gms_obi_weak_confirm_thresh: 0.05     # ~30th percentile

# ==============================================================================
# Data Configuration
# ==============================================================================
data_source: "features_1s"
feature_source: "precomputed"

# ==============================================================================
# Execution Refinement Settings
# ==============================================================================
execution:
  window_minutes: 5               # 5-minute execution window
  min_volume_percentile: 20       # Min volume for execution
  max_spread_percentile: 80       # Max spread for execution
  momentum_weight: 0.3            # Weight for momentum in timing

# ==============================================================================
# Expected Performance Metrics
# ==============================================================================
# With calibrated thresholds:
# - Total Trades: 100-200
# - Win Rate: >55%
# - Total Return: >100%
# - Sharpe Ratio: >1.5
# - Max Drawdown: <20%
#
# Regime Distribution Target:
# - BULL: 25-35%
# - BEAR: 25-35%
# - CHOP: 30-50%
# ==============================================================================