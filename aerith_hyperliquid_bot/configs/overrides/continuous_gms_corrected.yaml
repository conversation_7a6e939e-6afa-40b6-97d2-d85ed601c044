# Corrected Continuous GMS Configuration
# Fixes the momentum threshold issue while maintaining aggressive testing
# Expected to detect both BULL and BEAR regimes appropriately

# Full 2024 dataset for comprehensive testing
backtest:
  period_preset: 'custom'
  custom_start_date: "2024-01-01"
  custom_end_date: "2024-12-31"

# Enable continuous GMS detector with TF-v3 strategy
regime:
  detector_type: 'continuous_gms'
  use_filter: true
  use_strict_strategy_filtering: false  # Allow more trades for testing

strategies:
  use_tf_v2: false
  use_tf_v3: true
  use_mean_reversion: false
  use_mean_variance: false
  use_obi_scalper: false

# Corrected continuous GMS thresholds
regime:
  detector_type: 'continuous_gms'
  use_filter: true
  use_strict_strategy_filtering: false
  
  continuous_gms:
    # CORRECTED thresholds - proper scale for minute-based processing
    gms_vol_low_thresh: 0.01            # Low volatility: 1% (was 0.005)
    gms_vol_high_thresh: 0.03           # High volatility: 3% (was 0.015)
    gms_mom_weak_thresh: 0.5            # Weak momentum (was 0.001 - 500x too low!)
    gms_mom_strong_thresh: 2.5          # Strong momentum (was 0.003 - 833x too low!)
    
    # Keep other settings
    gms_spread_std_high_thresh: 0.0005  # High spread volatility (50 basis points)
    gms_spread_mean_low_thresh: 0.0001  # Tight spread threshold (1 basis point)
    cadence_sec: 60                     # Keep 1-minute cadence
    output_states: 8
    state_collapse_map_file: 'configs/gms_state_mapping.yaml'
    use_four_state_mapping: false
    risk_suppressed_notional_frac: 0.25
    risk_suppressed_pnl_atr_mult: 1.5

# Match GMS detector type
gms:
  detector_type: 'continuous_gms'
  cadence_sec: 60
  auto_thresholds: false  # Use fixed thresholds for consistency

# TF-v3 configuration with reasonable parameters
tf_v3:
  enabled: true
  ema_fast: 20               # Standard fast EMA
  ema_slow: 50               # Standard slow EMA  
  atr_period: 14
  atr_trail_k: 3.0           # Standard trailing stop
  max_trade_life_h: 24       # 24-hour max hold
  risk_frac: 0.25            # 25% risk fraction
  max_notional: 25000        # $25K max position
  gms_max_age_sec: 300       # 5-minute staleness threshold
  atr_fallback_pct: 0.01     # 1% fallback ATR
  trail_eps: 0.01            # Standard trailing epsilon
  
  # Optional: Disable tactical enhancements for baseline comparison
  disable_tactical_alignment: true    # Start without enhancements
  disable_execution_scoring: true     # Compare raw strategy first