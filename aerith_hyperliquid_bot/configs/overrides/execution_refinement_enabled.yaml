# Execution Refinement Configuration
# This enables Phase 3: Execution refinement using 1-minute data
# Refines trade execution timing based on regime confidence and market microstructure

# Enable execution refinement in backtester
backtest:
  use_execution_refinement: true

# Include the calibrated GMS configuration
regime:
  detector_type: 'continuous_gms'
  continuous_gms:
    # Legacy-inspired momentum values that work
    momentum_strong_thresh: 100.0
    momentum_weak_thresh: 50.0
    # Current volatility values that seem reasonable
    volatility_high_thresh: 0.03
    volatility_low_thresh: 0.01
    # Enable BEAR regime detection
    map_weak_bear_to_bear: true

# Execution configuration for ExecutionFilter
execution:
  # High confidence threshold (execute immediately above this)
  high_confidence_threshold: 0.8
  
  # Execution window in minutes (check first N minutes)
  execution_window_minutes: 5
  
  # Minimum execution score to trigger early execution
  min_execution_score: 35
  
  # Momentum lookback in seconds (not used with 1m candles)
  momentum_lookback_seconds: 60

# TF-v3 Configuration with confidence-based filtering
strategies:
  tf_v3:
    # Enable confidence-based position scaling
    confidence_position_scaling: true
    
    # Minimum confidence to take any position (0.0 = disabled)
    min_regime_confidence: 0.0
    
    # Position scaling parameters
    min_confidence_for_scaling: 0.3
    max_confidence_for_scaling: 1.0
    confidence_scale_factor: 0.5  # 50% position at minimum confidence