# ==============================================================================
# Leverage Parity Test Configuration
# ==============================================================================
# Purpose: Test if matching legacy leverage settings closes the ROI gap
# Target: Legacy system 215.41% ROI -> Match with modern system
# 
# Changes from execution_refinement_enabled.yaml:
# 1. Set max_leverage to match legacy (10.0 vs 8.0)
# 2. Add tf_v3 leverage configuration to match tf_leverage_base: 5.0
# 3. Set ATR stop multiplier to match legacy (2.0 vs 2.5)
# ==============================================================================

# Enable execution refinement in backtester and force MODERN system mode
backtest:
  use_execution_refinement: true
  force_system_mode: 'modern'  # Force modern system (continuous_gms + TF-v3)

# Include the calibrated GMS configuration
regime:
  detector_type: 'continuous_gms'
  use_strict_strategy_filtering: false  # Allow flexibility as in working config
  # CRITICAL: Disable weak bear mapping to test conservative approach
  map_weak_bear_to_bear: false
  continuous_gms:
    # CONSERVATIVE thresholds - calibrated to achieve 160-190 trades/year
    gms_mom_strong_thresh: 100.0   # Conservative momentum (vs 15.0 aggressive)
    gms_mom_weak_thresh: 50.0     # Conservative momentum (vs 5.0 aggressive)
    gms_vol_high_thresh: 0.0092    #0.015
    gms_vol_low_thresh: 0.0055     #0.005
    gms_spread_std_high_thresh: 0.0005
    gms_spread_mean_low_thresh: 0.0001
    cadence_sec: 3600
    output_states: 8
    state_collapse_map_file: 'configs/gms_state_mapping.yaml'
    use_four_state_mapping: false
    risk_suppressed_notional_frac: 0.25
    risk_suppressed_pnl_atr_mult: 1.5
    # Disable weak bear mapping for conservative approach
    map_weak_bear_to_bear: false

# GMS configuration (must match regime.detector_type)
gms:
  detector_type: 'continuous_gms'
  # CRITICAL: Disable adaptive thresholds to use our calibrated values
  auto_thresholds: false  # MUST be false to use fixed thresholds
  adaptive_thresholds: false  # Alternative name for same setting
  # CRITICAL: Set cadence at top level for detector to read
  cadence_sec: 3600  # A/B testing mode to match legacy
  # CONSERVATIVE thresholds - calibrated to achieve 160-190 trades/year
  gms_mom_strong_thresh: 75.0   # Conservative momentum (vs 15.0 aggressive)  
  gms_mom_weak_thresh: 35.0     # Conservative momentum (vs 5.0 aggressive)
  gms_vol_high_thresh: 0.015
  gms_vol_low_thresh: 0.005
  gms_spread_std_high_thresh: 0.0005
  gms_spread_mean_low_thresh: 0.0001
  # Other settings from regime config
  output_states: 8
  state_collapse_map_file: 'configs/gms_state_mapping.yaml'
  use_four_state_mapping: false
  risk_suppressed_notional_frac: 0.25
  risk_suppressed_pnl_atr_mult: 1.5
  map_weak_bear_to_bear: false

# Execution configuration for ExecutionFilter
execution:
  # High confidence threshold (execute immediately above this)
  high_confidence_threshold: 0.8
  
  # Execution window in minutes (check first N minutes)
  execution_window_minutes: 5
  
  # Minimum execution score to trigger early execution
  min_execution_score: 35
  
  # Momentum lookback in seconds (not used with 1m candles)
  momentum_lookback_seconds: 60

# Enable TF-v3 strategy for modern system - explicit configuration
strategies:
  use_tf_v3: true                              # Enable TF-v3 (modern system)
  use_tf_v2: false                             # Explicitly disable TF-v2 (legacy system)
  use_mean_reversion_microstructure: false     # Disable mean reversion

# TF-v3 configuration with LEVERAGE PARITY
tf_v3:
  # CRITICAL FIX #1: Match legacy leverage
  max_leverage: 5.0              # Match tf_leverage_base from legacy
  atr_trail_k: 2.0              # Match tf_atr_stop_mult from legacy
  
  # Enable confidence-based position scaling
  confidence_position_scaling: false
  
  # Enable regime transition handling for exits
  regime_transition_exits: false
  
  # Minimum confidence to take any position (0.0 = disabled)
  min_regime_confidence: 0.0
  
  # Position scaling parameters
  min_confidence_for_scaling: 0.3
  max_confidence_for_scaling: 1.0
  confidence_scale_factor: 0.5  # 50% position at minimum confidence

# CRITICAL FIX #2: Match legacy portfolio settings
portfolio:
  risk_per_trade: 0.015    # 1.5% risk per trade (same as before)
  max_leverage: 10.0       # MATCH LEGACY (vs 8.0 in previous config)
  
# CRITICAL FIX #3: Match legacy indicators exactly
indicators:
  tf_ewma_fast: 21         # Slightly slower than 20
  tf_ewma_slow: 55         # Slightly slower than 50
  tf_atr_period: 20        # Match legacy
  tf_atr_stop_mult: 2.0    # MATCH LEGACY (vs 2.5 in previous config)
  tf_atr_target_mult: 4.5  # Higher targets (vs 4.0)
  tf_leverage_base: 5.0    # MATCH LEGACY (explicitly set)