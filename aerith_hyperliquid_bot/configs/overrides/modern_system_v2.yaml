# Modern System V2 Configuration
# ==============================
# 
# This configuration uses the overhauled modern system with:
# - ModernContinuousDetectorV2 (no hardcoded values)
# - ModernDataAdapter for field transformations
# - TF-v3 strategy with modern enhancements
#
# Target: 60-200 trades for 2024 with positive ROI

# System identification
system_type: "modern"
version: "2.0"

# Backtest period
period_preset: "2024"

# Core data settings
data:
  features_1s_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s"
  ohlcv_base_path: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled_l2"
  
# Regime detection configuration
regime:
  detector_type: "continuous_modern_v2"  # Uses the new clean detector
  
  # Volatility thresholds (REQUIRED - no defaults!)
  gms_vol_high_thresh: 0.0006         # ~0.06% ATR - high volatility threshold
  gms_vol_low_thresh: 0.0002          # ~0.02% ATR - low volatility threshold
  
  # Momentum thresholds (REQUIRED - no defaults!)
  gms_mom_strong_thresh: 0.0001       # ~0.01% momentum - strong trend
  gms_mom_weak_thresh: 0.00003        # ~0.003% momentum - weak trend
  
  # Spread thresholds (REQUIRED - no defaults!)
  gms_spread_std_high_thresh: 0.0005  # High spread volatility (choppy market)
  gms_spread_mean_low_thresh: 0.0001  # Low average spread (tight market)
  
  # OBI confirmation thresholds (REQUIRED - no defaults!)
  gms_obi_strong_confirm_thresh: 0.15 # Strong order book imbalance
  gms_obi_weak_confirm_thresh: 0.05   # Weak order book imbalance
  
  # Optional features
  gms_use_adx_confirmation: false      # ADX not available in features_1s
  gms_use_funding_confirmation: false  # Funding data not reliable
  
  # Graceful degradation settings
  min_confidence_for_trend: 0.6        # Minimum confidence to declare trend
  allow_partial_signals: true          # Continue with missing optional signals
  
  # Operational settings
  operational:
    cadence_sec: 60                    # Update regime every 60 seconds
    risk_suppressed_notional_frac: 0.25
    risk_suppressed_pnl_atr_mult: 1.5

# Strategy configuration
strategies:
  use_tf_v3: true                      # Use modern TF-v3 strategy
  tf_v3:
    # Entry filters (calibrated for 60-200 trades)
    entry_atr_min: 0.0002              # Minimum volatility for entry
    entry_atr_max: 0.001               # Maximum volatility for entry
    
    # Momentum filters
    entry_momentum_min: 0.00005        # Minimum momentum strength
    ma_slope_threshold: 0.00005        # MA slope for trend confirmation
    
    # OBI confirmation
    obi_confirmation_threshold: 0.08   # OBI alignment threshold
    require_obi_confirmation: true     # Require OBI to align with direction
    
    # Risk management
    stop_atr_multiplier: 1.5           # Stop loss in ATR units
    target_atr_multiplier: 2.5         # Take profit in ATR units
    
    # Position sizing
    risk_per_trade: 0.02               # 2% risk per trade
    max_position_size: 0.1             # Max 10% of capital per position

# Data aggregation settings
data_aggregator:
  update_frequency_seconds: 60         # Match detector cadence
  lookback_hours: 24                   # Historical context
  
# Feature flags
features:
  use_data_adapter: true               # Enable field transformations
  log_transformations: true            # Log all data transformations
  validate_signals: true               # Validate signal quality
  
# Backtesting specific
backtest:
  start_date: "2024-06-01"
  end_date: "2024-06-08"               # One week for validation
  initial_capital: 10000
  commission_rate: 0.0002              # 2 bps commission
  slippage_bps: 1                      # 1 bp slippage
  
# Logging configuration
logging:
  level: "INFO"
  log_state_changes: true
  log_signal_quality: true
  log_trades: true