# Test override for priming functionality (Task R-112m)
# Tests adaptive threshold priming with a short 1-day backtest

backtest:
  period_preset: 'custom'
  custom_start_date: '2025-03-01'
  custom_end_date: '2025-03-02'

# Enable continuous GMS with adaptive thresholds and priming
regime:
  use_filter: true
  detector_type: 'continuous_gms'

gms:
  detector_type: 'continuous_gms'
  auto_thresholds: true
  priming_hours: 24  # Prime with 24 hours of historical data
  percentile_window_sec: 86400  # 24-hour rolling window
  vol_low_pct: 0.001
  vol_high_pct: 0.50
  mom_low_pct: 0.001
  mom_high_pct: 0.50
  min_history_rows: 100

# Enable TF-v3 strategy to test trade generation
strategies:
  tf_v3:
    enabled: true
    
# Disable other strategies for clean test
strategies:
  obi_scalper:
    enabled: false
