# Legacy Mirror Configuration
# Mirrors the baseline system (granular_microstructure + TF-v2) for A/B testing
# Expected results: ~180 trades, ~215% ROI for 2024

# Full 2024 dataset for comprehensive testing
backtest:
  period_preset: 'custom'
  custom_start_date: "2024-01-01"
  custom_end_date: "2024-12-31"

# Use legacy detector and strategy
regime:
  detector_type: 'granular_microstructure'
  use_filter: true
  use_strict_strategy_filtering: true  # Keep strict filtering as in baseline

strategies:
  use_tf_v2: true   # Legacy trend following strategy
  use_tf_v3: false  # Disable modern strategy
  use_mean_reversion: false
  use_mean_variance: false
  use_obi_scalper: false

# Legacy detector configuration (from base.yaml)
regime:
  detector_type: 'granular_microstructure'
  use_filter: true
  use_strict_strategy_filtering: true
  
  # Legacy thresholds (proper scale for hourly processing)
  granular_microstructure:
    gms_vol_high_thresh: 0.0092         # High volatility: 0.92%
    gms_vol_low_thresh: 0.0055          # Low volatility: 0.55%
    gms_mom_strong_thresh: 100.0        # Strong momentum threshold
    gms_mom_weak_thresh: 50.0           # Weak momentum threshold
    gms_spread_std_high_thresh: 0.000050 # High spread volatility (5 basis points)
    gms_spread_mean_low_thresh: 0.000045 # Tight spread threshold (4.5 basis points)
    cadence_sec: 3600                   # Update frequency: 3600s (1 hour)
    output_states: 8                    # Number of output states (before collapse)
    state_collapse_map_file: 'configs/gms_state_mapping.yaml'
    use_four_state_mapping: false       # Use 3-state mapping
    risk_suppressed_notional_frac: 0.25
    risk_suppressed_pnl_atr_mult: 1.5
    skip_l2_raw_processing_if_1h_features_exist: true

# Match GMS detector type
gms:
  detector_type: 'granular_microstructure'
  cadence_sec: 3600

# Legacy TF-v2 configuration (from base.yaml)
indicators:
  tf_ewma_fast: 20                     # Fast EWMA period
  tf_ewma_medium: 32                   # Medium EWMA period  
  use_tf_medium_ewma: false            # Don't use medium EWMA
  tf_ewma_slow: 50                     # Slow EWMA period
  tf_atr_period: 20                    # ATR period for stop/target calculation
  tf_atr_stop_mult: 2.0                # ATR multiplier for stop loss (2x ATR)
  tf_atr_target_mult: 4.0              # ATR multiplier for take profit (4x ATR)
  tf_leverage_base: 5.0                # Base leverage for TF strategy
  tf_max_entry_volatility_pct: 0.01    # Max volatility for TF entries (1%)

# Legacy strategy settings
strategies:
  tf_warmup_bars: "auto"               # Auto-calculate warm-up period
  tf_use_obi_filter: false             # No OBI filter in baseline
  tf_use_funding_filter: false         # No funding filter in baseline