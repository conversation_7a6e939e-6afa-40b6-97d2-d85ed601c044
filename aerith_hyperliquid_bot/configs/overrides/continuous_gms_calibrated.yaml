# Properly calibrated continuous_gms configuration
# Fixes momentum thresholds to enable BEAR regime detection

# Full 2024 dataset for testing
backtest:
  period_preset: 'custom'
  custom_start_date: "2024-01-01"
  custom_end_date: "2024-12-31"

# Enable continuous GMS detector with TF-v3 strategy
regime:
  detector_type: 'continuous_gms'
  use_filter: true
  use_strict_strategy_filtering: false  # Allow some flexibility
  
  # Properly calibrated thresholds for continuous_gms
  continuous_gms:
    # Use base config values (reasonable for 60s updates)
    gms_vol_low_thresh: 0.01      # 1% volatility threshold
    gms_vol_high_thresh: 0.03     # 3% high volatility
    gms_mom_weak_thresh: 0.5      # 0.5 momentum (was 0.001 - 500x too low!)
    gms_mom_strong_thresh: 2.5    # 2.5 strong momentum (was 0.003 - 833x too low!)
    # cadence_sec removed - uses default 60s from base.yaml

strategies:
  use_tf_v2: False
  use_tf_v3: True

# Set GMS to match regime configuration
gms:
  detector_type: 'continuous_gms'
  # cadence_sec removed - uses default 60s from base.yaml

# TF-v3 configuration with reasonable parameters
tf_v3:
  enabled: true
  ema_fast: 20               # Standard fast EMA
  ema_slow: 50               # Standard slow EMA  
  atr_period: 14
  atr_trail_k: 2.0           # Standard 2x ATR trailing stop
  max_trade_life_h: 24       # 24 hour max hold time
  risk_frac: 0.02            # 2% risk per trade (standard)
  max_notional: 50000        
  gms_max_age_sec: 300       # 5 minute staleness threshold
  atr_fallback_pct: 0.01     # 1% fallback ATR
  trail_eps: 0.01            # Standard trailing epsilon