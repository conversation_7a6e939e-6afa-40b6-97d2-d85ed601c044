# Aggressive test configuration to ensure trades and regime detection
# Loosened criteria for comprehensive system validation

# Full 2024 dataset for comprehensive testing
backtest:
  period_preset: 'custom'
  custom_start_date: "2024-01-01"
  custom_end_date: "2024-12-31"

# Enable continuous GMS detector with TF-v3 strategy
regime:
  detector_type: 'continuous_gms'
  use_filter: true                    # Keep regime filtering active
  use_strict_strategy_filtering: false # DISABLE strict filtering to allow more trades

strategies:
  use_tf_v2: False
  use_tf_v3: True

# Override regime configuration with aggressive thresholds
regime:
  detector_type: 'continuous_gms'
  use_filter: true
  use_strict_strategy_filtering: false # DISABLE strict filtering to allow more trades
  
  # Very aggressive thresholds in the correct location
  continuous_gms:
    gms_vol_low_thresh: 0.005    # Lower vol threshold (was 0.015)
    gms_vol_high_thresh: 0.015   # Lower high vol threshold (was 0.015)
    gms_mom_weak_thresh: 0.001   # Much lower momentum threshold (was 0.2)
    gms_mom_strong_thresh: 0.003 # Lower strong momentum threshold (was 1.0)
    # cadence_sec removed - will use default 60s from base.yaml

# Set GMS to match regime configuration
gms:
  detector_type: 'continuous_gms'
  # cadence_sec removed - will use default 60s from base.yaml

# Very aggressive TF-v3 configuration for maximum trade generation
tf_v3:
  enabled: true
  ema_fast: 10               # Much faster EMAs for quicker signals
  ema_slow: 20               # Much shorter period for easier alignment  
  atr_period: 14
  atr_trail_k: 1.5           # Looser stops for easier entries
  max_trade_life_h: 120      # Longer hold time (5 days)
  risk_frac: 0.05            # Smaller position size for more frequent trades
  max_notional: 50000        # Higher cap to allow larger trades
  gms_max_age_sec: 7200      # Very lenient staleness threshold (2 hours)
  atr_fallback_pct: 0.03     # Higher fallback ATR
  trail_eps: 0.005           # Looser trailing stop
  disable_ema_alignment: true # DISABLE EMA alignment check completely
  min_forecast_threshold: 0.0001 # Very low forecast threshold
  min_atr_threshold: 0.0001      # Very low ATR threshold