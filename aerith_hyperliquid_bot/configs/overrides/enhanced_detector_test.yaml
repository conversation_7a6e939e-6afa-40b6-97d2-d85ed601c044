# Enhanced Detector Test Configuration
# =====================================
# Uses EXACT legacy thresholds with enhanced detector wrapper
# For A/B testing against legacy baseline

# Inherit from modern system base
system_type: "modern"
version: "2.0"

# ==============================================================================
# Regime Detection - MUST MATCH LEGACY EXACTLY!
# ==============================================================================
regime:
  detector_type: "enhanced"  # Our new wrapper
  use_filter: true
  
  # CRITICAL: Use EXACT legacy thresholds
  gms_vol_high_thresh: 0.0092      # 0.92% ATR (NOT 6%!)
  gms_vol_low_thresh: 0.0055       # 0.55% ATR (NOT 2%!)
  gms_mom_strong_thresh: 100.0     # 100 (NOT 50!)
  gms_mom_weak_thresh: 50.0        # 50 (NOT 5!)
  
  # Spread thresholds from legacy
  gms_spread_std_high_thresh: 0.000050
  gms_spread_mean_low_thresh: 0.000045
  
  # State mapping - match legacy
  gms_use_three_state_mapping: true
  
  # Confirmation settings
  gms_use_adx_confirmation: false
  gms_use_funding_confirmation: false
  
  # Enhanced detector specific
  enhanced:
    # Inherit all base thresholds
    gms_vol_high_thresh: 0.0092
    gms_vol_low_thresh: 0.0055
    gms_mom_strong_thresh: 100.0
    gms_mom_weak_thresh: 50.0
    # Quality scoring thresholds
    quality_threshold: 0.7         # Execute if quality > 0.7
    spread_score_weight: 0.4       # 40% weight on spread
    momentum_score_weight: 0.4     # 40% weight on momentum
    volume_score_weight: 0.2       # 20% weight on volume

# ==============================================================================
# Microstructure - MUST MATCH LEGACY!
# ==============================================================================
microstructure:
  depth_levels: 5
  gms_obi_strong_confirm_thresh: 0.2    # 0.2 (NOT 0.24!)
  gms_obi_weak_confirm_thresh: 0.05     # 0.05 (NOT 0.10!)

# ==============================================================================
# TF-v3 Strategy - Remove extra constraints
# ==============================================================================
tf_v3:
  enabled: true
  # Copy all from modern but override these:
  min_regime_duration_minutes: 0    # No minimum (match legacy behavior)
  max_regime_changes_1h: 999        # No limit (match legacy)
  min_regime_confidence: 0.0        # No minimum (match legacy)
  
  # Keep modern signal engine parameters
  ema_fast: 12
  ema_slow: 26
  atr_period: 14
  risk_frac: 0.25
  
# ==============================================================================
# Everything else inherits from modern_system_v2_complete.yaml
# ==============================================================================