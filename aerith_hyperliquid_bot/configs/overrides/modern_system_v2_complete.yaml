# Modern System V2 Complete Configuration - THRESHOLD FIXES APPLIED
# ==================================================================
# CRITICAL FIXES:
# 1. Momentum thresholds corrected from 0.0003/0.001 to 50.0/100.0 (100,000x scale error)
# 2. Spread thresholds corrected from basis points to decimal format
# This should restore performance to match legacy system

# ==============================================================================
# Global Configuration
# ==============================================================================
is_backtest: true                 # Global flag indicating if running in backtest mode

# ==============================================================================
# Data & Cache Configuration
# ==============================================================================
data_paths:
  l2_data_root: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw2" # Contains YYYYMMDD_raw2.parquet
  raw_l2_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/l2_raw" # Raw L2 data directory
  feature_1s_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s" # 1-second feature data
  ohlcv_base_path: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled_l2" # Base path for OHLC bars
  log_dir: "/Users/<USER>/Desktop/trading_bot_/logs"
  require_ohlcv_volume: false # Set to false for now

# ==============================================================================
# Data Providers Configuration
# ==============================================================================
data_providers:
  fear_greed:
    enabled: false # Re-enabled after fixing timezone issues

# ==============================================================================
# Cache Configuration
# ==============================================================================
cache:
  l2_cache_max_size: 24 # Max daily L2 files to keep in memory (if applicable)

# ==============================================================================
# Timeframe Setting
# ==============================================================================
timeframe: "1h" # Or "4h" - Set the desired timeframe for the run

# ==============================================================================
# Backtest & Simulation Configuration
# ==============================================================================
backtest:
  period_preset: '2024'            # Test on full 2024 data
  custom_start_date: "2024-01-01"  
  custom_end_date: "2024-01-07"    # One week for initial testing

simulation:
  latency_seconds: 0.5             # Simulated order latency
  max_impact_levels: 5             # L2 levels assumed fillable without extra penalty
  force_taker_execution: True      # Set to False to enable maker attempt
  attempt_maker_orders: False      # Set to True to attempt maker fills
  maker_placement_type: 'best_passive'
  maker_time_buckets_seconds: [5, 30, 120]
  maker_fill_probabilities: [0.07, 0.13, 0.10]

# ==============================================================================
# Portfolio Configuration
# ==============================================================================
portfolio:
  # --- Risk Controls ---
  initial_balance: 10000       # Initial balance for backtests (in USD)
  risk_per_trade: 0.25         # Risk % of balance per trade (25% - matches legacy)
  max_leverage: 10.0           # Global maximum leverage cap
  asset_max_leverage: 50.0     # Asset-specific max leverage
  margin_mode: cross           # Margin mode: 'cross' or 'isolated'
  max_hold_time_hours: 24      # Maximum time to hold a position
  
  # --- Optional Guards ---
  max_notional: 0              # 0 = no explicit ceiling
  min_trade_size: 0.001        # Minimum trade size in BTC
  leverage_guard_pct: 0        # 0 = disabled
  margin_buffer_pct: 0.05      # Margin buffer percentage (5%)
  stop_loss_percent: 0.02      # Stop loss at 2% loss
  take_profit_percent: 0.04    # Take profit at 4% gain

# ==============================================================================
# Costs Configuration
# ==============================================================================
costs:
  funding_rate: 0.0001        # Default funding rate (per period)
  funding_hours: 8            # Funding interval
  taker_fee: 0.000315         # Taker fee rate (0.0315%)
  maker_fee: 0.00009          # Maker fee rate (0.009%)
  l2_penalty_factor: 1.005    # Slippage penalty factor (0.5%)

# ==============================================================================
# Strategy Selection & Configuration
# ==============================================================================
strategies:
  # --- Strategy Activation ---
  use_tf_v2: False           # Legacy trend following strategy (DISABLED for modern)
  use_mean_reversion: False  # Mean reversion strategy (inactive per PRD)
  use_mean_variance: False   # Mean variance strategy (inactive per PRD) 
  use_obi_scalper: False     # Order Book Imbalance scalper strategy (inactive)
  use_tf_v3: True            # Modern trend following strategy (v3 - ACTIVE)
  
  # --- Trend Following (TF-v2) Configuration ---
  tf_warmup_bars: "auto"     # Warm-up period: "auto" (calculates based on indicators) or integer
  tf_use_obi_filter: False   # Whether to use Order Book Imbalance filter for TF signals
  tf_use_funding_filter: False # Whether to use funding rate filter for TF signals
  
  # --- OBI Scalper Configuration ---
  obi_scalper_active_in_all_regimes: False # Whether OBI scalper works in all market regimes
  gms_activate_obi_scalper_in_chop: True   # Activate OBI scalper during CHOP regime
  scalper_risk_pct: 0.5      # Risk percentage for scalper strategy (0.5% of balance)
  
  # --- Trend Following V3 Configuration ---
  hourly_trend_follow_v3:
    strategy_type: "trend_following"
    min_confidence: 0.6
    base_notional: 2000.0
    adaptive_sizing: True
    risk_adjustment: True
    use_regime_filters: True
    max_drawdown_pct: 0.15
    confidence_scaling:
      low: 0.5
      medium: 1.0
      high: 1.5
    regime_multipliers:
      STRONG_BULL_TREND: 1.2
      WEAK_BULL_TREND: 0.8
      STRONG_BEAR_TREND: 1.2
      WEAK_BEAR_TREND: 0.8
      default: 0.5

# ==============================================================================
# Regime Detection Configuration (MODERN SYSTEM V2)
# ==============================================================================
regime:
  detector_type: "enhanced"  # Uses the enhanced detector that wraps legacy logic
  
  # Volatility thresholds (REQUIRED - no defaults!)
  # Calibrated based on actual data percentiles
  gms_vol_low_thresh: 0.006           # 25th percentile - low volatility threshold
  gms_vol_high_thresh: 0.009          # 75th percentile - high volatility threshold
  
  # Momentum thresholds (FIXED: Scale error corrected!)
  # Legacy uses 50.0/100.0, modern was using 0.0003/0.001 (100,000x too small!)
  gms_mom_weak_thresh: 50.0           # FIXED: was 0.0003 (166,666x too small)
  gms_mom_strong_thresh: 100.0        # FIXED: was 0.001 (100,000x too small)
  
  # Spread thresholds (FIXED: Unit conversion error corrected!)
  # Legacy uses decimal format (0.00005 = 5 basis points)
  # Modern was using basis points directly (2.5, 8.0) - WRONG UNITS!
  # Converting to decimal format to match legacy and code expectations
  gms_spread_std_high_thresh: 0.000050   # FIXED: was 2.5 bps (50x wrong scale)
  gms_spread_mean_low_thresh: 0.000045   # FIXED: was 8.0 bps (177x wrong scale)
  
  # Optional features
  gms_use_adx_confirmation: False     # ADX trend confirmation
  gms_use_funding_confirmation: False # Funding rate confirmation
  gms_use_adaptive_thresholds: False  # DISABLED - causes 655s bottleneck!
  
  # Graceful degradation
  min_confidence_for_trend: 0.6       # Minimum confidence to classify as trend
  allow_partial_signals: True         # Allow operation with missing optional fields
  
  # Operational settings
  gms_cadence_seconds: 60             # Update frequency (60 seconds)
  risk_suppressed_notional_frac: 0.02 # Risk suppression threshold
  risk_suppressed_pnl_atr_mult: 3.0   # PnL threshold for risk suppression
  min_confidence_threshold: 0.4       # Lower confidence threshold for initial trading
  
  # Fallback configuration
  fallback_confidence: 0.7            # Confidence for price-based fallback regime
  price_change_threshold: 0.001       # 0.1% threshold for bull/bear detection
  neutral_confidence_factor: 0.8      # Reduce confidence by 20% in neutral markets
  
  # State mapping
  gms_use_three_state_mapping: False  # Use full 8-state model

# ==============================================================================
# Microstructure Settings (OBI Thresholds)
# ==============================================================================
microstructure:
  depth_levels: 5
  # Calibrated based on actual OBI data percentiles
  gms_obi_strong_confirm_thresh: 0.24  # 75th percentile - strong OBI confirmation
  gms_obi_weak_confirm_thresh: 0.10    # 50th percentile - weak OBI confirmation

# ==============================================================================
# Indicator Configuration
# ==============================================================================
indicators:
  # Main indicators - match legacy for better regime/EMA alignment
  ema_short: 20
  ema_long: 50
  rsi_period: 14
  adx_period: 14
  bollinger_period: 20
  bollinger_std: 2
  
# ==============================================================================
# Data Processing Configuration (MODERN)
# ==============================================================================
data:
  features_1s_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s"
  ohlcv_base_path: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled_l2"
  
# ==============================================================================
# System Configuration (MODERN)
# ==============================================================================
system:
  data_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data"
  log_dir: "/Users/<USER>/Desktop/trading_bot_/logs"
  data_loader: "modern"  # Use modern data loader
  
# System identification
system_type: "modern"
version: "2.0"

# ==============================================================================
# TF-v3 Strategy Configuration (MODERN)
# ==============================================================================
tf_v3:
  enabled: true                                # TF-v3 strategy enabled
  # Technical Indicators - Match legacy system for better alignment
  ema_fast: 20                                 # Fast EMA period (matches legacy)
  ema_slow: 50                                 # Slow EMA period (matches legacy)
  ema_baseline: 50                             # Baseline EMA (same as slow)
  atr_period: 14                               # ATR period for volatility measurement
  atr_trail_k: 3.0                             # ATR multiplier for trailing stop (3x)
  # Risk Management
  max_trade_life_h: 24                         # Maximum trade duration (24 hours)
  risk_frac: 0.25                              # Risk fraction per trade (25% of balance)
  max_notional: 25000                          # Maximum notional position size ($25K)
  gms_max_age_sec: 300                         # Maximum age for GMS state (5 minutes)
  atr_fallback_pct: 0.01                       # Fallback ATR percentage if ATR calculation fails
  # Additional modern parameters
  rsi_length: 14                               # RSI period for overbought/oversold
  bb_length: 20                                # Bollinger Band period
  bb_std: 2.0                                  # Bollinger Band standard deviations
  warmup_mode: "auto"                          # Automatic warmup calculation
  shift_periods: 1                             # Look-ahead bias prevention
  # Entry thresholds
  base_position_size: 0.02                     # Base position size (2%)
  min_regime_duration_minutes: 30              # Minimum regime duration for entry (increased from 10)
  max_regime_changes_1h: 3                     # Maximum regime changes per hour (reduced from 5)
  min_regime_confidence: 0.65                  # Minimum regime confidence for entry (increased from 0.4)

# ==============================================================================
# Analysis Configuration
# ==============================================================================
analysis:
  save_results: true
  results_dir: "/Users/<USER>/Desktop/trading_bot_/analysis_results"
  
# ==============================================================================
# Visualization Configuration
# ==============================================================================
visualization:
  save_plots: true
  plot_dir: "/Users/<USER>/Desktop/trading_bot_/plots"
  show_plots: false