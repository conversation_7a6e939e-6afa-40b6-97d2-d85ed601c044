# Override configuration for TF-v2 (legacy) backtest
# Date range: March 1-22, 2025

backtest:
  period_preset: 'custom'
  custom_start_date: "2025-03-01"
  custom_end_date: "2025-03-22"

strategies:
  use_tf_v2: true
  use_tf_v3: false
  use_obi_scalper: false
  use_mean_reversion: false
  use_mean_variance: false

regime:
  detector_type: 'granular_microstructure'  # Use legacy detector type

# Ensure TF-v3 is disabled
tf_v3:
  enabled: false
