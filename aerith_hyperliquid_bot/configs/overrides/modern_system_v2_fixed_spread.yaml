# Modern System V2 - FIXED SPREAD THRESHOLDS
# =========================================================
# Based on analysis:
# - ma_slope thresholds fixed: 10/20 (was 50/100)
# - spread thresholds fixed: 1.5/1.2 basis points (was 0.000045/0.00005)
# - quality threshold reduced: 0.45 (was 0.70)

# ==============================================================================
# Use existing config as base
# ==============================================================================
inherit_from: modern_system_v2_adjusted_thresholds.yaml

# ==============================================================================
# Enhanced Detector Settings
# ==============================================================================
regime:
  detector_settings:
    enhanced:
      quality_threshold: 0.45  # Reduced from 0.70 to allow top ~20% of signals
      spread_score_weight: 0.4
      momentum_score_weight: 0.4
      volume_score_weight: 0.2

  # FIXED SPREAD THRESHOLDS
  # Enhanced data has spread values in basis points (1.3-2.3 range)
  # Not decimal format (0.000045)
  gms_spread_mean_low_thresh: 1.5    # 50th percentile of spread_mean
  gms_spread_std_high_thresh: 1.2    # ~50th percentile of spread_std

# ==============================================================================
# Test Period - February 2024
# ==============================================================================
backtest:
  period_preset: 'custom'
  custom_start_date: "2024-02-01"
  custom_end_date: "2024-02-29"