# Legacy 3-state configuration for R-108a debug
# Single day test: 2024-01-02 to 2024-01-03

# Set backtest period to March 2024 for full month test
backtest:
  period_preset: 'custom'
  custom_start_date: "2024-03-01"
  custom_end_date: "2024-04-01"

# Enable legacy strategy configuration
strategies:
  use_tf_v2: true            # Enable tf_v2 (legacy trend following)
  use_tf_v3: false           # Disable tf_v3 (modern)
  use_obi_scalper: false     # Disable OBI scalper
  use_mean_reversion: false  # Disable mean reversion
  use_mean_variance: false   # Disable mean variance

# Use legacy detector with proper 3-state configuration
regime:
  detector_type: 'granular_microstructure'  # Legacy detector type
  use_filter: true                          # Enable regime filtering
  use_strict_strategy_filtering: true       # Use strict strategy filtering
  output_states: 3                          # Collapse to BULL/BEAR/CHOP
  gms_state_mapping_file: 'configs/gms_state_mapping.yaml'  # State mapping file

# Align GMS configuration with regime detector
gms:
  detector_type: 'granular_microstructure'  # Match regime detector
  output_states: 3                          # Collapse to BULL/BEAR/CHOP
  state_collapse_map_file: 'configs/gms_state_mapping.yaml'

# Ensure TF-v3 is disabled
tf_v3:
  enabled: false

# Set backtest mode
is_backtest: true

# Use 1h timeframe as specified in task
timeframe: "1h"

# Override indicator periods to work with limited data
indicators:
  tf_ewma_fast: 8    # Reduced from 20 to work with limited data
  tf_ewma_slow: 20   # Reduced from 50 to work with limited data
