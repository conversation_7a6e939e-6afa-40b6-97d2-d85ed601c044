# Modern System v2 Configuration - FIXED THRESHOLDS
# Based on actual data analysis from analyze_data_ranges.py

strategy:
  name: tf_v3_modern
  version: 3.0
  detector_type: continuous_modern

# Regime detection thresholds - CALIBRATED TO ACTUAL DATA
regime:
  # Original momentum thresholds were 50.0/100.0 - that's 100,000x too high!
  # Actual momentum values range from -0.002 to 0.002
  gms_mom_weak_thresh: 0.0005      # 50th percentile of abs(momentum)
  gms_mom_strong_thresh: 0.002     # 90th percentile of abs(momentum)
  
  # OBI thresholds look reasonable already
  gms_obi_weak_confirm_thresh: 0.10    # 50th percentile
  gms_obi_strong_confirm_thresh: 0.24   # 75th percentile
  
  # Fixed spread thresholds - were off by 26,666x and 50,000x!
  # Actual spread_mean is around 0.0008 to 0.0015
  gms_spread_mean_low_thresh: 0.0008   # 25th percentile 
  # Actual spread_std is around 1.2 to 2.6
  gms_spread_std_high_thresh: 2.5      # 90th percentile
  
  # Volatility thresholds based on ATR percentages
  gms_volatility_low_percentile: 0.0061   # 25th percentile (~0.61% volatility)
  gms_volatility_high_percentile: 0.0094  # 75th percentile (~0.94% volatility)
  
  # Keep other settings as-is
  detection_window_seconds: 300
  min_regime_confidence: 0.65
  min_regime_duration_minutes: 30
  detector_type: continuous_modern

# TF-v3 Strategy parameters - keep existing
tf_v3:
  ema_fast_period: 12
  ema_slow_period: 26
  atr_period: 14
  atr_multiplier_stop: 3.0
  atr_multiplier_target: 5.0
  ema_fast_weight: 0.6
  ema_slow_weight: 0.4
  trade_size_pct: 0.25
  max_position_size: 0.25
  max_trades_per_day: 10
  forecast_threshold: 0.0001  # 0.01% of price
  use_forecast_confirmation: true
  use_regime_filter: true
  max_trade_life_h: 24
  adx_filter_threshold: null
  adx_period: null
  use_adx_filter: false
  
# Portfolio management - keep 25% risk
portfolio:
  initial_balance: 10000
  risk_per_trade: 0.25
  max_open_positions: 1
  max_daily_loss: 0.10
  max_total_risk: 0.25
  
# Keep all other settings from modern_system_v2_complete.yaml