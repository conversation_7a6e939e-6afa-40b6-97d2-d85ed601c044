# R-112f Momentum Threshold Test Override
# One-day test for 2025-03-05 with new momentum thresholds

# Override backtest period to single day
backtest:
  period_preset: 'custom'
  custom_start_date: 2025-03-05
  custom_end_date: 2025-03-05

# Enable continuous_gms and tf_v3 strategies
strategies:
  use_trend_following: false
  use_mean_reversion: false
  use_mean_variance: false
  use_tf_v3: true

# Ensure continuous_gms detector is used
regime:
  detector_type: 'continuous_gms'
  use_filter: true

# Ensure continuous_gms configuration
gms:
  detector_type: 'continuous_gms'
  cadence_sec: 60
  output_states: 8
  state_collapse_map_file: 'configs/gms_state_mapping.yaml'
  use_four_state_mapping: false

# TF-v3 strategy configuration
tf_v3:
  enabled: true
  ema_fast: 20
  ema_slow: 50
  atr_period: 14
  atr_trail_k: 3.0
  max_trade_life_h: 24
  risk_frac: 0.25
  max_notional: 25000
  gms_max_age_sec: 300
  atr_fallback_pct: 0.01
  trail_eps: 0.01
