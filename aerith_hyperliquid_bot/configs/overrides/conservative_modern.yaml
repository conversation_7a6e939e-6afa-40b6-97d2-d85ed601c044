# Conservative Modern System Configuration
# Calibrated to mirror legacy system conservatism (160-190 trades/year)
# while maintaining modern pipeline architecture

# Enable execution refinement in backtester
backtest:
  use_execution_refinement: true

# Conservative GMS configuration - mirror legacy conservatism
regime:
  detector_type: 'continuous_gms'
  use_strict_strategy_filtering: false  # Allow flexibility
  # CRITICAL: Enable BEAR regime detection 
  map_weak_bear_to_bear: true
  continuous_gms:
    # CONSERVATIVE thresholds - calibrated to match legacy trade frequency
    gms_mom_strong_thresh: 75.0   # Much higher than 15.0 (closer to legacy 100.0)
    gms_mom_weak_thresh: 35.0     # Much higher than 5.0 (closer to legacy 50.0)
    gms_vol_high_thresh: 0.015    # Keep volatility thresholds same
    gms_vol_low_thresh: 0.005
    gms_spread_std_high_thresh: 0.0005
    gms_spread_mean_low_thresh: 0.0001
    cadence_sec: 3600  # Match legacy hourly updates
    output_states: 8
    state_collapse_map_file: 'configs/gms_state_mapping.yaml'
    use_four_state_mapping: false
    risk_suppressed_notional_frac: 0.25
    risk_suppressed_pnl_atr_mult: 1.5

# GMS configuration (must match regime.detector_type)
gms:
  detector_type: 'continuous_gms'
  auto_thresholds: false  # Use fixed conservative thresholds
  adaptive_thresholds: false
  cadence_sec: 3600  # Hourly updates like legacy
  # Conservative thresholds
  gms_mom_strong_thresh: 75.0   # Conservative momentum (vs 15.0)
  gms_mom_weak_thresh: 35.0     # Conservative momentum (vs 5.0)  
  gms_vol_high_thresh: 0.015
  gms_vol_low_thresh: 0.005
  gms_spread_std_high_thresh: 0.0005
  gms_spread_mean_low_thresh: 0.0001
  output_states: 8
  state_collapse_map_file: 'configs/gms_state_mapping.yaml'
  use_four_state_mapping: false
  risk_suppressed_notional_frac: 0.25
  risk_suppressed_pnl_atr_mult: 1.5
  map_weak_bear_to_bear: true

# Execution configuration - conservative
execution:
  high_confidence_threshold: 0.85  # Higher confidence required (vs 0.8)
  execution_window_minutes: 3       # Shorter window (vs 5) 
  min_execution_score: 45           # Higher score required (vs 35)
  momentum_lookback_seconds: 60

# Enable TF-v3 strategy for modern system
strategies:
  use_tf_v3: true
  use_tf_v2: false  # Disable TF-v2 (legacy system)
  tf_v3:
    # Conservative position management
    confidence_position_scaling: true     # Enable conservative scaling
    regime_transition_exits: true        # Enable regime-aware exits
    min_regime_confidence: 0.6           # Require 60% confidence (vs 0.0)
    # Position scaling parameters
    min_confidence_for_scaling: 0.6      # Higher minimum confidence
    max_confidence_for_scaling: 1.0
    confidence_scale_factor: 0.3         # More conservative scaling (30% vs 50%)

# Conservative portfolio settings
portfolio:
  risk_per_trade: 0.015    # 1.5% risk per trade (more conservative than 2%)
  max_leverage: 8.0        # Lower max leverage (vs 10.0)
  
# Conservative indicators - closer to legacy TF-v2 
indicators:
  tf_ewma_fast: 21         # Slightly slower than 20
  tf_ewma_slow: 55         # Slightly slower than 50
  tf_atr_period: 20        # Match legacy
  tf_atr_stop_mult: 2.5    # More conservative stops (vs 2.0)
  tf_atr_target_mult: 4.5  # Higher targets (vs 4.0)