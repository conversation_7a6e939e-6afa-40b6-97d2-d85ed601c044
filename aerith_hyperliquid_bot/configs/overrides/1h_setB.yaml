# configs/overrides/1h_setB.yaml
microstructure:
  obi_smoothing_window: 12
regime:
  gms_obi_strong_confirm_thresh: 0.00           # we’ll gate on z-score instead
  gms_obi_weak_confirm_thresh: 0.00
  obi_zscore_threshold: 1.3                     # add new key, used inside detector
  gms_spread_percentile_gate: 0.70              # 70th pct as cut-off
  gms_depth_slope_thin_limit: 0.10              # suppress trades if slope < 0.10
  gms_confirmation_bars: 2