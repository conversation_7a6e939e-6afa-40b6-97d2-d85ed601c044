# Fixed Modern System Configuration
# Uses continuous_gms (60s cadence) + TF-v3 with proper settings

# Enable execution refinement in backtester
backtest:
  use_execution_refinement: true

# Regime detection configuration
regime:
  detector_type: 'continuous_gms'
  use_strict_strategy_filtering: false
  # Enable BEAR regime detection
  map_weak_bear_to_bear: true
  continuous_gms:
    # True continuous mode - 60 second cadence
    cadence_sec: 60
    output_states: 8
    state_collapse_map_file: 'configs/gms_state_mapping.yaml'
    use_four_state_mapping: false
    
    # Calibrated thresholds from working configuration
    gms_mom_strong_thresh: 2.5
    gms_mom_weak_thresh: 0.5
    gms_vol_high_thresh: 0.015
    gms_vol_low_thresh: 0.005
    gms_spread_std_high_thresh: 0.0005
    gms_spread_mean_low_thresh: 0.0001
    
    # Risk suppression settings
    risk_suppressed_notional_frac: 0.25
    risk_suppressed_pnl_atr_mult: 1.5

# GMS configuration (must match regime.detector_type)
gms:
  detector_type: 'continuous_gms'
  auto_thresholds: true  # Enable adaptive thresholds for modern system

# Execution configuration for ExecutionFilter
execution:
  # High confidence threshold (execute immediately above this)
  high_confidence_threshold: 0.8
  
  # Execution window in minutes (check first N minutes)
  execution_window_minutes: 5
  
  # Minimum execution score to trigger early execution
  min_execution_score: 35
  
  # Momentum lookback in seconds (not used with 1m candles)
  momentum_lookback_seconds: 60

# Enable TF-v3 strategy for modern system
strategies:
  use_tf_v3: true
  use_tf_v2: false  # Disable TF-v2 (legacy system)
  tf_v3:
    # Enable confidence-based position scaling
    confidence_position_scaling: true
    
    # Enable regime transition handling for exits
    regime_transition_exits: true
    
    # Minimum confidence to take any position (0.0 = disabled)
    min_regime_confidence: 0.0
    
    # Position scaling parameters
    min_confidence_for_scaling: 0.3
    max_confidence_for_scaling: 1.0
    confidence_scale_factor: 0.5  # 50% position at minimum confidence