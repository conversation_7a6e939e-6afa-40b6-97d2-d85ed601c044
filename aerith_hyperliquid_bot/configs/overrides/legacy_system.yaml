# ==============================================================================
# Legacy System Override Configuration
# ==============================================================================
# This file contains the EXACT configuration that produces:
# - 180 trades
# - 215% ROI 
# - Stable, proven performance
#
# DO NOT MODIFY unless you fully understand the implications
# ==============================================================================

# ==============================================================================
# System Selection
# ==============================================================================
# Note: system_mode parameter removed as it's not used by the main backtest runner

# ==============================================================================
# Regime Detection Configuration
# ==============================================================================
regime:
  # Detector Selection
  detector_type: "granular_microstructure"  # FROZEN: Legacy detector
  use_filter: true                          # Enable regime filtering
  
  # State mapping configuration
  gms_use_three_state_mapping: true        # Enable 3-state mapping
  gms_state_mapping_file: ""                # Empty string to disable state mapping file
  map_weak_bear_to_bear: false              # Map Weak_Bear_Trend to CHOP
  # The original behavior is complex to replicate exactly
  
  # Volatility Thresholds (CRITICAL - DO NOT CHANGE)
  gms_vol_high_thresh: 0.0092              # High volatility threshold (0.92% ATR)
  gms_vol_low_thresh: 0.0055               # Low volatility threshold (0.55% ATR)
  
  # Momentum Thresholds (CRITICAL - DO NOT CHANGE)  
  gms_mom_strong_thresh: 100.0             # Strong momentum threshold
  gms_mom_weak_thresh: 50.0                # Weak momentum threshold
  
  # Spread Configuration
  gms_spread_std_high_thresh: 0.000050     # High spread volatility threshold (5 basis points)
  gms_spread_mean_low_thresh: 0.000045     # Low spread mean threshold (4.5 basis points)
  
  # OBI Confirmation Thresholds
  gms_use_adx_confirmation: false          # ADX confirmation disabled
  gms_use_funding_confirmation: false      # Funding confirmation disabled
  
  # Granular Microstructure subsection (ensures all settings are overridden)
  granular_microstructure:
    # Copy all thresholds to subsection
    gms_vol_high_thresh: 0.0092
    gms_vol_low_thresh: 0.0055
    gms_mom_strong_thresh: 100.0
    gms_mom_weak_thresh: 50.0
    gms_spread_std_high_thresh: 0.000050
    gms_spread_mean_low_thresh: 0.000045

# ==============================================================================
# Strategy Configuration
# ==============================================================================
strategies:
  # Strategy Selection
  use_tf_v2: true                          # FROZEN: Legacy trend following
  use_tf_v3: false                         # Modern strategy disabled
  use_mean_reversion: false                # Mean reversion disabled
  use_obi_scalper: false                   # OBI scalper disabled
  
  # TF-v2 Filters
  tf_use_obi_filter: false                 # OBI filter disabled for legacy
  tf_use_funding_filter: false             # Funding filter disabled for legacy

# ==============================================================================
# Risk Management Configuration  
# ==============================================================================
tf_v3:  # Note: Despite the name, this section controls risk for both TF versions
  # Position Sizing (CRITICAL - DO NOT CHANGE)
  risk_frac: 0.25                          # 25% risk per trade (NOT 2%!)
  
  # Risk Management Settings
  use_dynamic_risk: false                  # Dynamic risk disabled
  use_regime_risk_scaling: false           # Regime-based scaling disabled
  
  # Stop Loss Configuration
  use_dynamic_stops: false                 # Dynamic stops disabled
  stop_loss_atr_mult: 2.0                  # 2x ATR stop loss
  
  # Take Profit Configuration
  use_dynamic_tp: false                    # Dynamic TP disabled
  take_profit_atr_mult: 4.0                # 4x ATR take profit

# ==============================================================================
# Microstructure Configuration
# ==============================================================================
microstructure:
  # OBI Settings
  depth_levels: 5                          # Use 5 orderbook levels
  obi_levels: 5                            # OBI calculation depth
  
  # Legacy Thresholds
  gms_obi_strong_confirm_thresh: 0.2       # Strong OBI confirmation
  gms_obi_weak_confirm_thresh: 0.05        # Weak OBI confirmation

# ==============================================================================
# Data Configuration
# ==============================================================================
data_source: "raw2"                        # Use raw2/ data files
feature_source: "calculated"               # Calculate features on-the-fly

# ==============================================================================
# GMS Configuration (for consistency)
# ==============================================================================
gms:
  detector_type: "granular_microstructure"  # Match regime detector
  state_collapse_map_file: ""               # Empty string to prevent loading
  
# ==============================================================================
# Performance Expectations
# ==============================================================================
# These are the expected results when running with this configuration:
# - Total Trades: ~180
# - Total Return: ~215%
# - Win Rate: ~55-60%
# - Sharpe Ratio: ~1.5-2.0
# - Max Drawdown: ~20-25%
#
# If results differ significantly, check:
# 1. Data integrity in raw2/ directory
# 2. All thresholds match exactly
# 3. No additional filters are active
# ==============================================================================