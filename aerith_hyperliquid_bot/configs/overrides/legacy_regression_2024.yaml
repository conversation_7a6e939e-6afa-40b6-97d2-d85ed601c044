# Override configuration for Legacy Regression Test (R-108a)
# Full year 2024 backtest with legacy stack: granular_microstructure + tf_v2
# Expected baseline: Sharpe ~4.00, Profit Factor ~2.08, Max DD ~6.9%, ROI ~203%, Trades ~184

# Set backtest period to full year 2024
backtest:
  period_preset: '2024'  # Full year 2024

# Enable legacy strategy configuration
strategies:
  use_tf_v2: true            # Enable tf_v2 (legacy trend following)
  use_tf_v3: false           # Disable tf_v3 (modern)
  use_obi_scalper: false     # Disable OBI scalper
  use_mean_reversion: false  # Disable mean reversion
  use_mean_variance: false   # Disable mean variance

# Use legacy detector with proper configuration
regime:
  detector_type: 'granular_microstructure'  # Legacy detector type
  use_filter: true                          # Enable regime filtering
  use_strict_strategy_filtering: true       # Use strict strategy filtering
  output_states: 3                          # Collapse to BULL/BEAR/CHOP
  gms_state_mapping_file: 'configs/gms_state_mapping.yaml'  # State mapping file

# Align GMS configuration with regime detector
gms:
  detector_type: 'granular_microstructure'  # Match regime detector
  output_states: 3                          # Collapse to BULL/BEAR/CHOP
  state_collapse_map_file: 'configs/gms_state_mapping.yaml'

# Ensure TF-v3 is disabled
tf_v3:
  enabled: false

# Set backtest mode
is_backtest: true

# Use 1h timeframe as specified in task
timeframe: "1h"
