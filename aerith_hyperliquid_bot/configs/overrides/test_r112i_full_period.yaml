# Override config for testing Task R-112i fixes on full period 2025-03-01 to 2025-03-22
backtest:
  period_preset: "custom"
  custom_start_date: "2025-03-01 00:00:00"
  custom_end_date: "2025-03-22 23:59:59"

# Use continuous_gms detector with loosened thresholds to generate diverse regimes
regime:
  detector_type: "continuous_gms"

  # Override detector-specific settings with data-calibrated thresholds
  continuous_gms:
    # Calibrate volatility thresholds based on actual data (ATR ~1.04%, but must be < 1.0)
    gms_vol_high_thresh: 0.95    # Just below 1.0 limit, above actual data to allow trend detection
    gms_vol_low_thresh: 0.80     # Below actual data range to allow low vol detection
    # Calibrate momentum thresholds based on actual data (range: -362 to +321, but must be < 200)
    gms_mom_strong_thresh: 150.0 # Strong momentum threshold (must be < 200 limit)
    gms_mom_weak_thresh: 50.0    # Weak momentum threshold (about 15% of max)
    # Keep spread thresholds reasonable
    gms_spread_std_high_thresh: 0.0005
    gms_spread_mean_low_thresh: 0.0001
    # Operational settings
    output_states: 3  # Use collapsed 3-state output (BULL/BEAR/CHOP)
    cadence_sec: 60
    state_collapse_map_file: 'configs/gms_state_mapping.yaml'
    use_four_state_mapping: false

# Enable continuous GMS detector with collapsed 3-state output (fallback)
gms:
  detector_type: "continuous_gms"
  cadence_sec: 60
  output_states: 3  # Use collapsed 3-state output (BULL/BEAR/CHOP) instead of raw 8-state
