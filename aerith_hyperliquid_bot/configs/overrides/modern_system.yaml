# ==============================================================================
# Modern System Override Configuration  
# ==============================================================================
# This file configures the modern trading system with:
# - Continuous GMS detector
# - TF-v3 enhanced strategy
# - 1-second feature data
#
# STATUS: Fixed - Added forecast indicator and calibrated thresholds
# ==============================================================================

# ==============================================================================
# System Selection
# ==============================================================================
system_mode: "modern"  # Selects the modern trading system

# ==============================================================================
# Regime Detection Configuration
# ==============================================================================
regime:
  # Detector Selection
  detector_type: "continuous_gms"          # Modern continuous detector
  use_filter: true                         # Enable regime filtering
  
  # Volatility Thresholds (FIXED: Now matches legacy scale)
  gms_vol_high_thresh: 0.0092              # High volatility threshold (matches legacy)
  gms_vol_low_thresh: 0.0055               # Low volatility threshold (matches legacy)
  
  # Momentum Thresholds (FIXED: Now matches legacy scale!)
  gms_mom_strong_thresh: 100.0             # Strong momentum threshold (was 2.5 - 40x increase!)
  gms_mom_weak_thresh: 50.0               # Weak momentum threshold (was 0.5 - 100x increase!)
  
  # Enhanced Detection Features
  gms_use_adaptive_thresholds: true        # Enable adaptive thresholds
  gms_use_three_state_mapping: false       # Use full 8-state system
  
  # Confirmation Settings
  gms_use_adx_confirmation: true           # ADX confirmation enabled
  gms_use_funding_confirmation: true       # Funding confirmation enabled

# ==============================================================================
# Strategy Configuration
# ==============================================================================
strategies:
  # Strategy Selection
  use_tf_v2: false                         # Legacy strategy disabled
  use_tf_v3: true                          # ACTIVE: Modern trend following
  use_mean_reversion: false                # Mean reversion disabled
  use_obi_scalper: false                   # OBI scalper disabled
  
  # TF-v3 Filters
  tf_use_obi_filter: true                  # OBI filter enabled
  tf_use_funding_filter: true              # Funding filter enabled

# ==============================================================================
# Risk Management Configuration
# ==============================================================================
tf_v3:
  # Position Sizing
  risk_frac: 0.25                          # 25% risk per trade (matches legacy)
  
  # Enhanced Risk Management
  use_dynamic_risk: true                   # Enable dynamic risk sizing
  use_regime_risk_scaling: true            # Scale risk by regime
  
  # Regime Risk Multipliers (adjusted for 25% base)
  strong_trend_risk_mult: 1.2              # 120% risk in strong trends (30% total)
  weak_trend_risk_mult: 1.0                # 100% risk in weak trends (25% total)
  range_risk_mult: 0.8                     # 80% risk in ranging markets (20% total)
  
  # Stop Loss Configuration
  use_dynamic_stops: true                  # Dynamic stops enabled
  stop_loss_atr_mult: 1.5                  # Tighter stops (1.5x ATR)
  
  # Take Profit Configuration
  use_dynamic_tp: true                     # Dynamic TP enabled
  take_profit_atr_mult: 3.0                # 3x ATR take profit

# ==============================================================================
# Microstructure Configuration
# ==============================================================================
microstructure:
  # Enhanced OBI Settings
  depth_levels: 10                         # Use 10 orderbook levels
  obi_levels: 10                           # Deeper OBI calculation
  
  # Modern Thresholds
  gms_obi_strong_confirm_thresh: 0.15      # Tighter OBI confirmation
  gms_obi_weak_confirm_thresh: 0.08        # More selective

# ==============================================================================
# Data Configuration
# ==============================================================================
data_source: "features_1s"                 # Use 1-second feature files
feature_source: "precomputed"              # Use pre-calculated features

# ==============================================================================
# Performance Expectations
# ==============================================================================
# TARGET performance (with fixes applied):
# - Total Trades: 150-200 (targeting ~180 like legacy)
# - Total Return: >200% (targeting +215% like legacy)
# - Win Rate: >60%
# - Sharpe Ratio: >2.0
# - Max Drawdown: <15%
#
# FIXES APPLIED:
# - Added forecast indicator (ema_fast - ema_slow)
# - Fixed momentum threshold scales (100x adjustment)
# - Calibrated volatility thresholds to match legacy
# ==============================================================================