# Fixed A/B Testing Configuration for Modern System
# Based on investigation guide recommendations for matching legacy performance

# Enable execution refinement
backtest:
  use_execution_refinement: true

# Regime detection configuration
regime:
  detector_type: 'continuous_gms'
  use_strict_strategy_filtering: false
  # Enable BEAR regime detection
  map_weak_bear_to_bear: true

# GMS configuration - CRITICAL section for detector to read
gms:
  detector_type: 'continuous_gms'
  # CRITICAL: Disable adaptive thresholds
  auto_thresholds: false
  adaptive_thresholds: false
  
  # A/B testing mode - match legacy cadence
  cadence_sec: 3600
  
  # Use legacy momentum values for A/B testing
  # Investigation guide shows legacy used 100.0/50.0
  gms_mom_strong_thresh: 100.0
  gms_mom_weak_thresh: 50.0
  
  # Volatility thresholds from working config
  gms_vol_high_thresh: 0.015
  gms_vol_low_thresh: 0.005
  
  # Spread thresholds
  gms_spread_std_high_thresh: 0.0005
  gms_spread_mean_low_thresh: 0.0001
  
  # Other required settings
  output_states: 8
  state_collapse_map_file: 'configs/gms_state_mapping.yaml'
  use_four_state_mapping: false
  risk_suppressed_notional_frac: 0.25
  risk_suppressed_pnl_atr_mult: 1.5
  map_weak_bear_to_bear: true
  
  # Use same data source as legacy for fair comparison
  data_source: 'raw2'  # Match legacy data source

# Execution configuration
execution:
  high_confidence_threshold: 0.8
  execution_window_minutes: 5
  min_execution_score: 35
  momentum_lookback_seconds: 60

# Enable TF-v3 strategy
strategies:
  use_tf_v3: true
  use_tf_v2: false
  tf_v3:
    confidence_position_scaling: true
    regime_transition_exits: true
    min_regime_confidence: 0.0
    min_confidence_for_scaling: 0.3
    max_confidence_for_scaling: 1.0
    confidence_scale_factor: 0.5