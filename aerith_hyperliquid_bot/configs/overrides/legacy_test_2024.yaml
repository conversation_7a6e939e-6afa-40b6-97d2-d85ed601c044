# Legacy mode configuration for 2024 full year test
# Uses granular_microstructure detector with TF-v2 strategy

# Full 2024 dataset
backtest:
  period_preset: 'custom'
  custom_start_date: "2024-01-01"
  custom_end_date: "2024-12-31"

# Enable legacy detector with TF-v2 strategy
regime:
  detector_type: 'granular_microstructure'
  use_filter: true
  use_strict_strategy_filtering: true

strategies:
  use_tf_v2: True
  use_tf_v3: False

# Match legacy detector type
gms:
  detector_type: 'granular_microstructure'