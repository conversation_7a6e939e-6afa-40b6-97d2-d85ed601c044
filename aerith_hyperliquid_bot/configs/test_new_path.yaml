# Test configuration for R-112q validation
# Tests continuous GMS detector with new ATR and momentum fixes

# Full dataset with proper priming (start from 03-02 so it can prime with 03-01)
backtest:
  period_preset: 'custom'
  custom_start_date: "2025-03-02"
  custom_end_date: "2025-03-22"

# Enable continuous GMS detector with TF-v3 strategy
regime:
  detector_type: 'continuous_gms'

strategies:
  use_tf_v2: False
  use_tf_v3: True

# Re-enable adaptive thresholds now that priming data is available
gms:
  auto_thresholds: true  # Should work now with 03-01 priming data
  detector_type: 'continuous_gms'
  cadence_sec: 3600  # Match hourly data frequency

# Refined TF-v3 configuration for better trade generation
tf_v3:
  enabled: true
  ema_fast: 20               # Very fast EMAs for quick alignment
  ema_slow: 50              # Much shorter period for easier alignment
  atr_period: 14
  atr_trail_k: 2.0          # Tighter stops for more aggressive entries
  max_trade_life_h: 72      # Longer hold time
  risk_frac: 0.10           # Smaller position size for more trades
  max_notional: 10000       # Lower cap to allow more trades
  gms_max_age_sec: 1800     # Very lenient staleness threshold (30 min)
  atr_fallback_pct: 0.02    # Higher fallback
  trail_eps: 0.01
  disable_ema_alignment: false  # Temporarily disable EMA alignment check for testing
