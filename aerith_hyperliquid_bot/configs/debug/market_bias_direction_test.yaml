# Market Bias Direction Test Configuration
# Testing direction-specific biases

# Override base.yaml configuration
use_base: true

# Only use trend_following strategy for consistent testing
strategies:
  trend_following:
    enabled: true
  mean_reversion:
    enabled: false
  mean_variance:
    enabled: false

# Market Bias with direction bias settings
regime:
  market_bias:
    enabled: true
    use_three_state_mapping: true
    
    # All leverage factors set to neutral
    bull_leverage_factor: 1.0
    bear_leverage_factor: 1.0
    chop_leverage_factor: 1.0
    
    # All risk factors set to neutral
    bull_risk_factor: 1.0
    bear_risk_factor: 1.0
    chop_risk_factor: 1.0
    
    # Direction bias factors set to non-neutral values
    bull_long_bias: 2.0    # Double position size for longs in bull market
    bull_short_bias: 0.5   # Half position size for shorts in bull market
    bear_long_bias: 0.5    # Half position size for longs in bear market
    bear_short_bias: 2.0   # Double position size for shorts in bear market

# Configuration for testing
core:
  # Use a fixed seed for reproducibility
  random_seed: 42
  # Small initial balance for clearer position size effects
  initial_balance: 10000
  # Standard risk per trade
  risk_per_trade: 0.01
