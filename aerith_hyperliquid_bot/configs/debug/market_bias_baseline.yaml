# Market Bias Baseline Test Configuration
# All bias factors set to neutral (1.0) to establish a baseline

# Override base.yaml configuration
use_base: true

# Only use trend_following strategy for consistent testing
strategies:
  trend_following:
    enabled: true
  mean_reversion:
    enabled: false
  mean_variance:
    enabled: false

# Market Bias with neutral settings
regime:
  market_bias:
    enabled: true
    use_three_state_mapping: true
    
    # All leverage factors set to neutral
    bull_leverage_factor: 1.0
    bear_leverage_factor: 1.0
    chop_leverage_factor: 1.0
    
    # All risk factors set to neutral
    bull_risk_factor: 1.0
    bear_risk_factor: 1.0
    chop_risk_factor: 1.0
    
    # All direction bias factors set to neutral
    bull_long_bias: 1.0
    bull_short_bias: 1.0
    bear_long_bias: 1.0
    bear_short_bias: 1.0

# Configuration for testing
core:
  # Use a fixed seed for reproducibility
  random_seed: 42
  # Small initial balance for clearer position size effects
  initial_balance: 10000
  # Standard risk per trade
  risk_per_trade: 0.01

# Backtesting parameters
backtest:
  symbol: "BTC-PERP"
  period_preset: "custom"  # Use custom date range
  custom_start_date: "2023-01-01"
  custom_end_date: "2023-01-14"
  timeframe: "15m"
  
  # Enable detailed logging
  log_level: "INFO"
  
  # Output directory for results
  output_dir: "results/market_bias_debug"
