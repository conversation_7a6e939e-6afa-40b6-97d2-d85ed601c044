# Test configuration for Modern System (1 day only)
# Single day for quick testing

# Single day dataset for testing
backtest:
  period_preset: 'custom'
  custom_start_date: "2025-03-02"
  custom_end_date: "2025-03-02"

# Enable continuous GMS detector with TF-v3 strategy
regime:
  detector_type: 'continuous_gms'

strategies:
  use_tf_v2: False
  use_tf_v3: True

# Re-enable adaptive thresholds
gms:
  auto_thresholds: true
  detector_type: 'continuous_gms'
  cadence_sec: 3600

# Simplified TF-v3 configuration
tf_v3:
  enabled: true
  ema_fast: 20
  ema_slow: 50
  atr_period: 14
  atr_trail_k: 2.0
  max_trade_life_h: 72
  risk_frac: 0.10
  max_notional: 10000
  gms_max_age_sec: 1800
  atr_fallback_pct: 0.02
  trail_eps: 0.01
  disable_ema_alignment: false
