# Test configuration for Modern System with FIXED thresholds
# Testing why adaptive thresholds are required

# Short period for testing
backtest:
  period_preset: 'custom'
  custom_start_date: "2025-03-02"
  custom_end_date: "2025-03-05"

# Enable continuous GMS detector with TF-v3 strategy
regime:
  detector_type: 'continuous_gms'

strategies:
  use_tf_v2: False
  use_tf_v3: True

# DISABLE adaptive thresholds and use FIXED values
gms:
  auto_thresholds: false  # DISABLE adaptive thresholds
  detector_type: 'continuous_gms'
  cadence_sec: 3600
  
  # Fixed threshold values (using legacy system values as baseline)
  vol_low_thresh: 0.55
  vol_high_thresh: 0.92
  mom_weak_thresh: 50.0
  mom_strong_thresh: 100.0
  
  # Spread thresholds
  spread_mean_thresh_low: 0.0001
  spread_std_thresh_high: 0.0005
  
  # OBI thresholds
  obi_weak_thresh: 0.11
  obi_strong_thresh: 0.20

# TF-v3 configuration
tf_v3:
  enabled: true
  ema_fast: 20
  ema_slow: 50
  atr_period: 14
  atr_trail_k: 2.0
  max_trade_life_h: 72
  risk_frac: 0.10
  max_notional: 10000
  gms_max_age_sec: 1800
  atr_fallback_pct: 0.02
  trail_eps: 0.01
  disable_ema_alignment: false
