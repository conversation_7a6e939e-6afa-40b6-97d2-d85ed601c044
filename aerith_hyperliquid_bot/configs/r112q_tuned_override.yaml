# ==============================================================================
# R-112q Tuned Override Configuration - Calibrated Thresholds for March 2025
# ==============================================================================
# This override enables continuous_gms + tf_v3 with properly tuned thresholds
# based on actual March 2025 data distribution analysis

# ==============================================================================
# Backtest Period Override - 22-day March test
# ==============================================================================
backtest:
  period_preset: 'custom'
  custom_start_date: "2025-03-01"
  custom_end_date: "2025-03-22"

# ==============================================================================
# Strategy Override - Enable TF-v3, Disable TF-v2
# ==============================================================================
strategies:
  use_tf_v2: False              # Disable legacy TF-v2
  use_tf_v3: True               # Enable TF-v3 for R-112q test

# ==============================================================================
# Regime Detection Override - Enable continuous_gms with tuned thresholds
# ==============================================================================
regime:
  detector_type: 'continuous_gms'  # Switch to continuous GMS detector

  # Continuous GMS detector specific settings with calibrated thresholds
  continuous_gms:
    # Volatility thresholds (ATR%) - Based on actual data range 0.0028-0.0191
    gms_vol_low_thresh: 0.007211    # 33rd percentile of actual data
    gms_vol_high_thresh: 0.011761   # 67th percentile of actual data

    # Momentum thresholds (MA Slope) - Extremely low values since actual momentum is ~0
    # Note: Setting to near-zero to allow regime classification based on other factors
    gms_mom_weak_thresh: 0.001      # Near-zero threshold for weak momentum
    gms_mom_strong_thresh: 0.01     # Very low threshold for strong momentum

    # OBI thresholds - Based on actual data range -0.999 to +0.999
    gms_obi_weak_thresh: 0.492333   # 33rd percentile of absolute OBI values
    gms_obi_strong_thresh: 0.505834 # 67th percentile of absolute OBI values

    # Spread thresholds - Based on actual data ranges
    # Spread mean: 1.03-6.19, Spread std: 0.19-9.51
    gms_spread_mean_low_thresh: 1.28604944   # 33rd percentile of spread mean
    gms_spread_std_high_thresh: 1.99301329   # 67th percentile of spread std

    # Keep other settings from base configuration
    cadence_sec: 60
    depth_levels: 5
    output_states: 8

    # Threshold modes - use fixed thresholds
    gms_vol_thresh_mode: 'fixed'
    gms_mom_thresh_mode: 'fixed'
    gms_obi_thresh_mode: 'fixed'
    gms_spread_mean_thresh_mode: 'fixed'
    gms_spread_std_thresh_mode: 'fixed'

# ==============================================================================
# GMS Configuration Override - Disable adaptive thresholds
# ==============================================================================
gms:
  auto_thresholds: false        # Use fixed thresholds tuned for March 2025 data
