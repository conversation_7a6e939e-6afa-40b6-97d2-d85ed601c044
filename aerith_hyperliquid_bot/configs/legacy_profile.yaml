# Legacy System Configuration Override
# This configuration ensures the Legacy System (Granular Microstructure + TF-v2) 
# runs with optimal performance by disabling modern system components

# === CORE SYSTEM CONFIGURATION ===
regime:
  detector_type: 'granular_microstructure'      # Use Legacy GMS detector

gms:
  detector_type: 'granular_microstructure'      # Ensure consistency
  auto_thresholds: false                        # Use fixed thresholds for Legacy

# === STRATEGY CONFIGURATION ===
strategies:
  use_tf_v2: true                              # Enable TF-v2 (Legacy strategy)
  use_tf_v3: false                             # Disable TF-v3 (Modern strategy)
  use_mean_reversion: false
  use_mean_variance: false
  use_obi_scalper: false

# === PERFORMANCE OPTIMIZATIONS ===
scheduler:
  etl_enabled: false                           # CRITICAL: Disable ETL scheduler for Legacy System
  etl_poll_sec: 300                           # Not used when disabled, but set high value

# === DATA LOADING OPTIMIZATIONS ===
granular_microstructure:
  skip_l2_raw_processing_if_1h_features_exist: true  # Skip expensive L2 processing

# === BACKTEST CONFIGURATION ===
backtest:
  period_preset: '2024'                        # Full year 2024 for comprehensive analysis
  timeframe: '1h'                              # 1-hour timeframe for Legacy

# === LOGGING ===
logging:
  level: 'INFO'
  performance_logging: true                    # Enable performance monitoring

# === REQUIRED CONFIGURATION SECTIONS ===
# Add all required fields from base.yaml to prevent validation errors

# Data paths configuration
data_paths:
  l2_data_root: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw2" # Legacy L2 data
  raw_l2_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/l2_raw" # Raw L2 data directory
  feature_1s_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s" # 1-second feature data
  ohlcv_base_path: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled_l2" # Legacy OHLCV data
  log_dir: "/Users/<USER>/Desktop/trading_bot_/logs"
  require_ohlcv_volume: false

# Cache configuration
cache:
  l2_cache_max_size: 24

# Simulation configuration
simulation:
  latency_seconds: 0.5
  max_impact_levels: 5
  force_taker_execution: true
  attempt_maker_orders: false
  maker_placement_type: 'best_passive'
  maker_time_buckets_seconds: [5, 30, 120]
  maker_fill_probabilities: [0.07, 0.13, 0.10]

# Portfolio configuration
portfolio:
  initial_balance: 10000
  risk_per_trade: 0.02
  max_leverage: 10.0
  asset_max_leverage: 50.0
  margin_mode: cross
  max_hold_time_hours: 24
  max_notional: 0
  min_trade_size: 0.001
  leverage_guard_pct: 0
  margin_buffer_pct: 0.05

# Costs configuration
costs:
  funding_rate: 0.0001
  funding_hours: 8
  taker_fee: 0.000315
  maker_fee: 0.00009
  l2_penalty_factor: 1.005

# Microstructure configuration
microstructure:
  depth_levels: 5
  obi_levels: null
  obi_smoothing_window: 8
  obi_smoothing_type: 'sma'
  obi_zscore_window: null
  depth_levels_for_calc: null
  spread_rolling_window: 24
  spread_metric_to_roll: 'relative'
  allow_nan_micro_depth: true
  gms_obi_strong_confirm_thresh: 0.20
  gms_obi_weak_confirm_thresh: 0.11
  tf_filter_obi_threshold_long: 0.1
  tf_filter_obi_threshold_short: -0.1
  tf_filter_funding_threshold_long: -0.0005
  tf_filter_funding_threshold_short: 0.0005

# Indicators configuration
indicators:
  require_volume_for_signals: false
  adx_period: 14
  adx_threshold: 30.0
  high_volatility_adx_threshold: 30.0
  low_forecast_threshold: 1.0
  volatility_thresh_percent: 0.005
  bbw_thresh: 0.03
  chop_index_period: 14
  chop_index_high_thresh: 61.8
  chop_index_low_thresh: 38.2
  min_leverage: 1.0
  tf_ewma_fast: 20
  tf_ewma_medium: 32
  use_tf_medium_ewma: false
  tf_ewma_slow: 50
  tf_atr_period: 20
  tf_atr_stop_mult: 2.0
  tf_atr_target_mult: 4.0
  tf_leverage_base: 5.0
  tf_max_entry_volatility_pct: 0.01
  mr_ema_period: 20
  mr_keltner_mult: 2.0
  mr_rsi_period: 14
  mr_rsi_oversold: 30.0
  mr_rsi_overbought: 70.0
  mr_atr_period: 20
  mr_atr_stop_mult: 1.5
  mr_leverage_base: 5.0
  mr_obi_long_threshold: -0.2
  mr_obi_short_threshold: 0.2
  mr_require_obi_filter: true
  mv_edge_ema_period: 10
  mv_volatility_period: 20
  mv_min_edge_threshold: 0.0005
  mv_min_exit_edge_threshold: 0.0
  mv_min_kelly: 0.05
  mv_max_kelly: 0.25
  mv_leverage_base: 3.0
  mv_atr_stop_mult: 2.5
  mv_atr_target_mult: 3.0
  mv_atr_period: 20
  mv_obi_long_threshold: -0.3
  mv_obi_short_threshold: 0.3
  mv_require_obi_filter: true
  gms_roc_period: 5
  gms_ma_slope_period: 30
  gms_atr_percent_period: 14

# Analysis configuration
analysis:
  analyze_trades_after_backtest: true

# Visualization configuration
visualization:
  require_volume: false
  panels:
    show_ma_slope: true
    show_obi: true
    show_fear_greed: false
  appearance:
    price_panel_ratio: 4
    indicator_panel_ratio: 1
    max_points: 5000

# Disable TF-v3 completely
tf_v3:
  enabled: false 