# ==============================================================================
# R-112p Override Configuration - Final Validation Test
# ==============================================================================
# This override enables continuous_gms + tf_v3 for the 22-day March test period
# while preserving all other frozen base.yaml settings

# ==============================================================================
# Backtest Period Override - 22-day March test
# ==============================================================================
backtest:
  period_preset: 'custom'
  custom_start_date: "2025-03-01"
  custom_end_date: "2025-03-22"

# ==============================================================================
# Strategy Override - Enable TF-v3, Disable TF-v2
# ==============================================================================
strategies:
  use_tf_v2: False              # Disable legacy TF-v2
  use_tf_v3: True               # Enable TF-v3 for R-112p test

# ==============================================================================
# Regime Detection Override - Enable continuous_gms
# ==============================================================================
regime:
  detector_type: 'continuous_gms'  # Switch to continuous GMS detector

# ==============================================================================
# GMS Configuration Override - Use fixed thresholds for R-112p test
# ==============================================================================
gms:
  auto_thresholds: false        # Use fixed thresholds for R-112p test (no priming data available)
  # Use the continuous_gms detector settings from base.yaml
