# Test configuration for timestamp vectorization optimization
# Short 1-day backtest to verify optimizations work correctly

backtest:
  start_date: "2025-03-01"
  end_date: "2025-03-02"
  timeframe: "1h"

# Use legacy system for faster testing (no 1s feature loading)
regime:
  detector_type: "granular_microstructure"
  granular_microstructure:
    skip_l2_raw_processing_if_1h_features_exist: true

# Enable only TF-v2 strategy for simple testing
strategies:
  use_trend_following: true
  use_obi_scalper: false
  use_tf_v3: false
  use_continuous_gms: false

# Simple portfolio settings
portfolio:
  initial_balance: 10000
  risk_fraction: 0.02
  max_hold_time_hours: 24

# Minimal logging for faster execution
logging:
  level: "INFO"
  save_signals: false
  save_trades: true
