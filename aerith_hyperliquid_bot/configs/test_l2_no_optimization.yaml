# Test configuration for L2 processing WITHOUT optimization
# This config tests the legacy behavior with full L2 processing

# Single-day test for quick validation
backtest:
  period_preset: 'custom'
  custom_start_date: "2025-03-05"
  custom_end_date: "2025-03-05"

# Use legacy granular_microstructure detector with optimization DISABLED
regime:
  detector_type: 'granular_microstructure'
  use_filter: true
  
  # Granular Microstructure Detector Settings without optimization
  granular_microstructure:
    # Threshold values (baseline)
    gms_vol_high_thresh: 0.92
    gms_vol_low_thresh: 0.55
    gms_mom_strong_thresh: 100.0
    gms_mom_weak_thresh: 50.0
    gms_spread_std_high_thresh: 0.000050
    gms_spread_mean_low_thresh: 0.000045
    # Operational settings
    cadence_sec: 3600
    output_states: 8
    state_collapse_map_file: 'configs/gms_state_mapping.yaml'
    use_four_state_mapping: false
    risk_suppressed_notional_frac: 0.25
    risk_suppressed_pnl_atr_mult: 1.5
    # Performance optimization - DISABLED for comparison
    skip_l2_raw_processing_if_1h_features_exist: false

# Enable TF-v2 strategy (legacy)
strategies:
  use_tf_v2: true
  use_tf_v3: false
  use_mean_reversion: false
  use_mean_variance: false
  use_obi_scalper: false
