# Test configuration for R-113: Hourly aggregation validation
# Tests continuous GMS detector with hourly timeframe to validate feature aggregation fix

# Single-day test for quick validation
backtest:
  period_preset: 'custom'
  custom_start_date: "2025-03-05"
  custom_end_date: "2025-03-05"

# Enable continuous GMS detector
regime:
  detector_type: 'continuous_gms'
  use_filter: true

# Configure continuous GMS with appropriate settings
gms:
  detector_type: 'continuous_gms'
  cadence_sec: 3600  # Match hourly data frequency
  output_states: 8
  state_collapse_map_file: 'configs/gms_state_mapping.yaml'
  use_four_state_mapping: false
  
  # Use tuned thresholds from R-112q
  vol_low_thresh: 0.0072
  vol_high_thresh: 0.0118
  mom_weak_thresh: 0.001
  mom_strong_thresh: 0.01
  spread_mean_low_thresh: 1.286049
  spread_std_high_thresh: 1.993013

# Enable TF-v3 strategy to test regime-based activation
strategies:
  use_tf_v2: false
  use_tf_v3: true
  
  tf_v3:
    enabled: true
    regime_gating: true
    allowed_regimes: ['BULL', 'BEAR']  # Should activate in trending regimes
