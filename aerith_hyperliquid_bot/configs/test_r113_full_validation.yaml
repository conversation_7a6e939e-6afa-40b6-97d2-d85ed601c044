# Test configuration for R-113 full dataset validation
# Tests continuous GMS detector with hourly aggregation fix across complete dataset

# Full date range for Task R-113 validation
backtest:
  period_preset: 'custom'
  custom_start_date: "2025-03-01"
  custom_end_date: "2025-03-22"

# Enable continuous GMS detector with TF-v3 strategy
regime:
  detector_type: 'continuous_gms'

strategies:
  use_tf_v2: False
  use_tf_v3: True

# Enable adaptive thresholds to test hourly aggregation
gms:
  auto_thresholds: true
  detector_type: 'continuous_gms'
  cadence_sec: 3600  # Match hourly data frequency

# Refined TF-v3 configuration for better trade generation
tf_v3:
  enabled: true
  ema_fast: 8               # Very fast EMAs for quick alignment
  ema_slow: 16              # Much shorter period for easier alignment
  atr_period: 14
  atr_trail_k: 2.0          # Tighter stops for more aggressive entries
  max_trade_life_h: 72      # Longer hold time
  risk_frac: 0.10           # Smaller position size for more trades
  max_notional: 10000       # Lower cap to allow more trades
  gms_max_age_sec: 1800     # Very lenient staleness threshold (30 min)
  atr_fallback_pct: 0.02    # Higher fallback
  trail_eps: 0.01
  disable_ema_alignment: true  # Temporarily disable EMA alignment check for testing

# Use hourly timeframe to test aggregation fix
timeframe: "1h"
