#!/usr/bin/env python3
"""
Configuration Validation Script for Modern System
Compares profile_modern_system.yaml against base.yaml to identify missing flags
"""

import yaml
from pathlib import Path

def load_config(config_path):
    """Load YAML configuration file."""
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)

def validate_modern_config():
    """Validate modern system configuration against base configuration."""
    
    # Load configurations
    base_config = load_config('configs/base.yaml')
    modern_config = load_config('configs/profile_modern_system.yaml')
    
    print("=== MODERN SYSTEM CONFIGURATION VALIDATION ===")
    print()
    
    # Check critical modern system flags
    issues = []
    recommendations = []
    
    # 1. Check regime detector configuration
    print("1. REGIME DETECTOR CONFIGURATION")
    print("-" * 40)
    
    regime_detector = modern_config.get('regime', {}).get('detector_type')
    if regime_detector == 'continuous_gms':
        print("✅ regime.detector_type: continuous_gms")
    else:
        print(f"❌ regime.detector_type: {regime_detector} (should be 'continuous_gms')")
        issues.append("regime.detector_type should be 'continuous_gms'")
    
    # 2. Check GMS detector configuration
    print("\n2. GMS DETECTOR CONFIGURATION")
    print("-" * 40)
    
    gms_detector = modern_config.get('gms', {}).get('detector_type')
    if gms_detector == 'continuous_gms':
        print("✅ gms.detector_type: continuous_gms")
    elif gms_detector is None:
        print("⚠️  gms.detector_type: not set (will inherit from base.yaml)")
        recommendations.append("Consider explicitly setting gms.detector_type: 'continuous_gms'")
    else:
        print(f"❌ gms.detector_type: {gms_detector} (should be 'continuous_gms')")
        issues.append("gms.detector_type should be 'continuous_gms'")
    
    auto_thresholds = modern_config.get('gms', {}).get('auto_thresholds')
    if auto_thresholds is True:
        print("✅ gms.auto_thresholds: true")
    elif auto_thresholds is None:
        print("⚠️  gms.auto_thresholds: not set (will inherit from base.yaml)")
        recommendations.append("Consider explicitly setting gms.auto_thresholds: true")
    else:
        print(f"❌ gms.auto_thresholds: {auto_thresholds}")
        issues.append("gms.auto_thresholds should be true for modern system")
    
    # 3. Check strategy configuration
    print("\n3. STRATEGY CONFIGURATION")
    print("-" * 40)
    
    use_tf_v2 = modern_config.get('strategies', {}).get('use_tf_v2')
    if use_tf_v2 is False:
        print("✅ strategies.use_tf_v2: False")
    elif use_tf_v2 is None:
        print("⚠️  strategies.use_tf_v2: not set (will inherit from base.yaml)")
        recommendations.append("Consider explicitly setting strategies.use_tf_v2: False")
    else:
        print(f"❌ strategies.use_tf_v2: {use_tf_v2} (should be False)")
        issues.append("strategies.use_tf_v2 should be False for modern system")
    
    use_tf_v3 = modern_config.get('strategies', {}).get('use_tf_v3')
    if use_tf_v3 is True:
        print("✅ strategies.use_tf_v3: True")
    elif use_tf_v3 is None:
        print("⚠️  strategies.use_tf_v3: not set (will inherit from base.yaml)")
        recommendations.append("Consider explicitly setting strategies.use_tf_v3: True")
    else:
        print(f"❌ strategies.use_tf_v3: {use_tf_v3} (should be True)")
        issues.append("strategies.use_tf_v3 should be True for modern system")
    
    # 4. Check TF-v3 configuration
    print("\n4. TF-V3 STRATEGY CONFIGURATION")
    print("-" * 40)
    
    tf_v3_enabled = modern_config.get('tf_v3', {}).get('enabled')
    if tf_v3_enabled is True:
        print("✅ tf_v3.enabled: true")
    elif tf_v3_enabled is None:
        print("⚠️  tf_v3.enabled: not set (will inherit from base.yaml)")
        recommendations.append("Consider explicitly setting tf_v3.enabled: true")
    else:
        print(f"❌ tf_v3.enabled: {tf_v3_enabled} (should be true)")
        issues.append("tf_v3.enabled should be true for modern system")
    
    # 5. Check data configuration
    print("\n5. DATA CONFIGURATION")
    print("-" * 40)
    
    # Check if modern system data paths are properly configured
    data_paths = modern_config.get('data_paths')
    if data_paths is None:
        print("⚠️  data_paths: not set (will inherit from base.yaml)")
        print("   This is usually fine for modern system")
    else:
        print("✅ data_paths: explicitly configured")
    
    # 6. Check backtest period
    print("\n6. BACKTEST CONFIGURATION")
    print("-" * 40)
    
    start_date = modern_config.get('backtest', {}).get('custom_start_date')
    end_date = modern_config.get('backtest', {}).get('custom_end_date')
    
    if start_date and end_date:
        print(f"✅ backtest period: {start_date} to {end_date}")
    else:
        print("❌ backtest period: not properly configured")
        issues.append("backtest.custom_start_date and custom_end_date should be set")
    
    # 7. Check for missing critical sections
    print("\n7. MISSING SECTIONS CHECK")
    print("-" * 40)
    
    critical_sections = ['regime', 'strategies', 'tf_v3', 'backtest']
    for section in critical_sections:
        if section in modern_config:
            print(f"✅ {section}: present")
        else:
            print(f"⚠️  {section}: missing (will inherit from base.yaml)")
    
    # Summary
    print("\n" + "="*60)
    print("VALIDATION SUMMARY")
    print("="*60)
    
    if issues:
        print(f"\n❌ CRITICAL ISSUES FOUND ({len(issues)}):")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
    else:
        print("\n✅ NO CRITICAL ISSUES FOUND")
    
    if recommendations:
        print(f"\n💡 RECOMMENDATIONS ({len(recommendations)}):")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
    
    # Generate improved configuration
    print(f"\n📝 SUGGESTED IMPROVED CONFIGURATION:")
    print("-" * 40)
    
    improved_config = {
        'backtest': {
            'period_preset': 'custom',
            'custom_start_date': "2025-03-02",
            'custom_end_date': "2025-03-05"
        },
        'regime': {
            'detector_type': 'continuous_gms'
        },
        'strategies': {
            'use_tf_v2': False,
            'use_tf_v3': True
        },
        'gms': {
            'auto_thresholds': True,
            'detector_type': 'continuous_gms',
            'cadence_sec': 3600
        },
        'tf_v3': {
            'enabled': True,
            'ema_fast': 20,
            'ema_slow': 50,
            'atr_period': 14,
            'atr_trail_k': 2.0,
            'max_trade_life_h': 72,
            'risk_frac': 0.10,
            'max_notional': 10000,
            'gms_max_age_sec': 1800,
            'atr_fallback_pct': 0.02,
            'trail_eps': 0.01,
            'disable_ema_alignment': False
        }
    }
    
    print(yaml.dump(improved_config, default_flow_style=False, indent=2))
    
    return len(issues) == 0

if __name__ == "__main__":
    validate_modern_config()
