# Trading Bot Project Memory

## System Status (Jan 2025)
- **Legacy System**: 184 trades/year, +203% ROI ✅ VERIFIED BASELINE
- **Modern System**: 82 trades/year, +6.22% ROI ❌ FAILED ATTEMPT
- **Current Focus**: FORENSIC REPLICATION - Understand legacy BEFORE enhancing
- **Current Branch**: `feature/enhanced-data-architecture`

## Legacy Architecture (Working System)
```
ENTRY FLOW:
1. DataHandler loads raw2/ hourly data (37 features)
2. SignalEngine calculates indicators (EMAs, forecast, ATR)
3. LegacyGranularMicrostructureDetector detects regime
   - Only allows: Strong_Bull_Trend, Strong_Bear_Trend
   - Thresholds: momentum 50/100, vol 0.0055/0.0092
4. LegacyTFV2Strategy evaluates entry
   - LONG: fast_ema > slow_ema AND forecast > 0
   - SHORT: fast_ema < slow_ema AND forecast < 0
5. RiskManager calculates position size:
   - Risk: 2% of balance (NOT 25%!)
   - Size = (Balance × 2%) / (ATR × StopMult)
   - Dynamic leverage: 2-10x based on volatility
6. Portfolio enforces ONE position max
7. Backtester handles exits:
   - Stop Loss: 2-3x ATR
   - Take Profit: ATR-based
   - Max hold time: configured hours

DATA LOADED:
- raw2/{symbol}_{date}.parquet → hourly microstructure
- resampled_l2/1h/{date}.parquet → OHLCV

KEY PARAMETERS:
- EMA periods: 20 (fast), 50 (slow)
- ATR period: 20
- Regime detector: granular_microstructure
- Position limit: 1
- Risk per trade: 2% (with ATR-based sizing)
- Stop multiplier: 2-3x ATR
- Effective leverage: 2-10x (dynamic)
```

## Modern Architecture (Failed Attempt)
```
ATTEMPTED FLOW:
1. RobustDataLoader loads enhanced_hourly/ (109 features)
2. ModernSignalEngine calculates signals
3. EnhancedRegimeDetector wraps legacy + quality scoring
   - Allowed Weak trends (mistake!)
   - Quality threshold: 0.45 (too low!)
4. ModernTFV3Strategy evaluates
5. Missing: Position limits, exit logic

PROBLEMS:
- Scale mismatches (momentum 50→10, spread decimal→bp)
- Allowed low-quality trades (Weak trends + 0.45 threshold)
- No position management (allows multiple positions)
- No sophisticated exit logic
```

## Critical Missing Logic
1. **Position Management**: Legacy allows ONLY 1 position
2. **Exit Logic**: SL/TP/Time handled in backtester, not strategy
3. **25% Risk**: Legacy uses 25% risk, not 2%
4. **Regime Selection**: Only Strong trends, not Weak

## Key Commands  
```bash
# Legacy system (180 trades/year, +215% ROI - our target)  
python3 -m hyperliquid_bot.backtester.run_backtest

# Failed Modern attempt (82 trades/year, +6.22% ROI)
python3 test_enhanced_full_2024.py

# Future: Forensic analysis scripts
python3 run_legacy_auditor.py          # Generate golden truth log
python3 verify_replica_matches.py      # Compare replica to legacy
```

## Proposed Architecture: Logic Capsule Pattern
```python
# hyperliquid_bot/logic/legacy_rules.py
class LegacyLogicCapsule:
    """Encapsulates exact legacy TFV3 behavior"""
    def check_entry_filters(self, signal, positions) -> bool
    def get_exit_decision(self, position, market_data) -> str

# Clean injection into modern engine
engine = RobustBacktestEngine(
    signal_source=ModernSignalEngine(...),
    logic_rules=LegacyLogicCapsule(config)  # Swappable
)
```

## Key Files to Analyze
- `hyperliquid_bot/legacy/exit_handler.py` - Exit logic we ignored
- `hyperliquid_bot/legacy/tfv3_strategy.py` - Core strategy logic
- `hyperliquid_bot/legacy/portfolio.py` - Position management rules

## Failed Enhanced Detector Analysis
Our Enhanced Detector failed because:
1. **Allowed Weak trends**: Diluted signal quality
2. **Lowered quality threshold**: 0.70 → 0.45 let in bad trades
3. **Missing exit logic**: No sophisticated profit taking
4. **Missing position limits**: Multiple positions destroyed returns

## Data Architecture
- Legacy: `raw2/` hourly files (37 features)
- Modern: `features_1s/` 1-second files (109 columns, 20-level order book)
- Enhanced: `enhanced_hourly/1h/` - Pre-resampled with microstructure
- Audit Logs: `legacy_audit_logs/` - Golden truth for replication

## Deep Book Features (Future Enhancement)
After achieving legacy replication, we can explore:
- **DBPS**: Deep Book Pressure Score (whale detection)
- **LSE**: Liquidity Shape Entropy (regime changes)
- **OBVG**: Order Book Velocity Gradient (momentum)
- **MMF**: Market Maker Footprint (algo vs organic)