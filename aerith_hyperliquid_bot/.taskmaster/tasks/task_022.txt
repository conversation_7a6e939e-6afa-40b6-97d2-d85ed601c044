# Task ID: 22
# Title: Create Unified Data Processing Pipeline for L2 Snapshots
# Status: done
# Dependencies: 4
# Priority: high
# Description: Develop a Python script that processes raw JSON L2 snapshots into standardized multi-timeframe data with optimizations for backtesting, including streaming JSON processing, smart 1Hz sampling, and OHLCV generation.
# Details:
1. Create a Python script (process_raw_json_to_parquet.py) that implements:
   - Streaming JSON processing to handle large files efficiently
   - Smart 1Hz L2 order book sampling algorithm
   - OHLCV generation at multiple timeframes (1m, 5m, 1h)
   - Integer price storage for compression and performance
   - Standardized column naming convention (spread_bps, obi_5, volume_imbalance)

2. Implement the following components:
   - JSON streaming parser using ijson or similar library
   - Order book state management class to track bid/ask changes
   - Sampling logic to extract meaningful 1Hz snapshots
   - Timeframe aggregation functions for OHLCV calculation
   - Compression optimization using integer price representation
   - Parquet output with appropriate compression settings

3. Design considerations:
   - Ensure memory efficiency for processing large historical datasets
   - Implement proper error handling for malformed JSON data
   - Add progress tracking for long-running processing jobs
   - Include configurable parameters for sampling frequency and timeframes
   - Document the data transformation pipeline clearly in code comments
   - Ensure compatibility with the field mapping defined in Task 5

4. Create a companion test script (test_data_processing.py) that:
   - Validates the correctness of the processing pipeline
   - Verifies data integrity across transformations
   - Tests performance with representative datasets
   - Ensures compatibility with downstream components

# Test Strategy:
1. Unit Testing:
   - Test each component of the pipeline independently
   - Verify correct JSON parsing with sample data
   - Test order book state management with known state changes
   - Validate OHLCV calculation against manually calculated values
   - Test integer price compression/decompression for accuracy

2. Integration Testing:
   - Process a known sample dataset and compare results to expected output
   - Verify all required fields are present in the output
   - Check that standardized column names are used consistently
   - Validate that all specified timeframes are generated correctly

3. Performance Testing:
   - Measure processing time and memory usage with various dataset sizes
   - Compare file sizes before and after processing to verify compression
   - Test with realistic historical data volumes to ensure scalability

4. Validation Testing:
   - Compare processed data against original source for accuracy
   - Verify that 1Hz sampling preserves critical market events
   - Ensure OHLCV aggregation correctly represents the underlying data
   - Validate that the output conforms to the unified schema design

5. Regression Testing:
   - Run the baseline validation script from Task 1 to ensure compatibility
   - Verify that processed data works with existing analysis tools
