Product Requirements Document: Modern System Surgical Reconstruction
================================================================

## Overview
Fix the modern trading system by taking a systematic approach: analyze both legacy and modern systems, identify robust solutions vs patches, and rebuild components correctly for production live trading.

## Background
- Legacy system achieves +215% ROI but is fragile patchwork
- Modern system has good ideas (enhanced regime detection) but broken implementation
- Need production-ready system that won't collapse in live trading

## Goals
- Build robust data handling that gracefully handles missing data
- Integrate proven regime detection with modern enhancements
- Create regime-aware TF-v3 strategy
- Ensure system is ready for live trading with 700ms latency
- No more patches - only proper engineering solutions

## Requirements

### Phase 1: Forensic Analysis (3 days)
- Analyze legacy data loading approach
- Analyze modern data loading approach
- Identify robust patterns vs patches
- Document correct implementation approach
- Create comparison matrix of approaches

### Phase 2: Data Foundation (Week 1)
- Implement robust data loader with gap handling
- <PERSON>le missing dates from Hyperliquid downtime
- Create proper warmup period calculation
- Add comprehensive data validation
- Ensure graceful degradation

### Phase 3: Regime Integration (Week 2)
- Port working regime detector correctly
- Implement 60-second continuous updates
- Add proper state management
- Create strongly-typed regime states
- Integrate with TF-v3 strategy

### Phase 4: System Robustness (Week 3)
- Add comprehensive error handling
- Implement performance monitoring
- Create full test suite
- Document all decisions
- Prepare for live trading

## Technical Requirements
- Python 3.11+
- Pandas/NumPy for data processing
- Existing Hyperliquid data formats
- Compatibility with legacy detector
- Sub-second processing for live trading

## Success Criteria
- Zero crashes on missing data
- Proper handling of exchange downtime
- 10x faster backtesting
- 90% test coverage
- Ready for live trading deployment

## Non-Goals
- Not rebuilding from scratch
- Not over-engineering for HFT
- Not changing core strategy logic
- Not affecting legacy system

## Timeline
- Week 1: Foundation fixes
- Week 2: Core integration
- Week 3: Robustness and testing
- Total: 3 weeks

## Testing Requirements
- Unit tests for all components
- Integration tests with real data
- Edge case testing (gaps, crashes)
- Performance benchmarks
- Live trading simulation