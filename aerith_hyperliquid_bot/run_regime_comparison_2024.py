#!/usr/bin/env python3
"""
Run Full 2024 Regime Detection Comparison
=========================================
Compare regime detection between legacy and modern systems.
Runs full 2024 backtest for both and analyzes differences.
"""

import subprocess
import sys
import json
from pathlib import Path
from datetime import datetime
import yaml

def run_legacy_2024():
    """Run legacy system for full 2024."""
    print("=" * 80)
    print("RUNNING LEGACY SYSTEM - FULL 2024")
    print("=" * 80)
    print()
    
    # Create override config for 2024
    config_override = {
        "backtest": {
            "period_preset": "2024"
        }
    }
    
    override_file = Path("temp_legacy_2024.yaml")
    with open(override_file, 'w') as f:
        yaml.dump(config_override, f)
    
    # Run legacy backtest
    cmd = [
        sys.executable,
        "-m", "hyperliquid_bot.backtester.run_backtest",
        "--override", str(override_file)
    ]
    
    log_file = f"/Users/<USER>/Desktop/trading_bot_/logs/legacy_regime_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    print(f"Starting legacy backtest...")
    print(f"Log file: {log_file}")
    print("-" * 80)
    
    with open(log_file, 'w') as f:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        for line in process.stdout:
            f.write(line)
            f.flush()
            
            # Show key progress
            if "Total Trades:" in line or "Total Return:" in line or "ERROR" in line:
                print(line.rstrip())
        
        process.wait()
    
    # Clean up
    override_file.unlink(missing_ok=True)
    
    if process.returncode != 0:
        print("\nLegacy backtest failed!")
        return None, None
    
    # Find the output files
    import glob
    log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    
    # Get most recent trades file
    trades_files = sorted(glob.glob(str(log_dir / "backtest_trades_*.json")), 
                         key=lambda x: Path(x).stat().st_mtime, reverse=True)
    
    if not trades_files:
        print("No trades file found!")
        return None, None
    
    latest_trades = trades_files[0]
    print(f"\nFound legacy trades file: {latest_trades}")
    
    # Load trades data
    with open(latest_trades) as f:
        trades_data = json.load(f)
    
    return trades_data, log_file


def run_modern_2024():
    """Run modern system for full 2024."""
    print("\n" + "=" * 80)
    print("RUNNING MODERN SYSTEM - FULL 2024")
    print("=" * 80)
    print()
    
    cmd = [
        sys.executable,
        "scripts/run_modern_backtest.py",
        "--start-date", "2024-01-01",
        "--end-date", "2024-12-31",
        "--override", "configs/overrides/modern_system_v2_complete.yaml"
    ]
    
    print(f"Starting modern backtest...")
    print("-" * 80)
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print("\nModern backtest failed!")
        print(result.stderr)
        return None, None
    
    # Parse output to find results file
    output_file = None
    for line in result.stdout.split('\n'):
        if "Results saved to:" in line:
            output_file = line.split("Results saved to:")[-1].strip()
            break
    
    if not output_file:
        print("Could not find modern output file!")
        return None, None
    
    print(f"\nFound modern results file: {output_file}")
    
    # Load results
    with open(output_file) as f:
        results = json.load(f)
    
    return results, output_file


def analyze_regime_patterns(legacy_trades, modern_results):
    """Analyze and compare regime detection patterns."""
    print("\n" + "=" * 80)
    print("REGIME DETECTION ANALYSIS")
    print("=" * 80)
    
    analysis = {
        "legacy": {
            "total_trades": len(legacy_trades) if legacy_trades else 0,
            "trades_by_month": {},
            "entry_regimes": {},
            "avg_trade_duration": 0
        },
        "modern": {
            "total_trades": modern_results['performance']['total_trades'],
            "total_return": modern_results['performance']['total_return'],
            "win_rate": modern_results['performance']['win_rate'],
            "trades_by_month": {},
            "entry_regimes": {}
        },
        "comparison": {}
    }
    
    # Analyze legacy trades
    if legacy_trades:
        print(f"\nLegacy System Analysis:")
        print(f"  Total Trades: {len(legacy_trades)}")
        
        # Count trades by month and regime
        for trade in legacy_trades:
            # Month
            if 'timestamp' in trade:
                month = str(trade['timestamp'])[:7]
                analysis['legacy']['trades_by_month'][month] = \
                    analysis['legacy']['trades_by_month'].get(month, 0) + 1
            
            # Entry regime (may not be available in legacy)
            if 'entry_regime' in trade:
                regime = trade['entry_regime']
                analysis['legacy']['entry_regimes'][regime] = \
                    analysis['legacy']['entry_regimes'].get(regime, 0) + 1
        
        # Calculate total return from trades
        total_return = sum(trade.get('pnl', 0) for trade in legacy_trades)
        analysis['legacy']['total_return'] = total_return
        
        print(f"  Total Return: ${total_return:,.2f}")
        print(f"  Monthly Distribution: {json.dumps(analysis['legacy']['trades_by_month'], indent=2)}")
    
    # Analyze modern trades
    if modern_results and 'trades' in modern_results:
        print(f"\nModern System Analysis:")
        print(f"  Total Trades: {len(modern_results['trades'])}")
        print(f"  Total Return: {modern_results['performance']['total_return']:.2%}")
        
        # Count trades by month and regime
        for trade in modern_results['trades']:
            # Month
            if 'entry_time' in trade:
                month = str(trade['entry_time'])[:7]
                analysis['modern']['trades_by_month'][month] = \
                    analysis['modern']['trades_by_month'].get(month, 0) + 1
            
            # Entry regime
            if 'entry_regime' in trade:
                regime = trade['entry_regime']
                analysis['modern']['entry_regimes'][regime] = \
                    analysis['modern']['entry_regimes'].get(regime, 0) + 1
        
        print(f"  Monthly Distribution: {json.dumps(analysis['modern']['trades_by_month'], indent=2)}")
        print(f"  Entry Regimes: {json.dumps(analysis['modern']['entry_regimes'], indent=2)}")
    
    # Key comparisons
    analysis['comparison']['trade_frequency_ratio'] = (
        analysis['modern']['total_trades'] / analysis['legacy']['total_trades'] 
        if analysis['legacy']['total_trades'] > 0 else 0
    )
    
    print(f"\nKey Findings:")
    print(f"  Trade Frequency Ratio (Modern/Legacy): {analysis['comparison']['trade_frequency_ratio']:.2f}x")
    print(f"  Legacy: {analysis['legacy']['total_trades']} trades")
    print(f"  Modern: {analysis['modern']['total_trades']} trades")
    
    return analysis


def main():
    print("=" * 80)
    print("FULL 2024 REGIME DETECTION COMPARISON")
    print("=" * 80)
    print()
    print("This will run both legacy and modern systems for full 2024")
    print("and analyze regime detection differences.")
    print()
    
    # Run legacy system
    legacy_trades, legacy_log = run_legacy_2024()
    
    if not legacy_trades:
        print("\nFailed to get legacy results!")
        return 1
    
    # Run modern system
    modern_results, modern_file = run_modern_2024()
    
    if not modern_results:
        print("\nFailed to get modern results!")
        return 1
    
    # Analyze differences
    analysis = analyze_regime_patterns(legacy_trades, modern_results)
    
    # Save analysis
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    analysis_file = f"/Users/<USER>/Desktop/trading_bot_/logs/regime_comparison_analysis_{timestamp}.json"
    
    with open(analysis_file, 'w') as f:
        json.dump(analysis, f, indent=2, default=str)
    
    print(f"\n📊 Analysis saved to: {analysis_file}")
    print(f"📄 Legacy log: {legacy_log}")
    print(f"📄 Modern results: {modern_file}")
    
    # Create findings document
    findings = f"""# Regime Detection Comparison Findings

## Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Summary
- **Legacy System**: {analysis['legacy']['total_trades']} trades, ${analysis['legacy'].get('total_return', 0):,.2f} return
- **Modern System**: {analysis['modern']['total_trades']} trades, {analysis['modern']['total_return']:.2%} return
- **Trade Frequency Ratio**: {analysis['comparison']['trade_frequency_ratio']:.2f}x

## Monthly Trade Distribution

### Legacy System
{json.dumps(analysis['legacy']['trades_by_month'], indent=2)}

### Modern System  
{json.dumps(analysis['modern']['trades_by_month'], indent=2)}

## Entry Regime Analysis

### Legacy System
{json.dumps(analysis['legacy']['entry_regimes'], indent=2) if analysis['legacy']['entry_regimes'] else "No regime data available"}

### Modern System
{json.dumps(analysis['modern']['entry_regimes'], indent=2)}

## Key Observations
1. Modern system trades {analysis['comparison']['trade_frequency_ratio']:.1f}x more frequently than legacy
2. {"Modern system shows negative returns while legacy is positive" if analysis['modern']['total_return'] < 0 else "Both systems show positive returns"}
3. {"Entry regime data suggests different regime detection patterns" if analysis['modern']['entry_regimes'] else "Need to investigate regime detection differences"}

## Next Steps
1. Analyze why modern system enters trades more frequently
2. Compare regime detection thresholds and logic
3. Investigate entry/exit timing differences
4. Check position management and risk controls
"""
    
    findings_file = f"/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/guides/regime_comparison_findings_{timestamp}.md"
    with open(findings_file, 'w') as f:
        f.write(findings)
    
    print(f"\n📝 Findings document: {findings_file}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())