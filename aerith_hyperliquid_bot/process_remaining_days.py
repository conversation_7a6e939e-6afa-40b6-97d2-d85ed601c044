#!/usr/bin/env python3
"""
Process remaining days for Task R-112l
"""
import subprocess
import sys
from datetime import datetime, timedelta

def process_date_range(start_date, end_date):
    """Process ETL for date range"""
    current_date = datetime.strptime(start_date, '%Y-%m-%d')
    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
    
    processed = 0
    failed = 0
    
    while current_date <= end_date_obj:
        date_str = current_date.strftime('%Y-%m-%d')
        print(f"Processing {date_str}...")
        
        try:
            result = subprocess.run([
                'python3', 'tools/etl_l20_to_1s.py', 
                '--date', date_str, 
                '--overwrite'
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ {date_str} completed successfully")
                processed += 1
            else:
                print(f"❌ {date_str} failed: {result.stderr}")
                failed += 1
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {date_str} timed out")
            failed += 1
        except Exception as e:
            print(f"💥 {date_str} error: {e}")
            failed += 1
            
        current_date += timedelta(days=1)
    
    print(f"\nSummary: {processed} processed, {failed} failed")
    return processed, failed

if __name__ == "__main__":
    # Process remaining days (03-03 to 03-22)
    processed, failed = process_date_range('2025-03-03', '2025-03-22')
    
    if failed > 0:
        print(f"Some days failed. Check logs.")
        sys.exit(1)
    else:
        print("All days processed successfully!")
        sys.exit(0)
