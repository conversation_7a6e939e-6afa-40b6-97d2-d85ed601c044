#!/usr/bin/env python3
"""
Test Modern system with regime cache DISABLED to verify performance improvement.
This is the quick fix to test if hourly cached regimes are the root cause.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import yaml
from pathlib import Path
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine

def run_test():
    # Load configs
    with open('config.yaml', 'r') as f:
        base_config = yaml.safe_load(f)
    
    with open('configs/overrides/modern_system_v2_complete.yaml', 'r') as f:
        modern_override = yaml.safe_load(f)
    
    # Merge configs
    merged_config = {**base_config, **modern_override}
    
    # Convert dict to object with attributes
    class Config:
        def __init__(self, d):
            for k, v in d.items():
                if isinstance(v, dict):
                    setattr(self, k, Config(v))
                else:
                    setattr(self, k, v)
    
    config = Config(merged_config)
    
    # Test period: 1 week in January 2024
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 22)
    
    print("="*60)
    print("MODERN SYSTEM TEST - NO REGIME CACHE")
    print("="*60)
    print(f"Period: {start_date} to {end_date}")
    print("Expected: Different regime distribution than cached version")
    print("Expected: Better performance than +41.78% annual")
    print("-"*60)
    
    # Initialize engine WITHOUT cache
    print("\n🔧 Initializing Modern engine with use_regime_cache=False...")
    engine = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=False  # ← THE KEY CHANGE!
    )
    
    print("✅ Engine initialized without regime cache")
    print("   This will calculate regimes in real-time")
    print("   Should update every 60 seconds, not hourly")
    
    # Run backtest
    print("\n🚀 Running backtest...")
    results = engine.run()
    
    # Display results
    print("\n" + "="*60)
    print("RESULTS:")
    print("="*60)
    
    if results:
        print(f"Total Return: {results.get('total_return', 0):.2%}")
        print(f"Total Trades: {results.get('total_trades', 0)}")
        print(f"Win Rate: {results.get('win_rate', 0):.1%}")
        print(f"Sharpe Ratio: {results.get('sharpe_ratio', 0):.2f}")
        
        # Check regime statistics if available
        if 'regime_stats' in results:
            print("\nRegime Distribution:")
            for regime, count in results['regime_stats'].items():
                print(f"  {regime}: {count}")
        
        # Save results
        import json
        with open('test_modern_no_cache_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print("\nResults saved to test_modern_no_cache_results.json")
        
        print("\n🎯 COMPARISON:")
        print("Cached version (annual): +41.78% ROI")
        print(f"No-cache version (1 week): {results.get('total_return', 0):.2%}")
        print("\nIf performance improved significantly, the cache was the problem!")
    else:
        print("❌ No results returned - check for errors above")

if __name__ == "__main__":
    try:
        run_test()
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("\nThis might happen if:")
        print("1. Regime detector isn't initialized properly")
        print("2. Data files aren't available") 
        print("3. The engine expects cached regimes")
        print("\nCheck the error message above for details.")