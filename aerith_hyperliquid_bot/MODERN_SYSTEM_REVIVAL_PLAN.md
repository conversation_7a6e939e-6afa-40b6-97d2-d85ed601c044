# Modern System Revival Plan

## Current Status

The modern system (continuous_gms + tf_v3) is currently generating 0 trades. This document outlines a systematic approach to diagnose and fix the issues.

## Quick Diagnostic

```bash
# Run modern system test
python3 -m hyperliquid_bot.backtester.run_backtest --override configs/overrides/modern_system.yaml
```

Current result: 0 trades, suggesting either:
1. Regime detection is too restrictive (always returning CHOP)
2. Signal generation is failing
3. Risk management is blocking all trades
4. Data loading issues with features_1s/

## Proposed Investigation Steps

### Phase 1: Data Validation (Priority: HIGH)

1. **Verify features_1s/ data exists and is readable**
   ```python
   # Check if modern data loader can access files
   # Verify field names (volume_imbalance, not imbalance)
   # Confirm data quality and completeness
   ```

2. **Compare data schemas**
   - Legacy uses: raw2/ with 37 features
   - Modern uses: features_1s/ with different schema
   - Ensure all required fields are present

### Phase 2: Regime Detection Analysis (Priority: HIGH)

1. **Check regime detector thresholds**
   ```yaml
   # Current modern thresholds (might be too aggressive)
   gms_vol_high_thresh: 0.03    # 3% vs legacy 0.92%
   gms_vol_low_thresh: 0.01     # 1% vs legacy 0.55%
   gms_mom_strong_thresh: 2.5   # vs legacy 100.0
   gms_mom_weak_thresh: 0.5     # vs legacy 50.0
   ```

2. **Debug regime states**
   - Add logging to see what regimes are detected
   - Check if it's always returning CHOP/non-tradeable states
   - Verify state mapping is working correctly

### Phase 3: Strategy Signal Generation (Priority: MEDIUM)

1. **TF-v3 Strategy Configuration**
   - Check if signals are being generated
   - Verify entry/exit logic
   - Test filter conditions (OBI, funding)

2. **Risk Parameters**
   ```yaml
   risk_frac: 0.02  # Only 2% vs legacy 25%
   ```
   This massive difference might be preventing trades

### Phase 4: Integration Testing (Priority: LOW)

1. **Component Compatibility**
   - Ensure modern detector works with modern strategy
   - Check data flow between components
   - Verify registry is finding correct implementations

## Recommended Approach

### Step 1: Start Simple
Create a minimal test configuration that should definitely generate trades:
```yaml
# Temporary test config
regime:
  detector_type: "continuous_gms"
  gms_vol_high_thresh: 0.02    # Less aggressive
  gms_vol_low_thresh: 0.008   # Less aggressive
  gms_mom_strong_thresh: 5.0   # More reasonable
  gms_mom_weak_thresh: 1.0     # More reasonable

tf_v3:
  risk_frac: 0.10  # 10% - more reasonable than 2%
```

### Step 2: Progressive Debugging
1. Add detailed logging to modern detector
2. Log every regime state change
3. Log when signals are generated/rejected
4. Track why trades are not executed

### Step 3: Incremental Fixes
1. Fix data loading issues first
2. Then adjust thresholds to get some trades
3. Finally optimize for performance

## Debug Script Template

```python
# debug_modern_system.py
import logging
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.data_loader import ModernDataLoader
from hyperliquid_bot.modern.detector import ModernContinuousGMSDetector

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)

# Load config
config = load_config('configs/base.yaml')
config.regime.detector_type = 'continuous_gms'

# Test data loading
loader = ModernDataLoader(config)
print(f"Can load data: {loader.test_data_access()}")

# Test regime detection  
detector = ModernContinuousGMSDetector(config)
# ... test with sample data
```

## Success Metrics

### Minimum Viable System
- Generate at least 50 trades in 2024 backtest
- Achieve positive ROI (even if lower than legacy)
- No critical errors during execution

### Target Performance  
- 100-200 trades (similar to legacy volume)
- ROI > 100% 
- Sharpe Ratio > 2.0
- Demonstrate unique advantages over legacy

## Timeline Estimate

1. **Data Validation**: 1-2 hours
2. **Threshold Adjustment**: 2-3 hours  
3. **Full Debug & Fix**: 4-6 hours
4. **Optimization**: 2-4 hours

**Total: 1-2 days of focused work**

## Next Steps

1. Run the diagnostic script above
2. Identify the primary blocker (data, regimes, or signals)
3. Apply targeted fixes
4. Iterate until trades are generated
5. Optimize for performance

The modern system has potential advantages:
- 60-second updates vs hourly
- More sophisticated features
- Adaptive thresholds
- Enhanced risk management

Once running, it could significantly outperform the legacy system.