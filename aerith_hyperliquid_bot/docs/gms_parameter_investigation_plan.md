# GMS Parameter Investigation Plan

This document outlines the plan to investigate the functionality of the following Granular Microstructure (GMS) parameters defined in `configs/base.yaml` and potentially overridden:

1.  `gms_spread_percentile_gate`
2.  `gms_depth_slope_thin_limit`
3.  `gms_depth_skew_thresh`
4.  `gms_spread_trend_lookback`
5.  `gms_adaptive_obi_base`

## Objective

Determine if these parameters are correctly implemented and functioning as intended within the `GranularMicrostructureRegimeDetector` and related signal calculations. Identify any discrepancies in naming, loading, or usage across the codebase.

## Investigation Steps (Per Parameter)

For each parameter listed above:

1.  **Configuration Loading (`settings.py`):**
    *   Verify the parameter is defined correctly in the `RegimeSettings` model.
    *   Check default values and type hints.
    *   Confirm it's loaded correctly from YAML files (base and overrides like `4h_setC.yaml`).
2.  **Detector Initialization (`detector.py`):**
    *   Verify the `GranularMicrostructureRegimeDetector.__init__` method correctly retrieves the parameter value from the loaded config.
    *   Check how the retrieved value is stored (e.g., `self.spread_percentile_gate`).
    *   Confirm the logging statement accurately reflects the loaded value.
3.  **Signal Requirement (`detector.py`):**
    *   Check if the `GranularMicrostructureRegimeDetector.required_signals` property correctly adds the necessary input signal(s) to the `signals_needed` list *only when the parameter is active* (e.g., `raw_spread_percentile` is needed only if `gms_spread_percentile_gate > 0`).
4.  **Signal Calculation (`calculator.py`):**
    *   Trace the required input signal(s) identified in step 3.
    *   Verify that `SignalEngine.calculate_all_signals` correctly calculates these input signals *only when necessary* based on the corresponding parameter being active in the config.
    *   Check for correct window sizes, column names, and handling of potential NaNs or errors during calculation (e.g., `raw_spread_percentile` calculation).
    *   *Note:* `depth_slope` and `depth_skew` are currently placeholders in `calculator.py`. Confirm this is still the case.
5.  **Detector Logic (`detector.py`):**
    *   In `GranularMicrostructureRegimeDetector.get_regime`, verify the parameter's value is used correctly in conditional logic.
    *   Check the priority of checks (e.g., does `depth_slope` check happen before `spread_percentile`?).
    *   Confirm the logic correctly handles cases where the parameter is disabled (e.g., `None` or `0` or `-inf`).
    *   Ensure comparisons are appropriate (e.g., `>` vs `>=`).
    *   Verify fallback logic if the parameter is active but the required signal is missing/NaN.
6.  **Naming Consistency:**
    *   Ensure consistent naming conventions between `base.yaml`, `settings.py`, `detector.py`, and `calculator.py` (e.g., `gms_spread_percentile_gate` vs `spread_percentile_gate`). Document any inconsistencies.

## Specific Parameter Notes

*   **`gms_spread_percentile_gate`:** Check fixed calculation in `calculator.py` (uses percentile rank) and usage in `detector.py` (`market_condition = 'WIDE_SPREAD'`).
*   **`gms_depth_slope_thin_limit`:** Check implemented (approximated) calculation in `calculator.py`. Check usage in `detector.py` (`market_condition = 'THIN_LIQUIDITY'`).
*   **`gms_depth_skew_thresh`:** Check implemented (approximated) calculation in `calculator.py`. Check usage in `detector.py`.
*   **`gms_spread_trend_lookback`:** Check refined calculation in `calculator.py` (uses `diff` on smoothed `spread_mean`). Check usage in `detector.py`.
*   **`gms_adaptive_obi_base`:** Check component calculation (`vol_long_term`, `vol_short_term`) in `calculator.py`. Check usage in `detector.py` (`obi_condition = 'STRONG'`).

## Deliverables (Subtask 1)

1.  This plan file created at `aerith_hyperliquid_bot/docs/gms_parameter_investigation_plan.md`.
2.  A summary report detailing the findings for each parameter based on the investigation steps above, highlighting any discrepancies, potential issues, or confirmations of correct implementation.

## Next Steps (Post Subtask 1)

1.  Review the findings report.
2.  If issues are found, create a follow-up subtask to implement necessary code fixes (e.g., correcting logic, renaming variables, implementing non-placeholder calculations if feasible).
3.  Provide guidance on testing each parameter individually using configuration overrides.

## Implementation Status

The investigation (Subtask 1) and initial implementation/fixes (Subtask 2) are complete as of May 1, 2025. All five GMS parameters now have functional logic implemented in `calculator.py` and `detector.py`, replacing previous placeholders or incorrect logic. Approximations were used for depth-related parameters. Refer to `gms_parameter_implementation_log.md` for detailed implementation notes and fixes.