# Adaptive Thresholds Performance Bottleneck Analysis

**Date**: 2025-01-23  
**System**: Modern System (Continuous GMS + TF-v3)  
**Issue**: Major performance bottleneck in `_prime_adaptive_thresholds`  

## Executive Summary

The Modern System's adaptive thresholds feature (`auto_thresholds: true`) creates a severe performance bottleneck, taking **629.34 seconds** (over 10 minutes) to prime the thresholds during initialization. When disabled (`auto_thresholds: false`), the detector only maps "uncertain" regime, making it ineffective.

## Profiling Results

### Test Configuration
- **Date Range**: 2025-03-02 to 2025-03-05 (4 days)
- **System**: Continuous GMS + TF-v3 Strategy  
- **Total Execution Time**: ~633 seconds
- **Major Bottleneck**: `_prime_adaptive_thresholds` = 629.34 seconds (99.4% of total time)

### Top Performance Bottlenecks

| Function | File | Total Time | Cumulative Time | % of Total |
|----------|------|------------|-----------------|------------|
| `_prime_adaptive_thresholds` | gms_detector.py | 629.34s | 629.34s | 99.4% |
| `percentile` | numpy | 625.12s | 625.12s | 98.7% |
| `_load_historical_features` | gms_detector.py | 2.66s | 2.66s | 0.4% |
| `_integrate_microstructure_features` | handler.py | 1.23s | 1.23s | 0.2% |

### Performance Comparison

| Configuration | Sharpe Ratio | ROI | Max Drawdown | Trade Count | Execution Time |
|---------------|--------------|-----|--------------|-------------|----------------|
| **Modern System** (auto_thresholds: true) | 0.79 | 5.87% | 10.31% | 32 | **633 seconds** |
| **Legacy System** (granular + TF-v2) | 1.29 | 1.58% | 6.41% | 12 | ~15 seconds |

## Root Cause Analysis

### 1. Priming Process Bottleneck

The `_prime_adaptive_thresholds` method performs the following expensive operations:

```python
# Load 24 hours of 1-second feature data (86,400 rows)
feature_data = self._load_historical_features(priming_start, priming_end)

# For each row, update adaptive thresholds
for _, row in feature_data.iterrows():
    vol_metric = row.get(self.ATR_PCT_COL)
    if pd.notna(vol_metric):
        self.adaptive_vol_threshold.update(vol_metric)  # Expensive!
    
    mom_metric = row.get('ma_slope_ema_30s')
    if pd.notna(mom_metric):
        self.adaptive_mom_threshold.update(abs(mom_metric))  # Expensive!
```

### 2. Adaptive Threshold Update Bottleneck

Each `update()` call in `AdaptiveThreshold` performs:

```python
def update(self, value: float) -> Tuple[Optional[float], Optional[float]]:
    # Convert buffer to numpy array
    arr = np.fromiter(self.buffer, dtype=float)
    valid_arr = arr[~np.isnan(arr)]
    
    # EXPENSIVE: Compute percentiles on entire buffer
    low_thresh = np.percentile(valid_arr, self.low_pct * 100)   # O(n log n)
    high_thresh = np.percentile(valid_arr, self.high_pct * 100) # O(n log n)
    
    self.buffer.append(value)
    return low_thresh, high_thresh
```

### 3. Computational Complexity

- **Priming Data**: 86,400 rows (24 hours × 3,600 seconds)
- **Updates per Row**: 2 (volatility + momentum)
- **Total Updates**: 172,800 adaptive threshold updates
- **Percentile Complexity**: O(n log n) per update
- **Total Complexity**: O(n² log n) where n = 86,400

### 4. Why Fixed Thresholds Fail

When `auto_thresholds: false`, the detector uses static thresholds that may not be calibrated for current market conditions:

```yaml
# Fixed thresholds from base.yaml
gms_vol_high_thresh: 0.03
gms_vol_low_thresh: 0.01
gms_mom_strong_thresh: 2.5
gms_mom_weak_thresh: 0.5
```

These static values may not match the actual data distribution, causing the detector to classify most periods as "uncertain."

## Optimization Solutions

### Solution 1: Optimized Percentile Calculation ⭐ **RECOMMENDED**

**Approach**: Use incremental percentile estimation instead of full recalculation.

```python
class OptimizedAdaptiveThreshold:
    def __init__(self, low_pct: float, high_pct: float, window_len: int):
        self.low_pct = low_pct
        self.high_pct = high_pct
        self.window_len = window_len
        self.buffer = deque(maxlen=window_len)
        
        # Pre-sorted array for faster percentile calculation
        self.sorted_buffer = []
        
    def update(self, value: float) -> Tuple[Optional[float], Optional[float]]:
        if len(self.buffer) == 0:
            self.buffer.append(value)
            self.sorted_buffer = [value]
            return None, None
            
        # Use binary search for insertion (O(log n) instead of O(n log n))
        import bisect
        
        # Remove oldest value if buffer is full
        if len(self.buffer) == self.window_len:
            old_value = self.buffer[0]
            old_idx = bisect.bisect_left(self.sorted_buffer, old_value)
            self.sorted_buffer.pop(old_idx)
            
        # Insert new value in sorted position
        bisect.insort(self.sorted_buffer, value)
        self.buffer.append(value)
        
        # Calculate percentiles from sorted array (O(1))
        n = len(self.sorted_buffer)
        low_idx = int(self.low_pct * n)
        high_idx = int(self.high_pct * n)
        
        return self.sorted_buffer[low_idx], self.sorted_buffer[high_idx]
```

**Performance Gain**: O(n log n) → O(log n) per update  
**Expected Speedup**: 100-1000x faster

### Solution 2: Reduced Priming Window

**Approach**: Use shorter priming period with higher sampling frequency.

```yaml
gms:
  auto_thresholds: true
  priming_hours: 4              # Reduce from 24 to 4 hours
  percentile_window_sec: 14400  # 4-hour window instead of 24-hour
  min_history_rows: 1000        # Reduce minimum history requirement
```

**Performance Gain**: 6x faster (4 hours vs 24 hours)  
**Trade-off**: Less historical context for threshold calculation

### Solution 3: Sampling-Based Priming

**Approach**: Sample every Nth row instead of processing all data.

```python
def _prime_adaptive_thresholds_sampled(self, priming_hours: int, sample_rate: int = 60):
    """Prime with sampled data (e.g., every 60 seconds instead of every second)."""
    feature_data = self._load_historical_features(priming_start, priming_end)
    
    # Sample every sample_rate rows (e.g., every 60 seconds)
    sampled_data = feature_data.iloc[::sample_rate]
    
    for _, row in sampled_data.iterrows():
        # Process sampled rows...
```

**Performance Gain**: 60x faster (1-minute sampling)  
**Trade-off**: Lower resolution threshold calculation

### Solution 4: Pre-computed Threshold Cache

**Approach**: Pre-compute and cache thresholds for common market periods.

```python
class ThresholdCache:
    def __init__(self):
        self.cache = {}
        
    def get_thresholds(self, date: datetime, market_session: str) -> dict:
        """Get cached thresholds for specific date/session."""
        cache_key = f"{date.strftime('%Y-%m-%d')}_{market_session}"
        return self.cache.get(cache_key)
        
    def compute_and_cache_thresholds(self, date: datetime):
        """Pre-compute thresholds for entire day."""
        # Batch process entire day's data
        # Store results in cache
```

**Performance Gain**: Near-instant threshold lookup  
**Trade-off**: Requires pre-processing step

### Solution 5: Hybrid Approach

**Approach**: Combine fast initialization with gradual adaptation.

```python
def _initialize_adaptive_thresholds_hybrid(self):
    """Initialize with fast static thresholds, then adapt gradually."""
    
    # Start with calibrated static thresholds
    self.adaptive_vol_threshold.set_initial_thresholds(0.007, 0.012)
    self.adaptive_mom_threshold.set_initial_thresholds(0.001, 0.01)
    
    # Gradually adapt using recent data only
    recent_data = self._load_recent_features(hours=1)  # Only 1 hour
    self._prime_with_recent_data(recent_data)
```

**Performance Gain**: 24x faster (1 hour vs 24 hours)  
**Trade-off**: Initial period uses less optimal thresholds

## Implementation Recommendations

### Phase 1: Quick Fix (Immediate)
1. **Reduce priming window** to 4 hours (`priming_hours: 4`)
2. **Increase sampling rate** to 60 seconds (`sample_rate: 60`)
3. **Lower minimum history** requirement (`min_history_rows: 1000`)

**Expected Result**: 360x speedup (24 hours → 4 hours, 60x sampling)

### Phase 2: Optimization (Short-term)
1. **Implement optimized percentile calculation** (Solution 1)
2. **Add threshold caching** for repeated backtests
3. **Benchmark and tune** parameters

**Expected Result**: Additional 100-1000x speedup

### Phase 3: Advanced (Long-term)
1. **Pre-compute threshold database** for historical periods
2. **Implement real-time adaptation** without priming
3. **Add threshold quality metrics** and validation

## Configuration Examples

### Quick Fix Configuration
```yaml
gms:
  auto_thresholds: true
  priming_hours: 4              # Reduced from 24
  percentile_window_sec: 14400  # 4-hour window
  vol_low_pct: 0.15
  vol_high_pct: 0.50
  mom_low_pct: 0.15
  mom_high_pct: 0.50
  min_history_rows: 1000        # Reduced from 10000
  sample_rate: 60               # New: sample every 60 seconds
```

### Optimized Configuration
```yaml
gms:
  auto_thresholds: true
  priming_hours: 2              # Further reduced
  percentile_window_sec: 7200   # 2-hour window
  vol_low_pct: 0.20             # Slightly higher for stability
  vol_high_pct: 0.80            # Slightly higher for stability
  mom_low_pct: 0.20
  mom_high_pct: 0.80
  min_history_rows: 500
  use_optimized_percentiles: true  # New: use optimized algorithm
```

### Fallback Configuration (If Optimization Fails)
```yaml
gms:
  auto_thresholds: false        # Disable adaptive thresholds
  
regime:
  continuous_gms:
    # Use calibrated fixed thresholds from March 2025 data
    gms_vol_low_thresh: 0.007211    # 33rd percentile
    gms_vol_high_thresh: 0.011761   # 67th percentile
    gms_mom_weak_thresh: 0.001      # Calibrated for low momentum
    gms_mom_strong_thresh: 0.01     # Calibrated for low momentum
```

## Risk Assessment

### Performance Risks
- **Current State**: 10+ minute initialization time makes system impractical
- **Quick Fix**: May reduce threshold quality but maintains functionality
- **Optimization**: Requires code changes but offers best long-term solution

### Trading Performance Risks
- **Adaptive Thresholds**: Better market adaptation but complex
- **Fixed Thresholds**: Simpler but may not adapt to changing conditions
- **Hybrid Approach**: Balanced risk/reward profile

## Monitoring and Validation

### Performance Metrics
1. **Initialization Time**: Target < 30 seconds
2. **Threshold Quality**: Compare adaptive vs fixed performance
3. **Memory Usage**: Monitor buffer sizes and cache usage

### Trading Metrics
1. **Regime Classification Accuracy**: % of non-uncertain classifications
2. **Sharpe Ratio**: Risk-adjusted performance
3. **Trade Frequency**: Ensure sufficient trading opportunities

## Conclusion

The adaptive thresholds bottleneck is a critical performance issue that makes the Modern System impractical for production use. The recommended approach is:

1. **Immediate**: Implement quick fixes (Phase 1) to reduce initialization time by 360x
2. **Short-term**: Implement optimized percentile calculation for 100-1000x additional speedup
3. **Long-term**: Build threshold caching and pre-computation infrastructure

The goal is to maintain the superior trading performance of the Modern System (higher trade frequency, absolute returns) while achieving initialization times comparable to the Legacy System (< 30 seconds).

**Next Steps**:
1. Implement Phase 1 quick fixes
2. Benchmark performance improvements
3. Validate trading performance remains acceptable
4. Proceed with Phase 2 optimizations

---

**Performance Target**: Reduce initialization time from 629 seconds to < 30 seconds while maintaining trading performance within 10% of current levels. 