# Percentile-Based Volatility Thresholds Implementation

## Overview

This document describes the implementation of percentile-based volatility thresholds in the Granular Microstructure (GMS) regime detector. This feature allows the detector to dynamically adjust volatility thresholds based on historical volatility distribution rather than using fixed values.

## Changes Made

### 1. Configuration Updates

Added a new configuration parameter in `base.yaml`:

```yaml
gms_vol_thresh_mode: 'fixed'  # ('fixed' or 'percentile')
```

This parameter controls whether the GMS detector uses fixed thresholds or percentile-based thresholds for volatility detection.

The existing percentile placeholders are now used when in 'percentile' mode:
```yaml
# Percentile placeholders (now active when gms_vol_thresh_mode = 'percentile')
gms_vol_high_percentile: 75
gms_vol_low_percentile: 25
```

### 2. GMS Detector Updates

Modified the `GranularMicrostructureRegimeDetector` class to support percentile-based volatility thresholds:

- Added loading of the volatility threshold mode in `__init__`
- Updated the `required_signals` method to include the `atr_percent_pctile` signal when in percentile mode
- Modified the volatility check logic in `get_regime` to use either fixed thresholds or percentile-based thresholds based on the mode
- Enhanced logging to include information about the volatility threshold mode

### 3. Signal Calculator Updates

Added calculation of the `atr_percent_pctile` signal in the `SignalEngine.calculate_all_signals` method:

```python
# Calculate percentile for volatility (ATR%)
if 'atr_percent' in signals_df.columns:
    signals_df['atr_percent_pctile'] = signals_df['atr_percent'].rolling(window=rolling_window).apply(
        lambda x: percentileofscore(x, x.iloc[-1]) / 100, raw=False
    )
```

This calculates the percentile rank of the current volatility (ATR%) value relative to its historical values within a rolling window.

## Usage

To use percentile-based volatility thresholds:

1. Set `gms_vol_thresh_mode: 'percentile'` in your configuration
2. Adjust `gms_vol_high_percentile` and `gms_vol_low_percentile` as needed (default: 75 and 25)

To revert to fixed thresholds:

1. Set `gms_vol_thresh_mode: 'fixed'` in your configuration
2. Adjust `gms_vol_high_thresh` and `gms_vol_low_thresh` as needed

## Benefits

- **Adaptability**: The bot adapts to changing market volatility conditions automatically
- **Market-Specific Calibration**: Different assets have different volatility profiles, and percentile-based thresholds automatically calibrate to each asset
- **Reduced Parameter Tuning**: Less need to manually tune volatility thresholds for different markets or time periods
- **Consistency**: Aligns with the existing percentile-based approach used for spread parameters

## Implementation Details

The implementation follows the same pattern used for spread parameters:

1. A mode parameter controls whether to use fixed or percentile-based thresholds
2. When in percentile mode, the detector requests the percentile signal
3. The signal calculator provides the percentile signal by calculating rolling percentiles
4. The detector uses the percentile values for regime determination

This approach maintains backward compatibility while adding new functionality.
