# Signal Calculator Improvements

## Date: May 9, 2025

This document logs the improvements made to the signal calculator module to enhance robustness and statistical validity.

## 1. Robust ATR Helper Function

Added a robust helper function for ATR calculations that handles different pandas-ta versions and edge cases.

### Implementation

```python
def _add_atr(df: pd.DataFrame, length: int, out_col: str) -> None:
    """
    Adds an ATR column to *df* with pandas_ta and renames it to *out_col*.
    Works with any pandas‑ta version (default column is 'ATR_{length}' or 'ATRr_{length}').
    """
    if length <= 1:
        df[out_col] = np.nan
        return

    df.ta.atr(length=length, append=True)
    
    # Try both possible column naming conventions
    possible_cols = [f'ATR_{length}', f'ATRr_{length}']
    found_col = None
    
    for col in possible_cols:
        if col in df.columns:
            found_col = col
            break
    
    if found_col:
        df.rename(columns={found_col: out_col}, inplace=True)
        logger.debug(f"ATR column '{found_col}' renamed to '{out_col}'")
    else:
        logger.error(f"ATR column not found after ta.atr(); checked {possible_cols}")
        df[out_col] = np.nan
```

### Benefits

- Handles both possible column naming conventions from pandas-ta (`ATR_{length}` and `ATRr_{length}`)
- Properly handles edge cases (length ≤ 1)
- Provides clear error messages when ATR calculation fails
- Ensures consistent behavior across all ATR calculations
- Eliminates code duplication

### Refactored Calls

Replaced all manual ATR calculations with calls to the helper function:

- Main ATR: `_add_atr(signals_df, atr_period, 'atr')`
- Trend-Following ATR: `_add_atr(signals_df, tf_atr_period, 'atr_tf')`
- Mean-Reversion ATR: `_add_atr(signals_df, mr_atr_period, 'atr_mr')`
- Mean-Variance ATR: `_add_atr(signals_df, mv_atr_period, 'atr_mv')`
- GMS ATR-percent: `_add_atr(signals_df, atr_perc_p, atr_col_for_perc)`

## 2. Unbiased Rolling Percentile Ranks

Replaced the existing `rolling_percentile_rank` function with an improved version that:
1. Returns values in the 0-1 range (instead of 0-100)
2. Evaluates against history up to the previous bar to avoid look-ahead bias

### Implementation

```python
def rolling_percentile_rank(series: pd.Series, window: int, min_periods: int) -> pd.Series:
    """
    Rolling percentile rank in **0–1** range, evaluated against the
    *history up to the previous bar* to avoid look‑ahead bias.
    """
    if series.isnull().all():
        return pd.Series(np.nan, index=series.index)

    def _pct(arr):
        # need at least 2 valid points to compute a percentile
        clean = pd.Series(arr[:-1]).dropna()
        if len(clean) == 0:
            return np.nan
        return percentileofscore(clean, arr[-1]) / 100.0

    return (
        series
        .rolling(window=window, min_periods=min_periods)
        .apply(_pct, raw=False)
    )
```

### Benefits

- Returns percentile ranks in the 0-1 range for consistent scaling
- Avoids look-ahead bias by only using historical data points
- Provides more statistically sound percentile calculations
- Handles edge cases properly (insufficient data)

### Removed Duplicate Calculations

Removed a duplicate block that was recalculating percentile ranks using `percentileofscore()` to prevent inconsistencies in the scale of percentile values.

## Verification

The changes were verified by running a backtest, which confirmed:

1. `signals_df['atr'].notna().any()` is True (ATR values are being calculated correctly)
2. All percentile columns contain values in the [0, 1] range

## Code Formatting

The code was formatted using `black` to maintain style consistency.

## Impact

These improvements enhance the robustness and statistical validity of the signal calculator, leading to:

- More reliable ATR calculations across different pandas-ta versions
- Statistically sound percentile calculations without look-ahead bias
- Consistent scaling of percentile values (0-1 range)
- Better error handling and debugging information
- Cleaner, more maintainable code

## 3. Look-ahead Bias Fixes (May 10, 2025)

Fixed two instances of look-ahead bias in the signal calculator to ensure backtesting results accurately reflect real-time trading conditions.

### 3.1 Volatility Features Forward-Fill

Replaced backfill with forward-fill for volatility features to prevent future data leakage.

#### Before:
```python
# Handle potential NaNs from rolling calculations
signals_df["vol_long_term"].fillna(
    method="bfill", inplace=True
)  # Backfill to get value sooner
signals_df["vol_short_term"].fillna(
    method="bfill", inplace=True
)
```

#### After:
```python
# Handle potential NaNs from rolling calculations
signals_df["vol_long_term"].fillna(
    method="ffill", inplace=True
)  # avoid look-ahead bias: forward-fill instead of back-fill
signals_df["vol_short_term"].fillna(
    method="ffill", inplace=True
)
```

### 3.2 Unbiased ATR Percentile Calculation

Added a dedicated helper function for unbiased percentile calculation and modified the ATR percentile calculation to use it.

#### Added Helper Function:
```python
# === helper: percentile of history (unbiased) ===============================
def _pct_of_history(arr: pd.Series) -> float:
    """
    Percentile of the *last* value versus the *previous* observations (0–1).
    Returns NaN until ≥2 valid points exist.
    """
    clean = pd.Series(arr[:-1]).dropna()
    if clean.empty:
        return np.nan
    return percentileofscore(clean, arr.iloc[-1]) / 100.0
# =============================================================================
```

#### Before:
```python
signals_df["atr_percent_pctile"] = (
    signals_df["atr_percent"]
    .rolling(window=rolling_window)
    .apply(
        lambda x: percentileofscore(x, x.iloc[-1]) / 100,
        raw=False,
    )
)
```

#### After:
```python
signals_df['atr_percent_pctile'] = (
    signals_df['atr_percent']
    .rolling(window=rolling_window, min_periods=max(1, rolling_window // 2))
    .apply(_pct_of_history, raw=False)
)
```

### Benefits

- Eliminates look-ahead bias in volatility features by using forward-fill instead of backfill
- Provides a dedicated helper function for unbiased percentile calculation
- Ensures percentile calculations exclude the current value when determining rank
- Adds proper min_periods parameter to ensure sufficient data for calculation
- Makes backtesting results more accurately reflect real-time trading conditions

### Verification

The changes were verified by running assertions that confirmed:

1. Volatility features have valid values after forward-fill (no NaN propagation issues)
2. ATR percentile values are properly bounded between 0 and 1

## 4. Additional Look-ahead Bias Fixes and Monitoring (May 10, 2025)

Implemented additional fixes to address look-ahead bias in spread percentile calculations and added monitoring for depth feature health.

### 4.1 Spread Percentile Calculation Fix

Updated the Primary Spread Percentiles calculation to use the unbiased `rolling_percentile_rank` helper function instead of the built-in `.rank(pct=True)` method.

#### Before:
```python
signals_df["spread_mean_primary_pctile"] = (
    signals_df["spread_mean"]
    .rolling(window=window, min_periods=min_p)
    .rank(pct=True)
)

signals_df["spread_std_primary_pctile"] = (
    signals_df["spread_std"]
    .rolling(window=window, min_periods=min_p)
    .rank(pct=True)
)
```

#### After:
```python
signals_df["spread_mean_primary_pctile"] = rolling_percentile_rank(
    signals_df["spread_mean"], window, min_p)

signals_df["spread_std_primary_pctile"] = rolling_percentile_rank(
    signals_df["spread_std"], window, min_p)
```

### 4.2 Depth Feature Health Monitoring

Added logging to alert when depth features are entirely NaN, which could indicate missing or degenerate micro-depth features:

```python
# --- post‑check: alert if depth features are unusable -----------------------
if 'depth_slope' in signals_df.columns and signals_df['depth_slope'].isna().all():
    self.logger.warning("Depth Metrics: 'depth_slope' is entirely NaN – micro‑depth features missing or degenerate.")

if 'depth_skew' in signals_df.columns and signals_df['depth_skew'].isna().all():
    self.logger.warning("Depth Metrics: 'depth_skew' is entirely NaN – micro‑depth features missing or degenerate.")
# ---------------------------------------------------------------------------
```

### 4.3 Added Validation for Spread Percentiles

Added validation code near the end of the `calculate_all_signals()` method to verify that spread percentile values stay within the expected 0-1 range:

```python
# Validation for spread percentiles
_debug_cols = ['spread_mean_primary_pctile', 'spread_std_primary_pctile']
for _c in _debug_cols:
    if _c in signals_df.columns:
        assert (signals_df[_c].between(0, 1) | signals_df[_c].isna()).all(), f"{_c} out of 0–1 bounds"
```

### Benefits

- Ensures spread percentile calculations are look-ahead-bias-free by using the same unbiased helper function
- Provides early warning when depth features are missing or degenerate
- Validates that percentile values stay within the expected 0-1 range
- Improves consistency across all percentile calculations in the codebase
- Enhances debugging capabilities for micro-depth feature issues

### Verification

The changes were verified by running a backtest, which confirmed:

1. No assertion errors occurred, indicating spread percentile values are properly bounded between 0 and 1
2. The backtest completed successfully, showing that the changes maintain overall system stability

## 5. Signal Calculator Robustness Improvements (May 10, 2025)

Implemented several improvements to the signal calculator to enhance robustness and fix potential issues.

### 5.1 Fixed MR ATR Column Name in Keltner Branch

Updated the ATR column name in the Keltner Channel calculation to match pandas-ta's default naming convention.

#### Before:
```python
kc_atr_col = f"ATRr_{mr_atr_period}"
```

#### After:
```python
kc_atr_col = f"ATR_{mr_atr_period}"      # pandas-ta's default column name
```

### 5.2 Unified Tight-Spread Fallback Percentile Logic

Replaced the direct percentile calculation with the unbiased helper function for consistency.

#### Before:
```python
signals_df["spread_mean_pctile"] = (
    signals_df["spread_mean"]
    .rolling(window=window, min_periods=min_p)
    .rank(pct=True)
)  # pct=True gives percentile rank
```

#### After:
```python
signals_df["spread_mean_pctile"] = rolling_percentile_rank(
    signals_df["spread_mean"], window, min_p)
```

### 5.3 Robust Helper Indexing

Updated both helper functions to use proper pandas Series indexing with `.iloc[-1]` instead of direct array indexing with `arr[-1]`.

#### Before (in `rolling_percentile_rank._pct`):
```python
return percentileofscore(clean, arr[-1]) / 100.0
```

#### After:
```python
return percentileofscore(clean, arr.iloc[-1]) / 100.0
```

#### Before (in `_pct_of_history`):
```python
return percentileofscore(clean, arr[-1]) / 100.0
```

#### After:
```python
return percentileofscore(clean, arr.iloc[-1]) / 100.0
```

### 5.4 Helper Deduplication

Updated the `atr_percent_pctile` calculation to use the `rolling_percentile_rank` helper function instead of `_pct_of_history` for consistency and to reduce code duplication.

#### Before:
```python
signals_df['atr_percent_pctile'] = (
    signals_df['atr_percent']
    .rolling(window=rolling_window, min_periods=max(1, rolling_window // 2))
    .apply(_pct_of_history, raw=False)
)
```

#### After:
```python
signals_df['atr_percent_pctile'] = (
    signals_df['atr_percent']
    .rolling(window=rolling_window, min_periods=max(1, rolling_window // 2))
    .apply(lambda arr: rolling_percentile_rank(pd.Series(arr), rolling_window, 1).iloc[-1], raw=False)
)
```

### Benefits

- Ensures compatibility with different pandas-ta versions by using the correct ATR column name
- Provides consistent percentile calculation logic across the codebase
- Improves robustness by using proper pandas Series indexing
- Reduces code duplication by leveraging the existing `rolling_percentile_rank` helper function
- Makes the code more maintainable and less prone to errors

### Verification

The changes were verified by running a backtest with the 1h_setA configuration, which completed successfully without any errors. Assertions were added to the code to verify that:

1. The `atr_mr` column has valid values when it should (not all NaN)
2. The `spread_mean_pctile` values are properly bounded between 0 and 1

No assertion errors were encountered, confirming that the patches are working as intended.
