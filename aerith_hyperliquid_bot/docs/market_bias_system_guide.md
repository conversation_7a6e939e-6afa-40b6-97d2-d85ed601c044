# Market Bias System Guide

## Overview

This document explains the market bias system in the trading bot, based on systematic testing and code analysis. The market bias system adjusts position sizes and margin requirements based on market conditions and trade direction.

## How Market Bias Works

The market bias system operates in three main parts:

1. **Market State Mapping**: Maps GMS regimes to simplified BULL/BEAR/CHOP states
2. **Risk Factor Adjustment**: Scales position sizes based on market state
3. **Leverage & Direction Adjustments**: Modifies margin requirements and can further adjust based on trade direction

## System Components

### Market State Mapping

The system maps complex GMS regimes (like "Strong_Bull_Trend") to simpler states:
- **BULL**: Bullish regimes (e.g., Strong_Bull_Trend, Weak_Bull_Trend)
- **BEAR**: Bearish regimes (e.g., Strong_Bear_Trend, Weak_Bear_Trend)
- **CHOP**: Ranging/choppy regimes (e.g., Mean_Reversion, Breakout)

This mapping is controlled by:
```yaml
market_bias:
  use_three_state_mapping: true  # Use BULL/BEAR/CHOP instead of raw regime names
```

### Risk Factors

Risk factors **directly affect position size** by scaling the risk_amount used in position size calculations:

```
risk_amount = balance * risk_per_trade * risk_factor * other_factors
position_size = risk_amount / stop_distance
```

Configured with:
```yaml
market_bias:
  bull_risk_factor: 1.2  # Larger positions in bull markets
  bear_risk_factor: 0.8  # Smaller positions in bear markets
  chop_risk_factor: 0.5  # Much smaller positions in choppy markets
```

**Neutral value**: 1.0 (no adjustment)

### Leverage Factors

Leverage factors **affect margin requirements only**, not position size:

```
base_leverage = strategy_specific_leverage  # (e.g. tf_leverage_base)
market_adjusted_leverage = base_leverage * market_leverage_factor
final_leverage = market_adjusted_leverage * direction_bias
```

Configured with:
```yaml
market_bias:
  bull_leverage_factor: 1.0  # No adjustment in bull markets
  bear_leverage_factor: 1.0  # No adjustment in bear markets
  chop_leverage_factor: 1.0  # No adjustment in choppy markets
```

**Neutral value**: 1.0 (no adjustment)

### Direction Bias

Direction bias further adjusts leverage based on both market state AND trade direction:

```
final_leverage = market_adjusted_leverage * direction_bias_factor
```

Configured with:
```yaml
market_bias:
  bull_long_bias: 1.0   # For long trades in bull markets
  bull_short_bias: 1.0  # For short trades in bull markets
  bear_long_bias: 1.0   # For long trades in bear markets
  bear_short_bias: 1.0  # For short trades in bear markets
```

**Neutral value**: 1.0 (no adjustment)

**Important**: Direction bias only works when the 'direction' field is included in the strategy_info dictionary passed to the risk manager.

## Current Status & Limitations

1. **Risk Factors**: Working correctly, directly influencing position size
2. **Leverage Factors**: Working correctly, but have limited impact in single-asset trading
3. **Direction Bias**: Currently not affecting trades because direction information is not passed to the risk manager

## Example Calculation

### Parameters
- Account balance: $10,000
- risk_per_trade: 1% (0.01)
- ATR: 800 (for stop distance)
- Base price: $16,000
- Strategy: trend_following
- Market state: BULL

### Calculation Steps

1. **Calculate risk amount with bull risk factor**:
   - risk_amount = $10,000 × 0.01 × 1.2 (bull_risk_factor) = $120

2. **Calculate position size**:
   - position_size = $120 ÷ 800 = 0.15 BTC

3. **Calculate leverage**:
   - base_leverage = 5.0 (from tf_leverage_base)
   - market_adjusted_leverage = 5.0 × 1.0 (bull_leverage_factor) = 5.0
   - final_leverage = 5.0 (within bounds)

4. **Calculate margin**:
   - notional_value = 0.15 × $16,000 = $2,400
   - margin_required = $2,400 ÷ 5.0 = $480

## Multi-Asset Trading Enhancements

For future multi-asset trading capabilities, the following enhancements would be beneficial:

### 1. Direction Information Addition

```python
# Add this to backtester.py where calculate_position is called
if strategy_info is None:
    strategy_info = {}
    
position_signal = current_signals.get(f"{strategy_name}_position", 0)
if 'direction' not in strategy_info:
    strategy_info['direction'] = 'long' if position_signal > 0 else 'short' if position_signal < 0 else 'flat'
```

### 2. Direction-Optimized Settings

For multi-asset trading, consider these settings:

```yaml
market_bias:
  # Adjust leverage factors for capital efficiency
  bull_leverage_factor: 1.5
  bear_leverage_factor: 0.7
  chop_leverage_factor: 0.5
  
  # Direct position size adjustments 
  bull_risk_factor: 1.2
  bear_risk_factor: 0.8
  chop_risk_factor: 0.5
  
  # Direction-specific bias
  bull_long_bias: 1.2    # Favor longs in bull markets
  bull_short_bias: 0.7   # Reduce shorts in bull markets
  bear_long_bias: 0.7    # Reduce longs in bear markets  
  bear_short_bias: 1.2   # Favor shorts in bear markets
```

## Recommended Settings

### Single-Asset Trading (Current)

```yaml
market_bias:
  # Leverage factors (minimal impact for single asset)
  bull_leverage_factor: 1.0
  bear_leverage_factor: 1.0
  chop_leverage_factor: 1.0
  
  # Risk factors (primary position sizing control)
  bull_risk_factor: 1.2
  bear_risk_factor: 0.8
  chop_risk_factor: 0.5
  
  # Direction bias (not active without direction info)
  bull_long_bias: 1.0
  bull_short_bias: 1.0
  bear_long_bias: 1.0
  bear_short_bias: 1.0
```
