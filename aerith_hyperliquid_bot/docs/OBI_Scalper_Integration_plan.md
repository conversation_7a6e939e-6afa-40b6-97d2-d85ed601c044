# PRD – OBI Scalper & GMS/TF Integration

*(v 0.2 – 2025‑05‑21)*

---

## 0  Purpose

Deliver a retail‑friendly, order‑book‑driven **OBI Scalper** and modernise the existing **Trend‑Follower (TF‑v2)** so both act under the **GMS Regime Detector**.  The bot must stay profitable on historical data and run on a single PC/VM with \~500 ms round‑trip latency.

## 1  Operating Model (Memory‑Bank Workflow)

| Step | Actor              | Action                                                                       |
| ---- | ------------------ | ---------------------------------------------------------------------------- |
| 1    | You / ChatGPT      | Feature ideation & coarse roadmap                                            |
| 2    | ChatGPT Supervisor | Break roadmap into **Tasks** (below)                                         |
| 3    | You                | Pick the next Task & instruct an **AI agent** (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>) |
| 4    | AI Agent           | Draft code / config diff, run local tests                                    |
| 5    | You                | Review & manually **commit** to Git                                          |
| 6    | AI Agent           | Write a *Change Summary* to **memory‑bank MCP** & append to this PRD         |
| 7    | ChatGPT            | Spot‑check, update KPIs, unlock next Task                                    |

> **Context retention**: This PRD is the single source of truth; every completed Task appends a short `### Change‑Log` entry.  All agents fetch the latest PRD from memory‑bank MCP before starting work.

---

## 2  Success Criteria

* Sharpe ≥ 1.2 and Profit Factor ≥ 1.3 over 2024 IS; Sharpe ≥ 1 OOS 2023 after taker fee & 500 ms latency sim.
* Legacy GMS + TF baseline metrics do **not** degrade by more than 1 %.
* Unit‑test suite passes (> 95 % coverage for new modules).
* Live paper‑trade (test‑net) slippage ≤ 2× simulated average.

---

## 3  Task Breakdown

> **Legend**
> **Owner** = recommended AI agent
> **DoD** = Definition of Done (what triggers Move‑to‑Done)
> **After‑care** = Summary text the agent must write to memory‑bank MCP

| ID        | Title                             | Owner                 | Dependencies           | DoD                                                                                                             | After‑care                                  |                                                      |                                                     |
| --------- | --------------------------------- | --------------------- | ---------------------- | --------------------------------------------------------------------------------------------------------------- | ------------------------------------------- | ---------------------------------------------------- | --------------------------------------------------- |
| **T‑101** | *Feature Layer – multi‑depth OBI* | **Cursor**            | Baseline branch frozen | • `features/microstructure.py` has \`calc\_obi(depth: list\[int]                                                | int, weights: list\[float]                  | None)\` <br>• Unit tests: correct output on toy book | • Summary of function signature + unit‑test results |
| **T‑102** | Signals – smoothing & z‑score     | Cursor                | T‑101                  | • `SignalEngine` outputs `obi_smoothed_{depth}` + `obi_z_{depth}` <br>• No perf‑regression tests fail           | • Note new config keys & default windows    |                                                      |                                                     |
| **T‑103** | Config plumbing                   | Windsurf              | T‑101 T‑102            | • `settings.py` & `base.yaml` accept `obi_depth_variants`, `obi_weight_scheme`                                  | • Dict of new YAML keys with description    |                                                      |                                                     |
| **T‑104** | **OBIScalperStrategy skeleton**   | Cline (high accuracy) | T‑102 T‑103            | • New class in `strategies/evaluator.py` passes linter & unit tests<br>• `evaluate()` logs diagnostics counters | • Outline of eval logic & config thresholds |                                                      |                                                     |
| **T‑105** | Risk integration                  | Cursor                | T‑104                  | • Scalper position sizing via fixed‑fraction or 1‑min ATR <br>• Unit test: size ≤ max\_leverage limit           | • Summary of risk params                    |                                                      |                                                     |
| **T‑106** | StrategyEvaluator wiring          | Windsurf              | T‑104 T‑105            | • `get_active_strategies()` activates scalper per flags                                                         | • comment explaining regime logic           |                                                      |                                                     |
| **T‑107** | 1‑min Back‑test & KPI report      | Roo (accurate)        | T‑106                  | • Back‑tester runs 2023‑24; CSV of KPIs saved<br>• Change‑Log entry with metrics table                          | • KPIs paste + recommendation next tweaks   |                                                      |                                                     |

*(Further tasks—tick‑level engine, latency sim, TF‑v2 upgrades—will be added after T‑107 passes the KPIs.)*

---

## 4  First Actionable Task (T‑101)

### Goal

Add a **single versatile function** to compute Order‑Book Imbalance for arbitrary depth ranges so downstream code can request any combination (L1, L1‑3, … L1‑20).

### Files to edit

```
hyperliquid_bot/features/microstructure.py
hyperliquid_bot/tests/test_microstructure.py  (new)
```

### Spec

```python
# microstructure.py (pseudo‑code)

def calc_obi(order_book: dict[str, np.ndarray], *,
             levels: int | list[int] = 5,
             weights: list[float] | None = None) -> float:
    """Return OBI for the given depth range.
    order_book['bid_qty'] and ['ask_qty'] are 1‑D arrays length ≥ max(levels).
    If `levels` is int N  → use  range(0, N).
    If list  → treat as explicit indices (1‑based).
    If `weights` provided → len(weights) == len(levels), apply before sum."""
```

*Equation*:
`OBI = (Σ w_i·bid_i − Σ w_i·ask_i) / (Σ w_i·bid_i + Σ w_i·ask_i)`

### Unit Test Outline

```python
# test_microstructure.py
fake_book = {
    'bid_qty': np.array([100, 80, 60, 40, 20]),
    'ask_qty': np.array([ 90, 70, 50, 30, 10]),
}
assert calc_obi(fake_book, levels=1) == (100-90)/(100+90)
assert np.isclose(
    calc_obi(fake_book, levels=[1,2]),
    ((100+80)-(90+70))/((100+80)+(90+70))
)
```

### Commit Message Template

```
T‑101: feature layer – generic calc_obi()
* adds calc_obi with depth & weight params
* updates __init__.py exports
* 4 unit tests green (pytest)
```

### After‑care Entry

The agent writes to memory‑bank MCP:

```
#### T‑101 completed (2025‑05‑21)
Implemented calc_obi() + tests. Default depths in config unchanged.
```

---

## 5  Risks & Mitigations (PRD‑level)

* **Context drift** – mitigated by mandatory Change‑Log updates in this PRD.
* **Agent hallucination** – high‑stakes tasks (T‑104, T‑107) assigned to Cline / Roo for accuracy.
* **Latency overlook** – separate latency sim task to be scheduled after T‑107.

---

## 6  Change‑Log

*(append below; empty for now)*

---

## ✅ Task T‑101 Completed (2025‑05‑21)

Implemented multi‑depth `calc_obi()` function, unit‑tested (7 cases), and environment fixes.  See task summary in memory‑bank MCP / commit log.

## ✅ Task T‑102a Completed (2025‑05‑21)

Bug‑fix: dynamic OBI signal naming in `GranularMicrostructureRegimeDetector`; NaN errors resolved.

## ✅ Task T‑102b Completed (2025‑05‑21)

Added `smooth_signal()` and `zscore_signal()` helpers, generated `obi_smoothed_*` / `obi_zscore_*` columns, unit‑tested (SMA/EMA correctness, z‑score stats, perf <100 ms), updated config & docs; all lint/tests green.

---

### 🔄 Next Tasks

| ID         | Task                                                                                                                            | Owner                           | Depends      |
| ---------- | ------------------------------------------------------------------------------------------------------------------------------- | ------------------------------- | ------------ |
| **T‑102b** | Implement `smooth_signal` & `zscore` helpers in `signals/calculator.py`; emit `obi_smoothed_*`, `obi_zscore_*`. Add unit tests. | Cursor AI (code) / You (commit) | T‑101        |
| **T‑103**  | Update downstream detectors & strategies to use new smoothed/z‑score columns.                                                   | Cline (for rigor)               | T‑102b       |
| **T‑104**  | Update config (`settings.py`, `base.yaml`) with smoothing/zscore params; write doc snippet.                                     | Windsurf                        | T‑102b       |
| **T‑105**  | End‑to‑end back‑test sanity run: verify no NaN and runtime budget.                                                              | You + ChatGPT review            | T‑103, T‑104 |
