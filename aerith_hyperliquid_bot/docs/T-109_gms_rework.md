#######################################################################
# TASK T-109 · Continuous & Depth-Agnostic GMS Rework  (2025-05-23)
# Applies to repo: aerith_hyperliquid_bot
#######################################################################

1. Goal
   Port the existing GranularMicrostructureDetector into a new
   **ContinuousGMSDetector** that:
     • recomputes every <config.gms.cadence_sec> seconds (default 60)  
     • accepts arbitrary depth (5 or 20) via config.microstructure.obi_levels  
     • outputs the raw 8 states *plus* a `risk_suppressed` boolean  
     • supports collapse-maps (3-state, 4-state) via YAML mapping file  
     • stays API-compatible with RegimeDetectorInterface

2. Project-flow alignment
   Place class under  `hyperliquid_bot/core/gms_detector.py`.  create this new file; import it inside `hyperliquid_bot/core/detector.py`
   Update `hyperliquid_bot/core/detector.py` (its existing get_regime_detector factory) to route the new detector_type: 'continuous_gms' to the class you create.

3. Config additions  (update base.yaml)
-----------------------------------------------------------------
gms:
  detector_type: 'continuous_gms'       # new default
  cadence_sec: 60
  output_states: 8                      # 8 = raw, 4 or 3 after collapse
  state_collapse_map_file: 'configs/gms_state_mapping.yaml'
  use_four_state_mapping: false         # adds TIGHT_SPREAD → TSP
  risk_suppressed_notional_frac: 0.25   # large-trade threshold
  risk_suppressed_pnl_atr_mult: 1.5
-----------------------------------------------------------------

4. Implementation checklist
   [ ] Copy logic from `core/detector.py::GranularMicrostructureDetector`
   [ ] Replace all window look-ups with `self.depth_levels`
       taken from config.microstructure.obi_levels  
   [ ] Add `update(signals_row: dict) -> None` to push new data each minute  
   [ ] Maintain `self.current_state` and `self.last_state_change_ts`  
   [ ] Calculate `risk_suppressed` flag using:
         notional ≥ config.core.initial_balance *
                    config.core.max_leverage *
                    gms.risk_suppressed_notional_frac
         OR
         unrealised_pnl      ≥ ATR(14) * gms.risk_suppressed_pnl_atr_mult
   [ ] `get_regime()` now returns a dict:  
       `{"state": "Strong_Bull_Trend", "risk_suppressed": false}`  
   [ ] Provide `get_collapsed_regime()` helper to apply mapping.

5. Unit tests  (tests/test_continuous_gms.py)
   • test_state_transitions_5lvl() – replicate old detector, depth=5  
   • test_state_transitions_20lvl() – same thresholds, depth=20  
   • test_collapse_map_3state_4state()  
   • test_risk_suppressed_flag_notional()  
   • test_risk_suppressed_flag_pnl()  
   Use synthetic DataFrame rows; no I/O.

6. Integration hooks
   • Modify `signals/calculator.py` to push latest signals to
     ContinuousGMSDetector each minute.  
   • `StrategyEvaluator` asks detector.get_collapsed_regime() each bar.  
   • Ensure old back-tests keep running by adding a shim that treats the
     dict output as a string when necessary (`state` field).

7. Migration notes
   • Delete legacy GranularMicrostructureDetector ONLY after tests pass.  
   • Keep config.regime.detector_type fallback mapping:
       'granular_microstructure' → instantiate ContinuousGMSDetector
       with cadence_sec = 3600  (legacy once-per-hour behaviour).

8. Deliverables
   ✓ `core/gms_detector.py` + docstrings  
   ✓ config diff committed  
   ✓ new unit-test file passing `pytest -q`  
   ✓ PR description referencing PRD sections (§P1, §2) & this task file

#######################################################################
# END OF BRIEF
#######################################################################
