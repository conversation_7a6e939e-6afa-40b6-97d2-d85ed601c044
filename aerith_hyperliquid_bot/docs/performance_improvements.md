# Performance Optimization Analysis

## 🚨 **Critical Performance Issues Identified**

### **Issue #1: Fear & Greed API Calls (HIGHEST IMPACT)**
**Location**: `configs/base.yaml:20`
```yaml
data_providers:
  fear_greed:
    enabled: true  # ← Major performance bottleneck
```

**Problem**: 
- Makes HTTP API calls to fetch historical Fear & Greed data for every backtest
- For 2024 full-year backtest: ~365 API requests
- Network latency + potential rate limiting
- **Completely unnecessary for TF-v2 legacy strategy**

**Impact**: Likely causing 50-80% of performance degradation

**Fix**: 
```yaml
data_providers:
  fear_greed:
    enabled: false  # ← Change to false for legacy backtests
```

---

### **Issue #2: Microstructure Feature Processing (SECOND HIGHEST)**
**Location**: `hyperliquid_bot/data/handler.py:507-726`

**Problem**:
- Loads raw L2 data for every day (365 files for 2024)
- Calculates complex microstructure features (OBI, depth metrics, spreads)
- Row-wise feature calculations using pandas `apply()` functions
- **Massive computational overhead for TF-v2 which only needs basic OHLCV**

**Current Process**:
```python
def _integrate_microstructure_features(self):
    # Processes EVERY day individually
    for date in unique_dates:
        l2_daily_df = self._load_l2_segment(date_str)  # Heavy I/O
        daily_features = merged_daily.apply(self._calculate_features_from_row, axis=1)  # Heavy computation
```

**Impact**: 60-90% performance overhead for unnecessary feature calculation

**Optimization**: Add conditional logic to skip microstructure processing for TF-v2-only backtests

---

### **Issue #3: ETL Scheduler Running**
**Location**: `configs/base.yaml:365-367`
```yaml
scheduler:
  etl_enabled: true       # ← Unnecessary for legacy backtest
  etl_poll_sec: 300       # Polling every 5 minutes
```

**Problem**: ETL scheduler actively processing 1-second feature files that TF-v2 doesn't need

**Fix**:
```yaml
scheduler:
  etl_enabled: false  # ← Disable for legacy backtests
```

---

### **Issue #4: 1-Second Feature File Loading**
**Location**: Multiple references to `features_1s` directory

**Problem**: System attempts to load high-frequency 1-second feature files when TF-v2 only needs hourly OHLCV

**Data Requirements by Strategy**:
- **TF-v2 Legacy**: Hourly OHLCV + basic indicators (ADX, EMA, ATR)
- **TF-v3 Modern**: 1-second features + regime detection
- **OBI Scalper**: High-frequency microstructure data

---

## 📊 **Performance Optimization Recommendations**

### **Immediate Quick Fixes (High Impact, Low Effort)**

#### **1. Use Legacy Configuration**
```bash
# Instead of base.yaml, use optimized legacy config
python run_backtest.py --config configs/overrides/legacy_regression_2024.yaml
```

#### **2. Disable Unnecessary Data Providers**
```yaml
# In your config file
data_providers:
  fear_greed:
    enabled: false

scheduler:
  etl_enabled: false
```

#### **3. Minimal Data Loading for TF-v2**
For TF-v2 legacy strategy, you only need:
- ✅ Hourly OHLCV data (`/resampled_l2/1h/`)
- ✅ Basic technical indicators (calculated in SignalEngine)
- ❌ Raw L2 data
- ❌ 1-second features
- ❌ Microstructure features
- ❌ Fear & Greed data

---

### **Medium-Term Code Optimizations**

#### **1. Conditional Data Loading**
```python
# In handler.py
def load_historical_data(self, start_date: datetime, end_date: datetime):
    # Load OHLCV (always needed)
    self._load_ohlcv(start_date, end_date)
    
    # Only load microstructure if modern strategies are enabled
    if self._needs_microstructure_features():
        self._integrate_microstructure_features()
    else:
        self.logger.info("Skipping microstructure features for legacy strategy")
        self.combined_data = self.ohlcv_data.copy()

def _needs_microstructure_features(self) -> bool:
    """Check if any enabled strategy requires microstructure features."""
    cfg = self.config.strategies
    return (cfg.use_tf_v3 or cfg.use_obi_scalper or 
            self.config.regime.detector_type == 'continuous_gms')
```

#### **2. Strategy-Specific Data Paths**
```python
# Different data loading strategies based on enabled strategies
if only_tf_v2_enabled:
    # Load only hourly OHLCV
    data_sources = ['ohlcv_hourly']
elif modern_strategies_enabled:
    # Load full feature set
    data_sources = ['ohlcv_hourly', 'features_1s', 'microstructure', 'fear_greed']
```

---

## 🎯 **Expected Performance Improvements**

| Optimization | Expected Speedup | Effort Level |
|--------------|------------------|--------------|
| Disable Fear & Greed | 50-80% faster | Low (config change) |
| Skip Microstructure | 60-90% faster | Medium (code change) |
| Use Legacy Config | 70-85% faster | Low (use existing file) |
| **Combined Optimizations** | **5-10x faster** | **Medium** |

---

## 🔧 **Implementation Priority**

### **Phase 1: Immediate (5 minutes)**
1. Use `configs/overrides/legacy_regression_2024.yaml`
2. Verify Fear & Greed is disabled in config
3. Verify ETL scheduler is disabled

### **Phase 2: Short-term (1-2 hours)**
1. Add conditional microstructure loading in `handler.py`
2. Create strategy-specific data loading logic
3. Add performance logging to measure improvements

### **Phase 3: Long-term (Future)**
1. Implement lazy loading for data sources
2. Add caching for frequently accessed data
3. Optimize pandas operations in feature calculations

---

## 📝 **Testing Performance Improvements**

### **Benchmark Test**
```bash
# Before optimization
time python run_backtest.py --config configs/base.yaml

# After optimization  
time python run_backtest.py --config configs/overrides/legacy_regression_2024.yaml
```

### **Memory Usage Monitoring**
```python
# Add to backtester
import psutil
process = psutil.Process()
self.logger.info(f"Memory usage: {process.memory_info().rss / 1024 / 1024:.1f} MB")
```

---

## 💡 **Root Cause Analysis**

The performance degradation occurred because the current configuration is optimized for **modern strategies** (TF-v3, OBI Scalper) that require:
- High-frequency microstructure data
- Real-time regime detection
- Complex feature engineering

However, **TF-v2 legacy strategy** only needs:
- Basic OHLCV data
- Simple technical indicators
- Rule-based regime detection

**Solution**: Implement strategy-aware data loading to avoid unnecessary computational overhead.
