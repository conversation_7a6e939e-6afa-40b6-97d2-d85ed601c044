# Balance Calculation and Risk Management System Improvements Plan

## Overview

This document outlines a plan to address two key issues identified in the trading bot:

1. **Balance Calculation Discrepancy**: A discrepancy between expected and actual account balance
2. **Risk Management System Enhancements**: Implementing configurable regime-specific leverage adjustments

These improvements will make the trading bot more accurate in reporting performance and more adaptable to different market conditions.

## Issue 1: Balance Calculation Discrepancy

### Problem Description

The backtester reports a discrepancy between the expected balance and the actual account value:

```
BALANCE_DEBUG: Expected balance=$44943.15 (initial + sum of trade PnLs)
BALANCE_DEBUG: Total account value=$42565.12 (balance + reserved margin)
BALANCE_DEBUG: DISCREPANCY DETECTED! Account value=$42565.12 vs Expected=$44943.15
```

The discrepancy of approximately $2,378.03 is likely due to funding fees not being properly accounted for in the trade PnL calculations. While individual trade PnLs include entry/exit fees and slippage, the funding fees are applied directly to the balance but not tracked in the trade records properly.

### Implementation Plan

#### Task 1.1: Update Balance Calculation in Backtester

Update the `_compute_and_log_metrics` method in the backtester to include funding fees in the expected balance calculation:

```python
# TO BE IMPLEMENTED
# Calculate total PnL from trades for verification
total_trade_pnl = sum(t.get('profit', 0) for t in portfolio.trades)
total_funding_pnl = portfolio.total_funding_pnl  # Add this line
self.logger.info(f"BALANCE_DEBUG: Sum of all trade PnLs=${total_trade_pnl:.2f}")
self.logger.info(f"BALANCE_DEBUG: Total funding PnL=${total_funding_pnl:.2f}")  # Add this line

# Calculate expected balance (should match balance + reserved_margin)
expected_balance = portfolio.initial_balance + total_trade_pnl + total_funding_pnl  # Update this line
```

#### Task 1.2: Improve Funding PnL Tracking in Portfolio

Ensure funding PnL is properly tracked and included in trade records by updating the `handle_exit` method in the Portfolio class:

```python
# TO BE IMPLEMENTED
# Update trade record to include cumulative funding PnL
trade = {
    # ... existing fields ...
    "funding_pnl": self.position.get("cum_funding_pnl", 0.0),
    # ... other fields ...
}
```

#### Task 1.3: Add Funding PnL to Total PnL Calculation

Update any methods that calculate total PnL to include funding fees:

```python
# TO BE IMPLEMENTED
# Example in performance reporting
total_pnl = trade_pnl + funding_pnl
```

## Issue 2: Risk Management System Enhancements

### Problem Description

The current risk management system needs to be enhanced to provide more control over leverage adjustments based on market regimes. Specifically:

1. The system should allow for different leverage factors for different market regimes
2. It should capitalize on strong bull trends with higher leverage
3. It should be more conservative in bear trends and high volatility periods
4. It should work with both the 7-state and 3-state regime detectors

### Implementation Plan

#### Task 2.1: Update Configuration Schema

Add new configuration parameters to `base.yaml` for regime-specific leverage and risk factors:

```yaml
# TO BE IMPLEMENTED
regime:
  # Dynamic risk adjustment based on market regime
  dynamic_risk_adjustment: true        # Master switch for regime-based risk adjustment
  
  # Leverage factors for different market regimes
  # Values > 1.0 increase leverage, values < 1.0 decrease leverage
  # These multiply the base leverage set in the strategy
  strong_bull_leverage_factor: 1.2     # Increase leverage in strong bull trends to capitalize on uptrends
  strong_bear_leverage_factor: 0.6     # Decrease leverage in strong bear trends to reduce risk
  weak_bull_leverage_factor: 0.9       # Slightly decrease leverage in weak bull trends
  weak_bear_leverage_factor: 0.7       # More decrease in weak bear trends
  chop_leverage_factor: 0.5            # Significant decrease in choppy markets
  high_vol_leverage_factor: 0.4        # Maximum decrease in high volatility
  
  # Risk factors for different market regimes
  # These multiply the base risk_per_trade value (e.g., 0.02 or 2%)
  # Lower values = smaller position sizes relative to account balance
  strong_bull_risk_factor: 0.8         # 80% of normal risk in strong bull trends
  strong_bear_risk_factor: 0.6         # 60% of normal risk in strong bear trends
  weak_bull_risk_factor: 0.7           # 70% of normal risk in weak bull trends
  weak_bear_risk_factor: 0.6           # 60% of normal risk in weak bear trends
  chop_risk_factor: 0.5                # 50% of normal risk in choppy markets
  high_vol_risk_factor: 0.4            # 40% of normal risk in high volatility
  
  # 3-State Regime Mapping Configuration
  # This section controls how the 7 granular states map to 3 states
  # and which risk/leverage factors apply to each
  gms_use_three_state_mapping: true    # Use 3-state mapping instead of 7 granular states
  
  # Define which leverage/risk factors apply to each 3-state category
  # Options: strong_bull, weak_bull, strong_bear, weak_bear, chop, high_vol
  bull_trend_leverage_factor: "strong_bull_leverage_factor"   # Use strong bull settings for Bull regime
  bear_trend_leverage_factor: "strong_bear_leverage_factor"   # Use strong bear settings for Bear regime
  ranging_leverage_factor: "chop_leverage_factor"             # Use chop settings for Range regime
  
  bull_trend_risk_factor: "strong_bull_risk_factor"           # Use strong bull settings for Bull regime
  bear_trend_risk_factor: "strong_bear_risk_factor"           # Use strong bear settings for Bear regime
  ranging_risk_factor: "chop_risk_factor"                     # Use chop settings for Range regime
```

#### Task 2.2: Update Pydantic Configuration Model

Add the new parameters to the Pydantic model in `settings.py`:

```python
# TO BE IMPLEMENTED
# Add to RegimeSettings class
strong_bull_leverage_factor: float = Field(default=1.2, gt=0, description="Leverage factor for strong bull trends")
strong_bear_leverage_factor: float = Field(default=0.6, gt=0, description="Leverage factor for strong bear trends")
weak_bull_leverage_factor: float = Field(default=0.9, gt=0, description="Leverage factor for weak bull trends")
weak_bear_leverage_factor: float = Field(default=0.7, gt=0, description="Leverage factor for weak bear trends")
chop_leverage_factor: float = Field(default=0.5, gt=0, description="Leverage factor for choppy markets")
high_vol_leverage_factor: float = Field(default=0.4, gt=0, description="Leverage factor for high volatility")

strong_bull_risk_factor: float = Field(default=0.8, gt=0, le=1, description="Risk factor for strong bull trends")
strong_bear_risk_factor: float = Field(default=0.6, gt=0, le=1, description="Risk factor for strong bear trends")
weak_bull_risk_factor: float = Field(default=0.7, gt=0, le=1, description="Risk factor for weak bull trends")
weak_bear_risk_factor: float = Field(default=0.6, gt=0, le=1, description="Risk factor for weak bear trends")
chop_risk_factor: float = Field(default=0.5, gt=0, le=1, description="Risk factor for choppy markets")
high_vol_risk_factor: float = Field(default=0.4, gt=0, le=1, description="Risk factor for high volatility")

# 3-State mapping configuration
bull_trend_leverage_factor: str = Field(default="strong_bull_leverage_factor", description="Which leverage factor to use for Bull regime in 3-state mapping")
bear_trend_leverage_factor: str = Field(default="strong_bear_leverage_factor", description="Which leverage factor to use for Bear regime in 3-state mapping")
ranging_leverage_factor: str = Field(default="chop_leverage_factor", description="Which leverage factor to use for Range regime in 3-state mapping")

bull_trend_risk_factor: str = Field(default="strong_bull_risk_factor", description="Which risk factor to use for Bull regime in 3-state mapping")
bear_trend_risk_factor: str = Field(default="strong_bear_risk_factor", description="Which risk factor to use for Bear regime in 3-state mapping")
ranging_risk_factor: str = Field(default="chop_risk_factor", description="Which risk factor to use for Range regime in 3-state mapping")
```

#### Task 2.3: Update Risk Manager to Use Regime-Specific Factors

Modify the `calculate_position` method in the `RiskManager` class to use the appropriate leverage and risk factors based on the current regime:

```python
# TO BE IMPLEMENTED
# Apply dynamic risk adjustment based on regime if configured
apply_dynamic_risk = False
if hasattr(cfg.regime, 'dynamic_risk_adjustment') and cfg.regime.dynamic_risk_adjustment:
    # Check if current regime should trigger dynamic risk adjustment
    if regime in ["Volatile_Chop", "Low_Vol_Chop", "Ranging", "High_Vol_Range", 
                 "Strong_Bull_Trend", "Weak_Bull_Trend", "Strong_Bear_Trend", "Weak_Bear_Trend",
                 "Bull_Trend", "Bear_Trend", "Range"]:  # Include 3-state regimes
        apply_dynamic_risk = True
        self.logger.info(f"Dynamic risk adjustment triggered by regime: {regime}")

if apply_dynamic_risk:
    # Determine which risk and leverage factors to use based on regime
    # Handle both 7-state and 3-state regimes
    if cfg.regime.gms_use_three_state_mapping and regime in ["Bull_Trend", "Bear_Trend", "Range"]:
        # 3-state regime mapping
        if regime == "Bull_Trend":
            leverage_factor_attr = getattr(cfg.regime, 'bull_trend_leverage_factor', 'strong_bull_leverage_factor')
            risk_factor_attr = getattr(cfg.regime, 'bull_trend_risk_factor', 'strong_bull_risk_factor')
        elif regime == "Bear_Trend":
            leverage_factor_attr = getattr(cfg.regime, 'bear_trend_leverage_factor', 'strong_bear_leverage_factor')
            risk_factor_attr = getattr(cfg.regime, 'bear_trend_risk_factor', 'strong_bear_risk_factor')
        else:  # Range
            leverage_factor_attr = getattr(cfg.regime, 'ranging_leverage_factor', 'chop_leverage_factor')
            risk_factor_attr = getattr(cfg.regime, 'ranging_risk_factor', 'chop_risk_factor')
            
        # Get the actual factor values using the attribute names
        leverage_factor = getattr(cfg.regime, leverage_factor_attr, cfg.regime.chop_leverage_factor)
        risk_factor = getattr(cfg.regime, risk_factor_attr, cfg.regime.chop_risk_factor)
    else:
        # 7-state granular regimes
        if regime == "Strong_Bull_Trend":
            leverage_factor = getattr(cfg.regime, 'strong_bull_leverage_factor', 1.2)
            risk_factor = getattr(cfg.regime, 'strong_bull_risk_factor', 0.8)
        elif regime == "Weak_Bull_Trend":
            leverage_factor = getattr(cfg.regime, 'weak_bull_leverage_factor', 0.9)
            risk_factor = getattr(cfg.regime, 'weak_bull_risk_factor', 0.7)
        elif regime == "Strong_Bear_Trend":
            leverage_factor = getattr(cfg.regime, 'strong_bear_leverage_factor', 0.6)
            risk_factor = getattr(cfg.regime, 'strong_bear_risk_factor', 0.6)
        elif regime == "Weak_Bear_Trend":
            leverage_factor = getattr(cfg.regime, 'weak_bear_leverage_factor', 0.7)
            risk_factor = getattr(cfg.regime, 'weak_bear_risk_factor', 0.6)
        elif regime == "High_Vol_Range":
            leverage_factor = getattr(cfg.regime, 'high_vol_leverage_factor', 0.4)
            risk_factor = getattr(cfg.regime, 'high_vol_risk_factor', 0.4)
        else:  # Choppy/Ranging regimes
            leverage_factor = getattr(cfg.regime, 'chop_leverage_factor', 0.5)
            risk_factor = getattr(cfg.regime, 'chop_risk_factor', 0.5)
    
    # Apply the adjustment
    original_leverage_ref = leverage
    leverage = max(cfg.indicators.min_leverage, 
                  min(leverage * leverage_factor, cfg.core.max_leverage))
    
    # Log the adjustment details
    self.logger.info(f"DYNAMIC RISK applied due to '{regime}' regime: "
                    f"Risk={risk_factor:.2f}, Leverage adjusted from {original_leverage_ref:.1f}x to {leverage:.1f}x "
                    f"(Factor: {leverage_factor:.2f})")
```

#### Task 2.4: Add Detailed Logging

Add detailed logging to track how the dynamic risk adjustment system is working:

```python
# TO BE IMPLEMENTED
# Add to RiskManager.calculate_position
self.logger.debug(f"Regime '{regime}' maps to: leverage_factor={leverage_factor:.2f}, risk_factor={risk_factor:.2f}")
self.logger.debug(f"Using configuration: dynamic_risk_adjustment={cfg.regime.dynamic_risk_adjustment}, "
                 f"3-state mapping={cfg.regime.gms_use_three_state_mapping}")
```

#### Task 2.5: Update Documentation

Update the dynamic risk adjustments documentation to reflect the new configuration options and how they work with the 3-state regime detector.

## Testing Plan

### Test 1: Balance Calculation Fix

1. Run a backtest with the updated code
2. Verify that the expected balance matches the actual account value
3. Check that funding PnL is properly accounted for in the balance calculation

### Test 2: Regime-Specific Risk Adjustments

1. Run backtests with different regime-specific leverage and risk factors
2. Verify that the system applies the correct factors based on the detected regime
3. Test with both 7-state and 3-state regime detectors
4. Compare performance metrics to ensure the adjustments have the desired effect

## Implementation Timeline

1. **Day 1**: Implement balance calculation fix (Tasks 1.1-1.3)
2. **Day 2**: Implement configuration schema updates (Tasks 2.1-2.2)
3. **Day 3**: Implement risk manager updates (Tasks 2.3-2.4)
4. **Day 4**: Update documentation and testing (Task 2.5)

## Expected Outcomes

1. **Accurate Balance Reporting**: The discrepancy between expected and actual account value will be resolved
2. **Improved Risk Management**: The trading bot will adapt its leverage and risk based on market regimes
3. **Better Performance**: Increased leverage in bull trends and reduced leverage in bear/choppy markets should improve overall risk-adjusted returns
4. **More Control**: Traders will have fine-grained control over how the system behaves in different market conditions

## Conclusion

These improvements will make the trading bot more accurate in reporting performance and more adaptable to different market conditions. The configurable regime-specific risk adjustments will give traders more control over how the system behaves, allowing them to capitalize on bull trends while being more conservative in bear markets and high volatility periods.
