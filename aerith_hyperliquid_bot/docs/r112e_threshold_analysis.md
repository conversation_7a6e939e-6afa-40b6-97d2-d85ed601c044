# R-112e Threshold Retune Analysis

## Summary

Applied new threshold configuration to reduce CHOP classification from 100% to ≤60%. **PARTIAL SUCCESS** achieved - volatility threshold fixed but momentum threshold needs further adjustment.

## Applied Thresholds

| Metric | Previous (R-112d) | New (R-112e) | Market Reality |
|--------|-------------------|--------------|----------------|
| Vol Low | 0.020 (2%) | **0.010 (1%)** | 1.04% |
| Vol High | 0.060 (6%) | **0.030 (3%)** | - |
| Mom Weak | 1.0 | **0.5** | 0.00 |
| Mom Strong | 5.0 | **2.5** | - |

## Results Analysis

### ✅ **Volatility Threshold Success**
```
GMS Factors: Vol=0.0104 (L:0.0100/H:0.0300)
```
- **Before**: 1.04% < 2.0% → "Low Vol"
- **After**: 1.04% > 1.0% → "Above Low Vol" ✅

### ❌ **Momentum Threshold Still Too High**
```
GMS Factors: Mom=0.00 (W:0.50/S:2.50)
```
- **Current**: 0.00 < 0.5 → "Weak Mom"
- **Issue**: Market momentum consistently near zero

### 📊 **Regime Distribution**
- **Uncertain**: 540/541 snapshots (~100%)
- **Unknown**: 1/541 snapshots (<1%)
- **CHOP Ratio**: Still ~100% (Uncertain maps to CHOP)

## Root Cause Analysis

1. **Volatility Fixed**: Successfully lowered threshold to capture market reality
2. **Momentum Problem**: Threshold 0.5 still too high for market conditions (actual ~0.00)
3. **Missing Signals**: Spread signals (spread_std, spread_mean) still missing, affecting regime detection

## Task Acceptance Criteria

| Criteria | Target | Actual | Status |
|----------|--------|--------|--------|
| CHOP ≤ 60% | ≤60% | ~100% | ❌ FAIL |
| Trade Count ≥ 10 | ≥10 | 0 | ❌ FAIL |
| No ATR fallback | ✓ | ✓ | ✅ PASS |
| No epsilon warnings | ✓ | ✓ | ✅ PASS |

## Recommended Next Action

**Lower momentum thresholds further** (as suggested in task):

```yaml
# Current (still too high)
gms_mom_weak_thresh: 0.5
gms_mom_strong_thresh: 2.5

# Suggested (more sensitive)
gms_mom_weak_thresh: 0.3  # As suggested in task
gms_mom_strong_thresh: 1.5
```

## Evidence Files

- **Backtest log**: `/Users/<USER>/Desktop/trading_bot_/logs/backtest_run_20250525_052059.log`
- **State counts**: `artifacts/state_counts.txt`
- **Metrics**: `artifacts/20250301_22_metrics_tuned.json`
- **Config**: `configs/base.yaml` (lines 143-150)

## Progress Summary

- ✅ **R-112c**: Diagnosed 95% CHOP issue (impossible thresholds)
- ✅ **R-112d**: Fixed volatility thresholds (55%/92% → 2%/6%)
- 🔄 **R-112e**: Partial fix (volatility working, momentum needs adjustment)
- 🎯 **Next**: Lower momentum thresholds to achieve ≤60% CHOP target
