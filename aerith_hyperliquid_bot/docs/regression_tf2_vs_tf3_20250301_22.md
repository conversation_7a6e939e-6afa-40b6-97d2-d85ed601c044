# TF-v2 vs TF-v3 Regression Test Results (T-111e)

## Test Period

- Start: 2025-03-01 00:00 UTC
- End: 2025-03-22 23:00 UTC

## Performance Metrics

| Metric | TF-v2 | TF-v3 |
| --- | --- | --- |
| Return % | 0.00% | 0.00% |
| Sharpe | 0.00 | 0.00 |
| Trade count | 0 | 0 |
| Max DD % | 0.00% | 0.00% |
| Win rate % | 0.00% | 0.00% |
| Profit factor | 0.00 | 0.00 |

## Trade Summary

- **TF-v2**: 0 trades
- **TF-v3**: 0 trades

## Skip Reason Analysis

The primary reasons for skipped trade opportunities:

1. **TF-v2**:
   - ATR not warmed up (first 14 hours)
   - No regime detected (remaining periods)

2. **TF-v3**:
   - ATR not warmed up (first 14 hours)
   - GMS stale (remaining periods)

## Conclusion

Both TF-v2 and TF-v3 strategies did not generate any trades during the test period. The primary issue with TF-v3 appears to be stale GMS signals, which is likely due to the `gms_max_age_sec` parameter being too restrictive (currently set to 120 seconds). Increasing this parameter or ensuring more frequent GMS updates may resolve this issue.

## Recommendations

1. Increase the `gms_max_age_sec` parameter in the TF-v3 configuration to allow for less frequent GMS updates.
2. Ensure the continuous GMS detector is properly initialized and receiving data.
3. Verify that the 1-second feature files contain all required columns for the GMS detector.

## Tests Passed

- Duplicate-index handling is working correctly
- ATR calculation is functioning as expected with proper warmup period
- Skip reason logging is capturing the reasons for skipped trades

This regression test has successfully identified the issues preventing TF-v3 from generating trades, providing clear direction for the next steps in development.
