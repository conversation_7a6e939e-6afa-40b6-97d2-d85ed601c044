# Debugging Log - Financial Discrepancy Investigation (Logging Attempts)

This document outlines the attempts made to enable detailed `DEBUG` level logging within the `Portfolio` and `Backtester` classes to diagnose financial discrepancies. Despite numerous approaches, the `DEBUG_PLAN:` log messages, intended to trace calculations, did not appear in the log files.

**Objective:** Add detailed `DEBUG` logs to `portfolio.py` and `backtester.py` to trace financial calculations.

**Initial State:**
- The system was functional enough to run backtests.
- `INFO` level logs were appearing correctly.
- The goal was to enable `DEBUG` level logs from specific class instances.

**Attempts and Observations:**

1.  **Initial Log Addition:**
    *   Added `self.logger.debug(f"DEBUG_PLAN: ...")` lines to relevant methods in `portfolio.py` and `backtester.py`.
    *   `self.logger` in these classes was initialized with `logging.getLogger(self.__class__.__name__)`.
    *   **Result:** `DEBUG_PLAN:` messages did not appear.

2.  **Ensuring Logger Levels in `setup_logging` (`run_backtest.py`):**
    *   Verified `setup_logging` set the root logger and file handler to `logging.DEBUG`.
    *   Explicitly set "Portfolio" and "Backtester" loggers to `logging.DEBUG` using `logging.getLogger("LoggerName").setLevel(logging.DEBUG)`.
    *   **Result:** `DEBUG_PLAN:` messages still did not appear. Test `DEBUG` messages emitted directly from `run_backtest.py` *using the same named loggers* did appear, confirming the basic setup was capable.

3.  **Forcing Logger Configuration in `setup_logging`:**
    *   Modified `setup_logging` to iterate through a list of specific logger names ("Portfolio", "Backtester").
    *   For each, it would:
        *   Set level to `DEBUG`.
        *   Clear existing handlers (`logger_obj.handlers[:]`).
        *   Add the globally defined file and console handlers.
        *   Set `logger_obj.propagate = False`.
    *   **Result:** `DEBUG_PLAN:` messages still did not appear. Test `DEBUG` messages from `run_backtest.py` continued to work.

4.  **Explicit Logger Names in Classes:**
    *   Modified `Portfolio.__init__` and `Backtester.__init__` to use `self.logger = logging.getLogger("Portfolio")` and `self.logger = logging.getLogger("Backtester")` respectively, instead of `logging.getLogger(self.__class__.__name__)`.
    *   **Result:** `DEBUG_PLAN:` messages still did not appear.

5.  **Passing Logger Instances to Constructors:**
    *   Modified `Portfolio.__init__` and `Backtester.__init__` to accept an optional `logger_instance` argument.
    *   Modified `run_backtest.py` to get logger instances after `setup_logging` and pass them to the constructors.
    *   **Initial Issue:** Encountered `TypeError: __init__() got an unexpected keyword argument 'logger_instance'`, suggesting file changes weren't being picked up by the interpreter immediately or consistently.
    *   **Workaround Attempts:**
        *   Deleted `.pyc` files.
        *   Attempted `sys.modules` deletion to force re-import.
        *   Temporarily simplified constructors to remove the `logger_instance` argument to get the script running, then planned to re-introduce logger passing.
    *   **Current State (before this report):** The `TypeError` was resolved by simplifying constructors, but the underlying issue of `DEBUG_PLAN:` logs not appearing from within class instances persisted even when those instances were modified to use explicitly named loggers matching those configured in `setup_logging`.

6.  **Forced Error Log:**
    *   Added `self.logger.error("FORCED_ERROR_LOG_IN_PORTFOLIO_INIT: ...")` in `Portfolio.__init__`.
    *   **Result:** This error-level message also did not appear, indicating the `self.logger` instance within the class was likely not using the handlers configured by `setup_logging`.

**Summary of Failure to Enable Detailed Debug Logs:**

Despite confirming that:
-   The `setup_logging` function in `run_backtest.py` correctly configures named loggers ("Portfolio", "Backtester") to `DEBUG` level and attaches appropriate file handlers.
-   Test `DEBUG` messages for these named loggers work when called directly from `run_backtest.py`.
-   The `DEBUG_PLAN:` log statements are physically present in the source code of `portfolio.py` and `backtester.py`.
-   The classes were modified to obtain their `self.logger` instances using the exact names "Portfolio" and "Backtester".

The `self.logger.debug(...)` calls (and even `self.logger.error(...)` calls) from within the `Portfolio` and `Backtester` class instances consistently failed to produce output in the log files.

**Possible Reasons for Persistent Logging Failure:**

1.  **Logger Instance Discrepancy:** The logger instances obtained by `logging.getLogger("LoggerName")` *inside* the class modules (at import time or instantiation) might be different objects or have a different configuration (level, handlers) than the logger objects of the same name configured by `setup_logging` in the main script. This could be due to the timing of module imports versus the execution of `setup_logging`.
2.  **Environment/Caching Issues:** The Python execution environment might not be consistently picking up the latest versions of the modified `.py` files, despite `apply_diff` reporting success and `.pyc` files being cleared. This was evidenced by the intermittent `TypeError` when attempting to pass logger instances.
3.  **Logging Hierarchy/Propagation:** While `propagate = False` was set, there might be a more complex interaction within Python's logging hierarchy that's preventing messages from reaching the intended file handler for these specific class-member loggers.
4.  **External Interference:** An unlikely but possible scenario is another imported module or part of the codebase re-configuring the logging system globally or affecting these specific loggers after `setup_logging` has run.

Due to these persistent issues in reliably enabling detailed `DEBUG` logs from within the class instances, direct tracing of calculations via these logs was not achieved. The focus should now shift to analyzing the existing `INFO` level logs and the reported discrepancies using other methods if the `git restore` makes the original discrepancy reappear.