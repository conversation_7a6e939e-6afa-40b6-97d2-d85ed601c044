# Hyperliquid Trading Bot: Project Structure & File Organization

This diagram maps the complete codebase structure with actual file paths, organized by functional areas and showing the relationship between legacy and modern system components.

## Project Structure Diagram

```mermaid
graph TD
    %% Root Project Structure
    Root["`🏠 **aerith_hyperliquid_bot/**
    📁 Project Root Directory`"]
    
    %% Core Detection Logic
    subgraph CoreDetection ["`🧠 **CORE DETECTION LOGIC**`"]
        DetectorPy["`📄 **hyperliquid_bot/core/detector.py**
        🎯 RegimeDetectorInterface
        🔧 GranularMicrostructureRegimeDetector
        🏭 get_regime_detector() factory`"]
        
        GMSDetectorPy["`📄 **hyperliquid_bot/core/gms_detector.py**
        🎯 ContinuousGMSDetector
        🔧 Adaptive threshold management
        ⚡ 1-second processing`"]
        
        UnifiedDetectorPy["`📄 **hyperliquid_bot/core/unified_gms_detector.py**
        ✨ UnifiedGMSDetector (PROPOSED)
        🔄 Mode: legacy | continuous
        🎯 Zero code duplication`"]
        
        StateMappingPy["`📄 **hyperliquid_bot/utils/state_mapping.py**
        🗺️ State standardization
        🔄 Regime name mapping`"]
    end
    
    %% Data Handling
    subgraph DataHandling ["`📊 **DATA HANDLING**`"]
        HandlerPy["`📄 **hyperliquid_bot/data/handler.py**
        🔌 DataHandlerInterface
        📥 OHLCV data loading
        🔗 Microstructure integration`"]
        
        MicrostructurePy["`📄 **hyperliquid_bot/features/microstructure.py**
        🧮 37 microstructure features
        📊 5-depth order book analysis
        🎯 Legacy system (raw2)`"]
        
        ETLScript["`📄 **tools/etl_l20_to_1s.py**
        🔄 Arrow → 1-second features
        📊 109 features (5-20 depth)
        🎯 Modern system (features_1s)`"]
        
        CalculatorPy["`📄 **hyperliquid_bot/signals/calculator.py**
        📈 SignalEngine
        🧮 Technical indicators
        📊 Feature computation`"]
    end
    
    %% Strategy Components
    subgraph Strategies ["`📈 **TRADING STRATEGIES**`"]
        EvaluatorPy["`📄 **hyperliquid_bot/strategies/evaluator.py**
        🎯 Strategy orchestration
        🔄 Regime-based activation`"]
        
        TFv2Py["`📄 **hyperliquid_bot/strategies/tf_v2.py**
        📊 Trend Following v2
        ⏰ Hourly execution
        🎯 Legacy system`"]
        
        TFv3Py["`📄 **hyperliquid_bot/strategies/tf_v3.py**
        📊 Trend Following v3
        ⏰ 60-second execution
        🎯 Modern system`"]
        
        OBIScalperPy["`📄 **hyperliquid_bot/strategies/obi_scalper.py**
        ⚡ OBI-based scalping
        🎯 Microstructure signals`"]
    end
    
    %% Configuration System
    subgraph Configuration ["`⚙️ **CONFIGURATION SYSTEM**`"]
        SettingsPy["`📄 **hyperliquid_bot/config/settings.py**
        🏗️ Pydantic models
        ✅ Configuration validation
        🔧 GMSSettings, RegimeSettings`"]
        
        BaseYaml["`📄 **configs/base.yaml**
        🎛️ Main configuration
        🔧 Unified GMS settings
        📊 Threshold values`"]
        
        ProfileConfigs["`📁 **configs/**
        📄 profile_modern_system.yaml
        📄 legacy_profile.yaml
        📄 test_*.yaml`"]
        
        MigrationPy["`📄 **hyperliquid_bot/utils/config_migration.py**
        🔄 GMSConfigMigrator (PROPOSED)
        ✅ Configuration validation
        🗂️ Legacy → Unified migration`"]
    end
    
    %% Optimization Modules
    subgraph Optimization ["`⚡ **PERFORMANCE OPTIMIZATION**`"]
        AdaptiveThresholdPy["`📄 **hyperliquid_bot/utils/adaptive_threshold.py**
        🐌 Current implementation (SLOW)
        ⏱️ 655s bottleneck (O(n²))`"]
        
        OptimizedThresholdPy["`📄 **hyperliquid_bot/utils/optimized_adaptive_threshold.py**
        ⚡ OptimizedAdaptiveThreshold (PROPOSED)
        🚀 Batch processing (O(n))
        💾 Memory pools`"]
        
        FeatureNamingPy["`📄 **hyperliquid_bot/utils/feature_naming.py**
        🏷️ Depth-agnostic naming
        🔧 get_obi_column_name()`"]
    end
    
    %% Backtesting & Execution
    subgraph Execution ["`🔄 **BACKTESTING & EXECUTION**`"]
        BacktesterPy["`📄 **hyperliquid_bot/backtester/backtester.py**
        📊 BacktesterConfig
        🎯 Performance simulation
        📈 Trade execution`"]
        
        RunBacktestPy["`📄 **run_backtest.py**
        🚀 Main entry point
        ⚙️ Configuration loading
        📊 Results output`"]
        
        ProfileScripts["`📁 **aerith_hyperliquid_bot/scripts/**
        📄 profile_modern_system.py
        📄 enhanced_legacy_profile_analysis.py
        ⏱️ Performance analysis`"]
    end
    
    %% Testing Framework
    subgraph Testing ["`🧪 **TESTING FRAMEWORK**`"]
        TestsDir["`📁 **tests/**
        🧪 Unit tests
        🔗 Integration tests
        ⏱️ Performance tests`"]
        
        UnifiedTestsPy["`📄 **tests/test_unified_gms_detector.py**
        ✅ UnifiedGMSDetector tests (PROPOSED)
        🔄 Mode compatibility tests
        ⏱️ Performance benchmarks`"]
        
        IntegrationTests["`📁 **tests/integration/**
        📄 test_gms_with_etl.py
        🔗 End-to-end testing`"]
    end
    
    %% Documentation
    subgraph Documentation ["`📚 **DOCUMENTATION**`"]
        DocsDir["`📁 **aerith_hyperliquid_bot/docs/**
        📄 Current documentation files`"]
        
        SpecificationMd["`📄 **docs/unified_gms_detector_specification_2025-05-30.md**
        📋 Technical specification
        🏗️ Implementation guide`"]
        
        PerformanceMd["`📄 **docs/modern_vs_legacy_performance_comparison_2025-05-30.md**
        📊 Performance analysis
        🔍 Bottleneck identification`"]
        
        ArchitectureMd["`📄 **docs/data_architecture_and_gms_systems.md**
        🏗️ System architecture
        📊 Data pipeline documentation`"]
    end
    
    %% Data Directories
    subgraph DataDirs ["`💾 **DATA DIRECTORIES**`"]
        HyperliquidData["`📁 **hyperliquid_data/**
        📊 External data directory`"]
        
        LegacyData["`📁 **hyperliquid_data/raw2/**
        📄 YYYYMMDD_raw2.parquet
        🎯 Legacy system (37 features)`"]
        
        ModernData["`📁 **hyperliquid_data/features_1s/**
        📄 YYYY-MM-DD/features_HH.parquet
        🎯 Modern system (109 features)`"]
        
        L2RawData["`📁 **hyperliquid_data/l2_raw/**
        📄 YYYY-MM-DD/BTC_HH_l2Book.txt
        🔄 Raw Arrow files`"]
    end
    
    %% Connections - Core Structure
    Root --> CoreDetection
    Root --> DataHandling
    Root --> Strategies
    Root --> Configuration
    Root --> Optimization
    Root --> Execution
    Root --> Testing
    Root --> Documentation
    Root --> DataDirs
    
    %% Internal Dependencies
    DetectorPy -.-> GMSDetectorPy
    DetectorPy -.-> UnifiedDetectorPy
    UnifiedDetectorPy -.-> OptimizedThresholdPy
    
    HandlerPy --> MicrostructurePy
    HandlerPy --> CalculatorPy
    ETLScript --> ModernData
    MicrostructurePy --> LegacyData
    
    EvaluatorPy --> TFv2Py
    EvaluatorPy --> TFv3Py
    EvaluatorPy --> OBIScalperPy
    
    SettingsPy --> BaseYaml
    MigrationPy -.-> BaseYaml
    
    BacktesterPy --> RunBacktestPy
    ProfileScripts --> PerformanceMd
    
    %% Color Coding
    classDef legacy fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef modern fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef unified fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef config fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef optimization fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef testing fill:#fce4ec,stroke:#ad1457,stroke-width:2px
    classDef docs fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef data fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef bottleneck fill:#ffebee,stroke:#c62828,stroke-width:3px
    
    %% Apply Classes
    class DetectorPy,TFv2Py,MicrostructurePy,LegacyData legacy
    class GMSDetectorPy,TFv3Py,ETLScript,ModernData modern
    class UnifiedDetectorPy,OptimizedThresholdPy,MigrationPy,UnifiedTestsPy unified
    class SettingsPy,BaseYaml,ProfileConfigs config
    class AdaptiveThresholdPy,FeatureNamingPy optimization
    class TestsDir,IntegrationTests testing
    class DocsDir,SpecificationMd,PerformanceMd,ArchitectureMd docs
    class HyperliquidData,L2RawData data
    class AdaptiveThresholdPy bottleneck
```

## File Organization Summary

### Core Detection Logic (`hyperliquid_bot/core/`)
- **detector.py**: Legacy GranularMicrostructureRegimeDetector + factory function
- **gms_detector.py**: Modern ContinuousGMSDetector with adaptive thresholds
- **unified_gms_detector.py**: 🆕 Proposed unified implementation

### Data Pipeline (`hyperliquid_bot/data/`, `hyperliquid_bot/features/`)
- **handler.py**: Data loading and integration interface
- **microstructure.py**: Legacy 37-feature calculation (5-depth)
- **calculator.py**: SignalEngine for technical indicators
- **tools/etl_l20_to_1s.py**: Modern 109-feature ETL (5-20 depth)

### Trading Strategies (`hyperliquid_bot/strategies/`)
- **evaluator.py**: Strategy orchestration and regime-based activation
- **tf_v2.py**: Legacy trend following (hourly, OHLCV-based)
- **tf_v3.py**: Modern trend following (60s, feature-based)
- **obi_scalper.py**: OBI-based scalping strategy

### Configuration System (`configs/`, `hyperliquid_bot/config/`)
- **base.yaml**: Main configuration with unified GMS settings
- **settings.py**: Pydantic models and validation
- **config_migration.py**: 🆕 Proposed migration utilities

### Performance Optimization (`hyperliquid_bot/utils/`)
- **adaptive_threshold.py**: Current slow implementation (655s bottleneck)
- **optimized_adaptive_threshold.py**: 🆕 Proposed fast implementation
- **feature_naming.py**: Depth-agnostic column naming utilities

### Data Storage Structure
```
hyperliquid_data/
├── l2_raw/YYYY-MM-DD/BTC_HH_l2Book.txt     # Raw Arrow files
├── raw2/YYYYMMDD_raw2.parquet              # Legacy 37 features
├── features_1s/YYYY-MM-DD/features_HH.parquet  # Modern 109 features
└── resampled_l2/1h/YYYY-MM-DD_1h.parquet   # Hourly aggregation
```

## Migration Path: Legacy → Unified

### Phase 1: Core Unification
1. Create `unified_gms_detector.py` with mode-based branching
2. Update factory function in `detector.py`
3. Implement configuration resolution logic

### Phase 2: Optimization Integration
1. Import `optimized_adaptive_threshold.py` optimizations
2. Add performance monitoring and fallback mechanisms
3. Integrate batch processing for adaptive thresholds

### Phase 3: Configuration Migration
1. Implement `config_migration.py` utilities
2. Update `base.yaml` with unified schema
3. Create validation and migration tools

### Phase 4: Testing & Validation
1. Comprehensive test suite in `test_unified_gms_detector.py`
2. Performance regression testing
3. End-to-end integration validation

## Key Architectural Benefits

1. **Zero Code Duplication**: Single detector implementation
2. **Backward Compatibility**: All existing configurations preserved
3. **Performance Optimization**: Integrated adaptive threshold improvements
4. **Unified Configuration**: Single source of truth for GMS settings
5. **Clean Architecture**: Separation of concerns and modular design
