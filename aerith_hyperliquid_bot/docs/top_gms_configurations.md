# Top GMS Configurations from Grid Search

This document records the top-performing GMS configurations identified through systematic grid search testing conducted on May 5, 2025.

## Overview

The grid search tested combinations of parameters:
- OBI Window (OBI_WIN): Time window for OBI calculation
- OBI Threshold (OBI_THR): Threshold for OBI significance
- Spread Threshold (SPD_THR): Threshold for spread significance
- Confirmation Bars (CONF_BARS): Number of bars required for confirmation

All configurations were evaluated against multiple metrics, with Sharpe/DD Ratio being the primary sorting criterion.

## Top 3 Configurations

### Configuration #1 (RunID 1)

| Parameter | Value |
|-----------|-------|
| OBI Window | 8 |
| OBI Threshold | 0.15 |
| Spread Threshold | 0.000026 |
| Confirmation Bars | 1 |

**Performance Metrics:**
- Sharpe Ratio: 4.61
- Max Drawdown: -7.53%
- ROI: 221.41%
- Profit Factor: 2.57
- Sharpe/DD Ratio: 61.22

### Configuration #2 (RunID 3)

| Parameter | Value |
|-----------|-------|
| OBI Window | 8 |
| OBI Threshold | 0.15 |
| Spread Threshold | 0.000044 |
| Confirmation Bars | 1 |

**Performance Metrics:**
- Sharpe Ratio: 4.46
- Max Drawdown: -7.53%
- ROI: 235.35%
- Profit Factor: 2.39
- Sharpe/DD Ratio: 59.23

### Configuration #3 (RunID 11)

| Parameter | Value |
|-----------|-------|
| OBI Window | 14 |
| OBI Threshold | 0.15 |
| Spread Threshold | 0.000044 |
| Confirmation Bars | 1 |

**Performance Metrics:**
- Sharpe Ratio: 3.64
- Max Drawdown: -6.20%
- ROI: 111.69%
- Profit Factor: 2.42
- Sharpe/DD Ratio: 58.71

## Analysis

1. **All top configurations** use a single confirmation bar, suggesting that requiring additional confirmation bars reduces performance.

2. **OBI threshold is consistently 0.15** across all top performers, indicating this is a robust parameter value.

3. **Spread threshold varies** between 0.000026 and 0.000044, with the lower value producing slightly better results in combination with an OBI window of 8.

4. **OBI window of 8** appears in the two best configurations, though a window of 14 also performs well with the right combination of other parameters.

5. **Performance significantly exceeds PRD criteria** of PF ≥ 1.15 and DD ≤ 20%, suggesting the GMS approach is viable with the L2-derived data.

## Recommendation

Based on these results, Configuration #1 should be used as the primary GMS configuration for the trading system, with the following settings:

```yaml
regime:
  gms:
    obi_window: 8
    obi_threshold: 0.15
    spread_threshold: 0.000026
    confirmation_bars: 1
```

For robustness testing, Configuration #2 and #3 could also be evaluated in out-of-sample periods.
