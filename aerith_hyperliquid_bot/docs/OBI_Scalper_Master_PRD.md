# OBI Scalper & TF‑v2 Integration — Master PRD

*(created 2025‑05‑21 · synced 2025‑05‑22 UTC)*

---

## 0 Pur<PERSON> & Scope

Retail‑grade trading stack for Hyperliquid perpetual futures:

* **GMS Regime Detector** (baseline, untouched – future fine‑tune)
* **Trend‑Follower v2 (TF‑v2)** with optional OBI filter
* **OBI Scalper** leveraging short‑term order‑book imbalance

Runs on single PC/VM (≤ 500 ms RTT) after taker fees.

---

## 1 Workflow & Roles

| Role | Responsibility |
| ---------------------- | ------------------------------------------------------ |
| **ChatGPT Supervisor** | Break tasks, arbitrate, maintain this PRD, code‑review |
| **Gemini 2.5 Pro** | Draft complex algorithm/strategy code |
| **IDE Agents** | Cursor AI, Cline, Roo, Augment Code – implement/tests |
| **Human Owner** | Final commits, data uploads, live deployment |

---

## 2 Task Timeline (Completed)

| ID | Date | Outcome |
| ---------- | ---------- | --------------------------------------------------------- |
| T‑101 | 2025‑05‑21 | Multi‑depth OBI feature layer ✅ |
| T‑102a | 2025‑05‑21 | Dynamic signal‑name bug‑fix ✅ |
| T‑102b | 2025‑05‑21 | `smooth_signal` & `zscore` helpers ✅ |
| T‑103 | 2025‑05‑21 | Down‑stream components use smoothed OBI ✅ |
| T‑104 | 2025‑05‑21 | `OBIScalperStrategy` skeleton wired in ✅ |
| T‑105 | 2025‑05‑21 | RiskManager integration for scalper ✅ |
| **T‑107d** | 2025‑05‑22 | Exploratory Data Analysis complete – thresholds derived ✅ |

---

## 3 Key Findings from T‑107d (EDA)

* **Predictive edge strongest in calm regimes**: ρ(OBI L1‑10, +1 s return) ≈ 0.45 when realised vol < 6 bps and spread < 4 ×10⁻⁵.
* Edge degrades in chaotic slice (ρ≈0.25); therefore, add vol/spread veto.
* Extreme shallow OBI values concentrate around |0.8 – 1.0| → use ±0.80 trigger level.

### Entry / Exit Parameter Set

| Parameter | Value |
| --------------------- | ----------------------------------------------------------------------------- |
| **Vol veto** | realised\_vol\_1s > 6 bps ⇒ no trade |
| **Spread veto** | spread\_relative > 4 × 10⁻⁵ ⇒ no trade |
| **Long trigger** | raw\_obi\_L1\_3 > +0.80 **AND** sign(raw\_obi\_L1\_10)==sign(raw\_obi\_L1\_3) |
| **Short trigger** | raw\_obi\_L1\_3 < –0.80 … |
| **TP / SL / Timeout** | +7 ticks / –5 ticks / 30 s |

> **Note‑to‑future**: Repeat this EDA quarterly & feed results into **GMS fine‑tune** and **TF‑v2 modernisation**.

---

## 4 Upcoming Tasks

| ID | Description | Owner | Depends |
| --------- | ----------------------------------------------------------------------------------------------------- | ----------------------- | ---------------------------- |
| **T‑106** | Implement entry/exit logic above in `OBIScalperStrategy`; add veto filters, unit & integration tests. | Gemini 2.5 Pro → Cursor | T‑105, T‑107d |
| **T‑108** | Schedule quarterly OBI‑edge EDA & integrate with GMS (future). | ChatGPT | after T‑106‑107 live results |

---

## 5 Data Assets

| Slice | Files | Rows (1 s) |
| ----------------------------------------------------------------------- | ------------------------------------------------ | ---------- |
| Calm hour | `calm_hour_slice_sample.csv.gz`, summary JSON | 3 600 |
| Chaotic wick | `chaotic_wick_slice_sample.csv.gz`, summary JSON | 1 800 |
| (All files stored under `/mnt/data/` and version‑controlled elsewhere.) | | |

---

## 6 Message to Future ChatGPT Supervisor

> You are resuming after chat break. T‑106 is next: use the threshold table in §3. If PRD diverges between repo & canvas, prioritise **repo copy**. Remember to revisit GMS fine‑tune in T‑108 after scalper proves live edge. 