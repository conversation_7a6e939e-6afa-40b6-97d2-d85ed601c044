# Profile Modern System Backtest Analysis Report

**Date**: June 2, 2025  
**Configuration**: `profile_modern_system.yaml`  
**Test Period**: March 2, 2025 - March 22, 2025 (20 days)  
**Strategy**: TF-v3 with Continuous GMS Regime Detection  

---

## Executive Summary

The backtest of the Profile Modern System configuration revealed **significant performance issues** and **critical technical problems** that require immediate attention. The system generated a **-2.31% return** over 20 days with poor risk-adjusted metrics and multiple technical failures.

### Key Findings:
- ❌ **Negative Performance**: -$230.50 loss on $10,000 initial capital
- ❌ **Technical Issues**: EMA calculations failing (77/84 evaluations)
- ❌ **Data Problems**: Missing OHLCV history and signal fields
- ❌ **Regime Detection**: Overly restrictive, blocking most trading opportunities
- ⚠️ **High Stop Loss Rate**: 43% of trades stopped out

---

## 📊 Performance Metrics

### Overall Results
| Metric | Value | Assessment |
|--------|-------|------------|
| **Net P&L** | -$230.50 (-2.31%) | ❌ Poor |
| **Sharpe Ratio** | -1.55 | ❌ Very Poor |
| **Max Drawdown** | 5.61% | ⚠️ Moderate |
| **Annual Return** | -33.40% | ❌ Unacceptable |
| **Win Rate** | 42.9% (3/7) | ⚠️ Below Average |
| **Profit Factor** | 0.71 | ❌ Unprofitable |

### Trade Statistics
- **Total Trades**: 7 (all long positions)
- **Average Trade P&L**: -$30.71
- **Best Trade**: +$330.99
- **Worst Trade**: -$263.60
- **Average Duration**: 21.4 hours
- **Win/Loss Ratio**: 0.95

---

## 🔍 Detailed Trade Analysis

### Winning Trades (3 trades, +$532.66 total)
1. **Trade #2** (Mar 4-5): +$195.61
   - Entry: $83,060 → Exit: $88,316 (time exit)
   - Duration: 25 hours
   
2. **Trade #5** (Mar 7-8): +$6.06
   - Entry: $85,896 → Exit: $86,063 (time exit)
   - Duration: 25 hours
   
3. **Trade #7** (Mar 10-11): +$330.99
   - Entry: $77,643 → Exit: $83,249 (time exit)
   - Duration: 25 hours

### Losing Trades (4 trades, -$747.61 total)
1. **Trade #1** (Mar 3-4): -$263.60
   - Entry: $87,120 → Exit: $82,745 (stop loss)
   - Duration: 19 hours
   
2. **Trade #3** (Mar 6-7): -$205.61
   - Entry: $91,015 → Exit: $86,678 (stop loss)
   - Duration: 22 hours
   
3. **Trade #4** (Mar 7-8): -$44.87
   - Entry: $88,225 → Exit: $86,143 (time exit)
   - Duration: 25 hours
   
4. **Trade #6** (Mar 10-11): -$233.53
   - Entry: $79,834 → Exit: $76,481 (stop loss)
   - Duration: 9 hours

---

## ⚠️ Critical Technical Issues

### 1. EMA Calculation Failures
**Severity**: 🔴 Critical
```
[WARNING] TFV3Strategy[tf_v3] : Invalid EMA values: Fast=nan, Slow=nan
```
- **Impact**: 77 out of 84 strategy evaluations failed
- **Root Cause**: EMA indicators returning NaN values
- **Consequence**: Strategy forced to use close price fallbacks
- **Action Required**: Debug EMA calculation pipeline

### 2. Missing Historical Data
**Severity**: 🔴 Critical
```
[WARNING] TFV3Strategy[tf_v3] : No OHLCV history available, using current signals only
```
- **Impact**: Strategy cannot perform proper trend analysis
- **Root Cause**: Historical data not being loaded or cached
- **Action Required**: Fix data loading mechanism

### 3. Missing Signal Fields
**Severity**: 🟡 High
```
[INFO] Backtester : Strategy tf_v3 missing signals: ['volume', 'regime_timestamp', 'risk_suppressed']
```
- **Impact**: Strategy operating with incomplete information
- **Action Required**: Ensure all required signals are generated

### 4. Regime Detection Issues
**Severity**: 🟡 High
- **Observation**: Most periods classified as "Uncertain" regime
- **Impact**: Strategy blocked from trading most of the time
- **Current Mapping**: Low_Vol_Range → BEAR (only tradeable regime)
- **Action Required**: Review and tune GMS thresholds

---

## 📈 Regime Analysis

### Regime Distribution
- **Uncertain**: ~85% of the time (no trading allowed)
- **Low_Vol_Range**: ~15% of the time (mapped to BEAR, trading allowed)
- **High_Vol_Range**: Present in losing trades analysis

### Current GMS Configuration Issues
```yaml
continuous_gms:
  gms_vol_high_thresh: 0.015    # May be too restrictive
  gms_vol_low_thresh: 0.005     # May be too restrictive
  gms_mom_strong_thresh: 1.0    # May be too high
  gms_mom_weak_thresh: 0.2      # May be too high
```

---

## 💰 Cost Analysis

### Trading Costs Breakdown
| Cost Type | Total | Per Trade | Impact |
|-----------|-------|-----------|---------|
| **Entry Fees** | $8.87 | $1.27 | Moderate |
| **Exit Fees** | $8.87 | $1.27 | Moderate |
| **Slippage** | $42.85 | $6.12 | High |
| **Funding** | $6.66 | $0.95 | Low |
| **Total Costs** | $67.25 | $9.61 | **High** |

**Analysis**: Trading costs represent 29% of gross losses, indicating significant friction.

---

## 🎯 Root Cause Analysis

### Primary Issues
1. **Technical Infrastructure**: EMA calculations and data loading failures
2. **Regime Calibration**: Overly restrictive thresholds preventing trade generation
3. **Risk Management**: Stop losses too tight (43% stop-out rate)
4. **Market Conditions**: Test period may have been unfavorable for trend following

### Secondary Issues
1. **Position Sizing**: 15% risk fraction with 3x leverage may be aggressive
2. **Signal Quality**: Missing volume and regime timestamp data
3. **Execution**: High slippage costs suggest execution model issues

---

## 🛠️ Recommended Actions

### Immediate (Critical)
1. **Fix EMA Calculations**
   - Debug why EMAs return NaN values
   - Ensure sufficient historical data for calculation
   - Add proper error handling and logging

2. **Resolve Data Issues**
   - Fix OHLCV history loading
   - Ensure volume data availability
   - Add regime timestamp generation

3. **Complete Signal Pipeline**
   - Add missing risk_suppressed field
   - Verify all required signals are generated
   - Add signal validation checks

### Short-term (High Priority)
1. **Regime Tuning**
   - Lower volatility thresholds (0.015 → 0.010, 0.005 → 0.003)
   - Reduce momentum thresholds (1.0 → 0.5, 0.2 → 0.1)
   - Test different state mapping configurations

2. **Risk Management Review**
   - Analyze stop loss effectiveness
   - Consider wider stops or different exit strategies
   - Review position sizing methodology

3. **Execution Optimization**
   - Investigate high slippage costs
   - Review order execution simulation
   - Consider maker vs taker strategies

### Medium-term (Optimization)
1. **Parameter Optimization**
   - Run systematic parameter sweeps
   - Test different EMA periods (current: 5/15)
   - Optimize ATR multipliers for stops/targets

2. **Extended Testing**
   - Test on longer time periods
   - Include different market conditions
   - Add out-of-sample validation

3. **Strategy Enhancement**
   - Consider additional entry filters
   - Add regime-specific parameter sets
   - Implement dynamic position sizing

---

## 📊 Configuration Recommendations

### Suggested GMS Threshold Adjustments
```yaml
continuous_gms:
  # More permissive volatility thresholds
  gms_vol_high_thresh: 0.010    # Reduced from 0.015
  gms_vol_low_thresh: 0.003     # Reduced from 0.005
  
  # More sensitive momentum thresholds
  gms_mom_strong_thresh: 0.5    # Reduced from 1.0
  gms_mom_weak_thresh: 0.1      # Reduced from 0.2
  
  # Wider spread thresholds
  gms_spread_std_high_thresh: 0.002   # Increased from 0.001
  gms_spread_mean_low_thresh: 0.0001  # Reduced from 0.00005
```

### Suggested TF-v3 Adjustments
```yaml
tf_v3:
  # More conservative risk management
  risk_frac: 0.10              # Reduced from 0.15
  atr_trail_k: 3.0             # Increased from 2.5
  
  # Longer trend confirmation
  ema_fast: 8                  # Increased from 5
  ema_slow: 21                 # Increased from 15
```

---

## 🔬 Market Context Analysis

### Test Period Characteristics (Mar 2-22, 2025)
- **Market Regime**: Predominantly ranging/choppy conditions
- **Volatility**: High volatility ranges observed
- **Trend Quality**: Limited sustained trends for trend-following strategy
- **Price Action**: BTC range approximately $76,000 - $91,000

### Strategy-Market Fit
- **TF-v3 Strategy**: Designed for trending markets
- **Test Period**: Characterized by ranging conditions
- **Mismatch**: Strategy may be poorly suited to test period conditions
- **Recommendation**: Test on trending periods for fair evaluation

---

## 📋 Testing Checklist

### Before Next Backtest
- [ ] Fix EMA calculation issues
- [ ] Resolve OHLCV history loading
- [ ] Add missing signal fields
- [ ] Validate regime detection pipeline
- [ ] Test on known trending periods
- [ ] Implement suggested parameter changes
- [ ] Add comprehensive logging
- [ ] Verify execution simulation accuracy

### Validation Tests
- [ ] Unit tests for EMA calculations
- [ ] Integration tests for signal pipeline
- [ ] Regime detection accuracy tests
- [ ] Cost model validation
- [ ] Historical data integrity checks

---

## 📈 Success Metrics for Next Test

### Minimum Acceptable Performance
- **Sharpe Ratio**: > 0.5
- **Win Rate**: > 50%
- **Max Drawdown**: < 10%
- **Profit Factor**: > 1.2

### Technical Requirements
- **EMA Failures**: < 5%
- **Missing Signals**: 0%
- **Regime Coverage**: > 30% tradeable time
- **Data Availability**: 100%

---

## 🎯 Conclusion

The Profile Modern System backtest revealed fundamental technical issues that must be resolved before meaningful performance evaluation can occur. While the strategy framework shows promise, the current implementation suffers from:

1. **Critical technical failures** in core calculations
2. **Overly restrictive regime detection** limiting trading opportunities
3. **Data pipeline issues** affecting strategy operation
4. **Suboptimal risk management** leading to frequent stop-outs

**Priority**: Focus on technical fixes before performance optimization. The strategy cannot be fairly evaluated until these foundational issues are resolved.

**Next Steps**: Implement the immediate action items, conduct thorough testing, and re-run the backtest with corrected configuration.

---

*Report generated on June 2, 2025*  
*Configuration tested: `configs/overrides/profile_modern_system.yaml`*  
*Data source: Backtest run 20250602_230353* 