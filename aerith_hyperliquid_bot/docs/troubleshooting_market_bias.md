# Troubleshooting Market Bias Parameters

## Overview

This document provides a structured approach to diagnose and fix issues with the Market Bias feature, particularly when changes to parameters like `bull_leverage_factor` don't produce expected results in backtests.

## Key Components to Understand

1. **Market Bias Implementation Path**:
   - Configured in `configs/base.yaml` under `regime.market_bias`
   - Applied in `RiskManager.calculate_position()` method in `risk.py`
   - Logs detailed information with prefix `MARKET BIAS:`

2. **Prerequisites for Market Bias to Work**:
   - `market_bias.enabled` must be set to `true`
   - Regime must be correctly mapped to a market state (BULL, BEAR, CHOP)
   - For direction-specific biases, the strategy must provide direction information

## Diagnostic Steps

### 1. Verify Basic Configuration

```yaml
regime:
  market_bias:
    enabled: true  # Must be true
    # Other parameters...
```

Ensure `enabled` is set to `true`. If it's `false`, none of the market bias adjustments will be applied.

### 2. Check Log Output During Backtests

Look for these key log messages:

```
MARKET BIAS: Mapped regime 'Strong_Bull_Trend' to market state 'BULL'
MARKET BIAS: Applying adjustments for BULL market
MARKET BIAS: Applied market risk factor: 1.20x, resulting in total risk factor: 0.96x
```

If you don't see these messages, the market bias might not be activating at all.

### 3. Analyze Market State Distribution

Add this code to `_compute_and_log_metrics()` in `backtester.py` to see the distribution of market states:

```python
# Add after other metrics are calculated
self.logger.info(f"\n{BOLD}{BLUE}{'MARKET STATE ANALYSIS'.center(80)}{ENDC}")

if 'all_signals_df' in dir(self) and 'regime' in self.all_signals_df.columns:
    # Count regimes
    regime_counts = self.all_signals_df['regime'].value_counts()
    self.logger.info(f"\n{BOLD}Regime Distribution:{ENDC}")
    for regime, count in regime_counts.items():
        percent = (count / len(self.all_signals_df)) * 100
        self.logger.info(f"  • {regime:<25} {count:>5} ({percent:.1f}%)")
    
    # Count market states (calculate them first)
    self.all_signals_df['market_state'] = 'UNKNOWN'
    bull_regimes = ["Strong_Bull_Trend", "Weak_Bull_Trend"]
    bear_regimes = ["Strong_Bear_Trend", "Weak_Bear_Trend"]
    chop_regimes = ["Ranging", "Volatile_Chop", "Low_Vol_Chop", "High_Vol_Range", "Sideways", "Choppy"]
    
    self.all_signals_df.loc[self.all_signals_df['regime'].isin(bull_regimes), 'market_state'] = 'BULL'
    self.all_signals_df.loc[self.all_signals_df['regime'].isin(bear_regimes), 'market_state'] = 'BEAR'
    self.all_signals_df.loc[self.all_signals_df['regime'].isin(chop_regimes), 'market_state'] = 'CHOP'
    
    market_state_counts = self.all_signals_df['market_state'].value_counts()
    self.logger.info(f"\n{BOLD}Market State Distribution:{ENDC}")
    for state, count in market_state_counts.items():
        percent = (count / len(self.all_signals_df)) * 100
        self.logger.info(f"  • {state:<10} {count:>5} ({percent:.1f}%)")
```

If you see very few BULL states (e.g., <5%), changes to `bull_leverage_factor` will have minimal impact.

### 4. Check Trade Distribution by Market State

Add this code to analyze trades by market state:

```python
# Add to _analyze_market_conditions() in backtester.py
if self.portfolio.trades:
    trade_df = pd.DataFrame(self.portfolio.trades)
    # Add market_state to trades based on entry_regime
    trade_df['market_state'] = 'UNKNOWN'
    bull_regimes = ["Strong_Bull_Trend", "Weak_Bull_Trend"]
    bear_regimes = ["Strong_Bear_Trend", "Weak_Bear_Trend"]
    chop_regimes = ["Ranging", "Volatile_Chop", "Low_Vol_Chop", "High_Vol_Range", "Sideways", "Choppy"]
    
    trade_df.loc[trade_df['entry_regime'].isin(bull_regimes), 'market_state'] = 'BULL'
    trade_df.loc[trade_df['entry_regime'].isin(bear_regimes), 'market_state'] = 'BEAR'
    trade_df.loc[trade_df['entry_regime'].isin(chop_regimes), 'market_state'] = 'CHOP'
    
    self.logger.info(f"\n{BOLD}Trades by Market State:{ENDC}")
    state_counts = trade_df['market_state'].value_counts()
    for state, count in state_counts.items():
        percent = (count / len(trade_df)) * 100
        trades_in_state = trade_df[trade_df['market_state'] == state]
        avg_profit = trades_in_state['profit'].mean() if not trades_in_state.empty else 0
        win_rate = (trades_in_state['profit'] > 0).mean() * 100 if not trades_in_state.empty else 0
        self.logger.info(f"  • {state:<10} {count:>3} trades ({percent:.1f}%) | Avg Profit: ${avg_profit:.2f} | Win Rate: {win_rate:.1f}%")
    
    # Also analyze by direction within each state
    self.logger.info(f"\n{BOLD}Trades by Direction and Market State:{ENDC}")
    for state in ['BULL', 'BEAR', 'CHOP']:
        trades_in_state = trade_df[trade_df['market_state'] == state]
        if trades_in_state.empty:
            continue
            
        for direction in ['long', 'short']:
            direction_trades = trades_in_state[trades_in_state['type'] == direction]
            if direction_trades.empty:
                continue
                
            count = len(direction_trades)
            percent = (count / len(trades_in_state)) * 100
            avg_profit = direction_trades['profit'].mean()
            win_rate = (direction_trades['profit'] > 0).mean() * 100
            self.logger.info(f"  • {state:<10} {direction:<5} {count:>3} trades ({percent:.1f}%) | Avg Profit: ${avg_profit:.2f} | Win Rate: {win_rate:.1f}%")
```

### 5. Verify Strategy Direction Information

Add temporary logging in the `calculate_position` method in `risk.py` to check if direction information is being correctly passed:

```python
# Add after line 180 in risk.py in the market bias section
direction = "unknown"
if strategy_info and 'direction' in strategy_info:
    direction = strategy_info['direction'].lower()  # Normalize to lowercase
self.logger.info(f"DIRECTION DEBUG: Strategy '{strategy_name}' provided direction: '{direction}'")
```

If you consistently see "unknown" direction, this explains why direction-specific biases aren't applying.

### 6. Extreme Parameter Testing

Use extreme parameter values to force a noticeable effect:

```yaml
regime:
  market_bias:
    bull_leverage_factor: 5.0  # Extremely high for test purposes
    bear_leverage_factor: 0.2  # Extremely low for test purposes
```

If these extreme values still produce no change, there's likely a fundamental issue with the feature activation.

### 7. Trade Signal Analysis

Add this code to save all trades with their market state and leverage info:

```python
# Add to run() method in backtester.py after the simulation loop
if self.portfolio.trades:
    # Create detailed trade record with bias-related information
    trade_records = []
    for trade in self.portfolio.trades:
        # Map regime to market state
        market_state = "UNKNOWN"
        regime = trade.get("entry_regime", "")
        if regime in ["Strong_Bull_Trend", "Weak_Bull_Trend"]:
            market_state = "BULL"
        elif regime in ["Strong_Bear_Trend", "Weak_Bear_Trend"]:
            market_state = "BEAR"
        else:
            market_state = "CHOP"
            
        # Create enhanced record
        enhanced_trade = {
            **trade,  # Include all original trade data
            "market_state": market_state,
            "expected_leverage_factor": {
                "BULL": self.config.regime.market_bias.bull_leverage_factor,
                "BEAR": self.config.regime.market_bias.bear_leverage_factor,
                "CHOP": self.config.regime.market_bias.chop_leverage_factor,
            }.get(market_state, 1.0),
            "expected_risk_factor": {
                "BULL": self.config.regime.market_bias.bull_risk_factor,
                "BEAR": self.config.regime.market_bias.bear_risk_factor,
                "CHOP": self.config.regime.market_bias.chop_risk_factor,
            }.get(market_state, 1.0),
        }
        trade_records.append(enhanced_trade)
    
    # Save enhanced trades
    enhanced_trades_path = log_dir / f"enhanced_trades_{run_timestamp}.json"
    with open(enhanced_trades_path, "w") as f:
        json.dump(trade_records, f, indent=2, default=str)
    self.logger.info(f"Saved {len(trade_records)} enhanced trade records to {enhanced_trades_path}")
```

## Common Issues and Solutions

### 1. No BULL Market States Detected

**Problem**: The backtesting period doesn't contain sufficient bullish market conditions.

**Solutions**:
- Test with a different date range known to include bull markets
- Modify the regime mapping to classify more regimes as BULL
- Create a customized backtest that forces all regimes to be "Strong_Bull_Trend" for testing

### 2. Direction Information Missing

**Problem**: The strategy doesn't provide direction information to the risk manager.

**Solutions**:
- Verify that all strategy instances properly include direction in the `strategy_info` dictionary
- Add a fallback mechanism in the risk manager to use the direction ('long'/'short') directly if available

### 3. Global Leverage Limits Override Bias

**Problem**: The global `min_leverage` and `max_leverage` settings may be restricting the bias adjustments.

**Solution**:
- Check if the market-adjusted leverage is being capped by `cfg.core.max_leverage`
- Look for log messages like `FINAL LEVERAGE: 1.50x from Base=1.50x with adjustments: Market-Bias-Based` to confirm adjustments

### 4. Conflicting Adjustments Cancel Out

**Problem**: Multiple adjustment factors might be canceling each other out.

**Solution**:
- Try isolating effects by temporarily disabling dynamic risk adjustment (`dynamic_risk_adjustment: false`)
- Test with simplified configurations that only adjust one factor at a time

## Validation Experiments

1. **Force Bull Market Backtest**:
```python
# Add this in backtester.py _run_simulation_loop method
# Before regime detection code
current_signals['regime'] = "Strong_Bull_Trend"  # Force bull market for testing
```

2. **Try Different Parameter Combinations**:
```yaml
# Test 1: Extreme leverage differentials
bull_leverage_factor: 3.0
bear_leverage_factor: 0.3
chop_leverage_factor: 0.5

# Test 2: Focus on risk factors
bull_risk_factor: 3.0
bear_risk_factor: 0.3
chop_risk_factor: 0.5

# Test 3: Direction biases
bull_long_bias: 2.0
bull_short_bias: 0.1
bear_long_bias: 0.1
bear_short_bias: 2.0
```

3. **Compare Trades Before/After**:
   - Run a baseline backtest with `market_bias.enabled: false`
   - Run the same backtest with `market_bias.enabled: true` and extreme parameters
   - Compare trade counts, win rates, and average profits

## Summary

Debugging market bias parameters requires a methodical approach:

1. Verify the feature is properly enabled
2. Check logs to confirm market bias adjustments are being applied
3. Analyze the distribution of market states in your backtest period
4. Verify strategy direction information is being passed correctly
5. Test with extreme parameter values to force noticeable effects
6. Add diagnostic logging to track adjustments at each step

By systematically following these steps, you can identify exactly why changes to market bias parameters might not be having the expected impact on your backtesting results.
