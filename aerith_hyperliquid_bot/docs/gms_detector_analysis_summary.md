# GMS Detector Implementation Analysis Summary

## Overview
This document summarizes the analysis of the current GMS detector implementations to inform the creation of a unified detector PRD.

## 1. Current Architecture

### 1.1 Two Detector Implementations

#### GranularMicrostructureRegimeDetector (Legacy)
- **Location**: `hyperliquid_bot/core/detector.py` (lines 306-910)
- **Size**: ~604 lines
- **Purpose**: Original hourly regime detection for tf-v2
- **Key Characteristics**:
  - Returns string regime states
  - Fixed depth levels (5)
  - No adaptive thresholds
  - Part of RegimeDetectorInterface hierarchy
  - Directly integrated into detector.py

#### ContinuousGMSDetector (Modern)
- **Location**: `hyperliquid_bot/core/gms_detector.py` (lines 53-1272)
- **Size**: ~1,219 lines
- **Purpose**: Enhanced continuous detection for tf-v3
- **Key Characteristics**:
  - Returns dict with state + risk_suppressed flag
  - Configurable depth levels (5-20)
  - Supports adaptive thresholds via AdaptiveThreshold class
  - Standalone file with own interface definition
  - Includes state collapse functionality (8-state to 3/4-state)

### 1.2 Code Duplication Analysis

**Duplicated Logic (~70%)**:
- Core regime determination logic (`_determine_state`)
- Signal validation and NaN handling
- Threshold-based classification (vol/mom/OBI/spread)
- State validation and standardization
- Configuration parameter resolution
- Logging patterns

**Unique Features**:
- **Legacy**: Simpler output, fixed thresholds, confirmation bars
- **Modern**: Risk suppression, adaptive thresholds, state collapse, priming

## 2. Configuration Complexity

### 2.1 Configuration Paths
Currently, configuration is scattered across multiple sections:

```yaml
# Path 1: regime.detector_type
regime:
  detector_type: 'granular_microstructure'  # or 'continuous_gms'
  
# Path 2: gms.detector_type (duplicates Path 1)
gms:
  detector_type: 'continuous_gms'
  
# Path 3: Detector-specific settings
regime:
  granular_microstructure:
    gms_vol_high_thresh: 0.92
  continuous_gms:
    gms_vol_high_thresh: 0.03
```

### 2.2 Threshold Configuration
Thresholds are duplicated across detectors with different scales:
- **Legacy**: ATR% scale (0.92), MA slope scale (100.0)
- **Modern**: Different ATR% scale (0.03), MA slope scale (2.5)

## 3. Integration Points

### 3.1 Factory Pattern
Both detectors are instantiated via `get_regime_detector()` in detector.py:
```python
elif detector_type == 'granular_microstructure':
    return GranularMicrostructureRegimeDetector(config)
elif detector_type == 'continuous_gms':
    return ContinuousGMSDetector(config)
```

### 3.2 Strategy Integration
- **tf-v2**: Expects string regime from detector
- **tf-v3**: Uses GMSProvider which wraps detector, expects dict output

### 3.3 State Mapping
- Centralized in `hyperliquid_bot/utils/state_mapping.py`
- Maps 8 GMS states to 3-state model (BULL/BEAR/CHOP)
- Configurable via YAML mapping file

## 4. Performance Characteristics

### 4.1 Legacy Detector
- Optimized performance: 48.95s for full backtest
- 184 trades generated
- Uses raw2/resampled_l2 data pipeline

### 4.2 Modern Detector  
- Performance bottleneck with adaptive thresholds: 663.45s
- Optimized with AdaptiveThreshold class
- Uses features_1s data pipeline
- Includes priming optimization for historical data

## 5. Key Differences Summary

| Aspect | Legacy (Granular) | Modern (Continuous) |
|--------|------------------|---------------------|
| **Output** | String | Dict {state, risk_suppressed} |
| **Cadence** | 3600s (hourly) | 60s (configurable) |
| **Thresholds** | Fixed only | Fixed + Adaptive |
| **Depth** | Fixed 5 | Configurable 5-20 |
| **State Count** | 8 states | 8 states + collapse to 3/4 |
| **Risk Management** | None | Risk suppression flag |
| **Data Source** | raw2/resampled_l2 | features_1s |
| **Performance** | 48.95s | 663.45s (unoptimized) |

## 6. Unification Opportunities

### 6.1 Common Core Logic
Both detectors share ~70% of their core logic, which can be extracted into:
- Base signal validation
- Threshold-based classification
- State combination logic
- Configuration resolution

### 6.2 Mode-Based Branching
Differences can be handled through mode-specific methods:
- `_init_legacy_mode()` vs `_init_continuous_mode()`
- `_format_output_legacy()` vs `_format_output_continuous()`
- `_get_thresholds_legacy()` vs `_get_thresholds_adaptive()`

### 6.3 Unified Configuration
New structure can consolidate settings:
```yaml
gms:
  mode: 'legacy'  # or 'continuous'
  thresholds:
    legacy: {...}
    continuous: {...}
  adaptive_thresholds:
    enabled: false
    ...
```

## 7. Implementation Recommendations

### 7.1 Architecture
- Create `UnifiedGMSDetector` in new file
- Inherit from `RegimeDetectorInterface`
- Use composition for mode-specific behavior
- Maintain backward compatibility via factory

### 7.2 Migration Strategy
1. Implement unified detector with full backward compatibility
2. Update factory to use unified detector for both types
3. Deprecate but don't remove original implementations
4. Provide configuration migration tools

### 7.3 Testing Requirements
- Ensure legacy mode produces identical results (184 trades)
- Verify continuous mode maintains functionality
- Performance regression tests
- Configuration compatibility tests

## 8. Risk Considerations

### 8.1 Backward Compatibility
- Must maintain exact behavior for existing configurations
- Output format must match expectations (string vs dict)
- Configuration paths must continue to work

### 8.2 Performance
- Legacy mode must maintain 48.95s baseline
- Continuous mode should improve from 663.45s
- Adaptive threshold priming needs optimization

### 8.3 Integration
- GMSProvider expects specific output format
- tf-v2 and tf-v3 have different requirements
- State mapping must remain consistent

## Conclusion

The analysis reveals significant code duplication (70%) between the two GMS detector implementations, with clear opportunities for unification. A unified detector can eliminate duplication while maintaining backward compatibility through mode-based behavior branching. The key challenges are preserving exact behavior for existing systems while providing a cleaner, more maintainable architecture for future development.