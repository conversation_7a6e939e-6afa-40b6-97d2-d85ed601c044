# TF-v3 Implementation Analysis and Issues Report

## Executive Summary

This document analyzes the TF-v3 strategy implementation, identifies discrepancies and issues found during testing, and provides recommendations for targeted fixes. The analysis covers detector compatibility, patch implementations, and potential look-ahead bias concerns.

## 1. Detector Compatibility Analysis

### 1.1 Granular Microstructure vs Continuous GMS

**Granular Microstructure (Working)**
- Successfully executed 22 trades with 63.6% win rate
- More lenient with missing signals (e.g., unrealised_pnl)
- Returns string regime states directly
- Cadence set to 3600 seconds (once per hour) for legacy behavior
- Works with existing data pipeline

**Continuous GMS (Failing)**
- 0 trades executed, 0 evaluations
- Requires ATR values to be present in 1-second feature data
- Returns dictionary with state and risk_suppressed keys
- Cadence of 60 seconds (every minute)
- Fails due to missing ATR values and timezone errors

### 1.2 Root Cause of Continuous GMS Failure

1. **Missing ATR Values**: Continuous GMS requires `atr_14_sec` column in 1-second feature data
2. **Timezone Errors**: "Cannot subtract tz-naive and tz-aware datetime-like objects"
3. **Data Pipeline Mismatch**: Current data pipeline doesn't generate required 1-second features for continuous GMS

## 2. Patch Analysis and Look-Ahead Bias Assessment

### 2.1 TF-v3 State Loading Patch

**File**: `hyperliquid_bot/strategies/tf_v3.py`
**Purpose**: Disable state loading during backtesting
**Look-ahead bias**: ❌ None
**Issues**: 
- ✅ **Flawed backtest detection**: `self.config.backtest` is an object, not boolean
- The condition `hasattr(self.config, "backtest") and self.config.backtest` always evaluates to True
- Should use a different method to detect backtest mode

**Recommendation**: Implement proper backtest mode detection or pass a flag to the strategy

### 2.2 SL/TP Calculation Patch (Backtester)

**File**: `hyperliquid_bot/backtester/backtester.py`
**Purpose**: Add fallback ATR calculation for TF-v3 strategy
**Look-ahead bias**: ❌ None
**Issues**:
- ⚠️ **Hardcoded fallback**: Uses 2% of entry price as ATR fallback
- ❌ **No issues with logic**: Uses current entry price, not future data

**Recommendation**: Make fallback percentage configurable or use more sophisticated fallback

### 2.3 RiskManager Patch

**File**: `hyperliquid_bot/core/risk.py`
**Purpose**: Use minimum size in backtest mode when margin insufficient
**Look-ahead bias**: ❌ None
**Issues**:
- ✅ **Same backtest detection flaw**: Uses same flawed logic as TF-v3 patch
- ⚠️ **Bypasses risk management**: Uses hardcoded 0.001 BTC size
- ⚠️ **May mask real issues**: Insufficient margin warnings ignored

**Recommendation**: Fix backtest detection and consider more realistic minimum sizing

### 2.4 Position Dictionary Patch (Backtester)

**File**: `hyperliquid_bot/backtester/backtester.py`
**Purpose**: Fix position dictionary to use correct values
**Look-ahead bias**: ❌ None
**Issues**: ❌ **No issues - legitimate bug fix**

**Recommendation**: Keep this patch as it fixes a real bug

## 3. Discrepancies and Duplicates Found

### 3.1 Backtest Detection Logic

**Issue**: Multiple patches use flawed backtest detection
**Files Affected**:
- `hyperliquid_bot/strategies/tf_v3.py`
- `hyperliquid_bot/core/risk.py`

**Problem**: 
```python
if hasattr(self.config, "backtest") and self.config.backtest:
```
This always evaluates to True because `config.backtest` is a BacktestSettings object, not a boolean.

**Solution**: Implement proper backtest mode detection, such as:
- Add a `is_backtest` boolean flag to config
- Check for presence of backtester instance
- Use environment variable or command-line flag

### 3.2 Hardcoded Values

**Issue**: Multiple hardcoded values that should be configurable
**Examples**:
- 2% ATR fallback in SL/TP calculation
- 0.001 BTC minimum size in RiskManager
- Various thresholds and multipliers

**Recommendation**: Move hardcoded values to configuration files

### 3.3 Duplicate Validation Methods

**Issue**: Duplicate `_validate_market_bias` method in risk.py
**Lines**: 405-501 and 502-596
**Recommendation**: Remove duplicate method

## 4. Data Pipeline Issues

### 4.1 Continuous GMS Requirements

**Missing Components**:
1. ATR calculation in 1-second feature generation
2. Proper timezone handling in GMS detector
3. Feature store integration for 1-second data

**Impact**: Continuous GMS detector cannot function with current data pipeline

### 4.2 Feature Data Inconsistencies

**Issues**:
- Granular microstructure works with hourly OHLCV data
- Continuous GMS expects 1-second feature data
- ATR values missing from 1-second features

## 5. Recommendations for Targeted Fixes

### 5.1 High Priority

1. **Fix backtest detection logic** in all patches
2. **Implement proper ATR calculation** for 1-second features
3. **Fix timezone handling** in ContinuousGMSDetector
4. **Remove duplicate methods** in risk.py

### 5.2 Medium Priority

1. **Make hardcoded values configurable**
2. **Improve error handling** in GMS detector
3. **Add proper logging** for debugging data pipeline issues

### 5.3 Low Priority

1. **Optimize performance** of feature calculations
2. **Add unit tests** for edge cases
3. **Improve documentation** for detector differences

## 6. Testing Results Summary

### 6.1 Successful Test (Granular Microstructure)
- **Detector**: granular_microstructure
- **Trades**: 22 executed
- **Win Rate**: 63.6% (14 wins, 8 losses)
- **Total P/L**: $8.19
- **Average Win**: $0.94
- **Average Loss**: $-0.63
- **Win/Loss Ratio**: 1.50

### 6.2 Failed Test (Continuous GMS)
- **Detector**: continuous_gms
- **Trades**: 0 executed
- **Evaluations**: 0
- **Issues**: Missing ATR values, timezone errors
- **GMS Values**: All NaN throughout backtest

## 7. Conclusion

The TF-v3 strategy implementation is functional with the granular_microstructure detector but requires significant fixes to work with continuous_gms. The patches introduced do not create look-ahead bias but contain flawed backtest detection logic and hardcoded values that should be addressed. The main blocker for continuous_gms is the missing ATR calculation in the 1-second feature pipeline.
