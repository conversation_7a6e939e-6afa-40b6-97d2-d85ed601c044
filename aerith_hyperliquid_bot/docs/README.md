# Hyperliquid Trading Bot Documentation

## Supported Strategies (Task R-103)

As of Task R-103, the trading bot supports exactly **5 strategies**:

| Strategy Name | Class | Description | Status |
|---------------|-------|-------------|---------|
| `tf_v2` | `TrendFollowingStrategy` | Legacy trend following strategy | Active |
| `tf_v3` | `TFV3Strategy` | Modern trend following with ATR trailing stops | Active |
| `mean_reversion` | `MeanReversionStrategy` | Mean reversion using Keltner Channels | Deprecated* |
| `mean_variance` | `MeanVarianceStrategy` | Funding-adjusted mean-reversion strategy | Deprecated* |
| `obi_scalper` | `OBIScalperStrategy` | Order Book Imbalance scalping strategy | Deprecated* |

*Deprecated strategies are kept for future use but not actively maintained.

### Strategy Aliases

- `trend_following` → `tf_v2` (for backward compatibility)

## Supported Detector Types

The bot supports exactly **3 detector types** for regime detection:

| Detector Type | Handler | Description | Status |
|---------------|---------|-------------|---------|
| `rule_based` | `detector.py` | Rule-based regime detection | Active |
| `granular_microstructure` | `detector.py` (legacy mode) | Granular microstructure analysis | Active (Legacy) |
| `continuous_gms` | `gms_detector.py` | Continuous GMS with 1-second features | Active (Modern) |

### Removed Detectors

- `hurst` - Removed in Task R-103 (not used)

## Configuration

All configuration is centralized in `configs/base.yaml`. Override YAML files in `configs/overrides/` are kept for reference but not actively used.

### Strategy Configuration

Strategies are configured using boolean flags in the config:

```yaml
strategies:
  use_tf_v2: true             # Enables tf_v2 (legacy trend following)
  use_tf_v3: false            # Enables tf_v3
  use_mean_reversion: false   # Enables mean_reversion (deprecated)
  use_mean_variance: false    # Enables mean_variance (deprecated)
  use_obi_scalper: false      # Enables obi_scalper (deprecated)
```

### Detector Configuration

Detector type is configured in the regime section:

```yaml
regime:
  detector_type: 'continuous_gms'  # Options: 'rule_based', 'continuous_gms', 'granular_microstructure'
```

## Strategy Factory

The `StrategyFactory` class provides a unified interface for creating strategy instances:

```python
from hyperliquid_bot.strategies.strategy_factory import StrategyFactory, SUPPORTED_STRATEGIES, SUPPORTED_DETECTORS

# Create factory
factory = StrategyFactory(config)

# Create strategy
strategy = factory.create_strategy("tf_v3")

# Check supported strategies
print(SUPPORTED_STRATEGIES.keys())  # ['tf_v2', 'tf_v3', 'mean_reversion', 'mean_variance', 'obi_scalper']

# Check supported detectors
print(SUPPORTED_DETECTORS)  # {'rule_based', 'continuous_gms', 'granular_microstructure'}
```

## Validation

The system includes fail-fast validation:

- **Strategy validation**: Performed at runtime when strategies are created
- **Detector validation**: Performed during config loading
- **Unsupported names**: Raise `ValueError` with helpful error messages

## Testing

Run the strategy factory tests:

```bash
python -m pytest tests/test_strategy_factory.py -v
```

## Migration Notes

### From Previous Versions

- The `hurst` detector has been removed
- Override YAML files are no longer actively used
- All configuration should be done in `base.yaml`
- Strategy registry is now limited to 5 supported strategies

### Future Development

- New strategies should be added to `SUPPORTED_STRATEGIES` in `strategy_factory.py`
- New detectors should be added to `SUPPORTED_DETECTORS`
- All changes should include corresponding unit tests
