# Dynamic Risk Adjustment System Implementation

## Overview
This document details the implementation of the dynamic risk adjustment system in the HyperLiquid trading bot, focusing on the separation of volatility-based and regime-based risk adjustments. It serves as a reference for the changes made and future improvements.

## Problem Statement
The initial implementation had an issue where the `use_dynamic_leverage` and `dynamic_risk_adjustment` flags were not functioning independently:

1. When `use_dynamic_leverage=false` and `dynamic_risk_adjustment=true`, the system produced the same results as when both flags were enabled
2. The risk factor settings like `chop_risk_factor` and `strong_trend_risk_factor` weren't working as expected
3. The implementation had the regime-based adjustments modifying the results of the volatility-based adjustments, making them inseparable

## Solution: Independent Component Design

### Key Changes
We redesigned the risk adjustment logic to make the volatility-based and regime-based components truly independent:

1. **Independent Component Calculation**:
   - Volatility-based adjustment is calculated only when `use_dynamic_leverage=true`
   - Regime-based adjustment is calculated only when `dynamic_risk_adjustment=true`
   - Each starts from the base leverage rather than building on each other

2. **Final Combination Step**:
   - Only at the end do we combine the effects of the enabled components
   - Each setting works independently without affecting the other

3. **Enhanced Validation and Logging**:
   - Improved validation shows exactly which components are active
   - Detailed logging of the leverage calculation process
   - Clear indicators of how each factor is applied

### Code Structure Changes
The key changes were made in the following components:

1. **Risk Manager's `calculate_position` Method**:
   ```python
   # Calculate each component independently
   final_leverage = base_leverage
   
   # Apply volatility adjustment IF enabled (completely independent)
   if use_dynamic_lev and volatility_adjusted_leverage is not None:
       final_leverage = volatility_adjusted_leverage
       
   # Apply regime leverage adjustment IF enabled (independent of dynamic leverage)
   if use_dynamic_risk and apply_regime_adjustment:
       final_leverage = final_leverage * regime_leverage_factor
   ```

2. **Validation Method**:
   ```python
   # Enhanced validation to verify independent components
   components_active = []
   if dynamic_lev_enabled:
       components_active.append("Volatility-Based")
   if dynamic_risk_enabled and regime_applies:
       components_active.append("Regime-Based")
   if not components_active:
       components_active.append("None - Base Leverage Only")
   ```

### Configuration Parameters

#### Dynamic Leverage (Volatility-based)
In `base.yaml` under the `core` section:
```yaml
# Dynamic leverage adjustment based on market volatility
use_dynamic_leverage: false   # When true, reduces leverage during high volatility periods
```

The volatility-based leverage is calculated in `Portfolio.calculate_dynamic_leverage()` using this formula:
```python
vol_multiplier = max(0.4, min(1.6, 1.0 / (volatility * 20 + 0.5)))
adjusted_leverage = base_leverage * vol_multiplier
```

This creates an inverse relationship between volatility and leverage:
- Higher volatility → Lower leverage multiplier (minimum 0.4)
- Lower volatility → Higher leverage multiplier (maximum 1.6)

#### Dynamic Risk Adjustment (Regime-based)
In `base.yaml` under the `regime` section:
```yaml
# Dynamic risk adjustment based on market regime
dynamic_risk_adjustment: false        # Apply dynamic risk in volatile regimes

# Risk factors for different market conditions
chop_risk_factor: 0.5                # Multiplier for risk_per_trade in choppy markets
chop_leverage_factor: 0.5            # Multiplier for leverage in choppy markets
strong_trend_risk_factor: 1.0        # Multiplier for risk_per_trade in strong trends
strong_trend_leverage_factor: 0.5    # Multiplier for leverage in strong trends
weak_trend_risk_scale: 1.0           # Scale risk in weak trend regimes
```

These settings apply different risk and leverage factors based on the detected market regime:
- Choppy markets → Lower risk and leverage (conservative)
- Strong trends → Potentially higher risk but controlled leverage
- Weak trends → Customizable risk scaling

## Test Results

### Test 1: Only Regime-Based (no dynamic leverage)
```
dynamic_risk_adjustment: true
use_dynamic_leverage: false
```
Results:
- ROI: 97.93%
- Risk adjustments correctly applied based on market regime
- Logging confirmed proper application of risk factors

### Test 2: Only Volatility-Based (no regime adjustment)
```
dynamic_risk_adjustment: false
use_dynamic_leverage: true
```
Results:
- ROI: 133.43%
- Volatility-based leverage adjustments correctly applied
- Higher ROI indicates volatility-based adjustments were effective

## Future Improvements: 3-State GMS Market Model

The current system uses a complex 7-state regime model. A proposed improvement is to simplify to a 3-state GMS model (BULL, BEAR, CHOP) with specific leverage and risk settings for each market state. This would:

1. Be more intuitive to understand and configure
2. Allow for market-direction biases (higher leverage in bull markets for BTC)
3. Potentially replace both current adjustment systems with a single, comprehensive approach

The full details of this proposed approach are documented in `market_bias_risk_adjustments.md`.

### Potential Migration Path
1. Implement the 3-state model alongside the existing systems
2. Test and compare performance with different configurations
3. If the 3-state model proves more effective, consider deprecating:
   - `use_dynamic_leverage` (replaced by more strategic leverage in the 3-state model)
   - The complex regime-based adjustments (simplified to the 3 market states)

## Conclusion
The separation of volatility-based and regime-based adjustments has fixed the core issues in the risk management system. Each component now functions independently as intended. The next step is to explore the 3-state market bias approach that could further enhance the risk adjustment strategy with a more intuitive and potentially more effective model.
