# Proposed Logging for gms_spread_percentile_gate Debugging

To diagnose why different `gms_spread_percentile_gate` settings yield identical overall backtest results despite varying `'WIDE_SPREAD'` triggers, the following detailed logging additions are proposed. These adhere to observed project practices (using `self.logger.debug()` and f-strings).

## 1. Logging Additions in `aerith_hyperliquid_bot/hyperliquid_bot/strategies/evaluator.py`

The primary focus here is the `get_active_strategies` method.

### A. Entry Point of Regime Evaluation Logic
*   **Purpose:** Log initial state.
*   **Location:** Early in `get_active_strategies` or its orchestrator.
*   **Example:**
    ```python
    self.logger.debug(f"T:{current_timestamp} | EvalPreProc | RawReg: {current_raw_regime} | GMSMapActive: {self.gms_mapping_active}")
    ```

### B. After `regime_to_evaluate` is Determined
*   **Purpose:** Log the regime for evaluation and key influencing config flags.
*   **Location:** After GMS mapping, before filtering logic.
*   **Example:**
    ```python
    log_msg_b = f"T:{current_timestamp} | EvalRegimeSet | RawReg: {current_raw_regime} | EvalReg: {regime_to_evaluate}"
    if hasattr(cfg.regime, 'pause_in_chop'):
        log_msg_b += f" | PauseInChopCfg: {cfg.regime.pause_in_chop}"
    if hasattr(cfg.regime, 'gms_filter_allow_weak_bull_trend'):
        log_msg_b += f" | GMSAllowWeakBullCfg: {cfg.regime.gms_filter_allow_weak_bull_trend}"
    if hasattr(cfg.regime, 'gms_filter_allow_weak_bear_trend'):
        log_msg_b += f" | GMSAllowWeakBearCfg: {cfg.regime.gms_filter_allow_weak_bear_trend}"
    self.logger.debug(log_msg_b)
    ```

### C. When a Specific Condition Results in No Active Strategies
*   **Purpose:** Pinpoint why strategies are filtered.
*   **Location:** Within conditional blocks leading to empty `active_names`.
*   **Example (Illustrative - adapt to actual logic for WIDE_SPREAD handling):**
    ```python
    self.logger.debug(f"T:{current_timestamp} | EvalFilter | FilterReason: {reason_log_for_no_strategies}")
    ```
    ```python
    self.logger.debug(f"T:{current_timestamp} | EvalFilter | EvalReg: {regime_to_evaluate} | FilterReason: {reason_log_for_no_strategies}")
    ```

### D. At the End of `get_active_strategies` (or immediately after its call)
*   **Purpose:** Consolidated log of inputs, decisions, and final active strategies.
*   **Location:** After `active_names` is finalized.
*   **Example:**
    ```python
    self.logger.debug(f"T:{current_timestamp} | EvalResult | RawReg: {current_raw_regime} | GMSMapActive: {self.gms_mapping_active} | EvalReg: {regime_to_evaluate} | ActiveStr: {active_names} | ReasonNoStr: {reason_log_for_no_strategies if not active_names else 'N/A'}")
    ```
(Note: Similar `EvalResult` logs exist at various points depending on the execution path within `get_active_strategies`.)

## 2. Logging Additions in `aerith_hyperliquid_bot/hyperliquid_bot/portfolio/portfolio.py`

Focus on processing strategy signals and final trade decisions.

### A. When Processing Signals from Each Active Strategy
*   **Purpose:** Log individual strategy signals.
*   **Location:** Loop where signals from `active_strategies` are evaluated.
*   **Example:**
    ```python
    # self.logger.debug(f"T:{timestamp_unix} | PortfolioSignal | Strategy: {strategy_name_for_signal} | Signal: Requesting {requested_action_type} entry")
    ```

### B. After Final Portfolio Decision for the Bar
*   **Purpose:** Log overall portfolio outcome.
*   **Location:** After all signals considered and portfolio-level risk/filtering applied.
*   **Example:**
    ```python
    # self.logger.debug(f"T:{timestamp_unix} | PortfolioAction | Action: {final_action_type_entry} | Reason: {reason_for_entry_action}")
    ```

## 3. Guidance on Log Analysis

1.  **Run Two Backtests:**
    *   **Run 1:** `gms_spread_percentile_gate: 30`.
    *   **Run 2:** `gms_spread_percentile_gate: 70`.
    Ensure both output `DEBUG` logs to separate files.

2.  **Compare Log Files:**
    *   Use a file comparison tool (e.g., `diff`).
    *   **Focus on Divergence Points:** Identify timestamps where `RawReg:` shows `'WIDE_SPREAD'` in one run but not the other.
    *   **Trace Forward:** For these differing bars, examine:
        *   `EvalReg:`, `ActiveStr:`, `ReasonNoStr:` in `evaluator.py` logs.
        *   `PortfolioSignal:` and `PortfolioAction:` in `portfolio.py` logs.
    *   **Identify Convergence:** The goal is to see *why* the `PortfolioAction` ends up being the same (or leading to the same overall trade list) despite the initial difference in `RawReg:` due to the `gms_spread_percentile_gate`. This could be due to other overriding filters, the nature of the `EvalReg`, or subsequent portfolio logic.

## 4. Implementation Notes

*   In `aerith_hyperliquid_bot/hyperliquid_bot/strategies/evaluator.py`, within the `get_active_strategies` method, the `current_timestamp` variable used in the logs is initialized to `"N/A"`.
*   The logging statements added to `aerith_hyperliquid_bot/hyperliquid_bot/portfolio/portfolio.py` (specifically in `handle_entry` and `handle_exit` methods) were implemented as commented-out lines (e.g., `# self.logger.debug(...)`). The examples in Section 2 above reflect this.