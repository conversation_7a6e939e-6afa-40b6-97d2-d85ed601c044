# Adaptive Thresholds Investigation - Why Fixed Thresholds Don't Work

**Date**: 2025-05-30  
**Investigation**: Why modern system requires adaptive thresholds  
**Status**: ✅ **ROOT CAUSE IDENTIFIED**

## Executive Summary

The modern system (continuous_gms + tf_v3) **requires adaptive thresholds** because the fixed threshold values in the configuration are **calibrated for different market conditions** than what exists in the March 2025 data. Fixed thresholds result in all periods being classified as "Low_Vol_Range" (CHOP), which prevents TF-v3 strategy activation.

## Test Results Comparison

### **With Adaptive Thresholds (Working)**
- **Period**: 2025-03-02 to 2025-03-22 (20 days)
- **Regime Distribution**: 
  - None: 472 periods (93.7%)
  - Weak_Bull_Trend: 32 periods (6.3%)
- **Trading Results**: 32 trades, Sharpe 0.79, ROI 5.87%
- **Strategy Activation**: TF-v3 active during Weak_Bull_Trend periods

### **With Fixed Thresholds (Not Working)**
- **Period**: 2025-03-02 to 2025-03-05 (4 days)
- **Regime Distribution**: 
  - Unknown: 1 period (1.2%)
  - Low_Vol_Range: 85 periods (98.8%)
- **Trading Results**: 0 trades, no strategy activation
- **Strategy Activation**: None (Low_Vol_Range maps to CHOP, TF-v3 filtered out)

## Root Cause Analysis

### **1. Fixed Threshold Values vs Actual Data**

| **Metric** | **Fixed Threshold** | **Actual Data** | **Result** |
|------------|-------------------|-----------------|------------|
| **ATR% (Volatility)** | Low: 1.0%, High: 3.0% | 0.46% | **Below Low → Low_Vol_Range** |
| **MA Slope (Momentum)** | Weak: 0.5, Strong: 2.5 | 0.00 | **Below Weak → Weak Momentum** |
| **Combination** | Low Vol + Weak Mom | Low Vol + Weak Mom | **→ Low_Vol_Range** |

### **2. Regime Classification Logic**

The continuous GMS detector uses this logic:
```python
# From the detector logs:
# Vol=0.0046 (L:0.0100/H:0.0300), Mom=0.00 (W:0.50/S:2.50)
# Since 0.0046 < 0.01 → Low Volatility
# Since 0.00 < 0.5 → Weak Momentum  
# → Classification: Low_Vol_Range
```

### **3. State Mapping Impact**

From `gms_state_mapping.yaml`:
```yaml
state_map:
  Low_Vol_Range: 'CHOP'  # Maps to CHOP
  Weak_Bull_Trend: 'BULL' # Maps to BULL
```

- **Low_Vol_Range** → **CHOP** → **TF-v3 strategy filtered out**
- **Weak_Bull_Trend** → **BULL** → **TF-v3 strategy active**

### **4. Why Adaptive Thresholds Work**

Adaptive thresholds learn from the actual data distribution:

1. **Data-Driven Calibration**: Thresholds adjust to actual ATR% and momentum ranges
2. **Percentile-Based**: Uses 25th/75th percentiles of observed data
3. **Causal Computation**: No look-ahead bias, uses only historical data
4. **Dynamic Adjustment**: Continuously updates as market conditions change

## Configuration Analysis

### **Fixed Threshold Sources**

The fixed thresholds come from multiple configuration layers:

1. **Base Configuration** (`base.yaml`):
   ```yaml
   continuous_gms:
     gms_vol_high_thresh: 0.03    # 3.0%
     gms_vol_low_thresh: 0.01     # 1.0%
     gms_mom_strong_thresh: 2.5
     gms_mom_weak_thresh: 0.5
   ```

2. **Test Configuration** (`test_modern_fixed_thresholds.yaml`):
   ```yaml
   gms:
     vol_low_thresh: 0.55         # Legacy values (wrong scale!)
     vol_high_thresh: 0.92
     mom_weak_thresh: 50.0
     mom_strong_thresh: 100.0
   ```

### **Scale Mismatch Issue**

The test configuration used **legacy system values** which are on a different scale:
- **Legacy**: vol_low_thresh: 0.55 (55% - percentile scale)
- **Modern**: gms_vol_low_thresh: 0.01 (1% - ATR% scale)

This caused the detector to use the base.yaml values (0.01/0.03) instead of the intended test values.

## Market Conditions Analysis

### **March 2025 Data Characteristics**
- **ATR% Range**: ~0.28% to 1.91% (much lower than 1-3% thresholds)
- **Momentum Range**: Near-zero values (much lower than 0.5-2.5 thresholds)
- **Market Behavior**: Relatively low volatility, weak momentum period

### **Threshold Calibration Mismatch**
The fixed thresholds appear to be calibrated for:
- **Higher volatility periods** (1-3% ATR%)
- **Stronger momentum periods** (0.5-2.5 slope values)
- **Different market regimes** than March 2025

## Solution Approaches

### **1. Use Adaptive Thresholds (Recommended)**
```yaml
gms:
  auto_thresholds: true  # Enable adaptive thresholds
  detector_type: 'continuous_gms'
```

**Advantages**:
- ✅ Automatically calibrates to actual data
- ✅ Adapts to changing market conditions
- ✅ No manual threshold tuning required
- ✅ Proven to work with current data

### **2. Calibrate Fixed Thresholds (Alternative)**
```yaml
gms:
  auto_thresholds: false
  # Calibrated for March 2025 data
  gms_vol_low_thresh: 0.007   # 33rd percentile of actual data
  gms_vol_high_thresh: 0.012  # 67th percentile of actual data
  gms_mom_weak_thresh: 0.001  # Near-zero for weak momentum
  gms_mom_strong_thresh: 0.01 # Very low for strong momentum
```

**Disadvantages**:
- ❌ Requires manual calibration for each data period
- ❌ May not work for different market conditions
- ❌ Needs constant monitoring and adjustment

### **3. Hybrid Approach (Future Enhancement)**
- Use adaptive thresholds as primary
- Fall back to calibrated fixed thresholds during warm-up
- Implement threshold validation and alerts

## Implications for Trading System

### **Why This Matters**
1. **Strategy Activation**: Fixed thresholds prevent strategy activation entirely
2. **Performance Impact**: 0 trades vs 32 trades (significant difference)
3. **Regime Detection**: Accurate regime detection is critical for strategy filtering
4. **Market Adaptation**: System must adapt to changing market conditions

### **Design Philosophy**
The modern system is designed around **adaptive, data-driven thresholds** because:
- Markets change over time
- Volatility regimes shift
- Fixed thresholds become stale
- Adaptive systems are more robust

## Recommendations

### **1. Always Use Adaptive Thresholds**
- Set `auto_thresholds: true` for modern system configurations
- This is the intended and tested approach

### **2. Fixed Thresholds Only for Special Cases**
- Use only when specific market conditions are known
- Require careful calibration for each data period
- Monitor performance and adjust as needed

### **3. Configuration Validation**
- Add validation to prevent scale mismatches
- Warn when fixed thresholds are outside expected ranges
- Provide guidance on proper threshold calibration

## Conclusion

The modern system **requires adaptive thresholds** because:

1. **Fixed thresholds are miscalibrated** for March 2025 market conditions
2. **ATR% and momentum values** are much lower than threshold expectations
3. **All periods classify as Low_Vol_Range** → CHOP → no strategy activation
4. **Adaptive thresholds automatically adjust** to actual data distribution
5. **This is the intended design** of the modern system

**The investigation confirms that adaptive thresholds are not optional but essential for the modern system to function correctly.**
