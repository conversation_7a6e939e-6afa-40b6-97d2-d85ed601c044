# Unified GMS Detector Implementation - Product Requirements Document

**Date:** May 31, 2025  
**Project:** Aerith Hyperliquid Bot  
**Feature:** Unified GMS Detector  
**Status:** Ready for Implementation  

## 1. Executive Summary

This PRD outlines the implementation plan for merging the `GranularMicrostructureRegimeDetector` and `ContinuousGMSDetector` into a single, unified `UnifiedGMSDetector` class. The unification will eliminate ~70% code duplication, maintain 100% backward compatibility, and significantly improve performance for the modern system.

### Key Objectives
- **Eliminate Code Duplication**: Merge ~1,800 lines into ~800 lines
- **Preserve Performance**: Maintain legacy system's 48.95s baseline
- **Optimize Modern System**: Reduce 663.45s to <30s target
- **Simplify Configuration**: Unified settings with backward compatibility
- **Zero Breaking Changes**: Seamless migration for existing users

## 2. Technical Architecture

### 2.1 Current State Analysis
- **Two Separate Detectors**: 604 lines (legacy) + 1,219 lines (modern)
- **Code Duplication**: ~70% shared logic
- **Performance Gap**: 13.6x difference (48.95s vs 663.45s)
- **Configuration Complexity**: Settings scattered across 3 sections

### 2.2 Target Architecture
```
UnifiedGMSDetector
├── Mode Resolution (legacy/continuous)
├── Configuration Management (unified)
├── Core Detection Logic (shared)
├── Output Formatting (mode-specific)
└── Performance Optimizations (integrated)
```

## 3. Implementation Breakdown

### Phase 1: Foundation (Days 1-3)

#### Task 1.1: Create UnifiedGMSDetector Base Structure
**Priority:** P0  
**Dependencies:** None  
**Estimated Time:** 4 hours  

**Deliverables:**
- Create `hyperliquid_bot/core/unified_gms_detector.py`
- Implement base class structure with RegimeDetectorInterface
- Add mode resolution logic with multiple fallback paths
- Implement configuration resolution hierarchy

**Acceptance Criteria:**
- [ ] Class inherits from RegimeDetectorInterface
- [ ] Mode resolution supports all legacy configuration paths
- [ ] Configuration resolution handles nested settings correctly
- [ ] Unit tests pass for initialization

#### Task 1.2: Implement Threshold Management
**Priority:** P0  
**Dependencies:** Task 1.1  
**Estimated Time:** 3 hours  

**Deliverables:**
- Unified threshold resolution with mode-specific defaults
- Support for both fixed and adaptive thresholds
- Fallback mechanism for missing configurations

**Acceptance Criteria:**
- [ ] Legacy mode uses correct threshold defaults
- [ ] Continuous mode supports adaptive thresholds
- [ ] All configuration paths resolve correctly
- [ ] Emergency fallback to fixed thresholds works

#### Task 1.3: Core Detection Logic Migration
**Priority:** P0  
**Dependencies:** Task 1.2  
**Estimated Time:** 6 hours  

**Deliverables:**
- Migrate `_determine_state()` logic from both detectors
- Implement unified signal validation
- Create mode-specific signal handling
- Preserve exact behavior for both modes

**Acceptance Criteria:**
- [ ] Legacy mode produces identical states
- [ ] Continuous mode maintains dict output format
- [ ] Signal validation works for both modes
- [ ] No regression in detection accuracy

### Phase 2: Mode-Specific Features (Days 4-5)

#### Task 2.1: Legacy Mode Implementation
**Priority:** P0  
**Dependencies:** Task 1.3  
**Estimated Time:** 4 hours  

**Deliverables:**
- Implement legacy-specific initialization
- Configure 3600s cadence and string output
- Add ADX/funding confirmation support
- Preserve raw2/resampled_l2 data pipeline

**Acceptance Criteria:**
- [ ] Legacy mode matches original detector behavior
- [ ] Performance remains at 48.95s baseline
- [ ] All 184 baseline trades reproduced
- [ ] String output format maintained

#### Task 2.2: Continuous Mode Implementation
**Priority:** P0  
**Dependencies:** Task 1.3  
**Estimated Time:** 5 hours  

**Deliverables:**
- Implement continuous-specific initialization
- Configure 60s cadence and dict output
- Add risk suppression calculation
- Support state collapse mapping

**Acceptance Criteria:**
- [ ] Continuous mode matches original behavior
- [ ] Dict output includes risk_suppressed flag
- [ ] State collapse works correctly
- [ ] Feature compatibility maintained

#### Task 2.3: Adaptive Threshold Integration
**Priority:** P1  
**Dependencies:** Task 2.2  
**Estimated Time:** 6 hours  

**Deliverables:**
- Import OptimizedAdaptiveThreshold implementation
- Implement batch priming for performance
- Add percentile caching mechanism
- Create emergency fallback system

**Acceptance Criteria:**
- [ ] Adaptive thresholds initialize correctly
- [ ] Priming completes in <10s (down from 655s)
- [ ] Emergency fallback triggers on errors
- [ ] Performance monitoring works

### Phase 3: Factory Integration (Days 6-7)

#### Task 3.1: Update Factory Function
**Priority:** P0  
**Dependencies:** Phase 1-2  
**Estimated Time:** 2 hours  

**Deliverables:**
- Modify `get_regime_detector()` in detector.py
- Route both GMS types to UnifiedGMSDetector
- Maintain backward compatibility
- Add configuration validation

**Acceptance Criteria:**
- [ ] Factory routes correctly based on detector_type
- [ ] Configuration validation catches missing attributes
- [ ] Existing code continues to work
- [ ] Clear error messages for invalid configs

#### Task 3.2: GMSProvider Integration
**Priority:** P0  
**Dependencies:** Task 3.1  
**Estimated Time:** 3 hours  

**Deliverables:**
- Update GMSProvider to use UnifiedGMSDetector
- Ensure dict output compatibility for tf-v3
- Maintain state history tracking
- Preserve async update capability

**Acceptance Criteria:**
- [ ] GMSProvider works with unified detector
- [ ] State history maintained correctly
- [ ] TF-v3 strategy receives correct format
- [ ] No breaking changes in API

### Phase 4: Configuration Migration (Days 8-9)

#### Task 4.1: Create Configuration Migration Tool
**Priority:** P1  
**Dependencies:** Phase 3  
**Estimated Time:** 4 hours  

**Deliverables:**
- Create `hyperliquid_bot/utils/config_migration.py`
- Implement GMSConfigMigrator class
- Support all legacy configuration formats
- Generate unified YAML structure

**Acceptance Criteria:**
- [ ] Migrator handles all configuration variants
- [ ] Backward compatibility preserved
- [ ] Validation catches migration issues
- [ ] Clear migration reports generated

#### Task 4.2: Update Configuration Schema
**Priority:** P1  
**Dependencies:** Task 4.1  
**Estimated Time:** 3 hours  

**Deliverables:**
- Update settings.py with unified GMSSettings
- Add new configuration models
- Implement validation rules
- Support nested threshold structure

**Acceptance Criteria:**
- [ ] Pydantic models validate correctly
- [ ] All configuration paths supported
- [ ] Clear validation error messages
- [ ] Schema documentation complete

#### Task 4.3: Update Base Configuration
**Priority:** P2  
**Dependencies:** Task 4.2  
**Estimated Time:** 2 hours  

**Deliverables:**
- Update configs/base.yaml
- Create example configurations
- Document migration path
- Add inline comments

**Acceptance Criteria:**
- [ ] Base config uses unified structure
- [ ] Examples cover common use cases
- [ ] Migration path clearly documented
- [ ] Comments explain all settings

### Phase 5: Testing & Validation (Days 10-12)

#### Task 5.1: Unit Test Suite
**Priority:** P0  
**Dependencies:** Phase 1-4  
**Estimated Time:** 6 hours  

**Deliverables:**
- Create tests/test_unified_gms_detector.py
- Test both detector modes
- Verify configuration resolution
- Test edge cases and errors

**Acceptance Criteria:**
- [ ] 100% coverage of core functionality
- [ ] Both modes tested thoroughly
- [ ] Configuration paths validated
- [ ] Error handling tested

#### Task 5.2: Integration Testing
**Priority:** P0  
**Dependencies:** Task 5.1  
**Estimated Time:** 4 hours  

**Deliverables:**
- Test with real data pipelines
- Verify backtest consistency
- Performance regression tests
- End-to-end validation

**Acceptance Criteria:**
- [ ] Legacy: 184 trades reproduced
- [ ] Modern: Performance <30s achieved
- [ ] No regressions in accuracy
- [ ] Data pipeline compatibility verified

#### Task 5.3: Performance Validation
**Priority:** P0  
**Dependencies:** Task 5.2  
**Estimated Time:** 3 hours  

**Deliverables:**
- Profile unified detector performance
- Compare against baselines
- Identify any bottlenecks
- Document performance metrics

**Acceptance Criteria:**
- [ ] Legacy mode: 48.95s maintained
- [ ] Continuous mode: <30s achieved
- [ ] Memory usage acceptable
- [ ] Performance report generated

### Phase 6: Documentation & Rollout (Days 13-14)

#### Task 6.1: Technical Documentation
**Priority:** P1  
**Dependencies:** Phase 5  
**Estimated Time:** 3 hours  

**Deliverables:**
- API documentation
- Migration guide
- Performance optimization guide
- Troubleshooting guide

**Acceptance Criteria:**
- [ ] All public methods documented
- [ ] Migration steps clear
- [ ] Common issues addressed
- [ ] Examples provided

#### Task 6.2: Deprecation Strategy
**Priority:** P2  
**Dependencies:** Task 6.1  
**Estimated Time:** 2 hours  

**Deliverables:**
- Add deprecation warnings to old detectors
- Create transition timeline
- Update existing documentation
- Prepare removal plan

**Acceptance Criteria:**
- [ ] Deprecation warnings added
- [ ] Timeline communicated
- [ ] Documentation updated
- [ ] Removal plan approved

## 4. Technical Challenges & Solutions

### Challenge 1: Backward Compatibility
**Risk:** Breaking existing configurations and workflows  
**Solution:**
- Multiple configuration resolution paths
- Comprehensive fallback mechanisms
- Extensive compatibility testing
- Clear migration tools

### Challenge 2: Performance Optimization
**Risk:** Modern system remains slow  
**Solution:**
- Import proven OptimizedAdaptiveThreshold
- Implement batch processing
- Add emergency fallbacks
- Performance monitoring

### Challenge 3: Code Complexity
**Risk:** Unified detector becomes too complex  
**Solution:**
- Clear separation of concerns
- Mode-specific methods
- Comprehensive documentation
- Extensive testing

### Challenge 4: Testing Coverage
**Risk:** Regressions in production  
**Solution:**
- Baseline comparison tests
- Performance regression tests
- Integration test suite
- Gradual rollout strategy

## 5. Success Metrics

### Functional Metrics
- **Code Reduction**: 1,823 → ~800 lines (56% reduction)
- **Backward Compatibility**: 100% existing configs work
- **Feature Parity**: All existing features preserved
- **Test Coverage**: >95% for core functionality

### Performance Metrics
- **Legacy Mode**: 48.95s baseline maintained
- **Continuous Mode**: 663.45s → <30s (95% improvement)
- **Memory Usage**: <10% increase
- **Initialization Time**: <1s for both modes

### Quality Metrics
- **Bug Count**: <5 minor issues in first week
- **Migration Success**: >95% automated migrations work
- **Documentation**: 100% public API documented
- **User Satisfaction**: No breaking changes reported

## 6. Risk Mitigation

### Risk 1: Production Regression
**Mitigation:**
- Extensive testing before release
- Gradual rollout with monitoring
- Quick rollback capability
- Clear communication

### Risk 2: Performance Degradation
**Mitigation:**
- Performance baselines established
- Continuous monitoring
- Emergency fallback modes
- Optimization documentation

### Risk 3: Configuration Complexity
**Mitigation:**
- Migration tools provided
- Clear documentation
- Validation utilities
- Support during transition

## 7. Implementation Timeline

### Week 1 (Days 1-7)
- Phase 1: Foundation
- Phase 2: Mode-Specific Features
- Phase 3: Factory Integration

### Week 2 (Days 8-14)
- Phase 4: Configuration Migration
- Phase 5: Testing & Validation
- Phase 6: Documentation & Rollout

### Post-Launch (Week 3+)
- Monitor production usage
- Address any issues
- Optimize based on feedback
- Plan deprecation timeline

## 8. Dependencies

### Internal Dependencies
- RegimeDetectorInterface (base class)
- OptimizedAdaptiveThreshold (performance fix)
- State mapping utilities
- Configuration system

### External Dependencies
- pandas/numpy (computation)
- PyYAML (configuration)
- pytest (testing)
- Standard logging

## 9. Open Questions

1. **Deprecation Timeline**: When to remove old detectors?
2. **Default Mode**: Should new installations default to continuous?
3. **Feature Flags**: Add runtime mode switching capability?
4. **Monitoring**: What metrics to expose for production?

## 10. Conclusion

The unified GMS detector implementation represents a significant architectural improvement that will:
- Reduce code complexity by 56%
- Improve performance by 95% for modern system
- Maintain 100% backward compatibility
- Provide foundation for future enhancements

The phased implementation approach minimizes risk while delivering value incrementally. With comprehensive testing and migration tools, the transition will be seamless for existing users while providing significant benefits for new implementations.