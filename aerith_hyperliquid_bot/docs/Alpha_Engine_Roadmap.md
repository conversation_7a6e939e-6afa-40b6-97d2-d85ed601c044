Below is a **self-contained “Alpha-Engine Roadmap & Data Blueprint”** you can drop into the agents’ workspace (or the canvas) once the TF-v3/Continuous-GMS recovery is signed off. It explains future milestones, the exact data surfaces each layer will expect, and the resampling logic, so engineers can code with tomorrow’s needs in mind rather than bolting on hacks later.

---

# **Alpha-Engine Roadmap & Data Blueprint**

*(rev 2025-05-24 · post-T-111 staging document)*

---

## 0 Big-Picture Timeline (after current recovery sprint)

| Seq | Tag | ETA | Milestone | High-level Deliverables |
| --- | --- | --- | --- | --- |
| **1** | **T-112** | +3 d | **L20 WebSocket Recorder** | - Real-time depth-20 snapshots @ 10 Hz → Arrow/Parquet hourly - CLI `tools.ws_l2_recorder.py` - Unit tests with replay |
| **2** | **T-113** | +7 d | **HF Micro-Alpha Engine (v0 Skeleton)** | - Event loop @ 200 ms - Loads 1-s features via FeatureStore - First module: **Order-Book Imbalance (OBI)** score & micro-entry/exit hooks - Plug-in interface for future micro-models |
| **3** | **T-114** | +9 d | **Unified RiskManager v2** | - Cross-strategy notional cap, leverage guard - REST/IPC callable (`risk.available_notional()`) |
| **4** | **T-115** | +12 d | **Paper-Trade Orchestrator** | - Docker compose or PM2 scripts - Dashboard (FastAPI + Plotly) - v2 vs v3 shadow-mode support |
| **5** | **T-116** | +15 d | **Adaptive-EMA R&D** (opt-in) | - Param sweep report; promote if Sharpe gain ≥ 0.15 |
| **6** | **T-117** | +18 d | **ETL Refactor** (day-level batching, perf) | - Single-pass ATR + feature calc - Plug-in transform registry - When exchange-metadata layer lands, replace `trail_eps` with `trail_eps_tick_mult` to auto-scale by tick size |
| **7** | **T-118** | +22 d | **Alpha-Engine v1 (multi-factor)** | - OBI + Queue-Imbalance + OFI features - Lightweight linear/logit ensemble - Live adaptivity hooks |

*(dates relative; adjust after T-111 completion)*

---

## 1 Data Surfaces – What Every Future Module Can Rely On

| Surface | Granularity | Stored In | Guaranteed Columns (post-R-101) |
| --- | --- | --- | --- |
| **features_1s/YYYY-MM-DD/*.parquet** | **1 s** | SSD (Arrow → Parquet) | `timestamp` (UTC naive) `mid_price`, `high`, `low`, `close` (copy of mid) `atr_14_sec`, `atr_percent_sec` `atr` (legacy), `atr_percent` `obi_smoothed_20`, `depth_slope`, `depth_skew`, `spread_mean`, `spread_std` *(add more via ETL plugins; columns MUST be float or int, no objects)* |
| **raw_l2/YYYY-MM-DD/BTC_??_l2Book.arrow** | tick (10 Hz) | SSD | Depth-20 snapshot arrays |
| **ohlcv_1h/*.parquet** | 1 h | SSD | standard OHLCV, already exists |
| **Redis / lmdb FeatureStore** (T-112+) | cached 1-s | RAM | rolling 2 h of most-recent feature rows for HF loop |
| **Back-test in-mem DF** | variable | RAM | union of 1 h OHLC + joined 1-s features |

**Continuous GMS** and **HF Engine** both consume the **1-second feature parquet** (or FeatureStore) – *not* the raw L2 or 1-h bars – so ATR **must live in that parquet** (solved in R-101).

If you invent a new column, append it to the ETL schema and document it in `/docs/feature_catalog.md`.

---

## 2 Resampling Rules

| From | To | Method |
| --- | --- | --- |
| **10 Hz L2 mid-price** | 1 s `mid_price` | **Last** quote within each second; if no quote, ffill previous mid. |
| 1 s → **1 h OHLC** | `resample('1H').ohlc()` on `mid_price` |  |
| 1 h → **ATR(14)** | Classic Wilder’s average on True-Range of 1 h OHLC; ffill down to seconds → `atr_14_sec` |  |
| 1 s → feature EMA’s | `pd.Series.ewm(span=x).mean()` – do **not** leak forward data. |  |

---

## 3 HF Micro-Alpha Engine (T-113) – Skeleton Spec

```
class MicroAlphaEngine:
    def __init__(self, feature_store, risk_manager, config):
        self.cfg = config.hf_alpha
        self.fs  = feature_store
        self.rm  = risk_manager
        self.model = OBIModule(...)   # first plug-in
        self.loop_time = 0.2  # seconds
    async def run_forever():
        while True:
            row = self.fs.latest()              # fastest 1-s slice
            edge = self.model.score(row)
            if edge > self.cfg.long_thresh:
                self.place_order('buy', edge)
            elif edge < self.cfg.short_thresh:
                self.place_order('sell', edge)
            await asyncio.sleep(self.loop_time)

```

- **Models are plug-ins** – each exposes `score(row) → float edge`.
- Engine respects `risk_manager.available_notional()` before any order.
- Execution is via **Hyperliquid REST “market-ioc”** until WebSocket trading is available.

---

## 4 Future Agent Prompts – Boilerplate Guidance

*Always start by asking* **“Do these column names already exist? (`atr_14_sec`, etc.)”** if uncertain.

*Never* change schema silently; coordinate via ETL once.

Sample micro prompt to regenerate one day of features:

```
### Task R-101-day-reprocess
Re-run ETL for 2025-03-03 with --overwrite.
Confirm post-write assertion: df['atr_14_sec'].isna().sum() < 14

```

Sample HF loop prompt:

```
### Task T-113a – OBI plug-in
Implement `OBIModule.score()` using depth-level 1 bid/ask imbalance
Input row fields: ['obi_smoothed_20', 'spread_mean', 'atr_percent_sec']
Return edge in [-1, 1].  Ask if any field missing.

```

---

## 5 Checklist Before Starting T-112+

1. **Recovery sprint (R-101…R-109)** green.
2. `docs/feature_catalog.md` updated with ATR & any new cols.
3. `configs/base.yaml` pared down – no dev overrides.
4. `tests/` green including new ETL ATR test.

Once these boxes tick, move the backlog pointer to **T-112** and proceed.

---

### End of Document

Copy/paste this into the new agent thread (or canvas).

It gives every engineer a clear north-star and prevents accidental schema drift while we push forward.