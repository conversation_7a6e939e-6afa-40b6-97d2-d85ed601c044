# GMS Spread Percentile Gate Fix

## Overview

This document explains the behavior of the `gms_spread_percentile_gate` parameter after the logic fix.

## Parameter Behavior

The `gms_spread_percentile_gate` parameter is used to filter out trades when the spread is wider than a certain historical percentile. The parameter works as follows:

1. The parameter value (1-100) represents the percentage of widest spreads to filter out
   - A value of 10 means "filter out trades when the spread is in the top 10% of historical values"
   - A value of 90 means "filter out trades when the spread is in the top 90% of historical values"

2. The parameter has a **binary effect** on trading:
   - When the spread percentile exceeds the threshold, the regime is set to `WIDE_SPREAD`
   - When the regime is `WIDE_SPREAD`, no strategies are activated, and no trades occur
   - This is an all-or-nothing effect - it either allows trading or blocks it completely for that bar

3. Implementation details:
   - The threshold is calculated as `100 - gms_spread_percentile_gate`
   - If `raw_spread_percentile > threshold`, the regime is set to `WIDE_SPREAD`
   - The `WIDE_SPREAD` regime causes no strategies to be activated in `StrategyEvaluator.get_active_strategies()`

## Recommended Usage

- Use lower values (e.g., 10-20) to filter out only the most extreme spread conditions
- Use higher values (e.g., 80-90) for more aggressive filtering
- Setting to 0 or null disables this filter
- Be aware that this parameter interacts with other regime filters and may not be the only factor determining whether trades occur

## Code Implementation

The logic has been reversed from the original implementation to make the parameter more intuitive:

```python
# Priority 3: Spread Percentile Gate - Only check if NORMAL so far
if market_condition == 'NORMAL' and self.spread_percentile_gate is not None and self.spread_percentile_gate > 0:
    market_logic_used = "Spread Percentile"
    # NaN check already done if this path is active
    threshold = 100 - self.spread_percentile_gate
    if raw_spread_percentile > threshold:
        market_condition = 'WIDE_SPREAD'
```

## Interaction with Other Parameters

This parameter works in conjunction with other regime detection parameters. The regime determination follows a priority order, and the spread percentile gate is checked only if the market condition is still 'NORMAL' after earlier checks. Other parameters like `gms_spread_std_high_thresh` and `gms_spread_mean_low_thresh` may also influence the final regime determination.

## Implementation Date

May 2, 2025
