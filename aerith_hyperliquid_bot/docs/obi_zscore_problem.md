# OBI Z-Score Threshold Implementation Issue

## Problem Description

The `gms_obi_zscore_threshold` parameter in the configuration is not working properly. When enabled (set to a value > 0), it causes the backtest to fail with the error:

```
ValueError: Signal calculation resulted in an empty DataFrame.
```

## Root Cause Analysis

After investigation, the following issues were identified:

1. **Signal Calculation Problem**: When the OBI Z-score threshold is enabled, the signal calculation process fails, resulting in an empty DataFrame. This suggests that:
   - The `raw_obi_zscore` signal may not be properly calculated
   - The Z-score calculation may depend on data that isn't available
   - The Z-score filter might be too restrictive, filtering out all data points

2. **Signal Flow**: The issue occurs in the signal calculation pipeline before the data reaches the detector:
   - The `SignalEngine` calculates the `raw_obi_zscore` signal
   - When this feature is enabled, the detector requests the signal
   - The signal calculation fails, resulting in an empty DataFrame
   - The backtest terminates before any trading logic is executed

3. **Code Implementation**: The Z-score calculation is implemented in `SignalEngine.calculate_all_signals()`:
   ```python
   signals_df['raw_obi_zscore'] = (signals_df[raw_obi_col] - rolling_mean) / rolling_std.replace(0, np.nan)
   signals_df['raw_obi_zscore'].fillna(0.0, inplace=True)
   ```

## Technical Details

1. **Z-Score Calculation**: The OBI Z-score is calculated as:
   ```
   Z-score = (raw_obi - rolling_mean) / rolling_std
   ```
   Where:
   - `raw_obi` is the raw Order Book Imbalance value
   - `rolling_mean` is the moving average of raw OBI
   - `rolling_std` is the moving standard deviation of raw OBI

2. **Potential Issues**:
   - Division by zero or near-zero standard deviation
   - Missing or invalid OBI data
   - Incorrect window size for rolling calculations
   - Interaction with other microstructure features

## Recommended Solutions

1. **Short-term Fix**: Keep the feature disabled (set to 0) until the root cause is properly identified and fixed.

2. **Diagnostic Steps**:
   - Create a standalone script to analyze the OBI Z-score calculation
   - Add detailed logging to the signal calculation process
   - Gradually reduce the Z-score threshold to find a working value
   - Check for NaN or infinite values in the raw OBI data

3. **Potential Code Fixes**:
   - Improve error handling in the Z-score calculation
   - Add more robust fallback mechanisms
   - Ensure proper handling of edge cases (division by zero, NaN values)
   - Consider alternative normalization methods for OBI

## Impact on Trading Performance

When properly implemented, the OBI Z-score threshold should:
- Filter out noise in the OBI signal
- Identify statistically significant imbalances
- Reduce false positives in trend detection
- Potentially improve trade quality at the cost of quantity

## Next Steps

1. Create a diagnostic script to analyze OBI Z-score calculation independently
2. Investigate the signal calculation process in isolation
3. Implement improved error handling and fallback mechanisms
4. Test with gradually increasing threshold values
5. Consider alternative approaches if the Z-score method proves problematic
