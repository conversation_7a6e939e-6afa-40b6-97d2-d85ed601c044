# GMS State Mapping Standardization Plan

## Overview

This document outlines the step-by-step plan to standardize the GMS (Granular Microstructure State) state mapping across the trading bot codebase. The core issue we're addressing is inconsistency in how the 7-state GMS model maps to the 3-state model (BULL, BEAR, CHOP) in different components of the system.

## Current Issues

1. **Inconsistent State Mapping**: 
   - The `risk.py` file has hardcoded mappings that differ from the official mapping in `gms_state_mapping.yaml`
   - `Weak_Bear_Trend` is incorrectly classified as BEAR in market bias code when it should be CHOP
   - Chop regime names don't match across components ("Ranging", "Sideways", "Choppy" vs "High_Vol_Range", "Low_Vol_Range", "Uncertain")

2. **Potential Impact**:
   - Market bias adjustments may not be applied correctly
   - Future OBI scalper for CHOP regimes might not activate properly
   - Risk management and strategy selection logic are inconsistent

## Implementation Strategy

We'll implement changes in small, testable increments, addressing one component at a time while maintaining backward compatibility.

## Action Steps

### Phase 1: Create State Mapping Utility (Foundational) ✓ COMPLETED

1. **Task 1.1: Create State Mapping Utility Module** ✓
   - Created `hyperliquid_bot/utils/state_mapping.py` with functions to load and apply state mappings
   - Implemented validation functions to verify state names
   - Added comprehensive docstrings and type hints
   - Created and ran test script to verify utility function behavior
   - Successfully loads mappings from `gms_state_mapping.yaml` configuration

2. **Task 1.2: Define Official GMS State Constants** ✓
   - Added functions to retrieve all official GMS states by category
   - Implemented validation tools to check state names against official list
   - Added graceful fallback handling for unknown states (defaults to CHOP)

### Phase 2: Update Risk Manager Implementation ✓ COMPLETED

3. **Task 2.1: Refactor Market Bias Implementation in `risk.py`** ✓
   - Replaced hardcoded state mappings with references to the utility module
   - Implemented proper error handling for unknown states
   - Added improved logging to track state mapping for debugging
   - Created and ran test script to verify market bias implementation

4. **Task 2.2: Add Validation in Risk Manager** ✓
   - Added validation in `_validate_market_bias` to confirm mapping consistency
   - Implemented logging for any potential state mapping issues
   - Fixed critical indentation problems in `risk.py` that caused "return outside function" errors
   - Tested the validation with various GMS states to verify proper functioning

### Phase 3: Update Strategy Evaluator ✓ COMPLETED

5. **Task 3.1: Refactor Strategy Evaluator in `evaluator.py`** ✓
   - Updated the GMS 3-state mapping logic to use the utility module
   - Modified `get_active_strategies` method to use `map_gms_state()` for regime mapping
   - Added validation to check for valid 3-state values
   - Improved error handling and fallback mechanisms
   - Implemented proper logging for debugging and traceability

6. **Task 3.2: Implement TIGHT_SPREAD Handling** ✓
   - Added TIGHT_SPREAD to the official state mapping in `gms_state_mapping.yaml`
   - Configured TIGHT_SPREAD to map to CHOP for consistent strategy activation
   - Ensured TIGHT_SPREAD is correctly handled when `gms_use_three_state_mapping` is enabled
   - Implemented and executed comprehensive verification tests
   - Identified architectural insight: TIGHT_SPREAD only overrides "Uncertain" states

### Phase 4: GMS Detector Enhancements ✓ COMPLETED

7. **Task 4.1: Update GMS Detector in `detector.py`** ✓
   - Added imports for standardized state constants from the utility module
   - Updated the class documentation to reflect standardized state names
   - Implemented output validation to ensure detector produces standardized state names
   - Added fallback to UNKNOWN state if invalid state names are produced

### Phase 5: Global System Validation ✓ COMPLETED

8. **Task 5.1: Implement System-wide Configuration Validation** ✓
   - Created `utils/system_validation.py` with validation functions
   - Implemented GMS state mapping validation at startup
   - Added configuration consistency checks
   - Integrated validation with the backtester startup process
   - Implemented proper error handling and warning mechanism

9. **Task 5.2: Create Detailed Regime Analytics** ✓
   - Created `utils/regime_analytics.py` for state transition analysis
   - Implemented special handling for TIGHT_SPREAD state
   - Added regime transition pattern detection
   - Created statistics for state durations and transition frequencies
   - Implemented JSON report generation for detailed analytics

10. **Task 5.3: Enhance Visualization Tools** ✓
    - Enhanced `visualize_backtest.py` to support regime transition visualization
    - Added dedicated analytics script `generate_regime_analytics.py`
    - Implemented timeline visualization for regime transitions
    - Added special highlighting for TIGHT_SPREAD states
    - Ensured backward compatibility with existing visualization
   - Added detailed error logging for state validation issues
   - Verified compatibility with TIGHT_SPREAD state generation

### Phase 5: Global System Validation

8. **Task 5.1: Add Startup Configuration Validation**
   - Create a validation routine to check mapping configuration at startup
   - Verify that all required states are properly mapped
   - Log warnings for any potential inconsistencies

9. **Task 5.2: Create Diagnostic Tools for Testing**
   - Add diagnostic logging in backtester to track state transitions
   - Create tools to analyze state distribution in backtests
   - Implement verification checks for state mapping consistency

## Testing Approach

For each task:
1. Implement the specific change in isolation
2. Run unit tests if available
3. Execute a simple backtest to verify behavior
4. Check logs for any warnings or errors
5. Verify that 3-state mapping behaves as expected

## Success Criteria

The implementation is successful when:
1. All components use the same state mapping logic
2. Market bias adjustments are correctly applied based on the official mapping
3. The system properly logs any state transitions and mappings
4. No existing functionality is broken
5. The codebase is prepared for implementing the OBI scalper for CHOP regimes

## Important Warning: Code Indentation Issues

During the implementation of Phase 2, critical indentation issues were discovered in the `risk.py` file. These problems caused syntax errors with messages like "return can be used only within a function" and made certain methods inaccessible due to improper indentation.

### Preventive Measures

1. **Maintain Proper Class Indentation**: 
   - All methods within classes MUST be properly indented with 4 spaces
   - Class method definitions should never be at the same indentation level as the class itself
   - Ensure all return statements are properly nested within functions

2. **Common Indentation Issues**:
   - Missing indentation after class or method definition
   - Inconsistent indentation levels within methods
   - Return statements outside of function scope
   - Duplicate method definitions at different indentation levels

3. **Syntax Validation**:
   - Always run a syntax check on Python files after editing: `python3 -m py_compile risk.py`
   - Utilize proper IDE features for code formatting and indentation checking
   - Review changes carefully during code reviews, paying special attention to indentation

## Known Issues and Potential Bugs

1. **TIGHT_SPREAD Detection Architecture**:
   - Investigation revealed that TIGHT_SPREAD is a fallback state that **only overrides "Uncertain"** states in the detector
   - The detection sequence follows this flow: Raw detector (7-state) → TIGHT_SPREAD check → Output 7-state → 3-state mapping
   - Both "Uncertain" and "TIGHT_SPREAD" ultimately map to CHOP in 3-state mapping
   - This explains why modifying `gms_tight_spread_fallback_percentile` (0.15, 0.55, 0.95) had no effect on backtest results
   - For TIGHT_SPREAD to affect strategy selection distinctly from CHOP, the evaluator must check for it before applying 3-state mapping
   - Documentation for future OBI scalper implementation has been created in `docs/OBI_preparation.md`

2. **Confirmation Bars Behavior**:
   - The `gms_confirmation_bars` setting affects regime detection stability:
     - `null` or `0`: No confirmation required, immediate regime changes
     - `1`: "CONFIRMED immediately" but still runs confirmation logic
     - `2+`: Multiple bars required for confirmation, shows "Uncertain" during transition

3. **Indentation Sensitivity in Python Files**:
   - Multiple instances of syntax errors were introduced during development due to indentation issues
   - Always use the `py_compile` module to verify syntax after making changes

## Achievements and Next Steps

1. **✓ COMPLETED: Phase 5: Global System Validation**
   - Successfully implemented configuration validation at startup
   - Created detailed regime analytics in backtest outputs
   - Added visualization tools for state transitions
   - Special handling for TIGHT_SPREAD state throughout the system
   - Created standalone regime analytics tools for in-depth analysis

2. **Next: Prepare for OBI Scalper Implementation**
   - Use insights from `docs/OBI_preparation.md` as foundation
   - Create the `OBIScalper` strategy class in `strategies/`
   - Leverage the new standardized state mapping system
   - Use the regime analytics tools to monitor TIGHT_SPREAD detection
   - Add OBI-specific configuration parameters to `base.yaml`:
     ```yaml
     # OBI Scalper specific settings
     obi_scalper_enabled: true
     obi_scalper_risk_factor: 0.3
     obi_min_imbalance_trigger: 2.0
     ```
   - Implement special handling for TIGHT_SPREAD in strategy activation logic

3. **Further Enhancements**
   - ✓ Added TIGHT_SPREAD detection metrics to backtest outputs
   - ✓ Created visualization tools to track regime transitions
   - Enhance the new analytics tools with order book imbalance metrics
   - Create additional visualizations specifically for order book dynamics
   - Develop integration between the regime analytics and strategy evaluation

## Rollback Plan

For each task, we will:
1. Keep a backup of original files before modifications
2. If issues are detected, roll back the most recent change
3. Diagnose the issue in isolation before attempting a fix
4. Re-implement the change with appropriate corrections
