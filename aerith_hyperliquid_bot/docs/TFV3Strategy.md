# TFV3Strategy Documentation

## Overview

The TFV3Strategy is an enhanced trend-following strategy that builds upon the original TrendFollowingStrategy with several key improvements:

1. **Regime-gated entries** - Only trades in BULL/BEAR regimes as determined by the Continuous GMS detector
2. **Risk suppression** - No new positions when the risk_suppressed flag is true
3. **ATR trailing stops** - Dynamic stop loss based on ATR
4. **Time-decay exit** - Positions are closed after a maximum holding period
5. **Forward-compatible RiskManager hooks** - Uses a clean interface for risk management
6. **Strict no look-ahead bias** - Only uses data available at or before candle open

## Configuration

The strategy is configured in the `tf_v3` section of the `base.yaml` file:

```yaml
tf_v3:
  enabled: true
  ema_fast: 20
  ema_slow: 50
  atr_period: 14          # on 1‑hour bars
  atr_trail_k: 3.0        # stop distance in ATRs
  max_trade_life_h: 24    # time‑decay exit
  risk_frac: 0.25         # fraction of free notional to allocate
  max_notional: 25000     # absolute cap per TF trade
  gms_max_age_sec: 120    # staleness threshold for regime snapshot
```

To enable the strategy, also set `use_tf_v3: true` in the `strategies` section.

## Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `enabled` | Enable/disable the strategy | `true` |
| `ema_fast` | Fast EMA period | `20` |
| `ema_slow` | Slow EMA period | `50` |
| `atr_period` | ATR period for stop calculation | `14` |
| `atr_trail_k` | ATR multiplier for trailing stop | `3.0` |
| `max_trade_life_h` | Maximum trade life in hours | `24` |
| `risk_frac` | Fraction of available notional to allocate | `0.25` |
| `max_notional` | Maximum notional value per trade | `25000` |
| `gms_max_age_sec` | Maximum age of GMS snapshot in seconds | `120` |

## Algorithm

The TFV3Strategy follows this algorithm for each new 1-hour candle:

1. **Gather regime snapshot** - Get the latest GMS snapshot available at candle open
2. **Staleness guard** - Skip if the GMS snapshot is older than `gms_max_age_sec`
3. **Validate regime stability** - Skip if the regime is unstable (implemented in T-111f)
4. **Risk suppression gate** - Skip if risk_suppressed is true
5. **Trend filter** - Skip if regime is not BULL or BEAR
6. **EMA alignment check** - Ensure EMAs align with regime (fast > slow for BULL, fast < slow for BEAR)
7. **Position sizing** - Calculate position size based on available notional and risk fraction
8. **Exit logic** - Check for ATR trailing stop or time decay exit

## Entry Conditions

For a long entry:
- Regime must be BULL
- Fast EMA must be above Slow EMA
- No existing long position

For a short entry:
- Regime must be BEAR
- Fast EMA must be below Slow EMA
- No existing short position

## Exit Conditions

Positions are exited when:
- **ATR trailing stop** - Price crosses entry ± k·ATR
- **Time decay** - Position age exceeds max_trade_life_h

## Failure Modes

The strategy tracks various failure modes for diagnostic purposes:

| Failure Mode | Description |
|--------------|-------------|
| `fail_missing_signal` | Required signals are missing |
| `fail_regime_gate` | Regime is not BULL/BEAR |
| `fail_risk_suppressed` | Risk suppression is active |
| `fail_gms_stale` | GMS snapshot is too old |
| `fail_gms_unstable` | GMS snapshot is unstable |
| `fail_ema_alignment` | EMAs not aligned with regime |
| `fail_condition` | Other condition failures |

## Risk Management

The strategy uses the RiskManagerInterface to calculate position sizes:

```python
size = min(cfg.tf_v3.max_notional,
           RiskManager.available_notional() * cfg.tf_v3.risk_frac)
```

This ensures that:
1. Position sizes are capped at `max_notional`
2. Position sizes are proportional to available notional
3. The strategy is compatible with future RiskManager implementations

## Dependencies

- Continuous GMS detector for regime information
- RiskManagerInterface for position sizing
- SignalEngine for EMA and ATR calculations

## Future Enhancements

- Integration with GMSValidator (T-111f) for regime stability checks
- Adaptive EMA lengths based on ATR (T-111e)
- Shadow mode comparison with TF-v2 (T-115a)
