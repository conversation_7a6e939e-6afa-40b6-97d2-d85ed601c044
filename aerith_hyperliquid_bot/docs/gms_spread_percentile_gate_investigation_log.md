# Investigation Log: gms_spread_percentile_gate Issue

This document summarizes the debugging steps taken to investigate why changes to the `gms_spread_percentile_gate` configuration do not affect backtest results in the `aerith_hyperliquid_bot`.

## 1. Initial Problem Statement

The core issue is that different settings for `gms_spread_percentile_gate` (e.g., 30 vs. 70) yield identical overall backtest results, despite this parameter being intended to influence the `'WIDE_SPREAD'` regime detection and subsequent trading decisions.

## 2. Initial Logging Setup and Checks (Based on `spread_percentile_gate_debug.md`)

*   **Log Levels Adjusted:**
    *   In [`aerith_hyperliquid_bot/hyperliquid_bot/backtester/run_backtest.py`](../hyperliquid_bot/backtester/run_backtest.py), the log levels for `StrategyEvaluator` and `Portfolio` loggers were changed from `INFO` to `DEBUG` to capture more detailed messages.
*   **Override Configurations Created:**
    *   [`aerith_hyperliquid_bot/configs/overrides/debug_gms_30.yaml`](../configs/overrides/debug_gms_30.yaml) (setting `gms_spread_percentile_gate: 30`)
    *   [`aerith_hyperliquid_bot/configs/overrides/debug_gms_70.yaml`](../configs/overrides/debug_gms_70.yaml) (setting `gms_spread_percentile_gate: 70`)
*   **Logging Statements Verification:**
    *   The logging statements proposed in [`aerith_hyperliquid_bot/docs/spread_percentile_gate_debug.md`](spread_percentile_gate_debug.md) for `StrategyEvaluator` and `Portfolio` were confirmed to be already implemented and active in the codebase.

## 3. Backtest Set 1 (Log Suffix: `_debug`)

*   **Log Files:**
    *   `logs/backtest_run_gms_30_debug_20250509_034823.log`
    *   `logs/backtest_run_gms_70_debug_20250509_035241.log`
*   **Key Finding:**
    *   Searches for `RawReg: WIDE_SPREAD` and `WIDE_SPREAD` (case-sensitive) yielded **0 results** in both log files.
    *   **Conclusion:** The `WIDE_SPREAD` regime was not being activated at all by the `StrategyEvaluator`.

## 4. Analysis of `GranularMicrostructureRegimeDetector` (Round 1)

*   **File:** [`aerith_hyperliquid_bot/hyperliquid_bot/core/detector.py`](../hyperliquid_bot/core/detector.py)
*   **Hypothesis:** The `SKEWED_BOOK` market condition check was potentially preempting the `WIDE_SPREAD` check. This was due to `gms_depth_skew_thresh` defaulting to `-np.inf` in the detector's `__init__` if not set in `base.yaml`, making the condition `abs(depth_skew) > self.depth_skew_thresh` almost always true.
*   **Action:**
    *   Modified [`aerith_hyperliquid_bot/configs/base.yaml`](../configs/base.yaml) to set `gms_depth_skew_thresh: 100.0` (a high positive value) to make the `SKEWED_BOOK` condition less likely to trigger.

## 5. Backtest Set 2 (Log Suffix: `_debug_v2`)

*   **Log Files (after `gms_depth_skew_thresh` change):**
    *   `logs/backtest_run_gms_30_debug_v2_20250509_040129.log`
    *   `logs/backtest_run_gms_70_debug_v2_20250509_040209.log`
*   **Key Findings:**
    *   Still **0 results** for `RawReg: WIDE_SPREAD`.
    *   Still **0 results** for `Mkt='WIDE_SPREAD'` (from the detector's internal logging).
    *   **0 results** for `Logic:Spread Percentile` (from the detector's internal logging).
    *   **Conclusion:** The `WIDE_SPREAD` logic within the detector was still not being reached, meaning `market_condition` was not `'NORMAL'` at that point in the code.

## 6. Further Analysis of `GranularMicrostructureRegimeDetector.get_regime`

*   **Hypothesis:** The `get_regime` method was exiting silently before the main "GMS Factors" log line, possibly due to an unhandled exception or an early return not covered by existing warnings.
*   **Action 1:** Added a `DEBUG` log: `"GMS get_regime ENTRY..."` at the very beginning of the `get_regime` method in [`aerith_hyperliquid_bot/hyperliquid_bot/core/detector.py`](../hyperliquid_bot/core/detector.py) (after the `Filter_Off` check).

## 7. Backtest Set 3 (Log Suffix: `_debug_v3`)

*   **Log File (gms_30 only):** `logs/backtest_run_gms_30_debug_v3_20250509_041205.log`
*   **Key Finding:**
    *   Still **0 results** for the new `"GMS get_regime ENTRY..."` log.
    *   **Conclusion:** The `get_regime` method itself was likely not being entered as expected, or an issue was occurring even before that new log.

## 8. Analysis of `Backtester._run_simulation_loop`

*   **File:** [`aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py`](../hyperliquid_bot/backtester/backtester.py)
*   **Observation:** The loop calling `self.regime_detector.get_regime` has a `try...except Exception as regime_e:` block that logs an error and defaults the regime to `"Unknown"` if an exception occurs during regime detection. This could mask errors within `get_regime`.
*   **Action 2:** Added a `DEBUG` log: `"Attempting to call self.regime_detector.get_regime..."` in `Backtester._run_simulation_loop` immediately before the call to `self.regime_detector.get_regime`.

## 9. Backtest Set 4 (Log Suffix: `_debug_v4`)

*   **Log File (gms_30 only, currently running as of 2025-05-09 04:20 AM Europe/Athens):**
    *   Expected: `logs/backtest_run_gms_30_debug_v4_YYYYMMDD_HHMMSS.log`
*   **Current Status:** The backtest for `run-id gms_30_debug_v4` is still in progress. Analysis of its logs is pending completion.

## Next Steps (for a new task)

1.  Await completion of the `gms_30_debug_v4` backtest.
2.  Analyze the log file `logs/backtest_run_gms_30_debug_v4_*.log`:
    *   Search for `"Attempting to call self.regime_detector.get_regime..."`.
    *   Search for `"GMS get_regime ENTRY..."`.
    *   If the first appears but not the second, it indicates an issue with the `GranularMicrostructureRegimeDetector` class or its instantiation, or the `get_regime` method is erroring out extremely early.
    *   If both appear, proceed to check for the "GMS Factors" log and then the `WIDE_SPREAD` conditions.
3.  Based on these findings, further pinpoint the exact location of the failure in the regime detection logic.