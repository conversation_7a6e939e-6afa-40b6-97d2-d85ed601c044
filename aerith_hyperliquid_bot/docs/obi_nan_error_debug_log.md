# OBI NaN Error Debugging Log - Task T-102

## Date: 2025-05-21

## Issue Description
Backtests were failing with NaN errors related to Order Book Imbalance (OBI) signals, specifically `obi_smoothed`, when used by the `GranularMicrostructureRegimeDetector`.

## Debugging Steps & Findings

1.  **Initial Diagnosis (Provided by Debug Mode):**
    *   **Root Cause Identified**: A mismatch between the generated OBI signal name and the name expected by the `GranularMicrostructureRegimeDetector`.
    *   **Signal Generation Context**:
        *   File: [`hyperliquid_bot/signals/calculator.py`](aerith_hyperliquid_bot/hyperliquid_bot/signals/calculator.py:1)
        *   Logic: Generates suffixed OBI signal names (e.g., `obi_smoothed_5`, `raw_obi_zscore_5`). The suffix is derived from the `microstructure.obi_levels` value in the configuration file (e.g., [`configs/base.yaml`](aerith_hyperliquid_bot/configs/base.yaml:1)).
    *   **Signal Consumption Context**:
        *   File: [`hyperliquid_bot/core/detector.py`](aerith_hyperliquid_bot/hyperliquid_bot/core/detector.py:1)
        *   Logic: The `GranularMicrostructureRegimeDetector` was hardcoded to look for non-suffixed OBI signal names like `'obi_smoothed'` and `'raw_obi_zscore'`.
    *   **Impact**: Because the detector was looking for the wrong signal names, it couldn't find them in the `signals_df` passed to its `get_regime` method. This resulted in these signals being treated as NaN, leading to downstream calculation errors or incorrect regime detection.

2.  **Code Review and Confirmation of Diagnosis:**
    *   Reviewed [`aerith_hyperliquid_bot/hyperliquid_bot/core/detector.py`](aerith_hyperliquid_bot/hyperliquid_bot/core/detector.py:1):
        *   Confirmed that in the `GranularMicrostructureRegimeDetector` class, the `required_signals` property and the signal access logic within `get_regime` (around line 485, later updated) were indeed using hardcoded, non-suffixed OBI signal names.
        *   The `obi_levels` configuration was not being utilized within the detector to construct these names dynamically.
    *   Reviewed [`aerith_hyperliquid_bot/hyperliquid_bot/signals/calculator.py`](aerith_hyperliquid_bot/hyperliquid_bot/signals/calculator.py:1) (conceptual, based on diagnosis):
        *   Confirmed that the signal generation logic correctly appends the `obi_levels` suffix to OBI-related signals.

3.  **Fix Implementation Strategy:**
    *   The core idea was to make the `GranularMicrostructureRegimeDetector` aware of the `obi_levels` configuration and use it to dynamically construct the expected signal names.
    *   **Step 1: Store `obi_levels`**:
        *   In the `__init__` method of `GranularMicrostructureRegimeDetector`, access `self.config.microstructure.obi_levels` and store it as an instance variable (e.g., `self.obi_levels`).
    *   **Step 2: Update `required_signals`**:
        *   Modify the `required_signals` property to use f-strings to build the OBI signal names, incorporating `self.obi_levels`. For example, `'obi_smoothed'` would become `f'obi_smoothed_{self.obi_levels}'`.
    *   **Step 3: Update `get_regime`**:
        *   Adjust the logic within `get_regime` where OBI signals are retrieved from the `signals` dictionary (e.g., `signals.get('obi_smoothed')`) to use the new dynamically constructed names (e.g., `signals.get(f'obi_smoothed_{self.obi_levels}')`).
        *   Ensure the list of signals checked for NaNs (`required_now`) also uses these dynamic names.

4.  **Applying the Fix (Iterative Process):**
    *   Applied the planned changes to [`aerith_hyperliquid_bot/hyperliquid_bot/core/detector.py`](aerith_hyperliquid_bot/hyperliquid_bot/core/detector.py:1) using the `apply_diff` tool.
    *   This involved several iterations due to concurrent file modifications or slight misalignments in diff application, requiring re-reading the file and re-applying diffs to specific hunks.
        *   Initial diff application.
        *   Re-application after partial success.
        *   Further re-application for remaining hunks.

5.  **Linting and Code Cleanup:**
    *   After applying the core fix, `ruff check --fix` was run.
    *   Several linting issues were identified:
        *   `F841` (local variable assigned but never used): Primarily for `chop_indicator_used` in `RuleBasedRegimeDetector` and some signal retrieval variables in `GranularMicrostructureRegimeDetector` that were later refactored or found to be unnecessary for the direct logic path. These were addressed by commenting out the unused assignments.
        *   `E701` (multiple statements on one line): Corrected by moving assignments or `raise` statements within `if` blocks to their own lines. This also involved an iterative process with `apply_diff` due to the file being in flux.
    *   The linting process also required multiple `read_file` and `apply_diff` cycles to ensure all reported issues were correctly addressed in the latest version of the file.

6.  **Verification:**
    *   The user confirmed that after these changes, the backtest which was previously failing due to NaN errors related to OBI signals now runs successfully. This serves as the primary verification that the fix was effective.

## Summary of Fix
The `GranularMicrostructureRegimeDetector` in [`aerith_hyperliquid_bot/hyperliquid_bot/core/detector.py`](aerith_hyperliquid_bot/hyperliquid_bot/core/detector.py:1) was updated to dynamically construct OBI-related signal names (e.g., `obi_smoothed_5`, `raw_obi_zscore_5`) using the `microstructure.obi_levels` value from the configuration. This resolved the mismatch where the detector was expecting non-suffixed names while the signal calculator produced suffixed names, thereby eliminating the NaN errors in backtests.