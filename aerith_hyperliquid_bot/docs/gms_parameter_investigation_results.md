# GMS Parameter Investigation Results

## Overview

This document summarizes the findings from our investigation into the Granular Microstructure (GMS) detector parameters in the Hyperliquid backtester. We specifically focused on understanding the validation requirements for these parameters and testing their actual impact on backtest results.

## Parameter Validation Issues

### Initial Problem

We encountered validation errors when trying to use certain parameters with values that didn't match their validation constraints:

```
CRITICAL ERROR: Failed to validate merged configuration: 1 validation error for Config
regime.gms_depth_slope_thin_limit
  Input should be greater than or equal to 0 [type=greater_than_equal, input_value=-0.1, input_type=float]
```

### Validation Requirements

After examining the code in `settings.py`, we found the following validation constraints:

| Parameter | Validation Constraint | Default |
|-----------|----------------------|---------|
| `gms_obi_zscore_threshold` | `ge=0` (>= 0) | `None` |
| `gms_spread_percentile_gate` | `ge=0, le=100` (0-100) | `None` |
| `gms_depth_slope_thin_limit` | `ge=0` (>= 0) | `None` |
| `gms_depth_skew_thresh` | No constraint | `None` |
| `gms_spread_trend_lookback` | `gt=0` (> 0) | `None` |
| `gms_adaptive_obi_base` | `gt=0` (> 0) | `None` |
| `gms_confirmation_bars` | `gt=0` (> 0) | `None` |

### Comment vs. Code Discrepancy

We found discrepancies between the comments in the configuration file and the actual validation requirements:

```yaml
# Incorrect comment
gms_depth_slope_thin_limit: -0.1           # Set > -inf (e.g., -0.1) to enable Depth Slope filter

# Corrected comment
gms_depth_slope_thin_limit: null           # Set >= 0 (e.g., 0.1) to enable Depth Slope filter, null to disable
```

## Parameter Effectiveness Testing

### Test Methodology

We tested the effectiveness of the `gms_depth_slope_thin_limit` parameter by running backtests with different values:

1. `gms_depth_slope_thin_limit: 0.1`
2. `gms_depth_slope_thin_limit: 0.5` (significantly higher value)

### Results

Both backtests produced identical results:
- 149 total trades
- 51.0% win rate
- $12,784.34 total P&L

This suggests that the `gms_depth_slope_thin_limit` parameter is not actually affecting trading decisions, despite being properly loaded by the detector (as shown in the logs).

### Possible Causes

1. **Missing Data**: The `depth_slope` data might not be available in the dataset, causing the filter to be effectively ignored.

2. **Placeholder Implementation**: In the detector code, there are comments indicating this might be a placeholder feature:
   ```python
   # NaN check already done if this path is active, but depth_slope might still be NaN (allowed placeholder)
   if not pd.isna(depth_slope):
       if depth_slope < self.depth_slope_thin_limit:
            market_condition = 'THIN_LIQUIDITY'
   ```

3. **Signal Calculation Issue**: The `depth_slope` signal might not be properly calculated in the signal engine.

## Handling of `None` Values

We also identified and fixed issues with how `None` values were handled in the code:

1. In the `SignalEngine.calculate_all_signals` method:
   ```python
   # Before (error-prone)
   if gms_obi_zscore_threshold > 0:
   
   # After (fixed)
   if gms_obi_zscore_threshold is not None and gms_obi_zscore_threshold > 0:
   ```

2. In the `GranularMicrostructureRegimeDetector` class:
   ```python
   # Before (error-prone)
   if self.depth_slope_thin_limit > -np.inf:
   
   # After (fixed)
   if self.depth_slope_thin_limit is not None and self.depth_slope_thin_limit > -np.inf:
   ```

## Recommendations

1. **Use `null` for Disabled Features**: Set parameters to `null` in the YAML file to disable them, rather than using numeric values like 0.

2. **Investigate Data Availability**: Check if the necessary data (like `depth_slope`) is actually available in your dataset.

3. **Add Debug Logs**: Add more detailed logging to track the actual values being used in comparisons.

4. **Review Signal Calculation**: Examine how signals like `depth_slope` are calculated and ensure they're properly implemented.

5. **Update Comments**: Ensure comments in the configuration file accurately reflect the validation requirements.

## Parameter Usage Guide

| Parameter | Purpose | Example Value | Notes |
|-----------|---------|--------------|-------|
| `gms_obi_zscore_threshold` | Filter based on OBI statistical significance | `1.5` | Higher values require more extreme imbalances |
| `gms_spread_percentile_gate` | Prevent trading during wide spreads | `95` | Filters out trades when spread is in top 5% |
| `gms_depth_slope_thin_limit` | Detect thin liquidity conditions | `0.1` | Currently may not be functioning |
| `gms_depth_skew_thresh` | Measure order book asymmetry | `0.5` | Currently may be a placeholder |
| `gms_spread_trend_lookback` | Analyze spread trends | `12` | Number of periods to analyze |
| `gms_adaptive_obi_base` | Adjust OBI threshold based on volatility | `0.1` | Base threshold multiplied by volatility ratio |
| `gms_confirmation_bars` | Require multiple bars to confirm regime | `1` | Higher values require more confirmation |

## Next Steps

1. Verify if `depth_slope` and `depth_skew` data is available and properly calculated
2. Add debug logging to track these values during backtesting
3. Consider implementing proper calculation of these metrics if they're currently placeholders
4. Update configuration file comments to accurately reflect validation requirements
