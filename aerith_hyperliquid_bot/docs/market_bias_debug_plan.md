# Market Bias Debugging Plan

## Overview

This document outlines a systematic approach to debug and verify each component of the market bias system. The goal is to ensure all configuration flags work as expected and to understand the exact sequence of operations in position size calculations.

## Market Bias Component Flow

```mermaid
flowchart TD
    A[Trade Signal] --> B[Regime Detection]
    B --> C[Map to 3-State Model\nBULL/BEAR/CHOP]
    C --> D[Calculate Base Position Size\nFrom risk_per_trade and ATR]
    
    D --> E{Market Bias\nEnabled?}
    E -->|No| L[Use Base Position Size]
    
    E -->|Yes| F[Apply Market Risk Factor\nbased on market state]
    F --> G{Direction\nKnown?}
    
    G -->|No| H[Skip Direction Bias]
    G -->|Yes| I[Apply Direction Bias\nbased on market state and trade direction]
    
    H --> J[Calculate Leverage\nbased on market state]
    I --> J
    
    J --> K[Apply Global Leverage\nBounds]
    K --> M[Final Position Size\nand Leverage]
```

## Debugging Steps

### 1. Baseline Test

First, establish a baseline with all market bias settings at neutral values:

```yaml
market_bias:
  enabled: true
  use_three_state_mapping: true
  
  # Set all factors to 1.0 (neutral)
  bull_leverage_factor: 1.0
  bear_leverage_factor: 1.0
  chop_leverage_factor: 1.0
  
  bull_risk_factor: 1.0
  bear_risk_factor: 1.0
  chop_risk_factor: 1.0
  
  bull_long_bias: 1.0
  bull_short_bias: 1.0
  bear_long_bias: 1.0
  bear_short_bias: 1.0
```

Run backtest with enhanced logging to capture baseline metrics.

### 2. Verify Market State Mapping

Test that regimes are correctly mapped to 3-state model:

```python
# Add to risk.py in calculate_position method:
def calculate_position(self, portfolio, signals, strategy_name, regime, strategy_info=None):
    # ...
    # After market state mapping
    self.logger.info(f"MAPPING TEST: Original regime '{regime}' mapped to '{market_state}'")
```

Run backtest, check logs for mapping consistency.

### 3. Test Market Risk Factors

Test each market risk factor independently:

#### 3.1 Bull Risk Factor Test
```yaml
market_bias:
  enabled: true
  # Set only bull_risk_factor to non-neutral
  bull_risk_factor: 2.0  # Double position size in bull markets
  # All other factors at 1.0
```

Run backtest with enhanced logging:

```python
# Add to risk.py:
self.logger.info(f"RISK FACTOR TEST: Market={market_state}, Base Risk=${risk_amount_per_trade/risk_factor:.2f}, After Market Risk=${risk_amount_per_trade:.2f}, Factor={risk_factor:.2f}")
```

#### 3.2 Bear Risk Factor Test
Same as 3.1 but with only `bear_risk_factor: 0.5`

#### 3.3 Chop Risk Factor Test
Same as 3.1 but with only `chop_risk_factor: 0.5`

### 4. Test Direction Bias

#### 4.1 Direction Information Debug

Modify backtester to explicitly set direction in the strategy_info dictionary:

```python
# In backtester.py, find where risk manager is called:
# The key issue appears to be direction information not being passed properly

# Add this before calling risk_manager.calculate_position():
if 'direction' not in strategy_info:
    # Determine direction from position signal
    direction = 'long' if position_signal > 0 else 'short' if position_signal < 0 else 'flat'
    strategy_info = strategy_info or {}
    strategy_info['direction'] = direction
    self.logger.info(f"DIRECTION DEBUG: Adding direction '{direction}' to strategy_info")

size, leverage = self.risk_manager.calculate_position(..., strategy_info=strategy_info)
```

Enhance logging in risk.py:

```python
self.logger.info(f"DIRECTION DEBUG: Strategy '{strategy_name}' provided direction: '{direction}'")
self.logger.info(f"STRATEGY_INFO DEBUG: {strategy_info}")
```

#### 4.2 Bull Long/Short Bias Test

Test with only direction bias enabled:

```yaml
market_bias:
  enabled: true
  bull_long_bias: 2.0   # Double long positions in bull markets
  bull_short_bias: 0.5  # Halve short positions in bull markets
  # All other factors at 1.0
```

Add logging to show direction bias application:

```python
if direction != "unknown":
    original_size = size  # Store original size before applying direction bias
    market_adjusted_leverage *= direction_bias
    self.logger.info(f"DIRECTION BIAS TEST: Market={market_state}, Direction={direction}, Bias={direction_bias:.2f}x")
```

#### 4.3 Bear Long/Short Bias Test

Same as 4.2 but with only bear direction biases modified.

### 5. Test Leverage Factors

For each market state (BULL, BEAR, CHOP), test with only the leverage factor modified:

```yaml
market_bias:
  enabled: true
  bull_leverage_factor: 2.0  # Only test one factor at a time
  # All other factors at 1.0
```

Add logging to show leverage factor application:

```python
self.logger.info(f"LEVERAGE TEST: Market={market_state}, Base Leverage={base_leverage_market:.2f}, Market Factor={market_leverage_factor:.2f}, Final={market_adjusted_leverage:.2f}")
```

### 6. Comprehensive Integration Test

Test all components together with distinct values:

```yaml
market_bias:
  enabled: true
  bull_leverage_factor: 1.5
  bear_leverage_factor: 0.7
  chop_leverage_factor: 0.5
  
  bull_risk_factor: 1.2
  bear_risk_factor: 0.8
  chop_risk_factor: 0.5
  
  bull_long_bias: 1.5
  bull_short_bias: 0.5
  bear_long_bias: 0.5
  bear_short_bias: 1.5
```

Run backtest with all enhanced logging enabled.

## Implementing Debug Logging

To add the necessary debug logging for these tests, create a new file `debug_risk.py` that enhances the RiskManager class:

```python
# File: hyperliquid_bot/core/debug_risk.py

import logging
from hyperliquid_bot.core.risk import RiskManager as OriginalRiskManager
from typing import Dict, Optional, Tuple

class DebugRiskManager(OriginalRiskManager):
    """Enhanced RiskManager with additional debug logging for market bias testing"""
    
    def calculate_position(self, portfolio, signals, strategy_name, regime, strategy_info=None):
        """Enhanced version with detailed debug logging"""
        self.logger.info(f"=== MARKET BIAS DEBUG === Strategy: {strategy_name}, Regime: {regime}")
        self.logger.info(f"Strategy Info: {strategy_info}")
        
        # Call original method to get results
        size, leverage = super().calculate_position(
            portfolio, signals, strategy_name, regime, strategy_info
        )
        
        # Log final results
        self.logger.info(f"=== RESULT === Size: {size}, Leverage: {leverage}")
        
        return size, leverage
```

To use this enhanced risk manager in backtesting, modify the backtester initialization to use the debug version.

## Backtest Configuration

To ensure consistent testing, use the following backtest parameters:

- Symbol: BTC-PERP
- Timeframe: 15m
- Date range: At least 2 weeks to capture different market regimes
- Initial balance: $10,000
- Only enable trend_following strategy

## Results Analysis

For each test, analyze:

1. Position sizes in different market regimes
2. Effect of direction bias on position sizes
3. Margin requirements based on leverage factors
4. Trade count consistency between tests

## Implementation Plan

1. Add the debug_risk.py file with enhanced logging
2. Modify backtester.py to include direction in strategy_info
3. Create test config variants in a new configs/debug folder
4. Run baseline test
5. Run individual component tests
6. Analyze logs and results
7. Document findings
8. Implement any necessary fixes
9. Validate fixes with final integration test

## Expected Outcomes

- Verification that all market bias settings work as documented
- Confirmation of the exact sequence of operations in position sizing
- Identification of any components not functioning as expected 
- Clear understanding of direction bias requirements and implementations
