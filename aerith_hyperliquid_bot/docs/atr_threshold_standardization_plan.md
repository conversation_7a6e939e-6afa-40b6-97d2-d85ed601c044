# ATR Threshold Standardization Plan for Hyperliquid Perps

**Date:** May 31, 2025  
**Status:** 🔴 **CRITICAL - Look-Ahead Bias Must Be Eliminated**  
**Impact:** System-wide threshold standardization for production trading

## Executive Summary

The current adaptive threshold implementation introduces **unacceptable look-ahead bias** in backtesting by using future data to calculate historical thresholds. This document proposes a standardized, market-based threshold system specifically calibrated for Hyperliquid perpetual futures that eliminates this bias while maintaining flexibility.

## Problem Statement

### Critical Issues with Adaptive Thresholds

1. **Look-Ahead Bias** (UNACCEPTABLE)
   - Current implementation uses percentiles calculated from future data
   - Backtests show unrealistic performance that cannot be replicated live
   - Example: Using 75th percentile of next 24 hours to set current threshold

2. **Duct-Tape Solution**
   - Adaptive thresholds were added to force continuous_gms/tf-v3 to generate trades
   - Not based on market structure or trading theory
   - Creates inconsistent behavior across different time periods

3. **Market Specificity Ignored**
   - Hyperliquid perps have unique microstructure characteristics
   - Current thresholds not calibrated for crypto perpetual futures
   - One-size-fits-all approach doesn't account for market regimes

## Proposed Solution: Market-Based Standardized Thresholds

### Core Principles

1. **No Look-Ahead Bias**
   - All thresholds based on historical data only
   - Rolling calculations use strictly past information
   - Backtests must be replicable in live trading

2. **Market Structure Aware**
   - Thresholds calibrated for Hyperliquid's specific characteristics
   - Account for 24/7 trading, funding rates, and leverage
   - Different thresholds for different market conditions

3. **Togglable Implementation**
   - Preserve legacy fixed thresholds for baseline replication
   - New standardized system as opt-in feature
   - Clear configuration hierarchy

### Threshold Standardization Framework

#### 1. Base Volatility Regimes (ATR-Based)

```yaml
# Hyperliquid Perps Calibrated Thresholds
volatility_regimes:
  # Based on empirical analysis of BTC/USD perps on Hyperliquid
  ultra_low:
    range: [0.0000, 0.0040]  # 0% - 0.4% ATR
    description: "Dead market, extremely tight ranges"
    typical_conditions: "Asian session lulls, pre-news quiet"
    
  low:
    range: [0.0040, 0.0080]  # 0.4% - 0.8% ATR
    description: "Normal quiet market"
    typical_conditions: "Standard trading, no major events"
    
  medium:
    range: [0.0080, 0.0150]  # 0.8% - 1.5% ATR
    description: "Active trading, normal volatility"
    typical_conditions: "US/EU overlap, minor news"
    
  high:
    range: [0.0150, 0.0300]  # 1.5% - 3.0% ATR
    description: "Elevated volatility"
    typical_conditions: "Major news, liquidation cascades"
    
  extreme:
    range: [0.0300, 1.0000]  # 3.0%+ ATR
    description: "Extreme market conditions"
    typical_conditions: "Black swan events, flash crashes"
```

#### 2. Momentum Regimes (MA Slope Based)

```yaml
# Calibrated for 30-period MA on hourly/minute bars
momentum_regimes:
  strong_bearish:
    threshold: < -150.0
    description: "Aggressive downtrend"
    
  bearish:
    threshold: [-150.0, -50.0]
    description: "Standard downtrend"
    
  neutral:
    threshold: [-50.0, 50.0]
    description: "Ranging/choppy market"
    
  bullish:
    threshold: [50.0, 150.0]
    description: "Standard uptrend"
    
  strong_bullish:
    threshold: > 150.0
    description: "Aggressive uptrend"
```

#### 3. Market Context Overlays

```yaml
# Additional context for threshold adjustment
market_contexts:
  funding_extreme:
    positive_threshold: 0.001   # 0.1% per 8h
    negative_threshold: -0.001  # -0.1% per 8h
    impact: "Reduce position sizes, expect reversals"
    
  low_liquidity:
    spread_threshold: 0.0002    # 2 basis points
    depth_threshold: 100000     # $100k per side
    impact: "Widen stops, reduce size"
    
  high_open_interest:
    oi_change_threshold: 0.20   # 20% daily change
    impact: "Expect volatility expansion"
```

### Implementation Architecture

#### 1. Configuration Structure

```yaml
# In base.yaml or separate threshold_config.yaml
threshold_system:
  mode: 'standardized'  # Options: 'legacy', 'standardized', 'adaptive'
  
  # Legacy mode - Original fixed thresholds
  legacy:
    enabled: true  # Always available for baseline
    vol_high: 0.0092
    vol_low: 0.0055
    mom_strong: 100.0
    mom_weak: 50.0
    
  # Standardized mode - Market-based thresholds
  standardized:
    enabled: false  # Opt-in
    base_config: 'hyperliquid_perps_v1'
    
    # Override specific thresholds if needed
    overrides:
      vol_high: null  # Use base_config value
      vol_low: null   # Use base_config value
      
    # Market regime adjustments
    regime_adjustments:
      bull_market_vol_multiplier: 0.8   # Tighten in bull markets
      bear_market_vol_multiplier: 1.2   # Loosen in bear markets
      
    # Rolling window for regime detection (NO LOOK-AHEAD)
    regime_window_hours: 168  # 7 days of past data only
```

#### 2. Threshold Resolution Logic

```python
class StandardizedThresholdResolver:
    """Resolves thresholds based on configuration and market conditions."""
    
    def get_thresholds(self, mode: str, current_time: pd.Timestamp, 
                      historical_data: pd.DataFrame) -> Dict[str, float]:
        """
        Get thresholds for current time using ONLY past data.
        
        CRITICAL: No look-ahead bias - only uses data before current_time
        """
        if mode == 'legacy':
            return self._get_legacy_thresholds()
            
        elif mode == 'standardized':
            # Get base thresholds
            base = self._get_base_thresholds()
            
            # Apply market regime adjustments using PAST data only
            regime = self._detect_market_regime(
                historical_data[historical_data.index < current_time]
            )
            
            return self._apply_regime_adjustments(base, regime)
            
        elif mode == 'adaptive':
            raise ValueError("Adaptive mode deprecated due to look-ahead bias")
```

#### 3. Market Regime Detection (No Look-Ahead)

```python
def _detect_market_regime(self, past_data: pd.DataFrame) -> str:
    """
    Detect market regime using ONLY historical data.
    
    Uses:
    - 30-day rolling correlation with trend
    - 7-day average volatility percentile
    - Recent funding rate trends
    - NO FUTURE INFORMATION
    """
    # Calculate metrics from past data only
    recent_returns = past_data['returns'].tail(30)
    trend_correlation = recent_returns.corr(pd.Series(range(len(recent_returns))))
    
    recent_vol = past_data['atr_percent'].tail(7).mean()
    vol_percentile = (past_data['atr_percent'].tail(180) < recent_vol).mean()
    
    # Simple regime classification
    if trend_correlation > 0.7 and vol_percentile < 0.5:
        return 'strong_bull'
    elif trend_correlation < -0.7 and vol_percentile > 0.5:
        return 'strong_bear'
    elif vol_percentile > 0.8:
        return 'high_volatility'
    else:
        return 'normal'
```

### Calibration Methodology

#### 1. Historical Analysis (One-Time Offline)

```python
# Analyze Hyperliquid perps characteristics
analysis_periods = [
    '2024_full_year',
    '2024_q4_bull',
    '2025_q1_volatile',
]

for period in analysis_periods:
    # Calculate ATR distributions
    atr_percentiles = calculate_atr_percentiles(period)
    
    # Analyze regime transitions
    regime_stats = analyze_regime_transitions(period)
    
    # Optimize thresholds for:
    # - Signal quality (true positive rate)
    # - Trade frequency (target: 150-200 trades/year)
    # - Risk-adjusted returns (Sharpe > 2.0)
```

#### 2. Threshold Validation

```python
# Ensure thresholds work across different market conditions
validation_metrics = {
    'trade_frequency': {
        'bull_market': [150, 250],  # trades/year
        'bear_market': [100, 200],
        'chop_market': [50, 150],
    },
    'win_rate': {
        'minimum': 0.45,  # 45% minimum
        'target': 0.55,   # 55% target
    },
    'sharpe_ratio': {
        'minimum': 1.5,
        'target': 2.5,
    }
}
```

### Migration Path

#### Phase 1: Validation (Immediate)
1. Implement standardized thresholds alongside legacy
2. Run parallel backtests comparing performance
3. Verify NO look-ahead bias in implementation

#### Phase 2: Soft Launch (1-2 weeks)
1. Enable standardized mode for continuous_gms
2. Monitor live performance vs backtest
3. Maintain legacy mode for TF-v2

#### Phase 3: Full Migration (1 month)
1. Make standardized mode default for new configs
2. Provide migration guide for existing setups
3. Deprecate adaptive mode completely

### Configuration Examples

#### Example 1: Legacy Mode (Baseline Preservation)
```yaml
threshold_system:
  mode: 'legacy'
  # Uses original fixed thresholds
```

#### Example 2: Standardized Mode (Recommended)
```yaml
threshold_system:
  mode: 'standardized'
  standardized:
    base_config: 'hyperliquid_perps_v1'
    regime_adjustments:
      enable_dynamic_adjustment: true
```

#### Example 3: Custom Standardized
```yaml
threshold_system:
  mode: 'standardized'
  standardized:
    base_config: 'hyperliquid_perps_v1'
    overrides:
      vol_high: 0.0120  # Custom high threshold
      # Other thresholds use base_config
```

### Testing Framework

```python
class ThresholdSystemTests:
    """Comprehensive testing for threshold system."""
    
    def test_no_look_ahead_bias(self):
        """CRITICAL: Verify no future data usage."""
        # Run same backtest with shuffled future data
        # Results should be identical
        
    def test_legacy_mode_preservation(self):
        """Ensure legacy mode produces exact baseline."""
        # Compare with saved baseline results
        
    def test_standardized_mode_performance(self):
        """Verify standardized mode meets targets."""
        # Check trade frequency, win rate, Sharpe
```

### Monitoring & Alerts

```yaml
threshold_monitoring:
  alerts:
    - metric: 'threshold_changes_per_day'
      max_value: 4  # Max 4 changes per day
      
    - metric: 'trade_frequency_deviation'
      range: [0.8, 1.2]  # ±20% of expected
      
    - metric: 'regime_detection_stability'
      min_hours: 4  # Regime must persist 4+ hours
```

## Recommendations

### Immediate Actions
1. **Deprecate adaptive thresholds** - Add warnings, plan removal
2. **Implement standardized system** - Start with research/backtesting
3. **Validate no look-ahead** - Comprehensive testing

### Short Term (2 weeks)
1. **Calibrate Hyperliquid-specific thresholds**
2. **Create threshold config UI/tool**
3. **Document migration process**

### Long Term (1 month)
1. **ML-based regime detection** (using only past data)
2. **Market-specific threshold sets** (BTC vs ETH vs alts)
3. **Automated threshold optimization** (offline, no look-ahead)

## Conclusion

The standardized threshold system will:
- ✅ **Eliminate look-ahead bias completely**
- ✅ **Provide market-appropriate thresholds**
- ✅ **Maintain legacy baseline compatibility**
- ✅ **Improve out-of-sample performance**
- ✅ **Enable confident production deployment**

The adaptive threshold system should be considered **deprecated** and replaced with this standardized approach that respects the fundamental rule of backtesting: **never use future information**.

---

**Next Steps:**
1. Review and approve this plan
2. Begin implementation of StandardizedThresholdResolver
3. Create comprehensive test suite
4. Start Hyperliquid market analysis for calibration