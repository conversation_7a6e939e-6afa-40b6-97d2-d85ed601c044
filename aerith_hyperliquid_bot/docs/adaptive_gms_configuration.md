# Optimal GMS Configuration

## Overview

This document documents the evolution of GMS configuration testing, from initial grid search to final out-of-sample validation. While earlier testing suggested potential benefits of adaptive percentile-based thresholds, comprehensive out-of-sample testing revealed that fixed thresholds perform significantly better.

## Configuration Evolution

### Step 1: Grid Search Results

The initial grid search identified optimal parameters:
- OBI Window: 8
- OBI Threshold: 0.15
- Spread Threshold: 0.000026
- Confirmation Bars: 1

### Step 2: Initial Adaptive Testing

Subsequent testing explored adaptive percentile-based thresholds with modified OBI parameters:
```yaml
# Core OBI parameters
microstructure:
  obi_smoothing_window: 10      # Increased from grid search value
  gms_obi_strong_confirm_thresh: 0.20  # Higher than grid search 0.15 value

# Percentile-based threshold settings  
gms_tight_spread_fallback_percentile: 0.25
gms_tight_spread_percentile_window: 24
gms_spread_mean_thresh_mode: 'percentile'  # Instead of 'fixed'
gms_spread_std_thresh_mode: 'percentile'   # Instead of 'fixed'
gms_vol_thresh_mode: 'percentile'          # Instead of 'fixed'
```

### Step 3: Out-of-Sample Validation (Conclusive)

Comprehensive out-of-sample testing on 2025 data (4 months) revealed that fixed thresholds significantly outperform percentile-based ones. The percentile-based approach actually degraded performance to the point of generating losses.

## Final Configuration (OOS Validated)

The final, out-of-sample validated configuration uses:
```yaml
# Core OBI parameters
microstructure:
  obi_smoothing_window: 8       # Consistent with grid search value
  gms_obi_strong_confirm_thresh: 0.20  # Higher threshold

# Fixed threshold settings
gms_tight_spread_fallback_percentile: null  # Disabled
gms_spread_mean_thresh_mode: 'fixed'        # Fixed thresholds
gms_spread_std_thresh_mode: 'fixed'         # Fixed thresholds
gms_vol_thresh_mode: 'fixed'                # Fixed thresholds
```

## Out-of-Sample Performance

This fixed threshold configuration delivered excellent out-of-sample performance on 2025 data:
- **Sharpe Ratio (Daily)**: 3.13
- **Profit Factor**: 1.79
- **Max Drawdown**: 5.47%
- **Return on Initial (ROI)**: 30.40%
- **Trade Count**: 68

## Why Fixed Thresholds Work Better

1. **Consistency**: Fixed thresholds provide more consistent behavior across different market conditions

2. **Simplicity**: The simpler approach avoids potential overfitting to recent market conditions

3. **Signal Clarity**: Fixed thresholds create clearer regime transition signals

4. **Empirical Validation**: The out-of-sample results conclusively demonstrate superior performance

## Implementation Considerations

1. The fixed threshold approach has the added benefit of being simpler to understand, implement, and maintain

2. This optimized configuration should be considered the primary GMS configuration based on rigorous empirical testing

3. When implementing this configuration, focus on proper OBI calculation and smoothing in SignalEngine

## Recommended Configuration

```yaml
regime:
  detector_type: 'granular_microstructure'
  gms_use_three_state_mapping: true
  
  # Volatility threshold mode and values
  gms_vol_thresh_mode: 'fixed'
  
  # OBI settings
  microstructure:
    obi_smoothing_window: 8
    gms_obi_strong_confirm_thresh: 0.20
    gms_obi_weak_confirm_thresh: 0.11
  
  # Fixed spread settings
  gms_tight_spread_fallback_percentile: null
  gms_spread_mean_thresh_mode: 'fixed'
  gms_spread_std_thresh_mode: 'fixed'
```
