# 3-State Market Bias Risk Adjustment Plan

## Overview
This document outlines the implementation plan for a more robust risk adjustment system based on the 3-state GMS (Granular Market Structure) market model. The goal is to optimize leveraged trading by applying different leverage factors based on whether the market is in a BULL, BEAR, or CHOP state.

## Current Limitations
The current system has two separate mechanisms:

1. **Dynamic Leverage (Volatility-based)**: Adjusts leverage based on market volatility
2. **Dynamic Risk Adjustment (Regime-based)**: Adjusts risk and leverage based on detailed market regimes

These systems, while functional, have overlapping responsibilities and use a complex 7-state regime model that may not be necessary for effective risk management.

## Proposed 3-State GMS Approach

### Core Concept
Simplify to a 3-state model (BULL, BEAR, CHOP) where each state has specific leverage and risk settings optimized for the typical behavior in those market conditions:

1. **BULL Market**: Apply higher leverage to capitalize on uptrends
2. **BEAR Market**: Apply lower leverage when shorting to mitigate risk
3. **CHOP Market**: Apply conservative leverage to account for unpredictability

### Implementation Details

#### 1. Configuration Settings
Add the following to `base.yaml`:

```yaml
# 3-State GMS Market Bias Settings
market_bias:
  enabled: true                   # Enable market bias risk adjustments
  use_three_state_mapping: true   # Use simplified 3-state market model

  # Market-specific leverage multipliers
  bull_leverage_factor: 1.5       # Increase leverage in bull markets
  bear_leverage_factor: 0.7       # Reduce leverage in bear markets
  chop_leverage_factor: 0.5       # Conservative leverage in choppy markets

  # Market-specific risk per trade multipliers (optional)
  bull_risk_factor: 1.2           # Increase risk per trade in bull markets
  bear_risk_factor: 0.8           # Reduce risk per trade in bear markets
  chop_risk_factor: 0.5           # Minimize risk in choppy markets

  # Long-Short bias (optional future enhancement)
  bull_long_bias: 1.0             # No adjustment for longs in bull market
  bull_short_bias: 0.5            # Reduce short size in bull markets
  bear_long_bias: 0.5             # Reduce long size in bear markets
  bear_short_bias: 1.0            # No adjustment for shorts in bear market
```

#### 2. Pydantic Model Updates
Update `settings.py` to include the new market bias settings:

```python
class MarketBiasSettings(BaseModel):
    enabled: bool = Field(default=False, description="Enable market bias risk adjustments")
    use_three_state_mapping: bool = Field(default=True, description="Use simplified 3-state market model")
    
    # Leverage factors
    bull_leverage_factor: float = Field(default=1.5, description="Leverage multiplier in bull markets")
    bear_leverage_factor: float = Field(default=0.7, description="Leverage multiplier in bear markets")
    chop_leverage_factor: float = Field(default=0.5, description="Leverage multiplier in choppy markets")
    
    # Risk factors
    bull_risk_factor: float = Field(default=1.2, description="Risk multiplier in bull markets")
    bear_risk_factor: float = Field(default=0.8, description="Risk multiplier in bear markets")
    chop_risk_factor: float = Field(default=0.5, description="Risk multiplier in choppy markets")
    
    # Optional long-short bias
    bull_long_bias: float = Field(default=1.0, description="Adjustment for longs in bull markets")
    bull_short_bias: float = Field(default=0.5, description="Adjustment for shorts in bull markets")
    bear_long_bias: float = Field(default=0.5, description="Adjustment for longs in bear markets")
    bear_short_bias: float = Field(default=1.0, description="Adjustment for shorts in bear markets")

class RegimeSettings(BaseModel):
    # Existing settings...
    market_bias: MarketBiasSettings = Field(default_factory=MarketBiasSettings, description="Market bias risk adjustment settings")
```

#### 3. GMS State Mapping
Ensure the GMS state mapping file properly maps the detailed states to the simplified 3-state model:

```yaml
# 3-State mapping (BULL, BEAR, CHOP)
BULL:
  - "Strong_Bull_Trend"
  - "Weak_Bull_Trend"
BEAR:
  - "Strong_Bear_Trend"
  - "Weak_Bear_Trend"
CHOP:
  - "Volatile_Chop"
  - "Low_Vol_Chop"
  - "Ranging"
  - "High_Vol_Range"
```

#### 4. Risk Manager Updates

Modify the `calculate_position` method in `risk.py` to implement the 3-state market bias system:

```python
def calculate_position(self, portfolio, signals, strategy_name, regime, direction, ...):
    # ...existing code...
    
    # Determine market state for 3-state model
    market_state = "CHOP"  # Default
    
    # If three-state mapping is enabled, map detailed regime to simplified state
    if hasattr(cfg.regime.market_bias, 'enabled') and cfg.regime.market_bias.enabled:
        if hasattr(cfg.regime.market_bias, 'use_three_state_mapping') and cfg.regime.market_bias.use_three_state_mapping:
            if regime in ["Strong_Bull_Trend", "Weak_Bull_Trend"]:
                market_state = "BULL"
            elif regime in ["Strong_Bear_Trend", "Weak_Bear_Trend"]:
                market_state = "BEAR"
            else:
                market_state = "CHOP"
        
        # Apply market bias-specific leverage factor
        if market_state == "BULL":
            leverage_factor = getattr(cfg.regime.market_bias, 'bull_leverage_factor', 1.5)
            risk_factor = getattr(cfg.regime.market_bias, 'bull_risk_factor', 1.2)
            
            # Optional: Apply long-short bias if direction is known
            if direction == "long":
                direction_bias = getattr(cfg.regime.market_bias, 'bull_long_bias', 1.0)
            elif direction == "short":
                direction_bias = getattr(cfg.regime.market_bias, 'bull_short_bias', 0.5)
            
        elif market_state == "BEAR":
            leverage_factor = getattr(cfg.regime.market_bias, 'bear_leverage_factor', 0.7)
            risk_factor = getattr(cfg.regime.market_bias, 'bear_risk_factor', 0.8)
            
            # Optional: Apply long-short bias if direction is known
            if direction == "long":
                direction_bias = getattr(cfg.regime.market_bias, 'bear_long_bias', 0.5)
            elif direction == "short":
                direction_bias = getattr(cfg.regime.market_bias, 'bear_short_bias', 1.0)
                
        else:  # CHOP
            leverage_factor = getattr(cfg.regime.market_bias, 'chop_leverage_factor', 0.5)
            risk_factor = getattr(cfg.regime.market_bias, 'chop_risk_factor', 0.5)
            direction_bias = 1.0  # No directional bias in chop
            
        # Apply the leverage factor to base leverage
        market_adjusted_leverage = base_leverage * leverage_factor
        
        # Apply direction bias if applicable (optional feature)
        if 'direction_bias' in locals():
            market_adjusted_leverage *= direction_bias
            
        # Log the adjustment
        self.logger.info(f"MARKET BIAS: State={market_state}, Base Leverage={base_leverage:.2f}x → Adjusted={market_adjusted_leverage:.2f}x")
        self.logger.info(f"MARKET BIAS: Risk Factor={risk_factor:.2f}, Direction Bias={direction_bias if 'direction_bias' in locals() else 1.0:.2f}")
        
        # Set final leverage (respect bounds)
        final_leverage = max(cfg.indicators.min_leverage, min(market_adjusted_leverage, cfg.core.max_leverage))
    
    # ...rest of the method...
```

#### 5. Validation Method
Add a validation method for the market bias settings:

```python
def _validate_market_bias(self, market_state, regime, base_leverage, final_leverage, risk_factor):
    """
    Validate that market bias settings are properly applied.
    
    Args:
        market_state: Simplified market state (BULL, BEAR, CHOP)
        regime: Detailed market regime
        base_leverage: Original base leverage
        final_leverage: Adjusted final leverage
        risk_factor: Applied risk factor
    """
    cfg = self.config
    
    self.logger.info("--------- MARKET BIAS VALIDATION ---------")
    self.logger.info(f"  • Detailed Regime: {regime}")
    self.logger.info(f"  • Simplified State: {market_state}")
    
    if market_state == "BULL":
        expected_lev_factor = getattr(cfg.regime.market_bias, 'bull_leverage_factor', 1.5)
        expected_risk_factor = getattr(cfg.regime.market_bias, 'bull_risk_factor', 1.2)
    elif market_state == "BEAR":
        expected_lev_factor = getattr(cfg.regime.market_bias, 'bear_leverage_factor', 0.7)
        expected_risk_factor = getattr(cfg.regime.market_bias, 'bear_risk_factor', 0.8)
    else:  # CHOP
        expected_lev_factor = getattr(cfg.regime.market_bias, 'chop_leverage_factor', 0.5)
        expected_risk_factor = getattr(cfg.regime.market_bias, 'chop_risk_factor', 0.5)
    
    expected_leverage = base_leverage * expected_lev_factor
    
    self.logger.info(f"  • Base Leverage: {base_leverage:.2f}x")
    self.logger.info(f"  • Expected Leverage Factor: {expected_lev_factor:.2f}")
    self.logger.info(f"  • Expected Adjusted Leverage: {expected_leverage:.2f}x")
    self.logger.info(f"  • Actual Final Leverage: {final_leverage:.2f}x")
    
    leverage_diff = abs(final_leverage - expected_leverage) / expected_leverage
    if leverage_diff < 0.05:  # Within 5% of expected
        self.logger.info(f"  • Leverage Application: CORRECT")
    else:
        self.logger.warning(f"  • Leverage Application: POSSIBLE ERROR")
    
    self.logger.info(f"  • Expected Risk Factor: {expected_risk_factor:.2f}")
    self.logger.info(f"  • Actual Risk Factor: {risk_factor:.2f}")
    
    if abs(risk_factor - expected_risk_factor) < 0.01:
        self.logger.info(f"  • Risk Factor Application: CORRECT")
    else:
        self.logger.warning(f"  • Risk Factor Application: POSSIBLE ERROR")
        
    self.logger.info("--------- END VALIDATION ---------")
```

#### 6. Logging Enhancements
Update the logging in `run_backtest.py` to show market bias settings:

```python
# Log market bias settings if enabled
if hasattr(config.regime, 'market_bias') and hasattr(config.regime.market_bias, 'enabled') and config.regime.market_bias.enabled:
    logger.info(f"CONFIG: Market Bias Enabled = {config.regime.market_bias.enabled}")
    logger.info(f"CONFIG: Using 3-State Mapping = {config.regime.market_bias.use_three_state_mapping}")
    logger.info(f"CONFIG: Bull Leverage Factor = {config.regime.market_bias.bull_leverage_factor}")
    logger.info(f"CONFIG: Bear Leverage Factor = {config.regime.market_bias.bear_leverage_factor}")
    logger.info(f"CONFIG: Chop Leverage Factor = {config.regime.market_bias.chop_leverage_factor}")
```

## Phase 2: Enhancements (Future)
After the basic 3-state model is implemented and tested, consider these enhancements:

1. **Direction-Specific Biases**: Further refine by having separate adjustments for:
   - Long trades in bull markets
   - Short trades in bull markets
   - Long trades in bear markets
   - Short trades in bear markets

2. **Time-Based Market Bias**: Adjust leverage based on historical performance during:
   - Different times of day
   - Different days of the week
   - Monthly seasonality

3. **Trend Strength Consideration**: Incorporate additional metrics to assess trend strength:
   - ADX (Average Directional Index)
   - Slope of EMAs
   - Volatility ratios

4. **Integration with ML Models**: Train machine learning models to predict optimal leverage:
   - Based on market conditions
   - Based on historical performance in similar conditions
   - Dynamically update during trading

## Testing Plan

1. **Backtest with Different Configurations**:
   - Test bull_leverage_factor values (1.2, 1.5, 2.0)
   - Test bear_leverage_factor values (0.5, 0.7, 1.0)
   - Test chop_leverage_factor values (0.3, 0.5, 0.7)

2. **Metrics to Monitor**:
   - Overall ROI
   - Drawdown
   - Sharpe/Sortino ratios
   - Win rate by market state
   - Average profit by market state

3. **Comparison Test**:
   - Current system vs. 3-state system
   - With/without directional bias
   - Various leverage factor combinations
