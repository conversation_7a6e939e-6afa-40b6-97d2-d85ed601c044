Here’s a self‑contained **Project‑Recovery Document (PRD)** you can lift straight into a fresh chat for the implementation agents.
It captures every known pain‑point, preserves critical file/column names, and breaks the work into bite‑size tasks with explicit “ASK IF UNSURE” notes so they stay aligned with the current codebase.

---

# **Project‑Recovery Document — TF‑v3 / Continuous GMS Back‑test**

*(rev 2025‑05‑24 · rescue plan for T‑111)*

---

## 0 Goal

> **By the end of this recovery sprint I, the human owner, can run:**
>
> ```bash
> # modern stack
> python run_backtest.py --strategies continuous_gms tf_v3  ...
> # legacy stack
> python run_backtest.py --strategies granular_microstructure tf_v2 ...
> ```
>
> …with **zero errors, ≥ 1 trade per strategy, no look‑ahead bias, and clean logs**.

---

## 1 Codebase Conventions (do **NOT** break)

| Area                   | Current conventions                                                                                                      |
| ---------------------- | ------------------------------------------------------------------------------------------------------------------------ |
| **Paths**              | `tools/etl_l20_to_1s.py`, feature root `/ext_ssd/hyperliquid/features_1s/YYYY‑MM‑DD/*.parquet`                           |
| **1‑s feature schema** | Required cols: `timestamp`, `mid_price`, `atr_14_sec`, `atr_percent_sec`, legacy `atr`, `atr_percent`, depth/OBI columns |
| **OHLCV 1 h**          | Parquet under `/ext_ssd/hyperliquid/ohlcv_1h/*.parquet`                                                                  |
| **Strategy registry**  | `hyperliquid_bot/registry/strategy_factory.py` – keys must match YAML exactly                                            |
| **Config file**        | single source of truth `configs/base.yaml`; remove ad‑hoc override files                                                 |
| **Timezone**           | **UTC everywhere** (`tzinfo=None` pandas naive)                                                                          |

If you are unsure about **exact column names or paths, ASK FIRST** – do *not* invent new ones.

---

## 2 High‑Level Issues to Fix

| ID | Symptom                                                              | Root cause (suspected)                              | Priority |
| -- | -------------------------------------------------------------------- | --------------------------------------------------- | -------- |
| A  | Continuous GMS raises “Missing atr” or NaN, then TF‑v3 gates forever | ATR not present / NaN in 1‑s features after warm‑up | High     |
| B  | Timezone error “tz‑naive and tz‑aware”                               | Mixed datetime types in detector or ETL             | High     |
| C  | Strategy factory can’t build `"continuous_gms"`                      | Registry key mismatch                               | High     |
| D  | Duplicate / conflicting back‑test flags                              | Incorrect `config.backtest` detection               | Critical |
| E  | Hard‑coded fallbacks (2 % ATR, 0.001 BTC size)                       | Quick patches; must move to config or remove        | High     |
| F  | Duplicate `_validate_market_bias` method in `risk.py`                | Same function defined twice (lines 417‑715)         | Critical |
| G  | Back‑test mode always “True”                                         | `config.backtest` is an object, not bool            | Critical |
| H  | Module‑level `logging.basicConfig()` overrides global logging        | Depth metrics calculator sets logging at import     | Critical |

---

## 3 Task Breakdown

| Task       | Owner          | Description                                                                                                                                                                                                                                                                                                              | Blocking  |
| ---------- | -------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | --------- |
| **R‑101**  | ETL dev        | **Inject ATR into 1‑s features correctly**  <br>• Resample inside ETL to 1 h OHLC → ATR(14) → ffill down to seconds  <br>• Assert `atr_14_sec` NaNs < 14 rows per day                                                                                                                                                    | —         |
| **R‑102a** | Core utils dev | **UTC helper + ETL assertions** – add `to_utc_naive()` util, refactor ETL write path to assert tz‑naive timestamps, no runtime patches yet                                                                                                                                                                               | R‑101     |
| **R‑102b** | Backend dev    | **Runtime tz purge** – replace ad‑hoc `.tz_localize/convert` in portfolio, strategy, gms\_provider, data handler with the new util; add unit tests & docs                                                                                                                                                                | R‑102a    |
| **R‑102c** | Data ops       | **One‑off data migration** – run `scripts/fix_timezone_data.py` to convert all historical hourly OHLCV parquet to UTC‑naïve, verify backups; decide on raw Arrow conversion vs on‑read fix                                                                                                                               | R‑102b    |
| **R‑103**  | Backend dev    | Trim strategy registry to only: `"tf_v2"`, `"tf_v3"`, `"granular_microstructure"`, `"continuous_gms"` – **no override YAML**                                                                                                                                                                                             | —         |
| **R‑104a** | Config dev     | **Schema update** – add top‑level `portfolio:` block and global `is_backtest: bool` flag in `configs/base.yaml`; extend pydantic settings model; no code refactor yet                                                                                                                                                    | R‑103     |
| **R‑104b** | Backend dev    | **Code refactor** – replace all `hasattr(cfg, "backtest")` checks with `cfg.is_backtest`; update strategy/risk modules & tests                                                                                                                                                                                           | R‑104a    |
| **R‑105a** | Config dev     | **Add fallback keys** – insert `tf_v3.atr_fallback_pct` (default 0.01) and `portfolio.min_trade_size` (default 0.001) into base.yaml & pydantic schema; no code changes yet                                                                                                                                              | R‑104b    |
| **R‑105b** | Core dev       | **Remove hard‑codes** – replace 1 % ATR fallback & 0.001 BTC size with config look‑ups; update tests                                                                                                                                                                                                                     | R‑105a    |
| **R‑105c** | Strategy dev   | **TF‑v3 epsilon config** – expose `strategies.tf_v3.trail_eps` (default inherits tick\_size) and remove 0.01 literals                                                                                                                                                                                                    | R‑105b    |
| **R‑105d** | Strategy dev   | **OBI scalper tick size config** – add `strategies.obi_scalper.tick_size` per asset; delete 0.01 literals                                                                                                                                                                                                                | R‑105b    |
| **R‑105e** | Risk dev       | **Margin buffer config** – move 5 % buffer to `portfolio.margin_buffer_pct` (default 5)                                                                                                                                                                                                                                  | R‑105b    |
| **R‑106**  | Detector dev   | Continuous GMS:  <br>• Read `atr_14_sec` by default, fall back to legacy `atr_percent`  <br>• Soft‑skip if ATR NaN; no hard error  <br>• Log snapshot age once per hour                                                                                                                                                  | R‑101     |
| **R‑107**  | QA             | **Smoke back‑test 2025‑03‑03**   <br>• strategies: `continuous_gms tf_v3`  <br>• Expect `trade_count ≥ 1`, no exceptions                                                                                                                                                                                                 | R‑101…106 |
| **R‑108a** | QA             | **Legacy regression run** – full‑year 2024 back‑test (`granular_microstructure` + `tf_v2`) **with 3‑state collapse** (BULL/BEAR/CHOP via `configs/gms_state_mapping.yaml`). Use `detector_type: granular_microstructure`, `output_states: 3`; prefer strategy name `tf_v2` explicitly. Capture metrics JSON & log footer | R‑107     |
| **R‑108b** | QA / Core dev  | **Investigate deviations** – if Sharpe ±10 % or trade count differs >10 from baseline, flag root‑cause (config.core drift or logic change) and raise fix task                                                                                                                                                            | R‑108a    |
| **R‑109**  | QA             | Document back‑test procedure in `docs/backtest_runbook.md`                                                                                                                                                                                                                                                               | R‑107,108 |
| **R‑110a** | Core dev       | **Scan config.core drift** – grep repo for `config.core` & `cfg.core`; produce report of occurrences categorized by module/test; no code changes                                                                                                                                                                         | —         |
| **R‑110b** | Core dev       | **Fix config.core drift** – patch each occurrence to the correct section (`portfolio`, `signals`, etc.); update tests & docs                                                                                                                                                                                             | R‑110a    |
| **R‑112**  | QA             | **Environment validation** – manual run on owner’s machine for `continuous_gms + tf_v3`; verify regime mapping looks reasonable and TF‑v3 executes trades; capture log & metrics                                                                                                                                         | R‑108a    |
| **R‑113**  | Core + QA      | **Regime mapping sanity** – inspect 3‑state vs 8‑state outputs on sample day; ensure mapping file used; fix if inconsistent                                                                                                                                                                                              | R‑112     |
| **R‑113**  | QA / Strat dev | **Regime mapping diagnostics** – ensure `continuous_gms` 8→4/3‑state collapse works; visual log sample, raise issues if mismatch                                                                                                                                                                                         | R‑112     |

### R‑110a – Config‑core scan **COMPLETED**

* 28 hits identified (1 runtime, 14 tests, 13 low‑priority).
* CSV + markdown summary artifacts saved; xfail guard tests added.

### R‑110b – Config‑core fix **COMPLETED**

* Removed all 28 legacy `config.core` references (runtime, tests, backups).
* Grep check clean; audit tests switched from xfail to pass.
* Schema now fully aligned; branch ready to merge.

### R‑112a – Diagnostics **COMPLETED**

* State collapse disabled for `continuous_gms`; detector emits only `Low_Vol_Range`, `Unknown`.
* Mapping YAML missing `Unknown` entry.
* Evaluator restricts collapse to `granular_microstructure`.
* TF‑v3 never activates because `active_names` empty.

### R‑112c – Diagnostics CHOP issue **COMPLETED**

* Found volatility thresholds mis‑configured by 100× in YAML (0.55/0.92 instead of 0.02/0.06).
* Momentum thresholds also too high (50/100 vs 1/5).

\| **R‑112d** | Config dev | **Threshold fix (first pass)** – adjusted vol 0.02/0.06, mom 1/5 | R‑112c |
\| **R‑112e** | Config dev | **Threshold retune #1** – vol 0.01/0.03, mom 0.5/2.5 | R‑112d |
\| **R‑112f** | Config dev | **Threshold retune #2** – mom 0.3/1.5; blocked by missing spread signals | R‑112e |
\| **R‑112g** | ETL dev      | **Spread feature bug‑hunt** – investigate NaN / missing `spread_mean`, `spread_std` in features\_1s; ensure columns populated for 2025‑03 span | R‑112f |

\| **R‑112h** | Core dev | **OBI depth param bug** – replace hard‑coded `raw_obi_20` references with dynamic column based on `cfg.gms.depth_levels` (currently 5); update loader & detector; unit test depth‑agnostic | R‑112g |
\| **R‑112i** | ETL dev | **Spread NaN hardening (causal)** – forward‑fill bid/ask, recompute 60‑sec rolling spread stats with `min_periods=1`; add no‑look‑ahead unit test; ensure NaN ratio < 1 %; legacy path untouched | R‑112h |

### R‑112i – Spread NaN hardening **COMPLETED**

* Forward‑fill bid/ask; rolling window causal min\_periods=1. NaN ratio <0.01.
* Detector now outputs collapsed states but March span still 99 % CHOP; no trades.

\| **R‑112j** | Quant analyst | **Detector behaviour audit** – compare state distribution of `continuous_gms` vs `granular_microstructure` over identical 2025‑03 span; plot distributions; identify threshold/momentum mis‑alignments; propose parameter tweaks to yield balanced regimes BULL/BEAR/CHOP (\~≤ 60 % CHOP) without harming legacy path | R‑112i |
\| **R‑112m** | Core dev | **Adaptive priming hook** – feed previous 24 h of 1‑s features into AdaptiveThreshold buffers at detector init; `priming_hours` config key; keeps pipeline causal | R‑112k |
\| **R‑112n** | Arch / Data | **Feature‑schema audit** – enumerate every column the detectors & strategies reference; compare against actual 1‑s parquet schema; output canonical list + mapping table | R‑112m |
\| **R‑112o** | ETL dev      | **Schema enforcement** – update ETL to generate missing columns (e.g. `ma_slope`, single ATR naming); add validator that asserts parquet schema matches canonical list | R‑112n |
\| **R‑112p** | Core dev     | **Detector‑schema contract test** – at runtime, detector asserts required\_cols ⊆ df.columns; unit tests for continuous\_gms & granular | R‑112o |
