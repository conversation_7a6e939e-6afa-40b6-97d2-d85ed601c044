# OBI Filters in Trading Bot

## Overview

Order Book Imbalance (OBI) filters provide a microstructure-based confirmation mechanism for trading strategies. These filters help validate trade entries by ensuring they align with the current order book dynamics.

## Implementation

The trading bot supports dynamic multi-depth OBI processing with the following features:

1. **Configurable Depth Variants**: OBI can be calculated at different depth levels (e.g., 1, 3, 5, 10) as specified in `config.microstructure.obi_levels`.

2. **Signal Naming Convention**: 
   - `obi_smoothed_{depth}`: Smoothed OBI values for specified depth
   - `obi_zscore_{depth}`: Z-score normalized OBI values (if enabled)

3. **Trend Following Strategy Filter**:
   - When `config.strategies.tf_use_obi_filter` is enabled, the Trend Following strategy will:
     - Block long entries when OBI is below the `tf_filter_obi_threshold_long` threshold
     - Block short entries when OBI is above the `tf_filter_obi_threshold_short` threshold

4. **Fallback Mechanism**:
   - If the dynamic OBI signal (`obi_smoothed_{depth}`) is not available, the system falls back to the legacy `obi_smoothed` signal
   - Appropriate warnings are logged when fallback is used

## Configuration

```yaml
strategies:
  tf_use_obi_filter: false  # Default: disabled

microstructure:
  obi_levels: 5             # Depth levels for OBI calculation
  obi_smoothing_window: 8   # Window for OBI smoothing
  obi_smoothing_type: 'sma' # 'sma' or 'ema'
  obi_zscore_window: null   # Window for z-score (null = disabled)
  
  # Trend Following filter thresholds
  tf_filter_obi_threshold_long: 0.1
  tf_filter_obi_threshold_short: -0.1
```

## Usage Notes

- OBI filters are particularly effective in volatile or choppy markets
- Positive OBI values indicate buying pressure (more bids than asks)
- Negative OBI values indicate selling pressure (more asks than bids)
- Z-score normalization helps identify statistically significant imbalances
