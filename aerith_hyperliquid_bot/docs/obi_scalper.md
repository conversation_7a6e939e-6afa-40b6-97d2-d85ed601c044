# OBI Scalper Strategy

## Overview

The OBI Scalper strategy is designed to identify and capitalize on short-term order book imbalance opportunities in cryptocurrency markets. It analyzes the order book depth to detect significant buy/sell imbalances that may indicate price movements.

## Strategy Components

### Class Structure

- **OBIScalperStrategy**: Implements `StrategyInterface` with specialized OBI-focused behavior
- **OBIScalperConfig**: Dataclass for strategy-specific configuration parameters (placeholder for future parameters)

### Configuration Flags

The following flags control the OBI Scalper's activation:

| Flag | Default | Description |
|------|---------|-------------|
| `strategies.use_obi_scalper` | `false` | Master switch for the OBI Scalper strategy |
| `strategies.obi_scalper_active_in_all_regimes` | `false` | If `true`, OBI Scalper runs in all market regimes |
| `strategies.gms_activate_obi_scalper_in_chop` | `false` | If `true`, OBI Scalper runs specifically in CHOP regime with GMS detector |

### Required Signals

The strategy relies on the following market signals:

- `close`: Current price
- `regime`: Current market regime (e.g., "CHOP", "Strong_Bull_Trend")
- `obi_smoothed_{levels}`: Smoothed Order Book Imbalance using configured depth levels
- `obi_zscore_{levels}`: Z-score of OBI for extreme value detection
- `spread_relative`: Relative bid-ask spread percentage

### Diagnostic Counters

The strategy tracks various diagnostic counters:

- `eval_count`: Total evaluation attempts
- `fail_missing_signal`: Count of evaluations skipped due to missing signals
- `fail_condition`: Count of base condition failures in the strategy logic
- `fail_obi_threshold`: Count of OBI threshold filter failures
- `fail_spread_filter`: Count of spread filter failures (spread too wide)
- `fail_zscore_filter`: Count of z-score filter failures (OBI too extreme)
- `fail_regime_filter`: Count of inappropriate market regime failures
- `success_entry_long`: Count of successful long entry signals
- `success_entry_short`: Count of successful short entry signals

## Market Regime Integration

The OBI Scalper has been designed to work in specific market regimes:

1. **CHOP Regime**: When markets are choppy/ranging, OBI signals can provide valuable short-term reversal opportunities. The `gms_activate_obi_scalper_in_chop` flag enables the strategy in CHOP regime when using the Granular Microstructure (GMS) detector.

2. **All Regimes**: For testing or specific use cases, the strategy can be enabled in all market regimes by setting `obi_scalper_active_in_all_regimes` to `true`.

## Current Implementation Status

This is a skeleton implementation that will be enhanced in future development stages. Currently:

- Basic infrastructure is in place with diagnostic counters
- No active entry logic is implemented (always returns `None`)
- Test stub implementation confirms the structure works correctly
- Ready for integration with the backtester and signal calculation pipeline

## Future Enhancements

Planned enhancements for future development:

1. Entry signal logic based on OBI thresholds and confirmation patterns
2. Z-score normalization and adaptive threshold calibration
3. Spread-based filtering for high liquidity conditions
4. Integration with other signals (funding rates, momentum) for confirmation
5. Custom position sizing based on OBI strength
6. Specialized exit logic based on OBI reversal patterns
