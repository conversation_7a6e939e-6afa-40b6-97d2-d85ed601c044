# ATR Consolidation Strategy - Post Bug Fix

## Recommended Approach: Standardize on `atr_percent_sec`

### Phase 1: Fix the Bug (Immediate)
```python
# calculator.py Line 1325 - REMOVE * 100
signals_df["atr_percent"] = signals_df["atr_percent_sec"]  # Direct copy

# calculator.py Lines 1352-1355 - REMOVE * 100  
signals_df["atr_percent"] = (
    signals_df[atr_col_for_perc] / signals_df["close"].replace(0, np.nan)
)  # Decimal values, not percentage
```

### Phase 2: Gradual Migration (Medium Term)

#### Step 1: Update GMS Detector (No Breaking Changes)
```python
# gms_detector.py - Keep existing priority but add deprecation warning
atr_pct = signals.get(self.ATR_PCT_COL, np.nan)  # ATR_PCT_COL = 'atr_percent_sec'
if pd.isna(atr_pct):
    atr_pct = signals.get('atr_percent', np.nan)
    self.logger.warning("Using deprecated 'atr_percent' column. Consider using 'atr_percent_sec'.")
```

#### Step 2: Update Signal Calculator (Backward Compatible)
```python
# calculator.py - Always ensure atr_percent_sec exists
if "atr_percent_sec" not in signals_df.columns:
    # Create atr_percent_sec from atr_percent if needed
    if "atr_percent" in signals_df.columns:
        signals_df["atr_percent_sec"] = signals_df["atr_percent"]
        self.logger.info("Created 'atr_percent_sec' from existing 'atr_percent'")

# Always create atr_percent as alias for backward compatibility
signals_df["atr_percent"] = signals_df["atr_percent_sec"]
```

### Phase 3: Full Standardization (Long Term)

#### Option A: Keep Both Columns (Recommended)
- **Primary**: `atr_percent_sec` (correct units, from feature files)
- **Alias**: `atr_percent` (backward compatibility)
- **Benefits**: No breaking changes, clear semantics

#### Option B: Deprecate atr_percent (Advanced)
- Remove `atr_percent` column creation
- Update all references to use `atr_percent_sec`
- Add migration warnings for old configurations

### Backward Compatibility Considerations

#### Configuration Files
```yaml
# Old configs continue to work
indicators:
  gms_atr_percent_period: 14  # Still valid

# New configs can be more explicit
indicators:
  atr_period: 14              # Generic ATR period
  atr_percent_column: "atr_percent_sec"  # Explicit column choice
```

#### Data Files
- **Feature files**: Continue using `atr_percent_sec` as primary
- **Legacy files**: May only have `atr_percent` - handled by fallback logic
- **Migration**: Automatic through signal calculator logic

### Implementation Timeline

#### Week 1: Bug Fix
- Remove `* 100` multiplications
- Add runtime validation
- Test with existing data

#### Week 2-3: Gradual Migration
- Update GMS detector with deprecation warnings
- Ensure both columns exist in signal calculator
- Update documentation

#### Month 2+: Full Standardization
- Choose Option A or B based on testing results
- Update all configuration examples
- Remove deprecated code paths (if Option B)

### Risk Mitigation

#### Runtime Validation
```python
def validate_atr_consistency(signals_df):
    """Validate ATR column consistency after bug fix."""
    if 'atr_percent' in signals_df.columns and 'atr_percent_sec' in signals_df.columns:
        atr_pct = signals_df['atr_percent'].dropna()
        atr_pct_sec = signals_df['atr_percent_sec'].dropna()
        
        if len(atr_pct) > 0 and len(atr_pct_sec) > 0:
            ratio = atr_pct.mean() / atr_pct_sec.mean()
            if abs(ratio - 1.0) > 0.1:  # Allow 10% tolerance
                raise ValueError(f"ATR column inconsistency detected! Ratio: {ratio:.2f}x")
```

#### Fallback Logic
```python
def get_atr_percent_safe(signals):
    """Safely get ATR percent with fallback logic."""
    # Priority 1: atr_percent_sec (correct units)
    atr_pct = signals.get('atr_percent_sec', np.nan)
    
    # Priority 2: atr_percent (potential legacy)
    if pd.isna(atr_pct):
        atr_pct = signals.get('atr_percent', np.nan)
        
    # Priority 3: Calculate from raw ATR
    if pd.isna(atr_pct) and 'atr' in signals and 'close' in signals:
        atr_pct = signals['atr'] / signals['close']
        
    return atr_pct
```

### Testing Strategy

#### Unit Tests
```python
def test_atr_consolidation():
    """Test ATR consolidation after bug fix."""
    # Test 1: Both columns should have same values
    assert np.allclose(df['atr_percent'], df['atr_percent_sec'], rtol=0.01)
    
    # Test 2: Values should be in reasonable range
    assert df['atr_percent_sec'].max() < 0.1  # Less than 10%
    assert df['atr_percent_sec'].min() > 0.0001  # Greater than 0.01%
    
    # Test 3: GMS detector should work with either column
    detector = ContinuousGMSDetector(config)
    regime1 = detector.get_regime({'atr_percent_sec': 0.005})
    regime2 = detector.get_regime({'atr_percent': 0.005})
    assert regime1 == regime2
```

#### Integration Tests
```python
def test_backtest_consistency():
    """Test that backtests produce same results with consolidated ATR."""
    # Run backtest with old data (both columns)
    results_old = run_backtest(old_data)
    
    # Run backtest with new data (consolidated)
    results_new = run_backtest(new_data)
    
    # Results should be identical
    assert abs(results_old.sharpe - results_new.sharpe) < 0.01
```

## Conclusion

**Recommended Path**: Phase 1 → Phase 2 → Phase 3 Option A

This approach:
- ✅ Fixes the immediate bug
- ✅ Maintains backward compatibility  
- ✅ Provides clear migration path
- ✅ Minimizes risk of breaking changes
- ✅ Allows gradual adoption of best practices
