# Balance Calculation Discrepancy Investigation Plan

## Current Issues Identified

Based on the debugging output, we have identified several critical issues in the balance calculation system:

```
[INFO   ] Backtester          : BALANCE_DEBUG: Initial balance=$10000.00, Current balance=$433.43
[INFO   ] Backtester          : BALANCE_DEBUG: Reserved margin=$41699.39
[INFO   ] Backtester          : BALANCE_DEBUG: Total trades=157
[INFO   ] Backtester          : BALANCE_DEBUG: Sum of all trade PnLs=$34498.12
[INFO   ] Backtester          : BALANCE_DEBUG: Total funding PnL=$-192.15
[INFO   ] Backtester          : BALANCE_DEBUG: Trade PnL breakdown - Price diff=$0.00, Fees=$4315.31, Slippage=$-2319.44, Funding=$-197.67
[INFO   ] Backtester          : BALANCE_DEBUG: Sum of funding PnL in trades=$-197.67 vs. Portfolio total=$-192.15
[WARNING] Backtester          : BALANCE_DEBUG: FUNDING PNL DISCREPANCY! Trade funding=$-197.67 vs Portfolio total=$-192.15
[WARNING] Backtester          : BALANCE_DEBUG: Difference=$5.52
[INFO   ] Backtester          : BALANCE_DEBUG: Expected balance=$44305.97 (initial + trade PnLs + funding PnL)
[INFO   ] Backtester          : BALANCE_DEBUG: Total account value=$42132.82 (balance + reserved margin)
[WARNING] Backtester          : BALANCE_DEBUG: DISCREPANCY DETECTED! Account value=$42132.82 vs Expected=$44305.97
```

Key issues:
1. **Major Balance Discrepancy**: ~$2,173.15 difference between expected balance ($44,305.97) and actual account value ($42,132.82)
2. **Funding PnL Discrepancy**: $5.52 difference between trade funding PnL and portfolio total funding PnL
3. **Extremely Low Current Balance**: $433.43 despite having $41,699.39 in reserved margin
4. **Missing Price Diff PnL**: Price diff PnL is showing as $0.00 in the breakdown
5. **Negative Calmar Ratio**: Indicating issues with drawdown or return calculations

## Investigation Plan

### Phase 1: Balance Accounting System Audit

#### 1.1 Trace Reserved Margin Flow
- [ ] Audit `handle_entry` method to verify correct margin reservation
- [ ] Audit `handle_exit` method to verify correct margin release
- [ ] Add transaction logging for all margin operations
- [ ] Track cumulative reserved margin across all trades
- [ ] Verify that reserved margin is properly accounted for in multi-position scenarios

#### 1.2 Verify Balance Calculation Components
- [ ] Trace initial balance through all transactions
- [ ] Verify that all trade PnLs are correctly calculated and applied
- [ ] Verify that all funding payments are correctly calculated and applied
- [ ] Verify that all fees are correctly calculated and applied
- [ ] Verify that all slippage is correctly calculated and applied
- [ ] Create a detailed balance ledger showing every transaction

#### 1.3 Fix Price Diff PnL Tracking
- [ ] Add price_diff_pnl to trade records
- [ ] Ensure price_diff_pnl is properly calculated and stored
- [ ] Fix the breakdown calculation to correctly extract price_diff_pnl from trades

### Phase 2: Funding PnL Reconciliation

#### 2.1 Fix Funding PnL Tracking
- [ ] Audit the `apply_funding` method for correctness
- [ ] Verify that funding PnL is correctly accumulated in the position
- [ ] Verify that funding PnL is correctly transferred to trade records on exit
- [ ] Add detailed logging for each funding event
- [ ] Create a funding PnL ledger for reconciliation

#### 2.2 Fix Funding PnL Discrepancy
- [ ] Identify the source of the $5.52 discrepancy between trade and portfolio funding PnL
- [ ] Fix rounding issues in funding PnL calculations
- [ ] Ensure consistent handling of funding PnL across all methods

### Phase 3: Balance Reconciliation System

#### 3.1 Implement Balance Reconciliation
- [ ] Create a balance reconciliation system that runs at the end of each backtest
- [ ] Track all balance-affecting operations (entries, exits, funding, fees, slippage)
- [ ] Identify and report any discrepancies in real-time
- [ ] Add assertions to catch balance inconsistencies early

#### 3.2 Fix get_free_balance Method
- [ ] Ensure `get_free_balance` correctly subtracts reserved margin from balance
- [ ] Verify that free balance is never negative
- [ ] Add validation to prevent invalid balance states

### Phase 4: Performance Metrics Fixes

#### 4.1 Fix Calmar Ratio Calculation
- [ ] Audit the drawdown calculation logic
- [ ] Verify that the maximum drawdown is correctly calculated
- [ ] Ensure annualized return is correctly calculated
- [ ] Fix the Calmar ratio calculation to use the correct values

#### 4.2 Verify Other Performance Metrics
- [ ] Audit Sharpe ratio calculation
- [ ] Audit Sortino ratio calculation
- [ ] Audit Omega ratio calculation
- [ ] Ensure all metrics use the correct balance/equity values

## Implementation Approach

### Step 1: Add Comprehensive Logging
Add detailed logging for all balance-affecting operations to create a complete audit trail:

```python
# Example: Enhanced logging for handle_exit
def handle_exit(self, ...):
    # Log before state
    self.logger.debug(f"EXIT_BEGIN: Balance=${self.balance:.4f}, Reserved=${self.reserved_margin:.4f}, Total=${(self.balance + self.reserved_margin):.4f}")
    
    # Existing code...
    
    # Log after state
    self.logger.debug(f"EXIT_END: Balance=${self.balance:.4f}, Reserved=${self.reserved_margin:.4f}, Total=${(self.balance + self.reserved_margin):.4f}, Delta=${(self.balance + self.reserved_margin - initial_total):.4f}")
```

### Step 2: Implement Balance Ledger
Create a transaction ledger system to track all balance changes:

```python
# Add to Portfolio class
self.balance_ledger = []

# Add transaction recording
def record_transaction(self, transaction_type, amount, details=None):
    """Records a balance-affecting transaction for auditing."""
    transaction = {
        "timestamp": time.time(),
        "type": transaction_type,  # entry, exit, funding, fee, slippage
        "amount": amount,
        "balance_before": self.balance,
        "balance_after": self.balance + amount,
        "reserved_margin": self.reserved_margin,
        "details": details or {}
    }
    self.balance_ledger.append(transaction)
    return transaction
```

### Step 3: Fix Funding PnL Tracking
Ensure funding PnL is correctly tracked and included in trade records:

```python
# In apply_funding method
funding_transaction = self.record_transaction("funding", funding_pnl, {
    "rate": funding_rate,
    "position_value": position_value,
    "position_type": self.position['type']
})
self.position["funding_transactions"] = self.position.get("funding_transactions", []) + [funding_transaction]

# In handle_exit method
# Ensure all funding transactions are properly accounted for
funding_transactions = self.position.get("funding_transactions", [])
total_funding_pnl = sum(t["amount"] for t in funding_transactions)
# Verify this matches cum_funding_pnl
if abs(total_funding_pnl - self.position.get("cum_funding_pnl", 0)) > 0.01:
    self.logger.warning(f"FUNDING PNL MISMATCH: Transactions total=${total_funding_pnl:.4f} vs Position cum=${self.position.get('cum_funding_pnl', 0):.4f}")
```

### Step 4: Implement Balance Reconciliation
Add a reconciliation system to verify balance correctness:

```python
def reconcile_balance(self):
    """Reconciles the current balance with the transaction ledger."""
    expected_balance = self.initial_balance
    for transaction in self.balance_ledger:
        expected_balance += transaction["amount"]
    
    # Check for discrepancy
    discrepancy = self.balance - expected_balance
    if abs(discrepancy) > 0.01:
        self.logger.error(f"BALANCE RECONCILIATION FAILED: Current=${self.balance:.4f}, Expected=${expected_balance:.4f}, Discrepancy=${discrepancy:.4f}")
        return False
    
    self.logger.info(f"BALANCE RECONCILIATION PASSED: Balance=${self.balance:.4f}")
    return True
```

## Testing Plan

1. **Unit Tests**: Create unit tests for each balance-affecting method
2. **Integration Tests**: Test the entire balance system with various scenarios
3. **Regression Tests**: Verify that fixes don't break existing functionality
4. **Edge Case Tests**: Test extreme scenarios (high leverage, long positions, etc.)

## Expected Outcomes

1. **Accurate Balance Reporting**: No discrepancy between expected and actual account value
2. **Consistent Funding PnL**: Trade funding PnL matches portfolio total funding PnL
3. **Correct Free Balance**: Free balance correctly reflects available funds
4. **Accurate Performance Metrics**: All metrics (including Calmar ratio) are correctly calculated
5. **Complete Audit Trail**: All balance changes are tracked and verifiable

## Timeline

1. **Phase 1 (Balance Audit)**: 1-2 days
2. **Phase 2 (Funding PnL)**: 1 day
3. **Phase 3 (Reconciliation)**: 1 day
4. **Phase 4 (Metrics)**: 1 day
5. **Testing**: 1-2 days

Total estimated time: 5-7 days

## Conclusion

This investigation plan provides a comprehensive approach to identifying and fixing the balance calculation discrepancies in the trading bot. By systematically auditing each component of the balance system and implementing proper tracking and reconciliation mechanisms, we can ensure accurate financial reporting and performance metrics.

Once these issues are resolved, we can proceed with implementing the configurable leverage adjustment system as outlined in the previous plan.
