# Feature Catalog

This document catalogs the canonical 1-second feature columns required by the trading system.

**All timestamps are stored as UTC-naïve.**

## Canonical Features

### ATR Features

- **atr**: Legacy ATR column (maps to atr_14_sec)
  - Alternate names: atr_14
- **atr_14_sec**: Average True Range calculated from hourly OHLC data (14-period)
- **atr_mr**: ATR for mean reversion strategy
- **atr_mv**: ATR for mean variance strategy
- **atr_percent**: Legacy ATR percentage (maps to atr_percent_sec)
- **atr_percent_sec**: ATR from hourly data as percentage of mid_price
- **atr_tf**: ATR for trend following strategy

### Core Features

- **best_ask**: Best ask price
- **best_bid**: Best bid price
- **close**: Close price (set to mid_price for continuity)
- **high**: High price (set to close for 1-second data)
- **low**: Low price (set to close for 1-second data)
- **mid_price**: Mid price ((best_bid + best_ask) / 2)
- **spread**: Absolute spread (best_ask - best_bid)
- **timestamp**: Timestamp of the observation (naive UTC)
- **volume**: Trading volume

### Momentum Features

- **ma_slope**: Moving average slope (momentum indicator)

### OBI Features

- **obi_smoothed**: Legacy smoothed OBI (maps to obi_smoothed_5)
  - Alternate names: obi_smoothed
- **obi_smoothed_20**: Smoothed Order Book Imbalance (20 levels)
- **obi_smoothed_5**: Smoothed Order Book Imbalance (5 levels)
- **obi_zscore_20**: Z-score normalized OBI (20 levels)
- **obi_zscore_5**: Z-score normalized OBI (5 levels)
- **raw_obi_20**: Raw Order Book Imbalance using 20 levels
- **raw_obi_5**: Raw Order Book Imbalance using 5 levels
- **raw_obi_L1_10**: Raw OBI for levels 1-10 (OBI Scalper specific)
- **raw_obi_L1_3**: Raw OBI for levels 1-3 (OBI Scalper specific)

### Spread Features

- **spread_mean**: Rolling mean of spread (60-second window)
- **spread_std**: Rolling standard deviation of spread (60-second window)

### Volatility Features

- **realised_vol_1s**: Realized volatility at 1-second level
- **unrealised_pnl**: Placeholder for unrealized P&L (set to 0.0)

## Feature Usage by Component

### Continuous GMS Detector
**Primary Features:**
- `atr_percent_sec` - Volatility measurement
- `ma_slope` - Momentum indicator  
- `obi_smoothed_{depth}` - Order book imbalance (depth-aware)
- `spread_mean` - Spread context (range)
- `spread_std` - Spread context (chop)

**Optional Features:**
- `atr_percent_pctile` - Percentile-based volatility
- `obi_zscore_{depth}` - Z-score normalized OBI

### Granular Microstructure Detector
**Primary Features:**
- `atr_percent` - Volatility measurement (legacy)
- `ma_slope` - Momentum indicator
- `obi_smoothed_{depth}` - Order book imbalance
- `spread_mean` - Spread context
- `spread_std` - Spread context

### TF-v3 Strategy
**Primary Features:**
- `atr_14_sec` - ATR for stop/target calculations
- `close` - Price reference
- `atr_tf` - Strategy-specific ATR

### TF-v2 Strategy  
**Primary Features:**
- `atr_tf` - Trend following ATR
- `close` - Price reference

### OBI Scalper Strategy
**Primary Features:**
- `raw_obi_L1_3` - Raw OBI for levels 1-3
- `raw_obi_L1_10` - Raw OBI for levels 1-10
- `obi_smoothed_{levels}` - Smoothed OBI (configurable depth)
- `obi_zscore_{levels}` - Z-score OBI for filtering extremes

### Risk Management
**Primary Features:**
- `atr_14_sec` - Default ATR for position sizing
- `atr_tf` - Trend following specific ATR
- `atr_mr` - Mean reversion specific ATR
- `atr_mv` - Mean variance specific ATR

## Schema Requirements

### Mandatory Core Schema
All parquet files must include these columns:
```
timestamp, mid_price, close, high, low, best_bid, best_ask, spread, volume
```

### Mandatory ATR Schema
```
atr_14_sec, atr_percent_sec, atr, atr_percent, atr_tf, atr_mr, atr_mv
```

### Mandatory OBI Schema (Depth-Aware)
```
raw_obi_5, raw_obi_20, obi_smoothed_5, obi_smoothed_20, obi_zscore_5, obi_zscore_20
```

### Mandatory Spread Schema
```
spread_mean, spread_std
```

### Mandatory Other Schema
```
ma_slope, realised_vol_1s, unrealised_pnl
```

### Optional OBI Scalper Schema
```
raw_obi_L1_3, raw_obi_L1_10
```

## Data Types

| Feature | Data Type | Notes |
|---------|-----------|-------|
| timestamp | datetime64[ns] | UTC-naïve |
| mid_price | float64 | Price in USD |
| close | float64 | Price in USD |
| high | float64 | Price in USD |
| low | float64 | Price in USD |
| best_bid | float64 | Price in USD |
| best_ask | float64 | Price in USD |
| spread | float64 | Absolute spread in USD |
| volume | float64 | Trading volume |
| atr_14_sec | float64 | ATR in USD |
| atr_percent_sec | float64 | ATR as fraction (0.01 = 1%) |
| atr | float64 | Legacy ATR in USD |
| atr_percent | float64 | Legacy ATR as percentage |
| atr_tf | float64 | TF-specific ATR in USD |
| atr_mr | float64 | MR-specific ATR in USD |
| atr_mv | float64 | MV-specific ATR in USD |
| raw_obi_* | float64 | OBI ratio (-1 to 1) |
| obi_smoothed_* | float64 | Smoothed OBI ratio |
| obi_zscore_* | float64 | Z-score normalized OBI |
| spread_mean | float64 | Mean spread in USD |
| spread_std | float64 | Spread std dev in USD |
| ma_slope | float64 | Slope value |
| realised_vol_1s | float64 | Volatility measure |
| unrealised_pnl | float64 | P&L in USD (placeholder) |

## Notes

- All timestamps are naive UTC
- OBI features support configurable depth levels (5 or 20)
- ATR features use different calculation methods for different strategies
- Spread statistics use causal rolling windows with forward-fill
- Depth placeholders `{depth}` and `{levels}` are resolved at runtime via configuration

## Schema Audit

This catalog was generated by Task R-112n schema audit to ensure consistency
between code references and actual parquet schema. The audit found 27 unique
feature columns referenced across the codebase in detectors, strategies, 
risk management, and signal calculation components.

## Validation

ETL pipelines should validate that all mandatory schema columns are present
and have the correct data types. Missing columns should trigger clear error
messages indicating which component requires the missing feature.
