# Look-ahead Bias Fixes

## Date: May 10, 2025

This document logs the fixes implemented to address look-ahead bias issues in the signal calculator module.

## Background

Look-ahead bias occurs when a model uses information that would not be available at the time of making a prediction in a real-world scenario. In backtesting, this can lead to overly optimistic results that don't reflect real-world performance.

## Identified Issues

Two instances of look-ahead bias were identified in the signal calculator:

1. **Volatility Features Backfill**: The volatility features (`vol_long_term` and `vol_short_term`) were using backfill to handle NaN values, which could introduce future data into past calculations.

2. **ATR Percentile Calculation**: The ATR percentile calculation was including the current value when determining the percentile rank, which is a form of look-ahead bias.

## Implemented Fixes

### 1. Volatility Features Forward-Fill

Changed the volatility features from using backfill to forward-fill to prevent future data leakage.

#### Before:
```python
signals_df["vol_long_term"].fillna(
    method="bfill", inplace=True
)  # Backfill to get value sooner
signals_df["vol_short_term"].fillna(
    method="bfill", inplace=True
)
```

#### After:
```python
signals_df["vol_long_term"].fillna(
    method="ffill", inplace=True
)  # avoid look-ahead bias: forward-fill instead of back-fill
signals_df["vol_short_term"].fillna(
    method="ffill", inplace=True
)
```

### 2. Unbiased ATR Percentile Calculation

Added a dedicated helper function for unbiased percentile calculation and modified the ATR percentile calculation to use it.

#### Added Helper Function:
```python
# === helper: percentile of history (unbiased) ===============================
def _pct_of_history(arr: pd.Series) -> float:
    """
    Percentile of the *last* value versus the *previous* observations (0–1).
    Returns NaN until ≥2 valid points exist.
    """
    clean = pd.Series(arr[:-1]).dropna()
    if clean.empty:
        return np.nan
    return percentileofscore(clean, arr.iloc[-1]) / 100.0
# =============================================================================
```

#### Before:
```python
signals_df["atr_percent_pctile"] = (
    signals_df["atr_percent"]
    .rolling(window=rolling_window)
    .apply(
        lambda x: percentileofscore(x, x.iloc[-1]) / 100,
        raw=False,
    )
)
```

#### After:
```python
signals_df['atr_percent_pctile'] = (
    signals_df['atr_percent']
    .rolling(window=rolling_window, min_periods=max(1, rolling_window // 2))
    .apply(_pct_of_history, raw=False)
)
```

## Verification

The changes were verified by running assertions that confirmed:

1. Volatility features have valid values after forward-fill (no NaN propagation issues)
2. ATR percentile values are properly bounded between 0 and 1

## Impact on Backtest Results

Interestingly, the backtest results remained unchanged after implementing these fixes. Further investigation revealed that:

1. **Adaptive OBI Thresholds**: The volatility features (`vol_long_term` and `vol_short_term`) are used for adaptive OBI thresholds in the `GranularMicrostructureRegimeDetector`, but this feature appears to be disabled in the current configuration as there's no explicit setting for `adaptive_obi_base`.

2. **Percentile-based Volatility Thresholds**: The `atr_percent_pctile` is used for determining high/low volatility conditions when `vol_thresh_mode` is set to 'percentile', but this mode doesn't appear to be active in the current configuration.

These findings explain why our look-ahead bias fixes didn't affect the backtest results - the specific features we modified are not being actively used in the current trading strategy configuration.

## Future Considerations

While these fixes don't impact the current backtest results, they are important for:

1. **Future Strategy Configurations**: If adaptive OBI thresholds or percentile-based volatility thresholds are enabled in future configurations, these fixes will ensure more accurate backtesting results.

2. **Statistical Validity**: The fixes ensure that the signal calculator follows proper statistical practices by avoiding look-ahead bias, which is important for maintaining the integrity of the backtesting framework.

3. **Real-world Performance Alignment**: By eliminating look-ahead bias, backtesting results will more accurately reflect what would happen in real-time trading when these features are used.
