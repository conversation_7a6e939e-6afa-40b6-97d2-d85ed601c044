# GMS Spread Percentile Gate Analysis

## Issue Investigation

The `gms_spread_percentile_gate` parameter appears to work the same regardless of the value set (e.g., 10, 70, or 99), consistently filtering out approximately 3 trades in backtests.

## Findings

### Implementation Details

1. **Calculation Method**:
   - The `raw_spread_percentile` is calculated using the `rolling_percentile_rank` function in `calculator.py`
   - This function returns values from 0-100, representing where the current spread sits relative to recent history
   - Higher values indicate wider spreads compared to the historical window

2. **Usage in Detector**:
   - In `detector.py`, the code checks: `if raw_spread_percentile > self.spread_percentile_gate`
   - When this condition is true, the market condition is set to `'WIDE_SPREAD'`
   - This market condition affects regime determination, often resulting in an `'Uncertain'` regime

3. **Impact on Trading**:
   - The `'Uncertain'` regime prevents any strategies from being active
   - This effectively filters out potential trades when the spread percentile is above the gate value

### Distribution of Percentile Values

Analysis of the backtest signal data revealed:
- Min: 4.17
- Max: 100.0
- Mean: 49.75
- Median: 47.83
- Values above 10: ~95% of data
- Values above 70: ~28% of data
- Values above 90: ~13% of data

### Why Different Gate Values Have Similar Effects

1. **Low Gate Value (10)**:
   - Filters out ~95% of potential trades because their percentile values are above 10
   - Only a small fraction of trades pass through the filter

2. **High Gate Values (70 or 99)**:
   - The same high-percentile trades (those with values > 99) are still filtered out
   - The number of these trades is small compared to the total
   - The majority of trades are already being filtered out at gate=10, so increasing it to 70 or 99 doesn't make a noticeable difference

3. **Missing Connection**:
   - `'WIDE_SPREAD'` is not directly used as a regime
   - It's a market condition that influences the regime determination logic
   - This typically results in an `'Uncertain'` regime, which prevents trading

## Conclusion

The current implementation of `gms_spread_percentile_gate` has an inverted logic compared to what might be expected. Setting a low gate value (e.g., 10) is very restrictive, allowing trades only when the spread is in the bottom 10% of historical values. Setting a high gate value (e.g., 99) is still restrictive because most trades end up with an `'Uncertain'` regime due to how the market condition affects regime determination.

## Recommendations

1. **Reverse the Percentile Logic**:
   - Change the condition to `raw_spread_percentile > (100 - gate_value)`
   - This way, a gate value of 10 would filter out only the top 10% of spreads
   - A gate value of 90 would filter out only the top 90% of spreads

2. **Modify Regime Determination**:
   - Change how `'WIDE_SPREAD'` affects regime determination
   - Instead of setting the regime to `'Uncertain'`, set it to a specific regime that allows certain strategies

3. **Clearer Parameter Documentation**:
   - Update the parameter description to clarify its behavior
   - Make it clear that lower values are more restrictive
