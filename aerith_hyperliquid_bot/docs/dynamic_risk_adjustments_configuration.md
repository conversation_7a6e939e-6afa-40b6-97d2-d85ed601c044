# Dynamic Risk Adjustments Configuration Guide

## Overview

This document provides detailed information on the dynamic risk adjustment system implemented in the trading bot. The system offers two complementary features:

1. **Dynamic Leverage**: Adjusts leverage based on market volatility
2. **Regime-Based Risk Adjustment**: Modifies risk parameters based on market regime

These features allow the trading bot to adapt to changing market conditions, reducing risk during high volatility periods and potentially increasing returns during favorable conditions.

## Configuration Parameters

All dynamic risk adjustment features are fully configurable through the `base.yaml` file.

### Core Settings (Dynamic Leverage)

```yaml
core:
  # Dynamic leverage adjustment based on market volatility
  use_dynamic_leverage: true   # When true, reduces leverage during high volatility periods
  max_leverage: 10.0           # Global maximum leverage cap
  asset_max_leverage: 50.0     # Asset-specific max leverage
```

### Regime Settings (Dynamic Risk Adjustment)

```yaml
regime:
  # Dynamic risk adjustment based on market regime
  dynamic_risk_adjustment: true        # Apply dynamic risk in volatile regimes
  
  # Risk factors for different market conditions
  chop_risk_factor: 0.5                # Multiplier for risk_per_trade in choppy markets
  chop_leverage_factor: 0.5            # Multiplier for leverage in choppy markets
  strong_trend_risk_factor: 0.7        # Multiplier for risk_per_trade in strong trends
  strong_trend_leverage_factor: 0.8    # Multiplier for leverage in strong trends
  weak_trend_risk_scale: 0.8           # Scale risk in weak trend regimes
```

## How It Works

### Dynamic Leverage Calculation

The dynamic leverage system adjusts the maximum allowed leverage based on recent market volatility:

1. **Volatility Calculation**: 
   - Uses recent price data to calculate normalized volatility
   - Can use pre-calculated ATR values when available

2. **Leverage Scaling**:
   - Applies an inverse relationship between volatility and leverage
   - Higher volatility → Lower leverage
   - Lower volatility → Higher leverage

3. **Formula**:
   ```
   vol_multiplier = max(0.4, min(1.6, 1.0 / (volatility * 20 + 0.5)))
   adjusted_leverage = base_leverage * vol_multiplier
   ```

4. **Limits**:
   - Ensures leverage stays within configured limits (min 1.0, max asset_max_leverage)
   - Provides a more gradual response to volatility changes

### Regime-Based Risk Adjustment

The regime-based risk adjustment system modifies risk parameters based on the detected market regime:

1. **Regime Detection**:
   - Uses the Granular Microstructure Detector (GMS) to identify market regimes
   - Responds to specific regimes: "High_Vol_Range", "Strong_Bull_Trend", "Strong_Bear_Trend", etc.

2. **Risk Parameter Adjustment**:
   - Adjusts both risk_per_trade and leverage based on regime
   - Different adjustment factors for different regime types

3. **Supported Regimes**:
   - **Choppy/Volatile**: "Volatile_Chop", "Low_Vol_Chop", "Ranging", "High_Vol_Range"
   - **Strong Trends**: "Strong_Bull_Trend", "Strong_Bear_Trend"
   - **Weak Trends**: Uses weak_trend_risk_scale for "Weak_Bull_Trend", "Weak_Bear_Trend"

## Implementation Details

### Dynamic Leverage

The dynamic leverage calculation is implemented in the `Portfolio` class:

```python
def calculate_dynamic_leverage(self, current_price: float = None, volatility: float = None, timestamp: Optional[float] = None) -> float:
    """
    Calculate dynamic leverage based on market volatility.
    Lower leverage during high volatility, higher leverage during low volatility.
    """
    # Get base leverage from config
    base_leverage = self.config.core.max_leverage
    
    # Calculate volatility if not provided
    if volatility is None:
        # [Code to calculate volatility from recent price data]
    
    # Scale volatility to a more sensitive range (0.4 to 1.6)
    vol_multiplier = max(0.4, min(1.6, 1.0 / (volatility * 20 + 0.5)))
    
    # Apply multiplier to base leverage
    adjusted_leverage = base_leverage * vol_multiplier
    
    # Cap at asset max leverage and minimum of 1.0
    max_allowed = self.config.core.asset_max_leverage
    adjusted_leverage = max(1.0, min(adjusted_leverage, max_allowed))
    
    return adjusted_leverage
```

### Regime-Based Risk Adjustment

The regime-based risk adjustment is implemented in the `RiskManager` class:

```python
# Apply dynamic risk adjustment based on regime if configured
apply_dynamic_risk = False
if hasattr(cfg.regime, 'dynamic_risk_adjustment') and cfg.regime.dynamic_risk_adjustment:
    # Check if current regime should trigger dynamic risk adjustment
    if regime in ["Volatile_Chop", "Low_Vol_Chop", "Ranging", "High_Vol_Range", 
                  "Strong_Bull_Trend", "Strong_Bear_Trend"]:
        apply_dynamic_risk = True
        self.logger.info(f"Dynamic risk adjustment triggered by regime: {regime}")

if apply_dynamic_risk:
    # Use different risk factors based on regime type
    if regime in ["Strong_Bull_Trend", "Strong_Bear_Trend"]:
        # For strong trends
        risk_factor = getattr(cfg.regime, 'strong_trend_risk_factor', cfg.regime.chop_risk_factor)
        leverage_factor = getattr(cfg.regime, 'strong_trend_leverage_factor', cfg.regime.chop_leverage_factor)
    else:
        # For choppy/ranging markets
        risk_factor = cfg.regime.chop_risk_factor
        leverage_factor = cfg.regime.chop_leverage_factor
        
    # Apply the adjustment
    original_leverage_ref = leverage
    leverage = max(cfg.indicators.min_leverage, 
                  min(leverage * leverage_factor, cfg.core.max_leverage))
```

## Performance Impact

The dynamic risk adjustment system can significantly impact trading performance:

### Potential Benefits

1. **Reduced Drawdowns**: Lower leverage during high volatility periods can reduce drawdowns
2. **Improved Risk-Adjusted Returns**: Better adaptation to market conditions can improve Sharpe ratio
3. **More Aggressive in Favorable Conditions**: Can increase returns during favorable market regimes

### Observed Impact

In our testing, enabling dynamic risk adjustments showed:

- **ROI Increase**: From 130.18% to 325.65% (nearly 2.5x higher)
- **Max Drawdown Increase**: From 6.36% to 12.46% (about 2x higher)
- **Sharpe Ratio Change**: Slight decrease from 3.72 to 3.09

This reflects a more dynamic approach to risk management, with higher potential returns but also higher risk.

## Troubleshooting

### Common Issues

1. **No Effect on Backtest Results**:
   - Check if the configuration parameters are correctly defined in the Pydantic model
   - Verify that the log shows "CONFIG: Dynamic Leverage = True"
   - Look for "Dynamic risk adjustment triggered" messages in the logs

2. **Excessive Risk Reduction**:
   - Adjust the risk factors (increase chop_risk_factor, strong_trend_risk_factor)
   - Modify the volatility scaling formula in calculate_dynamic_leverage

3. **Insufficient Risk Reduction**:
   - Decrease the risk factors
   - Make the volatility scaling more sensitive by adjusting the formula

### Debugging

Add these debug statements to your configuration to verify settings are being applied:

```python
self.logger.info(f"DEBUG CONFIG: use_dynamic_leverage = {self.config.core.use_dynamic_leverage}")
self.logger.info(f"DEBUG CONFIG: dynamic_risk_adjustment = {self.config.regime.dynamic_risk_adjustment}")
```

## Recommendations for Optimal Use

1. **Start Conservative**: Begin with the default settings and gradually adjust
2. **Monitor Volatility Levels**: Check the logs for volatility values to ensure they're reasonable
3. **Test Different Market Conditions**: Run backtests on different time periods to see how the system adapts
4. **Balance Risk and Reward**: Adjust parameters to find the optimal risk/reward balance for your strategy
5. **Consider Isolated Margin**: The dynamic risk system is particularly valuable when using isolated margin

## Advanced Configurations

For more advanced risk management, consider these configurations:

### Aggressive Configuration

```yaml
core:
  use_dynamic_leverage: true
  max_leverage: 15.0

regime:
  dynamic_risk_adjustment: true
  chop_risk_factor: 0.3
  chop_leverage_factor: 0.4
  strong_trend_risk_factor: 0.9
  strong_trend_leverage_factor: 1.0
```

### Conservative Configuration

```yaml
core:
  use_dynamic_leverage: true
  max_leverage: 5.0

regime:
  dynamic_risk_adjustment: true
  chop_risk_factor: 0.2
  chop_leverage_factor: 0.3
  strong_trend_risk_factor: 0.5
  strong_trend_leverage_factor: 0.6
```

## Conclusion

The dynamic risk adjustment system provides a powerful way to adapt to changing market conditions. By properly configuring these features, you can create a more resilient trading system that manages risk more effectively while potentially improving returns.

Remember that all these features are fully toggleable through the configuration file, allowing you to easily enable or disable them as needed.
