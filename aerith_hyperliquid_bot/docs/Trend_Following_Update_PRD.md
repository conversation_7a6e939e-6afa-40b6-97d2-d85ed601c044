# Hyperliquid Bot — Master PRD & Roadmap

*(rev 2025‑05‑25 · rebuilt after thread split)*

---

## 0 <PERSON><PERSON><PERSON> & Scope

A **retail‑grade, microstructure‑centric trading stack** for Hyperliquid perpetuals that runs on a single cloud VM (≈ 500 ms RTT) and exploits alpha unavailable to vanilla TA bots.

Core layers:

1. **Continuous GMS Detector** – 60 s cadence, depth‑agnostic (5 / 20), 8 raw states + `risk_suppressed`.
2. **TF‑v3 Swing Strategy** – 1 h cadence, regime‑aware EMA/ATR logic.
3. **HF Micro‑Alpha Engine** – 200 ms loop on 1 s features (OBI module first).
4. **Unified RiskManager v2** – single cross‑margin position cap shared by all layers.

Everything is parameterised via **`config/base.yaml`**; modules can be toggled without code edits.

---

## 1 Workflow & Roles

| Role                              | Responsibility                           |
| --------------------------------- | ---------------------------------------- |
| **ChatGPT Supervisor**            | Maintain PRD, break tasks, code‑review   |
| **Gemini 2.5 Pro / Augment Code** | Heavy implementation, long‑context tasks |
| **Cursor / Cline / Roo**          | IDE helpers, unit tests                  |
| **Human Owner**                   | Final merges, data uploads, live deploy  |

---

## 2 Data Flow & Processing Guide

1. **Raw L2 snapshots** (JSON‑lines, 10 Hz, depth 20) live on external SSD.
   *Example:* `/ext_ssd/hyperliquid/BTC_11_l2Book.txt`
2. **Convert once → hourly Arrow**

   ```bash
   python -m tools.txt_to_arrow \ 
         --infile  <raw.txt> \ 
         --outdir  $RAW_L2_DIR/YYYY-MM-DD/ \ 
         --date    2025-05-25 \ 
         --depth   20
   ```
3. **Run ETL → 1 s feature parquet** (manual or scheduler)

   ```bash
   python -m tools.etl_l20_to_1s \ 
         --raw-dir  $RAW_L2_DIR/2025-05-25 \ 
         --out-dir  $FEATURE_1S_DIR \ 
         --date     2025-05-25 \ 
         --depth    20
   ```

   → Files written to `.../features_1s/2025-05-25/features_20250525_08.parquet`, etc.
4. **1 h OHLCV** already stored under `$OHLC_DIR`; no change needed.
5. **Back‑testing** consumes 1 h bars + 1 s features. Raw L2 parquet is *optional* thanks to the hot‑fix.

---

## 3 Timeline & Task Board

### 3.1 Completed

| ID             | Date            | Outcome                                  |                            |
| -------------- | --------------- | ---------------------------------------- | -------------------------- |
| T‑101 → T‑107d | 2025‑05‑21 – 22 | OBI feature layer, initial EDA ✅         |                            |
| T‑106          | 2025‑05‑22      | Hourly OBI scalper prototype ✅           |                            |
| **T‑109**      | 2025‑05‑23      | Continuous GMS port + risk flag ✅        |                            |
| **T‑110a**     | 2025‑05‑24      | Depth‑flag wiring, unit tests ✅          |                            |
| **T‑110b**     | 2025‑05‑25      | Raw→1 s ETL CLI, FeatureStore ✅          |                            |
| **T‑110c**     | 2025‑05‑25      | ETL scheduler + integration smoke‑test ✅ |                            |
| \$1            | **T‑111a**      | 2025‑05‑26                               | Config wiring & RM hooks ✅ |
| **T‑111b**     | 2025‑05‑27      | TF‑v3 core logic & tests ✅               |                            |

### 3.2 Active / Upcoming

| ID         | P | Description                                                                     | Owner           | Depends      |
| ---------- | - | ------------------------------------------------------------------------------- | --------------- | ------------ |
| **T‑111**  | 1 | **TF‑v3 upgrade** – regime‑aware EMA, ATR stops, GMS gating, risk‑aware sizing. | Gemini → Cursor | T‑110c       |
| **T‑111e** | 1 | EMA‑grid sweep (8/21, 12/26, 20/50, 21/55, 8/128); report metrics.              | ChatGPT         | T‑111b       |
| **T‑111f** | 1 | Implement `GMSValidator` (staleness & flip‑flop guards, 200‑EMA contradiction). | Gemini          | T‑111a       |
| **T‑112**  | 2 | WebSocket L20 recorder (10 Hz) → hourly Arrow; CLI & tests.                     | Gemini          | —            |
| **T‑113**  | 3 | HF Micro‑Alpha Engine skeleton (OBI module first).                              | Cursor          | T‑112, T‑114 |
| **T‑114**  | 3 | Unified RiskManager v2 – global notional gating.                                | Cursor          | T‑110c       |
| **T‑115**  | 4 | Paper‑trade orchestrator (Docker/PM2, dashboards) incl. v2 vs v3 shadow mode.   | ChatGPT         | T‑111…114    |
| **T‑115a** | 2 | Phase‑1 shadow‑mode harness for TF‑v3.                                          | ChatGPT         | T‑111…114    |

---

## 4 Next Immediate Task (T‑111)

Fork TF‑v2 into **TF‑v3** with regime gating, ATR trailing stops, and RiskManager integration.  See §6 for full brief.

---

## 5 Message to Future ChatGPT Supervisor

> All tasks up to **T‑110c** are complete.  Start at **T‑111**, following the data‑flow in §2.  Raw L2 parquet is optional when 1‑s features are present.

---

## 6 TF‑v3 Implementation Brief (Task T‑111)

### 6.1 Objectives

1. **Regime gate** swing entries by Continuous GMS.
2. **Risk suppression** – no new positions if `risk_suppressed == true`.
3. **ATR‑scaled trailing stops** plus time‑decay exit.
4. **Forward‑compatible RiskManager hooks** (uses RM‑v1 today, plugs into RM‑v2 later).
5. **Strict no look‑ahead bias** – strategy only sees data available *at or before* candle open.

### 6.2 High‑Level Algorithm (look‑ahead–safe)

```text
For each new 1‑hour candle Ck about to open at time t0:
  # 0. Gather regime snapshot *available at t0*.
  gms_snapshot = latest_GMS_snapshot(ts <= t0)

  # 1. Staleness guard (configurable, default 120 s)
  if now - gms_snapshot.ts > cfg.tf_v3.gms_max_age_sec:
      skip("GMS stale")

  # 2. Validate regime stability / flip‑flop → see GMSValidator (T‑111f)
  if not GMSValidator.is_valid(gms_snapshot):
      skip("Regime unstable")

  # 3. Risk suppression gate
  if gms_snapshot.risk_suppressed:
      skip("Risk suppressed")

  # 4. Trend filter: require regime ∈ {BULL, BEAR}
  if gms_snapshot.state not in {BULL, BEAR}:
      skip("Neutral regime")

  # 5. Compute EMA_fast/slow, ATR on history up to prior candle Ck‑1 (no peek ahead).

  # 6. Entry rules (one‑at‑a‑time, no pyramiding)
  long_signal  = (gms_snapshot.state == BULL and EMA_fast > EMA_slow and not have_long)
  short_signal = (gms_snapshot.state == BEAR and EMA_fast < EMA_slow and not have_short)

  if long_signal or short_signal:
      size = min(cfg.tf_v3.max_notional,
                 RiskManager.available_notional() * cfg.tf_v3.risk_frac)
      send_order(direction=long/short, notional=size)

  # 7. Exit logic for existing position
      • Trailing stop: price crosses entry ± k·ATR
      • Time decay: if position_age_h > max_trade_life_h → close
```

### 6.3 Config Additions (`config/base.yaml`)

```yaml
tf_v3:
  enabled: true
  ema_fast: 20
  ema_slow: 50
  atr_period: 14          # on 1‑hour bars
  atr_trail_k: 3.0        # stop distance in ATRs
  max_trade_life_h: 24    # time‑decay exit
  risk_frac: 0.25         # fraction of free notional to allocate
  max_notional: 25_000    # absolute cap per TF trade
  gms_max_age_sec: 120    # staleness threshold for regime snapshot
```

### 6.4 Code Deliverables

| File/Module                | Purpose                                         |
| -------------------------- | ----------------------------------------------- |
| `strategies/tf_v3.py`      | `TFV3Strategy` core logic.                      |
| `helpers/gms_validator.py` | Flip‑flop & contradiction checks (T‑111f).      |
| `strategies/__init__.py`   | Registry export.                                |
| `tests/test_tf_v3.py`      | Unit tests w/ synthetic candles & regime flips. |
| `configs/base.yaml`        | New section above.                              |

### 6.5 Unit‑Test Plan

1. **Regime flip handling** – alternate BULL/BEAR every 4 h; assert entries align.
2. **Risk suppression** – set `risk_suppressed = true`; verify no new positions.
3. **ATR trailing stop** – trending price triggers stop at k·ATR distance.
4. **Time‑decay exit** – flat price exits after `max_trade_life_h`.
5. **Staleness guard** – feed snapshot older than `gms_max_age_sec`; strategy must skip.

### 6.6 EMA Parameter Sweep (T-111e)

Back‑test **2025‑05‑01 → 2025‑05‑21** on BTC‑PERP comparing:

* **Fixed pairs:** (8/21, 12/26, 20/50, 21/55, 8/128)
* **Adaptive prototype:** dynamic EMA lengths where
  `L_fast = clip(c / ATR_14, 8, 30)` and `L_slow = 2.5 * L_fast`

Record Sharpe, trade count, and max draw‑down. Promote the adaptive variant only if it out‑performs the best fixed pair by **≥ 0.15 Sharpe**, without increasing trade count by more than **10 %**, on both the main back‑test period *and* an out‑of‑sample hold‑out (2025‑05‑22 → 2025‑05‑28).

If the adaptive wins, spin up **Task T‑116** to productionise; otherwise keep 20/50 as default.

### 6.7 Back-Test Acceptance Criteria Back‑Test Acceptance Criteria

* Sharpe ≥ TF‑v2 Sharpe + 0.3.
* Trade count ≤ 60 % of TF‑v2 (lower churn).
* Max draw‑down ≤ TF‑v2.

### 6.8 Sub‑Task Breakdown

| Sub‑ID   | Owner   | Description                   | Est. hrs |
| -------- | ------- | ----------------------------- | -------- |
| **111a** | Gemini  | Config wiring & RM hooks      | 1.5      |
| **111b** | Gemini  | Implement `TFV3Strategy`      | 4        |
| **111c** | Cursor  | Write & run unit tests        | 2        |
| **111d** | ChatGPT | Back‑test metrics & report    | 3        |
| **111e** | ChatGPT | EMA grid sweep (see §6.6)     | 2        |
| **111f** | Gemini  | `GMSValidator` implementation | 1        |

➡ **Total budget**: \~13.5 engineering hours over two working days.

---

## 7 Phased Roll‑out Plan (links into T‑115)

| Phase | Duration | Mode                                | Capital    | Success Gate                      |
| ----- | -------- | ----------------------------------- | ---------- | --------------------------------- |
| 1     | Week 1‑2 | **Shadow** – v3 signals logged only | 0 %        | Stable logs, no errors            |
| 2     | Week 3‑4 | **Limited live**                    | 20 %       | v3 Sharpe ≥ v2 Sharpe + 0.2       |
| 3     | Week 5+  | **Gradual migration**               | 50 → 100 % | 2 weeks sustained out‑performance |

---

## 8 Operations Runbook (excerpt)

```markdown
### Daily Checks
- [ ] GMS uptime > 95 %
- [ ] Avg. regime confidence > 0.7
- [ ] ≤ 10 regime flips in 24 h
- [ ] TF‑v3 positions within risk caps

### Emergency
1. GMS failure → auto‑stand‑down; restart service.
2. Draw‑down > 5 % 1‑day → halve TF‑v3 capital.
3. Regime contradiction alerts > 3 / h → investigate data integrity.
```

---

*End of document – ready for T‑111 implementation.*
