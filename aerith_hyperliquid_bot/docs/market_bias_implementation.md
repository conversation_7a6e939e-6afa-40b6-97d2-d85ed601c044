# Market Bias Implementation Guide

## Overview

This document outlines the implementation of the 3-State Market Bias Risk Adjustment system for the trading bot. This feature allows the bot to adjust leverage and risk factors based on the current market state (BULL, BEAR, CHOP) and trade direction.

## Implementation Status

✅ **COMPLETED AND WORKING** - The 3-State Market Bias Risk Adjustment system has been implemented and verified.

**Key Features:**
- Adjusts leverage and risk based on market conditions (BULL, BEAR, CHOP)
- Applies direction-specific biases (e.g., reducing short size in bull markets)
- Includes detailed validation and logging for transparency
- Can be toggled on/off via the `enabled` flag in config

## Configuration Structure

The market bias settings should be added to `base.yaml` under the `regime` section:

```yaml
regime:
  # other regime settings...
  
  # Dynamic risk adjustment based on market regime
  dynamic_risk_adjustment: false        # Apply dynamic risk in volatile regimes
  
  # 3-State Market Bias Settings
  market_bias:
    enabled: true                      # Enable market bias risk adjustments
    use_three_state_mapping: true      # Use simplified 3-state market model
    
    # Market-specific leverage multipliers
    bull_leverage_factor: 1.5          # Increase leverage in bull markets
    bear_leverage_factor: 0.7          # Reduce leverage in bear markets
    chop_leverage_factor: 0.5          # Conservative leverage in choppy markets
    
    # Market-specific risk per trade multipliers
    bull_risk_factor: 1.2              # Increase risk per trade in bull markets
    bear_risk_factor: 0.8              # Reduce risk per trade in bear markets
    chop_risk_factor: 0.5              # Minimize risk in choppy markets
    
    # Long-Short bias
    bull_long_bias: 1.0                # No adjustment for longs in bull market
    bull_short_bias: 0.5               # Reduce short size in bull markets
    bear_long_bias: 0.5                # Reduce long size in bear markets
    bear_short_bias: 1.0               # No adjustment for shorts in bear market
```

## Implementation Steps

1. Update the `MarketBiasSettings` class in `settings.py`:

```python
class MarketBiasSettings(BaseModel):
    enabled: bool = Field(default=False, description="Enable market bias risk adjustments")
    use_three_state_mapping: bool = Field(default=True, description="Use simplified 3-state market model")
    
    # Market-specific leverage multipliers
    bull_leverage_factor: float = Field(default=1.5, description="Leverage multiplier for bull markets", gt=0)
    bear_leverage_factor: float = Field(default=0.7, description="Leverage multiplier for bear markets", gt=0)
    chop_leverage_factor: float = Field(default=0.5, description="Leverage multiplier for choppy markets", gt=0)
    
    # Market-specific risk per trade multipliers
    bull_risk_factor: float = Field(default=1.2, description="Risk factor multiplier for bull markets", gt=0)
    bear_risk_factor: float = Field(default=0.8, description="Risk factor multiplier for bear markets", gt=0)
    chop_risk_factor: float = Field(default=0.5, description="Risk factor multiplier for choppy markets", gt=0)
    
    # Long-Short bias
    bull_long_bias: float = Field(default=1.0, description="Direction bias for longs in bull market", gt=0)
    bull_short_bias: float = Field(default=0.5, description="Direction bias for shorts in bull market", gt=0)
    bear_long_bias: float = Field(default=0.5, description="Direction bias for longs in bear market", gt=0)
    bear_short_bias: float = Field(default=1.0, description="Direction bias for shorts in bear market", gt=0)
```

2. Update the `RegimeSettings` class in `settings.py` to include the `market_bias` field:

```python
# Add this to the RegimeSettings class 
market_bias: 'MarketBiasSettings' = Field(default_factory=lambda: MarketBiasSettings(), description="Market bias risk adjustment settings")
```

3. Add the market bias adjustment logic to the `RiskManager.calculate_position` method in `risk.py` after the existing dynamic risk adjustments:

```python
# --- STEP 4: Apply Market Bias Adjustments (COMPLETELY OPTIONAL) ---
# This only applies if market_bias.enabled = true in the config

if hasattr(cfg.regime, 'market_bias') and hasattr(cfg.regime.market_bias, 'enabled') and cfg.regime.market_bias.enabled:
    self.logger.info(f"MARKET BIAS: Checking market bias adjustment based on regime '{regime}'")
    
    # Determine market state based on current regime (BULL, BEAR, CHOP)
    market_state = "CHOP"  # Default fallback state
    
    # Map the current regime to a simplified 3-state model
    bull_regimes = ["Strong_Bull_Trend", "Weak_Bull_Trend"]
    bear_regimes = ["Strong_Bear_Trend", "Weak_Bear_Trend"]
    chop_regimes = ["Ranging", "Sideways", "Choppy"]
    
    if regime in bull_regimes:
        market_state = "BULL"
    elif regime in bear_regimes:
        market_state = "BEAR"
    else:
        market_state = "CHOP"
        
    self.logger.info(f"MARKET BIAS: Mapped regime '{regime}' to market state '{market_state}'")
    
    # Get direction from strategy_info if available
    direction = "unknown"
    if strategy_info and 'direction' in strategy_info:
        direction = strategy_info['direction'].lower()  # Normalize to lowercase
    
    # Apply market bias-specific factors
    base_leverage_market = final_leverage  # Start from the current final_leverage
    market_leverage_factor = 1.0
    market_risk_factor = 1.0
    direction_bias = 1.0
    
    if market_state in ["BULL", "BEAR", "CHOP"]:
        self.logger.info(f"MARKET BIAS: Applying adjustments for {market_state} market")
        
        # Apply market-specific factors based on market state
        if market_state == "BULL":
            market_leverage_factor = getattr(cfg.regime.market_bias, 'bull_leverage_factor', 1.5)
            market_risk_factor = getattr(cfg.regime.market_bias, 'bull_risk_factor', 1.2)
            
            # Apply direction-specific bias if direction is known
            if direction == "long":
                direction_bias = getattr(cfg.regime.market_bias, 'bull_long_bias', 1.0)
            elif direction == "short":
                direction_bias = getattr(cfg.regime.market_bias, 'bull_short_bias', 0.5)
                
        elif market_state == "BEAR":
            market_leverage_factor = getattr(cfg.regime.market_bias, 'bear_leverage_factor', 0.7)
            market_risk_factor = getattr(cfg.regime.market_bias, 'bear_risk_factor', 0.8)
            
            # Apply direction-specific bias if direction is known
            if direction == "long":
                direction_bias = getattr(cfg.regime.market_bias, 'bear_long_bias', 0.5)
            elif direction == "short":
                direction_bias = getattr(cfg.regime.market_bias, 'bear_short_bias', 1.0)
                
        else:  # CHOP
            market_leverage_factor = getattr(cfg.regime.market_bias, 'chop_leverage_factor', 0.5)
            market_risk_factor = getattr(cfg.regime.market_bias, 'chop_risk_factor', 0.5)
        
        # Apply adjustments
        market_adjusted_leverage = base_leverage_market * market_leverage_factor
        
        # Apply direction bias if direction is known
        if direction != "unknown":
            market_adjusted_leverage *= direction_bias
            self.logger.info(f"MARKET BIAS: Applied direction bias for {direction} in {market_state} market: {direction_bias:.2f}x")
        
        # Update final leverage with market bias adjustment
        final_leverage = market_adjusted_leverage
        
        # Update risk factor with market bias risk factor
        risk_factor *= market_risk_factor
        self.logger.info(f"MARKET BIAS: Applied market risk factor: {market_risk_factor:.2f}x, resulting in total risk factor: {risk_factor:.2f}x")
```

4. Make sure to update the `calculate_position` method to call the validation function:

```python
# For detailed debugging/analysis
self._validate_market_bias(
    market_state=market_state,
    regime=regime,
    base_leverage=base_leverage_market,
    final_leverage=final_leverage,
    market_risk_factor=market_risk_factor,
    direction_bias=direction_bias if direction != "unknown" else 1.0
)
```

5. Add a validation method to `RiskManager` in `risk.py`:

```python
def _validate_market_bias(self, market_state: str, regime: str, base_leverage: float, 
                           final_leverage: float, market_risk_factor: float, direction_bias: float):
    """
    Validate that market bias settings are properly applied.
    
    Args:
        market_state: Simplified market state (BULL, BEAR, CHOP)
        regime: Detailed market regime
        base_leverage: Original base leverage
        final_leverage: Adjusted final leverage
        market_risk_factor: Applied risk factor
        direction_bias: Direction-specific bias applied
    """
    cfg = self.config
    
    # Check if market bias should be enabled
    if not hasattr(cfg.regime, 'market_bias') or not hasattr(cfg.regime.market_bias, 'enabled'):
        self.logger.warning("VALIDATION: Market bias settings not available in config!")
        return
    
    if not cfg.regime.market_bias.enabled:
        self.logger.warning("VALIDATION: Market bias is disabled but seems to be applied!")
        return
    
    # Log the applied adjustments for verification
    leverage_change = ((final_leverage / base_leverage) - 1.0) * 100
    self.logger.info("--------- MARKET BIAS VALIDATION ---------")
    self.logger.info(f"  • Detailed Regime: {regime}")
    self.logger.info(f"  • Simplified State: {market_state}")
    self.logger.info(f"  • Base Leverage: {base_leverage:.2f}x")
    self.logger.info(f"  • Final Leverage: {final_leverage:.2f}x")
    self.logger.info(f"  • Market Risk Factor: {market_risk_factor:.2f}x")
    self.logger.info(f"  • Direction Bias: {direction_bias:.2f}x")
    self.logger.info(f"  • Effective Leverage Change: {leverage_change:.1f}%")
    
    if market_state == "BULL":
        expected_lev_factor = getattr(cfg.regime.market_bias, 'bull_leverage_factor', 1.5)
        expected_risk_factor = getattr(cfg.regime.market_bias, 'bull_risk_factor', 1.2)
    elif market_state == "BEAR":
        expected_lev_factor = getattr(cfg.regime.market_bias, 'bear_leverage_factor', 0.7)
        expected_risk_factor = getattr(cfg.regime.market_bias, 'bear_risk_factor', 0.8)
    else:  # CHOP
        expected_lev_factor = getattr(cfg.regime.market_bias, 'chop_leverage_factor', 0.5)
        expected_risk_factor = getattr(cfg.regime.market_bias, 'chop_risk_factor', 0.5)
    
    # Account for direction bias in the expected leverage calculation
    expected_leverage = base_leverage * expected_lev_factor * direction_bias
    self.logger.info(f"  • Expected Risk Factor: {expected_risk_factor}")
    self.logger.info(f"  • Expected Leverage Factor: {expected_lev_factor}")
    self.logger.info(f"  • Direction Bias: {direction_bias:.2f}")
    self.logger.info(f"  • Expected Adjusted Leverage: {expected_leverage:.2f}x")
    self.logger.info(f"  • Actual Final Leverage: {final_leverage:.2f}x")
    
    # Verify if applied leverage is close to expected
    leverage_diff = abs(final_leverage - expected_leverage) / expected_leverage if expected_leverage > 0 else 1.0
    if leverage_diff < 0.05:  # Within 5% of expected
        self.logger.info(f"  • Leverage Application: CORRECT")
    else:
        self.logger.warning(f"  • Leverage Application: POSSIBLE ERROR")
    
    self.logger.info("--------- END VALIDATION ---------")
    
    # Verify if applied risk factor is as expected
    risk_diff = abs(market_risk_factor - expected_risk_factor)
    if risk_diff < 0.01:  # Within reasonable precision
        self.logger.info(f"  • Risk Factor Adjustment: CORRECT")
    else:
        self.logger.warning(f"  • Risk Factor Adjustment: POSSIBLE ERROR")

## Sanity Checks

To verify that the market bias adjustments are working correctly, perform the following sanity checks:

### 1. Configuration Checks

- Ensure the `market_bias` section is correctly nested under the `regime` section in `base.yaml`
- Set `enabled: true` to activate the feature
- Check that all required parameters (leverage factors, risk factors, direction biases) have appropriate values:
  - Bull market factors typically > 1.0 (more aggressive)
  - Bear market factors typically < 1.0 (more conservative)
  - Chop market factors should be the most conservative (lowest)

```yaml
# Example configuration check
regime:
  market_bias:
    enabled: true  # Must be true to apply adjustments
    bull_leverage_factor: 1.5  # Should be > 1.0
    bear_leverage_factor: 0.7  # Should be < 1.0
    chop_leverage_factor: 0.5  # Should be lowest
```

### 2. Log Validation Checks

Run a backtest with market bias enabled and check the logs for the following patterns:

1. **Market State Mapping**: Verify that regimes are mapped correctly to simplified states
   - Look for: `MARKET BIAS: Mapped regime 'Strong_Bull_Trend' to market state 'BULL'`

2. **Adjustment Application**: Confirm adjustments are applied based on market state
   - Look for: `MARKET BIAS: Applied market risk factor: 1.20x, resulting in total risk factor: 0.96x`
   - Look for: `MARKET BIAS: Applied direction bias for long in BULL market: 1.00x`

3. **Validation Results**: Check that the validation confirms correct application
   - Look for: `• Leverage Application: CORRECT`
   - If you see `• Leverage Application: POSSIBLE ERROR`, investigate potential issues

### 3. Trade Count Check

Run backtests with the feature enabled and disabled to confirm it doesn't alter the core trading logic:

1. With market bias disabled: Should match the baseline (149 trades)
2. With market bias enabled: Should maintain the same trade count (149 trades)
   - The P&L and performance metrics may change, but the trade count should remain consistent

### 4. Performance Analysis

After confirming the feature works correctly, perform an analysis of performance differences:

1. Compare equity curves with and without market bias adjustments
2. Check for changes in key metrics (Sharpe, Sortino, drawdown)
3. Analyze whether the direction bias is having the intended effect (e.g., reduced losses on shorts in bull markets)

### 5. Error Handling Verification

Test the system's error handling by intentionally introducing edge cases:

1. Set `enabled: true` but remove one of the required parameters
2. Set extreme values (e.g., very high or very low factors) to ensure the system handles them gracefully
3. Confirm that the global leverage bounds (min/max leverage) are still respected regardless of market bias adjustments

## Troubleshooting

### Common Issues

1. **Bias Not Applied**: If adjustments aren't being applied:
   - Verify `enabled: true` in the configuration
   - Check that the regime mapping is correct (e.g., `Strong_Bull_Trend` → `BULL`)
   - Confirm that the strategy is providing a valid `direction` in `strategy_info`

2. **Incorrect Adjustments**: If adjustments seem wrong:
   - Look at the validation logs to compare expected vs. actual leverage
   - Verify that nested configuration settings are being accessed correctly
   - Check that the direction bias is being applied only when direction is known

3. **Trade Count Changes**: If trade count changes after enabling market bias:
   - Ensure the market bias logic is only adjusting size/leverage/risk, not affecting entry/exit signals
   - Verify that core trading logic is completely preserved
   - Check that risk factor adjustments aren't causing trades to be filtered out

### Logging

To get detailed logs for troubleshooting, set the logging level to DEBUG when running backtests. The market bias validation logs will show detailed information about applied adjustments.

## Usage Instructions

1. To enable market bias adjustments, set `market_bias.enabled: true` in `base.yaml`
2. To disable, set `market_bias.enabled: false`

The adjustments apply:
- Different leverage and risk factors based on market state (BULL/BEAR/CHOP)
- Direction-specific biases (favoring longs in bull markets, shorts in bear markets)

## Important Notes

- The dynamic leverage feature has been removed as it was non-functional
- Dynamic risk adjustment is now disabled by default, but market bias is enabled
- This implementation does NOT affect trade signals, only position sizing and leverage
- Ensure the configuration is properly loaded and that the settings are applied correctly
- Log messages with the prefix "MARKET BIAS:" can be used to verify the feature is working
- The risk validation now only shows Regime-Based adjustments in the active components list
