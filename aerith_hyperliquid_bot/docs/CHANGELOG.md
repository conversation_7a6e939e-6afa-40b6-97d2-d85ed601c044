# Changelog

All notable changes to this project will be documented in this file.

## [Unreleased]

### Added
- Unified depth flag in data pipeline with `microstructure.depth_levels` parameter
- Unit tests for depth flag functionality
- ETL pipeline for converting raw L2 snapshots to 1-second feature parquet files
- New CLI tool `tools/etl_l20_to_1s.py` for processing raw L2 data
- ETL scheduler service (`services/etl_scheduler.py`) for automatic processing of raw L2 data
- Integration test for ETL scheduler with ContinuousGMSDetector
- `FeatureStore` class for loading and querying 1-second feature data
- ATR calculation utilities in `features/ta_utils.py`
- Configuration settings for ETL pipeline and scheduler in `base.yaml`
- Optional raw L2 loading when 1-second feature files already exist
- TF-v3 strategy with regime gating, ATR trailing stops, and risk management
- `RiskManagerInterface` with forward-compatible `available_notional()` method
- Configuration settings for TF-v3 strategy in `base.yaml`
- Documentation for TF-v3 strategy in `docs/TFV3Strategy.md`
- `GMSProvider` class for GMS snapshot integration with staleness check
- `GMSValidator` stub for future implementation in T-111f
- Look-ahead safety for all indicators (EMA, ATR) in `features/indicators.py`
- State persistence for TF-v3 strategy with entry_price, trail_price, and entry_ts
- `deduplicate()` utility function to handle duplicate indices in DataFrames
- `SkipReasonLogger` class for logging SkipSignal reasons during backtesting
- Unit tests for the deduplicate function
- Smoke test script for running a single-day backtest
- Test script for TF-v3 strategy to verify functionality
- Risk manager support for TF-v3 strategy with ATR-based stop distance calculation
- EMA fallback mechanism for TF-v3 strategy when historical data is insufficient
- Scripts for resetting TF-v3 state before backtesting
- Scripts for patching TF-v3 strategy and RiskManager for backtesting
- Fallback ATR calculation in Backtester for TF-v3 strategy

### Changed
- Deprecated `microstructure.obi_levels` and `microstructure.depth_levels_for_calc` in favor of `microstructure.depth_levels`
- Updated data handler to use the unified depth flag
- Updated signal calculator to use the unified depth flag
- Updated detectors to use the unified depth flag

### Fixed
- Duplicate-index errors in the backtester by using the deduplicate utility function
- Chained assignment issues in the ATR calculation in signals/calculator.py
- Added ATR verification to ensure ATR values are available
- Added SkipSignal reason logging to track why trades are skipped
- GMS staleness issues by increasing max_age_sec threshold and adding detailed logging
- Added fallback calculations for ROC and ATR when primary methods fail
- Made depth metrics NaN-tolerant to prevent SignalEngine from aborting
- Fixed format specifier error in backtester debug logging
- Fixed timestamp comparison issues in GMS provider
- Added fallback for EMA values in TF-v3 strategy when historical data is insufficient
- Fixed risk manager to support TF-v3 strategy
- Fixed state management in TF-v3 strategy during backtesting
- Fixed SL/TP calculation in Backtester for TF-v3 strategy
- Fixed margin requirements calculation in RiskManager for backtesting
- Fixed position dictionary creation in Backtester
- **R-104b**: Replaced legacy backtest detection with `is_backtest` flag in RiskManager and TF-v3 strategy
- **R-105a**: Exposed hard-coded ATR fallback (1%) and min trade size (0.001 BTC) in configuration schema
- **R-105b**: Removed hard-coded literals from runtime code, now uses config values for ATR fallback and min trade size
- **R-105c**: TF-v3 trailing-stop epsilon now configurable via `trail_eps` (will migrate to `trail_eps_tick_mult` once exchange metadata layer exists)
- **R-105d**: OBI-Scalper tick size now configurable via `strategies.obi_scalper.tick_size` parameter
- **R-105e**: Margin buffer now configurable via `portfolio.margin_buffer_pct` parameter
- **R-111**: TF-v2 warm-up period now configurable via `strategies.tf_warmup_bars` (default: "auto" = max(ema_slow, atr_period, regime_lookback))

### Migration Notes
- If you have custom configurations with different values for `obi_levels` and `depth_levels_for_calc`, you should update them to use the same value in `depth_levels`
- The old parameters will continue to work but must match `depth_levels` or be set to `null` to inherit from `depth_levels`
