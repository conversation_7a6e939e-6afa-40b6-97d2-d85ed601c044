# Adaptive Thresholds Code Analysis

**Date**: 2025-01-23  
**Analyst**: AI Code Analysis  
**System**: ContinuousGMSDetector Adaptive Thresholds  
**Focus**: `_prime_adaptive_thresholds` Method Performance Bottleneck  

## Executive Summary

This document provides a detailed code-level analysis of the `_prime_adaptive_thresholds` method in the `ContinuousGMSDetector` class, which creates a severe performance bottleneck during backtest initialization. The analysis confirms the profiling results showing 629+ seconds of initialization time due to inefficient percentile calculations.

## Method Location and Structure

### File Location
- **File**: `hyperliquid_bot/core/gms_detector.py`
- **Method**: `_prime_adaptive_thresholds` (lines 1134-1200)
- **Class**: `ContinuousGMSDetector`
- **Helper Method**: `_load_historical_features` (lines 1201-1272)

### Method Signature
```python
def _prime_adaptive_thresholds(self, priming_hours: int) -> None:
    """
    Prime adaptive threshold buffers with historical data.
    
    Args:
        priming_hours: Number of hours of historical data to load for priming
    """
```

## Input Data Analysis

### Data Source and Volume
- **Data Source**: 1-second feature data from parquet files in `feature_1s_dir`
- **Default Priming Period**: 24 hours (`priming_hours: 24` from config)
- **Data Volume**: **86,400 rows** (24 hours × 3,600 seconds per hour)
- **Time Range**: `(backtest_start_time - priming_hours)` to `backtest_start_time`

### Data Loading Process
The `_load_historical_features()` method:
1. **File Structure**: Reads from date-specific directories (e.g., `2025-03-01/features_*.parquet`)
2. **Column Selection**: Loads only required columns for efficiency:
   ```python
   columns_needed = ['timestamp', self.ATR_PCT_COL, 'ma_slope', 'ma_slope_ema_30s']
   ```
3. **Multi-day Support**: Combines data across multiple days if time range spans dates
4. **Chronological Ordering**: Sorts data by timestamp for causal processing

### Configuration Parameters
```yaml
# From configs/base.yaml
gms:
  auto_thresholds: false                # Enable adaptive thresholds
  percentile_window_sec: 86400          # 24-hour rolling window
  vol_low_pct: 0.001                    # 0.1st percentile for volatility
  vol_high_pct: 0.50                    # 50th percentile for volatility
  mom_low_pct: 0.001                    # 0.1st percentile for momentum
  mom_high_pct: 0.50                    # 50th percentile for momentum
  priming_hours: 24                     # Hours of historical data for priming
  min_history_rows: 100                 # Minimum rows required
```

## Features Being Primed

The method primes adaptive thresholds for **exactly 2 features**:

### 1. Volatility Feature: `atr_percent_sec`
- **Column Name**: Stored in `self.ATR_PCT_COL = 'atr_percent_sec'`
- **Purpose**: ATR-based volatility metric for regime classification
- **Threshold Instance**: `self.adaptive_vol_threshold`
- **Percentiles**: 0.1st percentile (low) and 50th percentile (high)

### 2. Momentum Feature: `ma_slope_ema_30s`
- **Primary Column**: `ma_slope_ema_30s` (newer EMA-based implementation)
- **Fallback Column**: `ma_slope` (legacy implementation)
- **Purpose**: Moving average slope for momentum-based regime classification
- **Threshold Instance**: `self.adaptive_mom_threshold`
- **Processing**: Uses absolute value: `abs(mom_metric)`
- **Percentiles**: 0.1st percentile (low) and 50th percentile (high)

## Detailed Calculation Logic for `atr_percent_sec`

### Step-by-Step Process

```python
# 1. Load 86,400 rows of historical 1-second data
feature_data = self._load_historical_features(priming_start, priming_end)
feature_data = feature_data.sort_values('timestamp')  # Ensure chronological order

# 2. Initialize counters
primed_rows = 0

# 3. Iterate through each row (86,400 iterations)
for _, row in feature_data.iterrows():
    # 4. Extract volatility value from current row
    vol_metric = row.get(self.ATR_PCT_COL)  # 'atr_percent_sec'
    
    if pd.notna(vol_metric):
        # 5. Update adaptive threshold (BOTTLENECK OCCURS HERE)
        low_thresh, high_thresh = self.adaptive_vol_threshold.update(vol_metric)
    
    # 6. Extract momentum value from current row
    mom_metric = row.get('ma_slope_ema_30s')
    if pd.isna(mom_metric):
        mom_metric = row.get('ma_slope')  # Fallback to legacy column
    
    if pd.notna(mom_metric):
        # 7. Update momentum threshold (BOTTLENECK OCCURS HERE)
        self.adaptive_mom_threshold.update(abs(mom_metric))
    
    primed_rows += 1
```

### Rolling Window Mechanics

**Window Configuration**:
- **Window Length**: `percentile_window_sec = 86400` (24 hours)
- **Buffer Type**: `collections.deque(maxlen=86400)`
- **Causal Processing**: Percentiles calculated on existing buffer before adding new value

**Percentile Calculation**:
- **Low Threshold**: 0.1st percentile (`vol_low_pct: 0.001`)
- **High Threshold**: 50th percentile (`vol_high_pct: 0.50`)
- **Purpose**: Low threshold separates low volatility, high threshold separates high volatility

## AdaptiveThreshold Class Interaction

### Initialization Process
```python
# In ContinuousGMSDetector.__init__()
if self.cfg_gms and getattr(self.cfg_gms, 'auto_thresholds', False):
    from hyperliquid_bot.utils.adaptive_threshold import AdaptiveThreshold
    
    # Calculate window length (1 sample per second)
    window_sec = getattr(self.cfg_gms, 'percentile_window_sec', 86400)
    window_len = window_sec  # 86,400 samples
    
    # Get percentile settings
    vol_low_pct = getattr(self.cfg_gms, 'vol_low_pct', 0.15)
    vol_high_pct = getattr(self.cfg_gms, 'vol_high_pct', 0.50)
    mom_low_pct = getattr(self.cfg_gms, 'mom_low_pct', 0.15)
    mom_high_pct = getattr(self.cfg_gms, 'mom_high_pct', 0.50)
    
    # Create adaptive threshold instances
    self.adaptive_vol_threshold = AdaptiveThreshold(vol_low_pct, vol_high_pct, window_len)
    self.adaptive_mom_threshold = AdaptiveThreshold(mom_low_pct, mom_high_pct, window_len)
```

### Update Method Bottleneck Analysis

**AdaptiveThreshold.update() Implementation**:
```python
def update(self, value: float) -> Tuple[Optional[float], Optional[float]]:
    # STEP 1: Convert buffer to numpy array (O(n) complexity)
    arr = np.fromiter(self.buffer, dtype=float)
    valid_arr = arr[~np.isnan(arr)]
    
    if len(valid_arr) == 0:
        self.buffer.append(value)
        return None, None
    
    # STEP 2: BOTTLENECK - Compute percentiles (O(n log n) complexity)
    try:
        low_thresh = np.percentile(valid_arr, self.low_pct * 100)   # 0.1st percentile
        high_thresh = np.percentile(valid_arr, self.high_pct * 100) # 50th percentile
    except Exception as e:
        self.logger.warning(f"Error computing percentiles: {e}")
        self.buffer.append(value)
        return None, None
    
    # STEP 3: Add new value to buffer (O(1) complexity)
    self.buffer.append(value)
    
    return low_thresh, high_thresh
```

### Performance Complexity Analysis

**Per-Update Complexity**:
- **Buffer Conversion**: O(n) where n = current buffer size
- **Percentile Calculation**: O(n log n) - **PRIMARY BOTTLENECK**
- **Buffer Append**: O(1)
- **Total per Update**: O(n log n)

**Total Priming Complexity**:
- **Updates per Feature**: 86,400 (one per second for 24 hours)
- **Number of Features**: 2 (volatility + momentum)
- **Total Updates**: 172,800
- **Buffer Growth**: From 1 to 86,400 samples
- **Total Complexity**: O(n² log n) where n = 86,400
- **Estimated Operations**: ~7.5 billion percentile calculations

## Performance Impact Breakdown

### Profiling Correlation
The code analysis confirms the profiling results from the bottleneck analysis:

| Component | Time (seconds) | % of Total | Code Location |
|-----------|----------------|------------|---------------|
| `_prime_adaptive_thresholds` | 629.34s | 99.4% | gms_detector.py:1134-1200 |
| `np.percentile` calls | 625.12s | 98.7% | adaptive_threshold.py:75-76 |
| `_load_historical_features` | 2.66s | 0.4% | gms_detector.py:1201-1272 |

### Scaling Analysis
**Buffer Size Growth During Priming**:
- **Update 1**: 1 sample → O(1 × log(1)) = O(1)
- **Update 1,000**: 1,000 samples → O(1,000 × log(1,000)) ≈ O(10,000)
- **Update 10,000**: 10,000 samples → O(10,000 × log(10,000)) ≈ O(130,000)
- **Update 86,400**: 86,400 samples → O(86,400 × log(86,400)) ≈ O(1,400,000)

**Cumulative Complexity**:
```
Total = Σ(i × log(i)) for i = 1 to 86,400
      ≈ O(n² log n) where n = 86,400
      ≈ 7.5 billion operations
```

## Storage and Usage of Calculated Thresholds

### Threshold Storage
- **Location**: Stored within `AdaptiveThreshold` instances
- **Volatility Thresholds**: `self.adaptive_vol_threshold.buffer`
- **Momentum Thresholds**: `self.adaptive_mom_threshold.buffer`
- **Access Method**: Via `update()` method return values during runtime

### Runtime Usage
During regime detection, the thresholds are used as follows:
```python
# In regime detection logic
vol_metric = signals.get('atr_percent_sec')
if self.adaptive_vol_threshold:
    low_thresh, high_thresh = self.adaptive_vol_threshold.update(vol_metric)
    # Use thresholds for regime classification
    if vol_metric < low_thresh:
        # Low volatility regime
    elif vol_metric > high_thresh:
        # High volatility regime
```

## Root Cause Summary

### Primary Issues
1. **Inefficient Algorithm**: O(n log n) percentile calculation repeated 172,800 times
2. **Growing Buffer Size**: Percentile complexity increases as buffer grows to 86,400 samples
3. **Redundant Calculations**: Full percentile recalculation for each new sample
4. **Memory Allocation**: Repeated numpy array creation from deque buffer

### Secondary Issues
1. **Large Priming Window**: 24-hour window creates 86,400 samples per feature
2. **Multiple Features**: 2 features double the computational load
3. **No Caching**: No reuse of previously calculated percentiles

## Optimization Opportunities

### Immediate Fixes (Configuration-based)
1. **Reduce Priming Window**: `priming_hours: 4` (6x speedup)
2. **Increase Sampling Rate**: Process every 60th second (60x speedup)
3. **Reduce Window Size**: `percentile_window_sec: 14400` (6x speedup)

### Algorithmic Improvements
1. **Incremental Percentiles**: Use sorted buffer with binary search (O(log n) per update)
2. **Approximate Percentiles**: Use streaming algorithms (O(1) per update)
3. **Cached Calculations**: Reuse percentiles when buffer hasn't changed significantly

### Expected Performance Gains
- **Configuration Changes**: 360x speedup (629s → 1.7s)
- **Algorithmic Improvements**: Additional 100-1000x speedup
- **Combined Approach**: Target < 30 seconds total initialization time

## Conclusion

The code analysis confirms that the `_prime_adaptive_thresholds` method creates a severe O(n² log n) performance bottleneck due to repeated full percentile calculations on growing buffers. The 629-second initialization time makes the adaptive thresholds feature impractical for production use.

The bottleneck is precisely located in the `AdaptiveThreshold.update()` method's `np.percentile()` calls, which are executed 172,800 times during the 24-hour priming process. Immediate relief can be achieved through configuration changes, while long-term solutions require algorithmic improvements to the percentile calculation approach.

**Next Steps**:
1. Implement configuration-based quick fixes for immediate relief
2. Develop optimized percentile calculation algorithms
3. Add performance monitoring and validation
4. Consider hybrid approaches balancing performance and accuracy
