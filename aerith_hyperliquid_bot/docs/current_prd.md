# Product Requirements Document (PRD): Aerith Hyperliquid Bot Development (AI Collaboration)

**Version**: 2.5 (GMS Evaluation Complete, OOS Testing Successful)  
**Date**: 2025-05-17  
**Author/User**: jpegcollector  
**AI Collaborator**: <PERSON> (acting as Expert Quant Developer/Analyst)

## Project Goal

Develop a robust, near-institutional-grade trading bot framework for Hyperliquid, leveraging unique L2 snapshot data. Having established a solid data foundation by resampling L2 data into high-quality OHLCV bars (Step 0 Complete), successfully evaluated the Granular Microstructure (GMS) regime detector (Step 1 Complete), and validated its performance through out-of-sample testing (Step 1b Complete), the focus is now on final implementation of the optimized GMS configuration. The underlying goal remains achieving a risk-adjusted target ROI with acceptable drawdowns.

## 1. Overview

Initial efforts established a TF strategy. Simulation improvements fixed slippage and introduced Taker/ProbMaker modes. The project prioritized building a robust data foundation (Step 0), implementing scripts (`parse_l2_top5.py`, `resample_l2_to_ohlcv.py`) to process raw L2 snapshots into high-quality, volume-less OHLC bars (plus log return, realized volatility) for 1h and 4h timeframes, stored in `hyperliquid_data/resampled_l2/`. This L2-derived data source has been successfully integrated.

Subsequently, a comprehensive grid search evaluation of the GMS detector (Step 1) was conducted, yielding excellent results with the top configuration exhibiting a Sharpe ratio of 4.61, profit factor of 2.57, and maximum drawdown of only -7.53%. Out-of-sample testing on 2025 data conclusively demonstrated that fixed thresholds significantly outperform percentile-based ones, with the optimal configuration achieving a Sharpe ratio of 3.13, profit factor of 1.79, and maximum drawdown of only 5.47% in OOS testing. These strong results eliminate the need for the HMM alternative approach (Step 2).

Core execution bugs (PnL/fee calculation, component interactions) have been fixed, and the Market Bias System has been thoroughly tested and documented.

## 2. Goals & Objectives

- **G1: Robust Framework & Simulation (Status: COMPLETE)**
    - Solid framework components functional.
    - Execution simulation improved (Taker/ProbMaker modes functional).
    - L2->OHLCV resampling pipeline implemented, tested, and integrated. Framework supports 1h/4h timeframes and volume-less OHLCV data.

- **G2: Achieve Target ROI via Robust Strategy (Status: COMPLETE with OOS Validation)**
    - ✅ Completed Objective (Step 1 - GMS Eval): Systematically tested different GMS parameter sets on L2-derived data. Identified optimal configurations that significantly exceed target criteria (PF ≥ 1.15, DD ≤ 20%).
    - ✅ Completed Objective (Step 1b - OOS Testing): Performed thorough out-of-sample testing on 2025 data (4 months), conclusively demonstrating that fixed thresholds outperform percentile-based ones.
    - ✅ Achieved Superior OOS Performance: Sharpe: 3.13, PF: 1.79, DD: 5.47%, ROI: 30.40%.
    - ⛔ Cancelled Objective (Step 2 - HMM): HMM implementation is no longer necessary given the strong OOS performance of the optimized GMS configuration.

- **G3: DEX Alpha Exploitation (Status: COMPLETE with Validated Configuration)**
    - ✅ Completed (Step 0): Generate high-quality OHLCV from L2 data.
    - ✅ Completed (Step 1): Deep evaluation of GMS detector using L2-derived data.
    - ✅ Completed (Step 1b): Out-of-sample validation showing fixed thresholds are superior to percentile-based approaches.
    - ⛔ Cancelled (Step 2): HMM implementation not required.
    - ⏱️ Deferred: MCP filter analysis (Sentiment/Whale) for future enhancement.

- **G4: Modular & Testable Framework (Status: COMPLETE)**
    - ✅ Config override system implemented.
    - ✅ GMS Detector (`detector.py`) enhanced to support new parameters and conditional logic.
    - ✅ `SignalEngine` (`calculator.py`) enhanced to calculate required features.
    - ✅ 3-state mapping toggle implemented.
    - ✅ Market Bias System thoroughly tested and documented.

- **G5: Future AI/ML Integration (Status: GMS SUCCESS - PARTIALLY COMPLETE)**
    - ✅ Clustering analysis performed to generate data-driven 3-state GMS mapping.
    - ✅ Percentile-based adaptive thresholds implemented as a more sophisticated approach.
    - ⛔ HMM implementation cancelled due to GMS success.
    - ⏱️ LSTM model remains deferred.

- **G6: Live Trading Readiness (Status: Framework Hardening IN PROGRESS)**
    - Market Bias System thoroughly tested.
    - Additional framework components to be hardened based on out-of-sample testing results.

- **G7: Critical AI Collaboration (Status: Ongoing)**

## 3. Target User & AI Role

- **User**: jpegcollector (Project Lead, Domain Expert)
- **AI Collaborator**: Claude (Acting as Expert Quant Developer/Analyst, also Coder)

## 4. Scope (Current Roadmap - Revised Steps)

- **✅ Completed (Step 0 - L2 Resampling Foundation)**

- **✅ Completed (Step 1 - GMS Evaluation)**
    - ✅ Task: Executed systematic grid search of GMS parameters on L2-derived data.
    - ✅ Task: Identified optimal configurations (OBI Window: 8, OBI Threshold: 0.15, Spread Threshold: 0.000026, Confirmation Bars: 1).
    - ✅ Task: Further optimized with adaptive percentile-based configuration (OBI Smoothing: 10, OBI Threshold: 0.20, with percentile-based spread and volatility thresholds).
    - ✅ Task: Analyzed results showing excellent performance metrics (Sharpe: 4.61, PF: 2.57, DD: -7.53%, ROI: 221.41%).
    - ✅ Task: Decision: GMS configuration provides robust alpha worth pursuing further.

- **⛔ Cancelled (Step 2 - HMM Filter Test)**
    - Not required due to strong GMS performance.

- **🔜 In Progress (Step 1b - Extended Testing)**
    - **Task**: Perform thorough out-of-sample testing of the optimized adaptive GMS configuration on 2023 data.
    - **Task**: Fine-tune the configuration if necessary based on OOS results.
    - **Task**: Implement the final optimized configuration as the default in the production system.

- **⏱️ Deferred (Future Work)**
    - MCP Data Integration/Analysis (Sentiment, Whale).
    - Volume calculation/ingestion for OHLCV bars.
    - Advanced validation.
    - Using ProbMaker simulation + Slippage metric checks.
    - TF parameter optimization.
    - LSTM models.

## 5. Current Development State (As of PRD v2.5)

- **Phase**: Completed Step 1 (GMS Evaluation), proceeding to Extended Testing.

- **Completed**:
    - Step 0 (L2 Resampling Foundation).
    - Step 1 (GMS Evaluation) including grid search, optimal configuration identification, and adaptive optimization.
    - Market Bias System testing and documentation.
    - Core execution bugs fixed.

- **Current Status**: Ready to implement the optimized adaptive GMS configuration and perform thorough out-of-sample testing.

- **Next Task**: Implement the adaptive GMS configuration and test on 2023 out-of-sample data.

## 6. Agreed Development Roadmap (Revised Steps)

- **✅ Step 0: L2 Resampling Foundation (COMPLETE)**
- **✅ Step 1: GMS Evaluation & Optimization (COMPLETE)** - Systematically tested GMS configurations and developed an optimized adaptive configuration with excellent performance metrics.
- **⛔ Step 2: HMM Filter Test (CANCELLED)** - Not required due to GMS success.
- **🔜 Step 1b: Extended Testing (CURRENT)** - Implement optimized adaptive GMS configuration and perform thorough out-of-sample testing.
- **⏱️ Step 3+: Refinement & Production Readiness (FUTURE)** - Framework hardening, optimization, and preparation for live trading.

## 7. Key Technical Decisions & Data

- **Language/Libs**: Python 3.11+, Pandas, NumPy, Pydantic v2, PyYAML, PyArrow, Matplotlib, mplfinance, Pytest, Pandas-TA, JSON, httpx, scikit-learn, deepmerge.

- **Configuration**: 
  - `configs/base.yaml` + `configs/overrides/*.yaml`. 
  - Key Params: `timeframe`, `detector_type: 'granular_microstructure'`, `gms_use_three_state_mapping`
  - Optimal GMS Configuration (OOS Validated):
    ```yaml
    # Core OBI parameters
    microstructure:
      obi_smoothing_window: 8
      gms_obi_strong_confirm_thresh: 0.20

    # Fixed threshold settings (empirically superior)
    gms_tight_spread_fallback_percentile: null  # Disabled
    gms_spread_mean_thresh_mode: 'fixed'
    gms_spread_std_thresh_mode: 'fixed'
    gms_vol_thresh_mode: 'fixed'
    ```

- **Data Sources**:
    - Raw L2 Text -> Pre-processor (`parse_l2_top5.py`) -> Daily L2 Parquet (`raw2/`)
    - Daily L2 Parquet (`raw2/`) -> Resampler (`resample_l2_to_ohlcv.py`) -> L2-Derived OHLCV (`resampled_l2/`)
    - `DataHandler` uses `resampled_l2/` for OHLCV and `raw2/` for microstructure features.

- **Regime Detection**: Optimized GMS with adaptive percentile-based configuration.

- **Strategy Logic**: Baseline TF logic (e.g., 20/50 EMA).

- **Execution Simulation**: Taker-Only Mode.

## 8. Project File Structure & Key Data Formats

- **Root Project Directory**: `/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/`
    - `configs/`
        - `base.yaml`
        - `gms_state_mapping.yaml`
        - `overrides/`
    - `hyperliquid_bot/`
        - `core/detector.py` (Enhanced `GranularMicrostructureDetector`)
        - `signals/calculator.py` (Enhanced `SignalEngine`)
        - `strategies/evaluator.py` (Handles filtering)
        - (Other core components)
    - `logs/`
    - `plots/`
    - `scripts/`
    - `docs/`
        - `market_bias_system_guide.md`
        - `top_gms_configurations.md`
        - `adaptive_gms_configuration.md`
    - `grid_search_results/`
        - `grid_results_sorted_20250505_042217.csv`
    - `tests/`

- **External Data Directories**:
    - `btc_data_2024/`, `btc_data_2025/`
    - `hyperliquid_data/` (`raw2/`, `resampled_l2/`)

## 9. How to Use This PRD (For AI Collaboration)

- Reflects completed roadmap for Step 0 (L2 Resampling) and Step 1 (GMS Evaluation/Optimization).
- Note that GMS evaluation was successful, eliminating the need for HMM implementation (Step 2).
- The focus is now on implementing the optimized adaptive GMS configuration and conducting thorough out-of-sample testing.
- Use the revised roadmap (Section 6) and scope (Section 4) for current tasks.
- Refer to Deferred Items list in Section 4 for future work.

## 10. Message for Future AI Collaborator (Continuity Note - v2.5 Update)

- **Project Status**: Completed Step 0 (L2 resampling pipeline) and Step 1 (GMS evaluation/optimization). The GMS evaluation yielded excellent results, with the top configuration from the grid search showing a Sharpe ratio of 4.61, profit factor of 2.57, and maximum drawdown of only -7.53%. Further optimization revealed that an adaptive configuration using percentile-based thresholds with modified OBI parameters yields even better results. This success eliminates the need for the HMM approach (Step 2).

- **Current Roadmap (Revised Steps)**:
    - Step 1b (CURRENT): Implement the optimized adaptive GMS configuration and conduct thorough out-of-sample testing on 2023 data.
    - Step 3+ (FUTURE): Framework hardening, optimization, and preparation for live trading.

- **Immediate Next Task**: Implement the optimized adaptive GMS configuration and test on 2023 out-of-sample data.

- **AI Collaboration Guidance**: Assist with implementing the optimized adaptive GMS configuration, conducting out-of-sample testing, and analyzing results. Support framework hardening and preparation for live trading. Refer to PRD v2.5 and the detailed documentation in the docs directory.

Project: Aerith Hyperliquid Bot (AI Collab) - PRD v2.5 Context

Status: We have completed Step 0 (L2 data resampling and integration) and Step 1 (GMS evaluation/optimization). The GMS evaluation yielded excellent results, with the top configuration from the grid search showing impressive performance metrics. Further optimization revealed that an adaptive configuration using percentile-based thresholds with modified OBI parameters yields even better results. This success eliminates the need for the HMM approach (Step 2). The focus is now on implementing the optimized adaptive GMS configuration and conducting thorough out-of-sample testing.
