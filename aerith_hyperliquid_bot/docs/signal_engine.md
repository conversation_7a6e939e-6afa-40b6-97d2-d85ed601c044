# Signal Engine

The `SignalEngine` class, located in `hyperliquid_bot/signals/calculator.py`, is responsible for calculating various technical indicators and derived signals from input data that includes OHLCV and raw microstructure features.

## OBI Signal Processing

A key feature of the `SignalEngine` is its processing of Order Book Imbalance (OBI) signals. For every column in the input data that matches the pattern `raw_obi_*` (representing OBI calculated at different depths or with different weighting schemes), the engine can perform two main operations:

1.  **Smoothing:** Each `raw_obi_*` series can be smoothed to reduce noise and highlight underlying trends. This is controlled by the following configuration settings in `hyperliquid_bot/config/settings.py` under `MicrostructureSettings` (with defaults in `configs/base.yaml`):
    *   `obi_smoothing_window` (integer, default: `8`): The rolling window period used for smoothing.
    *   `obi_smoothing_type` (string, default: `"sma"`): The type of moving average to use for smoothing. Supported values are `"sma"` (Simple Moving Average) and `"ema"` (Exponential Moving Average).
    The smoothed signals are stored in new columns named `obi_smoothed_*`, where `*` corresponds to the suffix of the raw OBI column (e.g., `raw_obi_5` becomes `obi_smoothed_5`).

2.  **Z-Score Normalization:** The smoothed OBI signals (`obi_smoothed_*`) can be further processed by applying a rolling Z-score normalization. This transforms the signal to have a mean of approximately 0 and a standard deviation of approximately 1 over the lookback period, which can be useful for comparing signals with different scales or for certain types of strategy logic. This is controlled by:
    *   `obi_zscore_window` (integer, optional, default: `null`): The lookback period for calculating the rolling mean and standard deviation for the Z-score. If this value is `null` or not a positive integer greater than 1, Z-score normalization is skipped for all OBI signals.
    The Z-scored signals are stored in new columns named `obi_zscore_*` (e.g., `obi_smoothed_5` becomes `obi_zscore_5`).

This processing pipeline (raw OBI -> smoothed OBI -> Z-scored OBI) allows for flexible preparation of OBI-based signals for use in downstream trading strategies.