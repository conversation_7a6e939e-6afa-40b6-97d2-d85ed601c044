#!/usr/bin/env python3
"""
Test the fixed P&L tracking in modern backtester.
Run a short test to verify trades are properly tracked with actual returns.
"""

import subprocess
import sys
import json
from pathlib import Path

def main():
    print("Testing fixed P&L tracking...")
    print("-" * 80)
    
    # Run 1 week test
    cmd = [
        sys.executable,
        "scripts/run_modern_backtest.py",
        "--start-date", "2024-01-01",
        "--end-date", "2024-01-07",
        "--override", "configs/overrides/modern_system_v2_complete.yaml",
        "--output", "test_fixed_pnl.json"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print("Test failed!")
        print(result.stdout)
        print(result.stderr)
        return 1
    
    # Load and check results
    with open("test_fixed_pnl.json") as f:
        data = json.load(f)
    
    trades = data['trades']
    perf = data['performance']
    
    print(f"\nResults:")
    print(f"Total trades: {perf.get('total_trades', 0)}")
    print(f"Completed trades: {perf.get('completed_trades', 0)}")
    print(f"Total return: {perf.get('total_return', 0):.2%}")
    print(f"Win rate: {perf.get('win_rate', 0):.2%}")
    
    # Check first few trades
    print(f"\nFirst 3 trades:")
    for i, trade in enumerate(trades[:3]):
        if 'return' in trade:
            print(f"  Trade {i+1}: {trade['direction']} - Return: {trade['return']:.2%}")
        else:
            print(f"  Trade {i+1}: {trade['direction']} - OPEN (no exit)")
    
    # Verify trades have actual returns
    completed = [t for t in trades if 'return' in t]
    if completed:
        returns = [t['return'] for t in completed]
        print(f"\nReturn distribution:")
        print(f"  Best: {max(returns):.2%}")
        print(f"  Worst: {min(returns):.2%}")
        print(f"  Average: {sum(returns)/len(returns):.2%}")
    else:
        print("\nWARNING: No completed trades found!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())