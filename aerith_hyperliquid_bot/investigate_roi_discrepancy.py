#!/usr/bin/env python3
"""
Investigate ROI Discrepancy in Legacy System

This script analyzes why the legacy system is now producing:
- 198 trades with 248.08% ROI
Instead of the expected:
- 180 trades with 215% ROI (or 191 trades with 243% ROI)
"""

import logging
import pandas as pd
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)-7s] %(name)-20s: %(message)s'
)
logger = logging.getLogger(__name__)

def analyze_configuration():
    """Analyze configuration differences."""
    from hyperliquid_bot.config.settings import load_config
    
    # Load current config
    config = load_config('configs/base.yaml')
    
    print("\n=== Configuration Analysis ===")
    print(f"Detector Type: {config.regime.detector_type}")
    print(f"Use TF-v2: {config.strategies.use_tf_v2}")
    print(f"Risk Fraction: {config.tf_v3.risk_frac}")
    print(f"Use Filter: {config.regime.use_filter}")
    print(f"Map Weak Bear to Bear: {getattr(config.regime, 'map_weak_bear_to_bear', 'NOT SET')}")
    
    # Check thresholds
    print("\n=== Critical Thresholds ===")
    print(f"Vol High Thresh: {config.regime.gms_vol_high_thresh}")
    print(f"Vol Low Thresh: {config.regime.gms_vol_low_thresh}")
    print(f"Mom Strong Thresh: {config.regime.gms_mom_strong_thresh}")
    print(f"Mom Weak Thresh: {config.regime.gms_mom_weak_thresh}")
    
    # Check state mapping
    print("\n=== State Mapping Configuration ===")
    print(f"Use Three State Mapping: {config.regime.gms_use_three_state_mapping}")
    print(f"State Mapping File: {config.regime.gms_state_mapping_file}")
    
    # Check if state mapping file exists
    state_map_path = Path("configs") / config.regime.gms_state_mapping_file.split('/')[-1]
    print(f"State Mapping File Exists: {state_map_path.exists()}")
    
    if state_map_path.exists():
        import yaml
        with open(state_map_path, 'r') as f:
            state_map = yaml.safe_load(f)
        print("\nState Mappings:")
        for gms_state, mapped_state in state_map.get('state_map', {}).items():
            print(f"  {gms_state} → {mapped_state}")

def check_data_period():
    """Check the data period being used."""
    from hyperliquid_bot.config.settings import load_config
    
    config = load_config('configs/base.yaml')
    
    print("\n=== Data Period Configuration ===")
    print(f"Period Preset: {config.backtest.period_preset}")
    print(f"Custom Start: {config.backtest.custom_start_date}")
    print(f"Custom End: {config.backtest.custom_end_date}")
    
    # Determine actual period
    if config.backtest.period_preset == '2024':
        print("\nUsing full year 2024 (Jan 1 - Dec 31)")
    elif config.backtest.period_preset == 'custom':
        print(f"\nUsing custom period: {config.backtest.custom_start_date} to {config.backtest.custom_end_date}")

def check_force_close_logic():
    """Check if force close is affecting results."""
    print("\n=== Force Close Analysis ===")
    print("The backtester has force-close logic at line 1432-1469")
    print("It checks if portfolio.position exists and closes it at final price")
    print("This should add at most 1 extra trade")
    
def analyze_recent_logs():
    """Analyze recent backtest logs for clues."""
    import glob
    
    print("\n=== Recent Log Analysis ===")
    
    # Find recent logs
    log_pattern = "/Users/<USER>/Desktop/trading_bot_/logs/backtest_run_*.log"
    recent_logs = sorted(glob.glob(log_pattern), key=lambda x: Path(x).stat().st_mtime, reverse=True)[:5]
    
    for log_file in recent_logs:
        # Check if it's a legacy run
        with open(log_file, 'r') as f:
            content = f.read()
            
        if 'detector_type: granular_microstructure' in content and 'Total Trades:' in content:
            # Extract key metrics
            trades_match = re.search(r'Total Trades:\s*(\d+)', content)
            roi_match = re.search(r'Return on Initial \(ROI\):\s*([\d.]+)%', content)
            
            if trades_match and roi_match:
                trades = int(trades_match.group(1))
                roi = float(roi_match.group(1))
                print(f"\nLog: {Path(log_file).name}")
                print(f"  Trades: {trades}")
                print(f"  ROI: {roi}%")
                
                # Check for any config warnings
                if 'CONFIG FALLBACK' in content:
                    print("  ⚠️  Config fallback detected!")

def main():
    """Run all analyses."""
    print("=== ROI Discrepancy Investigation ===")
    print("Expected: 180 trades, 215% ROI (or 191 trades, 243% ROI)")
    print("Actual: 198 trades, 248.08% ROI")
    
    analyze_configuration()
    check_data_period()
    check_force_close_logic()
    
    print("\n=== Hypothesis ===")
    print("1. The 191 → 198 trade increase (+7 trades) suggests:")
    print("   - Possible change in regime detection thresholds")
    print("   - Different state mapping configuration")
    print("   - Data period might be slightly different")
    print("\n2. The 243% → 248% ROI increase is proportionally small")
    print("   - This could be normal variance")
    print("   - Or a result of the extra trades being profitable")
    
    print("\n=== Recommendations ===")
    print("1. Check if configs/gms_state_mapping.yaml has been modified")
    print("2. Verify the exact data period being used")
    print("3. Run with --override configs/overrides/legacy_system.yaml to ensure all settings are applied")
    print("4. Compare detailed trade logs between runs")

if __name__ == "__main__":
    import re
    main()