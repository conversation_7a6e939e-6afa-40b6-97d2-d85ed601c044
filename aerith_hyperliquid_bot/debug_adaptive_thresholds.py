#!/usr/bin/env python3
"""
Debug script to investigate why adaptive thresholds are returning None
"""
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.utils.adaptive_threshold import AdaptiveThreshold

def debug_adaptive_thresholds():
    """Debug the adaptive threshold calculation"""
    print("=== Debugging Adaptive Threshold Calculation ===")

    # Load config
    config = load_config('configs/base.yaml')

    print(f"Config loaded. auto_thresholds: {config.gms.auto_thresholds}")
    print(f"vol_low_pct: {config.gms.vol_low_pct}")
    print(f"mom_low_pct: {config.gms.mom_low_pct}")
    print(f"min_history_rows: {config.gms.min_history_rows}")
    print(f"percentile_window_sec: {config.gms.percentile_window_sec}")

    # Create adaptive threshold instances
    window_len = config.gms.percentile_window_sec  # 86400 samples for 24 hours

    vol_threshold = AdaptiveThreshold(
        low_pct=config.gms.vol_low_pct,
        high_pct=config.gms.vol_high_pct,
        window_len=window_len
    )

    mom_threshold = AdaptiveThreshold(
        low_pct=config.gms.mom_low_pct,
        high_pct=config.gms.mom_high_pct,
        window_len=window_len
    )

    print(f"\nAdaptive thresholds created:")
    print(f"Vol threshold: low_pct={vol_threshold.low_pct}, high_pct={vol_threshold.high_pct}")
    print(f"Mom threshold: low_pct={mom_threshold.low_pct}, high_pct={mom_threshold.high_pct}")

    # Create some test data
    print(f"\n=== Testing with synthetic data ===")

    # Create timestamps for the last 24 hours
    end_time = datetime(2025, 3, 2, 12, 0, 0)  # Middle of our test period
    start_time = end_time - timedelta(seconds=config.gms.percentile_window_sec)

    # Create 1-second data for 24 hours
    timestamps = pd.date_range(start_time, end_time, freq='1S')
    n_points = len(timestamps)

    print(f"Created {n_points} data points from {start_time} to {end_time}")

    # Generate synthetic volatility and momentum data
    np.random.seed(42)
    vol_data = np.random.lognormal(mean=-3, sigma=0.5, size=n_points)  # ATR-like data
    mom_data = np.random.normal(loc=0, scale=1, size=n_points)  # Momentum-like data

    print(f"Vol data range: {vol_data.min():.6f} to {vol_data.max():.6f}")
    print(f"Mom data range: {mom_data.min():.6f} to {mom_data.max():.6f}")

    # Test adaptive threshold calculation
    print(f"\n=== Testing adaptive threshold calculation ===")

    # Test by feeding data sequentially (as the detector would do)
    print(f"Testing sequential updates...")

    # Feed first 1000 points to build up buffer
    for i in range(min(1000, len(vol_data))):
        vol_low, vol_high = vol_threshold.update(vol_data[i])
        mom_low, mom_high = mom_threshold.update(mom_data[i])

        if i % 200 == 0:  # Print every 200 updates
            print(f"  Update {i}: Vol buffer size={vol_threshold.get_buffer_size()}, thresholds=({vol_low}, {vol_high})")
            print(f"  Update {i}: Mom buffer size={mom_threshold.get_buffer_size()}, thresholds=({mom_low}, {mom_high})")

    # Test final thresholds
    print(f"\nFinal thresholds after {min(1000, len(vol_data))} updates:")
    print(f"Vol: low={vol_low}, high={vol_high}")
    print(f"Mom: low={mom_low}, high={mom_high}")

    # Test buffer stats
    vol_stats = vol_threshold.get_buffer_stats()
    mom_stats = mom_threshold.get_buffer_stats()
    print(f"\nBuffer stats:")
    print(f"Vol: {vol_stats}")
    print(f"Mom: {mom_stats}")

if __name__ == "__main__":
    debug_adaptive_thresholds()
