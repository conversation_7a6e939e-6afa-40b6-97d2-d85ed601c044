#!/usr/bin/env python3
"""
Test the enhanced modern system for a full month (February 2024).
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import logging
from datetime import datetime

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtest_engine import RobustBacktestEngine

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_feb_2024_test.log'),
        logging.StreamHandler()
    ]
)

def main():
    """Run full month test."""
    print("\n" + "="*80)
    print("ENHANCED MODERN SYSTEM - FULL FEBRUARY 2024 TEST")
    print("="*80)
    
    # Load config
    config_path = Path(__file__).parent / "configs/overrides/modern_system_v2_adjusted_thresholds.yaml"
    config = load_config(str(config_path))
    
    # Show configuration
    print("\nConfiguration Summary:")
    print(f"  Detector: enhanced (with relaxed Weak trend trading)")
    print(f"  Quality threshold: 0.45")
    print(f"  Spread thresholds: {config.regime.gms_spread_mean_low_thresh} / {config.regime.gms_spread_std_high_thresh}")
    print(f"  Momentum thresholds: {config.regime.gms_mom_weak_thresh} / {config.regime.gms_mom_strong_thresh}")
    print(f"  Allowed regimes: Strong & Weak trends (Bull/Bear)")
    
    # Full month test
    start_date = datetime(2024, 2, 1)
    end_date = datetime(2024, 2, 29, 23, 59, 59)
    
    print(f"\nTest Period: {start_date.date()} to {end_date.date()} (full month)")
    print("-" * 80)
    
    try:
        # Create engine
        engine = RobustBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date,
            use_regime_cache=False,
            strict=False
        )
        
        print("\n✅ Engine initialized successfully")
        
        # Run the backtest
        print("\nRunning backtest...")
        results = engine.run_backtest()
        
        print("\n✅ Backtest completed")
        
        # Analyze results
        print("\n" + "="*80)
        print("RESULTS:")
        print("="*80)
        
        print(f"\nTrade Statistics:")
        print(f"  Total Trades: {results.get('total_trades', 0)}")
        print(f"  Winning Trades: {results.get('winning_trades', 0)}")
        print(f"  Losing Trades: {results.get('losing_trades', 0)}")
        print(f"  Win Rate: {results.get('win_rate', 0):.1%}")
        
        print(f"\nPerformance:")
        print(f"  Total Return: {results.get('total_return', 0):.2%}")
        print(f"  Average Return per Trade: {results.get('average_return', 0):.2%}")
        print(f"  Sharpe Ratio: {results.get('sharpe_ratio', 0):.2f}")
        print(f"  Max Drawdown: {results.get('max_drawdown', 0):.2%}")
        
        # Show some trades
        if results.get('total_trades', 0) > 0:
            trades = results.get('trades', [])
            print(f"\nFirst 5 Trades:")
            for i, trade in enumerate(trades[:5]):
                print(f"\n  Trade {i+1}:")
                print(f"    Entry: {trade.get('entry_time')} at ${trade.get('entry_price', 0):.2f}")
                print(f"    Direction: {trade.get('direction', 'unknown')}")
                print(f"    Regime: {trade.get('entry_regime', 'unknown')}")
                if trade.get('status') == 'closed':
                    print(f"    Exit: {trade.get('exit_time')} at ${trade.get('exit_price', 0):.2f}")
                    print(f"    P&L: {trade.get('pnl_pct', 0):.2%}")
        
        # Summary
        print("\n" + "="*80)
        print("PERFORMANCE COMPARISON:")
        print("="*80)
        
        print(f"\nEnhanced Modern System (Feb 2024):")
        print(f"  Trades: {results.get('total_trades', 0)}")
        print(f"  Return: {results.get('total_return', 0):.2%}")
        print(f"  Trades/month: {results.get('total_trades', 0)}")
        
        print(f"\nTarget (Legacy System 2024 average):")
        print(f"  Trades: ~15/month")
        print(f"  Return: ~18%/month")
        
        if results.get('total_return', 0) > 0:
            print("\n✅ System is generating profitable trades!")
            print("   Next steps: Test on more months and optimize parameters")
        else:
            print("\n⚠️ System needs further optimization")
        
    except Exception as e:
        print(f"\n❌ ERROR: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()