--- START OF FILE roadmap.md ---
roadmap.md
Aerith Hyperliquid Bot - Development Roadmap (v2.1 Base)
This roadmap outlines the planned development phases, tasks, deliverables, and decision gates based on PRD v2.1.
Overall Schedule Estimate: 13 Elapsed Weeks

Phase 3.1  ▶  Phase 3.2 ─┐     Phase 4           Phase 5           Phase 6
(MCP+Filter) (HMM Test)   |  (OBI Scalper)    (OFI & Liq α)    (Go-live Polish)
Days 1‑5     Days 6‑10    |  Wk 3 – Wk 6      Wk 7 – Wk 9      Wk 10 – Wk 13
Decision gate▲────────────┘

---

## PHASE 3.1 — External Data Overlay (Days 1‑5)

*   **Goal:** Integrate external sentiment/flow data and assess correlation with TF strategy performance.
*   **Dependencies:** Baseline TF backtest framework, `requests`/`httpx`.

| Day | Task | Deliverable | "Done" Metric |
|-----|------|-------------|---------------|
| 1   | **Ingest** Fear & Greed (Alternative.me API) hourly → Add `fear_greed_idx` column. | `data/providers/feargreed.py`, Integrated into DataHandler/SignalCalculator, Column present in output parquet. | ≤ 0.5 % missing data. |
| 2   | **Ingest** CryptoSentiment (Source TBD API) hourly → Add `sent_score` column. | `data/providers/sentiment.py`, Integrated, Column present. | ≤ 0.5 % missing data. |
| 3   | **Ingest** WhaleTracker (Source TBD API) hourly → Add `whale_buy`/`whale_sell` columns. | `data/providers/whaletracker.py`, Integrated, Column present. | Spike decoding matches source examples. |
| 4   | **Visualize** Overlays: Update `visualize_backtest.py` to plot new features. | Plot script updated, PNG in `/plots/phase3.1/`. | Visual inspection: Spikes align with price swings. |
| 5   | **Stats:** Correlate `fear_greed_idx`, `sent_score`, `whale_net` vs. TF trade P/L (lag 0, +1h). | Jupyter Notebook / Analysis script output (CSV/Table). | Candidate Features (|ρ|>0.2 & AUROC>0.55) flagged. |

*   **Exit Criteria:** Candidate features identified based on **Promotion rule: |ρ| > 0.20 AND AUROC > 0.55 (lag 0 or +1 h).**

---

## PHASE 3.2 — Minimal 3-state HMM Filter Test (Days 6‑10)

*   **Goal:** Test if a simple HMM filter improves TF strategy robustness.
*   **Dependencies:** Phase 3.1 completion, `scikit-learn`.

| Day | Task | Deliverable | "Done" Metric |
|-----|------|-------------|---------------|
| 6   | Implement `features/hmm_features.py` using log-return, realized vol, (+ candidate features from 3.1). | `hmm_features.py` unit-tested. | Runtime < 300 ms for 1 yr hourly data. |
| 7   | Train `GaussianHMM(n_components=3)` on 1H 2024 data (e.g., train Jan-Jun, predict Jul-Dec). | Saved HMM model (`models/regime_filters/`), `hmm_states.parquet`. | Converges in < 200 EM steps. |
| 8   | Auto-label HMM states based on mean return (`bull`/`bear`/`chop`). **Freeze mapping after first fit.** | State mapping saved (`hmm_meta.json`). | Label gap ≥ 0.5 σ between bull & bear. |
| 9   | Wire HMM state into Backtester/Evaluator as a trade filter for TF strategy (`skip trade if HMM state == 'chop'`). | Backtest logic updated. | Backtest runs; trade count drops ≥ 25%. |
| 10  | **Validation Report:** Compare Filtered TF vs. Vanilla TF (PF, Sharpe, MaxDD using **Taker-Only Simulation**). | Markdown report in `/logs/`. | Table + Go/No-Go recommendation committed. |

*   **Decision Gate:**
    *   **KEEP Filtered TF IF:** Profit Factor ≥ 1.15 **AND** Max DD ≤ 20% **AND** Trade Count ≤ ~80/year (Taker Sim). Archive GMS.
    *   **ELSE:** Archive Filtered TF & GMS. Proceed with bare TF only as a benchmark if needed.

---

## PHASE 4 — OBI Scalper MVP (Weeks 3 – 6 - *Contingent on Phase 3.2 Decision = Pivot*)

*   **Goal:** Develop and validate a baseline Order Book Imbalance scalping strategy.
*   **Dependencies:** Phase 3.2 Decision=Pivot, Tick Data Source.

| Week | Task | Deliverable | Success Criteria |
|------|------|-------------|------------------|
| 3    | Simplify Config (Remove GMS, MR, MV, Hurst params). | `config.yaml` vNext | Config simplified. |
| 3    | Setup Tick Data Ingestion (e.g., Dexscreener WS) → Save tick Parquet. | Data ingest script, `/data/ticks/` populated. | CPU < 50%, Gaps < 2 min. |
| 3    | Calculate OBI, OFI, Spread from tick data in `features/microstructure.py`. | `obi`, `ofi` columns available. | Matches reference calcs ±3%. |
| 4    | Implement `strategies/obi_scalper.py`: Enter OBI > 0.2, Exit flat/timeout. Taker-Only. | Strategy code + pytest. | Coding complete. |
| 4    | Backtest OBI Scalper on 2024 Tick Data (Taker Sim). | Backtest results JSON/Report. | **Sharpe ≥ 1.2**, Hit Rate ≥ 45%. |
| 5    | Paper Trade OBI Scalper (e.g., via external framework + adapter). | Live logs, DB records. | Realized Slippage ≤ Sim + 1 bp. |
| 6    | OOS Tuning: Grid search OBI threshold, timeout (e.g., on Jan-Mar 2025 data). | Heatmap plot (`grid_heatmap.png`). | **Sharpe ≥ 1.2** plateau region found. |

*   **Exit Criteria:** Freeze OBI work if Sharpe < 1.2 and no clear tuning plateau exists.

---

## PHASE 5 — Flow Enrichment & Liquidation Alpha (Weeks 7 – 9 - *Contingent*)

*   **Goal:** Enhance OBI with OFI and add Liquidation Reversion strategy.
*   **Dependencies:** Phase 4 success, External data sources for OFI/Liq.

| Week | Task | Deliverable |
|------|------|-------------|
| 7    | Integrate external data for refined OFI calculation. | `ofi` column updated. |
| 7    | (Optional) Backtest OFI filter on OBI strategy. | Backtest results. |
| 8    | Integrate Liquidation event data. | `liq_spike` feature. |
| 8    | Implement `strategies/liq_strat.py`: Fade large liquidations. | Strategy code. |
| 9    | Combine OBI (+OFI) + Liq strategies (e.g., risk parity sizing) in paper account. | Live logs, weekly recap. |

*   **Exit Criteria:** Combined Sharpe ≥ 1.4, VaR (95%) ≤ 10% equity.

---

## PHASE 6 — Execution & Risk Hardening (Weeks 10 – 13 - *Contingent*)

*   **Goal:** Prepare system for potential live deployment.
*   **Dependencies:** Phase 5 success.

| Week | Task | Deliverable |
|------|------|-------------|
| 10   | Config Cleanup (final state). | `config.yaml` vFinal |
| 11   | (Optional) Custom Async Execution Router. | Router code. |
| 11   | Add Latency/Slippage Monitoring & Alerting. | Monitoring code, DB tables. |
| 12   | Implement portfolio-level Risk Controls (VaR cap, kill-switch). | `core/risk.py` updated. |
| 13   | Dry-run 30-day live simulation parallel to paper. | Go/No-Go Report for live capital. |

*   **Exit Criteria:** System meets live readiness criteria (e.g., Stability, Monitoring, Risk Controls, Live vs Sim P/L ≥ 80%).

---
## Deferred Items
*   Advanced Simulation (Price-Touch, Delay Cost)
*   Kelly Criterion Sizing # for now
*   LSTM Predictors
*   HMM for portfolio construction
*   Other MCP integrations (snipers, routers)
*   TF Parameter Optimization (beyond baseline 8/128)

--- END OF FILE roadmap.md ---