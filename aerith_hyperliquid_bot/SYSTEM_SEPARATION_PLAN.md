# Minimal System Separation Plan

## Design Philosophy (Elite Algotrading Approach)

### Core Principles
1. **Explicit Over Implicit**: Force system selection at entry point
2. **Configuration Over Code**: Use config routing, not code duplication  
3. **Fail Loud**: No silent fallbacks between systems
4. **Production First**: Default to legacy if not specified

### Why This Matters in Algotrading
- **Risk Management**: Can't accidentally trade with experimental system
- **A/B Testing**: Clean comparison between systems
- **Compliance**: Clear audit trail of which system made trades
- **Performance**: No runtime overhead checking which system to use

## Implementation Plan (Minimal Effort, Maximum Safety)

### Step 1: Entry Point Separation
```python
# run_backtest.py
parser.add_argument('--system', choices=['legacy', 'modern'], default='legacy',
                   help='Trading system to use (default: legacy)')
```

### Step 2: Configuration Router
```python
# config_router.py
def get_system_config(system: str, override: Optional[str] = None):
    if system == 'legacy':
        # Force legacy detector and strategy
        config = load_config('base.yaml', override)
        config.regime.detector_type = 'granular_microstructure'
        config.strategies.use_tf_v2 = True
        config.strategies.use_tf_v3 = False
        return config
    else:  # modern
        # Force modern detector and strategy
        config = load_config('base.yaml', override)
        config.regime.detector_type = 'continuous_gms'
        config.strategies.use_tf_v2 = False
        config.strategies.use_tf_v3 = True
        return config
```

### Step 3: System Context Injection
```python
# backtester.py
class Backtester:
    def __init__(self, config, system_mode='legacy'):
        self.system_mode = system_mode
        # Pass system context to all components
```

### Step 4: Strategy-Aware Signal Access
```python
# In strategies
if self.system_mode == 'legacy':
    regime = signals['regime']  # Original
else:
    regime = signals.get('regime_mapped', signals['regime'])  # Mapped
```

## Benefits of This Approach

1. **Zero Code Duplication**: Shared components remain shared
2. **Explicit System Selection**: Can't accidentally mix systems
3. **Easy Testing**: `--system legacy` vs `--system modern`
4. **Safe Development**: Modern changes can't break legacy
5. **Clear Audit Trail**: Logs show which system is running

## Usage Examples

```bash
# Production (default to legacy)
python -m hyperliquid_bot.backtester.run_backtest

# Test modern system
python -m hyperliquid_bot.backtester.run_backtest --system modern

# Modern with override
python -m hyperliquid_bot.backtester.run_backtest --system modern --override configs/overrides/execution_refinement_enabled.yaml
```

## Alternative Considered (Rejected)

### Full Code Duplication
- ❌ Maintenance nightmare
- ❌ Bug fixes need to be applied twice
- ❌ Divergence over time

### Complex Inheritance Hierarchy
- ❌ Over-engineering
- ❌ Hard to understand flow
- ❌ Performance overhead

### Environment Variables
- ❌ Too implicit
- ❌ Easy to forget which mode you're in
- ❌ Not visible in command history

## Why This is the Elite Solution

1. **Simplicity**: One flag controls everything
2. **Safety**: Can't accidentally trade with wrong system
3. **Clarity**: Command line shows intent
4. **Flexibility**: Easy to add more systems later
5. **Testability**: CI/CD can test both systems separately

This is how professional trading firms separate strategies - explicit, auditable, and fail-safe.