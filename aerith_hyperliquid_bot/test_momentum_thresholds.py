#!/usr/bin/env python3
"""
Test script to simulate different momentum threshold scenarios
"""

import re
import numpy as np

def test_threshold_scenarios(log_file):
    """Test different momentum threshold scenarios."""
    
    # Extract all ma_slope values from log
    ma_slope_values = []
    with open(log_file, 'r') as f:
        for line in f:
            if 'ma_slope:' in line:
                match = re.search(r'ma_slope: ([0-9\.-]+)', line)
                if match:
                    ma_slope_values.append(float(match.group(1)))
    
    if not ma_slope_values:
        print("No ma_slope values found in log file")
        return
    
    abs_values = np.abs(ma_slope_values)
    
    print("=== MOMENTUM THRESHOLD SCENARIO TESTING ===")
    print(f"Total periods: {len(abs_values)}")
    print()
    
    # Test different threshold scenarios
    scenarios = [
        {"name": "Current (too restrictive)", "strong": 0.2, "weak": 0.05},
        {"name": "Recommended Conservative", "strong": 0.15, "weak": 0.01},
        {"name": "Recommended Balanced", "strong": 0.17, "weak": 0.0},
        {"name": "More Aggressive", "strong": 0.12, "weak": 0.01},
        {"name": "Legacy-like", "strong": 0.10, "weak": 0.02}
    ]
    
    for scenario in scenarios:
        strong_thresh = scenario["strong"]
        weak_thresh = scenario["weak"]
        
        strong_count = np.sum(abs_values >= strong_thresh)
        weak_count = np.sum(abs_values <= weak_thresh)
        medium_count = len(abs_values) - strong_count - weak_count
        
        strong_pct = strong_count / len(abs_values) * 100
        medium_pct = medium_count / len(abs_values) * 100
        weak_pct = weak_count / len(abs_values) * 100
        
        print(f"📊 {scenario['name']}:")
        print(f"   Strong >= {strong_thresh}: {strong_count:2d} periods ({strong_pct:5.1f}%)")
        print(f"   Medium:           {medium_count:2d} periods ({medium_pct:5.1f}%)")
        print(f"   Weak   <= {weak_thresh}: {weak_count:2d} periods ({weak_pct:5.1f}%)")
        
        # Estimate trade potential based on strong periods
        # Assuming ~4-5 trades per strong period (rough estimate)
        estimated_trades = strong_count * 4
        print(f"   Estimated trades: ~{estimated_trades} (target: 160-180)")
        
        # Assessment
        if strong_pct < 10:
            assessment = "❌ Too restrictive - insufficient trending periods"
        elif strong_pct > 30:
            assessment = "⚠️  Very aggressive - may overtrade"
        elif 15 <= strong_pct <= 25:
            assessment = "✅ Good balance - likely to hit target"
        else:
            assessment = "⚠️  Moderate - may work but needs testing"
        
        print(f"   Assessment: {assessment}")
        print()
    
    # Special analysis for zeros
    zero_count = np.sum(abs_values == 0.0)
    print(f"📈 Zero momentum periods: {zero_count} ({zero_count/len(abs_values)*100:.1f}%)")
    print("   These are likely from initial periods with NaN fills")
    print()
    
    # Percentile reference
    print("📊 Momentum percentiles for reference:")
    percentiles = [10, 20, 30, 40, 50, 60, 70, 80, 90, 95]
    for p in percentiles:
        value = np.percentile(abs_values, p)
        print(f"   {p:2d}%: {value:.4f}")

if __name__ == "__main__":
    log_file = "/Users/<USER>/Desktop/trading_bot_/logs/backtest_run_execution_refinement_20250715_183455.log"
    test_threshold_scenarios(log_file)