=== MODERN SYSTEM PERFORMANCE PROFILE REPORT ===
Generated: 2025-05-30 17:42:49.738222
Date Range: 2025-03-02 to 2025-03-05 (4 days)
System: Continuous GMS + TF-v3 Strategy

         135509559 function calls (129202719 primitive calls) in 666.562 seconds

   Ordered by: cumulative time
   List reduced from 10366 to 50 due to restriction <50>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.000    0.000  663.446  663.446 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/run_backtest.py:179(main)
        1    0.001    0.001  655.491  655.491 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:39(__init__)
        2    0.000    0.000  655.370  327.685 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/detector.py:913(get_regime_detector)
        2    0.018    0.009  655.369  327.684 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py:74(__init__)
        2    5.262    2.631  655.346  327.673 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py:1134(_prime_adaptive_thresholds)
   328926   23.412    0.000  630.221    0.002 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/utils/adaptive_threshold.py:48(update)
   657844    0.513    0.000  391.795    0.001 <__array_function__ internals>:177(percentile)
6004244/711259    4.939    0.000  391.628    0.001 {built-in method numpy.core._multiarray_umath.implement_array_function}
   657844    1.843    0.000  390.598    0.001 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:3920(percentile)
   657844    0.545    0.000  379.425    0.001 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:4465(_quantile_unchecked)
   657844    1.448    0.000  378.879    0.001 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:3692(_ureduce)
   657844    0.727    0.000  377.271    0.001 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:4614(_quantile_ureduce_func)
   657844    6.070    0.000  367.438    0.001 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:4682(_quantile)
   657844  326.319    0.000  326.319    0.000 {method 'partition' of 'numpy.ndarray' objects}
        1    0.000    0.000  321.982  321.982 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/strategies/evaluator.py:760(__init__)
        1    0.000    0.000  321.981  321.981 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/strategies/evaluator.py:802(_initialize_strategies)
        1    0.000    0.000  321.865  321.865 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/strategies/tf_v3.py:66(__init__)
        1    0.000    0.000  321.865  321.865 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_provider.py:26(__init__)
   328926  214.870    0.001  214.870    0.001 {built-in method numpy.fromiter}
   172791    0.603    0.000   14.132    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/frame.py:1366(iterrows)
236611/236103    1.715    0.000   13.254    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/series.py:342(__init__)
  1315712    9.282    0.000    9.282    0.000 {method 'flatten' of 'numpy.ndarray' objects}
   657844    1.891    0.000    9.166    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:4483(_quantile_is_valid)
   657844    3.680    0.000    8.041    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:4647(_get_indexes)
        1    0.001    0.001    7.879    7.879 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:1239(run)
  1976081    2.374    0.000    7.611    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/fromnumeric.py:69(_wrapreduction)
  1315916    0.609    0.000    7.278    0.000 <__array_function__ internals>:177(all)
        1    0.000    0.000    5.955    5.955 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:114(_load_and_prepare_data)
  1315916    0.716    0.000    5.552    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/fromnumeric.py:2432(all)
  1973532    1.129    0.000    5.328    0.000 <__array_function__ internals>:177(take)
   379081    0.905    0.000    5.257    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/series.py:966(__getitem__)
   657852    0.384    0.000    5.130    0.000 <__array_function__ internals>:177(unique)
   657844    4.529    0.000    4.627    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:4559(_lerp)
   658341    0.388    0.000    4.519    0.000 <__array_function__ internals>:177(any)
  2642592    1.505    0.000    4.412    0.000 {method 'any' of 'numpy.generic' objects}
   657852    0.647    0.000    4.302    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/arraysetops.py:138(unique)
   355655    0.216    0.000    4.143    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/generic.py:4263(get)
  4005975    3.883    0.000    3.883    0.000 {method 'reduce' of 'numpy.ufunc' objects}
   183637    0.853    0.000    3.633    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/construction.py:493(sanitize_array)
  2018675    1.561    0.000    3.584    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/numerictypes.py:356(issubdtype)
1883/1392    0.003    0.000    3.353    0.002 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/window/rolling.py:606(_apply)
1883/1392    0.002    0.000    3.349    0.002 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/window/rolling.py:491(_apply_blockwise)
1883/1392    0.007    0.000    3.345    0.002 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/window/rolling.py:471(_apply_series)
   657852    2.569    0.000    3.337    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/arraysetops.py:323(_unique1d)
        1    0.001    0.001    3.313    3.313 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/signals/calculator.py:247(calculate_all_signals)
  1973532    0.824    0.000    3.310    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/fromnumeric.py:93(take)
1883/1392    0.005    0.000    3.275    0.002 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/window/rolling.py:639(homogeneous_func)
1883/1392    0.007    0.000    3.262    0.002 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/window/rolling.py:645(calc)
    492/1    0.001    0.000    3.236    3.236 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/window/rolling.py:1979(apply)
    492/1    0.001    0.000    3.236    3.236 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/window/rolling.py:1385(apply)




=== DETAILED FUNCTION ANALYSIS ===

1. <method 'partition' of 'numpy.ndarray' objects> (~:0)
   File: ~
   Calls: 657844
   Total Time: 326.319042s
   Cumulative Time: 326.319042s
   Per Call (Total): 0.000496s
   Per Call (Cumulative): 0.000496s

2. <built-in method numpy.fromiter> (~:0)
   File: ~
   Calls: 328926
   Total Time: 214.870218s
   Cumulative Time: 214.870218s
   Per Call (Total): 0.000653s
   Per Call (Cumulative): 0.000653s

3. update (adaptive_threshold.py:48)
   File: /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/utils/adaptive_threshold.py
   Calls: 328926
   Total Time: 23.411584s
   Cumulative Time: 630.221253s
   Per Call (Total): 0.000071s
   Per Call (Cumulative): 0.001916s

4. <method 'flatten' of 'numpy.ndarray' objects> (~:0)
   File: ~
   Calls: 1315712
   Total Time: 9.281879s
   Cumulative Time: 9.281879s
   Per Call (Total): 0.000007s
   Per Call (Cumulative): 0.000007s

5. _quantile (function_base.py:4682)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657844
   Total Time: 6.069536s
   Cumulative Time: 367.437887s
   Per Call (Total): 0.000009s
   Per Call (Cumulative): 0.000559s

6. _prime_adaptive_thresholds (gms_detector.py:1134)
   File: /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py
   Calls: 2
   Total Time: 5.261680s
   Cumulative Time: 655.345803s
   Per Call (Total): 2.630840s
   Per Call (Cumulative): 327.672901s

7. <built-in method numpy.core._multiarray_umath.implement_array_function> (~:0)
   File: ~
   Calls: 6004244
   Total Time: 4.939315s
   Cumulative Time: 391.627755s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000065s

8. _lerp (function_base.py:4559)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657844
   Total Time: 4.529447s
   Cumulative Time: 4.626927s
   Per Call (Total): 0.000007s
   Per Call (Cumulative): 0.000007s

9. <method 'reduce' of 'numpy.ufunc' objects> (~:0)
   File: ~
   Calls: 4005975
   Total Time: 3.883183s
   Cumulative Time: 3.883187s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000001s

10. _get_indexes (function_base.py:4647)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657844
   Total Time: 3.679567s
   Cumulative Time: 8.040922s
   Per Call (Total): 0.000006s
   Per Call (Cumulative): 0.000012s

11. _unique1d (arraysetops.py:323)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/arraysetops.py
   Calls: 657852
   Total Time: 2.568812s
   Cumulative Time: 3.337138s
   Per Call (Total): 0.000004s
   Per Call (Cumulative): 0.000005s

12. _wrapreduction (fromnumeric.py:69)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/fromnumeric.py
   Calls: 1976081
   Total Time: 2.374451s
   Cumulative Time: 7.610963s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000004s

13. _quantile_is_valid (function_base.py:4483)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657844
   Total Time: 1.891428s
   Cumulative Time: 9.166102s
   Per Call (Total): 0.000003s
   Per Call (Cumulative): 0.000014s

14. percentile (function_base.py:3920)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657844
   Total Time: 1.842802s
   Cumulative Time: 390.598101s
   Per Call (Total): 0.000003s
   Per Call (Cumulative): 0.000594s

15. __init__ (series.py:342)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/series.py
   Calls: 236611
   Total Time: 1.714872s
   Cumulative Time: 13.254495s
   Per Call (Total): 0.000007s
   Per Call (Cumulative): 0.000056s

16. issubdtype (numerictypes.py:356)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/numerictypes.py
   Calls: 2018675
   Total Time: 1.560758s
   Cumulative Time: 3.583642s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000002s

17. <method 'any' of 'numpy.generic' objects> (~:0)
   File: ~
   Calls: 2642592
   Total Time: 1.504540s
   Cumulative Time: 4.411939s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000002s

18. <method 'take' of 'numpy.ndarray' objects> (~:0)
   File: ~
   Calls: 1973949
   Total Time: 1.483068s
   Cumulative Time: 1.483068s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000001s

19. _ureduce (function_base.py:3692)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657844
   Total Time: 1.448221s
   Cumulative Time: 378.879282s
   Per Call (Total): 0.000002s
   Per Call (Cumulative): 0.000576s

20. issubclass_ (numerictypes.py:282)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/numerictypes.py
   Calls: 4037350
   Total Time: 1.365996s
   Cumulative Time: 1.888829s
   Per Call (Total): 0.000000s
   Per Call (Cumulative): 0.000000s

21. _get_gamma (function_base.py:4537)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657844
   Total Time: 1.287021s
   Cumulative Time: 1.529293s
   Per Call (Total): 0.000002s
   Per Call (Cumulative): 0.000002s

22. <built-in method builtins.isinstance> (~:0)
   File: ~
   Calls: 9812990
   Total Time: 1.139123s
   Cumulative Time: 1.851848s
   Per Call (Total): 0.000000s
   Per Call (Cumulative): 0.000000s

23. take (<__array_function__ internals>:177)
   File: <__array_function__ internals>
   Calls: 1973532
   Total Time: 1.128547s
   Cumulative Time: 5.328189s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000003s

24. <built-in method _imp.create_dynamic> (~:0)
   File: ~
   Calls: 202
   Total Time: 1.101815s
   Cumulative Time: 1.102520s
   Per Call (Total): 0.005455s
   Per Call (Cumulative): 0.005458s

25. <built-in method numpy.asanyarray> (~:0)
   File: ~
   Calls: 7264270
   Total Time: 0.951822s
   Cumulative Time: 0.951946s
   Per Call (Total): 0.000000s
   Per Call (Cumulative): 0.000000s

26. __getitem__ (series.py:966)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/series.py
   Calls: 379081
   Total Time: 0.905340s
   Cumulative Time: 5.256615s
   Per Call (Total): 0.000002s
   Per Call (Cumulative): 0.000014s

27. <method 'all' of 'numpy.generic' objects> (~:0)
   File: ~
   Calls: 1315696
   Total Time: 0.900969s
   Cumulative Time: 2.698703s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000002s

28. <lambda> (function_base.py:110)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657844
   Total Time: 0.871583s
   Cumulative Time: 0.871583s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000001s

29. sanitize_array (construction.py:493)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/construction.py
   Calls: 183637
   Total Time: 0.853071s
   Cumulative Time: 3.632737s
   Per Call (Total): 0.000005s
   Per Call (Cumulative): 0.000020s

30. take (fromnumeric.py:93)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/fromnumeric.py
   Calls: 1973532
   Total Time: 0.823621s
   Cumulative Time: 3.309955s
   Per Call (Total): 0.000000s
   Per Call (Cumulative): 0.000002s
