#!/usr/bin/env python3
"""
Enhanced Legacy System Performance Analysis (Fixed)
Provides detailed analysis of performance bottlenecks with specific focus on:
1. Data loading inefficiencies
2. Time conversion overhead
3. Modern system data access detection
4. Memory usage patterns
5. Configuration issues

FIXED VERSION: Eliminates import contamination and profiling overhead
"""

import subprocess
import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime
import tempfile

# Add the project root to Python path
project_root = Path(__file__).parent.resolve()
sys.path.insert(0, str(project_root))

class LegacySystemProfiler:
    def __init__(self):
        self.profile_data = {}
        self.performance_issues = []
        self.start_time = None
        self.baseline_results = None

    def run_enhanced_profiling(self):
        """Run comprehensive profiling using external process to avoid import contamination."""
        print("=== ENHANCED LEGACY SYSTEM PROFILING - FULL 2024 (FIXED) ===")
        print(f"Start time: {datetime.now()}")
        self.start_time = time.time()

        print("Running full 2024 backtest with external profiling...")
        print("This approach eliminates import contamination and profiling overhead...")

        try:
            # Run baseline test first
            self._run_baseline_test()

            # Run profiled test
            self._run_profiled_backtest()

        except Exception as e:
            print(f"Error during profiling: {e}")
            import traceback
            traceback.print_exc()
            raise

        total_runtime = time.time() - self.start_time
        print(f"Profiling completed at: {datetime.now()}")
        print(f"Total analysis runtime: {total_runtime:.2f} seconds ({total_runtime/60:.2f} minutes)")

        # Analyze results
        self._analyze_performance()
        self._detect_configuration_issues()
        self._generate_comprehensive_report()

    def _run_baseline_test(self):
        """Run baseline test without profiling to establish performance baseline."""
        print("\n--- RUNNING BASELINE TEST (No Profiling) ---")

        baseline_start = time.time()

        # Run the backtest directly using subprocess to avoid import contamination
        cmd = [
            sys.executable,
            "hyperliquid_bot/backtester/run_backtest.py",
            "--override", "configs/legacy_profile.yaml",
            "--timeframe", "1h",
            "--run-id", "baseline_clean_2024",
            "--skip-validation-warnings"
        ]

        try:
            result = subprocess.run(
                cmd,
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )

            baseline_runtime = time.time() - baseline_start

            if result.returncode == 0:
                print(f"✅ Baseline test completed successfully in {baseline_runtime:.2f} seconds")
                self.baseline_results = {
                    "runtime_seconds": baseline_runtime,
                    "stdout": result.stdout,
                    "stderr": result.stderr
                }

                # Extract key metrics from output
                self._extract_baseline_metrics(result.stdout)

            else:
                print(f"❌ Baseline test failed with return code {result.returncode}")
                print(f"STDERR: {result.stderr}")
                raise RuntimeError(f"Baseline test failed: {result.stderr}")

        except subprocess.TimeoutExpired:
            print("❌ Baseline test timed out after 5 minutes")
            raise RuntimeError("Baseline test timed out")

    def _extract_baseline_metrics(self, stdout):
        """Extract key performance metrics from baseline stdout."""
        lines = stdout.split('\n')

        for line in lines:
            # Look for performance optimization message
            if "PERFORMANCE OPTIMIZATION" in line and "Skipping" in line:
                self.profile_data['l2_optimization_working'] = True
                print(f"✅ L2 Optimization confirmed: {line.strip()}")

            # Look for final results
            if "Sharpe Ratio" in line:
                try:
                    sharpe = float(line.split(':')[-1].strip())
                    self.profile_data['baseline_sharpe'] = sharpe
                except:
                    pass

            if "Return on Initial" in line or "ROI" in line:
                try:
                    roi_str = line.split(':')[-1].strip().replace('%', '')
                    roi = float(roi_str)
                    self.profile_data['baseline_roi'] = roi
                except:
                    pass

            if "Trade Count" in line:
                try:
                    trades = int(line.split(':')[-1].strip())
                    self.profile_data['baseline_trades'] = trades
                except:
                    pass

    def _run_profiled_backtest(self):
        """Run profiled backtest using external cProfile to avoid import contamination."""
        print("\n--- RUNNING PROFILED TEST (External cProfile) ---")

        profiled_start = time.time()

        # Create a temporary script that runs the backtest with profiling
        profile_script = project_root / "temp_profile_script.py"

        profile_script_content = '''#!/usr/bin/env python3
import sys
import cProfile
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.resolve()
sys.path.insert(0, str(project_root))

def run_backtest():
    """Run the backtest with minimal imports."""
    from hyperliquid_bot.backtester.run_backtest import main

    # Set command line arguments for the legacy configuration
    sys.argv = [
        'temp_profile_script.py',
        '--override', 'configs/legacy_profile.yaml',
        '--timeframe', '1h',
        '--run-id', 'profiled_clean_2024',
        '--skip-validation-warnings'
    ]

    # Run the main backtest function
    main()

if __name__ == "__main__":
    # Run with profiling
    profiler = cProfile.Profile()
    profiler.enable()
    try:
        run_backtest()
    finally:
        profiler.disable()
        profiler.dump_stats("enhanced_legacy_system_profile_2024.prof")
'''

        try:
            # Write the temporary script
            with open(profile_script, 'w') as f:
                f.write(profile_script_content)

            # Run the profiled script
            cmd = [sys.executable, str(profile_script)]

            result = subprocess.run(
                cmd,
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=600  # 10 minute timeout for profiled run
            )

            profiled_runtime = time.time() - profiled_start

            if result.returncode == 0:
                print(f"✅ Profiled test completed successfully in {profiled_runtime:.2f} seconds")
                self.profile_data['profiled_runtime'] = profiled_runtime

                # Extract profiled metrics
                self._extract_profiled_metrics(result.stdout)

                # Load and analyze profile data
                self._analyze_profile_data()

            else:
                print(f"❌ Profiled test failed with return code {result.returncode}")
                print(f"STDERR: {result.stderr}")
                self.performance_issues.append(f"Profiled test failed: {result.stderr}")

        except subprocess.TimeoutExpired:
            print("❌ Profiled test timed out after 10 minutes")
            self.performance_issues.append("Profiled test timed out")
        finally:
            # Clean up temporary script
            if profile_script.exists():
                profile_script.unlink()

    def _extract_profiled_metrics(self, stdout):
        """Extract key performance metrics from profiled stdout."""
        lines = stdout.split('\n')

        for line in lines:
            # Look for performance optimization message
            if "PERFORMANCE OPTIMIZATION" in line and "Skipping" in line:
                print(f"✅ L2 Optimization confirmed in profiled run: {line.strip()}")

            # Look for final results to ensure consistency
            if "Sharpe Ratio" in line:
                try:
                    sharpe = float(line.split(':')[-1].strip())
                    self.profile_data['profiled_sharpe'] = sharpe
                except:
                    pass

    def _analyze_profile_data(self):
        """Analyze the generated profile data."""
        print("\n--- ANALYZING PROFILE DATA ---")

        profile_file = project_root / "enhanced_legacy_system_profile_2024.prof"

        if not profile_file.exists():
            print("❌ Profile file not found")
            self.performance_issues.append("Profile file not generated")
            return

        try:
            import pstats
            import io

            # Load profile stats
            stats = pstats.Stats(str(profile_file))

            # Get basic statistics
            s = io.StringIO()
            ps = pstats.Stats(str(profile_file), stream=s)
            ps.sort_stats('cumulative')
            ps.print_stats(20)  # Top 20 functions
            output = s.getvalue()

            # Extract total runtime from profile
            lines = output.split('\n')
            for line in lines:
                if 'function calls' in line and 'seconds' in line:
                    parts = line.split()
                    if len(parts) >= 6:
                        try:
                            total_time = float(parts[5])
                            self.profile_data['profile_total_time'] = total_time
                            print(f"Profile total time: {total_time:.2f} seconds")
                            break
                        except:
                            pass

            # Check for modern system contamination
            self._check_profile_contamination(stats)

        except Exception as e:
            print(f"❌ Error analyzing profile data: {e}")
            self.performance_issues.append(f"Profile analysis failed: {e}")

    def _check_profile_contamination(self, stats):
        """Check profile for modern system component contamination."""
        print("\n--- CHECKING FOR IMPORT CONTAMINATION ---")

        import io
        import pstats

        # Check for modern system imports
        s = io.StringIO()
        ps = pstats.Stats(stats, stream=s)
        ps.sort_stats('cumulative')
        ps.print_stats('.*tf_v3.*|.*continuous.*|.*scheduler.*', 30)
        output = s.getvalue()

        contamination_found = False
        contamination_items = []

        lines = output.split('\n')
        for line in lines:
            if any(keyword in line.lower() for keyword in ['tf_v3', 'continuous', 'scheduler']):
                contamination_found = True
                contamination_items.append(line.strip())
                print(f"⚠️  Contamination detected: {line.strip()}")

        if contamination_found:
            self.performance_issues.append(f"Import contamination detected: {len(contamination_items)} items")
            self.profile_data['contamination_detected'] = True
            self.profile_data['contamination_items'] = contamination_items
        else:
            print("✅ No import contamination detected")
            self.profile_data['contamination_detected'] = False

    def _analyze_performance(self):
        """Analyze performance comparison between baseline and profiled runs."""
        print("\n=== PERFORMANCE COMPARISON ANALYSIS ===")

        baseline_time = self.baseline_results.get('runtime_seconds', 0) if self.baseline_results else 0
        profiled_time = self.profile_data.get('profiled_runtime', 0)

        if baseline_time > 0 and profiled_time > 0:
            overhead_ratio = profiled_time / baseline_time
            overhead_pct = (overhead_ratio - 1) * 100

            print(f"Baseline runtime: {baseline_time:.2f} seconds")
            print(f"Profiled runtime: {profiled_time:.2f} seconds")
            print(f"Profiling overhead: {overhead_pct:.1f}% ({overhead_ratio:.2f}x)")

            self.profile_data['overhead_ratio'] = overhead_ratio
            self.profile_data['overhead_percentage'] = overhead_pct

            if overhead_ratio > 2.0:  # More than 2x slower
                self.performance_issues.append(f"High profiling overhead: {overhead_pct:.1f}%")

        # Compare results consistency
        baseline_sharpe = self.profile_data.get('baseline_sharpe')
        profiled_sharpe = self.profile_data.get('profiled_sharpe')

        if baseline_sharpe and profiled_sharpe:
            if abs(baseline_sharpe - profiled_sharpe) < 0.01:  # Within 0.01
                print(f"✅ Results consistent: Sharpe {baseline_sharpe:.2f} vs {profiled_sharpe:.2f}")
                self.profile_data['results_consistent'] = True
            else:
                print(f"⚠️  Results inconsistent: Sharpe {baseline_sharpe:.2f} vs {profiled_sharpe:.2f}")
                self.profile_data['results_consistent'] = False
                self.performance_issues.append("Results inconsistency detected")

    def _detect_configuration_issues(self):
        """Detect configuration-related performance issues."""
        print("\n=== CONFIGURATION ISSUE DETECTION ===")

        config_issues = []

        # Check L2 optimization status
        if not self.profile_data.get('l2_optimization_working', False):
            config_issues.append("L2 optimization not working - may cause performance degradation")

        # Check for import contamination
        if self.profile_data.get('contamination_detected', False):
            contamination_count = len(self.profile_data.get('contamination_items', []))
            config_issues.append(f"Import contamination detected: {contamination_count} modern system components loaded")

        # Check profiling overhead
        overhead_ratio = self.profile_data.get('overhead_ratio', 1.0)
        if overhead_ratio > 3.0:  # More than 3x slower
            config_issues.append(f"Excessive profiling overhead: {overhead_ratio:.1f}x baseline")

        # Check results consistency
        if not self.profile_data.get('results_consistent', True):
            config_issues.append("Results inconsistency between baseline and profiled runs")

        self.profile_data['configuration_issues'] = config_issues

        if config_issues:
            print("Configuration issues detected:")
            for issue in config_issues:
                print(f"  ⚠️  {issue}")
        else:
            print("✅ No major configuration issues detected")

    def _detect_configuration_issues(self):
        """Detect configuration-related performance issues."""
        print("\n=== CONFIGURATION ISSUE DETECTION ===")

        config_issues = []

        # Check L2 optimization status
        if not self.profile_data.get('l2_optimization_working', False):
            config_issues.append("L2 optimization not working - may cause performance degradation")

        # Check for import contamination
        if self.profile_data.get('contamination_detected', False):
            contamination_count = len(self.profile_data.get('contamination_items', []))
            config_issues.append(f"Import contamination detected: {contamination_count} modern system components loaded")

        # Check profiling overhead
        overhead_ratio = self.profile_data.get('overhead_ratio', 1.0)
        if overhead_ratio > 3.0:  # More than 3x slower
            config_issues.append(f"Excessive profiling overhead: {overhead_ratio:.1f}x baseline")

        # Check results consistency
        if not self.profile_data.get('results_consistent', True):
            config_issues.append("Results inconsistency between baseline and profiled runs")

        self.profile_data['configuration_issues'] = config_issues

        if config_issues:
            print("Configuration issues detected:")
            for issue in config_issues:
                print(f"  ⚠️  {issue}")
        else:
            print("✅ No major configuration issues detected")

    def _generate_comprehensive_report(self):
        """Generate a comprehensive analysis report."""
        print("\n" + "="*80)
        print("COMPREHENSIVE LEGACY SYSTEM PERFORMANCE ANALYSIS REPORT (FIXED)")
        print("="*80)

        # Summary section
        print("\n📊 EXECUTIVE SUMMARY")
        print("-" * 40)

        baseline_time = self.baseline_results.get('runtime_seconds', 0) if self.baseline_results else 0
        profiled_time = self.profile_data.get('profiled_runtime', 0)
        overhead_ratio = self.profile_data.get('overhead_ratio', 1.0)

        print(f"Baseline Runtime: {baseline_time:.2f} seconds")
        print(f"Profiled Runtime: {profiled_time:.2f} seconds")
        print(f"Profiling Overhead: {overhead_ratio:.2f}x ({self.profile_data.get('overhead_percentage', 0):.1f}%)")

        # Performance metrics
        print(f"\n📈 PERFORMANCE METRICS")
        print("-" * 40)
        print(f"L2 Optimization Working: {'✅ Yes' if self.profile_data.get('l2_optimization_working', False) else '❌ No'}")
        print(f"Import Contamination: {'❌ Detected' if self.profile_data.get('contamination_detected', False) else '✅ Clean'}")
        print(f"Results Consistent: {'✅ Yes' if self.profile_data.get('results_consistent', True) else '❌ No'}")

        # Trading results
        baseline_sharpe = self.profile_data.get('baseline_sharpe')
        baseline_roi = self.profile_data.get('baseline_roi')
        baseline_trades = self.profile_data.get('baseline_trades')

        if baseline_sharpe:
            print(f"\n📈 TRADING RESULTS")
            print("-" * 40)
            print(f"Sharpe Ratio: {baseline_sharpe:.2f}")
            print(f"ROI: {baseline_roi:.2f}%" if baseline_roi else "ROI: Not captured")
            print(f"Trade Count: {baseline_trades}" if baseline_trades else "Trade Count: Not captured")

        # Issues section
        print(f"\n⚠️  PERFORMANCE ISSUES IDENTIFIED")
        print("-" * 40)
        if self.performance_issues:
            for i, issue in enumerate(self.performance_issues, 1):
                print(f"{i}. {issue}")
        else:
            print("✅ No major performance issues identified")

        # Configuration issues
        config_issues = self.profile_data.get('configuration_issues', [])
        if config_issues:
            print(f"\n🔧 CONFIGURATION ISSUES")
            print("-" * 40)
            for i, issue in enumerate(config_issues, 1):
                print(f"{i}. {issue}")

        # Recommendations
        print(f"\n💡 RECOMMENDATIONS")
        print("-" * 40)
        self._generate_recommendations()

        # Save detailed report
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'baseline_runtime_seconds': baseline_time,
            'profiled_runtime_seconds': profiled_time,
            'overhead_ratio': overhead_ratio,
            'profile_data': self.profile_data,
            'performance_issues': self.performance_issues,
            'configuration_issues': config_issues,
            'analysis_summary': {
                'l2_optimization_working': self.profile_data.get('l2_optimization_working', False),
                'contamination_detected': self.profile_data.get('contamination_detected', False),
                'results_consistent': self.profile_data.get('results_consistent', True),
                'overhead_percentage': self.profile_data.get('overhead_percentage', 0)
            }
        }

        report_file = "enhanced_legacy_profile_report_2024_fixed.json"
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)

        print(f"\n📄 Detailed report saved to: {report_file}")
        print("="*80)

    def _generate_recommendations(self):
        """Generate specific recommendations based on analysis."""
        recommendations = []

        # Check profiling overhead
        overhead_ratio = self.profile_data.get('overhead_ratio', 1.0)
        if overhead_ratio > 2.0:
            recommendations.append(f"Profiling overhead is {overhead_ratio:.1f}x - use external profiling tools for accurate measurement")

        # Check import contamination
        if self.profile_data.get('contamination_detected', False):
            contamination_count = len(self.profile_data.get('contamination_items', []))
            recommendations.append(f"Fix import contamination: {contamination_count} modern system components detected")

        # Check L2 optimization
        if not self.profile_data.get('l2_optimization_working', False):
            recommendations.append("Enable L2 optimization flag: skip_l2_raw_processing_if_1h_features_exist")

        # Check results consistency
        if not self.profile_data.get('results_consistent', True):
            recommendations.append("Investigate results inconsistency between baseline and profiled runs")

        # Performance recommendations
        if overhead_ratio < 1.5:
            recommendations.append("✅ Profiling methodology is working correctly with minimal overhead")

        if self.profile_data.get('l2_optimization_working', False) and not self.profile_data.get('contamination_detected', False):
            recommendations.append("✅ Legacy system is properly isolated and optimized")

        if not recommendations:
            recommendations.append("✅ System performance appears optimal for legacy configuration")

        for i, rec in enumerate(recommendations, 1):
            print(f"{i}. {rec}")


def main():
    """Main execution function."""
    profiler = LegacySystemProfiler()
    profiler.run_enhanced_profiling()


if __name__ == "__main__":
    main()