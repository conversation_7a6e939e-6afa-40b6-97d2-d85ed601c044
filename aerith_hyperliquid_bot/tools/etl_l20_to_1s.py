#!/usr/bin/env python
# tools/etl_l20_to_1s.py

"""
ETL Pipeline: Convert raw L2 snapshots to 1-second feature parquet files.

This script processes raw L2 snapshots (10 Hz, depth 5-20) into date-partitioned
1-second feature parquet files ready for Continuous GMS, TF-v3, and future HF back-tests.

The script processes one calendar day at a time, rolling each hourly raw chunk
(e.g. `lob_20250525_13.arrow`) into a matching feature file `features_20250525_13.parquet`.

Usage:
    python -m tools.etl_l20_to_1s \
        --raw-dir   ./data/l2_raw/2025-05-25/ \
        --out-dir   ./data/features_1s/ \
        --date      2025-05-25 \
        --depth     20  # default = cfg.microstructure.depth_levels
"""

import os
import sys
import json
import logging
import argparse
from pathlib import Path
from datetime import datetime, timedelta
import time
from typing import Dict, List, Optional, Tuple, Union, Any

import numpy as np
import pandas as pd
import pyarrow as pa
import pyarrow.dataset as ds
import pyarrow.parquet as pq
import pyarrow.compute as pc

# Add project root to path for imports
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.features import ta_utils
from hyperliquid_bot.features.builder_registry import FEATURE_BUILDERS, validate_schema, validate_nan_ratios, get_canonical_columns
from hyperliquid_bot.utils.time import to_utc_naive, vectorized_to_utc_naive

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("etl_l20_to_1s")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="ETL Pipeline: Convert raw L2 snapshots to 1-second feature parquet")

    parser.add_argument(
        "--raw-dir",
        type=str,
        help="Directory containing raw L2 data files"
    )

    parser.add_argument(
        "--out-dir",
        type=str,
        help="Output directory for feature parquet files"
    )

    parser.add_argument(
        "--date",
        type=str,
        help="Date to process in YYYY-MM-DD format"
    )

    parser.add_argument(
        "--depth",
        type=int,
        default=None,
        help="Depth levels for OBI calculation (default: from config)"
    )

    parser.add_argument(
        "--force",
        action="store_true",
        help="Force overwrite of existing output files"
    )

    parser.add_argument(
        "--overwrite",
        action="store_true",
        help="Delete existing parquet files for the day and rewrite them (replacement not append)"
    )

    return parser.parse_args()

def load_raw_l2_data(file_path: str) -> pd.DataFrame:
    """
    Load raw L2 data from a file.

    Args:
        file_path: Path to the raw L2 data file

    Returns:
        DataFrame with raw L2 data
    """
    start_time = time.time()
    logger.info(f"Loading raw L2 data from {file_path}")

    if file_path.endswith('.txt'):
        # Process JSON lines format
        records = []
        with open(file_path, 'r') as f:
            for line in f:
                try:
                    data = json.loads(line)
                    # Extract timestamp and L2 data
                    timestamp = data.get('raw', {}).get('data', {}).get('time')
                    levels = data.get('raw', {}).get('data', {}).get('levels', [])

                    if timestamp and len(levels) == 2:
                        bids = levels[0]
                        asks = levels[1]

                        # Create record with timestamp and bid/ask data
                        record = {'timestamp': timestamp}

                        # Process bids
                        for i, bid in enumerate(bids[:20], 1):  # Up to 20 levels
                            record[f'bid_price_{i}'] = float(bid['px'])
                            record[f'bid_size_{i}'] = float(bid['sz'])

                        # Process asks
                        for i, ask in enumerate(asks[:20], 1):  # Up to 20 levels
                            record[f'ask_price_{i}'] = float(ask['px'])
                            record[f'ask_size_{i}'] = float(ask['sz'])

                        records.append(record)
                except Exception as e:
                    logger.warning(f"Error processing line: {e}")
                    continue

        # Create DataFrame
        df = pd.DataFrame(records)

    elif file_path.endswith('.arrow'):
        # Load Arrow IPC file and expand bids/asks arrays
        import pyarrow.ipc as ipc

        with open(file_path, 'rb') as f:
            reader = ipc.open_file(f)
            table = reader.read_all()
            df_raw = table.to_pandas()

        # Expand bids/asks arrays into individual columns
        records = []
        for _, row in df_raw.iterrows():
            record = {'timestamp': row['timestamp']}

            # Process bids array
            bids = row['bids'] if isinstance(row['bids'], (list, np.ndarray)) else []
            for i, bid in enumerate(bids[:20], 1):  # Up to 20 levels
                if len(bid) >= 2:
                    record[f'bid_price_{i}'] = float(bid[0])
                    record[f'bid_size_{i}'] = float(bid[1])

            # Process asks array
            asks = row['asks'] if isinstance(row['asks'], (list, np.ndarray)) else []
            for i, ask in enumerate(asks[:20], 1):  # Up to 20 levels
                if len(ask) >= 2:
                    record[f'ask_price_{i}'] = float(ask[0])
                    record[f'ask_size_{i}'] = float(ask[1])

            records.append(record)

        df = pd.DataFrame(records)

    elif file_path.endswith('.parquet'):
        # Load Parquet file
        df = pd.read_parquet(file_path)

    else:
        raise ValueError(f"Unsupported file format: {file_path}")

    # Convert timestamp to datetime if it's not already
    if 'timestamp' in df.columns and not pd.api.types.is_datetime64_dtype(df['timestamp']):
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

    logger.info(f"Loaded {len(df)} rows in {time.time() - start_time:.2f} seconds")
    return df

def calculate_features(df: pd.DataFrame, depth: int) -> pd.DataFrame:
    """
    Calculate features from raw L2 data.

    Args:
        df: DataFrame with raw L2 data
        depth: Depth levels for OBI calculation

    Returns:
        DataFrame with calculated features
    """
    start_time = time.time()
    logger.info(f"Calculating features with depth={depth}")

    # Forward-fill best bid/ask (causal) before computing spread
    # This eliminates NaN values without introducing look-ahead bias
    df[['bid_price_1', 'ask_price_1']] = df[['bid_price_1', 'ask_price_1']].ffill()

    # Calculate mid price
    df['mid_price'] = (df['bid_price_1'] + df['ask_price_1']) / 2

    # Calculate spread
    df['spread'] = df['ask_price_1'] - df['bid_price_1']
    df['spread_relative'] = df['spread'] / df['mid_price']

    # Calculate OBI
    bid_cols = [f'bid_size_{i}' for i in range(1, depth + 1)]
    ask_cols = [f'ask_size_{i}' for i in range(1, depth + 1)]

    # Ensure all columns exist
    for col in bid_cols + ask_cols:
        if col not in df.columns:
            df[col] = 0

    # Calculate OBI
    bid_sum = df[bid_cols].sum(axis=1)
    ask_sum = df[ask_cols].sum(axis=1)
    df[f'raw_obi_{depth}'] = (bid_sum - ask_sum) / (bid_sum + ask_sum + 1e-9)  # Add epsilon for stability

    logger.info(f"Calculated features in {time.time() - start_time:.2f} seconds")
    return df

def resample_to_1s(df: pd.DataFrame, method: str = 'median') -> pd.DataFrame:
    """
    Resample data to 1-second intervals.

    Args:
        df: DataFrame with raw data
        method: Aggregation method ('median', 'mean', 'first', etc.)

    Returns:
        Resampled DataFrame
    """
    start_time = time.time()
    logger.info(f"Resampling to 1-second intervals using {method} method")

    # Set timestamp as index for resampling
    df = df.set_index('timestamp')

    # Define aggregation functions
    if method == 'median':
        agg_func = 'median'
    elif method == 'mean':
        agg_func = 'mean'
    elif method == 'first':
        agg_func = 'first'
    else:
        raise ValueError(f"Unsupported aggregation method: {method}")

    # Resample (using 's' instead of 'S' as per pandas deprecation warning)
    resampled = df.resample('1s').agg(agg_func)

    # Reset index to get timestamp as a column
    resampled = resampled.reset_index()

    logger.info(f"Resampled to {len(resampled)} rows in {time.time() - start_time:.2f} seconds")
    return resampled

def calculate_hourly_ohlc(df: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate 1-hour OHLC from 1-second data.

    Args:
        df: DataFrame with 1-second data

    Returns:
        DataFrame with 1-hour OHLC data
    """
    start_time = time.time()
    logger.info("Calculating 1-hour OHLC from 1-second data")

    # Ensure timestamp is the index
    if 'timestamp' in df.columns:
        df = df.set_index('timestamp')

    # Resample to 1-hour and calculate OHLC
    ohlc_1h = df['mid_price'].resample('1H').ohlc()

    logger.info(f"Calculated 1-hour OHLC with {len(ohlc_1h)} rows in {time.time() - start_time:.2f} seconds")
    return ohlc_1h

def calculate_hourly_atr(ohlc_1h: pd.DataFrame, length: int = 14) -> pd.DataFrame:
    """
    Calculate ATR from 1-hour OHLC data.

    Args:
        ohlc_1h: DataFrame with 1-hour OHLC data
        length: ATR period (default: 14)

    Returns:
        DataFrame with ATR values
    """
    start_time = time.time()
    logger.info(f"Calculating {length}-period ATR from 1-hour OHLC data")

    # Calculate True Range
    tr = np.maximum.reduce([
        ohlc_1h['high'] - ohlc_1h['low'],
        (ohlc_1h['high'] - ohlc_1h['close'].shift()).abs(),
        (ohlc_1h['low'] - ohlc_1h['close'].shift()).abs(),
    ])

    # Calculate ATR with specified period
    ohlc_1h = ohlc_1h.copy()
    # Convert numpy array to pandas Series for rolling calculation
    tr_series = pd.Series(tr, index=ohlc_1h.index)
    ohlc_1h['atr_14'] = tr_series.rolling(window=length, min_periods=length).mean()

    logger.info(f"Calculated ATR in {time.time() - start_time:.2f} seconds")
    return ohlc_1h

def merge_hourly_atr_to_seconds(df_1s: pd.DataFrame, ohlc_1h_atr: pd.DataFrame) -> pd.DataFrame:
    """
    Merge 1-hour ATR values back to 1-second data with forward fill.

    Args:
        df_1s: DataFrame with 1-second data
        ohlc_1h_atr: DataFrame with 1-hour ATR data

    Returns:
        DataFrame with 1-second data and ATR values
    """
    start_time = time.time()
    logger.info("Merging 1-hour ATR to 1-second data with forward fill")

    # Make copies to avoid modifying originals
    df_1s_copy = df_1s.copy()
    ohlc_1h_copy = ohlc_1h_atr.copy()

    # Remove existing ATR columns if they exist to avoid conflicts
    atr_columns_to_remove = ['atr_14_sec', 'atr_percent_sec']
    for col in atr_columns_to_remove:
        if col in df_1s_copy.columns:
            df_1s_copy = df_1s_copy.drop(columns=[col])
            logger.info(f"Removed existing {col} column to avoid conflicts")

    # Ensure timestamp is the index for both DataFrames
    if 'timestamp' in df_1s_copy.columns:
        df_1s_copy = df_1s_copy.set_index('timestamp')

    if 'timestamp' in ohlc_1h_copy.columns:
        ohlc_1h_copy = ohlc_1h_copy.set_index('timestamp')

    # Extract just the ATR column from the hourly data
    atr_series = ohlc_1h_copy['atr_14']

    # Create a combined index with all timestamps (1-second and hourly)
    combined_index = df_1s_copy.index.union(atr_series.index).sort_values()

    # Reindex ATR series to combined index and forward fill
    atr_reindexed = atr_series.reindex(combined_index).ffill()

    # Now merge with 1-second data
    df_merged = df_1s_copy.merge(
        atr_reindexed.to_frame('atr_14_sec'),
        left_index=True,
        right_index=True,
        how='left'
    )

    # Calculate ATR percent using mid_price
    if 'mid_price' in df_merged.columns:
        df_merged['atr_percent_sec'] = df_merged['atr_14_sec'] / df_merged['mid_price']

    # Reset index to get timestamp as a column
    if df_merged.index.name == 'timestamp':
        df_merged = df_merged.reset_index()

    logger.info(f"Merged ATR in {time.time() - start_time:.2f} seconds")
    return df_merged

def load_existing_hourly_ohlcv(date_str: str, config) -> pd.DataFrame:
    """
    Load existing hourly OHLCV data for ATR calculation.

    Args:
        date_str: Date string in YYYY-MM-DD format
        config: Configuration object with paths

    Returns:
        DataFrame with hourly OHLCV data including previous days for ATR calculation
    """
    start_time = time.time()
    logger.info(f"Loading existing hourly OHLCV data for {date_str}")

    # Parse the target date
    target_date = datetime.strptime(date_str, "%Y-%m-%d")

    # We need at least 14 days of data for ATR(14) calculation
    # Load data from 20 days before to ensure we have enough
    start_date = target_date - timedelta(days=20)

    # Path to hourly OHLCV data
    ohlcv_base_path = Path(config.data_paths.ohlcv_base_path) / "1h"

    all_data = []
    current_date = start_date

    while current_date <= target_date:
        date_file = ohlcv_base_path / f"{current_date.strftime('%Y-%m-%d')}_1h.parquet"

        if date_file.exists():
            try:
                df_day = pd.read_parquet(date_file)
                # Ensure timestamp is timezone-naive UTC
                if df_day['timestamp'].dt.tz is not None:
                    df_day['timestamp'] = df_day['timestamp'].dt.tz_convert('UTC').dt.tz_localize(None)
                all_data.append(df_day)
                logger.debug(f"Loaded {len(df_day)} hourly rows from {date_file}")
            except Exception as e:
                logger.warning(f"Failed to load {date_file}: {e}")
        else:
            logger.debug(f"Hourly OHLCV file not found: {date_file}")

        current_date += timedelta(days=1)

    if not all_data:
        raise FileNotFoundError(f"No hourly OHLCV data found for date range {start_date} to {target_date}")

    # Combine all data
    combined_df = pd.concat(all_data, ignore_index=True)
    combined_df = combined_df.sort_values('timestamp').reset_index(drop=True)

    logger.info(f"Loaded {len(combined_df)} hourly OHLCV rows in {time.time() - start_time:.2f} seconds")
    return combined_df


def calculate_atr_from_hourly_data(ohlcv_df: pd.DataFrame, length: int = 14) -> pd.DataFrame:
    """
    Calculate ATR from hourly OHLCV data using Wilder's smoothing method.

    Args:
        ohlcv_df: DataFrame with hourly OHLCV data
        length: ATR period (default: 14)

    Returns:
        DataFrame with ATR values added
    """
    start_time = time.time()
    logger.info(f"Calculating ATR({length}) from hourly OHLCV data")

    # Make a copy to avoid modifying the original
    df = ohlcv_df.copy()

    # Calculate True Range using Wilder's method
    df['prev_close'] = df['close'].shift(1)

    # True Range components
    df['tr1'] = df['high'] - df['low']
    df['tr2'] = (df['high'] - df['prev_close']).abs()
    df['tr3'] = (df['low'] - df['prev_close']).abs()

    # True Range is the maximum of the three components
    df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)

    # Calculate ATR using Wilder's smoothing method
    # First, calculate simple moving average for the first 'length' periods
    # Then use Wilder's smoothing for subsequent periods
    atr_values = []

    for i in range(len(df)):
        if i < length - 1:
            # Not enough data for ATR calculation
            atr_values.append(np.nan)
        elif i == length - 1:
            # First ATR value is simple average of first 'length' true ranges
            atr_values.append(df['true_range'].iloc[:length].mean())
        else:
            # Wilder's smoothing: ATR = (previous_ATR * (length-1) + current_TR) / length
            prev_atr = atr_values[-1]
            current_tr = df['true_range'].iloc[i]
            new_atr = (prev_atr * (length - 1) + current_tr) / length
            atr_values.append(new_atr)

    df['atr_14'] = atr_values

    # Clean up intermediate columns
    df = df.drop(columns=['prev_close', 'tr1', 'tr2', 'tr3', 'true_range'])

    logger.info(f"Calculated ATR in {time.time() - start_time:.2f} seconds")
    return df


def calculate_post_resample_features(df: pd.DataFrame, date_str: str = None, config=None) -> pd.DataFrame:
    """
    Calculate features that need to be computed after resampling.

    Args:
        df: Resampled DataFrame
        date_str: Date string in YYYY-MM-DD format (for loading hourly OHLCV data)
        config: Configuration object

    Returns:
        DataFrame with additional features
    """
    start_time = time.time()
    logger.info("Calculating post-resample features")

    # Set close = mid_price for continuity with OHLCV
    df['close'] = df['mid_price']

    # Calculate realized volatility (1-second)
    df['realised_vol_1s'] = np.abs(np.log(df['close'] / df['close'].shift(1)))

    # Calculate high/low for ATR (for backward compatibility)
    # For 1-second data, we don't have true high/low, so we'll use close
    df['high'] = df['close']
    df['low'] = df['close']

    # REMOVED: External hourly OHLCV ATR calculation (replaced by feature builder)
    # ATR is now calculated by the atr_rolling_14h feature builder which handles
    # the 14-hour rolling window internally without requiring external files
    logger.info("ATR calculation delegated to feature builder (atr_rolling_14h)")

    # REMOVED: Legacy ATR calculations (now handled by feature builders)
    # The atr_rolling_14h and atr_percent_sec feature builders will handle
    # ATR calculation with proper 14-hour rolling window

    # Add placeholder for unrealised_pnl
    df['unrealised_pnl'] = 0.0

    # Calculate rolling spread statistics (spread_mean and spread_std)
    # These are required by the continuous_GMS detector
    spread_roll_window = 60  # 60-second rolling window (configurable)
    if 'spread' in df.columns:
        logger.info(f"Calculating rolling spread statistics with window={spread_roll_window}")
        # Use causal rolling window with min_periods=1 to avoid unnecessary NaNs
        rolling_spread = df['spread'].rolling(
            window=spread_roll_window,
            min_periods=1  # Causal: use available data, no look-ahead
        )
        df['spread_mean'] = rolling_spread.mean()
        df['spread_std'] = rolling_spread.std()

        # Log NaN counts for debugging
        spread_mean_nans = df['spread_mean'].isna().sum()
        spread_std_nans = df['spread_std'].isna().sum()
        logger.info(f"Rolling spread stats calculated. spread_mean NaNs: {spread_mean_nans}, spread_std NaNs: {spread_std_nans}")

        # Assertion to ensure low NaN ratio
        nan_ratio = df['spread_mean'].isna().mean()
        assert nan_ratio < 0.01, f"Too many NaN spread stats: {nan_ratio:.3f} > 0.01"
        logger.info(f"Spread NaN ratio check passed: {nan_ratio:.3f} < 0.01")
    else:
        logger.warning("'spread' column not found, cannot calculate spread_mean and spread_std")
        df['spread_mean'] = np.nan
        df['spread_std'] = np.nan

    # NEW: Build missing features using the builder registry
    logger.info("Building missing canonical features using builder registry")
    depth_levels = config.microstructure.depth_levels if config else 5

    # Get list of missing features that need to be built
    missing_features = []
    for feature_name, builder_func in FEATURE_BUILDERS.items():
        if feature_name not in df.columns:
            missing_features.append(feature_name)

    if missing_features:
        logger.info(f"Building {len(missing_features)} missing features: {missing_features}")

        # Build each missing feature
        for feature_name in missing_features:
            try:
                builder_func = FEATURE_BUILDERS[feature_name]
                feature_series = builder_func(df)
                df[feature_name] = feature_series
                logger.debug(f"Built feature '{feature_name}' with {feature_series.notna().sum()}/{len(feature_series)} non-NaN values")
            except Exception as e:
                logger.error(f"Failed to build feature '{feature_name}': {e}")
                # Add NaN column as fallback
                df[feature_name] = np.nan
    else:
        logger.info("All canonical features already present")

    # NEW: Schema validation
    logger.info("Validating canonical schema")
    is_valid, missing_columns = validate_schema(df, depth_levels)
    if not is_valid:
        logger.error(f"Schema validation failed! Missing columns: {missing_columns}")
        raise ValueError(f"Schema validation failed! Missing canonical columns: {missing_columns}")
    else:
        logger.info("Schema validation passed - all canonical columns present")

    # NEW: Two-tier NaN ratio validation
    logger.info("Validating NaN ratios with two-tier system (Tier-1: ≤1%, Tier-2: ≤5%)")
    is_valid_nan, nan_ratios = validate_nan_ratios(df, warmup_rows=100, depth_levels=depth_levels)

    if not is_valid_nan:
        # Extract failed columns from the detailed error messages
        tier1_cols = {'atr_14_sec', 'spread_mean', 'spread_std', 'ma_slope', 'unrealised_pnl', 'volume'}
        tier2_patterns = ['bid_price_', 'ask_price_', 'bid_size_', 'ask_size_', 'raw_obi_']
        tier2_exact_cols = {'best_bid', 'best_ask', 'atr_percent_sec', 'mid_price', 'close', 'high', 'low', 'spread', 'realised_vol_1s'}

        failed_features = []
        for col, ratio in nan_ratios.items():
            if col in tier1_cols and ratio > 0.01:
                failed_features.append(col)
            elif (any(pattern in col for pattern in tier2_patterns) or col in tier2_exact_cols) and ratio > 0.05:
                failed_features.append(col)

        logger.error(f"Two-tier NaN ratio validation failed!")
        for col, ratio in nan_ratios.items():
            if col in failed_features:
                if col in tier1_cols:
                    tier, threshold = "tier-1", "1%"
                elif any(pattern in col for pattern in tier2_patterns) or col in tier2_exact_cols:
                    tier, threshold = "tier-2", "5%"
                else:
                    tier, threshold = "canonical", "1%"
                logger.error(f"  {col} ({tier}): {ratio:.3f} > {threshold}")
        raise ValueError(f"Two-tier NaN ratio validation failed! {len(failed_features)} features exceed thresholds")
    else:
        logger.info(f"Two-tier NaN ratio validation passed - all features within thresholds")

    logger.info(f"Calculated post-resample features in {time.time() - start_time:.2f} seconds")
    return df

def process_hour(
    raw_file: str,
    output_file: str,
    depth: int,
    rollup_method: str,
    force: bool = False,
    date_str: str = None,
    config=None
) -> bool:
    """
    Process a single hour of data.

    Args:
        raw_file: Path to raw data file
        output_file: Path to output file
        depth: Depth levels for OBI calculation
        rollup_method: Method for resampling ('median', 'mean', etc.)
        force: Whether to overwrite existing output file
        date_str: Date string in YYYY-MM-DD format (for ATR calculation)
        config: Configuration object

    Returns:
        True if processing was successful, False otherwise
    """
    # Check if output file already exists
    if os.path.exists(output_file) and not force:
        logger.info(f"Output file {output_file} already exists, skipping (use --force to overwrite)")
        return True

    try:
        # Load raw data
        df = load_raw_l2_data(raw_file)

        # Calculate features
        df = calculate_features(df, depth)

        # Resample to 1-second intervals
        resampled = resample_to_1s(df, method=rollup_method)

        # Calculate post-resample features with ATR
        resampled = calculate_post_resample_features(resampled, date_str=date_str, config=config)

        # Ensure timestamps are UTC-naive before saving (PERFORMANCE OPTIMIZATION)
        resampled['timestamp'] = vectorized_to_utc_naive(resampled['timestamp'])
        assert resampled['timestamp'].dt.tz is None, "ETL produced tz-aware timestamp!"

        # Save to parquet
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        resampled.to_parquet(output_file, compression='snappy')

        logger.info(f"Saved {len(resampled)} rows to {output_file}")
        return True

    except Exception as e:
        logger.error(f"Error processing {raw_file}: {e}")
        return False

def main():
    """Main entry point."""
    args = parse_args()

    # Load config
    config = load_config('configs/base.yaml')

    # Get paths from config if not provided
    raw_dir = args.raw_dir or config.data_paths.raw_l2_dir
    out_dir = args.out_dir or config.data_paths.feature_1s_dir

    # Get depth from config if not provided
    depth = args.depth or config.microstructure.depth_levels

    # Get rollup method from config
    rollup_method = config.etl.l20_to_1s.rollup_method

    # Parse date
    date_str = args.date
    date = datetime.strptime(date_str, "%Y-%m-%d")

    logger.info(f"Processing data for {date_str} with depth={depth}")

    # Find raw files for the date
    raw_dir_path = Path(raw_dir)
    date_pattern = date.strftime("%Y%m%d")
    date_pattern_dash = date.strftime("%Y-%m-%d")

    # Look for files in multiple patterns:
    # 1. New format: /l2_raw/2025-03-01/BTC_00_l2Book.arrow
    # 2. Old format: /l2_raw/20250301/BTC_0_l2Book.txt
    # 3. Direct files: /l2_raw/lob_20250301_13.arrow

    raw_files = []

    # Check new format directory (YYYY-MM-DD)
    new_format_dir = raw_dir_path / date_pattern_dash
    if new_format_dir.exists():
        raw_files.extend(list(new_format_dir.glob("BTC_*_l2Book.arrow")))
        raw_files.extend(list(new_format_dir.glob("BTC_*_l2Book.txt")))
        raw_files.extend(list(new_format_dir.glob("*.parquet")))

    # Check old format directory (YYYYMMDD)
    old_format_dir = raw_dir_path / date_pattern
    if old_format_dir.exists():
        raw_files.extend(list(old_format_dir.glob("BTC_*_l2Book.txt")))
        raw_files.extend(list(old_format_dir.glob("BTC_*_l2Book.arrow")))
        raw_files.extend(list(old_format_dir.glob("*.parquet")))

    # Check direct files in base directory
    raw_files.extend(list(raw_dir_path.glob(f"*{date_pattern}*.txt")))
    raw_files.extend(list(raw_dir_path.glob(f"*{date_pattern}*.arrow")))
    raw_files.extend(list(raw_dir_path.glob(f"*{date_pattern}*.parquet")))

    if not raw_files:
        logger.error(f"No raw files found for {date_str} in {raw_dir}")
        return 1

    logger.info(f"Found {len(raw_files)} raw files")

    # Check if we need to delete existing files for the day
    if args.overwrite:
        out_dir_path = Path(out_dir) / date_pattern_dash  # Use YYYY-MM-DD format for output
        existing_files = list(out_dir_path.glob(f"features_*.parquet"))
        if existing_files:
            logger.info(f"Deleting {len(existing_files)} existing parquet files for {date_str}")
            for file in existing_files:
                try:
                    file.unlink()
                    logger.info(f"Deleted {file}")
                except Exception as e:
                    logger.error(f"Failed to delete {file}: {e}")

    # Process the whole day at once
    if args.overwrite:
        logger.info("Processing whole day at once before chunking into hourly files")

        # Load all raw data for the day
        all_data = []
        for raw_file in sorted(raw_files):
            try:
                df = load_raw_l2_data(str(raw_file))
                all_data.append(df)
                logger.info(f"Loaded {len(df)} rows from {raw_file}")
            except Exception as e:
                logger.error(f"Error loading {raw_file}: {e}")
                continue

        if not all_data:
            logger.error("Failed to load any data for the day")
            return 1

        # Concatenate all data
        combined_df = pd.concat(all_data, ignore_index=True)
        logger.info(f"Combined {len(combined_df)} rows for the day")

        # Calculate features
        combined_df = calculate_features(combined_df, depth)

        # Resample to 1-second intervals
        resampled = resample_to_1s(combined_df, method=rollup_method)

        # Calculate post-resample features with ATR
        resampled = calculate_post_resample_features(resampled, date_str=date_str, config=config)

        # Log ATR statistics for debugging
        if 'atr_14_sec' in resampled.columns:
            nan_count = resampled['atr_14_sec'].isna().sum()
            total_count = len(resampled)
            nan_ratio = nan_count / total_count if total_count > 0 else 0.0
            logger.info(f"ATR statistics: {nan_count}/{total_count} NaN values ({nan_ratio:.3f} ratio)")
        else:
            logger.info("atr_14_sec column not found - will be generated by feature builder")

        # Split into hourly chunks and save
        resampled['hour'] = resampled['timestamp'].dt.hour
        success_count = 0
        total_hours = len(resampled['hour'].unique())

        for hour, hour_df in resampled.groupby('hour'):
            hour_df = hour_df.drop(columns=['hour'])

            # Ensure timestamps are UTC-naive before saving (PERFORMANCE OPTIMIZATION)
            hour_df['timestamp'] = vectorized_to_utc_naive(hour_df['timestamp'])
            assert hour_df['timestamp'].dt.tz is None, "ETL produced tz-aware timestamp!"

            output_file = Path(out_dir) / date_pattern_dash / f"features_{hour:02d}.parquet"
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            hour_df.to_parquet(str(output_file), compression='snappy')
            logger.info(f"Saved {len(hour_df)} rows to {output_file}")
            success_count += 1

        logger.info(f"Processed {success_count}/{total_hours} hours successfully")
        return 0 if success_count == total_hours else 1

    # Process each file individually (original behavior)
    else:
        success_count = 0
        for raw_file in sorted(raw_files):
            # Determine hour from filename
            hour = None
            file_name = raw_file.name

            # Try to extract hour from filename patterns like:
            # - lob_20250525_13.arrow (hour = 13)
            # - BTC_8_l2Book.txt (hour = 8)
            if '_' in file_name:
                parts = file_name.split('_')
                for part in parts:
                    if part.isdigit() and 0 <= int(part) <= 23:
                        hour = int(part)
                        break

            # If hour not found in filename, default to processing as a single day
            if hour is None:
                logger.warning(f"Could not determine hour from filename {file_name}, processing as full day")
                output_file = Path(out_dir) / date_pattern_dash / f"features_full_day.parquet"
                if process_hour(str(raw_file), str(output_file), depth, rollup_method, args.force, date_str, config):
                    success_count += 1
            else:
                # Process as hourly file
                output_file = Path(out_dir) / date_pattern_dash / f"features_{hour:02d}.parquet"
                if process_hour(str(raw_file), str(output_file), depth, rollup_method, args.force, date_str, config):
                    success_count += 1

        logger.info(f"Processed {success_count}/{len(raw_files)} files successfully")
        return 0 if success_count == len(raw_files) else 1

if __name__ == "__main__":
    sys.exit(main())
