"""
Production Architecture - Concrete Implementation Examples
==========================================================

These are actual code patterns for the proposed architecture.
"""

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
from pathlib import Path
import pyarrow.parquet as pq
import logging
from functools import lru_cache
import aiofiles


# ==============================================================================
# 1. UNIFIED DATA LAYER
# ==============================================================================

class DataResolution(Enum):
    TICK = "tick"
    SECOND_1 = "1s"
    MINUTE_1 = "1m"
    HOUR_1 = "1h"
    DAY_1 = "1d"


@dataclass
class DataRequest:
    """Immutable data request specification."""
    symbol: str
    start: datetime
    end: datetime
    resolution: DataResolution
    features: List[str]
    
    @property
    def cache_key(self) -> str:
        """Generate unique cache key for this request."""
        feature_str = "-".join(sorted(self.features))
        return f"{self.symbol}:{self.resolution.value}:{self.start.isoformat()}:{self.end.isoformat()}:{feature_str}"


class DataValidator:
    """Validates data integrity and structure."""
    
    @staticmethod
    def validate_dataframe(df: pd.DataFrame, schema: Dict[str, type]) -> Tuple[bool, List[str]]:
        """Validate DataFrame against schema."""
        errors = []
        
        # Check required columns
        missing = set(schema.keys()) - set(df.columns)
        if missing:
            errors.append(f"Missing columns: {missing}")
        
        # Check data types
        for col, expected_type in schema.items():
            if col in df.columns and not df[col].dtype == expected_type:
                errors.append(f"Column {col} has wrong type: {df[col].dtype} != {expected_type}")
        
        # Check for data issues
        if df.empty:
            errors.append("DataFrame is empty")
        
        if not df.index.is_monotonic_increasing:
            errors.append("Index is not monotonic increasing")
        
        return len(errors) == 0, errors


class HierarchicalCache:
    """Multi-level cache: Memory -> Disk -> Remote."""
    
    def __init__(self, memory_size_mb: int = 1000):
        self.memory_cache = {}  # In production: Use LRU cache
        self.memory_size_mb = memory_size_mb
        self.disk_cache_path = Path("cache/data")
        self.disk_cache_path.mkdir(parents=True, exist_ok=True)
    
    async def get(self, key: str) -> Optional[pd.DataFrame]:
        """Try to get from cache hierarchy."""
        # Level 1: Memory
        if key in self.memory_cache:
            return self.memory_cache[key]
        
        # Level 2: Disk
        disk_path = self.disk_cache_path / f"{key}.parquet"
        if disk_path.exists():
            df = pd.read_parquet(disk_path)
            # Promote to memory cache
            self.memory_cache[key] = df
            return df
        
        # Level 3: Remote cache (Redis/S3)
        # In production, implement remote cache lookup
        
        return None
    
    async def set(self, key: str, data: pd.DataFrame):
        """Store in cache hierarchy."""
        # Always store in memory (with eviction)
        self.memory_cache[key] = data
        
        # Async write to disk
        disk_path = self.disk_cache_path / f"{key}.parquet"
        await self._write_parquet_async(data, disk_path)
    
    async def _write_parquet_async(self, df: pd.DataFrame, path: Path):
        """Async parquet write to avoid blocking."""
        # In production: Use proper async parquet writer
        df.to_parquet(path)


class TradingDataStore:
    """
    Production-grade unified data layer.
    Single source of truth for all trading data.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.cache = HierarchicalCache()
        self.validator = DataValidator()
        self.logger = logging.getLogger(__name__)
        
        # Data sources in priority order
        self.data_sources = [
            EnhancedHourlySource(config),
            FeatureFileSource(config),
            RawDataSource(config)
        ]
    
    async def get_data(self, request: DataRequest) -> pd.DataFrame:
        """
        Get data with guaranteed structure.
        Handles missing data gracefully.
        """
        # Check cache first
        cached = await self.cache.get(request.cache_key)
        if cached is not None:
            self.logger.debug(f"Cache hit for {request.cache_key}")
            return cached
        
        # Try each data source
        for source in self.data_sources:
            if source.supports(request):
                try:
                    data = await source.load(request)
                    
                    # Validate
                    schema = self._get_schema(request.resolution)
                    is_valid, errors = self.validator.validate_dataframe(data, schema)
                    
                    if is_valid:
                        # Cache and return
                        await self.cache.set(request.cache_key, data)
                        return data
                    else:
                        self.logger.warning(f"Validation errors from {source}: {errors}")
                        
                except Exception as e:
                    self.logger.error(f"Error loading from {source}: {e}")
                    continue
        
        # All sources failed - return valid empty DataFrame
        return self._create_empty_dataframe(request)
    
    def _get_schema(self, resolution: DataResolution) -> Dict[str, type]:
        """Get expected schema for resolution."""
        base_schema = {
            'open': np.float64,
            'high': np.float64,
            'low': np.float64,
            'close': np.float64,
            'volume': np.float64
        }
        
        if resolution in [DataResolution.SECOND_1, DataResolution.MINUTE_1]:
            base_schema.update({
                'volume_imbalance': np.float64,
                'spread_mean': np.float64,
                'spread_std': np.float64
            })
        
        return base_schema
    
    def _create_empty_dataframe(self, request: DataRequest) -> pd.DataFrame:
        """Create valid empty DataFrame with correct structure."""
        # Generate timestamps
        freq_map = {
            DataResolution.SECOND_1: '1s',
            DataResolution.MINUTE_1: '1min',
            DataResolution.HOUR_1: '1h',
            DataResolution.DAY_1: '1d'
        }
        
        index = pd.date_range(
            start=request.start,
            end=request.end,
            freq=freq_map[request.resolution]
        )
        
        # Create DataFrame with schema
        schema = self._get_schema(request.resolution)
        df = pd.DataFrame(index=index)
        
        for col, dtype in schema.items():
            df[col] = np.nan
            df[col] = df[col].astype(dtype)
        
        return df


# ==============================================================================
# 2. FEATURE ENGINEERING PIPELINE
# ==============================================================================

class Feature(ABC):
    """Base class for all features."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        pass
    
    @property
    @abstractmethod
    def dependencies(self) -> List[str]:
        """List of feature names this depends on."""
        pass
    
    @abstractmethod
    def compute(self, data: pd.DataFrame) -> pd.Series:
        """Compute the feature."""
        pass


class EMAFeature(Feature):
    """Exponential Moving Average feature."""
    
    def __init__(self, period: int, column: str = 'close'):
        self.period = period
        self.column = column
    
    @property
    def name(self) -> str:
        return f"ema_{self.period}"
    
    @property 
    def dependencies(self) -> List[str]:
        return [self.column]
    
    def compute(self, data: pd.DataFrame) -> pd.Series:
        return data[self.column].ewm(span=self.period, adjust=False).mean()


class ATRFeature(Feature):
    """Average True Range feature."""
    
    def __init__(self, period: int = 14):
        self.period = period
    
    @property
    def name(self) -> str:
        return f"atr_{self.period}"
    
    @property
    def dependencies(self) -> List[str]:
        return ['high', 'low', 'close']
    
    def compute(self, data: pd.DataFrame) -> pd.Series:
        high_low = data['high'] - data['low']
        high_close = (data['high'] - data['close'].shift()).abs()
        low_close = (data['low'] - data['close'].shift()).abs()
        
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        return true_range.rolling(self.period).mean()


class FeatureEngine:
    """
    Centralized feature computation with dependency resolution.
    Computes features efficiently in correct order.
    """
    
    def __init__(self):
        self.features = {}
        self.computation_order = []
        self._register_features()
        self._resolve_dependencies()
    
    def _register_features(self):
        """Register all available features."""
        # Base features
        self.features['close'] = None  # Raw data
        self.features['high'] = None
        self.features['low'] = None
        self.features['volume'] = None
        
        # Computed features
        self.features['ema_12'] = EMAFeature(12)
        self.features['ema_26'] = EMAFeature(26)
        self.features['atr_14'] = ATRFeature(14)
        # Add more features...
    
    def _resolve_dependencies(self):
        """Topological sort to determine computation order."""
        # Simple implementation - in production use proper graph library
        computed = set()
        order = []
        
        # First add base features
        for name, feature in self.features.items():
            if feature is None:
                order.append(name)
                computed.add(name)
        
        # Then add computed features in dependency order
        while len(computed) < len(self.features):
            for name, feature in self.features.items():
                if name not in computed and feature is not None:
                    if all(dep in computed for dep in feature.dependencies):
                        order.append(name)
                        computed.add(name)
        
        self.computation_order = order
    
    def compute_features(self, 
                        base_data: pd.DataFrame,
                        required_features: List[str]) -> pd.DataFrame:
        """
        Compute requested features efficiently.
        Uses caching and vectorization.
        """
        result = base_data.copy()
        computed_features = set(base_data.columns)
        
        # Compute in dependency order
        for feature_name in self.computation_order:
            if feature_name in required_features and feature_name not in computed_features:
                feature = self.features[feature_name]
                
                if feature is not None:
                    # Check if dependencies are satisfied
                    if all(dep in result.columns for dep in feature.dependencies):
                        result[feature_name] = feature.compute(result)
                        computed_features.add(feature_name)
        
        # Return only requested features
        available = [f for f in required_features if f in result.columns]
        return result[available]


# ==============================================================================
# 3. STRATEGY FRAMEWORK
# ==============================================================================

@dataclass
class MarketState:
    """Immutable market state snapshot."""
    timestamp: datetime
    features: pd.Series
    regime: str
    confidence: float
    position: Optional['Position']


@dataclass
class Signal:
    """Trading signal with full context."""
    timestamp: datetime
    direction: str  # 'long', 'short', 'close'
    confidence: float
    size_fraction: float  # Fraction of capital
    stop_loss: Optional[float]
    take_profit: Optional[float]
    metadata: Dict[str, Any]


class Strategy(ABC):
    """Base strategy class with hooks for testing."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def evaluate(self, state: MarketState) -> Optional[Signal]:
        """Evaluate strategy with pre/post conditions."""
        # Pre-conditions
        if not self._check_preconditions(state):
            return None
        
        # Generate signal
        signal = self._generate_signal(state)
        
        if signal is None:
            return None
        
        # Post-conditions
        if not self._check_postconditions(signal, state):
            self.logger.warning(f"Signal failed post-conditions: {signal}")
            return None
        
        return signal
    
    def _check_preconditions(self, state: MarketState) -> bool:
        """Override to add strategy-specific preconditions."""
        # Example: Check required features exist
        required = self.get_required_features()
        missing = [f for f in required if f not in state.features.index]
        
        if missing:
            self.logger.debug(f"Missing required features: {missing}")
            return False
        
        return True
    
    @abstractmethod
    def _generate_signal(self, state: MarketState) -> Optional[Signal]:
        """Core strategy logic."""
        pass
    
    def _check_postconditions(self, signal: Signal, state: MarketState) -> bool:
        """Validate generated signal."""
        # Size limits
        if signal.size_fraction <= 0 or signal.size_fraction > 1:
            return False
        
        # Stop loss validation
        if signal.stop_loss is not None:
            current_price = state.features['close']
            if signal.direction == 'long' and signal.stop_loss >= current_price:
                return False
            if signal.direction == 'short' and signal.stop_loss <= current_price:
                return False
        
        return True
    
    @abstractmethod
    def get_required_features(self) -> List[str]:
        """List features required by this strategy."""
        pass


# ==============================================================================
# 4. EXECUTION ENGINE
# ==============================================================================

@dataclass
class Position:
    """Immutable position state."""
    entry_time: datetime
    entry_price: float
    size: float
    direction: str
    stop_loss: Optional[float]
    take_profit: Optional[float]
    metadata: Dict[str, Any]


class RiskManager:
    """Centralized risk management."""
    
    def __init__(self, config: Dict[str, Any]):
        self.max_position_size = config.get('max_position_size', 0.25)
        self.max_positions = config.get('max_positions', 1)
        self.max_daily_loss = config.get('max_daily_loss', 0.05)
        self.daily_loss = 0.0
    
    def check_signal(self, 
                     signal: Signal,
                     portfolio_value: float,
                     positions: List[Position]) -> Tuple[bool, str]:
        """Check if signal passes risk rules."""
        # Position count
        if len(positions) >= self.max_positions:
            return False, "Max positions reached"
        
        # Position size
        if signal.size_fraction > self.max_position_size:
            return False, f"Size {signal.size_fraction} exceeds max {self.max_position_size}"
        
        # Daily loss limit
        if self.daily_loss >= self.max_daily_loss:
            return False, f"Daily loss limit reached: {self.daily_loss:.2%}"
        
        return True, "OK"


class ExecutionEngine:
    """
    Handles all trade execution with proper state management.
    Ensures consistency and tracks all operations.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.risk_manager = RiskManager(config.get('risk', {}))
        self.positions: List[Position] = []
        self.portfolio_value = config.get('initial_capital', 10000)
        self.logger = logging.getLogger(__name__)
    
    async def execute_signal(self, signal: Signal) -> Optional[Position]:
        """
        Execute signal with full validation and state management.
        """
        # Risk checks
        passed, reason = self.risk_manager.check_signal(
            signal, self.portfolio_value, self.positions
        )
        
        if not passed:
            self.logger.info(f"Signal rejected by risk manager: {reason}")
            return None
        
        # Calculate position size
        position_size = self._calculate_position_size(signal)
        
        # Create position
        position = Position(
            entry_time=signal.timestamp,
            entry_price=signal.metadata.get('price', 0),  # Should come from market data
            size=position_size,
            direction=signal.direction,
            stop_loss=signal.stop_loss,
            take_profit=signal.take_profit,
            metadata=signal.metadata
        )
        
        # Update state
        self.positions.append(position)
        
        # Log execution
        self.logger.info(
            f"Executed {position.direction} position: "
            f"size={position.size:.4f} @ {position.entry_price:.2f}"
        )
        
        return position
    
    def _calculate_position_size(self, signal: Signal) -> float:
        """Calculate actual position size from signal."""
        # Kelly criterion or fixed fractional
        capital_to_risk = self.portfolio_value * signal.size_fraction
        
        # Adjust for leverage
        max_leverage = self.config.get('max_leverage', 10)
        
        # In production: More sophisticated sizing
        return capital_to_risk / signal.metadata.get('price', 1)


# ==============================================================================
# 5. BACKTEST ENGINE
# ==============================================================================

class ProductionBacktestEngine:
    """
    High-performance backtesting engine.
    Processes entire dataset in one pass.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.data_store = TradingDataStore(config)
        self.feature_engine = FeatureEngine()
        self.execution_engine = ExecutionEngine(config)
        self.logger = logging.getLogger(__name__)
    
    async def run_backtest(self,
                          strategy: Strategy,
                          start_date: datetime,
                          end_date: datetime) -> Dict[str, Any]:
        """
        Run efficient backtest.
        """
        # Load all data upfront
        data_request = DataRequest(
            symbol=self.config['symbol'],
            start=start_date - timedelta(days=100),  # Include warmup
            end=end_date,
            resolution=DataResolution.HOUR_1,
            features=strategy.get_required_features()
        )
        
        self.logger.info("Loading data...")
        data = await self.data_store.get_data(data_request)
        
        # Compute all features upfront
        self.logger.info("Computing features...")
        feature_data = self.feature_engine.compute_features(
            data, strategy.get_required_features()
        )
        
        # Process in single pass
        self.logger.info("Running backtest...")
        results = await self._process_backtest(
            strategy, feature_data, start_date, end_date
        )
        
        return results
    
    async def _process_backtest(self,
                               strategy: Strategy,
                               data: pd.DataFrame,
                               start_date: datetime,
                               end_date: datetime) -> Dict[str, Any]:
        """Process backtest efficiently."""
        trades = []
        
        # Vectorized where possible
        mask = (data.index >= start_date) & (data.index <= end_date)
        backtest_data = data[mask]
        
        for timestamp, row in backtest_data.iterrows():
            # Create market state
            state = MarketState(
                timestamp=timestamp,
                features=row,
                regime="Unknown",  # Would come from regime detector
                confidence=0.5,
                position=self.execution_engine.positions[-1] if self.execution_engine.positions else None
            )
            
            # Get signal
            signal = strategy.evaluate(state)
            
            if signal:
                # Execute
                position = await self.execution_engine.execute_signal(signal)
                if position:
                    trades.append(position)
        
        # Calculate metrics
        return {
            'trades': trades,
            'total_return': self._calculate_return(trades),
            'sharpe_ratio': self._calculate_sharpe(trades),
            'max_drawdown': self._calculate_max_drawdown(trades)
        }
    
    def _calculate_return(self, trades: List[Position]) -> float:
        """Calculate total return."""
        # Simplified - in production use proper P&L calculation
        return 0.0
    
    def _calculate_sharpe(self, trades: List[Position]) -> float:
        """Calculate Sharpe ratio."""
        return 0.0
    
    def _calculate_max_drawdown(self, trades: List[Position]) -> float:
        """Calculate maximum drawdown."""
        return 0.0


# ==============================================================================
# 6. MONITORING AND METRICS
# ==============================================================================

class MetricsCollector:
    """Collect and expose metrics for monitoring."""
    
    def __init__(self):
        self.metrics = {}
    
    def record_latency(self, operation: str, duration_ms: float):
        """Record operation latency."""
        if operation not in self.metrics:
            self.metrics[operation] = []
        self.metrics[operation].append(duration_ms)
    
    def get_percentile(self, operation: str, percentile: float) -> float:
        """Get latency percentile."""
        if operation in self.metrics:
            return np.percentile(self.metrics[operation], percentile)
        return 0.0


# ==============================================================================
# USAGE EXAMPLE
# ==============================================================================

async def main():
    """Example usage of production architecture."""
    
    # Configuration
    config = {
        'symbol': 'BTC-USD',
        'initial_capital': 10000,
        'risk': {
            'max_position_size': 0.25,
            'max_positions': 1,
            'max_daily_loss': 0.05
        }
    }
    
    # Initialize components
    data_store = TradingDataStore(config)
    feature_engine = FeatureEngine()
    
    # Load data with automatic caching and validation
    request = DataRequest(
        symbol='BTC-USD',
        start=datetime(2024, 1, 1),
        end=datetime(2024, 12, 31),
        resolution=DataResolution.HOUR_1,
        features=['open', 'high', 'low', 'close', 'volume']
    )
    
    data = await data_store.get_data(request)
    print(f"Loaded {len(data)} rows of validated data")
    
    # Compute features efficiently
    features = feature_engine.compute_features(
        data, ['ema_12', 'ema_26', 'atr_14']
    )
    print(f"Computed features: {features.columns.tolist()}")
    
    # Run backtest
    engine = ProductionBacktestEngine(config)
    
    # Example strategy would go here
    # results = await engine.run_backtest(strategy, start, end)


if __name__ == "__main__":
    asyncio.run(main())