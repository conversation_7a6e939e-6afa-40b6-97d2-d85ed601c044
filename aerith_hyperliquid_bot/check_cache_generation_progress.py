#!/usr/bin/env python3
"""
Monitor the regime cache generation progress and analyze the results.
"""

import pandas as pd
from pathlib import Path
import time

def check_cache_progress():
    """Check if 60s cache file exists and analyze its contents."""
    cache_dir = Path("data/precomputed_regimes")
    cache_60s = cache_dir / "regimes_2024_60s.parquet"
    cache_hourly = cache_dir / "regimes_2024.parquet"
    
    print("="*60)
    print("REGIME CACHE STATUS")
    print("="*60)
    
    # Check if 60s cache exists
    if cache_60s.exists():
        print(f"\n✅ 60s cache found: {cache_60s}")
        
        # Load and analyze
        df_60s = pd.read_parquet(cache_60s)
        print(f"\nTotal entries: {len(df_60s):,}")
        
        # Calculate intervals
        time_diffs = df_60s.index.to_series().diff().dropna()
        avg_interval = time_diffs.mean().total_seconds()
        
        print(f"Average interval: {avg_interval:.1f} seconds")
        print(f"Date range: {df_60s.index.min()} to {df_60s.index.max()}")
        
        # Regime distribution
        print("\nRegime distribution (60s):")
        regime_counts = df_60s['regime'].value_counts()
        for regime, count in regime_counts.items():
            pct = count / len(df_60s) * 100
            print(f"  {regime}: {count:,} ({pct:.1f}%)")
        
        # Compare with hourly cache
        if cache_hourly.exists():
            df_hourly = pd.read_parquet(cache_hourly)
            print(f"\n📊 COMPARISON:")
            print(f"Hourly cache: {len(df_hourly):,} entries")
            print(f"60s cache: {len(df_60s):,} entries")
            print(f"Ratio: {len(df_60s) / len(df_hourly):.1f}x more frequent")
            
            # Regime changes
            changes_60s = (df_60s['regime'] != df_60s['regime'].shift()).sum() - 1
            changes_hourly = (df_hourly['regime'] != df_hourly['regime'].shift()).sum() - 1
            
            print(f"\nRegime changes:")
            print(f"  Hourly: {changes_hourly:,} changes")
            print(f"  60s: {changes_60s:,} changes ({changes_60s/changes_hourly:.1f}x more)")
            
            print(f"\nChanges per day:")
            days_60s = (df_60s.index.max() - df_60s.index.min()).days
            print(f"  Hourly: {changes_hourly / days_60s:.1f}")
            print(f"  60s: {changes_60s / days_60s:.1f}")
            
        print("\n✅ Cache generation complete! Ready to run backtest.")
        return True
        
    else:
        print(f"\n⏳ 60s cache not found yet: {cache_60s}")
        print("Cache generation still in progress...")
        
        # Check if generation started by looking for partial file
        temp_files = list(cache_dir.glob("*.tmp")) + list(cache_dir.glob("*.partial"))
        if temp_files:
            print(f"Found temporary files: {len(temp_files)}")
        
        return False

def estimate_time_remaining():
    """Estimate time remaining based on file size growth."""
    # This is a rough estimate based on the assumption that the file grows linearly
    print("\n⏰ Estimated time remaining:")
    print("Based on 366 days × 1440 minutes/day = 527,040 entries")
    print("At ~1 second per day processing = ~6 minutes total")
    print("Please wait for completion...")

if __name__ == "__main__":
    while True:
        if check_cache_progress():
            break
        else:
            estimate_time_remaining()
            print("\nChecking again in 30 seconds...")
            time.sleep(30)