# Aerith Hyperliquid Bot

## Project Goal

Develop a near-institutional-grade trading bot framework for Hyperliquid perpetual futures (initially BTC/PERP). The current focus follows a structured 13-week plan to first validate potential filters (external data, HMM) for a baseline Trend Following (TF) strategy. If specific performance gates are not met, the project will pivot to developing and validating strategies (starting with OBI Scalper) that directly exploit Hyperliquid's market microstructure. The ultimate goal is achieving a risk-adjusted target ROI (e.g., 10-30%) with acceptable drawdowns.

## Current Status (As of PRD v2.1 - 2025-04-19)

*   **Phase:** Starting **Phase 3.1: MCP Overlay** of the 13-week plan.
*   **Previous Work:**
    *   Established core framework (Data Handling, Portfolio, Backtester, Config).
    *   Implemented and investigated a 7-state Granular Microstructure (GMS) detector; **Archived** due to reliability issues.
    *   Improved execution simulation: Fixed slippage P/L, added Taker-Only and Probabilistic Maker modes (ProbMaker noted as optimistic), fixed reproducibility.
    *   Implemented backtest data saving (signals parquet, trades json) and visualization script.
*   **Next Task:** Integrate Fear & Greed data from Alternative.me API into the signal generation pipeline.

## Technology Stack

*   **Language:** Python 3.11+
*   **Core Libraries:** Pandas, NumPy, Pydantic v2, PyYAML, PyArrow, Matplotlib, mplfinance, JSON, httpx, scikit-learn (for HMM)
*   **TA Lib:** pandas-ta
*   **Testing:** pytest (planned/partially implemented)
*   **Configuration:** YAML (`config.yaml`) parsed by Pydantic models (`settings.py`)

## Setup

*(To be added: Instructions for setting up virtual environment, installing requirements)*

```bash
# Example Placeholder
# python -m venv venv
# source venv/bin/activate
# pip install -r requirements.txt
```

## Project Documentation

*   **Roadmap Overview:** See `roadmap.md` for the detailed 13-week plan.
*   **Project Structure:** See `project_structure.md` for details on modules and directories.
*   **AI Collaboration Context:** See `message_to_ai.md` for the latest status summary for AI partners.
