#!/usr/bin/env python3
"""
Analyze threshold calibration for March 2025 data
Shows why fixed thresholds don't work and what adaptive thresholds compute
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.resolve()
sys.path.insert(0, str(project_root))

def analyze_march_2025_data():
    """Analyze the actual data ranges for March 2025 to understand threshold calibration."""
    
    print("=== THRESHOLD CALIBRATION ANALYSIS FOR MARCH 2025 ===")
    print()
    
    # Load the signals from our recent test
    signals_file = "/Users/<USER>/Desktop/trading_bot_/logs/backtest_signals_20250530_010315.parquet"
    
    try:
        signals = pd.read_parquet(signals_file)
        print(f"Loaded signals data: {signals.shape}")
        print(f"Columns: {list(signals.columns)}")
        print()
        
        # Analyze ATR% (volatility) distribution
        if 'atr_percent' in signals.columns:
            atr_pct = signals['atr_percent'].dropna()
            print("=== ATR% (VOLATILITY) ANALYSIS ===")
            print(f"Count: {len(atr_pct)}")
            print(f"Min: {atr_pct.min():.6f} ({atr_pct.min()*100:.4f}%)")
            print(f"Max: {atr_pct.max():.6f} ({atr_pct.max()*100:.4f}%)")
            print(f"Mean: {atr_pct.mean():.6f} ({atr_pct.mean()*100:.4f}%)")
            print(f"Median: {atr_pct.median():.6f} ({atr_pct.median()*100:.4f}%)")
            print()
            
            # Calculate percentiles
            percentiles = [10, 25, 33, 50, 67, 75, 90]
            print("ATR% Percentiles:")
            for p in percentiles:
                val = np.percentile(atr_pct, p)
                print(f"  {p:2d}th: {val:.6f} ({val*100:.4f}%)")
            print()
            
            # Compare with fixed thresholds
            fixed_low = 0.01  # 1%
            fixed_high = 0.03  # 3%
            
            below_low = (atr_pct < fixed_low).sum()
            above_high = (atr_pct > fixed_high).sum()
            between = len(atr_pct) - below_low - above_high
            
            print("Fixed Threshold Analysis:")
            print(f"  Fixed Low Threshold: {fixed_low:.4f} ({fixed_low*100:.2f}%)")
            print(f"  Fixed High Threshold: {fixed_high:.4f} ({fixed_high*100:.2f}%)")
            print(f"  Below Low: {below_low}/{len(atr_pct)} ({below_low/len(atr_pct)*100:.1f}%)")
            print(f"  Between: {between}/{len(atr_pct)} ({between/len(atr_pct)*100:.1f}%)")
            print(f"  Above High: {above_high}/{len(atr_pct)} ({above_high/len(atr_pct)*100:.1f}%)")
            print()
            
            # Suggest adaptive thresholds (25th/75th percentiles)
            adaptive_low = np.percentile(atr_pct, 25)
            adaptive_high = np.percentile(atr_pct, 75)
            print("Suggested Adaptive Thresholds (25th/75th percentiles):")
            print(f"  Adaptive Low: {adaptive_low:.6f} ({adaptive_low*100:.4f}%)")
            print(f"  Adaptive High: {adaptive_high:.6f} ({adaptive_high*100:.4f}%)")
            print()
        
        # Analyze momentum (MA slope) distribution
        momentum_cols = ['ma_slope', 'sma_gms_slope']
        for col in momentum_cols:
            if col in signals.columns:
                momentum = signals[col].dropna()
                if len(momentum) > 0:
                    print(f"=== {col.upper()} (MOMENTUM) ANALYSIS ===")
                    print(f"Count: {len(momentum)}")
                    print(f"Min: {momentum.min():.6f}")
                    print(f"Max: {momentum.max():.6f}")
                    print(f"Mean: {momentum.mean():.6f}")
                    print(f"Median: {momentum.median():.6f}")
                    print(f"Std: {momentum.std():.6f}")
                    print()
                    
                    # Calculate percentiles
                    print(f"{col} Percentiles:")
                    for p in percentiles:
                        val = np.percentile(momentum, p)
                        print(f"  {p:2d}th: {val:.6f}")
                    print()
                    
                    # Compare with fixed thresholds
                    fixed_weak = 0.5
                    fixed_strong = 2.5
                    
                    abs_momentum = np.abs(momentum)
                    below_weak = (abs_momentum < fixed_weak).sum()
                    above_strong = (abs_momentum > fixed_strong).sum()
                    between = len(abs_momentum) - below_weak - above_strong
                    
                    print("Fixed Momentum Threshold Analysis:")
                    print(f"  Fixed Weak Threshold: {fixed_weak:.2f}")
                    print(f"  Fixed Strong Threshold: {fixed_strong:.2f}")
                    print(f"  Below Weak: {below_weak}/{len(abs_momentum)} ({below_weak/len(abs_momentum)*100:.1f}%)")
                    print(f"  Between: {between}/{len(abs_momentum)} ({between/len(abs_momentum)*100:.1f}%)")
                    print(f"  Above Strong: {above_strong}/{len(abs_momentum)} ({above_strong/len(abs_momentum)*100:.1f}%)")
                    print()
                    break
        
        # Analyze spread statistics
        spread_cols = ['spread_mean', 'spread_std']
        for col in spread_cols:
            if col in signals.columns:
                spread_data = signals[col].dropna()
                if len(spread_data) > 0:
                    print(f"=== {col.upper()} ANALYSIS ===")
                    print(f"Count: {len(spread_data)}")
                    print(f"Min: {spread_data.min():.8f}")
                    print(f"Max: {spread_data.max():.8f}")
                    print(f"Mean: {spread_data.mean():.8f}")
                    print(f"Median: {spread_data.median():.8f}")
                    print()
                    
                    # Calculate percentiles
                    print(f"{col} Percentiles:")
                    for p in percentiles:
                        val = np.percentile(spread_data, p)
                        print(f"  {p:2d}th: {val:.8f}")
                    print()
        
        # Summary and recommendations
        print("=== SUMMARY AND RECOMMENDATIONS ===")
        print()
        print("1. VOLATILITY (ATR%) ISSUE:")
        print("   - Fixed thresholds: 1.0% (low) / 3.0% (high)")
        print("   - Actual data range: ~0.28% to 1.91%")
        print("   - Result: Most data falls below 'low' threshold → Low_Vol_Range")
        print()
        print("2. MOMENTUM ISSUE:")
        print("   - Fixed thresholds: 0.5 (weak) / 2.5 (strong)")
        print("   - Actual data range: Near-zero values")
        print("   - Result: All data falls below 'weak' threshold → Weak momentum")
        print()
        print("3. COMBINATION EFFECT:")
        print("   - Low volatility + Weak momentum = Low_Vol_Range")
        print("   - Low_Vol_Range maps to CHOP")
        print("   - TF-v3 strategy filtered out during CHOP")
        print("   - Result: 0 trades")
        print()
        print("4. ADAPTIVE THRESHOLDS SOLUTION:")
        print("   - Automatically calibrate to actual data distribution")
        print("   - Use 25th/75th percentiles of observed data")
        print("   - Allow for proper regime classification")
        print("   - Enable strategy activation")
        print()
        print("CONCLUSION: Fixed thresholds are miscalibrated for March 2025 data.")
        print("Adaptive thresholds are REQUIRED for proper system operation.")
        
    except Exception as e:
        print(f"Error loading signals data: {e}")
        print("Make sure to run a test first to generate signals data.")

if __name__ == "__main__":
    analyze_march_2025_data()
