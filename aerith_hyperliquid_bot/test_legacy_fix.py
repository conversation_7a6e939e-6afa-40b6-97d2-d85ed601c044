#!/usr/bin/env python3
"""
Test script to verify that the Legacy System fix is working correctly.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.resolve()
sys.path.insert(0, str(project_root))

def test_legacy_fix():
    """Test that the correct detector is being instantiated."""
    print("=== TESTING LEGACY SYSTEM FIX ===")
    print("Running short backtest to verify correct detector...")
    
    from hyperliquid_bot.backtester.run_backtest import main
    
    # Set command line arguments for the legacy configuration
    sys.argv = [
        'test_legacy_fix.py',
        '--override', 'configs/legacy_profile.yaml',
        '--timeframe', '1h',
        '--run-id', 'legacy_fix_verification',
        '--skip-validation-warnings'
    ]
    
    try:
        main()
        print("=== TEST COMPLETE ===")
        print("Check the logs above for 'Instantiating GranularMicrostructureRegimeDetector'")
    except Exception as e:
        print(f"Error during test: {e}")
        raise

if __name__ == "__main__":
    test_legacy_fix() 