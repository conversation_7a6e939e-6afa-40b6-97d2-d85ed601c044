#!/usr/bin/env python3
"""
Diagnose why quality scores are consistently low in the enhanced detector.
Analyze each component score to understand what's blocking trades.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import pandas as pd
import numpy as np
from datetime import datetime
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.enhanced_regime_detector import EnhancedRegimeDetector

def analyze_quality_scores():
    """Analyze quality score components from enhanced hourly data."""
    
    # Load config with adjusted thresholds
    config_path = Path(__file__).parent / "configs/overrides/modern_system_v2_adjusted_thresholds.yaml"
    config = load_config(str(config_path))
    
    # Initialize detector
    detector = EnhancedRegimeDetector(config)
    
    # Load sample data
    print("Loading enhanced hourly data for analysis...")
    file = "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/../hyperliquid_data/enhanced_hourly/1h/2024-02-02_1h_enhanced.parquet"
    data = pd.read_parquet(file)
    
    print(f"Loaded {len(data)} hours of data")
    print("\n" + "="*80)
    print("QUALITY SCORE COMPONENT ANALYSIS")
    print("="*80)
    
    # Analyze each hour's quality score
    spread_scores = []
    momentum_scores = []
    volume_scores = []
    total_scores = []
    regimes = []
    
    for idx, row in data.iterrows():
        # Prepare signals
        signals = {
            'atr_percent': row.get('atr_percent_sec', np.nan),
            'ma_slope': row.get('ma_slope', np.nan),
            f'obi_smoothed_{config.microstructure.depth_levels}': row.get('volume_imbalance', np.nan),
            'spread_mean': row.get('spread_mean', np.nan),
            'spread_std': row.get('spread_std', np.nan),
            'volume': row.get('volume', 0)
        }
        
        # Skip if core signals are missing
        if pd.isna(signals['atr_percent']) or pd.isna(signals['ma_slope']):
            continue
            
        # Get regime
        regime = detector.detect_regime(signals)
        regimes.append(regime)
        
        # Calculate individual scores
        spread_score = detector._calculate_spread_score(signals)
        momentum_score = detector._calculate_momentum_score(signals)
        volume_score = detector._calculate_volume_score(signals)
        
        spread_scores.append(spread_score)
        momentum_scores.append(momentum_score)
        volume_scores.append(volume_score)
        
        # Calculate total
        total = (detector.spread_weight * spread_score +
                detector.momentum_weight * momentum_score +
                detector.volume_weight * volume_score)
        total_scores.append(total)
    
    # Analyze distributions
    print("\nComponent Score Statistics:")
    print(f"\nSpread Score (40% weight):")
    print(f"  Mean: {np.mean(spread_scores):.3f}")
    print(f"  Min:  {np.min(spread_scores):.3f}")
    print(f"  Max:  {np.max(spread_scores):.3f}")
    print(f"  50th percentile: {np.percentile(spread_scores, 50):.3f}")
    print(f"  75th percentile: {np.percentile(spread_scores, 75):.3f}")
    
    print(f"\nMomentum Score (40% weight):")
    print(f"  Mean: {np.mean(momentum_scores):.3f}")
    print(f"  Min:  {np.min(momentum_scores):.3f}")
    print(f"  Max:  {np.max(momentum_scores):.3f}")
    print(f"  50th percentile: {np.percentile(momentum_scores, 50):.3f}")
    print(f"  75th percentile: {np.percentile(momentum_scores, 75):.3f}")
    
    print(f"\nVolume Score (20% weight):")
    print(f"  Mean: {np.mean(volume_scores):.3f}")
    print(f"  Min:  {np.min(volume_scores):.3f}")
    print(f"  Max:  {np.max(volume_scores):.3f}")
    
    print(f"\nTotal Quality Score:")
    print(f"  Mean: {np.mean(total_scores):.3f}")
    print(f"  Min:  {np.min(total_scores):.3f}")
    print(f"  Max:  {np.max(total_scores):.3f}")
    print(f"  50th percentile: {np.percentile(total_scores, 50):.3f}")
    print(f"  75th percentile: {np.percentile(total_scores, 75):.3f}")
    print(f"  90th percentile: {np.percentile(total_scores, 90):.3f}")
    
    # Check how many pass the threshold
    threshold = detector.quality_threshold
    passing = sum(1 for s in total_scores if s >= threshold)
    print(f"\nQuality Threshold: {threshold:.2f}")
    print(f"Passing Scores: {passing}/{len(total_scores)} ({passing/len(total_scores)*100:.1f}%)")
    
    # Analyze by regime
    print("\n" + "="*80)
    print("QUALITY SCORES BY REGIME")
    print("="*80)
    
    regime_scores = {}
    for regime, score in zip(regimes, total_scores):
        if regime not in regime_scores:
            regime_scores[regime] = []
        regime_scores[regime].append(score)
    
    for regime, scores in sorted(regime_scores.items()):
        if len(scores) > 0:
            print(f"\n{regime}:")
            print(f"  Count: {len(scores)}")
            print(f"  Mean Quality: {np.mean(scores):.3f}")
            print(f"  Max Quality: {np.max(scores):.3f}")
            passing = sum(1 for s in scores if s >= threshold)
            print(f"  Would Pass: {passing}/{len(scores)} ({passing/len(scores)*100:.1f}%)")
    
    # Deep dive into low scores
    print("\n" + "="*80)
    print("LOW SCORE ANALYSIS")
    print("="*80)
    
    # Find which component is dragging scores down
    spread_contribution = [s * detector.spread_weight for s in spread_scores]
    momentum_contribution = [s * detector.momentum_weight for s in momentum_scores]
    volume_contribution = [s * detector.volume_weight for s in volume_scores]
    
    print(f"\nAverage contribution to total score:")
    print(f"  Spread:   {np.mean(spread_contribution):.3f} (out of {detector.spread_weight:.1f} possible)")
    print(f"  Momentum: {np.mean(momentum_contribution):.3f} (out of {detector.momentum_weight:.1f} possible)")
    print(f"  Volume:   {np.mean(volume_contribution):.3f} (out of {detector.volume_weight:.1f} possible)")
    
    # Check thresholds used
    print("\n" + "="*80)
    print("THRESHOLD CONFIGURATION")
    print("="*80)
    print(f"\nMomentum thresholds:")
    print(f"  Weak: {config.regime.gms_mom_weak_thresh}")
    print(f"  Strong: {config.regime.gms_mom_strong_thresh}")
    print(f"\nSpread thresholds:")
    print(f"  Mean low: {config.regime.gms_spread_mean_low_thresh}")
    print(f"  Std high: {config.regime.gms_spread_std_high_thresh}")
    print(f"\nVolatility thresholds:")
    print(f"  Low: {config.regime.gms_vol_low_thresh}")
    print(f"  High: {config.regime.gms_vol_high_thresh}")
    
    # Recommendations
    print("\n" + "="*80)
    print("RECOMMENDATIONS")
    print("="*80)
    
    # Calculate what threshold would allow reasonable trading
    sorted_scores = sorted(total_scores)
    p10 = sorted_scores[int(len(sorted_scores) * 0.1)]
    p20 = sorted_scores[int(len(sorted_scores) * 0.2)]
    p30 = sorted_scores[int(len(sorted_scores) * 0.3)]
    
    print(f"\nTo allow different percentages of trades:")
    print(f"  Top 10% of signals: quality_threshold = {p10:.2f}")
    print(f"  Top 20% of signals: quality_threshold = {p20:.2f}")
    print(f"  Top 30% of signals: quality_threshold = {p30:.2f}")
    
    print(f"\nCurrent threshold of {threshold:.2f} is too high!")
    print(f"Suggested new threshold: {p20:.2f} (allows top 20% of signals)")

if __name__ == "__main__":
    analyze_quality_scores()