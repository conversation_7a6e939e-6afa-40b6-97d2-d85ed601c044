#!/usr/bin/env python3
"""
Test the hypothesis that 60s vs hourly regime updates cause the 5x performance gap.

This test will:
1. Run modern backtest with hourly regime cache (current behavior)
2. Run modern backtest with simulated 60s regime updates 
3. Compare the results to see if regime frequency is the issue
"""

import logging
from datetime import datetime
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def run_test():
    """Run the regime frequency hypothesis test."""
    
    # Test on Q4 2024 where we have regime cache data
    start_date = datetime(2024, 10, 1)
    end_date = datetime(2024, 12, 31)
    
    logger.info("=" * 60)
    logger.info("REGIME FREQUENCY HYPOTHESIS TEST")
    logger.info("=" * 60)
    logger.info(f"Period: {start_date.date()} to {end_date.date()}")
    
    # 1. Run with hourly regime cache (current behavior)
    logger.info("\n1. Running backtest with HOURLY regime cache...")
    from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
    from hyperliquid_bot.config.settings import load_config
    
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Run with cache (hourly updates)
    engine_hourly = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=True  # Uses hourly cache
    )
    
    results_hourly = engine_hourly.run_backtest()
    
    logger.info("\nHOURLY REGIME RESULTS:")
    logger.info(f"  Total trades: {results_hourly['performance']['total_trades']}")
    logger.info(f"  Total return: {results_hourly['performance']['total_return']:.2%}")
    logger.info(f"  Win rate: {results_hourly['performance']['win_rate']:.2%}")
    
    # 2. Run without cache (simulated 60s updates)
    logger.info("\n2. Running backtest with SIMULATED 60s regime updates...")
    
    engine_60s = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=False  # Simulates 60s updates
    )
    
    results_60s = engine_60s.run_backtest()
    
    logger.info("\n60-SECOND REGIME RESULTS:")
    logger.info(f"  Total trades: {results_60s['performance']['total_trades']}")
    logger.info(f"  Total return: {results_60s['performance']['total_return']:.2%}")
    logger.info(f"  Win rate: {results_60s['performance']['win_rate']:.2%}")
    
    # 3. Compare results
    logger.info("\n" + "=" * 60)
    logger.info("COMPARISON RESULTS")
    logger.info("=" * 60)
    
    trade_diff = results_60s['performance']['total_trades'] - results_hourly['performance']['total_trades']
    return_diff = results_60s['performance']['total_return'] - results_hourly['performance']['total_return']
    
    logger.info(f"Trade count difference: {trade_diff} ({trade_diff/results_hourly['performance']['total_trades']*100:+.1f}%)")
    logger.info(f"Return difference: {return_diff:.2%} ({return_diff/results_hourly['performance']['total_return']*100:+.1f}%)")
    
    # Save detailed results
    comparison = {
        'test_period': f"{start_date.date()} to {end_date.date()}",
        'hourly_regime': {
            'total_trades': results_hourly['performance']['total_trades'],
            'total_return': results_hourly['performance']['total_return'],
            'win_rate': results_hourly['performance']['win_rate'],
            'runtime_seconds': results_hourly['runtime_seconds']
        },
        '60s_regime': {
            'total_trades': results_60s['performance']['total_trades'],
            'total_return': results_60s['performance']['total_return'],
            'win_rate': results_60s['performance']['win_rate'],
            'runtime_seconds': results_60s['runtime_seconds']
        },
        'differences': {
            'trade_count_diff': trade_diff,
            'trade_count_pct': trade_diff/results_hourly['performance']['total_trades']*100 if results_hourly['performance']['total_trades'] > 0 else 0,
            'return_diff': return_diff,
            'return_pct': return_diff/results_hourly['performance']['total_return']*100 if results_hourly['performance']['total_return'] != 0 else 0
        }
    }
    
    with open('regime_frequency_hypothesis_results.json', 'w') as f:
        json.dump(comparison, f, indent=2)
    
    logger.info(f"\nDetailed results saved to: regime_frequency_hypothesis_results.json")
    
    # Hypothesis verdict
    logger.info("\n" + "=" * 60)
    logger.info("HYPOTHESIS VERDICT")
    logger.info("=" * 60)
    
    # If 60s regime updates show significant improvement (>20% better returns)
    if return_diff > 0.2 * abs(results_hourly['performance']['total_return']):
        logger.info("✅ HYPOTHESIS CONFIRMED: 60s regime updates significantly improve performance!")
        logger.info("   The stale regime data (up to 59 min old) IS a major factor in the performance gap.")
    else:
        logger.info("❌ HYPOTHESIS REJECTED: 60s regime updates do NOT significantly improve performance.")
        logger.info("   The performance gap must be due to other factors:")
        logger.info("   - Position management (multiple positions)")
        logger.info("   - Exit logic differences")
        logger.info("   - Entry threshold differences")
        logger.info("   - Signal calculation differences")
    
    # Additional insights
    if abs(trade_diff) > 10:
        logger.info(f"\n⚠️  Large trade count difference ({trade_diff}) suggests regime frequency affects trade selection")
    
    logger.info("\n" + "=" * 60)

if __name__ == "__main__":
    run_test()