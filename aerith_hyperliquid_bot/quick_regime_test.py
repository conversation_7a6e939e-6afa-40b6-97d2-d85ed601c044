#!/usr/bin/env python3
"""
Quick test to check if regime update frequency matters.
Test on just 1 week of data to get fast results.
"""

import logging
from datetime import datetime
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def run_quick_test():
    """Run a quick 1-week test."""
    
    # Test on 1 week in Q4 2024
    start_date = datetime(2024, 10, 1)
    end_date = datetime(2024, 10, 7)  # Just 1 week
    
    logger.info("=" * 60)
    logger.info("QUICK REGIME FREQUENCY TEST (1 WEEK)")
    logger.info("=" * 60)
    logger.info(f"Period: {start_date.date()} to {end_date.date()}")
    
    from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
    from hyperliquid_bot.config.settings import load_config
    
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # 1. Run with hourly cache
    logger.info("\n1. Testing with HOURLY regime cache...")
    engine_hourly = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=True
    )
    
    results_hourly = engine_hourly.run_backtest()
    trades_hourly = results_hourly['performance']['total_trades']
    return_hourly = results_hourly['performance']['total_return']
    
    logger.info(f"HOURLY: {trades_hourly} trades, {return_hourly:.2%} return")
    
    # 2. Instead of simulating 60s updates, let's check regime change frequency
    # Count how many regime changes occurred in the cached data
    regime_history = results_hourly.get('regime_history', [])
    if regime_history:
        unique_regimes = set(r.get('regime', r.get('state')) for r in regime_history)
        logger.info(f"Unique regimes seen: {unique_regimes}")
        
        # Count regime changes
        regime_changes = 0
        last_regime = None
        for r in regime_history:
            current_regime = r.get('regime', r.get('state'))
            if last_regime and current_regime != last_regime:
                regime_changes += 1
            last_regime = current_regime
        
        logger.info(f"Regime changes in 1 week: {regime_changes}")
        logger.info(f"Average hours per regime: {len(regime_history) / (regime_changes + 1):.1f}")
    
    # Save results
    results = {
        'test_period': f"{start_date.date()} to {end_date.date()}",
        'hourly_trades': trades_hourly,
        'hourly_return': return_hourly,
        'regime_changes': regime_changes if 'regime_changes' in locals() else 0,
        'unique_regimes': list(unique_regimes) if 'unique_regimes' in locals() else []
    }
    
    with open('quick_regime_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info("\n" + "=" * 60)
    logger.info("ANALYSIS")
    logger.info("=" * 60)
    
    if regime_changes < 10:  # Less than 10 changes in a week
        logger.info("✅ Regimes are relatively stable (change every ~17 hours)")
        logger.info("   Hourly updates are probably sufficient")
    else:
        logger.info("⚠️  Regimes change frequently")
        logger.info("   60-second updates might capture more opportunities")
    
    logger.info("\nTo properly test the hypothesis, we would need to:")
    logger.info("1. Generate a proper 60s regime cache for 2024")
    logger.info("2. Compare backtest results with both caches")
    logger.info("3. OR fix the other known issues first:")
    logger.info("   - Position management (no multiple positions)")
    logger.info("   - Exit logic improvements")
    logger.info("   - Entry threshold tuning")

if __name__ == "__main__":
    run_quick_test()