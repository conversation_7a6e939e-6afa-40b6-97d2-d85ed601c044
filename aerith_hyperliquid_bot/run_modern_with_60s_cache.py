#!/usr/bin/env python3
"""
Run Modern system with 60-second regime cache.
This should significantly improve performance compared to hourly cache.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import json
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
from hyperliquid_bot.config.settings import load_config

def main():
    print("="*60)
    print("MODERN SYSTEM - WITH 60s REGIME CACHE")
    print("="*60)
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Update cache path to use 60s cache
    config.data_paths.cache_file = "data/precomputed_regimes/regimes_2024_60s.parquet"
    
    # Test period - full 2024
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 12, 31)
    
    print(f"\nTest Period: {start_date.date()} to {end_date.date()}")
    print("\nKey Configuration:")
    print("✅ Using 60-second regime cache")
    print(f"✅ Cache file: {config.data_paths.cache_file}")
    print("✅ Expected: ~525,600 regime updates (1 per minute)")
    print("-"*60)
    
    # Run with 60s cache
    print("\n🚀 Starting backtest...")
    engine = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=True  # Using cache, but now it's 60s!
    )
    
    results = engine.run_backtest()
    
    if results:
        print("\n" + "="*60)
        print("RESULTS WITH 60s CACHE:")
        print("="*60)
        
        # Key metrics
        total_return = results.get('total_return', 0)
        total_trades = results.get('total_trades', 0)
        win_rate = results.get('win_rate', 0)
        sharpe_ratio = results.get('sharpe_ratio', 0)
        max_drawdown = results.get('max_drawdown', 0)
        
        print(f"\nPerformance Metrics:")
        print(f"  Total Return: {total_return:.2%}")
        print(f"  Total Trades: {total_trades}")
        print(f"  Win Rate: {win_rate:.1%}")
        print(f"  Sharpe Ratio: {sharpe_ratio:.2f}")
        print(f"  Max Drawdown: {max_drawdown:.2%}")
        
        # Calculate profit per trade
        profit_per_trade = (total_return / total_trades * 100) if total_trades > 0 else 0
        print(f"  Profit/Trade: {profit_per_trade:.2%}")
        
        # Comparison
        print("\n" + "="*60)
        print("COMPARISON:")
        print("="*60)
        
        print("\nModern (Hourly Cache):")
        print("  - Annual ROI: +41.78%")
        print("  - Trades: 222")
        print("  - Profit/Trade: 0.19%")
        
        print(f"\nModern (60s Cache):")
        print(f"  - Annual ROI: {total_return:.2%}")
        print(f"  - Trades: {total_trades}")
        print(f"  - Profit/Trade: {profit_per_trade:.2%}")
        
        print("\nLegacy (Target):")
        print("  - Annual ROI: +215%")
        print("  - Trades: 180")
        print("  - Profit/Trade: 1.19%")
        
        # Analysis
        print("\n📈 ANALYSIS:")
        improvement = (profit_per_trade - 0.19) / 0.19 * 100 if profit_per_trade > 0.19 else 0
        if improvement > 0:
            print(f"✅ Profit per trade IMPROVED by {improvement:.0f}%!")
            print("   60-second regime updates are working!")
        else:
            print("❌ No improvement - investigate further")
        
        # Save results
        with open('modern_60s_cache_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n📁 Results saved to: modern_60s_cache_results.json")

if __name__ == "__main__":
    main()