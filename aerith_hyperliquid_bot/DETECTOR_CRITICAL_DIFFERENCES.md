# CRITICAL DIFFERENCES BETWEEN LEGACY AND MODERN DETECTORS

## Summary of Key Findings

The modern system is configured with **COMPLETELY WRONG THRESHOLDS** that explain the 5x worse performance.

## 1. Detector Types
- **Legacy**: `granular_microstructure` (hourly updates)
- **Modern**: `continuous_modern_v2` (60-second updates)

## 2. CRITICAL THRESHOLD DIFFERENCES

### Momentum Thresholds (THE SMOKING GUN! 🔴)
```yaml
# LEGACY (granular_microstructure)
gms_mom_strong_thresh: 100.0    # MA slope units
gms_mom_weak_thresh: 50.0       # MA slope units

# MODERN (continuous_modern_v2) 
gms_mom_strong_thresh: 0.001    # 100,000x SMALLER!
gms_mom_weak_thresh: 0.0003     # 166,666x SMALLER!
```

**PROBLEM**: The modern system uses momentum thresholds that are 100,000x too small! This means:
- Legacy requires MA slope of 100 for "strong momentum"
- Modern requires MA slope of 0.001 for "strong momentum"
- **Result**: Modern sees "strong trends" in tiny noise movements!

### Volatility Thresholds
```yaml
# LEGACY
gms_vol_high_thresh: 0.0092     # 0.92% ATR
gms_vol_low_thresh: 0.0055      # 0.55% ATR

# MODERN
gms_vol_high_thresh: 0.009      # 0.9% ATR (similar)
gms_vol_low_thresh: 0.006       # 0.6% ATR (similar)
```
These are reasonably similar.

### Spread Thresholds (MAJOR DIFFERENCE! 🔴)
```yaml
# LEGACY
gms_spread_std_high_thresh: 0.000050   # 5 basis points
gms_spread_mean_low_thresh: 0.000045   # 4.5 basis points

# MODERN  
gms_spread_std_high_thresh: 2.5        # 2.5 BASIS POINTS (50x larger!)
gms_spread_mean_low_thresh: 8.0        # 8.0 BASIS POINTS (177x larger!)
```

**PROBLEM**: The modern system has completely different units for spread thresholds:
- Legacy uses decimal values (0.00005 = 5 basis points)
- Modern uses basis points directly (2.5 = 2.5 basis points)
- But modern's thresholds are WAY too high (8 bps vs 0.045 bps)

### OBI Thresholds
```yaml
# LEGACY
gms_obi_strong_confirm_thresh: 0.20    # 20% imbalance
gms_obi_weak_confirm_thresh: 0.05      # 5% imbalance

# MODERN
gms_obi_strong_confirm_thresh: 0.24    # 24% imbalance (slightly higher)
gms_obi_weak_confirm_thresh: 0.10      # 10% imbalance (2x higher)
```
Modern requires stronger OBI signals (harder to trigger).

## 3. Update Frequency
- **Legacy**: 3600 seconds (1 hour) - matches hourly data
- **Modern**: 60 seconds (1 minute) - but still using hourly bars!

**PROBLEM**: Modern updates regime every 60s but the underlying data (hourly bars) only changes every 3600s. This causes:
- 59 redundant updates per hour
- Potential for flip-flopping on noise
- Wasted computation

## 4. Entry Requirements (From TF-v3 Config)
```yaml
# MODERN ONLY - Additional filters
min_regime_duration_minutes: 30    # Must be in regime for 30 min
max_regime_changes_1h: 3           # Max 3 regime changes per hour  
min_regime_confidence: 0.65        # 65% confidence required
```

These additional requirements make it HARDER for modern to enter trades.

## 5. THE ROOT CAUSE

The modern system is failing because:

1. **Momentum Scale Error**: Using thresholds 100,000x too small means it sees "strong trends" in market noise
2. **Spread Unit Confusion**: Wrong units/scale for spread thresholds (basis points vs decimals)
3. **Over-updating**: 60s updates on hourly data creates instability
4. **Stricter Entry**: Additional duration/confidence requirements reduce opportunities

## IMMEDIATE FIX NEEDED

Update modern thresholds to match legacy scale:
```yaml
# Fix momentum to match MA slope scale
gms_mom_strong_thresh: 100.0      # Not 0.001!
gms_mom_weak_thresh: 50.0         # Not 0.0003!

# Fix spread to use decimal format
gms_spread_std_high_thresh: 0.000050   # Not 2.5!
gms_spread_mean_low_thresh: 0.000045   # Not 8.0!

# Consider changing update frequency
gms_cadence_seconds: 3600         # Match data frequency
```

## Data Field Differences

The modern system also uses different data fields due to the ModernDataAdapter:
- Legacy: Direct field access (e.g., `atr_percent`, `ma_slope`)
- Modern: Adapted fields that may have different calculations

This needs further investigation to ensure the calculations produce comparable values.