#!/usr/bin/env python3
"""Quick regime detection analysis for legacy vs new path comparison"""

import re
import sys

def analyze_backtest_log(log_file):
    """Extract regime detection and trade statistics from backtest log"""
    
    with open(log_file, 'r') as f:
        content = f.read()
    
    # Extract key metrics
    roi_match = re.search(r'Return on Initial \(ROI\):\s*\[.*?\]\s*([\d.]+)%', content)
    trades_match = re.search(r'Total Trades:\s*\[.*?\]\s*(\d+)', content)
    winrate_match = re.search(r'Win Rate:\s*\[.*?\]\s*([\d.]+)\s*%', content)
    drawdown_match = re.search(r'Max Drawdown:\s*\[.*?\]\s*([\d.]+)%', content)
    
    # Count regime detections
    bull_signals = len(re.findall(r"Regime='BULL'", content))
    bear_signals = len(re.findall(r"Regime='BEAR'", content))
    chop_signals = len(re.findall(r"Regime='CHOP'", content))
    
    # Count winning/losing by regime
    winning_bull = re.search(r'Winning Trades by Entry Regime:.*?BULL\s+(\d+)', content, re.DOTALL)
    winning_bear = re.search(r'Winning Trades by Entry Regime:.*?BEAR\s+(\d+)', content, re.DOTALL)
    losing_bull = re.search(r'Losing Trades by Entry Regime:.*?BULL\s+(\d+)', content, re.DOTALL)
    losing_bear = re.search(r'Losing Trades by Entry Regime:.*?BEAR\s+(\d+)', content, re.DOTALL)
    
    # Count long vs short signals
    long_signals = len(re.findall(r"Dir='long'", content))
    short_signals = len(re.findall(r"Dir='short'", content))
    
    results = {
        'roi': float(roi_match.group(1)) if roi_match else 0,
        'total_trades': int(trades_match.group(1)) if trades_match else 0,
        'win_rate': float(winrate_match.group(1)) if winrate_match else 0,
        'max_drawdown': float(drawdown_match.group(1)) if drawdown_match else 0,
        'regime_signals': {
            'BULL': bull_signals,
            'BEAR': bear_signals,
            'CHOP': chop_signals
        },
        'winning_trades': {
            'BULL': int(winning_bull.group(1)) if winning_bull else 0,
            'BEAR': int(winning_bear.group(1)) if winning_bear else 0
        },
        'losing_trades': {
            'BULL': int(losing_bull.group(1)) if losing_bull else 0,
            'BEAR': int(losing_bear.group(1)) if losing_bear else 0
        },
        'direction_signals': {
            'long': long_signals,
            'short': short_signals
        }
    }
    
    return results

def print_comparison(legacy_results, new_results):
    """Print side-by-side comparison"""
    
    print("\n" + "="*80)
    print("BACKTEST COMPARISON: Legacy vs New Path (2024 Full Year)")
    print("="*80)
    
    print(f"\n{'Metric':<30} {'Legacy (TF-v2)':<25} {'New Path (TF-v3)':<25}")
    print("-"*80)
    
    # Performance metrics
    print(f"{'Total Trades':<30} {legacy_results['total_trades']:<25} {new_results['total_trades']:<25}")
    print(f"{'ROI %':<30} {legacy_results['roi']:<25.2f} {new_results['roi']:<25.2f}")
    print(f"{'Win Rate %':<30} {legacy_results['win_rate']:<25.2f} {new_results['win_rate']:<25.2f}")
    print(f"{'Max Drawdown %':<30} {legacy_results['max_drawdown']:<25.2f} {new_results['max_drawdown']:<25.2f}")
    
    # Regime detection
    print(f"\n{'Regime Detection Signals:':<30}")
    print(f"{'  BULL':<30} {legacy_results['regime_signals']['BULL']:<25} {new_results['regime_signals']['BULL']:<25}")
    print(f"{'  BEAR':<30} {legacy_results['regime_signals']['BEAR']:<25} {new_results['regime_signals']['BEAR']:<25}")
    print(f"{'  CHOP':<30} {legacy_results['regime_signals']['CHOP']:<25} {new_results['regime_signals']['CHOP']:<25}")
    
    # Trade direction
    print(f"\n{'Trade Direction:':<30}")
    print(f"{'  Long signals':<30} {legacy_results['direction_signals']['long']:<25} {new_results['direction_signals']['long']:<25}")
    print(f"{'  Short signals':<30} {legacy_results['direction_signals']['short']:<25} {new_results['direction_signals']['short']:<25}")
    
    # Winning/losing by regime
    print(f"\n{'Winning Trades by Regime:':<30}")
    print(f"{'  BULL':<30} {legacy_results['winning_trades']['BULL']:<25} {new_results['winning_trades']['BULL']:<25}")
    print(f"{'  BEAR':<30} {legacy_results['winning_trades']['BEAR']:<25} {new_results['winning_trades']['BEAR']:<25}")
    
    print(f"\n{'Losing Trades by Regime:':<30}")
    print(f"{'  BULL':<30} {legacy_results['losing_trades']['BULL']:<25} {new_results['losing_trades']['BULL']:<25}")
    print(f"{'  BEAR':<30} {legacy_results['losing_trades']['BEAR']:<25} {new_results['losing_trades']['BEAR']:<25}")
    
    # Analysis summary
    print("\n" + "="*80)
    print("ANALYSIS SUMMARY:")
    print("="*80)
    
    # Check if new path is long-only
    new_long_pct = (new_results['direction_signals']['long'] / 
                    (new_results['direction_signals']['long'] + new_results['direction_signals']['short']) * 100
                    if new_results['direction_signals']['long'] + new_results['direction_signals']['short'] > 0 else 0)
    
    legacy_long_pct = (legacy_results['direction_signals']['long'] / 
                       (legacy_results['direction_signals']['long'] + legacy_results['direction_signals']['short']) * 100
                       if legacy_results['direction_signals']['long'] + legacy_results['direction_signals']['short'] > 0 else 0)
    
    print(f"\n1. Direction Bias:")
    print(f"   - Legacy: {legacy_long_pct:.1f}% long, {100-legacy_long_pct:.1f}% short")
    print(f"   - New Path: {new_long_pct:.1f}% long, {100-new_long_pct:.1f}% short")
    
    if new_long_pct > 95:
        print("   ⚠️  WARNING: New path appears to be long-only!")
    else:
        print("   ✓ New path properly takes both long and short positions")
    
    print(f"\n2. Regime Detection:")
    total_legacy_regimes = sum(legacy_results['regime_signals'].values())
    total_new_regimes = sum(new_results['regime_signals'].values())
    
    if legacy_results['regime_signals']['BEAR'] > 0:
        print("   ✓ Legacy detects BEAR regimes")
    else:
        print("   ⚠️  Legacy never detects BEAR regime")
        
    if new_results['regime_signals']['BEAR'] > 0:
        print("   ✓ New path detects BEAR regimes")
    else:
        print("   ⚠️  New path never detects BEAR regime")
    
    print(f"\n3. Performance Comparison:")
    roi_diff = new_results['roi'] - legacy_results['roi']
    print(f"   - ROI difference: {roi_diff:+.2f}%")
    print(f"   - New path is {'more' if roi_diff > 0 else 'less'} profitable")
    
    print(f"\n4. Risk Metrics:")
    dd_diff = new_results['max_drawdown'] - legacy_results['max_drawdown']
    print(f"   - Drawdown difference: {dd_diff:+.2f}%")
    print(f"   - New path has {'higher' if dd_diff > 0 else 'lower'} risk")
    
    print("\n" + "="*80)

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python analyze_regimes.py <legacy_log> <new_log>")
        sys.exit(1)
    
    legacy_results = analyze_backtest_log(sys.argv[1])
    new_results = analyze_backtest_log(sys.argv[2])
    
    print_comparison(legacy_results, new_results)