# Available Data Report for Novel Trading Features

## Data Sources Overview

### 1. features_1s (Primary 1-second data)
- **Location**: `/hyperliquid_data/features_1s/`
- **Format**: Parquet files organized by date and hour
- **Total Columns**: 109
- **Key Finding**: NO direct liquidation, open interest, or funding rate data

#### Available Columns:
- **Order Book Data** (20 levels):
  - bid_price_1 to bid_price_20
  - bid_size_1 to bid_size_20
  - ask_price_1 to ask_price_20
  - ask_size_1 to ask_size_20
  
- **Price Data**:
  - close, high, low, volume
  - mid_price, best_ask, best_bid
  
- **Order Book Imbalance (OBI) variants**:
  - raw_obi_5, raw_obi_20
  - raw_obi_L1_3, raw_obi_L1_10
  - obi_smoothed, obi_smoothed_5, obi_smoothed_20
  - obi_zscore_5, obi_zscore_20
  
- **Volatility Metrics**:
  - atr, atr_14_sec
  - atr_percent, atr_percent_sec
  - realised_vol_1s
  
- **Spread Metrics**:
  - spread, spread_mean, spread_std, spread_relative
  
- **Momentum**:
  - ma_slope, ma_slope_ema_30s
  
- **Other**:
  - unrealised_pnl
  - timestamp

### 2. raw2 (Hourly aggregated data)
- **Location**: `/hyperliquid_data/raw2/`
- **Format**: Daily parquet files
- **Total Columns**: 37
- **Key Features**: Microstructure metrics

#### Notable Columns:
- **Order Book Shape**:
  - bid_slope, ask_slope
  - bid_concentration, ask_concentration
  - book_asymmetry, book_volatility
  
- **Market Quality**:
  - bid_ask_ratio, bid_ask_ratio_log
  - market_efficiency
  - price_impact_bid, price_impact_ask
  
- **Volume Proxy**:
  - volume_proxy (derived from order book)
  - imbalance (different from features_1s)

### 3. resampled_l2 (OHLCV + basic features)
- **Location**: `/hyperliquid_data/resampled_l2/`
- **Timeframes**: 1m, 1h, 4h
- **Total Columns**: 10
- **Basic Features**: OHLC, volume, log returns, realized vol, book slopes

### 4. l2_raw (Raw order book snapshots)
- **Location**: `/hyperliquid_data/l2_raw/`
- **Format**: Arrow files
- **Columns**: asks, bids, best_ask, best_bid, timestamp
- **Raw L2 data**: Full order book arrays

## Current Feature Usage in Modern System

The `ModernContinuousDetector` currently uses:
- volume_imbalance (mapped from obi_smoothed)
- spread_mean, spread_std
- atr_percent_sec
- ma_slope_ema_30s
- close, high, low, volume

## Novel Feature Opportunities

### 1. Advanced Order Book Microstructure Features
Using the 20-level order book data:
- **Book Pressure Indicators**: Weighted bid/ask pressure using all 20 levels
- **Liquidity Concentration**: Where is liquidity clustered in the book
- **Order Book Velocity**: Rate of change in book levels
- **Micro Price**: Volume-weighted mid using full book depth

### 2. Multi-timeframe OBI Analysis
Combine different OBI variants:
- **OBI Divergence**: Compare raw_obi_5 vs raw_obi_20 for trend changes
- **OBI Momentum**: Rate of change in smoothed OBI
- **OBI Regime**: Classify market based on OBI zscore patterns

### 3. Advanced Volatility Regimes
- **Volatility Ratio**: atr_14_sec / atr for multi-scale vol
- **Volatility Acceleration**: Second derivative of ATR
- **Realized vs Expected Vol**: Compare realised_vol_1s patterns

### 4. Spread-based Market Quality
- **Spread Volatility Ratio**: spread_std / spread_mean
- **Relative Spread Extremes**: Identify abnormal spread conditions
- **Spread vs Volatility**: Correlation patterns for regime detection

### 5. Order Book Imbalance Variants
- **L1 vs Deep Book**: Compare raw_obi_L1_3 with raw_obi_20
- **OBI Acceleration**: Second-order changes in OBI
- **Cross-timeframe OBI**: Combine 1s OBI with hourly imbalance from raw2

### 6. Synthetic Features (Not in raw data but derivable)
- **Trade Flow Toxicity**: Using volume patterns and price impact
- **Hidden Liquidity Indicators**: Gaps in order book levels
- **Market Making Activity**: Patterns in bid-ask updates
- **Momentum Quality**: Combine ma_slope variants with volume

## Missing Data (Not Available)
- ❌ Liquidation data
- ❌ Open Interest
- ❌ Funding Rates
- ❌ Trade-by-trade data
- ❌ Actual executed trades

## Implementation Recommendations

1. **Immediate High-Value Features**:
   - Multi-level order book pressure indicators
   - OBI divergence signals
   - Spread quality metrics
   
2. **Medium-term Features**:
   - Cross-timeframe feature aggregation
   - Microstructure regime classification
   - Advanced volatility patterns

3. **Data Pipeline Enhancements**:
   - Add feature computation layer for derived metrics
   - Implement efficient multi-timeframe aggregation
   - Create feature importance tracking

## Key Insight
While we lack liquidation/OI/funding data, we have extremely rich order book microstructure data (20 levels) that is currently underutilized. The modern system only uses basic features, leaving significant alpha potential in the deeper order book dynamics.