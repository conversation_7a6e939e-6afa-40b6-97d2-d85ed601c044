# Task ID: 1
# Title: Implement Multi-Depth OBI Feature Layer
# Status: done
# Dependencies: None
# Priority: high
# Description: Develop the calc_obi() function in microstructure.py that calculates Order Book Imbalance with support for arbitrary depth ranges and custom weights.
# Details:
Create the calc_obi() function in microstructure.py with the following implementation:

```python
def calc_obi(bids, asks, depth_ranges=None, weights=None):
    """
    Calculate Order Book Imbalance (OBI) with support for arbitrary depth ranges and custom weights.
    
    Args:
        bids (list): List of bid price-quantity tuples [(price, qty), ...] in descending price order
        asks (list): List of ask price-quantity tuples [(price, qty), ...] in ascending price order
        depth_ranges (list, optional): List of depth ranges to consider. Default is [10]
        weights (list, optional): List of weights corresponding to each level. Default is uniform weights.
    
    Returns:
        float: OBI value between -1 and 1
        
    Formula: OBI = (Σ w_i·bid_i − Σ w_i·ask_i) / (Σ w_i·bid_i + Σ w_i·ask_i)
    """
    if depth_ranges is None:
        depth_ranges = [10]
    
    max_depth = max(depth_ranges)
    
    # Ensure we have enough levels
    bids = bids[:max_depth] if len(bids) > max_depth else bids
    asks = asks[:max_depth] if len(asks) > max_depth else asks
    
    # Default to uniform weights if not provided
    if weights is None:
        weights = [1.0] * max_depth
    elif len(weights) < max_depth:
        weights = weights + [weights[-1]] * (max_depth - len(weights))
    
    # Calculate weighted sums
    weighted_bids = sum(weights[i] * qty for i, (_, qty) in enumerate(bids) if i < max_depth)
    weighted_asks = sum(weights[i] * qty for i, (_, qty) in enumerate(asks) if i < max_depth)
    
    # Calculate OBI
    total_volume = weighted_bids + weighted_asks
    if total_volume == 0:
        return 0.0
    
    obi = (weighted_bids - weighted_asks) / total_volume
    return obi
```

Add appropriate error handling and edge cases (empty order books, etc.). Ensure the function handles different depth ranges efficiently.

# Test Strategy:
Create comprehensive unit tests in test_microstructure.py:
1. Test with uniform weights and various depth ranges
2. Test with custom weights
3. Test edge cases (empty order book, single-sided book)
4. Test with known inputs and expected outputs
5. Verify OBI values are always between -1 and 1
6. Test performance with large order books

Example test case:
```python
def test_calc_obi_basic():
    bids = [(100, 10), (99, 20), (98, 30)]
    asks = [(101, 10), (102, 20), (103, 30)]
    obi = calc_obi(bids, asks)
    assert -1 <= obi <= 1
    
    # With known values that should give OBI = 0
    bids = [(100, 10), (99, 10)]
    asks = [(101, 10), (102, 10)]
    obi = calc_obi(bids, asks)
    assert abs(obi) < 1e-10
```

Ensure test coverage is >95% for this module.
