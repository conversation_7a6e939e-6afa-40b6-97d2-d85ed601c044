# Task ID: 6
# Title: Update StrategyEvaluator for OBI Integration
# Status: pending
# Dependencies: 4, 5
# Priority: medium
# Description: Modify the StrategyEvaluator to activate and integrate the OBI scalper strategy based on configuration flags and market regime.
# Details:
Update the StrategyEvaluator class to integrate the OBI scalper strategy:

```python
class StrategyEvaluator:
    # ... existing code ...
    
    def get_active_strategies(self):
        """
        Determine which strategies should be active based on current market conditions
        and configuration.
        
        Returns:
            list: List of active strategy instances
        """
        active_strategies = []
        
        # Market regime detection
        is_trending = self._detect_trending_regime()
        is_ranging = self._detect_ranging_regime()
        is_volatile = self._detect_volatile_regime()
        
        self.logger.debug(f"Market regime: Trending={is_trending}, Ranging={is_ranging}, Volatile={is_volatile}")
        
        # Legacy GMS strategy activation
        if self.config.gms_enabled:
            gms = GMSStrategy(self.config, self.signal_engine, self.risk_manager)
            active_strategies.append(gms)
            self.logger.debug("GMS strategy activated")
        
        # Legacy TF strategy activation
        if self.config.tf_enabled:
            tf = TrendFollowingStrategy(self.config, self.signal_engine, self.risk_manager)
            active_strategies.append(tf)
            self.logger.debug("TF strategy activated")
        
        # OBI Scalper activation
        if self.config.obi.enabled:
            # Check if market conditions are suitable for OBI scalper
            activate_obi = False
            
            # Activation logic based on regime and config
            if self.config.get('obi_always_active', False):
                activate_obi = True
            elif is_ranging and self.config.get('obi_active_in_ranging', True):
                activate_obi = True
            elif is_volatile and self.config.get('obi_active_in_volatile', False):
                activate_obi = True
            elif not is_trending and not is_ranging and not is_volatile:
                # Default/neutral market condition
                activate_obi = self.config.get('obi_active_in_neutral', True)
            
            if activate_obi:
                obi_scalper = OBIScalperStrategy(self.config, self.signal_engine, self.risk_manager)
                active_strategies.append(obi_scalper)
                self.logger.info("OBI Scalper strategy activated")
                self.logger.debug(f"OBI config: primary_depth={self.config.get('obi_primary_depth')}, " 
                                 f"entry_threshold={self.config.get('obi_entry_threshold')}")
        
        return active_strategies
    
    def _detect_ranging_regime(self):
        """
        Detect if the market is in a ranging/sideways regime.
        
        Returns:
            bool: True if market is ranging, False otherwise
        """
        # Implementation depends on available indicators
        # Example using ADX (Average Directional Index)
        adx = self.signal_engine.get_adx()
        if adx is not None:
            # Low ADX indicates ranging market
            return adx < self.config.get('ranging_adx_threshold', 20)
        
        # Fallback method if ADX not available
        return self._fallback_regime_detection('ranging')
    
    def _detect_volatile_regime(self):
        """
        Detect if the market is in a volatile regime.
        
        Returns:
            bool: True if market is volatile, False otherwise
        """
        # Implementation using volatility indicators
        # Example using ATR relative to historical levels
        atr = self.signal_engine.get_atr()
        atr_percentile = self.signal_engine.get_atr_percentile()
        
        if atr is not None and atr_percentile is not None:
            # High ATR percentile indicates volatile market
            return atr_percentile > self.config.get('volatile_atr_percentile', 80)
        
        return self._fallback_regime_detection('volatile')
    
    def _fallback_regime_detection(self, regime_type):
        """
        Fallback method for regime detection when primary indicators are unavailable.
        
        Args:
            regime_type (str): Type of regime to detect ('trending', 'ranging', 'volatile')
            
        Returns:
            bool: Regime detection result
        """
        # Simple fallback using recent price action
        recent_returns = self.signal_engine.get_recent_returns()
        
        if regime_type == 'ranging':
            # Low absolute returns indicate ranging market
            return np.mean(np.abs(recent_returns)) < self.config.get('ranging_return_threshold', 0.001)
        elif regime_type == 'volatile':
            # High standard deviation indicates volatile market
            return np.std(recent_returns) > self.config.get('volatile_std_threshold', 0.005)
        elif regime_type == 'trending':
            # Consistent direction indicates trending market
            return np.abs(np.mean(recent_returns)) > self.config.get('trending_return_threshold', 0.002)
        
        return False
```

Add configuration parameters to base.yaml:

```yaml
# Strategy activation settings
obi_always_active: false
obi_active_in_ranging: true
obi_active_in_volatile: false
obi_active_in_neutral: true

# Regime detection parameters
ranging_adx_threshold: 20
volatile_atr_percentile: 80
ranging_return_threshold: 0.001
volatile_std_threshold: 0.005
trending_return_threshold: 0.002
```

# Test Strategy:
1. Create unit tests for strategy activation logic:
```python
def test_obi_strategy_activation():
    # Setup
    config = MockConfig(obi_enabled=True, obi_always_active=False)
    evaluator = StrategyEvaluator(config, MockSignalEngine(), MockRiskManager())
    
    # Test 1: Always active mode
    config.obi_always_active = True
    strategies = evaluator.get_active_strategies()
    assert any(isinstance(s, OBIScalperStrategy) for s in strategies)
    
    # Test 2: Ranging market activation
    config.obi_always_active = False
    config.obi_active_in_ranging = True
    
    # Mock ranging market
    evaluator._detect_ranging_regime = lambda: True
    evaluator._detect_trending_regime = lambda: False
    evaluator._detect_volatile_regime = lambda: False
    
    strategies = evaluator.get_active_strategies()
    assert any(isinstance(s, OBIScalperStrategy) for s in strategies)
    
    # Test 3: Volatile market deactivation
    config.obi_active_in_ranging = False
    config.obi_active_in_volatile = False
    
    # Mock volatile market
    evaluator._detect_ranging_regime = lambda: False
    evaluator._detect_trending_regime = lambda: False
    evaluator._detect_volatile_regime = lambda: True
    
    strategies = evaluator.get_active_strategies()
    assert not any(isinstance(s, OBIScalperStrategy) for s in strategies)
```

2. Test regime detection methods:
```python
def test_regime_detection():
    # Setup
    config = MockConfig()
    signal_engine = MockSignalEngine()
    evaluator = StrategyEvaluator(config, signal_engine, MockRiskManager())
    
    # Test ranging detection with ADX
    signal_engine.adx_value = 15  # Low ADX = ranging
    assert evaluator._detect_ranging_regime() is True
    
    signal_engine.adx_value = 30  # High ADX = not ranging
    assert evaluator._detect_ranging_regime() is False
    
    # Test volatile detection with ATR
    signal_engine.atr_percentile = 85  # High percentile = volatile
    assert evaluator._detect_volatile_regime() is True
    
    signal_engine.atr_percentile = 50  # Medium percentile = not volatile
    assert evaluator._detect_volatile_regime() is False
    
    # Test fallback methods
    signal_engine.adx_value = None  # Force fallback
    signal_engine.recent_returns = [0.0001, -0.0002, 0.0001, -0.0001]  # Low returns = ranging
    assert evaluator._detect_ranging_regime() is True
    
    signal_engine.recent_returns = [0.01, -0.015, 0.02, -0.01]  # High std = volatile
    assert evaluator._detect_volatile_regime() is True
```

3. Integration testing:
   - Verify OBI strategy activates correctly with different market conditions
   - Test interaction with other strategies
   - Validate logging output for debugging
