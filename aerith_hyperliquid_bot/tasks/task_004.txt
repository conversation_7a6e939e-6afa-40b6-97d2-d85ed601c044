# Task ID: 4
# Title: Implement OBIScalperStrategy Class
# Status: pending
# Dependencies: 2, 3
# Priority: high
# Description: Create a new strategy class that uses OBI signals to make trading decisions, with diagnostic logging.
# Details:
Create the OBIScalperStrategy class in strategies/evaluator.py:

```python
class OBIScalperStrategy(BaseStrategy):
    """Strategy that uses Order Book Imbalance (OBI) signals for scalping."""
    
    def __init__(self, config, signal_engine, risk_manager):
        super().__init__(config, signal_engine, risk_manager)
        self.name = "OBIScalper"
        self.logger = logging.getLogger(f"strategy.{self.name}")
        
        # Strategy parameters from config
        self.primary_depth = config.get('obi_primary_depth', 10)
        self.entry_threshold = config.get('obi_entry_threshold', 1.5)  # Z-score threshold
        self.exit_threshold = config.get('obi_exit_threshold', 0.5)   # Z-score threshold
        self.max_holding_time = config.get('obi_max_holding_time', 60)  # seconds
        
        # State variables
        self.position_entry_time = None
        self.position_entry_price = None
    
    def evaluate(self):
        """
        Evaluate market conditions and generate trading signals based on OBI.
        Returns:
            dict: Strategy evaluation result with signal, confidence, and metadata
        """
        # Get current OBI z-score
        z_key = f'obi_z_{self.primary_depth}'
        if z_key not in self.signal_engine.signals:
            self.logger.warning(f"Required signal {z_key} not found")
            return self._create_result(signal=0, confidence=0)
        
        obi_z = self.signal_engine.signals[z_key]
        raw_obi = self.signal_engine.signals.get(f'obi_raw_{self.primary_depth}', 0)
        smoothed_obi = self.signal_engine.signals.get(f'obi_smoothed_{self.primary_depth}', 0)
        
        # Current position and timing
        current_position = self.risk_manager.get_position()
        current_time = time.time()
        holding_time = (current_time - self.position_entry_time) if self.position_entry_time else 0
        
        # Log diagnostic information
        self.logger.debug(f"OBI Raw: {raw_obi:.4f}, Smoothed: {smoothed_obi:.4f}, Z-score: {obi_z:.4f}")
        self.logger.debug(f"Current position: {current_position}, Holding time: {holding_time:.1f}s")
        
        # Trading logic
        signal = 0
        confidence = 0
        metadata = {
            'obi_raw': raw_obi,
            'obi_smoothed': smoothed_obi,
            'obi_z': obi_z,
            'holding_time': holding_time
        }
        
        # No position - look for entry
        if current_position == 0:
            if obi_z > self.entry_threshold:  # Strong bid imbalance
                signal = 1  # Buy signal
                confidence = min(1.0, (obi_z - self.entry_threshold) / 2)
                self.logger.info(f"BUY signal generated: OBI z-score {obi_z:.2f} > {self.entry_threshold}")
                self.position_entry_time = current_time
                self.position_entry_price = self.signal_engine.get_mid_price()
                
            elif obi_z < -self.entry_threshold:  # Strong ask imbalance
                signal = -1  # Sell signal
                confidence = min(1.0, (-obi_z - self.entry_threshold) / 2)
                self.logger.info(f"SELL signal generated: OBI z-score {obi_z:.2f} < -{self.entry_threshold}")
                self.position_entry_time = current_time
                self.position_entry_price = self.signal_engine.get_mid_price()
        
        # Exit existing position
        elif current_position > 0:  # Long position
            # Exit if OBI z-score reverses or holding time exceeded
            if obi_z < -self.exit_threshold or holding_time > self.max_holding_time:
                signal = -1  # Exit long
                confidence = 1.0
                reason = "z-score reversal" if obi_z < -self.exit_threshold else "max holding time"
                self.logger.info(f"EXIT LONG signal: {reason}, z-score: {obi_z:.2f}")
                self.position_entry_time = None
        
        elif current_position < 0:  # Short position
            # Exit if OBI z-score reverses or holding time exceeded
            if obi_z > self.exit_threshold or holding_time > self.max_holding_time:
                signal = 1  # Exit short
                confidence = 1.0
                reason = "z-score reversal" if obi_z > self.exit_threshold else "max holding time"
                self.logger.info(f"EXIT SHORT signal: {reason}, z-score: {obi_z:.2f}")
                self.position_entry_time = None
        
        return self._create_result(signal=signal, confidence=confidence, metadata=metadata)
    
    def _create_result(self, signal, confidence, metadata=None):
        """Create a standardized strategy result dictionary"""
        return {
            'strategy': self.name,
            'signal': signal,  # -1 (sell), 0 (neutral), 1 (buy)
            'confidence': confidence,  # 0.0 to 1.0
            'metadata': metadata or {}
        }
```

Ensure the strategy handles all edge cases and includes proper logging for diagnostics.

# Test Strategy:
1. Create unit tests in test_strategies.py:
```python
def test_obi_scalper_strategy():
    # Setup mock dependencies
    config = MockConfig()
    signal_engine = MockSignalEngine()
    risk_manager = MockRiskManager()
    
    # Initialize strategy
    strategy = OBIScalperStrategy(config, signal_engine, risk_manager)
    
    # Test case 1: Strong buy signal
    signal_engine.signals = {
        'obi_z_10': 2.5,
        'obi_raw_10': 0.3,
        'obi_smoothed_10': 0.25
    }
    result = strategy.evaluate()
    assert result['signal'] == 1
    assert result['confidence'] > 0
    
    # Test case 2: Strong sell signal
    signal_engine.signals = {
        'obi_z_10': -2.5,
        'obi_raw_10': -0.3,
        'obi_smoothed_10': -0.25
    }
    result = strategy.evaluate()
    assert result['signal'] == -1
    assert result['confidence'] > 0
    
    # Test case 3: Neutral zone
    signal_engine.signals = {
        'obi_z_10': 0.5,
        'obi_raw_10': 0.05,
        'obi_smoothed_10': 0.04
    }
    result = strategy.evaluate()
    assert result['signal'] == 0
    
    # Test case 4: Exit long position
    signal_engine.signals = {
        'obi_z_10': -1.0,
        'obi_raw_10': -0.15,
        'obi_smoothed_10': -0.12
    }
    risk_manager.position = 1.0  # Mock long position
    strategy.position_entry_time = time.time() - 10  # 10 seconds ago
    result = strategy.evaluate()
    assert result['signal'] == -1  # Exit signal
    
    # Test case 5: Max holding time exit
    signal_engine.signals = {
        'obi_z_10': 1.0,  # Still positive
        'obi_raw_10': 0.15,
        'obi_smoothed_10': 0.12
    }
    risk_manager.position = 1.0  # Mock long position
    strategy.position_entry_time = time.time() - 100  # Exceed max holding time
    result = strategy.evaluate()
    assert result['signal'] == -1  # Exit signal
```

2. Test error handling and edge cases:
   - Missing signals
   - Invalid configuration
   - Extreme OBI values

3. Integration testing with the full system:
   - Verify strategy activates correctly
   - Check position management
   - Validate logging output
