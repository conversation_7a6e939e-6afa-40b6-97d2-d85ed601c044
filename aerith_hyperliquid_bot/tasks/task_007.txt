# Task ID: 7
# Title: Implement Backtesting and KPI Reporting
# Status: pending
# Dependencies: 4, 5, 6
# Priority: medium
# Description: Create a backtesting framework to evaluate the OBI scalper strategy performance and generate comprehensive KPI reports.
# Details:
Implement a backtesting system and KPI reporting for the OBI scalper strategy:

1. Create a backtester module:

```python
class Backtester:
    """Backtesting engine for trading strategies"""
    
    def __init__(self, config_path=None, start_date=None, end_date=None):
        self.config = Config(config_path)
        self.start_date = start_date or "2023-01-01"
        self.end_date = end_date or "2024-12-31"
        self.logger = logging.getLogger("backtester")
        
        # Initialize components
        self.data_loader = DataLoader(self.config)
        self.signal_engine = SignalEngine(self.config)
        self.risk_manager = RiskManager(self.config, self.signal_engine)
        self.strategy_evaluator = StrategyEvaluator(self.config, self.signal_engine, self.risk_manager)
        
        # Results storage
        self.trades = []
        self.metrics = {}
        self.equity_curve = []
        self.signals_history = []
    
    def run(self, latency_ms=500, taker_fee_bps=10):
        """
        Run backtest with specified latency simulation and fees.
        
        Args:
            latency_ms (int): Simulated latency in milliseconds
            taker_fee_bps (int): Taker fee in basis points (1 bps = 0.01%)
            
        Returns:
            dict: Backtest results and metrics
        """
        self.logger.info(f"Starting backtest from {self.start_date} to {self.end_date}")
        self.logger.info(f"Latency simulation: {latency_ms}ms, Taker fee: {taker_fee_bps} bps")
        
        # Load historical data
        data = self.data_loader.load_data(self.start_date, self.end_date)
        if not data:
            self.logger.error("Failed to load historical data")
            return {"error": "Data loading failed"}
        
        # Initialize simulation state
        initial_capital = self.config.initial_capital
        current_capital = initial_capital
        current_position = 0
        position_entry_price = 0
        
        # Metrics tracking
        trade_count = 0
        winning_trades = 0
        total_pnl = 0
        returns = []
        drawdowns = []
        max_equity = initial_capital
        max_drawdown = 0
        
        # Process each data point
        for i, tick in enumerate(data):
            # Update signal engine with current market data
            self.signal_engine.update_with_tick(tick)
            
            # Get strategy evaluations
            strategy_results = self.strategy_evaluator.evaluate_all()
            
            # Store signals for analysis
            self.signals_history.append({
                'timestamp': tick['timestamp'],
                'price': tick['price'],
                'signals': {r['strategy']: r['signal'] for r in strategy_results}
            })
            
            # Determine position from risk manager
            target_position = self.risk_manager.manage_positions(strategy_results)
            
            # Apply latency simulation
            if i + int(latency_ms / tick['interval_ms']) < len(data):
                execution_tick = data[i + int(latency_ms / tick['interval_ms'])]
                execution_price = execution_tick['price']
            else:
                execution_price = tick['price']
            
            # Execute trade if position changes
            if target_position != current_position:
                # Calculate trade size
                trade_size = abs(target_position - current_position)
                trade_direction = 1 if target_position > current_position else -1
                
                # Apply fees
                fee = trade_size * execution_price * (taker_fee_bps / 10000)
                current_capital -= fee
                
                # Record trade
                trade = {
                    'timestamp': tick['timestamp'],
                    'direction': trade_direction,
                    'size': trade_size,
                    'price': execution_price,
                    'fee': fee
                }
                
                # Calculate PnL if closing position
                if current_position != 0 and (
                    (current_position > 0 and target_position < current_position) or
                    (current_position < 0 and target_position > current_position)
                ):
                    # Closing or reducing position
                    close_size = min(abs(current_position), abs(current_position - target_position))
                    pnl = close_size * (execution_price - position_entry_price) * (1 if current_position > 0 else -1)
                    trade['pnl'] = pnl
                    total_pnl += pnl
                    current_capital += pnl
                    
                    # Track win/loss
                    if pnl > 0:
                        winning_trades += 1
                    trade_count += 1
                
                # Update position
                if target_position != 0:
                    # New or updated position entry price
                    if current_position == 0 or (
                        (current_position > 0 and target_position > current_position) or
                        (current_position < 0 and target_position < current_position)
                    ):
                        position_entry_price = execution_price
                
                current_position = target_position
                self.trades.append(trade)
            
            # Update equity curve and drawdown
            unrealized_pnl = 0
            if current_position != 0:
                unrealized_pnl = current_position * (tick['price'] - position_entry_price)
            
            current_equity = current_capital + unrealized_pnl
            self.equity_curve.append({
                'timestamp': tick['timestamp'],
                'equity': current_equity,
                'position': current_position
            })
            
            # Track returns and drawdowns
            if i > 0:
                daily_return = (current_equity / self.equity_curve[-2]['equity']) - 1
                returns.append(daily_return)
            
            if current_equity > max_equity:
                max_equity = current_equity
            
            drawdown = (max_equity - current_equity) / max_equity
            drawdowns.append(drawdown)
            max_drawdown = max(max_drawdown, drawdown)
        
        # Calculate performance metrics
        self.metrics = self._calculate_metrics(returns, drawdowns, trade_count, winning_trades, total_pnl, initial_capital)
        
        self.logger.info(f"Backtest completed. Final equity: ${current_equity:.2f}")
        self.logger.info(f"Sharpe Ratio: {self.metrics['sharpe_ratio']:.2f}, Profit Factor: {self.metrics['profit_factor']:.2f}")
        
        return {
            'metrics': self.metrics,
            'trades': self.trades,
            'equity_curve': self.equity_curve,
            'signals': self.signals_history
        }
    
    def _calculate_metrics(self, returns, drawdowns, trade_count, winning_trades, total_pnl, initial_capital):
        """
        Calculate performance metrics from backtest results.
        """
        metrics = {}
        
        # Return metrics
        if returns:
            metrics['total_return'] = ((self.equity_curve[-1]['equity'] / initial_capital) - 1) * 100
            metrics['annualized_return'] = metrics['total_return'] * (252 / len(returns))
            metrics['volatility'] = np.std(returns) * np.sqrt(252)
            metrics['sharpe_ratio'] = metrics['annualized_return'] / metrics['volatility'] if metrics['volatility'] > 0 else 0
            metrics['sortino_ratio'] = self._calculate_sortino(returns)
            metrics['max_drawdown'] = max(drawdowns) * 100 if drawdowns else 0
        
        # Trade metrics
        metrics['trade_count'] = trade_count
        metrics['win_rate'] = (winning_trades / trade_count) * 100 if trade_count > 0 else 0
        metrics['profit_factor'] = self._calculate_profit_factor()
        metrics['avg_trade_pnl'] = total_pnl / trade_count if trade_count > 0 else 0
        
        return metrics
    
    def _calculate_sortino(self, returns):
        """
        Calculate Sortino ratio (Sharpe ratio but only considering downside volatility).
        """
        if not returns:
            return 0
        
        # Calculate downside returns (negative returns only)
        downside_returns = [r for r in returns if r < 0]
        if not downside_returns:
            return float('inf')  # No downside volatility
        
        # Calculate downside deviation
        downside_deviation = np.std(downside_returns) * np.sqrt(252)
        if downside_deviation == 0:
            return 0
        
        # Annualized return / downside deviation
        annualized_return = ((self.equity_curve[-1]['equity'] / self.equity_curve[0]['equity']) - 1) * (252 / len(returns))
        return annualized_return / downside_deviation
    
    def _calculate_profit_factor(self):
        """
        Calculate profit factor (gross profits / gross losses).
        """
        gross_profit = sum(t.get('pnl', 0) for t in self.trades if t.get('pnl', 0) > 0)
        gross_loss = sum(abs(t.get('pnl', 0)) for t in self.trades if t.get('pnl', 0) < 0)
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0
        
        return gross_profit / gross_loss
    
    def generate_report(self, output_path=None):
        """
        Generate CSV report of backtest results.
        
        Args:
            output_path (str, optional): Path to save CSV report
            
        Returns:
            str: Path to saved report
        """
        if not self.metrics:
            self.logger.error("No backtest results to report")
            return None
        
        # Default output path
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"backtest_report_{timestamp}.csv"
        
        # Prepare report data
        report_data = [
            ["Metric", "Value"],
            ["Total Return (%)", f"{self.metrics.get('total_return', 0):.2f}"],
            ["Annualized Return (%)", f"{self.metrics.get('annualized_return', 0):.2f}"],
            ["Sharpe Ratio", f"{self.metrics.get('sharpe_ratio', 0):.2f}"],
            ["Sortino Ratio", f"{self.metrics.get('sortino_ratio', 0):.2f}"],
            ["Max Drawdown (%)", f"{self.metrics.get('max_drawdown', 0):.2f}"],
            ["Profit Factor", f"{self.metrics.get('profit_factor', 0):.2f}"],
            ["Trade Count", self.metrics.get('trade_count', 0)],
            ["Win Rate (%)", f"{self.metrics.get('win_rate', 0):.2f}"],
            ["Avg Trade PnL", f"{self.metrics.get('avg_trade_pnl', 0):.2f}"],
        ]
        
        # Write to CSV
        with open(output_path, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerows(report_data)
        
        self.logger.info(f"Report saved to {output_path}")
        return output_path
```

2. Create a command-line script for running backtests:

```python
#!/usr/bin/env python

import argparse
import logging
from datetime import datetime
from backtester import Backtester

def main():
    parser = argparse.ArgumentParser(description="Run backtest for trading strategies")
    parser.add_argument("--config", type=str, help="Path to configuration file")
    parser.add_argument("--start", type=str, default="2023-01-01", help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end", type=str, default="2024-12-31", help="End date (YYYY-MM-DD)")
    parser.add_argument("--latency", type=int, default=500, help="Simulated latency in milliseconds")
    parser.add_argument("--fee", type=int, default=10, help="Taker fee in basis points")
    parser.add_argument("--output", type=str, help="Output path for report CSV")
    parser.add_argument("--log-level", type=str, default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                        help="Logging level")
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run backtest
    backtester = Backtester(args.config, args.start, args.end)
    results = backtester.run(latency_ms=args.latency, taker_fee_bps=args.fee)
    
    # Generate report
    if args.output:
        report_path = args.output
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = f"backtest_report_{timestamp}.csv"
    
    backtester.generate_report(report_path)
    
    # Print summary
    print("\nBacktest Summary:")
    print(f"Period: {args.start} to {args.end}")
    print(f"Latency: {args.latency}ms, Fee: {args.fee} bps")
    print(f"Sharpe Ratio: {results['metrics']['sharpe_ratio']:.2f}")
    print(f"Profit Factor: {results['metrics']['profit_factor']:.2f}")
    print(f"Total Return: {results['metrics']['total_return']:.2f}%")
    print(f"Max Drawdown: {results['metrics']['max_drawdown']:.2f}%")
    print(f"Win Rate: {results['metrics']['win_rate']:.2f}%")
    print(f"\nDetailed report saved to: {report_path}")

if __name__ == "__main__":
    main()
```

3. Add a changelog entry template:

```markdown
# Changelog Entry: OBI Scalper Implementation

## Summary
Implemented Order Book Imbalance (OBI) scalper strategy with multi-depth support and integration with existing GMS/TF framework.

## Metrics
| Metric | Value | Target | Status |
|--------|-------|--------|--------|
| Sharpe Ratio (IS 2024) | {sharpe_is} | ≥ 1.2 | {sharpe_is_status} |
| Profit Factor (IS 2024) | {pf_is} | ≥ 1.3 | {pf_is_status} |
| Sharpe Ratio (OOS 2023) | {sharpe_oos} | ≥ 1.0 | {sharpe_oos_status} |
| GMS+TF Degradation | {legacy_degradation}% | < 1.0% | {degradation_status} |
| Test Coverage | {test_coverage}% | > 95% | {coverage_status} |
| Live/Sim Slippage Ratio | {slippage_ratio}x | ≤ 2x | {slippage_status} |

## Implementation Notes
- Added multi-depth OBI calculation with configurable weights
- Implemented signal smoothing and z-score normalization
- Created OBIScalperStrategy with regime-aware activation
- Integrated with risk management system
- Added comprehensive unit tests
- Performed backtesting with latency simulation

## Configuration
Optimal parameters found during testing:
- Primary OBI depth: {primary_depth}
- Entry threshold: {entry_threshold}
- Exit threshold: {exit_threshold}
- Position sizing: {position_sizing}
- Risk per trade: {risk_per_trade}%

## Future Improvements
- Further optimize OBI depth combinations
- Explore adaptive thresholds based on volatility
- Implement machine learning for regime detection
```

# Test Strategy:
1. Create unit tests for the backtester:
```python
def test_backtester_initialization():
    # Test basic initialization
    backtester = Backtester()
    assert backtester is not None
    assert backtester.start_date == "2023-01-01"
    assert backtester.end_date == "2024-12-31"
    
    # Test custom date range
    backtester = Backtester(start_date="2022-01-01", end_date="2022-12-31")
    assert backtester.start_date == "2022-01-01"
    assert backtester.end_date == "2022-12-31"

def test_metrics_calculation():
    backtester = Backtester()
    
    # Mock equity curve and trades
    backtester.equity_curve = [
        {'timestamp': '2023-01-01', 'equity': 10000},
        {'timestamp': '2023-01-02', 'equity': 10100},
        {'timestamp': '2023-01-03', 'equity': 10050},
        {'timestamp': '2023-01-04', 'equity': 10200}
    ]
    
    backtester.trades = [
        {'timestamp': '2023-01-02', 'pnl': 100},
        {'timestamp': '2023-01-03', 'pnl': -50},
        {'timestamp': '2023-01-04', 'pnl': 150}
    ]
    
    # Calculate metrics
    returns = [0.01, -0.005, 0.015]  # Daily returns
    drawdowns = [0, 0, 0.005, 0]  # Daily drawdowns
    metrics = backtester._calculate_metrics(returns, drawdowns, 3, 2, 200, 10000)
    
    # Verify metrics
    assert metrics['trade_count'] == 3
    assert metrics['win_rate'] == 2/3 * 100
    assert abs(metrics['profit_factor'] - 5.0) < 0.01  # (100+150)/(50) = 5.0
    assert abs(metrics['max_drawdown'] - 0.5) < 0.01  # 0.5%
```

2. Test the report generation:
```python
def test_report_generation():
    import os
    import tempfile
    
    backtester = Backtester()
    
    # Mock metrics
    backtester.metrics = {
        'total_return': 12.5,
        'annualized_return': 15.2,
        'sharpe_ratio': 1.3,
        'sortino_ratio': 1.8,
        'max_drawdown': 8.5,
        'profit_factor': 1.4,
        'trade_count': 120,
        'win_rate': 58.3,
        'avg_trade_pnl': 25.6
    }
    
    # Generate report to temp file
    with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as tmp:
        report_path = tmp.name
    
    backtester.generate_report(report_path)
    
    # Verify file exists and contains data
    assert os.path.exists(report_path)
    with open(report_path, 'r') as f:
        content = f.read()
        assert 'Sharpe Ratio' in content
        assert '1.30' in content  # Formatted sharpe ratio
    
    # Clean up
    os.unlink(report_path)
```

3. Integration testing:
   - Run backtest with sample data
   - Verify metrics match expected values
   - Test with different latency and fee settings
   - Compare results against baseline strategies

4. Performance validation:
   - Run backtest for 2023 (OOS) and 2024 (IS) periods
   - Verify Sharpe ratio ≥ 1.2 and Profit Factor ≥ 1.3 for IS
   - Verify Sharpe ratio ≥ 1.0 for OOS with fees and latency
   - Compare GMS+TF metrics with and without OBI integration
