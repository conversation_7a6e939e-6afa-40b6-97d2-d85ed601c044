# Task ID: 5
# Title: Implement Risk Management Integration
# Status: pending
# Dependencies: 4
# Priority: high
# Description: Extend the risk management system to handle position sizing for the OBI scalper strategy based on fixed-fraction or ATR-based approaches.
# Details:
Update the risk management system to support OBI scalper position sizing:

```python
class RiskManager:
    # ... existing code ...
    
    def calculate_position_size_for_obi_scalper(self, signal, confidence, price):
        """
        Calculate appropriate position size for OBI scalper strategy.
        
        Args:
            signal (int): Trading signal (-1, 0, 1)
            confidence (float): Signal confidence (0.0-1.0)
            price (float): Current price for the asset
            
        Returns:
            float: Target position size (positive for long, negative for short)
        """
        if signal == 0:
            return 0.0
        
        # Get risk parameters from config
        sizing_method = self.config.get('obi_position_sizing', 'fixed_fraction')
        max_position = self.config.get('obi_max_position', 0.5)  # As fraction of max_leverage
        risk_per_trade = self.config.get('obi_risk_per_trade', 0.01)  # 1% of capital
        
        # Calculate base position size
        if sizing_method == 'fixed_fraction':
            # Simple fixed fraction of capital
            position_size = self.capital * risk_per_trade * confidence
            
        elif sizing_method == 'atr_based':
            # Use ATR for dynamic position sizing
            atr = self.signal_engine.get_atr(timeframe='1m')
            if atr is None or atr == 0:
                self.logger.warning("ATR not available, falling back to fixed fraction")
                position_size = self.capital * risk_per_trade * confidence
            else:
                # Size based on ATR stop distance
                stop_distance = atr * self.config.get('obi_atr_multiplier', 1.5)
                risk_amount = self.capital * risk_per_trade
                position_size = risk_amount / stop_distance * confidence
        else:
            self.logger.error(f"Unknown position sizing method: {sizing_method}")
            position_size = 0.0
        
        # Apply direction
        position_size = position_size * signal
        
        # Apply maximum position constraint
        max_allowed = self.capital * self.config.max_leverage * max_position
        if abs(position_size) > max_allowed:
            position_size = max_allowed * (1 if position_size > 0 else -1)
            self.logger.info(f"Position size capped at {max_allowed:.4f}")
        
        # Log position sizing details
        self.logger.debug(f"OBI Scalper position size: {position_size:.4f} ({sizing_method})")
        
        return position_size
    
    def size_obi_scalper_trade(self, strategy_result):
        """
        Calculate position size for OBI scalper strategy based on its evaluation result.
        
        Args:
            strategy_result (dict): The result from OBIScalperStrategy.evaluate()
            
        Returns:
            float: Target position size
        """
        signal = strategy_result['signal']
        confidence = strategy_result['confidence']
        current_price = self.signal_engine.get_mid_price()
        
        return self.calculate_position_size_for_obi_scalper(signal, confidence, current_price)
```

Update the main position management code to integrate with the OBI scalper:

```python
def manage_positions(self, strategy_evaluations):
    """
    Determine final position based on all active strategies.
    """
    # ... existing code ...
    
    # Handle OBI Scalper if active
    obi_result = next((r for r in strategy_evaluations if r['strategy'] == 'OBIScalper'), None)
    if obi_result and obi_result['signal'] != 0:
        obi_position = self.size_obi_scalper_trade(obi_result)
        
        # Combine with other strategies or use standalone based on config
        if self.config.get('obi_standalone', True):
            # OBI operates independently
            final_position = obi_position
        else:
            # Combine with existing position (weighted average or other method)
            obi_weight = self.config.get('obi_strategy_weight', 0.5)
            final_position = (final_position * (1 - obi_weight)) + (obi_position * obi_weight)
    
    # Apply final position constraints
    # ... existing position constraint code ...
```

Add configuration parameters to base.yaml:

```yaml
# OBI Risk Management
obi_position_sizing: "atr_based"  # Options: fixed_fraction, atr_based
obi_risk_per_trade: 0.01  # 1% of capital per trade
obi_max_position: 0.5  # Maximum 50% of max_leverage
obi_atr_multiplier: 1.5  # For ATR-based sizing
obi_standalone: true  # Whether OBI operates independently of other strategies
obi_strategy_weight: 0.5  # Weight when combining with other strategies
```

# Test Strategy:
1. Create unit tests for position sizing:
```python
def test_obi_position_sizing():
    # Setup test environment
    config = MockConfig(
        obi_position_sizing='fixed_fraction',
        obi_risk_per_trade=0.01,
        obi_max_position=0.5,
        max_leverage=3.0
    )
    signal_engine = MockSignalEngine()
    risk_manager = RiskManager(config, signal_engine)
    risk_manager.capital = 10000.0  # $10,000 capital
    
    # Test fixed fraction sizing
    position_size = risk_manager.calculate_position_size_for_obi_scalper(1, 0.8, 100.0)
    assert position_size > 0  # Long position
    assert position_size <= 10000 * 3.0 * 0.5  # Within max position limit
    
    # Test ATR-based sizing
    config.obi_position_sizing = 'atr_based'
    signal_engine.atr_value = 2.0  # Mock ATR value
    position_size = risk_manager.calculate_position_size_for_obi_scalper(-1, 0.9, 100.0)
    assert position_size < 0  # Short position
    assert abs(position_size) <= 10000 * 3.0 * 0.5  # Within max position limit
    
    # Test max position cap
    position_size = risk_manager.calculate_position_size_for_obi_scalper(1, 10.0, 100.0)  # Unrealistically high confidence
    assert position_size <= 10000 * 3.0 * 0.5  # Should be capped
```

2. Test integration with strategy evaluator:
```python
def test_obi_risk_integration():
    # Setup
    config = MockConfig(obi_standalone=True)
    risk_manager = RiskManager(config, MockSignalEngine())
    
    # Mock strategy evaluations
    evaluations = [
        {'strategy': 'OBIScalper', 'signal': 1, 'confidence': 0.8},
        {'strategy': 'OtherStrategy', 'signal': -1, 'confidence': 0.7}
    ]
    
    # Test standalone mode
    final_position = risk_manager.manage_positions(evaluations)
    # Should match OBI position since standalone=True
    obi_position = risk_manager.size_obi_scalper_trade(evaluations[0])
    assert abs(final_position - obi_position) < 0.001
    
    # Test combined mode
    config.obi_standalone = False
    config.obi_strategy_weight = 0.6
    final_position = risk_manager.manage_positions(evaluations)
    # Should be a weighted combination
    assert final_position != obi_position
```

3. Test with various market conditions and edge cases:
   - Zero ATR values
   - Extreme confidence values
   - Different capital levels
   - Various leverage settings
