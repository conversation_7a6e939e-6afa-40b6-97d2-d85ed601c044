# Task ID: 2
# Title: Implement OBI Signal Processing
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Extend the SignalEngine to output smoothed OBI values and z-scores for different depth configurations.
# Details:
Update the SignalEngine class to process raw OBI values into smoothed signals and z-scores:

```python
class SignalEngine:
    # ... existing code ...
    
    def _calculate_obi_signals(self):
        """
        Calculate OBI-based signals for configured depth variants
        """
        for depth in self.config.obi_depth_variants:
            # Get raw OBI for this depth
            raw_obi = self.calc_obi(
                self.orderbook.bids, 
                self.orderbook.asks, 
                depth_ranges=[depth], 
                weights=self.config.obi_weight_scheme
            )
            
            # Store raw value
            self.signals[f'obi_raw_{depth}'] = raw_obi
            
            # Calculate smoothed OBI (EMA)
            window = self.config.get(f'obi_smooth_window_{depth}', 20)
            if f'obi_smoothed_{depth}' not in self.signals_history:
                self.signals_history[f'obi_smoothed_{depth}'] = deque(maxlen=window*3)
            
            # Initialize or update smoothed value
            if not self.signals_history[f'obi_smoothed_{depth}']:
                smoothed = raw_obi
            else:
                alpha = 2 / (window + 1)
                prev_smoothed = self.signals.get(f'obi_smoothed_{depth}', raw_obi)
                smoothed = alpha * raw_obi + (1 - alpha) * prev_smoothed
            
            self.signals[f'obi_smoothed_{depth}'] = smoothed
            self.signals_history[f'obi_smoothed_{depth}'].append(smoothed)
            
            # Calculate z-score
            z_window = self.config.get(f'obi_z_window_{depth}', 100)
            if len(self.signals_history[f'obi_smoothed_{depth}']) >= z_window:
                recent_values = list(self.signals_history[f'obi_smoothed_{depth}'])[-z_window:]
                mean = sum(recent_values) / len(recent_values)
                std_dev = (sum((x - mean) ** 2 for x in recent_values) / len(recent_values)) ** 0.5
                
                if std_dev > 0:
                    z_score = (smoothed - mean) / std_dev
                else:
                    z_score = 0.0
                    
                self.signals[f'obi_z_{depth}'] = z_score
    
    def update(self):
        # ... existing update code ...
        
        # Add OBI signal calculation
        self._calculate_obi_signals()
        
        # ... rest of update method ...
```

Ensure the implementation handles historical data properly for accurate z-score calculation and smoothing.

# Test Strategy:
1. Create unit tests in test_signal_engine.py to verify:
   - Correct smoothing of OBI values
   - Proper z-score calculation
   - Handling of initialization (first few ticks)
   - Correct behavior with different window sizes

2. Create integration tests that:
   - Verify no performance regression in existing signals
   - Check signal outputs match expected patterns
   - Test with real market data samples

3. Performance testing:
   - Measure CPU and memory impact
   - Ensure signal calculation doesn't introduce significant latency

Example test:
```python
def test_obi_signal_processing():
    # Setup mock orderbook and config
    config = MockConfig(obi_depth_variants=[10, 20], obi_smooth_window_10=5)
    engine = SignalEngine(config)
    
    # Feed synthetic orderbook data
    for i in range(200):
        # Create mock orderbook with known imbalance
        mock_ob = create_mock_orderbook(bid_heavy=(i % 2 == 0))
        engine.update_orderbook(mock_ob)
        engine.update()
        
        # After warmup period
        if i > 100:
            assert 'obi_raw_10' in engine.signals
            assert 'obi_smoothed_10' in engine.signals
            assert 'obi_z_10' in engine.signals
            
            # Verify z-score is reasonable
            assert -5 <= engine.signals['obi_z_10'] <= 5
```
