# Task ID: 3
# Title: Update Configuration System for OBI Parameters
# Status: done
# Dependencies: 1, 2
# Priority: medium
# Description: Extend settings.py and base.yaml to support new OBI parameters including depth variants and weight schemes.
# Details:
Update the configuration system to include OBI-specific parameters:

1. Modify settings.py to add new configuration classes or extend existing ones:

```python
class OBIConfig:
    """Configuration for Order Book Imbalance features"""
    def __init__(self, config_dict):
        self.enabled = config_dict.get('obi_enabled', False)
        self.depth_variants = config_dict.get('obi_depth_variants', [10])
        
        # Parse weight scheme
        weight_scheme = config_dict.get('obi_weight_scheme', 'uniform')
        if weight_scheme == 'uniform':
            self.weights = None  # calc_obi will use default uniform weights
        elif weight_scheme == 'linear':
            max_depth = max(self.depth_variants)
            self.weights = [(max_depth - i) / max_depth for i in range(max_depth)]
        elif weight_scheme == 'exponential':
            max_depth = max(self.depth_variants)
            self.weights = [math.exp(-0.5 * i) for i in range(max_depth)]
        elif isinstance(weight_scheme, list):
            self.weights = weight_scheme  # Custom weights provided directly
        else:
            raise ValueError(f"Unknown OBI weight scheme: {weight_scheme}")
        
        # Signal processing parameters
        self.smooth_windows = {}
        self.z_windows = {}
        for depth in self.depth_variants:
            self.smooth_windows[depth] = config_dict.get(f'obi_smooth_window_{depth}', 20)
            self.z_windows[depth] = config_dict.get(f'obi_z_window_{depth}', 100)

# Update main Config class to include OBI config
class Config:
    # ... existing code ...
    
    def __init__(self, config_path=None):
        # ... existing initialization ...
        
        # Add OBI configuration
        self.obi = OBIConfig(self.config_dict)
```

2. Update base.yaml with default OBI configuration:

```yaml
# OBI Configuration
obi_enabled: true
obi_depth_variants: [10, 20, 50]
obi_weight_scheme: "exponential"  # Options: uniform, linear, exponential, or custom list

# Signal processing parameters
obi_smooth_window_10: 15
obi_smooth_window_20: 20
obi_smooth_window_50: 30

obi_z_window_10: 100
obi_z_window_20: 150
obi_z_window_50: 200
```

3. Add documentation in the configuration files explaining each parameter.

# Test Strategy:
1. Create unit tests for configuration parsing:
   - Test loading default configuration
   - Test with custom weight schemes
   - Test with invalid configurations
   - Verify all parameters are correctly loaded

2. Create test_config.py with test cases:
```python
def test_obi_config_parsing():
    # Test default config
    config = Config()
    assert hasattr(config, 'obi')
    assert config.obi.enabled is True
    assert 10 in config.obi.depth_variants
    
    # Test custom config
    custom_config = Config('path/to/test_config.yaml')  # With custom OBI settings
    assert custom_config.obi.weights is not None
    
    # Test weight scheme parsing
    for scheme in ['uniform', 'linear', 'exponential', [0.5, 0.3, 0.2]]:
        config_dict = {'obi_weight_scheme': scheme, 'obi_depth_variants': [5]}
        obi_config = OBIConfig(config_dict)
        if scheme == 'uniform':
            assert obi_config.weights is None
        else:
            assert obi_config.weights is not None
```

3. Document all configuration options in a README or documentation file with examples.
