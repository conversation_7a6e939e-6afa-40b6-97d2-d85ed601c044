#!/usr/bin/env python3
# scripts/test_depth_metrics_integration.py

"""
Test script to verify the integration of the depth_metrics_calculator with the SignalEngine.
This script loads a sample parquet file, initializes the SignalEngine, and checks if
depth_slope and depth_skew metrics are properly calculated.
"""

import os
import sys
import logging
import pandas as pd
from pathlib import Path

# Add project root to path to allow imports
project_root = str(Path(__file__).parent.parent.absolute())
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.signals.calculator import SignalEngine
from hyperliquid_bot.signals.depth_metrics_calculator import calculate_depth_metrics
from hyperliquid_bot.data import DataHandlerInterface

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("test_depth_metrics_integration")

class MockDataHandler(DataHandlerInterface):
    """Mock DataHandler that loads a parquet file directly for testing."""
    
    def __init__(self, parquet_path):
        self.data = pd.read_parquet(parquet_path)
        logger.info(f"Loaded test data with shape: {self.data.shape}")
        logger.info(f"Columns: {self.data.columns.tolist()}")
        
    def get_ohlcv_data(self):
        """Return the loaded parquet data directly."""
        return self.data
    
    # Implement required abstract methods with minimal functionality
    def load_historical_data(self, start_date, end_date):
        """Mock implementation - returns True to indicate success."""
        return True
    
    def get_recent_close_prices(self, lookback_bars=1):
        """Return recent close prices or empty series if not available."""
        if 'close' in self.data.columns:
            return self.data['close'].tail(lookback_bars)
        return pd.Series()
    
    def get_funding_rate(self):
        """Return default funding rate."""
        return 0.0001
    
    def get_open_interest(self):
        """Return empty series for open interest."""
        return pd.Series()
    
    def get_recent_liquidations(self, lookback_bars=24):
        """Return empty dataframe for liquidations."""
        return pd.DataFrame()

def test_depth_metrics_integration():
    """Main test function to verify depth metrics integration."""
    
    # Load config
    config_path = os.path.join(project_root, "configs", "base.yaml")
    config = load_config(config_path)
    
    # Enable depth metrics in config for testing
    if not hasattr(config.regime, 'gms_depth_slope_thin_limit') or config.regime.gms_depth_slope_thin_limit is None:
        logger.info("Setting gms_depth_slope_thin_limit for testing")
        config.regime.gms_depth_slope_thin_limit = 0.5
    
    if not hasattr(config.regime, 'gms_depth_skew_thresh') or config.regime.gms_depth_skew_thresh is None:
        logger.info("Setting gms_depth_skew_thresh for testing")
        config.regime.gms_depth_skew_thresh = 0.3
    
    # Determine path to a test parquet file (using raw data path from config)
    raw_data_path = config.data_paths.l2_data_root
    
    # Find a suitable parquet file for testing
    parquet_files = list(Path(raw_data_path).glob("*.parquet"))
    if not parquet_files:
        logger.error(f"No parquet files found in {raw_data_path}")
        return
    
    test_file = str(parquet_files[0])
    logger.info(f"Using test file: {test_file}")
    
    # Create mock data handler with the test file
    data_handler = MockDataHandler(test_file)
    
    # Test direct calculation with depth_metrics_calculator
    logger.info("Testing direct depth_metrics_calculator...")
    direct_result = calculate_depth_metrics(data_handler.data)
    
    direct_slope = pd.Series(dtype=float) # Initialize empty series for comparison later
    direct_skew = pd.Series(dtype=float)
    if direct_result is not None:
        # The function returns a tuple of (depth_slope, depth_skew)
        direct_slope, direct_skew = direct_result
        
        logger.info("Direct calculation successful.")
        logger.info(f"Direct depth_slope stats: min={direct_slope.min()}, max={direct_slope.max()}, mean={direct_slope.mean()}")
        logger.info(f"Direct depth_skew stats: min={direct_skew.min()}, max={direct_skew.max()}, mean={direct_skew.mean()}")
        assert not direct_slope.isna().all(), "Direct depth_slope calculation resulted in all NaNs"
    else:
        logger.error("Direct calculation failed or returned None")
        # Fail the test if direct calculation doesn't work
        assert False, "Direct call to calculate_depth_metrics failed"
    
    # Test integration with SignalEngine
    logger.info("Testing SignalEngine integration...")
    signal_engine = SignalEngine(config, data_handler)
    
    # Calculate signals (which should include depth metrics)
    signals_df = signal_engine.calculate_all_signals()
    
    if signals_df is not None and not signals_df.empty:
        logger.info(f"SignalEngine calculation successful. Result shape: {signals_df.shape}")
        
        # Check if depth metrics columns exist
        # --- Assertions ---
        assert 'depth_slope' in signals_df.columns, "depth_slope column missing from SignalEngine output"
        assert 'depth_skew' in signals_df.columns, "depth_skew column missing from SignalEngine output"
        
        logger.info("depth_slope column exists in SignalEngine output")
        logger.info("depth_skew column exists in SignalEngine output")
        
        # Assert that the calculation didn't result in all NaNs
        assert not signals_df['depth_slope'].isna().all(), "SignalEngine depth_slope calculation resulted in all NaNs"
        logger.info(f"SignalEngine depth_slope NaN count: {signals_df['depth_slope'].isna().sum()} / {len(signals_df)}")
        logger.info(f"SignalEngine depth_skew NaN count: {signals_df['depth_skew'].isna().sum()} / {len(signals_df)}")

        # Assert that the SignalEngine result matches the direct calculation result
        # This verifies that the correct function was called internally
        try:
            pd.testing.assert_series_equal(signals_df['depth_slope'], direct_slope, check_names=False)
            logger.info("SUCCESS: SignalEngine depth_slope matches direct calculation.")
        except AssertionError as e:
            logger.error("FAIL: SignalEngine depth_slope does NOT match direct calculation!")
            logger.error(e)
            # Optionally re-raise or handle failure
            assert False, "SignalEngine depth_slope mismatch"
            
        # Optional: Assert equality for depth_skew as well
        try:
            pd.testing.assert_series_equal(signals_df['depth_skew'], direct_skew, check_names=False)
            logger.info("SUCCESS: SignalEngine depth_skew matches direct calculation.")
        except AssertionError as e:
            logger.error("FAIL: SignalEngine depth_skew does NOT match direct calculation!")
            logger.error(e)
            assert False, "SignalEngine depth_skew mismatch"
    else:
        logger.error("SignalEngine calculation failed or returned empty result")

if __name__ == "__main__":
    logger.info("Starting depth metrics integration test")
    test_depth_metrics_integration()
    logger.info("Test completed")
