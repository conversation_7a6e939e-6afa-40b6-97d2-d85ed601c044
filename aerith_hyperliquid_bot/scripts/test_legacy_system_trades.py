#!/usr/bin/env python3
"""
Test Legacy System Trades
=========================

Run the legacy backtester to verify it generates trades properly.
This will help us understand what the modern system should produce.
"""

import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.backtester.run_backtest import main as run_legacy_backtest


def main():
    """Run legacy backtest for Q1 2024."""
    print("\n" + "="*60)
    print("Legacy System Test - Q1 2024")
    print("="*60)
    
    # Set up arguments for legacy backtester
    sys.argv = [
        'run_backtest.py',
        '--start', '2024-01-01',
        '--end', '2024-03-31',
        '--config', 'configs/overrides/legacy_system.yaml',
        '--output', 'legacy_q1_2024_test.csv'
    ]
    
    print("\nRunning legacy backtester...")
    print("This uses the proven legacy system that generates ~180 trades/year")
    
    try:
        # Run legacy backtest
        run_legacy_backtest()
        print("\n✓ Legacy backtest completed")
        print("Check legacy_q1_2024_test.csv for results")
    except Exception as e:
        print(f"\n✗ Legacy backtest failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()