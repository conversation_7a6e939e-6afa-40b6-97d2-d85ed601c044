#!/usr/bin/env python3
"""
Analyze regime filtering in backtest logs to understand signal filtering patterns.
"""

import re
import sys
from collections import defaultdict
from pathlib import Path


def analyze_log(log_file):
    """Analyze a backtest log for regime filtering patterns."""
    
    stats = defaultdict(int)
    regime_signals = defaultdict(lambda: defaultdict(int))
    
    with open(log_file, 'r') as f:
        for line in f:
            # Count regime detections
            if "Using mapped regime" in line:
                match = re.search(r"Using mapped regime '(\w+)' \(raw was '(\w+)'\)", line)
                if match:
                    mapped, raw = match.groups()
                    stats[f'regime_{mapped}'] += 1
                    stats[f'raw_regime_{raw}'] += 1
            
            # Count signals by regime
            if "ENTRY:" in line and "signal generated in" in line:
                match = re.search(r"ENTRY: (\w+) signal generated in (\w+) regime", line)
                if match:
                    direction, regime = match.groups()
                    regime_signals[regime][direction] += 1
                    stats['total_signals'] += 1
            
            # Count filtered signals
            if "EMAs not aligned with" in line:
                match = re.search(r"EMAs not aligned with (\w+) regime", line)
                if match:
                    regime = match.groups()[0]
                    stats[f'ema_filtered_{regime}'] += 1
                    
            if "Neutral regime:" in line:
                stats['neutral_regime_filtered'] += 1
                
            # Count trades
            if "TRADE EXECUTED" in line or "Position opened" in line:
                stats['trades_executed'] += 1
    
    return stats, regime_signals


def print_analysis(stats, regime_signals):
    """Print analysis results."""
    
    print("\n=== Regime Detection Analysis ===")
    print(f"BULL regimes detected: {stats.get('regime_BULL', 0)}")
    print(f"BEAR regimes detected: {stats.get('regime_BEAR', 0)}")
    print(f"CHOP regimes detected: {stats.get('regime_CHOP', 0)}")
    
    print("\n=== Raw Regime States ===")
    for key, value in sorted(stats.items()):
        if key.startswith('raw_regime_'):
            print(f"{key.replace('raw_regime_', '')}: {value}")
    
    print("\n=== Signals by Regime ===")
    for regime, directions in regime_signals.items():
        print(f"\n{regime} regime:")
        for direction, count in directions.items():
            print(f"  {direction}: {count}")
    
    print("\n=== Filtering Statistics ===")
    print(f"Total signals generated: {stats.get('total_signals', 0)}")
    print(f"Trades executed: {stats.get('trades_executed', 0)}")
    print(f"Neutral regime filtered: {stats.get('neutral_regime_filtered', 0)}")
    
    print("\n=== EMA Alignment Filters ===")
    for key, value in sorted(stats.items()):
        if key.startswith('ema_filtered_'):
            print(f"{key.replace('ema_filtered_', '')} regime: {value}")
    
    # Calculate filter rate
    total_signals = stats.get('total_signals', 0)
    trades = stats.get('trades_executed', 0)
    if total_signals > 0:
        filter_rate = (total_signals - trades) / total_signals * 100
        print(f"\nOverall filter rate: {filter_rate:.1f}%")


def main():
    if len(sys.argv) < 2:
        # Find the most recent backtest log
        log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
        logs = sorted(log_dir.glob("backtest_run_*.log"))
        if logs:
            log_file = logs[-1]
            print(f"Analyzing most recent log: {log_file}")
        else:
            print("No backtest logs found")
            return
    else:
        log_file = sys.argv[1]
    
    stats, regime_signals = analyze_log(log_file)
    print_analysis(stats, regime_signals)


if __name__ == "__main__":
    main()