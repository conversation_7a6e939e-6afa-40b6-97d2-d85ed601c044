#!/usr/bin/env python3
"""
Test Field Mapping Fix
======================

Quick test to verify volume_imbalance mapping works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from datetime import datetime
from hyperliquid_bot.modern.adapters.data_adapter import ModernDataAdapter, AdapterConfig
from hyperliquid_bot.modern.contracts.data_schema import ModernDataContract

def main():
    print("=== Testing Field Mapping Fix ===\n")
    
    # Create test data similar to features_1s
    test_data = pd.DataFrame({
        'timestamp': pd.date_range('2024-01-15', periods=10, freq='1s'),
        'close': [42000 + i * 10 for i in range(10)],
        'obi_smoothed': [0.1, 0.2, -0.1, -0.2, 0.15, -0.15, 0.05, -0.05, 0.0, 0.1],
        'spread_mean': [1.5] * 10,
        'spread_std': [0.5] * 10,
        'atr_14_sec': [200.0] * 10,
        'atr_percent_sec': [0.005] * 10,
        'ma_slope_ema_30s': [0.001] * 10,
    })
    
    print("Original columns:", list(test_data.columns))
    print(f"Has 'obi_smoothed': {'obi_smoothed' in test_data.columns}")
    print(f"Has 'volume_imbalance': {'volume_imbalance' in test_data.columns}")
    
    # Test data adapter
    adapter_config = AdapterConfig(
        handle_missing_with_defaults=True,
        log_transformations=True,
        compute_derived_fields=True
    )
    adapter = ModernDataAdapter(adapter_config)
    
    # Apply transformation
    print("\nApplying data adapter transformation...")
    adapted_df = adapter.adapt_features_dataframe(test_data)
    
    print("\nAdapted columns:", list(adapted_df.columns))
    print(f"Has 'obi_smoothed': {'obi_smoothed' in adapted_df.columns}")
    print(f"Has 'volume_imbalance': {'volume_imbalance' in adapted_df.columns}")
    
    # Check if mapping worked
    if 'volume_imbalance' in adapted_df.columns:
        print(f"\n✅ SUCCESS: Field mapping worked!")
        print(f"First few volume_imbalance values: {adapted_df['volume_imbalance'].head().tolist()}")
    else:
        print(f"\n❌ FAILED: Field mapping did not work")
        
        # Try direct contract transformation
        print("\nTrying direct contract transformation...")
        contract = ModernDataContract()
        transformed_df = contract.transform_to_expected_schema(test_data)
        
        print("Contract transformed columns:", list(transformed_df.columns))
        print(f"Has 'volume_imbalance' after contract: {'volume_imbalance' in transformed_df.columns}")

if __name__ == "__main__":
    main()