#!/usr/bin/env python3
"""
Analyze EMA Values During Backtest
===================================

Extract and analyze actual EMA values to understand why no trades are generated.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime, timedelta
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.data_loader import ModernDataLoader
from hyperliquid_bot.modern.signal_engine import ModernSignalEngine
from hyperliquid_bot.modern.tf_v3_modern import ModernTFV3Strategy

def main():
    print("=== Analyzing EMA Values During Backtest ===\n")
    
    # Setup
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    logging.basicConfig(level=logging.INFO)
    
    # Load data
    loader = ModernDataLoader(config)
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 17)
    
    print(f"Loading data from {start_date} to {end_date}...")
    hourly_features = loader.load_hourly_features(start_date, end_date)
    
    # Create signal engine
    signal_engine = ModernSignalEngine(config)
    
    # Strategy for reference
    strategy = ModernTFV3Strategy(config)
    
    print(f"\nTotal hourly bars: {len(hourly_features)}")
    print(f"\nEMA Configuration:")
    print(f"  Fast: {signal_engine.ema_fast}")
    print(f"  Slow: {signal_engine.ema_slow}")
    print(f"  Baseline: {signal_engine.ema_baseline}")
    
    # Track EMA values and entry opportunities
    ema_analysis = []
    entry_opportunities = {
        'long': [],
        'short': []
    }
    
    # Process each hourly bar
    for timestamp, hourly_bar in hourly_features.iterrows():
        # Calculate signals
        hourly_bar_dict = hourly_bar.to_dict()
        hourly_bar_dict['timestamp'] = timestamp
        
        # Calculate technical indicators
        # Need sufficient history for EMAs
        lookback_hours = signal_engine.calculate_required_lookback()
        history_start = timestamp - timedelta(hours=lookback_hours)
        
        # Get historical data up to current timestamp
        historical_data = hourly_features[hourly_features.index <= timestamp].tail(lookback_hours)
        
        if len(historical_data) < 30:  # Need at least 30 bars for reliable EMAs
            continue
            
        # Calculate signals
        regime_features = {'current_state': 'Weak_Bear_Trend', 'current_confidence': 0.7}  # Dummy for now
        signals = signal_engine.calculate_signals(historical_data, regime_features)
        
        # Extract EMAs
        ema_fast = signals.get('ema_fast', np.nan)
        ema_slow = signals.get('ema_slow', np.nan)
        ema_baseline = signals.get('ema_baseline', np.nan)
        close_price = signals.get('close', np.nan)
        
        # Skip if any NaN
        if any(pd.isna(x) for x in [ema_fast, ema_slow, ema_baseline, close_price]):
            continue
            
        # Calculate conditions
        fast_above_slow = ema_fast > ema_slow
        fast_below_slow = ema_fast < ema_slow
        ema_diff = ema_fast - ema_slow
        ema_diff_pct = (ema_diff / ema_slow) * 100 if ema_slow > 0 else 0
        
        # Store analysis
        analysis_row = {
            'timestamp': timestamp,
            'close': close_price,
            'ema_fast': ema_fast,
            'ema_slow': ema_slow,
            'ema_baseline': ema_baseline,
            'fast_above_slow': fast_above_slow,
            'ema_diff': ema_diff,
            'ema_diff_pct': ema_diff_pct
        }
        ema_analysis.append(analysis_row)
        
        # Check for potential entry opportunities
        # Simplified logic from strategy
        if fast_above_slow:
            entry_opportunities['long'].append({
                'timestamp': timestamp,
                'ema_diff_pct': ema_diff_pct,
                'close': close_price
            })
        elif fast_below_slow:
            entry_opportunities['short'].append({
                'timestamp': timestamp,
                'ema_diff_pct': abs(ema_diff_pct),
                'close': close_price
            })
    
    # Convert to DataFrame for analysis
    ema_df = pd.DataFrame(ema_analysis)
    
    if ema_df.empty:
        print("\nNo valid EMA data found!")
        return
        
    print(f"\n\nEMA Analysis Results:")
    print(f"Total analyzed bars: {len(ema_df)}")
    
    # EMA relationship statistics
    fast_above_count = ema_df['fast_above_slow'].sum()
    fast_below_count = (~ema_df['fast_above_slow']).sum()
    
    print(f"\nEMA Crossover Statistics:")
    print(f"  Fast > Slow: {fast_above_count} bars ({fast_above_count/len(ema_df)*100:.1f}%)")
    print(f"  Fast < Slow: {fast_below_count} bars ({fast_below_count/len(ema_df)*100:.1f}%)")
    
    # EMA difference statistics
    print(f"\nEMA Difference Statistics:")
    print(f"  Mean diff %: {ema_df['ema_diff_pct'].mean():.3f}%")
    print(f"  Max diff %: {ema_df['ema_diff_pct'].max():.3f}%")
    print(f"  Min diff %: {ema_df['ema_diff_pct'].min():.3f}%")
    print(f"  Std diff %: {ema_df['ema_diff_pct'].std():.3f}%")
    
    # Show some examples
    print(f"\nFirst 10 EMA values:")
    for _, row in ema_df.head(10).iterrows():
        print(f"  {row['timestamp']}: Fast={row['ema_fast']:.2f}, Slow={row['ema_slow']:.2f}, "
              f"Diff={row['ema_diff']:.2f} ({row['ema_diff_pct']:.3f}%), "
              f"{'BULL' if row['fast_above_slow'] else 'BEAR'}")
    
    # Entry opportunities
    print(f"\n\nPotential Entry Opportunities:")
    print(f"  Long opportunities: {len(entry_opportunities['long'])}")
    print(f"  Short opportunities: {len(entry_opportunities['short'])}")
    
    # Show some short opportunities (should align with bear regime)
    if entry_opportunities['short']:
        print(f"\nExample SHORT opportunities (first 5):")
        for opp in entry_opportunities['short'][:5]:
            print(f"  {opp['timestamp']}: EMA diff={opp['ema_diff_pct']:.3f}%, Close={opp['close']:.2f}")
    
    # Check actual close prices vs EMAs
    print(f"\n\nPrice vs EMA Analysis:")
    first_bar = ema_df.iloc[0]
    last_bar = ema_df.iloc[-1]
    
    print(f"\nFirst bar:")
    print(f"  Close: {first_bar['close']:.2f}")
    print(f"  EMA Fast: {first_bar['ema_fast']:.2f}")
    print(f"  EMA Slow: {first_bar['ema_slow']:.2f}")
    print(f"  EMA Baseline: {first_bar['ema_baseline']:.2f}")
    
    print(f"\nLast bar:")
    print(f"  Close: {last_bar['close']:.2f}")
    print(f"  EMA Fast: {last_bar['ema_fast']:.2f}")
    print(f"  EMA Slow: {last_bar['ema_slow']:.2f}")
    print(f"  EMA Baseline: {last_bar['ema_baseline']:.2f}")
    
    # Save detailed analysis
    output_file = 'ema_analysis_results.json'
    with open(output_file, 'w') as f:
        json.dump({
            'summary': {
                'total_bars': len(ema_df),
                'fast_above_slow_count': int(fast_above_count),
                'fast_below_slow_count': int(fast_below_count),
                'mean_diff_pct': float(ema_df['ema_diff_pct'].mean()),
                'long_opportunities': len(entry_opportunities['long']),
                'short_opportunities': len(entry_opportunities['short'])
            },
            'ema_values': ema_df.head(20).to_dict('records'),
            'entry_opportunities': entry_opportunities
        }, f, indent=2, default=str)
    
    print(f"\nDetailed results saved to: {output_file}")
    
    # CRITICAL INSIGHT
    print(f"\n\n🔍 CRITICAL INSIGHT:")
    if abs(ema_df['ema_diff_pct'].mean()) < 0.1:
        print("  ⚠️  EMAs are too close together! The difference is less than 0.1%")
        print("  This explains why no trades are generated - the EMAs are essentially equal!")
        print("  The issue might be:")
        print("  1. EMA periods are too similar (12 vs 26)")
        print("  2. Not enough price movement in the data")
        print("  3. The lookback calculation might be using too much data")

if __name__ == "__main__":
    main()