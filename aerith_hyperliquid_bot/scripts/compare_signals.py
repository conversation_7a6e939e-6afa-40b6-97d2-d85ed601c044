# scripts/compare_signals.py

import logging
import pandas as pd
from pathlib import Path
import sys
import importlib.util
from datetime import datetime

# --- Add project root to sys.path ---
project_root = Path(__file__).parent.parent.resolve()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# --- Imports (Current Versions) ---
from hyperliquid_bot.config.settings import Config, load_config
from hyperliquid_bot.data.handler import HistoricalDataHandler as CurrentDataHandler
from hyperliquid_bot.signals.calculator import SignalCalculator as CurrentSignalCalculator

# --- Imports (Previous Versions - from Desktop using importlib.util) ---
try:
    handler_prev_path = "/Users/<USER>/Desktop/handler_prev.py"
    calculator_prev_path = "/Users/<USER>/Desktop/calculator_prev.py"

    handler_prev_spec = importlib.util.spec_from_file_location("handler_prev", handler_prev_path)
    handler_prev = importlib.util.module_from_spec(handler_prev_spec)
    handler_prev_spec.loader.exec_module(handler_prev)

    calculator_prev_spec = importlib.util.spec_from_file_location("calculator_prev", calculator_prev_path)
    calculator_prev = importlib.util.module_from_spec(calculator_prev_spec)
    calculator_prev_spec.loader.exec_module(calculator_prev)

    PrevDataHandler = handler_prev.HistoricalDataHandler
    PrevSignalCalculator = calculator_prev.SignalCalculator
    prev_versions_available = True
except Exception as e:
    print(f"ERROR: Could not import previous versions of Handler/Calculator: {e}")
    prev_versions_available = False
    class PrevDataHandler: # Dummy
        def __init__(self, config): self.config=config; self.ohlcv_data=pd.DataFrame()
        def load_historical_data(self, *args, **kwargs): pass
        def get_ohlcv_data(self): return self.ohlcv_data
    class PrevSignalCalculator: # Dummy
        def __init__(self, config, data_handler): pass
        def calculate_all_signals(self): return pd.DataFrame()

# --- Basic Logging Setup ---
log_format = '%(asctime)s [%(levelname)-5s] %(name)-25s: %(message)s'
logging.basicConfig(level=logging.INFO, format=log_format)
logger = logging.getLogger("SignalComparison")

def compare_signals(config: Config):
    """Loads data and calculates signals using both versions, then compares."""
    if not prev_versions_available:
        logger.error("Cannot run comparison due to missing previous version imports.")
        return

    logger.info("Comparing signal generation between previous and current versions...")

    # --- Run Previous Version ---
    logger.info("--- Processing Previous Version ---")
    try:
        prev_data_handler = PrevDataHandler(config)
        prev_data_handler.load_historical_data(config.start_date, config.end_date)
        prev_ohlcv_df = prev_data_handler.get_ohlcv_data()
        if prev_ohlcv_df.empty: raise ValueError("Previous DataHandler failed to load OHLCV data.")
        prev_signal_calculator = PrevSignalCalculator(config, prev_data_handler)
        prev_signals_df = prev_signal_calculator.calculate_all_signals()
        if prev_signals_df.empty: raise ValueError("Previous SignalCalculator failed.")
        logger.info(f"Previous signals calculated. Shape: {prev_signals_df.shape}")
    except Exception as e:
        logger.error(f"Error running previous version: {e}", exc_info=True)
        return

    # --- Run Current Version ---
    logger.info("--- Processing Current Version ---")
    try:
        current_data_handler = CurrentDataHandler(config)
        current_data_handler.load_historical_data(config.start_date, config.end_date)
        current_combined_df = current_data_handler.get_ohlcv_data() # Gets OHLCV + raw micro
        if current_combined_df.empty: raise ValueError("Current DataHandler failed to load data.")

        current_signal_calculator = CurrentSignalCalculator(config, current_data_handler)
        current_signals_df = current_signal_calculator.calculate_all_signals() # Calculates all signals
        if current_signals_df.empty: raise ValueError("Current SignalCalculator failed.")
        logger.info(f"Current signals calculated. Shape: {current_signals_df.shape}")

    except Exception as e:
        logger.error(f"Error running current version: {e}", exc_info=True)
        return

    # --- Align DataFrames ---
    logger.info("Aligning DataFrames on common index...")
    common_index = prev_signals_df.index.intersection(current_signals_df.index)
    logger.info(f"Comparing on common index length: {len(common_index)}")
    if len(common_index) == 0:
         logger.error("No common timestamps found between previous and current data. Cannot compare.")
         return
    prev_signals_df = prev_signals_df.loc[common_index]
    current_signals_df = current_signals_df.loc[common_index]

    # --- Save Full DataFrames for Manual Inspection ---
    try:
        prev_signals_df.to_csv("prev_signals_full.csv")
        current_signals_df.to_csv("current_signals_full.csv")
        logger.info("Saved full previous and current signals to CSV files (prev_signals_full.csv, current_signals_full.csv).")
    except Exception as e:
        logger.error(f"Error saving full signal DataFrames: {e}")

    # --- Compare Signals ---
    logger.info("--- Comparing Key Signals (and others if possible) ---")
    # Signals essential for the rule_based benchmark TF strategy
    signals_to_compare = ['open', 'high', 'low', 'close', 'volume', 'forecast', 'adx', 'atr_tf']
    # Add other common columns if they exist in both
    common_cols = list(prev_signals_df.columns.intersection(current_signals_df.columns))
    signals_to_compare.extend([c for c in common_cols if c not in signals_to_compare])

    all_match = True
    mismatched_cols = []

    for signal in signals_to_compare:
        logger.debug(f"Comparing '{signal}'...") # Use debug level for individual signal checks

        s_prev = prev_signals_df[signal]
        s_curr = current_signals_df[signal]

        # Handle potential type differences before comparison (e.g., object vs float)
        try:
            s_prev = pd.to_numeric(s_prev, errors='coerce')
            s_curr = pd.to_numeric(s_curr, errors='coerce')
        except Exception:
             pass # Ignore if conversion fails, comparison might still work or fail below

        try:
            # Use pandas testing function for robust comparison with tolerance
            pd.testing.assert_series_equal(s_prev, s_curr, check_dtype=False, rtol=1e-5, atol=1e-8)
            logger.debug(f"  '{signal}': MATCH")
        except AssertionError:
            all_match = False
            mismatched_cols.append(signal)
            logger.warning(f"  '{signal}': MISMATCH FOUND!")
            # Log details from the assertion error if helpful
            # logger.warning(f"    Details: {str(e).splitlines()[0]}") # Log first line of error
            # Calculate and show difference stats for numeric columns
            if pd.api.types.is_numeric_dtype(s_prev) and pd.api.types.is_numeric_dtype(s_curr):
                diff = (s_prev - s_curr).abs()
                max_diff = diff.max()
                mean_diff = diff.mean()
                if pd.notna(max_diff): logger.warning(f"    Max Abs Difference: {max_diff:.6g}")
                if pd.notna(mean_diff): logger.warning(f"    Mean Abs Difference: {mean_diff:.6g}")
        except Exception as cmp_e:
            logger.error(f"Error comparing signal '{signal}': {cmp_e}")
            all_match = False
            mismatched_cols.append(f"{signal} (Error)")


    if all_match:
        logger.info(">>> Conclusion: All common signals appear to match between versions.")
    else:
        logger.warning(f">>> Conclusion: Signal MISMATCH detected. Mismatched/Error columns: {mismatched_cols}")
        logger.warning(">>> Please inspect the saved CSV files (prev_signals_full.csv, current_signals_full.csv) for differences.")


def main():
    logger.info("Starting signal comparison script...")
    try:
        project_root = Path(__file__).parent.parent.resolve()
        config_path = project_root / 'config.yaml'
        if not config_path.exists(): raise FileNotFoundError(f"Config file not found at {config_path}")
        config = load_config(str(config_path)) # Load full config

        # Setup file logging for the script itself
        log_dir_path = Path(config.data_paths.log_dir)
        log_dir_path.mkdir(parents=True, exist_ok=True)
        log_file = log_dir_path / f"signal_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO) # Or DEBUG
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)
        logger.info(f"Logging comparison details to: {log_file}")

        compare_signals(config)

    except FileNotFoundError as e: logger.critical(f"Configuration file error: {e}")
    except Exception as e: logger.critical(f"An unexpected error occurred: {e}", exc_info=True)

if __name__ == "__main__":
    main()