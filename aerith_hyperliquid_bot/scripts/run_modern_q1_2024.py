#!/usr/bin/env python3
"""
Run Q1 2024 Modern System Backtest - Calibrated
===============================================

Run just Q1 2024 to avoid duplicate timestamp issues.
"""

import sys
from pathlib import Path
from datetime import datetime
import json

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine


def main():
    print("="*80)
    print("AERITH MODERN SYSTEM - Q1 2024 BACKTEST (CALIBRATED)")
    print("="*80)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Q1 2024 dates
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 3, 31, 23, 59, 59)
    
    # Load configuration
    config_path = project_root / "configs/overrides/modern_system_v2_complete.yaml"
    config = load_config(config_path=str(config_path))
    
    print(f"📅 Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    print(f"⚙️  Config: modern_system_v2_complete.yaml")
    print()
    print("🎯 CALIBRATED SETTINGS:")
    print("  - Confidence threshold: 0.5")
    print("  - State persistence: DISABLED")
    print("  - Max transitions/hour: 60")
    print("  - Risk per trade: 25%")
    print()
    print("-"*80)
    
    try:
        # Create modern backtesting engine
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        # Run backtest
        print("\n🚀 Starting Q1 2024 backtest...\n")
        results = engine.run_backtest()
        
        # Extract key metrics
        total_trades = results['performance']['total_trades']
        total_return = results['performance']['total_return']
        win_rate = results['performance']['win_rate']
        runtime = results['runtime_seconds']
        integrity = results.get('system_integrity', {})
        
        print("\n" + "="*80)
        print("📊 Q1 2024 RESULTS (3 months)")
        print("="*80)
        print(f"Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        print(f"Runtime: {runtime/60:.1f} minutes")
        print()
        print("PERFORMANCE METRICS:")
        print(f"  - Total Trades: {total_trades}")
        print(f"  - Total Return: {total_return:.2%}")
        print(f"  - Win Rate: {win_rate:.2%}")
        print()
        
        # Extrapolate to full year
        print("FULL YEAR PROJECTION (4x Q1):")
        print(f"  - Expected Trades: {total_trades * 4}")
        print(f"  - Expected ROI: {(1 + total_return)**4 - 1:.2%}")
        print()
        
        # Compare with legacy
        print("VS LEGACY SYSTEM (full year):")
        legacy_trades = 180
        legacy_roi = 2.15  # 215%
        
        projected_trades = total_trades * 4
        projected_roi = (1 + total_return)**4 - 1
        
        print(f"  - Trade Count: {projected_trades}/{legacy_trades} ({projected_trades/legacy_trades:.1%})")
        print(f"  - ROI: {projected_roi:.2%}/{legacy_roi:.0%} ({projected_roi/legacy_roi:.1%})")
        
        # System integrity
        if integrity.get('is_pure_modern', False):
            print("\n✅ System Integrity: PURE MODERN (no legacy fallbacks)")
        else:
            print("\n❌ System Integrity: LEGACY CONTAMINATION DETECTED")
            
        # Save results
        output_path = Path('modern_q1_2024_results.json')
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Detailed results saved to: {output_path}")
        
        # Trade distribution by month
        if results['trades']:
            print("\nMONTHLY DISTRIBUTION:")
            trades_by_month = {}
            for trade in results['trades']:
                month = trade['timestamp'].strftime('%Y-%m')
                trades_by_month[month] = trades_by_month.get(month, 0) + 1
            
            for month in sorted(trades_by_month.keys()):
                count = trades_by_month[month]
                print(f"  {month}: {'█' * (count // 2)} ({count})")
        
        print("\n" + "="*80)
        
    except Exception as e:
        print(f"\n❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()