#!/usr/bin/env python3
"""
Performance Profiling Script for Modern System (Continuous GMS + TF-v3)

This script profiles the execution of the Modern System to identify performance bottlenecks.
It uses cProfile to capture detailed timing information and generates a comprehensive report.
"""

import cProfile
import pstats
import io
import sys
import os
from pathlib import Path
from datetime import datetime
import subprocess

def run_profiled_backtest():
    """Run the backtest with profiling enabled."""
    
    # Get the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # Set up paths
    run_backtest_script = project_root / "hyperliquid_bot" / "backtester" / "run_backtest.py"
    profile_config = project_root / "configs" / "profile_modern_system.yaml"
    
    print("=== MODERN SYSTEM PERFORMANCE PROFILING ===")
    print(f"Project Root: {project_root}")
    print(f"Backtest Script: {run_backtest_script}")
    print(f"Profile Config: {profile_config}")
    print(f"Working Directory: {os.getcwd()}")
    
    # Verify files exist
    if not run_backtest_script.exists():
        print(f"ERROR: Backtest script not found: {run_backtest_script}")
        return False
        
    if not profile_config.exists():
        print(f"ERROR: Profile config not found: {profile_config}")
        return False
    
    # Change to project root for execution
    original_cwd = os.getcwd()
    os.chdir(project_root)
    
    try:
        print("\n=== STARTING PROFILED BACKTEST ===")
        print("Date Range: 2025-03-02 to 2025-03-05 (4 days)")
        print("System: Continuous GMS + TF-v3 Strategy")
        print("Profiling with cProfile...")
        
        # Create profiler
        profiler = cProfile.Profile()
        
        # Import and run the backtest within the profiler
        sys.path.insert(0, str(project_root))
        
        # Set up command line arguments for the backtest
        original_argv = sys.argv.copy()
        sys.argv = [
            str(run_backtest_script),
            "--override", str(profile_config),
            "--run-id", "profile_modern_system",
            "--skip-validation-warnings"
        ]
        
        try:
            # Start profiling
            profiler.enable()
            
            # Import and run the main function
            from hyperliquid_bot.backtester.run_backtest import main
            main()
            
        finally:
            # Stop profiling
            profiler.disable()
            sys.argv = original_argv
        
        print("\n=== BACKTEST COMPLETED ===")
        return profiler
        
    except Exception as e:
        print(f"ERROR during profiled execution: {e}")
        import traceback
        traceback.print_exc()
        return None
        
    finally:
        os.chdir(original_cwd)

def analyze_profile_results(profiler):
    """Analyze the profiling results and generate a detailed report."""
    
    if profiler is None:
        print("ERROR: No profiler data to analyze")
        return
    
    print("\n=== ANALYZING PROFILE RESULTS ===")
    
    # Create a StringIO buffer to capture pstats output
    s = io.StringIO()
    ps = pstats.Stats(profiler, stream=s)
    
    # Sort by cumulative time and get top functions
    ps.sort_stats('cumulative')
    
    print("\n=== TOP BOTTLENECKS BY CUMULATIVE TIME ===")
    print("(Functions that take the most total time including sub-calls)")
    
    # Get the raw stats for detailed analysis
    stats = ps.stats
    
    # Convert to list and sort by cumulative time
    func_stats = []
    for func_key, (cc, nc, tt, ct, callers) in stats.items():
        filename, line_num, func_name = func_key
        func_stats.append({
            'function': f"{func_name} ({Path(filename).name}:{line_num})",
            'filename': filename,
            'function_name': func_name,
            'ncalls': nc,
            'tottime': tt,
            'cumtime': ct,
            'percall_tot': tt/nc if nc > 0 else 0,
            'percall_cum': ct/nc if nc > 0 else 0
        })
    
    # Sort by cumulative time
    func_stats.sort(key=lambda x: x['cumtime'], reverse=True)
    
    # Print top 15 by cumulative time
    print("\nTop 15 Functions by Cumulative Time:")
    print("-" * 120)
    print(f"{'Function':<50} {'File':<25} {'NCalls':<8} {'TotTime':<8} {'CumTime':<8} {'PerCall':<8}")
    print("-" * 120)
    
    for i, stat in enumerate(func_stats[:15]):
        filename = Path(stat['filename']).name
        if len(filename) > 24:
            filename = "..." + filename[-21:]
        
        function_name = stat['function_name']
        if len(function_name) > 49:
            function_name = function_name[:46] + "..."
            
        print(f"{function_name:<50} {filename:<25} {stat['ncalls']:<8} "
              f"{stat['tottime']:<8.3f} {stat['cumtime']:<8.3f} {stat['percall_cum']:<8.6f}")
    
    # Sort by total time (excluding sub-calls)
    func_stats.sort(key=lambda x: x['tottime'], reverse=True)
    
    print("\n\nTop 15 Functions by Total Time (excluding sub-calls):")
    print("-" * 120)
    print(f"{'Function':<50} {'File':<25} {'NCalls':<8} {'TotTime':<8} {'CumTime':<8} {'PerCall':<8}")
    print("-" * 120)
    
    for i, stat in enumerate(func_stats[:15]):
        filename = Path(stat['filename']).name
        if len(filename) > 24:
            filename = "..." + filename[-21:]
        
        function_name = stat['function_name']
        if len(function_name) > 49:
            function_name = function_name[:46] + "..."
            
        print(f"{function_name:<50} {filename:<25} {stat['ncalls']:<8} "
              f"{stat['tottime']:<8.3f} {stat['cumtime']:<8.3f} {stat['percall_tot']:<8.6f}")
    
    # Identify key bottlenecks in our system
    print("\n\n=== KEY SYSTEM BOTTLENECKS ANALYSIS ===")
    
    # Filter for our key modules
    key_modules = [
        'handler.py', 'detector.py', 'calculator.py', 'backtester.py',
        'evaluator.py', 'tf_v3.py', 'portfolio.py'
    ]
    
    system_bottlenecks = []
    for stat in func_stats:
        filename = Path(stat['filename']).name
        if any(module in filename for module in key_modules):
            system_bottlenecks.append(stat)
    
    print("\nTop System Component Bottlenecks:")
    print("-" * 120)
    print(f"{'Function':<50} {'File':<25} {'NCalls':<8} {'TotTime':<8} {'CumTime':<8} {'PerCall':<8}")
    print("-" * 120)
    
    for stat in system_bottlenecks[:10]:
        filename = Path(stat['filename']).name
        if len(filename) > 24:
            filename = "..." + filename[-21:]
        
        function_name = stat['function_name']
        if len(function_name) > 49:
            function_name = function_name[:46] + "..."
            
        print(f"{function_name:<50} {filename:<25} {stat['ncalls']:<8} "
              f"{stat['tottime']:<8.3f} {stat['cumtime']:<8.3f} {stat['percall_cum']:<8.6f}")
    
    # Save detailed stats to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    stats_file = f"profile_modern_system_{timestamp}.txt"
    
    with open(stats_file, 'w') as f:
        f.write("=== MODERN SYSTEM PERFORMANCE PROFILE REPORT ===\n")
        f.write(f"Generated: {datetime.now()}\n")
        f.write(f"Date Range: 2025-03-02 to 2025-03-05 (4 days)\n")
        f.write(f"System: Continuous GMS + TF-v3 Strategy\n\n")
        
        # Write all stats to file
        ps_file = pstats.Stats(profiler, stream=f)
        ps_file.sort_stats('cumulative')
        ps_file.print_stats(50)  # Top 50 functions
        
        f.write("\n\n=== DETAILED FUNCTION ANALYSIS ===\n")
        for i, stat in enumerate(func_stats[:30]):
            f.write(f"\n{i+1}. {stat['function']}\n")
            f.write(f"   File: {stat['filename']}\n")
            f.write(f"   Calls: {stat['ncalls']}\n")
            f.write(f"   Total Time: {stat['tottime']:.6f}s\n")
            f.write(f"   Cumulative Time: {stat['cumtime']:.6f}s\n")
            f.write(f"   Per Call (Total): {stat['percall_tot']:.6f}s\n")
            f.write(f"   Per Call (Cumulative): {stat['percall_cum']:.6f}s\n")
    
    print(f"\nDetailed profile report saved to: {stats_file}")
    
    # Generate summary
    total_time = max(stat['cumtime'] for stat in func_stats[:5])
    print(f"\n=== PROFILING SUMMARY ===")
    print(f"Total Execution Time: ~{total_time:.2f} seconds")
    print(f"Top 5 bottlenecks account for significant execution time")
    print(f"Detailed analysis saved to: {stats_file}")

def main():
    """Main profiling function."""
    print("Starting Modern System Performance Profiling...")
    
    # Run the profiled backtest
    profiler = run_profiled_backtest()
    
    if profiler:
        # Analyze the results
        analyze_profile_results(profiler)
        print("\n=== PROFILING COMPLETED SUCCESSFULLY ===")
    else:
        print("\n=== PROFILING FAILED ===")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 