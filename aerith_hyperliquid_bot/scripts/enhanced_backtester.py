#!/usr/bin/env python3
"""
Enhanced backtester with trade direction analytics
"""

import sys
import os
from datetime import datetime

# Add project root to path
sys.path.append('/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot')

from hyperliquid_bot.backtester.run_backtest import main as original_main
from hyperliquid_bot.config.settings import Config
from scripts.trade_direction_analytics import get_analytics, compare_systems

def run_backtest_with_analytics(system_mode, config_override=None):
    """Run backtest with enhanced analytics tracking"""
    
    print(f"\n🚀 Starting {system_mode.upper()} system backtest with analytics...")
    
    # Get analytics instance for this system
    analytics = get_analytics(system_mode)
    analytics.reset()
    
    # Prepare arguments for the backtest
    import argparse
    
    # Create args namespace
    args = argparse.Namespace()
    args.system = system_mode
    args.timeframe = None
    args.run_id = None 
    args.skip_validation_warnings = False
    
    if system_mode == 'legacy':
        args.override = None  # Use base config
    else:
        args.override = config_override or 'configs/overrides/execution_refinement_enabled.yaml'
    
    # Monkey patch the backtester to add analytics tracking
    patch_backtester_for_analytics(system_mode)
    
    try:
        # Run the backtest
        from hyperliquid_bot.backtester.run_backtest import main
        main_with_args(args)
        
    except Exception as e:
        print(f"❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()
        return None
        
    # Generate and save analytics
    summary = analytics.print_summary(system_mode)
    filepath = analytics.save_to_file(system_mode)
    
    return summary, filepath

def patch_backtester_for_analytics(system_mode):
    """Monkey patch the backtester to add analytics tracking"""
    
    from hyperliquid_bot.backtester import backtester
    from hyperliquid_bot.utils.state_mapping import map_gms_state
    
    # Get analytics instance
    analytics = get_analytics(system_mode)
    
    # Store original run method
    original_run = backtester.Backtester.run
    
    def enhanced_run(self, start_date, end_date):
        """Enhanced run method with analytics tracking"""
        
        # Store reference to analytics in backtester instance
        self.analytics = analytics
        
        # Monkey patch the main simulation loop
        original_simulate_step = getattr(self, '_simulate_step', None)
        
        # Call original run method
        return original_run(self, start_date, end_date)
    
    # Replace the run method
    backtester.Backtester.run = enhanced_run
    
    # Also patch strategy evaluation to track signals
    from hyperliquid_bot.strategies import evaluator
    
    original_get_signals = evaluator.StrategyEvaluator.get_signals_for_regime
    
    def enhanced_get_signals(self, regime_state, signals_dict, ohlcv_history=None):
        """Enhanced signal generation with analytics tracking"""
        
        # Track regime detection and mapping
        if hasattr(self, 'analytics') or system_mode in ['legacy', 'modern']:
            analytics = get_analytics(system_mode)
            
            # Map the regime
            mapped_regime = map_gms_state(regime_state, 
                                        map_weak_bear_to_bear=getattr(self.config.regime, 'map_weak_bear_to_bear', False))
            
            # Track regime detection
            timestamp = signals_dict.get('timestamp', datetime.now())
            analytics.track_regime(regime_state, mapped_regime, timestamp)
        
        # Call original method
        result = original_get_signals(self, regime_state, signals_dict, ohlcv_history)
        
        # Track any signals generated
        if result and hasattr(self, 'analytics') or system_mode in ['legacy', 'modern']:
            analytics = get_analytics(system_mode)
            timestamp = signals_dict.get('timestamp', datetime.now())
            
            for strategy_name, (direction, strategy_info) in result.items():
                if direction:
                    mapped_regime = map_gms_state(regime_state,
                                                map_weak_bear_to_bear=getattr(self.config.regime, 'map_weak_bear_to_bear', False))
                    analytics.track_signal(direction, regime_state, mapped_regime, timestamp, strategy_name)
        
        return result
    
    # Replace the method
    evaluator.StrategyEvaluator.get_signals_for_regime = enhanced_get_signals

def main_with_args(args):
    """Run main with prepared args"""
    import sys
    
    # Temporarily replace sys.argv
    original_argv = sys.argv[:]
    
    try:
        # Build new argv
        new_argv = ['run_backtest.py']
        
        if args.override:
            new_argv.extend(['--override', args.override])
        if args.system:
            new_argv.extend(['--system', args.system])
        if args.timeframe:
            new_argv.extend(['--timeframe', args.timeframe])
        if args.run_id:
            new_argv.extend(['--run-id', args.run_id])
        if args.skip_validation_warnings:
            new_argv.append('--skip-validation-warnings')
            
        sys.argv = new_argv
        
        # Import and run main
        from hyperliquid_bot.backtester.run_backtest import main
        main()
        
    finally:
        # Restore original argv
        sys.argv = original_argv

def run_comparison_analysis():
    """Run both systems and compare results"""
    
    print("🔍 STARTING COMPREHENSIVE TRADE DIRECTION COMPARISON")
    print("=" * 80)
    
    # Run legacy system
    print("\n1️⃣ Running LEGACY system backtest...")
    legacy_summary, legacy_file = run_backtest_with_analytics('legacy')
    
    if legacy_summary is None:
        print("❌ Legacy backtest failed, aborting comparison")
        return
    
    print(f"\n⏳ Waiting 2 seconds before modern system...")
    import time
    time.sleep(2)
    
    # Run modern system  
    print("\n2️⃣ Running MODERN system backtest...")
    modern_summary, modern_file = run_backtest_with_analytics('modern')
    
    if modern_summary is None:
        print("❌ Modern backtest failed, comparison incomplete")
        return
        
    # Compare results
    print(f"\n3️⃣ Performing comparison analysis...")
    comparison_result = compare_systems(legacy_summary, modern_summary)
    
    # Save comparison results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    comparison_file = f"/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/trade_comparison_{timestamp}.json"
    
    import json
    comparison_data = {
        'timestamp': datetime.now().isoformat(),
        'legacy_summary': legacy_summary,
        'modern_summary': modern_summary,
        'comparison_result': comparison_result,
        'legacy_file': legacy_file,
        'modern_file': modern_file
    }
    
    with open(comparison_file, 'w') as f:
        json.dump(comparison_data, f, indent=2, default=str)
    
    print(f"\n💾 Comparison results saved to: {comparison_file}")
    
    # Final recommendations
    print(f"\n🔧 RECOMMENDATIONS:")
    if comparison_result.get('issue_confirmed'):
        print(f"  1. ✅ Confirmed: Modern system has long-only issue")
        print(f"  2. 🔍 Check regime detection: Are Strong_Bear_Trend states being generated?")
        print(f"  3. 🔍 Check strategy logic: Is TF-v3 responding to BEAR mapped regimes?")
        print(f"  4. 🔍 Check state mapping: Verify 8-state to 3-state conversion")
    else:
        print(f"  1. ✅ Both systems have similar trade distributions")
        print(f"  2. 🔍 Issue may be in different time periods or configurations")
    
    return comparison_data

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "legacy":
            run_backtest_with_analytics('legacy')
        elif sys.argv[1] == "modern":
            run_backtest_with_analytics('modern') 
        elif sys.argv[1] == "compare":
            run_comparison_analysis()
        else:
            print("Usage: python enhanced_backtester.py [legacy|modern|compare]")
    else:
        # Default: run comparison
        run_comparison_analysis()