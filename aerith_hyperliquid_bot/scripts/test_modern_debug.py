#!/usr/bin/env python3
"""
Debug script to trace modern system execution for just 3 hours.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from datetime import datetime, timedelta
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
import time

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Silence noisy loggers
logging.getLogger('hyperliquid_bot.modern.adapters').setLevel(logging.WARNING)
logging.getLogger('ModernDataContract').setLevel(logging.WARNING)

def main():
    print("=== Modern System Debug (3-hour test) ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Test just 3 hours
    start_date = datetime(2024, 1, 1, 0, 0)
    end_date = datetime(2024, 1, 1, 3, 0)
    
    print(f"Period: {start_date} to {end_date}")
    print(f"Adaptive thresholds: {getattr(config.regime, 'gms_use_adaptive_thresholds', True)}")
    print(f"Risk per trade: {config.portfolio.risk_per_trade:.2%}\n")
    
    try:
        # Create engine
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        # Override to process hour by hour with timing
        hourly_timestamps = []
        current = start_date
        while current < end_date:
            hourly_timestamps.append(current)
            current += timedelta(hours=1)
        
        print(f"Processing {len(hourly_timestamps)} hours...\n")
        
        # Process each hour with timing
        for hour_idx, hour_start in enumerate(hourly_timestamps):
            hour_end = hour_start + timedelta(hours=1)
            
            print(f"\n--- Hour {hour_idx + 1}/{len(hourly_timestamps)}: {hour_start} ---")
            
            # Time regime updates
            t0 = time.time()
            regime_states = engine._simulate_hourly_regime_updates(hour_start, hour_end)
            t1 = time.time()
            print(f"  Regime updates: {len(regime_states)} states in {t1-t0:.1f}s")
            
            if regime_states:
                unique_states = set(s['state'] for s in regime_states)
                print(f"  States seen: {unique_states}")
            
            # Time trading evaluation
            if hour_idx > 0:
                t0 = time.time()
                trade_signal = engine._evaluate_trading_opportunity(hour_end)
                t1 = time.time()
                print(f"  Trade evaluation: {t1-t0:.1f}s")
                
                if trade_signal:
                    print(f"  🎯 TRADE SIGNAL: {trade_signal['direction']} with confidence {trade_signal.get('confidence', 0):.2f}")
                else:
                    print(f"  No trade signal")
        
        print(f"\n\nTotal trades generated: {len(engine.trades)}")
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()