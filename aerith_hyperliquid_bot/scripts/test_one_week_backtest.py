#!/usr/bin/env python3
"""
Test One Week Backtest
======================

Run a one-week backtest to verify the system works and generates trades.
"""

import sys
import logging
from pathlib import Path
from datetime import datetime
import json

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_backtest_engine import RobustBacktestEngine


def main():
    """Run one week backtest."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    print("\n" + "="*60)
    print("One Week Backtest Test")
    print("="*60)
    
    # Load configuration
    config = load_config("configs/overrides/modern_system_v2_complete.yaml")
    
    # Use legacy detector
    config.regime.detector_type = "granular_microstructure"
    
    # Test dates - one week in February 2024
    start_date = datetime(2024, 2, 1)
    end_date = datetime(2024, 2, 7, 23, 59, 59)
    
    print(f"\nBacktest period: {start_date} to {end_date}")
    
    # Create and run backtest
    engine = RobustBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=True
    )
    
    results = engine.run_backtest()
    
    # Save results
    output_file = "test_one_week_results.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # Display results
    print("\n" + "="*60)
    print("Results Summary")
    print("="*60)
    print(f"Total Trades: {results['total_trades']}")
    
    if results['total_trades'] > 0:
        print(f"Win Rate: {results['win_rate']:.2%}")
        print(f"Total Return: {results['total_return']:.2%}")
        print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
        print(f"Max Drawdown: {results['max_drawdown']:.2%}")
        
        # Show individual trades
        print("\nIndividual Trades:")
        for i, trade in enumerate(results['trades'][:5]):  # Show first 5
            print(f"\nTrade {i+1}:")
            print(f"  Direction: {trade['direction']}")
            print(f"  Entry: {trade['entry_time']} @ ${trade['entry_price']:.2f}")
            print(f"  Exit: {trade['exit_time']} @ ${trade['exit_price']:.2f}")
            print(f"  P&L: {trade['pnl_pct']:.2%}")
            print(f"  Reason: {trade.get('exit_reason', 'N/A')}")
    else:
        print("\nNo trades generated - checking why...")
        
        # Check data quality
        print(f"\nData Quality:")
        print(f"  Warmup hours used: {results['data_quality']['warmup_hours_used']}")
        print(f"  Regime cache available: {results['data_quality']['regime_cache_available']}")
    
    print(f"\nResults saved to: {output_file}")


if __name__ == "__main__":
    main()