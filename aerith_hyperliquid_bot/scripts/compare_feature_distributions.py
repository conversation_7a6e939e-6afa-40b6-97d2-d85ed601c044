# aerith_hyperliquid_bot/scripts/compare_feature_distributions.py

import argparse
import logging
import warnings
from pathlib import Path

import matplotlib.pyplot as plt
import pandas as pd
import pyarrow  # noqa: F401 - Required by pandas for parquet
import seaborn as sns
# import yaml # No longer needed

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
warnings.filterwarnings('ignore', category=FutureWarning) # Suppress seaborn/pandas future warnings

# Features to analyze (based on SignalCalculator and GMS usage)
FEATURES_TO_ANALYZE = [
    'obi_smoothed',
    'spread_mean',
    'spread_std',
    'roc',          # gms_roc_period
    'ma_slope',     # gms_ma_slope_period
    'atr_percent'   # gms_atr_percent_period
]

# YEARS_TO_COMPARE = [2023, 2024] # Years derived from input files
OUTPUT_DIR_NAME = "analysis_outputs"

# --- Helper Functions --- # No longer need load_config or load_data_for_year

# --- Main Analysis Function ---

def main(file_path_2023_str: str, file_path_2024_str: str):
    """Main function to perform feature distribution comparison."""
    file_path_2023 = Path(file_path_2023_str)
    file_path_2024 = Path(file_path_2024_str)

    # Load data for specified years directly
    all_data = {}
    try:
        logging.info(f"Loading 2023 data from: {file_path_2023}")
        df_2023 = pd.read_parquet(file_path_2023)
        df_2023['year'] = 2023
        all_data[2023] = df_2023
        logging.info(f"Loaded {len(df_2023)} rows for 2023.")
    except Exception as e:
        logging.error(f"Error loading 2023 data from {file_path_2023}: {e}")
        return

    try:
        logging.info(f"Loading 2024 data from: {file_path_2024}")
        df_2024 = pd.read_parquet(file_path_2024)
        df_2024['year'] = 2024
        all_data[2024] = df_2024
        logging.info(f"Loaded {len(df_2024)} rows for 2024.")
    except Exception as e:
        logging.error(f"Error loading 2024 data from {file_path_2024}: {e}")
        return

    # Combine dataframes
    combined_df = pd.concat(all_data.values(), ignore_index=False) # Keep original index if datetime
    logging.info(f"Combined data shape: {combined_df.shape}")
    logging.info(f"Columns found in combined data: {combined_df.columns.tolist()}") # Print columns

    # Create output directory
    output_dir = Path(OUTPUT_DIR_NAME)
    output_dir.mkdir(exist_ok=True)
    logging.info(f"Saving analysis outputs to: {output_dir.resolve()}")

    # Analyze each feature
    for feature in FEATURES_TO_ANALYZE:
        logging.info(f"\n--- Analyzing Feature: {feature} ---")

        if feature not in combined_df.columns:
            logging.warning(f"Feature '{feature}' not found in the combined data. Skipping.")
            continue

        # Drop NaNs for this specific feature for accurate stats/plotting
        # Ensure 'year' column exists before using it
        if 'year' not in combined_df.columns:
             logging.error("Critical error: 'year' column missing after data loading/concatenation.")
             return
        feature_data = combined_df[['year', feature]].dropna()


        if feature_data.empty:
            logging.warning(f"No valid data found for feature '{feature}' after dropping NaNs. Skipping.")
            continue

        # 1. Calculate and Print Descriptive Statistics
        try:
            stats = feature_data.groupby('year')[feature].describe()
            logging.info(f"Descriptive Statistics for {feature}:\n{stats.to_string()}")
        except Exception as e:
             logging.error(f"Error calculating statistics for {feature}: {e}")
             continue # Skip to next feature

        # 2. Generate and Save Comparison Plots
        plt.figure(figsize=(12, 6))

        # Histogram / KDE Plot
        try:
            sns.histplot(data=feature_data, x=feature, hue='year', kde=True, palette='viridis', common_norm=False, stat='density')
            plt.title(f'Distribution of {feature} (2023 vs 2024)')
            plot_filename_hist = output_dir / f"{feature}_distribution_hist.png"
            plt.savefig(plot_filename_hist)
            logging.info(f"Saved histogram plot: {plot_filename_hist}")
            plt.clf() # Clear figure for the next plot
        except Exception as e:
            logging.error(f"Error generating histogram for {feature}: {e}")
            plt.clf()

        # Box Plot
        try:
            # Ensure 'year' is treated as categorical for plotting if needed
            feature_data['year'] = feature_data['year'].astype('category')
            sns.boxplot(data=feature_data, x='year', y=feature, palette='viridis')
            plt.title(f'Box Plot of {feature} (2023 vs 2024)')
            plot_filename_box = output_dir / f"{feature}_distribution_box.png"
            plt.savefig(plot_filename_box)
            logging.info(f"Saved box plot: {plot_filename_box}")
            plt.clf() # Clear figure
        except Exception as e:
            logging.error(f"Error generating box plot for {feature}: {e}")
            plt.clf()

        plt.close('all') # Close all figures to free memory

    logging.info("\n--- Analysis Complete ---")

# --- Script Execution ---

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Compare microstructure feature distributions between years using saved signal files.")
    parser.add_argument(
        "file_2023",
        type=str,
        help="Path to the Parquet file containing signals for the 2023 run."
    )
    parser.add_argument(
        "file_2024",
        type=str,
        help="Path to the Parquet file containing signals for the 2024 run."
    )
    args = parser.parse_args()

    main(args.file_2023, args.file_2024)