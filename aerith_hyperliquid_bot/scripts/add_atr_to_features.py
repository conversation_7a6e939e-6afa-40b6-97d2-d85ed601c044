#!/usr/bin/env python
# scripts/add_atr_to_features.py

"""
<PERSON>rip<PERSON> to add ATR column to feature files.

This script reads feature files for a date range, calculates ATR, and adds it to the feature files.
"""

import os
import sys
import argparse
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import logging

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def calculate_atr(df, period=14):
    """
    Calculate ATR for a DataFrame.

    Args:
        df: DataFrame with 'high', 'low', 'close' columns
        period: ATR period

    Returns:
        Series with ATR values
    """
    # Make sure we have high, low, close columns
    required_cols = ['high', 'low', 'close']
    if not all(col in df.columns for col in required_cols):
        # Try to create them from mid price if we have best_bid and best_ask
        if 'best_bid' in df.columns and 'best_ask' in df.columns:
            df['mid'] = (df['best_bid'] + df['best_ask']) / 2
            df['high'] = df['mid']
            df['low'] = df['mid']
            df['close'] = df['mid']
        else:
            raise ValueError(f"DataFrame must have {required_cols} columns")

    # Calculate true range
    df = df.copy()
    df['prev_close'] = df['close'].shift(1)
    df['tr1'] = abs(df['high'] - df['low'])
    df['tr2'] = abs(df['high'] - df['prev_close'])
    df['tr3'] = abs(df['low'] - df['prev_close'])
    df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)

    # Calculate ATR
    df['atr_14_sec'] = df['true_range'].rolling(window=period).mean()

    # Calculate ATR percent (as decimal, not percentage)
    df['atr_percent_sec'] = df['atr_14_sec'] / df['close']

    return df[['atr_14_sec', 'atr_percent_sec']]

def process_day(date_str, feature_dir, overwrite=False):
    """
    Process feature files for a single day.

    Args:
        date_str: Date string in YYYY-MM-DD format
        feature_dir: Directory containing feature files
        overwrite: Whether to overwrite existing files
    """
    date = datetime.strptime(date_str, '%Y-%m-%d')
    date_dir = Path(feature_dir) / date_str

    if not date_dir.exists():
        logger.error(f"Directory not found: {date_dir}")
        return False

    # Get all feature files for the day
    feature_files = list(date_dir.glob('features_*.parquet'))
    if not feature_files:
        logger.error(f"No feature files found in {date_dir}")
        return False

    logger.info(f"Found {len(feature_files)} feature files for {date_str}")

    # Load all feature files for the day
    all_data = []
    for file in sorted(feature_files):
        try:
            df = pd.read_parquet(file)
            df['hour'] = int(file.stem.split('_')[1])
            all_data.append(df)
            logger.info(f"Loaded {len(df)} rows from {file}")
        except Exception as e:
            logger.error(f"Error loading {file}: {e}")
            continue

    if not all_data:
        logger.error(f"Failed to load any data for {date_str}")
        return False

    # Concatenate all data
    combined_df = pd.concat(all_data, ignore_index=True)
    logger.info(f"Combined {len(combined_df)} rows for {date_str}")

    # Check if ATR columns already exist
    if 'atr_14_sec' in combined_df.columns:
        logger.info("ATR columns already exist, dropping them before recalculation")
        combined_df = combined_df.drop(columns=['atr_14_sec', 'atr_percent_sec'], errors='ignore')

    # Calculate ATR
    atr_df = calculate_atr(combined_df)

    # Add ATR columns to combined_df
    for col in atr_df.columns:
        combined_df[col] = atr_df[col]

    # Add legacy ATR columns for backward compatibility
    combined_df['atr'] = combined_df['atr_14_sec']
    combined_df['atr_percent'] = combined_df['atr_percent_sec']

    # Verify ATR is not NaN after warmup
    nan_count = combined_df['atr_14_sec'].isna().sum()
    logger.info(f"NaN count in atr_14_sec: {nan_count}")

    # Split into hourly chunks and save
    for hour, hour_df in combined_df.groupby('hour'):
        hour_df = hour_df.drop(columns=['hour'])
        output_file = date_dir / f"features_{hour:02d}.parquet"

        if output_file.exists() and not overwrite:
            logger.warning(f"File {output_file} already exists. Use --overwrite to overwrite.")
            continue

        hour_df.to_parquet(str(output_file), compression='snappy')
        logger.info(f"Saved {len(hour_df)} rows to {output_file}")

    return True

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Add ATR column to feature files")

    parser.add_argument(
        "--feature-dir",
        type=str,
        default="hyperliquid_data/features_1s",
        help="Directory containing feature files"
    )

    parser.add_argument(
        "--start-date",
        type=str,
        default="2025-03-01",
        help="Start date in YYYY-MM-DD format"
    )

    parser.add_argument(
        "--end-date",
        type=str,
        default="2025-03-22",
        help="End date in YYYY-MM-DD format"
    )

    parser.add_argument(
        "--overwrite",
        action="store_true",
        help="Overwrite existing files"
    )

    args = parser.parse_args()

    # Parse dates
    start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
    end_date = datetime.strptime(args.end_date, '%Y-%m-%d')

    # Process each day
    current_date = start_date
    success_count = 0
    total_days = (end_date - start_date).days + 1

    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        logger.info(f"Processing {date_str} ({success_count+1}/{total_days})")

        if process_day(date_str, args.feature_dir, args.overwrite):
            success_count += 1

        current_date += timedelta(days=1)

    logger.info(f"Processed {success_count}/{total_days} days successfully")
    return 0 if success_count == total_days else 1

if __name__ == "__main__":
    sys.exit(main())
