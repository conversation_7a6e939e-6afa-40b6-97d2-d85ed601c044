#!/usr/bin/env python3
"""
Compare regime distributions between Legacy and Modern systems for 2024.
This will help diagnose why Modern is only taking short trades.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import Counter
import json

# Import both systems
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.legacy.detector import LegacyGranularMicrostructureDetector
from hyperliquid_bot.modern.enhanced_regime_detector import EnhancedRegimeDetector
from hyperliquid_bot.modern.robust_data_loader import Robust<PERSON>ataLoader

def analyze_legacy_regimes(config, start_date: datetime, end_date: datetime):
    """Run legacy detector and analyze regime distribution."""
    print("\n=== LEGACY SYSTEM REGIME ANALYSIS ===")
    
    # Create legacy detector
    detector = LegacyGranularMicrostructureDetector(config)
    
    # Load data using modern loader (it can load legacy format too)
    data_loader = RobustDataLoader(config)
    data = data_loader.load_data(start_date, end_date)
    
    if data.empty:
        print("ERROR: No data loaded")
        return None
    
    print(f"Loaded {len(data)} hours of data")
    
    # Track regimes
    regime_counts = Counter()
    regime_sequence = []
    
    # Process each hour
    for idx, (timestamp, row) in enumerate(data.iterrows()):
        if idx % 1000 == 0:
            print(f"Processing hour {idx}/{len(data)}...")
        
        # Prepare signals for legacy detector
        signals = {
            'atr_percent': row.get('atr_percent_sec', 0),
            'ma_slope': row.get('ma_slope', 0),
            'obi_smoothed_5': row.get('volume_imbalance', row.get('obi_smoothed_5', 0)),
            'spread_mean': row.get('spread_mean', 0),
            'spread_std': row.get('spread_std', 0),
            'volume': row.get('volume', 0)
        }
        
        # Detect regime
        regime = detector.detect_regime(signals, timestamp)
        confidence = detector.get_confidence()
        
        regime_counts[regime] += 1
        regime_sequence.append({
            'timestamp': timestamp,
            'regime': regime,
            'confidence': confidence,
            'ma_slope': signals['ma_slope'],
            'atr_percent': signals['atr_percent']
        })
    
    # Calculate percentages
    total = sum(regime_counts.values())
    regime_pcts = {regime: (count/total)*100 for regime, count in regime_counts.items()}
    
    print(f"\nLegacy Regime Distribution:")
    for regime, pct in sorted(regime_pcts.items(), key=lambda x: x[1], reverse=True):
        count = regime_counts[regime]
        print(f"  {regime:20s}: {count:6d} ({pct:5.1f}%)")
    
    # Group by trend type (note the underscore format)
    bull_regimes = ['Strong_Bull_Trend', 'Weak_Bull_Trend', 'STRONG_BULL_TREND', 'WEAK_BULL_TREND', 'Bull']
    bear_regimes = ['Strong_Bear_Trend', 'Weak_Bear_Trend', 'STRONG_BEAR_TREND', 'WEAK_BEAR_TREND', 'Bear']
    neutral_regimes = ['CHOP', 'Neutral', 'Unknown', 'Uncertain', 'High_Vol_Range', 'Low_Vol_Range']
    
    bull_pct = sum(regime_pcts.get(r, 0) for r in bull_regimes)
    bear_pct = sum(regime_pcts.get(r, 0) for r in bear_regimes)
    neutral_pct = sum(regime_pcts.get(r, 0) for r in neutral_regimes)
    
    print(f"\nLegacy Trend Summary:")
    print(f"  BULLISH: {bull_pct:5.1f}%")
    print(f"  BEARISH: {bear_pct:5.1f}%")
    print(f"  NEUTRAL: {neutral_pct:5.1f}%")
    
    return {
        'regime_counts': dict(regime_counts),
        'regime_pcts': regime_pcts,
        'trend_summary': {
            'bull': bull_pct,
            'bear': bear_pct,
            'neutral': neutral_pct
        },
        'sequence': regime_sequence
    }

def analyze_modern_regimes(config, start_date: datetime, end_date: datetime):
    """Run modern/enhanced detector and analyze regime distribution."""
    print("\n=== MODERN/ENHANCED SYSTEM REGIME ANALYSIS ===")
    
    # Create enhanced detector
    detector = EnhancedRegimeDetector(config)
    
    # Load data
    data_loader = RobustDataLoader(config)
    data = data_loader.load_data(start_date, end_date)
    
    if data.empty:
        print("ERROR: No data loaded")
        return None
    
    print(f"Loaded {len(data)} hours of data")
    
    # Track regimes
    regime_counts = Counter()
    regime_sequence = []
    quality_scores = []
    
    # Process each hour
    for idx, (timestamp, row) in enumerate(data.iterrows()):
        if idx % 1000 == 0:
            print(f"Processing hour {idx}/{len(data)}...")
        
        # Prepare signals
        signals = {
            'atr_percent': row.get('atr_percent_sec', 0),
            'ma_slope': row.get('ma_slope', 0),
            'obi_smoothed_5': row.get('volume_imbalance', 0),
            'spread_mean': row.get('spread_mean', 0),
            'spread_std': row.get('spread_std', 0),
            'volume': row.get('volume', 0),
            'close': row.get('close', 0),
            'atr_percent_sec': row.get('atr_percent_sec', 0),
            'volume_imbalance': row.get('volume_imbalance', 0)
        }
        
        # Detect regime
        regime = detector.detect_regime(signals, timestamp)
        confidence = detector.get_confidence()
        
        # Also get quality evaluation
        eval_result = detector.evaluate_with_quality(signals, None, timestamp)
        quality_score = eval_result['quality']
        quality_scores.append(quality_score)
        
        regime_counts[regime] += 1
        regime_sequence.append({
            'timestamp': timestamp,
            'regime': regime,
            'confidence': confidence,
            'quality': quality_score,
            'ma_slope': signals['ma_slope'],
            'atr_percent': signals['atr_percent']
        })
    
    # Calculate percentages
    total = sum(regime_counts.values())
    regime_pcts = {regime: (count/total)*100 for regime, count in regime_counts.items()}
    
    print(f"\nModern/Enhanced Regime Distribution:")
    for regime, pct in sorted(regime_pcts.items(), key=lambda x: x[1], reverse=True):
        count = regime_counts[regime]
        print(f"  {regime:20s}: {count:6d} ({pct:5.1f}%)")
    
    # Group by trend type (note the underscore format)
    bull_regimes = ['Strong_Bull_Trend', 'Weak_Bull_Trend', 'STRONG_BULL_TREND', 'WEAK_BULL_TREND', 'Bull']
    bear_regimes = ['Strong_Bear_Trend', 'Weak_Bear_Trend', 'STRONG_BEAR_TREND', 'WEAK_BEAR_TREND', 'Bear']
    neutral_regimes = ['CHOP', 'Neutral', 'Unknown', 'Uncertain', 'High_Vol_Range', 'Low_Vol_Range']
    
    bull_pct = sum(regime_pcts.get(r, 0) for r in bull_regimes)
    bear_pct = sum(regime_pcts.get(r, 0) for r in bear_regimes)
    neutral_pct = sum(regime_pcts.get(r, 0) for r in neutral_regimes)
    
    print(f"\nModern/Enhanced Trend Summary:")
    print(f"  BULLISH: {bull_pct:5.1f}%")
    print(f"  BEARISH: {bear_pct:5.1f}%") 
    print(f"  NEUTRAL: {neutral_pct:5.1f}%")
    
    # Quality score analysis
    avg_quality = np.mean(quality_scores)
    print(f"\nQuality Score Stats:")
    print(f"  Average: {avg_quality:.3f}")
    print(f"  Min: {min(quality_scores):.3f}")
    print(f"  Max: {max(quality_scores):.3f}")
    print(f"  % Above 0.7 threshold: {sum(1 for q in quality_scores if q >= 0.7) / len(quality_scores) * 100:.1f}%")
    
    return {
        'regime_counts': dict(regime_counts),
        'regime_pcts': regime_pcts,
        'trend_summary': {
            'bull': bull_pct,
            'bear': bear_pct,
            'neutral': neutral_pct
        },
        'sequence': regime_sequence,
        'quality_stats': {
            'avg': avg_quality,
            'min': min(quality_scores),
            'max': max(quality_scores),
            'above_threshold_pct': sum(1 for q in quality_scores if q >= 0.7) / len(quality_scores) * 100
        }
    }

def compare_results(legacy_results, modern_results):
    """Compare and highlight differences."""
    print("\n=== COMPARISON SUMMARY ===")
    
    # Trend comparison
    print("\nTrend Distribution Comparison:")
    print(f"{'Type':10s} {'Legacy':>10s} {'Modern':>10s} {'Diff':>10s}")
    print("-" * 42)
    
    for trend in ['bull', 'bear', 'neutral']:
        legacy_pct = legacy_results['trend_summary'][trend]
        modern_pct = modern_results['trend_summary'][trend]
        diff = modern_pct - legacy_pct
        print(f"{trend.upper():10s} {legacy_pct:9.1f}% {modern_pct:9.1f}% {diff:+9.1f}%")
    
    # Find regime mapping issues
    print("\nRegime-by-Regime Comparison:")
    all_regimes = set(legacy_results['regime_counts'].keys()) | set(modern_results['regime_counts'].keys())
    
    for regime in sorted(all_regimes):
        legacy_pct = legacy_results['regime_pcts'].get(regime, 0)
        modern_pct = modern_results['regime_pcts'].get(regime, 0)
        diff = modern_pct - legacy_pct
        if abs(diff) > 5:  # Only show significant differences
            print(f"  {regime:20s}: Legacy {legacy_pct:5.1f}% vs Modern {modern_pct:5.1f}% (diff: {diff:+5.1f}%)")
    
    # Check for potential inversions
    print("\nPotential Issues Detected:")
    if modern_results['trend_summary']['bear'] > 50 and legacy_results['trend_summary']['bull'] > 40:
        print("  ⚠️  CRITICAL: Modern system appears to be detecting BEAR regimes where Legacy detects BULL!")
        print("     This explains why all trades are shorts in a bull market.")
    
    # Sample some specific hours to see the difference
    print("\nSample Hour Comparisons (first 5 divergences):")
    divergences = 0
    for i in range(min(len(legacy_results['sequence']), len(modern_results['sequence']))):
        legacy = legacy_results['sequence'][i]
        modern = modern_results['sequence'][i]
        
        # Check if regimes differ significantly
        if legacy['regime'] != modern['regime']:
            # Check if it's a bull/bear inversion
            legacy_is_bull = any(x in legacy['regime'] for x in ['BULL', 'Bull'])
            legacy_is_bear = any(x in legacy['regime'] for x in ['BEAR', 'Bear'])
            modern_is_bull = any(x in modern['regime'] for x in ['BULL', 'Bull'])
            modern_is_bear = any(x in modern['regime'] for x in ['BEAR', 'Bear'])
            
            if (legacy_is_bull and modern_is_bear) or (legacy_is_bear and modern_is_bull):
                divergences += 1
                if divergences <= 5:
                    print(f"\n  Time: {legacy['timestamp']}")
                    print(f"    Legacy: {legacy['regime']} (conf: {legacy['confidence']:.2f})")
                    print(f"    Modern: {modern['regime']} (conf: {modern['confidence']:.2f})")
                    print(f"    MA Slope: {legacy['ma_slope']:.6f}")
                    print(f"    ATR %: {legacy['atr_percent']:.6f}")

def main():
    """Run the comparison."""
    # Load config
    config_path = Path(__file__).parent.parent / "configs/overrides/modern_system_v2_complete.yaml"
    config = load_config(str(config_path))
    
    # Set date range for 2024
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 12, 31, 23, 59, 59)
    
    print(f"Comparing regime distributions for {start_date.date()} to {end_date.date()}")
    
    # Run legacy analysis
    legacy_results = analyze_legacy_regimes(config, start_date, end_date)
    if not legacy_results:
        print("Failed to analyze legacy system")
        return
    
    # Run modern analysis
    modern_results = analyze_modern_regimes(config, start_date, end_date)
    if not modern_results:
        print("Failed to analyze modern system")
        return
    
    # Compare results
    compare_results(legacy_results, modern_results)
    
    # Save detailed results
    results = {
        'period': {
            'start': start_date.isoformat(),
            'end': end_date.isoformat()
        },
        'legacy': legacy_results,
        'modern': modern_results
    }
    
    output_file = Path(__file__).parent.parent / "regime_distribution_comparison_2024.json"
    with open(output_file, 'w') as f:
        # Don't save full sequences - too large
        results['legacy'].pop('sequence', None)
        results['modern'].pop('sequence', None)
        json.dump(results, f, indent=2)
    
    print(f"\nDetailed results saved to: {output_file}")

if __name__ == "__main__":
    main()