#!/usr/bin/env python3
"""
<PERSON>ript to run backtest with execution refinement enabled.
This is a convenience wrapper around run_backtest.py with the execution refinement override.
"""

import subprocess
import sys
from pathlib import Path
import argparse

def main():
    """Run backtest with execution refinement override."""
    # Get the project root
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # Path to the main backtest script
    backtest_script = project_root / "hyperliquid_bot" / "backtester" / "run_backtest.py"
    
    # Path to the execution refinement override
    override_path = project_root / "configs" / "overrides" / "execution_refinement_enabled.yaml"
    
    # Parse additional arguments
    parser = argparse.ArgumentParser(description="Run backtest with execution refinement enabled")
    parser.add_argument('--timeframe', type=str, default=None, choices=['1h', '4h'],
                        help='Timeframe override (e.g., 1h, 4h)')
    parser.add_argument('--run-id', type=str, default="execution_refinement",
                        help='Unique identifier for this backtest run')
    parser.add_argument('--skip-validation-warnings', action='store_true',
                        help='Skip warnings about validation issues')
    
    args = parser.parse_args()
    
    # Build the command
    cmd = [
        sys.executable,
        str(backtest_script),
        "--override", str(override_path),
        "--run-id", args.run_id
    ]
    
    # Add optional arguments
    if args.timeframe:
        cmd.extend(["--timeframe", args.timeframe])
    
    if args.skip_validation_warnings:
        cmd.append("--skip-validation-warnings")
    
    print(f"Running backtest with execution refinement enabled...")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 80)
    
    # Run the backtest
    try:
        result = subprocess.run(cmd, check=True)
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"Backtest failed with error code: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print("\nBacktest interrupted by user")
        return 1

if __name__ == "__main__":
    sys.exit(main())