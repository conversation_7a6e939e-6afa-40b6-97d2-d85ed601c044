#!/usr/bin/env python3
"""
Test script for UnifiedGMSDetector implementation.
Verifies both legacy and continuous modes work correctly.
"""

import sys
import os
import logging
import numpy as np
import pandas as pd
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.detector_factory import get_regime_detector
from hyperliquid_bot.core.unified_gms_detector import UnifiedGMSDetector


def create_mock_config(detector_type: str = 'granular_microstructure'):
    """Create a mock configuration for testing."""
    class MockConfig:
        def __init__(self):
            # Regime settings
            self.regime = type('obj', (object,), {
                'detector_type': detector_type,
                'use_filter': True,
                'gms_vol_high_thresh': 0.92 if detector_type == 'granular_microstructure' else 0.03,
                'gms_vol_low_thresh': 0.55 if detector_type == 'granular_microstructure' else 0.01,
                'gms_mom_strong_thresh': 100.0 if detector_type == 'granular_microstructure' else 2.5,
                'gms_mom_weak_thresh': 50.0 if detector_type == 'granular_microstructure' else 0.5,
                'gms_spread_std_high_thresh': 0.000050 if detector_type == 'granular_microstructure' else 0.0005,
                'gms_spread_mean_low_thresh': 0.000045 if detector_type == 'granular_microstructure' else 0.0001,
                'gms_use_adx_confirmation': False,
                'gms_use_funding_confirmation': False
            })()
            
            # Microstructure settings
            self.microstructure = type('obj', (object,), {
                'depth_levels': 5,
                'gms_obi_strong_confirm_thresh': 0.20,
                'gms_obi_weak_confirm_thresh': 0.11
            })()
            
            # GMS settings (optional)
            self.gms = type('obj', (object,), {
                'detector_type': detector_type,
                'cadence_sec': 3600 if detector_type == 'granular_microstructure' else 60,
                'output_states': 8,
                'auto_thresholds': False,
                'risk_suppressed_notional_frac': 0.25,
                'risk_suppressed_pnl_atr_mult': 1.5
            })()
    
    return MockConfig()


def create_test_signals(mode: str = 'legacy'):
    """Create test signals for detector testing."""
    if mode == 'legacy':
        return {
            'timestamp': 1640995200,
            'atr_percent': 0.02,  # Legacy uses atr_percent
            'ma_slope': 75.0,
            'obi_smoothed_5': 0.15,
            'spread_mean': 0.0001,
            'spread_std': 0.00005,
            'close': 50000.0
        }
    else:
        return {
            'timestamp': 1640995200,
            'atr_percent': 0.02,  # Both modes now use atr_percent
            'ma_slope': 1.5,
            'ma_slope_ema_30s': 1.8,
            'obi_smoothed_5': 0.15,
            'spread_mean': 0.0001,
            'spread_std': 0.00005,
            'close': 50000.0,
            'position_size_usd': 1000,
            'realized_pnl_usd': 50
        }


def test_legacy_mode():
    """Test UnifiedGMSDetector in legacy mode."""
    print("\n=== Testing Legacy Mode ===")
    
    # Create config for legacy mode
    config = create_mock_config('granular_microstructure')
    
    # Test factory function
    detector = get_regime_detector(config)
    print(f"Factory created detector type: {type(detector).__name__}")
    assert isinstance(detector, UnifiedGMSDetector), "Factory should create UnifiedGMSDetector"
    
    # Check mode settings
    print(f"Detector mode: {detector.detector_mode}")
    print(f"Detector type: {detector.detector_type}")
    print(f"Cadence: {detector.cadence_sec}s")
    print(f"Output format: {detector.output_format}")
    
    assert detector.detector_mode == 'legacy', "Should be in legacy mode"
    assert detector.cadence_sec == 3600, "Legacy mode should use 3600s cadence"
    assert detector.output_format == 'string', "Legacy mode should output string"
    
    # Test required signals
    required = detector.required_signals
    print(f"Required signals: {required}")
    assert 'atr_percent' in required, "Legacy mode should require atr_percent"
    
    # Test regime detection
    signals = create_test_signals('legacy')
    regime = detector.get_regime(signals)
    print(f"Detected regime: {regime}")
    assert isinstance(regime, str), "Legacy mode should return string"
    assert regime != "Unknown", "Should detect valid regime"
    
    print("✓ Legacy mode tests passed!")


def test_continuous_mode():
    """Test UnifiedGMSDetector in continuous mode."""
    print("\n=== Testing Continuous Mode ===")
    
    # Create config for continuous mode
    config = create_mock_config('continuous_gms')
    
    # Test factory function
    detector = get_regime_detector(config)
    print(f"Factory created detector type: {type(detector).__name__}")
    assert isinstance(detector, UnifiedGMSDetector), "Factory should create UnifiedGMSDetector"
    
    # Check mode settings
    print(f"Detector mode: {detector.detector_mode}")
    print(f"Detector type: {detector.detector_type}")
    print(f"Cadence: {detector.cadence_sec}s")
    print(f"Output format: {detector.output_format}")
    
    assert detector.detector_mode == 'continuous', "Should be in continuous mode"
    assert detector.cadence_sec == 60, "Continuous mode should use 60s cadence"
    assert detector.output_format == 'dict', "Continuous mode should output dict"
    
    # Test required signals
    required = detector.required_signals
    print(f"Required signals: {required}")
    assert 'atr_percent' in required, "Continuous mode should require atr_percent"
    
    # Test regime detection
    signals = create_test_signals('continuous')
    regime = detector.get_regime(signals)
    print(f"Detected regime: {regime}")
    assert isinstance(regime, dict), "Continuous mode should return dict"
    assert 'state' in regime, "Dict should have 'state' key"
    assert 'risk_suppressed' in regime, "Dict should have 'risk_suppressed' key"
    assert regime['state'] != "Unknown", "Should detect valid regime"
    
    print("✓ Continuous mode tests passed!")


def test_threshold_resolution():
    """Test threshold resolution for both modes."""
    print("\n=== Testing Threshold Resolution ===")
    
    # Test legacy thresholds
    config = create_mock_config('granular_microstructure')
    detector = UnifiedGMSDetector(config)
    print(f"Legacy thresholds: {detector.thresholds}")
    assert detector.thresholds['vol_high'] == 0.92, "Legacy vol_high should be 0.92"
    assert detector.thresholds['mom_strong'] == 100.0, "Legacy mom_strong should be 100.0"
    
    # Test continuous thresholds
    config = create_mock_config('continuous_gms')
    detector = UnifiedGMSDetector(config)
    print(f"Continuous thresholds: {detector.thresholds}")
    assert detector.thresholds['vol_high'] == 0.03, "Continuous vol_high should be 0.03"
    assert detector.thresholds['mom_strong'] == 2.5, "Continuous mom_strong should be 2.5"
    
    print("✓ Threshold resolution tests passed!")


def test_backward_compatibility():
    """Test backward compatibility with various config formats."""
    print("\n=== Testing Backward Compatibility ===")
    
    # Test 1: Config with detector_type in regime section only
    class Config1:
        def __init__(self):
            self.regime = type('obj', (object,), {
                'detector_type': 'granular_microstructure',
                'use_filter': True,
                'gms_vol_high_thresh': 0.92,
                'gms_vol_low_thresh': 0.55,
                'gms_mom_strong_thresh': 100.0,
                'gms_mom_weak_thresh': 50.0,
                'gms_spread_std_high_thresh': 0.000050,
                'gms_spread_mean_low_thresh': 0.000045
            })()
            self.microstructure = type('obj', (object,), {
                'depth_levels': 5,
                'gms_obi_strong_confirm_thresh': 0.20,
                'gms_obi_weak_confirm_thresh': 0.11
            })()
    
    detector = UnifiedGMSDetector(Config1())
    assert detector.detector_mode == 'legacy', "Should resolve to legacy mode"
    print("✓ Config format 1 (regime.detector_type) works")
    
    # Test 2: Config with detector_type in gms section
    class Config2:
        def __init__(self):
            self.regime = type('obj', (object,), {
                'use_filter': True,
                'gms_vol_high_thresh': 0.03,
                'gms_vol_low_thresh': 0.01,
                'gms_mom_strong_thresh': 2.5,
                'gms_mom_weak_thresh': 0.5,
                'gms_spread_std_high_thresh': 0.0005,
                'gms_spread_mean_low_thresh': 0.0001
            })()
            self.microstructure = type('obj', (object,), {
                'depth_levels': 5,
                'gms_obi_strong_confirm_thresh': 0.20,
                'gms_obi_weak_confirm_thresh': 0.11
            })()
            self.gms = type('obj', (object,), {
                'detector_type': 'continuous_gms',
                'cadence_sec': 60
            })()
    
    detector = UnifiedGMSDetector(Config2())
    assert detector.detector_mode == 'continuous', "Should resolve to continuous mode"
    print("✓ Config format 2 (gms.detector_type) works")
    
    print("✓ All backward compatibility tests passed!")


def main():
    """Run all tests."""
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("Testing UnifiedGMSDetector Implementation")
    print("=" * 50)
    
    try:
        test_legacy_mode()
        test_continuous_mode()
        test_threshold_resolution()
        test_backward_compatibility()
        
        print("\n" + "=" * 50)
        print("✅ All tests passed! UnifiedGMSDetector is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()