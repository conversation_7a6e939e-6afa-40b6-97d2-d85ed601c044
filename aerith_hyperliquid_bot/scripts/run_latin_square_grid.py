#!/usr/bin/env python3
"""
Latin Square Parameter Grid Search for Hyperliquid Bot

This script implements a 2x2 Latin-square design for parameter tuning,
generating temporary override files with different parameter combinations.

Usage:
    python scripts/run_latin_square_grid.py
"""
import os
import sys
import subprocess
import yaml
import csv
from pathlib import Path
import datetime
import time
import re
import pandas as pd

# Define the project root
PROJECT_ROOT = Path(__file__).parent.parent.resolve()

# Parameter grid for Latin-square design
PARAM_GRID = {
    "obi_smoothing_window": [8, 14],  # Low, High
    "gms_obi_strong_confirm_thresh": [0.15, 0.22],  # Low, High
    "gms_spread_std_high_thresh": [0.000026, 0.000044],  # Low, High
    "gms_confirmation_bars": [1, 2]  # Low, High
}

# No longer needed as we construct the dictionary directly
# OVERRIDE_TEMPLATE = """..."""

def create_override_file(params, run_id):
    """Create a temporary override file with the given parameters using correct structure."""
    # Construct the override dictionary with the correct structure
    override_data = {
        "microstructure": {
            "obi_smoothing_window": params["obi_smoothing_window"],
            "gms_obi_strong_confirm_thresh": params["gms_obi_strong_confirm_thresh"],
            # Assuming weak threshold should mirror strong threshold based on original template logic
            "gms_obi_weak_confirm_thresh": params["gms_obi_strong_confirm_thresh"]
        },
        "regime": {
            "gms_spread_std_high_thresh": params["gms_spread_std_high_thresh"],
            "gms_confirmation_bars": params["gms_confirmation_bars"] # Corrected key name
        }
    }
    
    # Create a unique filename
    override_dir = PROJECT_ROOT / "configs" / "overrides" / "grid_search"
    override_dir.mkdir(exist_ok=True, parents=True)
    
    override_path = override_dir / f"run_{run_id}.yaml"
    
    # Write the override config dictionary to the YAML file
    with open(override_path, 'w') as f:
        yaml.dump(override_data, f, default_flow_style=False, sort_keys=False)
    
    return override_path

def find_most_recent_files(log_dir, pattern, before_time=None):
    """Find the most recent files matching the pattern before a certain time."""
    matching_files = list(log_dir.glob(pattern))
    
    if not matching_files:
        return None
    
    # Filter files by modification time if before_time is provided
    if before_time:
        matching_files = [f for f in matching_files if os.path.getmtime(f) < before_time]
    
    if not matching_files:
        return None
    
    # Return the most recent file
    return max(matching_files, key=os.path.getmtime)

def extract_metrics_from_trades_file(log_dir, run_id: str):
    """Extract metrics from the log file corresponding to the given run_id."""
    # Sanitize run_id for filename matching (should match sanitization in run_backtest.py)
    safe_run_id = "".join(c if c.isalnum() or c in ('-', '_') else '_' for c in run_id)
    
    # Look for the specific log file matching the run_id pattern
    log_pattern = f"backtest_run_{safe_run_id}_*.log"
    matching_logs = list(log_dir.glob(log_pattern))

    if not matching_logs:
        print(f"Warning: No log file found matching pattern '{log_pattern}' for run_id '{run_id}'")
        return None, None, None, None
    
    # If multiple match (e.g., rerun), take the most recent one
    log_file = max(matching_logs, key=os.path.getmtime)
    print(f"Found log file for run {run_id}: {log_file}")
    
    try:
        # Extract metrics from log file
        with open(log_file, 'r') as f:
            log_content = f.read()
        
        # The log file contains ANSI escape codes for formatting, so we need to use more generic patterns
        
        # Look for Sharpe ratio using a generic pattern that works with ANSI codes
        sharpe_match = re.search(r'Sharpe Ratio.*?(\d+\.\d+)', log_content)
        if sharpe_match:
            sharpe = float(sharpe_match.group(1))
            print(f"Found Sharpe ratio: {sharpe}")
        else:
            sharpe = None
        
        # Look for Max Drawdown using a generic pattern that works with ANSI codes
        dd_match = re.search(r'Max Drawdown.*?(\d+\.\d+)%', log_content)
        if dd_match:
            max_dd = float(dd_match.group(1)) / -100.0  # Convert percentage to decimal and make negative
            print(f"Found Max Drawdown: {max_dd}")
        else:
            max_dd = None
        
        # Look for ROI using a generic pattern that works with ANSI codes
        roi_match = re.search(r'Return on Initial \(ROI\).*?(\d+\.\d+)%', log_content)
        if roi_match:
            roi = float(roi_match.group(1)) / 100.0  # Convert percentage to decimal
            print(f"Found ROI: {roi}")
        else:
            roi = None
        
        # Look for Profit Factor using a generic pattern that works with ANSI codes
        pf_match = re.search(r'Profit Factor.*?(\d+\.\d+)', log_content)
        if pf_match:
            profit_factor = float(pf_match.group(1))
            print(f"Found Profit Factor: {profit_factor}")
        else:
            profit_factor = None
        
        # If we still don't have metrics, use placeholder values
        if sharpe is None:
            sharpe = 0.0
            print("Warning: Could not extract Sharpe ratio, using 0.0")
        
        if max_dd is None:
            max_dd = -0.1  # Default 10% drawdown
            print("Warning: Could not extract Max Drawdown, using -0.1 (10%)")
        
        if roi is None:
            roi = 0.0
            print("Warning: Could not extract ROI, using 0.0")
        
        if profit_factor is None:
            profit_factor = 1.0
            print("Warning: Could not extract Profit Factor, using 1.0")
        
        return sharpe, max_dd, roi, profit_factor
    
    except Exception as e:
        print(f"Error extracting metrics from log file: {e}")
        return 0.0, -0.1, 0.0, 1.0  # Default values

def run_grid_search():
    """Run the Latin-square grid search."""
    # Create results directory
    results_dir = PROJECT_ROOT / "grid_search_results"
    results_dir.mkdir(exist_ok=True)
    
    # Timestamp for this run
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create CSV file for results
    csv_path = results_dir / f"grid_results_{timestamp}.csv"
    with open(csv_path, 'w', newline='') as csvfile:
        fieldnames = ['RunID', 'OBI_WIN', 'OBI_THR', 'SPD_THR', 'CONF_BARS', 'Sharpe', 'MaxDD', 'ROI', 'ProfitFactor', 'Sharpe_DD_Ratio']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
    
    # Generate all parameter combinations (16 total runs)
    total_runs = len(PARAM_GRID["obi_smoothing_window"]) * \
                 len(PARAM_GRID["gms_obi_strong_confirm_thresh"]) * \
                 len(PARAM_GRID["gms_spread_std_high_thresh"]) * \
                 len(PARAM_GRID["gms_confirmation_bars"])
    
    print(f"Starting 2x2 Latin-square grid search with {total_runs} total runs")
    print(f"Results will be saved to {csv_path}")
    
    # Log directory from base config
    log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    
    run_count = 0
    for obi_win in PARAM_GRID["obi_smoothing_window"]:
        for obi_thr in PARAM_GRID["gms_obi_strong_confirm_thresh"]:
            for spd_thr in PARAM_GRID["gms_spread_std_high_thresh"]:
                for conf_bars in PARAM_GRID["gms_confirmation_bars"]:
                    run_count += 1
                    run_id = f"{run_count:02d}"
                    
                    # Create parameter set
                    params = {
                        "obi_smoothing_window": obi_win,
                        "gms_obi_strong_confirm_thresh": obi_thr,
                        "gms_spread_std_high_thresh": spd_thr,
                        "gms_confirmation_bars": conf_bars
                    }
                    
                    print(f"\nRun {run_id}/{total_runs}")
                    print(f"Parameters: OBI_WIN={obi_win}, OBI_THR={obi_thr}, SPD_THR={spd_thr}, CONF_BARS={conf_bars}")
                    
                    # Create override file for this run
                    override_path = create_override_file(params, run_id)
                    print(f"Created override file: {override_path}")
                    
                    sharpe, max_dd, roi, profit_factor, sharpe_dd_ratio = None, None, None, None, None # Initialize metrics
                    run_status = "Success" # Track run status

                    try:
                        # Record start time
                        run_start_time = time.time()

                        # Run the backtester with the override
                        backtest_script = PROJECT_ROOT / "hyperliquid_bot" / "backtester" / "run_backtest.py"
                        cmd = [
                            sys.executable,  # Current Python interpreter
                            str(backtest_script),
                            "--override", str(override_path),
                            "--run-id", run_id  # Pass the unique run_id
                        ]

                        print(f"Running command: {' '.join(cmd)}")
                        # Use check=False initially to handle errors manually, add timeout
                        process = subprocess.run(cmd, capture_output=True, text=True, timeout=1800) # e.g., 30 min timeout

                        # Record end time
                        run_end_time = time.time()

                        # Check if the process failed
                        if process.returncode != 0:
                            run_status = f"Failed (Code {process.returncode})"
                            print(f"Error running backtest (return code {process.returncode}) for run {run_id}")
                            # Log truncated output to avoid excessive printing
                            print(f"STDOUT (last 500 chars): ...{process.stdout[-500:]}")
                            print(f"STDERR (last 500 chars): ...{process.stderr[-500:]}")
                            # Assign error placeholders for metrics
                            sharpe, max_dd, roi, profit_factor = 'ERROR', 'ERROR', 'ERROR', 'ERROR'
                            sharpe_dd_ratio = 'ERROR'
                        else:
                            # Extract metrics only if successful run
                            print(f"Backtest run {run_id} completed successfully. Extracting metrics...")
                            sharpe, max_dd, roi, profit_factor = extract_metrics_from_trades_file(log_dir, run_id)

                            # Calculate Sharpe/DD ratio only if metrics are valid numbers
                            if isinstance(sharpe, (int, float)) and isinstance(max_dd, (int, float)) and max_dd != 0:
                                sharpe_dd_ratio = sharpe / abs(max_dd)
                            elif sharpe is None or max_dd is None or roi is None or profit_factor is None: # Handle extraction failures explicitly
                                sharpe_dd_ratio = 'EXTRACT_FAIL'
                                sharpe = sharpe if sharpe is not None else 'EXTRACT_FAIL'
                                max_dd = max_dd if max_dd is not None else 'EXTRACT_FAIL'
                                roi = roi if roi is not None else 'EXTRACT_FAIL'
                                profit_factor = profit_factor if profit_factor is not None else 'EXTRACT_FAIL'
                                run_status = "Metric Extraction Failed"
                                print(f"Warning: Metric extraction failed for run {run_id}. Check log parsing.")
                            elif max_dd == 0: # Handle max_dd == 0 case
                                sharpe_dd_ratio = 'INF' if sharpe > 0 else ('-INF' if sharpe < 0 else 0.0) # Assign INF/-INF or 0
                            else: # Should not happen if types are correct, but catch anyway
                                sharpe_dd_ratio = 'CALC_ERROR'
                                run_status = "Ratio Calculation Error"


                    except subprocess.TimeoutExpired:
                        run_status = "Timeout"
                        print(f"Backtest run {run_id} timed out after 1800 seconds.")
                        sharpe, max_dd, roi, profit_factor = 'TIMEOUT', 'TIMEOUT', 'TIMEOUT', 'TIMEOUT'
                        sharpe_dd_ratio = 'TIMEOUT'
                    except Exception as e:
                        run_status = f"Unexpected Error: {type(e).__name__}"
                        print(f"Unexpected error during run {run_id}: {e}")
                        # Assign error placeholders for metrics
                        sharpe, max_dd, roi, profit_factor = 'UNEXPECTED_ERR', 'UNEXPECTED_ERR', 'UNEXPECTED_ERR', 'UNEXPECTED_ERR'
                        sharpe_dd_ratio = 'UNEXPECTED_ERR'

                    # Always write to CSV, even if the run failed
                    # Consider adding 'RunStatus' to fieldnames if you want that column
                    # fieldnames = ['RunID', 'OBI_WIN', 'OBI_THR', 'SPD_THR', 'CONF_BARS', 'Sharpe', 'MaxDD', 'ROI', 'ProfitFactor', 'Sharpe_DD_Ratio', 'RunStatus']
                    with open(csv_path, 'a', newline='') as csvfile:
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames) # Ensure fieldnames match header written earlier
                        writer.writerow({
                            'RunID': run_id,
                            'OBI_WIN': obi_win,
                            'OBI_THR': obi_thr,
                            'SPD_THR': spd_thr,
                            'CONF_BARS': conf_bars,
                            'Sharpe': sharpe,
                            'MaxDD': max_dd,
                            'ROI': roi,
                            'ProfitFactor': profit_factor,
                            'Sharpe_DD_Ratio': sharpe_dd_ratio
                            # 'RunStatus': run_status # Uncomment if adding RunStatus column
                        })
    
    print(f"\nGrid search complete. Full results available in {csv_path}")
    
    # Sort results by Sharpe/DD ratio and display top combinations
    try:
        df = pd.read_csv(csv_path)
        if not df.empty and 'Sharpe_DD_Ratio' in df.columns:
            # Sort by Sharpe/DD ratio
            df_sorted = df.sort_values('Sharpe_DD_Ratio', ascending=False)
            
            # Save sorted results
            sorted_csv_path = results_dir / f"grid_results_sorted_{timestamp}.csv"
            df_sorted.to_csv(sorted_csv_path, index=False)
            
            # Display top 5
            print("\nTop 5 parameter combinations by Sharpe/DD ratio:")
            print(df_sorted.head(5).to_string(index=False))
            
            # Display best combination
            best = df_sorted.iloc[0]
            print("\nBest parameter combination:")
            print(f"OBI_WIN: {best['OBI_WIN']}")
            print(f"OBI_THR: {best['OBI_THR']}")
            print(f"SPD_THR: {best['SPD_THR']}")
            print(f"CONF_BARS: {best['CONF_BARS']}")
            print(f"Sharpe: {best['Sharpe']}")
            print(f"MaxDD: {best['MaxDD']}")
            print(f"ROI: {best['ROI']}")
            print(f"ProfitFactor: {best['ProfitFactor']}")
            print(f"Sharpe/DD Ratio: {best['Sharpe_DD_Ratio']}")
    except Exception as e:
        print(f"Error analyzing results: {e}")

if __name__ == "__main__":
    run_grid_search()
