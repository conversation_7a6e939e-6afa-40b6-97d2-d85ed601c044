#!/usr/bin/env python3
"""
Debug GMS Signal Processing
Test actual detector behavior vs manual logic on the same data.
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

def test_detector_vs_manual():
    """Compare detector output with manual calculations."""
    # Load signals
    import glob
    files = glob.glob('/Users/<USER>/Desktop/trading_bot_/logs/backtest_signals_*.parquet')
    if not files:
        print("No signals files found")
        return
    
    latest_file = max(files)
    print(f"Loading {latest_file}")
    df = pd.read_parquet(latest_file)
    
    # Test on first 10 rows with detailed logging
    sample = df.head(10).copy()
    
    # Manual thresholds (from our config)
    vol_thresh_low = 0.005
    vol_thresh_high = 0.015
    mom_thresh_weak = 5.0
    mom_thresh_strong = 15.0
    
    print(f"Testing {len(sample)} samples...")
    print(f"Thresholds: vol({vol_thresh_low:.3f},{vol_thresh_high:.3f}) mom({mom_thresh_weak:.1f},{mom_thresh_strong:.1f})")
    
    for idx, row in sample.iterrows():
        print(f"\n--- Sample {idx} ---")
        
        # Extract signals
        signals = row.to_dict()
        atr_pct = signals.get('atr_percent', np.nan)
        ma_slope = signals.get('ma_slope', np.nan)
        ma_slope_ema = signals.get('ma_slope_ema_30s', np.nan)
        obi_5 = signals.get('obi_smoothed_5', np.nan)
        spread_mean = signals.get('spread_mean', np.nan)
        spread_std = signals.get('spread_std', np.nan)
        
        print(f"Raw signals:")
        print(f"  atr_percent: {atr_pct:.6f}")
        print(f"  ma_slope: {ma_slope:.3f}")
        print(f"  ma_slope_ema_30s: {ma_slope_ema:.3f}")
        print(f"  obi_smoothed_5: {obi_5:.6f}")
        print(f"  spread_mean: {spread_mean:.8f}")
        print(f"  spread_std: {spread_std:.8f}")
        
        # Check for missing signals
        missing_signals = []
        if pd.isna(atr_pct): missing_signals.append('atr_percent')
        if pd.isna(ma_slope): missing_signals.append('ma_slope')
        if pd.isna(obi_5): missing_signals.append('obi_smoothed_5')
        if pd.isna(spread_mean): missing_signals.append('spread_mean')
        if pd.isna(spread_std): missing_signals.append('spread_std')
        
        if missing_signals:
            print(f"Missing signals: {missing_signals}")
            manual_state = "Unknown"
        else:
            # Manual classification
            if atr_pct >= vol_thresh_high:
                vol_regime = "High"
            elif atr_pct <= vol_thresh_low:
                vol_regime = "Low"
            else:
                vol_regime = "Medium"
            
            abs_ma_slope = abs(ma_slope)
            if abs_ma_slope >= mom_thresh_strong:
                mom_regime = "Strong"
            elif abs_ma_slope <= mom_thresh_weak:
                mom_regime = "Weak"
            else:
                mom_regime = "Medium"
            
            direction = "Bull" if ma_slope > 0 else "Bear"
            obi_confirms = (obi_5 > 0 and direction == "Bull") or (obi_5 < 0 and direction == "Bear")
            
            print(f"Classifications:")
            print(f"  vol_regime: {vol_regime} (atr={atr_pct:.6f})")
            print(f"  mom_regime: {mom_regime} (|slope|={abs_ma_slope:.3f})")
            print(f"  direction: {direction}")
            print(f"  obi_confirms: {obi_confirms} (obi={obi_5:.6f})")
            
            # Combine (same logic as manual script)
            if vol_regime == "High":
                if mom_regime == "Strong" and obi_confirms:
                    manual_state = f"Strong_{direction}_Trend"
                else:
                    manual_state = "High_Vol_Range"
            elif vol_regime == "Low":
                if mom_regime == "Weak":
                    manual_state = "Low_Vol_Range"
                else:
                    manual_state = f"Weak_{direction}_Trend"
            else:  # Medium
                if mom_regime == "Strong" and obi_confirms:
                    manual_state = f"Strong_{direction}_Trend"
                elif mom_regime == "Medium":
                    manual_state = f"Weak_{direction}_Trend"
                else:
                    manual_state = "Uncertain"
        
        print(f"Manual result: {manual_state}")
        
        # Test what the actual detector would see
        # Check the signals the detector expects
        required_signals = [
            'timestamp',
            'atr_percent',
            'ma_slope',
            'obi_smoothed_5',
            'spread_mean',
            'spread_std',
            'close',
            'position_size_usd', 
            'realized_pnl_usd'
        ]
        
        detector_signals = {}
        detector_missing = []
        for signal in required_signals:
            if signal in signals:
                value = signals[signal]
                detector_signals[signal] = value
                if pd.isna(value):
                    detector_missing.append(f"{signal}=NaN")
            else:
                detector_missing.append(f"{signal}=MISSING")
                detector_signals[signal] = np.nan
        
        if detector_missing:
            print(f"Detector missing/NaN: {detector_missing}")
        
        # Manual simulation of detector's _validate_signals logic
        detector_would_fail = False
        for signal in ['close', 'atr_percent', 'ma_slope']:
            if pd.isna(detector_signals.get(signal, np.nan)):
                print(f"Detector validation would FAIL on: {signal}")
                detector_would_fail = True
        
        if detector_would_fail:
            print(f"Detector would return: Unknown (failed validation)")
        else:
            print(f"Detector would proceed with classification")
            # The detector should get the same result as manual
            print(f"Expected detector result: {manual_state}")

if __name__ == "__main__":
    test_detector_vs_manual()