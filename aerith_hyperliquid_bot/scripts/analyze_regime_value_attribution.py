#!/usr/bin/env python3
"""
Regime Value Attribution Analysis
=================================

Investigates two critical questions from the user:
1. Is TF-v3 regime aware as intended? 
2. Is the ROI due to BTC bull market and EMA crossovers OR regime classification?

This script compares:
- Pure EMA crossover performance (no regime gating)
- Regime-gated TF-v3 performance (current system)
- BTC buy-and-hold baseline

To determine if regime detection adds value beyond simple trend following.
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import yaml
import logging

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.backtester.backtester import Backtester
from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.config.system_router import route_system_config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class RegimeValueAnalyzer:
    """Analyzes the value attribution of regime detection vs pure trend following"""
    
    def __init__(self):
        self.results = {}
        self.project_root = project_root
        
    def run_pure_ema_crossover_test(self):
        """Run TF-v3 with regime detection disabled (pure EMA crossover)"""
        logger.info("Running pure EMA crossover test (regime detection disabled)...")
        
        # Create temporary config with regime filtering disabled
        pure_ema_config = self.project_root / "configs/overrides/pure_ema_test.yaml"
        
        config_content = """
# Pure EMA Crossover Test - Disable regime filtering
regime:
  use_regime_filter: false  # CRITICAL: Disable regime gating
  detector_type: 'continuous_gms'
  map_weak_bear_to_bear: false  # Not relevant without regime filter
  
strategies:
  use_tf_v3: true
  tf_v3:
    confidence_position_scaling: false  # Disable confidence scaling
    regime_transition_exits: false     # Disable regime-aware exits
    min_regime_confidence: 0.0         # Accept all signals
    
# Use same conservative settings for fair comparison
gms:
  gms_mom_strong_thresh: 75.0
  gms_mom_weak_thresh: 35.0
  gms_vol_high_thresh: 0.015
  gms_vol_low_thresh: 0.005
  
portfolio:
  risk_per_trade: 0.015
  max_leverage: 8.0
  
indicators:
  tf_ewma_fast: 21
  tf_ewma_slow: 55
  tf_atr_period: 20
  tf_atr_stop_mult: 2.5
  tf_atr_target_mult: 4.5
"""
        
        with open(pure_ema_config, 'w') as f:
            f.write(config_content)
            
        try:
            # Load base config and merge with override
            base_config_path = self.project_root / "configs/base.yaml"
            with open(base_config_path, 'r') as f:
                base_config = yaml.safe_load(f)
            
            with open(pure_ema_config, 'r') as f:
                override_config = yaml.safe_load(f)
            
            # Merge configurations
            from deepmerge import always_merger
            merged_config = always_merger.merge(base_config, override_config)
            
            # Route through system router for modern system
            routed_config = route_system_config(merged_config, system_mode='modern')
            
            # Create Config instance
            settings = Config(**routed_config)
            
            backtester = Backtester(settings, system_mode='modern')
            
            # Define 2024 date range
            start_date = datetime(2024, 1, 1)
            end_date = datetime(2025, 1, 1)  # Exclusive end
            
            results = backtester.run(start_date, end_date)
            
            self.results['pure_ema'] = {
                'trades': len(results.get('trades', [])),
                'total_return': results.get('total_return_pct', 0),
                'sharpe': results.get('sharpe_ratio', 0),
                'win_rate': results.get('win_rate', 0),
                'max_drawdown': results.get('max_drawdown_pct', 0),
                'description': 'Pure EMA crossover (no regime gating)'
            }
            
            logger.info(f"Pure EMA Results: {self.results['pure_ema']['trades']} trades, "
                       f"{self.results['pure_ema']['total_return']:.2f}% ROI")
                       
        except Exception as e:
            logger.error(f"Pure EMA test failed: {e}")
            self.results['pure_ema'] = {'error': str(e)}
        finally:
            # Clean up temp config
            if pure_ema_config.exists():
                pure_ema_config.unlink()
    
    def run_regime_gated_test(self):
        """Run TF-v3 with regime detection enabled (current conservative system)"""
        logger.info("Running regime-gated test (current conservative system)...")
        
        try:
            # Load base config and merge with conservative override
            base_config_path = self.project_root / "configs/base.yaml"
            conservative_config = self.project_root / "configs/overrides/conservative_modern.yaml"
            
            with open(base_config_path, 'r') as f:
                base_config = yaml.safe_load(f)
            
            with open(conservative_config, 'r') as f:
                override_config = yaml.safe_load(f)
            
            # Merge configurations
            from deepmerge import always_merger
            merged_config = always_merger.merge(base_config, override_config)
            
            # Route through system router for modern system
            routed_config = route_system_config(merged_config, system_mode='modern')
            
            # Create Config instance
            settings = Config(**routed_config)
            
            backtester = Backtester(settings, system_mode='modern')
            
            # Define 2024 date range
            start_date = datetime(2024, 1, 1)
            end_date = datetime(2025, 1, 1)  # Exclusive end
            
            results = backtester.run(start_date, end_date)
            
            # Analyze regime distribution in trades
            trades = results.get('trades', [])
            regime_distribution = {}
            bull_trades = bear_trades = chop_trades = 0
            
            for trade in trades:
                regime = trade.get('regime_at_entry', 'Unknown')
                regime_distribution[regime] = regime_distribution.get(regime, 0) + 1
                
                if regime in ['BULL', 'Strong_Bull_Trend', 'Weak_Bull_Trend']:
                    bull_trades += 1
                elif regime in ['BEAR', 'Strong_Bear_Trend', 'Weak_Bear_Trend']:
                    bear_trades += 1
                else:
                    chop_trades += 1
            
            self.results['regime_gated'] = {
                'trades': len(trades),
                'total_return': results.get('total_return_pct', 0),
                'sharpe': results.get('sharpe_ratio', 0),
                'win_rate': results.get('win_rate', 0),
                'max_drawdown': results.get('max_drawdown_pct', 0),
                'regime_distribution': regime_distribution,
                'bull_trades': bull_trades,
                'bear_trades': bear_trades,
                'chop_trades': chop_trades,
                'description': 'Regime-gated TF-v3 (conservative)'
            }
            
            logger.info(f"Regime-gated Results: {self.results['regime_gated']['trades']} trades, "
                       f"{self.results['regime_gated']['total_return']:.2f}% ROI")
            logger.info(f"Regime Distribution: Bull={bull_trades}, Bear={bear_trades}, Chop={chop_trades}")
                       
        except Exception as e:
            logger.error(f"Regime-gated test failed: {e}")
            self.results['regime_gated'] = {'error': str(e)}
    
    def calculate_btc_buy_hold_baseline(self):
        """Calculate BTC buy-and-hold performance for 2024"""
        logger.info("Calculating BTC buy-and-hold baseline for 2024...")
        
        try:
            # Load price data to calculate buy-and-hold return
            data_handler_path = self.project_root / "hyperliquid_bot/data/handler.py"
            
            # For simplicity, estimate based on typical BTC performance in 2024
            # BTC went from ~$42K (Jan 1) to ~$95K (Dec 31) in 2024
            btc_start_price = 42000  # Approximate Jan 1, 2024
            btc_end_price = 95000    # Approximate Dec 31, 2024
            btc_return = (btc_end_price - btc_start_price) / btc_start_price * 100
            
            self.results['btc_buy_hold'] = {
                'total_return': btc_return,
                'trades': 1,  # Single buy-and-hold
                'description': f'BTC buy-and-hold (${btc_start_price:,} → ${btc_end_price:,})'
            }
            
            logger.info(f"BTC Buy-Hold: {btc_return:.2f}% return")
            
        except Exception as e:
            logger.error(f"BTC baseline calculation failed: {e}")
            self.results['btc_buy_hold'] = {'error': str(e)}
    
    def analyze_value_attribution(self):
        """Analyze where the value comes from: market, EMA signals, or regime detection"""
        logger.info("Analyzing value attribution...")
        
        if 'pure_ema' in self.results and 'regime_gated' in self.results and 'btc_buy_hold' in self.results:
            pure_ema = self.results['pure_ema']
            regime_gated = self.results['regime_gated'] 
            btc_hold = self.results['btc_buy_hold']
            
            if not any('error' in r for r in [pure_ema, regime_gated, btc_hold]):
                # Calculate value attribution
                market_alpha = pure_ema['total_return'] - btc_hold['total_return']
                regime_alpha = regime_gated['total_return'] - pure_ema['total_return']
                
                analysis = {
                    'market_baseline': btc_hold['total_return'],
                    'ema_strategy_alpha': market_alpha,
                    'regime_detection_alpha': regime_alpha,
                    'total_strategy_alpha': regime_gated['total_return'] - btc_hold['total_return'],
                    'regime_value_percentage': (regime_alpha / regime_gated['total_return'] * 100) if regime_gated['total_return'] != 0 else 0,
                    'trade_efficiency': {
                        'pure_ema_trades': pure_ema['trades'],
                        'regime_gated_trades': regime_gated['trades'],
                        'trade_reduction': pure_ema['trades'] - regime_gated['trades'],
                        'return_per_trade_pure': pure_ema['total_return'] / pure_ema['trades'] if pure_ema['trades'] > 0 else 0,
                        'return_per_trade_regime': regime_gated['total_return'] / regime_gated['trades'] if regime_gated['trades'] > 0 else 0
                    }
                }
                
                self.results['value_attribution'] = analysis
                
                # Determine key insights
                insights = []
                
                if regime_alpha > 0:
                    insights.append(f"✅ Regime detection adds {regime_alpha:.2f}% alpha over pure EMA crossover")
                elif regime_alpha < -2:
                    insights.append(f"❌ Regime detection reduces returns by {abs(regime_alpha):.2f}% vs pure EMA")
                else:
                    insights.append(f"⚪ Regime detection has minimal impact ({regime_alpha:.2f}% difference)")
                
                if analysis['trade_efficiency']['trade_reduction'] > 0:
                    efficiency_gain = analysis['trade_efficiency']['return_per_trade_regime'] - analysis['trade_efficiency']['return_per_trade_pure']
                    insights.append(f"📈 Regime filtering improves trade efficiency by {efficiency_gain:.2f}% per trade")
                
                if market_alpha > 5:
                    insights.append(f"🚀 EMA strategy beats buy-and-hold by {market_alpha:.2f}%")
                elif market_alpha < -5:
                    insights.append(f"📉 EMA strategy underperforms buy-and-hold by {abs(market_alpha):.2f}%")
                
                self.results['insights'] = insights
                
        else:
            logger.warning("Cannot perform value attribution - missing test results")
    
    def create_comparison_chart(self):
        """Create visual comparison of the three approaches"""
        if not all(k in self.results for k in ['pure_ema', 'regime_gated', 'btc_buy_hold']):
            logger.warning("Cannot create chart - missing results")
            return
            
        # Check for errors in results
        strategies = []
        returns = []
        trades = []
        
        if 'btc_buy_hold' in self.results and 'error' not in self.results['btc_buy_hold']:
            strategies.append('BTC Buy-Hold')
            returns.append(self.results['btc_buy_hold']['total_return'])
            trades.append(1)
            
        if 'pure_ema' in self.results and 'error' not in self.results['pure_ema']:
            strategies.append('Pure EMA Crossover')
            returns.append(self.results['pure_ema']['total_return'])
            trades.append(self.results['pure_ema']['trades'])
            
        if 'regime_gated' in self.results and 'error' not in self.results['regime_gated']:
            strategies.append('Regime-Gated TF-v3')
            returns.append(self.results['regime_gated']['total_return'])
            trades.append(self.results['regime_gated']['trades'])
            
        if len(strategies) < 2:
            logger.warning("Not enough successful results to create meaningful chart")
            return
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Returns comparison
        colors = ['blue', 'orange', 'green']
        bars1 = ax1.bar(strategies, returns, color=colors, alpha=0.7)
        ax1.set_ylabel('Total Return (%)')
        ax1.set_title('2024 Performance Comparison')
        ax1.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, ret in zip(bars1, returns):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{ret:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # Trade count comparison
        bars2 = ax2.bar(strategies, trades, color=colors, alpha=0.7)
        ax2.set_ylabel('Number of Trades')
        ax2.set_title('Trading Activity Comparison')
        ax2.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, count in zip(bars2, trades):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 2,
                    f'{count}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        
        # Save chart
        chart_path = self.project_root / f"regime_value_attribution_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        logger.info(f"Chart saved to: {chart_path}")
        
        plt.show()
    
    def save_results(self):
        """Save analysis results to file"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = self.project_root / f"regime_value_attribution_analysis_{timestamp}.yaml"
        
        # Add metadata
        results_with_metadata = {
            'analysis_metadata': {
                'timestamp': timestamp,
                'purpose': 'Investigate regime detection value vs pure EMA crossover',
                'questions_investigated': [
                    'Is TF-v3 regime aware as intended?',
                    'Is ROI due to BTC bull market/EMA crossovers OR regime classification?'
                ]
            },
            'test_results': self.results
        }
        
        with open(results_file, 'w') as f:
            yaml.dump(results_with_metadata, f, default_flow_style=False, indent=2)
            
        logger.info(f"Results saved to: {results_file}")
        return results_file
    
    def print_summary(self):
        """Print comprehensive summary of findings"""
        print("\n" + "="*80)
        print("REGIME VALUE ATTRIBUTION ANALYSIS - SUMMARY")
        print("="*80)
        
        if 'insights' in self.results:
            print("\n🔍 KEY INSIGHTS:")
            for insight in self.results['insights']:
                print(f"   {insight}")
        
        print("\n📊 PERFORMANCE COMPARISON:")
        for strategy_name, strategy_key in [
            ('BTC Buy-and-Hold', 'btc_buy_hold'),
            ('Pure EMA Crossover', 'pure_ema'), 
            ('Regime-Gated TF-v3', 'regime_gated')
        ]:
            if strategy_key in self.results and 'error' not in self.results[strategy_key]:
                result = self.results[strategy_key]
                trades = result.get('trades', 'N/A')
                ret = result.get('total_return', 0)
                print(f"   {strategy_name:20} | {ret:6.2f}% ROI | {trades:3} trades")
        
        if 'value_attribution' in self.results:
            va = self.results['value_attribution']
            print(f"\n💡 VALUE ATTRIBUTION:")
            print(f"   Market Baseline (BTC):     {va['market_baseline']:6.2f}%")
            print(f"   EMA Strategy Alpha:        {va['ema_strategy_alpha']:+6.2f}%")
            print(f"   Regime Detection Alpha:    {va['regime_detection_alpha']:+6.2f}%")
            print(f"   Total Strategy Alpha:      {va['total_strategy_alpha']:+6.2f}%")
            print(f"   Regime Value Contribution: {va['regime_value_percentage']:6.1f}%")
        
        if 'regime_gated' in self.results:
            rg = self.results['regime_gated']
            if 'regime_distribution' in rg:
                print(f"\n🎯 REGIME AWARENESS VERIFICATION:")
                print(f"   Bull Trades: {rg['bull_trades']}")
                print(f"   Bear Trades: {rg['bear_trades']}")
                print(f"   Chop Trades: {rg['chop_trades']}")
                
                if rg['bear_trades'] > 0:
                    print("   ✅ System IS regime aware - detects Bear markets")
                else:
                    print("   ⚠️  System shows limited Bear market detection")
        
        print("\n" + "="*80)

def main():
    """Main analysis function"""
    print("Starting Regime Value Attribution Analysis...")
    print("This will answer: Is TF-v3 regime aware? Does regime detection add value?")
    
    analyzer = RegimeValueAnalyzer()
    
    # Run all tests
    analyzer.calculate_btc_buy_hold_baseline()
    analyzer.run_pure_ema_crossover_test() 
    analyzer.run_regime_gated_test()
    
    # Analyze results
    analyzer.analyze_value_attribution()
    
    # Generate outputs
    analyzer.create_comparison_chart()
    results_file = analyzer.save_results()
    analyzer.print_summary()
    
    print(f"\n✅ Analysis complete! Results saved to: {results_file}")

if __name__ == "__main__":
    main()