#!/usr/bin/env python3
"""
Simple Direct Debug Script for Modern System
===========================================

Direct analysis without complex wrappers to identify the root cause
of 0 trades in the modern system.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import json

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.data_loader import ModernDataLoader
from hyperliquid_bot.modern.continuous_detector_v2 import ModernContinuousDetectorV2


def main():
    """Run direct analysis."""
    print("="*80)
    print("MODERN SYSTEM DIRECT DEBUG ANALYSIS")
    print("="*80)
    
    # Load config
    config = load_config("configs/overrides/modern_system_v2_complete.yaml")
    
    # Initialize components
    loader = ModernDataLoader(config)
    detector = ModernContinuousDetectorV2(config, mode='backtest')
    
    # Test dates
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 2)  # Just one day
    
    print(f"\nTesting period: {start_date} to {end_date}")
    
    # 1. Check if we have data files
    print("\n" + "="*60)
    print("1. CHECKING DATA FILES")
    print("="*60)
    
    feature_dir = Path(config.data_paths.feature_1s_dir)
    date_dir = feature_dir / "2024-01-01"
    
    if date_dir.exists():
        files = list(date_dir.glob("features_*.parquet"))
        print(f"Found {len(files)} feature files in {date_dir}")
        
        if files:
            # Load one file to check structure
            df = pd.read_parquet(files[0])
            print(f"\nSample file: {files[0].name}")
            print(f"Shape: {df.shape}")
            print(f"Columns: {list(df.columns)}")
            
            # Check for critical fields
            critical_fields = ['obi_smoothed', 'ma_slope_ema_30_sec', 'atr_percent_14_sec']
            missing = [f for f in critical_fields if f not in df.columns]
            if missing:
                print(f"\nMISSING CRITICAL FIELDS: {missing}")
            
            # Check data ranges
            if 'obi_smoothed' in df.columns:
                obi_vals = df['obi_smoothed'].dropna()
                print(f"\nOBI values - min: {obi_vals.min():.4f}, max: {obi_vals.max():.4f}, mean: {obi_vals.mean():.4f}")
            
            if 'atr_percent_14_sec' in df.columns:
                atr_vals = df['atr_percent_14_sec'].dropna()
                print(f"ATR% values - min: {atr_vals.min():.6f}, max: {atr_vals.max():.6f}, mean: {atr_vals.mean():.6f}")
            
            if 'ma_slope_ema_30_sec' in df.columns:
                ma_vals = df['ma_slope_ema_30_sec'].dropna()
                print(f"MA slope values - min: {ma_vals.min():.6f}, max: {ma_vals.max():.6f}, mean: {ma_vals.mean():.6f}")
    else:
        print(f"ERROR: Data directory not found: {date_dir}")
        return
    
    # 2. Test data loading through loader
    print("\n" + "="*60)
    print("2. TESTING DATA LOADER")
    print("="*60)
    
    try:
        # Load raw features
        raw_df = loader._load_feature_data(start_date, end_date)
        print(f"Loaded {len(raw_df)} rows of feature data")
        
        if len(raw_df) > 0:
            # Test loading signals for specific timestamps
            test_timestamps = raw_df.index[:5]  # First 5 timestamps
            
            for ts in test_timestamps:
                signals = loader.load_signals(ts)
                print(f"\nSignals at {ts}:")
                
                # Check adapted fields
                for field in ['volume_imbalance', 'atr_percent_sec', 'ma_slope_ema_30s']:
                    if field in signals:
                        print(f"  {field}: {signals[field]}")
                    else:
                        print(f"  {field}: MISSING")
    except Exception as e:
        print(f"ERROR loading data: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. Test detector state transitions
    print("\n" + "="*60)
    print("3. TESTING DETECTOR STATE TRANSITIONS")
    print("="*60)
    
    # Detector thresholds
    print(f"\nDetector thresholds:")
    print(f"  Volatility: low={detector.vol_low_thresh:.6f}, high={detector.vol_high_thresh:.6f}")
    print(f"  Momentum: weak={detector.mom_weak_thresh:.6f}, strong={detector.mom_strong_thresh:.6f}")
    print(f"  OBI: weak={detector.obi_weak_confirm_thresh:.3f}, strong={detector.obi_strong_confirm_thresh:.3f}")
    
    # Process signals through detector
    if 'raw_df' in locals() and len(raw_df) > 0:
        state_counts = {}
        state_transitions = []
        last_state = None
        
        # Sample every 60 seconds (detector cadence)
        sample_indices = range(0, min(1000, len(raw_df)), 60)
        
        for i in sample_indices:
            ts = raw_df.index[i]
            row = raw_df.iloc[i]
            
            # Get signals for this timestamp
            signals = loader.load_signals(ts)
            
            # Update detector
            update_result = detector.update(signals, ts)
            
            if update_result:
                state = update_result['state']
                confidence = update_result['confidence']
                signal_quality = update_result.get('signal_quality', 0)
                
                # Count states
                state_counts[state] = state_counts.get(state, 0) + 1
                
                # Track transitions
                if last_state and state != last_state:
                    state_transitions.append({
                        'time': ts,
                        'from': last_state,
                        'to': state,
                        'confidence': confidence,
                        'quality': signal_quality
                    })
                
                last_state = state
        
        print(f"\nProcessed {len(sample_indices)} detector updates")
        print("\nState distribution:")
        for state, count in sorted(state_counts.items(), key=lambda x: x[1], reverse=True):
            pct = (count / len(sample_indices)) * 100
            print(f"  {state}: {count} ({pct:.1f}%)")
        
        print(f"\nState transitions: {len(state_transitions)}")
        if state_transitions:
            print("First 3 transitions:")
            for trans in state_transitions[:3]:
                print(f"  {trans['time']}: {trans['from']} -> {trans['to']} "
                      f"(conf: {trans['confidence']:.2f}, qual: {trans['quality']:.2f})")
    
    # 4. Check if we're in trading states
    print("\n" + "="*60)
    print("4. TRADING STATE ANALYSIS")
    print("="*60)
    
    trading_states = ['Strong_Bull_Trend', 'Weak_Bull_Trend', 'Strong_Bear_Trend', 'Weak_Bear_Trend']
    
    if 'state_counts' in locals():
        trading_count = sum(state_counts.get(s, 0) for s in trading_states)
        total_count = sum(state_counts.values())
        
        if total_count > 0:
            trading_pct = (trading_count / total_count) * 100
            print(f"\nTrading states: {trading_count}/{total_count} ({trading_pct:.1f}%)")
            
            if trading_pct < 10:
                print("\nWARNING: Less than 10% of time in trading states!")
                print("This is likely why we see 0 trades.")
        else:
            print("\nERROR: No detector updates processed!")
    
    # 5. Analyze why we might not be in trading states
    print("\n" + "="*60)
    print("5. ROOT CAUSE ANALYSIS")
    print("="*60)
    
    if 'raw_df' in locals() and len(raw_df) > 0:
        # Sample some data
        sample_df = raw_df.iloc[:100]
        
        # Check if data values match threshold ranges
        if 'atr_percent_14_sec' in sample_df.columns:
            atr_vals = sample_df['atr_percent_14_sec'].dropna()
            if len(atr_vals) > 0:
                low_vol_pct = (atr_vals <= detector.vol_low_thresh).sum() / len(atr_vals) * 100
                high_vol_pct = (atr_vals >= detector.vol_high_thresh).sum() / len(atr_vals) * 100
                
                print(f"\nVolatility analysis:")
                print(f"  Data range: [{atr_vals.min():.6f}, {atr_vals.max():.6f}]")
                print(f"  Thresholds: low={detector.vol_low_thresh:.6f}, high={detector.vol_high_thresh:.6f}")
                print(f"  Low volatility: {low_vol_pct:.1f}% of samples")
                print(f"  High volatility: {high_vol_pct:.1f}% of samples")
        
        if 'ma_slope_ema_30_sec' in sample_df.columns:
            ma_vals = sample_df['ma_slope_ema_30_sec'].dropna()
            if len(ma_vals) > 0:
                abs_ma = ma_vals.abs()
                weak_trend_pct = ((abs_ma >= detector.mom_weak_thresh) & 
                                 (abs_ma < detector.mom_strong_thresh)).sum() / len(ma_vals) * 100
                strong_trend_pct = (abs_ma >= detector.mom_strong_thresh).sum() / len(ma_vals) * 100
                
                print(f"\nMomentum analysis:")
                print(f"  Data range: [{ma_vals.min():.6f}, {ma_vals.max():.6f}]")
                print(f"  Thresholds: weak=±{detector.mom_weak_thresh:.6f}, strong=±{detector.mom_strong_thresh:.6f}")
                print(f"  Weak trend: {weak_trend_pct:.1f}% of samples")
                print(f"  Strong trend: {strong_trend_pct:.1f}% of samples")
    
    print("\n" + "="*80)
    print("ANALYSIS COMPLETE")
    print("="*80)


if __name__ == "__main__":
    main()