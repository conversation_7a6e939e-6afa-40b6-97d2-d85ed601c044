#!/usr/bin/env python3
"""
Test script to verify that the fixed override files work correctly.
"""
import sys
import subprocess
import re
import time
from pathlib import Path

# Define the project root
PROJECT_ROOT = Path(__file__).parent.parent.resolve()

def run_test():
    """Run a test with the fixed override file."""
    # Use the first override file from the Latin square design
    override_path = PROJECT_ROOT / "configs" / "overrides" / "grid_search" / "run_01.yaml"
    print(f"Using override file: {override_path}")
    
    # Display the contents of the override file
    with open(override_path, 'r') as f:
        override_content = f.read()
    print(f"Override file contents:\n{override_content}")
    
    # Record start time
    run_start_time = time.time()
    
    # Run the backtester with the override
    backtest_script = PROJECT_ROOT / "hyperliquid_bot" / "backtester" / "run_backtest.py"
    cmd = [
        sys.executable,  # Current Python interpreter
        str(backtest_script),
        "--override", str(override_path)
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    process = subprocess.run(cmd, check=True, capture_output=True, text=True)
    
    # Record end time
    run_end_time = time.time()
    
    # Extract and print any log messages about the loaded configuration
    config_logs = re.findall(r'.*GMS OBI Strong Confirm.*', process.stdout)
    if config_logs:
        print("\nConfiguration logs:")
        for log in config_logs:
            print(log)
    else:
        print("\nNo configuration logs found.")
    
    print("\nBacktest completed successfully.")

if __name__ == "__main__":
    run_test()
