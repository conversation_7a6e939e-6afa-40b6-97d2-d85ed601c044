#!/usr/bin/env python3
"""
Simple investigation of trade count discrepancy.
"""

import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.core.detector_factory import get_regime_detector


def main():
    """Check detector configuration."""
    
    print("Trade Count Investigation")
    print("="*60)
    print("Expected: 184 trades (original GranularMicrostructureRegimeDetector)")
    print("Actual: 175 trades (UnifiedGMSDetector in legacy mode)")
    print("Difference: -9 trades (-4.9%)")
    print("="*60)
    
    # Load config
    config = load_config("configs/base.yaml")
    
    # Check detector configuration
    print(f"\nDetector type in config: {config.regime.detector_type}")
    
    # Create detector
    detector = get_regime_detector(config)
    print(f"Created detector: {type(detector).__name__}")
    
    # Check if it's the unified detector
    print(f"Module: {type(detector).__module__}")
    
    # Check key parameters
    if hasattr(detector, 'detector_mode'):
        print(f"\nDetector mode: {detector.detector_mode}")
    
    if hasattr(detector, 'thresholds'):
        print(f"\nThresholds:")
        for k, v in detector.thresholds.items():
            print(f"  {k}: {v}")
    
    # Check OBI thresholds
    print(f"\nOBI thresholds:")
    print(f"  Strong: {getattr(detector, 'obi_strong_confirm_thresh', 'N/A')}")
    print(f"  Weak: {getattr(detector, 'obi_weak_confirm_thresh', 'N/A')}")
    
    # Check for differences in state handling
    print("\n\nPotential causes for trade count difference:")
    print("1. State name mapping differences")
    print("   - Original uses states like 'High_Vol_Trending_Bull'")
    print("   - Unified uses 'Strong_Bull_Trend' (standardized names)")
    print("   - This affects strategy filtering")
    print("\n2. Threshold comparison differences")
    print("   - Check if >= vs > is consistent")
    print("   - Floating point precision issues")
    print("\n3. State validation")
    print("   - UnifiedGMSDetector validates states against allowed list")
    print("   - Original might not validate as strictly")
    print("\n4. Edge case handling")
    print("   - Different NaN handling")
    print("   - Different fallback behaviors")
    
    print("\n\nNext steps:")
    print("1. Run both detectors side-by-side on same data")
    print("2. Log every regime decision to find where they diverge")
    print("3. Check if strategies are filtering on exact state names")


if __name__ == "__main__":
    main()