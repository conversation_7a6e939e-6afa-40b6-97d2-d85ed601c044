# Simplified verification for direct execution
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger()

# Import the modules we want to test
from hyperliquid_bot.utils.state_mapping import (
    get_valid_gms_states,
    get_state_map,
    map_gms_state,
    validate_gms_state,
    validate_3state,
    GMS_STATE_UNKNOWN,
    GMS_STATE_FILTER_OFF
)

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.detector import GranularMicrostructureRegimeDetector

# Define test functions
def test_state_validation():
    """Test state validation functionality"""
    logger.info("=== Testing State Validation ===")
    
    # Get valid states
    valid_states = get_valid_gms_states()
    logger.info(f"Valid GMS states: {sorted(list(valid_states))}")
    
    # Test validation on valid states
    for state in valid_states:
        if validate_gms_state(state) or state in [GMS_STATE_UNKNOWN, GMS_STATE_FILTER_OFF]:
            logger.info(f"✓ '{state}' correctly validates")
        else:
            logger.error(f"✗ State '{state}' failed validation despite being in valid_states")
    
    # Test validation on invalid states
    for state in ["bull", "bear", "chop", "INVALID_STATE", "test", ""]:
        if state not in valid_states:
            logger.info(f"✓ '{state}' correctly identified as invalid")
        else:
            logger.warning(f"✗ Unexpected: '{state}' is considered valid")

def test_mapping():
    """Test mapping from 7-state to 3-state"""
    logger.info("\n=== Testing State Mapping ===")
    
    # Get the state map
    state_map = get_state_map()
    logger.info(f"Loaded state map: {state_map}")
    
    # Test mapping of all valid states
    for state in get_valid_gms_states():
        if state in [GMS_STATE_UNKNOWN, GMS_STATE_FILTER_OFF]:
            # Special states - these aren't part of the normal mapping
            continue
            
        mapped = map_gms_state(state)
        if validate_3state(mapped):
            logger.info(f"✓ '{state}' maps to valid 3-state '{mapped}'")
        else:
            logger.error(f"✗ Mapped value '{mapped}' for '{state}' is not a valid 3-state")

def test_detector():
    """Test the GMS detector's output validation"""
    logger.info("\n=== Testing GMS Detector Output Validation ===")
    
    try:
        # Create a minimal configuration for testing
        config = Config.from_yaml("configs/base.yaml")
        
        # Create a detector instance
        detector = GranularMicrostructureRegimeDetector(config)
        
        # Create a minimal signal set to test detector behavior
        # This will intentionally produce an "Uncertain" state
        minimal_signals = {
            'atr_pct': 0.005,
            'ma_slope': 0.0001,  # Near zero slope
            'obi_ratio': 0.0,    # Neutral OBI
            'spread_mean': 0.1,
            'spread_std': 0.01,
            'funding_rate': 0.0,
            'adx': 15.0,
            'rsi': 50.0
        }
        
        # Test detector output
        regime = detector.get_regime(minimal_signals)
        logger.info(f"Detector produced regime: '{regime}'")
        
        # Verify the output is in our valid states list
        valid_states = get_valid_gms_states()
        if regime in valid_states:
            logger.info(f"✓ Detector output '{regime}' is a valid state")
        else:
            logger.error(f"✗ Output regime '{regime}' is not in valid states")
        
        # Map the regime to 3-state
        mapped_regime = map_gms_state(regime)
        logger.info(f"✓ Mapped to 3-state: '{mapped_regime}'")
        
    except Exception as e:
        logger.error(f"Error testing detector: {e}")
        import traceback
        traceback.print_exc()

# Run all tests
if __name__ == "__main__":
    print("Starting GMS State Mapping Verification...")
    test_state_validation()
    test_mapping()
    test_detector()
    print("\n=== Verification Complete ===")
