#!/usr/bin/env python3
"""
Debug spread values in enhanced hourly data
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

import pandas as pd
import numpy as np
from datetime import datetime

# Load a sample of enhanced hourly data
data_dir = Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/enhanced_hourly/1h")
sample_file = data_dir / "2024-01-01_1h_enhanced.parquet"

if sample_file.exists():
    df = pd.read_parquet(sample_file)
    print(f"Loaded {len(df)} rows from {sample_file}")
    
    # Check spread-related columns
    spread_cols = [col for col in df.columns if 'spread' in col.lower()]
    print(f"\nSpread columns found: {spread_cols}")
    
    for col in spread_cols:
        if col in df.columns:
            print(f"\n{col} statistics:")
            print(f"  Min: {df[col].min():.8f}")
            print(f"  25%: {df[col].quantile(0.25):.8f}")
            print(f"  50%: {df[col].quantile(0.50):.8f}")
            print(f"  75%: {df[col].quantile(0.75):.8f}")
            print(f"  Max: {df[col].max():.8f}")
            print(f"  Mean: {df[col].mean():.8f}")
            print(f"  Std: {df[col].std():.8f}")
            
            # Show some sample values
            print(f"\n  First 5 values:")
            for i in range(min(5, len(df))):
                print(f"    {df[col].iloc[i]:.8f}")
    
    # Also check if there's a 'spread' column
    if 'spread' in df.columns:
        print(f"\nspread column statistics:")
        print(f"  Min: {df['spread'].min():.8f}")
        print(f"  25%: {df['spread'].quantile(0.25):.8f}")
        print(f"  50%: {df['spread'].quantile(0.50):.8f}")
        print(f"  75%: {df['spread'].quantile(0.75):.8f}")
        print(f"  Max: {df['spread'].max():.8f}")
        
    # Compare to the threshold
    threshold = 0.000045  # 4.5 bps
    print(f"\nThreshold comparison:")
    print(f"  Low spread threshold: {threshold:.8f} (4.5 bps)")
    print(f"  High spread std: {0.000050:.8f} (5.0 bps)")
    
    if 'spread_mean' in df.columns:
        below_threshold = (df['spread_mean'] <= threshold).sum()
        print(f"\n  Rows with spread_mean <= {threshold}: {below_threshold} ({below_threshold/len(df)*100:.1f}%)")
        
        # Check what percentile the threshold represents
        if df['spread_mean'].min() <= threshold <= df['spread_mean'].max():
            percentile = (df['spread_mean'] <= threshold).sum() / len(df) * 100
            print(f"  {threshold} is at the {percentile:.1f}th percentile")
else:
    print(f"File not found: {sample_file}")