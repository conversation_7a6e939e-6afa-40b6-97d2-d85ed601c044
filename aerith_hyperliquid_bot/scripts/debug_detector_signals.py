#!/usr/bin/env python3
"""
Debug what signals are actually passed to the detector
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from datetime import datetime
import logging
import pandas as pd

# Patch detector to log signals
from hyperliquid_bot.legacy.detector import LegacyGranularMicrostructureDetector

original_detect = LegacyGranularMicrostructureDetector.detect_regime

def patched_detect(self, signals, timestamp=None):
    """Log what signals we receive"""
    print(f"\n=== DETECTOR SIGNALS at {timestamp} ===")
    if isinstance(signals, dict):
        for key, value in sorted(signals.items()):
            if key.startswith('obi') or key == 'atr_percent' or key == 'ma_slope':
                print(f"  {key}: {value}")
    
    # Check specifically what we need
    atr_pct = signals.get('atr_percent', 'MISSING')
    ma_slope = signals.get('ma_slope', 'MISSING') 
    obi = signals.get(f'obi_smoothed_{self.depth_levels}', 'MISSING')
    spread_mean = signals.get('spread_mean', 'MISSING')
    
    print(f"\nCRITICAL FIELDS:")
    print(f"  atr_percent: {atr_pct}")
    print(f"  ma_slope: {ma_slope}")
    print(f"  obi_smoothed_5: {obi}")
    print(f"  spread_mean: {spread_mean}")
    
    return original_detect(self, signals, timestamp)

LegacyGranularMicrostructureDetector.detect_regime = patched_detect

# Run a minimal test
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_backtest_engine import RobustBacktestEngine

config = load_config("configs/overrides/modern_system_v2_complete.yaml")
config.regime.detector_type = "enhanced"

engine = RobustBacktestEngine(
    config=config,
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 1, 2),  # Just 2 hours
    use_regime_cache=False
)

print("Running test...")
results = engine.run_backtest()