#!/usr/bin/env python3
"""
Test Regime-Only Trading
========================

Temporarily modify TF-v3 to trade based on regime alone,
to verify the rest of the system works.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.tf_v3_modern import ModernTFV3Strategy
from hyperliquid_bot.utils.state_mapping import (
    GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND,
    GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_WEAK_BEAR_TREND
)

# Monkey patch the evaluate_entry method
original_evaluate_entry = ModernTFV3Strategy.evaluate_entry

def regime_only_evaluate_entry(self, signals, regime):
    """
    Simplified entry logic - trade based on regime alone.
    This is TEMPORARY for testing only!
    """
    # Extract regime features
    regime_features = signals.get('regime_features', {})
    
    # 1. Check if regime allows trading
    if regime not in self.trend_states:
        self.logger.debug(f"Regime {regime} not in trend states, skipping")
        return None
    
    # 2. Check regime stability (keep this check)
    if not self._is_regime_stable(regime_features):
        self.logger.debug("Regime not stable enough for entry")
        return None
    
    # 3. Check risk suppression
    if regime_features.get('risk_suppressed', False):
        self.logger.debug("Risk suppressed, skipping entry")
        return None
    
    # 4. SIMPLIFIED: Determine direction based on regime alone
    direction = None
    
    if regime in [GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND]:
        direction = 'long'
        self.logger.info(f"LONG signal based on {regime} regime")
    elif regime in [GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_WEAK_BEAR_TREND]:
        direction = 'short'
        self.logger.info(f"SHORT signal based on {regime} regime")
    
    if direction is None:
        return None
    
    # 5. Calculate entry confidence (simplified)
    confidence = regime_features.get('current_confidence', 0.6)
    
    # 6. Prepare entry decision
    entry_decision = {
        'direction': direction,
        'confidence': confidence,
        'position_size': self._calculate_position_size(signals, confidence),
        'entry_reason': 'regime_based_entry',
        'regime': regime,
        'regime_confidence': regime_features.get('current_confidence', 0.0),
        'regime_persistence': regime_features.get('state_persistence', 0.0),
        'is_trending': regime_features.get('is_trending', False)
    }
    
    self.logger.info(
        f"REGIME-ONLY Entry signal: {direction} "
        f"(confidence: {confidence:.2f}, regime: {regime})"
    )
    
    return entry_decision

# Apply patch
ModernTFV3Strategy.evaluate_entry = regime_only_evaluate_entry

def main():
    print("=== Testing Regime-Only Trading ===\n")
    print("⚠️  WARNING: Using simplified entry logic (regime-only)")
    print("This is for testing purposes only!\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Import here to get patched version
    from hyperliquid_bot.backtester.backtester import Backtester
    
    # Run backtest
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 17)
    
    print(f"Running backtest from {start_date} to {end_date}...")
    
    try:
        # Create and run backtester
        backtester = Backtester(config)
        backtester.run(start_date, end_date)
        
        # Get results - backtester doesn't expose trades directly
        # But it prints a summary, so let's check that
        trades = getattr(backtester, 'trades', [])
        if hasattr(backtester, 'portfolio_tracker'):
            trades = backtester.portfolio_tracker.executed_trades
            portfolio_history = backtester.portfolio_tracker.portfolio_history
        else:
            trades = []
            portfolio_history = []
        regime_history = []
        
        print(f"\n✅ Backtest completed!")
        print(f"📊 Total trades: {len(trades)}")
        
        if len(trades) > 0:
            print(f"\n🎉 SUCCESS! Generated {len(trades)} trades with regime-only logic")
            
            # Show trade summary
            long_trades = [t for t in trades if t['direction'] == 'long']
            short_trades = [t for t in trades if t['direction'] == 'short']
            
            print(f"\nTrade breakdown:")
            print(f"  Long trades: {len(long_trades)}")
            print(f"  Short trades: {len(short_trades)}")
            
            # Show first few trades
            print(f"\nFirst 5 trades:")
            for i, trade in enumerate(trades[:5]):
                print(f"  {i+1}. {trade['entry_time']}: {trade['direction'].upper()} "
                      f"@ ${trade['entry_price']:.2f} (regime: {trade.get('regime', 'unknown')})")
            
            print(f"\n📌 CONCLUSION:")
            print(f"The system WORKS when we simplify entry logic!")
            print(f"The issue is that EMAs never cross bearish during this period.")
            print(f"We need to either:")
            print(f"  1. Test with a longer date range with more price movement")
            print(f"  2. Adjust the entry logic to be less strict")
            print(f"  3. Use different technical indicators")
            
        else:
            print(f"\n❌ Still no trades even with regime-only logic")
            print(f"This suggests a deeper issue with regime detection or stability")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()