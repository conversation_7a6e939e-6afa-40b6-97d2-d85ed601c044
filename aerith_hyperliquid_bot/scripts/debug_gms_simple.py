#!/usr/bin/env python3
"""
Simple GMS Detection Debug
Direct analysis of signal values without complex config loading.
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path

def analyze_gms_logic():
    """Analyze the signals and manually apply GMS logic."""
    # Find the latest signals file
    import glob
    files = glob.glob('/Users/<USER>/Desktop/trading_bot_/logs/backtest_signals_*.parquet')
    if not files:
        print("No signals files found")
        return
    
    latest_file = max(files)
    print(f"Loading {latest_file}")
    df = pd.read_parquet(latest_file)
    
    print(f"Total rows: {len(df)}")
    print(f"Columns: {list(df.columns)}")
    
    # Manual thresholds from our config
    vol_thresh_low = 0.005   # Low volatility threshold
    vol_thresh_high = 0.015  # High volatility threshold
    mom_thresh_weak = 5.0    # Weak momentum threshold
    mom_thresh_strong = 15.0 # Strong momentum threshold
    
    print(f"\nUsing thresholds:")
    print(f"  Vol Low:     {vol_thresh_low:.3f}")
    print(f"  Vol High:    {vol_thresh_high:.3f}")
    print(f"  Mom Weak:    {mom_thresh_weak:.1f}")
    print(f"  Mom Strong:  {mom_thresh_strong:.1f}")
    
    # Sample first 1000 rows for analysis
    sample = df.head(1000).copy()
    
    # Manual classification
    classifications = []
    detailed_analysis = []
    
    for idx, row in sample.iterrows():
        # Extract signal values
        atr_pct = row.get('atr_percent', np.nan)
        ma_slope = row.get('ma_slope', np.nan)
        obi_5 = row.get('obi_smoothed_5', np.nan)
        spread_mean = row.get('spread_mean', np.nan)
        spread_std = row.get('spread_std', np.nan)
        
        # Check for missing signals
        if any(pd.isna(val) for val in [atr_pct, ma_slope, obi_5, spread_mean, spread_std]):
            state = "Unknown"
        else:
            # Volatility classification
            if atr_pct >= vol_thresh_high:
                vol_regime = "High"
            elif atr_pct <= vol_thresh_low:
                vol_regime = "Low"
            else:
                vol_regime = "Medium"
            
            # Momentum classification
            abs_ma_slope = abs(ma_slope)
            if abs_ma_slope >= mom_thresh_strong:
                mom_regime = "Strong"
            elif abs_ma_slope <= mom_thresh_weak:
                mom_regime = "Weak"
            else:
                mom_regime = "Medium"
            
            # Direction classification
            direction = "Bull" if ma_slope > 0 else "Bear"
            
            # Simple OBI confirmation (aligned with direction)
            if direction == "Bull":
                obi_confirms = obi_5 > 0  # Positive OBI for bull
            else:
                obi_confirms = obi_5 < 0  # Negative OBI for bear
            
            # Combine classifications (simplified GMS logic)
            if vol_regime == "High":
                if mom_regime == "Strong" and obi_confirms:
                    state = f"Strong_{direction}_Trend"
                else:
                    state = "High_Vol_Range"
            elif vol_regime == "Low":
                if mom_regime == "Weak":
                    state = "Low_Vol_Range"
                else:
                    state = f"Weak_{direction}_Trend"
            else:  # Medium volatility
                if mom_regime == "Strong" and obi_confirms:
                    state = f"Strong_{direction}_Trend"
                elif mom_regime == "Medium":
                    state = f"Weak_{direction}_Trend"
                else:
                    state = "Uncertain"
        
        classifications.append(state)
        detailed_analysis.append({
            'timestamp': row.get('timestamp', idx),
            'atr_pct': atr_pct,
            'ma_slope': ma_slope,
            'abs_ma_slope': abs(ma_slope) if not pd.isna(ma_slope) else np.nan,
            'obi_5': obi_5,
            'vol_regime': vol_regime if 'vol_regime' in locals() else 'Unknown',
            'mom_regime': mom_regime if 'mom_regime' in locals() else 'Unknown',
            'direction': direction if 'direction' in locals() else 'Unknown',
            'obi_confirms': obi_confirms if 'obi_confirms' in locals() else False,
            'final_state': state
        })
    
    # Summary statistics
    from collections import Counter
    state_counts = Counter(classifications)
    
    print(f"\n=== SIGNAL ANALYSIS SUMMARY ===")
    print(f"Sample size: {len(sample)} rows")
    
    print(f"\nATR Percent statistics:")
    atr_stats = sample['atr_percent'].describe()
    for stat, value in atr_stats.items():
        print(f"  {stat:8s}: {value:.6f}")
    
    print(f"\nMA Slope statistics:")
    slope_stats = sample['ma_slope'].describe()
    for stat, value in slope_stats.items():
        print(f"  {stat:8s}: {value:.6f}")
    
    print(f"\nATR classification breakdown:")
    atr_low = (sample['atr_percent'] <= vol_thresh_low).sum()
    atr_high = (sample['atr_percent'] >= vol_thresh_high).sum()
    atr_medium = len(sample) - atr_low - atr_high
    print(f"  Low vol (≤{vol_thresh_low:.3f}):    {atr_low:3d} ({atr_low/len(sample)*100:.1f}%)")
    print(f"  High vol (≥{vol_thresh_high:.3f}):   {atr_high:3d} ({atr_high/len(sample)*100:.1f}%)")
    print(f"  Medium vol:                  {atr_medium:3d} ({atr_medium/len(sample)*100:.1f}%)")
    
    print(f"\nMomentum classification breakdown:")
    slope_abs = sample['ma_slope'].abs()
    mom_weak = (slope_abs <= mom_thresh_weak).sum()
    mom_strong = (slope_abs >= mom_thresh_strong).sum()
    mom_medium = len(sample) - mom_weak - mom_strong
    print(f"  Weak mom (≤{mom_thresh_weak:.1f}):     {mom_weak:3d} ({mom_weak/len(sample)*100:.1f}%)")
    print(f"  Strong mom (≥{mom_thresh_strong:.1f}):   {mom_strong:3d} ({mom_strong/len(sample)*100:.1f}%)")
    print(f"  Medium mom:                 {mom_medium:3d} ({mom_medium/len(sample)*100:.1f}%)")
    
    print(f"\nFinal state distribution:")
    for state, count in state_counts.most_common():
        print(f"  {state:<25}: {count:3d} ({count/len(sample)*100:.1f}%)")
    
    # Show some specific examples
    print(f"\n=== DETAILED EXAMPLES ===")
    analysis_df = pd.DataFrame(detailed_analysis)
    
    # Show examples of each major category
    trend_states = analysis_df[analysis_df['final_state'].str.contains('Trend', na=False)]
    range_states = analysis_df[analysis_df['final_state'].str.contains('Range', na=False)]
    uncertain_states = analysis_df[analysis_df['final_state'] == 'Uncertain']
    unknown_states = analysis_df[analysis_df['final_state'] == 'Unknown']
    
    print(f"\nTrend states found: {len(trend_states)}")
    if len(trend_states) > 0:
        print("Sample trend examples:")
        for _, row in trend_states.head(3).iterrows():
            print(f"  ATR: {row['atr_pct']:.4f} ({row['vol_regime']}) | "
                  f"Slope: {row['ma_slope']:7.2f} ({row['mom_regime']}) | "
                  f"OBI: {row['obi_5']:6.3f} (confirms: {row['obi_confirms']}) | "
                  f"State: {row['final_state']}")
    
    print(f"\nRange states found: {len(range_states)}")
    print(f"Uncertain states found: {len(uncertain_states)}")
    print(f"Unknown states found: {len(unknown_states)}")
    
    # Check for missing data
    print(f"\n=== MISSING SIGNAL ANALYSIS ===")
    required_signals = ['atr_percent', 'ma_slope', 'obi_smoothed_5', 'spread_mean', 'spread_std']
    for signal in required_signals:
        missing = sample[signal].isna().sum()
        print(f"  {signal:<15}: {missing:3d} missing ({missing/len(sample)*100:.1f}%)")
    
    return analysis_df

if __name__ == "__main__":
    analyze_gms_logic()