#!/usr/bin/env python3
"""
Quick test to verify modern system fixes without full backtest overhead.
Tests just the data loading and signal generation pipeline.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from datetime import datetime, timedelta
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.data_loader import ModernDataLoader
from hyperliquid_bot.modern.registry import get_modern_detector
from hyperliquid_bot.modern.regime_state_manager import RegimeStateManager

# Set minimal logging
logging.basicConfig(level=logging.INFO)

def main():
    print("=== Modern System Quick Test ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Test 1: Data loading with features
    print("1. Testing feature-rich hourly data loading...")
    loader = ModernDataLoader(config)
    
    start = datetime(2024, 1, 1)
    end = datetime(2024, 1, 2)
    
    # Test the new method
    hourly_features = loader.load_hourly_features(start, end)
    
    print(f"   - Loaded {len(hourly_features)} hourly bars")
    print(f"   - Columns: {len(hourly_features.columns)}")
    
    # Check key fields
    key_fields = ['close', 'volume_imbalance', 'spread_mean', 'atr_14_sec']
    print("\n   Key fields present:")
    for field in key_fields:
        if field in hourly_features.columns:
            sample_val = hourly_features[field].iloc[0] if not hourly_features.empty else 'N/A'
            if isinstance(sample_val, (int, float)):
                print(f"   - {field}: ✓ (sample: {sample_val:.4f})")
            else:
                print(f"   - {field}: ✓ (sample: {sample_val})")
        else:
            print(f"   - {field}: ✗")
    
    # Test 2: Regime detection
    print("\n2. Testing regime detection...")
    regime_manager = RegimeStateManager(mode='backtest')
    detector = get_modern_detector(config.regime.detector_type, config=config)
    
    # Load 1s features for regime
    features_1s = loader.load_features_1s(start, start + timedelta(hours=1))
    
    if not features_1s.empty:
        # Test regime detection on first minute
        test_time = start + timedelta(minutes=1)
        features_slice = features_1s[features_1s.index <= test_time].iloc[-60:]
        
        if len(features_slice) >= 60:
            latest_features = features_slice.iloc[-1].to_dict()
            regime_state = detector.detect_regime(latest_features, test_time)
            print(f"   - Regime state: {regime_state}")
        else:
            print(f"   - Insufficient data for regime detection")
    
    # Test 3: Check configuration
    print("\n3. Configuration check:")
    print(f"   - Risk per trade: {config.portfolio.risk_per_trade:.2%}")
    print(f"   - Regime detector: {config.regime.detector_type}")
    print(f"   - Regime cadence: 60s")
    print(f"   - Trading evaluation: hourly")
    
    print("\n✅ Quick test complete!")
    print("\nNext steps:")
    print("- If data loading works, run full backtest")
    print("- Check regime thresholds if no trades generated")
    print("- Review strategy entry conditions")

if __name__ == "__main__":
    main()