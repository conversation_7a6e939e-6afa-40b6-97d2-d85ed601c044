#!/usr/bin/env python3
"""
Test script to verify state mapping fix.
"""

import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.core.detector_factory import get_regime_detector


def test_state_mapping():
    """Test that states activate strategies properly."""
    print("Testing State Mapping Fix")
    print("="*50)
    
    # Load config
    config = load_config("configs/base.yaml")
    
    # Create detector
    detector = get_regime_detector(config)
    print(f"Created detector: {type(detector).__name__}")
    print(f"Mode: {getattr(detector, 'detector_mode', 'N/A')}")
    print(f"Adaptive thresholds: {getattr(detector, 'adaptive_vol_threshold', None) is not None}")
    
    # Test cases that previously returned TIGHT_SPREAD
    test_cases = [
        {
            'name': 'Low Vol + Weak Momentum',
            'signals': {
                'timestamp': 1640995200,
                'atr_percent': 0.50,  # Below 0.55 threshold (low vol)
                'ma_slope': 25.0,     # Below 50.0 threshold (weak momentum)
                'obi_smoothed_5': 0.05,
                'spread_mean': 0.000040,  # Below 0.000045 (tight)
                'spread_std': 0.000030,   # Below 0.000050 (normal)
                'close': 50000.0
            }
        },
        {
            'name': 'Low Vol + Medium Momentum Bull',
            'signals': {
                'timestamp': 1640995200,
                'atr_percent': 0.50,  # Below 0.55 (low vol)
                'ma_slope': 75.0,     # Between 50-100 (medium momentum)
                'obi_smoothed_5': 0.15,
                'spread_mean': 0.000040,  # Tight spread
                'spread_std': 0.000030,
                'close': 50000.0
            }
        },
        {
            'name': 'Normal conditions',
            'signals': {
                'timestamp': 1640995200,
                'atr_percent': 0.75,  # Between thresholds (medium vol)
                'ma_slope': 150.0,    # Above 100 (strong momentum)
                'obi_smoothed_5': 0.25,  # Strong OBI
                'spread_mean': 0.000050,
                'spread_std': 0.000060,
                'close': 50000.0
            }
        }
    ]
    
    states_that_activate_strategies = [
        'Strong_Bull_Trend', 'Weak_Bull_Trend', 
        'Strong_Bear_Trend', 'Weak_Bear_Trend',
        'High_Vol_Range', 'Low_Vol_Range',
        'Uncertain'
    ]
    
    states_that_disable_strategies = [
        'TIGHT_SPREAD', 'THIN_LIQUIDITY', 'SKEWED_BOOK'
    ]
    
    print(f"\nStates that activate strategies: {states_that_activate_strategies}")
    print(f"States that disable strategies: {states_that_disable_strategies}")
    
    success_count = 0
    for i, test_case in enumerate(test_cases):
        print(f"\n--- Test Case {i+1}: {test_case['name']} ---")
        
        try:
            regime = detector.get_regime(test_case['signals'])
            if isinstance(regime, dict):
                state = regime['state']
            else:
                state = regime
                
            print(f"Detected state: {state}")
            
            if state in states_that_activate_strategies:
                print(f"✅ Good: '{state}' should activate strategies")
                success_count += 1
            elif state in states_that_disable_strategies:
                print(f"❌ Bad: '{state}' disables ALL strategies")
            else:
                print(f"⚠️  Unknown: '{state}' not in known categories")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print(f"\n" + "="*50)
    print(f"Success rate: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
    
    if success_count == len(test_cases):
        print("✅ All test cases now return strategy-activating states!")
        print("\nExpected improvement:")
        print("- Should see 'Active strategy names: [...]' with actual strategies")
        print("- Trade count should increase significantly")
        print("- Performance should improve towards 184 trade baseline")
    else:
        print("❌ Some test cases still return problematic states")
    
    return success_count == len(test_cases)


if __name__ == "__main__":
    test_state_mapping()