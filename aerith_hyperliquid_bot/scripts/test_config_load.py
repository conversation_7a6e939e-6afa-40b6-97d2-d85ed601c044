#!/usr/bin/env python3
"""
Test if config is loading correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hyperliquid_bot.config.settings import load_config

def main():
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    print("=== Config Test ===")
    print(f"Risk per trade: {config.portfolio.risk_per_trade}")
    print(f"Detector type: {config.regime.detector_type}")
    
    # Check if adaptive thresholds setting exists
    if hasattr(config.regime, 'gms_use_adaptive_thresholds'):
        print(f"Adaptive thresholds: {config.regime.gms_use_adaptive_thresholds}")
    else:
        print("Adaptive thresholds: NOT FOUND IN CONFIG")
    
    # Show all regime config attributes
    print("\nAll regime config attributes:")
    for attr in dir(config.regime):
        if not attr.startswith('_'):
            value = getattr(config.regime, attr)
            if not callable(value):
                print(f"  {attr}: {value}")

if __name__ == "__main__":
    main()