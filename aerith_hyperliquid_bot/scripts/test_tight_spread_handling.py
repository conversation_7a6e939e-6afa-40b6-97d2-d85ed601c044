#!/usr/bin/env python3
"""
Test script to verify TIGHT_SPREAD handling in the GMS state mapping system.
This script simulates the flow from detector output through mapping to strategy activation.
"""

import logging
import sys
from pathlib import Path
import yaml

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger('test_tight_spread')

# Ensure the package root is in the Python path
project_root = Path(__file__).parent.parent.resolve()
sys.path.insert(0, str(project_root))

# Import necessary components
from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.utils.state_mapping import map_gms_state, get_state_map
from hyperliquid_bot.strategies.evaluator import StrategyEvaluator

def load_config():
    """Load configuration with TIGHT_SPREAD enabled"""
    config_path = project_root / "configs" / "base.yaml"
    if not config_path.exists():
        logger.error(f"Configuration file not found: {config_path}")
        sys.exit(1)
        
    with open(config_path, 'r') as f:
        config_dict = yaml.safe_load(f)
    
    # Verify tight spread is enabled in config
    tight_spread_pct = config_dict.get('regime', {}).get('gms_tight_spread_fallback_percentile')
    logger.info(f"Current tight_spread_fallback_percentile setting: {tight_spread_pct}")
    
    config = Config(**config_dict)
    return config

def test_tight_spread_mapping():
    """Test how TIGHT_SPREAD is mapped in the state mapping system"""
    # 1. Load the state map
    state_map = get_state_map()
    logger.info(f"Loaded state map: {state_map}")
    
    # 2. Check if TIGHT_SPREAD is in the mapping
    if 'TIGHT_SPREAD' in state_map:
        mapped_state = state_map['TIGHT_SPREAD']
        logger.info(f"✓ TIGHT_SPREAD is mapped to: {mapped_state}")
    else:
        logger.error("✗ TIGHT_SPREAD is not present in the state mapping configuration!")
        
    # 3. Test the map_gms_state function directly
    mapped_result = map_gms_state('TIGHT_SPREAD')
    logger.info(f"map_gms_state('TIGHT_SPREAD') returns: {mapped_result}")
    
    # 4. Verify it maps to CHOP as expected
    if mapped_result == 'CHOP':
        logger.info("✓ TIGHT_SPREAD correctly maps to CHOP")
    else:
        logger.error(f"✗ Unexpected mapping: TIGHT_SPREAD maps to {mapped_result}, expected CHOP")

def test_strategy_activation():
    """Test how strategies are activated in response to TIGHT_SPREAD"""
    config = load_config()
    
    # Create evaluator
    evaluator = StrategyEvaluator(config)
    
    # Set up a fake strategy dictionary
    evaluator.strategies = {
        'trend_following': None,  # Placeholder
        'mean_variance': None     # Placeholder
    }
    
    # Test cases:
    test_cases = [
        {'mapping_active': True, 'regime': 'TIGHT_SPREAD', 'expected': ['mean_variance'], 'desc': 'TIGHT_SPREAD with mapping ON'},
        {'mapping_active': False, 'regime': 'TIGHT_SPREAD', 'expected': [], 'desc': 'TIGHT_SPREAD with mapping OFF'}
    ]
    
    for case in test_cases:
        # Set the mapping flag for this test
        evaluator.gms_mapping_active = case['mapping_active']
        
        # Get active strategies for the regime
        active_strategies = evaluator.get_active_strategies(
            detector_type='granular_microstructure',
            current_regime=case['regime']
        )
        
        logger.info(f"\nTest: {case['desc']}")
        logger.info(f"  Mapping active: {case['mapping_active']}")
        logger.info(f"  Actual active strategies: {active_strategies}")
        logger.info(f"  Expected active strategies: {case['expected']}")
        
        if set(active_strategies) == set(case['expected']):
            logger.info("  ✓ PASSED: Active strategies match expected")
        else:
            logger.error("  ✗ FAILED: Active strategies don't match expected")

def main():
    """Run all tests"""
    logger.info("=== TESTING TIGHT_SPREAD HANDLING ===")
    
    # Test state mapping
    logger.info("\n--- Testing TIGHT_SPREAD State Mapping ---")
    test_tight_spread_mapping()
    
    # Test strategy activation logic
    logger.info("\n--- Testing Strategy Activation for TIGHT_SPREAD ---")
    test_strategy_activation()
    
    logger.info("\n=== TESTING COMPLETE ===")

if __name__ == "__main__":
    main()
