#!/usr/bin/env python3
"""
Test Enhanced Regime Detector
=============================

This script verifies that the enhanced detector:
1. Produces identical regimes to legacy detector
2. Adds quality scoring without breaking compatibility
3. Uses correct thresholds from config
"""

import sys
import logging
from pathlib import Path
from datetime import datetime
import json

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.legacy.detector import LegacyGranularMicrostructureDetector
from hyperliquid_bot.modern.enhanced_regime_detector import EnhancedRegimeDetector


def setup_logging():
    """Configure logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def create_test_signals():
    """Create test signal sets to verify detector behavior."""
    test_cases = [
        {
            'name': 'Strong Bull Trend',
            'signals': {
                'atr_percent': 0.007,      # Normal volatility
                'ma_slope': 150.0,         # Strong positive momentum
                'obi_smoothed_5': 0.25,    # Strong buy pressure
                'spread_mean': 0.00003,    # Tight spread
                'spread_std': 0.00003,     # Low spread volatility
                'volume': 1000000,
                'close': 50000
            },
            'expected_regime': 'Strong_Bull_Trend'
        },
        {
            'name': 'Weak Bear Trend',
            'signals': {
                'atr_percent': 0.006,
                'ma_slope': -60.0,         # Weak negative momentum
                'obi_smoothed_5': -0.08,   # Weak sell pressure
                'spread_mean': 0.00006,
                'spread_std': 0.00004,
                'volume': 800000,
                'close': 49000
            },
            'expected_regime': 'Weak_Bear_Trend'
        },
        {
            'name': 'High Volatility Range',
            'signals': {
                'atr_percent': 0.012,      # High volatility
                'ma_slope': 30.0,          # Low momentum
                'obi_smoothed_5': 0.05,
                'spread_mean': 0.00008,
                'spread_std': 0.00007,     # High spread volatility
                'volume': 1200000,
                'close': 48000
            },
            'expected_regime': 'High_Vol_Range'
        },
        {
            'name': 'Low Volatility Range',
            'signals': {
                'atr_percent': 0.004,      # Low volatility
                'ma_slope': 10.0,          # Very low momentum
                'obi_smoothed_5': 0.02,
                'spread_mean': 0.00002,    # Very tight spread
                'spread_std': 0.00002,
                'volume': 500000,
                'close': 51000
            },
            'expected_regime': 'Low_Vol_Range'
        }
    ]
    
    return test_cases


def compare_detectors(legacy_detector, enhanced_detector, test_cases):
    """Compare outputs of legacy and enhanced detectors."""
    print("\n" + "="*60)
    print("Comparing Legacy vs Enhanced Detector")
    print("="*60)
    
    all_match = True
    results = []
    
    for test in test_cases:
        print(f"\nTest Case: {test['name']}")
        print("-" * 40)
        
        signals = test['signals']
        
        # Get legacy regime
        legacy_regime = legacy_detector.detect_regime(signals)
        legacy_confidence = legacy_detector.get_confidence()
        
        # Get enhanced regime (should be identical)
        enhanced_regime = enhanced_detector.detect_regime(signals)
        enhanced_confidence = enhanced_detector.get_confidence()
        
        # Get enhanced evaluation with quality
        enhanced_eval = enhanced_detector.evaluate_with_quality(signals)
        
        # Check if regimes match
        regime_match = legacy_regime == enhanced_regime
        confidence_match = abs(legacy_confidence - enhanced_confidence) < 0.01
        
        if not regime_match:
            all_match = False
        
        # Display results
        print(f"  Legacy Regime:    {legacy_regime} (confidence: {legacy_confidence:.2f})")
        print(f"  Enhanced Regime:  {enhanced_regime} (confidence: {enhanced_confidence:.2f})")
        print(f"  Regime Match:     {'✓' if regime_match else '✗'}")
        print(f"\n  Quality Score:    {enhanced_eval['quality']:.3f}")
        print(f"  Quality Details:")
        for key, value in enhanced_eval['quality_details'].items():
            print(f"    - {key}: {value:.3f}")
        print(f"  Execute Decision: {enhanced_eval['execute']}")
        
        # Store results
        results.append({
            'test_name': test['name'],
            'legacy_regime': legacy_regime,
            'enhanced_regime': enhanced_regime,
            'regime_match': regime_match,
            'quality_score': enhanced_eval['quality'],
            'quality_details': enhanced_eval['quality_details'],
            'execute': enhanced_eval['execute']
        })
    
    return all_match, results


def verify_thresholds(detector, config):
    """Verify detector is using correct thresholds."""
    print("\n" + "="*60)
    print("Verifying Thresholds")
    print("="*60)
    
    # Check if enhanced detector has legacy core
    if hasattr(detector, 'legacy_detector'):
        core = detector.legacy_detector
        print("Enhanced detector using legacy core ✓")
    else:
        core = detector
        print("Direct detector access")
    
    # Verify key thresholds
    checks = [
        ('vol_high_thresh', 0.0092, core.vol_high_thresh),
        ('vol_low_thresh', 0.0055, core.vol_low_thresh),
        ('mom_strong_thresh', 100.0, core.mom_strong_thresh),
        ('mom_weak_thresh', 50.0, core.mom_weak_thresh),
        ('obi_strong_confirm_thresh', 0.2, core.obi_strong_confirm_thresh),
        ('obi_weak_confirm_thresh', 0.05, core.obi_weak_confirm_thresh),
    ]
    
    all_correct = True
    for name, expected, actual in checks:
        match = abs(expected - actual) < 0.0001
        if not match:
            all_correct = False
        print(f"  {name}: {actual:.4f} (expected: {expected:.4f}) {'✓' if match else '✗'}")
    
    return all_correct


def main():
    """Run enhanced detector tests."""
    setup_logging()
    
    print("\n" + "="*60)
    print("Enhanced Regime Detector Test Suite")
    print("="*60)
    
    # Load configurations
    print("\nLoading configurations...")
    
    # For testing, we'll use the modern config as base
    base_config = load_config("configs/overrides/modern_system_v2_complete.yaml")
    print("✓ Base config loaded")
    
    # Override with legacy thresholds for proper testing
    # This ensures both detectors use identical thresholds
    base_config.regime.gms_vol_high_thresh = 0.0092
    base_config.regime.gms_vol_low_thresh = 0.0055
    base_config.regime.gms_mom_strong_thresh = 100.0
    base_config.regime.gms_mom_weak_thresh = 50.0
    base_config.regime.gms_spread_std_high_thresh = 0.000050
    base_config.regime.gms_spread_mean_low_thresh = 0.000045
    base_config.microstructure.gms_obi_strong_confirm_thresh = 0.2
    base_config.microstructure.gms_obi_weak_confirm_thresh = 0.05
    
    print("✓ Applied legacy thresholds to config")
    
    # Create detectors
    print("\nInitializing detectors...")
    legacy_detector = LegacyGranularMicrostructureDetector(base_config)
    enhanced_detector = EnhancedRegimeDetector(base_config)
    
    # Verify thresholds
    print("\nVerifying enhanced detector thresholds...")
    thresholds_correct = verify_thresholds(enhanced_detector, base_config)
    
    if not thresholds_correct:
        print("\n⚠️  WARNING: Thresholds don't match legacy values!")
        print("This will invalidate A/B testing. Fix config before proceeding.")
        return
    
    # Create test cases
    test_cases = create_test_signals()
    
    # Compare detectors
    regimes_match, results = compare_detectors(
        legacy_detector, enhanced_detector, test_cases
    )
    
    # Summary
    print("\n" + "="*60)
    print("Test Summary")
    print("="*60)
    
    print(f"\nThresholds Correct: {'✓' if thresholds_correct else '✗'}")
    print(f"All Regimes Match: {'✓' if regimes_match else '✗'}")
    
    # Quality score statistics
    quality_scores = [r['quality_score'] for r in results]
    print(f"\nQuality Score Statistics:")
    print(f"  Average: {sum(quality_scores)/len(quality_scores):.3f}")
    print(f"  Min: {min(quality_scores):.3f}")
    print(f"  Max: {max(quality_scores):.3f}")
    
    # Execution decisions
    execute_count = sum(1 for r in results if r['execute'])
    print(f"\nExecution Decisions:")
    print(f"  Execute: {execute_count}/{len(results)}")
    print(f"  Skip: {len(results) - execute_count}/{len(results)}")
    
    # Save results
    output_file = "enhanced_detector_test_results.json"
    with open(output_file, 'w') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'thresholds_correct': thresholds_correct,
            'regimes_match': regimes_match,
            'test_results': results
        }, f, indent=2)
    
    print(f"\nResults saved to {output_file}")
    
    if thresholds_correct and regimes_match:
        print("\n✅ Enhanced detector is ready for A/B testing!")
        print("\nNext steps:")
        print("1. Run parallel backtests with legacy vs enhanced")
        print("2. Compare trade counts and performance")
        print("3. Verify quality scoring improves entry timing")
    else:
        print("\n❌ Enhanced detector needs fixes before use")


if __name__ == "__main__":
    main()