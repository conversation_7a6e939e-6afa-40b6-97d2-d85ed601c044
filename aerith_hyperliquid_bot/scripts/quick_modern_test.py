#!/usr/bin/env python3
"""
Quick Modern System Test
========================

Run a short backtest to see if trades are being generated.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from datetime import datetime, timedelta
import json
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine

def main():
    print("=== Quick Modern System Test ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Set a short date range for quick testing
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 17)  # Just 3 days
    
    print(f"Testing from {start_date} to {end_date}")
    
    # Create and run backtester
    backtester = ModernBacktestEngine(config, start_date, end_date)
    
    try:
        print("\nRunning backtest...")
        results = backtester.run_backtest()
        
        # Check results
        num_trades = len(results['trades'])
        print(f"\n✅ Backtest completed!")
        print(f"📊 Total trades: {num_trades}")
        
        if num_trades > 0:
            print("\n🎉 SUCCESS! The modern system is generating trades!")
            print("\nFirst few trades:")
            for i, trade in enumerate(results['trades'][:5]):
                print(f"  Trade {i+1}: {trade['direction']} at {trade['timestamp']}, "
                      f"size: {trade['position_size']:.4f}, "
                      f"confidence: {trade.get('confidence', 0):.2f}")
            
            # Calculate performance
            total_return = results['performance']['total_return']
            win_rate = results['performance']['win_rate']
            print(f"\nPerformance:")
            print(f"  Total return: {total_return:.2%}")
            print(f"  Win rate: {win_rate:.2%}")
        else:
            print("\n❌ Still no trades generated. Debugging needed.")
            
            # Check regime history
            if 'regime_history' in results and results['regime_history']:
                print(f"\nRegime history entries: {len(results['regime_history'])}")
                # Show unique regimes
                regimes = set(r['regime'] for r in results['regime_history'])
                print(f"Unique regimes seen: {regimes}")
                
                # Check for trading regimes
                trading_regimes = {'Weak_Bull_Trend', 'Weak_Bear_Trend', 
                                 'Strong_Bull_Trend', 'Strong_Bear_Trend'}
                trading_regime_count = sum(1 for r in results['regime_history'] 
                                         if r['regime'] in trading_regimes)
                print(f"Trading regime hours: {trading_regime_count}/{len(results['regime_history'])}")
        
        # Save results
        output_file = f"quick_modern_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\nResults saved to: {output_file}")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()