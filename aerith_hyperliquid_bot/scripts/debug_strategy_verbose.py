#!/usr/bin/env python3
"""
Debug Strategy with Extreme Verbosity
=====================================

Trace every single decision point in the strategy.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.tf_v3_modern import ModernTFV3Strategy

# Monkey patch with extreme verbosity
original_evaluate_entry = ModernTFV3Strategy.evaluate_entry
original_is_regime_stable = ModernTFV3Strategy._is_regime_stable

def verbose_is_regime_stable(self, regime_features):
    print("\n  📋 Checking regime stability...")
    
    # Check confidence
    confidence = regime_features.get('current_confidence', 0.0)
    print(f"    Confidence: {confidence:.3f} (min: {self.min_regime_confidence})")
    if confidence < self.min_regime_confidence:
        print(f"    ❌ Confidence too low!")
        return False
    
    # Check recent transitions
    recent_transitions = regime_features.get('recent_transitions', 0)
    state_changes_equiv = recent_transitions * 6
    print(f"    Recent transitions: {recent_transitions} (scaled to {state_changes_equiv}/hr, max: {self.max_regime_changes_1h})")
    if state_changes_equiv > self.max_regime_changes_1h:
        print(f"    ❌ Too many state changes!")
        return False
    
    # Check state persistence
    state_persistence = regime_features.get('state_persistence', 0.0)
    print(f"    State persistence: {state_persistence:.3f} (min: 0.5)")
    if state_persistence < 0.5:
        print(f"    ❌ State not persistent enough!")
        return False
    
    # Check is_trending
    is_trending = regime_features.get('is_trending', False)
    print(f"    Is trending: {is_trending}")
    
    print(f"    ✅ Regime is stable!")
    return True

def verbose_evaluate_entry(self, signals, regime):
    print("\n" + "="*60)
    print(f"🎯 VERBOSE: evaluate_entry called at {signals.get('timestamp', 'unknown')}")
    print("="*60)
    
    # Extract regime_features
    regime_features = signals.get('regime_features', {})
    
    print(f"\n1️⃣ Regime Check:")
    print(f"   Current regime: {regime}")
    print(f"   Allowed regimes: {self.trend_states}")
    if regime not in self.trend_states:
        print(f"   ❌ Regime not in trend states!")
        return None
    print(f"   ✅ Regime allowed")
    
    print(f"\n2️⃣ Regime Features:")
    if not regime_features:
        print(f"   ❌ No regime_features in signals!")
        return None
    
    for key, value in regime_features.items():
        print(f"   {key}: {value}")
    
    # Check stability with verbose version
    stable = verbose_is_regime_stable(self, regime_features)
    if not stable:
        print(f"\n   ❌ Regime not stable enough for entry!")
        return None
    
    print(f"\n3️⃣ Risk Suppression Check:")
    risk_suppressed = regime_features.get('risk_suppressed', False)
    print(f"   Risk suppressed: {risk_suppressed}")
    if risk_suppressed:
        print(f"   ❌ Risk is suppressed!")
        return None
    print(f"   ✅ Risk not suppressed")
    
    print(f"\n4️⃣ Required Indicators Check:")
    ema_fast = signals.get('ema_fast')
    ema_slow = signals.get('ema_slow')
    ema_baseline = signals.get('ema_baseline')
    close_price = signals.get('close')
    
    print(f"   EMA Fast: {ema_fast}")
    print(f"   EMA Slow: {ema_slow}")
    print(f"   EMA Baseline: {ema_baseline}")
    print(f"   Close: {close_price}")
    
    if any(pd.isna(x) for x in [ema_fast, ema_slow, ema_baseline, close_price]):
        print(f"   ❌ Missing required indicators!")
        return None
    print(f"   ✅ All indicators present")
    
    print(f"\n5️⃣ Entry Signal Detection:")
    
    # Bullish conditions
    ema_bull = ema_fast > ema_slow
    price_bull = close_price > ema_baseline
    bullish_cross = ema_bull and price_bull
    
    print(f"   Bullish checks:")
    print(f"     EMA Fast ({ema_fast:.2f}) > EMA Slow ({ema_slow:.2f}): {ema_bull}")
    print(f"     Close ({close_price:.2f}) > Baseline ({ema_baseline:.2f}): {price_bull}")
    print(f"     Combined bullish: {bullish_cross}")
    
    # Bearish conditions
    ema_bear = ema_fast < ema_slow
    price_bear = close_price < ema_baseline
    bearish_cross = ema_bear and price_bear
    
    print(f"   Bearish checks:")
    print(f"     EMA Fast ({ema_fast:.2f}) < EMA Slow ({ema_slow:.2f}): {ema_bear}")
    print(f"     Close ({close_price:.2f}) < Baseline ({ema_baseline:.2f}): {price_bear}")
    print(f"     Combined bearish: {bearish_cross}")
    
    # Determine direction
    direction = None
    if bullish_cross and regime in ['Weak_Bull_Trend', 'Strong_Bull_Trend']:
        direction = 'long'
        print(f"   ✅ LONG signal confirmed (bullish cross + bull regime)")
    elif bearish_cross and regime in ['Weak_Bear_Trend', 'Strong_Bear_Trend']:
        direction = 'short'
        print(f"   ✅ SHORT signal confirmed (bearish cross + bear regime)")
    else:
        print(f"   ❌ No valid entry signal")
        print(f"      Bullish cross: {bullish_cross}, Bull regime: {regime in ['Weak_Bull_Trend', 'Strong_Bull_Trend']}")
        print(f"      Bearish cross: {bearish_cross}, Bear regime: {regime in ['Weak_Bear_Trend', 'Strong_Bear_Trend']}")
        return None
    
    # If we got here, we have a valid signal!
    result = original_evaluate_entry(self, signals, regime)
    
    if result:
        print(f"\n✅ ENTRY DECISION GENERATED:")
        print(f"   Direction: {result['direction']}")
        print(f"   Confidence: {result['confidence']:.3f}")
        print(f"   Position size: {result['position_size']:.4f}")
    
    return result

# Apply patches
ModernTFV3Strategy.evaluate_entry = verbose_evaluate_entry

def main():
    print("=== Strategy Debug with Extreme Verbosity ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Create strategy
    strategy = ModernTFV3Strategy(config)
    
    # Test with realistic data from backtest
    test_signals = {
        'timestamp': datetime(2024, 1, 15, 8, 0, 0),
        'close': 42667.50,
        'ema_fast': 42559.50,
        'ema_slow': 42970.97,
        'ema_baseline': 43000.00,
        'atr_14': 220.13,
        'atr_percent': 0.0052,
        'rsi': 45,
        'bb_upper': 43000,
        'bb_middle': 42600,
        'bb_lower': 42200,
        'volume': 0,
        'regime_features': {
            'current_state': 'Weak_Bear_Trend',
            'current_confidence': 0.67,
            'current_momentum': -0.001,
            'recent_bullish_pct': 0.0,
            'recent_bearish_pct': 100.0,
            'momentum_trend': -0.0001,
            'avg_momentum': -0.0005,
            'momentum_volatility': 0.0002,
            'avg_volatility': 0.005,
            'max_volatility': 0.006,
            'avg_volume_imbalance': -0.05,
            'recent_transitions': 0,
            'is_trending': True,
            'state_persistence': 0.8
        }
    }
    
    print("Testing with realistic backtest data...")
    result = strategy.evaluate_entry(test_signals, 'Weak_Bear_Trend')
    
    if not result:
        print("\n\n⚠️  ISSUE: Strategy should have generated a SHORT signal!")
        print("The conditions were perfect for a short entry.")

if __name__ == "__main__":
    main()