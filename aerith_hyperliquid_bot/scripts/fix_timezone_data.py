#!/usr/bin/env python3
"""
Fix timezone issues in existing data files to ensure UTC-naive compliance.

This script addresses the timezone inconsistencies found after R-102a implementation:
1. Resampled OHLCV files have timezone-aware timestamps (should be UTC-naive)
2. Arrow files have integer timestamps (should be datetime UTC-naive)

Usage:
    python scripts/fix_timezone_data.py --fix-resampled
    python scripts/fix_timezone_data.py --fix-arrow
    python scripts/fix_timezone_data.py --check-only
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import List
import pandas as pd
import pyarrow as pa
import pyarrow.ipc as ipc

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.utils.time import to_utc_naive

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_file_timezone_compliance(file_path: str) -> dict:
    """
    Check if a file has UTC-naive timestamps.
    
    Returns:
        dict with compliance info
    """
    result = {
        'file': file_path,
        'exists': False,
        'readable': False,
        'has_timestamp': False,
        'is_utc_naive': False,
        'timestamp_type': None,
        'sample_timestamps': [],
        'error': None
    }
    
    try:
        if not Path(file_path).exists():
            return result
        
        result['exists'] = True
        
        # Try different file formats
        if file_path.endswith('.arrow'):
            # Arrow file
            table = pa.ipc.open_file(file_path).read_all()
            df = table.to_pandas()
        else:
            # Parquet file
            df = pd.read_parquet(file_path)
        
        result['readable'] = True
        
        if 'timestamp' in df.columns:
            result['has_timestamp'] = True
            result['timestamp_type'] = str(df['timestamp'].dtype)
            result['sample_timestamps'] = df['timestamp'].head(3).tolist()
            
            # Check if it's datetime and timezone-naive
            if pd.api.types.is_datetime64_any_dtype(df['timestamp']):
                result['is_utc_naive'] = df['timestamp'].dt.tz is None
            else:
                # Integer timestamps (like in arrow files)
                result['is_utc_naive'] = False
                result['timestamp_type'] = f"integer ({result['timestamp_type']})"
        
    except Exception as e:
        result['error'] = str(e)
    
    return result


def fix_resampled_file(file_path: str, backup: bool = True) -> bool:
    """
    Fix timezone-aware timestamps in resampled OHLCV files.
    
    Args:
        file_path: Path to the resampled parquet file
        backup: Whether to create a backup before fixing
        
    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Fixing resampled file: {file_path}")
        
        # Load the file
        df = pd.read_parquet(file_path)
        
        # Check if it needs fixing
        if 'timestamp' not in df.columns:
            logger.warning(f"No timestamp column in {file_path}")
            return False
        
        if df['timestamp'].dt.tz is None:
            logger.info(f"File {file_path} already has UTC-naive timestamps")
            return True
        
        # Create backup if requested
        if backup:
            backup_path = file_path + '.backup'
            if not Path(backup_path).exists():
                df.to_parquet(backup_path, index=False)
                logger.info(f"Created backup: {backup_path}")
        
        # Fix timestamps
        df['timestamp'] = df['timestamp'].apply(to_utc_naive)
        
        # Verify fix
        assert df['timestamp'].dt.tz is None, "Failed to convert to UTC-naive"
        
        # Save fixed file
        df.to_parquet(file_path, index=False)
        logger.info(f"Successfully fixed {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error fixing {file_path}: {e}")
        return False


def fix_arrow_file(file_path: str, backup: bool = True) -> bool:
    """
    Fix integer timestamps in arrow files by converting to UTC-naive datetime.
    
    Args:
        file_path: Path to the arrow file
        backup: Whether to create a backup before fixing
        
    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Fixing arrow file: {file_path}")
        
        # Load the arrow file
        table = pa.ipc.open_file(file_path).read_all()
        df = table.to_pandas()
        
        # Check if it needs fixing
        if 'timestamp' not in df.columns:
            logger.warning(f"No timestamp column in {file_path}")
            return False
        
        if pd.api.types.is_datetime64_any_dtype(df['timestamp']) and df['timestamp'].dt.tz is None:
            logger.info(f"File {file_path} already has UTC-naive datetime timestamps")
            return True
        
        # Create backup if requested
        if backup:
            backup_path = file_path + '.backup'
            if not Path(backup_path).exists():
                # Save original arrow file as backup
                import shutil
                shutil.copy2(file_path, backup_path)
                logger.info(f"Created backup: {backup_path}")
        
        # Fix timestamps (convert from epoch ms to UTC-naive datetime)
        if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
            # Convert integer timestamps to UTC-naive datetime
            df['timestamp'] = df['timestamp'].apply(to_utc_naive)
        else:
            # Already datetime, just ensure UTC-naive
            df['timestamp'] = df['timestamp'].apply(to_utc_naive)
        
        # Verify fix
        assert df['timestamp'].dt.tz is None, "Failed to convert to UTC-naive"
        assert pd.api.types.is_datetime64_any_dtype(df['timestamp']), "Failed to convert to datetime"
        
        # Save fixed arrow file
        table_fixed = pa.Table.from_pandas(df)
        with open(file_path, 'wb') as f:
            writer = ipc.new_file(f, table_fixed.schema)
            writer.write_table(table_fixed)
            writer.close()
        
        logger.info(f"Successfully fixed {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error fixing {file_path}: {e}")
        return False


def scan_and_check_files(base_dir: str, pattern: str) -> List[dict]:
    """
    Scan for files matching pattern and check their timezone compliance.
    
    Args:
        base_dir: Base directory to scan
        pattern: File pattern to match
        
    Returns:
        List of compliance check results
    """
    base_path = Path(base_dir)
    files = list(base_path.glob(pattern))
    
    logger.info(f"Found {len(files)} files matching pattern: {pattern}")
    
    results = []
    for file_path in files:
        result = check_file_timezone_compliance(str(file_path))
        results.append(result)
    
    return results


def main():
    parser = argparse.ArgumentParser(description="Fix timezone issues in existing data files")
    parser.add_argument("--check-only", action="store_true",
                        help="Only check files, don't fix them")
    parser.add_argument("--fix-resampled", action="store_true",
                        help="Fix timezone-aware timestamps in resampled OHLCV files")
    parser.add_argument("--fix-arrow", action="store_true",
                        help="Fix integer timestamps in arrow files")
    parser.add_argument("--no-backup", action="store_true",
                        help="Don't create backup files before fixing")
    parser.add_argument("--base-dir", 
                        default="/Users/<USER>/Desktop/trading_bot_/hyperliquid_data",
                        help="Base data directory")
    
    args = parser.parse_args()
    
    if not any([args.check_only, args.fix_resampled, args.fix_arrow]):
        logger.error("Must specify at least one action: --check-only, --fix-resampled, or --fix-arrow")
        sys.exit(1)
    
    base_dir = Path(args.base_dir)
    if not base_dir.exists():
        logger.error(f"Base directory does not exist: {base_dir}")
        sys.exit(1)
    
    # Check resampled files
    if args.check_only or args.fix_resampled:
        logger.info("=" * 60)
        logger.info("CHECKING RESAMPLED OHLCV FILES")
        logger.info("=" * 60)
        
        resampled_results = scan_and_check_files(
            str(base_dir / "resampled_l2"), "**/*.parquet"
        )
        
        non_compliant_resampled = [r for r in resampled_results 
                                 if r['readable'] and r['has_timestamp'] and not r['is_utc_naive']]
        
        logger.info(f"Found {len(non_compliant_resampled)} non-compliant resampled files")
        
        if args.fix_resampled and non_compliant_resampled:
            logger.info("Fixing resampled files...")
            success_count = 0
            for result in non_compliant_resampled:
                if fix_resampled_file(result['file'], backup=not args.no_backup):
                    success_count += 1
            logger.info(f"Fixed {success_count}/{len(non_compliant_resampled)} resampled files")
    
    # Check arrow files
    if args.check_only or args.fix_arrow:
        logger.info("=" * 60)
        logger.info("CHECKING ARROW FILES")
        logger.info("=" * 60)
        
        arrow_results = scan_and_check_files(
            str(base_dir / "l2_raw"), "**/*.arrow"
        )
        
        non_compliant_arrow = [r for r in arrow_results 
                             if r['readable'] and r['has_timestamp'] and not r['is_utc_naive']]
        
        logger.info(f"Found {len(non_compliant_arrow)} non-compliant arrow files")
        
        if args.fix_arrow and non_compliant_arrow:
            logger.info("Fixing arrow files...")
            success_count = 0
            for result in non_compliant_arrow:
                if fix_arrow_file(result['file'], backup=not args.no_backup):
                    success_count += 1
            logger.info(f"Fixed {success_count}/{len(non_compliant_arrow)} arrow files")
    
    logger.info("Timezone fix process complete!")


if __name__ == "__main__":
    main()
