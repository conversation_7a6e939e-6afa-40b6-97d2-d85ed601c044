#!/usr/bin/env python
# scripts/reprocess_march_data.py

"""
Reprocess March 1-22 data to include ATR in 1-second feature files.

This script reprocesses the raw L2 data for March 1-22 to include ATR in the
1-second feature files, which is required for the Continuous GMS detector.
"""

import os
import sys
import logging
import argparse
from datetime import datetime, timedelta
from pathlib import Path
import concurrent.futures
import time

# Add project root to path for imports
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from tools.etl_l20_to_1s import process_hour

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("reprocess_march_data")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Reprocess March 1-22 data to include ATR in 1-second feature files")

    parser.add_argument(
        "--start-date",
        type=str,
        default="2025-03-01",
        help="Start date in YYYY-MM-DD format (default: 2025-03-01)"
    )

    parser.add_argument(
        "--end-date",
        type=str,
        default="2025-03-22",
        help="End date in YYYY-MM-DD format (default: 2025-03-22)"
    )

    parser.add_argument(
        "--force",
        action="store_true",
        help="Force overwrite of existing output files"
    )

    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Process dates in parallel"
    )

    return parser.parse_args()

def process_date(date_str, config, force=False):
    """
    Process a single date.

    Args:
        date_str: Date string in YYYY-MM-DD format
        config: Configuration object
        force: Whether to overwrite existing output files

    Returns:
        Tuple of (date_str, success_count, total_count)
    """
    logger.info(f"Processing data for {date_str}")

    date = datetime.strptime(date_str, "%Y-%m-%d")
    date_pattern = date.strftime("%Y%m%d")

    # Find raw files for the date
    raw_dir = config.data_paths.raw_l2_dir
    out_dir = config.data_paths.feature_1s_dir
    depth = config.microstructure.depth_levels
    rollup_method = config.etl.l20_to_1s.rollup_method

    raw_dir_path = Path(raw_dir)

    # Check for files in both formats: "2025-03-01" and "20250301"
    date_pattern_dash = date.strftime("%Y-%m-%d")

    # Look for files directly in the raw_dir
    raw_files = list(raw_dir_path.glob(f"*{date_pattern}*.txt")) + \
                list(raw_dir_path.glob(f"*{date_pattern}*.arrow")) + \
                list(raw_dir_path.glob(f"*{date_pattern}*.parquet"))

    # Look for files in subdirectories with both date formats
    if not raw_files:
        # Check for directory with dashes (2025-03-01)
        date_dir = raw_dir_path / date_pattern_dash
        if date_dir.exists():
            logger.info(f"Found directory {date_dir}")
            raw_files = list(date_dir.glob("*.txt")) + \
                        list(date_dir.glob("*.arrow")) + \
                        list(date_dir.glob("*.parquet"))

    # If still no files, check for directory without dashes (20250301)
    if not raw_files:
        date_dir = raw_dir_path / date_pattern
        if date_dir.exists():
            logger.info(f"Found directory {date_dir}")
            raw_files = list(date_dir.glob("*.txt")) + \
                        list(date_dir.glob("*.arrow")) + \
                        list(date_dir.glob("*.parquet"))

    if not raw_files:
        logger.error(f"No raw files found for {date_str} in {raw_dir}")
        return date_str, 0, 0

    logger.info(f"Found {len(raw_files)} raw files for {date_str}")

    # Process each file
    success_count = 0
    for raw_file in sorted(raw_files):
        # Determine hour from filename
        hour = None
        file_name = raw_file.name

        # Try to extract hour from filename patterns like:
        # - lob_20250525_13.arrow (hour = 13)
        # - BTC_8_l2Book.txt (hour = 8)
        if '_' in file_name:
            parts = file_name.split('_')
            for part in parts:
                if part.isdigit() and 0 <= int(part) <= 23:
                    hour = int(part)
                    break

        # If hour not found in filename, default to processing as a single day
        if hour is None:
            logger.warning(f"Could not determine hour from filename {file_name}, processing as full day")
            output_file = Path(out_dir) / f"features_{date_pattern}.parquet"
            if process_hour(str(raw_file), str(output_file), depth, rollup_method, force):
                success_count += 1
        else:
            # Process as hourly file
            output_file = Path(out_dir) / f"features_{date_pattern}_{hour:02d}.parquet"
            if process_hour(str(raw_file), str(output_file), depth, rollup_method, force):
                success_count += 1

    logger.info(f"Processed {success_count}/{len(raw_files)} files successfully for {date_str}")
    return date_str, success_count, len(raw_files)

def main():
    """Main entry point."""
    args = parse_args()

    # Load config
    config = load_config()

    # Parse dates
    start_date = datetime.strptime(args.start_date, "%Y-%m-%d")
    end_date = datetime.strptime(args.end_date, "%Y-%m-%d")

    # Generate list of dates to process
    dates = []
    current_date = start_date
    while current_date <= end_date:
        dates.append(current_date.strftime("%Y-%m-%d"))
        current_date += timedelta(days=1)

    logger.info(f"Processing {len(dates)} dates from {args.start_date} to {args.end_date}")

    start_time = time.time()

    if args.parallel:
        # Process dates in parallel
        with concurrent.futures.ProcessPoolExecutor() as executor:
            futures = [executor.submit(process_date, date_str, config, args.force) for date_str in dates]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
    else:
        # Process dates sequentially
        results = [process_date(date_str, config, args.force) for date_str in dates]

    # Summarize results
    total_success = sum(success for _, success, _ in results)
    total_files = sum(total for _, _, total in results)

    end_time = time.time()
    logger.info(f"Processed {total_success}/{total_files} files successfully in {end_time - start_time:.2f} seconds")

    return 0 if total_success == total_files else 1

if __name__ == "__main__":
    sys.exit(main())
