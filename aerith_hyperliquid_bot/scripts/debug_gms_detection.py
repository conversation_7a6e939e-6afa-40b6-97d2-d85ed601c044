#!/usr/bin/env python3
"""
Debug GMS Detection Logic
Analyzes the continuous_gms detector logic to understand why only Uncertain/Low_Vol_Range states are detected.
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.unified_gms_detector import UnifiedGMSDetector

def analyze_signals_from_backtest():
    """Load the latest backtest signals and analyze classification logic."""
    # Find the latest signals file
    import glob
    files = glob.glob('/Users/<USER>/Desktop/trading_bot_/logs/backtest_signals_*.parquet')
    if not files:
        print("No signals files found")
        return
    
    latest_file = max(files)
    print(f"Loading {latest_file}")
    df = pd.read_parquet(latest_file)
    
    print(f"Total rows: {len(df)}")
    print(f"Columns: {list(df.columns)}")
    
    # Load config (simplified approach using base config)
    import yaml
    from hyperliquid_bot.config.settings import Config
    
    # Load base config and merge with override
    with open('/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/configs/base.yaml', 'r') as f:
        base_config = yaml.safe_load(f)
    
    with open('/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/configs/overrides/execution_refinement_enabled.yaml', 'r') as f:
        override_config = yaml.safe_load(f)
    
    # Simple merge
    merged_config = {**base_config, **override_config}
    
    # Create config object with direct dict
    config = Config.from_dict(merged_config)
    
    # Create detector
    detector = UnifiedGMSDetector(config)
    print(f"Detector mode: {detector.detector_mode}")
    print(f"Thresholds: {detector.thresholds}")
    
    # Sample first 100 rows for analysis
    sample = df.head(100).copy()
    
    classifications = []
    detailed_analysis = []
    
    for idx, row in sample.iterrows():
        signals = row.to_dict()
        
        # Analyze signal values
        atr_pct = signals.get('atr_percent', np.nan)
        ma_slope = signals.get('ma_slope', np.nan)
        obi_5 = signals.get('obi_smoothed_5', np.nan)
        spread_mean = signals.get('spread_mean', np.nan)
        spread_std = signals.get('spread_std', np.nan)
        
        # Check thresholds
        vol_thresholds = detector._get_current_vol_thresholds()
        mom_thresholds = detector._get_current_mom_thresholds()
        
        # Manual classification
        vol_regime = "Unknown"
        if not pd.isna(atr_pct):
            if atr_pct >= vol_thresholds['high']:
                vol_regime = "High"
            elif atr_pct <= vol_thresholds['low']:
                vol_regime = "Low"
            else:
                vol_regime = "Medium"
        
        mom_regime = "Unknown"
        if not pd.isna(ma_slope):
            abs_ma_slope = abs(ma_slope)
            if abs_ma_slope >= mom_thresholds['strong']:
                mom_regime = "Strong"
            elif abs_ma_slope <= mom_thresholds['weak']:
                mom_regime = "Weak"
            else:
                mom_regime = "Medium"
        
        direction = "Bull" if ma_slope > 0 else "Bear"
        
        # Get actual detector result
        try:
            result = detector.get_regime(signals)
            if isinstance(result, dict):
                state = result['state']
            else:
                state = result
        except Exception as e:
            state = f"ERROR: {e}"
        
        analysis = {
            'timestamp': signals.get('timestamp', idx),
            'atr_pct': atr_pct,
            'ma_slope': ma_slope,
            'abs_ma_slope': abs(ma_slope) if not pd.isna(ma_slope) else np.nan,
            'vol_regime': vol_regime,
            'mom_regime': mom_regime,
            'direction': direction,
            'final_state': state,
            'vol_thresh_low': vol_thresholds['low'],
            'vol_thresh_high': vol_thresholds['high'],
            'mom_thresh_weak': mom_thresholds['weak'],
            'mom_thresh_strong': mom_thresholds['strong']
        }
        
        detailed_analysis.append(analysis)
        classifications.append(state)
    
    # Summary statistics
    from collections import Counter
    state_counts = Counter(classifications)
    
    print(f"\n=== SIGNAL ANALYSIS SUMMARY ===")
    print(f"Sample size: {len(sample)} rows")
    
    print(f"\nATR Percent statistics:")
    atr_stats = sample['atr_percent'].describe()
    for stat, value in atr_stats.items():
        print(f"  {stat:8s}: {value:.6f}")
    
    print(f"\nMA Slope statistics:")
    slope_stats = sample['ma_slope'].describe()
    for stat, value in slope_stats.items():
        print(f"  {stat:8s}: {value:.6f}")
    
    print(f"\nCurrent thresholds:")
    print(f"  Vol Low:  {vol_thresholds['low']:.6f}")
    print(f"  Vol High: {vol_thresholds['high']:.6f}")
    print(f"  Mom Weak: {mom_thresholds['weak']:.6f}")
    print(f"  Mom Strong: {mom_thresholds['strong']:.6f}")
    
    print(f"\nATR classification breakdown:")
    atr_low = (sample['atr_percent'] <= vol_thresholds['low']).sum()
    atr_high = (sample['atr_percent'] >= vol_thresholds['high']).sum()
    atr_medium = len(sample) - atr_low - atr_high
    print(f"  Low vol (≤{vol_thresholds['low']:.3f}):    {atr_low:3d} ({atr_low/len(sample)*100:.1f}%)")
    print(f"  High vol (≥{vol_thresholds['high']:.3f}):   {atr_high:3d} ({atr_high/len(sample)*100:.1f}%)")
    print(f"  Medium vol:                  {atr_medium:3d} ({atr_medium/len(sample)*100:.1f}%)")
    
    print(f"\nMomentum classification breakdown:")
    slope_abs = sample['ma_slope'].abs()
    mom_weak = (slope_abs <= mom_thresholds['weak']).sum()
    mom_strong = (slope_abs >= mom_thresholds['strong']).sum()
    mom_medium = len(sample) - mom_weak - mom_strong
    print(f"  Weak mom (≤{mom_thresholds['weak']:.1f}):     {mom_weak:3d} ({mom_weak/len(sample)*100:.1f}%)")
    print(f"  Strong mom (≥{mom_thresholds['strong']:.1f}):   {mom_strong:3d} ({mom_strong/len(sample)*100:.1f}%)")
    print(f"  Medium mom:                 {mom_medium:3d} ({mom_medium/len(sample)*100:.1f}%)")
    
    print(f"\nFinal state distribution:")
    for state, count in state_counts.most_common():
        print(f"  {state:<20}: {count:3d} ({count/len(sample)*100:.1f}%)")
    
    # Show some specific examples
    print(f"\n=== DETAILED EXAMPLES ===")
    analysis_df = pd.DataFrame(detailed_analysis)
    
    # Show some high momentum examples
    high_mom = analysis_df[analysis_df['abs_ma_slope'] > mom_thresholds['weak']].head(5)
    if len(high_mom) > 0:
        print(f"\nExamples with higher momentum (>{mom_thresholds['weak']:.1f}):")
        for _, row in high_mom.iterrows():
            print(f"  ATR: {row['atr_pct']:.4f} ({row['vol_regime']}) | "
                  f"Slope: {row['ma_slope']:7.2f} ({row['mom_regime']}) | "
                  f"State: {row['final_state']}")
    
    # Check for any missing signals
    print(f"\n=== MISSING SIGNAL ANALYSIS ===")
    required_signals = ['atr_percent', 'ma_slope', 'obi_smoothed_5', 'spread_mean', 'spread_std']
    for signal in required_signals:
        missing = sample[signal].isna().sum()
        print(f"  {signal:<15}: {missing:3d} missing ({missing/len(sample)*100:.1f}%)")
    
    return analysis_df

if __name__ == "__main__":
    analyze_signals_from_backtest()