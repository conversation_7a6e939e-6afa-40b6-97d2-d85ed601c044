#!/usr/bin/env python
# scripts/run_regression_test.py

"""
Run regression test for TF-v2 vs TF-v3 for March 1-22.

This script runs a back-test for both TF-v2 and TF-v3 strategies for the
March 1-22 period and compares the results.
"""

import os
import sys
import logging
import argparse
import json
from datetime import datetime
from pathlib import Path
import pandas as pd
import time

# Add project root to path for imports
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.backtester.backtester import Backtester
from hyperliquid_bot.strategies.strategy_factory import StrategyFactory

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("run_regression_test")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run regression test for TF-v2 vs TF-v3 for March 1-22")

    parser.add_argument(
        "--start-date",
        type=str,
        default="2025-03-01",
        help="Start date in YYYY-MM-DD format (default: 2025-03-01)"
    )

    parser.add_argument(
        "--end-date",
        type=str,
        default="2025-03-22",
        help="End date in YYYY-MM-DD format (default: 2025-03-22)"
    )

    parser.add_argument(
        "--output-dir",
        type=str,
        default="results",
        help="Output directory for results (default: results)"
    )

    return parser.parse_args()

def run_backtest(config, start_date, end_date, strategies):
    """
    Run a back-test for the specified strategies.

    Args:
        config: Configuration object
        start_date: Start date string in YYYY-MM-DD format
        end_date: End date string in YYYY-MM-DD format
        strategies: List of strategy names to test

    Returns:
        Dictionary with back-test results
    """
    logger.info(f"Running back-test for {', '.join(strategies)} from {start_date} to {end_date}")

    # Parse dates
    start_date_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")

    # Save original strategy settings
    original_tf = config.strategies.use_trend_following
    original_tf_v3 = config.strategies.use_tf_v3

    # Disable all strategies
    config.strategies.use_trend_following = False
    config.strategies.use_mean_reversion = False
    config.strategies.use_mean_variance = False
    config.strategies.use_obi_scalper = False
    config.strategies.use_tf_v3 = False

    # Enable only the strategies we want to test
    results = {}

    for strategy_name in strategies:
        # Enable only this strategy
        if strategy_name == 'trend_following':
            config.strategies.use_trend_following = True
        elif strategy_name == 'tf_v3':
            config.strategies.use_tf_v3 = True

        # Initialize backtester
        backtester = Backtester(config)

        # Run back-test
        start_time = time.time()
        backtester.run(
            start_date=start_date_dt,
            end_date=end_date_dt
        )
        end_time = time.time()

        # Extract metrics from the backtester
        metrics = {}
        if hasattr(backtester, 'portfolio') and backtester.portfolio:
            metrics['trade_count'] = len(backtester.portfolio.trades)
            metrics['total_pnl'] = backtester.portfolio.balance - config.portfolio.initial_balance

            # Calculate win rate
            if metrics['trade_count'] > 0:
                winning_trades = sum(1 for trade in backtester.portfolio.trades if trade['pnl'] > 0)
                metrics['win_rate'] = winning_trades / metrics['trade_count']
            else:
                metrics['win_rate'] = 0.0

            # Extract skip reasons
            if hasattr(backtester, 'skip_logger') and backtester.skip_logger:
                skip_reasons = backtester.skip_logger.get_skip_counts(strategy_name)
                metrics['skip_reasons'] = skip_reasons

        # Store metrics for this strategy
        results[strategy_name] = metrics

        logger.info(f"Back-test for {strategy_name} completed in {end_time - start_time:.2f} seconds")

        # Disable this strategy for the next run
        if strategy_name == 'trend_following':
            config.strategies.use_trend_following = False
        elif strategy_name == 'tf_v3':
            config.strategies.use_tf_v3 = False

    # Restore original strategy settings
    config.strategies.use_trend_following = original_tf
    config.strategies.use_tf_v3 = original_tf_v3

    return results

def save_results(results, output_dir, start_date, end_date):
    """
    Save back-test results to a file.

    Args:
        results: Dictionary with back-test results
        output_dir: Output directory
        start_date: Start date string in YYYY-MM-DD format
        end_date: End date string in YYYY-MM-DD format

    Returns:
        Path to the saved file
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Create filename
    filename = f"regression_tf2_vs_tf3_{start_date.replace('-', '')}_{end_date[-2:]}.json"
    filepath = os.path.join(output_dir, filename)

    # Save results
    with open(filepath, 'w') as f:
        json.dump(results, f, indent=2)

    logger.info(f"Results saved to {filepath}")

    return filepath

def generate_report(results, output_dir, start_date, end_date):
    """
    Generate a report comparing TF-v2 and TF-v3 results.

    Args:
        results: Dictionary with back-test results
        output_dir: Output directory
        start_date: Start date string in YYYY-MM-DD format
        end_date: End date string in YYYY-MM-DD format

    Returns:
        Path to the saved report
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Create filename
    filename = f"regression_tf2_vs_tf3_{start_date.replace('-', '')}_{end_date[-2:]}.md"
    filepath = os.path.join(output_dir, filename)

    # Extract metrics
    tf_v2_metrics = results.get('trend_following', {})
    tf_v3_metrics = results.get('tf_v3', {})

    # Generate report
    with open(filepath, 'w') as f:
        f.write(f"# TF-v2 vs TF-v3 Regression Test ({start_date} to {end_date})\n\n")

        f.write("## Summary\n\n")
        f.write("| Metric | TF-v2 | TF-v3 |\n")
        f.write("|--------|-------|-------|\n")

        # Add key metrics
        metrics = [
            ('trade_count', 'Trade Count'),
            ('win_rate', 'Win Rate'),
            ('profit_factor', 'Profit Factor'),
            ('total_pnl', 'Total PnL'),
            ('max_drawdown', 'Max Drawdown'),
            ('sharpe_ratio', 'Sharpe Ratio'),
            ('avg_trade_duration', 'Avg Trade Duration'),
            ('avg_profit_per_trade', 'Avg Profit/Trade'),
        ]

        for key, label in metrics:
            tf_v2_value = tf_v2_metrics.get(key, 'N/A')
            tf_v3_value = tf_v3_metrics.get(key, 'N/A')

            # Format values
            if isinstance(tf_v2_value, float):
                tf_v2_value = f"{tf_v2_value:.4f}"
            if isinstance(tf_v3_value, float):
                tf_v3_value = f"{tf_v3_value:.4f}"

            f.write(f"| {label} | {tf_v2_value} | {tf_v3_value} |\n")

        # Add skip reasons
        f.write("\n## Skip Reasons\n\n")

        # TF-v2 skip reasons
        f.write("### TF-v2 Skip Reasons\n\n")
        tf_v2_skip_reasons = tf_v2_metrics.get('skip_reasons', {})
        if tf_v2_skip_reasons:
            f.write("| Reason | Count |\n")
            f.write("|--------|-------|\n")
            for reason, count in sorted(tf_v2_skip_reasons.items(), key=lambda x: x[1], reverse=True):
                f.write(f"| {reason} | {count} |\n")
        else:
            f.write("No skip reasons recorded.\n")

        # TF-v3 skip reasons
        f.write("\n### TF-v3 Skip Reasons\n\n")
        tf_v3_skip_reasons = tf_v3_metrics.get('skip_reasons', {})
        if tf_v3_skip_reasons:
            f.write("| Reason | Count |\n")
            f.write("|--------|-------|\n")
            for reason, count in sorted(tf_v3_skip_reasons.items(), key=lambda x: x[1], reverse=True):
                f.write(f"| {reason} | {count} |\n")
        else:
            f.write("No skip reasons recorded.\n")

    logger.info(f"Report saved to {filepath}")

    return filepath

def main():
    """Main entry point."""
    args = parse_args()

    # Load config
    config = load_config()

    # Run back-test
    results = run_backtest(
        config=config,
        start_date=args.start_date,
        end_date=args.end_date,
        strategies=['trend_following', 'tf_v3']
    )

    # Save results
    results_file = save_results(
        results=results,
        output_dir=args.output_dir,
        start_date=args.start_date,
        end_date=args.end_date
    )

    # Generate report
    report_file = generate_report(
        results=results,
        output_dir='reports',
        start_date=args.start_date,
        end_date=args.end_date
    )

    # Check if TF-v3 has trades
    tf_v3_trade_count = results.get('tf_v3', {}).get('trade_count', 0)
    if tf_v3_trade_count == 0:
        logger.error("TF-v3 has no trades. Check skip reasons.")

        # Print top 5 skip reasons
        tf_v3_skip_reasons = results.get('tf_v3', {}).get('skip_reasons', {})
        if tf_v3_skip_reasons:
            logger.error("Top 5 TF-v3 skip reasons:")
            for reason, count in sorted(tf_v3_skip_reasons.items(), key=lambda x: x[1], reverse=True)[:5]:
                logger.error(f"  {reason}: {count}")

        return 1

    logger.info(f"TF-v3 trade count: {tf_v3_trade_count}")
    logger.info("Regression test passed")

    return 0

if __name__ == "__main__":
    sys.exit(main())
