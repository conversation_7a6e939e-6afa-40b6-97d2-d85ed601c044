#!/usr/bin/env python3
"""
Verify ATR quality in processed feature files.
"""

import pandas as pd
import numpy as np
import random
from pathlib import Path

def verify_atr_quality():
    """Verify ATR calculation quality in random feature files."""
    
    print('=== COMPREHENSIVE ATR VERIFICATION ===')
    print()

    # Check two random dates from the processed range
    test_dates = ['2025-03-07', '2025-03-15']  # Random dates from the range
    base_path = Path('/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s')

    for date in test_dates:
        print(f'📅 CHECKING DATE: {date}')
        print('=' * 50)
        
        # Pick a random hour file
        hour = random.randint(8, 20)  # Pick active trading hours
        
        file_path = base_path / date / f'features_{hour:02d}.parquet'
        
        try:
            df = pd.read_parquet(file_path)
            
            print(f'📁 File: features_{hour:02d}.parquet')
            print(f'📊 Shape: {df.shape}')
            print(f'🕐 Time range: {df["timestamp"].min()} to {df["timestamp"].max()}')
            print()
            
            # Check ATR columns
            atr_cols = [col for col in df.columns if 'atr' in col]
            print(f'🎯 ATR Columns: {atr_cols}')
            print()
            
            # Detailed ATR analysis
            if 'atr_14_sec' in df.columns:
                atr_series = df['atr_14_sec']
                
                print('📈 ATR_14_SEC ANALYSIS:')
                print(f'  Total rows: {len(atr_series)}')
                print(f'  Non-null count: {atr_series.count()}')
                print(f'  Null count: {atr_series.isna().sum()}')
                print(f'  Mean: {atr_series.mean():.4f}')
                print(f'  Std: {atr_series.std():.4f}')
                print(f'  Min: {atr_series.min():.4f}')
                print(f'  Max: {atr_series.max():.4f}')
                print(f'  Unique values: {atr_series.nunique()}')
                print()
                
                # Check for proper variation (ATR should change over time)
                atr_changes = atr_series.diff().abs().sum()
                print(f'  Total ATR variation: {atr_changes:.4f}')
                print(f'  ATR is dynamic: {"✅ YES" if atr_changes > 0 else "❌ NO (static)"}')
                print()
                
                # Sample values throughout the hour
                sample_indices = [0, len(df)//4, len(df)//2, 3*len(df)//4, -1]
                print('  Sample ATR values throughout hour:')
                for i, idx in enumerate(sample_indices):
                    if idx < len(df):
                        timestamp = df['timestamp'].iloc[idx]
                        atr_val = atr_series.iloc[idx]
                        print(f'    {timestamp}: {atr_val:.4f}')
                print()
            
            # Check ATR percent
            if 'atr_percent_sec' in df.columns:
                atr_pct = df['atr_percent_sec']
                print('📊 ATR_PERCENT_SEC ANALYSIS:')
                print(f'  Mean: {atr_pct.mean():.6f} ({atr_pct.mean()*100:.4f}%)')
                print(f'  Range: {atr_pct.min():.6f} to {atr_pct.max():.6f}')
                print()
            
            # Check mid_price for context
            if 'mid_price' in df.columns:
                mid_price = df['mid_price']
                print('💰 MID_PRICE CONTEXT:')
                print(f'  Mean: ${mid_price.mean():.2f}')
                print(f'  Range: ${mid_price.min():.2f} to ${mid_price.max():.2f}')
                print(f'  Price volatility: {mid_price.std():.2f}')
                print()
            
            # Quality gates
            print('✅ QUALITY GATES:')
            gates = {
                'ATR column exists': 'atr_14_sec' in df.columns,
                'ATR has values': df['atr_14_sec'].count() > 0 if 'atr_14_sec' in df.columns else False,
                'ATR is numeric': df['atr_14_sec'].dtype in ['float64', 'float32'] if 'atr_14_sec' in df.columns else False,
                'Minimal NaN values': df['atr_14_sec'].isna().sum() < 14 if 'atr_14_sec' in df.columns else False,
                'ATR is dynamic': atr_series.diff().abs().sum() > 0 if 'atr_14_sec' in df.columns else False,
                'Timestamps monotonic': df['timestamp'].is_monotonic_increasing,
                'Has essential features': all(col in df.columns for col in ['mid_price', 'raw_obi_20'])
            }
            
            for gate, passed in gates.items():
                status = '✅' if passed else '❌'
                print(f'  {status} {gate}')
            
            all_passed = all(gates.values())
            print(f'\n🎉 OVERALL: {"✅ ALL QUALITY GATES PASSED" if all_passed else "❌ SOME QUALITY GATES FAILED"}')
            
        except Exception as e:
            print(f'❌ Error reading {file_path}: {e}')
        
        print('\n' + '='*70 + '\n')

    print('🏁 VERIFICATION COMPLETE')

if __name__ == '__main__':
    verify_atr_quality()
