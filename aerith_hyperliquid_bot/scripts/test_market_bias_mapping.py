#!/usr/bin/env python3
# Test script to verify market bias mapping functionality

import sys
import os
import logging
from pathlib import Path
import yaml

# Setup logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_market_bias")

# Add project root to path for imports
project_root = Path(os.path.abspath(os.path.dirname(__file__))).parent
sys.path.insert(0, str(project_root))

# Import necessary modules
from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.risk import RiskManager
from hyperliquid_bot.utils.state_mapping import get_state_map, map_gms_state

def load_config():
    """Load the configuration from base.yaml"""
    config_path = project_root / "configs" / "base.yaml"
    with open(config_path, 'r') as f:
        config_dict = yaml.safe_load(f)
    return Config.parse_obj(config_dict)

def test_market_bias_mapping():
    """Test the market bias mapping in RiskManager"""
    logger.info("Loading configuration...")
    config = load_config()
    
    # Ensure market bias is enabled for testing
    if not hasattr(config.regime, 'market_bias') or not config.regime.market_bias.enabled:
        logger.warning("Market bias not enabled in config, enabling for test...")
        config.regime.market_bias.enabled = True
    
    # Initialize the RiskManager
    logger.info("Initializing RiskManager...")
    risk_manager = RiskManager(config)
    
    # Get the official state map for validation
    state_map = get_state_map()
    logger.info(f"Loaded state map with {len(state_map)} entries")
    
    # List of test states to check
    test_states = [
        "Strong_Bull_Trend", 
        "Weak_Bull_Trend",
        "Strong_Bear_Trend", 
        "Weak_Bear_Trend",  # Critical test - this should map to CHOP not BEAR!
        "High_Vol_Range",
        "Low_Vol_Range",
        "Uncertain",
        "TIGHT_SPREAD",
        "Unknown_State"  # Test fallback behavior
    ]
    
    # Test each state
    for state in test_states:
        # Map the state using our utility for reference
        expected_mapping = map_gms_state(state) 
        
        # Create a mock signals dict with regime
        signals = {
            "timestamp": "2023-01-01 00:00:00",
            "close": 1000.0,
            "atr_tf": 50.0,  # Required for risk calculation
            "regime": state
        }
        
        # Create a mock strategy_info with direction for testing direction bias
        strategy_info = {"direction": "long"}
        
        # Log the test case
        logger.info(f"Testing regime '{state}' (Expected mapping: '{expected_mapping}')")
        
        # To properly test the mapping without running the full calculate_position,
        # we'll create a simplified wrapper that calls _validate_market_bias directly
        # with our test state to see the logging output
        try:
            # Call _validate_market_bias directly with the expected mapped state
            # This will show up in the logs if mapping is correct
            risk_manager._validate_market_bias(
                market_state=expected_mapping,
                regime=state,
                base_leverage=1.0,
                final_leverage=1.0, 
                market_risk_factor=1.0,
                direction_bias=1.0
            )
            logger.info(f"✅ Successfully validated '{state}' -> '{expected_mapping}'")
        except Exception as e:
            logger.error(f"❌ Error validating '{state}': {e}")
    
    logger.info("Market bias mapping tests completed!")

if __name__ == "__main__":
    test_market_bias_mapping()
