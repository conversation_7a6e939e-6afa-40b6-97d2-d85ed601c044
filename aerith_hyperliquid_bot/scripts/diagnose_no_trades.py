#!/usr/bin/env python3
"""
Diagnose why modern system generates no trades
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
import logging
import pandas as pd

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_data_loader import RobustDataLoader
from hyperliquid_bot.modern.regime_state_manager import RegimeStateManager
from hyperliquid_bot.modern.continuous_detector_v2 import ModernContinuousDetectorV2
from hyperliquid_bot.modern.hourly_evaluator import HourlyStrategyEvaluator
from hyperliquid_bot.modern.tf_v3_modern import ModernTFV3Strategy

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Suppress some verbose loggers
for logger_name in ['ModernDataAdapter']:
    logging.getLogger(logger_name).setLevel(logging.WARNING)

print("=" * 60)
print("DIAGNOSING NO TRADES ISSUE")
print("=" * 60)

# Load config
config = load_config('configs/overrides/modern_system_v2_complete.yaml')

# Create components
data_loader = RobustDataLoader(config)
regime_manager = RegimeStateManager(mode='backtest')
detector = ModernContinuousDetectorV2(config)
strategy = ModernTFV3Strategy(config=config)
evaluator = HourlyStrategyEvaluator(
    config=config,
    regime_manager=regime_manager,
    strategy=strategy,
    mode='backtest'
)

# Test date
test_date = datetime(2024, 4, 2, 10, 0, 0)  # Middle of test period

print(f"\nTesting at: {test_date}")
print("=" * 60)

# Load data
warmup_start = test_date - timedelta(hours=100)
data = data_loader.load_data(warmup_start, test_date + timedelta(hours=1))
print(f"\n1. Data loaded: {len(data)} rows")
print(f"   Date range: {data.index[0]} to {data.index[-1]}")

# Get hour data
if test_date in data.index:
    hour_data = data.loc[test_date]
    print(f"\n2. Hour data at {test_date}:")
    print(f"   Close: ${hour_data['close']:.2f}")
    print(f"   Volume: {hour_data['volume']:.0f}")
    print(f"   ATR: {hour_data.get('atr_14_sec', 'N/A')}")
    
    # Check signals
    print(f"\n3. Microstructure signals:")
    print(f"   Volume imbalance: {hour_data.get('volume_imbalance', 0):.4f}")
    print(f"   Trade intensity: {hour_data.get('trade_intensity', 0):.4f}")
    print(f"   Price momentum: {hour_data.get('price_momentum', 0):.6f}")
    
    # Convert to dict
    hourly_bar = hour_data.to_dict()
    current_signals = {
        'volume_imbalance': hour_data.get('volume_imbalance', 0),
        'trade_intensity': hour_data.get('trade_intensity', 0),
        'price_momentum': hour_data.get('price_momentum', 0)
    }
    
    # Test evaluator
    print(f"\n4. Testing strategy evaluation...")
    try:
        signal = evaluator.evaluate(
            hourly_bar=hourly_bar,
            current_signals=current_signals,
            timestamp=test_date,
            ohlcv_history=data
        )
        
        print(f"\n5. Signal result:")
        if signal:
            print(f"   Action: {signal.get('action', 'None')}")
            print(f"   Direction: {signal.get('direction', 'None')}")
            print(f"   Size: {signal.get('size', 0)}")
            print(f"   Regime: {signal.get('regime', 'Unknown')}")
            print(f"   Full signal: {signal}")
        else:
            print("   No signal generated!")
            
            # Check regime
            regime_features = regime_manager.get_regime_features_for_strategy(
                timestamp=test_date,
                lookback_hours=4
            )
            print(f"\n6. Regime features:")
            print(f"   Current state: {regime_features.get('current_state')}")
            print(f"   Confidence: {regime_features.get('current_confidence')}")
            
    except Exception as e:
        print(f"   ERROR: {e}")
        import traceback
        traceback.print_exc()
else:
    print(f"ERROR: No data found for {test_date}")

# Check strategy conditions directly
print(f"\n7. Testing strategy conditions...")
# Calculate indicators needed by strategy
if len(data) > 26:
    close_prices = data['close']
    ema_12 = close_prices.ewm(span=12, adjust=False).mean()
    ema_26 = close_prices.ewm(span=26, adjust=False).mean()
    macd = ema_12 - ema_26
    
    print(f"   EMA 12: {ema_12.iloc[-1]:.2f}")
    print(f"   EMA 26: {ema_26.iloc[-1]:.2f}")
    print(f"   MACD: {macd.iloc[-1]:.2f}")
    
    # Check if MACD is positive (bullish) or negative (bearish)
    if macd.iloc[-1] > 0:
        print("   -> MACD is POSITIVE (potential long)")
    else:
        print("   -> MACD is NEGATIVE (potential short)")
        
# Check forecast threshold
print(f"\n8. Forecast threshold check:")
try:
    threshold = config.trading.forecast_threshold
    print(f"   Config threshold: {threshold}")
    print(f"   This means price must move {threshold * 100:.2f}% to trigger")
except:
    print("   Could not find forecast threshold in config")
    
# Update regime manually to test
print(f"\n9. Testing with manual regime update...")
regime_manager.update_state(
    timestamp=test_date,
    state='Weak_Bull_Trend',
    confidence=0.7,
    features={'momentum': 0.001, 'volatility': 0.01}
)

# Try evaluation again
signal2 = evaluator.evaluate(
    hourly_bar=hourly_bar,
    current_signals=current_signals,
    timestamp=test_date,
    ohlcv_history=data
)

print(f"\n10. Signal with regime:")
if signal2:
    print(f"   Action: {signal2.get('action', 'None')}")
    print(f"   Direction: {signal2.get('direction', 'None')}")
    print(f"   Size: {signal2.get('size', 0)}")
    print(f"   Full signal: {signal2}")
else:
    print("   Still no signal!")