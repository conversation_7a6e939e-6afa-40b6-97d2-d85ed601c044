#!/usr/bin/env python3
"""
Test Modern Strategy with Relaxed Regime Requirements
=====================================================

Temporarily relax regime stability requirements to verify the system works.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
import json


def main():
    print("=== Testing Modern System with Relaxed Requirements ===\n")
    
    # Monkey patch the strategy to relax requirements
    from hyperliquid_bot.modern import tf_v3_modern
    
    original_is_regime_stable = tf_v3_modern.ModernTFV3Strategy._is_regime_stable
    
    def relaxed_is_regime_stable(self, regime_features):
        """Relaxed regime stability check for testing."""
        # Just check confidence, ignore persistence
        confidence = regime_features.get('current_confidence', 0.0)
        if confidence < 0.5:  # Relaxed from 0.6
            return False
        
        # Ignore state persistence check
        # Ignore recent transitions check
        
        return True
    
    # Apply patch
    tf_v3_modern.ModernTFV3Strategy._is_regime_stable = relaxed_is_regime_stable
    
    # Now run backtest
    from hyperliquid_bot.config.settings import load_config
    from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
    
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 16)
    
    print(f"Running backtest from {start_date} to {end_date}")
    print("With RELAXED regime requirements:\n")
    print("  - Confidence threshold: 0.5 (was 0.6)")
    print("  - State persistence: IGNORED (was >= 0.5)")
    print("  - Recent transitions: IGNORED (was <= 5)")
    print("\n" + "-"*60 + "\n")
    
    try:
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        results = engine.run_backtest()
        
        print(f"\n✅ Backtest completed!")
        print(f"Total trades: {results['performance']['total_trades']}")
        print(f"Total return: {results['performance']['total_return']:.2%}")
        print(f"Win rate: {results['performance']['win_rate']:.2%}")
        
        if results['trades']:
            print(f"\n📈 First 5 trades:")
            for i, trade in enumerate(results['trades'][:5]):
                print(
                    f"  {i+1}. {trade['timestamp']} - "
                    f"{trade['direction'].upper()} @ {trade['entry_price']:.2f} "
                    f"(size: {trade['position_size']:.2%})"
                )
        
        # Save results
        with open('relaxed_modern_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: relaxed_modern_results.json")
        
        # Analysis
        if results['performance']['total_trades'] > 0:
            print("\n✅ SUCCESS! The system works with relaxed requirements.")
            print("The issue is that regime stability requirements are too strict.")
            print("\nNext steps:")
            print("1. Tune state_persistence threshold (currently 0.5)")
            print("2. Tune recent_transitions threshold (currently 5*6=30)")
            print("3. Consider using regime confidence only")
        else:
            print("\n❌ Still no trades even with relaxed requirements!")
            print("The issue might be:")
            print("1. EMA crossover not aligning with regime")
            print("2. Data quality issues")
            print("3. Other hidden constraints")
        
    except Exception as e:
        print(f"\n❌ Backtest error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()