#!/usr/bin/env python3
"""
Analyze the regime cache file to understand what's being cached
"""

import pandas as pd
import os

def analyze_regime_cache():
    cache_file = "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/data/precomputed_regimes/regimes_2024.parquet"
    
    if os.path.exists(cache_file):
        print(f"Loading regime cache: {cache_file}")
        df = pd.read_parquet(cache_file)
        
        print(f"\nCache shape: {df.shape}")
        print(f"Date range: {df.index.min()} to {df.index.max()}")
        
        # Calculate time between entries
        time_diffs = df.index.to_series().diff().dropna()
        avg_seconds = time_diffs.mean().total_seconds()
        
        print(f"\nAverage seconds between entries: {avg_seconds:.1f}")
        print(f"This is: {avg_seconds/60:.1f} minutes")
        print(f"Or: {avg_seconds/3600:.1f} hours")
        
        if avg_seconds >= 3000:  # More than 50 minutes
            print("\n❌ SMOKING GUN: Cache contains HOURLY regimes!")
            print("   Modern system is NOT updating every 60 seconds!")
        
        # Show sample
        print(f"\nFirst 10 entries:")
        print(df.head(10))
        
        # Show regime distribution
        if 'regime' in df.columns:
            print(f"\nRegime distribution:")
            regime_counts = df['regime'].value_counts()
            total = len(df)
            for regime, count in regime_counts.items():
                pct = count / total * 100
                print(f"  {regime}: {count} ({pct:.1f}%)")
    else:
        print(f"❌ Cache file not found: {cache_file}")

if __name__ == "__main__":
    analyze_regime_cache()