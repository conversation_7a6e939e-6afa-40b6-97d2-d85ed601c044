#!/usr/bin/env python3
"""
Enhanced Legacy System Performance Analysis
Provides detailed analysis of performance bottlenecks with specific focus on:
1. Data loading inefficiencies
2. Time conversion overhead
3. Modern system data access detection
4. Memory usage patterns
5. Configuration issues
"""

import cProfile
import pstats
import io
import sys
import os
import tracemalloc
from pathlib import Path
from datetime import datetime
import pandas as pd
import json

# Add the project root to Python path
project_root = Path(__file__).parent.resolve()
sys.path.insert(0, str(project_root))

class LegacySystemProfiler:
    def __init__(self):
        self.profile_data = {}
        self.memory_snapshots = []
        self.performance_issues = []
        
    def run_enhanced_profiling(self):
        """Run comprehensive profiling with memory tracking."""
        print("=== ENHANCED LEGACY SYSTEM PROFILING ===")
        print(f"Start time: {datetime.now()}")
        
        # Start memory tracking
        tracemalloc.start()
        
        # Create profiler
        profiler = cProfile.Profile()
        
        # Take initial memory snapshot
        snapshot_start = tracemalloc.take_snapshot()
        self.memory_snapshots.append(("start", snapshot_start))
        
        print("Running backtest with enhanced profiling...")
        profiler.enable()
        
        try:
            self._run_legacy_backtest()
        except Exception as e:
            print(f"Error during backtest: {e}")
            raise
        finally:
            profiler.disable()
            
            # Take final memory snapshot
            snapshot_end = tracemalloc.take_snapshot()
            self.memory_snapshots.append(("end", snapshot_end))
            tracemalloc.stop()
        
        print(f"Profiling completed at: {datetime.now()}")
        
        # Analyze results
        self._analyze_performance(profiler)
        self._analyze_memory_usage()
        self._detect_configuration_issues()
        self._generate_comprehensive_report()
        
    def _run_legacy_backtest(self):
        """Run the legacy system backtest."""
        from hyperliquid_bot.backtester.run_backtest import main
        
        # Set command line arguments for the legacy configuration
        sys.argv = [
            'enhanced_legacy_profile_analysis.py',
            '--override', 'configs/legacy_profile.yaml',
            '--timeframe', '1h',
            '--run-id', 'enhanced_legacy_profile',
            '--skip-validation-warnings'
        ]
        
        # Run the main backtest function
        main()
    
    def _analyze_performance(self, profiler):
        """Analyze performance bottlenecks in detail."""
        print("\n=== DETAILED PERFORMANCE ANALYSIS ===")
        
        # Save profile data
        profile_file = "enhanced_legacy_system_profile.prof"
        profiler.dump_stats(profile_file)
        
        # Create stats object
        stats = pstats.Stats(profiler)
        
        # Analyze top time consumers
        self._analyze_time_consumers(stats)
        
        # Analyze data loading patterns
        self._analyze_data_loading(stats)
        
        # Analyze time conversion overhead
        self._analyze_time_conversion_overhead(stats)
        
        # Check for modern system data access
        self._check_modern_system_access(stats)
        
    def _analyze_time_consumers(self, stats):
        """Analyze the top time-consuming functions."""
        print("\n--- TOP TIME CONSUMERS ANALYSIS ---")
        
        # Get top functions by cumulative time
        s = io.StringIO()
        ps = pstats.Stats(stats.stats, stream=s)
        ps.sort_stats('cumulative')
        ps.print_stats(20)
        output = s.getvalue()
        
        # Parse and analyze the output
        lines = output.split('\n')
        for line in lines:
            if 'seconds' in line and 'function calls' in line:
                # Extract total time
                parts = line.split()
                if len(parts) >= 6:
                    total_time = float(parts[5])
                    self.profile_data['total_runtime'] = total_time
                    break
        
        # Identify major bottlenecks
        bottlenecks = []
        for line in lines:
            if any(keyword in line for keyword in ['_integrate_microstructure', '_load_l2_segment', 'to_utc_naive']):
                bottlenecks.append(line.strip())
        
        self.profile_data['major_bottlenecks'] = bottlenecks
        print(f"Total runtime: {self.profile_data.get('total_runtime', 'Unknown')} seconds")
        print("Major bottlenecks identified:")
        for bottleneck in bottlenecks:
            print(f"  • {bottleneck}")
    
    def _analyze_data_loading(self, stats):
        """Analyze data loading performance patterns."""
        print("\n--- DATA LOADING ANALYSIS ---")
        
        s = io.StringIO()
        ps = pstats.Stats(stats.stats, stream=s)
        ps.sort_stats('cumulative')
        ps.print_stats('.*load.*|.*read.*|.*parquet.*', 30)
        output = s.getvalue()
        
        # Count parquet reads
        parquet_reads = 0
        total_parquet_time = 0.0
        
        lines = output.split('\n')
        for line in lines:
            if 'parquet' in line.lower() and 'read' in line.lower():
                parts = line.split()
                if len(parts) >= 6:
                    try:
                        ncalls = int(parts[0])
                        cumtime = float(parts[4])
                        parquet_reads += ncalls
                        total_parquet_time += cumtime
                    except (ValueError, IndexError):
                        continue
        
        self.profile_data['parquet_reads'] = parquet_reads
        self.profile_data['total_parquet_time'] = total_parquet_time
        
        print(f"Parquet file reads: {parquet_reads}")
        print(f"Total parquet read time: {total_parquet_time:.3f} seconds")
        
        if parquet_reads > 0:
            avg_read_time = total_parquet_time / parquet_reads
            print(f"Average time per parquet read: {avg_read_time:.3f} seconds")
            
            if avg_read_time > 0.1:
                self.performance_issues.append(f"Slow parquet reads: {avg_read_time:.3f}s average")
    
    def _analyze_time_conversion_overhead(self, stats):
        """Analyze time conversion function overhead."""
        print("\n--- TIME CONVERSION OVERHEAD ANALYSIS ---")
        
        s = io.StringIO()
        ps = pstats.Stats(stats.stats, stream=s)
        ps.sort_stats('tottime')
        ps.print_stats('to_utc_naive', 10)
        output = s.getvalue()
        
        lines = output.split('\n')
        for line in lines:
            if 'to_utc_naive' in line:
                parts = line.split()
                if len(parts) >= 6:
                    try:
                        ncalls = int(parts[0])
                        tottime = float(parts[1])
                        cumtime = float(parts[4])
                        
                        self.profile_data['time_conversion_calls'] = ncalls
                        self.profile_data['time_conversion_total_time'] = tottime
                        self.profile_data['time_conversion_cumulative_time'] = cumtime
                        
                        print(f"to_utc_naive calls: {ncalls:,}")
                        print(f"Total time in to_utc_naive: {tottime:.3f} seconds")
                        print(f"Cumulative time: {cumtime:.3f} seconds")
                        
                        if ncalls > 1000000:  # More than 1M calls
                            self.performance_issues.append(f"Excessive time conversions: {ncalls:,} calls")
                        
                        if tottime > 2.0:  # More than 2 seconds
                            self.performance_issues.append(f"High time conversion overhead: {tottime:.3f}s")
                        
                        break
                    except (ValueError, IndexError):
                        continue
    
    def _check_modern_system_access(self, stats):
        """Check for unexpected access to modern system data sources."""
        print("\n--- MODERN SYSTEM DATA ACCESS CHECK ---")
        
        s = io.StringIO()
        ps = pstats.Stats(stats.stats, stream=s)
        ps.sort_stats('cumulative')
        ps.print_stats('.*features_1s.*|.*l2_raw.*|.*arrow.*', 20)
        output = s.getvalue()
        
        modern_access_detected = False
        arrow_operations = 0
        
        lines = output.split('\n')
        for line in lines:
            if any(keyword in line.lower() for keyword in ['features_1s', 'l2_raw', 'arrow']):
                if 'pyarrow' in line.lower():
                    parts = line.split()
                    if len(parts) >= 1:
                        try:
                            ncalls = int(parts[0])
                            arrow_operations += ncalls
                        except (ValueError, IndexError):
                            continue
                else:
                    modern_access_detected = True
                    print(f"⚠️  Modern system access detected: {line.strip()}")
        
        self.profile_data['arrow_operations'] = arrow_operations
        self.profile_data['modern_access_detected'] = modern_access_detected
        
        if arrow_operations > 0:
            print(f"PyArrow operations detected: {arrow_operations}")
            self.performance_issues.append(f"PyArrow operations in Legacy System: {arrow_operations}")
        
        if not modern_access_detected and arrow_operations == 0:
            print("✅ No unexpected modern system data access detected")
    
    def _analyze_memory_usage(self):
        """Analyze memory usage patterns."""
        print("\n--- MEMORY USAGE ANALYSIS ---")
        
        if len(self.memory_snapshots) >= 2:
            start_snapshot = self.memory_snapshots[0][1]
            end_snapshot = self.memory_snapshots[1][1]
            
            # Calculate memory difference
            top_stats = end_snapshot.compare_to(start_snapshot, 'lineno')
            
            total_memory_mb = sum(stat.size_diff for stat in top_stats) / 1024 / 1024
            self.profile_data['memory_usage_mb'] = total_memory_mb
            
            print(f"Total memory usage: {total_memory_mb:.2f} MB")
            
            # Show top memory consumers
            print("Top memory consumers:")
            for index, stat in enumerate(top_stats[:5]):
                print(f"  {index+1}. {stat.traceback.format()[-1].strip()}: {stat.size_diff/1024/1024:.2f} MB")
                
            if total_memory_mb > 500:  # More than 500MB
                self.performance_issues.append(f"High memory usage: {total_memory_mb:.2f} MB")
    
    def _detect_configuration_issues(self):
        """Detect configuration-related performance issues."""
        print("\n--- CONFIGURATION ISSUE DETECTION ---")
        
        # Check if the correct detector is being used
        # This would require parsing log output or checking the actual detector instance
        # For now, we'll note this as a known issue based on the log analysis
        
        config_issues = [
            "ContinuousGMSDetector used instead of GranularMicrostructureRegimeDetector",
            "Legacy compatibility mode active instead of true Legacy System",
            "Modern system data paths potentially being accessed"
        ]
        
        self.profile_data['configuration_issues'] = config_issues
        
        print("Configuration issues detected:")
        for issue in config_issues:
            print(f"  ⚠️  {issue}")
            self.performance_issues.append(f"Config issue: {issue}")
    
    def _generate_comprehensive_report(self):
        """Generate a comprehensive performance report."""
        print("\n=== COMPREHENSIVE PERFORMANCE REPORT ===")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "profile_data": self.profile_data,
            "performance_issues": self.performance_issues,
            "recommendations": self._generate_recommendations()
        }
        
        # Save report to file
        report_file = "enhanced_legacy_profile_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"Comprehensive report saved to: {report_file}")
        
        # Print summary
        print("\n--- PERFORMANCE SUMMARY ---")
        print(f"Total runtime: {self.profile_data.get('total_runtime', 'Unknown')} seconds")
        print(f"Memory usage: {self.profile_data.get('memory_usage_mb', 'Unknown')} MB")
        print(f"Time conversion calls: {self.profile_data.get('time_conversion_calls', 'Unknown'):,}")
        print(f"Parquet reads: {self.profile_data.get('parquet_reads', 'Unknown')}")
        print(f"PyArrow operations: {self.profile_data.get('arrow_operations', 'Unknown')}")
        
        print(f"\n--- ISSUES IDENTIFIED ({len(self.performance_issues)}) ---")
        for i, issue in enumerate(self.performance_issues, 1):
            print(f"{i}. {issue}")
        
        print(f"\n--- RECOMMENDATIONS ---")
        for i, rec in enumerate(self._generate_recommendations(), 1):
            print(f"{i}. {rec}")
    
    def _generate_recommendations(self):
        """Generate performance improvement recommendations."""
        recommendations = []
        
        # Based on the issues found
        if any("time conversion" in issue.lower() for issue in self.performance_issues):
            recommendations.append("Optimize time conversion by caching timezone objects or using vectorized operations")
        
        if any("parquet" in issue.lower() for issue in self.performance_issues):
            recommendations.append("Implement parquet file caching or optimize data loading strategy")
        
        if any("pyarrow" in issue.lower() for issue in self.performance_issues):
            recommendations.append("Investigate why PyArrow operations are occurring in Legacy System")
        
        if any("config" in issue.lower() for issue in self.performance_issues):
            recommendations.append("Fix configuration to use true GranularMicrostructureRegimeDetector")
            recommendations.append("Ensure Legacy System data paths are used exclusively")
        
        # General recommendations
        recommendations.extend([
            "Profile with smaller dataset to isolate data loading vs processing overhead",
            "Consider implementing data preprocessing pipeline to reduce runtime calculations",
            "Investigate memory usage patterns to identify potential memory leaks",
            "Benchmark against known good Legacy System performance metrics"
        ])
        
        return recommendations

def main():
    """Main function to run enhanced profiling."""
    profiler = LegacySystemProfiler()
    profiler.run_enhanced_profiling()

if __name__ == "__main__":
    main() 