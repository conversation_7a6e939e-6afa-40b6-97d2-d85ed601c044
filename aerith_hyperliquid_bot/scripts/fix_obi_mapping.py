#!/usr/bin/env python3
"""
Fix the OBI mapping issue so detector can find the right column
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_backtest_engine import RobustBacktestEngine

# Monkey patch the detector to use volume_imbalance instead of obi_smoothed_5
from hyperliquid_bot.legacy.detector import LegacyGranularMicrostructureDetector

# Save original method
original_detect = LegacyGranularMicrostructureDetector.detect_regime

def patched_detect_regime(self, signals, timestamp=None):
    """Patched version that maps volume_imbalance to obi_smoothed_5"""
    # Create a copy of signals
    patched_signals = signals.copy() if isinstance(signals, dict) else signals.to_dict()
    
    # Map volume_imbalance to obi_smoothed_5
    if 'volume_imbalance' in patched_signals and f'obi_smoothed_{self.depth_levels}' not in patched_signals:
        patched_signals[f'obi_smoothed_{self.depth_levels}'] = patched_signals['volume_imbalance']
        
    # Map atr_percent_sec to atr_percent
    if 'atr_percent_sec' in patched_signals and 'atr_percent' not in patched_signals:
        patched_signals['atr_percent'] = patched_signals['atr_percent_sec']
    
    # Call original with patched signals
    return original_detect(self, patched_signals, timestamp)

# Apply patch
LegacyGranularMicrostructureDetector.detect_regime = patched_detect_regime

# Now run the baseline test
print("Running baseline test with OBI mapping fix...")

config = load_config("configs/overrides/modern_system_v2_complete.yaml")
config.regime.detector_type = "enhanced"

engine = RobustBacktestEngine(
    config=config,
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 7),  # One week test
    use_regime_cache=False
)

results = engine.run_backtest()

print(f"\n=== RESULTS ===")
print(f"Total trades: {len(results.get('trades', []))}")
print(f"Total return: {results.get('total_return', 0):.2%}")
print(f"Regime sources: {results.get('regime_sources', {})}")

# Check if detector is now working
if results.get('regime_sources', {}).get('detector_used', 0) > 0:
    print("\n✅ SUCCESS: Detector is now calculating regimes!")
else:
    print("\n❌ Still using fallback regimes only")