#!/usr/bin/env python3
"""
Fixed Modern System Backtest Runner
===================================
This version addresses the data flow issue:
- Resamples features_1s to 1h bars for backtesting (as per MODERN_SYSTEM_EXPECTED.md)
- Keeps 60s regime updates
- Uses proper field mappings
"""

import argparse
import sys
from pathlib import Path
from datetime import datetime
import json
import logging
import pandas as pd
import time

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
from hyperliquid_bot.backtester.backtester import Backtester
from hyperliquid_bot.strategies.evaluator import StrategyEvaluator
from hyperliquid_bot.portfolio.portfolio import Portfolio
from hyperliquid_bot.execution.simulation import SimulationE<PERSON>ine


def resample_features_to_hourly(features_1s_dir: Path, start_date: datetime, end_date: datetime) -> pd.DataFrame:
    """
    Resample 1-second features to hourly bars for backtesting.
    
    According to MODERN_SYSTEM_EXPECTED.md:
    - For backtesting: features_1s → Resample to 1h bars → Feed to TF-v3
    """
    logging.info("Resampling features_1s to hourly bars for backtesting...")
    
    all_hourly_data = []
    current_date = start_date.date()
    
    while current_date <= end_date.date():
        date_dir = features_1s_dir / current_date.strftime('%Y-%m-%d')
        if not date_dir.exists():
            logging.warning(f"Date directory not found: {date_dir}")
            current_date += pd.Timedelta(days=1)
            continue
            
        # Load all hourly files for this date
        for hour_file in sorted(date_dir.glob('features_*.parquet')):
            try:
                df_1s = pd.read_parquet(hour_file)
                
                # Set timestamp as index
                df_1s['timestamp'] = pd.to_datetime(df_1s['timestamp'])
                df_1s.set_index('timestamp', inplace=True)
                
                # Resample to hourly
                agg_dict = {
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'volume': 'sum',
                    'obi_smoothed': 'mean',  # Will be mapped to volume_imbalance
                    'spread_mean': 'mean',
                    'spread_std': 'mean',
                    'atr_14_sec': 'last',
                    'ma_slope': 'last',
                    'ma_slope_ema_30s': 'last'
                }
                
                # Filter to only columns that exist
                agg_dict_filtered = {k: v for k, v in agg_dict.items() if k in df_1s.columns}
                
                # Resample with proper labeling
                df_hourly = df_1s.resample('1h', label='right', closed='left').agg(agg_dict_filtered)
                
                # Add timestamp column back
                df_hourly['timestamp'] = df_hourly.index
                
                # Map obi_smoothed to volume_imbalance
                if 'obi_smoothed' in df_hourly.columns:
                    df_hourly['volume_imbalance'] = df_hourly['obi_smoothed']
                    df_hourly['imbalance'] = df_hourly['obi_smoothed']  # Also add legacy field
                
                all_hourly_data.append(df_hourly)
                
            except Exception as e:
                logging.error(f"Error processing {hour_file}: {e}")
                continue
        
        current_date += pd.Timedelta(days=1)
    
    if all_hourly_data:
        combined = pd.concat(all_hourly_data, ignore_index=False)
        combined = combined[(combined.index >= start_date) & (combined.index < end_date)]
        logging.info(f"Resampled {len(combined)} hourly bars")
        return combined
    else:
        logging.error("No data resampled!")
        return pd.DataFrame()


def run_fast_backtest(config_path: str, start_date: datetime, end_date: datetime):
    """Run backtest using the legacy engine with modern config for faster execution."""
    logging.info("Running fast backtest with resampled hourly data...")
    
    # Load config
    config = load_config(config_path=config_path)
    
    # Get features_1s directory
    features_dir = Path(config.data_paths.feature_1s_dir)
    
    # Resample data to hourly
    hourly_data = resample_features_to_hourly(features_dir, start_date, end_date)
    
    if hourly_data.empty:
        raise ValueError("No data available after resampling")
    
    # Create components
    portfolio = Portfolio(config)
    strategy_evaluator = StrategyEvaluator(config, feature_store=None)
    simulation_engine = SimulationEngine(config)
    
    # Create backtester with pre-loaded data
    backtester = Backtester(
        config=config,
        portfolio=portfolio,
        strategy_evaluator=strategy_evaluator,
        simulation_engine=simulation_engine,
        data_handler=None  # We'll provide data directly
    )
    
    # Monkey-patch data loading to use our resampled data
    original_load = backtester.data_handler.load_data if hasattr(backtester, 'data_handler') else None
    
    def mock_load_data(start, end):
        return hourly_data[(hourly_data.index >= start) & (hourly_data.index < end)].reset_index()
    
    if hasattr(backtester, 'data_handler'):
        backtester.data_handler.load_data = mock_load_data
    
    # Run backtest
    start_time = time.time()
    results = backtester.run(start_date, end_date)
    runtime = time.time() - start_time
    
    # Format results
    formatted_results = {
        'performance': {
            'total_trades': len(results.get('trades', [])),
            'total_return': results.get('total_return', 0),
            'win_rate': results.get('win_rate', 0),
            'sharpe_ratio': results.get('sharpe_ratio', 0),
            'max_drawdown': results.get('max_drawdown', 0)
        },
        'trades': results.get('trades', []),
        'runtime_seconds': runtime,
        'config': {
            'detector_type': config.regime.detector_type,
            'risk_per_trade': config.portfolio.risk_per_trade,
            'start_date': str(start_date),
            'end_date': str(end_date)
        }
    }
    
    return formatted_results


def main():
    """Run modern system backtest with proper data resampling."""
    parser = argparse.ArgumentParser(description="Run modern system backtest (fixed)")
    
    parser.add_argument('--start-date', type=str, default='2024-01-01')
    parser.add_argument('--end-date', type=str, default='2024-01-02')
    parser.add_argument('--override', type=str, default='configs/overrides/modern_system_v2_complete.yaml')
    parser.add_argument('--output', type=str, default='modern_backtest_results.json')
    parser.add_argument('--use-slow-engine', action='store_true', help='Use original slow engine')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("=" * 80)
    print("MODERN SYSTEM BACKTEST - FIXED VERSION")
    print("=" * 80)
    print(f"Config: {args.override}")
    print(f"Period: {args.start_date} to {args.end_date}")
    print()
    
    # Parse dates
    start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
    end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
    
    # Resolve config path
    override_path = Path(args.override)
    if not override_path.is_absolute():
        override_path = project_root / override_path
    
    try:
        if args.use_slow_engine:
            print("Using original ModernBacktestEngine (slow)...")
            config = load_config(config_path=str(override_path))
            engine = ModernBacktestEngine(config=config, start_date=start_date, end_date=end_date)
            results = engine.run_backtest()
        else:
            print("Using fast backtest with hourly resampling...")
            results = run_fast_backtest(str(override_path), start_date, end_date)
        
        # Display results
        print("-" * 80)
        print("✅ Backtest completed!")
        print(f"Runtime: {results['runtime_seconds']:.1f} seconds")
        print()
        print("📊 RESULTS:")
        print(f"  Total Trades: {results['performance']['total_trades']}")
        print(f"  Total Return: {results['performance']['total_return']:.2%}")
        print(f"  Win Rate: {results['performance'].get('win_rate', 0):.2%}")
        
        # Save results
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n💾 Results saved to: {args.output}")
        
        # Show first few trades
        if results['trades']:
            print(f"\n📈 First 3 trades:")
            for i, trade in enumerate(results['trades'][:3]):
                print(f"  {i+1}. {trade.get('timestamp', 'N/A')} - "
                      f"{trade.get('direction', 'N/A').upper()} @ {trade.get('entry_price', 0):.2f}")
        else:
            print("\n⚠️  No trades generated!")
            print("\nPossible issues:")
            print("  1. Risk per trade too low (should be 0.25)")
            print("  2. Regime thresholds too restrictive")
            print("  3. Missing required signals")
            
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()