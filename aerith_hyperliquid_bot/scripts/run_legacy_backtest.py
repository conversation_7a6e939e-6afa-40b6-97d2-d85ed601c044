#!/usr/bin/env python3
"""
Run Legacy System Backtest
==========================
This script runs the working legacy trading system with its frozen configuration.
Expected results: ~180 trades, +215% ROI

DO NOT MODIFY THE LEGACY SYSTEM - IT IS OUR WORKING BASELINE!
"""

import subprocess
import sys
from pathlib import Path
from datetime import datetime

def main():
    """Run legacy system backtest with frozen configuration."""
    print("=" * 80)
    print("AERITH HYPERLIQUID BOT - LEGACY SYSTEM BACKTEST")
    print("=" * 80)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Set up paths
    project_root = Path(__file__).parent.parent
    config_path = project_root / "configs" / "overrides" / "legacy_system.yaml"
    backtest_script = project_root / "hyperliquid_bot" / "backtester" / "run_backtest.py"
    
    # Verify files exist
    if not config_path.exists():
        print(f"❌ ERROR: Legacy config not found at {config_path}")
        sys.exit(1)
    
    if not backtest_script.exists():
        print(f"❌ ERROR: Backtest script not found at {backtest_script}")
        sys.exit(1)
    
    print(f"📁 Project root: {project_root}")
    print(f"⚙️  Config: {config_path.name}")
    print(f"🚀 Running legacy system backtest...")
    print()
    print("Expected results:")
    print("  - Total trades: ~180")
    print("  - Total return: ~215%")
    print("  - Strategy: TF-v2 (legacy)")
    print("  - Detector: granular_microstructure")
    print("  - Risk per trade: 25%")
    print()
    print("-" * 80)
    
    # Run the backtest
    try:
        result = subprocess.run([
            sys.executable,
            str(backtest_script),
            "--override", str(config_path)
        ], check=True)
        
        print("-" * 80)
        print(f"✅ Legacy backtest completed successfully!")
        print(f"Finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except subprocess.CalledProcessError as e:
        print("-" * 80)
        print(f"❌ Legacy backtest failed with exit code {e.returncode}")
        sys.exit(e.returncode)
    except Exception as e:
        print("-" * 80)
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()