#!/usr/bin/env python3
"""
Test fixing the signal engine issue
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from datetime import datetime
import pandas as pd
import numpy as np

from hyperliquid_bot.config.settings import load_config

# Load config
config = load_config("configs/overrides/modern_system_v2_complete.yaml")

# Create test data
timestamps = pd.date_range(start='2024-01-01', periods=100, freq='h')
test_data = pd.DataFrame({
    'open': 42000 + np.random.randn(100) * 100,
    'high': 42100 + np.random.randn(100) * 100,
    'low': 41900 + np.random.randn(100) * 100,
    'close': 42000 + np.random.randn(100) * 100,
    'volume': np.random.rand(100) * 1000000,
    'atr_14_sec': 400 + np.random.randn(100) * 50,
    'atr_percent_sec': 0.008 + np.random.randn(100) * 0.001
}, index=timestamps)

# Test signal engine
from hyperliquid_bot.modern.signal_engine import ModernSignalEngine
signal_engine = ModernSignalEngine(config)

print("Testing signal engine with DataFrame...")
try:
    # Test with full DataFrame
    signals = signal_engine.calculate_signals(test_data, {})
    print(f"✓ Success with DataFrame! Got {len(signals)} signals")
    print(f"  EMAs: fast={signals.get('ema_fast', 'N/A')}, slow={signals.get('ema_slow', 'N/A')}")
    print(f"  ATR: {signals.get('atr_14', 'N/A')}")
except Exception as e:
    print(f"✗ Failed: {e}")

print("\nTesting with single row (Series)...")
try:
    # Test with single row - this should fail
    single_row = test_data.iloc[-1]
    signals = signal_engine.calculate_signals(single_row, {})
    print(f"✓ Success with Series! Got {len(signals)} signals")
except Exception as e:
    print(f"✗ Failed as expected: {type(e).__name__}: {e}")

print("\nTesting with single-row DataFrame...")
try:
    # Test with single-row DataFrame - this should also have issues
    single_df = pd.DataFrame([test_data.iloc[-1]])
    signals = signal_engine.calculate_signals(single_df, {})
    print(f"✓ Success with single-row DataFrame! Got {len(signals)} signals")
    print(f"  But indicators might be NaN: ema_fast={signals.get('ema_fast', 'N/A')}")
except Exception as e:
    print(f"✗ Failed: {e}")