# Instruction for Cursor: Create scripts/analyze_gms_clusters.py with the following content:

import pandas as pd
import numpy as np
from pathlib import Path
import scipy.cluster.hierarchy as sch
import pickle

def analyze_and_map_gms_states(log_dir_path: str, output_mapping_file: str = "gms_state_mapping.pkl"):
    """
    Analyzes the performance of GMS states from the latest backtest signals file,
    clusters them into 3 regimes (BULL, BEAR, CHOP) based on mean return and volatility,
    and saves the mapping.

    Args:
        log_dir_path (str): Path to the directory containing backtest log files.
        output_mapping_file (str): Filename for saving the pickled mapping dictionary.
    """
    log_dir = Path(log_dir_path)
    output_mapping_path = log_dir / output_mapping_file

    # 1. Find the latest backtest signals file
    try:
        latest_signals_file = max(log_dir.glob("backtest_signals_*.parquet"), key=lambda p: p.stat().st_mtime)
        print(f"Found latest signals file: {latest_signals_file}")
    except ValueError:
        print(f"Error: No 'backtest_signals_*.parquet' files found in {log_dir_path}")
        return

    # 2. Load the data
    try:
        df = pd.read_parquet(latest_signals_file)
        print(f"Loaded data shape: {df.shape}")
        # Verify required columns
        if 'regime' not in df.columns or 'log_ret' not in df.columns:
            print(f"Error: Required columns 'regime' or 'log_ret' not found in {latest_signals_file}")
            return

        # --- Start Diagnostics ---
        print("\n--- DataFrame Info ---")
        df.info()
        print("\n--- Head of 'regime' and 'log_ret' ---")
        print(df[['regime', 'log_ret']].head())
        print("\n--- Value Counts for 'regime' ---")
        print(df['regime'].value_counts(dropna=False))
        print("\n--- Description of 'log_ret' (before cleaning) ---")
        print(df['log_ret'].describe())
        # --- End Diagnostics ---

    except Exception as e:
        print(f"Error loading Parquet file {latest_signals_file}: {e}")
        return

    # 3. Calculate state statistics
    print("Calculating statistics per GMS state...")
    # Ensure log_ret is numeric and handle potential infinities from calculation
    df['log_ret'] = pd.to_numeric(df['log_ret'], errors='coerce')
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    state_stats = df.groupby('regime')['log_ret'].agg(['mean', 'std']).reset_index()
    state_stats.rename(columns={'mean': 'mean_ret', 'std': 'vol'}, inplace=True)

    # --- Start Diagnostics ---
    print("\nRaw GMS State Statistics (before dropna/fillna):")
    print(state_stats)
    # --- End Diagnostics ---

    # Handle cases where std might be NaN (e.g., single data point per state)
    state_stats['vol'] = state_stats['vol'].fillna(0)
    # Handle cases where mean might be NaN
    state_stats.dropna(subset=['mean_ret'], inplace=True)

    print("\nInitial GMS State Statistics:")
    print(state_stats)

    if state_stats.shape[0] < 3:
         print(f"\nError: Found only {state_stats.shape[0]} valid GMS states with statistics. Need at least 3 for clustering.")
         return
    elif state_stats.shape[0] != 7:
         print(f"\nWarning: Expected 7 GMS states, but found {state_stats.shape[0]} with valid stats.")


    # 4. Perform Hierarchical Clustering (on mean return and volatility)
    print("\nPerforming hierarchical clustering...")
    # Prepare data for clustering (mean_ret, vol)
    clustering_data = state_stats[['mean_ret', 'vol']].values

    # Calculate the linkage matrix using Ward's method
    linked = sch.linkage(clustering_data, method='ward')

    # Assign cluster labels (k=3)
    n_clusters = 3
    state_stats['cluster'] = sch.fcluster(linked, n_clusters, criterion='maxclust')

    print("\nClustering Results (Cluster labels assigned):")
    print(state_stats)

    # 5. Map clusters to BULL/BEAR/CHOP regimes
    print("\nMapping clusters to BULL/BEAR/CHOP...")
    # Calculate the average mean return for each cluster
    cluster_means = state_stats.groupby('cluster')['mean_ret'].mean().sort_values()

    if len(cluster_means) != n_clusters:
        print(f"Error: Clustering resulted in {len(cluster_means)} clusters, expected {n_clusters}. Cannot map.")
        # Optionally print linkage matrix for debugging
        # print("\nLinkage Matrix:")
        # print(linked)
        return

    # Assign regime labels based on sorted cluster means
    bull_cluster = cluster_means.index[-1] # Highest mean return
    bear_cluster = cluster_means.index[0]  # Lowest mean return
    chop_cluster = cluster_means.index[1]  # Middle mean return

    cluster_to_regime = {
        bull_cluster: 'BULL',
        bear_cluster: 'BEAR',
        chop_cluster: 'CHOP'
    }

    state_stats['mapped_regime'] = state_stats['cluster'].map(cluster_to_regime)

    print("\nFinal GMS State Statistics with Mapped Regimes:")
    print(state_stats)

    # 6. Create the final mapping dictionary
    final_mapping = state_stats.set_index('regime')['mapped_regime'].to_dict()

    print("\nFinal GMS State -> BULL/BEAR/CHOP Mapping:")
    print(final_mapping)

    # 7. Save the mapping dictionary using pickle
    try:
        with open(output_mapping_path, 'wb') as f:
            pickle.dump(final_mapping, f)
        print(f"\nSuccessfully saved mapping to: {output_mapping_path}")
    except Exception as e:
        print(f"\nError saving mapping dictionary to {output_mapping_path}: {e}")


if __name__ == "__main__":
    # Define the path to the log directory
    # Instruction for Cursor: Ensure this path is correct for the user's system.
    LOG_DIRECTORY = "/Users/<USER>/Desktop/trading_bot_/logs/"
    analyze_and_map_gms_states(LOG_DIRECTORY)