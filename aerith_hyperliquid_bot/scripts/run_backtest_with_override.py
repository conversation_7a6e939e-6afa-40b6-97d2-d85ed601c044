# scripts/run_backtest_with_override.py
"""
Run the backtester using a specified override config file (e.g. 1h_setA.yaml) without modifying any backtest logic.

Usage:
    python scripts/run_backtest_with_override.py

If no override is specified, falls back to base config.
"""
import sys
import os
from pathlib import Path

if __name__ == "__main__":
    # Always use 4knob_tuning config by default
    config_path = "aerith_hyperliquid_bot/configs/overrides/4knob_tuning.yaml"

    # Absolute path resolution for safety
    config_path = str(Path(config_path).resolve())

    # Import the backtest runner
    # Assumes run_backtest.py is the standard entry point
    backtest_script = Path(__file__).parent.parent / "hyperliquid_bot" / "backtester" / "run_backtest.py"

    if not backtest_script.exists():
        print(f"ERROR: Could not find backtest script at {backtest_script}")
        sys.exit(1)

    # Run the backtest script with the config as argument
    # Assumes run_backtest.py supports a --override argument
    python_executable = sys.executable # Get the full path to the current python interpreter
    cmd = f"{python_executable} {backtest_script} --override {config_path}"
    print(f"[INFO] Running: {cmd}")
    os.system(cmd)
