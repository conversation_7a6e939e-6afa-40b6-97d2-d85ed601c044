#!/usr/bin/env python3
"""
<PERSON><PERSON>t to fix the grid search override files by correctly placing parameters in their proper sections.
"""
from pathlib import Path

# Define the parameter grid
PARAM_GRID = {
    "obi_smoothing_window": [8, 14],  # Low, High
    "gms_obi_strong_confirm_thresh": [0.15, 0.22],  # Low, High
    "gms_spread_std_high_thresh": [0.000026, 0.000044],  # Low, High
    "gms_confirmation_bars": [1, 2]  # Low, High
}

# Correct template for override config
OVERRIDE_TEMPLATE = """microstructure:
  obi_smoothing_window: {obi_win}
  gms_obi_strong_confirm_thresh: {obi_thr}
  gms_obi_weak_confirm_thresh: {obi_thr}

regime:
  gms_spread_std_high_thresh: {spd_thr}
  gms_confirmation_bars: {conf_bars}
"""

# Define the project root
PROJECT_ROOT = Path(__file__).parent.parent.resolve()
OVERRIDE_DIR = PROJECT_ROOT / "configs" / "overrides" / "grid_search"

# Ensure the directory exists
OVERRIDE_DIR.mkdir(exist_ok=True, parents=True)

# Generate all combinations for the Latin square design
run_id = 1
for obi_thr in PARAM_GRID["gms_obi_strong_confirm_thresh"]:
    for obi_win in PARAM_GRID["obi_smoothing_window"]:
        for spd_thr in PARAM_GRID["gms_spread_std_high_thresh"]:
            for conf_bars in PARAM_GRID["gms_confirmation_bars"]:
                # Format the override content with correct parameter placement
                override_content = OVERRIDE_TEMPLATE.format(
                    obi_win=obi_win,
                    obi_thr=obi_thr,
                    spd_thr=spd_thr,
                    conf_bars=conf_bars
                )
                
                # Create the override file
                override_path = OVERRIDE_DIR / f"run_{run_id:02d}.yaml"
                
                # Show what's being written to help verify correctness
                print(f"Fixing Run {run_id:02d}: OBI_WIN={obi_win}, OBI_THR={obi_thr}, SPD_THR={spd_thr}, CONF_BARS={conf_bars}")
                print(f"Writing to {override_path}:")
                print(override_content)
                print("-" * 40)
                
                # Write the file (overwriting existing file)
                with open(override_path, 'w') as f:
                    f.write(override_content)
                
                run_id += 1

print(f"Fixed {run_id-1} override files in {OVERRIDE_DIR}")

# Create a test file to verify the override structure is read correctly
test_path = OVERRIDE_DIR / "verify_fix.yaml"
test_content = """microstructure:
  obi_smoothing_window: 10
  gms_obi_strong_confirm_thresh: 0.15
  gms_obi_weak_confirm_thresh: 0.15

regime:
  gms_spread_std_high_thresh: 0.000035
  gms_confirmation_bars: 1
"""

with open(test_path, 'w') as f:
    f.write(test_content)

print(f"Created verification file at {test_path}")
print("Run this to test: python -m hyperliquid_bot.backtester.run_backtest --override configs/overrides/grid_search/verify_fix.yaml")
