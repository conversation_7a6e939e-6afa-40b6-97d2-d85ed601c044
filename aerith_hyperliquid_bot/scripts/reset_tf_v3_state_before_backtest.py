#!/usr/bin/env python
# scripts/reset_tf_v3_state_before_backtest.py

"""
Reset the TF-v3 strategy state before running a backtest.

This script clears the TF-v3 strategy state file to ensure that the strategy
starts with a clean state when running a backtest.
"""

import os
import json
import logging
import stat
import shutil
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def reset_tf_v3_state():
    """Reset the TF-v3 strategy state."""
    # Get the project root directory
    project_root = Path(__file__).parent.parent.resolve()

    # Define the state file path
    state_dir = project_root / "hyperliquid_bot" / "state"

    # Create the state directory if it doesn't exist
    if not state_dir.exists():
        state_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Created state directory: {state_dir}")

    # Define the default state
    default_state = {
        'entry_price': None,
        'entry_time': None,
        'trail_price': None,
        'position_type': None,
        'last_update_time': None,
        'last_gms_snapshot': None,
        'historical_snapshots': []
    }

    # Find all TF-v3 state files and reset them
    state_files = list(state_dir.glob("tf_v3_*.json"))
    if state_files:
        for state_file in state_files:
            logger.info(f"Resetting TF-v3 state file: {state_file}")

            # Make the file writable
            if state_file.exists():
                try:
                    os.chmod(state_file, stat.S_IWRITE | stat.S_IREAD)
                except Exception as e:
                    logger.warning(f"Failed to make file writable: {e}")
                    # Try to remove the file and create a new one
                    try:
                        os.remove(state_file)
                    except Exception as e2:
                        logger.error(f"Failed to remove file: {e2}")

            # Write the default state
            try:
                with open(state_file, 'w') as f:
                    json.dump(default_state, f, indent=2)

                # Make the file read-only to prevent updates during backtest
                os.chmod(state_file, stat.S_IREAD)
            except Exception as e:
                logger.error(f"Failed to write state file: {e}")
    else:
        # Create a default state file if none exists
        state_file = state_dir / "tf_v3_tf_v3.json"
        logger.info(f"Creating default TF-v3 state file: {state_file}")
        with open(state_file, 'w') as f:
            json.dump(default_state, f, indent=2)

        # Make the file read-only to prevent updates during backtest
        os.chmod(state_file, stat.S_IREAD)

    # Also reset portfolio state files
    portfolio_dir = project_root / "hyperliquid_bot" / "portfolio"
    if portfolio_dir.exists():
        logger.info(f"Resetting portfolio state files in {portfolio_dir}")
        portfolio_files = list(portfolio_dir.glob("*.json"))
        for portfolio_file in portfolio_files:
            try:
                logger.info(f"Removing portfolio file: {portfolio_file}")
                os.remove(portfolio_file)
            except Exception as e:
                logger.error(f"Failed to remove portfolio file: {e}")

    logger.info("TF-v3 strategy state and portfolio state reset successfully.")

if __name__ == "__main__":
    reset_tf_v3_state()
