#!/usr/bin/env python3
"""Quick script to check regime data in visualization."""

import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

# Try to find the latest backtest data
log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")

# Look for pickle files
pkl_files = list(log_dir.glob("backtest_signals_*.pkl"))
if pkl_files:
    latest_pkl = max(pkl_files, key=lambda p: p.stat().st_mtime)
    print(f"Loading data from: {latest_pkl}")
    
    # Load the data
    signals_df = pd.read_pickle(latest_pkl)
    
    print(f"\nDataframe shape: {signals_df.shape}")
    print(f"Date range: {signals_df.index.min()} to {signals_df.index.max()}")
    
    if 'regime' in signals_df.columns:
        print("\nRegime value counts:")
        print(signals_df['regime'].value_counts())
        
        # Check for unique values
        unique_regimes = signals_df['regime'].unique()
        print(f"\nUnique regimes: {unique_regimes}")
        
        # Check if there are any transitions
        regime_changes = signals_df['regime'].ne(signals_df['regime'].shift()).sum()
        print(f"\nNumber of regime changes: {regime_changes}")
        
        # Sample some data
        print("\nFirst 10 regime values:")
        print(signals_df['regime'].head(10))
        
        print("\nLast 10 regime values:")
        print(signals_df['regime'].tail(10))
    else:
        print("\nNo 'regime' column found in the data!")
        print(f"Available columns: {list(signals_df.columns)}")
else:
    print("No backtest pickle files found in logs directory")