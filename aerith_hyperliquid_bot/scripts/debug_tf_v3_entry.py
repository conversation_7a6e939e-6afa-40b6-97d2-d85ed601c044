#!/usr/bin/env python3
"""
Debug TF-v3 Entry Logic
=======================

Trace through the exact entry conditions to see why trades aren't generated.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from datetime import datetime
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.tf_v3_modern import ModernTFV3Strategy

def main():
    print("=== Debugging TF-v3 Entry Logic ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Create strategy
    strategy = ModernTFV3Strategy(config)
    
    # Create test signals that should trigger entry
    test_cases = [
        {
            'name': 'Perfect Long Setup',
            'signals': {
                'close': 42000,
                'ema_fast': 42100,  # > ema_slow
                'ema_slow': 41900,
                'ema_baseline': 41800,  # close > baseline
                'atr_14': 200.0,
                'atr_percent': 0.5,
                'rsi': 60,
                'bb_upper': 42500,
                'bb_middle': 42000,
                'bb_lower': 41500,
                'volume': 1000000,
                'regime_features': {
                    'current_state': 'Weak_Bull_Trend',
                    'current_confidence': 0.8,
                    'state_duration_minutes': 30,
                    'risk_suppressed': False,
                    'state_changes_1h': 2
                }
            },
            'regime': 'Weak_Bull_Trend'
        },
        {
            'name': 'Perfect Short Setup',
            'signals': {
                'close': 42000,
                'ema_fast': 41900,  # < ema_slow
                'ema_slow': 42100,
                'ema_baseline': 42200,  # close < baseline
                'atr_14': 200.0,
                'atr_percent': 0.5,
                'rsi': 40,
                'bb_upper': 42500,
                'bb_middle': 42000,
                'bb_lower': 41500,
                'volume': 1000000,
                'regime_features': {
                    'current_state': 'Weak_Bear_Trend',
                    'current_confidence': 0.8,
                    'state_duration_minutes': 30,
                    'risk_suppressed': False,
                    'state_changes_1h': 2
                }
            },
            'regime': 'Weak_Bear_Trend'
        },
        {
            'name': 'Real Market Conditions',
            'signals': {
                'close': 42667.50,
                'ema_fast': 42559.50,
                'ema_slow': 42970.97,
                'ema_baseline': 43000.00,  # Estimated
                'atr_14': 220.13,
                'atr_percent': 0.52,
                'rsi': 45,
                'bb_upper': 43000,
                'bb_middle': 42600,
                'bb_lower': 42200,
                'volume': 0,  # This might be the issue!
                'regime_features': {
                    'current_state': 'Weak_Bear_Trend',
                    'current_confidence': 0.7,
                    'state_duration_minutes': 60,
                    'risk_suppressed': False,
                    'state_changes_1h': 3
                }
            },
            'regime': 'Weak_Bear_Trend'
        }
    ]
    
    # Test each case
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest Case {i}: {test_case['name']}")
        print("-" * 50)
        
        signals = test_case['signals']
        regime = test_case['regime']
        
        # Display key values
        print(f"Close: {signals['close']}")
        print(f"EMA Fast: {signals['ema_fast']} {'>' if signals['ema_fast'] > signals['ema_slow'] else '<'} EMA Slow: {signals['ema_slow']}")
        print(f"Close vs Baseline: {signals['close']} {'>' if signals['close'] > signals['ema_baseline'] else '<'} {signals['ema_baseline']}")
        print(f"Regime: {regime} (confidence: {signals['regime_features']['current_confidence']})")
        print(f"Risk Suppressed: {signals['regime_features']['risk_suppressed']}")
        
        # Check each condition step by step
        print("\nCondition Checks:")
        
        # 1. Regime allowed?
        trend_states = ['Strong_Bull_Trend', 'Weak_Bull_Trend', 'Strong_Bear_Trend', 'Weak_Bear_Trend']
        regime_allowed = regime in trend_states
        print(f"1. Regime allowed: {regime_allowed} (regime={regime})")
        
        # 2. Regime stable?
        duration_ok = signals['regime_features']['state_duration_minutes'] >= 10
        changes_ok = signals['regime_features']['state_changes_1h'] <= 5
        confidence_ok = signals['regime_features']['current_confidence'] >= 0.6
        regime_stable = duration_ok and changes_ok and confidence_ok
        print(f"2. Regime stable: {regime_stable} (duration={duration_ok}, changes={changes_ok}, confidence={confidence_ok})")
        
        # 3. Risk suppressed?
        risk_ok = not signals['regime_features']['risk_suppressed']
        print(f"3. Risk not suppressed: {risk_ok}")
        
        # 4. Indicators valid?
        indicators_valid = all(not pd.isna(signals.get(k)) for k in ['ema_fast', 'ema_slow', 'ema_baseline', 'close'])
        print(f"4. Indicators valid: {indicators_valid}")
        
        # 5. Entry direction
        bullish_cross = signals['ema_fast'] > signals['ema_slow'] and signals['close'] > signals['ema_baseline']
        bearish_cross = signals['ema_fast'] < signals['ema_slow'] and signals['close'] < signals['ema_baseline']
        
        if bullish_cross and regime in ['Strong_Bull_Trend', 'Weak_Bull_Trend']:
            direction = 'long'
        elif bearish_cross and regime in ['Strong_Bear_Trend', 'Weak_Bear_Trend']:
            direction = 'short'
        else:
            direction = None
        
        print(f"5. Entry direction: {direction} (bullish_cross={bullish_cross}, bearish_cross={bearish_cross})")
        
        # Call the actual strategy
        print("\nCalling strategy.evaluate_entry()...")
        try:
            result = strategy.evaluate_entry(signals, regime)
            if result:
                print(f"✅ ENTRY SIGNAL: {result}")
            else:
                print("❌ No entry signal generated")
        except Exception as e:
            print(f"❌ ERROR: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "="*50)
    print("ANALYSIS:")
    print("If all test cases fail, there might be an issue in:")
    print("1. The _is_regime_stable() method")
    print("2. The _calculate_entry_confidence() method")
    print("3. The _calculate_position_size() method")
    print("4. Some other validation in the strategy")

if __name__ == "__main__":
    main()