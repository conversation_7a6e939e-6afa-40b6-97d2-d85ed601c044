#!/usr/bin/env python3
"""
Test Modern Signal Pipeline
===========================

This script validates that the modern signal pipeline is working correctly:
1. ModernSignalEngine calculates all required indicators
2. HourlyStrategyEvaluator properly prepares signals
3. Modern TF-v3 receives all required signals
4. No look-ahead bias in calculations
5. Proper warm-up period handling

Usage:
    python3 scripts/test_modern_signal_pipeline.py
"""

import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import numpy as np

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.signal_engine import ModernSignalEngine
from hyperliquid_bot.modern.hourly_evaluator import HourlyStrategyEvaluator
from hyperliquid_bot.modern.regime_state_manager import RegimeStateManager
from hyperliquid_bot.modern.registry import get_modern_strategy
from hyperliquid_bot.modern.data_loader import ModernDataLoader
from hyperliquid_bot.modern.data_aggregator import ModernDataAggregator


def setup_logging():
    """Set up comprehensive logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def create_test_ohlcv_data(periods: int = 100) -> pd.DataFrame:
    """Create synthetic OHLCV data for testing."""
    dates = pd.date_range(end=datetime.now(), periods=periods, freq='h')
    
    # Create realistic price movement
    np.random.seed(42)
    close_prices = 100 + np.cumsum(np.random.randn(periods) * 0.5)
    
    df = pd.DataFrame({
        'open': close_prices + np.random.randn(periods) * 0.1,
        'high': close_prices + np.abs(np.random.randn(periods) * 0.3),
        'low': close_prices - np.abs(np.random.randn(periods) * 0.3),
        'close': close_prices,
        'volume': np.random.randint(1000, 10000, periods)
    }, index=dates)
    
    # Ensure high >= close/open and low <= close/open
    df['high'] = df[['open', 'close', 'high']].max(axis=1)
    df['low'] = df[['open', 'close', 'low']].min(axis=1)
    
    return df


def test_signal_engine(config):
    """Test ModernSignalEngine functionality."""
    print("\n" + "="*60)
    print("Testing ModernSignalEngine")
    print("="*60)
    
    # Create signal engine
    signal_engine = ModernSignalEngine(config)
    
    # Test warmup calculation
    warmup = signal_engine.calculate_required_lookback()
    print(f"✓ Calculated warmup period: {warmup} bars")
    
    # Create test data
    ohlcv_df = create_test_ohlcv_data(periods=warmup + 50)
    
    # Create mock regime features
    regime_features = {
        'current_state': 'BULL',
        'current_confidence': 0.85,
        'state_duration_minutes': 120,
        'risk_suppressed': False,
        'timestamp': datetime.now(),
        'dominant_state_1h': 'BULL',
        'state_changes_1h': 2,
        'avg_momentum_1h': 0.0015,
        'avg_volatility_1h': 0.008
    }
    
    # Calculate signals
    signals = signal_engine.calculate_signals(ohlcv_df, regime_features)
    
    # Validate signals
    print("\nSignal validation:")
    required_signals = [
        'close', 'volume', 'ema_fast', 'ema_slow', 'ema_baseline',
        'atr_14', 'atr_percent', 'rsi', 'bb_upper', 'bb_middle', 'bb_lower',
        'regime_state', 'regime_confidence', 'risk_suppressed'
    ]
    
    for signal in required_signals:
        value = signals.get(signal)
        if value is None:
            print(f"✗ {signal}: Missing")
        elif pd.isna(value):
            print(f"⚠ {signal}: NaN")
        else:
            if isinstance(value, (int, float)):
                print(f"✓ {signal}: {value:.4f}")
            else:
                print(f"✓ {signal}: {value}")
    
    # Test validation
    is_valid = signal_engine.validate_signals(signals)
    print(f"\n{'✓' if is_valid else '✗'} Signal validation: {'PASSED' if is_valid else 'FAILED'}")
    
    return signals


def test_look_ahead_bias(config):
    """Test that there's no look-ahead bias in calculations."""
    print("\n" + "="*60)
    print("Testing Look-Ahead Bias Prevention")
    print("="*60)
    
    signal_engine = ModernSignalEngine(config)
    
    # Create data with a known pattern
    periods = 100
    ohlcv_df = create_test_ohlcv_data(periods)
    
    # Add a spike in the last row
    ohlcv_df.iloc[-1, ohlcv_df.columns.get_loc('close')] = 200  # Double the price
    
    regime_features = {
        'current_state': 'BULL',
        'current_confidence': 0.85,
        'risk_suppressed': False
    }
    
    # Calculate signals
    signals = signal_engine.calculate_signals(ohlcv_df, regime_features)
    
    # The indicators should NOT reflect the spike due to shift=1
    print(f"Last close price: {ohlcv_df['close'].iloc[-1]:.2f}")
    print(f"EMA fast: {signals.get('ema_fast', 'NaN'):.2f}")
    print(f"EMA slow: {signals.get('ema_slow', 'NaN'):.2f}")
    
    # EMAs should be around 100, not affected by the 200 spike
    ema_fast = signals.get('ema_fast', 0)
    if not pd.isna(ema_fast) and ema_fast < 150:
        print("✓ Look-ahead bias prevention working correctly")
    else:
        print("✗ Look-ahead bias detected!")


def test_hourly_evaluator_integration(config):
    """Test HourlyStrategyEvaluator with signal pipeline."""
    print("\n" + "="*60)
    print("Testing HourlyStrategyEvaluator Integration")
    print("="*60)
    
    # Create components
    regime_manager = RegimeStateManager(mode='backtest')
    strategy = get_modern_strategy('tf_v3_modern', config=config)
    
    evaluator = HourlyStrategyEvaluator(
        config=config,
        regime_manager=regime_manager,
        strategy=strategy,
        mode='backtest'
    )
    
    # Create test data
    ohlcv_history = create_test_ohlcv_data(periods=100)
    latest_bar = ohlcv_history.iloc[-1].to_dict()
    latest_bar['timestamp'] = ohlcv_history.index[-1]
    
    # Mock regime state
    regime_manager.update_state(
        timestamp=datetime.now(),
        state='Weak_Bull_Trend',
        confidence=0.75,
        features={
            'volatility': 0.008,
            'momentum': 0.0012,
            'volume_imbalance': 0.35,
            'risk_suppressed': False
        }
    )
    
    # Create current signals
    current_signals = {
        'spread_mean': 0.0002,
        'spread_std': 0.0001,
        'volume_imbalance': 0.35
    }
    
    # Test evaluation
    try:
        result = evaluator.evaluate(
            hourly_bar=latest_bar,
            current_signals=current_signals,
            timestamp=datetime.now(),
            ohlcv_history=ohlcv_history
        )
        
        if result:
            print("✓ Evaluation successful - Trade signal generated!")
            print(f"  Direction: {result.get('direction')}")
            print(f"  Confidence: {result.get('confidence', 0):.2f}")
            print(f"  Position size: {result.get('position_size', 0):.4f}")
        else:
            print("✓ Evaluation successful - No trade signal (conditions not met)")
            
    except Exception as e:
        print(f"✗ Evaluation failed: {e}")
        import traceback
        traceback.print_exc()


def test_warm_up_handling(config):
    """Test warm-up period handling."""
    print("\n" + "="*60)
    print("Testing Warm-up Period Handling")
    print("="*60)
    
    signal_engine = ModernSignalEngine(config)
    warmup_needed = signal_engine.calculate_required_lookback()
    
    # Test with insufficient data
    insufficient_data = create_test_ohlcv_data(periods=10)  # Less than warmup
    
    regime_features = {'current_state': 'BULL', 'risk_suppressed': False}
    signals = signal_engine.calculate_signals(insufficient_data, regime_features)
    
    # Check that some indicators are NaN due to insufficient data
    nan_count = 0
    for key, value in signals.items():
        if pd.isna(value) and key in ['ema_slow', 'ema_baseline', 'rsi']:
            nan_count += 1
    
    print(f"With {len(insufficient_data)} bars (warmup needs {warmup_needed}):")
    print(f"  NaN indicators: {nan_count}")
    
    # Test with sufficient data
    sufficient_data = create_test_ohlcv_data(periods=warmup_needed + 10)
    signals = signal_engine.calculate_signals(sufficient_data, regime_features)
    
    # Check that indicators are calculated
    valid_count = 0
    for key in ['ema_fast', 'ema_slow', 'ema_baseline', 'atr_14', 'rsi']:
        if not pd.isna(signals.get(key)):
            valid_count += 1
    
    print(f"\nWith {len(sufficient_data)} bars:")
    print(f"  Valid indicators: {valid_count}/5")
    
    if valid_count == 5:
        print("✓ Warm-up handling working correctly")
    else:
        print("✗ Warm-up handling has issues")


def main():
    """Run all tests."""
    setup_logging()
    
    # Load config
    config_path = Path(__file__).parent.parent / "configs/overrides/modern_system_v2_complete.yaml"
    if not config_path.exists():
        print(f"Config not found at {config_path}, using base config")
        config = load_config('configs/base.yaml')
    else:
        config = load_config(str(config_path))
    
    print("Modern Signal Pipeline Test Suite")
    print("=================================")
    
    # Run tests
    try:
        # Test 1: Signal Engine
        signals = test_signal_engine(config)
        
        # Test 2: Look-ahead bias
        test_look_ahead_bias(config)
        
        # Test 3: Warm-up handling
        test_warm_up_handling(config)
        
        # Test 4: Full integration
        test_hourly_evaluator_integration(config)
        
        print("\n" + "="*60)
        print("✓ All tests completed successfully!")
        print("="*60)
        
    except Exception as e:
        print(f"\n✗ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()