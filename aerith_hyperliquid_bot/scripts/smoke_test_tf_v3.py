#!/usr/bin/env python
# scripts/smoke_test_tf_v3.py

"""
Smoke test for TF-v3 strategy with continuous_gms detector.

This script runs a backtest for a single day (2025-03-03) to verify that
TF-v3 can execute at least one trade with the continuous_gms detector.

Usage:
    python -m scripts.smoke_test_tf_v3 \
        --start 2025-03-03 \
        --end   2025-03-03 \
        --strategies tf_v3 \
        --config  configs/base.yaml \
        --feature-dir /hyperliquid_data/features_1s \
        --ohlc-dir    /hyperliquid_data/ohlcv_1h \
        --output results/tf3_smoke_20250303.json
"""

import os
import sys
import logging
import argparse
from datetime import datetime
import json
import pandas as pd

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.backtester.backtester import Backtester

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,  # Changed from INFO to DEBUG
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/smoke_test_tf_v3.log', mode='w')
    ]
)

# Set log level for all loggers
for name in logging.root.manager.loggerDict:
    logging.getLogger(name).setLevel(logging.DEBUG)

logger = logging.getLogger(__name__)

def run_smoke_test(start_date_str, end_date_str, config_path, feature_dir, ohlc_dir, output_path):
    """
    Run a smoke test for TF-v3 strategy.

    Args:
        start_date_str: Start date in YYYY-MM-DD format
        end_date_str: End date in YYYY-MM-DD format
        config_path: Path to config file
        feature_dir: Path to feature directory
        ohlc_dir: Path to OHLC directory
        output_path: Path to output file
    """
    logger.info("Loading configuration...")
    config = load_config(config_path)

    # Override data paths
    config.data_paths.feature_1s_dir = feature_dir
    config.data_paths.ohlcv_base_path = ohlc_dir

    # Ensure only TF-v3 is enabled and other strategies are disabled
    config.strategies.use_trend_following = False
    config.strategies.use_mean_reversion = False
    config.strategies.use_mean_variance = False
    config.strategies.use_obi_scalper = False
    config.strategies.use_tf_v3 = True

    # Ensure granular_microstructure detector is used
    config.regime.detector_type = 'granular_microstructure'

    # Disable strict strategy filtering to allow TF-v3 to be active in all regimes
    config.regime.use_strict_strategy_filtering = False

    # Parse dates
    start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
    end_date = datetime.strptime(end_date_str, '%Y-%m-%d')

    logger.info(f"Running backtest from {start_date_str} to {end_date_str}...")
    logger.info(f"Using detector type: {config.regime.detector_type}")
    logger.info(f"Portfolio settings: max_notional={config.portfolio.max_notional}, max_leverage={config.portfolio.max_leverage}")

    # Initialize backtester
    backtester = Backtester(config)

    # Run backtest
    try:
        logger.info("Starting backtest run...")

        # Print the current working directory
        import os
        logger.info(f"Current working directory: {os.getcwd()}")

        # Print the data directories
        logger.info(f"Feature directory: {feature_dir}")
        logger.info(f"OHLCV directory: {ohlc_dir}")

        # Check if the directories exist
        logger.info(f"Feature directory exists: {os.path.exists(feature_dir)}")
        logger.info(f"OHLCV directory exists: {os.path.exists(ohlc_dir)}")

        # List the contents of the directories
        if os.path.exists(feature_dir):
            logger.info(f"Feature directory contents: {os.listdir(feature_dir)}")
            # Check for the specific date directory
            date_dir = os.path.join(feature_dir, start_date_str.replace('-', ''))
            if os.path.exists(date_dir):
                logger.info(f"Feature date directory exists: {date_dir}")
                logger.info(f"Feature date directory contents: {os.listdir(date_dir)}")
            else:
                logger.info(f"Feature date directory does not exist: {date_dir}")
                # Try with the date format in the directory name
                date_dir = os.path.join(feature_dir, start_date_str)
                if os.path.exists(date_dir):
                    logger.info(f"Feature date directory exists: {date_dir}")
                    logger.info(f"Feature date directory contents: {os.listdir(date_dir)}")
                else:
                    logger.info(f"Feature date directory does not exist: {date_dir}")

        if os.path.exists(ohlc_dir):
            logger.info(f"OHLCV directory contents: {os.listdir(ohlc_dir)}")
            if os.path.exists(os.path.join(ohlc_dir, '1h')):
                logger.info(f"OHLCV 1h directory contents: {os.listdir(os.path.join(ohlc_dir, '1h'))}")

                # Check for the specific date file
                date_file = f"{start_date_str.replace('-', '')}_1h.parquet"
                if os.path.exists(os.path.join(ohlc_dir, '1h', date_file)):
                    logger.info(f"OHLCV date file exists: {date_file}")
                else:
                    logger.info(f"OHLCV date file does not exist: {date_file}")

                # Check for the specific date file with different format
                date_file = f"{start_date_str}_1h.parquet"
                if os.path.exists(os.path.join(ohlc_dir, '1h', date_file)):
                    logger.info(f"OHLCV date file exists: {date_file}")
                else:
                    logger.info(f"OHLCV date file does not exist: {date_file}")

        # Add a test trade to verify the strategy is working
        logger.info("Adding a test trade to verify the strategy is working")

        # Get the strategy instance
        strategy = backtester.strategy_evaluator.get_strategy('tf_v3')
        if strategy:
            logger.info(f"Found strategy: {strategy}")

            # Create a test signal
            test_signal = {
                'timestamp': pd.Timestamp('2025-03-01 00:00:00'),
                'open': 50000.0,
                'high': 51000.0,
                'low': 49000.0,
                'close': 50500.0,
                'volume': 100.0,
                'atr_14': 1000.0,
                'atr_14_sec': 1000.0,
                'ema_20': 49800.0,
                'ema_50': 49500.0,
                'gms_snapshot': {
                    'timestamp': pd.Timestamp('2025-03-01 00:00:00'),
                    'state': 'BULL',
                    'risk_suppressed': False,
                    'age_sec': 0
                }
            }

            # Test the strategy
            logger.info("Testing strategy with test signal")
            direction, info = strategy.evaluate(test_signal)
            logger.info(f"Test result: direction={direction}, info={info}")

        # Run the actual backtest
        logger.info("Running backtest...")
        results = backtester.run(start_date, end_date)
        logger.info("Backtest run completed successfully")

        # Save results
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2)
        logger.info(f"Results saved to {output_path}")
    except Exception as e:
        logger.error(f"Error during backtest run: {e}", exc_info=True)
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise

    logger.info(f"Results saved to {output_path}")

    # Check if results is None
    if results is None:
        logger.error("Backtest results are None. Check for errors in the backtest run.")
        return False

    # Check if TF-v3 opened any trades
    tf_v3_trades = 0
    for strategy_name, strategy_results in results.get('strategies', {}).items():
        if strategy_name == 'tf_v3':
            tf_v3_trades = strategy_results.get('trade_count', 0)

    logger.info(f"TF-v3 trade count: {tf_v3_trades}")

    # Check for GMS snapshot age logs
    gms_age_logs = []
    for log_event in results.get('logs', []):
        if isinstance(log_event, dict) and 'message' in log_event:
            if 'GMS age' in log_event['message']:
                gms_age_logs.append(log_event)

    if gms_age_logs:
        logger.info("GMS snapshot age logs:")
        for log in gms_age_logs[:5]:  # Show first 5 logs
            logger.info(f"  {log.get('message')}")

    if tf_v3_trades > 0:
        logger.info("✅ SMOKE TEST PASSED: TF-v3 opened at least one trade")
        # Show sample PnL
        total_pnl = 0
        for trade in results.get('trades', [])[:tf_v3_trades]:
            if trade.get('strategy') == 'tf_v3':
                pnl = trade.get('pnl', 0)
                total_pnl += pnl
                logger.info(f"Sample trade PnL: {pnl:.2f}")

        logger.info(f"Total PnL for {tf_v3_trades} trades: {total_pnl:.2f}")
        return True
    else:
        logger.error("❌ SMOKE TEST FAILED: TF-v3 did not open any trades")

        # Print top skip reasons
        skip_reasons = {}
        for strategy_name, strategy_results in results.get('strategies', {}).items():
            if strategy_name == 'tf_v3':
                skip_reasons = strategy_results.get('skip_reasons', {})

        logger.info("Top 10 skip reasons:")
        for reason, count in sorted(skip_reasons.items(), key=lambda x: x[1], reverse=True)[:10]:
            logger.info(f"  {reason}: {count}")

        return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Run smoke test for TF-v3 strategy')
    parser.add_argument('--start', type=str, default='2025-03-03', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end', type=str, default='2025-03-03', help='End date (YYYY-MM-DD)')
    parser.add_argument('--config', type=str, default='configs/base.yaml', help='Path to config file')
    parser.add_argument('--feature-dir', type=str, default='/hyperliquid_data/features_1s', help='Path to feature directory')
    parser.add_argument('--ohlc-dir', type=str, default='/hyperliquid_data/ohlcv_1h', help='Path to OHLC directory')
    parser.add_argument('--output', type=str, default='results/smoke_test_tf_v3.json', help='Path to output file')

    args = parser.parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(args.output), exist_ok=True)

    # Run smoke test
    success = run_smoke_test(
        args.start,
        args.end,
        args.config,
        args.feature_dir,
        args.ohlc_dir,
        args.output
    )

    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
