#!/usr/bin/env python3
"""
Quick check of actual regime states in signals data
"""

import pandas as pd
from pathlib import Path

# Load latest signals
logs_dir = Path(__file__).parent.parent.parent / "logs"
signals_files = list(logs_dir.glob("backtest_signals_*.parquet"))
latest_file = max(signals_files, key=lambda x: x.stat().st_mtime)

print(f"Loading: {latest_file.name}")
df = pd.read_parquet(latest_file)

print(f"Total samples: {len(df)}")

# Check regime column
if 'regime' in df.columns:
    regime_counts = df['regime'].value_counts()
    print("\nRegime Distribution:")
    for regime, count in regime_counts.items():
        pct = count / len(df) * 100
        print(f"  {regime}: {count} ({pct:.1f}%)")
else:
    print("No 'regime' column found")
    print(f"Available columns: {list(df.columns)}")

# Look for any regime-related columns
regime_cols = [col for col in df.columns if 'regime' in col.lower()]
print(f"\nRegime-related columns: {regime_cols}")

# Check for raw GMS states if available
for col in df.columns:
    if 'state' in col.lower() or 'gms' in col.lower():
        print(f"\nFound potential state column: {col}")
        if col in df.columns:
            values = df[col].value_counts().head(10)
            print(f"Top values in {col}:")
            for val, count in values.items():
                print(f"  {val}: {count}")