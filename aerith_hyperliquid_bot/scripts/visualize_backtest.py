#!/usr/bin/env python3
"""
Convenience wrapper for the visualization script.
This maintains backward compatibility with existing scripts and documentation.
"""

import subprocess
import sys
from pathlib import Path

# Point to the actual visualization script
visualization_script = Path(__file__).parent / "visualization" / "visualize.py"

# Pass all arguments to the actual script
sys.exit(subprocess.call([sys.executable, str(visualization_script)] + sys.argv[1:]))