#!/usr/bin/env python
# scripts/run_smoke_test.py
# Run a smoke test for the backtester on 2025-03-03

import logging
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(project_root))

# Import the necessary modules
from aerith_hyperliquid_bot.hyperliquid_bot.config.settings import load_config
from aerith_hyperliquid_bot.hyperliquid_bot.backtester.backtester import Backtester, setup_logging

def main():
    """Run a smoke test for the backtester on 2025-03-03."""
    # Load the configuration
    config_path = project_root / 'aerith_hyperliquid_bot' / 'config.yaml'
    config = load_config(str(config_path))
    
    # Set up logging
    setup_logging(config)
    
    # Set the start and end dates for the backtest
    start_date = datetime(2025, 3, 3)
    end_date = datetime(2025, 3, 4)  # End date is exclusive
    
    # Create the backtester
    backtester = Backtester(config)
    
    # Run the backtest
    logging.info(f"Running smoke test for {start_date.strftime('%Y-%m-%d')}")
    backtester.run(start_date, end_date)
    
    # Check if skip reasons were logged
    log_dir = Path(config.data_paths.log_dir)
    skip_reasons_path = log_dir / f"skip_reasons_{start_date.strftime('%Y%m%d')}.csv"
    
    if skip_reasons_path.exists():
        logging.info(f"Skip reasons saved to {skip_reasons_path}")
    else:
        logging.warning(f"No skip reasons file found at {skip_reasons_path}")

if __name__ == "__main__":
    main()
