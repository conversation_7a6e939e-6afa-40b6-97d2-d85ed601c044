#!/usr/bin/env python3
"""
Threshold Scale Analysis
Demonstrates the fundamental differences between percentile and decimal scaling approaches.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

def analyze_threshold_scaling():
    """Analyze the difference between percentile and decimal threshold scaling."""
    
    print("🔍 Threshold Scale Analysis")
    print("=" * 60)
    
    # Load actual BTC volatility data
    feature_file = Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s/2025-03-02/features_06.parquet")
    
    if feature_file.exists():
        df = pd.read_parquet(feature_file)
        atr_values = df['atr_percent_sec'].dropna()
        
        print(f"\n📊 Actual BTC Volatility Data (Sample):")
        print(f"   Count: {len(atr_values):,} observations")
        print(f"   Range: {atr_values.min():.6f} to {atr_values.max():.6f}")
        print(f"   Mean: {atr_values.mean():.6f} ({atr_values.mean()*100:.3f}%)")
        print(f"   Std: {atr_values.std():.6f}")
        
        # Calculate percentiles
        percentiles = [1, 5, 10, 25, 50, 55, 75, 90, 92, 95, 99]
        print(f"\n📈 Volatility Percentiles:")
        for p in percentiles:
            value = atr_values.quantile(p/100)
            print(f"   {p:2d}th percentile: {value:.6f} ({value*100:.3f}%)")
        
        # Legacy System Analysis (Percentile Scale)
        print(f"\n🏛️  LEGACY SYSTEM (Percentile Scale):")
        legacy_low = atr_values.quantile(0.55)   # 55th percentile
        legacy_high = atr_values.quantile(0.92)  # 92nd percentile
        
        print(f"   Low Threshold (55th percentile):  {legacy_low:.6f} ({legacy_low*100:.3f}%)")
        print(f"   High Threshold (92nd percentile): {legacy_high:.6f} ({legacy_high*100:.3f}%)")
        
        # Calculate regime classification for legacy
        low_vol_legacy = (atr_values <= legacy_low).mean()
        high_vol_legacy = (atr_values >= legacy_high).mean()
        mid_vol_legacy = 1 - low_vol_legacy - high_vol_legacy
        
        print(f"   Regime Distribution:")
        print(f"     Low Volatility:  {low_vol_legacy:.1%}")
        print(f"     Mid Volatility:  {mid_vol_legacy:.1%}")
        print(f"     High Volatility: {high_vol_legacy:.1%}")
        
        # Modern System Analysis (Decimal Scale)
        print(f"\n🔬 MODERN SYSTEM (Decimal Scale):")
        modern_low = 0.01    # 1% fixed
        modern_high = 0.03   # 3% fixed
        
        print(f"   Low Threshold (fixed):  {modern_low:.6f} ({modern_low*100:.1f}%)")
        print(f"   High Threshold (fixed): {modern_high:.6f} ({modern_high*100:.1f}%)")
        
        # Calculate regime classification for modern
        low_vol_modern = (atr_values <= modern_low).mean()
        high_vol_modern = (atr_values >= modern_high).mean()
        mid_vol_modern = 1 - low_vol_modern - high_vol_modern
        
        print(f"   Regime Distribution:")
        print(f"     Low Volatility:  {low_vol_modern:.1%}")
        print(f"     Mid Volatility:  {mid_vol_modern:.1%}")
        print(f"     High Volatility: {high_vol_modern:.1%}")
        
        # Current Market Context
        current_vol = atr_values.mean()
        print(f"\n🎯 CURRENT MARKET CONTEXT:")
        print(f"   Current Volatility: {current_vol:.6f} ({current_vol*100:.3f}%)")
        
        # Legacy classification
        if current_vol <= legacy_low:
            legacy_regime = "LOW VOLATILITY"
        elif current_vol >= legacy_high:
            legacy_regime = "HIGH VOLATILITY"
        else:
            legacy_regime = "MID VOLATILITY"
        
        # Modern classification
        if current_vol <= modern_low:
            modern_regime = "LOW VOLATILITY"
        elif current_vol >= modern_high:
            modern_regime = "HIGH VOLATILITY"
        else:
            modern_regime = "MID VOLATILITY"
        
        print(f"   Legacy System Classification:  {legacy_regime}")
        print(f"   Modern System Classification:  {modern_regime}")
        
        # Sensitivity Analysis
        print(f"\n⚡ SENSITIVITY ANALYSIS:")
        
        # How often would regimes change?
        regime_changes_legacy = calculate_regime_changes(atr_values, legacy_low, legacy_high)
        regime_changes_modern = calculate_regime_changes(atr_values, modern_low, modern_high)
        
        print(f"   Legacy System Regime Changes: {regime_changes_legacy} ({regime_changes_legacy/len(atr_values)*100:.1f}%)")
        print(f"   Modern System Regime Changes: {regime_changes_modern} ({regime_changes_modern/len(atr_values)*100:.1f}%)")
        
        # Trading implications
        print(f"\n💰 TRADING IMPLICATIONS:")
        
        # Estimate trade frequency based on regime distribution
        # Assuming trades occur primarily in mid/high volatility periods
        legacy_trade_potential = mid_vol_legacy + high_vol_legacy
        modern_trade_potential = mid_vol_modern + high_vol_modern
        
        print(f"   Legacy Trade Potential: {legacy_trade_potential:.1%} of time")
        print(f"   Modern Trade Potential: {modern_trade_potential:.1%} of time")
        
        if modern_trade_potential > 0:
            trade_ratio = legacy_trade_potential / modern_trade_potential
            print(f"   Legacy vs Modern Trade Frequency Ratio: {trade_ratio:.2f}x")
        
        return {
            'legacy_low': legacy_low,
            'legacy_high': legacy_high,
            'modern_low': modern_low,
            'modern_high': modern_high,
            'current_vol': current_vol,
            'legacy_regime': legacy_regime,
            'modern_regime': modern_regime,
            'atr_values': atr_values
        }
    
    else:
        print("❌ Feature file not found - using synthetic data")
        return None

def calculate_regime_changes(atr_values, low_thresh, high_thresh):
    """Calculate number of regime changes."""
    regimes = []
    for val in atr_values:
        if val <= low_thresh:
            regimes.append('LOW')
        elif val >= high_thresh:
            regimes.append('HIGH')
        else:
            regimes.append('MID')
    
    # Count changes
    changes = 0
    for i in range(1, len(regimes)):
        if regimes[i] != regimes[i-1]:
            changes += 1
    
    return changes

def practical_trading_examples():
    """Provide practical examples of threshold differences."""
    
    print(f"\n" + "=" * 60)
    print("🎯 PRACTICAL TRADING EXAMPLES")
    print("=" * 60)
    
    # Example scenarios
    scenarios = [
        {"name": "Quiet Market", "volatility": 0.003, "description": "Typical overnight trading"},
        {"name": "Normal Market", "volatility": 0.008, "description": "Regular trading hours"},
        {"name": "Active Market", "volatility": 0.015, "description": "News-driven movement"},
        {"name": "Volatile Market", "volatility": 0.025, "description": "Major announcement"},
        {"name": "Crisis Market", "volatility": 0.050, "description": "Black swan event"},
    ]
    
    legacy_low, legacy_high = 0.004776, 0.004794  # From actual data analysis
    modern_low, modern_high = 0.01, 0.03
    
    print(f"\nScenario Analysis:")
    print(f"{'Scenario':<15} {'Volatility':<12} {'Legacy Regime':<15} {'Modern Regime':<15} {'Trade Likely?'}")
    print("-" * 80)
    
    for scenario in scenarios:
        vol = scenario['volatility']
        
        # Legacy classification
        if vol <= legacy_low:
            legacy_regime = "LOW"
        elif vol >= legacy_high:
            legacy_regime = "HIGH"
        else:
            legacy_regime = "MID"
        
        # Modern classification
        if vol <= modern_low:
            modern_regime = "LOW"
        elif vol >= modern_high:
            modern_regime = "HIGH"
        else:
            modern_regime = "MID"
        
        # Trade likelihood (simplified)
        trade_likely = "Yes" if legacy_regime != "LOW" or modern_regime != "LOW" else "No"
        
        print(f"{scenario['name']:<15} {vol*100:>8.1f}%     {legacy_regime:<15} {modern_regime:<15} {trade_likely}")

def main():
    """Main analysis function."""
    
    # Run threshold scale analysis
    results = analyze_threshold_scaling()
    
    # Show practical examples
    practical_trading_examples()
    
    print(f"\n✅ Analysis complete. Key insights:")
    print(f"   • Legacy system adapts to data distribution")
    print(f"   • Modern system uses fixed absolute thresholds")
    print(f"   • Current market conditions favor different systems")
    print(f"   • Threshold choice significantly impacts trade frequency")

if __name__ == "__main__":
    main()
