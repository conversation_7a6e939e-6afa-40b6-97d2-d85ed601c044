#!/usr/bin/env python3
"""
Debug warmup data loading issue
==============================

This script diagnoses why warmup data isn't being loaded correctly
and why EMAs are calculated incorrectly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
import pandas as pd
import logging
from pathlib import Path

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_data_loader import RobustDataLoader
from hyperliquid_bot.modern.signal_engine import ModernSignalEngine
from hyperliquid_bot.features.indicators import calculate_ema

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Diagnose warmup data loading issues."""
    # Initialize config
    import yaml
    from hyperliquid_bot.config.settings import Config
    
    # Load base config
    with open("configs/base.yaml", 'r') as f:
        base_dict = yaml.safe_load(f)
    
    # Load modern override
    with open("configs/overrides/modern_system.yaml", 'r') as f:
        modern_override = yaml.safe_load(f)
    
    # Deep merge function
    def deep_merge(base, override):
        for key, value in override.items():
            if isinstance(value, dict) and key in base and isinstance(base[key], dict):
                deep_merge(base[key], value)
            else:
                base[key] = value
    
    # Apply override with deep merge
    deep_merge(base_dict, modern_override)
    
    # Fix the microstructure depth_levels issue
    if 'microstructure' in base_dict:
        base_dict['microstructure']['depth_levels_for_calc'] = None
    
    # Create config
    config = Config(**base_dict)
    
    # Test parameters
    test_start = datetime(2024, 4, 1, 0, 0, 0)
    test_end = datetime(2024, 4, 8, 0, 0, 0)
    
    # Create data loader
    data_loader = RobustDataLoader(config)
    signal_engine = ModernSignalEngine(config)
    
    # Calculate required warmup
    warmup_hours = signal_engine.calculate_required_lookback()
    warmup_start = test_start - timedelta(hours=warmup_hours)
    
    logger.info(f"\n=== WARMUP DATA DIAGNOSIS ===")
    logger.info(f"Test period: {test_start} to {test_end}")
    logger.info(f"Required warmup: {warmup_hours} hours")
    logger.info(f"Warmup start needed: {warmup_start}")
    
    # Step 1: Check what data files exist
    logger.info(f"\n--- STEP 1: Checking available data files ---")
    
    # Check different data sources
    sources = {
        'enhanced_hourly': data_loader.enhanced_hourly_path,
        'features_1s': data_loader.feature_1s_path,
        'legacy_raw2': data_loader.legacy_l2_path,
        'ohlcv': data_loader.ohlcv_path
    }
    
    for source_name, path in sources.items():
        logger.info(f"\n{source_name}: {path}")
        if path.exists():
            # Find earliest file
            files = []
            if path.is_dir():
                for f in path.rglob('*.parquet'):
                    files.append(f)
            
            if files:
                files.sort()
                logger.info(f"  First file: {files[0].name}")
                logger.info(f"  Total files: {len(files)}")
                
                # Try to determine date range
                try:
                    # Read first file to check dates
                    df = pd.read_parquet(files[0])
                    if not df.empty:
                        if 'timestamp' in df.columns:
                            df['timestamp'] = pd.to_datetime(df['timestamp'])
                            logger.info(f"  Data starts: {df['timestamp'].min()}")
                            logger.info(f"  Data ends: {df['timestamp'].max()}")
                        elif df.index.name == 'timestamp' or isinstance(df.index, pd.DatetimeIndex):
                            logger.info(f"  Data starts: {df.index.min()}")
                            logger.info(f"  Data ends: {df.index.max()}")
                except Exception as e:
                    logger.error(f"  Error reading file: {e}")
        else:
            logger.info(f"  Path does not exist!")
    
    # Step 2: Load data with warmup
    logger.info(f"\n--- STEP 2: Loading data with warmup ---")
    data = data_loader.load_data(warmup_start, test_end)
    
    logger.info(f"Loaded data shape: {data.shape}")
    logger.info(f"Data range: {data.index.min()} to {data.index.max()}")
    logger.info(f"Columns: {list(data.columns)[:10]}...")  # First 10 columns
    
    # Check how much warmup we actually got
    hours_before_start = 0
    if not data.empty and data.index.min() < test_start:
        hours_before_start = int((test_start - data.index.min()).total_seconds() / 3600)
    logger.info(f"Hours before test start: {hours_before_start} (requested: {warmup_hours})")
    
    # Step 3: Test EMA calculation
    logger.info(f"\n--- STEP 3: Testing EMA calculation ---")
    
    if not data.empty and len(data) > 50:
        # Get subset of data around test start
        subset_start = max(data.index.min(), test_start - timedelta(hours=100))
        subset_end = min(data.index.max(), test_start + timedelta(hours=24))
        subset = data.loc[subset_start:subset_end]
        
        logger.info(f"Subset for EMA test: {len(subset)} rows")
        logger.info(f"Price range: {subset['close'].min():.2f} to {subset['close'].max():.2f}")
        
        # Calculate EMAs
        ema_12 = calculate_ema(subset, period=12, shift=1)
        ema_26 = calculate_ema(subset, period=26, shift=1)
        
        # Show some values
        logger.info(f"\nSample values at test start:")
        test_idx = subset.index.get_indexer([test_start], method='nearest')[0]
        if test_idx >= 0 and test_idx < len(subset):
            logger.info(f"  Close price: {subset['close'].iloc[test_idx]:.2f}")
            logger.info(f"  EMA 12: {ema_12.iloc[test_idx]:.2f}")
            logger.info(f"  EMA 26: {ema_26.iloc[test_idx]:.2f}")
            
            # Check if EMAs are reasonable
            close_price = subset['close'].iloc[test_idx]
            if abs(ema_12.iloc[test_idx] - close_price) > close_price * 0.5:
                logger.warning(f"  ⚠️ EMA 12 seems incorrect (too far from price)")
            if abs(ema_26.iloc[test_idx] - close_price) > close_price * 0.5:
                logger.warning(f"  ⚠️ EMA 26 seems incorrect (too far from price)")
    
    # Step 4: Test signal engine calculation
    logger.info(f"\n--- STEP 4: Testing signal engine ---")
    
    if not data.empty and len(data) > warmup_hours:
        # Get data at test start
        hour_data = data.loc[test_start:test_start]
        if not hour_data.empty:
            # Calculate signals
            signals = signal_engine.calculate_signals(
                ohlcv_df=data.loc[:test_start],  # All data up to test start
                regime_features={'current_state': 'Neutral', 'current_confidence': 0.5}
            )
            
            logger.info(f"\nSignals at test start:")
            logger.info(f"  Close: {signals.get('close', 'N/A')}")
            logger.info(f"  EMA Fast: {signals.get('ema_fast', 'N/A')}")
            logger.info(f"  EMA Slow: {signals.get('ema_slow', 'N/A')}")
            logger.info(f"  Forecast: {signals.get('forecast', 'N/A')}")
            logger.info(f"  ATR: {signals.get('atr_14', 'N/A')}")
    
    # Step 5: Check for common issues
    logger.info(f"\n--- STEP 5: Common Issues Check ---")
    
    issues = []
    
    # Issue 1: No warmup data
    if hours_before_start < warmup_hours * 0.5:
        issues.append(f"Insufficient warmup data: only {hours_before_start}/{warmup_hours} hours available")
    
    # Issue 2: Data gaps
    if not data.empty:
        expected_hours = int((data.index.max() - data.index.min()).total_seconds() / 3600) + 1
        actual_hours = len(data)
        if actual_hours < expected_hours * 0.9:
            issues.append(f"Data has gaps: {actual_hours}/{expected_hours} hours present")
    
    # Issue 3: Wrong price scale
    if not data.empty and 'close' in data.columns:
        avg_price = data['close'].mean()
        if avg_price < 100:  # BTC should be > $1000
            issues.append(f"Price scale seems wrong: average = ${avg_price:.2f}")
    
    # Issue 4: Missing columns
    required_cols = ['open', 'high', 'low', 'close', 'volume']
    if not data.empty:
        missing = [col for col in required_cols if col not in data.columns]
        if missing:
            issues.append(f"Missing required columns: {missing}")
    
    if issues:
        logger.warning(f"\n⚠️ ISSUES FOUND:")
        for issue in issues:
            logger.warning(f"  - {issue}")
    else:
        logger.info(f"\n✅ No major issues found")
    
    # Recommendations
    logger.info(f"\n=== RECOMMENDATIONS ===")
    if hours_before_start < warmup_hours:
        logger.info(f"1. Data starts too late for proper warmup")
        logger.info(f"   - Consider using earlier data files")
        logger.info(f"   - Or reduce warmup requirements in config")
        logger.info(f"   - Or start backtest from a later date")
    
    if data.empty or len(data) < 100:
        logger.info(f"2. Very little data loaded")
        logger.info(f"   - Check if data files exist for the requested period")
        logger.info(f"   - Verify data file format is correct")
        logger.info(f"   - Check data loader fallback chain")

if __name__ == "__main__":
    main()