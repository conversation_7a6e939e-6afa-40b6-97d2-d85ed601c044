#!/usr/bin/env python3
"""
Compare threshold values between legacy and modern systems.
Look for potential 100x errors (percentage vs absolute values).
"""

import yaml
from pathlib import Path
import json
from typing import Dict, Any, List, Tuple

def load_yaml_config(path: str) -> Dict[str, Any]:
    """Load YAML configuration file."""
    with open(path, 'r') as f:
        return yaml.safe_load(f)

def extract_thresholds(config: Dict[str, Any], prefix: str = "") -> Dict[str, Any]:
    """Recursively extract all threshold-like values from config."""
    thresholds = {}
    
    for key, value in config.items():
        full_key = f"{prefix}.{key}" if prefix else key
        
        # Look for threshold-related keys
        if any(pattern in key.lower() for pattern in ['thresh', 'min_', 'max_', 'limit', 'percent', 'fraction', 'rate']):
            thresholds[full_key] = value
        
        # Recurse into nested dicts
        if isinstance(value, dict):
            nested = extract_thresholds(value, full_key)
            thresholds.update(nested)
    
    return thresholds

def analyze_threshold_differences(legacy: Dict[str, Any], modern: Dict[str, Any]) -> List[Tuple[str, Any, Any, float]]:
    """Compare thresholds and identify potential 100x errors."""
    differences = []
    
    # Get all unique keys
    all_keys = set(legacy.keys()) | set(modern.keys())
    
    for key in sorted(all_keys):
        legacy_val = legacy.get(key, "NOT FOUND")
        modern_val = modern.get(key, "NOT FOUND")
        
        # Skip if same value
        if legacy_val == modern_val:
            continue
        
        # Calculate ratio if both are numbers
        ratio = None
        if isinstance(legacy_val, (int, float)) and isinstance(modern_val, (int, float)):
            if legacy_val != 0:
                ratio = modern_val / legacy_val
            elif modern_val != 0:
                ratio = float('inf')
        
        differences.append((key, legacy_val, modern_val, ratio))
    
    return differences

def main():
    # Load configurations
    print("Loading configurations...")
    
    # Base config
    base_config = load_yaml_config("configs/base.yaml")
    
    # Legacy system config
    legacy_config = load_yaml_config("configs/overrides/legacy_system.yaml")
    
    # Modern system config  
    modern_config = load_yaml_config("configs/overrides/modern_system_v2_complete.yaml")
    
    # Merge configs (override style)
    def merge_configs(base: Dict, override: Dict) -> Dict:
        """Deep merge override into base."""
        import copy
        result = copy.deepcopy(base)
        
        def deep_merge(target: Dict, source: Dict):
            for key, value in source.items():
                if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                    deep_merge(target[key], value)
                else:
                    target[key] = value
        
        deep_merge(result, override)
        return result
    
    # Create full configs
    legacy_full = merge_configs(base_config, legacy_config)
    modern_full = merge_configs(base_config, modern_config)
    
    # Extract thresholds
    print("\nExtracting thresholds...")
    legacy_thresholds = extract_thresholds(legacy_full)
    modern_thresholds = extract_thresholds(modern_full)
    
    print(f"Found {len(legacy_thresholds)} thresholds in legacy config")
    print(f"Found {len(modern_thresholds)} thresholds in modern config")
    
    # Analyze differences
    differences = analyze_threshold_differences(legacy_thresholds, modern_thresholds)
    
    # Create report
    report = {
        "summary": {
            "legacy_threshold_count": len(legacy_thresholds),
            "modern_threshold_count": len(modern_thresholds),
            "differences_count": len(differences)
        },
        "potential_100x_errors": [],
        "significant_differences": [],
        "all_differences": []
    }
    
    # Categorize differences
    for key, legacy_val, modern_val, ratio in differences:
        diff_entry = {
            "key": key,
            "legacy": legacy_val,
            "modern": modern_val,
            "ratio": ratio
        }
        
        # Check for potential 100x errors
        if ratio is not None:
            if ratio >= 50 or ratio <= 0.02:  # 50x or more difference
                report["potential_100x_errors"].append(diff_entry)
            elif ratio >= 10 or ratio <= 0.1:  # 10x difference
                report["significant_differences"].append(diff_entry)
        
        report["all_differences"].append(diff_entry)
    
    # Print findings
    print("\n" + "=" * 80)
    print("THRESHOLD COMPARISON REPORT")
    print("=" * 80)
    
    if report["potential_100x_errors"]:
        print(f"\n🚨 FOUND {len(report['potential_100x_errors'])} POTENTIAL 100x ERRORS:")
        for item in report["potential_100x_errors"]:
            print(f"\n{item['key']}:")
            print(f"  Legacy: {item['legacy']}")
            print(f"  Modern: {item['modern']}")
            if item['ratio'] is not None:
                print(f"  Ratio: {item['ratio']:.1f}x")
    
    if report["significant_differences"]:
        print(f"\n⚠️  FOUND {len(report['significant_differences'])} SIGNIFICANT DIFFERENCES (10x):")
        for item in report["significant_differences"]:
            print(f"\n{item['key']}:")
            print(f"  Legacy: {item['legacy']}")
            print(f"  Modern: {item['modern']}")
            if item['ratio'] is not None:
                print(f"  Ratio: {item['ratio']:.1f}x")
    
    # Save full report
    report_path = "threshold_comparison_report.json"
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\n📊 Full report saved to: {report_path}")
    
    # Look specifically for known problem areas
    print("\n" + "=" * 80)
    print("CHECKING KNOWN PROBLEM AREAS")
    print("=" * 80)
    
    # Check momentum thresholds
    print("\n🔍 Momentum Thresholds:")
    for key in ['regime.gms_mom_weak_thresh', 'regime.gms_mom_strong_thresh']:
        if key in legacy_thresholds or key in modern_thresholds:
            print(f"{key}:")
            print(f"  Legacy: {legacy_thresholds.get(key, 'NOT FOUND')}")
            print(f"  Modern: {modern_thresholds.get(key, 'NOT FOUND')}")
    
    # Check OBI thresholds
    print("\n🔍 OBI/Volume Imbalance Thresholds:")
    for key in ['regime.gms_obi_weak_confirm_thresh', 'regime.gms_obi_strong_confirm_thresh']:
        if key in legacy_thresholds or key in modern_thresholds:
            print(f"{key}:")
            print(f"  Legacy: {legacy_thresholds.get(key, 'NOT FOUND')}")
            print(f"  Modern: {modern_thresholds.get(key, 'NOT FOUND')}")
    
    return report

if __name__ == "__main__":
    main()