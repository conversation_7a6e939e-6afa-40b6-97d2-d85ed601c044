#!/usr/bin/env python3
"""
Debug Calibrated Modern System
==============================

Check why we're still getting 0 trades with relaxed thresholds.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
import pandas as pd
import numpy as np


def main():
    print("=== Debugging Calibrated Modern System ===\n")
    
    # Monkey patch to add logging
    from hyperliquid_bot.modern import tf_v3_modern
    
    original_is_regime_stable = tf_v3_modern.ModernTFV3Strategy._is_regime_stable
    original_evaluate_entry = tf_v3_modern.ModernTFV3Strategy.evaluate_entry
    
    # Track all calls
    stability_checks = []
    entry_evaluations = []
    
    def debug_is_regime_stable(self, regime_features):
        """Log all stability checks."""
        result = original_is_regime_stable(self, regime_features)
        
        check_info = {
            'confidence': regime_features.get('current_confidence', 0.0),
            'state_persistence': regime_features.get('state_persistence', 0.0),
            'recent_transitions': regime_features.get('recent_transitions', 0),
            'risk_suppressed': regime_features.get('risk_suppressed', False),
            'result': result
        }
        stability_checks.append(check_info)
        
        if not result:
            print(f"  ❌ Regime stability FAILED:")
            conf = regime_features.get('current_confidence', 0.0)
            pers = regime_features.get('state_persistence', 0.0)
            trans = regime_features.get('recent_transitions', 0)
            
            if conf < 0.5:
                print(f"     - Low confidence: {conf:.3f} < 0.5")
            if pers < 0.2:
                print(f"     - Low persistence: {pers:.3f} < 0.2")
            if trans * 6 > 60:
                print(f"     - Too many transitions: {trans*6} > 60")
            if regime_features.get('risk_suppressed', False):
                print(f"     - Risk suppressed")
        
        return result
    
    def debug_evaluate_entry(self, signals, regime):
        """Log all entry evaluations."""
        timestamp = signals.get('timestamp', 'unknown')
        print(f"\n📍 Evaluating at {timestamp}, regime: {regime}")
        
        # Log key values
        ema_fast = signals.get('ema_fast', 0)
        ema_slow = signals.get('ema_slow', 0)
        
        if ema_fast and ema_slow:
            if ema_fast > ema_slow:
                print(f"  EMA: Bullish ({ema_fast:.2f} > {ema_slow:.2f})")
            else:
                print(f"  EMA: Bearish ({ema_fast:.2f} < {ema_slow:.2f})")
        
        result = original_evaluate_entry(self, signals, regime)
        
        eval_info = {
            'timestamp': timestamp,
            'regime': regime,
            'ema_fast': ema_fast,
            'ema_slow': ema_slow,
            'result': result is not None
        }
        entry_evaluations.append(eval_info)
        
        if result:
            print(f"  ✅ Trade signal: {result['direction']}")
        else:
            print(f"  ❌ No trade signal")
        
        return result
    
    # Apply patches
    tf_v3_modern.ModernTFV3Strategy._is_regime_stable = debug_is_regime_stable
    tf_v3_modern.ModernTFV3Strategy.evaluate_entry = debug_evaluate_entry
    
    # Run a day's backtest
    from hyperliquid_bot.config.settings import load_config
    from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
    
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 16)
    
    print(f"Running backtest from {start_date} to {end_date}...")
    print("With CALIBRATED thresholds:")
    print("  - Confidence: >= 0.5")
    print("  - Persistence: >= 0.2")
    print("  - Transitions: <= 60/hour")
    print("\n" + "-"*60)
    
    try:
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        results = engine.run_backtest()
        
        print("\n" + "="*60)
        print("ANALYSIS SUMMARY")
        print("="*60)
        
        print(f"\nTotal trades: {results['performance']['total_trades']}")
        print(f"Entry evaluations: {len(entry_evaluations)}")
        print(f"Stability checks: {len(stability_checks)}")
        
        # Analyze stability failures
        if stability_checks:
            failed_checks = [c for c in stability_checks if not c['result']]
            print(f"\nStability check failures: {len(failed_checks)}/{len(stability_checks)}")
            
            if failed_checks:
                # Group by failure reason
                low_conf = sum(1 for c in failed_checks if c['confidence'] < 0.5)
                low_pers = sum(1 for c in failed_checks if c['state_persistence'] < 0.2)
                high_trans = sum(1 for c in failed_checks if c['recent_transitions'] * 6 > 60)
                risk_supp = sum(1 for c in failed_checks if c['risk_suppressed'])
                
                print(f"  - Low confidence: {low_conf}")
                print(f"  - Low persistence: {low_pers}")
                print(f"  - High transitions: {high_trans}")
                print(f"  - Risk suppressed: {risk_supp}")
                
                # Show persistence distribution
                all_pers = [c['state_persistence'] for c in stability_checks]
                print(f"\nPersistence distribution:")
                print(f"  Min: {min(all_pers):.3f}")
                print(f"  Max: {max(all_pers):.3f}")
                print(f"  Mean: {np.mean(all_pers):.3f}")
                print(f"  < 0.2: {sum(1 for p in all_pers if p < 0.2)}/{len(all_pers)}")
        
        # Analyze regime/EMA alignment
        if entry_evaluations:
            bull_ema_bull_regime = sum(1 for e in entry_evaluations 
                                      if e['ema_fast'] > e['ema_slow'] and 'BULL' in e['regime'])
            bear_ema_bear_regime = sum(1 for e in entry_evaluations 
                                      if e['ema_fast'] < e['ema_slow'] and 'BEAR' in e['regime'])
            
            print(f"\nRegime/EMA alignment:")
            print(f"  - Bull EMA + Bull regime: {bull_ema_bull_regime}")
            print(f"  - Bear EMA + Bear regime: {bear_ema_bear_regime}")
            print(f"  - Total aligned: {bull_ema_bull_regime + bear_ema_bear_regime}/{len(entry_evaluations)}")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()