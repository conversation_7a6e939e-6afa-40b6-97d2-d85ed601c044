#!/usr/bin/env python
# scripts/regression_test_tf_v3.py

"""
Regression test for TF-v3 strategy with continuous_gms detector.

This script runs a full regression test to verify that:
1. The continuous_gms detector is recognized
2. The portfolio settings are properly loaded
3. TF-v3 opens trades over a longer period
4. Generates a comparison report between TF-v2 and TF-v3

Usage:
    python -m scripts.regression_test_tf_v3
"""

import os
import sys
import logging
import argparse
from datetime import datetime, timedelta
import json
import pandas as pd
import matplotlib.pyplot as plt

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.backtester.backtester import Backtester

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/regression_test_tf_v3.log', mode='w')
    ]
)

logger = logging.getLogger(__name__)

def run_regression_test(start_date_str, end_date_str, config_path, feature_dir, ohlc_dir, output_path):
    """
    Run a regression test for TF-v3 strategy.
    
    Args:
        start_date_str: Start date in YYYY-MM-DD format
        end_date_str: End date in YYYY-MM-DD format
        config_path: Path to config file
        feature_dir: Path to feature directory
        ohlc_dir: Path to OHLC directory
        output_path: Path to output file
    """
    logger.info("Loading configuration...")
    config = load_config(config_path)
    
    # Override data paths
    config.data_paths.feature_1s_dir = feature_dir
    config.data_paths.ohlcv_base_path = ohlc_dir
    
    # Ensure TF-v2 and TF-v3 are enabled
    config.strategies.use_trend_following = True
    config.strategies.use_tf_v3 = True
    
    # Ensure continuous_gms detector is used
    config.regime.detector_type = 'continuous_gms'
    
    # Parse dates
    start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
    end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
    
    logger.info(f"Running backtest from {start_date_str} to {end_date_str}...")
    logger.info(f"Using detector type: {config.regime.detector_type}")
    logger.info(f"Portfolio settings: max_notional={config.portfolio.max_notional}, max_leverage={config.portfolio.max_leverage}")
    
    # Initialize backtester
    backtester = Backtester(config)
    
    # Run backtest
    results = backtester.run(start_date, end_date)
    
    # Save results
    with open(output_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Results saved to {output_path}")
    
    # Check if TF-v3 opened any trades
    tf_v2_trades = 0
    tf_v3_trades = 0
    for strategy_name, strategy_results in results.get('strategies', {}).items():
        if strategy_name == 'trend_following':
            tf_v2_trades = strategy_results.get('trade_count', 0)
        elif strategy_name == 'tf_v3':
            tf_v3_trades = strategy_results.get('trade_count', 0)
    
    logger.info(f"TF-v2 trade count: {tf_v2_trades}")
    logger.info(f"TF-v3 trade count: {tf_v3_trades}")
    
    if tf_v3_trades > 0:
        logger.info("✅ REGRESSION TEST PASSED: TF-v3 opened at least one trade")
        return True, results
    else:
        logger.error("❌ REGRESSION TEST FAILED: TF-v3 did not open any trades")
        
        # Print top skip reasons
        skip_reasons = {}
        for strategy_name, strategy_results in results.get('strategies', {}).items():
            if strategy_name == 'tf_v3':
                skip_reasons = strategy_results.get('skip_reasons', {})
        
        logger.info("Top skip reasons:")
        for reason, count in sorted(skip_reasons.items(), key=lambda x: x[1], reverse=True)[:5]:
            logger.info(f"  {reason}: {count}")
        
        return False, results

def generate_comparison_report(results, output_path):
    """
    Generate a comparison report between TF-v2 and TF-v3.
    
    Args:
        results: Backtest results
        output_path: Path to output file
    """
    logger.info("Generating comparison report...")
    
    # Extract strategy results
    tf_v2_results = None
    tf_v3_results = None
    for strategy_name, strategy_results in results.get('strategies', {}).items():
        if strategy_name == 'trend_following':
            tf_v2_results = strategy_results
        elif strategy_name == 'tf_v3':
            tf_v3_results = strategy_results
    
    if not tf_v2_results or not tf_v3_results:
        logger.error("Missing strategy results for comparison")
        return
    
    # Create comparison table
    comparison = {
        'Metric': [
            'Trade Count',
            'Win Rate',
            'Profit Factor',
            'Average Win',
            'Average Loss',
            'Max Drawdown',
            'Sharpe Ratio',
            'Total PnL',
            'Avg Hold Time (h)'
        ],
        'TF-v2': [
            tf_v2_results.get('trade_count', 0),
            f"{tf_v2_results.get('win_rate', 0) * 100:.1f}%",
            f"{tf_v2_results.get('profit_factor', 0):.2f}",
            f"${tf_v2_results.get('avg_win', 0):.2f}",
            f"${tf_v2_results.get('avg_loss', 0):.2f}",
            f"${tf_v2_results.get('max_drawdown', 0):.2f}",
            f"{tf_v2_results.get('sharpe', 0):.2f}",
            f"${tf_v2_results.get('total_pnl', 0):.2f}",
            f"{tf_v2_results.get('avg_hold_time_hours', 0):.1f}"
        ],
        'TF-v3': [
            tf_v3_results.get('trade_count', 0),
            f"{tf_v3_results.get('win_rate', 0) * 100:.1f}%",
            f"{tf_v3_results.get('profit_factor', 0):.2f}",
            f"${tf_v3_results.get('avg_win', 0):.2f}",
            f"${tf_v3_results.get('avg_loss', 0):.2f}",
            f"${tf_v3_results.get('max_drawdown', 0):.2f}",
            f"{tf_v3_results.get('sharpe', 0):.2f}",
            f"${tf_v3_results.get('total_pnl', 0):.2f}",
            f"{tf_v3_results.get('avg_hold_time_hours', 0):.1f}"
        ]
    }
    
    # Create markdown report
    markdown = "# TF-v2 vs TF-v3 Comparison Report\n\n"
    markdown += f"**Date Range:** {results.get('start_date')} to {results.get('end_date')}\n\n"
    markdown += f"**Detector Type:** {results.get('config', {}).get('regime', {}).get('detector_type', 'unknown')}\n\n"
    
    # Add comparison table
    markdown += "## Performance Metrics\n\n"
    markdown += "| Metric | TF-v2 | TF-v3 |\n"
    markdown += "|--------|-------|-------|\n"
    for i in range(len(comparison['Metric'])):
        markdown += f"| {comparison['Metric'][i]} | {comparison['TF-v2'][i]} | {comparison['TF-v3'][i]} |\n"
    
    # Add skip reasons
    markdown += "\n## Skip Reasons\n\n"
    markdown += "### TF-v2 Skip Reasons\n\n"
    markdown += "| Reason | Count |\n"
    markdown += "|--------|-------|\n"
    for reason, count in sorted(tf_v2_results.get('skip_reasons', {}).items(), key=lambda x: x[1], reverse=True)[:10]:
        markdown += f"| {reason} | {count} |\n"
    
    markdown += "\n### TF-v3 Skip Reasons\n\n"
    markdown += "| Reason | Count |\n"
    markdown += "|--------|-------|\n"
    for reason, count in sorted(tf_v3_results.get('skip_reasons', {}).items(), key=lambda x: x[1], reverse=True)[:10]:
        markdown += f"| {reason} | {count} |\n"
    
    # Write report to file
    with open(output_path, 'w') as f:
        f.write(markdown)
    
    logger.info(f"Comparison report saved to {output_path}")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Run regression test for TF-v3 strategy')
    parser.add_argument('--start', type=str, default='2025-03-01', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end', type=str, default='2025-03-22', help='End date (YYYY-MM-DD)')
    parser.add_argument('--config', type=str, default='configs/base.yaml', help='Path to config file')
    parser.add_argument('--feature-dir', type=str, default='/hyperliquid_data/features_1s', help='Path to feature directory')
    parser.add_argument('--ohlc-dir', type=str, default='/hyperliquid_data/ohlcv_1h', help='Path to OHLC directory')
    parser.add_argument('--output', type=str, default='results/regression_tf_v3.json', help='Path to output file')
    parser.add_argument('--report', type=str, default='reports/regression_tf2_vs_tf3.md', help='Path to report file')
    
    args = parser.parse_args()
    
    # Create output directories if they don't exist
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    os.makedirs(os.path.dirname(args.report), exist_ok=True)
    
    # Run regression test
    success, results = run_regression_test(
        args.start,
        args.end,
        args.config,
        args.feature_dir,
        args.ohlc_dir,
        args.output
    )
    
    # Generate comparison report
    if success:
        generate_comparison_report(results, args.report)
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
