#!/usr/bin/env python3
"""
Analyze regime confidence distribution from backtest results.

This script examines why all regimes show high confidence (0.9-1.0) in 2024 data.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_backtest_trades(log_dir="/Users/<USER>/Desktop/trading_bot_/logs"):
    """Load the most recent trades JSON from backtest."""
    log_path = Path(log_dir)
    
    # Find most recent trades file (JSON format)
    trade_files = list(log_path.glob("backtest_trades_*.json"))
    if not trade_files:
        logger.error("No trade files found in logs directory")
        return None
    
    # Sort by modification time and get the most recent
    latest_file = max(trade_files, key=lambda p: p.stat().st_mtime)
    logger.info(f"Loading trades from: {latest_file}")
    
    # Load the JSON
    with open(latest_file, 'r') as f:
        trades_data = json.load(f)
    
    # Convert to DataFrame
    df = pd.DataFrame(trades_data)
    return df


def analyze_confidence_distribution(trades_df):
    """Analyze the distribution of regime confidence values."""
    if 'regime_confidence' not in trades_df.columns:
        logger.warning("No regime_confidence column found in trades data")
        return
    
    confidence_values = trades_df['regime_confidence'].dropna()
    
    # Basic statistics
    logger.info("\n=== Regime Confidence Statistics ===")
    logger.info(f"Total trades: {len(confidence_values)}")
    logger.info(f"Mean confidence: {confidence_values.mean():.4f}")
    logger.info(f"Median confidence: {confidence_values.median():.4f}")
    logger.info(f"Min confidence: {confidence_values.min():.4f}")
    logger.info(f"Max confidence: {confidence_values.max():.4f}")
    logger.info(f"Std deviation: {confidence_values.std():.4f}")
    
    # Distribution by bins
    bins = [0, 0.3, 0.5, 0.7, 0.9, 0.95, 1.0]
    hist, bin_edges = np.histogram(confidence_values, bins=bins)
    
    logger.info("\n=== Confidence Distribution ===")
    for i in range(len(hist)):
        pct = (hist[i] / len(confidence_values)) * 100
        logger.info(f"{bin_edges[i]:.2f} - {bin_edges[i+1]:.2f}: {hist[i]} trades ({pct:.1f}%)")
    
    # High confidence analysis
    high_conf = confidence_values[confidence_values >= 0.9]
    logger.info(f"\nTrades with confidence >= 0.9: {len(high_conf)} ({len(high_conf)/len(confidence_values)*100:.1f}%)")
    
    # Create histogram plot
    plt.figure(figsize=(10, 6))
    plt.hist(confidence_values, bins=30, alpha=0.7, color='blue', edgecolor='black')
    plt.axvline(confidence_values.mean(), color='red', linestyle='dashed', linewidth=2, label=f'Mean: {confidence_values.mean():.3f}')
    plt.axvline(confidence_values.median(), color='green', linestyle='dashed', linewidth=2, label=f'Median: {confidence_values.median():.3f}')
    plt.xlabel('Regime Confidence')
    plt.ylabel('Number of Trades')
    plt.title('Distribution of Regime Confidence Values (2024 Data)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Save plot
    plt.savefig('regime_confidence_distribution.png', dpi=150, bbox_inches='tight')
    logger.info("\nSaved confidence distribution plot to: regime_confidence_distribution.png")
    
    return confidence_values


def analyze_confidence_by_regime(trades_df):
    """Analyze confidence by regime type."""
    if 'entry_regime' not in trades_df.columns or 'regime_confidence' not in trades_df.columns:
        logger.warning("Required columns not found for regime analysis")
        return
    
    logger.info("\n=== Confidence by Regime Type ===")
    
    regime_groups = trades_df.groupby('entry_regime')['regime_confidence']
    
    for regime, group in regime_groups:
        logger.info(f"\n{regime}:")
        logger.info(f"  Count: {len(group)}")
        logger.info(f"  Mean: {group.mean():.4f}")
        logger.info(f"  Std: {group.std():.4f}")
        logger.info(f"  Min: {group.min():.4f}")
        logger.info(f"  Max: {group.max():.4f}")


def check_confidence_calculation():
    """Examine the confidence calculation logic in the code."""
    logger.info("\n=== Confidence Calculation Analysis ===")
    
    # From gms_detector.py lines 1018-1067, the confidence calculation is:
    # 1. For natural progressions (e.g., BULL->STRONG_BULL): confidence = 0.9
    # 2. For reversals (e.g., BULL->BEAR): confidence = 0.5
    # 3. For stable states: confidence based on sigmoid of consistency score
    
    logger.info("\nConfidence calculation logic:")
    logger.info("1. Natural progressions: 0.9 (90%)")
    logger.info("2. Reversals: 0.5 (50%)")
    logger.info("3. Stable states: sigmoid(consistency_score)")
    logger.info("   - consistency_score = time_in_state / 300 (5 minutes)")
    logger.info("   - sigmoid steepness = 10.0")
    
    # Simulate sigmoid transformation
    time_values = np.linspace(0, 600, 100)  # 0 to 10 minutes
    consistency_scores = time_values / 300  # Normalized by 5 minutes
    sigmoid_values = 1 / (1 + np.exp(-10 * (consistency_scores - 0.5)))
    
    # Find when confidence reaches 0.9
    idx_90 = np.argmax(sigmoid_values >= 0.9)
    time_to_90 = time_values[idx_90]
    
    logger.info(f"\nTime to reach 0.9 confidence in stable state: {time_to_90:.1f} seconds ({time_to_90/60:.1f} minutes)")
    
    # Plot sigmoid curve
    plt.figure(figsize=(10, 6))
    plt.plot(time_values/60, sigmoid_values, 'b-', linewidth=2)
    plt.axhline(y=0.9, color='r', linestyle='--', alpha=0.5, label='0.9 confidence')
    plt.axvline(x=time_to_90/60, color='r', linestyle='--', alpha=0.5)
    plt.xlabel('Time in State (minutes)')
    plt.ylabel('Confidence')
    plt.title('Confidence vs Time in Stable State (sigmoid transformation)')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.xlim(0, 10)
    plt.ylim(0, 1)
    
    plt.savefig('confidence_sigmoid_curve.png', dpi=150, bbox_inches='tight')
    logger.info("Saved sigmoid curve to: confidence_sigmoid_curve.png")


def analyze_state_durations(trades_df):
    """Analyze how long the system stays in each state."""
    # This would require access to the full regime history, not just trade entries
    # For now, we can look at trade intervals
    
    if 'entry_time' not in trades_df.columns:
        logger.warning("No entry_time column found")
        return
    
    # Convert to datetime if needed
    if not pd.api.types.is_datetime64_any_dtype(trades_df['entry_time']):
        trades_df['entry_time'] = pd.to_datetime(trades_df['entry_time'])
    
    # Sort by entry time
    trades_df = trades_df.sort_values('entry_time')
    
    # Calculate time between trades
    trade_intervals = trades_df['entry_time'].diff().dt.total_seconds() / 3600  # Convert to hours
    
    logger.info("\n=== Trade Interval Analysis ===")
    logger.info(f"Mean time between trades: {trade_intervals.mean():.2f} hours")
    logger.info(f"Median time between trades: {trade_intervals.median():.2f} hours")
    logger.info(f"Min interval: {trade_intervals.min():.2f} hours")
    logger.info(f"Max interval: {trade_intervals.max():.2f} hours")


def main():
    """Main analysis function."""
    logger.info("Starting regime confidence analysis...")
    
    # Load trades data
    trades_df = load_backtest_trades()
    if trades_df is None:
        return
    
    logger.info(f"Loaded {len(trades_df)} trades")
    logger.info(f"Columns: {list(trades_df.columns)}")
    
    # Analyze confidence distribution
    confidence_values = analyze_confidence_distribution(trades_df)
    
    # Analyze by regime type
    analyze_confidence_by_regime(trades_df)
    
    # Check confidence calculation logic
    check_confidence_calculation()
    
    # Analyze state durations
    analyze_state_durations(trades_df)
    
    # Key findings
    logger.info("\n=== KEY FINDINGS ===")
    logger.info("1. If most trades show 0.9 confidence, they are likely 'natural progressions'")
    logger.info("2. The sigmoid curve reaches 0.9 after ~3.3 minutes in a stable state")
    logger.info("3. This suggests the market spent most of 2024 in stable trending conditions")
    logger.info("4. Consider adjusting sigmoid steepness or time normalization for more sensitivity")


if __name__ == "__main__":
    main()