import os
import glob
import json
import pandas as pd
import time
from tqdm import tqdm
import numpy as np
import pyarrow as pa
import pyarrow.parquet as pq

def process_l2book_files_for_day(day_dir, output_dir):
    """
    Process all L2 book files for a specific day
    
    Args:
        day_dir: Directory containing L2 book files for a day
        output_dir: Directory to save the processed parquet file
    """
    date_match = os.path.basename(day_dir)
    print(f"Processing data for date: {date_match}")
    
    # Find all files in the day directory
    all_files = glob.glob(os.path.join(day_dir, "*.txt"))
    l2_files = [f for f in all_files if "l2Book" in f]
    
    if not l2_files:
        print(f"No L2 book files found in {day_dir}")
        return
        
    print(f"Found {len(l2_files)} L2 book files for day {date_match}")
    
    # Create data structure for order book levels - EXPLICITLY CREATING ALL COLUMNS
    data = {
        'timestamp': [],
        'bid_price_1': [], 'bid_size_1': [],
        'bid_price_2': [], 'bid_size_2': [],
        'bid_price_3': [], 'bid_size_3': [],
        'bid_price_4': [], 'bid_size_4': [],
        'bid_price_5': [], 'bid_size_5': [],
        'ask_price_1': [], 'ask_size_1': [],
        'ask_price_2': [], 'ask_size_2': [],
        'ask_price_3': [], 'ask_size_3': [],
        'ask_price_4': [], 'ask_size_4': [],
        'ask_price_5': [], 'ask_size_5': [],
        'mid_price': [],
        'spread': []
    }
    
    # Process all files for the day
    total_lines = 0
    valid_count = 0
    error_count = 0
    hour_stats = {}
    
    for l2_file in sorted(l2_files):
        filename = os.path.basename(l2_file)
        file_size_mb = os.path.getsize(l2_file) / (1024 * 1024)
        print(f"\nProcessing file: {filename} ({file_size_mb:.2f} MB)")
        
        # Count lines for progress bar
        with open(l2_file, 'r') as f:
            line_count = sum(1 for _ in f)
            total_lines += line_count
        
        lines_processed = 0
        hour_valid_count = 0
        
        with open(l2_file, 'r') as f:
            for line in tqdm(f, total=line_count, desc=f"Parsing {filename}"):
                lines_processed += 1
                
                if not line.strip():
                    continue
                    
                try:
                    # Handle malformed JSON with extra {} at beginning
                    if line.startswith('{}'):
                        line = line[2:]
                        
                    # Parse the JSON object
                    obj = json.loads(line)
                    
                    # Skip non-L2Book messages
                    if not (obj.get("raw", {}).get("channel") == "l2Book"):
                        continue
                        
                    # Extract timestamp - USE INNER EXCHANGE TIMESTAMP
                    ts_inner = obj.get("raw", {}).get("data", {}).get("time")
                    if not ts_inner:
                        continue # Skip if inner timestamp is missing
                    
                    # Convert assuming MILLISECONDS since epoch
                    try:
                        # Check if ts_inner is already a number
                        if not isinstance(ts_inner, (int, float)):
                            ts_inner = int(ts_inner) # Try converting if it's a string digit
                        
                        timestamp = pd.to_datetime(ts_inner / 1000, unit='s', utc=True) # Divide by 1000 for ms -> s
                        
                    except (ValueError, TypeError) as e_ts:
                        #logger.warning(f"Could not parse inner timestamp {ts_inner} at line {lines_processed}: {e_ts}") # Add logger if needed
                        error_count += 1 # Count as an error for now
                        if error_count < 10: print(f"Warn: Could not parse inner timestamp {ts_inner} at line {lines_processed}: {e_ts}")
                        continue # Skip record if timestamp invalid
                    
                    hour = timestamp.hour
                    
                    # Track stats by hour
                    if hour not in hour_stats:
                        hour_stats[hour] = 0
                    hour_stats[hour] += 1
                    
                    # Extract order book data
                    levels = obj.get("raw", {}).get("data", {}).get("levels")
                    if not levels or len(levels) < 2:
                        continue
                        
                    bids = levels[0]
                    asks = levels[1]
                    
                    # We'll use whatever levels are available up to 5
                    # but skip records with no levels at all
                    if not bids or not asks:
                        continue
                        
                    # Extract top 5 levels - or fewer if not enough data
                    data['timestamp'].append(timestamp)
                    
                    # Extract all 5 bid levels - CRITICAL: Must explicitly handle each level
                    # Level 1 (Always ensure this exists as it's required)
                    if len(bids) >= 1:
                        data['bid_price_1'].append(float(bids[0]['px']))
                        data['bid_size_1'].append(float(bids[0]['sz']))
                    else:
                        data['bid_price_1'].append(None)
                        data['bid_size_1'].append(None)
                    
                    # Level 2
                    if len(bids) >= 2:
                        data['bid_price_2'].append(float(bids[1]['px']))
                        data['bid_size_2'].append(float(bids[1]['sz']))
                    else:
                        data['bid_price_2'].append(None)
                        data['bid_size_2'].append(None)
                    
                    # Level 3
                    if len(bids) >= 3:
                        data['bid_price_3'].append(float(bids[2]['px']))
                        data['bid_size_3'].append(float(bids[2]['sz']))
                    else:
                        data['bid_price_3'].append(None)
                        data['bid_size_3'].append(None)
                    
                    # Level 4
                    if len(bids) >= 4:
                        data['bid_price_4'].append(float(bids[3]['px']))
                        data['bid_size_4'].append(float(bids[3]['sz']))
                    else:
                        data['bid_price_4'].append(None)
                        data['bid_size_4'].append(None)
                    
                    # Level 5
                    if len(bids) >= 5:
                        data['bid_price_5'].append(float(bids[4]['px']))
                        data['bid_size_5'].append(float(bids[4]['sz']))
                    else:
                        data['bid_price_5'].append(None)
                        data['bid_size_5'].append(None)
                    
                    # Extract all 5 ask levels - CRITICAL: Must explicitly handle each level
                    # Level 1 (Always ensure this exists as it's required)
                    if len(asks) >= 1:
                        data['ask_price_1'].append(float(asks[0]['px']))
                        data['ask_size_1'].append(float(asks[0]['sz']))
                    else:
                        data['ask_price_1'].append(None)
                        data['ask_size_1'].append(None)
                    
                    # Level 2
                    if len(asks) >= 2:
                        data['ask_price_2'].append(float(asks[1]['px']))
                        data['ask_size_2'].append(float(asks[1]['sz']))
                    else:
                        data['ask_price_2'].append(None)
                        data['ask_size_2'].append(None)
                    
                    # Level 3
                    if len(asks) >= 3:
                        data['ask_price_3'].append(float(asks[2]['px']))
                        data['ask_size_3'].append(float(asks[2]['sz']))
                    else:
                        data['ask_price_3'].append(None)
                        data['ask_size_3'].append(None)
                    
                    # Level 4
                    if len(asks) >= 4:
                        data['ask_price_4'].append(float(asks[3]['px']))
                        data['ask_size_4'].append(float(asks[3]['sz']))
                    else:
                        data['ask_price_4'].append(None)
                        data['ask_size_4'].append(None)
                    
                    # Level 5
                    if len(asks) >= 5:
                        data['ask_price_5'].append(float(asks[4]['px']))
                        data['ask_size_5'].append(float(asks[4]['sz']))
                    else:
                        data['ask_price_5'].append(None)
                        data['ask_size_5'].append(None)
                    
                    # Calculate mid price and spread
                    best_bid = float(bids[0]['px']) if bids else None
                    best_ask = float(asks[0]['px']) if asks else None
                    
                    if best_bid is not None and best_ask is not None:
                        mid_price = (best_bid + best_ask) / 2
                        spread = best_ask - best_bid
                    else:
                        mid_price = None
                        spread = None
                    
                    data['mid_price'].append(mid_price)
                    data['spread'].append(spread)
                    
                    valid_count += 1
                    hour_valid_count += 1
                    
                except Exception as e:
                    error_count += 1
                    if error_count < 10:  # Only show first few errors
                        print(f"Error processing line: {e}")
                    elif error_count == 10:
                        print("Additional errors suppressed...")
        
        print(f"Processed {lines_processed} lines, extracted {hour_valid_count} valid records")
    
    print(f"\nTotal for day {date_match}: {total_lines} lines processed, {valid_count} valid records, {error_count} errors")
    
    if valid_count == 0:
        print(f"No valid records found for day {date_match}, skipping")
        return
    
    # Print hour distribution
    print("\nHour distribution:")
    for hour in range(24):
        count = hour_stats.get(hour, 0)
        print(f"Hour {hour:02d}: {count} records")
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    # Verify levels before derivation
    print("\nVerifying L2 data before derived metrics:")
    for i in range(1, 6):
        bid_price_col = f'bid_price_{i}'
        ask_price_col = f'ask_price_{i}'
        bid_count = df[bid_price_col].count()
        ask_count = df[ask_price_col].count()
        print(f"Level {i}: {bid_count} bids, {ask_count} asks")
    
    # ============================================================================
    # DERIVED METRICS SECTION - ORGANIZED BY CATEGORY
    # ============================================================================
    
    # --- Basic Volume Metrics ---
    # Calculate total volume on each side
    bid_vol = df[[f'bid_size_{i}' for i in range(1, 6)]].sum(axis=1)
    ask_vol = df[[f'ask_size_{i}' for i in range(1, 6)]].sum(axis=1)
    
    # Total volume in orderbook
    df['volume_proxy'] = bid_vol + ask_vol
    
    # --- Mean Reversion Indicators ---
    # Order book imbalance (normalized difference between bid and ask volumes)
    df['imbalance'] = (bid_vol - ask_vol) / (bid_vol + ask_vol)
    
    # Bid-ask ratio (multiplicative version of imbalance)
    df['bid_ask_ratio'] = bid_vol / ask_vol
    
    # Log-transformed bid-ask ratio (more normally distributed)
    df['bid_ask_ratio_log'] = np.log(df['bid_ask_ratio'])
    
    # Weighted mid-price calculation
    bid_weights = df[[f'bid_size_{i}' for i in range(1, 6)]].values
    ask_weights = df[[f'ask_size_{i}' for i in range(1, 6)]].values
    bid_prices = df[[f'bid_price_{i}' for i in range(1, 6)]].values
    ask_prices = df[[f'ask_price_{i}' for i in range(1, 6)]].values
    
    weighted_bid = np.nansum(bid_prices * bid_weights, axis=1) / np.nansum(bid_weights, axis=1)
    weighted_ask = np.nansum(ask_prices * ask_weights, axis=1) / np.nansum(ask_weights, axis=1)
    df['weighted_mid'] = (weighted_bid + weighted_ask) / 2
    
    # --- Liquidity Structure Metrics ---
    # Price impact metrics - shows steepness of order book
    df['price_impact_bid'] = (df['bid_price_1'] - df['bid_price_5']) / df['bid_price_1']
    df['price_impact_ask'] = (df['ask_price_5'] - df['ask_price_1']) / df['ask_price_1']
    
    # Order book slope (price change relative to volume)
    df['bid_slope'] = (df['bid_price_1'] - df['bid_price_5']) / bid_vol
    df['ask_slope'] = (df['ask_price_5'] - df['ask_price_1']) / ask_vol
    
    # Concentration at top of book
    df['bid_concentration'] = df['bid_size_1'] / bid_vol
    df['ask_concentration'] = df['ask_size_1'] / ask_vol
    
    # --- Kelly Metrics for Position Sizing ---
    # Book volatility estimate
    df['book_volatility'] = (df['ask_price_1'] - df['bid_price_1']) / df['mid_price'] * np.sqrt(252 * 1440)
    
    # Market efficiency - how tight spread is relative to available liquidity
    df['market_efficiency'] = df['spread'] / df['volume_proxy']
    
    # Book asymmetry - shows if book is more resistant to moves in one direction
    df['book_asymmetry'] = (df['ask_slope'] - df['bid_slope']) / (df['ask_slope'] + df['bid_slope'])
    
    # Sort by timestamp
    df = df.sort_values('timestamp')
    
    # Verify all derived metrics are present
    print("\nVerifying derived metrics columns:")
    for metric_col in ['volume_proxy', 'imbalance', 'bid_ask_ratio', 'weighted_mid', 
                      'bid_slope', 'ask_slope', 'book_asymmetry']:
        if metric_col in df.columns:
            print(f"✓ {metric_col}: {df[metric_col].count()} values")
        else:
            print(f"✗ Missing: {metric_col}")
    
    # IMPORTANT: Force data to non-null for intermediate levels if missing
    for i in range(2, 5):  # Force levels 2,3,4 to have at least one non-null value
        for prefix in ['bid_price_', 'bid_size_', 'ask_price_', 'ask_size_']:
            col = f"{prefix}{i}"
            # Replace first None with a dummy value if entire column is null
            if df[col].count() == 0:
                df.loc[0, col] = 0.0 if 'size' in col else df.loc[0, f"{prefix}1"]
                print(f"Added dummy value to ensure column {col} is preserved")

    # Define explicit column order to ensure all columns are included
    all_columns = ['timestamp']
    for side in ['bid', 'ask']:
        for level in range(1, 6):
            all_columns.append(f'{side}_price_{level}')
            all_columns.append(f'{side}_size_{level}')
    all_columns.extend(['mid_price', 'spread'])

    # Add derived metrics to column list
    all_columns.extend(['volume_proxy', 'imbalance', 'bid_ask_ratio', 'bid_ask_ratio_log',
                    'weighted_mid', 'price_impact_bid', 'price_impact_ask', 
                    'bid_slope', 'ask_slope', 'bid_concentration', 'ask_concentration',
                    'book_volatility', 'market_efficiency', 'book_asymmetry'])

    # Create a new DataFrame with explicit column order 
    # This ensures all columns exist even if they're empty
    final_df = pd.DataFrame(index=df.index)
    for col in all_columns:
        if col in df.columns:
            final_df[col] = df[col]
        else:
            final_df[col] = np.nan
            print(f"Created missing column: {col}")

    # Ensure at least one non-null value in each column
    for col in df.columns:
        if df[col].isna().all():
            print(f"Adding non-null value to column {col}")
            df.loc[0, col] = 0.0  # Add a non-null value to the first row
        
    # Use PyArrow directly to preserve schema
    output_file = os.path.join(output_dir, f"{date_match}_raw2.parquet")
    table = pa.Table.from_pandas(df)
    pq.write_table(table, output_file)
    print("Saved parquet file with explicit schema preservation")

    # Verify all columns are preserved
    test_df = pd.read_parquet(output_file)
    print(f"Columns in saved file: {test_df.columns.tolist()}")

    # Final verification of saved file
    print("\nVerifying saved file structure:")
    try:
        saved_df = pd.read_parquet(output_file)
        print(f"All columns in saved file ({len(saved_df.columns)}):")
        for col in saved_df.columns:
            print(f"- {col}: {saved_df[col].count()} values")
    except Exception as e:
        print(f"Error verifying saved file: {e}")
    
    print(f"\nSaved {len(df)} records to {output_file}")
    print(f"Time range: {df['timestamp'].min()} to {df['timestamp'].max()}")
    print(f"File size: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
    
    # Quick validation
    hour_coverage = df['timestamp'].dt.hour.nunique()
    print(f"Hours covered: {hour_coverage}/24")
    
    return df

# Rest of the functions unchanged...
def find_day_dirs(base_dir):
    """Find all day directories in the base directory"""
    day_dirs = []
    
    # First, check if directories follow YYYYMMDD pattern
    for item in os.listdir(base_dir):
        item_path = os.path.join(base_dir, item)
        if os.path.isdir(item_path) and item.isdigit() and len(item) == 8:
            day_dirs.append(item_path)
    
    return sorted(day_dirs)

def validate_first_file(day_dir):
    """Validate first file processing to ensure all 5 levels are properly extracted"""
    print("\nVALIDATING LEVEL EXTRACTION...")
    
    # Find first L2 book file
    all_files = glob.glob(os.path.join(day_dir, "*.txt"))
    l2_files = [f for f in all_files if "l2Book" in f]
    if not l2_files:
        print("No files found for validation")
        return
    
    # Read just a small portion to validate
    with open(l2_files[0], 'r') as f:
        # Read first 10 valid lines
        valid_lines = 0
        samples = []
        for line in f:
            if not line.strip():
                continue
                
            # Process the line
            try:
                # Handle malformed JSON
                if line.startswith('{}'):
                    line = line[2:]
                    
                obj = json.loads(line)
                
                # Check if it's an L2Book message
                if obj.get("raw", {}).get("channel") == "l2Book":
                    levels = obj.get("raw", {}).get("data", {}).get("levels")
                    if levels and len(levels) >= 2:
                        bids = levels[0]
                        asks = levels[1]
                        
                        if bids and asks:
                            # Store this sample
                            samples.append({
                                'timestamp': obj.get("time"),
                                'bids': bids[:5] if len(bids) >= 5 else bids,
                                'asks': asks[:5] if len(asks) >= 5 else asks
                            })
                            valid_lines += 1
                            
                            if valid_lines >= 10:
                                break
            except:
                continue
    
    # Display the validation results
    if samples:
        print(f"Found {len(samples)} sample records")
        sample = samples[0]
        
        print("\nBID LEVELS:")
        for i, bid in enumerate(sample['bids']):
            print(f"  Level {i+1}: Price={bid['px']}, Size={bid['sz']}")
            
        print("\nASK LEVELS:")
        for i, ask in enumerate(sample['asks']):
            print(f"  Level {i+1}: Price={ask['px']}, Size={ask['sz']}")
            
        print("\nConfirming all 5 levels will be extracted correctly...")
        # Simulate the extraction process on the first sample
        level_data = {
            'bid_price_1': [], 'bid_size_1': [],
            'bid_price_2': [], 'bid_size_2': [],
            'bid_price_3': [], 'bid_size_3': [],
            'bid_price_4': [], 'bid_size_4': [],
            'bid_price_5': [], 'bid_size_5': [],
            'ask_price_1': [], 'ask_size_1': [],
            'ask_price_2': [], 'ask_size_2': [], 
            'ask_price_3': [], 'ask_size_3': [],
            'ask_price_4': [], 'ask_size_4': [],
            'ask_price_5': [], 'ask_size_5': []
        }
        
        # Extract bid levels individually to ensure correct indexing
        bids = sample['bids']
        asks = sample['asks']
        
        # Explicitly handle each bid level
        if len(bids) >= 1:
            level_data['bid_price_1'].append(float(bids[0]['px']))
            level_data['bid_size_1'].append(float(bids[0]['sz']))
        else:
            level_data['bid_price_1'].append(None)
            level_data['bid_size_1'].append(None)
            
        if len(bids) >= 2:
            level_data['bid_price_2'].append(float(bids[1]['px']))
            level_data['bid_size_2'].append(float(bids[1]['sz']))
        else:
            level_data['bid_price_2'].append(None)
            level_data['bid_size_2'].append(None)
            
        if len(bids) >= 3:
            level_data['bid_price_3'].append(float(bids[2]['px']))
            level_data['bid_size_3'].append(float(bids[2]['sz']))
        else:
            level_data['bid_price_3'].append(None)
            level_data['bid_size_3'].append(None)
            
        if len(bids) >= 4:
            level_data['bid_price_4'].append(float(bids[3]['px']))
            level_data['bid_size_4'].append(float(bids[3]['sz']))
        else:
            level_data['bid_price_4'].append(None)
            level_data['bid_size_4'].append(None)
            
        if len(bids) >= 5:
            level_data['bid_price_5'].append(float(bids[4]['px']))
            level_data['bid_size_5'].append(float(bids[4]['sz']))
        else:
            level_data['bid_price_5'].append(None)
            level_data['bid_size_5'].append(None)
        
        # Explicitly handle each ask level (same pattern)
        if len(asks) >= 1:
            level_data['ask_price_1'].append(float(asks[0]['px']))
            level_data['ask_size_1'].append(float(asks[0]['sz']))
        else:
            level_data['ask_price_1'].append(None)
            level_data['ask_size_1'].append(None)
            
        if len(asks) >= 2:
            level_data['ask_price_2'].append(float(asks[1]['px']))
            level_data['ask_size_2'].append(float(asks[1]['sz']))
        else:
            level_data['ask_price_2'].append(None)
            level_data['ask_size_2'].append(None)
            
        if len(asks) >= 3:
            level_data['ask_price_3'].append(float(asks[2]['px']))
            level_data['ask_size_3'].append(float(asks[2]['sz']))
        else:
            level_data['ask_price_3'].append(None)
            level_data['ask_size_3'].append(None)
            
        if len(asks) >= 4:
            level_data['ask_price_4'].append(float(asks[3]['px']))
            level_data['ask_size_4'].append(float(asks[3]['sz']))
        else:
            level_data['ask_price_4'].append(None)
            level_data['ask_size_4'].append(None)
            
        if len(asks) >= 5:
            level_data['ask_price_5'].append(float(asks[4]['px']))
            level_data['ask_size_5'].append(float(asks[4]['sz']))
        else:
            level_data['ask_price_5'].append(None)
            level_data['ask_size_5'].append(None)
        
        # Display the extracted data
        print("\nEXTRACTED DATA VERIFICATION:")
        for i in range(1, 6):
            print(f"Level {i}: bid_price={level_data[f'bid_price_{i}'][0]}, " + 
                  f"bid_size={level_data[f'bid_size_{i}'][0]}, " + 
                  f"ask_price={level_data[f'ask_price_{i}'][0]}, " + 
                  f"ask_size={level_data[f'ask_size_{i}'][0]}")
        
        print("\nValidation complete. All 5 levels should be properly extracted.")
    else:
        print("No valid samples found for validation.")

def main():
    # Source directories
    btc_data_2024 = "/Users/<USER>/Desktop/trading_bot_/btc_data_2024"
    btc_data_2025 = "/Users/<USER>/Desktop/trading_bot_/btc_data_2025"
    
    # Output directory
    output_dir = "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw2"
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all day directories
    day_dirs_2024 = find_day_dirs(btc_data_2024)
    day_dirs_2025 = find_day_dirs(btc_data_2025)
    
    all_day_dirs = day_dirs_2024 + day_dirs_2025
    print(f"Found {len(all_day_dirs)} day directories")
    
    if not all_day_dirs:
        print("No day directories found. Please check the input directories.")
        return
    
    # Run validation on the first directory
    if all_day_dirs:
        validate_first_file(all_day_dirs[0])
    
    # Process each day directory
    start_time = time.time()
    
    # Ask for confirmation to proceed
    print("\nReady to process all day directories.")
    proceed = input("Type 'y' to proceed or anything else to run one test file only: ").strip().lower()
    
    if proceed == 'y':
        for i, day_dir in enumerate(all_day_dirs):
            print(f"\n{'='*80}")
            print(f"Processing day directory {i+1}/{len(all_day_dirs)}: {day_dir}")
            print(f"{'='*80}")
            process_l2book_files_for_day(day_dir, output_dir)
    else:
        # Process just the first directory as a test
        print("\nRunning test on first directory only...")
        process_l2book_files_for_day(all_day_dirs[0], output_dir)
        
    end_time = time.time()
    print(f"\nProcessing complete! Total time: {end_time - start_time:.2f} seconds")
    print(f"Processed data saved to {output_dir}")
    
    # Optional: Verify final output
    output_files = glob.glob(os.path.join(output_dir, "*.parquet"))
    print(f"Generated {len(output_files)} output files")

if __name__ == "__main__":
    main()

def test_and_fix_parquet():
    """Diagnostic function to demonstrate the column pruning issue and fix"""
    import pandas as pd
    import numpy as np
    import pyarrow as pa
    import pyarrow.parquet as pq
    
    # Create test data
    df = pd.DataFrame({
        'timestamp': pd.date_range('2023-01-01', periods=10),
        'bid_price_1': np.random.rand(10),
        'bid_price_2': np.nan,  # All NaN
        'bid_price_3': np.nan,  # All NaN
        'bid_price_4': np.nan,  # All NaN
        'bid_price_5': np.random.rand(10)
    })
    
    print("Original DataFrame columns:", df.columns.tolist())
    
    # Write using pandas
    df.to_parquet('test_pandas.parquet')
    
    # Read back
    df_read = pd.read_parquet('test_pandas.parquet')
    print("Columns after pandas roundtrip:", df_read.columns.tolist())
    
    # Fix 1: Add non-null values to ensure columns aren't pruned
    df_fix = df.copy()
    df_fix.loc[0, 'bid_price_2'] = 0.0
    df_fix.loc[0, 'bid_price_3'] = 0.0
    df_fix.loc[0, 'bid_price_4'] = 0.0
    df_fix.to_parquet('test_fix1.parquet')
    
    # Read back
    df_fix_read = pd.read_parquet('test_fix1.parquet')
    print("Columns with non-null values added:", df_fix_read.columns.tolist())
    
    # Fix 2: Use pyarrow directly with schema preservation
    table = pa.Table.from_pandas(df)
    pq.write_table(table, 'test_fix2.parquet')
    
    # Read back
    df_fix2_read = pd.read_parquet('test_fix2.parquet')
    print("Columns using pyarrow directly:", df_fix2_read.columns.tolist())
    
    # Fix 3: Force pandas to preserve the schema with a dummy row
    df_fix3 = df.copy()
    # Add this line to your process_l2book_files_for_day function
    pq.write_table(pa.Table.from_pandas(df_fix3), 'test_fix3.parquet')
    
    # Read back
    df_fix3_read = pd.read_parquet('test_fix3.parquet')
    print("Columns using schema preservation:", df_fix3_read.columns.tolist())