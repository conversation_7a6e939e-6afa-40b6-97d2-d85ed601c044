#!/usr/bin/env python
# scripts/update_features_with_atr.py

"""
Update existing feature files with 1-hour ATR.

This script updates existing 1-second feature files with 1-hour ATR columns
without reprocessing the raw data.
"""

import os
import sys
import logging
import argparse
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import numpy as np
import concurrent.futures
import time

# Add project root to path for imports
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("update_features_with_atr")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Update existing feature files with 1-hour ATR")

    parser.add_argument(
        "--start-date",
        type=str,
        default="2025-03-01",
        help="Start date in YYYY-MM-DD format (default: 2025-03-01)"
    )

    parser.add_argument(
        "--end-date",
        type=str,
        default="2025-03-22",
        help="End date in YYYY-MM-DD format (default: 2025-03-22)"
    )

    parser.add_argument(
        "--force",
        action="store_true",
        help="Force overwrite of existing output files"
    )

    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Process dates in parallel"
    )

    return parser.parse_args()

def calculate_hourly_atr(df, date_str='2025-03-03', length=14):
    """
    Calculate ATR from 1-hour OHLC data.

    Args:
        df: DataFrame with 1-second data
        date_str: Date string in YYYY-MM-DD format (default: 2025-03-03)
        length: ATR period (default: 14)

    Returns:
        DataFrame with ATR values
    """
    start_time = time.time()
    logger.info("Calculating 1-hour ATR")

    # Make a copy to avoid modifying the original
    df = df.copy()

    # Create a new timestamp column with the correct date
    base_date = pd.to_datetime(date_str)

    # Create a new timestamp column with the correct date
    if 'timestamp' in df.columns:
        # Check if timestamp is already a datetime
        if pd.api.types.is_datetime64_any_dtype(df['timestamp']):
            # Check if the date is correct (not 1970)
            if df['timestamp'].dt.year.iloc[0] < 2000:
                # Create a new timestamp column with the hour from the original timestamp
                # and the date from the date_str parameter
                hour_of_day = df['timestamp'].dt.hour
                minute_of_hour = df['timestamp'].dt.minute
                second_of_minute = df['timestamp'].dt.second

                # Create new timestamps
                df['timestamp'] = base_date + pd.to_timedelta(hour_of_day, unit='h') + \
                                  pd.to_timedelta(minute_of_hour, unit='m') + \
                                  pd.to_timedelta(second_of_minute, unit='s')
        else:
            # If timestamp is not a datetime, convert it
            if pd.api.types.is_integer_dtype(df['timestamp']):
                # Assume it's seconds within the day
                seconds_of_day = df['timestamp'] % (24 * 60 * 60)
                df['timestamp'] = base_date + pd.to_timedelta(seconds_of_day, unit='s')
            else:
                # Try to parse as string
                df['timestamp'] = pd.to_datetime(df['timestamp'])
    elif 'ts' in df.columns:
        # Use ts column instead
        if pd.api.types.is_integer_dtype(df['ts']):
            # Assume it's seconds within the day
            seconds_of_day = df['ts'] % (24 * 60 * 60)
            df['timestamp'] = base_date + pd.to_timedelta(seconds_of_day, unit='s')
        else:
            # Try to parse as string
            df['timestamp'] = pd.to_datetime(df['ts'])
    else:
        logger.error("No timestamp column found")
        return df

    # Set timestamp as index
    df = df.set_index('timestamp')

    # Resample to 1-hour and calculate OHLC
    ohlc_1h = df['mid_price'].resample('1h').ohlc()

    # Calculate True Range
    tr1 = ohlc_1h['high'] - ohlc_1h['low']
    tr2 = (ohlc_1h['high'] - ohlc_1h['close'].shift()).abs()
    tr3 = (ohlc_1h['low'] - ohlc_1h['close'].shift()).abs()

    # Combine the three TR components
    tr = pd.DataFrame({'tr1': tr1, 'tr2': tr2, 'tr3': tr3}).max(axis=1)

    # Calculate ATR with specified period
    ohlc_1h['atr_14'] = tr.rolling(window=length, min_periods=length).mean()

    # Merge ATR to 1-second data
    df_merged = df.merge(
        ohlc_1h['atr_14'],
        left_index=True,
        right_index=True,
        how='left'
    )

    # Forward fill ATR values
    df_merged['atr_14_sec'] = df_merged['atr_14'].ffill()

    # Drop the original ATR column from the merge
    df_merged = df_merged.drop(columns=['atr_14'])

    # Add ATR percent for the hourly ATR
    df_merged['atr_percent_sec'] = df_merged['atr_14_sec'] / df_merged['mid_price']

    # Reset index to get timestamp as a column
    df_merged = df_merged.reset_index()

    logger.info(f"Calculated ATR in {time.time() - start_time:.2f} seconds")
    return df_merged

def process_hour(date_str, hour, config, force=False):
    """
    Process a single hour.

    Args:
        date_str: Date string in YYYY-MM-DD format
        hour: Hour (0-23)
        config: Configuration object
        force: Whether to overwrite existing output files

    Returns:
        Tuple of (date_str, hour, success)
    """
    logger.info(f"Processing data for {date_str} hour {hour:02d}")

    date = datetime.strptime(date_str, "%Y-%m-%d")
    date_pattern = date.strftime("%Y%m%d")

    # Find feature file for the hour
    feature_dir = Path(config.data_paths.feature_1s_dir) / date_str
    feature_file = feature_dir / f"features_{hour:02d}.parquet"

    if not feature_file.exists():
        logger.error(f"Feature file {feature_file} does not exist")
        return date_str, hour, False

    # Check if file already has ATR columns
    df = pd.read_parquet(feature_file)
    if 'atr_14_sec' in df.columns and 'atr_percent_sec' in df.columns and not force:
        logger.info(f"Feature file {feature_file} already has ATR columns")
        return date_str, hour, True

    # Calculate 1-hour ATR
    df = calculate_hourly_atr(df, date_str=date_str)

    # Save updated file
    df.to_parquet(feature_file, index=False)
    logger.info(f"Saved updated feature file {feature_file}")

    return date_str, hour, True

def process_date(date_str, config, force=False):
    """
    Process a single date.

    Args:
        date_str: Date string in YYYY-MM-DD format
        config: Configuration object
        force: Whether to overwrite existing output files

    Returns:
        Tuple of (date_str, success_count, total_count)
    """
    logger.info(f"Processing data for {date_str}")

    # Find feature files for the date
    feature_dir = Path(config.data_paths.feature_1s_dir) / date_str

    if not feature_dir.exists():
        logger.error(f"Feature directory {feature_dir} does not exist")
        return date_str, 0, 0

    # Process each hour
    success_count = 0
    total_count = 0

    for hour in range(24):
        feature_file = feature_dir / f"features_{hour:02d}.parquet"
        if feature_file.exists():
            _, _, success = process_hour(date_str, hour, config, force)
            total_count += 1
            if success:
                success_count += 1

    logger.info(f"Processed {success_count}/{total_count} files successfully for {date_str}")
    return date_str, success_count, total_count

def main():
    """Main entry point."""
    args = parse_args()

    # Load config
    config = load_config()

    # Parse dates
    start_date = datetime.strptime(args.start_date, "%Y-%m-%d")
    end_date = datetime.strptime(args.end_date, "%Y-%m-%d")

    # Generate list of dates to process
    dates = []
    current_date = start_date
    while current_date <= end_date:
        dates.append(current_date.strftime("%Y-%m-%d"))
        current_date += timedelta(days=1)

    logger.info(f"Processing {len(dates)} dates from {args.start_date} to {args.end_date}")

    start_time = time.time()

    if args.parallel:
        # Process dates in parallel
        with concurrent.futures.ProcessPoolExecutor() as executor:
            futures = [executor.submit(process_date, date_str, config, args.force) for date_str in dates]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
    else:
        # Process dates sequentially
        results = [process_date(date_str, config, args.force) for date_str in dates]

    # Summarize results
    total_success = sum(success for _, success, _ in results)
    total_files = sum(total for _, _, total in results)

    end_time = time.time()
    logger.info(f"Processed {total_success}/{total_files} files successfully in {end_time - start_time:.2f} seconds")

    return 0 if total_success == total_files else 1

if __name__ == "__main__":
    sys.exit(main())
