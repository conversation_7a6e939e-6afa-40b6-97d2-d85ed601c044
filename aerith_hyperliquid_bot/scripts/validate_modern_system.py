#!/usr/bin/env python3
"""
Validate Modern System Architecture
===================================

Ensure clean separation between legacy and modern systems.
Add runtime checks to prevent cross-contamination.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
import importlib
import inspect


def validate_system_boundaries():
    """Validate that modern and legacy systems are properly separated."""
    
    print("=== Modern System Architecture Validation ===\n")
    
    errors = []
    warnings = []
    
    # 1. Check modern modules don't import legacy
    print("1. Checking modern module imports...")
    modern_modules = [
        'hyperliquid_bot.modern.backtester_engine',
        'hyperliquid_bot.modern.tf_v3_modern',
        'hyperliquid_bot.modern.hourly_evaluator',
        'hyperliquid_bot.modern.signal_engine',
        'hyperliquid_bot.modern.data_loader',
    ]
    
    legacy_modules = [
        'hyperliquid_bot.strategies.tf_v3',
        'hyperliquid_bot.strategies.tf_v2',
        'hyperliquid_bot.backtester.backtester',
        'hyperliquid_bot.data.handler',
    ]
    
    for modern_module_name in modern_modules:
        try:
            module = importlib.import_module(modern_module_name)
            
            # Check imports
            for name, obj in inspect.getmembers(module):
                if inspect.ismodule(obj):
                    module_path = getattr(obj, '__name__', '')
                    
                    # Check if it's importing legacy modules
                    for legacy in legacy_modules:
                        if legacy in module_path:
                            errors.append(
                                f"❌ {modern_module_name} imports legacy module: {legacy}"
                            )
        except ImportError as e:
            warnings.append(f"⚠️  Could not import {modern_module_name}: {e}")
    
    # 2. Check configuration usage
    print("\n2. Checking configuration usage...")
    from hyperliquid_bot.config.settings import load_config
    
    # Load modern config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Check for dangerous config combinations
    if config.strategies.use_tf_v3 and config.system.get('system_type') == 'modern':
        errors.append(
            "❌ Configuration conflict: use_tf_v3=True loads LEGACY TF-v3, "
            "but system_type='modern'. Modern system should use ModernBacktestEngine directly!"
        )
    
    if config.regime.detector_type == 'granular_microstructure':
        errors.append(
            "❌ Modern system using legacy detector 'granular_microstructure'!"
        )
    
    # 3. Check data paths
    print("\n3. Checking data paths...")
    if 'raw2' in str(config.data_paths.l2_data_root):
        warnings.append(
            "⚠️  Modern system referencing legacy raw2 data path"
        )
    
    # 4. Validate modern strategy
    print("\n4. Validating modern strategy...")
    try:
        from hyperliquid_bot.modern.tf_v3_modern import ModernTFV3Strategy
        
        # Check it has the right methods
        required_methods = ['evaluate_entry', 'check_exit', '_is_regime_stable']
        for method in required_methods:
            if not hasattr(ModernTFV3Strategy, method):
                errors.append(
                    f"❌ ModernTFV3Strategy missing required method: {method}"
                )
    except ImportError as e:
        errors.append(f"❌ Cannot import ModernTFV3Strategy: {e}")
    
    # 5. Runtime validation
    print("\n5. Testing runtime isolation...")
    
    # Summary
    print("\n" + "="*60)
    print("VALIDATION SUMMARY")
    print("="*60)
    
    if errors:
        print(f"\n❌ ERRORS ({len(errors)}):")
        for error in errors:
            print(f"  {error}")
    
    if warnings:
        print(f"\n⚠️  WARNINGS ({len(warnings)}):")
        for warning in warnings:
            print(f"  {warning}")
    
    if not errors and not warnings:
        print("\n✅ All validations passed! Systems are properly separated.")
    
    print("\n" + "="*60)
    print("ARCHITECTURAL GUIDELINES")
    print("="*60)
    print("""
1. NEVER use legacy backtester for modern system:
   ❌ from hyperliquid_bot.backtester.backtester import Backtester
   ✅ from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine

2. NEVER set use_tf_v3=True for modern system:
   ❌ strategies.use_tf_v3: true  # This loads LEGACY TF-v3!
   ✅ Use ModernBacktestEngine which loads ModernTFV3Strategy directly

3. ALWAYS use proper data paths:
   Legacy: raw2/ → granular_microstructure → TFV2Strategy
   Modern: features_1s/ → continuous_modern_v2 → ModernTFV3Strategy

4. ALWAYS run modern backtest with:
   ✅ python scripts/run_modern_backtest.py
   ❌ python -m hyperliquid_bot.backtester.run_backtest --override modern_system.yaml
""")
    
    return len(errors) == 0


def create_system_validator():
    """Create a runtime validator to prevent cross-contamination."""
    
    validator_code = '''
"""
System Boundary Validator
========================

Runtime checks to prevent legacy/modern cross-contamination.
"""

import functools
import logging

logger = logging.getLogger(__name__)


def validate_modern_context(func):
    """Decorator to ensure function runs in modern context only."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Check if we're in modern context
        import inspect
        frame = inspect.currentframe()
        
        # Walk up the stack
        while frame:
            module = inspect.getmodule(frame)
            if module:
                module_name = module.__name__
                
                # Check for legacy contamination
                if 'hyperliquid_bot.strategies.tf_v3' in module_name:
                    raise RuntimeError(
                        f"Modern function {func.__name__} called from legacy context: {module_name}"
                    )
                elif 'hyperliquid_bot.backtester.backtester' in module_name:
                    raise RuntimeError(
                        f"Modern function {func.__name__} called from legacy backtester!"
                    )
            
            frame = frame.f_back
        
        return func(*args, **kwargs)
    
    return wrapper


def validate_legacy_context(func):
    """Decorator to ensure function runs in legacy context only."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Check if we're in legacy context
        import inspect
        frame = inspect.currentframe()
        
        # Walk up the stack
        while frame:
            module = inspect.getmodule(frame)
            if module:
                module_name = module.__name__
                
                # Check for modern contamination
                if 'hyperliquid_bot.modern' in module_name:
                    raise RuntimeError(
                        f"Legacy function {func.__name__} called from modern context: {module_name}"
                    )
            
            frame = frame.f_back
        
        return func(*args, **kwargs)
    
    return wrapper


class SystemBoundaryChecker:
    """Runtime system boundary checker."""
    
    @staticmethod
    def assert_modern_system(config):
        """Assert we're in modern system context."""
        if hasattr(config, 'system') and config.system.get('system_type') != 'modern':
            raise ValueError(
                "Modern component initialized with non-modern config! "
                f"system_type={config.system.get('system_type')}"
            )
        
        if hasattr(config, 'strategies') and config.strategies.use_tf_v3:
            logger.warning(
                "⚠️  use_tf_v3=True in modern system config. "
                "This flag is for LEGACY system only!"
            )
    
    @staticmethod
    def assert_legacy_system(config):
        """Assert we're in legacy system context."""
        if hasattr(config, 'system') and config.system.get('system_type') == 'modern':
            raise ValueError(
                "Legacy component initialized with modern config!"
            )
'''
    
    # Save validator
    validator_path = 'hyperliquid_bot/core/system_validator.py'
    print(f"\nCreating system validator at: {validator_path}")
    
    os.makedirs(os.path.dirname(validator_path), exist_ok=True)
    with open(validator_path, 'w') as f:
        f.write(validator_code)
    
    print("✅ System validator created")


if __name__ == "__main__":
    # Run validation
    valid = validate_system_boundaries()
    
    if valid:
        print("\n✅ System boundaries are properly maintained!")
    else:
        print("\n❌ System boundary violations detected!")
        print("Run the fixes above to ensure proper separation.")
    
    # Create validator
    print("\nCreating runtime validator...")
    create_system_validator()