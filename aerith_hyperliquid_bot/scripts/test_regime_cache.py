#!/usr/bin/env python3
"""
Test regime cache to find root cause of None returns
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
import pandas as pd
from pathlib import Path

from hyperliquid_bot.modern.regime_cache import RegimeCache

# Test the regime cache
cache = RegimeCache()

# Test dates
test_dates = [
    datetime(2024, 4, 1, 0, 0, 0),
    datetime(2024, 4, 2, 0, 0, 0),
    datetime(2024, 4, 3, 0, 0, 0),
]

print("=== REGIME CACHE TEST ===")
print(f"Cache directory: {cache.cache_dir}")

# Check cache info
info = cache.get_cache_info()
print(f"\nCache info: {info}")

# Test loading 2024
print(f"\nLoading year 2024...")
success = cache.load_year(2024)
print(f"Load success: {success}")

if success:
    # Check the actual data
    df = cache.regime_data[2024]
    print(f"\nData shape: {df.shape}")
    print(f"Data range: {df.index.min()} to {df.index.max()}")
    print(f"Columns: {list(df.columns)}")
    
    # Show first few rows
    print(f"\nFirst 5 rows:")
    print(df.head())
    
    # Test queries
    print(f"\n=== TESTING QUERIES ===")
    for test_date in test_dates:
        result = cache.get_regime_at_time(test_date)
        print(f"\nQuery: {test_date}")
        print(f"Result: {result}")
        
        if not result:
            # Debug why it's None
            if 2024 in cache.regime_data:
                df = cache.regime_data[2024]
                # Check if date is in range
                if test_date < df.index.min():
                    print(f"  -> Date is before data start: {df.index.min()}")
                elif test_date > df.index.max():
                    print(f"  -> Date is after data end: {df.index.max()}")
                else:
                    # Find nearest
                    mask = df.index <= test_date
                    if mask.any():
                        nearest = df.index[mask][-1]
                        print(f"  -> Nearest prior date: {nearest}")
                        print(f"  -> Data at nearest: {df.loc[nearest]}")
                    else:
                        print(f"  -> No data before this date!")

# Also check the parquet file directly
print(f"\n=== CHECKING PARQUET FILE DIRECTLY ===")
parquet_file = Path("data/precomputed_regimes/regimes_2024.parquet")
if parquet_file.exists():
    df = pd.read_parquet(parquet_file)
    print(f"File exists: {parquet_file}")
    print(f"Shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    if 'timestamp' in df.columns:
        print(f"Timestamp range: {df['timestamp'].min()} to {df['timestamp'].max()}")
    else:
        print(f"Index range: {df.index.min()} to {df.index.max()}")
    print(f"\nFirst few rows:")
    print(df.head())