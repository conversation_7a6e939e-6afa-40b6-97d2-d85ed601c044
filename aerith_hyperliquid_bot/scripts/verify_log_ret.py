#!/usr/bin/env python3
"""
Simple script to verify that log_ret and realised_vol columns are present in the OHLCV data.
"""

import sys
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
script_dir = Path(__file__).resolve().parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

# Import necessary components
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.data.handler import HistoricalDataHandler
import logging

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)-5s] %(name)-30s: %(message)s')
logger = logging.getLogger(__name__)

def main():
    try:
        # Load config
        config_path = project_root / 'config.yaml'
        if not config_path.exists():
            raise FileNotFoundError(f"Config file not found at {config_path}")
        
        logger.info(f"Loading config from {config_path}")
        config = load_config(str(config_path))
        
        # Create data handler
        logger.info("Initializing HistoricalDataHandler")
        data_handler = HistoricalDataHandler(config, logger)
        
        # Define test period (same as in config.yaml)
        start_date = datetime.strptime("2024-01-01", "%Y-%m-%d")
        end_date = datetime.strptime("2024-01-03", "%Y-%m-%d") + timedelta(days=1)  # Add 1 day to include end date
        
        # Load data
        logger.info(f"Loading historical data from {start_date} to {end_date}")
        data_handler.load_historical_data(start_date=start_date, end_date=end_date)
        
        # Retrieve the loaded data using get_ohlcv_data()
        logger.info("Retrieving loaded data using get_ohlcv_data()")
        ohlcv_data = data_handler.get_ohlcv_data()
        
        # Debug output to see what's returned
        logger.info(f"Type of returned data: {type(ohlcv_data)}")
        
        if ohlcv_data is None:
            logger.error("No data loaded - returned None!")
            return
        
        if isinstance(ohlcv_data, pd.DataFrame) and ohlcv_data.empty:
            logger.error("Empty DataFrame returned!")
            return
            
        # If we got here, we have data
        if not isinstance(ohlcv_data, pd.DataFrame):
            logger.error(f"Expected DataFrame but got {type(ohlcv_data)}")
            return
            
        # Check for required columns
        logger.info(f"Data loaded successfully. Shape: {ohlcv_data.shape}")
        logger.info(f"Columns: {ohlcv_data.columns.tolist()}")
        
        required_columns = ['open', 'high', 'low', 'close', 'log_ret', 'realised_vol']  # Removed 'volume' as it's optional
        missing_columns = [col for col in required_columns if col not in ohlcv_data.columns]
        
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
        else:
            logger.info("All required columns are present!")
            
            # Show some sample data
            logger.info("\nSample data (first 5 rows):")
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', 120)
            logger.info(f"\n{ohlcv_data.head(5)}")
            
            # Check for NaN values in log_ret and realised_vol
            nan_log_ret = ohlcv_data['log_ret'].isna().sum()
            nan_real_vol = ohlcv_data['realised_vol'].isna().sum()
            
            logger.info(f"NaN values in log_ret: {nan_log_ret} out of {len(ohlcv_data)} rows")
            logger.info(f"NaN values in realised_vol: {nan_real_vol} out of {len(ohlcv_data)} rows")
            
            # If there are NaNs, show where they occur
            if nan_log_ret > 0:
                logger.info("\nRows with NaN log_ret:")
                logger.info(f"\n{ohlcv_data[ohlcv_data['log_ret'].isna()].head()}")
            
            if nan_real_vol > 0:
                logger.info("\nRows with NaN realised_vol:")
                logger.info(f"\n{ohlcv_data[ohlcv_data['realised_vol'].isna()].head()}")
        
    except Exception as e:
        logger.exception(f"Error: {e}")
        
if __name__ == "__main__":
    main()
