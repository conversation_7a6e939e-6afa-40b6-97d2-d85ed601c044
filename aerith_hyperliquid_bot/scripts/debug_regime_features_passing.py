#!/usr/bin/env python3
"""
Debug Regime Features Passing
=============================

Trace how regime_features are passed from hourly_evaluator to strategy.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from datetime import datetime
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.tf_v3_modern import ModernTFV3Strategy
from hyperliquid_bot.modern.hourly_evaluator import HourlyStrategyEvaluator
from hyperliquid_bot.modern.regime_state_manager import RegimeStateManager
from hyperliquid_bot.modern.continuous_detector_v2 import ModernContinuousDetectorV2

def main():
    print("=== Debugging Regime Features Passing ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Create components
    regime_manager = RegimeStateManager(
        detector=ModernContinuousDetectorV2(config),
        config=config
    )
    
    strategy = ModernTFV3Strategy(config)
    evaluator = HourlyStrategyEvaluator(
        config=config,
        regime_manager=regime_manager,
        strategy=strategy,
        mode="backtest"
    )
    
    # Create test hourly bar
    test_hourly_bar = {
        'timestamp': datetime(2024, 1, 15, 10, 0, 0),
        'open': 42500,
        'high': 42700,
        'low': 42400,
        'close': 42600,
        'volume': 1000000,
        'atr_14_sec': 220.0,
        'atr_percent_sec': 0.52
    }
    
    # Create test current signals
    test_current_signals = {
        'volume_imbalance': -0.05
    }
    
    # Create test regime features (what get_regime_features_for_strategy returns)
    test_regime_features = {
        'current_state': 'Weak_Bear_Trend',
        'current_confidence': 0.7,
        'state_duration_minutes': 30,
        'risk_suppressed': False,
        'state_changes_1h': 2,
        'avg_momentum_1h': -0.001,
        'timestamp': datetime(2024, 1, 15, 10, 0, 0)
    }
    
    # Mock the regime manager method
    original_method = regime_manager.get_regime_features_for_strategy
    def mock_get_regime_features(*args, **kwargs):
        print("Mock: Returning regime features:", test_regime_features)
        return test_regime_features
    regime_manager.get_regime_features_for_strategy = mock_get_regime_features
    
    # Now let's trace through _prepare_strategy_signals
    print("\n1. Calling _prepare_strategy_signals()...")
    
    # Create minimal OHLCV history
    ohlcv_history = pd.DataFrame([
        {
            'timestamp': datetime(2024, 1, 15, 9, 0, 0),
            'open': 42400,
            'high': 42500,
            'low': 42300,
            'close': 42450,
            'volume': 900000
        },
        test_hourly_bar
    ])
    ohlcv_history.set_index('timestamp', inplace=True)
    
    # Call _prepare_strategy_signals
    signals = evaluator._prepare_strategy_signals(
        test_hourly_bar,
        test_current_signals,
        test_regime_features,
        ohlcv_history
    )
    
    print("\n2. Signals returned from _prepare_strategy_signals:")
    for key in ['regime', 'regime_timestamp', 'risk_suppressed', 'regime_features']:
        value = signals.get(key, 'NOT FOUND')
        print(f"   - signals['{key}'] = {value}")
    
    print("\n3. Now calling strategy.evaluate_entry()...")
    
    # Instrument the strategy's evaluate_entry method
    original_evaluate = strategy.evaluate_entry
    def instrumented_evaluate(signals, regime):
        print(f"\n   Strategy received:")
        print(f"   - regime parameter: {regime}")
        print(f"   - signals['regime_features']: {signals.get('regime_features', 'NOT FOUND')}")
        
        # Extract regime_features
        regime_features = signals.get('regime_features', {})
        if not regime_features:
            print("\n   ❌ ERROR: No regime_features in signals!")
        
        return original_evaluate(signals, regime)
    
    strategy.evaluate_entry = instrumented_evaluate
    
    # Call evaluate method
    result = evaluator.evaluate(
        test_hourly_bar,
        test_current_signals,
        datetime(2024, 1, 15, 10, 0, 0),
        ohlcv_history
    )
    
    print("\n4. Result from evaluator.evaluate():", result)
    
    print("\n" + "="*50)
    print("ANALYSIS:")
    print("The bug is in _prepare_strategy_signals() at line 262-264.")
    print("It sets signals['regime'] but NOT signals['regime_features']!")
    print("The strategy expects signals['regime_features'] to exist.")

if __name__ == "__main__":
    main()