#!/usr/bin/env python3
"""
Enhanced Legacy System Profiling Script - Full 2024 Analysis
============================================================

This script runs a comprehensive performance analysis of the Legacy System
(Granular Microstructure + TF-v2) over the entire 2024 period to identify
performance bottlenecks and provide optimization recommendations.

Key Analysis Areas:
- Data loading performance and bottlenecks
- Microstructure processing time analysis
- Memory usage patterns
- Time conversion overhead
- Modern system access detection
- Configuration integrity verification

Usage:
    python scripts/enhanced_legacy_profile_2024.py
"""

import cProfile
import pstats
import io
import sys
import os
import time
import tracemalloc
import traceback
import json
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_enhanced_legacy_analysis():
    """
    Run comprehensive Legacy System analysis with detailed profiling
    """
    print("=" * 80)
    print("ENHANCED LEGACY SYSTEM PROFILING - FULL 2024 ANALYSIS")
    print("=" * 80)
    print(f"Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Analysis Period: Full Year 2024")
    print(f"Configuration: legacy_profile.yaml")
    print(f"Expected Data: ~365 days of 1-hour OHLCV + L2 features")
    print("=" * 80)
    
    # Start memory tracking
    tracemalloc.start()
    start_time = time.time()
    
    # Initialize profiler
    profiler = cProfile.Profile()
    
    try:
        print("\n🔄 Starting Legacy System backtest with profiling...")
        profiler.enable()
        
        # Import and load configuration
        from hyperliquid_bot.config.settings import load_config
        from hyperliquid_bot.backtester.backtester import Backtester
        
        # Load configuration
        config_path = project_root / "configs" / "legacy_profile.yaml"
        print(f"📁 Loading configuration: {config_path}")
        config = load_config(str(config_path))
        
        # Initialize backtester with config object
        print(f"🔧 Initializing Backtester...")
        backtester = Backtester(config)
        
        # Get date range from config
        from hyperliquid_bot.utils.date_utils import get_date_range
        start_date, end_date = get_date_range(config.backtest.period_preset)
        
        print(f"🚀 Running backtest for period: {start_date} to {end_date}")
        results = backtester.run(start_date, end_date)
        
        profiler.disable()
        
        # Get memory usage
        current_memory, peak_memory = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        total_time = time.time() - start_time
        
        print(f"\n✅ Backtest completed successfully!")
        print(f"⏱️  Total Runtime: {total_time:.2f} seconds ({total_time/60:.2f} minutes)")
        print(f"💾 Peak Memory Usage: {peak_memory / 1024 / 1024:.2f} MB")
        print(f"💾 Final Memory Usage: {current_memory / 1024 / 1024:.2f} MB")
        
        # Analyze profiling results
        analyze_profiling_results(profiler, total_time, peak_memory, results)
        
        return True
        
    except Exception as e:
        profiler.disable()
        tracemalloc.stop()
        
        print(f"\n❌ Error during backtest execution:")
        print(f"Error: {str(e)}")
        print(f"Type: {type(e).__name__}")
        print("\n📋 Full traceback:")
        traceback.print_exc()
        
        # Still try to analyze partial profiling data
        print(f"\n🔍 Attempting to analyze partial profiling data...")
        try:
            analyze_profiling_results(profiler, time.time() - start_time, 0, None)
        except Exception as analysis_error:
            print(f"❌ Could not analyze profiling data: {analysis_error}")
        
        return False

def analyze_profiling_results(profiler, total_time, peak_memory, results):
    """
    Analyze profiling results and generate comprehensive performance report
    """
    print("\n" + "=" * 80)
    print("PERFORMANCE ANALYSIS RESULTS")
    print("=" * 80)
    
    # Save raw profile data
    profile_file = project_root / "enhanced_legacy_system_profile_2024.prof"
    profiler.dump_stats(str(profile_file))
    print(f"📊 Raw profile data saved to: {profile_file}")
    
    # Create detailed analysis
    s = io.StringIO()
    ps = pstats.Stats(profiler, stream=s)
    ps.sort_stats('cumulative')
    
    # Get top functions by cumulative time
    ps.print_stats(50)  # Top 50 functions
    profile_output = s.getvalue()
    
    # Analyze specific bottlenecks
    bottlenecks = identify_performance_bottlenecks(ps)
    
    # Generate comprehensive report
    report = generate_performance_report(
        total_time, peak_memory, bottlenecks, profile_output, results
    )
    
    # Save detailed report
    report_file = project_root / "enhanced_legacy_profile_report_2024.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"📋 Detailed report saved to: {report_file}")
    
    # Print summary
    print_performance_summary(report)

def identify_performance_bottlenecks(pstats_obj):
    """
    Identify specific performance bottlenecks in the Legacy System
    """
    bottlenecks = {
        'data_loading': [],
        'microstructure_processing': [],
        'time_conversion': [],
        'parquet_operations': [],
        'modern_system_access': [],
        'memory_intensive': []
    }
    
    # Get function statistics
    stats = pstats_obj.stats
    
    for func_key, (cc, nc, tt, ct, callers) in stats.items():
        filename, line_num, func_name = func_key
        
        # Categorize bottlenecks
        if any(keyword in filename.lower() for keyword in ['parquet', 'pandas', 'pyarrow']):
            bottlenecks['parquet_operations'].append({
                'function': func_name,
                'file': filename,
                'cumulative_time': ct,
                'total_time': tt,
                'calls': nc
            })
        
        if any(keyword in func_name.lower() for keyword in ['load', 'read', 'fetch']):
            bottlenecks['data_loading'].append({
                'function': func_name,
                'file': filename,
                'cumulative_time': ct,
                'total_time': tt,
                'calls': nc
            })
        
        if any(keyword in filename.lower() for keyword in ['microstructure', 'gms', 'obi']):
            bottlenecks['microstructure_processing'].append({
                'function': func_name,
                'file': filename,
                'cumulative_time': ct,
                'total_time': tt,
                'calls': nc
            })
        
        if any(keyword in func_name.lower() for keyword in ['datetime', 'timestamp', 'tz']):
            bottlenecks['time_conversion'].append({
                'function': func_name,
                'file': filename,
                'cumulative_time': ct,
                'total_time': tt,
                'calls': nc
            })
        
        # Check for unexpected modern system access
        if any(keyword in filename.lower() for keyword in ['tf_v3', 'continuous', 'scheduler']):
            bottlenecks['modern_system_access'].append({
                'function': func_name,
                'file': filename,
                'cumulative_time': ct,
                'total_time': tt,
                'calls': nc,
                'warning': 'Unexpected modern system component access detected'
            })
    
    # Sort each category by cumulative time
    for category in bottlenecks:
        bottlenecks[category].sort(key=lambda x: x['cumulative_time'], reverse=True)
        bottlenecks[category] = bottlenecks[category][:10]  # Top 10 per category
    
    return bottlenecks

def generate_performance_report(total_time, peak_memory, bottlenecks, profile_output, results):
    """
    Generate comprehensive performance analysis report
    """
    report = {
        'analysis_metadata': {
            'timestamp': datetime.now().isoformat(),
            'analysis_period': '2024 Full Year',
            'configuration': 'legacy_profile.yaml',
            'system_type': 'Legacy System (Granular Microstructure + TF-v2)'
        },
        'performance_metrics': {
            'total_runtime_seconds': total_time,
            'total_runtime_minutes': total_time / 60,
            'peak_memory_mb': peak_memory / 1024 / 1024 if peak_memory else 0,
            'estimated_days_processed': estimate_days_processed(total_time),
            'avg_time_per_day_seconds': total_time / 365 if total_time > 0 else 0
        },
        'bottleneck_analysis': bottlenecks,
        'performance_breakdown': analyze_time_breakdown(bottlenecks, total_time),
        'optimization_recommendations': generate_optimization_recommendations(bottlenecks, total_time),
        'configuration_integrity': check_configuration_integrity(bottlenecks),
        'backtest_results_summary': summarize_backtest_results(results) if results else None,
        'raw_profile_summary': profile_output[:2000] + "..." if len(profile_output) > 2000 else profile_output
    }
    
    return report

def estimate_days_processed(total_time):
    """
    Estimate how many days of data were processed based on runtime
    """
    # Rough estimate: Legacy system processes ~1 day per 10-30 seconds
    # depending on data complexity and system performance
    if total_time < 60:  # Less than 1 minute
        return f"~{total_time/10:.1f} days (estimated)"
    elif total_time < 3600:  # Less than 1 hour
        return f"~{total_time/20:.1f} days (estimated)"
    else:  # More than 1 hour
        return f"~{total_time/30:.1f} days (estimated)"

def analyze_time_breakdown(bottlenecks, total_time):
    """
    Analyze percentage breakdown of time spent in different components
    """
    breakdown = {}
    
    for category, functions in bottlenecks.items():
        category_time = sum(func['cumulative_time'] for func in functions)
        breakdown[category] = {
            'total_time_seconds': category_time,
            'percentage_of_total': (category_time / total_time * 100) if total_time > 0 else 0,
            'top_function': functions[0]['function'] if functions else None
        }
    
    return breakdown

def generate_optimization_recommendations(bottlenecks, total_time):
    """
    Generate specific optimization recommendations based on bottleneck analysis
    """
    recommendations = []
    
    # Data loading optimizations
    if bottlenecks['data_loading']:
        data_time = sum(func['cumulative_time'] for func in bottlenecks['data_loading'])
        if data_time / total_time > 0.3:  # More than 30% of time
            recommendations.append({
                'category': 'Data Loading',
                'priority': 'High',
                'issue': f'Data loading consumes {data_time/total_time*100:.1f}% of total runtime',
                'recommendations': [
                    'Consider implementing data caching mechanisms',
                    'Optimize parquet file reading with column selection',
                    'Implement parallel data loading for multiple days',
                    'Pre-process and cache frequently accessed data'
                ]
            })
    
    # Microstructure processing optimizations
    if bottlenecks['microstructure_processing']:
        micro_time = sum(func['cumulative_time'] for func in bottlenecks['microstructure_processing'])
        if micro_time / total_time > 0.2:  # More than 20% of time
            recommendations.append({
                'category': 'Microstructure Processing',
                'priority': 'Medium',
                'issue': f'Microstructure calculations consume {micro_time/total_time*100:.1f}% of total runtime',
                'recommendations': [
                    'Optimize GMS threshold calculations',
                    'Consider vectorized operations for OBI calculations',
                    'Cache intermediate microstructure results',
                    'Review depth level requirements (currently using 5 levels)'
                ]
            })
    
    # Modern system access warnings
    if bottlenecks['modern_system_access']:
        recommendations.append({
            'category': 'Configuration Issue',
            'priority': 'Critical',
            'issue': 'Unexpected modern system component access detected',
            'recommendations': [
                'Review configuration to ensure complete Legacy System isolation',
                'Check for TF-v3 or continuous GMS detector access',
                'Verify ETL scheduler is properly disabled',
                'Audit code paths for modern system dependencies'
            ]
        })
    
    # Memory optimization
    recommendations.append({
        'category': 'Memory Optimization',
        'priority': 'Low',
        'issue': 'General memory usage optimization',
        'recommendations': [
            'Monitor memory usage patterns during long backtests',
            'Consider implementing data chunking for very large datasets',
            'Review pandas DataFrame memory usage',
            'Implement garbage collection at strategic points'
        ]
    })
    
    return recommendations

def check_configuration_integrity(bottlenecks):
    """
    Check if configuration is properly isolating Legacy System
    """
    integrity_check = {
        'status': 'PASS',
        'issues': [],
        'warnings': []
    }
    
    # Check for modern system access
    if bottlenecks['modern_system_access']:
        integrity_check['status'] = 'FAIL'
        integrity_check['issues'].append(
            'Modern system components accessed during Legacy System run'
        )
        for access in bottlenecks['modern_system_access']:
            integrity_check['issues'].append(
                f"Unexpected access: {access['function']} in {access['file']}"
            )
    
    # Check for excessive scheduler activity (should be disabled)
    scheduler_functions = [
        func for func in bottlenecks.get('data_loading', [])
        if 'scheduler' in func['file'].lower() or 'etl' in func['function'].lower()
    ]
    if scheduler_functions:
        integrity_check['status'] = 'WARNING'
        integrity_check['warnings'].append(
            'Potential ETL/Scheduler activity detected (should be disabled in Legacy System)'
        )
    
    return integrity_check

def summarize_backtest_results(results):
    """
    Summarize backtest results if available
    """
    if not results:
        return None
    
    try:
        summary = {
            'total_trades': len(results.get('trades', [])) if results.get('trades') else 0,
            'final_balance': results.get('final_balance', 'Unknown'),
            'total_pnl': results.get('total_pnl', 'Unknown'),
            'success': True
        }
        return summary
    except Exception as e:
        return {
            'error': f'Could not summarize results: {str(e)}',
            'success': False
        }

def print_performance_summary(report):
    """
    Print a formatted summary of the performance analysis
    """
    print("\n" + "=" * 80)
    print("PERFORMANCE SUMMARY")
    print("=" * 80)
    
    metrics = report['performance_metrics']
    print(f"⏱️  Total Runtime: {metrics['total_runtime_minutes']:.2f} minutes")
    print(f"💾 Peak Memory: {metrics['peak_memory_mb']:.2f} MB")
    print(f"📊 Estimated Data: {metrics['estimated_days_processed']}")
    print(f"⚡ Avg Time/Day: {metrics['avg_time_per_day_seconds']:.2f} seconds")
    
    print(f"\n📈 TIME BREAKDOWN:")
    breakdown = report['performance_breakdown']
    for category, data in breakdown.items():
        if data['percentage_of_total'] > 1:  # Only show categories > 1%
            print(f"  {category.replace('_', ' ').title()}: {data['percentage_of_total']:.1f}%")
    
    print(f"\n🔧 OPTIMIZATION RECOMMENDATIONS:")
    recommendations = report['optimization_recommendations']
    for i, rec in enumerate(recommendations[:3], 1):  # Top 3 recommendations
        print(f"  {i}. [{rec['priority']}] {rec['category']}: {rec['issue']}")
    
    integrity = report['configuration_integrity']
    print(f"\n⚙️  CONFIGURATION INTEGRITY: {integrity['status']}")
    if integrity['issues']:
        for issue in integrity['issues']:
            print(f"  ❌ {issue}")
    if integrity['warnings']:
        for warning in integrity['warnings']:
            print(f"  ⚠️  {warning}")
    
    if report['backtest_results_summary']:
        results = report['backtest_results_summary']
        if results['success']:
            print(f"\n📊 BACKTEST RESULTS:")
            print(f"  Total Trades: {results['total_trades']}")
            print(f"  Final Balance: {results['final_balance']}")
            print(f"  Total PnL: {results['total_pnl']}")

if __name__ == "__main__":
    print("Enhanced Legacy System Profiling - Full 2024 Analysis")
    print("This analysis will run the Legacy System over the entire 2024 period")
    print("and provide comprehensive performance metrics and optimization recommendations.")
    
    success = run_enhanced_legacy_analysis()
    
    if success:
        print(f"\n✅ Analysis completed successfully!")
        print(f"📁 Check the following files for detailed results:")
        print(f"   - enhanced_legacy_system_profile_2024.prof (raw profiling data)")
        print(f"   - enhanced_legacy_profile_report_2024.json (comprehensive analysis)")
    else:
        print(f"\n❌ Analysis encountered errors. Check the output above for details.")
    
    print(f"\n🔍 Use the following command to explore the raw profile data:")
    print(f"   python -c \"import pstats; pstats.Stats('enhanced_legacy_system_profile_2024.prof').sort_stats('cumulative').print_stats(20)\"") 