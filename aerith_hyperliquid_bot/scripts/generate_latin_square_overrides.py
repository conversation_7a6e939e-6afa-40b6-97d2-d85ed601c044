#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to generate the correct override files for the Latin square design.
"""
from pathlib import Path

# Define the parameter grid
PARAM_GRID = {
    "obi_smoothing_window": [8, 14],  # Low, High
    "gms_obi_strong_confirm_thresh": [0.15, 0.22],  # Low, High
    "gms_spread_std_high_thresh": [0.000026, 0.000044],  # Low, High
    "gms_confirmation_bars": [1, 2]  # Low, High
}

# Template for override config
OVERRIDE_TEMPLATE = """microstructure:
  obi_smoothing_window: {obi_win}
  gms_obi_strong_confirm_thresh: {obi_thr}
  gms_obi_weak_confirm_thresh: {obi_thr}

regime:
  gms_spread_std_high_thresh: {spd_thr}
  gms_confirmation_bars: {conf_bars}
"""

# Define the project root
PROJECT_ROOT = Path(__file__).parent.parent.resolve()
OVERRIDE_DIR = PROJECT_ROOT / "configs" / "overrides" / "grid_search"

# Ensure the directory exists
OVERRIDE_DIR.mkdir(exist_ok=True, parents=True)

# Generate all combinations for the Latin square design
run_id = 1
for obi_thr in PARAM_GRID["gms_obi_strong_confirm_thresh"]:
    for obi_win in PARAM_GRID["obi_smoothing_window"]:
        for spd_thr in PARAM_GRID["gms_spread_std_high_thresh"]:
            for conf_bars in PARAM_GRID["gms_confirmation_bars"]:
                # Format the override content
                override_content = OVERRIDE_TEMPLATE.format(
                    obi_win=obi_win,
                    obi_thr=obi_thr,
                    spd_thr=spd_thr,
                    conf_bars=conf_bars
                )
                
                # Create the override file
                override_path = OVERRIDE_DIR / f"run_{run_id:02d}.yaml"
                with open(override_path, 'w') as f:
                    f.write(override_content)
                
                print(f"Generated {override_path}")
                print(f"Run {run_id:02d}: OBI_WIN={obi_win}, OBI_THR={obi_thr}, SPD_THR={spd_thr}, CONF_BARS={conf_bars}")
                
                run_id += 1

print(f"Generated {run_id-1} override files in {OVERRIDE_DIR}")
