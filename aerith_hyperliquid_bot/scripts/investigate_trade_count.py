#!/usr/bin/env python3
"""
Investigate trade count discrepancy between original (184) and unified (175) detectors.
"""

import sys
import os
import logging
from pathlib import Path
import pandas as pd
import numpy as np

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from hyperliquid_bot.config.settings import Config, load_config
from hyperliquid_bot.core.detector_factory import get_regime_detector
from hyperliquid_bot.data.handler import DataHandler
from hyperliquid_bot.signals.calculator import SignalCalculator


def analyze_detector_differences(config_path: str):
    """Analyze differences between detectors."""
    
    # Load configuration
    config = load_config(config_path)
    
    # Force legacy detector
    config.regime.detector_type = 'granular_microstructure'
    
    # Initialize components
    data_handler = DataHandler(config)
    signal_calc = SignalCalculator(config, data_handler)
    detector = get_regime_detector(config)
    
    print(f"Detector type: {type(detector).__name__}")
    print(f"Detector mode: {getattr(detector, 'detector_mode', 'N/A')}")
    print(f"Using UnifiedGMSDetector: {'unified_gms_detector' in str(type(detector).__module__)}")
    
    # Check thresholds
    if hasattr(detector, 'thresholds'):
        print(f"\nThresholds:")
        for key, value in detector.thresholds.items():
            print(f"  {key}: {value}")
    
    # Check other settings
    print(f"\nOther settings:")
    print(f"  OBI strong threshold: {getattr(detector, 'obi_strong_confirm_thresh', 'N/A')}")
    print(f"  OBI weak threshold: {getattr(detector, 'obi_weak_confirm_thresh', 'N/A')}")
    print(f"  Skip L2 processing: {getattr(detector, 'skip_l2_processing', 'N/A')}")
    
    # Test with sample data
    print("\nTesting regime detection with sample signals...")
    
    # Create test signals with different parameter values
    test_cases = [
        # Case 1: High volatility
        {
            'timestamp': 1640995200,
            'atr_percent': 0.95,  # Above legacy threshold (0.92)
            'ma_slope': 150.0,
            'obi_smoothed_5': 0.25,
            'spread_mean': 0.0001,
            'spread_std': 0.00005,
            'close': 50000.0
        },
        # Case 2: Low volatility
        {
            'timestamp': 1640995200,
            'atr_percent': 0.50,  # Below legacy threshold (0.55)
            'ma_slope': 30.0,
            'obi_smoothed_5': 0.08,
            'spread_mean': 0.000040,  # Below threshold
            'spread_std': 0.00003,
            'close': 50000.0
        },
        # Case 3: Edge case - just above threshold
        {
            'timestamp': 1640995200,
            'atr_percent': 0.56,  # Just above low threshold
            'ma_slope': 51.0,     # Just above weak threshold
            'obi_smoothed_5': 0.12,  # Just above weak threshold
            'spread_mean': 0.000046,  # Just above threshold
            'spread_std': 0.000040,
            'close': 50000.0
        }
    ]
    
    for i, signals in enumerate(test_cases):
        regime = detector.get_regime(signals)
        print(f"\nTest case {i+1}:")
        print(f"  ATR: {signals['atr_percent']}, MA Slope: {signals['ma_slope']}")
        print(f"  OBI: {signals['obi_smoothed_5']}, Spread Mean: {signals['spread_mean']}")
        print(f"  Detected regime: {regime}")


def compare_configurations():
    """Compare key configuration differences that might affect trade count."""
    print("\n" + "="*60)
    print("Configuration Analysis")
    print("="*60)
    
    # Key parameters that could affect trade count
    params = {
        "Volatility thresholds": {
            "Legacy high": 0.92,
            "Legacy low": 0.55,
            "Expected trades impact": "Lower thresholds = more volatile regimes = fewer trades"
        },
        "Momentum thresholds": {
            "Legacy strong": 100.0,
            "Legacy weak": 50.0,
            "Expected trades impact": "Higher thresholds = fewer strong trends = different trades"
        },
        "OBI thresholds": {
            "Strong confirm": 0.20,
            "Weak confirm": 0.11,
            "Expected trades impact": "Affects trend confirmation = trade filtering"
        },
        "Spread thresholds": {
            "Std high": 0.000050,
            "Mean low": 0.000045,
            "Expected trades impact": "Affects market condition classification"
        }
    }
    
    for category, settings in params.items():
        print(f"\n{category}:")
        for key, value in settings.items():
            print(f"  {key}: {value}")
    
    print("\n\nPossible causes for 175 vs 184 trades:")
    print("1. State mapping differences in edge cases")
    print("2. Floating point precision in threshold comparisons")
    print("3. Different handling of NaN values")
    print("4. State validation logic differences")
    print("5. ADX/Funding confirmation logic (if enabled)")


def main():
    """Run analysis."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    config_path = "configs/base.yaml"
    
    print("Investigating Trade Count Discrepancy")
    print("Original GranularMicrostructureRegimeDetector: 184 trades")
    print("UnifiedGMSDetector (legacy mode): 175 trades")
    print("Difference: -9 trades (-4.9%)")
    
    try:
        analyze_detector_differences(config_path)
        compare_configurations()
        
        print("\n\nRecommendations:")
        print("1. Run side-by-side comparison with original detector")
        print("2. Log regime states for each bar to find differences")
        print("3. Check if state validation is too strict")
        print("4. Verify all threshold comparisons use same logic (>= vs >)")
        
    except Exception as e:
        print(f"\nError during analysis: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()