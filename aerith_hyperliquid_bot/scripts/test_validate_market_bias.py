#!/usr/bin/env python3
# Test script for verifying the _validate_market_bias method functionality

import sys
import os
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_validate_market_bias")

# Add project root to path for imports
project_root = Path(os.path.abspath(os.path.dirname(__file__))).parent
sys.path.insert(0, str(project_root))

# Import necessary modules
from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.utils.state_mapping import map_gms_state, validate_3state
import yaml

def load_config():
    """Load the configuration from base.yaml"""
    config_path = project_root / "configs" / "base.yaml"
    with open(config_path, 'r') as f:
        config_dict = yaml.safe_load(f)
    return Config.parse_obj(config_dict)

class TestRiskManager:
    """Test class to simulate the RiskManager for testing the validation method"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger("TestRiskManager")
    
    def validate_market_bias(self, market_state, regime, base_leverage, final_leverage, market_risk_factor, direction_bias):
        """
        Clean implementation of the _validate_market_bias method for testing purposes
        """
        cfg = self.config
        
        # Check if market bias should be enabled
        if not hasattr(cfg.regime, 'market_bias') or not hasattr(cfg.regime.market_bias, 'enabled'):
            self.logger.warning("VALIDATION: Market bias settings not available in config!")
            return
        
        if not cfg.regime.market_bias.enabled:
            self.logger.warning("VALIDATION: Market bias is disabled but seems to be applied!")
            return
            
        # Validate state mapping consistency
        try:
            # Confirm that the state is a valid 3-state value
            if not validate_3state(market_state):
                self.logger.warning(f"VALIDATION: Invalid market state '{market_state}' provided!")
                
            # Verify consistency between regime and mapped state
            expected_market_state = map_gms_state(regime)
            if expected_market_state != market_state:
                self.logger.warning(f"VALIDATION: State mapping inconsistency detected! Regime '{regime}' should map to '{expected_market_state}' but got '{market_state}'")
        except ImportError:
            self.logger.warning("VALIDATION: State mapping utility not available - skipping mapping consistency check")
        
        # --- Validation Messages for Review ---
        self.logger.info("---------- MARKET BIAS VALIDATION ----------")
        self.logger.info(f"GMS Regime: {regime}")
        self.logger.info(f"Market State: {market_state}")
        self.logger.info(f"Base Leverage: {base_leverage:.2f}x")
        
        # Specific direction bias
        direction_enabled = hasattr(cfg.regime.market_bias, 'direction_bias_enabled') and cfg.regime.market_bias.direction_bias_enabled
        self.logger.info(f"Direction-Based Bias: {'Enabled' if direction_enabled else 'Disabled'}")
        
        if direction_enabled and direction_bias != 1.0:
            self.logger.info(f"Direction Risk Bias: {direction_bias:.2f}x")
        
        # Get market state specific risk for validation
        market_cfg = getattr(cfg.regime.market_bias, market_state.lower(), None)
        expected_risk = getattr(market_cfg, 'risk_factor', 1.0) if market_cfg else 1.0
        
        # Validate market risk factor
        risk_diff = abs(market_risk_factor - expected_risk)
        if risk_diff < 0.0001:  # Close enough to expected value
            self.logger.info(f"Market Risk Factor: {market_risk_factor:.2f}x (Correct)")
        else:
            self.logger.warning(f"Market Risk Factor: {market_risk_factor:.2f}x (INCONSISTENT - Expected {expected_risk:.2f}x)")
        
        # Leverage change validation
        leverage_change_pct = ((final_leverage / base_leverage) - 1.0) * 100
        leverage_direction = "increase" if leverage_change_pct > 0 else "decrease"
        self.logger.info(f"Leverage Change: {abs(leverage_change_pct):.1f}% {leverage_direction} from {base_leverage:.2f}x to {final_leverage:.2f}x")
        
        self.logger.info("--------- END MARKET BIAS VALIDATION ---------")

def run_validation_tests():
    """Test the validate_market_bias method with different scenarios"""
    
    logger.info("Loading configuration...")
    config = load_config()
    
    # Ensure market bias is enabled for testing
    if not hasattr(config.regime, 'market_bias') or not hasattr(config.regime.market_bias, 'enabled'):
        logger.warning("Market bias not configured, enabling for test...")
        if not hasattr(config.regime, 'market_bias'):
            config.regime.market_bias = type('MarketBias', (object,), {})()
        config.regime.market_bias.enabled = True
    
    # Initialize the test risk manager
    logger.info("Initializing test risk manager...")
    risk_manager = TestRiskManager(config)
    
    # Define test cases
    test_cases = [
        # Correct mappings
        {
            "regime": "Strong_Bull_Trend", 
            "market_state": "BULL", 
            "base_leverage": 2.0,
            "final_leverage": 2.4,
            "market_risk_factor": 1.2,
            "direction_bias": 1.0,
            "expected_consistent": True
        },
        {
            "regime": "Weak_Bear_Trend", 
            "market_state": "CHOP",
            "base_leverage": 2.0,
            "final_leverage": 1.6, 
            "market_risk_factor": 0.8,
            "direction_bias": 1.0,
            "expected_consistent": True
        },
        
        # Inconsistent mappings
        {
            "regime": "Strong_Bull_Trend", 
            "market_state": "BEAR",
            "base_leverage": 2.0,
            "final_leverage": 2.4,
            "market_risk_factor": 1.2,
            "direction_bias": 1.0,
            "expected_consistent": False
        },
        
        # Edge cases
        {
            "regime": "Unknown_Regime", 
            "market_state": "CHOP",
            "base_leverage": 2.0,
            "final_leverage": 1.0,
            "market_risk_factor": 0.5,
            "direction_bias": 1.0,
            "expected_consistent": True
        }
    ]
    
    # Run tests
    for i, case in enumerate(test_cases):
        logger.info(f"\n=== Test Case #{i+1} ===")
        logger.info(f"Testing: '{case['regime']}' -> '{case['market_state']}' (Expected consistent: {case['expected_consistent']})")
        
        # Call validation method
        risk_manager.validate_market_bias(
            market_state=case["market_state"],
            regime=case["regime"],
            base_leverage=case["base_leverage"],
            final_leverage=case["final_leverage"],
            market_risk_factor=case["market_risk_factor"],
            direction_bias=case["direction_bias"]
        )
    
    logger.info("\nAll market bias validation tests completed!")

if __name__ == "__main__":
    run_validation_tests()
