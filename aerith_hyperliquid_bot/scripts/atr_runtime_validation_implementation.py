#!/usr/bin/env python3
"""
ATR Runtime Validation Implementation
Demonstrates where and how to implement runtime validation for ATR unit consistency.
"""

import pandas as pd
import numpy as np
import logging
from typing import Optional, Dict, Any

class ATRValidator:
    """Runtime validator for ATR column consistency and value ranges."""
    
    def __init__(self, logger: Optional[logging.Logger] = None, strict_mode: bool = False):
        self.logger = logger or logging.getLogger(__name__)
        self.strict_mode = strict_mode  # If True, raise exceptions; if False, log warnings
        
    def validate_atr_columns(self, signals_df: pd.DataFrame, context: str = "unknown") -> bool:
        """
        Validate ATR column consistency and value ranges.
        
        Args:
            signals_df: DataFrame containing ATR columns
            context: Context string for logging (e.g., "signal_calculator", "gms_detector")
            
        Returns:
            bool: True if validation passes, False if issues detected
            
        Raises:
            ValueError: If strict_mode=True and critical issues detected
        """
        issues = []
        
        # Check 1: Column existence
        has_atr_percent = 'atr_percent' in signals_df.columns
        has_atr_percent_sec = 'atr_percent_sec' in signals_df.columns
        
        if not has_atr_percent and not has_atr_percent_sec:
            issues.append("No ATR percentage columns found")
        
        # Check 2: Unit conversion bug detection
        if has_atr_percent and has_atr_percent_sec:
            atr_pct = signals_df['atr_percent'].dropna()
            atr_pct_sec = signals_df['atr_percent_sec'].dropna()
            
            if len(atr_pct) > 0 and len(atr_pct_sec) > 0:
                ratio = atr_pct.mean() / atr_pct_sec.mean() if atr_pct_sec.mean() != 0 else float('inf')
                
                # Check for 100x bug
                if abs(ratio - 100) < 5:
                    issues.append(f"100x unit conversion bug detected! atr_percent/atr_percent_sec ratio: {ratio:.1f}x")
                elif abs(ratio - 1) > 0.2:  # Allow 20% tolerance for normal variation
                    issues.append(f"Unexpected ATR column ratio: {ratio:.2f}x (expected ~1.0x)")
        
        # Check 3: Value range validation
        for col in ['atr_percent', 'atr_percent_sec']:
            if col in signals_df.columns:
                values = signals_df[col].dropna()
                if len(values) > 0:
                    min_val, max_val, mean_val = values.min(), values.max(), values.mean()
                    
                    # Check for unrealistic values
                    if max_val > 0.15:  # 15% ATR is extremely high for Bitcoin
                        issues.append(f"{col} max value {max_val:.4f} exceeds realistic range (>15%)")
                    
                    if min_val < 0:  # ATR should never be negative
                        issues.append(f"{col} has negative values (min: {min_val:.4f})")
                    
                    if mean_val > 0.05:  # 5% average ATR is very high
                        issues.append(f"{col} mean value {mean_val:.4f} exceeds typical range (>5%)")
        
        # Check 4: NaN ratio validation
        for col in ['atr_percent', 'atr_percent_sec']:
            if col in signals_df.columns:
                nan_ratio = signals_df[col].isna().mean()
                if nan_ratio > 0.5:  # More than 50% NaN is concerning
                    issues.append(f"{col} has high NaN ratio: {nan_ratio:.1%}")
        
        # Report results
        if issues:
            message = f"ATR validation issues in {context}: {'; '.join(issues)}"
            if self.strict_mode:
                self.logger.error(message)
                raise ValueError(message)
            else:
                self.logger.warning(message)
            return False
        else:
            self.logger.debug(f"ATR validation passed in {context}")
            return True

# Implementation in Signal Calculator
def add_to_signal_calculator():
    """Example of how to add validation to SignalEngine.calculate_signals()"""
    
    code_example = '''
    # Add to hyperliquid_bot/signals/calculator.py in calculate_signals() method
    # After ATR calculations are complete (around line 1400)
    
    def calculate_signals(self, ohlcv_data: pd.DataFrame) -> pd.DataFrame:
        # ... existing code ...
        
        # Add ATR validation before returning
        try:
            validator = ATRValidator(logger=self.logger, strict_mode=False)
            validator.validate_atr_columns(signals_df, context="signal_calculator")
        except Exception as e:
            self.logger.error(f"ATR validation failed: {e}")
            # Continue execution but log the issue
        
        return signals_df
    '''
    
    return code_example

# Implementation in GMS Detector  
def add_to_gms_detector():
    """Example of how to add validation to GMS detector"""
    
    code_example = '''
    # Add to hyperliquid_bot/core/gms_detector.py in get_regime() method
    # At the beginning of the method (around line 520)
    
    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None) -> Union[str, Dict[str, Any]]:
        # Convert signals dict to DataFrame for validation
        signals_df = pd.DataFrame([signals])
        
        # Validate ATR columns
        try:
            validator = ATRValidator(logger=self.logger, strict_mode=False)
            validator.validate_atr_columns(signals_df, context="gms_detector")
        except Exception as e:
            self.logger.warning(f"ATR validation in GMS detector: {e}")
        
        # ... existing code ...
    '''
    
    return code_example

# Implementation in Data Handler
def add_to_data_handler():
    """Example of how to add validation to DataHandler"""
    
    code_example = '''
    # Add to hyperliquid_bot/data/handler.py in load_data() method
    # After loading feature files
    
    def load_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        # ... existing code to load data ...
        
        # Validate ATR columns in loaded data
        if not data.empty:
            try:
                validator = ATRValidator(logger=self.logger, strict_mode=False)
                validator.validate_atr_columns(data, context="data_handler")
            except Exception as e:
                self.logger.warning(f"ATR validation in data handler: {e}")
        
        return data
    '''
    
    return code_example

# Configuration-based validation
class ConfigurableATRValidator(ATRValidator):
    """ATR validator with configurable thresholds and behavior."""
    
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        # Extract validation settings from config
        validation_config = config.get('atr_validation', {})
        
        strict_mode = validation_config.get('strict_mode', False)
        super().__init__(logger=logger, strict_mode=strict_mode)
        
        # Configurable thresholds
        self.max_atr_threshold = validation_config.get('max_atr_threshold', 0.15)  # 15%
        self.max_mean_threshold = validation_config.get('max_mean_threshold', 0.05)  # 5%
        self.max_nan_ratio = validation_config.get('max_nan_ratio', 0.5)  # 50%
        self.ratio_tolerance = validation_config.get('ratio_tolerance', 0.2)  # 20%
        self.enabled = validation_config.get('enabled', True)
        
    def validate_atr_columns(self, signals_df: pd.DataFrame, context: str = "unknown") -> bool:
        """Override with configurable thresholds."""
        if not self.enabled:
            return True
            
        # Use configurable thresholds in validation logic
        # ... (similar to base class but with self.max_atr_threshold, etc.)
        return super().validate_atr_columns(signals_df, context)

# Example configuration in base.yaml
def example_config():
    """Example configuration for ATR validation."""
    
    config_example = '''
    # Add to configs/base.yaml
    atr_validation:
      enabled: true                    # Enable/disable validation
      strict_mode: false               # If true, raise exceptions; if false, log warnings
      max_atr_threshold: 0.15          # Maximum realistic ATR (15%)
      max_mean_threshold: 0.05         # Maximum realistic mean ATR (5%)
      max_nan_ratio: 0.5               # Maximum acceptable NaN ratio (50%)
      ratio_tolerance: 0.2             # Tolerance for atr_percent/atr_percent_sec ratio (20%)
      
      # Transition period settings
      transition_mode: true            # Special handling during bug fix transition
      allow_legacy_columns: true       # Allow old column names during migration
      deprecation_warnings: true      # Show warnings for deprecated usage
    '''
    
    return config_example

# Transition period handling
def transition_period_validation():
    """Special validation logic for the transition period during bug fixes."""
    
    code_example = '''
    def validate_during_transition(self, signals_df: pd.DataFrame) -> bool:
        """Special validation for transition period after bug fix."""
        
        # During transition, we expect:
        # 1. Both atr_percent and atr_percent_sec to exist
        # 2. They should have identical values (ratio ~1.0)
        # 3. Values should be in decimal format (not percentage)
        
        if 'atr_percent' in signals_df.columns and 'atr_percent_sec' in signals_df.columns:
            atr_pct = signals_df['atr_percent'].dropna()
            atr_pct_sec = signals_df['atr_percent_sec'].dropna()
            
            if len(atr_pct) > 0 and len(atr_pct_sec) > 0:
                ratio = atr_pct.mean() / atr_pct_sec.mean()
                
                # Post-fix validation: should be ~1.0, not ~100.0
                if abs(ratio - 1.0) < 0.1:
                    self.logger.info("✅ ATR bug fix validation passed - columns have consistent values")
                    return True
                elif abs(ratio - 100) < 5:
                    self.logger.error("🚨 ATR bug still present - 100x ratio detected!")
                    return False
                else:
                    self.logger.warning(f"⚠️ Unexpected ATR ratio: {ratio:.2f}x")
                    return False
        
        return True
    '''
    
    return code_example

def main():
    """Demonstrate the validation implementation."""
    
    print("🔧 ATR Runtime Validation Implementation Guide")
    print("=" * 60)
    
    print("\n📍 Implementation Locations:")
    print("1. Signal Calculator (Primary): After ATR calculations")
    print("2. GMS Detector (Secondary): At start of get_regime()")
    print("3. Data Handler (Optional): After loading feature files")
    
    print("\n⚙️ Validation Types:")
    print("1. Unit conversion bug detection (100x ratio)")
    print("2. Value range validation (0.01% to 15%)")
    print("3. Column consistency checks")
    print("4. NaN ratio validation")
    
    print("\n🎛️ Configuration Options:")
    print("- strict_mode: Exception vs Warning")
    print("- Configurable thresholds")
    print("- Enable/disable per environment")
    print("- Transition period handling")
    
    print("\n📋 Recommended Implementation Order:")
    print("1. Add basic validation to Signal Calculator")
    print("2. Add configuration support")
    print("3. Add transition period logic")
    print("4. Add to GMS Detector and Data Handler")
    print("5. Enable in production with warnings")

if __name__ == "__main__":
    main()
