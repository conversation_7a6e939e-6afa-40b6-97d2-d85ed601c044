#!/usr/bin/env python3
"""
Resample raw Level-2 snapshot daily parquet files into 1h / 4h OHLC bars.

Input files are expected to be daily Parquet files containing timestamped L2 snapshots,
including 'timestamp', 'best_bid', and 'best_ask' (or 'mid_price'). Assumes these
files are located in RAW_DIR (e.g., hyperliquid_data/raw2/) following the pattern
YYYYMMDD_raw2.parquet.

Output files are OHLC bars saved as Parquet files in subdirectories based on
timeframe (e.g., resampled_l2/1h/, resampled_l2/4h/), named YYYY-MM-DD_tf.parquet.

Usage Examples:
    # Process a specific date and timeframe
    python scripts/resample_l2_to_ohlcv.py --date 2024-01-02 --tf 1h

    # Process ALL dates found in RAW_DIR for BOTH 1h and 4h timeframes
    python scripts/resample_l2_to_ohlcv.py
"""

import argparse
import pathlib
import pyarrow.dataset as ds
import pandas as pd
import numpy as np
from typing import List, Set
import logging
from tqdm import tqdm
import re
from datetime import datetime
import sys

# Add project root to path for imports
project_root = pathlib.Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.utils.time import to_utc_naive

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define base directories using absolute paths
RAW_DIR = pathlib.Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw2")
OUT_DIR = pathlib.Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled_l2") # Base output directory


# --- Core Processing Function ---
def process_date(date_str_iso: str, timeframe: str):
    """
    Processes a single date and timeframe. Resamples L2 mid-price to OHLC.
    Args:
        date_str_iso: Date string in 'YYYY-MM-DD' format.
        timeframe: Timeframe ('1h' or '4h').
    """
    logger.debug(f"Processing date: {date_str_iso}, timeframe: {timeframe}")

    # --- Construct Output Path ---
    output_filename = f"{date_str_iso}_{timeframe}.parquet"
    output_dir = OUT_DIR / timeframe
    output_file = output_dir / output_filename
    if output_file.exists():
        logger.info(f"Skipping {output_file} (already exists)")
        return

    # --- Construct Input Path ---
    # Convert YYYY-MM-DD back to YYYYMMDD for the input filename
    try:
        date_obj = datetime.strptime(date_str_iso, '%Y-%m-%d')
        input_date_str = date_obj.strftime('%Y%m%d')
        input_filename = f"{input_date_str}_raw2.parquet"
        input_file = RAW_DIR / input_filename
    except ValueError:
        logger.error(f"Invalid date format derived: {date_str_iso}. Expected YYYY-MM-DD. Skipping.")
        return # Skip this date

    # --- Check if output already exists ---
    if output_file.exists():
        logger.debug(f"Output file already exists, skipping: {output_file}")
        return

    # --- Load Input Data ---
    if not input_file.exists():
        logger.warning(f"Input file not found, skipping: {input_file}")
        return

    try:
        logger.debug(f"Loading input file: {input_file}")
        dataset = ds.dataset(input_file, format="parquet")
        # Select only necessary columns if possible
        cols_to_load = ['timestamp']
        schema_cols = dataset.schema.names
        if 'mid_price' in schema_cols:
             cols_to_load.append('mid_price')
        elif 'best_bid' in schema_cols and 'best_ask' in schema_cols:
             cols_to_load.extend(['best_bid', 'best_ask'])
        else:
             logger.error(f"Required price columns (mid_price or best_bid/ask) missing in schema of {input_file}. Skipping.")
             return

        df = dataset.to_table(columns=cols_to_load).to_pandas()
        logger.debug(f"Loaded {len(df)} rows from {input_file}")

        if df.empty:
            logger.warning(f"Input file is empty after loading columns, skipping: {input_file}")
            return

        # Ensure timestamp is datetime and UTC-naive
        df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
        df.dropna(subset=['timestamp'], inplace=True) # Drop rows where timestamp conversion failed

        if df.empty:
             logger.warning(f"DataFrame empty after timestamp conversion/dropna for {input_file}. Skipping.")
             return

        # Convert to UTC-naive using our helper function
        df['timestamp'] = df['timestamp'].apply(to_utc_naive)
        df = df.set_index('timestamp').sort_index()

        # Calculate mid-price (or use existing if available)
        if 'mid_price' not in df.columns:
             df['best_bid'] = pd.to_numeric(df['best_bid'], errors='coerce')
             df['best_ask'] = pd.to_numeric(df['best_ask'], errors='coerce')
             df['mid_price'] = (df['best_bid'] + df['best_ask']) / 2.0
        else:
             df['mid_price'] = pd.to_numeric(df['mid_price'], errors='coerce')

        df = df[['mid_price']].dropna() # Only need mid_price for resampling

        if df.empty:
            logger.warning(f"DataFrame empty after calculating/cleaning mid_price for {input_file}. Skipping.")
            return

    except Exception as e:
        logger.error(f"Error loading or preparing data from {input_file}: {e}", exc_info=True)
        return

    # --- Resample ---
    try:
        logger.debug(f"Resampling data for {date_str_iso} to {timeframe}...")
        ohlc = df['mid_price'].resample(timeframe, origin='epoch').ohlc()
        ohlc = ohlc.dropna(subset=['open', 'high', 'low', 'close']) # Drop rows where OHLC failed

        # --- Feature Calculation ---
        if not ohlc.empty:
            logger.info(f"Calculating log returns for {len(ohlc)} rows")
            # Calculate log returns (shift is needed to align with next period)
            prev_close = ohlc['close'].shift(1)
            valid_prices = (ohlc['close'] > 0) & (prev_close > 0)
            ohlc.loc[valid_prices, 'log_ret'] = np.log(ohlc.loc[valid_prices, 'close'] / prev_close[valid_prices])
            logger.info(f"Log returns calculation complete. NaN count: {ohlc['log_ret'].isna().sum()} / {len(ohlc)}")

            # Calculate realized volatility (20-period rolling standard deviation of log returns)
            window = 24 if timeframe == "1h" else 6  # Define window based on timeframe for ~24 hours equivalent
            logger.info(f"Calculating realized volatility with {timeframe}-specific window={window}") # Updated log
            # Ensure min_periods makes sense (e.g., at least half the window)
            min_p = max(1, window // 2)
            ohlc['realised_vol'] = ohlc['log_ret'].rolling(window=window, min_periods=min_p).std()
            logger.info(f"Realized volatility calculation complete (window={window}, min_periods={min_p}). NaN count: {ohlc['realised_vol'].isna().sum()} / {len(ohlc)}")

            # Print column names and first few rows for debugging
            logger.info(f"DataFrame columns after feature calculation: {ohlc.columns.tolist()}")
        else:
            logger.warning("OHLC DataFrame is empty before feature calculation. Adding empty feature columns.")
            # Ensure columns exist even if empty
            ohlc['log_ret'] = np.nan
            ohlc['realised_vol'] = np.nan
        # --- End Feature Calculation ---

        if ohlc.empty:
            logger.warning(f"Resampling resulted in empty DataFrame for {date_str_iso}, {timeframe}. Skipping save.")
            return

    except Exception as e:
        logger.error(f"Error during resampling for {date_str_iso}, {timeframe}: {e}", exc_info=True)
        return

    # --- Save Output ---
    try:
        logger.info(f"Saving {len(ohlc)} OHLC bars to: {output_file}")

        # Debug output to verify columns before saving
        logger.info(f"Columns before saving: {ohlc.columns.tolist()}")

        # Ensure all columns are included in the output
        if 'log_ret' not in ohlc.columns:
            logger.error("log_ret column missing before save! Adding empty column.")
            ohlc['log_ret'] = np.nan

        if 'realised_vol' not in ohlc.columns:
            logger.error("realised_vol column missing before save! Adding empty column.")
            ohlc['realised_vol'] = np.nan

        # Reset index to convert timestamp index to a column
        save_df = ohlc.reset_index()

        # Verify columns after reset_index
        logger.info(f"Columns after reset_index: {save_df.columns.tolist()}")

        # Save with all columns explicitly specified to ensure order and inclusion
        save_columns = ['timestamp', 'open', 'high', 'low', 'close', 'log_ret', 'realised_vol']
        save_df = save_df[save_columns]

        # Ensure timestamps are UTC-naive before saving
        save_df['timestamp'] = save_df['timestamp'].apply(to_utc_naive)
        assert save_df['timestamp'].dt.tz is None, "Resampled OHLCV produced tz-aware timestamp!"

        # Final verification before saving
        logger.info(f"Final columns to be saved: {save_df.columns.tolist()}")

        # Save the DataFrame to Parquet
        save_df.to_parquet(output_file, index=False)

        # Verify the saved file
        check_df = pd.read_parquet(output_file)
        logger.info(f"Columns in saved file: {check_df.columns.tolist()}")

        logger.info(f"Successfully saved {timeframe} OHLCV bars to {output_file}")
    except Exception as e:
        logger.error(f"Failed to save OHLCV bars for {date_str_iso} {timeframe} to {output_file}: {e}", exc_info=True)


# --- Main Execution Logic ---
def main():
    parser = argparse.ArgumentParser(description="Resample L2 raw data to OHLCV bars.")
    parser.add_argument(
        "--date",
        required=False, # Made optional
        help="Specific date to process (YYYY-MM-DD). If omitted, processes all found dates."
    )
    parser.add_argument(
        "--tf",
        required=False, # Made optional
        choices=['1h', '4h'],
        help="Specific timeframe to process. If omitted, processes both '1h' and '4h'."
    )
    args = parser.parse_args()

    dates_to_process: List[str] = []
    timeframes_to_process: List[str] = []

    # Determine timeframes
    if args.tf:
        timeframes_to_process.append(args.tf)
        logger.info(f"Processing specified timeframe: {args.tf}")
    else:
        timeframes_to_process = ['1h', '4h']
        logger.info("No timeframe specified, processing both '1h' and '4h'.")

    # Determine dates
    if args.date:
        # Validate format if specific date is given
        try:
            datetime.strptime(args.date, '%Y-%m-%d')
            dates_to_process.append(args.date)
            logger.info(f"Processing specified date: {args.date}")
        except ValueError:
            logger.error(f"Invalid date format for --date: {args.date}. Expected YYYY-MM-DD.")
            return # Exit if specific date is invalid
    else:
        logger.info(f"Scanning {RAW_DIR} for input files (YYYYMMDD_raw2.parquet)...")
        found_dates: Set[str] = set()
        # Regex to match YYYYMMDD at the start of the filename
        date_pattern = re.compile(r"^(\d{8})_raw2\.parquet$")
        try:
            if not RAW_DIR.exists():
                 logger.error(f"Raw input directory not found: {RAW_DIR}")
                 return
            for item in RAW_DIR.iterdir():
                if item.is_file():
                    match = date_pattern.match(item.name)
                    if match:
                        date_str_yyyymmdd = match.group(1)
                        # Convert YYYYMMDD to YYYY-MM-DD
                        try:
                            date_obj = datetime.strptime(date_str_yyyymmdd, '%Y%m%d')
                            date_str_iso = date_obj.strftime('%Y-%m-%d')
                            found_dates.add(date_str_iso)
                        except ValueError:
                            logger.warning(f"Skipping file with invalid date format in name: {item.name}")
        except Exception as e:
             logger.error(f"Error scanning directory {RAW_DIR}: {e}", exc_info=True)
             return

        if not found_dates:
            logger.error(f"No valid input files (YYYYMMDD_raw2.parquet) found in {RAW_DIR}.")
            return
        dates_to_process = sorted(list(found_dates))
        logger.info(f"Found {len(dates_to_process)} dates to process in {RAW_DIR}.")

    # --- Process dates and timeframes ---
    logger.info(f"Starting processing for {len(dates_to_process)} dates: {', '.join(dates_to_process)}")
    logger.info(f"Processing timeframes: {', '.join(timeframes_to_process)}")
    total_tasks = len(dates_to_process) * len(timeframes_to_process)
    with tqdm(total=total_tasks, desc="Resampling Progress") as pbar:
        for date_str in dates_to_process:
            for tf in timeframes_to_process:
                pbar.set_description(f"Processing {date_str} {tf}")
                process_date(date_str, tf)
                pbar.update(1) # Update progress bar for each date/tf pair

    logger.info("Resampling process finished.")


if __name__ == "__main__":
    main()