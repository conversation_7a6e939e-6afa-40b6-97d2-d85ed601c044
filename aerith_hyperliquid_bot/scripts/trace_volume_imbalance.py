#!/usr/bin/env python3
"""
Trace Volume Imbalance Field
============================

Debug where volume_imbalance is getting lost in the data pipeline.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from datetime import datetime
from hyperliquid_bot.modern.data_loader import ModernDataLoader
from hyperliquid_bot.config.settings import load_config

def main():
    print("=== Tracing Volume Imbalance Field ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Create data loader
    loader = ModernDataLoader(config)
    
    # Test date range
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 16)
    
    print(f"Testing data from {start_date} to {end_date}\n")
    
    # Step 1: Load raw features_1s data
    print("Step 1: Loading raw features_1s data...")
    features_1s = loader._load_feature_data(start_date, end_date)
    
    if not features_1s.empty:
        print(f"  Loaded {len(features_1s)} rows")
        print(f"  Has 'obi_smoothed': {'obi_smoothed' in features_1s.columns}")
        print(f"  Has 'volume_imbalance': {'volume_imbalance' in features_1s.columns}")
        
        # Show some sample values
        if 'volume_imbalance' in features_1s.columns:
            vi_vals = features_1s['volume_imbalance'].dropna().head(5).tolist()
            print(f"  Sample volume_imbalance values: {vi_vals}")
        elif 'obi_smoothed' in features_1s.columns:
            obi_vals = features_1s['obi_smoothed'].dropna().head(5).tolist()
            print(f"  Sample obi_smoothed values: {obi_vals}")
    else:
        print("  ERROR: No data loaded!")
        return
    
    # Step 2: Load hourly features
    print("\nStep 2: Loading hourly features (resampled)...")
    hourly_features = loader.load_hourly_features(start_date, end_date)
    
    if not hourly_features.empty:
        print(f"  Loaded {len(hourly_features)} hourly bars")
        print(f"  Has 'volume_imbalance': {'volume_imbalance' in hourly_features.columns}")
        print(f"  Has 'obi_smoothed': {'obi_smoothed' in hourly_features.columns}")
        
        # Show which columns are present
        print(f"\n  Columns present: {sorted(hourly_features.columns)[:10]}...")
        
        # Check if volume_imbalance has values
        if 'volume_imbalance' in hourly_features.columns:
            vi_nulls = hourly_features['volume_imbalance'].isna().sum()
            print(f"  Volume imbalance nulls: {vi_nulls}/{len(hourly_features)}")
            if vi_nulls < len(hourly_features):
                vi_sample = hourly_features['volume_imbalance'].dropna().head(3).tolist()
                print(f"  Sample hourly volume_imbalance: {vi_sample}")
    else:
        print("  ERROR: No hourly data!")
    
    # Step 3: Test a single signal extraction
    print("\nStep 3: Testing single signal extraction...")
    if not hourly_features.empty:
        # Get first row
        first_row = hourly_features.iloc[0]
        print(f"  First row timestamp: {first_row.get('timestamp', 'N/A')}")
        
        # Check what's in the row
        print(f"  Has 'volume_imbalance': {'volume_imbalance' in first_row}")
        if 'volume_imbalance' in first_row:
            print(f"  Value: {first_row['volume_imbalance']}")
        
        # Check the dict conversion
        row_dict = first_row.to_dict()
        print(f"\n  Dict has 'volume_imbalance': {'volume_imbalance' in row_dict}")
        
    print("\n" + "="*50)
    print("ANALYSIS:")
    if 'volume_imbalance' not in features_1s.columns:
        print("❌ PROBLEM: volume_imbalance not in features_1s after adapter!")
        print("   The data adapter is not working correctly.")
    elif 'volume_imbalance' not in hourly_features.columns:
        print("❌ PROBLEM: volume_imbalance lost during hourly resampling!")
        print("   Check the aggregation dict in load_hourly_features.")
    elif hourly_features['volume_imbalance'].isna().all():
        print("❌ PROBLEM: volume_imbalance is all NaN after resampling!")
        print("   The aggregation method might be wrong.")
    else:
        print("✅ volume_imbalance field is present in the data pipeline")
        print("   The issue might be in how signals are extracted.")

if __name__ == "__main__":
    main()