#!/usr/bin/env python3
"""
Trace why modern system generates 0 trades
==========================================

This script steps through the modern system to identify
exactly where and why trades are not being generated.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
import pandas as pd
import logging
import yaml
from pathlib import Path
import json

from hyperliquid_bot.config.settings import Config, load_config
from hyperliquid_bot.modern.robust_backtest_engine import RobustBacktestEngine

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_modern_config():
    """Load config with modern system override."""
    # Load base config
    with open("configs/base.yaml", 'r') as f:
        base_dict = yaml.safe_load(f)
    
    # Load modern override
    with open("configs/overrides/modern_system.yaml", 'r') as f:
        modern_override = yaml.safe_load(f)
    
    # Deep merge function
    def deep_merge(base, override):
        for key, value in override.items():
            if isinstance(value, dict) and key in base and isinstance(base[key], dict):
                deep_merge(base[key], value)
            else:
                base[key] = value
    
    # Apply override with deep merge
    deep_merge(base_dict, modern_override)
    
    # Fix the microstructure depth_levels issue
    if 'microstructure' in base_dict:
        base_dict['microstructure']['depth_levels_for_calc'] = None
    
    # Create config
    return Config(**base_dict)

def trace_signal_generation():
    """Trace signal generation step by step."""
    logger.info("=== TRACING MODERN SYSTEM - NO TRADES ISSUE ===")
    
    # Load config
    config = load_modern_config()
    
    # Test period - just one week
    start_date = datetime(2024, 4, 1, 0, 0, 0)
    end_date = datetime(2024, 4, 8, 0, 0, 0)
    
    # Create engine
    engine = RobustBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=True
    )
    
    logger.info(f"Test period: {start_date} to {end_date}")
    logger.info(f"Warmup hours: {engine.requested_warmup_hours}")
    
    # Load data manually to inspect
    warmup_start = start_date - timedelta(hours=engine.requested_warmup_hours)
    data = engine.data_loader.load_data(warmup_start, end_date)
    
    logger.info(f"Data loaded: {len(data)} rows")
    logger.info(f"Data range: {data.index.min()} to {data.index.max()}")
    
    # Check a few specific hours for signal generation
    test_hours = [
        start_date + timedelta(hours=24),  # Day 1
        start_date + timedelta(hours=48),  # Day 2
        start_date + timedelta(hours=72),  # Day 3
    ]
    
    for test_hour in test_hours:
        logger.info(f"\n--- TESTING HOUR: {test_hour} ---")
        
        # 1. Check if data exists
        if test_hour not in data.index:
            logger.warning(f"No data for {test_hour}")
            continue
        
        hour_data = data.loc[test_hour]
        logger.info(f"Price: ${hour_data['close']:.2f}")
        
        # 2. Check regime state
        regime_features = engine.regime_manager.get_regime_features_for_strategy(
            timestamp=test_hour,
            lookback_hours=1
        )
        logger.info(f"Regime: {regime_features.get('current_state', 'UNKNOWN')}")
        logger.info(f"Confidence: {regime_features.get('current_confidence', 0):.3f}")
        
        # 3. Test signal calculation
        ohlcv_history = data.loc[:test_hour]
        signals = engine.hourly_evaluator.signal_engine.calculate_signals(
            ohlcv_df=ohlcv_history,
            regime_features=regime_features
        )
        
        logger.info(f"Signals calculated:")
        logger.info(f"  - Close: {signals.get('close', 'N/A')}")
        logger.info(f"  - EMA Fast: {signals.get('ema_fast', 'N/A')}")
        logger.info(f"  - EMA Slow: {signals.get('ema_slow', 'N/A')}")
        logger.info(f"  - Forecast: {signals.get('forecast', 'N/A')}")
        logger.info(f"  - ATR: {signals.get('atr_14', 'N/A')}")
        logger.info(f"  - RSI: {signals.get('rsi', 'N/A')}")
        
        # 4. Check if signals are valid
        is_valid = engine.hourly_evaluator.signal_engine.validate_signals(signals)
        logger.info(f"Signals valid: {is_valid}")
        
        if not is_valid:
            # Find what's missing
            required = ['close', 'high', 'low', 'volume', 'ema_fast', 'ema_slow', 
                       'ema_baseline', 'atr_14', 'atr_percent', 'rsi', 
                       'bb_upper', 'bb_middle', 'bb_lower']
            for sig in required:
                if sig not in signals or pd.isna(signals.get(sig)):
                    logger.warning(f"  - Missing/NaN: {sig}")
        
        # 5. Test strategy evaluation
        if is_valid or True:  # Force evaluation even if not valid
            # Prepare hourly bar
            hourly_bar = hour_data.to_dict()
            
            # Current signals
            current_signals = {
                'volume_imbalance': hour_data.get('volume_imbalance', 0),
                'trade_intensity': hour_data.get('trade_intensity', 0),
                'price_momentum': hour_data.get('price_momentum', 0)
            }
            
            # Try to evaluate
            try:
                decision = engine.hourly_evaluator.evaluate(
                    hourly_bar=hourly_bar,
                    current_signals=current_signals,
                    timestamp=test_hour,
                    ohlcv_history=ohlcv_history
                )
                
                if decision:
                    logger.info(f"🎯 SIGNAL GENERATED: {decision}")
                else:
                    logger.info(f"No signal generated")
                    
                    # Deep dive into strategy
                    allowed_states = engine.strategy.get_allowed_states('trend_following')
                    current_regime = regime_features.get('current_state', 'UNKNOWN')
                    logger.info(f"  - Allowed states: {allowed_states}")
                    logger.info(f"  - Current regime: {current_regime}")
                    logger.info(f"  - Regime allowed: {current_regime in allowed_states}")
                    
            except Exception as e:
                logger.error(f"Error evaluating: {e}")
    
    # Run a quick backtest to check final results
    logger.info(f"\n--- RUNNING QUICK BACKTEST ---")
    results = engine.run_backtest()
    
    logger.info(f"\nBACKTEST RESULTS:")
    logger.info(f"  - Total trades: {results['total_trades']}")
    logger.info(f"  - Data quality: {results['data_quality']}")
    
    # Save detailed trace
    trace_file = "trace_no_trades_results.json"
    with open(trace_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    logger.info(f"\nDetailed results saved to: {trace_file}")

if __name__ == "__main__":
    trace_signal_generation()