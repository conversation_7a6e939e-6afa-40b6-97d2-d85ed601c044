#!/usr/bin/env python3
"""
A/B Backtest: Legacy Detector
==============================

Run full 2024 backtest using legacy detector with robust engine.
This is the control group for comparison.
"""

import sys
import logging
from pathlib import Path
from datetime import datetime, timedelta
import json

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_backtest_engine import RobustBacktestEngine


def setup_logging():
    """Configure logging to file and console."""
    log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    log_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = log_dir / f"ab_backtest_legacy_{timestamp}.log"
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return log_file


def main():
    """Run legacy detector backtest for 2024."""
    print("\n" + "="*60)
    print("A/B Backtest: Legacy Detector (Control)")
    print("="*60)
    
    # Setup logging
    log_file = setup_logging()
    logger = logging.getLogger("ABBacktestLegacy")
    
    logger.info("Starting A/B backtest with legacy detector")
    
    # Load configuration with legacy detector
    config = load_config("configs/overrides/modern_system_v2_complete.yaml")
    
    # Override to use legacy detector (no enhanced features)
    config.regime.detector_type = "granular_microstructure"
    
    # Correct thresholds (already in config, but ensure)
    config.regime.gms_vol_high_thresh = 0.0092
    config.regime.gms_vol_low_thresh = 0.0055
    config.regime.gms_mom_strong_thresh = 100.0
    config.regime.gms_mom_weak_thresh = 50.0
    config.microstructure.gms_obi_strong_confirm_thresh = 0.2
    config.microstructure.gms_obi_weak_confirm_thresh = 0.05
    
    logger.info("Configuration loaded with legacy detector")
    
    # Set up backtest parameters
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 12, 31, 23, 59, 59)
    
    logger.info(f"Backtest period: {start_date} to {end_date}")
    
    # Create and run backtest engine
    print("\nInitializing backtest engine...")
    engine = RobustBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=True  # Use pre-computed regimes
    )
    
    print("\nRunning legacy detector backtest...")
    print("This will take several minutes...")
    
    # Run the backtest
    results = engine.run_backtest()
    
    # Save results
    output_file = f"ab_results_legacy_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # Display summary
    print("\n" + "="*60)
    print("Legacy Detector Results Summary")
    print("="*60)
    print(f"Total Trades: {results['total_trades']}")
    print(f"Win Rate: {results['win_rate']:.2%}")
    print(f"Total Return: {results['total_return']:.2%}")
    print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
    print(f"Max Drawdown: {results['max_drawdown']:.2%}")
    print(f"\nLog file: {log_file}")
    print(f"Results saved to: {output_file}")
    
    # Trade frequency analysis
    if results['total_trades'] > 0:
        days = (end_date - start_date).days
        trades_per_year = results['total_trades'] * 365 / days
        print(f"\nTrade Frequency: {trades_per_year:.0f} trades/year")
    
    return results


if __name__ == "__main__":
    main()