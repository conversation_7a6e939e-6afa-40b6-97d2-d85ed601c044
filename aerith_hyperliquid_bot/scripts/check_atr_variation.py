#!/usr/bin/env python3
"""
Check ATR variation across hours and days to verify proper calculation.
"""

import pandas as pd
from pathlib import Path

def check_atr_variation():
    """Check ATR variation across multiple hours and days."""
    
    print('=== ATR VARIATION ACROSS HOURS AND DAYS ===')
    print()

    # Check ATR variation across multiple hours and days
    base_path = Path('/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s')

    # Sample different dates and hours
    test_cases = [
        ('2025-03-01', 10),
        ('2025-03-01', 14),
        ('2025-03-07', 10),
        ('2025-03-07', 17),
        ('2025-03-15', 10),
        ('2025-03-15', 15),
        ('2025-03-20', 10),
        ('2025-03-20', 16)
    ]

    atr_values = []

    for date, hour in test_cases:
        file_path = base_path / date / f'features_{hour:02d}.parquet'
        
        try:
            df = pd.read_parquet(file_path)
            atr_val = df['atr_14_sec'].iloc[0]  # Get first ATR value (should be same for whole hour)
            mid_price = df['mid_price'].mean()
            atr_pct = atr_val / mid_price
            
            atr_values.append({
                'date': date,
                'hour': hour,
                'atr_14_sec': atr_val,
                'mid_price': mid_price,
                'atr_percent': atr_pct
            })
            
            print(f'{date} {hour:02d}:00 | ATR: {atr_val:8.2f} | Price: ${mid_price:8.2f} | ATR%: {atr_pct*100:5.3f}%')
            
        except Exception as e:
            print(f'{date} {hour:02d}:00 | ERROR: {e}')

    print()
    print('📊 ATR VARIATION ANALYSIS:')

    if atr_values:
        atr_series = pd.Series([x['atr_14_sec'] for x in atr_values])
        price_series = pd.Series([x['mid_price'] for x in atr_values])
        
        print(f'ATR Range: {atr_series.min():.2f} to {atr_series.max():.2f}')
        print(f'ATR Std Dev: {atr_series.std():.2f}')
        print(f'ATR Variation: {(atr_series.max() - atr_series.min()):.2f}')
        print(f'Price Range: ${price_series.min():.2f} to ${price_series.max():.2f}')
        print()
        
        # Check if ATR changes over time (this is the key test)
        unique_atr_values = atr_series.nunique()
        print(f'✅ Unique ATR values across samples: {unique_atr_values}')
        print(f'✅ ATR varies across time: {"YES" if unique_atr_values > 1 else "NO"}')
        print()
        
        # Show ATR evolution over time
        print('📈 ATR EVOLUTION:')
        for i, data in enumerate(atr_values):
            if i > 0:
                prev_atr = atr_values[i-1]['atr_14_sec']
                change = data['atr_14_sec'] - prev_atr
                change_pct = (change / prev_atr) * 100 if prev_atr != 0 else 0
                print(f'  {data["date"]} {data["hour"]:02d}:00 | Change: {change:+7.2f} ({change_pct:+5.2f}%)')
        
        print()
        print('🎯 CONCLUSION:')
        if unique_atr_values > 1:
            print('✅ ATR calculation is working correctly!')
            print('✅ ATR values change appropriately across different hours/days')
            print('✅ Static values within each hour are EXPECTED (hourly ATR forward-filled)')
            print('✅ This is the correct implementation for Task R-101')
        else:
            print('❌ ATR values are not changing - potential issue')
    else:
        print('❌ No data could be loaded')

if __name__ == '__main__':
    check_atr_variation()
