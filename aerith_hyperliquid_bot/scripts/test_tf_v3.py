#!/usr/bin/env python
# scripts/test_tf_v3.py

"""
Simple test script for TF-v3 strategy.

This script creates a TF-v3 strategy instance and tests it with a simple signal.
"""

import os
import sys
import logging
from datetime import datetime
import pandas as pd

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.strategies.tf_v3 import TFV3Strategy
from hyperliquid_bot.portfolio.portfolio import Portfolio

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/test_tf_v3.log', mode='w')
    ]
)

# Set log level for all loggers
for name in logging.root.manager.loggerDict:
    logging.getLogger(name).setLevel(logging.DEBUG)

logger = logging.getLogger(__name__)

def main():
    """Main entry point."""
    logger.info("Loading configuration...")
    config = load_config('configs/base.yaml')

    # Create a portfolio instance
    portfolio = Portfolio(config)

    # Create a TF-v3 strategy instance
    strategy = TFV3Strategy(config, 'tf_v3', portfolio=portfolio)

    # Create a test signal
    test_signal = {
        'timestamp': pd.Timestamp('2025-03-01 00:00:00'),
        'open': 50000.0,
        'high': 51000.0,
        'low': 49000.0,
        'close': 50500.0,
        'volume': 100.0,
        'atr_14': 1000.0,
        'atr_14_sec': 1000.0,
        'ema_20': 49800.0,
        'ema_50': 49500.0,
        # Add required signals
        'regime': 'BULL',
        'regime_timestamp': pd.Timestamp('2025-03-01 00:00:00'),
        'risk_suppressed': False,
    }

    # Add GMS snapshot to test signal
    test_signal['gms_snapshot'] = {
        'timestamp': pd.Timestamp('2025-03-01 00:00:00'),
        'state': 'BULL',
        'risk_suppressed': False,
        'age_sec': 0
    }

    # Test the strategy
    logger.info("Testing strategy with test signal")
    direction, info = strategy.evaluate(test_signal)
    logger.info(f"Test result: direction={direction}, info={info}")

    # Test with different regimes
    for regime in ['BULL', 'BEAR', 'CHOP']:
        test_signal['gms_snapshot']['state'] = regime
        logger.info(f"Testing strategy with regime: {regime}")
        direction, info = strategy.evaluate(test_signal)
        logger.info(f"Test result for {regime}: direction={direction}, info={info}")

    logger.info("Test completed")

if __name__ == '__main__':
    main()
