#!/usr/bin/env python3
"""
Test script to verify ATR percent calculation fix.
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.core.detector_factory import get_regime_detector


def test_atr_calculation():
    """Test that ATR calculation no longer multiplies by 100."""
    print("Testing ATR Calculation Fix")
    print("="*50)
    
    # Create sample data
    data = {
        'timestamp': [1640995200, 1640995260, 1640995320],
        'close': [50000.0, 50100.0, 49900.0],
        'high': [50050.0, 50150.0, 49950.0],
        'low': [49950.0, 50050.0, 49850.0],
        'volume': [100.0, 120.0, 90.0]
    }
    df = pd.DataFrame(data)
    
    # Manually calculate what ATR percent SHOULD be
    # ATR is roughly the average of true ranges
    # True range = max(high-low, high-prev_close, prev_close-low)
    
    # For close=50000, if ATR=500, then ATR% should be 500/50000 = 0.01 (1%)
    # NOT 1.0 (100%)
    
    expected_atr_percent = 0.015  # 1.5% as decimal
    
    print(f"Expected ATR%: {expected_atr_percent} (decimal) = {expected_atr_percent*100}%")
    print(f"Wrong ATR%: {expected_atr_percent*100} (would be {expected_atr_percent*100*100}%)")
    
    # Test that values are now in correct range
    if expected_atr_percent < 0.1:
        print("✅ Expected ATR% is in reasonable range (<10%)")
    else:
        print("❌ Expected ATR% is too high")
    
    print("\nValidation ranges:")
    print("- Correct ATR%: 0.005 - 0.05 (0.5% - 5%)")
    print("- Wrong ATR%: 0.5 - 5.0 (50% - 500%)")


def test_unified_detector_signals():
    """Test that UnifiedGMSDetector works with fixed signals."""
    print("\n" + "="*50)
    print("Testing UnifiedGMSDetector with Fixed Signals")
    print("="*50)
    
    # Load config
    config = load_config("configs/base.yaml")
    
    # Create detector
    detector = get_regime_detector(config)
    print(f"Created detector: {type(detector).__name__}")
    print(f"Mode: {getattr(detector, 'detector_mode', 'N/A')}")
    
    # Check required signals
    required = detector.required_signals
    print(f"\nRequired signals: {required}")
    
    # Create test signals with correct ATR values
    signals = {
        'timestamp': 1640995200,
        'atr_percent': 0.02,  # 2% as decimal (correct)
        'ma_slope': 75.0,
        'obi_smoothed_5': 0.15,
        'spread_mean': 0.0001,
        'spread_std': 0.00005,
        'close': 50000.0
    }
    
    print(f"\nTest signals:")
    for key, value in signals.items():
        print(f"  {key}: {value}")
    
    # Test regime detection
    try:
        regime = detector.get_regime(signals)
        print(f"\n✅ Regime detection successful: {regime}")
        return True
    except Exception as e:
        print(f"\n❌ Regime detection failed: {e}")
        return False


def validate_atr_values():
    """Provide validation criteria for ATR values."""
    print("\n" + "="*50)
    print("ATR Value Validation Guide")
    print("="*50)
    
    print("\n📊 Expected ATR% ranges for Bitcoin:")
    print("- Normal market: 0.01 - 0.03 (1% - 3%)")
    print("- Volatile market: 0.03 - 0.06 (3% - 6%)")
    print("- Extreme volatility: 0.06 - 0.10 (6% - 10%)")
    print("- Impossible values: >0.10 (>10%)")
    
    print("\n🚨 Red flags (indicating bug):")
    print("- ATR% > 0.5 (50%)")
    print("- ATR% > 1.0 (100%)")
    print("- ATR% > 4.0 (400%)")
    
    print("\n✅ Validation function:")
    print("""
def validate_atr_percent(atr_pct_series):
    max_val = atr_pct_series.max()
    mean_val = atr_pct_series.mean()
    
    if max_val > 0.1:
        return f"❌ Max ATR% {max_val:.4f} exceeds 10%"
    elif mean_val > 0.05:
        return f"❌ Mean ATR% {mean_val:.4f} exceeds 5%"
    else:
        return f"✅ ATR% values in normal range (max: {max_val:.4f}, mean: {mean_val:.4f})"
    """)


def main():
    """Run all tests."""
    print("ATR Percent Calculation Fix Verification")
    print("="*60)
    
    test_atr_calculation()
    
    success = test_unified_detector_signals()
    
    validate_atr_values()
    
    print("\n" + "="*60)
    if success:
        print("✅ All tests passed! ATR fix appears to be working.")
        print("\nNext steps:")
        print("1. Run a full backtest to verify trade count")
        print("2. Check that strategies can now find regime signals")
        print("3. Validate ATR values are in realistic ranges")
    else:
        print("❌ Tests failed. Check the error messages above.")
    print("="*60)


if __name__ == "__main__":
    main()