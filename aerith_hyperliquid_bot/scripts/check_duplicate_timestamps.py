#!/usr/bin/env python3
"""
Check for Duplicate Timestamps
==============================

Diagnose duplicate timestamp issues in the data.
"""

import sys
from pathlib import Path
from datetime import datetime
import pandas as pd
import numpy as np

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))


def check_parquet_duplicates(file_path):
    """Check a single parquet file for duplicates."""
    try:
        df = pd.read_parquet(file_path)
        
        if 'timestamp' in df.columns:
            total = len(df)
            unique = df['timestamp'].nunique()
            duplicates = total - unique
            
            if duplicates > 0:
                print(f"  ❌ {file_path.name}: {duplicates} duplicate timestamps out of {total}")
                # Show some examples
                dup_times = df[df.duplicated('timestamp', keep=False)]['timestamp'].unique()[:5]
                for t in dup_times:
                    count = len(df[df['timestamp'] == t])
                    print(f"     - {t}: {count} rows")
            else:
                print(f"  ✅ {file_path.name}: No duplicates ({total} rows)")
        else:
            print(f"  ⚠️  {file_path.name}: No timestamp column")
            
    except Exception as e:
        print(f"  ❌ {file_path.name}: Error reading - {e}")


def main():
    print("="*80)
    print("CHECKING FOR DUPLICATE TIMESTAMPS")
    print("="*80)
    
    # Check different data directories
    data_root = Path("/Volumes/HL_Data/hyperliquid_data")
    
    # Check 1-hour resampled data
    print("\n📊 Checking 1-hour resampled data (Q1 2024):")
    resampled_dir = data_root / "resampled_l2/1h"
    
    dates_to_check = [
        "2024-01-01", "2024-01-15", "2024-02-01", "2024-03-01"
    ]
    
    for date_str in dates_to_check:
        file_path = resampled_dir / f"{date_str}_1h.parquet"
        if file_path.exists():
            check_parquet_duplicates(file_path)
    
    # Check features_1s data
    print("\n📊 Checking features_1s data (sample hours):")
    features_dir = data_root / "features_1s"
    
    # Check a few hours from Jan 15
    jan15_dir = features_dir / "2024-01-15"
    if jan15_dir.exists():
        for hour in ["00", "12", "23"]:
            file_path = jan15_dir / f"features_{hour}.parquet"
            if file_path.exists():
                check_parquet_duplicates(file_path)
    
    # Check raw2 data
    print("\n📊 Checking raw2 data (legacy):")
    raw2_dir = data_root / "raw2"
    
    sample_files = ["20240115_raw2.parquet", "20240201_raw2.parquet"]
    for fname in sample_files:
        file_path = raw2_dir / fname
        if file_path.exists():
            check_parquet_duplicates(file_path)
    
    # Check for timezone issues
    print("\n🕐 Checking for timezone issues:")
    test_file = resampled_dir / "2024-01-15_1h.parquet"
    if test_file.exists():
        df = pd.read_parquet(test_file)
        if 'timestamp' in df.columns:
            print(f"  - First timestamp: {df['timestamp'].iloc[0]}")
            print(f"  - Last timestamp: {df['timestamp'].iloc[-1]}")
            print(f"  - Timestamp dtype: {df['timestamp'].dtype}")
            
            # Check if timestamps are timezone aware
            if hasattr(df['timestamp'].iloc[0], 'tz'):
                print(f"  - Timezone: {df['timestamp'].iloc[0].tz}")
            else:
                print("  - Timezone: None (naive timestamps)")


if __name__ == "__main__":
    main()