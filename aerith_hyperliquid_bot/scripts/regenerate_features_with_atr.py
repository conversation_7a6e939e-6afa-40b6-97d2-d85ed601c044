#!/usr/bin/env python3
"""
Regenerate 1-second feature files with improved ATR calculation.

This script regenerates all 1-second feature parquet files for the specified date range
using the new ATR injection functionality from Task R-101.
"""

import os
import sys
import argparse
from pathlib import Path
from datetime import datetime, timedelta
import logging

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def regenerate_features_for_date(date_str: str, config, overwrite: bool = False) -> bool:
    """
    Regenerate 1-second features for a specific date.

    Args:
        date_str: Date in YYYY-MM-DD format
        config: Configuration object
        overwrite: Whether to overwrite existing files

    Returns:
        True if successful, False otherwise
    """
    from tools.etl_l20_to_1s import main as etl_main

    try:
        logger.info(f"Regenerating features for {date_str}")

        # Check if raw Arrow files exist for this date
        raw_dir = Path(config.data_paths.raw_l2_dir) / date_str
        if not raw_dir.exists():
            logger.warning(f"Raw data directory does not exist: {raw_dir}")
            return False

        arrow_files = list(raw_dir.glob("BTC_*_l2Book.arrow"))
        if not arrow_files:
            logger.warning(f"No Arrow files found in {raw_dir}")
            return False

        logger.info(f"Found {len(arrow_files)} Arrow files for {date_str}")

        # Run ETL for this date
        # We'll use the command line interface to ensure proper argument handling
        import subprocess

        cmd = [
            sys.executable, "-m", "tools.etl_l20_to_1s",
            "--date", date_str
        ]

        if overwrite:
            cmd.append("--overwrite")

        # Run from the project root
        result = subprocess.run(
            cmd,
            cwd=str(project_root),
            capture_output=True,
            text=True
        )

        if result.returncode == 0:
            logger.info(f"Successfully regenerated features for {date_str}")
            return True
        else:
            logger.error(f"ETL failed for {date_str}: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"Error regenerating features for {date_str}: {e}")
        return False


def regenerate_date_range(start_date: str, end_date: str, config, overwrite: bool = False) -> None:
    """
    Regenerate features for a date range.

    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        config: Configuration object
        overwrite: Whether to overwrite existing files
    """
    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")

    current_dt = start_dt
    success_count = 0
    error_count = 0

    while current_dt <= end_dt:
        date_str = current_dt.strftime("%Y-%m-%d")

        if regenerate_features_for_date(date_str, config, overwrite):
            success_count += 1
        else:
            error_count += 1

        current_dt += timedelta(days=1)

    logger.info(f"Regeneration complete: {success_count} successful, {error_count} errors")


def verify_atr_injection(date_str: str, config) -> bool:
    """
    Verify that ATR injection worked for a specific date.

    Args:
        date_str: Date in YYYY-MM-DD format
        config: Configuration object

    Returns:
        True if ATR columns are properly populated
    """
    try:
        import pandas as pd

        features_dir = Path(config.data_paths.feature_1s_dir) / date_str
        if not features_dir.exists():
            logger.error(f"Features directory does not exist: {features_dir}")
            return False

        # Check a sample feature file
        feature_files = list(features_dir.glob("features_*.parquet"))
        if not feature_files:
            logger.error(f"No feature files found in {features_dir}")
            return False

        # Load first file and check ATR columns
        sample_file = feature_files[0]
        df = pd.read_parquet(sample_file)

        # Check if ATR columns exist
        if 'atr_14_sec' not in df.columns:
            logger.error(f"atr_14_sec column missing in {sample_file}")
            return False

        if 'atr_percent_sec' not in df.columns:
            logger.error(f"atr_percent_sec column missing in {sample_file}")
            return False

        # Check if spread feature columns exist (Task R-112g)
        if 'spread_mean' not in df.columns:
            logger.error(f"spread_mean column missing in {sample_file}")
            return False

        if 'spread_std' not in df.columns:
            logger.error(f"spread_std column missing in {sample_file}")
            return False

        # Check if ATR values are populated (not all NaN)
        atr_nan_count = df['atr_14_sec'].isna().sum()
        total_rows = len(df)

        if atr_nan_count == total_rows:
            logger.error(f"All ATR values are NaN in {sample_file}")
            return False

        # Check if spread feature values are populated (Task R-112g)
        spread_mean_nan_count = df['spread_mean'].isna().sum()
        spread_std_nan_count = df['spread_std'].isna().sum()

        # Allow up to 70% NaN for spread features (due to 60-second rolling window)
        max_allowed_nan = int(0.7 * total_rows)

        if spread_mean_nan_count > max_allowed_nan:
            logger.error(f"Too many NaN values in spread_mean: {spread_mean_nan_count}/{total_rows} (max allowed: {max_allowed_nan})")
            return False

        if spread_std_nan_count > max_allowed_nan:
            logger.error(f"Too many NaN values in spread_std: {spread_std_nan_count}/{total_rows} (max allowed: {max_allowed_nan})")
            return False

        logger.info(f"ATR verification passed for {date_str}: {total_rows - atr_nan_count}/{total_rows} rows have ATR values")
        logger.info(f"Spread features verification passed for {date_str}: spread_mean {total_rows - spread_mean_nan_count}/{total_rows}, spread_std {total_rows - spread_std_nan_count}/{total_rows}")
        return True

    except Exception as e:
        logger.error(f"Error verifying ATR for {date_str}: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Regenerate 1-second features with improved ATR")
    parser.add_argument("--start-date", default="2025-03-01",
                        help="Start date in YYYY-MM-DD format")
    parser.add_argument("--end-date", default="2025-03-22",
                        help="End date in YYYY-MM-DD format")
    parser.add_argument("--date",
                        help="Single date to process in YYYY-MM-DD format")
    parser.add_argument("--overwrite", action="store_true",
                        help="Overwrite existing feature files")
    parser.add_argument("--verify-only", action="store_true",
                        help="Only verify ATR injection, don't regenerate")

    args = parser.parse_args()

    # Load configuration
    try:
        config = load_config('configs/base.yaml')
    except Exception as e:
        logger.error(f"Failed to load configuration: {e}")
        sys.exit(1)

    if args.verify_only:
        # Verification mode
        if args.date:
            dates = [args.date]
        else:
            start_dt = datetime.strptime(args.start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(args.end_date, "%Y-%m-%d")
            dates = []
            current_dt = start_dt
            while current_dt <= end_dt:
                dates.append(current_dt.strftime("%Y-%m-%d"))
                current_dt += timedelta(days=1)

        success_count = 0
        for date_str in dates:
            if verify_atr_injection(date_str, config):
                success_count += 1

        logger.info(f"Verification complete: {success_count}/{len(dates)} dates passed")

    else:
        # Regeneration mode
        if args.date:
            # Single date
            regenerate_features_for_date(args.date, config, args.overwrite)
        else:
            # Date range
            regenerate_date_range(args.start_date, args.end_date, config, args.overwrite)


if __name__ == "__main__":
    main()
