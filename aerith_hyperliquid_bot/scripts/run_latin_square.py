#!/usr/bin/env python3
"""
Latin Square Parameter Grid Search for Hyperliquid Bot

This script implements a 2x2 Latin-square design for parameter tuning,
generating temporary override files with different parameter combinations.

Usage:
    python scripts/run_latin_square.py
"""
import os
import sys
import subprocess
import yaml
import pandas as pd
from pathlib import Path
import tempfile
import json
from datetime import datetime

# Define the project root
PROJECT_ROOT = Path(__file__).parent.parent.resolve()

# Parameter grid - 2 levels per parameter
PARAM_GRID = {
    "obi_smoothing_window": [6, 14],
    "gms_obi_strong_confirm_thresh": [0.12, 0.20],
    "gms_spread_std_high_thresh": [0.000026, 0.000044],
    "gms_confirmation_bars": [1, 2]
}

# Template for override config
OVERRIDE_TEMPLATE = {
    "microstructure": {
        "obi_smoothing_window": None  # Will be filled in
    },
    "regime": {
        "gms_obi_strong_confirm_thresh": None,  # Will be filled in
        "gms_spread_std_high_thresh": None,     # Will be filled in
        "gms_confirmation_bars": None           # Will be filled in
    }
}

def create_temp_override(params):
    """Create a temporary override file with the given parameters."""
    # Deep copy the template to avoid modifying it
    override = {
        "microstructure": {
            "obi_smoothing_window": params["obi_smoothing_window"]
        },
        "regime": {
            "gms_obi_strong_confirm_thresh": params["gms_obi_strong_confirm_thresh"],
            "gms_spread_std_high_thresh": params["gms_spread_std_high_thresh"],
            "gms_confirmation_bars": params["gms_confirmation_bars"]
        }
    }
    
    # Create a temporary file
    fd, temp_path = tempfile.mkstemp(suffix='.yaml', prefix='grid_override_', dir=PROJECT_ROOT / "configs" / "overrides")
    os.close(fd)
    
    # Write the override config to the temp file
    with open(temp_path, 'w') as f:
        yaml.dump(override, f, default_flow_style=False)
    
    return temp_path

def extract_metrics(results_dir, tag):
    """Extract Sharpe ratio and max drawdown from results files."""
    # This is a placeholder - adjust based on your actual results format
    results_files = list(Path(results_dir).glob(f"*{tag}*.json"))
    
    if not results_files:
        print(f"Warning: No results file found for tag {tag}")
        return None, None
    
    results_file = results_files[0]
    
    try:
        with open(results_file, 'r') as f:
            results = json.load(f)
            
        # Adjust these keys based on your actual results structure
        sharpe = results.get("metrics", {}).get("sharpe_ratio", None)
        max_dd = results.get("metrics", {}).get("max_drawdown", None)
        
        return sharpe, max_dd
    except Exception as e:
        print(f"Error extracting metrics from {results_file}: {e}")
        return None, None

def run_grid_search():
    """Run the Latin-square grid search."""
    # Create results directory
    results_dir = PROJECT_ROOT / "grid_search_results"
    results_dir.mkdir(exist_ok=True)
    
    # Prepare results dataframe
    results = []
    
    # Generate all parameter combinations (16 total runs)
    total_runs = len(PARAM_GRID["obi_smoothing_window"]) * \
                 len(PARAM_GRID["gms_obi_strong_confirm_thresh"]) * \
                 len(PARAM_GRID["gms_spread_std_high_thresh"]) * \
                 len(PARAM_GRID["gms_confirmation_bars"])
    
    print(f"Starting 2x2 Latin-square grid search with {total_runs} total runs")
    
    run_count = 0
    for obi_win in PARAM_GRID["obi_smoothing_window"]:
        for obi_thr in PARAM_GRID["gms_obi_strong_confirm_thresh"]:
            for spd_thr in PARAM_GRID["gms_spread_std_high_thresh"]:
                for conf_bars in PARAM_GRID["gms_confirmation_bars"]:
                    run_count += 1
                    
                    # Create parameter set
                    params = {
                        "obi_smoothing_window": obi_win,
                        "gms_obi_strong_confirm_thresh": obi_thr,
                        "gms_spread_std_high_thresh": spd_thr,
                        "gms_confirmation_bars": conf_bars
                    }
                    
                    # Create tag for this run
                    tag = f"gs_{obi_thr}_{obi_win}_{spd_thr}_{conf_bars}"
                    
                    print(f"\nRun {run_count}/{total_runs}: {tag}")
                    print(f"Parameters: {params}")
                    
                    # Create temporary override file
                    temp_override = create_temp_override(params)
                    print(f"Created temporary override file: {temp_override}")
                    
                    try:
                        # Run the backtester with the override
                        cmd = [
                            sys.executable,  # Current Python interpreter
                            str(PROJECT_ROOT / "hyperliquid_bot" / "backtester" / "run_backtest.py"),
                            "--override", temp_override,
                            "--tag", tag
                        ]
                        
                        print(f"Running command: {' '.join(cmd)}")
                        subprocess.run(cmd, check=True)
                        
                        # Extract metrics
                        sharpe, max_dd = extract_metrics(PROJECT_ROOT / "results", tag)
                        
                        # Calculate Sharpe/DD ratio
                        if sharpe is not None and max_dd is not None and max_dd != 0:
                            sharpe_dd_ratio = sharpe / (-1 * max_dd)
                        else:
                            sharpe_dd_ratio = None
                        
                        # Add to results
                        results.append({
                            "OBI_WIN": obi_win,
                            "OBI_THR": obi_thr,
                            "SPD_THR": spd_thr,
                            "CONF_BARS": conf_bars,
                            "Sharpe": sharpe,
                            "MaxDD": max_dd,
                            "Sharpe_DD_Ratio": sharpe_dd_ratio,
                            "Tag": tag
                        })
                        
                    except Exception as e:
                        print(f"Error running backtest: {e}")
                    
                    finally:
                        # Clean up temporary file
                        try:
                            os.remove(temp_override)
                        except:
                            pass
    
    # Convert results to DataFrame
    df = pd.DataFrame(results)
    
    # Save to CSV
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_path = results_dir / f"grid_results_{timestamp}.csv"
    df.to_csv(csv_path, index=False)
    
    # Sort by Sharpe/DD ratio and display top 5
    if not df.empty and "Sharpe_DD_Ratio" in df.columns:
        df_sorted = df.sort_values("Sharpe_DD_Ratio", ascending=False)
        print("\nTop 5 parameter combinations by Sharpe/DD ratio:")
        print(df_sorted.head(5).to_string())
        
        # Save sorted results
        sorted_csv_path = results_dir / f"grid_results_sorted_{timestamp}.csv"
        df_sorted.to_csv(sorted_csv_path, index=False)
        
        print("\nBest parameter combination:")
        best = df_sorted.iloc[0]
        print(f"OBI_WIN: {best['OBI_WIN']}")
        print(f"OBI_THR: {best['OBI_THR']}")
        print(f"SPD_THR: {best['SPD_THR']}")
        print(f"CONF_BARS: {best['CONF_BARS']}")
        print(f"Sharpe: {best['Sharpe']}")
        print(f"MaxDD: {best['MaxDD']}")
        print(f"Sharpe/DD Ratio: {best['Sharpe_DD_Ratio']}")
    
    print(f"\nGrid search complete. Full results available in {csv_path}")

if __name__ == "__main__":
    run_grid_search()
