#!/bin/bash
# Script to regenerate feature files for 2025-03-03 using the --overwrite flag

# Set paths
RAW_DIR="hyperliquid_data/l2_raw/20250303"
OUT_DIR="hyperliquid_data/features_1s"
DATE="2025-03-03"

echo "Regenerating feature files for $DATE..."
echo "Raw data directory: $RAW_DIR"
echo "Output directory: $OUT_DIR"

# Run ETL with --overwrite flag
python -m aerith_hyperliquid_bot.tools.etl_l20_to_1s \
    --raw-dir "$RAW_DIR" \
    --out-dir "$OUT_DIR" \
    --date "$DATE" \
    --overwrite

# Check exit status
if [ $? -eq 0 ]; then
    echo "✅ Successfully regenerated feature files for $DATE"
else
    echo "❌ Failed to regenerate feature files for $DATE"
    exit 1
fi

# Run smoke test
echo "Running smoke test for TF-v3 on $DATE..."
python -m aerith_hyperliquid_bot.scripts.smoke_test_tf_v3 \
    --start "$DATE" \
    --end "$DATE" \
    --config "aerith_hyperliquid_bot/configs/base.yaml" \
    --feature-dir "$OUT_DIR" \
    --ohlc-dir "hyperliquid_data/resampled_l2" \
    --output "results/tf3_smoke_20250303.json"

# Check exit status
if [ $? -eq 0 ]; then
    echo "✅ Smoke test passed! TF-v3 executed at least one trade."
else
    echo "❌ Smoke test failed! TF-v3 did not execute any trades."
    exit 1
fi

echo "Task T-111-rescue completed successfully."
