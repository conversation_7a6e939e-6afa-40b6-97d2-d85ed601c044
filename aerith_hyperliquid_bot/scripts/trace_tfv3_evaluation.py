#!/usr/bin/env python3
"""
Trace TF-v3 Evaluation
======================

Trace exactly what happens during TF-v3 evaluate_entry calls.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime
from hyperliquid_bot.config.settings import load_config

# Capture all evaluate_entry calls
evaluate_calls = []

def trace_evaluate_entry(original_func):
    """Wrapper to trace evaluate_entry calls"""
    def wrapper(self, signals, regime):
        # Capture the call
        call_info = {
            'timestamp': signals.get('timestamp', 'unknown'),
            'regime': regime,
            'signals': {k: v for k, v in signals.items() if k != 'timestamp'},
            'result': None,
            'error': None
        }
        
        try:
            # Call original
            result = original_func(self, signals, regime)
            call_info['result'] = result
            
            # Log the call
            print(f"\n{'='*60}")
            print(f"evaluate_entry called at {call_info['timestamp']}")
            print(f"Regime: {regime}")
            print(f"Signals keys: {list(signals.keys())}")
            
            # Check regime_features
            regime_features = signals.get('regime_features', {})
            if regime_features:
                print(f"Regime features: {list(regime_features.keys())}")
                print(f"  - confidence: {regime_features.get('current_confidence', 'N/A')}")
                print(f"  - state_persistence: {regime_features.get('state_persistence', 'N/A')}")
                print(f"  - recent_transitions: {regime_features.get('recent_transitions', 'N/A')}")
            else:
                print("⚠️  NO regime_features in signals!")
            
            print(f"Result: {result}")
            print('='*60)
            
        except Exception as e:
            call_info['error'] = str(e)
            print(f"\n❌ ERROR in evaluate_entry: {e}")
            result = None
            
        evaluate_calls.append(call_info)
        return result
    
    return wrapper

def main():
    print("=== Tracing TF-v3 Evaluation ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Patch BEFORE importing strategies
    # First, check which TF-v3 is being used
    import hyperliquid_bot.strategies.tf_v3 as tf_v3_module
    
    # Check if it's using modern or legacy
    if hasattr(tf_v3_module.TFV3Strategy, 'evaluate'):
        print("Using legacy TFV3Strategy with evaluate() method")
        original_evaluate = tf_v3_module.TFV3Strategy.evaluate
        tf_v3_module.TFV3Strategy.evaluate = trace_evaluate_entry(original_evaluate)
    else:
        print("Legacy TFV3Strategy doesn't have expected methods")
        
    # Also patch modern if it exists
    try:
        from hyperliquid_bot.modern.tf_v3_modern import ModernTFV3Strategy
        if hasattr(ModernTFV3Strategy, 'evaluate_entry'):
            print("Also patching ModernTFV3Strategy")
            original_modern = ModernTFV3Strategy.evaluate_entry
            ModernTFV3Strategy.evaluate_entry = trace_evaluate_entry(original_modern)
    except ImportError:
        print("ModernTFV3Strategy not found")
    
    # Now import backtester (which will use patched version)
    from hyperliquid_bot.backtester.backtester import Backtester
    
    # Run short backtest
    start_date = datetime(2024, 1, 15, 15)  # Start at 15:00 when we have bear trend
    end_date = datetime(2024, 1, 15, 20)    # Just a few hours
    
    print(f"Running backtest from {start_date} to {end_date}...")
    
    try:
        backtester = Backtester(config)
        backtester.run(start_date, end_date)
        
        print(f"\n\n📊 SUMMARY OF EVALUATE_ENTRY CALLS:")
        print(f"Total calls: {len(evaluate_calls)}")
        
        if len(evaluate_calls) == 0:
            print("\n❌ NO CALLS TO EVALUATE_ENTRY!")
            print("This means the strategy is not being invoked at all.")
        else:
            print("\nCall details:")
            for i, call in enumerate(evaluate_calls):
                print(f"\n{i+1}. Time: {call['timestamp']}, Regime: {call['regime']}")
                if call['error']:
                    print(f"   ERROR: {call['error']}")
                elif call['result']:
                    print(f"   ✅ Generated signal: {call['result'].get('direction', 'unknown')}")
                else:
                    print(f"   ❌ No signal generated")
                    
                # Check why no signal
                if not call['result'] and not call['error']:
                    regime_features = call['signals'].get('regime_features', {})
                    if not regime_features:
                        print(f"      Reason: No regime_features")
                    elif regime_features.get('state_persistence', 0) < 0.5:
                        print(f"      Reason: Low state_persistence ({regime_features.get('state_persistence', 0)})")
                    elif regime_features.get('recent_transitions', 0) > 5:
                        print(f"      Reason: Too many transitions ({regime_features.get('recent_transitions', 0)})")
                    else:
                        print(f"      Reason: Unknown (needs investigation)")
        
    except Exception as e:
        print(f"❌ Backtest error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()