#!/usr/bin/env python3
"""
Fix Duplicate Timestamps in Data Loader
=======================================

Create a patched version of data loading that handles duplicates.
"""

import pandas as pd
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)


def safe_concat_dataframes(dfs: List[pd.DataFrame], 
                          timestamp_col: str = 'timestamp') -> pd.DataFrame:
    """
    Safely concatenate dataframes, handling duplicate timestamps.
    
    Args:
        dfs: List of dataframes to concatenate
        timestamp_col: Name of timestamp column
        
    Returns:
        Concatenated dataframe with duplicates handled
    """
    if not dfs:
        return pd.DataFrame()
    
    # Concatenate all dataframes
    combined = pd.concat(dfs, ignore_index=True)
    
    # If timestamp column exists, handle duplicates
    if timestamp_col in combined.columns:
        # Convert to datetime if not already
        if not pd.api.types.is_datetime64_any_dtype(combined[timestamp_col]):
            combined[timestamp_col] = pd.to_datetime(combined[timestamp_col])
        
        # Check for duplicates
        duplicates = combined.duplicated(subset=[timestamp_col], keep=False)
        dup_count = duplicates.sum()
        
        if dup_count > 0:
            logger.warning(f"Found {dup_count} duplicate timestamps, keeping last occurrence")
            
            # Group by timestamp and keep the last occurrence
            # This assumes the last loaded data is most recent/correct
            combined = combined.drop_duplicates(subset=[timestamp_col], keep='last')
            
            # Sort by timestamp to ensure chronological order
            combined = combined.sort_values(timestamp_col).reset_index(drop=True)
    
    return combined


def safe_resample_with_duplicates(df: pd.DataFrame, freq: str = '1h') -> pd.DataFrame:
    """
    Safely resample data, handling duplicate timestamps first.
    
    Args:
        df: Input dataframe with potential duplicates
        freq: Resampling frequency
        
    Returns:
        Resampled dataframe
    """
    if df.empty:
        return df
    
    # First handle any duplicate timestamps
    if 'timestamp' in df.columns:
        # Set timestamp as index
        df = df.set_index('timestamp')
        
        # Check for duplicate index values
        if df.index.duplicated().any():
            logger.warning(f"Found {df.index.duplicated().sum()} duplicate timestamps before resampling")
            
            # Group by index and aggregate duplicates
            # Use mean for numeric columns, last for others
            numeric_cols = df.select_dtypes(include='number').columns
            agg_dict = {col: 'mean' for col in numeric_cols}
            
            # Group and aggregate
            df = df.groupby(level=0).agg(agg_dict)
    
    # Now resample safely
    resampled = df.resample(freq, label='right', closed='left').agg({
        # Define aggregation for each column type
        col: 'mean' if col in df.select_dtypes(include='number').columns else 'last'
        for col in df.columns
    })
    
    return resampled


def patch_modern_data_loader():
    """
    Monkey patch the modern data loader to handle duplicates.
    """
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    from hyperliquid_bot.modern import data_loader
    
    # Store original methods
    original_load_feature_data = data_loader.ModernDataLoader._load_feature_data
    
    def patched_load_feature_data(self, start_date, end_date):
        """Patched version that handles duplicates."""
        all_data = []
        
        # Call original method logic but use safe concat
        from datetime import timedelta
        
        current_date = start_date.date()
        end_date_only = end_date.date()
        
        while current_date <= end_date_only:
            date_dir = self.feature_base_path / current_date.strftime('%Y-%m-%d')
            
            if date_dir.exists():
                # Load all hourly files for this date
                for hour_file in sorted(date_dir.glob('features_*.parquet')):
                    try:
                        df = pd.read_parquet(hour_file)
                        
                        if not df.empty:
                            # Ensure timestamp column
                            if 'timestamp' in df.columns:
                                df['timestamp'] = pd.to_datetime(df['timestamp'])
                            
                            all_data.append(df)
                            self.logger.debug(f"Loaded feature file: {hour_file}")
                    
                    except Exception as e:
                        self.logger.error(f"Error loading {hour_file}: {e}")
                        continue
            
            current_date += timedelta(days=1)
        
        if not all_data:
            self.logger.warning(f"No feature data found between {start_date} and {end_date}")
            return pd.DataFrame()
        
        # Use safe concatenation
        combined = safe_concat_dataframes(all_data)
        
        # Filter by time range
        if 'timestamp' in combined.columns:
            mask = (combined['timestamp'] >= start_date) & (combined['timestamp'] < end_date)
            combined = combined[mask]
        
        self.logger.info(f"Loaded {len(combined)} rows of feature data")
        return combined
    
    # Apply patches
    data_loader.ModernDataLoader._load_feature_data = patched_load_feature_data
    
    print("✅ Applied duplicate timestamp fixes to ModernDataLoader")


if __name__ == "__main__":
    print("This module provides functions to handle duplicate timestamps.")
    print("Import and use in your data loading pipeline.")