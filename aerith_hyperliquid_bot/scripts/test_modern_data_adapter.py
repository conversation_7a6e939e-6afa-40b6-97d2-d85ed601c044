#!/usr/bin/env python3
"""
Test Modern Data Adapter
========================

This script tests the ModernDataAdapter with real features_1s data
to ensure field mappings and transformations work correctly.
"""

import pandas as pd
import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.modern.contracts.data_schema import ModernDataContract
from hyperliquid_bot.modern.adapters.data_adapter import ModernDataAdapter, AdapterConfig


def test_adapter():
    """Test the modern data adapter with real data."""
    print("=== Testing Modern Data Adapter ===\n")
    
    # Load sample data
    sample_file = "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s/2024-06-01/features_12.parquet"
    print(f"Loading sample data from: {sample_file}")
    
    df_raw = pd.read_parquet(sample_file)
    print(f"Raw data shape: {df_raw.shape}")
    print(f"Raw columns: {sorted(df_raw.columns)[:10]}... (showing first 10)")
    
    # Initialize adapter
    config = AdapterConfig(
        log_transformations=True,
        compute_derived_fields=True
    )
    adapter = ModernDataAdapter(config)
    
    # Test 1: Transform full dataframe
    print("\n--- Test 1: Transform Full DataFrame ---")
    df_adapted = adapter.adapt_features_dataframe(df_raw)
    
    # Check if volume_imbalance was mapped
    if 'volume_imbalance' in df_adapted.columns:
        print("✓ volume_imbalance field created successfully")
        # Check values
        sample_values = df_adapted['volume_imbalance'].head()
        print(f"  Sample values: {sample_values.tolist()}")
    else:
        print("✗ volume_imbalance field NOT created")
    
    # Verify all required fields
    contract = ModernDataContract()
    required_fields = contract.get_required_fields()
    missing_fields = []
    
    print("\n--- Required Fields Check ---")
    for field in required_fields:
        if field in df_adapted.columns:
            print(f"✓ {field}")
        else:
            print(f"✗ {field} - MISSING")
            missing_fields.append(field)
    
    # Test 2: Transform single row to signals dict
    print("\n--- Test 2: Transform Single Row ---")
    sample_row = df_raw.iloc[100]
    signals = adapter.adapt_signals_dict(sample_row.to_dict())
    
    print("Adapted signals sample:")
    for key in ['timestamp', 'close', 'volume_imbalance', 'atr_percent_sec', 'ma_slope_ema_30s']:
        if key in signals:
            print(f"  {key}: {signals[key]}")
        else:
            print(f"  {key}: MISSING")
    
    # Test 3: Check statistics
    print("\n--- Adapter Statistics ---")
    stats = adapter.get_adapter_statistics()
    for key, value in stats.items():
        if isinstance(value, dict):
            print(f"{key}:")
            for k, v in value.items():
                print(f"  {k}: {v}")
        else:
            print(f"{key}: {value}")
    
    # Test 4: Validate adapted data
    print("\n--- Data Validation ---")
    validation_result = contract.validate_dataframe(df_adapted)
    print(f"Valid: {validation_result['valid']}")
    if validation_result['missing_required']:
        print(f"Missing required fields: {validation_result['missing_required']}")
    if validation_result['warnings']:
        print("Warnings:")
        for warning in validation_result['warnings']:
            print(f"  - {warning}")
    
    # Test 5: Check NaN handling
    print("\n--- NaN Handling Test ---")
    null_counts = df_adapted[required_fields].isnull().sum()
    fields_with_nulls = null_counts[null_counts > 0]
    if len(fields_with_nulls) > 0:
        print("Fields with remaining nulls:")
        for field, count in fields_with_nulls.items():
            pct = (count / len(df_adapted)) * 100
            print(f"  {field}: {count} ({pct:.2f}%)")
    else:
        print("✓ No nulls in required fields")
    
    # Summary
    print("\n=== Summary ===")
    if validation_result['valid'] and len(missing_fields) == 0:
        print("✓ Adapter working correctly - all tests passed!")
    else:
        print("✗ Adapter has issues that need fixing")
        if missing_fields:
            print(f"  Missing fields: {missing_fields}")
    
    return validation_result['valid']


def test_detector_compatibility():
    """Test if adapted data works with ModernContinuousDetector."""
    print("\n\n=== Testing Detector Compatibility ===\n")
    
    # This would import and test with the actual detector
    # For now, just verify the signal format
    
    sample_file = "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s/2024-06-01/features_12.parquet"
    df_raw = pd.read_parquet(sample_file)
    
    adapter = ModernDataAdapter()
    
    # Get a sample row and adapt it
    sample_row = df_raw.iloc[1000]
    signals = adapter.adapt_signals_dict(sample_row.to_dict())
    
    # Check detector expected fields
    detector_required = [
        'timestamp', 'close', 'volume_imbalance', 
        'spread_mean', 'spread_std', 'atr_percent_sec', 
        'ma_slope_ema_30s'
    ]
    
    print("Detector required fields check:")
    all_present = True
    for field in detector_required:
        if field in signals and signals[field] is not None:
            print(f"✓ {field}: {type(signals[field]).__name__}")
        else:
            print(f"✗ {field}: MISSING or None")
            all_present = False
    
    if all_present:
        print("\n✓ Adapted data is compatible with detector!")
    else:
        print("\n✗ Adapted data missing required detector fields")
    
    return all_present


if __name__ == "__main__":
    # Run tests
    adapter_valid = test_adapter()
    detector_compatible = test_detector_compatibility()
    
    # Exit code
    if adapter_valid and detector_compatible:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed")
        sys.exit(1)