#!/usr/bin/env python3
"""
Debug script to verify data loading and field mapping issues.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
from hyperliquid_bot.modern.data_loader import ModernDataLoader
from hyperliquid_bot.config.settings import load_config
import pandas as pd

def main():
    print("=== Modern Data Loading Debug ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Initialize loader
    loader = ModernDataLoader(config)
    
    # Test 1: Load OHLCV data
    print("1. Testing OHLCV data loading:")
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 2)
    
    ohlcv_data = loader._load_ohlcv_data(start_date, end_date)
    print(f"   - OHLCV shape: {ohlcv_data.shape}")
    print(f"   - OHLCV columns: {list(ohlcv_data.columns)}")
    if not ohlcv_data.empty:
        print(f"   - Sample row:\n{ohlcv_data.iloc[0]}")
    print()
    
    # Test 2: Load features_1s data
    print("2. Testing features_1s data loading:")
    features_data = loader._load_feature_data(start_date, end_date)
    print(f"   - Features shape: {features_data.shape}")
    print(f"   - Features columns (first 20): {list(features_data.columns)[:20]}")
    
    # Check for key fields
    key_fields = ['volume_imbalance', 'obi_smoothed', 'spread_mean', 'spread_std', 'atr_14_sec']
    print("\n   - Key field presence:")
    for field in key_fields:
        present = field in features_data.columns
        print(f"     * {field}: {'✓' if present else '✗'}")
    print()
    
    # Test 3: Load merged data
    print("3. Testing merged data loading:")
    merged_data = loader.load_data(start_date, end_date)
    print(f"   - Merged shape: {merged_data.shape}")
    print(f"   - Has volume_imbalance: {'volume_imbalance' in merged_data.columns}")
    print(f"   - Has spread_mean: {'spread_mean' in merged_data.columns}")
    print()
    
    # Test 4: Check hourly resampling
    print("4. Testing hourly resampling of features:")
    if not features_data.empty:
        # Simple hourly resample
        hourly_features = features_data.resample('1h', label='right', closed='left').agg({
            'close': 'last',
            'volume': 'sum',
            'volume_imbalance': 'mean',
            'spread_mean': 'mean',
            'spread_std': 'mean',
            'atr_14_sec': 'last'
        }).dropna()
        
        print(f"   - Hourly shape: {hourly_features.shape}")
        print(f"   - Hourly columns: {list(hourly_features.columns)}")
        if not hourly_features.empty:
            print(f"\n   - Sample hourly row:\n{hourly_features.iloc[0]}")
    
    print("\n=== Analysis Complete ===")
    print("\nKey Findings:")
    print("- OHLCV data has basic price/volume fields only")
    print("- Features_1s has microstructure fields")
    print("- Field mapping IS working (obi_smoothed → volume_imbalance)")
    print("- But hourly bars for TF-v3 need feature aggregation!")

if __name__ == "__main__":
    main()