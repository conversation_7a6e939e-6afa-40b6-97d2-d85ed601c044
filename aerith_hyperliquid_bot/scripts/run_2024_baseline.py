#!/usr/bin/env python3
"""
Run 2024 Baseline Test with Comprehensive Metrics
=================================================

This script runs a full 2024 backtest to establish baseline performance.
Includes all essential metrics formatted nicely.
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import time
import logging
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_backtest_engine import RobustBacktestEngine


# ANSI color codes
GREEN = '\033[92m'
RED = '\033[91m'
YELLOW = '\033[93m'
BLUE = '\033[94m'
CYAN = '\033[96m'
BOLD = '\033[1m'
ENDC = '\033[0m'


def setup_logging():
    """Configure logging for the test."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('baseline_2024_test.log')
        ]
    )


def print_divider(title="", char="═", width=80):
    """Print a formatted divider."""
    if title:
        padding = (width - len(title) - 2) // 2
        print(f"\n{BOLD}{char * padding} {title} {char * padding}{ENDC}")
    else:
        print(f"\n{BOLD}{char * width}{ENDC}")


def format_pct(value, good_threshold=0):
    """Format percentage with color."""
    color = GREEN if value >= good_threshold else RED
    return f"{color}{value:>8.2f}%{ENDC}"


def format_money(value):
    """Format money value with color."""
    color = GREEN if value >= 0 else RED
    return f"{color}${value:>12,.2f}{ENDC}"


def format_ratio(value, good_threshold=1.0):
    """Format ratio with color."""
    color = GREEN if value >= good_threshold else YELLOW if value >= 0.8 else RED
    return f"{color}{value:>8.2f}{ENDC}"


def calculate_additional_metrics(trades_df):
    """Calculate additional performance metrics."""
    if trades_df.empty:
        return {}
    
    # Calculate Sortino ratio (downside deviation)
    negative_returns = trades_df[trades_df['pnl_pct'] < 0]['pnl_pct']
    downside_std = negative_returns.std() if len(negative_returns) > 0 else 0
    sortino = np.sqrt(252) * (trades_df['pnl_pct'].mean() / downside_std) if downside_std > 0 else 0
    
    # Calculate profit factor
    gross_profit = trades_df[trades_df['pnl_pct'] > 0]['pnl_pct'].sum()
    gross_loss = abs(trades_df[trades_df['pnl_pct'] < 0]['pnl_pct'].sum())
    profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
    
    # Calculate win/loss ratio
    avg_win = trades_df[trades_df['pnl_pct'] > 0]['pnl_pct'].mean() if len(trades_df[trades_df['pnl_pct'] > 0]) > 0 else 0
    avg_loss = abs(trades_df[trades_df['pnl_pct'] < 0]['pnl_pct'].mean()) if len(trades_df[trades_df['pnl_pct'] < 0]) > 0 else 0
    win_loss_ratio = avg_win / avg_loss if avg_loss > 0 else float('inf')
    
    # Calculate streaks
    wins = (trades_df['pnl_pct'] > 0).astype(int)
    win_streak = wins.groupby((wins != wins.shift()).cumsum()).sum().max()
    
    losses = (trades_df['pnl_pct'] < 0).astype(int)
    loss_streak = losses.groupby((losses != losses.shift()).cumsum()).sum().max()
    
    return {
        'sortino_ratio': sortino,
        'profit_factor': profit_factor,
        'win_loss_ratio': win_loss_ratio,
        'win_streak': win_streak,
        'loss_streak': loss_streak,
        'avg_win': avg_win,
        'avg_loss': avg_loss
    }


def print_performance_summary(results):
    """Print comprehensive performance summary."""
    # Extract metrics
    total_trades = results.get('total_trades', 0)
    if total_trades == 0:
        print(f"\n{YELLOW}No trades were executed during this backtest period.{ENDC}")
        return
    
    trades_df = pd.DataFrame(results.get('trades', []))
    additional_metrics = calculate_additional_metrics(trades_df)
    
    # Overall Performance
    print_divider("OVERALL PERFORMANCE", "═")
    print(f"  • {'Period:':<25} {results['period']['start'][:10]} to {results['period']['end'][:10]}")
    print(f"  • {'Initial Balance:':<25} ${'10,000.00':>12}")
    
    total_return = results.get('total_return', 0)
    final_balance = 10000 * (1 + total_return)
    print(f"  • {'Final Balance:':<25} {format_money(final_balance)}")
    print(f"  • {'Net Profit (P&L):':<25} {format_money(final_balance - 10000)}")
    print(f"  • {'Return on Initial (ROI):':<25} {format_pct(total_return * 100)}")
    print(f"  • {'Annual Return:':<25} {format_pct(total_return * 100)}")  # Simplified for 1 year
    
    # Risk & Drawdown
    print_divider("RISK & DRAWDOWN", "═")
    print(f"  • {'Max Drawdown:':<25} {format_pct(results.get('max_drawdown', 0) * 100, -10)}")
    print(f"  • {'Sharpe Ratio (Daily):':<25} {format_ratio(results.get('sharpe_ratio', 0), 1.0)}")
    print(f"  • {'Sortino Ratio (Daily):':<25} {format_ratio(additional_metrics.get('sortino_ratio', 0), 1.5)}")
    print(f"  • {'Profit Factor:':<25} {format_ratio(additional_metrics.get('profit_factor', 0), 1.5)}")
    
    # Trade Statistics
    print_divider("TRADE STATISTICS", "═")
    print(f"  • {'Total Trades:':<25} {BOLD}{total_trades:>12}{ENDC}")
    print(f"  • {'Win Rate:':<25} {format_pct(results.get('win_rate', 0) * 100, 50)}")
    print(f"  • {'Avg Trade P&L:':<25} {format_pct(results.get('total_return', 0) / total_trades * 100 if total_trades > 0 else 0)}")
    print(f"  • {'Avg Win / Avg Loss:':<25} {format_pct(additional_metrics.get('avg_win', 0) * 100)} / {format_pct(-additional_metrics.get('avg_loss', 0) * 100)}")
    print(f"  • {'Win/Loss Ratio:':<25} {format_ratio(additional_metrics.get('win_loss_ratio', 0), 1.5)}")
    print(f"  • {'Winning Streak:':<25} {additional_metrics.get('win_streak', 0):>12}")
    print(f"  • {'Losing Streak:':<25} {additional_metrics.get('loss_streak', 0):>12}")
    
    # Direction Analysis
    long_trades = [t for t in results.get('trades', []) if t.get('direction') == 'long']
    short_trades = [t for t in results.get('trades', []) if t.get('direction') == 'short']
    print(f"  • {'Longs (pct):':<25} {len(long_trades) / total_trades * 100:>11.1f} %")
    print(f"  • {'Shorts (pct):':<25} {len(short_trades) / total_trades * 100:>11.1f} %")
    
    # Exit Reason Analysis
    print_divider("EXIT REASON ANALYSIS", "═")
    print(f"  {BOLD}{'Exit Reason':<20} {'Count':>8} {'Total P&L':>16} {'Avg P&L':>16}{ENDC}")
    print(f"  {'-'*20:<20} {'-'*8:>8} {'-'*16:>16} {'-'*16:>16}")
    
    exit_reasons = {}
    for trade in results.get('trades', []):
        reason = trade.get('exit_reason', 'unknown')
        if reason not in exit_reasons:
            exit_reasons[reason] = {'count': 0, 'pnl': 0}
        exit_reasons[reason]['count'] += 1
        exit_reasons[reason]['pnl'] += trade.get('pnl_pct', 0)
    
    for reason, stats in sorted(exit_reasons.items()):
        avg_pnl = stats['pnl'] / stats['count'] if stats['count'] > 0 else 0
        total_pnl_color = GREEN if stats['pnl'] > 0 else RED
        avg_pnl_color = GREEN if avg_pnl > 0 else RED
        print(f"  {reason:<20} {stats['count']:>8} {total_pnl_color}{stats['pnl']*100:>15.2f}%{ENDC} {avg_pnl_color}{avg_pnl*100:>15.2f}%{ENDC}")
    
    # Regime Analysis
    print_divider("REGIME TRADING ANALYSIS", "═")
    print(f"  {BOLD}{'Regime':<20} {'Trades':>8} {'Win Rate':>16} {'Avg P&L':>16}{ENDC}")
    print(f"  {'-'*20:<20} {'-'*8:>8} {'-'*16:>16} {'-'*16:>16}")
    
    regime_stats = {}
    for trade in results.get('trades', []):
        regime = trade.get('entry_regime', 'Unknown')
        if regime not in regime_stats:
            regime_stats[regime] = {'count': 0, 'wins': 0, 'pnl': 0}
        regime_stats[regime]['count'] += 1
        if trade.get('pnl_pct', 0) > 0:
            regime_stats[regime]['wins'] += 1
        regime_stats[regime]['pnl'] += trade.get('pnl_pct', 0)
    
    for regime, stats in sorted(regime_stats.items()):
        win_rate = stats['wins'] / stats['count'] * 100 if stats['count'] > 0 else 0
        avg_pnl = stats['pnl'] / stats['count'] * 100 if stats['count'] > 0 else 0
        wr_color = GREEN if win_rate >= 50 else YELLOW if win_rate >= 40 else RED
        pnl_color = GREEN if avg_pnl > 0 else RED
        print(f"  {regime:<20} {stats['count']:>8} {wr_color}{win_rate:>15.1f}%{ENDC} {pnl_color}{avg_pnl:>15.2f}%{ENDC}")
    
    # Regime Source Breakdown
    regime_sources = results.get('regime_sources', {})
    print_divider("REGIME SOURCE BREAKDOWN", "═")
    total_regimes = regime_sources.get('total', 0)
    if total_regimes > 0:
        print(f"  • {'Pre-computed cache:':<25} {regime_sources.get('cache_used', 0):>8} ({regime_sources.get('cache_used', 0)/total_regimes*100:>6.1f}%)")
        print(f"  • {'Detector calculated:':<25} {regime_sources.get('detector_used', 0):>8} ({regime_sources.get('detector_used', 0)/total_regimes*100:>6.1f}%)")
        print(f"  • {'Price-based fallback:':<25} {regime_sources.get('fallback_used', 0):>8} ({regime_sources.get('fallback_used', 0)/total_regimes*100:>6.1f}%)")
        print(f"  • {'Total regime updates:':<25} {total_regimes:>8}")
    
    # Quality Filter Stats (if available)
    if hasattr(results, 'quality_stats'):
        print_divider("QUALITY FILTERING STATS", "═")
        stats = results.quality_stats
        print(f"  • {'Total regime detections:':<25} {stats.get('total', 0):>8}")
        print(f"  • {'High quality regimes:':<25} {stats.get('high_quality', 0):>8}")
        print(f"  • {'Low quality filtered:':<25} {stats.get('low_quality', 0):>8}")
        print(f"  • {'Filter rate:':<25} {stats.get('filter_rate', 0):>7.1f}%")
    
    print_divider("", "═")


def generate_equity_curve(results, output_dir):
    """Generate and save equity curve plot."""
    trades = results.get('trades', [])
    if not trades:
        return
    
    # Calculate cumulative returns
    dates = []
    equity = [10000]  # Start with initial balance
    
    for trade in trades:
        if 'exit_time' in trade:
            dates.append(pd.to_datetime(trade['exit_time']))
            new_equity = equity[-1] * (1 + trade.get('pnl_pct', 0))
            equity.append(new_equity)
    
    if len(dates) < 2:
        return
    
    # Create plot
    plt.figure(figsize=(12, 6))
    plt.plot(dates, equity[1:], linewidth=2, color='blue')
    
    # Add horizontal line at initial balance
    plt.axhline(y=10000, color='gray', linestyle='--', alpha=0.5)
    
    # Formatting
    plt.title('Equity Curve - 2024 Baseline', fontsize=16, fontweight='bold')
    plt.xlabel('Date', fontsize=12)
    plt.ylabel('Account Value ($)', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # Format y-axis as currency
    ax = plt.gca()
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
    
    # Rotate x-axis labels
    plt.xticks(rotation=45)
    
    # Add performance text
    final_value = equity[-1]
    total_return = (final_value - 10000) / 10000 * 100
    plt.text(0.02, 0.98, f'Final: ${final_value:,.2f} ({total_return:+.1f}%)', 
             transform=ax.transAxes, fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    
    # Save plot
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'equity_curve_baseline_{timestamp}.png'
    filepath = output_dir / filename
    plt.tight_layout()
    plt.savefig(filepath, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"\nEquity curve saved to: {filepath}")


def run_baseline_test():
    """Run 2024 baseline test."""
    logger = logging.getLogger("BaselineTest")
    
    # Test configuration
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 12, 31)
    
    print_divider("2024 BASELINE TEST - ENHANCED DETECTOR (NO CACHE)", "═")
    print(f"\nPeriod: {start_date} to {end_date}")
    print("Configuration: Enhanced detector with quality filtering")
    print("Cache: DISABLED - Testing real-world performance\n")
    
    # Load configuration
    config_path = project_root / "configs" / "overrides" / "modern_system_v2_complete.yaml"
    config = load_config(config_path=str(config_path))
    
    # Force enhanced detector
    config.regime.detector_type = "enhanced"
    
    # Create output directory
    output_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    output_dir.mkdir(exist_ok=True)
    
    # Create backtesting engine WITHOUT cache
    logger.info("Creating backtest engine (cache disabled)...")
    engine = RobustBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=False  # No cache for baseline
    )
    
    # Run backtest and measure time
    logger.info("Starting backtest...")
    start_time = time.time()
    
    try:
        results = engine.run_backtest()
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # Print performance metrics
        print_performance_summary(results)
        
        # Performance timing
        print(f"\n{BOLD}Execution Performance:{ENDC}")
        print(f"  • Total time: {elapsed_time:.1f} seconds ({elapsed_time/60:.1f} minutes)")
        print(f"  • Hours per second: {results.get('hours_processed', 0) / elapsed_time:.1f}")
        
        # Generate equity curve
        generate_equity_curve(results, output_dir)
        
        # Save detailed results
        results_file = output_dir / f"baseline_2024_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            # Convert datetime objects to strings for JSON
            json_results = {
                'config': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'cache_enabled': False,
                    'detector_type': 'enhanced'
                },
                'performance': {
                    'elapsed_seconds': elapsed_time,
                    'hours_per_second': results.get('hours_processed', 0) / elapsed_time
                },
                'results': results
            }
            json.dump(json_results, f, indent=2, default=str)
        
        print(f"\nDetailed results saved to: {results_file}")
        
    except Exception as e:
        logger.error(f"Error during backtest: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    setup_logging()
    run_baseline_test()