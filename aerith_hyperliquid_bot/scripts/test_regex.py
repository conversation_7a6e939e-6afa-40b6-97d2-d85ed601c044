#!/usr/bin/env python3
"""
Test script to verify regex patterns for extracting metrics from log files.
"""
import re
import sys
from pathlib import Path

def test_regex_patterns(log_file_path):
    """Test regex patterns on a log file."""
    print(f"Testing regex patterns on: {log_file_path}")
    
    with open(log_file_path, 'r') as f:
        log_content = f.read()
    
    # Test pattern for Sharpe ratio with the bullet point character
    sharpe_pattern = r'• Sharpe Ratio \(Daily\):\s+(\d+\.\d+)'
    sharpe_match = re.search(sharpe_pattern, log_content)
    
    if sharpe_match:
        print(f"Found Sharpe ratio: {sharpe_match.group(1)}")
    else:
        print("Did not find Sharpe ratio")
    
    # Test pattern for Max Drawdown with the bullet point character
    dd_pattern = r'• Max Drawdown:\s+(\d+\.\d+)%'
    dd_match = re.search(dd_pattern, log_content)
    
    if dd_match:
        print(f"Found Max Drawdown: {dd_match.group(1)}%")
    else:
        print("Did not find Max Drawdown")
    
    # Test alternative pattern for Sharpe ratio (with ASCII bullet)
    sharpe_pattern_alt = r'[•\*] Sharpe Ratio \(Daily\):\s+(\d+\.\d+)'
    sharpe_match_alt = re.search(sharpe_pattern_alt, log_content)
    
    if sharpe_match_alt:
        print(f"Alternative pattern found Sharpe ratio: {sharpe_match_alt.group(1)}")
    else:
        print("Alternative pattern did not find Sharpe ratio")
    
    # Test alternative pattern for Max Drawdown (with ASCII bullet)
    dd_pattern_alt = r'[•\*] Max Drawdown:\s+(\d+\.\d+)%'
    dd_match_alt = re.search(dd_pattern_alt, log_content)
    
    if dd_match_alt:
        print(f"Alternative pattern found Max Drawdown: {dd_match_alt.group(1)}%")
    else:
        print("Alternative pattern did not find Max Drawdown")
    
    # Try a more generic pattern
    generic_sharpe = r'Sharpe Ratio.*?(\d+\.\d+)'
    generic_sharpe_match = re.search(generic_sharpe, log_content)
    
    if generic_sharpe_match:
        print(f"Generic pattern found Sharpe ratio: {generic_sharpe_match.group(1)}")
    else:
        print("Generic pattern did not find Sharpe ratio")
    
    # Try a more generic pattern for Max Drawdown
    generic_dd = r'Max Drawdown.*?(\d+\.\d+)%'
    generic_dd_match = re.search(generic_dd, log_content)
    
    if generic_dd_match:
        print(f"Generic pattern found Max Drawdown: {generic_dd_match.group(1)}%")
    else:
        print("Generic pattern did not find Max Drawdown")
    
    # Print a small section of the log file to see the exact format
    print("\nExamining a section of the log file:")
    important_section = re.search(r'IMPORTANT METRICS.*?(?=\n\s*\n|\Z)', log_content, re.DOTALL)
    if important_section:
        section_text = important_section.group(0)
        print(section_text[:500])  # Print first 500 chars of the section
        
        # Print the raw representation to see any special characters
        print("\nRaw representation:")
        print(repr(section_text[:500]))
    else:
        print("Could not find IMPORTANT METRICS section")

if __name__ == "__main__":
    # Use the most recent log file
    log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    log_files = list(log_dir.glob("backtest_run_*.log"))
    
    if not log_files:
        print("No log files found")
        sys.exit(1)
    
    most_recent_log = max(log_files, key=lambda p: p.stat().st_mtime)
    test_regex_patterns(most_recent_log)
