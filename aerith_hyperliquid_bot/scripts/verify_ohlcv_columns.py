#!/usr/bin/env python3
"""
Script to verify the contents of OHLCV Parquet files with specific focus on log_ret and realised_vol columns.
"""

import pandas as pd

# --- Inspect 1h file ---
file_1h = "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled_l2/1h/2024-01-01_1h.parquet"
print(f"--- Inspecting: {file_1h} ---")
try:
    df_1h = pd.read_parquet(file_1h)
    print("Shape:", df_1h.shape)
    print("Info:")
    df_1h.info()
    print("\nHead:\n", df_1h.head())
    print("\nTail:\n", df_1h.tail())
    # Check NaN counts specifically
    print("\nNaN Counts:\n", df_1h.isna().sum())
    
    # Check log_ret calculation
    print("\nVerifying log_ret calculation...")
    if 'log_ret' in df_1h.columns:
        # Calculate log returns manually to verify
        import numpy as np
        manual_log_ret = np.log(df_1h['close'] / df_1h['close'].shift(1))
        # Compare with stored values
        comparison = pd.DataFrame({
            'stored_log_ret': df_1h['log_ret'],
            'calculated_log_ret': manual_log_ret,
            'difference': df_1h['log_ret'] - manual_log_ret
        })
        print("\nLog Return Verification (first 5 rows):")
        print(comparison.head())
        
        # Check if values match (within floating point precision)
        matching = np.isclose(df_1h['log_ret'].dropna(), manual_log_ret.dropna(), rtol=1e-10, atol=1e-10)
        match_percentage = (matching.sum() / len(matching)) * 100 if len(matching) > 0 else 0
        print(f"\nLog return values match: {match_percentage:.2f}% (excluding NaNs)")
    
    # Check realised_vol calculation
    print("\nVerifying realised_vol calculation...")
    if 'realised_vol' in df_1h.columns:
        # Calculate realized volatility manually with 20-period window
        window = 20
        if len(df_1h) >= window:
            manual_vol = df_1h['log_ret'].rolling(window=window).std()
            # Compare with stored values
            vol_comparison = pd.DataFrame({
                'stored_vol': df_1h['realised_vol'],
                'calculated_vol': manual_vol,
                'difference': df_1h['realised_vol'] - manual_vol
            })
            print("\nRealized Volatility Verification (first 5 rows with non-NaN values):")
            non_nan_idx = vol_comparison['stored_vol'].notna()
            if non_nan_idx.any():
                print(vol_comparison[non_nan_idx].head())
                
                # Check if values match (within floating point precision)
                matching_vol = np.isclose(
                    df_1h['realised_vol'].dropna(), 
                    manual_vol.loc[df_1h['realised_vol'].dropna().index], 
                    rtol=1e-10, atol=1e-10
                )
                match_percentage = (matching_vol.sum() / len(matching_vol)) * 100 if len(matching_vol) > 0 else 0
                print(f"\nRealized volatility values match: {match_percentage:.2f}% (excluding NaNs)")
            else:
                print("No non-NaN values found in realised_vol column")
        else:
            print(f"Not enough data points for {window}-period volatility calculation")
except Exception as e:
    print(f"Error reading {file_1h}: {e}")

print("\n" + "="*50 + "\n")

# --- Inspect 4h file ---
file_4h = "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled_l2/4h/2024-01-01_4h.parquet"
print(f"--- Inspecting: {file_4h} ---")
try:
    df_4h = pd.read_parquet(file_4h)
    print("Shape:", df_4h.shape)
    print("Info:")
    df_4h.info()
    print("\nHead:\n", df_4h.head())
    print("\nTail:\n", df_4h.tail())
    # Check NaN counts specifically
    print("\nNaN Counts:\n", df_4h.isna().sum())
    
    # Check log_ret calculation for 4h data
    print("\nVerifying log_ret calculation...")
    if 'log_ret' in df_4h.columns:
        # Calculate log returns manually to verify
        import numpy as np
        manual_log_ret = np.log(df_4h['close'] / df_4h['close'].shift(1))
        # Compare with stored values
        comparison = pd.DataFrame({
            'stored_log_ret': df_4h['log_ret'],
            'calculated_log_ret': manual_log_ret,
            'difference': df_4h['log_ret'] - manual_log_ret
        })
        print("\nLog Return Verification (all rows):")
        print(comparison)
        
        # Check if values match (within floating point precision)
        matching = np.isclose(df_4h['log_ret'].dropna(), manual_log_ret.dropna(), rtol=1e-10, atol=1e-10)
        match_percentage = (matching.sum() / len(matching)) * 100 if len(matching) > 0 else 0
        print(f"\nLog return values match: {match_percentage:.2f}% (excluding NaNs)")
except Exception as e:
    print(f"Error reading {file_4h}: {e}")
