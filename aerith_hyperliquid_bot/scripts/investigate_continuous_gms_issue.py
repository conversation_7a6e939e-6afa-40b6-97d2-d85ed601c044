#!/usr/bin/env python3
"""
Investigation script for continuous_gms detector CHOP over-classification issue.

This script analyzes why the continuous_gms detector is classifying ~90% of regimes
as CHOP state, preventing tf-v3 from executing trades.
"""

import sys
import os
import pandas as pd
import numpy as np
import yaml
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Try to import the modules
try:
    from hyperliquid_bot.core.config import Config
    from hyperliquid_bot.core.unified_gms_detector import UnifiedGMSDetector
    from hyperliquid_bot.utils.state_mapping import map_gms_state
    IMPORTS_AVAILABLE = True
except ImportError as e:
    print(f"Import error: {e}")
    IMPORTS_AVAILABLE = False



def load_sample_data():
    """Load a small sample of 1s features data for testing."""
    # Look for recent data files
    data_dir = project_root / "hyperliquid_data" / "features_1s"
    
    if not data_dir.exists():
        print(f"Data directory not found: {data_dir}")
        return None
    
    # Find the most recent parquet file
    parquet_files = list(data_dir.glob("*.parquet"))
    if not parquet_files:
        print(f"No parquet files found in {data_dir}")
        return None
    
    # Load the most recent file
    latest_file = sorted(parquet_files)[-1]
    print(f"Loading data from: {latest_file}")
    
    df = pd.read_parquet(latest_file)
    print(f"Loaded {len(df)} rows of data")
    print(f"Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
    
    return df

def analyze_detector_configuration():
    """Analyze the current detector configuration."""
    print("=== DETECTOR CONFIGURATION ANALYSIS ===")

    if not IMPORTS_AVAILABLE:
        print("Cannot analyze detector configuration - imports failed")
        return analyze_config_files_directly()

    try:
        # Load base configuration
        config = Config()

        print(f"regime.detector_type: {config.regime.detector_type}")
        print(f"gms.detector_type: {config.gms.detector_type}")
        print(f"gms.auto_thresholds: {getattr(config.gms, 'auto_thresholds', 'Not set')}")

        # Initialize detector
        detector = UnifiedGMSDetector(config)

        print(f"Resolved detector mode: {detector.detector_mode}")
        print(f"Data source: {detector.data_source}")
        print(f"Cadence: {detector.cadence_sec}s")
        print(f"Output format: {detector.output_format}")
        print(f"Adaptive thresholds enabled: {detector.adaptive_vol_threshold is not None}")

        # Print current thresholds
        print(f"\nCurrent thresholds:")
        for key, value in detector.thresholds.items():
            print(f"  {key}: {value}")

        return detector, config
    except Exception as e:
        print(f"Error analyzing detector configuration: {e}")
        return analyze_config_files_directly()

def analyze_config_files_directly():
    """Analyze configuration files directly when imports fail."""
    print("=== DIRECT CONFIG FILE ANALYSIS ===")

    # Load base.yaml
    base_config_path = project_root / "configs" / "base.yaml"
    if not base_config_path.exists():
        print(f"Base config not found at {base_config_path}")
        return None, None

    with open(base_config_path, 'r') as f:
        base_config = yaml.safe_load(f)

    print(f"regime.detector_type: {base_config.get('regime', {}).get('detector_type', 'Not set')}")
    print(f"gms.detector_type: {base_config.get('gms', {}).get('detector_type', 'Not set')}")
    print(f"gms.auto_thresholds: {base_config.get('gms', {}).get('auto_thresholds', 'Not set')}")

    # Analyze thresholds
    regime_config = base_config.get('regime', {})
    continuous_gms_config = regime_config.get('continuous_gms', {})

    print(f"\nContinuous GMS thresholds:")
    print(f"  gms_vol_high_thresh: {continuous_gms_config.get('gms_vol_high_thresh', 'Not set')}")
    print(f"  gms_vol_low_thresh: {continuous_gms_config.get('gms_vol_low_thresh', 'Not set')}")
    print(f"  gms_mom_strong_thresh: {continuous_gms_config.get('gms_mom_strong_thresh', 'Not set')}")
    print(f"  gms_mom_weak_thresh: {continuous_gms_config.get('gms_mom_weak_thresh', 'Not set')}")

    # Check adaptive threshold settings
    gms_config = base_config.get('gms', {})
    print(f"\nAdaptive threshold settings:")
    print(f"  auto_thresholds: {gms_config.get('auto_thresholds', 'Not set')}")
    print(f"  vol_low_pct: {gms_config.get('vol_low_pct', 'Not set')}")
    print(f"  vol_high_pct: {gms_config.get('vol_high_pct', 'Not set')}")
    print(f"  mom_low_pct: {gms_config.get('mom_low_pct', 'Not set')}")
    print(f"  mom_high_pct: {gms_config.get('mom_high_pct', 'Not set')}")

    return None, base_config

def analyze_sample_signals(detector, df):
    """Analyze a sample of signals to understand regime classification."""
    print("\n=== SIGNAL ANALYSIS ===")

    if df is None or len(df) == 0:
        print("No data available for analysis")
        return

    if not IMPORTS_AVAILABLE or detector is None:
        print("Cannot analyze signals - detector not available")
        return analyze_data_distributions(df)

    # Take a sample of recent data
    sample_size = min(100, len(df))
    sample_df = df.tail(sample_size).copy()

    print(f"Analyzing {len(sample_df)} recent signals...")

    # Required columns for GMS detector
    required_cols = ['atr_percent_sec', 'ma_slope', 'obi_5', 'spread_mean', 'spread_std']
    missing_cols = [col for col in required_cols if col not in sample_df.columns]

    if missing_cols:
        print(f"Missing required columns: {missing_cols}")
        print(f"Available columns: {list(sample_df.columns)}")
        return analyze_data_distributions(df)

    # Analyze signal distributions
    print(f"\nSignal distributions:")
    for col in required_cols:
        values = sample_df[col].dropna()
        if len(values) > 0:
            print(f"  {col}: min={values.min():.6f}, max={values.max():.6f}, "
                  f"mean={values.mean():.6f}, std={values.std():.6f}")
        else:
            print(f"  {col}: No valid values")

    # Test regime detection on sample
    regime_counts = {}
    raw_state_counts = {}

    for idx, row in sample_df.iterrows():
        signals = row.to_dict()

        try:
            result = detector.get_regime(signals)

            if detector.detector_mode == 'continuous':
                raw_state = result.get('state', 'Unknown')
                risk_suppressed = result.get('risk_suppressed', False)
            else:
                raw_state = result
                risk_suppressed = False

            # Count raw states
            raw_state_counts[raw_state] = raw_state_counts.get(raw_state, 0) + 1

            # Map to 3-state system
            mapped_state = map_gms_state(raw_state)
            regime_counts[mapped_state] = regime_counts.get(mapped_state, 0) + 1

        except Exception as e:
            print(f"Error processing signal at {row.get('timestamp', 'unknown')}: {e}")
            regime_counts['ERROR'] = regime_counts.get('ERROR', 0) + 1

    print(f"\nRaw state distribution (8-state system):")
    total_raw = sum(raw_state_counts.values())
    for state, count in sorted(raw_state_counts.items()):
        pct = (count / total_raw) * 100 if total_raw > 0 else 0
        print(f"  {state}: {count} ({pct:.1f}%)")

    print(f"\nMapped regime distribution (3-state system):")
    total_mapped = sum(regime_counts.values())
    for regime, count in sorted(regime_counts.items()):
        pct = (count / total_mapped) * 100 if total_mapped > 0 else 0
        print(f"  {regime}: {count} ({pct:.1f}%)")

    # Identify the issue
    chop_pct = (regime_counts.get('CHOP', 0) / total_mapped) * 100 if total_mapped > 0 else 0
    if chop_pct > 80:
        print(f"\n*** ISSUE IDENTIFIED: {chop_pct:.1f}% CHOP classification ***")
        analyze_chop_causes(detector, sample_df, raw_state_counts)

def analyze_data_distributions(df):
    """Analyze data distributions when detector is not available."""
    print("\n=== DATA DISTRIBUTION ANALYSIS ===")

    if df is None or len(df) == 0:
        print("No data available")
        return

    print(f"Dataset info:")
    print(f"  Total rows: {len(df)}")
    print(f"  Columns: {len(df.columns)}")
    print(f"  Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")

    # Check for key GMS columns
    key_cols = ['atr_percent_sec', 'ma_slope', 'obi_5', 'spread_mean', 'spread_std']
    available_cols = [col for col in key_cols if col in df.columns]
    missing_cols = [col for col in key_cols if col not in df.columns]

    print(f"\nKey GMS columns:")
    print(f"  Available: {available_cols}")
    print(f"  Missing: {missing_cols}")

    # Analyze available columns
    for col in available_cols:
        values = df[col].dropna()
        if len(values) > 0:
            print(f"\n{col} distribution:")
            print(f"  Count: {len(values)}")
            print(f"  Min: {values.min():.6f}")
            print(f"  Max: {values.max():.6f}")
            print(f"  Mean: {values.mean():.6f}")
            print(f"  Std: {values.std():.6f}")
            print(f"  25th percentile: {values.quantile(0.25):.6f}")
            print(f"  50th percentile: {values.quantile(0.50):.6f}")
            print(f"  75th percentile: {values.quantile(0.75):.6f}")
        else:
            print(f"\n{col}: No valid values")

def analyze_chop_causes(detector, df, raw_state_counts):
    """Analyze what's causing excessive CHOP classification."""
    print(f"\n=== CHOP CAUSE ANALYSIS ===")
    
    # Check which raw states are mapping to CHOP
    chop_causing_states = []
    for state, count in raw_state_counts.items():
        mapped = map_gms_state(state)
        if mapped == 'CHOP':
            chop_causing_states.append((state, count))
    
    print(f"Raw states mapping to CHOP:")
    for state, count in sorted(chop_causing_states, key=lambda x: x[1], reverse=True):
        print(f"  {state}: {count} occurrences")
    
    # Analyze threshold issues
    print(f"\nThreshold analysis:")
    
    # Check volatility thresholds
    atr_values = df['atr_percent_sec'].dropna()
    if len(atr_values) > 0:
        vol_low = detector.thresholds['vol_low']
        vol_high = detector.thresholds['vol_high']
        
        low_vol_count = (atr_values <= vol_low).sum()
        high_vol_count = (atr_values >= vol_high).sum()
        med_vol_count = len(atr_values) - low_vol_count - high_vol_count
        
        print(f"  Volatility classification:")
        print(f"    Low (≤{vol_low:.6f}): {low_vol_count} ({low_vol_count/len(atr_values)*100:.1f}%)")
        print(f"    Medium: {med_vol_count} ({med_vol_count/len(atr_values)*100:.1f}%)")
        print(f"    High (≥{vol_high:.6f}): {high_vol_count} ({high_vol_count/len(atr_values)*100:.1f}%)")
    
    # Check momentum thresholds
    ma_slope_values = df['ma_slope'].dropna()
    if len(ma_slope_values) > 0:
        mom_weak = detector.thresholds['mom_weak']
        mom_strong = detector.thresholds['mom_strong']
        
        abs_slopes = ma_slope_values.abs()
        weak_mom_count = (abs_slopes <= mom_weak).sum()
        strong_mom_count = (abs_slopes >= mom_strong).sum()
        med_mom_count = len(abs_slopes) - weak_mom_count - strong_mom_count
        
        print(f"  Momentum classification:")
        print(f"    Weak (≤{mom_weak:.2f}): {weak_mom_count} ({weak_mom_count/len(abs_slopes)*100:.1f}%)")
        print(f"    Medium: {med_mom_count} ({med_mom_count/len(abs_slopes)*100:.1f}%)")
        print(f"    Strong (≥{mom_strong:.2f}): {strong_mom_count} ({strong_mom_count/len(abs_slopes)*100:.1f}%)")

def main():
    """Main investigation function."""
    print("Investigating continuous_gms detector CHOP over-classification issue...")
    
    # Analyze detector configuration
    detector, config = analyze_detector_configuration()
    
    # Load and analyze sample data
    df = load_sample_data()
    analyze_sample_signals(detector, df)
    
    print(f"\n=== INVESTIGATION COMPLETE ===")

if __name__ == "__main__":
    main()
