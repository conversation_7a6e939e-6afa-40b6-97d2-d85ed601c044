#!/usr/bin/env python3
"""
Comprehensive Data Schema Discovery for features_1s
===================================================

This script analyzes features_1s parquet files to:
1. Document all available columns with data types
2. Calculate value ranges and statistics for each column
3. Identify missing vs present data
4. Check timestamp formats and frequency
5. Compare expected vs actual fields
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import json
from typing import Dict, List, Any
import warnings
warnings.filterwarnings('ignore')


class FeatureSchemaDiscovery:
    """Discover and document the actual schema of features_1s data."""
    
    # Expected fields from ModernContinuousDetector
    EXPECTED_FIELDS = {
        'core': [
            'timestamp', 'close', 'high', 'low', 'volume',
            'volume_imbalance',  # Modern expected name
            'spread_mean', 'spread_std',
            'atr_percent_sec',   # 1-second ATR
            'ma_slope_ema_30s',  # EMA-based momentum
        ],
        'optional': [
            'adx', 'funding_rate', 'atr_14_sec', 'unrealised_pnl'
        ]
    }
    
    # Possible field mappings based on analysis
    POTENTIAL_MAPPINGS = {
        'volume_imbalance': ['imbalance', 'obi_smoothed', 'raw_obi_5', 'raw_obi_20'],
        'atr_percent_sec': ['atr_percent_sec', 'atr_14_sec', 'atr_percent'],
        'ma_slope_ema_30s': ['ma_slope_ema_30s', 'ma_slope'],
    }
    
    def __init__(self, data_dir: str):
        self.data_dir = Path(data_dir)
        self.report = {
            'discovery_timestamp': datetime.now().isoformat(),
            'data_directory': str(self.data_dir),
            'files_analyzed': 0,
            'date_range': {'start': None, 'end': None},
            'columns': {},
            'field_mappings': {},
            'data_quality': {},
            'recommendations': []
        }
    
    def analyze_sample_files(self, num_samples: int = 5) -> None:
        """Analyze a sample of parquet files across different dates."""
        print(f"Discovering schema in {self.data_dir}...")
        
        # Get list of date directories
        date_dirs = sorted([d for d in self.data_dir.iterdir() if d.is_dir() and d.name.startswith('2024')])
        
        if not date_dirs:
            print("ERROR: No date directories found!")
            return
        
        # Sample evenly across available dates
        sample_indices = np.linspace(0, len(date_dirs)-1, min(num_samples, len(date_dirs)), dtype=int)
        sample_dirs = [date_dirs[i] for i in sample_indices]
        
        all_columns = set()
        column_stats = {}
        
        for date_dir in sample_dirs:
            # Get a sample file from each date
            parquet_files = list(date_dir.glob("features_*.parquet"))
            if not parquet_files:
                continue
                
            # Analyze middle file of the day (usually features_12.parquet)
            sample_file = parquet_files[min(12, len(parquet_files)-1)]
            print(f"\nAnalyzing {sample_file.parent.name}/{sample_file.name}...")
            
            df = pd.read_parquet(sample_file)
            self.report['files_analyzed'] += 1
            
            # Update date range
            date = date_dir.name
            if self.report['date_range']['start'] is None or date < self.report['date_range']['start']:
                self.report['date_range']['start'] = date
            if self.report['date_range']['end'] is None or date > self.report['date_range']['end']:
                self.report['date_range']['end'] = date
            
            # Analyze each column
            for col in df.columns:
                all_columns.add(col)
                if col not in column_stats:
                    column_stats[col] = {
                        'dtype': str(df[col].dtype),
                        'values': [],
                        'null_counts': [],
                        'sample_values': []
                    }
                
                # Collect statistics
                if df[col].dtype in ['datetime64[ns]', 'object'] or col == 'timestamp':
                    # Handle timestamp columns differently
                    column_stats[col]['values'].extend([
                        str(df[col].min()) if pd.notna(df[col].min()) else None,
                        str(df[col].max()) if pd.notna(df[col].max()) else None
                    ])
                else:
                    column_stats[col]['values'].extend([
                        float(df[col].min()) if pd.notna(df[col].min()) else None,
                        float(df[col].max()) if pd.notna(df[col].max()) else None
                    ])
                column_stats[col]['null_counts'].append(df[col].isna().sum())
                
                # Collect sample values (first 5 non-null)
                sample_vals = df[col].dropna().head(5).tolist()
                if sample_vals and len(column_stats[col]['sample_values']) < 5:
                    column_stats[col]['sample_values'] = sample_vals
        
        # Process collected statistics
        self._process_column_stats(column_stats)
        
        # Analyze field mappings
        self._analyze_field_mappings(all_columns)
        
        # Check data quality
        self._check_data_quality(sample_dirs)
        
        # Generate recommendations
        self._generate_recommendations()
    
    def _process_column_stats(self, column_stats: Dict[str, Any]) -> None:
        """Process collected column statistics."""
        for col, stats in column_stats.items():
            # Calculate ranges
            values = [v for v in stats['values'] if v is not None]
            
            # Handle different data types
            if values and isinstance(values[0], str):
                value_range = {
                    'min': values[0] if values else None,
                    'max': values[-1] if values else None
                }
            else:
                value_range = {
                    'min': min(values) if values else None,
                    'max': max(values) if values else None
                }
            
            self.report['columns'][col] = {
                'dtype': stats['dtype'],
                'always_present': all(n == 0 for n in stats['null_counts']),
                'null_percentage': np.mean(stats['null_counts']) / 3600 * 100,  # Assuming 3600 rows per hour
                'value_range': value_range,
                'sample_values': stats['sample_values'][:5]
            }
    
    def _analyze_field_mappings(self, actual_columns: set) -> None:
        """Analyze mapping between expected and actual fields."""
        print("\n=== Field Mapping Analysis ===")
        
        # Check expected core fields
        for expected in self.EXPECTED_FIELDS['core']:
            if expected in actual_columns:
                self.report['field_mappings'][expected] = {
                    'status': 'FOUND',
                    'actual_name': expected
                }
                print(f"✓ {expected}: Found")
            else:
                # Look for potential mappings
                candidates = []
                for candidate in self.POTENTIAL_MAPPINGS.get(expected, []):
                    if candidate in actual_columns:
                        candidates.append(candidate)
                
                if candidates:
                    # Prefer exact substring matches
                    best_match = None
                    for c in candidates:
                        if expected.replace('_', '') in c.replace('_', ''):
                            best_match = c
                            break
                    if not best_match:
                        best_match = candidates[0]
                    
                    self.report['field_mappings'][expected] = {
                        'status': 'MAPPED',
                        'actual_name': best_match,
                        'alternatives': candidates
                    }
                    print(f"✗ {expected}: Missing, mapped to '{best_match}'")
                else:
                    self.report['field_mappings'][expected] = {
                        'status': 'MISSING',
                        'actual_name': None
                    }
                    print(f"✗ {expected}: MISSING - No suitable mapping found")
    
    def _check_data_quality(self, sample_dirs: List[Path]) -> None:
        """Check data quality issues."""
        print("\n=== Data Quality Check ===")
        
        quality_issues = []
        
        for date_dir in sample_dirs[:2]:  # Check first 2 dates
            files = sorted(date_dir.glob("features_*.parquet"))
            if len(files) < 24:
                quality_issues.append(f"Missing hourly files in {date_dir.name}: only {len(files)}/24 files")
            
            # Check a file for timestamp continuity
            if files:
                df = pd.read_parquet(files[0])
                if 'timestamp' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    time_diffs = df['timestamp'].diff().dropna()
                    
                    # Check if mostly 1-second intervals
                    expected_diff = pd.Timedelta(seconds=1)
                    gaps = time_diffs[time_diffs > expected_diff * 1.5]
                    
                    if len(gaps) > 0:
                        quality_issues.append(
                            f"Timestamp gaps in {date_dir.name}: {len(gaps)} gaps found"
                        )
        
        self.report['data_quality']['issues'] = quality_issues
        self.report['data_quality']['severity'] = 'HIGH' if quality_issues else 'GOOD'
    
    def _generate_recommendations(self) -> None:
        """Generate recommendations based on analysis."""
        recommendations = []
        
        # Check critical missing fields
        missing_critical = []
        for field, mapping in self.report['field_mappings'].items():
            if mapping['status'] == 'MISSING' and field in self.EXPECTED_FIELDS['core']:
                missing_critical.append(field)
        
        if missing_critical:
            recommendations.append({
                'priority': 'HIGH',
                'issue': 'Missing critical fields',
                'fields': missing_critical,
                'action': 'These fields must be computed or the detector logic must be updated'
            })
        
        # Check field mappings
        mapped_fields = []
        for field, mapping in self.report['field_mappings'].items():
            if mapping['status'] == 'MAPPED':
                mapped_fields.append({
                    'expected': field,
                    'actual': mapping['actual_name']
                })
        
        if mapped_fields:
            recommendations.append({
                'priority': 'HIGH',
                'issue': 'Field name mismatches',
                'mappings': mapped_fields,
                'action': 'Create data adapter to transform field names'
            })
        
        # Check for high null percentages
        high_null_fields = []
        for col, stats in self.report['columns'].items():
            if stats['null_percentage'] > 10:
                high_null_fields.append({
                    'field': col,
                    'null_percentage': round(stats['null_percentage'], 2)
                })
        
        if high_null_fields:
            recommendations.append({
                'priority': 'MEDIUM',
                'issue': 'Fields with high null percentage',
                'fields': high_null_fields,
                'action': 'Implement null handling or remove from required fields'
            })
        
        self.report['recommendations'] = recommendations
    
    def save_report(self, output_path: str) -> None:
        """Save the discovery report."""
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w') as f:
            json.dump(self.report, f, indent=2, default=str)
        
        print(f"\n✅ Report saved to: {output_file}")
    
    def print_summary(self) -> None:
        """Print a summary of findings."""
        print("\n" + "="*60)
        print("SCHEMA DISCOVERY SUMMARY")
        print("="*60)
        
        print(f"\nFiles Analyzed: {self.report['files_analyzed']}")
        print(f"Date Range: {self.report['date_range']['start']} to {self.report['date_range']['end']}")
        print(f"Total Columns Found: {len(self.report['columns'])}")
        
        print("\n=== Critical Field Status ===")
        for field, mapping in self.report['field_mappings'].items():
            status = mapping['status']
            actual = mapping.get('actual_name', 'N/A')
            print(f"{field:20s} -> {status:8s} ({actual})")
        
        print("\n=== Top Recommendations ===")
        for rec in self.report['recommendations'][:3]:
            print(f"\n[{rec['priority']}] {rec['issue']}")
            print(f"Action: {rec['action']}")


def main():
    """Run the schema discovery."""
    # Configuration
    data_dir = "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s"
    output_path = "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/guides/data_schema_report.json"
    
    # Run discovery
    discovery = FeatureSchemaDiscovery(data_dir)
    discovery.analyze_sample_files(num_samples=5)
    discovery.save_report(output_path)
    discovery.print_summary()
    
    # Also create a markdown report
    create_markdown_report(output_path)


def create_markdown_report(json_path: str):
    """Create a markdown version of the report."""
    with open(json_path, 'r') as f:
        report = json.load(f)
    
    md_path = json_path.replace('.json', '.md')
    
    with open(md_path, 'w') as f:
        f.write("# Features 1s Data Schema Report\n\n")
        f.write(f"Generated: {report['discovery_timestamp']}\n\n")
        
        f.write("## Executive Summary\n\n")
        f.write(f"- **Files Analyzed**: {report['files_analyzed']}\n")
        f.write(f"- **Date Range**: {report['date_range']['start']} to {report['date_range']['end']}\n")
        f.write(f"- **Total Columns**: {len(report['columns'])}\n\n")
        
        f.write("## Critical Field Mappings\n\n")
        f.write("| Expected Field | Status | Actual Field | Action Required |\n")
        f.write("|----------------|--------|--------------|----------------|\n")
        
        for field, mapping in report['field_mappings'].items():
            status = mapping['status']
            actual = mapping.get('actual_name', 'N/A')
            action = "✅ None" if status == 'FOUND' else "🔧 Map field" if status == 'MAPPED' else "❌ Compute/Remove"
            f.write(f"| {field} | {status} | {actual} | {action} |\n")
        
        f.write("\n## Recommendations\n\n")
        for i, rec in enumerate(report['recommendations'], 1):
            f.write(f"### {i}. [{rec['priority']}] {rec['issue']}\n\n")
            f.write(f"**Action**: {rec['action']}\n\n")
            
            if 'mappings' in rec:
                f.write("**Required Mappings**:\n")
                for m in rec['mappings']:
                    f.write(f"- `{m['expected']}` → `{m['actual']}`\n")
                f.write("\n")
        
        f.write("\n## Available Columns\n\n")
        f.write("<details>\n<summary>Click to expand full column list</summary>\n\n")
        f.write("| Column | Data Type | Null % | Sample Values |\n")
        f.write("|--------|-----------|--------|---------------|\n")
        
        for col, stats in sorted(report['columns'].items()):
            dtype = stats['dtype']
            null_pct = round(stats['null_percentage'], 2)
            samples = str(stats['sample_values'][:3]) if stats['sample_values'] else 'N/A'
            f.write(f"| {col} | {dtype} | {null_pct}% | {samples} |\n")
        
        f.write("\n</details>\n")
    
    print(f"✅ Markdown report saved to: {md_path}")


if __name__ == "__main__":
    main()