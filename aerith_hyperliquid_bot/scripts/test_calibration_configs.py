#!/usr/bin/env python3
"""
Test script to verify calibration configurations are properly structured.
"""
import sys
from pathlib import Path
import yaml
import logging

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_conservative_config():
    """Test the conservative calibration config."""
    logger.info("Testing conservative calibration config...")
    
    config_file = Path(__file__).parent.parent / "configs" / "overrides" / "conservative_modern_calibration.yaml"
    
    try:
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        # Check key settings
        assert config['regime']['detector_type'] == 'continuous_gms'
        assert config['gms']['detector_type'] == 'continuous_gms'
        assert config['strategies']['use_tf_v3'] == True
        assert config['strategies']['use_tf_v2'] == False
        
        # Check momentum thresholds
        continuous_gms = config['regime']['continuous_gms']
        assert continuous_gms['gms_mom_strong_thresh'] == 25.0
        assert continuous_gms['gms_mom_weak_thresh'] == 12.5
        
        # Check TF-v3 settings
        tf_v3 = config['tf_v3']
        assert tf_v3['enabled'] == True
        assert tf_v3['risk_frac'] == 0.02
        assert tf_v3['min_regime_confidence'] == 0.7
        
        logger.info("✓ Conservative config validation passed")
        return True
        
    except Exception as e:
        logger.error(f"✗ Conservative config validation failed: {e}")
        return False


def test_state_mapping():
    """Test the conservative state mapping."""
    logger.info("Testing conservative state mapping...")
    
    mapping_file = Path(__file__).parent.parent / "configs" / "gms_state_mapping_conservative.yaml"
    
    try:
        with open(mapping_file, 'r') as f:
            mapping = yaml.safe_load(f)
        
        state_map = mapping['state_map']
        
        # Check key mappings
        assert state_map['Strong_Bull_Trend'] == 'BULL'
        assert state_map['Weak_Bull_Trend'] == 'BULL'
        assert state_map['Strong_Bear_Trend'] == 'BEAR'
        assert state_map['Weak_Bear_Trend'] == 'BEAR'  # This is the fix!
        assert state_map['High_Vol_Range'] == 'CHOP'
        assert state_map['Low_Vol_Range'] == 'CHOP'
        
        logger.info("✓ State mapping validation passed")
        logger.info(f"  Weak_Bear_Trend -> {state_map['Weak_Bear_Trend']} (FIXED!)")
        return True
        
    except Exception as e:
        logger.error(f"✗ State mapping validation failed: {e}")
        return False


def compare_thresholds():
    """Compare legacy vs modern thresholds."""
    logger.info("Comparing threshold configurations...")
    
    base_config_file = Path(__file__).parent.parent / "configs" / "base.yaml"
    conservative_config_file = Path(__file__).parent.parent / "configs" / "overrides" / "conservative_modern_calibration.yaml"
    
    try:
        # Load base config
        with open(base_config_file, 'r') as f:
            base_config = yaml.safe_load(f)
        
        # Load conservative config
        with open(conservative_config_file, 'r') as f:
            conservative_config = yaml.safe_load(f)
        
        # Extract values
        legacy_mom_high = base_config['regime']['granular_microstructure']['gms_mom_strong_thresh']
        legacy_mom_low = base_config['regime']['granular_microstructure']['gms_mom_weak_thresh']
        legacy_cadence = base_config['regime']['granular_microstructure']['cadence_sec']
        
        original_modern_mom_high = base_config['regime']['continuous_gms']['gms_mom_strong_thresh']
        original_modern_mom_low = base_config['regime']['continuous_gms']['gms_mom_weak_thresh'] 
        modern_cadence = base_config['regime']['continuous_gms']['cadence_sec']
        
        calibrated_mom_high = conservative_config['regime']['continuous_gms']['gms_mom_strong_thresh']
        calibrated_mom_low = conservative_config['regime']['continuous_gms']['gms_mom_weak_thresh']
        
        logger.info("=== THRESHOLD COMPARISON ===")
        logger.info(f"Legacy System (granular_microstructure, {legacy_cadence}s):")
        logger.info(f"  Momentum High: {legacy_mom_high}")
        logger.info(f"  Momentum Low:  {legacy_mom_low}")
        logger.info(f"")
        logger.info(f"Original Modern System (continuous_gms, {modern_cadence}s):")
        logger.info(f"  Momentum High: {original_modern_mom_high} (PROBLEM: Too low)")
        logger.info(f"  Momentum Low:  {original_modern_mom_low} (PROBLEM: Too low)")
        logger.info(f"")
        logger.info(f"Calibrated Modern System (continuous_gms, {modern_cadence}s):")
        logger.info(f"  Momentum High: {calibrated_mom_high} (10x increase)")
        logger.info(f"  Momentum Low:  {calibrated_mom_low} (25x increase)")
        logger.info(f"")
        
        # Calculate scaling factors
        high_scale = calibrated_mom_high / original_modern_mom_high
        low_scale = calibrated_mom_low / original_modern_mom_low
        
        logger.info(f"Scaling Factors:")
        logger.info(f"  High threshold: {high_scale:.1f}x increase")
        logger.info(f"  Low threshold:  {low_scale:.1f}x increase")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Threshold comparison failed: {e}")
        return False


def main():
    """Run all configuration tests."""
    logger.info("Starting calibration configuration tests...")
    
    tests_passed = 0
    total_tests = 3
    
    if test_conservative_config():
        tests_passed += 1
    
    if test_state_mapping():
        tests_passed += 1
        
    if compare_thresholds():
        tests_passed += 1
    
    logger.info(f"\n{'='*50}")
    logger.info(f"CONFIGURATION TEST RESULTS")
    logger.info(f"{'='*50}")
    logger.info(f"Tests Passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        logger.info("✓ All configuration tests passed!")
        logger.info("Ready to run validation backtest.")
    else:
        logger.error("✗ Some configuration tests failed!")
        logger.error("Fix issues before proceeding.")
    
    logger.info(f"{'='*50}")


if __name__ == "__main__":
    main()