#!/usr/bin/env python3
"""Fix syntax errors in the evaluator.py file"""

import sys
from pathlib import Path

# Path to evaluator.py
file_path = Path('/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/strategies/evaluator.py')

print(f"Reading file: {file_path}")
with open(file_path, 'r') as f:
    lines = f.readlines()

print(f"Original file has {len(lines)} lines")

# Locate the problematic section
print("Finding problematic section...")
weak_bull_line_idx = None
weak_bear_line_idx = None
tight_spread_line_idx = None
wide_spread_line_idx = None

for i, line in enumerate(lines):
    if "elif current_regime == \"Weak_Bull_Trend\"" in line:
        weak_bull_line_idx = i
    elif "elif current_regime == \"Weak_Bear_Trend\"" in line:
        weak_bear_line_idx = i
    elif "elif current_regime == \"TIGHT_SPREAD\"" in line:
        tight_spread_line_idx = i
    elif "elif current_regime == \"WIDE_SPREAD\"" in line:
        wide_spread_line_idx = i

if None in (weak_bull_line_idx, weak_bear_line_idx, tight_spread_line_idx, wide_spread_line_idx):
    print("Could not find all needed sections, aborting")
    sys.exit(1)

print(f"Found key lines at: {weak_bull_line_idx}, {weak_bear_line_idx}, {tight_spread_line_idx}, {wide_spread_line_idx}")

# Fix the entire section by rebuilding it correctly
new_lines = lines.copy()

# Fix Weak_Bull_Trend section if needed
weak_bull_code_line = new_lines[weak_bull_line_idx + 1]
if "if \"trend_following\" in self.strategies" in weak_bull_code_line and \
   weak_bull_code_line.count("if \"trend_following\" in self.strategies") > 1:
    print("Fixing duplicated code in Weak_Bull_Trend section")
    new_lines[weak_bull_line_idx + 1] = "                          if \"trend_following\" in self.strategies: active_names.append(\"trend_following\")\n"

# Rewrite the TIGHT_SPREAD and WIDE_SPREAD sections to ensure they're correct
tight_spread_section = [
    "                    elif current_regime == \"TIGHT_SPREAD\": # Added explicit check for TIGHT_SPREAD\n",
    "                         # active_names remains empty\n",
    "                         pass\n"
]

wide_spread_section = [
    "                    elif current_regime == \"WIDE_SPREAD\": # Add explicit check for WIDE_SPREAD\n",
    "                         # active_names remains empty\n",
    "                         pass\n"
]

# Replace the sections (note: we might be replacing more or fewer lines than original)
new_lines[tight_spread_line_idx:wide_spread_line_idx] = tight_spread_section
wide_spread_line_idx = tight_spread_line_idx + len(tight_spread_section)  # Recalculate position
new_lines[wide_spread_line_idx:wide_spread_line_idx+3] = wide_spread_section

# Write the fixed file
print(f"Writing fixed file with {len(new_lines)} lines")
with open(file_path, 'w') as f:
    f.writelines(new_lines)

# Verify the syntax
try:
    import py_compile
    py_compile.compile(file_path, doraise=True)
    print("✅ Syntax check passed - file successfully fixed")
except Exception as e:
    print(f"❌ Syntax check failed: {e}")
    sys.exit(1)
