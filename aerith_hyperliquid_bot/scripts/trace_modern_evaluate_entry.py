#!/usr/bin/env python3
"""
Trace Modern TF-v3 Evaluation
=============================

Trace exactly what happens during ModernTFV3Strategy.evaluate_entry() calls.
Properly uses the modern architecture, not legacy.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
import json


def main():
    print("=== Tracing Modern TF-v3 Evaluation ===\n")
    
    # Patch BEFORE importing anything else
    from hyperliquid_bot.modern import tf_v3_modern
    
    evaluate_calls = []
    original_evaluate_entry = tf_v3_modern.ModernTFV3Strategy.evaluate_entry
    original_is_regime_stable = tf_v3_modern.ModernTFV3Strategy._is_regime_stable
    
    def trace_evaluate_entry(self, signals, regime):
        """Wrapper to trace evaluate_entry calls"""
        timestamp = signals.get('timestamp', 'unknown')
        
        print(f"\n{'='*80}")
        print(f"📍 evaluate_entry called at {timestamp}")
        print(f"   Regime: {regime}")
        
        # Check all required fields
        required_fields = ['close', 'volume', 'ema_fast', 'ema_slow', 'atr_14']
        missing = [f for f in required_fields if f not in signals]
        if missing:
            print(f"   ❌ Missing required fields: {missing}")
            # Don't return None, let's see what happens
            
        # Print key values
        print(f"   Close: {signals.get('close', 'N/A')}")
        print(f"   Volume: {signals.get('volume', 'N/A')}")
        print(f"   EMA Fast (12): {signals.get('ema_fast', 'N/A')}")
        print(f"   EMA Slow (26): {signals.get('ema_slow', 'N/A')}")
        print(f"   ATR: {signals.get('atr_14', 'N/A')}")
        
        # Check regime features
        regime_features = signals.get('regime_features', {})
        if regime_features:
            print(f"   Regime features:")
            print(f"     - current_confidence: {regime_features.get('current_confidence', 'N/A')}")
            print(f"     - state_persistence: {regime_features.get('state_persistence', 'N/A')}")
            print(f"     - recent_transitions: {regime_features.get('recent_transitions', 'N/A')}")
            print(f"     - risk_suppressed: {regime_features.get('risk_suppressed', 'N/A')}")
        else:
            print("   ⚠️  NO regime_features in signals!")
        
        # Call original
        try:
            result = original_evaluate_entry(self, signals, regime)
            
            if result:
                print(f"   ✅ Generated {result['direction'].upper()} signal!")
                print(f"      Size: {result['position_size']:.2%}")
                print(f"      Stop: {result.get('stop_loss', 'N/A')}")
                print(f"      Target: {result.get('take_profit', 'N/A')}")
            else:
                print(f"   ❌ No signal generated")
                
                # Debug why no signal
                ema_fast = signals.get('ema_fast', 0)
                ema_slow = signals.get('ema_slow', 0)
                
                # Check EMA condition
                if ema_fast > ema_slow:
                    print(f"      EMA: BULLISH (fast {ema_fast:.2f} > slow {ema_slow:.2f})")
                    if 'BULL' not in regime:
                        print(f"      ➡️  But regime is {regime}, need BULL regime for long")
                else:
                    print(f"      EMA: BEARISH (fast {ema_fast:.2f} < slow {ema_slow:.2f})")
                    if 'BEAR' not in regime:
                        print(f"      ➡️  But regime is {regime}, need BEAR regime for short")
                
                # Check regime stability
                if regime_features:
                    if regime_features.get('state_persistence', 0) < 0.5:
                        print(f"      ➡️  Low persistence: {regime_features.get('state_persistence', 0)} < 0.5")
                    # Scale recent transitions to hourly
                    recent_transitions = regime_features.get('recent_transitions', 0)
                    state_changes_equiv = recent_transitions * 6
                    if state_changes_equiv > self.max_regime_changes_1h:
                        print(f"      ➡️  Too many transitions: {state_changes_equiv} (scaled) > {self.max_regime_changes_1h}")
                    if regime_features.get('risk_suppressed', False):
                        print(f"      ➡️  Risk suppressed!")
            
            evaluate_calls.append({
                'timestamp': timestamp,
                'regime': regime,
                'result': result,
                'ema_short': signals.get('ema_short'),
                'ema_long': signals.get('ema_long'),
                'regime_features': regime_features
            })
            
            return result
            
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def trace_is_regime_stable(self, regime_features):
        """Trace regime stability checks"""
        result = original_is_regime_stable(self, regime_features)
        
        if not result:
            print(f"      🔍 Regime stability check FAILED:")
            if not regime_features:
                print(f"         - No regime_features provided")
            else:
                persistence = regime_features.get('state_persistence', 0)
                transitions = regime_features.get('recent_transitions', 0)
                risk_suppressed = regime_features.get('risk_suppressed', False)
                confidence = regime_features.get('current_confidence', 0.0)
                
                if persistence < 0.5:
                    print(f"         - Persistence too low: {persistence} < 0.5")
                state_changes_equiv = transitions * 6
                if state_changes_equiv > self.max_regime_changes_1h:
                    print(f"         - Too many transitions: {state_changes_equiv} (scaled) > {self.max_regime_changes_1h}")
                if confidence < self.min_regime_confidence:
                    print(f"         - Confidence too low: {confidence} < {self.min_regime_confidence}")
                if risk_suppressed:
                    print(f"         - Risk is suppressed")
        
        return result
    
    # Apply patches
    tf_v3_modern.ModernTFV3Strategy.evaluate_entry = trace_evaluate_entry
    tf_v3_modern.ModernTFV3Strategy._is_regime_stable = trace_is_regime_stable
    
    # Now run modern backtest
    from hyperliquid_bot.config.settings import load_config
    from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Use a short period where we know we have bear regime
    start_date = datetime(2024, 1, 15, 18)  # Has Weak_Bear_Trend
    end_date = datetime(2024, 1, 15, 23)    # 5 hours
    
    print(f"Running MODERN backtest from {start_date} to {end_date}...")
    print(f"Using ModernBacktestEngine + ModernTFV3Strategy\n")
    
    try:
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        results = engine.run_backtest()
        
        print(f"\n\n{'='*80}")
        print(f"📊 BACKTEST RESULTS:")
        print(f"Total trades: {results['performance']['total_trades']}")
        print(f"Total evaluate_entry calls: {len(evaluate_calls)}")
        
        if len(evaluate_calls) == 0:
            print("\n❌ NO CALLS TO evaluate_entry!")
            print("This means the strategy is not being invoked at all.")
            print("Check that the modern system is properly integrated.")
        else:
            # Analyze why no trades
            print(f"\n📈 Call Analysis:")
            bull_ema_count = sum(1 for c in evaluate_calls if c.get('ema_short', 0) > c.get('ema_long', 0))
            bear_ema_count = sum(1 for c in evaluate_calls if c.get('ema_short', 0) < c.get('ema_long', 0))
            
            print(f"  - Bullish EMA setups: {bull_ema_count}")
            print(f"  - Bearish EMA setups: {bear_ema_count}")
            
            # Group by regime
            regime_counts = {}
            for call in evaluate_calls:
                regime = call['regime']
                regime_counts[regime] = regime_counts.get(regime, 0) + 1
            
            print(f"\n  Calls by regime:")
            for regime, count in regime_counts.items():
                print(f"    - {regime}: {count}")
        
    except Exception as e:
        print(f"\n❌ Backtest error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()