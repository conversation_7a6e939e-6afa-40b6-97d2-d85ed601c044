#!/usr/bin/env python3
"""
Script to find min/max values of a specific column in a parquet file.
"""

import pandas as pd
import sys
from pathlib import Path

def analyze_column(file_path, column_name):
    """Analyze min/max values of a specific column in a parquet file."""
    try:
        print(f"\n{'='*50}")
        print(f"ANALYZING COLUMN: {column_name} in {file_path}")
        print(f"{'='*50}")
        
        # Read the Parquet file
        df = pd.read_parquet(file_path)
        
        if column_name not in df.columns:
            print(f"Error: Column '{column_name}' not found in the file.")
            print(f"Available columns: {df.columns.tolist()}")
            return
        
        # Calculate statistics
        min_val = df[column_name].min()
        max_val = df[column_name].max()
        mean_val = df[column_name].mean()
        median_val = df[column_name].median()
        std_val = df[column_name].std()
        
        # Print results
        print(f"Minimum value: {min_val}")
        print(f"Maximum value: {max_val}")
        print(f"Mean value: {mean_val}")
        print(f"Median value: {median_val}")
        print(f"Standard deviation: {std_val}")
        
        # Count values outside typical z-score ranges
        if column_name.endswith('zscore'):
            abs_2_count = (df[column_name].abs() > 2).sum()
            abs_3_count = (df[column_name].abs() > 3).sum()
            print(f"\nValues with absolute magnitude > 2: {abs_2_count} ({abs_2_count/len(df)*100:.2f}%)")
            print(f"Values with absolute magnitude > 3: {abs_3_count} ({abs_3_count/len(df)*100:.2f}%)")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python temp_min_max.py <parquet_file_path> <column_name>")
        sys.exit(1)
    
    file_path = Path(sys.argv[1])
    column_name = sys.argv[2]
    
    if not file_path.exists():
        print(f"Error: File not found: {file_path}")
        sys.exit(1)
    
    analyze_column(file_path, column_name)
