#!/usr/bin/env python3
"""
Process all March data from 2025-03-01 to 2025-03-22 with new ATR implementation.
"""

import subprocess
import sys
from datetime import datetime, timedelta
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def process_date(date_str):
    """Process a single date using the ETL."""
    cmd = [
        'python3', 'tools/etl_l20_to_1s.py',
        '--date', date_str,
        '--force',
        '--overwrite'
    ]
    
    logger.info(f"Processing {date_str}...")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        if result.returncode == 0:
            logger.info(f"✅ Successfully processed {date_str}")
            return True
        else:
            logger.error(f"❌ Failed to process {date_str}: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        logger.error(f"❌ Timeout processing {date_str}")
        return False
    except Exception as e:
        logger.error(f"❌ Error processing {date_str}: {e}")
        return False

def main():
    """Process all dates from 2025-03-01 to 2025-03-22."""
    start_date = datetime(2025, 3, 1)
    end_date = datetime(2025, 3, 22)
    
    current_date = start_date
    successful = 0
    failed = 0
    
    logger.info(f"Starting to process dates from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        
        if process_date(date_str):
            successful += 1
        else:
            failed += 1
            
        current_date += timedelta(days=1)
    
    logger.info(f"Processing complete: {successful} successful, {failed} failed")
    
    if failed > 0:
        logger.error(f"Some dates failed to process. Check logs above.")
        sys.exit(1)
    else:
        logger.info("All dates processed successfully! 🎉")

if __name__ == "__main__":
    main()
