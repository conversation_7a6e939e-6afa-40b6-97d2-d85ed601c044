#!/usr/bin/env python3
"""
Enhanced Hourly Data Resampling Script

Converts 1-second features data to enhanced hourly format with all TF-v3 requirements.
This solves the performance bottleneck by pre-computing hourly aggregations.

Performance improvement: ~3,600x (from 259,200 points/hour to 72 points/hour)
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import pyarrow.parquet as pq
import logging
from typing import Dict, List, Optional
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedHourlyResampler:
    """Resample 1-second features to enhanced hourly format."""
    
    def __init__(self, data_dir: Path):
        """
        Initialize resampler with data directory.
        
        Args:
            data_dir: Root directory containing hyperliquid_data
        """
        self.data_dir = data_dir
        self.features_1s_dir = data_dir / "features_1s"
        self.output_dir = data_dir / "enhanced_hourly" / "1h"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def get_aggregation_config(self) -> Dict[str, str]:
        """
        Define aggregation strategy for each field.
        
        Returns:
            Dictionary mapping field names to aggregation methods
        """
        return {
            # Core OHLCV - standard aggregation
            # Note: 'open' doesn't exist in 1s data, will use first 'close'
            'close': ['first', 'last'],  # Will create both open and close
            'high': 'max',
            'low': 'min',
            'volume': 'sum',
            
            # Microstructure - statistical aggregation
            'obi_smoothed': 'mean',  # Will be renamed to volume_imbalance
            'spread_mean': 'mean',
            'spread_std': 'mean',
            'realised_vol_1s': 'mean',
            
            # Momentum indicators - use last value (most recent state)
            'ma_slope': 'last',
            'ma_slope_ema_30s': 'last',
            
            # Volatility indicators - use last value
            'atr_14_sec': 'last',
            'atr_percent_sec': 'last',
            
            # Additional microstructure features if available
            'bid_ask_spread': 'mean',
            'bid_ask_spread_bps': 'mean',
            'book_imbalance': 'mean',
            'book_imbalance_smoothed': 'mean',
        }
    
    def load_daily_features_1s(self, date: str) -> pd.DataFrame:
        """
        Load all 1-second features for a given date.
        
        Args:
            date: Date string in YYYY-MM-DD format
            
        Returns:
            DataFrame with all 1-second data for the day
        """
        date_dir = self.features_1s_dir / date
        if not date_dir.exists():
            logger.warning(f"No data found for {date}")
            return pd.DataFrame()
        
        # Load all hourly files for the day
        hourly_files = sorted(date_dir.glob("features_*.parquet"))
        if not hourly_files:
            logger.warning(f"No parquet files found in {date_dir}")
            return pd.DataFrame()
        
        logger.info(f"Loading {len(hourly_files)} files for {date}")
        
        # Load and concatenate all hourly files
        dataframes = []
        for file_path in hourly_files:
            try:
                df = pd.read_parquet(file_path)
                dataframes.append(df)
            except Exception as e:
                logger.error(f"Error loading {file_path}: {e}")
                continue
        
        if not dataframes:
            return pd.DataFrame()
        
        # Concatenate all dataframes
        daily_df = pd.concat(dataframes, ignore_index=True)
        
        # Ensure timestamp is datetime and set as index
        if 'timestamp' in daily_df.columns:
            daily_df['timestamp'] = pd.to_datetime(daily_df['timestamp'])
            daily_df.set_index('timestamp', inplace=True)
        
        # Sort by timestamp
        daily_df.sort_index(inplace=True)
        
        logger.info(f"Loaded {len(daily_df)} rows for {date}")
        
        return daily_df
    
    def resample_to_enhanced_hourly(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Resample 1-second data to enhanced hourly format.
        
        Args:
            df: DataFrame with 1-second data
            
        Returns:
            Enhanced hourly DataFrame
        """
        if df.empty:
            return pd.DataFrame()
        
        # Get aggregation config
        agg_config = self.get_aggregation_config()
        
        # Filter to only include columns that exist in the data
        available_columns = df.columns.tolist()
        agg_dict = {}
        
        for col, agg_func in agg_config.items():
            if col in available_columns:
                agg_dict[col] = agg_func
            elif col == 'close' and isinstance(agg_func, list):
                # Special handling for close -> [open, close]
                if 'close' in available_columns:
                    agg_dict['close'] = agg_func
        
        if not agg_dict:
            logger.error("No valid columns found for aggregation")
            return pd.DataFrame()
        
        logger.info(f"Aggregating columns: {list(agg_dict.keys())}")
        
        # Resample to hourly frequency
        hourly_df = df.resample('1h').agg(agg_dict)
        
        # Handle multi-level columns from close aggregation
        if isinstance(hourly_df.columns, pd.MultiIndex):
            # Flatten column names
            hourly_df.columns = ['_'.join(col).strip() if col[1] else col[0] 
                                for col in hourly_df.columns.values]
            # Rename close_first to open
            if 'close_first' in hourly_df.columns:
                hourly_df.rename(columns={'close_first': 'open', 'close_last': 'close'}, inplace=True)
        
        # Clean up column names - remove aggregation suffixes
        rename_map = {
            'high_max': 'high',
            'low_min': 'low',
            'volume_sum': 'volume',
            'obi_smoothed_mean': 'volume_imbalance',
            'spread_mean_mean': 'spread_mean',
            'spread_std_mean': 'spread_std',
            'realised_vol_1s_mean': 'realised_vol_1s',
            'ma_slope_last': 'ma_slope',
            'ma_slope_ema_30s_last': 'ma_slope_ema_30s',
            'atr_14_sec_last': 'atr_14_sec',
            'atr_percent_sec_last': 'atr_percent_sec'
        }
        
        hourly_df.rename(columns=rename_map, inplace=True)
        
        # Remove any rows with all NaN values
        hourly_df.dropna(how='all', inplace=True)
        
        # Forward fill any remaining NaN values for continuity
        hourly_df.ffill(inplace=True)
        
        logger.info(f"Created {len(hourly_df)} hourly bars")
        logger.info(f"Final columns: {list(hourly_df.columns)}")
        
        return hourly_df
    
    def validate_enhanced_data(self, enhanced_df: pd.DataFrame, date: str) -> bool:
        """
        Validate the enhanced hourly data.
        
        Args:
            enhanced_df: Enhanced hourly DataFrame
            date: Date string for validation
            
        Returns:
            True if validation passes
        """
        if enhanced_df.empty:
            logger.error(f"Enhanced data for {date} is empty")
            return False
        
        # Check for required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume_imbalance']
        missing_columns = [col for col in required_columns if col not in enhanced_df.columns]
        
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            return False
        
        # Check for data quality
        # 1. OHLC relationships
        ohlc_valid = (
            (enhanced_df['high'] >= enhanced_df['low']).all() and
            (enhanced_df['high'] >= enhanced_df['open']).all() and
            (enhanced_df['high'] >= enhanced_df['close']).all() and
            (enhanced_df['low'] <= enhanced_df['open']).all() and
            (enhanced_df['low'] <= enhanced_df['close']).all()
        )
        
        if not ohlc_valid:
            logger.error("OHLC validation failed - invalid price relationships")
            return False
        
        # 2. Volume should be non-negative
        if 'volume' in enhanced_df.columns:
            if (enhanced_df['volume'] < 0).any():
                logger.error("Negative volume detected")
                return False
        
        # 3. Check for reasonable number of hours (should be ~24 for a full day)
        expected_hours = 24
        actual_hours = len(enhanced_df)
        
        if actual_hours < expected_hours * 0.9:  # Allow 10% missing
            logger.warning(f"Only {actual_hours} hours found for {date}, expected ~{expected_hours}")
        
        logger.info(f"Validation passed for {date}")
        return True
    
    def process_single_date(self, date: str) -> bool:
        """
        Process a single date to create enhanced hourly data.
        
        Args:
            date: Date string in YYYY-MM-DD format
            
        Returns:
            True if successful
        """
        logger.info(f"Processing {date}...")
        
        try:
            # Load 1-second data
            daily_1s = self.load_daily_features_1s(date)
            if daily_1s.empty:
                logger.warning(f"No data to process for {date}")
                return False
            
            # Resample to enhanced hourly
            enhanced_hourly = self.resample_to_enhanced_hourly(daily_1s)
            
            # Validate
            if not self.validate_enhanced_data(enhanced_hourly, date):
                logger.error(f"Validation failed for {date}")
                return False
            
            # Save to parquet
            output_path = self.output_dir / f"{date}_1h_enhanced.parquet"
            enhanced_hourly.to_parquet(output_path, compression='snappy')
            
            logger.info(f"✅ Created {output_path} with {len(enhanced_hourly)} hours")
            return True
            
        except Exception as e:
            logger.error(f"Error processing {date}: {e}")
            return False
    
    def get_date_range(self, start_date: str, end_date: str) -> List[str]:
        """
        Generate list of dates between start and end.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            
        Returns:
            List of date strings
        """
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        
        dates = []
        current = start
        while current <= end:
            dates.append(current.strftime("%Y-%m-%d"))
            current += timedelta(days=1)
        
        return dates


def main():
    """Main entry point for the script."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Create enhanced hourly data from 1-second features"
    )
    parser.add_argument(
        "--data-dir",
        type=Path,
        default=Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data"),
        help="Root data directory"
    )
    parser.add_argument(
        "--date",
        type=str,
        help="Single date to process (YYYY-MM-DD)"
    )
    parser.add_argument(
        "--start",
        type=str,
        help="Start date for range processing (YYYY-MM-DD)"
    )
    parser.add_argument(
        "--end",
        type=str,
        help="End date for range processing (YYYY-MM-DD)"
    )
    parser.add_argument(
        "--test",
        action="store_true",
        help="Test mode - process only first date"
    )
    
    args = parser.parse_args()
    
    # Initialize resampler
    resampler = EnhancedHourlyResampler(args.data_dir)
    
    # Determine dates to process
    if args.date:
        # Single date mode
        success = resampler.process_single_date(args.date)
        sys.exit(0 if success else 1)
    
    elif args.start and args.end:
        # Date range mode
        dates = resampler.get_date_range(args.start, args.end)
        
        if args.test:
            dates = dates[:1]  # Only process first date in test mode
        
        logger.info(f"Processing {len(dates)} dates from {args.start} to {args.end}")
        
        # Process each date
        successful = 0
        failed = 0
        
        for date in dates:
            if resampler.process_single_date(date):
                successful += 1
            else:
                failed += 1
        
        logger.info(f"\nSummary: {successful} successful, {failed} failed")
        sys.exit(0 if failed == 0 else 1)
    
    else:
        parser.error("Please specify either --date or both --start and --end")


if __name__ == "__main__":
    main()