#!/usr/bin/env python3
"""
Run Modern System Backtest
==========================
This script runs the experimental modern trading system using the new architecture:
- 60-second regime updates for market awareness
- Hourly trading evaluation for position decisions
- Modern continuous detector with fixed thresholds
- Modern TF-v3 strategy with regime history

FIXED: Now uses proper timing architecture to generate trades!
"""

import argparse
import sys
from pathlib import Path
from datetime import datetime
import json
import logging

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtest_engine import RobustBacktestEngine as ModernBacktestEngine


def setup_logging(verbose: bool = False):
    """Set up logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Run modern system backtest"
    )
    
    parser.add_argument(
        '--start-date',
        type=str,
        default='2024-01-01',
        help='Start date (YYYY-MM-DD, default: 2024-01-01)'
    )
    
    parser.add_argument(
        '--end-date',
        type=str,
        default='2024-01-07',
        help='End date (YYYY-MM-DD, default: 2024-01-07)'
    )
    
    parser.add_argument(
        '--override',
        type=str,
        default='configs/overrides/modern_system_v2_complete.yaml',
        help='Config override file (default: modern_system_v2_complete.yaml)'
    )
    
    parser.add_argument(
        '--output',
        type=str,
        default=None,
        help='Output file for results (default: logs/modern_backtest_YYYYMMDD_HHMMSS.json)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--use-legacy-engine',
        action='store_true',
        help='Use legacy backtesting engine (for comparison)'
    )
    
    parser.add_argument(
        '--no-regime-cache',
        action='store_true',
        help='Disable regime cache and simulate regimes live'
    )
    
    return parser.parse_args()


def run_legacy_backtest(config_path: str, args):
    """Run using legacy backtesting engine."""
    import subprocess
    
    backtest_script = project_root / "hyperliquid_bot" / "backtester" / "run_backtest.py"
    
    cmd = [
        sys.executable,
        str(backtest_script),
        "--override", config_path,
        "--start-date", args.start_date,
        "--end-date", args.end_date
    ]
    
    result = subprocess.run(cmd, check=True)
    return result.returncode


def main():
    """Run modern system backtest with new architecture."""
    args = parse_args()
    
    # Set up logging
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    print("=" * 80)
    print("AERITH HYPERLIQUID BOT - MODERN SYSTEM BACKTEST")
    print("=" * 80)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Use legacy engine if requested
    if args.use_legacy_engine:
        print("Using LEGACY backtesting engine...")
        return run_legacy_backtest(args.override, args)
    
    # Parse dates
    try:
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
    except ValueError as e:
        logger.error(f"Invalid date format: {e}")
        sys.exit(1)
    
    # Load configuration
    logger.info(f"Loading config with override: {args.override}")
    # Check if override path is absolute or relative
    override_path = Path(args.override)
    if not override_path.is_absolute():
        override_path = project_root / override_path
    config = load_config(config_path=str(override_path))
    
    print(f"📁 Project root: {project_root}")
    print(f"⚙️  Config: {args.override}")
    print(f"🚀 Running MODERN backtesting engine...")
    print()
    print("System architecture:")
    print("  - Regime updates: Every 60 seconds")
    print("  - Trading evaluation: Every 60 minutes")
    print("  - Strategy: TF-v3 (modern)")
    print("  - Detector: continuous_modern")
    print("  - Risk per trade: 25% (fixed)")
    print("  - Data source: features_1s/")
    print()
    print("✅ FIXED ISSUES:")
    print("  - Proper 60s regime updates")
    print("  - Hourly trading evaluation")
    print("  - Risk management fixed (25% not 2%)")
    print()
    print("-" * 80)
    
    try:
        # Create modern backtesting engine
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date,
            use_regime_cache=not args.no_regime_cache  # Use cache unless disabled
        )
        
        # Run backtest
        results = engine.run_backtest()
        
        # Log summary
        print("-" * 80)
        print(f"✅ Modern backtest completed!")
        print(f"Finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        print("📊 RESULTS:")
        print(f"  - Total Trades: {results['performance']['total_trades']}")
        print(f"  - Total Return: {results['performance']['total_return']:.2%}")
        print(f"  - Win Rate: {results['performance']['win_rate']:.2%}")
        print(f"  - Runtime: {results['runtime_seconds']:.1f} seconds")
        print()
        
        # Save results
        if args.output:
            output_path = Path(args.output)
        else:
            # Default to logs directory with timestamp
            log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
            log_dir.mkdir(exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = log_dir / f"modern_backtest_{timestamp}.json"
        
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"💾 Results saved to: {output_path}")
        
        # Print trade summary
        if results['trades']:
            print(f"\n📈 First 5 trades:")
            for i, trade in enumerate(results['trades'][:5]):
                print(
                    f"  {i+1}. {trade['timestamp']} - "
                    f"{trade['direction'].upper()} @ {trade['entry_price']:.2f} "
                    f"(size: {trade['position_size']:.2%})"
                )
        else:
            print("\n⚠️  No trades generated - check regime thresholds!")
        
        print()
        print("Expected performance targets:")
        print("  - 100-200 trades: ✅ System working properly")
        print("  - 0 trades: ❌ Regime thresholds too restrictive")
        print("  - 900+ trades: ❌ Regime thresholds too loose")
        
    except Exception as e:
        print("-" * 80)
        print(f"❌ Modern backtest failed: {e}")
        logger.error(f"Backtest failed: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()