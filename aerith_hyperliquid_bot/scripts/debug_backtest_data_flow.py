#!/usr/bin/env python3
"""
Debug the actual data flow in backtest to find where it breaks
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from datetime import datetime
import logging

# Set debug logging BEFORE imports
logging.basicConfig(
    level=logging.DEBUG,
    format='%(name)s - %(message)s'
)

# Patch the hourly evaluator to log data shapes
original_prepare = None

def patched_prepare_signals(self, hourly_bar, current_signals, regime_features, ohlcv_history=None):
    """Patched version that logs data shapes"""
    print(f"\n=== PREPARE SIGNALS DEBUG ===")
    print(f"hourly_bar type: {type(hourly_bar)}")
    print(f"ohlcv_history type: {type(ohlcv_history)}")
    if ohlcv_history is not None:
        print(f"ohlcv_history shape: {ohlcv_history.shape}")
        print(f"ohlcv_history columns: {list(ohlcv_history.columns) if hasattr(ohlcv_history, 'columns') else 'N/A'}")
    else:
        print("ohlcv_history is None!")
    
    # Call original
    result = original_prepare(hourly_bar, current_signals, regime_features, ohlcv_history)
    
    print(f"Result signals keys: {list(result.keys()) if result else 'None'}")
    return result

# Apply patch
from hyperliquid_bot.modern.hourly_evaluator import HourlyStrategyEvaluator
original_prepare = HourlyStrategyEvaluator._prepare_strategy_signals
HourlyStrategyEvaluator._prepare_strategy_signals = patched_prepare_signals

# Now run minimal backtest
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_backtest_engine import RobustBacktestEngine

config = load_config("configs/overrides/modern_system_v2_complete.yaml")
config.regime.detector_type = "enhanced"

print("Creating backtest engine...")
engine = RobustBacktestEngine(
    config=config,
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 1, 12),  # Just 12 hours
    use_regime_cache=False
)

print("\nRunning backtest...")
try:
    results = engine.run_backtest()
    print(f"\nBacktest completed: {len(results.get('trades', []))} trades")
except Exception as e:
    print(f"\nBacktest failed: {e}")
    import traceback
    traceback.print_exc()