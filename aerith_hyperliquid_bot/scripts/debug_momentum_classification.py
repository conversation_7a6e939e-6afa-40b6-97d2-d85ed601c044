#!/usr/bin/env python3
"""
Debug script to analyze momentum (ma_slope) values and classification distribution.

This script helps understand why the continuous_gms detector is classifying
90%+ of regimes as CHOP.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime
import logging
from pathlib import Path

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.data.handler import HistoricalDataHandler
from hyperliquid_bot.signals.calculator import SignalEngine
from deepmerge import always_merger
import yaml

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def analyze_momentum_distribution(config_path: str = None, override_path: str = None):
    """Analyze the distribution of ma_slope values in the data."""
    
    # Load configuration
    if config_path:
        with open(config_path, 'r') as f:
            base_cfg = yaml.safe_load(f)
    else:
        with open('configs/base.yaml', 'r') as f:
            base_cfg = yaml.safe_load(f)
    
    # Start with base config
    merged_cfg = base_cfg
    
    # Apply override if provided
    if override_path and os.path.exists(override_path):
        with open(override_path, 'r') as f:
            override_cfg = yaml.safe_load(f)
        
        # Deep merge override into base
        merged_cfg = always_merger.merge(base_cfg, override_cfg)
    
    # Create Config object from merged dictionary
    config = Config(**merged_cfg)
    
    logger.info(f"Loaded configuration - Period: {config.backtest.period_preset}")
    
    # Initialize data handler
    data_handler = HistoricalDataHandler(config, logger)
    
    # Initialize signal engine
    signal_engine = SignalEngine(config, data_handler)
    
    # Load and calculate signals
    logger.info("Loading data and calculating signals...")
    
    # Load the data first
    try:
        # Get date range from config
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2025, 1, 1)
        
        data_handler.load_historical_data(start_date=start_date, end_date=end_date)
        ohlcv_data = data_handler.get_ohlcv_data()
    except Exception as e:
        logger.error(f"Failed to load data: {e}")
        return
    
    if ohlcv_data is None or ohlcv_data.empty:
        logger.error("No data loaded!")
        return
    
    # Calculate signals
    signals_df = signal_engine.calculate_all_signals()
    
    # Analyze ma_slope distribution
    if 'ma_slope' not in signals_df.columns:
        logger.error("ma_slope not found in signals!")
        return
    
    ma_slope = signals_df['ma_slope'].dropna()
    ma_slope_abs = ma_slope.abs()
    
    logger.info("\n=== MA SLOPE ANALYSIS ===")
    logger.info(f"Total data points: {len(ma_slope)}")
    logger.info(f"Date range: {signals_df.index[0]} to {signals_df.index[-1]}")
    
    # Basic statistics
    logger.info("\nBasic Statistics:")
    logger.info(f"Mean (absolute): ${ma_slope_abs.mean():.2f}")
    logger.info(f"Median (absolute): ${ma_slope_abs.median():.2f}")
    logger.info(f"Std Dev: ${ma_slope.std():.2f}")
    logger.info(f"Min: ${ma_slope.min():.2f}")
    logger.info(f"Max: ${ma_slope.max():.2f}")
    
    # Percentile analysis
    logger.info("\nPercentile Distribution (absolute values):")
    percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
    for p in percentiles:
        value = ma_slope_abs.quantile(p/100)
        logger.info(f"  {p}th percentile: ${value:.2f}")
    
    # Compare with thresholds
    logger.info("\n=== THRESHOLD ANALYSIS ===")
    
    # Get configured thresholds
    mom_strong = 100.0  # From config
    mom_weak = 50.0     # From config
    
    logger.info(f"\nConfigured Thresholds:")
    logger.info(f"  Strong momentum: ${mom_strong}")
    logger.info(f"  Weak momentum: ${mom_weak}")
    
    # Classification distribution
    strong_count = (ma_slope_abs >= mom_strong).sum()
    weak_count = (ma_slope_abs <= mom_weak).sum()
    medium_count = ((ma_slope_abs > mom_weak) & (ma_slope_abs < mom_strong)).sum()
    
    logger.info(f"\nMomentum Classification Distribution:")
    logger.info(f"  Strong: {strong_count} ({strong_count/len(ma_slope)*100:.1f}%)")
    logger.info(f"  Medium: {medium_count} ({medium_count/len(ma_slope)*100:.1f}%)")
    logger.info(f"  Weak: {weak_count} ({weak_count/len(ma_slope)*100:.1f}%)")
    
    # Price analysis for context
    if 'close' in signals_df.columns:
        close_prices = signals_df['close'].dropna()
        avg_price = close_prices.mean()
        
        logger.info(f"\n=== PRICE CONTEXT ===")
        logger.info(f"Average price: ${avg_price:.2f}")
        logger.info(f"Price range: ${close_prices.min():.2f} - ${close_prices.max():.2f}")
        
        # Calculate what the thresholds mean as percentage
        logger.info(f"\nThresholds as percentage of average price:")
        logger.info(f"  Weak (${mom_weak}): {mom_weak/avg_price*100:.3f}%")
        logger.info(f"  Strong (${mom_strong}): {mom_strong/avg_price*100:.3f}%")
    
    # ATR analysis if available
    if 'atr' in signals_df.columns:
        atr = signals_df['atr'].dropna()
        avg_atr = atr.mean()
        
        logger.info(f"\n=== ATR CONTEXT ===")
        logger.info(f"Average ATR: ${avg_atr:.2f}")
        
        # Calculate ma_slope as ATR multiples
        ma_slope_atr_multiples = ma_slope_abs / atr.reindex(ma_slope_abs.index)
        
        logger.info(f"\nMA Slope as ATR multiples:")
        logger.info(f"  Mean: {ma_slope_atr_multiples.mean():.2f}x ATR")
        logger.info(f"  Median: {ma_slope_atr_multiples.median():.2f}x ATR")
        
        logger.info(f"\nThresholds as ATR multiples:")
        logger.info(f"  Weak (${mom_weak}): {mom_weak/avg_atr:.2f}x ATR")
        logger.info(f"  Strong (${mom_strong}): {mom_strong/avg_atr:.2f}x ATR")
        
        # Suggested normalized thresholds
        logger.info(f"\n=== SUGGESTED NORMALIZED THRESHOLDS ===")
        suggested_weak = ma_slope_atr_multiples.quantile(0.50)
        suggested_strong = ma_slope_atr_multiples.quantile(0.85)
        logger.info(f"Based on ATR normalization:")
        logger.info(f"  Weak momentum: {suggested_weak:.2f}x ATR")
        logger.info(f"  Strong momentum: {suggested_strong:.2f}x ATR")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Debug momentum classification")
    parser.add_argument('--config', help='Path to config file', 
                       default='configs/base.yaml')
    parser.add_argument('--override', help='Path to override config file',
                       default='configs/overrides/execution_refinement_enabled.yaml')
    
    args = parser.parse_args()
    
    analyze_momentum_distribution(args.config, args.override)