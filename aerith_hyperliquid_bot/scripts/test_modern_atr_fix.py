#!/usr/bin/env python3
"""
Test Modern System with ATR Fix
===============================

Quick test to verify:
1. ATR is now properly loaded from features_1s
2. Trades are being generated
3. Simplified execution works
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from datetime import datetime
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
import json

# Set up logging - only show important messages
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Silence noisy loggers
for logger_name in ['ModernDataLoader', 'ModernDataAdapter', 'ModernDataContract',
                   'hyperliquid_bot.modern.data_aggregator', 'hyperliquid_bot.modern.regime_state_manager',
                   'ModernContinuousDetectorV2']:
    logging.getLogger(logger_name).setLevel(logging.WARNING)

import warnings
warnings.filterwarnings('ignore')


def main():
    print("=== Modern System ATR Fix Test ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Test just 2 days
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 17)
    
    print(f"Test period: {start_date.date()} to {end_date.date()}")
    print(f"Risk per trade: {config.portfolio.risk_per_trade:.2%}")
    print("\nKey changes:")
    print("- ATR now uses pre-computed 'atr_14_sec' from features_1s")
    print("- Execution simplified (no 1s data loading)")
    print("- Regime cache for fast lookups\n")
    
    try:
        # Create backtest engine
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date,
            use_regime_cache=True
        )
        
        # Add logging to check ATR values
        orig_evaluate = engine.hourly_evaluator._prepare_strategy_signals
        
        def logged_evaluate(*args, **kwargs):
            signals = orig_evaluate(*args, **kwargs)
            # Log ATR value
            atr_val = signals.get('atr_14', 'MISSING')
            if atr_val != 'MISSING' and atr_val > 0:
                print(f"✅ ATR detected: {atr_val:.2f}")
            elif atr_val == 'MISSING':
                print("❌ ATR field missing!")
            else:
                print(f"❌ ATR is {atr_val}")
            return signals
        
        engine.hourly_evaluator._prepare_strategy_signals = logged_evaluate
        
        print("Running backtest...")
        print("-" * 50)
        
        import time
        start_time = time.time()
        
        results = engine.run_backtest()
        
        elapsed = time.time() - start_time
        
        # Results
        print("\n" + "=" * 50)
        print("RESULTS")
        print("=" * 50)
        
        trades = results['trades']
        print(f"\nTotal trades: {len(trades)}")
        print(f"Runtime: {elapsed:.1f}s")
        
        if trades:
            print(f"\n✅ SUCCESS! ATR fix worked - {len(trades)} trades generated")
            
            # Show first trade details
            first_trade = trades[0]
            print(f"\nFirst trade:")
            print(f"  Time: {first_trade['timestamp']}")
            print(f"  Direction: {first_trade['direction']}")
            print(f"  Entry: ${first_trade['entry_price']:.2f}")
            print(f"  Size: {first_trade['position_size']:.2%}")
            print(f"  Stop Loss: ${first_trade.get('stop_loss', 0):.2f}")
            print(f"  Regime: {first_trade.get('regime', 'N/A')}")
        else:
            print("\n❌ Still no trades generated")
            print("\nPossible remaining issues:")
            print("  1. TF-v3 entry conditions too strict")
            print("  2. Risk management blocking trades")
            print("  3. Regime states not favorable")
        
        # Save results
        output_file = f"atr_fix_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\nResults saved to: {output_file}")
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())