#!/usr/bin/env python3
"""
Integrate Modern Engine into Main Backtester
============================================

This script patches the main backtester to route to the modern engine
when system_mode is set to "modern" in the configuration.

Usage:
    python scripts/integrate_modern_engine.py
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))


def main():
    """Apply the modern engine integration patch."""
    print("=" * 80)
    print("MODERN ENGINE INTEGRATION")
    print("=" * 80)
    
    # Path to the main backtester file
    backtester_path = project_root / "hyperliquid_bot" / "backtester" / "run_backtest.py"
    
    # Read the current content
    print(f"Reading {backtester_path}...")
    content = backtester_path.read_text()
    
    # Check if already patched
    if "should_use_modern_engine" in content:
        print("✅ Main backtester already patched for modern engine routing!")
        return
    
    # Find the location to insert the import
    import_marker = "from hyperliquid_bot.utils.system_validation import perform_startup_validation"
    if import_marker not in content:
        print("❌ Could not find import marker in run_backtest.py")
        return
    
    # Add the import after the marker
    new_import = """from hyperliquid_bot.utils.system_validation import perform_startup_validation
# Import modern backtester routing
from hyperliquid_bot.backtester.run_backtest_modern_patch import (
    should_use_modern_engine, run_modern_backtest
)"""
    
    content = content.replace(import_marker, new_import)
    
    # Find the backtester initialization section
    init_marker = "    # 9. Initialize and Run Backtester (using validated config)"
    if init_marker not in content:
        print("❌ Could not find backtester initialization marker")
        return
    
    # Find the try block
    try_marker = """    try:
        logger.info("Initializing Backtester instance...")
        backtester = Backtester(config)"""
    
    if try_marker not in content:
        print("❌ Could not find try block marker")
        return
    
    # Replace with routing logic
    new_try_block = """    try:
        # Check if we should use modern engine
        if should_use_modern_engine(config):
            # Use modern backtesting engine
            run_modern_backtest(config, start_date, end_date, logger)
        else:
            # Use legacy backtesting engine
            logger.info("Initializing Backtester instance...")
            backtester = Backtester(config)"""
    
    content = content.replace(try_marker, new_try_block)
    
    # Also need to handle the run() call
    run_marker = """        logger.info("Running backtest...")

        # Run the backtest using the Backtester's run method
        backtester.run(start_date, end_date)"""
    
    new_run_block = """        logger.info("Running backtest...")

        # Run the backtest using the Backtester's run method
        if not should_use_modern_engine(config):
            backtester.run(start_date, end_date)"""
    
    content = content.replace(run_marker, new_run_block)
    
    # Write the patched content back
    print("Writing patched content...")
    backtester_path.write_text(content)
    
    print("✅ Successfully patched main backtester!")
    print("\nThe main backtester will now:")
    print("1. Check for system_mode == 'modern' in config")
    print("2. Route to modern engine if detected")
    print("3. Use legacy engine otherwise")
    print("\nTo use the modern engine, ensure your config has:")
    print("  system_mode: 'modern'")
    print("\nOr use the modern_system.yaml override config.")


if __name__ == '__main__':
    main()