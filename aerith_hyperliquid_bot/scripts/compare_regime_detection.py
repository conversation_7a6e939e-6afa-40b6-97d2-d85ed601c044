#!/usr/bin/env python3
"""
Compare regime detection between legacy and modern systems.
Run both systems on the same data and analyze differences.
"""

import subprocess
import json
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
import sys
import yaml
import glob

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

def run_legacy_backtest(start_date: str, end_date: str) -> dict:
    """Run legacy system backtest."""
    print(f"Running legacy backtest for January 2024...")
    
    # Create temporary config override for legacy system
    config_override = {
        "backtest": {
            "period_preset": "custom",
            "custom_start_date": start_date,
            "custom_end_date": end_date
        }
    }
    
    override_file = Path("temp_legacy_override.yaml")
    with open(override_file, 'w') as f:
        yaml.dump(config_override, f)
    
    cmd = [
        sys.executable,
        "-m", "hyperliquid_bot.backtester.run_backtest",
        "--override", str(override_file)
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # Clean up temp file
    override_file.unlink(missing_ok=True)
    
    if result.returncode != 0:
        print("Legacy backtest failed!")
        print(result.stderr)
        return {}
    
    # Find the output file - legacy system creates files with timestamps
    output_files = glob.glob("backtest_results_*.json")
    if not output_files:
        print("No output file found!")
        return {}
    
    # Get most recent file
    latest_file = max(output_files, key=lambda x: Path(x).stat().st_mtime)
    
    # Load results
    with open(latest_file) as f:
        data = json.load(f)
    
    # Rename to expected filename
    Path(latest_file).rename("legacy_regime_test.json")
    
    return data

def run_modern_backtest(start_date: str, end_date: str) -> dict:
    """Run modern system backtest."""
    print(f"Running modern backtest from {start_date} to {end_date}...")
    
    cmd = [
        sys.executable,
        "scripts/run_modern_backtest.py",
        "--start-date", start_date,
        "--end-date", end_date,
        "--override", "configs/overrides/modern_system_v2_complete.yaml",
        "--output", "modern_regime_test.json"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print("Modern backtest failed!")
        print(result.stderr)
        return {}
    
    # Load results
    with open("modern_regime_test.json") as f:
        return json.load(f)

def analyze_regime_patterns(legacy_data: dict, modern_data: dict) -> dict:
    """Analyze and compare regime detection patterns."""
    analysis = {
        "legacy": {},
        "modern": {},
        "comparison": {}
    }
    
    # Extract regime history if available
    if 'regime_history' in legacy_data:
        legacy_regimes = legacy_data['regime_history']
        # Count regime distribution
        regime_counts = {}
        for regime in legacy_regimes:
            state = regime.get('regime', 'Unknown')
            regime_counts[state] = regime_counts.get(state, 0) + 1
        analysis['legacy']['regime_distribution'] = regime_counts
        analysis['legacy']['total_regime_changes'] = len(legacy_regimes)
    
    if 'regime_history' in modern_data:
        modern_regimes = modern_data['regime_history']
        # Count regime distribution
        regime_counts = {}
        for regime in modern_regimes:
            state = regime.get('regime', 'Unknown')
            regime_counts[state] = regime_counts.get(state, 0) + 1
        analysis['modern']['regime_distribution'] = regime_counts
        analysis['modern']['total_regime_changes'] = len(modern_regimes)
    
    # Compare trade entry regimes
    if 'trades' in legacy_data:
        legacy_entry_regimes = {}
        for trade in legacy_data['trades']:
            regime = trade.get('entry_regime', 'Unknown')
            legacy_entry_regimes[regime] = legacy_entry_regimes.get(regime, 0) + 1
        analysis['legacy']['trade_entry_regimes'] = legacy_entry_regimes
    
    if 'trades' in modern_data:
        modern_entry_regimes = {}
        for trade in modern_data['trades']:
            regime = trade.get('entry_regime', 'Unknown')
            modern_entry_regimes[regime] = modern_entry_regimes.get(regime, 0) + 1
        analysis['modern']['trade_entry_regimes'] = modern_entry_regimes
    
    return analysis

def main():
    print("=" * 80)
    print("REGIME DETECTION COMPARISON")
    print("=" * 80)
    print()
    
    # Test period (January 2024 for speed)
    start_date = "2024-01-01"
    end_date = "2024-01-31"
    
    # Run both systems
    legacy_results = run_legacy_backtest(start_date, end_date)
    modern_results = run_modern_backtest(start_date, end_date)
    
    if not legacy_results or not modern_results:
        print("Failed to get results from one or both systems!")
        return 1
    
    # Extract performance metrics
    legacy_perf = legacy_results.get('performance', {})
    modern_perf = modern_results.get('performance', {})
    
    print("\n" + "=" * 80)
    print("PERFORMANCE COMPARISON")
    print("=" * 80)
    
    print(f"\nLegacy System:")
    print(f"  Trades: {legacy_perf.get('total_trades', 0)}")
    print(f"  Return: {legacy_perf.get('total_return', 0):.2%}")
    print(f"  Win Rate: {legacy_perf.get('win_rate', 0):.2%}")
    
    print(f"\nModern System:")
    print(f"  Trades: {modern_perf.get('total_trades', 0)}")
    print(f"  Return: {modern_perf.get('total_return', 0):.2%}")
    print(f"  Win Rate: {modern_perf.get('win_rate', 0):.2%}")
    
    # Analyze regime patterns
    regime_analysis = analyze_regime_patterns(legacy_results, modern_results)
    
    print("\n" + "=" * 80)
    print("REGIME DETECTION ANALYSIS")
    print("=" * 80)
    
    # Legacy regime distribution
    if 'regime_distribution' in regime_analysis['legacy']:
        print(f"\nLegacy Regime Distribution:")
        for regime, count in sorted(regime_analysis['legacy']['regime_distribution'].items()):
            print(f"  {regime}: {count}")
        print(f"  Total changes: {regime_analysis['legacy'].get('total_regime_changes', 0)}")
    
    # Modern regime distribution
    if 'regime_distribution' in regime_analysis['modern']:
        print(f"\nModern Regime Distribution:")
        for regime, count in sorted(regime_analysis['modern']['regime_distribution'].items()):
            print(f"  {regime}: {count}")
        print(f"  Total changes: {regime_analysis['modern'].get('total_regime_changes', 0)}")
    
    # Trade entry regimes
    print(f"\n" + "-" * 40)
    print("TRADE ENTRY REGIMES")
    print("-" * 40)
    
    if 'trade_entry_regimes' in regime_analysis['legacy']:
        print(f"\nLegacy Trade Entries by Regime:")
        for regime, count in sorted(regime_analysis['legacy']['trade_entry_regimes'].items()):
            print(f"  {regime}: {count} trades")
    
    if 'trade_entry_regimes' in regime_analysis['modern']:
        print(f"\nModern Trade Entries by Regime:")
        for regime, count in sorted(regime_analysis['modern']['trade_entry_regimes'].items()):
            print(f"  {regime}: {count} trades")
    
    # Save detailed analysis
    analysis_output = {
        "test_period": f"{start_date} to {end_date}",
        "legacy_performance": legacy_perf,
        "modern_performance": modern_perf,
        "regime_analysis": regime_analysis,
        "key_findings": []
    }
    
    # Add key findings
    if regime_analysis['legacy'].get('total_regime_changes', 0) > 0:
        legacy_changes_per_day = regime_analysis['legacy']['total_regime_changes'] / 31
        modern_changes_per_day = regime_analysis['modern'].get('total_regime_changes', 0) / 31
        
        analysis_output['key_findings'].append(
            f"Legacy averages {legacy_changes_per_day:.1f} regime changes per day"
        )
        analysis_output['key_findings'].append(
            f"Modern averages {modern_changes_per_day:.1f} regime changes per day"
        )
    
    # Save analysis
    with open("regime_detection_comparison.json", 'w') as f:
        json.dump(analysis_output, f, indent=2)
    
    print(f"\n📊 Detailed analysis saved to: regime_detection_comparison.json")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())