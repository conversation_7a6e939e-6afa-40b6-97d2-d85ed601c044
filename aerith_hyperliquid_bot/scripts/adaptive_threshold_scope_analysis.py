#!/usr/bin/env python3
"""
Adaptive Threshold Scope Analysis
Analyzes whether adaptive thresholds work with granular_microstructure detector.
"""

import sys
from pathlib import Path
import logging

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import Config, load_config
import yaml

def analyze_adaptive_threshold_scope():
    """Analyze the scope of adaptive threshold implementation."""

    print("🔍 Adaptive Threshold Scope Analysis")
    print("=" * 60)

    # Load current configuration
    config_path = project_root / 'configs' / 'base.yaml'
    config = load_config(str(config_path))

    print(f"📋 Current Configuration Analysis:")
    print(f"   Config file: {config_path}")

    # Analyze detector type setting
    detector_type = getattr(config.gms, 'detector_type', 'continuous_gms')
    print(f"   Detector Type: {detector_type}")

    # Analyze adaptive threshold setting
    auto_thresholds = getattr(config.gms, 'auto_thresholds', False)
    print(f"   Auto Thresholds: {auto_thresholds}")

    # Get detector-specific settings
    detector_settings = config.regime.get_detector_settings(detector_type)
    print(f"\n📊 Detector-Specific Settings ({detector_type}):")

    key_thresholds = [
        'gms_vol_high_thresh',
        'gms_vol_low_thresh',
        'gms_mom_strong_thresh',
        'gms_mom_weak_thresh'
    ]

    for threshold in key_thresholds:
        value = detector_settings.get(threshold, 'NOT_FOUND')
        print(f"   {threshold}: {value}")

    # Analyze adaptive threshold configuration
    print(f"\n🧠 Adaptive Threshold Configuration:")
    adaptive_settings = [
        'percentile_window_sec',
        'vol_low_pct',
        'vol_high_pct',
        'mom_low_pct',
        'mom_high_pct',
        'min_history_rows'
    ]

    for setting in adaptive_settings:
        value = getattr(config.gms, setting, 'NOT_FOUND')
        print(f"   {setting}: {value}")

    # Test detector instantiation
    print(f"\n🔧 Detector Instantiation Test:")

    try:
        from hyperliquid_bot.core.detector_factory import get_regime_detector

        # Set up logging to capture detector initialization messages
        logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

        print(f"   Creating {detector_type} detector...")
        detector = get_regime_detector(config)

        # Check if adaptive thresholds were initialized
        has_adaptive_vol = hasattr(detector, 'adaptive_vol_threshold') and detector.adaptive_vol_threshold is not None
        has_adaptive_mom = hasattr(detector, 'adaptive_mom_threshold') and detector.adaptive_mom_threshold is not None

        print(f"   ✅ Detector created successfully")
        print(f"   Adaptive Vol Threshold: {'✅ Initialized' if has_adaptive_vol else '❌ Not Initialized'}")
        print(f"   Adaptive Mom Threshold: {'✅ Initialized' if has_adaptive_mom else '❌ Not Initialized'}")

        # Check detector class
        detector_class = detector.__class__.__name__
        print(f"   Detector Class: {detector_class}")

        # Analyze threshold behavior
        analyze_threshold_behavior(detector, detector_type, auto_thresholds)

    except Exception as e:
        print(f"   ❌ Error creating detector: {e}")
        import traceback
        traceback.print_exc()

def analyze_threshold_behavior(detector, detector_type, auto_thresholds):
    """Analyze how the detector handles thresholds."""

    print(f"\n📈 Threshold Behavior Analysis:")

    # Check if detector has adaptive threshold attributes
    has_adaptive_vol = hasattr(detector, 'adaptive_vol_threshold') and detector.adaptive_vol_threshold is not None
    has_adaptive_mom = hasattr(detector, 'adaptive_mom_threshold') and detector.adaptive_mom_threshold is not None

    print(f"   Detector Type: {detector_type}")
    print(f"   Auto Thresholds Config: {auto_thresholds}")
    print(f"   Adaptive Vol Initialized: {has_adaptive_vol}")
    print(f"   Adaptive Mom Initialized: {has_adaptive_mom}")

    # Check static threshold values
    if hasattr(detector, 'vol_high_thresh'):
        print(f"   Static Vol High Thresh: {detector.vol_high_thresh}")
    if hasattr(detector, 'vol_low_thresh'):
        print(f"   Static Vol Low Thresh: {detector.vol_low_thresh}")
    if hasattr(detector, 'mom_strong_thresh'):
        print(f"   Static Mom Strong Thresh: {detector.mom_strong_thresh}")
    if hasattr(detector, 'mom_weak_thresh'):
        print(f"   Static Mom Weak Thresh: {detector.mom_weak_thresh}")

    # Determine expected behavior
    print(f"\n🎯 Expected Behavior Analysis:")

    if detector_type == 'granular_microstructure':
        if auto_thresholds and has_adaptive_vol:
            print(f"   ✅ UNEXPECTED: Granular detector has adaptive thresholds!")
            print(f"   📝 This means adaptive thresholds DO work with granular_microstructure")
        elif auto_thresholds and not has_adaptive_vol:
            print(f"   ❌ EXPECTED: Granular detector ignores adaptive threshold config")
            print(f"   📝 This means adaptive thresholds are continuous_gms only")
        elif not auto_thresholds:
            print(f"   ✅ EXPECTED: Auto thresholds disabled, using static values")
        else:
            print(f"   ⚠️  UNCLEAR: Unexpected configuration state")

    elif detector_type == 'continuous_gms':
        if auto_thresholds and has_adaptive_vol:
            print(f"   ✅ EXPECTED: Continuous detector has adaptive thresholds")
        elif auto_thresholds and not has_adaptive_vol:
            print(f"   ❌ UNEXPECTED: Continuous detector should have adaptive thresholds!")
        elif not auto_thresholds:
            print(f"   ✅ EXPECTED: Auto thresholds disabled, using static values")
        else:
            print(f"   ⚠️  UNCLEAR: Unexpected configuration state")

    # Check for identical results explanation
    print(f"\n🔄 Identical Results Analysis:")

    if detector_type == 'granular_microstructure':
        if auto_thresholds and has_adaptive_vol:
            print(f"   📊 Adaptive thresholds are active for granular detector")
            print(f"   🎯 Identical results suggest adaptive thresholds are working correctly")
            print(f"   💡 The 184 trade baseline is maintained through adaptive adjustment")
        elif auto_thresholds and not has_adaptive_vol:
            print(f"   📊 Adaptive thresholds are ignored by granular detector")
            print(f"   🎯 Identical results expected - using same static thresholds")
            print(f"   💡 The 184 trade baseline uses fixed percentile thresholds")
        else:
            print(f"   📊 Using static thresholds as configured")

    # Recommendation
    print(f"\n💡 RECOMMENDATIONS:")

    if detector_type == 'granular_microstructure' and auto_thresholds:
        if has_adaptive_vol:
            print(f"   ✅ Adaptive thresholds work with granular_microstructure")
            print(f"   📈 Current setup is optimal for legacy system")
        else:
            print(f"   🔄 Switch to continuous_gms to use adaptive thresholds")
            print(f"   ⚙️  Or verify why adaptive initialization failed")

    elif detector_type == 'continuous_gms' and not auto_thresholds:
        print(f"   🔧 Enable auto_thresholds: true for continuous_gms")
        print(f"   📈 This will improve trade generation vs fixed thresholds")

def test_both_detector_types():
    """Test both detector types to compare behavior."""

    print(f"\n" + "=" * 60)
    print("🔬 COMPARATIVE DETECTOR ANALYSIS")
    print("=" * 60)

    config_path = project_root / 'configs' / 'base.yaml'

    detector_types = ['granular_microstructure', 'continuous_gms']

    for detector_type in detector_types:
        print(f"\n📋 Testing {detector_type.upper()} Detector:")

        try:
            # Load fresh config
            config = load_config(str(config_path))

            # Override detector type
            config.gms.detector_type = detector_type

            # Create detector
            from hyperliquid_bot.core.detector_factory import get_regime_detector
            detector = get_regime_detector(config)

            # Check adaptive threshold initialization
            has_adaptive_vol = hasattr(detector, 'adaptive_vol_threshold') and detector.adaptive_vol_threshold is not None
            has_adaptive_mom = hasattr(detector, 'adaptive_mom_threshold') and detector.adaptive_mom_threshold is not None

            print(f"   Detector Class: {detector.__class__.__name__}")
            print(f"   Adaptive Vol: {'✅ Yes' if has_adaptive_vol else '❌ No'}")
            print(f"   Adaptive Mom: {'✅ Yes' if has_adaptive_mom else '❌ No'}")

            # Check threshold values
            if hasattr(detector, 'vol_high_thresh'):
                print(f"   Vol High Thresh: {detector.vol_high_thresh}")
            if hasattr(detector, 'vol_low_thresh'):
                print(f"   Vol Low Thresh: {detector.vol_low_thresh}")

        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    """Main analysis function."""

    try:
        analyze_adaptive_threshold_scope()
        test_both_detector_types()

        print(f"\n✅ Analysis complete.")
        print(f"\n📋 KEY QUESTIONS ANSWERED:")
        print(f"   1. Do adaptive thresholds work with granular_microstructure? [See above]")
        print(f"   2. Why are results identical with auto_thresholds true/false? [See above]")
        print(f"   3. Is adaptive threshold feature continuous_gms only? [See above]")
        print(f"   4. How does detector_type interact with threshold mode? [See above]")
    except Exception as e:
        print(f"❌ Error in main analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
