#!/usr/bin/env python3
"""
Test Enhanced Detector WITHOUT Cache
====================================

This script tests the enhanced regime detector without relying on pre-computed cache.
It ensures the system works in the worst case scenario (no cache available).

Key Points:
1. Cache is explicitly disabled (use_regime_cache=False)
2. System must calculate regimes on-the-fly
3. Performance of ~3 minutes for a month is acceptable
4. Focus is on quality filtering, not speed
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import time
import logging

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_backtest_engine import RobustBacktestEngine


def setup_logging():
    """Configure logging for the test."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('enhanced_detector_no_cache_test.log')
        ]
    )


def run_test():
    """Run enhanced detector test without cache."""
    logger = logging.getLogger("EnhancedDetectorTest")
    
    # Test configuration
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 7)  # One week for initial test
    
    logger.info("=" * 80)
    logger.info("ENHANCED DETECTOR TEST - NO CACHE")
    logger.info("=" * 80)
    logger.info(f"Period: {start_date} to {end_date}")
    logger.info("Cache: DISABLED - Testing worst case scenario")
    logger.info("Expected time: ~3 minutes for one month")
    logger.info("")
    
    # Load configuration - ensure enhanced detector is used
    config_path = project_root / "configs" / "overrides" / "modern_system_v2_complete.yaml"
    config = load_config(config_path=str(config_path))
    
    # Force enhanced detector
    config.regime.detector_type = "enhanced"
    
    logger.info("Configuration:")
    logger.info(f"  - Detector: enhanced (with quality filtering)")
    logger.info(f"  - Fallback confidence: {getattr(config.regime, 'fallback_confidence', 0.7)}")
    logger.info(f"  - Price change threshold: {getattr(config.regime, 'price_change_threshold', 0.001)}")
    logger.info("")
    
    # Create backtesting engine WITHOUT cache
    logger.info("Creating backtest engine (cache disabled)...")
    engine = RobustBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=False  # CRITICAL: No cache!
    )
    
    # Run backtest and measure time
    logger.info("Starting backtest...")
    start_time = time.time()
    
    try:
        results = engine.run_backtest()
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # Extract key metrics
        total_trades = len(results.get('trades', []))
        completed_trades = len(results.get('completed_trades', []))
        
        # Calculate performance
        total_pnl = sum(t.get('pnl_pct', 0) for t in results.get('completed_trades', []))
        winning_trades = [t for t in results.get('completed_trades', []) if t.get('pnl_pct', 0) > 0]
        win_rate = len(winning_trades) / completed_trades if completed_trades > 0 else 0
        
        # Get regime source breakdown
        regime_sources = results.get('regime_sources', {})
        
        logger.info("")
        logger.info("=" * 80)
        logger.info("RESULTS SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Execution time: {elapsed_time:.1f} seconds ({elapsed_time/60:.1f} minutes)")
        logger.info(f"Hours per second: {results.get('hours_processed', 0) / elapsed_time:.1f}")
        logger.info("")
        logger.info("REGIME SOURCE BREAKDOWN:")
        logger.info(f"  - Pre-computed cache: {regime_sources.get('cache_used', 0)} (should be 0)")
        logger.info(f"  - Detector calculated: {regime_sources.get('detector_used', 0)}")
        logger.info(f"  - Price-based fallback: {regime_sources.get('fallback_used', 0)}")
        logger.info("")
        logger.info("TRADING RESULTS:")
        logger.info(f"  - Total trades: {total_trades}")
        logger.info(f"  - Completed trades: {completed_trades}")
        logger.info(f"  - Win rate: {win_rate:.1%}")
        logger.info(f"  - Total P&L: {total_pnl:.2%}")
        logger.info("")
        
        # Quality filtering analysis
        if hasattr(engine.detector, 'quality_stats'):
            stats = engine.detector.quality_stats
            logger.info("QUALITY FILTERING STATS:")
            logger.info(f"  - Total regime detections: {stats.get('total', 0)}")
            logger.info(f"  - High quality regimes: {stats.get('high_quality', 0)}")
            logger.info(f"  - Low quality filtered: {stats.get('low_quality', 0)}")
            logger.info(f"  - Filter rate: {stats.get('filter_rate', 0):.1%}")
        
        # Performance assessment
        logger.info("")
        logger.info("PERFORMANCE ASSESSMENT:")
        if elapsed_time < 180:  # Less than 3 minutes
            logger.info("✅ Performance is EXCELLENT - under 3 minutes")
        elif elapsed_time < 300:  # Less than 5 minutes
            logger.info("✅ Performance is GOOD - under 5 minutes")
        else:
            logger.info("⚠️  Performance is SLOW - consider optimization")
        
        # Cache verification
        if regime_sources.get('cache_used', 0) == 0:
            logger.info("✅ Cache was NOT used - test successful!")
        else:
            logger.info("❌ ERROR: Cache was used when it should be disabled!")
        
        # Save detailed results
        import json
        results_file = f"enhanced_detector_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump({
                'config': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'cache_enabled': False,
                    'detector_type': 'enhanced'
                },
                'performance': {
                    'elapsed_seconds': elapsed_time,
                    'hours_per_second': results.get('hours_processed', 0) / elapsed_time
                },
                'regime_sources': regime_sources,
                'trading_results': {
                    'total_trades': total_trades,
                    'completed_trades': completed_trades,
                    'win_rate': win_rate,
                    'total_pnl': total_pnl
                }
            }, f, indent=2)
        
        logger.info(f"\nDetailed results saved to: {results_file}")
        
    except Exception as e:
        logger.error(f"Error during backtest: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    setup_logging()
    run_test()