#!/usr/bin/env python3
"""
Z-Score Ana<PERSON><PERSON>

This script analyzes z-score columns in parquet files, providing detailed statistics
and distribution information. It's particularly useful for examining signal columns
like raw_obi_zscore.

Usage:
    python zscore_analyzer.py <parquet_file_path> <zscore_column_name>
"""

import pandas as pd
import sys
import matplotlib.pyplot as plt
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_zscore(file_path, column_name, plot=False):
    """
    Analyze a z-score column in a parquet file.
    
    Args:
        file_path: Path to the parquet file
        column_name: Name of the z-score column to analyze
        plot: Whether to generate and save distribution plots
    """
    try:
        logger.info(f"Analyzing column '{column_name}' in {file_path}")
        
        # Read the parquet file
        df = pd.read_parquet(file_path)
        
        # Check if column exists
        if column_name not in df.columns:
            logger.error(f"Column '{column_name}' not found in file. Available columns: {df.columns.tolist()}")
            return
        
        # Basic statistics
        stats = {
            "min": df[column_name].min(),
            "max": df[column_name].max(),
            "mean": df[column_name].mean(),
            "median": df[column_name].median(),
            "std": df[column_name].std(),
            "skew": df[column_name].skew(),
            "kurtosis": df[column_name].kurtosis(),
            "non_null_count": df[column_name].count(),
            "null_count": df[column_name].isna().sum()
        }
        
        # Calculate percentiles
        percentiles = {
            "1%": df[column_name].quantile(0.01),
            "5%": df[column_name].quantile(0.05),
            "10%": df[column_name].quantile(0.10),
            "25%": df[column_name].quantile(0.25),
            "50%": df[column_name].quantile(0.50),
            "75%": df[column_name].quantile(0.75),
            "90%": df[column_name].quantile(0.90),
            "95%": df[column_name].quantile(0.95),
            "99%": df[column_name].quantile(0.99)
        }
        
        # Count values outside typical z-score ranges
        thresholds = [1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0]
        threshold_counts = {}
        for t in thresholds:
            count = (df[column_name].abs() > t).sum()
            pct = (count / len(df)) * 100
            threshold_counts[f">{t}"] = (count, pct)
        
        # Print results
        print(f"\n{'='*60}")
        print(f"Z-SCORE ANALYSIS: {column_name}")
        print(f"{'='*60}")
        
        print("\nBASIC STATISTICS:")
        print(f"Min: {stats['min']:.6f}")
        print(f"Max: {stats['max']:.6f}")
        print(f"Mean: {stats['mean']:.6f}")
        print(f"Median: {stats['median']:.6f}")
        print(f"Std Dev: {stats['std']:.6f}")
        print(f"Skewness: {stats['skew']:.6f}")
        print(f"Kurtosis: {stats['kurtosis']:.6f}")
        print(f"Non-null values: {stats['non_null_count']} ({stats['non_null_count']/len(df)*100:.2f}%)")
        if stats['null_count'] > 0:
            print(f"Null values: {stats['null_count']} ({stats['null_count']/len(df)*100:.2f}%)")
        
        print("\nPERCENTILES:")
        for p, val in percentiles.items():
            print(f"{p}: {val:.6f}")
        
        print("\nTHRESHOLD ANALYSIS:")
        for t, (count, pct) in threshold_counts.items():
            print(f"Values with |{column_name}| {t}: {count} ({pct:.2f}%)")
        
        # Check for related boolean filter column if it exists
        filter_col = f"{column_name}_filtered"
        if filter_col in df.columns:
            if pd.api.types.is_bool_dtype(df[filter_col]):
                true_count = df[filter_col].sum()
                true_pct = (true_count / len(df)) * 100
                print(f"\nFILTERED SIGNAL ANALYSIS ({filter_col}):")
                print(f"True values: {true_count} ({true_pct:.2f}%)")
                print(f"False values: {len(df) - true_count} ({100 - true_pct:.2f}%)")
                
                # Show statistics for when filter is True
                if true_count > 0:
                    filtered_vals = df.loc[df[filter_col], column_name]
                    print(f"\nStatistics when {filter_col} is True:")
                    print(f"Min: {filtered_vals.min():.6f}")
                    print(f"Max: {filtered_vals.max():.6f}")
                    print(f"Mean: {filtered_vals.mean():.6f}")
                    print(f"Std Dev: {filtered_vals.std():.6f}")
            else:
                print(f"\nNote: Column '{filter_col}' exists but is not boolean type.")
        
        # Plotting (if enabled)
        if plot:
            try:
                # Create output directory if it doesn't exist
                output_dir = Path("./zscore_analysis")
                output_dir.mkdir(exist_ok=True)
                
                # Histogram
                plt.figure(figsize=(12, 6))
                plt.hist(df[column_name].dropna(), bins=50, alpha=0.7)
                plt.axvline(x=0, color='r', linestyle='--')
                for t in [1, 2, 3]:
                    plt.axvline(x=t, color='g', linestyle=':', alpha=0.7)
                    plt.axvline(x=-t, color='g', linestyle=':', alpha=0.7)
                plt.title(f'Distribution of {column_name}')
                plt.xlabel('Z-Score Value')
                plt.ylabel('Frequency')
                plt.grid(True, alpha=0.3)
                plt.savefig(output_dir / f"{column_name}_histogram.png")
                
                # Time series plot
                if isinstance(df.index, pd.DatetimeIndex):
                    plt.figure(figsize=(14, 7))
                    plt.plot(df.index, df[column_name])
                    plt.axhline(y=0, color='r', linestyle='--')
                    for t in [1, 2, 3]:
                        plt.axhline(y=t, color='g', linestyle=':', alpha=0.7)
                        plt.axhline(y=-t, color='g', linestyle=':', alpha=0.7)
                    plt.title(f'Time Series of {column_name}')
                    plt.xlabel('Time')
                    plt.ylabel('Z-Score Value')
                    plt.grid(True, alpha=0.3)
                    plt.savefig(output_dir / f"{column_name}_timeseries.png")
                
                logger.info(f"Plots saved to {output_dir}")
            except Exception as e:
                logger.warning(f"Error generating plots: {e}")
        
        return stats, percentiles, threshold_counts
        
    except Exception as e:
        logger.error(f"Error analyzing z-score: {e}")
        return None

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python zscore_analyzer.py <parquet_file_path> <zscore_column_name> [--plot]")
        sys.exit(1)
    
    file_path = Path(sys.argv[1])
    column_name = sys.argv[2]
    plot = "--plot" in sys.argv
    
    if not file_path.exists():
        logger.error(f"File not found: {file_path}")
        sys.exit(1)
    
    analyze_zscore(file_path, column_name, plot)
