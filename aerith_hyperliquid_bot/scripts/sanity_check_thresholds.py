#!/usr/bin/env python3
"""
R-112e Threshold Sanity Check

Quick analysis of volatility bucket ratios with new thresholds
to validate they're reasonable before running full backtest.
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config

def analyze_volatility_buckets():
    """Analyze volatility bucket distribution with new thresholds"""
    print("=== R-112e Threshold Sanity Check ===")
    
    # Load config to get new thresholds
    config = load_config("configs/base.yaml")
    vol_low = config.regime.gms_vol_low_thresh
    vol_high = config.regime.gms_vol_high_thresh
    mom_weak = config.regime.gms_mom_weak_thresh
    mom_strong = config.regime.gms_mom_strong_thresh
    
    print(f"\nNew Thresholds:")
    print(f"  Volatility: {vol_low:.3f} / {vol_high:.3f} ({vol_low*100:.1f}% / {vol_high*100:.1f}%)")
    print(f"  Momentum:   {mom_weak:.1f} / {mom_strong:.1f}")
    
    # Try to load some sample feature data to analyze
    features_dir = Path("/hyperliquid_data/features_1s")
    
    # Look for March 2025 data
    sample_files = []
    for date_dir in ["2025-03-01", "2025-03-02", "2025-03-03"]:
        date_path = features_dir / date_dir
        if date_path.exists():
            for hour_file in sorted(date_path.glob("features_*.parquet"))[:3]:  # First 3 hours
                sample_files.append(hour_file)
    
    if not sample_files:
        print("\nWARNING: No sample feature files found for analysis")
        print("Expected path: /hyperliquid_data/features_1s/2025-03-0X/features_XX.parquet")
        return
    
    print(f"\nAnalyzing {len(sample_files)} sample files...")
    
    all_vol_data = []
    all_mom_data = []
    
    for file_path in sample_files:
        try:
            df = pd.read_parquet(file_path)
            
            # Look for ATR percentage column
            atr_col = None
            for col in ['atr_percent', 'atr_percent_sec', 'atr_14_sec']:
                if col in df.columns:
                    atr_col = col
                    break
            
            if atr_col is None:
                print(f"  WARNING: No ATR column found in {file_path.name}")
                continue
                
            # Look for momentum column
            mom_col = None
            for col in ['ma_slope', 'ema_slope', 'momentum']:
                if col in df.columns:
                    mom_col = col
                    break
            
            if mom_col is None:
                print(f"  WARNING: No momentum column found in {file_path.name}")
                continue
            
            # Collect data
            vol_data = df[atr_col].dropna()
            mom_data = df[mom_col].dropna()
            
            all_vol_data.extend(vol_data.tolist())
            all_mom_data.extend(mom_data.tolist())
            
            print(f"  ✓ {file_path.name}: {len(vol_data)} vol points, {len(mom_data)} mom points")
            
        except Exception as e:
            print(f"  ERROR reading {file_path.name}: {e}")
    
    if not all_vol_data:
        print("\nERROR: No volatility data collected for analysis")
        return
    
    # Convert to numpy arrays
    vol_array = np.array(all_vol_data)
    mom_array = np.array(all_mom_data) if all_mom_data else np.array([])
    
    print(f"\n=== VOLATILITY ANALYSIS ===")
    print(f"Total data points: {len(vol_array)}")
    print(f"Volatility stats:")
    print(f"  Mean: {vol_array.mean():.4f} ({vol_array.mean()*100:.2f}%)")
    print(f"  Median: {np.median(vol_array):.4f} ({np.median(vol_array)*100:.2f}%)")
    print(f"  5th percentile: {np.percentile(vol_array, 5):.4f} ({np.percentile(vol_array, 5)*100:.2f}%)")
    print(f"  95th percentile: {np.percentile(vol_array, 95):.4f} ({np.percentile(vol_array, 95)*100:.2f}%)")
    
    # Calculate bucket ratios
    low_bucket = (vol_array <= vol_low).mean()
    mid_bucket = ((vol_array > vol_low) & (vol_array <= vol_high)).mean()
    high_bucket = (vol_array > vol_high).mean()
    
    print(f"\n=== BUCKET RATIOS ===")
    print(f"Low Vol  (≤{vol_low:.3f}): {low_bucket:.1%}")
    print(f"Mid Vol  ({vol_low:.3f}-{vol_high:.3f}): {mid_bucket:.1%}")
    print(f"High Vol (>{vol_high:.3f}): {high_bucket:.1%}")
    
    # Validation
    print(f"\n=== VALIDATION ===")
    if low_bucket > 0.8:
        print(f"⚠️  WARNING: {low_bucket:.1%} in low bucket - thresholds may still be too high")
    elif low_bucket < 0.3:
        print(f"⚠️  WARNING: Only {low_bucket:.1%} in low bucket - thresholds may be too low")
    else:
        print(f"✅ Low bucket ratio {low_bucket:.1%} looks reasonable (target: 40-60%)")
    
    if mid_bucket < 0.1:
        print(f"⚠️  WARNING: Only {mid_bucket:.1%} in mid bucket - need more regime diversity")
    else:
        print(f"✅ Mid bucket ratio {mid_bucket:.1%} shows good diversity")
    
    if high_bucket < 0.05:
        print(f"⚠️  WARNING: Only {high_bucket:.1%} in high bucket - may need lower high threshold")
    else:
        print(f"✅ High bucket ratio {high_bucket:.1%} shows volatility spikes detected")
    
    # Momentum analysis if available
    if len(mom_array) > 0:
        print(f"\n=== MOMENTUM ANALYSIS ===")
        print(f"Momentum stats:")
        print(f"  Mean: {mom_array.mean():.3f}")
        print(f"  Median: {np.median(mom_array):.3f}")
        print(f"  Abs Mean: {np.abs(mom_array).mean():.3f}")
        
        weak_mom = (np.abs(mom_array) <= mom_weak).mean()
        mid_mom = ((np.abs(mom_array) > mom_weak) & (np.abs(mom_array) <= mom_strong)).mean()
        strong_mom = (np.abs(mom_array) > mom_strong).mean()
        
        print(f"\nMomentum buckets:")
        print(f"Weak Mom  (≤{mom_weak:.1f}): {weak_mom:.1%}")
        print(f"Mid Mom   ({mom_weak:.1f}-{mom_strong:.1f}): {mid_mom:.1%}")
        print(f"Strong Mom (>{mom_strong:.1f}): {strong_mom:.1%}")
        
        if weak_mom > 0.8:
            print(f"⚠️  WARNING: {weak_mom:.1%} weak momentum - consider lowering mom_weak to 0.3")
        else:
            print(f"✅ Momentum distribution looks reasonable")
    
    print(f"\n=== RECOMMENDATION ===")
    if low_bucket <= 0.6 and mid_bucket >= 0.1 and high_bucket >= 0.05:
        print("✅ Thresholds look good for full backtest")
    else:
        print("⚠️  Consider further threshold adjustments before full backtest")

if __name__ == "__main__":
    analyze_volatility_buckets()
