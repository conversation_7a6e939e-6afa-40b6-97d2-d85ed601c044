#!/usr/bin/env python3
"""
Trace signal flow for specific hours to diagnose why only shorts are generated.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
import json

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.enhanced_regime_detector import EnhancedRegimeDetector
from hyperliquid_bot.modern.robust_data_loader import RobustDataLoader
from hyperliquid_bot.modern.signal_engine import ModernSignalEngine
from hyperliquid_bot.modern.tf_v3_modern import ModernTFV3Strategy
from hyperliquid_bot.modern.regime_state_manager import RegimeStateManager
from hyperliquid_bot.features.indicators import calculate_ema

def trace_hour(hour_data, detector, signal_engine, strategy, regime_manager, ohlcv_history):
    """Trace signal flow for a single hour."""
    timestamp = hour_data.name
    print(f"\n{'='*80}")
    print(f"TRACING HOUR: {timestamp}")
    print(f"{'='*80}")
    
    # 1. Prepare signals for detector
    detection_signals = {
        'atr_percent': hour_data.get('atr_percent_sec', 0),
        'ma_slope': hour_data.get('ma_slope', 0),
        'obi_smoothed_5': hour_data.get('volume_imbalance', 0),
        'spread_mean': hour_data.get('spread_mean', 0),
        'spread_std': hour_data.get('spread_std', 0),
        'volume': hour_data.get('volume', 0),
        'close': hour_data.get('close', 0),
        'atr_percent_sec': hour_data.get('atr_percent_sec', 0),
        'volume_imbalance': hour_data.get('volume_imbalance', 0)
    }
    
    # 2. Detect regime
    regime = detector.detect_regime(detection_signals, timestamp)
    confidence = detector.get_confidence()
    
    print(f"\n1. REGIME DETECTION:")
    print(f"   Regime: {regime}")
    print(f"   Confidence: {confidence:.3f}")
    print(f"   MA Slope: {detection_signals['ma_slope']:.6f}")
    print(f"   ATR %: {detection_signals['atr_percent']:.6f}")
    print(f"   Spread Mean: {detection_signals['spread_mean']:.6f}")
    
    # 3. Update regime manager
    regime_manager.update_state(
        timestamp=timestamp,
        state=regime,
        confidence=confidence,
        features=detection_signals
    )
    
    # Get current state as features dict
    current_state = regime_manager.get_current_state()
    regime_features = {
        'current_state': current_state.state if current_state else regime,
        'current_confidence': current_state.confidence if current_state else confidence,
        'timestamp': timestamp,
        'risk_suppressed': False
    }
    
    # 4. Calculate signals with signal engine
    signals = signal_engine.calculate_signals(ohlcv_history, regime_features)
    
    print(f"\n2. CALCULATED SIGNALS:")
    print(f"   Close: {signals.get('close', 'N/A')}")
    print(f"   EMA Fast: {signals.get('ema_fast', 'N/A')}")
    print(f"   EMA Slow: {signals.get('ema_slow', 'N/A')}")
    print(f"   Forecast: {signals.get('forecast', 'N/A')}")
    print(f"   ATR: {signals.get('atr_14', 'N/A')}")
    
    # 5. Evaluate with strategy
    entry_decision = strategy.evaluate_entry(signals, regime_features)
    
    print(f"\n3. STRATEGY EVALUATION:")
    if entry_decision:
        print(f"   Direction: {entry_decision.get('direction', 'None')}")
        print(f"   Confidence: {entry_decision.get('confidence', 0):.3f}")
        print(f"   Entry Reason: {entry_decision.get('entry_reason', 'N/A')}")
    else:
        print("   No entry signal")
        
        # Check why no signal
        ema_fast = signals.get('ema_fast', 0)
        ema_slow = signals.get('ema_slow', 0)
        forecast = signals.get('forecast', 0)
        close_price = signals.get('close', 1.0)
        forecast_threshold = close_price * 0.0001
        
        print(f"\n   DEBUG - Why no signal?")
        print(f"   - EMA alignment: Fast {ema_fast:.2f} {'>' if ema_fast > ema_slow else '<'} Slow {ema_slow:.2f}")
        print(f"   - Forecast: {forecast:.6f} (threshold: ±{forecast_threshold:.6f})")
        print(f"   - Regime trending? {regime in ['Strong_Bull_Trend', 'Weak_Bull_Trend', 'Strong_Bear_Trend', 'Weak_Bear_Trend']}")
        
        # Check specific conditions
        if regime in ['Strong_Bull_Trend', 'Weak_Bull_Trend']:
            print(f"   - Bull regime detected")
            if ema_fast > ema_slow:
                print(f"   - EMA aligned for LONG")
                if forecast > forecast_threshold:
                    print(f"   - Forecast positive - SHOULD HAVE LONG SIGNAL!")
                else:
                    print(f"   - Forecast too weak ({forecast:.6f} <= {forecast_threshold:.6f})")
            else:
                print(f"   - EMA not aligned for long (fast < slow)")
                
        elif regime in ['Strong_Bear_Trend', 'Weak_Bear_Trend']:
            print(f"   - Bear regime detected")
            if ema_fast < ema_slow:
                print(f"   - EMA aligned for SHORT")
                if forecast < -forecast_threshold:
                    print(f"   - Forecast negative - SHOULD HAVE SHORT SIGNAL!")
                else:
                    print(f"   - Forecast too weak ({forecast:.6f} >= {-forecast_threshold:.6f})")
            else:
                print(f"   - EMA not aligned for short (fast > slow)")
    
    return {
        'timestamp': timestamp,
        'regime': regime,
        'confidence': confidence,
        'signals': signals,
        'entry_decision': entry_decision
    }

def main():
    """Run the trace analysis."""
    # Load config
    config_path = Path(__file__).parent.parent / "configs/overrides/modern_system_v2_complete.yaml"
    config = load_config(str(config_path))
    
    # Create components
    detector = EnhancedRegimeDetector(config=config)
    signal_engine = ModernSignalEngine(config=config)
    strategy = ModernTFV3Strategy(config=config)
    regime_manager = RegimeStateManager(mode='backtest')
    regime_manager.detector = detector
    
    # Load data for specific dates
    data_loader = RobustDataLoader(config)
    
    # Pick some sample dates from 2024
    # Let's trace January 15-16, 2024 (mid-month to avoid initialization issues)
    start_date = datetime(2024, 1, 10)  # Load extra for warmup
    end_date = datetime(2024, 1, 17)
    
    print(f"Loading data from {start_date} to {end_date}")
    data = data_loader.load_data(start_date, end_date)
    
    if data.empty:
        print("ERROR: No data loaded")
        return
    
    print(f"Loaded {len(data)} hours of data")
    
    # Find hours with different regimes
    bull_hours = []
    bear_hours = []
    
    # First pass: identify regime distributions
    print("\nScanning for regime examples...")
    for idx, (timestamp, row) in enumerate(data.iterrows()):
        if idx < 50:  # Skip warmup
            continue
            
        detection_signals = {
            'atr_percent': row.get('atr_percent_sec', 0),
            'ma_slope': row.get('ma_slope', 0),
            'obi_smoothed_5': row.get('volume_imbalance', 0),
            'spread_mean': row.get('spread_mean', 0),
            'spread_std': row.get('spread_std', 0),
            'volume': row.get('volume', 0)
        }
        
        regime = detector.detect_regime(detection_signals, timestamp)
        
        if regime in ['Strong_Bull_Trend', 'Weak_Bull_Trend'] and len(bull_hours) < 3:
            bull_hours.append(idx)
        elif regime in ['Strong_Bear_Trend', 'Weak_Bear_Trend'] and len(bear_hours) < 3:
            bear_hours.append(idx)
    
    print(f"Found {len(bull_hours)} bull hours and {len(bear_hours)} bear hours")
    
    # Trace specific hours
    examples_to_trace = bull_hours[:2] + bear_hours[:2]
    
    results = []
    for idx in examples_to_trace:
        if idx >= len(data):
            continue
            
        hour_data = data.iloc[idx]
        
        # Get OHLCV history up to this point
        ohlcv_history = data.iloc[:idx+1].copy()
        
        result = trace_hour(
            hour_data, 
            detector, 
            signal_engine, 
            strategy, 
            regime_manager,
            ohlcv_history
        )
        results.append(result)
    
    # Summary
    print(f"\n{'='*80}")
    print("SUMMARY")
    print(f"{'='*80}")
    
    bull_signals = [r for r in results if 'Bull' in r['regime']]
    bear_signals = [r for r in results if 'Bear' in r['regime']]
    
    print(f"\nBull regime hours traced: {len(bull_signals)}")
    print(f"Bear regime hours traced: {len(bear_signals)}")
    
    long_signals = [r for r in results if r['entry_decision'] and r['entry_decision'].get('direction') == 'long']
    short_signals = [r for r in results if r['entry_decision'] and r['entry_decision'].get('direction') == 'short']
    
    print(f"\nLong signals generated: {len(long_signals)}")
    print(f"Short signals generated: {len(short_signals)}")
    
    if len(long_signals) == 0 and len(bull_signals) > 0:
        print("\nWARNING: No long signals despite bull regimes!")
        print("This explains why the backtest only has shorts.")

if __name__ == "__main__":
    main()