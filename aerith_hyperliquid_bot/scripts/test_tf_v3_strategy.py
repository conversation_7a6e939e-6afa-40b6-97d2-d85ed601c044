#!/usr/bin/env python3
"""
Simple test script for TFV3Strategy.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import yaml

# Add project root to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.strategies.tf_v3 import TFV3Strategy
from hyperliquid_bot.utils.state_mapping import map_gms_state

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/test_tf_v3_strategy.log', mode='w')
    ]
)

logger = logging.getLogger(__name__)

def create_mock_signals(atr_period=14):
    """Create mock signals for testing."""
    # Create a timestamp
    timestamp = pd.Timestamp('2025-03-01 00:00:00')

    # Create mock signals
    signals = {
        'timestamp': timestamp,
        'open': 50000.0,
        'high': 51000.0,
        'low': 49000.0,
        'close': 50500.0,
        'volume': 100.0,
        'regime': 'BULL',  # Set to BULL for testing
        'regime_timestamp': timestamp,
        'risk_suppressed': False,
    }

    # Add historical data for look-ahead safety
    # Create 200 rows of data to ensure we have enough for ATR calculation
    num_rows = 200
    ohlcv_history = pd.DataFrame({
        'timestamp': [pd.Timestamp('2025-03-01 00:00:00') - pd.Timedelta(minutes=i) for i in range(num_rows)],
        'open': np.linspace(49000, 50000, num_rows),
        'high': np.linspace(50000, 51000, num_rows),
        'low': np.linspace(48000, 49000, num_rows),
        'close': np.linspace(49500, 50500, num_rows),
        'volume': np.ones(num_rows) * 100,
    })
    # Set timestamp as index
    ohlcv_history = ohlcv_history.set_index('timestamp')
    signals['ohlcv_history'] = ohlcv_history

    # Add EMA values
    signals[f'ema_20'] = 49800.0
    signals[f'ema_50'] = 49500.0

    # Add ATR value
    signals[f'atr_{atr_period}'] = 1000.0  # Use the ATR period from the strategy
    signals[f'atr'] = 1000.0
    signals[f'atr_percent'] = 0.02  # 2% of price
    signals[f'atr_14_sec'] = 1000.0
    signals[f'atr_percent_sec'] = 0.02

    # Add GMS required signals
    signals[f'obi_smoothed_5'] = 0.15  # Positive OBI (buy pressure)
    signals[f'ma_slope'] = 100.0  # Strong upward slope
    signals[f'spread_mean'] = 0.0001  # 0.01% spread
    signals[f'spread_std'] = 0.00005  # Low spread volatility

    return signals

def test_tf_v3_strategy():
    """Test the TFV3Strategy."""
    # Load config
    config_path = 'aerith_hyperliquid_bot/configs/base.yaml'
    logger.info(f"Loading configuration from {config_path}...")

    try:
        with open(config_path, 'r') as f:
            config_dict = yaml.safe_load(f)
        config = Config.model_validate(config_dict)
        logger.info("Configuration loaded successfully")
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return False

    # Create TFV3Strategy
    logger.info("Creating TFV3Strategy...")
    strategy = TFV3Strategy(config, "tf_v3")
    logger.info("TFV3Strategy created successfully")

    # Get the ATR period from the strategy
    atr_period = strategy.tf_v3_config.atr_period
    logger.info(f"ATR period from strategy config: {atr_period}")

    # Create mock signals
    logger.info("Creating mock signals...")
    signals = create_mock_signals(atr_period)
    logger.info(f"Mock signals created: {signals}")

    # Test mapping GMS states
    logger.info("Testing GMS state mapping...")
    for state in ['Strong_Bull_Trend', 'Weak_Bull_Trend', 'Strong_Bear_Trend', 'Weak_Bear_Trend']:
        mapped_state = map_gms_state(state)
        logger.info(f"Mapped {state} to {mapped_state}")

    # Skip the calculate_indicators method and use our own values
    logger.info("Testing evaluate method directly...")
    try:
        # Create a copy of the signals with our own indicators
        signals_with_indicators = signals.copy()

        # Add GMS snapshot
        gms_snapshot = {
            'timestamp': signals['timestamp'],
            'state': 'BULL',
            'risk_suppressed': False,
            'age_sec': 0
        }
        signals_with_indicators['gms_snapshot'] = gms_snapshot

        # Test evaluate method
        logger.info("Testing evaluate method...")

        # Monkey patch the _calculate_indicators method to return our signals
        original_calculate_indicators = strategy._calculate_indicators

        def mock_calculate_indicators(signals):
            result = signals.copy()
            result[f'atr_{atr_period}'] = 1000.0
            result[f'ema_{strategy.tf_v3_config.ema_fast}'] = 49800.0
            result[f'ema_{strategy.tf_v3_config.ema_slow}'] = 49500.0
            return result

        # Replace the method
        strategy._calculate_indicators = mock_calculate_indicators

        # Call evaluate
        direction, info = strategy.evaluate(signals_with_indicators)

        # Restore the original method
        strategy._calculate_indicators = original_calculate_indicators

        logger.info(f"Evaluate result: direction={direction}, info={info}")
        return direction is not None
    except Exception as e:
        logger.error(f"Error evaluating strategy: {e}", exc_info=True)
        return False

if __name__ == '__main__':
    logger.info("Starting TFV3Strategy test...")
    success = test_tf_v3_strategy()
    if success:
        logger.info("✅ TFV3Strategy test passed!")
        sys.exit(0)
    else:
        logger.error("❌ TFV3Strategy test failed!")
        sys.exit(1)
