#!/usr/bin/env python3
"""
Batch Create Enhanced Hourly Data

Process full date ranges to create enhanced hourly data for the entire dataset.
Includes progress tracking, error handling, and resumption capability.
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta
import argparse
import logging
import json
from typing import List, Set
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from scripts.create_enhanced_hourly_data import EnhancedHourlyResampler

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BatchProcessor:
    """Batch process multiple dates for enhanced hourly data creation."""
    
    def __init__(self, data_dir: Path, num_workers: int = None):
        """
        Initialize batch processor.
        
        Args:
            data_dir: Root data directory
            num_workers: Number of parallel workers (default: CPU count - 1)
        """
        self.data_dir = data_dir
        self.num_workers = num_workers or max(1, multiprocessing.cpu_count() - 1)
        self.progress_file = data_dir / "enhanced_hourly" / "processing_progress.json"
        self.resampler = EnhancedHourlyResampler(data_dir)
        
    def load_progress(self) -> Set[str]:
        """Load set of already processed dates."""
        if self.progress_file.exists():
            with open(self.progress_file, 'r') as f:
                progress = json.load(f)
                return set(progress.get('completed_dates', []))
        return set()
    
    def save_progress(self, completed_dates: Set[str]):
        """Save processing progress."""
        self.progress_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.progress_file, 'w') as f:
            json.dump({
                'completed_dates': sorted(list(completed_dates)),
                'last_updated': datetime.now().isoformat()
            }, f, indent=2)
    
    def get_date_range(self, start_date: str, end_date: str) -> List[str]:
        """Generate list of dates between start and end."""
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        
        dates = []
        current = start
        while current <= end:
            dates.append(current.strftime("%Y-%m-%d"))
            current += timedelta(days=1)
        
        return dates
    
    def process_date_wrapper(self, date: str) -> tuple:
        """Wrapper for process_single_date that returns status."""
        try:
            success = self.resampler.process_single_date(date)
            return (date, success, None)
        except Exception as e:
            return (date, False, str(e))
    
    def process_date_range(
        self, 
        start_date: str, 
        end_date: str,
        resume: bool = True,
        dry_run: bool = False
    ) -> dict:
        """
        Process a range of dates in parallel.
        
        Args:
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            resume: Whether to resume from previous progress
            dry_run: If True, only show what would be processed
            
        Returns:
            Dictionary with processing statistics
        """
        # Get all dates in range
        all_dates = self.get_date_range(start_date, end_date)
        logger.info(f"Total dates in range: {len(all_dates)}")
        
        # Load progress if resuming
        completed_dates = self.load_progress() if resume else set()
        
        # Filter out already completed dates
        dates_to_process = [d for d in all_dates if d not in completed_dates]
        
        if not dates_to_process:
            logger.info("All dates already processed!")
            return {
                'total': len(all_dates),
                'already_completed': len(completed_dates),
                'processed': 0,
                'successful': 0,
                'failed': 0
            }
        
        logger.info(f"Dates to process: {len(dates_to_process)}")
        logger.info(f"Already completed: {len(completed_dates)}")
        
        if dry_run:
            logger.info("DRY RUN - Would process these dates:")
            for date in dates_to_process[:10]:
                logger.info(f"  {date}")
            if len(dates_to_process) > 10:
                logger.info(f"  ... and {len(dates_to_process) - 10} more")
            return {}
        
        # Process dates in parallel
        successful = 0
        failed = 0
        failed_dates = []
        
        logger.info(f"Starting parallel processing with {self.num_workers} workers")
        
        with ProcessPoolExecutor(max_workers=self.num_workers) as executor:
            # Submit all tasks
            future_to_date = {
                executor.submit(self.process_date_wrapper, date): date 
                for date in dates_to_process
            }
            
            # Process completed tasks
            for future in as_completed(future_to_date):
                date, success, error = future.result()
                
                if success:
                    successful += 1
                    completed_dates.add(date)
                    # Save progress periodically
                    if successful % 10 == 0:
                        self.save_progress(completed_dates)
                else:
                    failed += 1
                    failed_dates.append((date, error))
                    logger.error(f"Failed to process {date}: {error}")
                
                # Progress update
                total_processed = successful + failed
                if total_processed % 10 == 0:
                    logger.info(
                        f"Progress: {total_processed}/{len(dates_to_process)} "
                        f"({successful} successful, {failed} failed)"
                    )
        
        # Save final progress
        self.save_progress(completed_dates)
        
        # Final report
        logger.info("\n" + "="*50)
        logger.info("BATCH PROCESSING COMPLETE")
        logger.info("="*50)
        logger.info(f"Total dates in range: {len(all_dates)}")
        logger.info(f"Already completed: {len(all_dates) - len(dates_to_process)}")
        logger.info(f"Processed this run: {successful + failed}")
        logger.info(f"Successful: {successful}")
        logger.info(f"Failed: {failed}")
        
        if failed_dates:
            logger.info("\nFailed dates:")
            for date, error in failed_dates[:10]:
                logger.info(f"  {date}: {error}")
            if len(failed_dates) > 10:
                logger.info(f"  ... and {len(failed_dates) - 10} more")
        
        return {
            'total': len(all_dates),
            'already_completed': len(all_dates) - len(dates_to_process),
            'processed': successful + failed,
            'successful': successful,
            'failed': failed,
            'failed_dates': failed_dates
        }


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Batch create enhanced hourly data from 1-second features"
    )
    parser.add_argument(
        "--data-dir",
        type=Path,
        default=Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data"),
        help="Root data directory"
    )
    parser.add_argument(
        "--start",
        type=str,
        required=True,
        help="Start date (YYYY-MM-DD)"
    )
    parser.add_argument(
        "--end",
        type=str,
        required=True,
        help="End date (YYYY-MM-DD)"
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=None,
        help="Number of parallel workers (default: CPU count - 1)"
    )
    parser.add_argument(
        "--no-resume",
        action="store_true",
        help="Don't resume from previous progress"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be processed without actually processing"
    )
    
    args = parser.parse_args()
    
    # Create batch processor
    processor = BatchProcessor(args.data_dir, args.workers)
    
    # Process date range
    stats = processor.process_date_range(
        args.start,
        args.end,
        resume=not args.no_resume,
        dry_run=args.dry_run
    )
    
    # Exit with error if any failures
    if stats and stats.get('failed', 0) > 0:
        sys.exit(1)


if __name__ == "__main__":
    main()