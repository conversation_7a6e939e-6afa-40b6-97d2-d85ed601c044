#!/usr/bin/env python3
"""
Run Full 2024 Modern System Backtest - Calibrated
=================================================

This script runs the modern system for the entire 2024 year with:
- Calibrated regime thresholds for ~100-200 trades
- Full system integrity checks
- Fallback detection
- Performance comparison with legacy system

Target: Match or exceed legacy system's 180 trades, +215% ROI
"""

import argparse
import sys
from pathlib import Path
from datetime import datetime
import json
import logging

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine


def setup_logging(verbose: bool = False):
    """Set up logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def main():
    """Run modern system backtest for full 2024 with calibrated settings."""
    parser = argparse.ArgumentParser(
        description="Run calibrated modern system backtest for 2024"
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--output',
        type=str,
        default='modern_2024_calibrated_results.json',
        help='Output file for results'
    )
    
    args = parser.parse_args()
    
    # Set up logging
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    print("="*80)
    print("AERITH MODERN SYSTEM - 2024 FULL YEAR BACKTEST (CALIBRATED)")
    print("="*80)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Full 2024 dates
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 12, 31, 23, 59, 59)
    
    # Load configuration
    config_path = project_root / "configs/overrides/modern_system_v2_complete.yaml"
    logger.info(f"Loading config: {config_path}")
    config = load_config(config_path=str(config_path))
    
    print(f"📅 Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    print(f"⚙️  Config: modern_system_v2_complete.yaml")
    print()
    print("🎯 CALIBRATED SETTINGS:")
    print("  - Confidence threshold: 0.5 (was 0.6)")
    print("  - State persistence: 0.2 (was 0.5)")
    print("  - Max transitions/hour: 60 (was 30)")
    print("  - Risk per trade: 25%")
    print()
    print("📊 TARGET PERFORMANCE:")
    print("  - Trades: 100-200 (legacy: 180)")
    print("  - ROI: >100% (legacy: +215%)")
    print()
    print("-"*80)
    
    try:
        # Create modern backtesting engine
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        # Run backtest
        print("\n🚀 Starting backtest... This will take several minutes.\n")
        results = engine.run_backtest()
        
        # Extract key metrics
        total_trades = results['performance']['total_trades']
        total_return = results['performance']['total_return']
        win_rate = results['performance']['win_rate']
        runtime = results['runtime_seconds']
        integrity = results.get('system_integrity', {})
        
        print("\n" + "="*80)
        print("📊 2024 FULL YEAR RESULTS")
        print("="*80)
        print(f"Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        print(f"Runtime: {runtime/60:.1f} minutes")
        print()
        print("PERFORMANCE METRICS:")
        print(f"  - Total Trades: {total_trades}")
        print(f"  - Total Return: {total_return:.2%}")
        print(f"  - Win Rate: {win_rate:.2%}")
        print(f"  - Sharpe Ratio: {results['performance'].get('sharpe_ratio', 0):.2f}")
        print(f"  - Max Drawdown: {results['performance'].get('max_drawdown', 0):.2%}")
        print()
        
        # Compare with legacy
        print("COMPARISON WITH LEGACY SYSTEM:")
        legacy_trades = 180
        legacy_roi = 2.15  # 215%
        
        trade_ratio = total_trades / legacy_trades if legacy_trades > 0 else 0
        roi_ratio = total_return / legacy_roi if legacy_roi > 0 else 0
        
        print(f"  - Trade Count: {total_trades}/{legacy_trades} ({trade_ratio:.1%})")
        print(f"  - ROI: {total_return:.2%}/{legacy_roi:.0%} ({roi_ratio:.1%})")
        
        if total_trades >= 100 and total_trades <= 300:
            print("  ✅ Trade count in target range (100-300)")
        else:
            print("  ❌ Trade count outside target range")
            
        if total_return > 1.0:  # > 100%
            print("  ✅ ROI exceeds minimum target (>100%)")
        else:
            print("  ❌ ROI below minimum target")
        
        print()
        
        # Execution quality
        if 'execution_stats' in results['performance']:
            exec_stats = results['performance']['execution_stats']
            print("EXECUTION QUALITY:")
            print(f"  - Avg Quality Score: {exec_stats.get('avg_quality_score', 0):.1f}")
            print(f"  - Long trades: {exec_stats.get('long_count', 0)}")
            print(f"  - Short trades: {exec_stats.get('short_count', 0)}")
            print()
        
        # Trade distribution
        if results['trades']:
            trades_by_month = {}
            for trade in results['trades']:
                month = trade['timestamp'].strftime('%Y-%m')
                trades_by_month[month] = trades_by_month.get(month, 0) + 1
            
            print("MONTHLY DISTRIBUTION:")
            for month in sorted(trades_by_month.keys()):
                count = trades_by_month[month]
                print(f"  {month}: {'█' * (count // 2)} ({count})")
            print()
        
        # Save detailed results
        output_path = Path(args.output)
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"💾 Detailed results saved to: {output_path}")
        print()
        
        # Final assessment
        print("="*80)
        print("FINAL ASSESSMENT")
        print("="*80)
        
        if integrity.get('is_pure_modern', False):
            print("✅ System Integrity: PURE MODERN (no legacy fallbacks)")
        else:
            print("❌ System Integrity: LEGACY CONTAMINATION DETECTED")
            
        if total_trades > 0:
            if total_trades >= 100 and total_return > 1.0:
                print("✅ CALIBRATION SUCCESSFUL: System meets performance targets")
            elif total_trades >= 50:
                print("⚠️  PARTIAL SUCCESS: Generating trades but needs tuning")
            else:
                print("❌ NEEDS MORE CALIBRATION: Too few trades")
        else:
            print("❌ CRITICAL: No trades generated")
            
        print("="*80)
        
    except Exception as e:
        print("\n" + "="*80)
        print(f"❌ Backtest failed: {e}")
        logger.error(f"Backtest failed: {e}", exc_info=True)
        print("="*80)
        sys.exit(1)


if __name__ == "__main__":
    main()