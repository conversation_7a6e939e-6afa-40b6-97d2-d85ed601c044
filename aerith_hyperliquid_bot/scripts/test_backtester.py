#!/usr/bin/env python3
"""
Simple test script for the backtester.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from hyperliquid_bot.config.settings import Config, load_config
from hyperliquid_bot.backtester.backtester import Backtester
from hyperliquid_bot.strategies.tf_v3 import TFV3Strategy

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/test_backtester.log', mode='w')
    ]
)

logger = logging.getLogger(__name__)

def test_backtester():
    """Test the backtester."""
    # Load config
    config_path = 'aerith_hyperliquid_bot/configs/base.yaml'
    logger.info(f"Loading configuration from {config_path}...")

    try:
        config = load_config(config_path)
        logger.info("Configuration loaded successfully")
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return False

    # Ensure TF-v3 is enabled
    config.strategies.use_tf_v3 = True
    config.strategies.use_mean_variance = False
    config.strategies.use_obi_scalper = False

    # Ensure granular_microstructure detector is used
    config.regime.detector_type = 'granular_microstructure'

    # Create a backtester
    logger.info("Creating backtester...")
    backtester = Backtester(config)
    logger.info("Backtester created successfully")

    # Get the strategy instance
    logger.info("Getting TF-v3 strategy instance...")
    strategy = backtester.strategy_evaluator.get_strategy('tf_v3')
    if not strategy:
        logger.error("TF-v3 strategy not found")
        return False
    logger.info(f"Found strategy: {strategy}")

    # Create a test signal
    logger.info("Creating test signal...")

    # Create OHLCV history
    num_rows = 200
    timestamp = pd.Timestamp('2025-03-01 00:00:00')
    timestamps = [timestamp - pd.Timedelta(minutes=i) for i in range(num_rows)]

    ohlcv_history = pd.DataFrame({
        'timestamp': timestamps,
        'open': np.linspace(49000, 50000, num_rows),
        'high': np.linspace(50000, 51000, num_rows),
        'low': np.linspace(48000, 49000, num_rows),
        'close': np.linspace(49500, 50500, num_rows),
        'volume': np.ones(num_rows) * 100,
    })

    # Set timestamp as index
    ohlcv_history = ohlcv_history.set_index('timestamp')

    test_signal = {
        'timestamp': pd.Timestamp('2025-03-01 00:00:00'),
        'open': 50000.0,
        'high': 51000.0,
        'low': 49000.0,
        'close': 50500.0,
        'volume': 100.0,
        'atr_14': 1000.0,
        'atr_14_sec': 1000.0,
        'atr': 1000.0,
        'atr_percent': 0.02,  # 2% of price
        'atr_percent_sec': 0.02,
        'ema_20': 49800.0,
        'ema_50': 49500.0,
        'obi_smoothed_5': 0.15,  # Positive OBI (buy pressure)
        'ma_slope': 100.0,  # Strong upward slope
        'spread_mean': 0.0001,  # 0.01% spread
        'spread_std': 0.00005,  # Low spread volatility
        'ohlcv_history': ohlcv_history,
        'gms_snapshot': {
            'timestamp': pd.Timestamp('2025-03-01 00:00:00'),
            'state': 'Weak_Bull_Trend',
            'risk_suppressed': False,
            'age_sec': 0
        }
    }

    # Test the strategy
    logger.info("Testing strategy with test signal...")
    try:
        # Monkey patch the _calculate_indicators method to return our signals
        original_calculate_indicators = strategy._calculate_indicators

        def mock_calculate_indicators(signals):
            logger.info("Using mock _calculate_indicators method")
            result = signals.copy()
            result[f'atr_{strategy.tf_v3_config.atr_period}'] = 1000.0
            result[f'ema_{strategy.tf_v3_config.ema_fast}'] = 49800.0
            result[f'ema_{strategy.tf_v3_config.ema_slow}'] = 49500.0
            return result

        # Replace the method
        strategy._calculate_indicators = mock_calculate_indicators

        # Call evaluate
        direction, info = strategy.evaluate(test_signal)

        # Restore the original method
        strategy._calculate_indicators = original_calculate_indicators

        logger.info(f"Test result: direction={direction}, info={info}")
        if direction is None:
            logger.error("Strategy returned None direction")
            return False
    except Exception as e:
        logger.error(f"Error evaluating strategy: {e}", exc_info=True)
        return False

    # Run a short backtest
    logger.info("Running short backtest...")
    start_date = datetime(2025, 3, 1)
    end_date = datetime(2025, 3, 2)
    try:
        results = backtester.run(start_date, end_date)
        logger.info("Backtest completed successfully")
        logger.info(f"Results: {results}")
        return True
    except Exception as e:
        logger.error(f"Error running backtest: {e}", exc_info=True)
        return False

if __name__ == '__main__':
    logger.info("Starting backtester test...")
    success = test_backtester()
    if success:
        logger.info("✅ Backtester test passed!")
        sys.exit(0)
    else:
        logger.error("❌ Backtester test failed!")
        sys.exit(1)
