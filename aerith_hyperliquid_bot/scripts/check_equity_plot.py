#!/usr/bin/env python3
"""
Script to check if equity curve plot is being generated during backtest.
This helps debug why the plot might not be appearing with run_execution_refinement.py
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import subprocess

def check_log_directory():
    """Check the log directory for equity curve plots."""
    # Get the project root
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # Check default log directory
    log_dir = project_root / "logs"
    
    print(f"Checking log directory: {log_dir}")
    
    if not log_dir.exists():
        print(f"Log directory does not exist: {log_dir}")
        return
    
    # List all PNG files in the log directory
    png_files = list(log_dir.glob("*.png"))
    
    if not png_files:
        print("No PNG files found in log directory")
    else:
        print(f"\nFound {len(png_files)} PNG files:")
        # Sort by modification time (most recent first)
        png_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        for i, png_file in enumerate(png_files[:10]):  # Show only 10 most recent
            mod_time = datetime.fromtimestamp(png_file.stat().st_mtime)
            size_kb = png_file.stat().st_size / 1024
            print(f"  {i+1}. {png_file.name}")
            print(f"     Modified: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"     Size: {size_kb:.1f} KB")
            
            # Check if it's an equity curve plot
            if "equity" in png_file.name.lower():
                print(f"     -> This is an equity curve plot!")

def run_test_backtest():
    """Run a simple backtest to test plot generation."""
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # Run the execution refinement script
    refinement_script = script_dir / "run_execution_refinement.py"
    
    print(f"\n{'='*60}")
    print("Running backtest with execution refinement...")
    print(f"{'='*60}\n")
    
    try:
        # Run the script and capture output
        result = subprocess.run(
            [sys.executable, str(refinement_script), "--run-id", "plot_test"],
            capture_output=True,
            text=True,
            check=False
        )
        
        # Check for plot generation in output
        output_lines = result.stdout.split('\n') + result.stderr.split('\n')
        
        plot_generated = False
        plot_error = False
        
        for line in output_lines:
            if "equity curve plot saved to" in line.lower():
                print(f"✓ Found plot generation message: {line.strip()}")
                plot_generated = True
            elif "failed to plot equity curve" in line.lower():
                print(f"✗ Found plot error: {line.strip()}")
                plot_error = True
            elif "not enough equity data points" in line.lower():
                print(f"✗ Insufficient data for plot: {line.strip()}")
                plot_error = True
        
        if not plot_generated and not plot_error:
            print("⚠ No plot generation messages found in output")
            print("\nSearching for any mentions of 'plot' or 'equity' in output...")
            for line in output_lines:
                if 'plot' in line.lower() or 'equity' in line.lower():
                    print(f"  - {line.strip()}")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"Error running backtest: {e}")
        return False

def main():
    """Main function to check equity plot generation."""
    print("Equity Curve Plot Checker")
    print("=" * 60)
    
    # First check existing plots
    print("\n1. Checking existing plots in log directory:")
    check_log_directory()
    
    # Ask if user wants to run a test
    print(f"\n{'='*60}")
    response = input("\nRun a test backtest to check plot generation? (y/N): ")
    
    if response.lower() == 'y':
        success = run_test_backtest()
        
        if success:
            print("\n2. Checking for new plots after test run:")
            check_log_directory()
        else:
            print("\nBacktest failed. Check the logs for errors.")
    
    print("\nDone!")

if __name__ == "__main__":
    main()