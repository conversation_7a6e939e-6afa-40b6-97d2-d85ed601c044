#!/usr/bin/env python3
"""
Test script to verify that the spread feature fix works correctly.

This script creates synthetic L2 data and runs it through the ETL pipeline
to verify that spread_mean and spread_std columns are properly generated.
"""

import os
import sys
import tempfile
import json
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from tools.etl_l20_to_1s import load_raw_l2_data, calculate_features, resample_to_1s, calculate_post_resample_features


def create_synthetic_l2_data(num_records: int = 3600) -> list:
    """
    Create synthetic L2 data for testing.
    
    Args:
        num_records: Number of records to generate (default: 3600 for 1 hour at 10Hz)
        
    Returns:
        List of L2 data records
    """
    print(f"Creating {num_records} synthetic L2 records...")
    
    records = []
    base_time = int(datetime(2025, 3, 5, 10, 0, 0).timestamp() * 1000)  # Start at 10:00 AM
    base_price = 50000.0
    
    for i in range(num_records):
        # Generate timestamp (10Hz = 100ms intervals)
        timestamp = base_time + (i * 100)
        
        # Generate price with some random walk
        price_offset = 10 * np.sin(i * 0.01) + 5 * np.random.randn()
        mid_price = base_price + price_offset
        
        # Generate spread (varies between 0.005 and 0.02)
        spread = 0.005 + 0.015 * (0.5 + 0.5 * np.sin(i * 0.001)) + 0.001 * np.random.randn()
        spread = max(0.001, spread)  # Ensure positive spread
        
        bid_price = mid_price - spread / 2
        ask_price = mid_price + spread / 2
        
        # Create L2 data structure
        bids = []
        asks = []
        
        # Generate 5 levels of depth
        for level in range(5):
            bid_level_price = bid_price - level * 0.01
            ask_level_price = ask_price + level * 0.01
            size = 10.0 + 5 * np.random.randn()
            size = max(0.1, size)  # Ensure positive size
            
            bids.append({"px": str(bid_level_price), "sz": str(size)})
            asks.append({"px": str(ask_level_price), "sz": str(size)})
        
        record = {
            "raw": {
                "data": {
                    "time": timestamp,
                    "levels": [bids, asks]
                }
            }
        }
        
        records.append(record)
    
    return records


def create_test_data_file(records: list, file_path: str) -> None:
    """
    Create a test data file in JSON lines format.
    
    Args:
        records: List of L2 data records
        file_path: Path to output file
    """
    print(f"Writing {len(records)} records to {file_path}")
    
    with open(file_path, 'w') as f:
        for record in records:
            f.write(json.dumps(record) + '\n')


def test_etl_pipeline_with_spread_fix():
    """
    Test the ETL pipeline to verify spread_mean and spread_std are generated.
    """
    print("=== Testing ETL Pipeline with Spread Fix ===")
    
    # Create temporary directory for test data
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create synthetic L2 data
        records = create_synthetic_l2_data(3600)  # 1 hour of data at 10Hz
        
        # Create test data file
        test_file = temp_path / "test_l2_data.txt"
        create_test_data_file(records, str(test_file))
        
        print(f"\n=== Step 1: Loading raw L2 data ===")
        # Load raw data
        df_raw = load_raw_l2_data(str(test_file))
        print(f"Loaded {len(df_raw)} raw records")
        print(f"Columns: {list(df_raw.columns)}")
        
        print(f"\n=== Step 2: Calculating features ===")
        # Calculate features
        df_features = calculate_features(df_raw, depth=5)
        print(f"Features calculated. Columns: {list(df_features.columns)}")
        
        # Check if basic spread is calculated
        if 'spread' in df_features.columns:
            spread_stats = df_features['spread'].describe()
            print(f"Basic spread stats:\n{spread_stats}")
        else:
            print("ERROR: Basic spread column not found!")
            return False
        
        print(f"\n=== Step 3: Resampling to 1-second ===")
        # Resample to 1-second
        df_resampled = resample_to_1s(df_features, method='median')
        print(f"Resampled to {len(df_resampled)} 1-second records")
        
        print(f"\n=== Step 4: Calculating post-resample features (including spread stats) ===")
        # Calculate post-resample features (this should add spread_mean and spread_std)
        df_final = calculate_post_resample_features(df_resampled)
        print(f"Final features calculated. Columns: {list(df_final.columns)}")
        
        print(f"\n=== Step 5: Verifying spread_mean and spread_std ===")
        # Check if spread_mean and spread_std exist
        required_columns = ['spread_mean', 'spread_std']
        missing_columns = [col for col in required_columns if col not in df_final.columns]
        
        if missing_columns:
            print(f"ERROR: Missing required columns: {missing_columns}")
            return False
        
        print("✅ Required columns found: spread_mean, spread_std")
        
        # Check NaN ratios
        for col in required_columns:
            nan_count = df_final[col].isna().sum()
            total_count = len(df_final)
            nan_ratio = nan_count / total_count
            print(f"{col}: {nan_count}/{total_count} NaN ({nan_ratio:.1%})")
            
            if nan_ratio > 0.05:  # Allow up to 5% NaN for warmup
                print(f"WARNING: High NaN ratio for {col}: {nan_ratio:.1%}")
            else:
                print(f"✅ {col} NaN ratio acceptable: {nan_ratio:.1%}")
        
        # Show sample values
        print(f"\n=== Sample Values ===")
        sample_data = df_final[['timestamp', 'spread', 'spread_mean', 'spread_std']].tail(10)
        print(sample_data)
        
        # Check that values are reasonable
        spread_mean_valid = df_final['spread_mean'].dropna()
        spread_std_valid = df_final['spread_std'].dropna()
        
        if len(spread_mean_valid) > 0:
            print(f"\nspread_mean stats: min={spread_mean_valid.min():.6f}, max={spread_mean_valid.max():.6f}, mean={spread_mean_valid.mean():.6f}")
        
        if len(spread_std_valid) > 0:
            print(f"spread_std stats: min={spread_std_valid.min():.6f}, max={spread_std_valid.max():.6f}, mean={spread_std_valid.mean():.6f}")
        
        print(f"\n=== Test Result ===")
        if len(spread_mean_valid) > 0 and len(spread_std_valid) > 0:
            print("✅ SUCCESS: spread_mean and spread_std are properly calculated!")
            return True
        else:
            print("❌ FAILURE: spread_mean and/or spread_std have no valid values!")
            return False


if __name__ == "__main__":
    success = test_etl_pipeline_with_spread_fix()
    sys.exit(0 if success else 1)
