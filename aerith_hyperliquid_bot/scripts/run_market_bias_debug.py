#!/usr/bin/env python3
"""
Market Bias Debug Test Runner

This script runs a backtest using our enhanced debug backtester
and risk manager to validate market bias settings.
"""

import sys
import logging
import argparse
from pathlib import Path
import yaml
from datetime import datetime, timedelta

# Add project root to PYTHONPATH
project_root = Path(__file__).parent.parent.resolve()
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.backtester.debug_backtester import DebugBacktester
from hyperliquid_bot.portfolio import Portfolio
from hyperliquid_bot.data import HistoricalDataH<PERSON><PERSON>


def setup_logging(output_dir, run_id=None):
    """Configure logging for the debug session"""
    # Ensure log directory exists
    log_dir = Path(output_dir)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Set up log file name with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if run_id:
        log_filename = f"market_bias_debug_{run_id}_{timestamp}.log"
    else:
        log_filename = f"market_bias_debug_{timestamp}.log"
    log_path = log_dir / log_filename
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    
    # Remove any existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create file handler with detailed format
    file_format = '%(asctime)s [%(levelname)-7s] %(name)-25s: %(message)s'
    file_formatter = logging.Formatter(file_format, datefmt='%Y-%m-%d %H:%M:%S')
    file_handler = logging.FileHandler(log_path, mode='w')
    file_handler.setLevel(logging.DEBUG)  # Log everything to file
    file_handler.setFormatter(file_formatter)
    
    # Create console handler with simpler format
    console_format = '[%(levelname)-7s] %(name)-20s: %(message)s'
    console_formatter = logging.Formatter(console_format)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(console_formatter)
    
    # Add both handlers to root logger
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # Set specific loggers to higher levels to reduce noise
    logging.getLogger("matplotlib").setLevel(logging.WARNING)
    
    # Set RiskManager to DEBUG to capture all market bias logs
    logging.getLogger("RiskManager").setLevel(logging.DEBUG)
    logging.getLogger("DebugRiskManager").setLevel(logging.DEBUG)
    
    return log_path


def load_config(base_config_path, override_config_path=None):
    """Load configuration with optional overrides"""
    # Load base config first
    with open(base_config_path, 'r') as f:
        base_config_data = yaml.safe_load(f)
    
    # Load override config if provided
    if override_config_path:
        with open(override_config_path, 'r') as f:
            override_config_data = yaml.safe_load(f)
        
        # Merge override into base
        from deepmerge import always_merger
        config_data = always_merger.merge(base_config_data, override_config_data)
    else:
        config_data = base_config_data
    
    # Create Config object
    return Config(**config_data)


def run_debug_backtest(config_path, run_id=None):
    """Run a backtest with the debug backtester and enhanced logging"""
    logger = logging.getLogger(__name__)
    logger.info(f"Starting Market Bias Debug Backtest (Run ID: {run_id or 'unspecified'})")
    
    # Load configuration
    project_root = Path(__file__).parent.parent.resolve()
    base_config_path = project_root / "configs" / "base.yaml"
    config = load_config(base_config_path, config_path)
    
    # Set up logging with output to results directory
    output_dir = project_root / "results" / "market_bias_debug"
    log_path = setup_logging(output_dir, run_id)
    logger.info(f"Logging to: {log_path}")
    
    # Extract symbol and timeframe from config
    symbol = "BTC-PERP"  # Default if not found
    timeframe = config.timeframe or "1h"
    
    # Configure backtesting dates based on period_preset
    if config.backtest.period_preset == 'custom':
        if hasattr(config.backtest, 'custom_start_date') and hasattr(config.backtest, 'custom_end_date'):
            start_date = config.backtest.custom_start_date
            end_date = config.backtest.custom_end_date
            # Add one day to end_date for inclusive range
            if not isinstance(end_date, datetime):
                end_date = datetime.strptime(end_date, "%Y-%m-%d")
            end_date = end_date + timedelta(days=1)
            
            logger.info(f"Using custom date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        else:
            raise ValueError("Custom start/end date required when period_preset is 'custom'")
    else:
        # Default to a two-week test period if not custom
        logger.info("Using default two-week test period")
        start_date = datetime.now() - timedelta(days=14)
        end_date = datetime.now()
    
    logger.info(f"Backtest period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    logger.info(f"Symbol: {symbol}, Timeframe: {timeframe}")
    
    # Initialize data handler - passing only the required config
    data_handler = HistoricalDataHandler(config, logger)
    
    # Initialize portfolio with the data handler
    portfolio = Portfolio(config, data_handler)
    
    # Initialize debug backtester
    backtester = DebugBacktester(config=config)
    
    # Run backtest
    logger.info("Running backtest with debug-enhanced components...")
    logger.info("DIRECTION BIAS DEBUG: Using enhanced risk manager to track direction information")
    
    try:
        # Load data first
        logger.info(f"Loading historical data for {symbol}...")
        # Call load_historical_data with proper parameters
        data_handler.load_historical_data(start_date=start_date, end_date=end_date)
        
        # Run backtest
        results = backtester.run_backtest(
            portfolio=portfolio,
            data_handler=data_handler
        )
        
        # Create results directory if it doesn't exist
        results_dir = output_dir
        results_dir.mkdir(parents=True, exist_ok=True)
        
        # Save log path for reference
        with open(results_dir / f"log_path_{run_id}.txt", "w") as f:
            f.write(f"Log path: {log_path}\n")
            f.write(f"Run ID: {run_id}\n")
            f.write(f"Symbol: {symbol}\n")
            f.write(f"Timeframe: {timeframe}\n")
            f.write(f"Start date: {start_date.strftime('%Y-%m-%d')}\n")
            f.write(f"End date: {end_date.strftime('%Y-%m-%d')}\n")
            f.write(f"ROI: {results['roi']:.2f}%\n")
            f.write(f"Trades: {len(results['trades'])}\n")
        
    except Exception as e:
        logger.error(f"Error running backtest: {e}", exc_info=True)
        return {"error": str(e)}
    
    
    # Log summary results
    logger.info(f"Backtest completed: {len(results['trades'])} trades executed")
    logger.info(f"Final balance: ${results['final_balance']:.2f} (ROI: {results['roi']:.2f}%)")
    
    return results


def main():
    """Main entry point with command line parsing"""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Run Market Bias Debug Tests")
    parser.add_argument('--config', type=str, required=True,
                        help='Path to the override config file for testing')
    parser.add_argument('--run-id', type=str, default=None,
                        help='Identifier for this test run')
    args = parser.parse_args()
    
    # Print header
    print("=" * 80)
    print("MARKET BIAS DEBUG TEST RUNNER")
    print("=" * 80)
    
    # Run the debug backtest
    results = run_debug_backtest(args.config, args.run_id)
    
    # Print footer
    print("=" * 80)
    print(f"Test completed: {results['roi']:.2f}% ROI with {len(results['trades'])} trades")
    print("=" * 80)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
