import pandas as pd
import numpy as np
import json
from datetime import datetime, timezone, timedelta
import glob
import os
import gzip

def parse_timestamp_flexible(ts_str):
    """Parses a timestamp string, attempting multiple formats including with/without fractional seconds and Z."""
    formats_to_try = [
        "%Y-%m-%dT%H:%M:%S.%fZ",  # With microseconds and Z
        "%Y-%m-%dT%H:%M:%S.%f",   # With microseconds, no Z
        "%Y-%m-%dT%H:%M:%SZ",     # Without microseconds, with Z
        "%Y-%m-%dT%H:%M:%S"       # Without microseconds, no Z
    ]
    for fmt in formats_to_try:
        try:
            dt = datetime.strptime(ts_str, fmt)
            # If timezone is naive, assume UTC
            if dt.tzinfo is None or dt.tzinfo.utcoffset(dt) is None:
                dt = dt.replace(tzinfo=timezone.utc)
            return dt
        except ValueError:
            continue
    # As a last resort, try to parse without fractional seconds if present
    if '.' in ts_str:
        try:
            dt = datetime.strptime(ts_str.split('.')[0] + 'Z', "%Y-%m-%dT%H:%M:%SZ")
            dt = dt.replace(tzinfo=timezone.utc) # Ensure timezone awareness
            # Add microseconds back if possible
            if len(ts_str.split('.')) > 1 and ts_str.split('.')[1].replace('Z','').isdigit():
                 dt = dt.replace(microsecond=int(ts_str.split('.')[1].replace('Z','').ljust(6, '0')[:6]))
            return dt
        except ValueError:
            pass # or raise an error if all parsing attempts fail
            
    raise ValueError(f"Timestamp {ts_str} could not be parsed with known formats.")


def load_slice_from_txt(file_path, start_ts_iso, end_ts_iso):
    """
    Loads L2 snapshots from a line-delimited JSON text file within a given ISO timestamp range.
    Parses the complex nested JSON for bid/ask levels.
    """
    print(f"Loading data from: {file_path}")
    print(f"Start timestamp: {start_ts_iso}, End timestamp: {end_ts_iso}")

    start_ts = parse_timestamp_flexible(start_ts_iso)
    end_ts = parse_timestamp_flexible(end_ts_iso)
    
    print(f"Parsed start_ts: {start_ts}, Parsed end_ts: {end_ts}")

    all_records = []
    malformed_lines = 0

    with open(file_path, 'r') as f:
        for i, line in enumerate(f):
            try:
                raw_json = json.loads(line)
            except json.JSONDecodeError:
                malformed_lines += 1
                # print(f"Skipping malformed JSON on line {i+1}")
                continue

            record_ts_str = raw_json.get("time")
            if not record_ts_str:
                # print(f"Skipping line {i+1} due to missing 'time' field in raw_json")
                continue
            
            try:
                record_ts = parse_timestamp_flexible(record_ts_str)
            except ValueError as e:
                # print(f"Skipping line {i+1} due to timestamp parse error: {e}")
                continue

            if not (start_ts <= record_ts <= end_ts):
                continue

            data_levels = raw_json.get("raw", {}).get("data", {}).get("levels")
            if not data_levels or len(data_levels) < 2:
                # print(f"Skipping line {i+1} due to missing or incomplete 'levels' data")
                continue

            record = {"timestamp": record_ts}
            # data_levels[0] are bids, data_levels[1] are asks
            
            # Bids (assuming data_levels[0] are bids (typically higher prices first in raw data, but sorted to be px_1 = best bid))
            # We need to be careful here, Hyperliquid API px are strings.
            # Best bid is the highest price, best ask is the lowest price.
            # The raw data might not be sorted as best bid/ask = level 0.
            # For Hyperliquid: bids are [ {px, sz, n}, ... ] and asks are [ {px, sz, n}, ... ]
            # bids are sorted descending by price, asks are sorted ascending by price in the API response.
            # So, bids[0] is best bid, asks[0] is best ask.

            bids_data = data_levels[0] # Best bids first (highest prices)
            asks_data = data_levels[1] # Best asks first (lowest prices)

            for j in range(20): # Max 20 levels
                if j < len(bids_data):
                    record[f"bid_px_{j+1}"] = float(bids_data[j]["px"])
                    record[f"bid_sz_{j+1}"] = float(bids_data[j]["sz"])
                else:
                    record[f"bid_px_{j+1}"] = np.nan
                    record[f"bid_sz_{j+1}"] = np.nan
                
                if j < len(asks_data):
                    record[f"ask_px_{j+1}"] = float(asks_data[j]["px"])
                    record[f"ask_sz_{j+1}"] = float(asks_data[j]["sz"])
                else:
                    record[f"ask_px_{j+1}"] = np.nan
                    record[f"ask_sz_{j+1}"] = np.nan
            all_records.append(record)
    
    if malformed_lines > 0:
        print(f"Warning: Skipped {malformed_lines} malformed JSON lines during file reading.")
    if not all_records:
        print("Warning: No records were loaded. Check timestamps and file content.")
        # Return an empty DataFrame with expected columns if no records are found
        cols = ["timestamp"]
        for k in range(1, 21):
            cols.extend([f"bid_px_{k}", f"bid_sz_{k}", f"ask_px_{k}", f"ask_sz_{k}"])
        return pd.DataFrame(columns=cols).set_index("timestamp")

    df = pd.DataFrame(all_records)
    df["timestamp"] = pd.to_datetime(df["timestamp"], utc=True)
    df = df.set_index("timestamp").sort_index()
    return df

def calc_raw_obi(df, lvls):
    bid_sz_cols = [f"bid_sz_{i}" for i in range(1, lvls + 1)]
    ask_sz_cols = [f"ask_sz_{i}" for i in range(1, lvls + 1)]
    
    # Ensure all specified columns exist, fill with 0 if not (e.g. if lvls > actual levels in data)
    for col in bid_sz_cols + ask_sz_cols:
        if col not in df.columns:
            df[col] = 0 # Or np.nan, then handle NaNs in sum, but 0 makes sense for non-existent level size

    bid_sz_sum = df[bid_sz_cols].sum(axis=1)
    ask_sz_sum = df[ask_sz_cols].sum(axis=1)
    
    obi = (bid_sz_sum - ask_sz_sum) / (bid_sz_sum + ask_sz_sum + 1e-9) # Epsilon for stability
    return obi

def find_chaotic_wick_slice(chaotic_data_dir, window_minutes=30, price_drop_threshold=500):
    """
    Finds a 30-minute slice with a significant price drop (wick) across multiple text files.
    Looks for a drop of at least `price_drop_threshold` (e.g., 2000 for a 2k drop).
    Returns a dictionary {'start_ts': iso_string, 'end_ts': iso_string, 'file_path': path}
    or None if no suitable wick is found.
    """
    print(f"Searching for chaotic wick in directory: {chaotic_data_dir}")
    best_wick_info = None
    max_price_drop = 0

    # Using glob to find all .txt files, including subdirectories if they exist
    # However, the user path implies files are directly in subdirs like '2k drop' or 'defi liquidations'
    # So we might need to iterate through those known subdirs.
    # For now, let's make it flexible to find files in the chaotic_data_dir and its immediate subdirectories.
    file_paths = []
    for root, _, files in os.walk(chaotic_data_dir):
        for file in files:
            if file.endswith(".txt"):
                file_paths.append(os.path.join(root, file))
    
    print(f"Found text files to scan: {file_paths}")

    if not file_paths:
        print(f"No .txt files found in {chaotic_data_dir} or its subdirectories.")
        return None

    for file_path in file_paths:
        print(f"Scanning file: {file_path}")
        try:
            records = []
            malformed_lines = 0
            with open(file_path, 'r') as f:
                for i, line in enumerate(f):
                    try:
                        raw_json = json.loads(line)
                        record_ts_str = raw_json.get("time")
                        data_levels = raw_json.get("raw", {}).get("data", {}).get("levels")
                        if not record_ts_str or not data_levels or len(data_levels) < 2:
                            continue
                        
                        record_ts = parse_timestamp_flexible(record_ts_str)
                        # For wick finding, we are interested in best ask (lowest ask price) and best bid (highest bid price)
                        # Hyperliquid API: bids[0] is best bid (highest px), asks[0] is best ask (lowest px)
                        best_ask_px = float(data_levels[1][0]["px"]) # asks_data[0]['px']
                        best_bid_px = float(data_levels[0][0]["px"]) # bids_data[0]['px']
                        records.append({
                            "timestamp": record_ts,
                            "ask_px_1": best_ask_px,
                            "bid_px_1": best_bid_px,
                            "mid_price": (best_ask_px + best_bid_px) / 2.0
                        })
                    except (json.JSONDecodeError, ValueError, IndexError, TypeError) as e:
                        malformed_lines +=1
                        # print(f"Skipping line {i+1} in {file_path} due to error: {e}")
                        continue
            if malformed_lines > 0:
                print(f"Skipped {malformed_lines} malformed lines in {file_path}")

            if not records:
                print(f"No valid records found in {file_path}")
                continue
            
            df = pd.DataFrame(records).set_index("timestamp").sort_index()
            if df.empty:
                print(f"DataFrame is empty after processing {file_path}")
                continue

            # Resample to 1 second to make rolling window more manageable and less sensitive to micro-ticks
            # Use mid_price for identifying the general price movement and potential wick start/end
            df_1s = df[['mid_price', 'ask_px_1', 'bid_px_1']].resample('1S').agg(
                {'mid_price': 'mean', 'ask_px_1': 'min', 'bid_px_1':'max'}
            ).dropna()

            if df_1s.empty or len(df_1s) < window_minutes * 60:
                print(f"Not enough 1s data in {file_path} for a {window_minutes}-minute window after resampling.")
                continue

            # Calculate min and max prices in a rolling window
            # For a wick down (2k drop), we're looking for a high price followed by a low price.
            # The 'drop' is max_price_before - min_price_during_wick.
            # Let's use a rolling window of `window_minutes`.
            window_size_seconds = window_minutes * 60
            
            # Find highest point (using max of bid_px_1) and lowest point (using min of ask_px_1) in any window_minutes interval
            # This is a simplification. A true wick often has a rapid drop and recovery.
            # The task asks for a "chaotic wick" and a "30-minute wick window (start_ts_wick, end_ts_wick)"
            # This implies we need to find *the* 30-minute window that best captures this chaos.

            # Iterate through all possible 30-minute windows
            # A more direct way to find a large drop: 
            # For each point, look back `window_minutes` and find the max price.
            # The drop is current price - max price in lookback (if current is lower).
            # Or, more simply, for every possible 30min window, what's max(price) - min(price)

            rolling_max_bid = df_1s['bid_px_1'].rolling(window=f'{window_minutes}T', closed='both').max()
            rolling_min_ask = df_1s['ask_px_1'].rolling(window=f'{window_minutes}T', closed='both').min()
            price_diff = rolling_max_bid - rolling_min_ask

            if price_diff.empty:
                continue

            current_max_drop_in_file = price_diff.max()

            if current_max_drop_in_file > max_price_drop and current_max_drop_in_file >= price_drop_threshold:
                max_price_drop = current_max_drop_in_file
                # Find the end time of the window that had this max drop
                end_time_of_max_drop_window = price_diff.idxmax()
                start_time_of_max_drop_window = end_time_of_max_drop_window - timedelta(minutes=window_minutes-1) # -1 because idxmax gives end of window
                
                # To ensure the window is exactly 30 minutes for `process_slice` later,
                # we define the end as start + 30 mins.
                # The idxmax() is the timestamp *at which* the max difference was observed for a window *ending* at that time.
                # So the window starts at end_time_of_max_drop_window - 30min + 1sec and ends at end_time_of_max_drop_window
                
                end_ts_dt = end_time_of_max_drop_window
                start_ts_dt = end_ts_dt - timedelta(minutes=window_minutes) + timedelta(seconds=1) # to make it a 30 min interval for process_slice

                best_wick_info = {
                    'start_ts': start_ts_dt.isoformat().replace('+00:00', 'Z'),
                    'end_ts': end_ts_dt.isoformat().replace('+00:00', 'Z'),
                    'file_path': file_path,
                    'price_drop': max_price_drop
                }
                print(f"New best wick found in {file_path}: Drop={max_price_drop:.2f}, " \
                      f"Start={best_wick_info['start_ts']}, End={best_wick_info['end_ts']}")

        except Exception as e:
            print(f"Error processing file {file_path} for wick detection: {e}")
            import traceback
            traceback.print_exc()
            continue
            
    if best_wick_info:
        print(f"Finished wick search. Best wick found: Drop={best_wick_info['price_drop']:.2f} in {best_wick_info['file_path']}")
    else:
        print(f"Finished wick search. No wick meeting threshold {price_drop_threshold} found in a {window_minutes}-minute window.")
    return best_wick_info

def process_slice(file_path, start_ts_iso, end_ts_iso, slice_name, output_dir="."):
    print(f"Processing slice: {slice_name}")
    
    # 1. Load L2 snapshots
    df_raw = load_slice_from_txt(file_path, start_ts_iso, end_ts_iso)

    if df_raw.empty:
        print(f"No data loaded for slice {slice_name}. Skipping further processing.")
        summary_stats = {
            "slice_name": slice_name,
            "realised_vol_1s": np.nan,
            "mean_spread_rel": np.nan,
            "obi_L1_3_corr_1s_fwd_ret": np.nan,
            "obi_L1_3_corr_5s_fwd_ret": np.nan,
            "obi_L1_10_corr_1s_fwd_ret": np.nan,
            "obi_L1_10_corr_5s_fwd_ret": np.nan,
            "obi_L1_20_corr_1s_fwd_ret": np.nan,
            "obi_L1_20_corr_5s_fwd_ret": np.nan,
            "raw_obi_L1_3_p95": np.nan,
            "raw_obi_L1_3_p99": np.nan,
            "raw_obi_L1_10_p95": np.nan,
            "raw_obi_L1_10_p99": np.nan,
            "raw_obi_L1_20_p95": np.nan,
            "raw_obi_L1_20_p99": np.nan,
            "resampled_row_count": 0,
            "raw_row_count": 0,
            "error": "No data loaded, check timestamps or file content."
        }
        summary_filename = os.path.join(output_dir, f"{slice_name}_summary.json")
        with open(summary_filename, 'w') as f:
            json.dump(summary_stats, f, indent=4)
        print(f"Empty summary saved to {summary_filename}")
        
        # Create empty CSV
        csv_cols = ['mid_price', 'raw_obi_L1_3', 'raw_obi_L1_10', 'raw_obi_L1_20', 'spread_relative', 'micro_return_1s']
        empty_sample_df = pd.DataFrame(columns=csv_cols)
        sample_filename = os.path.join(output_dir, f"{slice_name}_sample.csv.gz")
        empty_sample_df.to_csv(sample_filename, index_label='timestamp', compression='gzip')
        print(f"Empty sample CSV saved to {sample_filename}")
        return

    raw_row_count = len(df_raw)
    print(f"Loaded {raw_row_count} raw snapshots.")

    # 2. Compute features per-snapshot
    df_raw['mid_price'] = (df_raw['bid_px_1'] + df_raw['ask_px_1']) / 2.0
    df_raw['raw_obi_L1_3'] = calc_raw_obi(df_raw, 3)
    df_raw['raw_obi_L1_10'] = calc_raw_obi(df_raw, 10)
    df_raw['raw_obi_L1_20'] = calc_raw_obi(df_raw, 20)
    
    # Avoid division by zero if mid_price can be 0 or NaN
    df_raw['spread_relative'] = np.where(
        (df_raw['mid_price'].notna()) & (df_raw['mid_price'] != 0),
        (df_raw['ask_px_1'] - df_raw['bid_px_1']) / df_raw['mid_price'],
        np.nan
    )

    # Micro return calculation needs to be done on 1s resampled mid_price later

    # 3. Aggregate to 1-second resample (mean)
    # Select columns for resampling
    cols_to_resample = ['mid_price', 'raw_obi_L1_3', 'raw_obi_L1_10', 'raw_obi_L1_20', 'spread_relative']
    df_1s = df_raw[cols_to_resample].resample('1S').mean()
    
    # Calculate micro_return_1s on the 1s resampled mid_price
    # The task states mid(t) – mid(t-1s), which is a diff not pct_change
    df_1s['micro_return_1s'] = df_1s['mid_price'].diff() # mid(t) - mid(t-1s)
    
    df_1s = df_1s.dropna(subset=['mid_price']) # Drop rows where mid_price became NaN after resampling if all raw values in that second were NaN.
                                         # micro_return_1s will have a NaN for the first row, this is expected.

    print(f"Resampled to {len(df_1s)} 1-second rows.")

    # 4. Produce summary JSON
    summary_stats = {"slice_name": slice_name}
    summary_stats['realised_vol_1s'] = df_1s['micro_return_1s'].std()
    summary_stats['mean_spread_rel'] = df_1s['spread_relative'].mean()

    for obi_col in ['raw_obi_L1_3', 'raw_obi_L1_10', 'raw_obi_L1_20']:
        # Pearson correlation with future returns
        # Ensure micro_return_1s is not all NaN (can happen if only 1 resampled mid_price point)
        if df_1s['micro_return_1s'].notna().sum() > 1:
            summary_stats[f'{obi_col}_corr_1s_fwd_ret'] = df_1s[obi_col].corr(df_1s['micro_return_1s'].shift(-1))
            summary_stats[f'{obi_col}_corr_5s_fwd_ret'] = df_1s[obi_col].corr(df_1s['micro_return_1s'].shift(-5))
        else:
            summary_stats[f'{obi_col}_corr_1s_fwd_ret'] = np.nan
            summary_stats[f'{obi_col}_corr_5s_fwd_ret'] = np.nan
        
        # Percentiles
        summary_stats[f'{obi_col}_p95'] = df_1s[obi_col].quantile(0.95)
        summary_stats[f'{obi_col}_p99'] = df_1s[obi_col].quantile(0.99)
        
    summary_stats['resampled_row_count'] = len(df_1s)
    summary_stats['raw_row_count'] = raw_row_count


    summary_filename = os.path.join(output_dir, f"{slice_name}_summary.json")
    os.makedirs(output_dir, exist_ok=True) # Ensure output directory exists
    with open(summary_filename, 'w') as f:
        # Convert numpy types to native Python types for JSON serialization
        for key, value in summary_stats.items():
            if isinstance(value, (np.generic, np.bool_)): # np.bool_ for boolean types
                summary_stats[key] = value.item()
            elif isinstance(value, pd.Timestamp):
                 summary_stats[key] = value.isoformat()

        json.dump(summary_stats, f, indent=4)
    print(f"Summary saved to {summary_filename}")

    # 5. Export sample CSV (first 5000 resampled rows)
    sample_df = df_1s.head(5000)
    cols_for_csv = ['mid_price', 'raw_obi_L1_3', 'raw_obi_L1_10', 'raw_obi_L1_20', 'spread_relative', 'micro_return_1s']
    # Ensure all columns exist, add NaN columns if they are missing (e.g. if df_1s is very small)
    for col_csv in cols_for_csv:
        if col_csv not in sample_df.columns:
            sample_df[col_csv] = np.nan
            
    sample_filename = os.path.join(output_dir, f"{slice_name}_sample.csv.gz")
    sample_df[cols_for_csv].to_csv(sample_filename, index_label='timestamp', compression='gzip')
    print(f"Sample CSV saved to {sample_filename}")

if __name__ == "__main__":
    # Configuration for the calm slice
    # Using the full path for the file as it's outside the script's relative path context if run from elsewhere.
    # IMPORTANT: Adjust this base_path if your 'trading_bot_' project is not in '/Users/<USER>/Desktop/'
    # Or ensure the script is run from a location where this relative path is correct.
    
    # The user provided: /Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw JSON for testing/calm hour
    # We'll use the BTC_13 file for the 13:00 hour.
    
    # --- USER CONFIGURABLE SECTION ---
    # Base path to the trading_bot_ directory
    # This should be the absolute path to your "trading_bot_" project directory.
    # Example: base_project_path = "/Users/<USER>/Desktop/trading_bot_"
    # If you run this script from within "trading_bot_/aerith_hyperliquid_bot/scripts/",
    # you might use relative paths like "../../../hyperliquid_data/..."
    # For robustness with varying execution locations, an absolute path is safer if known.
    
    # Determine the project root based on the script's location
    # Assuming the script is in trading_bot_/aerith_hyperliquid_bot/scripts/
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.abspath(os.path.join(script_dir, "..", "..")) # up three levels
    
    calm_file_path = os.path.join(project_root, "hyperliquid_data", "raw JSON for testing", "calm hour", "BTC_13_l2Book.txt")
    
    # Timestamps identified from file BTC_13_l2Book.txt
    calm_start_ts_iso = "2024-02-15T13:00:01.699086589Z" 
    # Calculate end_ts as 1 hour from start_ts
    # Use the flexible parser for the start_ts to ensure it's a datetime object
    try:
        _start_dt = parse_timestamp_flexible(calm_start_ts_iso)
        _end_dt = _start_dt + timedelta(hours=1)
        calm_end_ts_iso = _end_dt.isoformat().replace('+00:00', 'Z') # Ensure Z format
    except ValueError as e:
        print(f"Error parsing calm_start_ts_iso for timedelta calculation: {e}")
        calm_end_ts_iso = "2024-02-15T14:00:01.699086589Z" # Fallback
        
    calm_slice_name = "calm_hour_slice"
    
    # Output directory for results (e.g., in the project root or a specific 'analysis_outputs' folder)
    # Let's put it in an 'analysis_outputs' directory at the project root
    output_directory = os.path.join(project_root, "analysis_outputs", "obi_eda")
    os.makedirs(output_directory, exist_ok=True) # Create if it doesn't exist
    # --- END USER CONFIGURABLE SECTION ---

    print(f"Script directory: {script_dir}")
    print(f"Determined project root: {project_root}")
    print(f"Using calm file: {calm_file_path}")
    print(f"Output directory: {output_directory}")

    # Process Calm Slice
    print("\n--- Processing Calm Slice ---")
    if not os.path.exists(calm_file_path):
        print(f"ERROR: Calm data file not found at {calm_file_path}")
        print("Please ensure the path is correct and the file exists.")
    else:
        print(f"Using calm file: {calm_file_path}")
        process_slice(
            file_path=calm_file_path,
            start_ts_iso=calm_start_ts_iso,
            end_ts_iso=calm_end_ts_iso,
            slice_name=calm_slice_name,
            output_dir=output_directory
        )
        print(f"Finished processing {calm_slice_name}. Output in {output_directory}")

    # Process Chaotic Wick Slice
    print("\n--- Processing Chaotic Wick Slice ---")
    # User provided path: /Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw JSON for testing/chaotic wicks
    chaotic_data_directory = os.path.join(project_root, "hyperliquid_data", "raw JSON for testing", "chaotic wicks")
    
    # Find the 30-minute chaotic wick slice
    # Looking for a 2k+ drop as per user's latest request implies threshold=2000
    # User has clarified: "no need to be exactly 2k just a sharp drop"
    # Adjusting threshold to 500 to capture sharper, potentially smaller drops.
    wick_info = find_chaotic_wick_slice(chaotic_data_directory, window_minutes=30, price_drop_threshold=500)

    if wick_info:
        print(f"Found chaotic wick. File: {wick_info['file_path']}, Start: {wick_info['start_ts']}, End: {wick_info['end_ts']}")
        chaotic_slice_name = "chaotic_wick_slice"
        process_slice(
            file_path=wick_info['file_path'],
            start_ts_iso=wick_info['start_ts'],
            end_ts_iso=wick_info['end_ts'],
            slice_name=chaotic_slice_name,
            output_dir=output_directory
        )
        print(f"Finished processing {chaotic_slice_name}. Output in {output_directory}")
    else:
        print("Could not find a suitable chaotic wick slice with a 2k+ drop in the provided chaotic files.")

    print("\nScript finished.") 