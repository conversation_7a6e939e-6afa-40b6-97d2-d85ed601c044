#!/usr/bin/env python3
"""
Minimal test case for modern system - runs 1 day backtest.
This helps verify signal generation quickly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from datetime import datetime
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
import json

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    print("=== Modern System Minimal Test ===")
    print("Running 1-day backtest to verify trade generation...\n")
    
    # Load config
    config_path = 'configs/overrides/modern_system_v2_complete.yaml'
    config = load_config(config_path)
    
    # Override to test just 1 day
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 2)
    
    print(f"Test period: {start_date} to {end_date}")
    print(f"Config: {config_path}")
    print(f"Risk per trade: {config.portfolio.risk_per_trade:.2%}")
    print(f"Regime detector: {config.regime.detector_type}")
    print(f"Strategy: tf_v3_modern\n")
    
    # Create and run backtest
    try:
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        print("Starting backtest...")
        results = engine.run_backtest()
        
        # Display results
        print("\n=== RESULTS ===")
        print(f"Total trades: {len(results['trades'])}")
        print(f"Runtime: {results['runtime_seconds']:.1f}s")
        
        if results['trades']:
            print("\nFirst 5 trades:")
            for i, trade in enumerate(results['trades'][:5]):
                print(f"  {i+1}. {trade['timestamp']} - {trade['direction']} @ ${trade['entry_price']:.2f}")
        else:
            print("\n❌ NO TRADES GENERATED!")
            print("\nDebugging information:")
            
            # Check regime history
            if results['regime_history']:
                regime_states = [r['state'] for r in results['regime_history'][:100]]
                unique_states = set(regime_states)
                print(f"- Regime states detected: {unique_states}")
                print(f"- Total regime updates: {len(results['regime_history'])}")
            else:
                print("- No regime states recorded!")
            
            print("\nPotential issues to check:")
            print("1. Regime thresholds may be too restrictive")
            print("2. Strategy entry conditions not being met")
            print("3. Risk management blocking trades")
            print("4. Check logs above for detailed trace")
        
        # Save results
        output_file = f"modern_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\nResults saved to: {output_file}")
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())