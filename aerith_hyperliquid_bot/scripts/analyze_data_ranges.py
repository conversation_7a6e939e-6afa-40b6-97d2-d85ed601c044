#!/usr/bin/env python3
"""
Analyze actual data ranges to calibrate thresholds properly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime


def analyze_feature_ranges():
    """Analyze feature ranges across multiple days."""
    feature_dir = Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s")
    
    # Collect data from multiple days
    all_data = []
    dates_to_check = ["2024-01-01", "2024-02-01", "2024-03-01", "2024-04-01", 
                      "2024-05-01", "2024-06-01", "2024-07-01", "2024-08-01"]
    
    for date_str in dates_to_check:
        date_dir = feature_dir / date_str
        if date_dir.exists():
            # Load a few hours from each day
            for hour in [0, 6, 12, 18]:
                file_path = date_dir / f"features_{hour:02d}.parquet"
                if file_path.exists():
                    df = pd.read_parquet(file_path)
                    all_data.append(df)
                    print(f"Loaded {file_path.name} - {len(df)} rows")
    
    if not all_data:
        print("No data found!")
        return
    
    # Combine all data
    combined_df = pd.concat(all_data, ignore_index=True)
    print(f"\nTotal rows analyzed: {len(combined_df):,}")
    
    # Analyze key fields
    print("\n" + "="*80)
    print("FEATURE RANGE ANALYSIS")
    print("="*80)
    
    # ATR Percent
    if 'atr_percent_sec' in combined_df.columns:
        atr_data = combined_df['atr_percent_sec'].dropna()
        if len(atr_data) > 0:
            print(f"\nATR Percent (Volatility):")
            print(f"  Count: {len(atr_data):,}")
            print(f"  Range: [{atr_data.min():.8f}, {atr_data.max():.8f}]")
            print(f"  Mean: {atr_data.mean():.8f}")
            print(f"  Std: {atr_data.std():.8f}")
            print(f"  Percentiles:")
            for p in [1, 5, 10, 25, 50, 75, 90, 95, 99]:
                print(f"    {p}%: {atr_data.quantile(p/100):.8f}")
    
    # MA Slope
    if 'ma_slope_ema_30s' in combined_df.columns:
        ma_data = combined_df['ma_slope_ema_30s'].dropna()
        if len(ma_data) > 0:
            abs_ma = ma_data.abs()
            print(f"\nMA Slope (Momentum):")
            print(f"  Count: {len(ma_data):,}")
            print(f"  Range: [{ma_data.min():.8f}, {ma_data.max():.8f}]")
            print(f"  Mean: {ma_data.mean():.8f}")
            print(f"  Std: {ma_data.std():.8f}")
            print(f"  Absolute value percentiles:")
            for p in [50, 75, 90, 95, 99]:
                print(f"    {p}%: {abs_ma.quantile(p/100):.8f}")
    
    # OBI/Volume Imbalance
    if 'obi_smoothed' in combined_df.columns:
        obi_data = combined_df['obi_smoothed'].dropna()
        if len(obi_data) > 0:
            abs_obi = obi_data.abs()
            print(f"\nOBI Smoothed (Volume Imbalance):")
            print(f"  Count: {len(obi_data):,}")
            print(f"  Range: [{obi_data.min():.4f}, {obi_data.max():.4f}]")
            print(f"  Mean: {obi_data.mean():.4f}")
            print(f"  Std: {obi_data.std():.4f}")
            print(f"  Absolute value percentiles:")
            for p in [50, 75, 90, 95, 99]:
                print(f"    {p}%: {abs_obi.quantile(p/100):.4f}")
    
    # Spread statistics
    if 'spread_mean' in combined_df.columns and 'spread_std' in combined_df.columns:
        spread_mean = combined_df['spread_mean'].dropna()
        spread_std = combined_df['spread_std'].dropna()
        
        if len(spread_mean) > 0:
            print(f"\nSpread Mean:")
            print(f"  Count: {len(spread_mean):,}")
            print(f"  Range: [{spread_mean.min():.8f}, {spread_mean.max():.8f}]")
            print(f"  Percentiles:")
            for p in [1, 5, 10, 25, 50]:
                print(f"    {p}%: {spread_mean.quantile(p/100):.8f}")
        
        if len(spread_std) > 0:
            print(f"\nSpread Std:")
            print(f"  Count: {len(spread_std):,}")
            print(f"  Range: [{spread_std.min():.8f}, {spread_std.max():.8f}]")
            print(f"  Percentiles:")
            for p in [50, 75, 90, 95, 99]:
                print(f"    {p}%: {spread_std.quantile(p/100):.8f}")
    
    print("\n" + "="*80)
    print("THRESHOLD RECOMMENDATIONS")
    print("="*80)
    
    print("\nBased on the data distribution, recommended thresholds:")
    
    if 'atr_data' in locals():
        print(f"\nVolatility (ATR%):")
        print(f"  Low volatility: < {atr_data.quantile(0.25):.6f} (25th percentile)")
        print(f"  High volatility: > {atr_data.quantile(0.75):.6f} (75th percentile)")
    
    if 'abs_ma' in locals():
        print(f"\nMomentum (MA Slope):")
        print(f"  Weak trend: > {abs_ma.quantile(0.50):.6f} (50th percentile)")
        print(f"  Strong trend: > {abs_ma.quantile(0.90):.6f} (90th percentile)")
    
    if 'abs_obi' in locals():
        print(f"\nOBI Confirmation:")
        print(f"  Weak confirmation: > {abs_obi.quantile(0.75):.3f} (75th percentile)")
        print(f"  Strong confirmation: > {abs_obi.quantile(0.90):.3f} (90th percentile)")


if __name__ == "__main__":
    analyze_feature_ranges()