#!/usr/bin/env python3
"""
Generate 1-minute OHLCV candles from raw2 L2 data.

This script resamples the 1-second L2 data to create 1-minute OHLCV candles
for use in execution refinement.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import logging
from typing import Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def resample_to_1m(df: pd.DataFrame) -> pd.DataFrame:
    """
    Resample L2 data to 1-minute OHLCV candles.
    
    Args:
        df: DataFrame with L2 data including 'timestamp' and 'mid_price'
        
    Returns:
        DataFrame with 1-minute OHLCV candles
    """
    # Ensure timestamp is datetime and set as index
    if 'timestamp' in df.columns:
        df = df.copy()
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.set_index('timestamp')
    
    # Create OHLCV from mid_price
    ohlcv = df['mid_price'].resample('1min').agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last'
    })
    
    # Add volume proxy (sum of volume_proxy if available, otherwise count)
    if 'volume_proxy' in df.columns:
        ohlcv['volume'] = df['volume_proxy'].resample('1min').sum()
    else:
        ohlcv['volume'] = df['mid_price'].resample('1min').count()
    
    # Add additional useful fields if available
    if 'spread' in df.columns:
        ohlcv['avg_spread'] = df['spread'].resample('1min').mean()
        
    if 'imbalance' in df.columns:
        ohlcv['avg_imbalance'] = df['imbalance'].resample('1min').mean()
    
    # Reset index to have timestamp as column
    ohlcv = ohlcv.reset_index()
    
    # Remove any rows with NaN values in OHLC
    ohlcv = ohlcv.dropna(subset=['open', 'high', 'low', 'close'])
    
    return ohlcv


def process_date(date_str: str, input_dir: Path, output_dir: Path) -> bool:
    """
    Process a single date's raw2 file to create 1-minute candles.
    
    Args:
        date_str: Date string in YYYYMMDD format
        input_dir: Directory containing raw2 files
        output_dir: Directory to save 1m candles
        
    Returns:
        True if successful, False otherwise
    """
    # Input file path
    input_file = input_dir / f"{date_str}_raw2.parquet"
    
    # Convert date format for output
    date_obj = datetime.strptime(date_str, "%Y%m%d")
    output_date_str = date_obj.strftime("%Y-%m-%d")
    output_file = output_dir / f"{output_date_str}_1m.parquet"
    
    # Skip if output already exists
    if output_file.exists():
        logger.debug(f"Skipping {date_str} - output already exists")
        return True
    
    # Check if input exists
    if not input_file.exists():
        logger.warning(f"Input file not found: {input_file}")
        return False
    
    try:
        # Read raw2 data
        logger.info(f"Processing {date_str}...")
        df = pd.read_parquet(input_file)
        
        # Resample to 1-minute
        df_1m = resample_to_1m(df)
        
        # Save to parquet
        df_1m.to_parquet(output_file, index=False)
        logger.info(f"  Saved {len(df_1m)} 1-minute candles to {output_file.name}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error processing {date_str}: {e}")
        return False


def main():
    """Main function to process all available raw2 files."""
    # Set up paths
    base_dir = Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data")
    input_dir = base_dir / "raw2"
    output_dir = base_dir / "resampled_l2" / "1m"
    
    # Create output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Get all raw2 files
    raw2_files = sorted(input_dir.glob("*_raw2.parquet"))
    logger.info(f"Found {len(raw2_files)} raw2 files to process")
    
    # Process each file
    successful = 0
    failed = 0
    
    for raw2_file in raw2_files:
        # Extract date from filename
        date_str = raw2_file.stem.split('_')[0]
        
        # Only process 2024 data for now
        if date_str.startswith('2024'):
            if process_date(date_str, input_dir, output_dir):
                successful += 1
            else:
                failed += 1
    
    # Summary
    logger.info(f"\nProcessing complete:")
    logger.info(f"  Successful: {successful}")
    logger.info(f"  Failed: {failed}")
    logger.info(f"  Total: {successful + failed}")
    
    # Check output
    output_files = sorted(output_dir.glob("*.parquet"))
    logger.info(f"\nTotal 1-minute files created: {len(output_files)}")


if __name__ == "__main__":
    main()