import subprocess
import os
from datetime import datetime, timedelta
import lz4.frame

# --- Config ---
BUCKET = "hyperliquid-archive"
PREFIX = "market_data"
OUTPUT_BASE_DIR = "/Users/<USER>/Desktop/trading_bot_/btc_data_2024"

# Ranges
date_ranges = [
    ("2023-10-17", "2023-12-31"),
    ("2025-03-11", "2025-04-14")
]

def download_and_decompress(date_str, hour):
    hour_str = str(hour)
    s3_path = f"{PREFIX}/{date_str}/{hour_str}/l2Book/BTC.lz4"
    output_date_dir = os.path.join(OUTPUT_BASE_DIR, date_str)
    os.makedirs(output_date_dir, exist_ok=True)
    output_file_path = os.path.join(output_date_dir, f"BTC_{hour_str}_l2Book.txt")

    # Temporary file
    tmp_lz4_file = "tmp_btc.lz4"

    try:
        # Download the .lz4 file
        subprocess.run([
            "aws", "s3", "cp",
            f"s3://{BUCKET}/{s3_path}",
            tmp_lz4_file,
            "--request-payer", "requester"
        ], check=True)

        # Decompress and save as .txt
        with open(tmp_lz4_file, "rb") as compressed_file:
            decompressed_data = lz4.frame.decompress(compressed_file.read())
            with open(output_file_path, "wb") as out_file:
                out_file.write(decompressed_data)

        print(f"✅ {output_file_path}")
        os.remove(tmp_lz4_file)

    except subprocess.CalledProcessError:
        print(f"❌ Not found: {s3_path}")
    except Exception as e:
        print(f"⚠️ Error decompressing {s3_path}: {e}")
        if os.path.exists(tmp_lz4_file):
            os.remove(tmp_lz4_file)

def main():
    for start_str, end_str in date_ranges:
        start = datetime.strptime(start_str, "%Y-%m-%d")
        end = datetime.strptime(end_str, "%Y-%m-%d")
        current = start

        while current <= end:
            date_str = current.strftime("%Y%m%d")
            for hour in range(24):
                download_and_decompress(date_str, hour)
            current += timedelta(days=1)

if __name__ == "__main__":
    main()
