#!/usr/bin/env python3
"""
Test Modern System - Phase 5
============================

This script tests the modern system with calibrated thresholds.
It runs progressive tests to validate the system is working correctly.

Tests:
1. Quick test (1 week) - Verify basic functionality
2. Monthly test - Check trade generation
3. Threshold comparison - Compare different threshold settings
"""

import argparse
import sys
from pathlib import Path
from datetime import datetime, timedelta
import json
import time

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine


def run_test(config_path: str, start_date: datetime, end_date: datetime, test_name: str):
    """
    Run a single test with given parameters.
    
    Args:
        config_path: Path to config file
        start_date: Test start date
        end_date: Test end date
        test_name: Name of the test
        
    Returns:
        Test results dictionary
    """
    print(f"\n{'='*60}")
    print(f"Running: {test_name}")
    print(f"Period: {start_date.date()} to {end_date.date()}")
    print(f"Config: {config_path}")
    print('='*60)
    
    # Load configuration with base + override merging
    import yaml
    from deepmerge import always_merger
    
    # Load base config
    base_config_path = 'configs/base.yaml'
    with open(base_config_path, 'r') as f:
        base_config = yaml.safe_load(f)
    
    # Load override config if it's an override
    if 'overrides' in config_path:
        with open(config_path, 'r') as f:
            override_config = yaml.safe_load(f)
        # Merge configs
        merged_config = always_merger.merge(base_config, override_config)
    else:
        merged_config = base_config
    
    # Create config object
    from hyperliquid_bot.config.settings import Config
    config = Config(**merged_config)
    
    # Verify critical settings
    print("\nVerifying configuration:")
    print(f"  - System mode: {getattr(config, 'system_mode', 'not set')}")
    print(f"  - Detector: {config.regime.detector_type}")
    print(f"  - Cadence: {getattr(config.regime, 'cadence_sec', 60)} seconds")
    print(f"  - Risk fraction: {config.tf_v3.risk_frac:.2%}")
    
    # Create and run backtest
    start_time = time.time()
    
    try:
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        results = engine.run_backtest()
        
        runtime = time.time() - start_time
        
        # Extract key metrics
        metrics = {
            'test_name': test_name,
            'config': config_path,
            'period_days': (end_date - start_date).days,
            'runtime_seconds': runtime,
            'total_trades': results['performance']['total_trades'],
            'total_return': results['performance']['total_return'],
            'win_rate': results['performance']['win_rate'],
            'regime_updates': len(results.get('regime_history', [])),
            'updates_per_hour': len(results.get('regime_history', [])) / ((end_date - start_date).total_seconds() / 3600) if results.get('regime_history') else 0
        }
        
        # Print summary
        print(f"\n✅ Test completed in {runtime:.1f} seconds")
        print(f"Results:")
        print(f"  - Total trades: {metrics['total_trades']}")
        print(f"  - Total return: {metrics['total_return']:.2%}")
        print(f"  - Win rate: {metrics['win_rate']:.2%}")
        print(f"  - Regime updates: {metrics['regime_updates']}")
        print(f"  - Updates/hour: {metrics['updates_per_hour']:.1f}")
        
        # Analyze regime distribution
        if results.get('regime_history'):
            regime_states = [r['state'] for r in results['regime_history']]
            state_counts = {}
            for state in set(regime_states):
                state_counts[state] = regime_states.count(state)
            
            print("\nRegime distribution:")
            total = len(regime_states)
            for state, count in sorted(state_counts.items()):
                print(f"  - {state}: {count} ({count/total*100:.1f}%)")
            
            metrics['regime_distribution'] = state_counts
        
        # Check for issues
        print("\nDiagnostics:")
        if metrics['total_trades'] == 0:
            print("  ❌ No trades generated - check thresholds and filters")
        elif metrics['total_trades'] < 10:
            print("  ⚠️  Low trade count - may need threshold adjustment")
        elif metrics['total_trades'] > 100:
            print("  ⚠️  High trade count - may be overtrading")
        else:
            print("  ✅ Trade count looks reasonable")
        
        if metrics['updates_per_hour'] < 50:
            print("  ❌ Low regime update rate - check data loading")
        elif metrics['updates_per_hour'] > 70:
            print("  ⚠️  High regime update rate - check timing logic")
        else:
            print("  ✅ Regime update rate looks correct (~60/hour)")
        
        return metrics
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return {
            'test_name': test_name,
            'error': str(e),
            'runtime_seconds': time.time() - start_time
        }


def main():
    """Run progressive modern system tests."""
    parser = argparse.ArgumentParser(
        description="Test modern system with calibrated thresholds"
    )
    
    parser.add_argument(
        '--quick',
        action='store_true',
        help='Run quick 1-week test only'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='Override config file (default: tries both calibrated and standard)'
    )
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("MODERN SYSTEM PHASE 5 TESTING")
    print("=" * 80)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test configurations
    configs_to_test = []
    
    if args.config:
        configs_to_test.append(args.config)
    else:
        # Test both configurations
        configs_to_test = [
            'configs/overrides/modern_calibrated.yaml',
            'configs/overrides/modern_system.yaml'
        ]
    
    all_results = []
    
    # Test 1: Quick functionality test (1 week)
    print("\n" + "="*80)
    print("TEST 1: QUICK FUNCTIONALITY TEST (1 WEEK)")
    print("="*80)
    
    for config in configs_to_test:
        if Path(project_root / config).exists():
            results = run_test(
                config_path=config,
                start_date=datetime(2024, 1, 1),
                end_date=datetime(2024, 1, 8),
                test_name=f"1-week test ({Path(config).stem})"
            )
            all_results.append(results)
    
    if not args.quick:
        # Test 2: Monthly test
        print("\n" + "="*80)
        print("TEST 2: MONTHLY PERFORMANCE TEST")
        print("="*80)
        
        for config in configs_to_test[:1]:  # Only test calibrated config for longer test
            if Path(project_root / config).exists():
                results = run_test(
                    config_path=config,
                    start_date=datetime(2024, 1, 1),
                    end_date=datetime(2024, 2, 1),
                    test_name=f"Monthly test ({Path(config).stem})"
                )
                all_results.append(results)
    
    # Summary comparison
    print("\n" + "="*80)
    print("SUMMARY COMPARISON")
    print("="*80)
    
    print(f"\n{'Test Name':<40} {'Trades':<10} {'Return':<10} {'Win Rate':<10} {'Runtime':<10}")
    print("-" * 80)
    
    for result in all_results:
        if 'error' not in result:
            print(f"{result['test_name']:<40} "
                  f"{result['total_trades']:<10} "
                  f"{result['total_return']:<10.2%} "
                  f"{result['win_rate']:<10.2%} "
                  f"{result['runtime_seconds']:<10.1f}s")
        else:
            print(f"{result['test_name']:<40} ERROR: {result['error']}")
    
    # Save results
    output_file = f"phase5_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(all_results, f, indent=2, default=str)
    
    print(f"\n💾 Detailed results saved to: {output_file}")
    
    # Final recommendations
    print("\n" + "="*80)
    print("RECOMMENDATIONS")
    print("="*80)
    
    best_result = None
    for result in all_results:
        if 'error' not in result and result['total_trades'] > 0:
            if best_result is None or result['total_return'] > best_result['total_return']:
                best_result = result
    
    if best_result:
        print(f"✅ Best configuration: {best_result['config']}")
        print(f"   - Trades: {best_result['total_trades']}")
        print(f"   - Return: {best_result['total_return']:.2%}")
        
        if best_result['total_trades'] < 50:
            print("\n📊 Suggested adjustments for more trades:")
            print("   - Reduce momentum thresholds by 20%")
            print("   - Reduce min_regime_confidence to 0.5")
        elif best_result['total_trades'] > 200:
            print("\n📊 Suggested adjustments for fewer trades:")
            print("   - Increase momentum thresholds by 20%")
            print("   - Increase min_regime_confidence to 0.7")
    else:
        print("❌ No successful tests - check configuration and data")
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == '__main__':
    main()