#!/usr/bin/env python
# scripts/verify_tf_v3_config.py

"""
Verification script for TF-v3 configuration.

This script verifies that:
1. The continuous_gms detector is recognized
2. The portfolio settings are properly loaded
3. The StrategyEvaluator correctly handles the continuous_gms detector

Usage:
    python -m scripts.verify_tf_v3_config
"""

import os
import sys
import logging

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.strategies.evaluator import StrategyEvaluator
from hyperliquid_bot.core.detector_factory import get_regime_detector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def verify_config():
    """Verify the configuration for TF-v3."""
    logger.info("Loading configuration...")
    config_path = os.path.join(os.path.dirname(__file__), '..', 'configs', 'base.yaml')
    config = load_config(config_path)
    
    # Ensure TF-v3 is enabled
    config.strategies.use_tf_v3 = True
    
    # Ensure continuous_gms detector is used
    config.regime.detector_type = 'continuous_gms'
    
    logger.info(f"Using detector type: {config.regime.detector_type}")
    logger.info(f"Portfolio settings: max_notional={config.portfolio.max_notional}, max_leverage={config.portfolio.max_leverage}")
    
    # Verify that the detector is recognized
    logger.info("Verifying detector...")
    detector = get_regime_detector(config)
    logger.info(f"Detector class: {detector.__class__.__name__}")
    
    # Verify that the strategy evaluator recognizes the detector
    logger.info("Verifying strategy evaluator...")
    evaluator = StrategyEvaluator(config)
    
    # Test with different regimes
    test_regimes = ['BULL', 'BEAR', 'CHOP', 'Unknown']
    for regime in test_regimes:
        active_strategies = evaluator.get_active_strategies(regime)
        logger.info(f"Regime '{regime}' -> Active strategies: {active_strategies}")
        
        # Check if TF-v3 is active in the expected regimes
        if regime in ['BULL', 'BEAR']:
            if 'tf_v3' in active_strategies:
                logger.info(f"✅ TF-v3 is correctly active in {regime} regime")
            else:
                logger.error(f"❌ TF-v3 should be active in {regime} regime")
        else:
            if 'tf_v3' not in active_strategies:
                logger.info(f"✅ TF-v3 is correctly inactive in {regime} regime")
            else:
                logger.error(f"❌ TF-v3 should not be active in {regime} regime")
    
    logger.info("Verification complete!")
    return True

if __name__ == '__main__':
    verify_config()
