#!/usr/bin/env python3
"""
Trace Full Pipeline
===================

Trace data flow through the entire modern pipeline.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from datetime import datetime
import logging

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(name)s - %(levelname)s - %(message)s'
)

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
from hyperliquid_bot.modern.hourly_evaluator import HourlyStrategyEvaluator
from hyperliquid_bot.modern.tf_v3_modern import ModernTFV3Strategy

# Instrument key methods
original_prepare_signals = HourlyStrategyEvaluator._prepare_strategy_signals
original_evaluate = HourlyStrategyEvaluator.evaluate
original_evaluate_entry = ModernTFV3Strategy.evaluate_entry

def traced_prepare_signals(self, hourly_bar, current_signals, regime_features, ohlcv_history=None):
    print("\n🔍 TRACE: _prepare_strategy_signals called")
    print(f"   hourly_bar keys: {list(hourly_bar.keys())}")
    print(f"   ATR from hourly_bar: atr_14_sec={hourly_bar.get('atr_14_sec')}")
    print(f"   regime_features keys: {list(regime_features.keys())}")
    
    result = original_prepare_signals(self, hourly_bar, current_signals, regime_features, ohlcv_history)
    
    print(f"\n   Returned signals keys: {list(result.keys())}")
    print(f"   signals['atr_14'] = {result.get('atr_14')}")
    print(f"   signals['atr_percent'] = {result.get('atr_percent')}")
    print(f"   'regime_features' in signals: {'regime_features' in result}")
    
    return result

def traced_evaluate(self, hourly_bar, current_signals, timestamp, ohlcv_history=None):
    print(f"\n📊 TRACE: HourlyEvaluator.evaluate called at {timestamp}")
    print(f"   hourly_bar has atr_14_sec: {'atr_14_sec' in hourly_bar}")
    
    result = original_evaluate(self, hourly_bar, current_signals, timestamp, ohlcv_history)
    
    if result:
        print(f"   ✅ Entry decision generated: {result['direction']}")
    else:
        print(f"   ❌ No entry decision")
    
    return result

def traced_evaluate_entry(self, signals, regime):
    print(f"\n🎯 TRACE: Strategy.evaluate_entry called")
    print(f"   Regime: {regime}")
    print(f"   'regime_features' in signals: {'regime_features' in signals}")
    print(f"   atr_14: {signals.get('atr_14')}")
    print(f"   atr_percent: {signals.get('atr_percent')}")
    
    # Check all required signals
    missing = []
    for sig in self.required_signals:
        if sig not in signals or pd.isna(signals.get(sig)):
            missing.append(sig)
    
    if missing:
        print(f"   ⚠️  Missing/NaN signals: {missing}")
    
    result = original_evaluate_entry(self, signals, regime)
    
    if result:
        print(f"   ✅ Strategy decision: {result['direction']}")
    else:
        print(f"   ❌ No strategy decision")
    
    return result

# Apply patches
HourlyStrategyEvaluator._prepare_strategy_signals = traced_prepare_signals
HourlyStrategyEvaluator.evaluate = traced_evaluate
ModernTFV3Strategy.evaluate_entry = traced_evaluate_entry

def main():
    print("=== Tracing Full Pipeline ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Run for just 1 day
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 15, 23, 59, 59)
    
    print(f"Testing {start_date} to {end_date}")
    
    # Create and run backtester
    backtester = ModernBacktestEngine(config, start_date, end_date)
    
    print("\nRunning backtest with tracing...\n")
    print("="*60)
    
    results = backtester.run_backtest()
    
    print("\n" + "="*60)
    print(f"\nFinal results: {len(results['trades'])} trades")
    
    if results['trades']:
        print("\nFirst trade:")
        print(results['trades'][0])

if __name__ == "__main__":
    main()