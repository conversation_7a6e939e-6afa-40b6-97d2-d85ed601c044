#!/usr/bin/env python3
"""
Simplified diagnostic to check if Modern system is actually updating regimes every 60s
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import yaml

def check_regime_cache_usage():
    """Check if Modern system is using regime cache"""
    print("\n=== CHECKING REGIME CACHE USAGE ===")
    
    # Check ModernBacktestEngine for cache usage
    engine_file = "hyperliquid_bot/modern/backtester_engine.py"
    if os.path.exists(engine_file):
        with open(engine_file, 'r') as f:
            content = f.read()
        
        # Look for cache usage patterns
        cache_indicators = [
            ('use_regime_cache', 'Config uses regime cache'),
            ('regime_cache_dir', 'Has regime cache directory'),
            ('load_cached_regime', 'Loads regimes from cache'),
            ('precomputed_regimes', 'Uses precomputed regimes'),
            ('RegimeStateManager', 'Uses RegimeStateManager (likely caching)')
        ]
        
        for pattern, description in cache_indicators:
            if pattern in content:
                print(f"❌ FOUND: {description} - '{pattern}'")
            else:
                print(f"✅ NOT FOUND: {description}")
    
    # Check for regime cache files
    cache_dir = "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/data/precomputed_regimes"
    if os.path.exists(cache_dir):
        files = os.listdir(cache_dir)
        if files:
            print(f"\n❌ REGIME CACHE FILES FOUND: {len(files)} files in {cache_dir}")
            for f in files[:5]:  # Show first 5
                print(f"   - {f}")
        else:
            print(f"\n✅ No regime cache files found")
    
def analyze_detector_update_logic():
    """Analyze how often detector should update"""
    print("\n\n=== DETECTOR UPDATE FREQUENCY ANALYSIS ===")
    
    # Load configs
    with open('config.yaml', 'r') as f:
        base_config = yaml.safe_load(f)
    
    with open('configs/overrides/modern_system_v2_complete.yaml', 'r') as f:
        modern_config = yaml.safe_load(f)
    
    # Legacy detector config
    legacy_detector = base_config.get('regime', {}).get('detector_type', 'unknown')
    legacy_cadence = base_config.get('regime', {}).get('granular_microstructure', {}).get('cadence_sec', 3600)
    
    # Modern detector config
    modern_detector = modern_config.get('regime', {}).get('detector_type', 'unknown')
    modern_continuous = modern_config.get('regime', {}).get('continuous_modern_v2', {})
    
    print(f"LEGACY SYSTEM:")
    print(f"  Detector: {legacy_detector}")
    print(f"  Update frequency: {legacy_cadence}s ({legacy_cadence/60:.0f} minutes)")
    
    print(f"\nMODERN SYSTEM:")
    print(f"  Detector: {modern_detector}")
    print(f"  Config says: continuous (should be 60s)")
    
    # Check if continuous detector is configured correctly
    if 'continuous' in modern_detector:
        print(f"\n✅ Modern uses continuous detector")
        print(f"   Expected: ~1440 updates per day")
        print(f"   Expected: ~20-50 regime changes per day")
    else:
        print(f"\n❌ Modern doesn't use continuous detector!")

def check_backtester_flow():
    """Check how backtester actually processes data"""
    print("\n\n=== BACKTESTER PROCESSING FLOW ===")
    
    # Check modern backtester
    bt_file = "hyperliquid_bot/modern/backtester_engine.py"
    if os.path.exists(bt_file):
        with open(bt_file, 'r') as f:
            content = f.read()
        
        # Look for hourly processing
        if 'for hour_idx' in content or 'hourly_timestamps' in content:
            print("❌ Backtester processes data HOURLY")
            
            # Check if it updates regime within the hour
            if 'for minute in range(60)' in content:
                print("✅ But it DOES iterate through minutes")
            else:
                print("❌ And does NOT iterate through minutes")
                print("   This confirms regimes only update hourly!")
        
        # Check regime update mechanism
        if 'regime_manager.get_regime' in content:
            print("\n⚠️  Uses regime_manager.get_regime() - likely cached")
        
        if 'detector.compute_regime_live' in content:
            print("✅ Also calls detector.compute_regime_live() - good")
        elif 'compute_regime' in content:
            print("⚠️  Calls compute_regime() - check if it's live or cached")

def main():
    print("="*60)
    print("MODERN SYSTEM REGIME UPDATE DIAGNOSTIC")
    print("="*60)
    
    check_regime_cache_usage()
    analyze_detector_update_logic()
    check_backtester_flow()
    
    print("\n\n" + "="*60)
    print("DIAGNOSIS SUMMARY:")
    print("="*60)
    
    print("\n🔍 KEY INDICATORS OF CACHED HOURLY REGIMES:")
    print("1. Uses RegimeStateManager or regime cache")
    print("2. Processes data hourly without minute iteration")
    print("3. Loads precomputed regime files")
    print("4. Identical distributions to Legacy (21.5% Bull)")
    
    print("\n🎯 TO CONFIRM THE BUG:")
    print("1. Add logging to detector.compute_regime_live()")
    print("2. Count how many times it's called per day")
    print("3. Should be ~1440 (every minute), not ~24 (hourly)")
    
    print("\n💡 THE FIX:")
    print("1. Disable use_regime_cache in config")
    print("2. Remove RegimeStateManager from backtester")
    print("3. Call detector.compute_regime_live() every minute")
    print("4. Trade hourly but with latest regime")

if __name__ == "__main__":
    main()