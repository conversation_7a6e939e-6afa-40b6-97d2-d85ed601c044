#!/usr/bin/env python3
"""
Market Bias Direct Test Script

This script focuses specifically on testing the market bias logic in the RiskManager
without requiring historical data or running a full backtest.
"""

import sys
import logging
import yaml
from pathlib import Path

# Add project root to path to ensure imports work
project_root = Path(__file__).parent.parent.resolve()
sys.path.insert(0, str(project_root))

# Import required components
from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.risk import RiskManager

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(levelname)-7s] %(name)-25s: %(message)s'
)
logger = logging.getLogger("MarketBiasTest")

class MockPortfolio:
    """Mock Portfolio class for testing risk calculations"""
    def __init__(self, balance=10000.0):
        self.balance = balance
        self.logger = logging.getLogger("MockPortfolio")
    
    def get_mark_price(self, signals):
        """Return a fixed mark price"""
        return signals.get('close', 16000.0)
    
    def calculate_account_value(self, signals):
        """Return the balance as account value"""
        return self.balance

def load_config(base_path, override_path=None):
    """Load and merge configuration files"""
    with open(base_path, 'r') as f:
        base_config = yaml.safe_load(f)
    
    if override_path:
        with open(override_path, 'r') as f:
            override_config = yaml.safe_load(f)
        
        # Merge configs
        from deepmerge import always_merger
        config = always_merger.merge(base_config, override_config)
    else:
        config = base_config
    
    return Config(**config)

def create_mock_signals(price=16000.0, atr=800.0):
    """Create mock signals for testing"""
    return {
        'close': price,
        'atr_tf': atr,
        'atr_mr': atr * 0.8,  # Slightly different ATR for MR strategy
        'atr_mv': atr * 0.7,  # Another ATR variant
    }

def test_market_bias_with_regime(config, regime, direction=None):
    """Test market bias calculations with a specific regime"""
    logger.info(f"=== Testing Market Bias for Regime: {regime} ===")
    risk_manager = RiskManager(config)
    portfolio = MockPortfolio(balance=10000.0)
    
    # Create mock signals
    signals = create_mock_signals(price=16000.0, atr=800.0)
    
    # Create strategy info with direction if provided
    strategy_info = None
    if direction:
        strategy_info = {'direction': direction}
        logger.info(f"Using direction: {direction}")
    
    # Try with different strategies to see if results differ
    strategies = ['trend_following', 'mean_reversion']
    
    for strategy in strategies:
        logger.info(f"Strategy: {strategy}")
        
        # Calculate position
        size, leverage = risk_manager.calculate_position(
            portfolio=portfolio,
            signals=signals,
            strategy_name=strategy,
            regime=regime,
            strategy_info=strategy_info
        )
        
        logger.info(f"Result: Position size = {size:.4f}, Leverage = {leverage:.2f}x")
        
        # Estimate notional value and margin required
        notional = size * signals['close']
        margin = notional / leverage if leverage else 0
        pct_of_balance = (size * signals['close']) / portfolio.balance * 100
        
        logger.info(f"Notional: ${notional:.2f}, Margin: ${margin:.2f} ({pct_of_balance:.2f}% of balance)")
        logger.info("-" * 80)
    
    return {'size': size, 'leverage': leverage, 'notional': notional, 'margin': margin}

def main():
    """Main entry point"""
    # Parse command-line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Test Market Bias Settings")
    parser.add_argument('--config', type=str, default=None, help='Path to an override config file')
    parser.add_argument('--regime', type=str, default='Strong_Bull_Trend', help='Regime to test with')
    parser.add_argument('--direction', type=str, choices=['long', 'short'], default=None, help='Trade direction (if specified)')
    args = parser.parse_args()
    
    logger.info("=== Market Bias Direct Test ===")
    
    # Load config
    base_config_path = project_root / 'configs' / 'base.yaml'
    if not base_config_path.exists():
        logger.error(f"Base config not found at {base_config_path}")
        return 1
    
    if args.config:
        override_path = Path(args.config)
        if not override_path.is_absolute():
            override_path = project_root / override_path
        
        if not override_path.exists():
            logger.error(f"Override config not found at {override_path}")
            return 1
        
        logger.info(f"Using base config: {base_config_path}")
        logger.info(f"With override: {override_path}")
        config = load_config(base_config_path, override_path)
    else:
        logger.info(f"Using base config only: {base_config_path}")
        config = load_config(base_config_path)
    
    # Print config settings for verification
    logger.info("=== Market Bias Config ===")
    if hasattr(config, 'regime') and hasattr(config.regime, 'market_bias'):
        market_bias = config.regime.market_bias
        logger.info(f"Enabled: {market_bias.enabled}")
        logger.info(f"Use Three State Mapping: {getattr(market_bias, 'use_three_state_mapping', True)}")
        
        logger.info("--- Leverage Factors ---")
        logger.info(f"Bull: {getattr(market_bias, 'bull_leverage_factor', 1.0)}")
        logger.info(f"Bear: {getattr(market_bias, 'bear_leverage_factor', 1.0)}")
        logger.info(f"Chop: {getattr(market_bias, 'chop_leverage_factor', 1.0)}")
        
        logger.info("--- Risk Factors ---")
        logger.info(f"Bull: {getattr(market_bias, 'bull_risk_factor', 1.0)}")
        logger.info(f"Bear: {getattr(market_bias, 'bear_risk_factor', 1.0)}")
        logger.info(f"Chop: {getattr(market_bias, 'chop_risk_factor', 1.0)}")
        
        logger.info("--- Direction Bias ---")
        logger.info(f"Bull Long: {getattr(market_bias, 'bull_long_bias', 1.0)}")
        logger.info(f"Bull Short: {getattr(market_bias, 'bull_short_bias', 1.0)}")
        logger.info(f"Bear Long: {getattr(market_bias, 'bear_long_bias', 1.0)}")
        logger.info(f"Bear Short: {getattr(market_bias, 'bear_short_bias', 1.0)}")
    else:
        logger.warning("Market bias settings not found in config!")
    
    # Run specific test or comprehensive test suite
    if args.regime:
        # Test with specified regime and direction
        test_market_bias_with_regime(config, args.regime, args.direction)
    else:
        # Run comprehensive test suite
        logger.info("=== Running Comprehensive Test Suite ===")
        
        results = {}
        
        # Test all regimes with no direction
        regimes = [
            'Strong_Bull_Trend', 'Weak_Bull_Trend',
            'Strong_Bear_Trend', 'Weak_Bear_Trend',
            'Volatile_Chop', 'Low_Vol_Chop', 
        ]
        
        for regime in regimes:
            # Test without direction first
            results[f"{regime}_none"] = test_market_bias_with_regime(config, regime)
            
            # Test with long direction
            results[f"{regime}_long"] = test_market_bias_with_regime(config, regime, "long")
            
            # Test with short direction
            results[f"{regime}_short"] = test_market_bias_with_regime(config, regime, "short")
        
        # Print summary
        logger.info("=== Summary of Results ===")
        for key, result in results.items():
            regime, direction = key.rsplit('_', 1)
            logger.info(f"{regime} ({direction}): Size={result['size']:.4f}, Leverage={result['leverage']:.2f}x")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
