#!/usr/bin/env python3
"""
Simplified test script to verify TIGHT_SPREAD handling with the state mapping system.
"""

import logging
import sys
from pathlib import Path
import yaml

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger('verify_tight_spread')

# Ensure the package root is in the Python path
project_root = Path(__file__).parent.parent.resolve()
sys.path.insert(0, str(project_root))

# Import necessary components
from hyperliquid_bot.utils.state_mapping import map_gms_state, get_state_map

def test_tight_spread_mapping():
    """Test how TIGHT_SPREAD is mapped in the state mapping system"""
    # 1. Show the current setting in config
    config_path = project_root / "configs" / "base.yaml"
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    tight_spread_pct = config.get('regime', {}).get('gms_tight_spread_fallback_percentile')
    logger.info(f"CONFIG CHECK: tight_spread_fallback_percentile = {tight_spread_pct}")
    if tight_spread_pct is not None and tight_spread_pct > 0:
        logger.info("✓ TIGHT_SPREAD detection is enabled in configuration")
    else:
        logger.warning("! TIGHT_SPREAD detection is not enabled in configuration")
    
    # 2. Load the state map
    state_map = get_state_map()
    logger.info(f"Loaded state map: {state_map}")
    
    # 3. Check if TIGHT_SPREAD is in the mapping
    if 'TIGHT_SPREAD' in state_map:
        mapped_state = state_map['TIGHT_SPREAD']
        logger.info(f"✓ TIGHT_SPREAD is in the state map and maps to: {mapped_state}")
    else:
        logger.error("✗ TIGHT_SPREAD is not present in the state mapping configuration!")
        return False
        
    # 4. Test the map_gms_state function directly
    mapped_result = map_gms_state('TIGHT_SPREAD')
    logger.info(f"map_gms_state('TIGHT_SPREAD') returns: {mapped_result}")
    
    # 5. Verify mapping to CHOP
    if mapped_result == 'CHOP':
        logger.info("✓ TIGHT_SPREAD correctly maps to CHOP")
        return True
    else:
        logger.error(f"✗ Unexpected mapping: TIGHT_SPREAD maps to {mapped_result}, expected CHOP")
        return False

if __name__ == "__main__":
    print("=== VERIFYING TIGHT_SPREAD HANDLING ===")
    success = test_tight_spread_mapping()
    
    if success:
        print("\n✅ VERIFICATION SUCCESSFUL: TIGHT_SPREAD correctly maps to CHOP")
        print("   - TIGHT_SPREAD detection is enabled in configuration (0.15)")
        print("   - TIGHT_SPREAD is present in the state mapping file")
        print("   - When GMS mapping is active, TIGHT_SPREAD will be treated as CHOP")
        print("   - This will activate any strategies configured for CHOP regimes")
    else:
        print("\n❌ VERIFICATION FAILED: There are issues with the TIGHT_SPREAD mapping")
