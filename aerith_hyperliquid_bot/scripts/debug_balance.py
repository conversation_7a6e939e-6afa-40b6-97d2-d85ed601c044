#!/usr/bin/env python3
# debug_balance.py - Targeted script to verify balance calculation fixes

import sys
import logging
from datetime import datetime
from pathlib import Path

# Add project root to path to ensure imports work
project_root = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.portfolio.portfolio import Portfolio

# Set up basic logging
logging.basicConfig(
    level=logging.WARNING,
    format='[%(levelname)-7s] %(name)-18s: %(message)s',
    handlers=[
        logging.StreamHandler(),
    ]
)

# Ensure all balance-related logs are shown
logging.getLogger('Portfolio').setLevel(logging.WARNING)
logging.getLogger('hyperliquid_bot.portfolio.portfolio').setLevel(logging.WARNING)
logging.getLogger('Backtester').setLevel(logging.WARNING)

def test_portfolio_balance_tracking():
    """Test the Portfolio balance tracking and reconciliation features."""
    print("\n=== Testing Portfolio Balance Tracking and Reconciliation ===\n")
    
    # Load config
    config = load_config('configs/base.yaml')
    
    # Initialize portfolio
    portfolio = Portfolio(config)
    print(f"Initial balance: ${portfolio.balance:.2f}")
    
    # Simulate some transactions
    current_time = datetime.now().timestamp()
    
    # 1. Record margin reservation
    portfolio.record_transaction(
        "margin_reserve", 
        -1000.0, 
        current_time,
        {"position_type": "long", "leverage": 5.0, "notional": 5000.0}
    )
    print(f"After margin reserve: ${portfolio.balance:.2f}, Reserved: ${portfolio.reserved_margin:.2f}")
    
    # 2. Record entry fee
    portfolio.record_transaction(
        "entry_fee", 
        -25.0, 
        current_time,
        {"fee_rate": 0.005, "notional": 5000.0}
    )
    print(f"After entry fee: ${portfolio.balance:.2f}")
    
    # 3. Record positive price difference PnL
    portfolio.record_transaction(
        "price_diff_pnl", 
        300.0, 
        current_time + 3600,
        {"entry_price": 1000.0, "exit_price": 1060.0, "size": 5.0, "direction": "long"}
    )
    print(f"After price diff PnL: ${portfolio.balance:.2f}")
    
    # 4. Record exit fee
    portfolio.record_transaction(
        "exit_fee", 
        -26.5, 
        current_time + 3600,
        {"fee_rate": 0.005, "notional": 5300.0}
    )
    print(f"After exit fee: ${portfolio.balance:.2f}")
    
    # 5. Return margin
    portfolio.record_transaction(
        "margin_release", 
        1000.0, 
        current_time + 3600,
        {"position_type": "long", "leverage": 5.0}
    )
    print(f"After margin release: ${portfolio.balance:.2f}, Reserved: ${portfolio.reserved_margin:.2f}")
    
    # Run reconciliation
    is_reconciled, discrepancy, transaction_totals = portfolio.reconcile_balance()
    
    print(f"\nReconciliation result: {'PASSED' if is_reconciled else 'FAILED'}")
    print(f"Discrepancy: ${discrepancy:.4f}")
    print("\nTransaction totals:")
    for tx_type, amount in transaction_totals.items():
        print(f"  • {tx_type}: ${amount:.2f}")
    
    print("\nFinal balance check:")
    expected_final = 10000.0 - 25.0 + 300.0 - 26.5  # Initial - entry fee + pnl - exit fee
    print(f"Expected final balance: ${expected_final:.2f}")
    print(f"Actual final balance: ${portfolio.balance:.2f}")
    print(f"Difference: ${portfolio.balance - expected_final:.2f}")
    
    return is_reconciled

def main():
    """Main execution function"""
    print("=== Balance Calculation Debug Tool ===")
    
    # Test portfolio balance tracking
    portfolio_test_passed = test_portfolio_balance_tracking()
    
    print("\n=== Summary ===")
    print(f"Portfolio balance tracking test: {'PASSED' if portfolio_test_passed else 'FAILED'}")
    
    # If tests passed, the balance calculation fixes were successful
    if portfolio_test_passed:
        print("\nBalance calculation fixes have been successfully implemented.")
    else:
        print("\nBalance calculation issues still exist. Further debugging is needed.")

if __name__ == "__main__":
    main()
