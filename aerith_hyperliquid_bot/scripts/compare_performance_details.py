#!/usr/bin/env python3
"""
Detailed performance comparison between legacy and modern systems
Focuses on understanding ROI differences despite similar risk metrics
"""

import sys
import os
import subprocess
import json
import re
from datetime import datetime
from collections import defaultdict
import statistics

# Add project root to path  
sys.path.append('/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot')

def run_backtest_and_extract_detailed_metrics(system_mode, config_override=None):
    """Run backtest and extract detailed performance metrics"""
    
    print(f"\n🚀 Running {system_mode.upper()} system backtest...")
    
    # Prepare command
    cmd = [
        'python3', '-m', 'hyperliquid_bot.backtester.run_backtest',
        '--system', system_mode
    ]
    
    if config_override and system_mode == 'modern':
        cmd.extend(['--override', config_override])
    
    # Run with output capture
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=300,  # 5 minute timeout
            cwd='/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot'
        )
        
        output = result.stdout + result.stderr
        
        if result.returncode != 0:
            print(f"❌ Backtest failed with return code {result.returncode}")
            print("Error output:")
            print(result.stderr[:1000])  # First 1000 chars
            return None
            
    except subprocess.TimeoutExpired:
        print(f"❌ Backtest timed out after 5 minutes")
        return None
    except Exception as e:
        print(f"❌ Error running backtest: {e}")
        return None
    
    # Extract comprehensive metrics
    performance_metrics = extract_performance_metrics(output)
    trade_details = extract_trade_details(output)
    portfolio_details = extract_portfolio_details(output)
    
    return {
        'system_mode': system_mode,
        'performance': performance_metrics,
        'trades': trade_details,
        'portfolio': portfolio_details,
        'raw_output': output[-3000:]  # Last 3000 chars for debugging
    }

def extract_performance_metrics(output):
    """Extract detailed performance metrics"""
    
    metrics = {}
    
    # Performance patterns
    patterns = {
        'total_return': r'Total Return.*?([+-]?[\d,]+\.?\d*)%',
        'final_balance': r'Final Balance.*?\$?([\d,]+\.?\d*)',
        'initial_balance': r'Initial.*?Balance.*?\$?([\d,]+\.?\d*)',
        'total_trades': r'Total.*?([\d,]+).*?trades',
        'winning_trades': r'Winning.*?([\d,]+)',
        'losing_trades': r'Losing.*?([\d,]+)',
        'win_rate': r'Win Rate.*?([\d,]+\.?\d*)%',
        'avg_win': r'Avg Win.*?\$?([\d,]+\.?\d*)',
        'avg_loss': r'Avg Loss.*?\$?([\d,]+\.?\d*)',
        'largest_win': r'Largest Win.*?\$?([\d,]+\.?\d*)',
        'largest_loss': r'Largest Loss.*?\$?([\d,]+\.?\d*)',
        'profit_factor': r'Profit Factor.*?([\d,]+\.?\d*)',
        'sharpe': r'Sharpe.*?([\d,]+\.?\d*)',
        'max_drawdown': r'Max Drawdown.*?([\d,]+\.?\d*)%',
        'avg_leverage': r'Avg.*?Leverage.*?([\d,]+\.?\d*)',
        'max_leverage': r'Max.*?Leverage.*?([\d,]+\.?\d*)'
    }
    
    for metric, pattern in patterns.items():
        matches = re.findall(pattern, output, re.IGNORECASE)
        if matches:
            try:
                # Take the last match (most recent/final)
                value = matches[-1].replace(',', '')
                metrics[metric] = float(value)
            except ValueError:
                metrics[metric] = matches[-1]
    
    return metrics

def extract_trade_details(output):
    """Extract individual trade details for analysis"""
    
    trades = []
    
    # Pattern for individual trade exits
    exit_pattern = r'EXIT: (WIN|LOSS) \([^)]+\) Closed (long|short) ([\d.]+) at \$([^.]+\.\d+)\. Net PnL: \$([^.]+\.\d+)'
    
    for match in re.finditer(exit_pattern, output):
        outcome = match.group(1)  # WIN/LOSS
        direction = match.group(2)  # long/short
        size = float(match.group(3))
        exit_price = float(match.group(4))
        pnl = float(match.group(5))
        
        trades.append({
            'outcome': outcome,
            'direction': direction,
            'size': size,
            'exit_price': exit_price,
            'pnl': pnl
        })
    
    # Analyze trade distribution
    analysis = {
        'total_trades': len(trades),
        'winning_trades': len([t for t in trades if t['outcome'] == 'WIN']),
        'losing_trades': len([t for t in trades if t['outcome'] == 'LOSS']),
        'long_trades': len([t for t in trades if t['direction'] == 'long']),
        'short_trades': len([t for t in trades if t['direction'] == 'short']),
        'all_pnls': [t['pnl'] for t in trades],
        'winning_pnls': [t['pnl'] for t in trades if t['outcome'] == 'WIN'],
        'losing_pnls': [t['pnl'] for t in trades if t['outcome'] == 'LOSS'],
        'trade_sizes': [t['size'] for t in trades],
        'trades': trades
    }
    
    # Calculate additional statistics
    if analysis['all_pnls']:
        analysis['total_pnl'] = sum(analysis['all_pnls'])
        analysis['avg_pnl'] = statistics.mean(analysis['all_pnls'])
        analysis['median_pnl'] = statistics.median(analysis['all_pnls'])
        analysis['pnl_std'] = statistics.stdev(analysis['all_pnls']) if len(analysis['all_pnls']) > 1 else 0
        
    if analysis['winning_pnls']:
        analysis['avg_win'] = statistics.mean(analysis['winning_pnls'])
        analysis['largest_win'] = max(analysis['winning_pnls'])
        
    if analysis['losing_pnls']:
        analysis['avg_loss'] = statistics.mean(analysis['losing_pnls'])
        analysis['largest_loss'] = min(analysis['losing_pnls'])  # Most negative
        
    if analysis['trade_sizes']:
        analysis['avg_size'] = statistics.mean(analysis['trade_sizes'])
        analysis['median_size'] = statistics.median(analysis['trade_sizes'])
        analysis['max_size'] = max(analysis['trade_sizes'])
        
    return analysis

def extract_portfolio_details(output):
    """Extract portfolio and leverage information"""
    
    portfolio = {}
    
    # Look for leverage information
    leverage_pattern = r'Lev=([\d.]+)x'
    leverages = [float(m) for m in re.findall(leverage_pattern, output)]
    
    if leverages:
        portfolio['avg_leverage'] = statistics.mean(leverages)
        portfolio['max_leverage'] = max(leverages)
        portfolio['min_leverage'] = min(leverages)
        portfolio['leverage_std'] = statistics.stdev(leverages) if len(leverages) > 1 else 0
    
    # Look for balance progression
    balance_pattern = r'Balance.*?\$([^,\s]+)'
    balances = []
    for match in re.finditer(balance_pattern, output):
        try:
            balance = float(match.group(1).replace(',', ''))
            balances.append(balance)
        except ValueError:
            continue
    
    if balances:
        portfolio['initial_balance'] = balances[0] if balances else 10000
        portfolio['final_balance'] = balances[-1] if balances else 10000
        portfolio['balance_progression'] = balances[-20:]  # Last 20 balance points
        
    return portfolio

def compare_detailed_performance(legacy_result, modern_result):
    """Detailed comparison of performance metrics"""
    
    print(f"\n{'='*80}")
    print(f"🔍 DETAILED PERFORMANCE COMPARISON")
    print(f"{'='*80}")
    
    if not legacy_result or not modern_result:
        print("❌ Cannot compare - one or both backtests failed")
        return
    
    legacy_perf = legacy_result['performance']
    modern_perf = modern_result['performance']
    legacy_trades = legacy_result['trades']
    modern_trades = modern_result['trades']
    
    print(f"\n📊 CORE PERFORMANCE METRICS:")
    print(f"{'Metric':<20} {'Legacy':<15} {'Modern':<15} {'Difference':<15} {'% Change'}")
    print(f"{'-'*80}")
    
    core_metrics = [
        ('Total Return %', 'total_return'),
        ('Final Balance', 'final_balance'),
        ('Total Trades', 'total_trades'),
        ('Win Rate %', 'win_rate'),
        ('Profit Factor', 'profit_factor'),
        ('Sharpe Ratio', 'sharpe'),
        ('Max Drawdown %', 'max_drawdown'),
    ]
    
    roi_gap = None
    for metric_name, key in core_metrics:
        legacy_val = legacy_perf.get(key, 0)
        modern_val = modern_perf.get(key, 0)
        diff = modern_val - legacy_val
        pct_change = (diff / legacy_val * 100) if legacy_val != 0 else 0
        
        if key == 'total_return':
            roi_gap = diff
            
        print(f"{metric_name:<20} {legacy_val:10.2f}     {modern_val:10.2f}     {diff:10.2f}     {pct_change:7.1f}%")
    
    print(f"\n💰 TRADE ANALYSIS:")
    print(f"{'Metric':<25} {'Legacy':<15} {'Modern':<15} {'Difference'}")
    print(f"{'-'*70}")
    
    trade_metrics = [
        ('Average Win $', 'avg_win'),
        ('Average Loss $', 'avg_loss'),
        ('Largest Win $', 'largest_win'),
        ('Largest Loss $', 'largest_loss'),
        ('Total PnL $', 'total_pnl'),
        ('Average PnL $', 'avg_pnl'),
        ('PnL Std Dev $', 'pnl_std'),
        ('Average Size', 'avg_size'),
        ('Max Size', 'max_size'),
    ]
    
    for metric_name, key in trade_metrics:
        legacy_val = legacy_trades.get(key, 0)
        modern_val = modern_trades.get(key, 0)
        diff = modern_val - legacy_val
        print(f"{metric_name:<25} {legacy_val:10.2f}     {modern_val:10.2f}     {diff:10.2f}")
    
    print(f"\n🎯 ROI GAP ANALYSIS:")
    if roi_gap:
        print(f"  📉 ROI Gap: {roi_gap:.1f}% (Legacy: {legacy_perf.get('total_return', 0):.1f}% vs Modern: {modern_perf.get('total_return', 0):.1f}%)")
        
        # Calculate potential causes
        trade_diff = modern_trades.get('total_trades', 0) - legacy_trades.get('total_trades', 0)
        pnl_diff = modern_trades.get('total_pnl', 0) - legacy_trades.get('total_pnl', 0)
        
        print(f"  📊 Trade Count Impact: {trade_diff:+d} trades")
        print(f"  💵 PnL Impact: ${pnl_diff:+.2f}")
        
        # Analyze win/loss distribution
        legacy_win_pct = legacy_trades.get('winning_trades', 0) / max(legacy_trades.get('total_trades', 1), 1) * 100
        modern_win_pct = modern_trades.get('winning_trades', 0) / max(modern_trades.get('total_trades', 1), 1) * 100
        win_rate_diff = modern_win_pct - legacy_win_pct
        
        print(f"  📈 Win Rate Impact: {win_rate_diff:+.1f}% points")
        
        # Check for outlier trades
        if modern_trades.get('losing_pnls'):
            worst_modern_loss = min(modern_trades['losing_pnls'])
            worst_legacy_loss = min(legacy_trades['losing_pnls']) if legacy_trades.get('losing_pnls') else 0
            outlier_impact = worst_modern_loss - worst_legacy_loss
            
            print(f"  💥 Worst Loss Impact: ${outlier_impact:+.2f}")
            
        # Leverage analysis
        legacy_portfolio = legacy_result.get('portfolio', {})
        modern_portfolio = modern_result.get('portfolio', {})
        
        legacy_avg_lev = legacy_portfolio.get('avg_leverage', 0)
        modern_avg_lev = modern_portfolio.get('avg_leverage', 0)
        lev_diff = modern_avg_lev - legacy_avg_lev
        
        print(f"  🎚️  Average Leverage Impact: {lev_diff:+.2f}x")
    
    print(f"\n🔍 DETAILED FINDINGS:")
    
    # Check for specific issues
    if roi_gap and roi_gap < -50:  # Significant ROI gap
        print(f"  🚨 SIGNIFICANT ROI GAP DETECTED: {roi_gap:.1f}%")
        
        # Check position sizing
        modern_avg_size = modern_trades.get('avg_size', 0)
        legacy_avg_size = legacy_trades.get('avg_size', 0)
        size_ratio = modern_avg_size / legacy_avg_size if legacy_avg_size > 0 else 1
        
        if size_ratio < 0.8:
            print(f"  📉 Modern system has smaller average position sizes: {size_ratio:.2f}x")
            
        # Check for excessive losses
        modern_worst = min(modern_trades.get('losing_pnls', [0]))
        legacy_worst = min(legacy_trades.get('losing_pnls', [0]))
        
        if modern_worst < legacy_worst * 1.5:
            print(f"  💥 Modern system has larger losses: ${modern_worst:.2f} vs ${legacy_worst:.2f}")
            
        # Check trade frequency efficiency
        modern_pnl_per_trade = modern_trades.get('avg_pnl', 0)
        legacy_pnl_per_trade = legacy_trades.get('avg_pnl', 0)
        efficiency_ratio = modern_pnl_per_trade / legacy_pnl_per_trade if legacy_pnl_per_trade != 0 else 1
        
        if efficiency_ratio < 0.8:
            print(f"  📊 Modern system less efficient per trade: {efficiency_ratio:.2f}x")
    
    print(f"\n💡 RECOMMENDATIONS:")
    if roi_gap and roi_gap < -30:
        print(f"  1. 🔧 Check position sizing - modern avg: {modern_trades.get('avg_size', 0):.3f} vs legacy: {legacy_trades.get('avg_size', 0):.3f}")
        print(f"  2. 🎚️  Review leverage settings - modern avg: {modern_portfolio.get('avg_leverage', 0):.2f}x vs legacy: {legacy_portfolio.get('avg_leverage', 0):.2f}x")
        print(f"  3. 💰 Analyze largest losses for outliers")
        print(f"  4. ⚖️  Compare risk management parameters")
    
    return {
        'roi_gap': roi_gap,
        'legacy_performance': legacy_perf,
        'modern_performance': modern_perf,
        'legacy_trades': legacy_trades,
        'modern_trades': modern_trades
    }

def main():
    """Run comprehensive performance comparison"""
    
    print("🔍 STARTING DETAILED PERFORMANCE COMPARISON")
    print("=" * 80)
    
    # Run legacy system
    legacy_result = run_backtest_and_extract_detailed_metrics('legacy')
    
    if not legacy_result:
        print("❌ Legacy system failed, aborting comparison")
        return
    
    # Run modern system
    modern_result = run_backtest_and_extract_detailed_metrics('modern', 'configs/overrides/execution_refinement_enabled.yaml')
    
    if not modern_result:
        print("❌ Modern system failed, aborting comparison")
        return
    
    # Compare results
    comparison = compare_detailed_performance(legacy_result, modern_result)
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/performance_comparison_{timestamp}.json"
    
    comparison_data = {
        'timestamp': datetime.now().isoformat(),
        'legacy_result': legacy_result,
        'modern_result': modern_result,
        'comparison': comparison
    }
    
    with open(results_file, 'w') as f:
        json.dump(comparison_data, f, indent=2, default=str)
    
    print(f"\n💾 Detailed results saved to: {results_file}")
    
    return comparison_data

if __name__ == "__main__":
    main()