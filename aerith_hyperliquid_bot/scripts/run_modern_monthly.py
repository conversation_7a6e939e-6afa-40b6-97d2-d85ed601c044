#!/usr/bin/env python3
"""
Run Modern System Monthly Backtests
===================================

Run monthly backtests to avoid data loading issues.
"""

import sys
from pathlib import Path
from datetime import datetime
import json

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine


def run_month(year: int, month: int) -> dict:
    """Run backtest for a single month."""
    # Calculate start and end dates
    start_date = datetime(year, month, 1)
    
    # Calculate end of month
    if month == 12:
        end_date = datetime(year + 1, 1, 1)
    else:
        end_date = datetime(year, month + 1, 1)
    
    print(f"\n📅 Running {start_date.strftime('%B %Y')}...")
    
    # Load configuration
    config_path = project_root / "configs/overrides/modern_system_v2_complete.yaml"
    config = load_config(config_path=str(config_path))
    
    try:
        # Create modern backtesting engine
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        # Run backtest
        results = engine.run_backtest()
        
        # Extract key metrics
        total_trades = results['performance']['total_trades']
        total_return = results['performance']['total_return']
        win_rate = results['performance']['win_rate']
        
        print(f"  ✅ Trades: {total_trades}, Return: {total_return:.2%}, Win Rate: {win_rate:.2%}")
        
        return {
            'month': start_date.strftime('%Y-%m'),
            'trades': total_trades,
            'return': total_return,
            'win_rate': win_rate,
            'success': True
        }
        
    except Exception as e:
        print(f"  ❌ Failed: {e}")
        return {
            'month': start_date.strftime('%Y-%m'),
            'trades': 0,
            'return': 0.0,
            'win_rate': 0.0,
            'success': False,
            'error': str(e)
        }


def main():
    print("="*80)
    print("AERITH MODERN SYSTEM - 2024 MONTHLY BACKTESTS")
    print("="*80)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("Running monthly backtests to avoid data loading issues...")
    print()
    print("🎯 CALIBRATED SETTINGS:")
    print("  - Confidence threshold: 0.5")
    print("  - State persistence: DISABLED")
    print("  - Max transitions/hour: 60")
    print("  - Risk per trade: 25%")
    print("-"*80)
    
    # Run each month
    monthly_results = []
    
    for month in range(1, 13):  # Jan through Dec
        result = run_month(2024, month)
        monthly_results.append(result)
    
    # Aggregate results
    successful_months = [r for r in monthly_results if r['success']]
    
    if successful_months:
        total_trades = sum(r['trades'] for r in successful_months)
        
        # Calculate compound return
        compound_return = 1.0
        for r in successful_months:
            compound_return *= (1 + r['return'])
        compound_return -= 1
        
        # Average win rate
        avg_win_rate = sum(r['win_rate'] for r in successful_months) / len(successful_months)
        
        print("\n" + "="*80)
        print("📊 2024 FULL YEAR RESULTS (AGGREGATED)")
        print("="*80)
        print(f"Successful months: {len(successful_months)}/12")
        print()
        print("PERFORMANCE METRICS:")
        print(f"  - Total Trades: {total_trades}")
        print(f"  - Compound Return: {compound_return:.2%}")
        print(f"  - Average Win Rate: {avg_win_rate:.2%}")
        print()
        
        # Compare with legacy
        print("VS LEGACY SYSTEM:")
        legacy_trades = 180
        legacy_roi = 2.15  # 215%
        
        print(f"  - Trade Count: {total_trades}/{legacy_trades} ({total_trades/legacy_trades:.1%})")
        print(f"  - ROI: {compound_return:.2%}/{legacy_roi:.0%} ({compound_return/legacy_roi:.1%})")
        
        # Monthly breakdown
        print("\nMONTHLY BREAKDOWN:")
        for r in monthly_results:
            status = "✅" if r['success'] else "❌"
            print(f"  {status} {r['month']}: {r['trades']} trades, {r['return']:.2%} return")
        
        # Save results
        output_path = Path('modern_2024_monthly_results.json')
        with open(output_path, 'w') as f:
            json.dump({
                'monthly_results': monthly_results,
                'aggregate': {
                    'total_trades': total_trades,
                    'compound_return': compound_return,
                    'avg_win_rate': avg_win_rate
                }
            }, f, indent=2, default=str)
        
        print(f"\n💾 Detailed results saved to: {output_path}")
        
    else:
        print("\n❌ All months failed!")
    
    print("\n" + "="*80)


if __name__ == "__main__":
    main()