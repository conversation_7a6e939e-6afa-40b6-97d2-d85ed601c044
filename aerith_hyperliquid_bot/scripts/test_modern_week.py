#!/usr/bin/env python3
"""
Test modern system with a full week of data (Jan 2-7, 2024).
This avoids the data boundary issue on Jan 1st.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from datetime import datetime
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
import json

# Set up minimal logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Silence verbose loggers
for logger_name in ['ModernDataLoader', 'ModernDataAdapter', 'ModernDataContract', 
                   'hyperliquid_bot.modern.data_aggregator', 'hyperliquid_bot.modern.regime_state_manager']:
    logging.getLogger(logger_name).setLevel(logging.WARNING)

def main():
    print("=== Modern System Week Test (Jan 2-7, 2024) ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Test Jan 2-7 to avoid data boundary issue
    start_date = datetime(2024, 1, 2)
    end_date = datetime(2024, 1, 7)
    
    print(f"Test period: {start_date.date()} to {end_date.date()} (5 days)")
    print(f"Risk per trade: {config.portfolio.risk_per_trade:.2%}")
    print(f"Regime detector: {config.regime.detector_type}")
    print(f"Adaptive thresholds: DISABLED (performance fix)\n")
    
    # Create and run backtest
    try:
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        print("Starting backtest...")
        print("-" * 50)
        
        results = engine.run_backtest()
        
        # Display results
        print("\n" + "=" * 50)
        print("BACKTEST RESULTS")
        print("=" * 50)
        
        trades = results['trades']
        perf = results['performance']
        
        print(f"\nTotal trades: {len(trades)}")
        print(f"Runtime: {results['runtime_seconds']:.1f}s")
        
        if trades:
            print(f"\n✅ SUCCESS! Generated {len(trades)} trades")
            print(f"Total return: {perf.get('total_return', 0):.2%}")
            print(f"Win rate: {perf.get('win_rate', 0):.2%}")
            
            # Show first 5 trades
            print("\nFirst 5 trades:")
            for i, trade in enumerate(trades[:5]):
                print(f"  {i+1}. {trade['timestamp']} - {trade['direction']} @ ${trade['entry_price']:.2f}")
                print(f"     Size: {trade['position_size']:.2%}, Regime: {trade.get('regime', 'N/A')}")
            
            # Analyze regime distribution
            regime_counts = {}
            for trade in trades:
                regime = trade.get('regime', 'Unknown')
                regime_counts[regime] = regime_counts.get(regime, 0) + 1
            
            print("\nRegime distribution:")
            for regime, count in sorted(regime_counts.items()):
                print(f"  {regime}: {count} trades ({count/len(trades)*100:.1f}%)")
        else:
            print("\n❌ NO TRADES GENERATED!")
            
            # Debug information
            if results['regime_history']:
                states = [r['state'] for r in results['regime_history']]
                unique_states = set(states)
                print(f"\nRegime states detected: {unique_states}")
                print(f"Total regime updates: {len(results['regime_history'])}")
                
                # Count state distribution
                state_counts = {}
                for state in states:
                    state_counts[state] = state_counts.get(state, 0) + 1
                
                print("\nState distribution:")
                for state, count in sorted(state_counts.items()):
                    print(f"  {state}: {count} ({count/len(states)*100:.1f}%)")
        
        # Save results
        output_file = f"modern_week_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\nResults saved to: {output_file}")
        
        # Summary
        print("\n" + "=" * 50)
        if trades:
            print(f"✅ Modern system is now working! Generated {len(trades)} trades.")
            if len(trades) < 60:
                print(f"⚠️  Trade count ({len(trades)}) is below target (60+).")
                print("   Consider tuning thresholds or strategy parameters.")
        else:
            print("❌ Modern system still not generating trades.")
            print("   Check strategy entry conditions and thresholds.")
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())