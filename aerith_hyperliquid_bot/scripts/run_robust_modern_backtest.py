#!/usr/bin/env python3
"""
Run a backtest using the robust modern system.

This script demonstrates how to use the new robust components
that combine modern architecture with legacy robustness.
"""

import sys
import logging
import json
from pathlib import Path
from datetime import datetime
import argparse

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_backtest_engine import RobustBacktestEngine


def setup_logging(log_level: str = "INFO"):
    """Configure logging for the backtest."""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Create log file with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"robust_modern_backtest_{timestamp}.log"
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    # Reduce noise from some modules
    logging.getLogger("matplotlib").setLevel(logging.WARNING)
    logging.getLogger("PIL").setLevel(logging.WARNING)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Logging to {log_file}")
    
    return logger


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Run backtest with robust modern system"
    )
    
    parser.add_argument(
        "--start-date",
        type=str,
        default="2024-01-01",
        help="Start date (YYYY-MM-DD)"
    )
    
    parser.add_argument(
        "--end-date", 
        type=str,
        default="2024-02-01",
        help="End date (YYYY-MM-DD)"
    )
    
    parser.add_argument(
        "--config",
        type=str,
        default="configs/overrides/modern_system_v2_complete.yaml",
        help="Config name to use"
    )
    
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Disable regime cache (slower but real-time simulation)"
    )
    
    parser.add_argument(
        "--log-level",
        type=str,
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Logging level"
    )
    
    parser.add_argument(
        "--output",
        type=str,
        help="Output file for results (JSON)"
    )
    
    return parser.parse_args()


def format_results(results: dict) -> str:
    """Format backtest results for display."""
    output = []
    output.append("\n" + "="*60)
    output.append("BACKTEST RESULTS - ROBUST MODERN SYSTEM")
    output.append("="*60)
    
    # Performance metrics
    output.append("\nPerformance Metrics:")
    output.append(f"  Total Return: {results['total_return']:.2%}")
    output.append(f"  Average Return per Trade: {results['average_return']:.2%}")
    output.append(f"  Sharpe Ratio: {results['sharpe_ratio']:.2f}")
    output.append(f"  Maximum Drawdown: {results['max_drawdown']:.2%}")
    
    # Trade statistics
    output.append("\nTrade Statistics:")
    output.append(f"  Total Trades: {results['total_trades']}")
    output.append(f"  Winning Trades: {results['winning_trades']}")
    output.append(f"  Losing Trades: {results['losing_trades']}")
    output.append(f"  Win Rate: {results['win_rate']:.1%}")
    
    # Data quality
    quality = results['data_quality']
    output.append("\nData Quality:")
    output.append(f"  Warmup Period Used: {quality['warmup_hours_used']} hours")
    output.append(f"  Warmup Period Requested: {quality['warmup_hours_requested']} hours")
    output.append(f"  Regime Cache Available: {'Yes' if quality['regime_cache_available'] else 'No'}")
    
    # Warnings
    if 'error' in results:
        output.append(f"\n⚠️  ERROR: {results['error']}")
    
    if quality['warmup_hours_used'] < quality['warmup_hours_requested'] * 0.5:
        output.append(f"\n⚠️  WARNING: Only {quality['warmup_hours_used']/quality['warmup_hours_requested']:.0%} of requested warmup available")
    
    output.append("\n" + "="*60)
    
    return "\n".join(output)


def main():
    """Run the robust modern backtest."""
    args = parse_arguments()
    
    # Setup logging
    logger = setup_logging(args.log_level)
    
    logger.info("Starting Robust Modern System Backtest")
    logger.info(f"Period: {args.start_date} to {args.end_date}")
    
    # Parse dates
    try:
        start_date = datetime.strptime(args.start_date, "%Y-%m-%d")
        end_date = datetime.strptime(args.end_date, "%Y-%m-%d")
    except ValueError as e:
        logger.error(f"Invalid date format: {e}")
        return 1
    
    # Load configuration
    try:
        config = load_config(args.config)
        logger.info(f"Loaded config: {args.config}")
    except Exception as e:
        logger.error(f"Failed to load config: {e}")
        return 1
    
    # Create backtest engine
    try:
        engine = RobustBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date,
            use_regime_cache=not args.no_cache
        )
        
        logger.info("Backtest engine initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize backtest engine: {e}")
        return 1
    
    # Run backtest
    logger.info("Running backtest...")
    
    try:
        results = engine.run_backtest()
        
        # Display results
        print(format_results(results))
        
        # Save results if requested
        if args.output:
            output_path = Path(args.output)
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            logger.info(f"Results saved to {output_path}")
        
        # Return success/failure based on results
        if results['total_trades'] == 0:
            logger.warning("No trades executed - check strategy parameters")
            return 2
        elif results['total_return'] < -0.5:
            logger.warning(f"Large loss detected: {results['total_return']:.2%}")
            return 3
        else:
            logger.info("Backtest completed successfully")
            return 0
            
    except Exception as e:
        logger.error(f"Backtest failed: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    sys.exit(main())