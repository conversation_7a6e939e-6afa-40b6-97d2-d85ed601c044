#!/usr/bin/env python3
"""
Pre-compute Regime States for Modern System
===========================================

This script pre-computes all regime states offline to enable fast backtesting.
Run this ONCE when GMS logic changes or for new data periods.

The pre-computation:
1. Loads features_1s data
2. Updates GMS detector every 60 seconds
3. Saves hourly regime states to parquet

This approach provides 100x+ speedup for backtesting!
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import argparse
import logging
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, List, Any

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.continuous_detector_v2 import ModernContinuousDetectorV2
from hyperliquid_bot.modern.data_loader import ModernDataLoader
from hyperliquid_bot.modern.data_aggregator import ModernDataAggregator


class RegimePrecomputer:
    """Pre-compute regime states for efficient backtesting."""
    
    def __init__(self, config_path: str = 'configs/overrides/modern_system_v2_complete.yaml'):
        """
        Initialize regime pre-computer.
        
        Args:
            config_path: Path to configuration file
        """
        self.config = load_config(config_path)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize components
        self.detector = ModernContinuousDetectorV2(self.config)
        self.data_loader = ModernDataLoader(self.config)
        self.data_aggregator = ModernDataAggregator()
        
        # Output directory
        self.output_dir = Path('data/precomputed_regimes')
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(
            f"RegimePrecomputer initialized:\n"
            f"  - Config: {config_path}\n"
            f"  - Detector: {self.config.regime.detector_type}\n"
            f"  - Output: {self.output_dir}"
        )
    
    def precompute_date_range(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """
        Pre-compute regime states for a date range.
        
        Args:
            start_date: Start date (inclusive)
            end_date: End date (exclusive)
            
        Returns:
            DataFrame with hourly regime states
        """
        self.logger.info(f"Pre-computing regimes from {start_date} to {end_date}")
        
        results = []
        current_date = start_date
        
        # Process day by day to manage memory
        while current_date < end_date:
            next_date = current_date + timedelta(days=1)
            
            self.logger.info(f"Processing {current_date.date()}...")
            
            # Load 1-second features for the day
            features_1s = self.data_loader.load_features_1s(current_date, next_date)
            
            if features_1s.empty:
                self.logger.warning(f"No data for {current_date.date()}, skipping")
                current_date = next_date
                continue
            
            # Process minute by minute
            minute_results = self._process_day(features_1s, current_date)
            results.extend(minute_results)
            
            current_date = next_date
        
        # Convert to DataFrame
        if not results:
            self.logger.error("No regime states computed!")
            return pd.DataFrame()
        
        df = pd.DataFrame(results)
        self.logger.info(f"Computed {len(df)} hourly regime states")
        
        return df
    
    def _process_day(self, features_1s: pd.DataFrame, date: datetime) -> List[Dict[str, Any]]:
        """
        Process one day of data, updating GMS every 60 seconds.
        
        Args:
            features_1s: 1-second feature data for the day
            date: The date being processed
            
        Returns:
            List of hourly regime states
        """
        hourly_states = []
        
        # Process hourly to save computation time
        # We still update every minute but only save hourly states
        current_hour = date.replace(hour=0, minute=0, second=0)
        end_time = current_hour + timedelta(days=1)
        
        while current_hour < end_time:
            # Process the hour
            hour_end = current_hour + timedelta(hours=1)
            
            # Get all data for this hour
            hour_data = features_1s[
                (features_1s.index >= current_hour) & 
                (features_1s.index < hour_end)
            ]
            
            if hour_data.empty:
                current_hour = hour_end
                continue
            
            # Simulate minute-by-minute updates for this hour
            for minute_offset in range(60):
                minute_time = current_hour + timedelta(minutes=minute_offset)
                
                # Get data up to this minute
                minute_data = hour_data[hour_data.index <= minute_time]
                
                if len(minute_data) < 60:  # Need at least 1 minute
                    continue
                
                # Aggregate last 60 seconds
                last_60s = minute_data.tail(60)
                if len(last_60s) < 60:
                    continue
                
                # Simple aggregation - use mean for most fields
                latest_features = {
                    'timestamp': minute_time,
                    'close': last_60s['close'].iloc[-1],
                    'volume': last_60s['volume'].sum(),
                    'volume_imbalance': last_60s['volume_imbalance'].mean(),
                    'spread_mean': last_60s['spread_mean'].mean(),
                    'spread_std': last_60s['spread_std'].mean(),
                    'atr_14_sec': last_60s['atr_14_sec'].iloc[-1] if 'atr_14_sec' in last_60s else 0,
                    'atr_percent_sec': last_60s['atr_percent_sec'].iloc[-1] if 'atr_percent_sec' in last_60s else 0,
                    'ma_slope': last_60s['ma_slope'].iloc[-1] if 'ma_slope' in last_60s else 0,
                    'ma_slope_ema_30s': last_60s['ma_slope_ema_30s'].iloc[-1] if 'ma_slope_ema_30s' in last_60s else 0,
                }
                
                # Update regime detector
                regime_state = self.detector.detect_regime(latest_features, minute_time)
            
            # After processing all 60 minutes, save the hourly state
            confidence = self.detector.get_confidence()
            
            hourly_state = {
                'timestamp': current_hour,
                'regime': regime_state,  # Last regime of the hour
                'confidence': confidence,
                'volatility': latest_features.get('atr_percent_sec', 0.0),
                'momentum': latest_features.get('ma_slope_ema_30s', 0.0),
                'volume_imbalance': latest_features.get('volume_imbalance', 0.0),
                'spread_volatility': latest_features.get('spread_std', 0.0),
                'regime_duration_hours': self._get_regime_duration(),
                'signal_quality': 1.0,
                'risk_suppressed': False
            }
            
            hourly_states.append(hourly_state)
            
            self.logger.info(
                f"{current_hour}: {regime_state} "
                f"(confidence: {confidence:.2f})"
            )
            
            current_hour = hour_end
        
        return hourly_states
    
    def _get_regime_duration(self) -> int:
        """Get duration of current regime in hours."""
        # This would track regime transitions in the detector
        # For now, return a placeholder
        return 1
    
    def save_results(self, df: pd.DataFrame, year: int):
        """
        Save pre-computed regime states to parquet.
        
        Args:
            df: DataFrame with regime states
            year: Year of the data
        """
        if df.empty:
            self.logger.error("Cannot save empty DataFrame")
            return
        
        output_file = self.output_dir / f"regimes_{year}.parquet"
        
        # Set timestamp as index for efficient lookups
        df.set_index('timestamp', inplace=True)
        
        # Save to parquet with compression
        df.to_parquet(output_file, compression='snappy')
        
        # Calculate file size
        file_size_kb = output_file.stat().st_size / 1024
        
        self.logger.info(
            f"Saved {len(df)} regime states to {output_file} "
            f"({file_size_kb:.1f} KB)"
        )
        
        # Print summary statistics
        print("\nRegime Distribution:")
        print(df['regime'].value_counts())
        print(f"\nAverage confidence: {df['confidence'].mean():.3f}")
        print(f"Risk suppressed hours: {df['risk_suppressed'].sum()}")


def main():
    """Main entry point for regime pre-computation."""
    parser = argparse.ArgumentParser(
        description="Pre-compute regime states for modern system backtesting"
    )
    parser.add_argument(
        '--start', 
        type=str, 
        required=True,
        help='Start date (YYYY-MM-DD)'
    )
    parser.add_argument(
        '--end', 
        type=str, 
        required=True,
        help='End date (YYYY-MM-DD)'
    )
    parser.add_argument(
        '--config',
        type=str,
        default='configs/overrides/modern_system_v2_complete.yaml',
        help='Configuration file path'
    )
    parser.add_argument(
        '--log-level',
        type=str,
        default='INFO',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='Logging level'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Suppress verbose loggers
    for logger_name in ['ModernDataLoader', 'ModernDataAdapter']:
        logging.getLogger(logger_name).setLevel(logging.WARNING)
    
    # Parse dates
    start_date = datetime.strptime(args.start, '%Y-%m-%d')
    end_date = datetime.strptime(args.end, '%Y-%m-%d')
    
    print(f"\n{'='*60}")
    print("REGIME PRE-COMPUTATION")
    print(f"{'='*60}")
    print(f"Period: {start_date.date()} to {end_date.date()}")
    print(f"Config: {args.config}")
    print(f"{'='*60}\n")
    
    # Create pre-computer
    precomputer = RegimePrecomputer(args.config)
    
    # Process the date range
    import time
    start_time = time.time()
    
    regime_states = precomputer.precompute_date_range(start_date, end_date)
    
    if not regime_states.empty:
        # Save by year
        for year in regime_states['timestamp'].dt.year.unique():
            year_data = regime_states[regime_states['timestamp'].dt.year == year]
            precomputer.save_results(year_data, year)
    
    elapsed = time.time() - start_time
    print(f"\n{'='*60}")
    print(f"Pre-computation complete in {elapsed:.1f} seconds")
    print(f"{'='*60}\n")
    

if __name__ == "__main__":
    main()