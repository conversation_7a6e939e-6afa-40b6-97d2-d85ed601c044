#!/usr/bin/env python3
"""
Verify regime update frequencies between Legacy and Modern systems.
If Modern updates every 60s vs Legacy's hourly, we should see ~60x more transitions.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hyperliquid_bot.backtester.gms_detector import GMSDetector  # Legacy
from hyperliquid_bot.modern.continuous_gms_detector import ModernContinuousGMSDetector  # Modern
from hyperliquid_bot.data_loader import DataLoader
from hyperliquid_bot.modern.data_loader import ModernDataLoader
import yaml

def analyze_regime_transitions():
    print("=== REGIME UPDATE FREQUENCY ANALYSIS ===\n")
    
    # Load configs
    with open('config.yaml', 'r') as f:
        base_config = yaml.safe_load(f)
    
    with open('configs/overrides/modern_system_v2_complete.yaml', 'r') as f:
        modern_config = yaml.safe_load(f)
    
    # Merge configs
    config = {**base_config, **modern_config}
    
    # Test period: January 2024 (1 month)
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 31, 23, 59, 59)
    
    print(f"Test Period: {start_date} to {end_date}")
    print("-" * 50)
    
    # 1. Legacy System Analysis
    print("\n1. LEGACY SYSTEM (Hourly Updates):")
    legacy_transitions = []
    legacy_detector = GMSDetector(config)
    legacy_loader = DataLoader(config)
    
    current_regime = None
    regime_start = None
    
    # Process hourly
    current = start_date
    while current <= end_date:
        try:
            data = legacy_loader.get_data('BTC', current, current)
            if not data.empty:
                regime_data = legacy_detector.compute_regime(data)
                new_regime = regime_data['state'].iloc[-1]
                
                if current_regime != new_regime:
                    if current_regime is not None:
                        duration = (current - regime_start).total_seconds() / 3600
                        legacy_transitions.append({
                            'timestamp': current,
                            'from': current_regime,
                            'to': new_regime,
                            'duration_hours': duration
                        })
                    current_regime = new_regime
                    regime_start = current
        except Exception as e:
            pass
        
        current += timedelta(hours=1)
    
    print(f"Total regime changes: {len(legacy_transitions)}")
    print(f"Average regime duration: {np.mean([t['duration_hours'] for t in legacy_transitions if t['duration_hours'] > 0]):.1f} hours")
    
    # Show sample transitions
    print("\nSample transitions:")
    for t in legacy_transitions[:5]:
        print(f"  {t['timestamp']}: {t['from']} → {t['to']} (lasted {t['duration_hours']:.1f}h)")
    
    # 2. Modern System Analysis
    print("\n2. MODERN SYSTEM (60-second Updates):")
    modern_transitions = []
    modern_detector = ModernContinuousGMSDetector(config)
    modern_loader = ModernDataLoader(config)
    
    current_regime = None
    regime_start = None
    transition_count = 0
    
    # Process every 60 seconds
    current = start_date
    sample_transitions = []
    
    while current <= end_date:
        try:
            # Modern loads 1s data but we check regime every 60s
            data = modern_loader.load_data('BTC', current, current + timedelta(minutes=1))
            if data is not None and not data.empty:
                regime_result = modern_detector.compute_regime_live(data)
                new_regime = regime_result[0]  # (regime, confidence)
                
                if current_regime != new_regime:
                    if current_regime is not None:
                        duration = (current - regime_start).total_seconds() / 60
                        transition_data = {
                            'timestamp': current,
                            'from': current_regime,
                            'to': new_regime,
                            'duration_minutes': duration
                        }
                        modern_transitions.append(transition_data)
                        if len(sample_transitions) < 10:
                            sample_transitions.append(transition_data)
                        transition_count += 1
                    current_regime = new_regime
                    regime_start = current
        except Exception as e:
            pass
        
        current += timedelta(seconds=60)
        
        # Progress update every hour
        if current.minute == 0 and current.second == 0:
            hours_processed = (current - start_date).total_seconds() / 3600
            print(f"\r  Processing... {hours_processed:.0f}/744 hours, {transition_count} transitions found", end='')
    
    print(f"\n\nTotal regime changes: {len(modern_transitions)}")
    if modern_transitions:
        avg_duration = np.mean([t['duration_minutes'] for t in modern_transitions if t['duration_minutes'] > 0])
        print(f"Average regime duration: {avg_duration:.1f} minutes ({avg_duration/60:.1f} hours)")
    
    # Show sample transitions
    print("\nSample transitions:")
    for t in sample_transitions[:5]:
        print(f"  {t['timestamp']}: {t['from']} → {t['to']} (lasted {t['duration_minutes']:.1f}m)")
    
    # 3. COMPARISON
    print("\n" + "="*50)
    print("CRITICAL COMPARISON:")
    print(f"Legacy transitions: {len(legacy_transitions)}")
    print(f"Modern transitions: {len(modern_transitions)}")
    print(f"Ratio (Modern/Legacy): {len(modern_transitions)/max(1, len(legacy_transitions)):.1f}x")
    
    print("\n🚨 EXPECTED vs ACTUAL:")
    print(f"Expected ratio (60x more frequent): ~60x")
    print(f"Actual ratio: {len(modern_transitions)/max(1, len(legacy_transitions)):.1f}x")
    
    if len(modern_transitions) < len(legacy_transitions) * 10:
        print("\n❌ RED FLAG: Modern is NOT updating 60x more frequently!")
        print("This suggests Modern is using hourly data for regime detection")
        print("or there's a fundamental issue with the update mechanism.")
    
    # Save detailed results
    results = {
        'legacy': {
            'total_transitions': len(legacy_transitions),
            'transitions': legacy_transitions[:20]  # First 20
        },
        'modern': {
            'total_transitions': len(modern_transitions),
            'transitions': sample_transitions[:20]  # First 20
        },
        'comparison': {
            'ratio': len(modern_transitions)/max(1, len(legacy_transitions)),
            'expected_ratio': 60.0
        }
    }
    
    import json
    with open('regime_transition_analysis.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\nDetailed results saved to regime_transition_analysis.json")

if __name__ == "__main__":
    analyze_regime_transitions()