#!/usr/bin/env python3
"""
Debug signals flow to understand why detector gets NaN
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from datetime import datetime
import logging
import pandas as pd

logging.basicConfig(level=logging.DEBUG)

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_backtest_engine import RobustBacktestEngine

# Load config
config = load_config("configs/overrides/modern_system_v2_complete.yaml")
config.regime.detector_type = "enhanced"

# Create engine
engine = RobustBacktestEngine(
    config=config,
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 2),  # Just 1 day
    use_regime_cache=False
)

# Load data
print("\n=== Loading Data ===")
# Use the data loader directly
from hyperliquid_bot.modern.robust_data_loader import RobustDataLoader
loader = RobustDataLoader(config)
data = loader.load_data(datetime(2024, 1, 1), datetime(2024, 1, 2))
print(f"Data shape: {data.shape}")
print(f"Data columns: {list(data.columns)}")

# Check for NaN in key columns
print("\n=== NaN Check ===")
key_cols = ['atr_14_sec', 'atr_percent_sec', 'ma_slope', 'ma_slope_ema_30s', 
            'spread_mean', 'volume_imbalance', 'close']
for col in key_cols:
    if col in data.columns:
        nan_count = data[col].isna().sum()
        print(f"{col}: {nan_count} NaN values out of {len(data)}")
        if nan_count < len(data):
            print(f"  First non-NaN: {data[col].dropna().iloc[0] if not data[col].dropna().empty else 'None'}")

# Get first hour of data
print("\n=== First Hour Data ===")
if not data.empty:
    first_hour = data.iloc[0]
    for col in key_cols:
        if col in data.columns:
            print(f"{col}: {first_hour[col]}")

# Test signal engine
print("\n=== Testing Signal Engine ===")
from hyperliquid_bot.modern.signal_engine import ModernSignalEngine
signal_engine = ModernSignalEngine(config)

# Initialize with some data
if len(data) > 10:
    for i in range(10):
        hour_data = data.iloc[i]
        signals = signal_engine.calculate_signals(hour_data, data[:i+1])
        print(f"\nHour {i}: {hour_data.name}")
        for key in ['atr_14', 'atr_percent', 'ema_fast', 'ema_slow']:
            if key in signals:
                print(f"  {key}: {signals[key]}")

# Test detector directly
print("\n=== Testing Detector ===")
from hyperliquid_bot.modern.registry import modern_registry
detector_class = modern_registry.get_regime_detector("enhanced")
detector = detector_class(config)

# Create test signals
test_signals = {
    'atr_percent_sec': 0.01,
    'ma_slope': 0.0001,
    'obi_smoothed': 0.1,
    'spread_mean': 2.0,
    'spread_std': 1.0,
    'volume': 1000000
}
print(f"Test signals: {test_signals}")
regime = detector.detect_regime(test_signals)
print(f"Detected regime: {regime}")