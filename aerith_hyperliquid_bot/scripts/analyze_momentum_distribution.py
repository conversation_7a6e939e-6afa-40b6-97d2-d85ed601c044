#!/usr/bin/env python3
"""
Momentum Distribution Analysis for Bear Regime Detection
========================================================

Analyzes the momentum distribution in 2024 to understand why no bear regimes
are being detected with the current conservative thresholds (75.0/35.0).

This will help determine if we need to:
1. Lower the momentum thresholds to detect bear periods
2. Adjust the state mapping configuration
3. Fix the detection logic
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import logging

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class MomentumAnalyzer:
    """Analyzes momentum distribution to understand bear regime detection issues"""
    
    def __init__(self):
        self.project_root = project_root
        self.signals_df = None
        
    def load_latest_signals(self):
        """Load the latest signals data"""
        logs_dir = self.project_root.parent / "logs"
        signals_files = list(logs_dir.glob("backtest_signals_*.parquet"))
        
        if not signals_files:
            logger.error("No signals files found")
            return False
            
        latest_file = max(signals_files, key=lambda x: x.stat().st_mtime)
        logger.info(f"Loading signals from: {latest_file.name}")
        
        try:
            self.signals_df = pd.read_parquet(latest_file)
            logger.info(f"Loaded {len(self.signals_df)} signal samples")
            
            # Display available columns
            logger.info(f"Available columns: {list(self.signals_df.columns)}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading signals: {e}")
            return False
    
    def analyze_momentum_distribution(self):
        """Analyze momentum distribution to understand bear detection thresholds"""
        if self.signals_df is None:
            logger.error("No signals data available")
            return
            
        # Look for momentum-related columns
        momentum_cols = [col for col in self.signals_df.columns if 'slope' in col.lower() or 'momentum' in col.lower()]
        logger.info(f"Momentum-related columns: {momentum_cols}")
        
        if 'ma_slope' in self.signals_df.columns:
            momentum = self.signals_df['ma_slope']
            logger.info("Using 'ma_slope' for momentum analysis")
        elif momentum_cols:
            momentum = self.signals_df[momentum_cols[0]]
            logger.info(f"Using '{momentum_cols[0]}' for momentum analysis")
        else:
            logger.error("No momentum column found")
            return
            
        # Remove NaN values
        momentum_clean = momentum.dropna()
        logger.info(f"Clean momentum samples: {len(momentum_clean)}")
        
        # Calculate statistics
        stats = {
            'count': len(momentum_clean),
            'mean': momentum_clean.mean(),
            'std': momentum_clean.std(),
            'min': momentum_clean.min(),
            'max': momentum_clean.max(),
            'q1': momentum_clean.quantile(0.25),
            'median': momentum_clean.quantile(0.5),
            'q3': momentum_clean.quantile(0.75),
            'q5': momentum_clean.quantile(0.05),
            'q95': momentum_clean.quantile(0.95)
        }
        
        logger.info("Momentum Distribution Statistics:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value:.4f}")
        
        # Analyze negative momentum (potential bear signals)
        negative_momentum = momentum_clean[momentum_clean < 0]
        negative_pct = len(negative_momentum) / len(momentum_clean) * 100
        
        logger.info(f"Negative momentum samples: {len(negative_momentum)} ({negative_pct:.1f}%)")
        
        if len(negative_momentum) > 0:
            logger.info("Negative Momentum Statistics:")
            logger.info(f"  Mean: {negative_momentum.mean():.4f}")
            logger.info(f"  Min: {negative_momentum.min():.4f}")
            logger.info(f"  Q25: {negative_momentum.quantile(0.25):.4f}")
            logger.info(f"  Q75: {negative_momentum.quantile(0.75):.4f}")
        
        # Test different thresholds
        current_thresholds = {'strong': -75.0, 'weak': -35.0}  # Negative for bear
        
        logger.info("Testing current conservative thresholds for bear detection:")
        strong_bear_count = len(momentum_clean[momentum_clean <= current_thresholds['strong']])
        weak_bear_count = len(momentum_clean[momentum_clean <= current_thresholds['weak']])
        
        logger.info(f"  Strong bear signals (≤ -75.0): {strong_bear_count} ({strong_bear_count/len(momentum_clean)*100:.1f}%)")
        logger.info(f"  Weak bear signals (≤ -35.0): {weak_bear_count} ({weak_bear_count/len(momentum_clean)*100:.1f}%)")
        
        # Test more sensitive thresholds
        sensitive_thresholds = {'strong': -15.0, 'weak': -5.0}  # Original sensitive values
        
        logger.info("Testing original sensitive thresholds:")
        strong_bear_sens = len(momentum_clean[momentum_clean <= sensitive_thresholds['strong']])
        weak_bear_sens = len(momentum_clean[momentum_clean <= sensitive_thresholds['weak']])
        
        logger.info(f"  Strong bear signals (≤ -15.0): {strong_bear_sens} ({strong_bear_sens/len(momentum_clean)*100:.1f}%)")
        logger.info(f"  Weak bear signals (≤ -5.0): {weak_bear_sens} ({weak_bear_sens/len(momentum_clean)*100:.1f}%)")
        
        # Suggest optimal thresholds
        logger.info("Suggested thresholds for balanced regime detection:")
        
        # Target: ~10-20% bear regimes for balanced detection
        target_bear_pct = 15  # 15% bear regimes
        target_count = len(momentum_clean) * target_bear_pct / 100
        
        if len(negative_momentum) > 0:
            suggested_threshold = negative_momentum.quantile(1 - target_bear_pct/100)
            suggested_count = len(momentum_clean[momentum_clean <= suggested_threshold])
            
            logger.info(f"  Suggested threshold for ~{target_bear_pct}% bear: ≤ {suggested_threshold:.1f}")
            logger.info(f"  This would give: {suggested_count} samples ({suggested_count/len(momentum_clean)*100:.1f}%)")
        
        return stats, momentum_clean
    
    def create_momentum_distribution_chart(self, momentum_data):
        """Create momentum distribution visualization"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # Chart 1: Full distribution histogram
        ax1 = axes[0, 0]
        ax1.hist(momentum_data, bins=100, alpha=0.7, color='blue', edgecolor='black')
        ax1.axvline(0, color='red', linestyle='--', alpha=0.7, label='Zero Line')
        ax1.axvline(-75.0, color='orange', linestyle='--', alpha=0.7, label='Conservative Strong (-75.0)')
        ax1.axvline(-35.0, color='yellow', linestyle='--', alpha=0.7, label='Conservative Weak (-35.0)')
        ax1.axvline(-15.0, color='green', linestyle='--', alpha=0.7, label='Sensitive Strong (-15.0)')
        ax1.axvline(-5.0, color='purple', linestyle='--', alpha=0.7, label='Sensitive Weak (-5.0)')
        ax1.set_title('Momentum Distribution with Thresholds')
        ax1.set_xlabel('Momentum (ma_slope)')
        ax1.set_ylabel('Frequency')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Chart 2: Focus on negative momentum (bear region)
        ax2 = axes[0, 1]
        negative_momentum = momentum_data[momentum_data < 0]
        if len(negative_momentum) > 0:
            ax2.hist(negative_momentum, bins=50, alpha=0.7, color='red', edgecolor='black')
            ax2.axvline(-75.0, color='orange', linestyle='--', alpha=0.7, label='Conservative Strong (-75.0)')
            ax2.axvline(-35.0, color='yellow', linestyle='--', alpha=0.7, label='Conservative Weak (-35.0)')
            ax2.axvline(-15.0, color='green', linestyle='--', alpha=0.7, label='Sensitive Strong (-15.0)')
            ax2.axvline(-5.0, color='purple', linestyle='--', alpha=0.7, label='Sensitive Weak (-5.0)')
            ax2.set_title('Negative Momentum Distribution (Bear Region)')
            ax2.set_xlabel('Momentum (ma_slope)')
            ax2.set_ylabel('Frequency')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        
        # Chart 3: Cumulative distribution
        ax3 = axes[1, 0]
        sorted_momentum = np.sort(momentum_data)
        cumulative = np.arange(1, len(sorted_momentum) + 1) / len(sorted_momentum) * 100
        ax3.plot(sorted_momentum, cumulative, linewidth=2)
        ax3.axvline(0, color='red', linestyle='--', alpha=0.7, label='Zero Line')
        ax3.axvline(-75.0, color='orange', linestyle='--', alpha=0.7, label='Conservative Strong (-75.0)')
        ax3.axvline(-35.0, color='yellow', linestyle='--', alpha=0.7, label='Conservative Weak (-35.0)')
        ax3.axhline(15, color='green', linestyle=':', alpha=0.7, label='Target 15% Bear')
        ax3.set_title('Cumulative Distribution')
        ax3.set_xlabel('Momentum (ma_slope)')
        ax3.set_ylabel('Cumulative Percentage')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Chart 4: Box plot
        ax4 = axes[1, 1]
        ax4.boxplot([momentum_data], labels=['Momentum'])
        ax4.axhline(0, color='red', linestyle='--', alpha=0.7)
        ax4.axhline(-75.0, color='orange', linestyle='--', alpha=0.7)
        ax4.axhline(-35.0, color='yellow', linestyle='--', alpha=0.7)
        ax4.axhline(-15.0, color='green', linestyle='--', alpha=0.7)
        ax4.axhline(-5.0, color='purple', linestyle='--', alpha=0.7)
        ax4.set_title('Momentum Box Plot with Thresholds')
        ax4.set_ylabel('Momentum (ma_slope)')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save chart
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        chart_path = self.project_root / f"momentum_distribution_analysis_{timestamp}.png"
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        logger.info(f"Chart saved to: {chart_path}")
        
        plt.show()
    
    def generate_recommendations(self, stats, momentum_data):
        """Generate threshold adjustment recommendations"""
        logger.info("\n" + "="*80)
        logger.info("MOMENTUM ANALYSIS RECOMMENDATIONS")
        logger.info("="*80)
        
        negative_count = len(momentum_data[momentum_data < 0])
        negative_pct = negative_count / len(momentum_data) * 100
        
        # Current conservative thresholds analysis
        strong_bear_current = len(momentum_data[momentum_data <= -75.0])
        weak_bear_current = len(momentum_data[momentum_data <= -35.0])
        
        logger.info(f"\n📊 CURRENT SITUATION:")
        logger.info(f"   Negative momentum samples: {negative_count} ({negative_pct:.1f}%)")
        logger.info(f"   Strong bear signals (≤ -75.0): {strong_bear_current} ({strong_bear_current/len(momentum_data)*100:.1f}%)")
        logger.info(f"   Weak bear signals (≤ -35.0): {weak_bear_current} ({weak_bear_current/len(momentum_data)*100:.1f}%)")
        
        if strong_bear_current == 0:
            logger.info(f"\n🚨 PROBLEM IDENTIFIED:")
            logger.info(f"   Conservative thresholds (-75.0/-35.0) are TOO HIGH for 2024 data")
            logger.info(f"   No strong bear signals detected, explaining long-only behavior")
        
        # Calculate balanced thresholds
        if negative_count > 0:
            # Target 10-15% bear regimes for balanced trading
            target_bear_pct = 12
            target_strong_pct = 5
            
            # Find thresholds that would give target percentages
            suggested_weak = np.percentile(momentum_data, target_bear_pct)
            suggested_strong = np.percentile(momentum_data, target_strong_pct)
            
            logger.info(f"\n💡 SUGGESTED BALANCED THRESHOLDS:")
            logger.info(f"   Weak bear threshold: ≤ {suggested_weak:.1f} (target {target_bear_pct}% bear)")
            logger.info(f"   Strong bear threshold: ≤ {suggested_strong:.1f} (target {target_strong_pct}% strong bear)")
            
            # Test these thresholds
            test_weak_count = len(momentum_data[momentum_data <= suggested_weak])
            test_strong_count = len(momentum_data[momentum_data <= suggested_strong])
            
            logger.info(f"\n📈 EXPECTED RESULTS WITH SUGGESTED THRESHOLDS:")
            logger.info(f"   Weak bear signals: {test_weak_count} ({test_weak_count/len(momentum_data)*100:.1f}%)")
            logger.info(f"   Strong bear signals: {test_strong_count} ({test_strong_count/len(momentum_data)*100:.1f}%)")
        
        logger.info(f"\n🔧 RECOMMENDED ACTIONS:")
        logger.info(f"   1. Reduce momentum thresholds to enable bear detection")
        logger.info(f"   2. Update execution_refinement_enabled.yaml with balanced thresholds")
        logger.info(f"   3. Ensure Weak_Bear_Trend maps to BEAR (not CHOP) for modern system")
        logger.info(f"   4. Re-run backtest to verify both long and short trades")
        
        logger.info("="*80)
    
    def run_analysis(self):
        """Run the complete momentum analysis"""
        logger.info("Starting momentum distribution analysis...")
        
        if not self.load_latest_signals():
            return
            
        stats, momentum_data = self.analyze_momentum_distribution()
        if stats is None:
            return
            
        self.create_momentum_distribution_chart(momentum_data)
        self.generate_recommendations(stats, momentum_data)
        
        logger.info("Momentum analysis complete!")

def main():
    """Main analysis function"""
    analyzer = MomentumAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()