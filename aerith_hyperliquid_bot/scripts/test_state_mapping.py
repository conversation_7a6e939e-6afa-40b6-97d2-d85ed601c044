#!/usr/bin/env python3
# Test script for verifying state_mapping utility functionality

import sys
import os
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_state_mapping")

# Add project root to path for imports
project_root = Path(os.path.abspath(os.path.dirname(__file__))).parent
sys.path.insert(0, str(project_root))

# Import the state mapping utility
from hyperliquid_bot.utils.state_mapping import (
    get_state_map, 
    map_gms_state, 
    get_states_by_category,
    get_bull_states,
    get_bear_states,
    get_chop_states
)

def run_state_mapping_tests():
    """Run tests to verify state mapping functionality."""
    logger.info("Testing state mapping utility...")
    
    # 1. Test map loading
    logger.info("1. Loading state map...")
    state_map = get_state_map()
    logger.info(f"Loaded state map with {len(state_map)} entries")
    
    # 2. Test individual state mapping
    test_states = [
        "Strong_Bull_Trend", 
        "Weak_Bull_Trend",
        "Strong_Bear_Trend", 
        "Weak_Bear_Trend",  # This should map to CHOP not BEAR!
        "High_Vol_Range",
        "Low_Vol_Range",
        "Uncertain",
        "TIGHT_SPREAD",
        "Unknown_State"  # Test fallback behavior
    ]
    
    logger.info("2. Testing individual state mapping...")
    for state in test_states:
        mapped = map_gms_state(state)
        logger.info(f"  • {state} -> {mapped}")
    
    # 3. Test categorized states
    logger.info("3. Testing state categorization...")
    states_by_category = get_states_by_category()
    for category, states in states_by_category.items():
        logger.info(f"  • {category} states: {states}")
    
    # 4. Test convenience functions
    logger.info("4. Testing convenience functions...")
    logger.info(f"  • Bull states: {get_bull_states()}")
    logger.info(f"  • Bear states: {get_bear_states()}")
    logger.info(f"  • Chop states: {get_chop_states()}")
    
    logger.info("All tests completed!")

if __name__ == "__main__":
    run_state_mapping_tests()
