#!/usr/bin/env python3
"""
Test the complete regime flow in backtest engine
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
import pandas as pd

from hyperliquid_bot.modern.regime_cache import RegimeCache
from hyperliquid_bot.modern.regime_state_manager import RegimeStateManager

# Test dates
test_date = datetime(2024, 4, 2, 0, 0, 0)

print("=== TESTING REGIME FLOW ===")

# 1. Test cache directly
print("\n1. Testing RegimeCache directly:")
cache = RegimeCache()
cache.load_year(2024)
result = cache.get_regime_at_time(test_date)
print(f"Cache result for {test_date}: {result}")

# 2. Test regime manager
print("\n2. Testing RegimeStateManager:")
manager = RegimeStateManager(mode='backtest')
print(f"Initial state: {manager.get_current_state()}")

# Update with cache data
if result:
    manager.update_state(
        timestamp=test_date,
        state=result['regime'],
        confidence=result['confidence'],
        features=result
    )
    print(f"After update: {manager.get_current_state()}")

# 3. Test get_regime_features_for_strategy
print("\n3. Testing get_regime_features_for_strategy:")
features = manager.get_regime_features_for_strategy(
    timestamp=test_date,
    lookback_hours=1
)
print(f"Features: {features}")
print(f"Current state from features: {features.get('current_state')}")

# 4. Check the data in cache
print("\n4. Checking cache data:")
df = cache.regime_data[2024]
# Find entries around test date
mask = (df.index >= test_date - pd.Timedelta(hours=2)) & (df.index <= test_date + pd.Timedelta(hours=2))
print(f"Entries around {test_date}:")
print(df[mask])