#!/usr/bin/env python3
"""
Test script for the modern trading system.
This validates that all components are properly registered and working.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import logging
from datetime import datetime, timedelta

# Import modern system
import hyperliquid_bot.modern  # Trigger component registration
from hyperliquid_bot.systems import ModernTradingSystem


def test_modern_system():
    """Test the modern trading system components."""
    print("=" * 80)
    print("Testing Modern Trading System")
    print("=" * 80)
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )
    
    try:
        # Load configuration with modern override
        print("\n1. Loading configuration...")
        # Load config using the same method as backtester
        import yaml
        from deepmerge import always_merger
        
        base_config_path = project_root / "configs" / "base.yaml"
        override_config_path = project_root / "configs" / "overrides" / "modern_system.yaml"
        
        # Load base config
        with open(base_config_path, 'r') as f:
            base_cfg = yaml.safe_load(f)
        
        # Load override config
        with open(override_config_path, 'r') as f:
            override_cfg = yaml.safe_load(f)
        
        # Merge configs
        merged_cfg = always_merger.merge(base_cfg, override_cfg)
        
        # Create config object
        from hyperliquid_bot.config.settings import Config
        config = Config(**merged_cfg)
        print(f"   ✓ Configuration loaded successfully")
        print(f"   - System mode: {merged_cfg.get('system_mode', 'unknown')}")
        print(f"   - Detector type: {config.regime.detector_type}")
        print(f"   - Strategy: TF-v3 = {config.strategies.use_tf_v3}")
        
        # Validate critical settings
        print("\n2. Validating modern system configuration...")
        issues = []
        
        # Check thresholds
        if config.regime.gms_vol_high_thresh != 0.015:
            issues.append(f"Vol high threshold: {config.regime.gms_vol_high_thresh} != 0.015")
        if config.regime.gms_vol_low_thresh != 0.005:
            issues.append(f"Vol low threshold: {config.regime.gms_vol_low_thresh} != 0.005")
        if config.regime.gms_mom_strong_thresh != 2.5:
            issues.append(f"Mom strong threshold: {config.regime.gms_mom_strong_thresh} != 2.5")
        if config.regime.gms_mom_weak_thresh != 0.5:
            issues.append(f"Mom weak threshold: {config.regime.gms_mom_weak_thresh} != 0.5")
        
        # Check risk fraction
        if config.tf_v3.risk_frac != 0.02:
            issues.append(f"Risk fraction: {config.tf_v3.risk_frac} != 0.02")
        
        if issues:
            print("   ⚠️  Configuration issues found:")
            for issue in issues:
                print(f"      - {issue}")
        else:
            print("   ✓ Configuration validated successfully")
        
        # Initialize system
        print("\n3. Initializing modern trading system...")
        system = ModernTradingSystem(config)
        print("   ✓ System initialized successfully")
        
        # Test regime detector
        print("\n4. Testing regime detector...")
        test_signals = {
            'timestamp': datetime.now(),
            'close': 50000.0,
            'atr_percent_sec': 0.02,  # 2% volatility
            'ma_slope_ema_30s': 1.5,  # Moderate momentum
            'obi_smoothed_10': 0.1,   # Positive OBI
            'spread_mean': 0.0002,
            'spread_std': 0.0003,
            'adx': 35.0,
            'funding_rate': 0.0005,
            'unrealised_pnl': 0.0,
        }
        
        regime_state = system.regime_detector.get_raw_state(test_signals)
        print(f"   ✓ Regime detection working")
        print(f"   - Raw state: {regime_state}")
        
        # Test data loader
        print("\n5. Testing data loader...")
        start_date = datetime.now() - timedelta(days=7)
        end_date = datetime.now()
        
        try:
            data = system.data_loader.load_data(start_date, end_date)
            if not data.empty:
                print(f"   ✓ Data loader working")
                print(f"   - Loaded {len(data)} rows")
                print(f"   - Columns: {list(data.columns)[:5]}...")
            else:
                print(f"   ⚠️  No data found for date range")
        except Exception as e:
            print(f"   ⚠️  Data loader error: {e}")
        
        # Test full signal processing
        print("\n6. Testing full signal processing...")
        result = system.process_signals(test_signals)
        print(f"   ✓ Signal processing complete")
        print(f"   - Regime: {result['regime']} (raw: {result['regime_raw']})")
        print(f"   - Risk suppressed: {result['risk_suppressed']}")
        print(f"   - Action: {result['action']}")
        print(f"   - Direction: {result['direction']}")
        
        # Analyze regime failures
        print("\n7. Analyzing regime gate failures...")
        analysis = system.analyze_regime_failures()
        print(f"   - Regime distribution: {analysis.get('regime_distribution', {})}")
        if 'issue' in analysis:
            print(f"   ⚠️  Issue detected: {analysis['issue']}")
            print(f"   - Likely cause: {analysis.get('likely_cause', 'Unknown')}")
        
        # Get diagnostics
        print("\n8. System diagnostics:")
        diagnostics = system.get_diagnostics()
        print(f"   - Current regime: {diagnostics['current_regime']}")
        if 'strategy_stats' in diagnostics:
            stats = diagnostics['strategy_stats']
            print(f"   - Strategy evaluations: {stats.get('evaluations', 0)}")
            print(f"   - Regime gates failed: {stats.get('regime_gates_failed', 0)}")
            print(f"   - Successful signals: L={stats.get('successful_longs', 0)}, S={stats.get('successful_shorts', 0)}")
        
        print("\n" + "=" * 80)
        print("✓ Modern system test completed successfully")
        print("=" * 80)
        
        # Print potential issues
        print("\nKNOWN ISSUES:")
        print("- 100% regime gate failures in backtesting")
        print("- Needs investigation of regime mapping logic")
        print("- May need threshold calibration")
        
    except Exception as e:
        print(f"\n❌ Error testing modern system: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    test_modern_system()