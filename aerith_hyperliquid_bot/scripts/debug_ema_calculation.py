#!/usr/bin/env python3
"""
Debug why EMAs are showing values around 94,000 instead of Bitcoin's ~42,000.
Check what data is being fed to the EMA calculation.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from datetime import datetime
import pandas as pd
import logging

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.data_loader import ModernDataLoader
from hyperliquid_bot.modern.signal_engine import ModernSignalEngine
from hyperliquid_bot.modern.enhanced_regime_detector import EnhancedRegimeDetector

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    print("\n" + "="*80)
    print("DEBUGGING EMA CALCULATION - WHY ARE VALUES ~94,000?")
    print("="*80)
    
    # Load config
    config_path = Path(__file__).parent.parent / "configs/overrides/modern_system_v2_complete.yaml"
    config = load_config(str(config_path))
    
    # Create data loader
    data_loader = ModernDataLoader(config)
    signal_engine = ModernSignalEngine(config)
    
    # Load a sample hour from 2024
    test_date = datetime(2024, 1, 15, 12, 0)  # Mid-January 2024
    
    print(f"\nTesting hour: {test_date}")
    
    # 1. Check what data is being loaded for the full range
    print("\n1. Loading data range for analysis...")
    # Skip checking 1s data for now, focus on OHLCV that feeds EMAs
    
    # 2. Check OHLCV data that gets loaded
    print("\n2. Loading OHLCV data for signal calculation...")
    # Get sufficient history for EMA calculation
    lookback_hours = signal_engine.calculate_required_lookback()
    print(f"   Required lookback: {lookback_hours} hours")
    
    # Load OHLCV data using the correct method
    # Modern data loader loads 1s data, but we need hourly OHLCV
    # Let's use the _load_ohlcv_data method directly
    ohlcv_data = data_loader._load_ohlcv_data(
        start_date=test_date - pd.Timedelta(hours=lookback_hours),
        end_date=test_date
    )
    
    if ohlcv_data is not None and not ohlcv_data.empty:
        print(f"   OHLCV shape: {ohlcv_data.shape}")
        print(f"   OHLCV columns: {ohlcv_data.columns.tolist()}")
        print(f"   Close prices - min: {ohlcv_data['close'].min():.2f}, max: {ohlcv_data['close'].max():.2f}, mean: {ohlcv_data['close'].mean():.2f}")
        print(f"\n   Last 5 close prices:")
        print(ohlcv_data['close'].tail())
        
        # 3. Calculate EMAs manually
        print("\n3. Calculating EMAs manually...")
        ema_20 = ohlcv_data['close'].ewm(span=20, adjust=False).mean()
        ema_50 = ohlcv_data['close'].ewm(span=50, adjust=False).mean()
        
        print(f"   EMA 20 - last value: {ema_20.iloc[-1]:.2f}")
        print(f"   EMA 50 - last value: {ema_50.iloc[-1]:.2f}")
        print(f"   Forecast (EMA20 - EMA50): {ema_20.iloc[-1] - ema_50.iloc[-1]:.2f}")
        
        # 4. Check what signal engine calculates
        print("\n4. Testing signal engine calculation...")
        regime_features = {
            'current_state': 'Strong_Bull_Trend',
            'current_confidence': 0.8,
            'state_duration_minutes': 45,
            'risk_suppressed': False
        }
        
        signals = signal_engine.calculate_signals(ohlcv_data, regime_features)
        
        print(f"\n   Signal Engine Results:")
        print(f"   Close: {signals.get('close', 'N/A')}")
        print(f"   EMA Fast: {signals.get('ema_fast', 'N/A')}")
        print(f"   EMA Slow: {signals.get('ema_slow', 'N/A')}")
        print(f"   Forecast: {signals.get('forecast', 'N/A')}")
        
    # 5. Check if there's a data transformation issue
    print("\n5. Checking for data transformation issues...")
    # Look at the actual file being loaded
    ohlcv_path = Path(config.data_paths.ohlcv_base_path) / "1h"
    sample_files = list(ohlcv_path.glob("2024-01-15_*.parquet"))[:3]
    
    if sample_files:
        print(f"\n   Checking raw OHLCV files:")
        for file in sample_files:
            df = pd.read_parquet(file)
            print(f"   {file.name}: close range {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    # 6. Summary diagnosis
    print("\n" + "="*80)
    print("DIAGNOSIS SUMMARY")
    print("="*80)
    
    if ohlcv_data is not None and not ohlcv_data.empty:
        actual_price = ohlcv_data['close'].iloc[-1]
        if actual_price > 80000:  # If price seems too high
            print("\n⚠️  PROBLEM IDENTIFIED: Prices are much higher than expected!")
            print("   This suggests:")
            print("   1. Wrong data file being loaded (possibly wrong asset)")
            print("   2. Data transformation issue (multiplied by factor)")
            print("   3. Wrong column being used (maybe using volume as price?)")
        else:
            print("\n✅ Prices look reasonable for Bitcoin in 2024")
            print("   The issue might be in the backtest engine's data loading")

if __name__ == "__main__":
    main()