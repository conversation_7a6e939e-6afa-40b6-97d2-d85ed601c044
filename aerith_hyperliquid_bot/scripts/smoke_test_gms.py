#!/usr/bin/env python
# scripts/smoke_test_gms.py

"""
Smoke test for Continuous GMS detector with 1-hour ATR.

This script loads 1-second feature data for a specific date and tests the
Continuous GMS detector to ensure it can detect regimes using the 1-hour ATR.
"""

import os
import sys
import logging
import argparse
from datetime import datetime
from pathlib import Path
import pandas as pd
import numpy as np

# Add project root to path for imports
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.core.gms_detector import ContinuousGMSDetector

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("smoke_test_gms")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Smoke test for Continuous GMS detector with 1-hour ATR")

    parser.add_argument(
        "--date",
        type=str,
        default="2025-03-03",
        help="Date to test in YYYY-MM-DD format (default: 2025-03-03)"
    )

    parser.add_argument(
        "--hour",
        type=int,
        default=12,
        help="Hour to test (0-23, default: 12)"
    )

    return parser.parse_args()

def load_feature_data(date_str, hour, config):
    """
    Load 1-second feature data for a specific date and hour.

    Args:
        date_str: Date string in YYYY-MM-DD format
        hour: Hour (0-23)
        config: Configuration object

    Returns:
        DataFrame with 1-second feature data
    """
    date = datetime.strptime(date_str, "%Y-%m-%d")
    date_pattern = date.strftime("%Y%m%d")

    # Feature files are in a subdirectory with the date
    feature_dir = Path(config.data_paths.feature_1s_dir) / date_str
    feature_file = feature_dir / f"features_{hour:02d}.parquet"

    if not feature_file.exists():
        logger.error(f"Feature file {feature_file} does not exist")
        return None

    logger.info(f"Loading feature data from {feature_file}")
    df = pd.read_parquet(feature_file)

    return df

def prepare_signals(df, config):
    """
    Prepare signals for the GMS detector.

    Args:
        df: DataFrame with 1-second feature data
        config: Configuration object

    Returns:
        DataFrame with signals
    """
    logger.info("Preparing signals for GMS detector")

    # Set timestamp as index
    if 'timestamp' in df.columns:
        df = df.set_index('timestamp')

    # Calculate OBI smoothed
    depth = config.microstructure.depth_levels
    obi_col = f'raw_obi_{depth}'

    if obi_col not in df.columns:
        logger.error(f"OBI column {obi_col} not found in feature data")
        return None

    # Calculate OBI smoothed using EMA
    window = config.microstructure.obi_smoothing_window
    df[f'obi_smoothed_{depth}'] = df[obi_col].ewm(span=window).mean()

    # Calculate MA slope
    df['ma_slope'] = df['close'].diff(10) / df['close'].shift(10) * 100

    # Calculate spread mean and std
    df['spread_mean'] = df['spread'].rolling(window=60).mean()
    df['spread_std'] = df['spread'].rolling(window=60).std()

    # Reset index to get timestamp as a column
    df = df.reset_index()

    return df

def test_gms_detector(df, config):
    """
    Test the Continuous GMS detector.

    Args:
        df: DataFrame with signals
        config: Configuration object

    Returns:
        Dictionary with regime counts
    """
    logger.info("Testing Continuous GMS detector")

    # Initialize GMS detector
    gms = ContinuousGMSDetector(config)

    # Process each row and track regime counts
    counts = {}
    risk_suppressed_count = 0

    for _, row in df.iterrows():
        # Convert row to dict
        signals = row.to_dict()

        # Get regime
        result = gms.get_regime(signals)

        # Handle different return types
        if isinstance(result, dict):
            state = result['state']
            risk_suppressed = result['risk_suppressed']
            if risk_suppressed:
                risk_suppressed_count += 1
        else:
            state = result

        # Count regimes
        if state not in counts:
            counts[state] = 0
        counts[state] += 1

    # Log counts
    logger.info("Regime counts:")
    for state, count in sorted(counts.items()):
        logger.info(f"  {state}: {count}")

    logger.info(f"Risk suppressed count: {risk_suppressed_count}")

    return counts

def main():
    """Main entry point."""
    args = parse_args()

    # Load config
    config = load_config()

    # Load feature data
    df = load_feature_data(args.date, args.hour, config)
    if df is None:
        return 1

    # Check if ATR columns exist
    if 'atr_14_sec' not in df.columns:
        logger.error("ATR column 'atr_14_sec' not found in feature data")
        return 1

    logger.info(f"Loaded {len(df)} rows of feature data")
    logger.info(f"ATR column 'atr_14_sec' exists: {df['atr_14_sec'].notna().sum()} non-NaN values")

    # Prepare signals
    df = prepare_signals(df, config)
    if df is None:
        return 1

    # Test GMS detector
    counts = test_gms_detector(df, config)

    # Check if any regimes were detected
    if not counts or all(count == 0 for count in counts.values()):
        logger.error("No regimes detected")
        return 1

    logger.info("Smoke test passed")
    return 0

if __name__ == "__main__":
    sys.exit(main())
