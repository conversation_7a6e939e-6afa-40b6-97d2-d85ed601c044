#!/usr/bin/env python3
"""
Debug Strategy Conditions
=========================

Deep dive into why strategy isn't generating trades.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from datetime import datetime
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.tf_v3_modern import ModernTFV3Strategy

def test_regime_stability():
    """Test the _is_regime_stable method with various inputs."""
    print("=== Testing Regime Stability Checks ===\n")
    
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    strategy = ModernTFV3Strategy(config)
    
    print(f"Strategy settings:")
    print(f"  min_regime_duration_minutes: {strategy.min_regime_duration_minutes}")
    print(f"  max_regime_changes_1h: {strategy.max_regime_changes_1h}")
    print(f"  min_regime_confidence: {strategy.min_regime_confidence}")
    
    test_cases = [
        {
            'name': 'Perfect stability',
            'regime_features': {
                'state_duration_minutes': 30,
                'state_changes_1h': 2,
                'current_confidence': 0.8
            }
        },
        {
            'name': 'Too short duration',
            'regime_features': {
                'state_duration_minutes': 5,
                'state_changes_1h': 2,
                'current_confidence': 0.8
            }
        },
        {
            'name': 'Too many changes',
            'regime_features': {
                'state_duration_minutes': 30,
                'state_changes_1h': 10,
                'current_confidence': 0.8
            }
        },
        {
            'name': 'Low confidence',
            'regime_features': {
                'state_duration_minutes': 30,
                'state_changes_1h': 2,
                'current_confidence': 0.4
            }
        },
        {
            'name': 'Real data from backtest',
            'regime_features': {
                'state_duration_minutes': 60,  # Seems like this might be the issue
                'state_changes_1h': 0,
                'current_confidence': 0.67
            }
        }
    ]
    
    for case in test_cases:
        stable = strategy._is_regime_stable(case['regime_features'])
        print(f"\n{case['name']}:")
        print(f"  Duration: {case['regime_features']['state_duration_minutes']} min")
        print(f"  Changes: {case['regime_features']['state_changes_1h']}")
        print(f"  Confidence: {case['regime_features']['current_confidence']}")
        print(f"  Result: {'✅ STABLE' if stable else '❌ NOT STABLE'}")

def test_ema_conditions():
    """Test EMA crossover conditions."""
    print("\n\n=== Testing EMA Crossover Conditions ===\n")
    
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    strategy = ModernTFV3Strategy(config)
    
    # Get EMA settings
    print(f"EMA settings:")
    print(f"  Fast: {config.tf_v3.ema_fast}")
    print(f"  Slow: {config.tf_v3.ema_slow}")
    print(f"  Baseline: {getattr(config.tf_v3, 'baseline_period', 50)}")
    
    test_cases = [
        {
            'name': 'Clear long signal',
            'ema_fast': 42100,
            'ema_slow': 41900,
            'ema_baseline': 41800,
            'close': 42000,
            'regime': 'Weak_Bull_Trend'
        },
        {
            'name': 'Clear short signal',
            'ema_fast': 41900,
            'ema_slow': 42100,
            'ema_baseline': 42200,
            'close': 42000,
            'regime': 'Weak_Bear_Trend'
        },
        {
            'name': 'Real backtest values',
            'ema_fast': 42697.89,
            'ema_slow': 43142.38,
            'ema_baseline': 43000,  # Estimate
            'close': 42316.00,
            'regime': 'Weak_Bear_Trend'
        }
    ]
    
    for case in test_cases:
        print(f"\n{case['name']}:")
        print(f"  Close: {case['close']}")
        print(f"  EMA Fast: {case['ema_fast']} {'>' if case['ema_fast'] > case['ema_slow'] else '<'} EMA Slow: {case['ema_slow']}")
        print(f"  Close: {case['close']} {'>' if case['close'] > case['ema_baseline'] else '<'} Baseline: {case['ema_baseline']}")
        print(f"  Regime: {case['regime']}")
        
        # Check conditions
        bullish_cross = case['ema_fast'] > case['ema_slow'] and case['close'] > case['ema_baseline']
        bearish_cross = case['ema_fast'] < case['ema_slow'] and case['close'] < case['ema_baseline']
        
        print(f"  Bullish conditions: {bullish_cross}")
        print(f"  Bearish conditions: {bearish_cross}")
        
        if bullish_cross and case['regime'] in ['Weak_Bull_Trend', 'Strong_Bull_Trend']:
            print(f"  Signal: ✅ LONG")
        elif bearish_cross and case['regime'] in ['Weak_Bear_Trend', 'Strong_Bear_Trend']:
            print(f"  Signal: ✅ SHORT")
        else:
            print(f"  Signal: ❌ NO ENTRY")

def analyze_regime_features():
    """Analyze what regime features look like."""
    print("\n\n=== Analyzing Regime Features ===\n")
    
    # From our trace output
    sample_features = {
        'current_state': 'Weak_Bear_Trend',
        'current_confidence': 0.67,
        'current_momentum': -0.001,
        'recent_bullish_pct': 0.0,
        'recent_bearish_pct': 1.0,
        'momentum_trend': 'decreasing',
        'avg_momentum': -0.0005,
        'momentum_volatility': 0.0002,
        'avg_volatility': 0.005,
        'max_volatility': 0.006,
        'avg_volume_imbalance': -0.05,
        'recent_transitions': 1,
        'is_trending': True,
        'state_persistence': 0.8
    }
    
    print("Sample regime features from backtest:")
    for key, value in sample_features.items():
        print(f"  {key}: {value}")
    
    print("\nPotential issues:")
    print("1. 'state_duration_minutes' might not be in regime_features")
    print("2. 'state_changes_1h' might not be in regime_features")
    print("3. These values might need to be calculated from other fields")

def main():
    print("=== Debug Strategy Conditions ===\n")
    
    test_regime_stability()
    test_ema_conditions()
    analyze_regime_features()
    
    print("\n" + "="*50)
    print("\nCONCLUSIONS:")
    print("1. Check if regime_features contains the expected fields")
    print("2. The regime might be stable but EMA conditions not met")
    print("3. In the real data, EMA fast < slow and close < baseline")
    print("   This would be a SHORT signal, but only if regime is bearish")

if __name__ == "__main__":
    main()