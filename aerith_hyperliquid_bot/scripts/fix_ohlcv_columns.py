#!/usr/bin/env python3
"""
Script to add log_ret and realised_vol columns to existing OHLCV Parquet files.
"""

import pandas as pd
import numpy as np
import pathlib
import logging
import sys
from tqdm import tqdm

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define base directories
RESAMPLED_DIR = pathlib.Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled_l2")

def process_file(file_path):
    """Process a single OHLCV file to add log_ret and realised_vol columns."""
    try:
        logger.info(f"Processing file: {file_path}")
        
        # Determine timeframe from file path
        file_path_str = str(file_path)
        if "1h" in file_path_str:
            tf = "1h"
            window = 24  # 24-period window for 1h timeframe
        elif "4h" in file_path_str:
            tf = "4h"
            window = 6   # 6-period window for 4h timeframe
        else:
            logger.error(f"Could not determine timeframe from file path: {file_path}")
            return False
        
        # Read the existing file
        df = pd.read_parquet(file_path)
        logger.info(f"Original columns: {df.columns.tolist()}")
        
        # Set timestamp as index if it's not already
        if 'timestamp' in df.columns:
            df = df.set_index('timestamp')
        
        # Calculate log returns
        logger.info(f"Calculating log returns for {len(df)} rows")
        df['log_ret'] = np.log(df['close'] / df['close'].shift(1))
        logger.info(f"Log returns calculation complete. NaN count: {df['log_ret'].isna().sum()} / {len(df)}")
        
        # Calculate realized volatility with timeframe-specific window
        min_p = max(1, window // 2)  # Ensure min_periods makes sense
        logger.info(f"Calculating realized volatility for {tf} with window={window}, min_periods={min_p}")
        df['realised_vol'] = df['log_ret'].rolling(window=window, min_periods=min_p).std()
        logger.info(f"Realized volatility calculation complete. NaN count: {df['realised_vol'].isna().sum()} / {len(df)}")
        
        # Reset index to convert timestamp back to a column
        df = df.reset_index()
        
        # Ensure columns are in the right order
        columns = ['timestamp', 'open', 'high', 'low', 'close', 'log_ret', 'realised_vol']
        for col in columns:
            if col not in df.columns:
                logger.error(f"Column {col} missing!")
                return False
        
        df = df[columns]
        logger.info(f"Final columns: {df.columns.tolist()}")
        
        # Save the updated file
        df.to_parquet(file_path, index=False)
        logger.info(f"Successfully saved updated file to {file_path}")
        
        # Verify the saved file
        check_df = pd.read_parquet(file_path)
        logger.info(f"Columns in saved file: {check_df.columns.tolist()}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error processing file {file_path}: {e}", exc_info=True)
        return False

def main():
    """Process all OHLCV files in the resampled directory."""
    # Process files for both timeframes
    timeframes = ["1h", "4h"]
    
    # Process a specific file for testing if date is provided
    if len(sys.argv) > 1:
        date_str = sys.argv[1]
        timeframe = sys.argv[2] if len(sys.argv) > 2 else "1h"
        
        if timeframe not in timeframes:
            logger.error(f"Invalid timeframe: {timeframe}. Must be one of {timeframes}")
            return
            
        test_file = RESAMPLED_DIR / timeframe / f"{date_str}_{timeframe}.parquet"
        if test_file.exists():
            logger.info(f"Processing single file: {test_file}")
            process_file(test_file)
            return
        else:
            logger.error(f"Test file not found: {test_file}")
            return
    
    # Process all files for each timeframe
    for timeframe in timeframes:
        tf_dir = RESAMPLED_DIR / timeframe
        
        if not tf_dir.exists():
            logger.error(f"Timeframe directory not found: {tf_dir}")
            continue
        
        files = list(tf_dir.glob("*.parquet"))
        logger.info(f"Found {len(files)} files in {tf_dir}")
        
        if len(files) == 0:
            logger.warning(f"No Parquet files found in {tf_dir}")
            continue
        
        success_count = 0
        with tqdm(total=len(files), desc=f"Processing {timeframe} files") as pbar:
            for file_path in files:
                if process_file(file_path):
                    success_count += 1
                pbar.update(1)
        
        logger.info(f"Successfully processed {success_count} out of {len(files)} files for {timeframe}")

if __name__ == "__main__":
    main()
