#!/usr/bin/env python3
"""
Check for specific hidden features in Legacy system:
1. Order book feature usage beyond basic OBI
2. Position management limits
3. Time-based trading filters
4. Exit logic implementation
5. Special data fields like 'imbalance'
"""

import yaml
import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_legacy_configuration():
    print("=== LEGACY SYSTEM HIDDEN FEATURES CHECK ===\n")
    
    # Load base config
    with open('config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    print("1. CONFIGURATION ANALYSIS:")
    print("-" * 50)
    
    # Check position limits
    print("\n📊 Position Management:")
    max_positions = config.get('max_concurrent_positions', 'NOT SET')
    print(f"   - Max concurrent positions: {max_positions}")
    
    position_sizing = config.get('position_sizing_method', 'NOT SET')
    print(f"   - Position sizing method: {position_sizing}")
    
    risk_per_trade = config.get('risk_per_trade', 'NOT SET')
    print(f"   - Risk per trade: {risk_per_trade}")
    
    # Check time filters
    print("\n⏰ Time-based Filters:")
    trading_hours = config.get('trading_hours', 'NOT SET')
    print(f"   - Trading hours restriction: {trading_hours}")
    
    avoid_funding = config.get('avoid_funding_hours', False)
    print(f"   - Avoid funding hours: {avoid_funding}")
    
    # Check data sources
    print("\n📈 Data Sources:")
    use_order_book = config.get('use_order_book_features', False)
    print(f"   - Use order book features: {use_order_book}")
    
    order_book_levels = config.get('order_book_levels', 5)
    print(f"   - Order book levels used: {order_book_levels}")
    
    # Check exit logic
    print("\n🚪 Exit Logic:")
    stop_loss = config.get('stop_loss_pct', 'NOT SET')
    print(f"   - Stop loss %: {stop_loss}")
    
    take_profit = config.get('take_profit_pct', 'NOT SET')
    print(f"   - Take profit %: {take_profit}")
    
    trailing_stop = config.get('use_trailing_stop', False)
    print(f"   - Use trailing stop: {trailing_stop}")
    
    time_based_exit = config.get('max_position_hours', 'NOT SET')
    print(f"   - Time-based exit (hours): {time_based_exit}")

def analyze_data_differences():
    print("\n\n2. DATA STRUCTURE ANALYSIS:")
    print("-" * 50)
    
    # Load sample data from both systems
    print("\n📁 Legacy Data (raw2/):")
    try:
        # Check a sample legacy file
        legacy_sample = "/Users/<USER>/hyperliquid_data/data/raw2/BTC/2024/01/BTC_2024_01_01_00.parquet"
        if os.path.exists(legacy_sample):
            df_legacy = pd.read_parquet(legacy_sample)
            print(f"   - Columns: {len(df_legacy.columns)}")
            print(f"   - Special fields: {[col for col in df_legacy.columns if 'imbalance' in col or 'book' in col]}")
            
            # Check for 'imbalance' field specifically
            if 'imbalance' in df_legacy.columns:
                print(f"   ⚠️  FOUND 'imbalance' field! Range: [{df_legacy['imbalance'].min():.4f}, {df_legacy['imbalance'].max():.4f}]")
        else:
            print("   - Sample file not found")
    except Exception as e:
        print(f"   - Error loading legacy data: {e}")
    
    print("\n📁 Modern Data (features_1s/):")
    try:
        # Check a sample modern file
        modern_sample = "/Users/<USER>/hyperliquid_data/data/features_1s/BTC/2024/01/BTC_2024_01_01.parquet"
        if os.path.exists(modern_sample):
            df_modern = pd.read_parquet(modern_sample)
            print(f"   - Columns: {len(df_modern.columns)}")
            print(f"   - Has 'imbalance' field: {'imbalance' in df_modern.columns}")
            
            # Check order book columns
            book_cols = [col for col in df_modern.columns if 'bid' in col or 'ask' in col]
            print(f"   - Order book columns: {len(book_cols)} (up to level {max([int(c.split('_')[-1]) for c in book_cols if c.split('_')[-1].isdigit()])})")
        else:
            print("   - Sample file not found")
    except Exception as e:
        print(f"   - Error loading modern data: {e}")

def check_strategy_differences():
    print("\n\n3. STRATEGY IMPLEMENTATION CHECK:")
    print("-" * 50)
    
    # Check TF_V3 strategy files
    print("\n🎯 Legacy TF_V3 Strategy:")
    legacy_strategy_path = "hyperliquid_bot/strategies/tf_v3.py"
    if os.path.exists(legacy_strategy_path):
        with open(legacy_strategy_path, 'r') as f:
            legacy_code = f.read()
            
        # Check for specific patterns
        checks = {
            'imbalance_usage': 'imbalance' in legacy_code,
            'position_limit': 'max_positions' in legacy_code or 'concurrent_positions' in legacy_code,
            'time_filter': 'hour' in legacy_code or 'trading_hours' in legacy_code,
            'order_book_depth': any(f'level_{i}' in legacy_code or f'bid_sz_{i}' in legacy_code for i in range(6, 21)),
            'special_exit': 'exit_signal' in legacy_code or 'should_exit' in legacy_code
        }
        
        for check, found in checks.items():
            print(f"   - {check}: {'✅ FOUND' if found else '❌ NOT FOUND'}")
    
    print("\n🎯 Modern TF_V3 Strategy:")
    modern_strategy_path = "hyperliquid_bot/modern/tf_v3_modern.py"
    if os.path.exists(modern_strategy_path):
        with open(modern_strategy_path, 'r') as f:
            modern_code = f.read()
            
        # Same checks for modern
        checks = {
            'imbalance_usage': 'imbalance' in modern_code,
            'position_limit': 'max_positions' in modern_code or 'concurrent_positions' in modern_code,
            'time_filter': 'hour' in modern_code or 'trading_hours' in modern_code,
            'order_book_depth': any(f'level_{i}' in modern_code or f'bid_sz_{i}' in modern_code for i in range(6, 21)),
            'special_exit': 'exit_signal' in modern_code or 'should_exit' in modern_code
        }
        
        for check, found in checks.items():
            print(f"   - {check}: {'✅ FOUND' if found else '❌ NOT FOUND'}")

def check_backtester_differences():
    print("\n\n4. BACKTESTER IMPLEMENTATION CHECK:")
    print("-" * 50)
    
    # Check exit logic in backtesters
    print("\n🔄 Legacy Backtester:")
    legacy_bt_path = "hyperliquid_bot/backtester/backtester.py"
    if os.path.exists(legacy_bt_path):
        with open(legacy_bt_path, 'r') as f:
            legacy_bt_code = f.read()
            
        checks = {
            'position_tracking': 'self.positions' in legacy_bt_code,
            'multiple_positions': 'append' in legacy_bt_code and 'positions' in legacy_bt_code,
            'exit_evaluation': 'evaluate_exit' in legacy_bt_code or 'check_exit' in legacy_bt_code,
            'stop_loss': 'stop_loss' in legacy_bt_code,
            'take_profit': 'take_profit' in legacy_bt_code
        }
        
        for check, found in checks.items():
            print(f"   - {check}: {'✅ FOUND' if found else '❌ NOT FOUND'}")
    
    print("\n🔄 Modern Backtester:")
    modern_bt_path = "hyperliquid_bot/modern/backtester_engine.py"
    if os.path.exists(modern_bt_path):
        with open(modern_bt_path, 'r') as f:
            modern_bt_code = f.read()
            
        # Same checks for modern
        for check, found in checks.items():
            found_modern = check.replace('_', ' ') in modern_bt_code or check in modern_bt_code
            print(f"   - {check}: {'✅ FOUND' if found_modern else '❌ NOT FOUND'}")

def main():
    check_legacy_configuration()
    analyze_data_differences()
    check_strategy_differences()
    check_backtester_differences()
    
    print("\n\n" + "="*60)
    print("CRITICAL FINDINGS SUMMARY:")
    print("="*60)
    print("\n1. Check if Legacy uses 'imbalance' field (not in modern data)")
    print("2. Check if Legacy limits concurrent positions")
    print("3. Check if Legacy has time-based trading filters")
    print("4. Check if Legacy has different exit evaluation logic")
    print("5. Check if Legacy uses deeper order book levels")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Run verify_regime_updates.py to check update frequencies")
    print("2. Run legacy_system_analyzer.py for deep trade analysis")
    print("3. Compare the findings to identify the SECRET SAUCE")

if __name__ == "__main__":
    main()