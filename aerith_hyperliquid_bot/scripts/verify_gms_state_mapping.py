#!/usr/bin/env python3
"""
Verification script for GMS State Mapping Standardization Phase 4.
Tests both state validation and detector integration without modifying any live data.
"""

import sys
import logging
from pathlib import Path

# Ensure we can import from the parent directory
sys.path.append(str(Path(__file__).parent.parent))

from hyperliquid_bot.utils.state_mapping import (
    get_valid_gms_states, 
    get_state_map,
    map_gms_state,
    validate_gms_state,
    validate_3state,
    GMS_STATE_UNKNOWN,
    GMS_STATE_FILTER_OFF
)

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.detector import (
    GranularMicrostructureRegimeDetector
)

# Setup logging
logging.basicConfig(level=logging.INFO, 
                  format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('gms_verification')

def test_state_validation():
    """Test the state validation functionality"""
    logger.info("=== Testing State Validation ===")
    
    # Get valid states
    valid_states = get_valid_gms_states()
    logger.info(f"Valid GMS states: {sorted(list(valid_states))}")
    
    # Test validation on known valid states
    for state in valid_states:
        assert validate_gms_state(state) or state in [GMS_STATE_UNKNOWN, GMS_STATE_FILTER_OFF], \
            f"State '{state}' failed validation despite being in valid_states"
    
    logger.info("✓ All valid states pass validation")
    
    # Test validation on invalid states
    invalid_states = ["bull", "bear", "chop", "INVALID_STATE", "test", ""]
    for state in invalid_states:
        if state in valid_states:
            logger.warning(f"Unexpected: '{state}' is considered valid")
        else:
            logger.info(f"✓ '{state}' correctly identified as invalid")
    
    logger.info("State validation test complete")

def test_mapping():
    """Test the mapping from 7-state to 3-state"""
    logger.info("=== Testing State Mapping ===")
    
    # Get the state map
    state_map = get_state_map()
    logger.info(f"Loaded state map: {state_map}")
    
    # Test mapping of all valid states
    for state in get_valid_gms_states():
        if state in [GMS_STATE_UNKNOWN, GMS_STATE_FILTER_OFF]:
            # Special states - these aren't part of the normal mapping
            continue
            
        mapped = map_gms_state(state)
        logger.info(f"'{state}' maps to '{mapped}'")
        assert validate_3state(mapped), f"Mapped value '{mapped}' is not a valid 3-state"
    
    logger.info("✓ All standard states map to valid 3-states")
    
    # Test mapping of invalid states (should return default CHOP)
    for invalid_state in ["INVALID", "unknown_state"]:
        mapped = map_gms_state(invalid_state)
        logger.info(f"Invalid state '{invalid_state}' maps to '{mapped}'")
    
    logger.info("State mapping test complete")

def test_detector():
    """Test the GMS detector's output validation"""
    logger.info("=== Testing GMS Detector Output Validation ===")
    
    # Create a minimal configuration for testing
    config = Config.from_yaml(str(Path(__file__).parent.parent / "configs" / "base.yaml"))
    
    # Create a detector instance
    detector = GranularMicrostructureRegimeDetector(config)
    
    # Create a minimal signal set to test detector behavior
    # This will intentionally produce an "Uncertain" state
    minimal_signals = {
        'atr_pct': 0.005,
        'ma_slope': 0.0001,  # Near zero slope
        'obi_ratio': 0.0,    # Neutral OBI
        'spread_mean': 0.1,
        'spread_std': 0.01
    }
    
    # Test detector output
    try:
        regime = detector.get_regime(minimal_signals)
        logger.info(f"Detector produced regime: '{regime}'")
        
        # Verify the output is in our valid states list
        valid_states = get_valid_gms_states()
        assert regime in valid_states, f"Output regime '{regime}' is not in valid states"
        logger.info("✓ Detector output is a valid state")
        
        # Map the regime to 3-state
        mapped_regime = map_gms_state(regime)
        logger.info(f"Mapped to 3-state: '{mapped_regime}'")
        
    except Exception as e:
        logger.error(f"Error testing detector: {e}")
        raise
    
    logger.info("Detector test complete")

def main():
    """Run all verification tests"""
    logger.info("Starting GMS State Mapping Verification")
    
    try:
        # Test state validation
        test_state_validation()
        
        # Test state mapping 
        test_mapping()
        
        # Test detector integration
        test_detector()
        
        logger.info("=== ALL TESTS PASSED ===")
        logger.info("Phase 4 implementation verified successfully!")
        
    except AssertionError as e:
        logger.error(f"VERIFICATION FAILED: {e}")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error during verification: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
