# scripts/analyze_features.py

import logging
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import yaml # To load config directly for logging setup
from datetime import datetime  # Add missing datetime import

# --- Project Imports ---
# Adjust paths if your script location differs relative to the package
try:
    from hyperliquid_bot.config.settings import Config, load_config
    from hyperliquid_bot.data.handler import HistoricalDataHandler
    from hyperliquid_bot.signals.calculator import SignalCalculator
except ImportError:
    import sys
    # Add project root to path if running script directly and imports fail
    project_root = Path(__file__).parent.parent.resolve()
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    from hyperliquid_bot.config.settings import Config, load_config
    from hyperliquid_bot.data.handler import HistoricalDataHandler
    from hyperliquid_bot.signals.calculator import SignalCalculator

# --- Basic Logging Setup ---
log_format = '%(asctime)s [%(levelname)-5s] %(name)-25s: %(message)s'
logging.basicConfig(level=logging.INFO, format=log_format)
logger = logging.getLogger("FeatureAnalysis")

def analyze_signal_distributions(config: Config, signals_df: pd.DataFrame):
    """
    Calculates and prints descriptive statistics and percentiles for key signals,
    and saves distribution plots.
    """
    if signals_df.empty:
        logger.error("Signals DataFrame is empty. Cannot perform analysis.")
        return

    # Define key features for the Granular Detector
    # Add/remove features as needed based on your detector's requirements
    features_to_analyze = [
        'atr_percent',      # Volatility
        'ma_slope',         # Momentum
        'obi_smoothed',     # Microstructure Confirmation
        'spread_mean',      # Microstructure Context (Range)
        'spread_std',       # Microstructure Context (Chop)
        # Optional additions for context:
        'adx',
        'forecast',
        'raw_spread_rel',
        f'raw_obi_{config.microstructure.obi_levels}'
    ]

    percentiles_to_calc = [1, 5, 10, 25, 50, 75, 90, 95, 99]
    stats_summary = {}

    # Ensure plot directory exists
    plot_dir = Path(config.data_paths.log_dir) / "feature_analysis_plots"
    plot_dir.mkdir(parents=True, exist_ok=True)
    logger.info(f"Saving distribution plots to: {plot_dir}")

    print("\n--- Feature Distribution Analysis ---")

    for feature in features_to_analyze:
        if feature not in signals_df.columns:
            logger.warning(f"Feature '{feature}' not found in signals DataFrame. Skipping analysis.")
            continue

        # Drop NaNs for accurate statistics and plotting
        feature_data = signals_df[feature].dropna()

        if feature_data.empty:
            logger.warning(f"Feature '{feature}' has no non-NaN data. Skipping analysis.")
            continue

        logger.info(f"Analyzing feature: '{feature}' (Non-NaN count: {len(feature_data)})")

        # Calculate stats
        stats = {
            'mean': feature_data.mean(),
            'std': feature_data.std(),
            'min': feature_data.min(),
            'max': feature_data.max(),
            'percentiles': {p: feature_data.quantile(p / 100.0) for p in percentiles_to_calc}
        }
        stats_summary[feature] = stats

        # --- Print Statistics ---
        print(f"\nFeature: {feature}")
        print(f"  Mean: {stats['mean']:.6f}")
        print(f"  Std Dev: {stats['std']:.6f}")
        print(f"  Min: {stats['min']:.6f}")
        print(f"  Max: {stats['max']:.6f}")
        print("  Percentiles:")
        for p, value in stats['percentiles'].items():
            print(f"    {p:2d}%: {value:.6f}")

        # --- Plot Histogram ---
        try:
            plt.figure(figsize=(10, 6))
            # Use more bins for potentially skewed data, handle potential errors
            n_bins = min(100, max(20, len(feature_data) // 100)) # Adaptive bins
            plt.hist(feature_data, bins=n_bins, alpha=0.75, edgecolor='black')
            plt.title(f'Distribution of {feature}', fontsize=14)
            plt.xlabel('Value', fontsize=12)
            plt.ylabel('Frequency', fontsize=12)
            plt.grid(True, linestyle='--', alpha=0.6)

            # Add percentile lines for reference
            if 25 in percentiles_to_calc:
                plt.axvline(stats['percentiles'][25], color='red', linestyle='--', linewidth=1, label=f"25th: {stats['percentiles'][25]:.4f}")
            if 50 in percentiles_to_calc:
                plt.axvline(stats['percentiles'][50], color='black', linestyle='-', linewidth=1, label=f"50th: {stats['percentiles'][50]:.4f}")
            if 75 in percentiles_to_calc:
                plt.axvline(stats['percentiles'][75], color='green', linestyle='--', linewidth=1, label=f"75th: {stats['percentiles'][75]:.4f}")

            plt.legend(fontsize=10)
            plt.tight_layout()
            plot_path = plot_dir / f"{feature}_distribution.png"
            plt.savefig(plot_path, dpi=100)
            plt.close() # Close plot to free memory
            logger.info(f"Saved plot: {plot_path.name}")

        except Exception as plot_e:
            logger.error(f"Failed to generate plot for '{feature}': {plot_e}", exc_info=False)

    print("\n--- Analysis Complete ---")
    return stats_summary

def analyze_losing_trades_by_regime(config: Config):
    """
    Loads the latest losing trades analysis CSV and prints details for
    trades entered during specific regimes.
    """
    logger.info("--- Analyzing Losing Trades by Regime ---")
    log_dir = Path(config.data_paths.log_dir)
    analysis_files = sorted(log_dir.glob("losing_trades_analysis_*.csv"), reverse=True)

    if not analysis_files:
        logger.error("No losing_trades_analysis CSV file found.")
        return

    latest_analysis_file = analysis_files[0]
    logger.info(f"Loading data from: {latest_analysis_file}")

    try:
        df_analysis = pd.read_csv(latest_analysis_file, index_col=0)
        # Ensure entry_dt is datetime
        if 'entry_dt' in df_analysis.columns:
             df_analysis['entry_dt'] = pd.to_datetime(df_analysis['entry_dt'], errors='coerce', utc=True)

        if 'entry_regime' not in df_analysis.columns:
            logger.error("'entry_regime' column not found in the CSV. Cannot perform analysis.")
            logger.info(f"Available columns: {list(df_analysis.columns)}")
            return

        # --- Filter for Weak Trend Regimes ---
        weak_regimes = ['Weak_Bull_Trend', 'Weak_Bear_Trend']
        df_weak_losses = df_analysis[df_analysis['entry_regime'].isin(weak_regimes)].copy()

        if df_weak_losses.empty:
            logger.info("No losing trades found with entry_regime in Weak_Bull_Trend or Weak_Bear_Trend.")
            return

        logger.info(f"Found {len(df_weak_losses)} losing trades entered during Weak Trend regimes.")

        # --- Select and Format Relevant Columns ---
        columns_to_show = [
            'entry_dt',
            'profit',
            'exit_reason',
            'entry_regime',
            'ma_slope',
            'obi_smoothed',
            'atr_percent',
            'adx',
            'forecast'
        ]
        # Filter columns that actually exist in the DataFrame
        available_columns = [col for col in columns_to_show if col in df_weak_losses.columns]

        df_filtered_output = df_weak_losses[available_columns]

        # Print the filtered data to console
        print("\nLosing Trades Entered During Weak Trend Regimes (Sample):")
        # Use pandas display options for better formatting
        with pd.option_context('display.max_rows', 20,
                               'display.max_columns', None,
                               'display.width', 1000,
                               'display.float_format', '{:,.4f}'.format):
             print(df_filtered_output.head(20)) # Print first 20 rows

        # Log the filtered data as well
        logger.info("\nLosing Trades Entered During Weak Trend Regimes (Sample):")
        for idx, row in df_filtered_output.head(20).iterrows():
            # Format each row as a readable log entry
            log_entry = [f"{col}={row[col]}" for col in available_columns]
            logger.info(f"Trade {idx}: {', '.join(log_entry)}")

        # Save this filtered DataFrame to a dedicated CSV
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filtered_csv_path = log_dir / f"weak_trend_losses_{timestamp}.csv"
        df_filtered_output.to_csv(filtered_csv_path)
        logger.info(f"Saved filtered weak trend losses to: {filtered_csv_path}")

        # Summary statistics in logs
        logger.info("\nWeak Trend Losing Trades - Summary Statistics:")
        for col in [c for c in available_columns if c not in ['entry_dt', 'exit_reason', 'entry_regime']]:
            if col in df_filtered_output.columns:
                try:
                    logger.info(f"{col}: mean={df_filtered_output[col].mean():.4f}, " 
                                f"min={df_filtered_output[col].min():.4f}, "
                                f"max={df_filtered_output[col].max():.4f}")
                except:
                    # Handle case where statistics can't be calculated
                    logger.info(f"{col}: Unable to calculate statistics")

    except Exception as e:
        logger.error(f"Error during losing trade analysis: {e}", exc_info=True)

def main():
    """
    Main function to load data, calculate signals, and run analysis.
    """
    logger.info("Starting feature analysis script...")

    try:
        # --- Load Configuration ---
        # Assumes config.yaml is in the parent directory of 'scripts'
        project_root = Path(__file__).parent.parent.resolve()
        config_path = project_root / 'config.yaml'
        if not config_path.exists():
            raise FileNotFoundError(f"Config file not found at {config_path}")

        # Basic load just for logging path
        temp_config_data = yaml.safe_load(open(config_path))
        log_dir_path = Path(temp_config_data.get('data_paths', {}).get('log_dir', './logs'))
        log_dir_path.mkdir(parents=True, exist_ok=True)
        log_file = log_dir_path / f"feature_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler) # Add file handler to root logger
        logger.info(f"Logging to console and file: {log_file}")

        # Full config load and validation
        config = load_config(str(config_path))
        logger.info(f"Configuration loaded for period: {config.backtest.period_preset} ({config.start_date.date()} to {config.end_date.date()})")

        # --- Load Data ---
        logger.info("Initializing DataHandler...")
        data_handler = HistoricalDataHandler(config)
        data_handler.load_historical_data(config.start_date, config.end_date)
        combined_df = data_handler.get_ohlcv_data() # Gets OHLCV + raw micro features

        if combined_df.empty:
            logger.error("Failed to load data via DataHandler. Aborting analysis.")
            return

        # --- Calculate Signals ---
        logger.info("Initializing SignalCalculator...")
        signal_calculator = SignalCalculator(config, data_handler) # Pass handler (it uses combined_df internally now)
        logger.info("Calculating all signals...")
        all_signals_df = signal_calculator.calculate_all_signals() # Calculates smoothed/rolled features etc.

        if all_signals_df.empty:
            logger.error("Signal calculation failed. Aborting analysis.")
            return

        # --- Perform Analysis ---
        logger.info("Starting signal distribution analysis...")
        analysis_results = analyze_signal_distributions(config, all_signals_df)

        # --- ADD CALL TO NEW FUNCTION ---
        analyze_losing_trades_by_regime(config)
        # --- END ADDITION ---

        if analysis_results:
             logger.info("Analysis results generated. Use the printed statistics and plots to update thresholds in config.yaml.")
             # Example: How to use results for thresholds
             # print("\n--- Example Threshold Suggestions ---")
             # if 'atr_percent' in analysis_results:
             #     print(f"gms_vol_low_thresh: {analysis_results['atr_percent']['percentiles'][25]:.6f} # 25th percentile")
             #     print(f"gms_vol_high_thresh: {analysis_results['atr_percent']['percentiles'][75]:.6f} # 75th percentile")
             # if 'ma_slope' in analysis_results:
             #     # Use absolute values for symmetrical thresholds
             #     abs_ma_slope = all_signals_df['ma_slope'].dropna().abs()
             #     print(f"gms_mom_weak_thresh: {abs_ma_slope.quantile(0.30):.6f} # 30th percentile of abs(ma_slope)")
             #     print(f"gms_mom_strong_thresh: {abs_ma_slope.quantile(0.85):.6f} # 85th percentile of abs(ma_slope)")
             # # Add similar suggestions for OBI, Spread etc.

    except FileNotFoundError as e:
        logger.critical(f"Configuration file error: {e}")
    except Exception as e:
        logger.critical(f"An unexpected error occurred during analysis: {e}", exc_info=True)

if __name__ == "__main__":
    main()