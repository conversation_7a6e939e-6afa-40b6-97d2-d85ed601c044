import pandas as pd
import sys
import logging

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Tell pandas to display all rows
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000) # Adjust width if needed

if len(sys.argv) != 2:
    logging.error("Usage: python parquet_inspector.py <path_to_parquet_file>")
    sys.exit(1)

file_path = sys.argv[1]

try:
    logging.info(f"Attempting to read: {file_path}")
    df = pd.read_parquet(file_path)
    logging.info(f"Successfully read file. Shape: {df.shape}")
    print("\n--- File Info ---")
    df.info()
    print("\n--- Full Data ---")
    print(df) # Print the entire DataFrame

except FileNotFoundError:
    logging.error(f"Error: File not found at {file_path}")
    sys.exit(1)
except Exception as e:
    logging.error(f"Error reading Parquet file: {e}")
    sys.exit(1)