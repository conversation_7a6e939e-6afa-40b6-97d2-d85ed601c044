#!/usr/bin/env python3
"""
Test script to validate hourly aggregation fix across the full dataset.

This script tests the data handler's hourly aggregation functionality
for the complete date range (2025-03-01 to 2025-03-22) to ensure
the Task R-113 fix works consistently.
"""

import sys
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
import logging

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.data.handler import HistoricalDataHandler

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_hourly_aggregation_for_date(date_str: str, config) -> dict:
    """
    Test hourly aggregation for a specific date.
    
    Args:
        date_str: Date in YYYY-MM-DD format
        config: Configuration object
        
    Returns:
        Dictionary with test results
    """
    logger.info(f"Testing hourly aggregation for {date_str}")
    
    try:
        # Create date range for single day
        start_date = datetime.strptime(date_str, '%Y-%m-%d')
        end_date = start_date + timedelta(days=1)
        
        # Initialize data handler
        data_handler = HistoricalDataHandler(config)
        
        # Load data for this date
        data_handler.load_data(start_date, end_date)
        
        # Get the combined data
        combined_data = data_handler.get_ohlcv_data()
        
        if combined_data.empty:
            return {
                'status': 'error',
                'error': 'No data loaded',
                'date': date_str
            }
        
        # Check for critical features
        critical_features = [
            'spread_mean', 'spread_std', 'ma_slope_ema_30s',
            'atr_14_sec', 'atr_percent_sec'
        ]
        
        results = {
            'status': 'success',
            'date': date_str,
            'total_rows': len(combined_data),
            'feature_stats': {},
            'missing_features': [],
            'regime_test': None
        }
        
        # Check each critical feature
        for feature in critical_features:
            if feature not in combined_data.columns:
                results['missing_features'].append(feature)
            else:
                nan_count = combined_data[feature].isna().sum()
                nan_ratio = nan_count / len(combined_data)
                
                results['feature_stats'][feature] = {
                    'nan_count': nan_count,
                    'nan_ratio': nan_ratio,
                    'total_values': len(combined_data)
                }
        
        # Test regime detection if possible
        if not results['missing_features']:
            try:
                # Try to get a sample row for regime detection test
                sample_row = combined_data.iloc[len(combined_data)//2]  # Middle row
                
                # Check if we have the required columns for GMS
                gms_required = ['spread_mean', 'spread_std', 'ma_slope_ema_30s']
                if all(col in sample_row.index for col in gms_required):
                    # Check if values are not NaN
                    if not any(pd.isna(sample_row[col]) for col in gms_required):
                        results['regime_test'] = 'ready'
                    else:
                        results['regime_test'] = 'nan_values'
                else:
                    results['regime_test'] = 'missing_columns'
                    
            except Exception as e:
                results['regime_test'] = f'error: {str(e)}'
        
        return results
        
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e),
            'date': date_str
        }


def main():
    """Main test function."""
    logger.info("=== TESTING HOURLY AGGREGATION ACROSS FULL DATASET ===")
    
    # Load configuration
    try:
        config = load_config('configs/base.yaml')
    except Exception as e:
        logger.error(f"Failed to load configuration: {e}")
        sys.exit(1)
    
    # Test dates from different periods
    test_dates = [
        '2025-03-01',  # Start of range
        '2025-03-05',  # Early March
        '2025-03-12',  # Mid March
        '2025-03-18',  # Late March
        '2025-03-22'   # End of range
    ]
    
    all_results = {}
    overall_success = True
    
    for date_str in test_dates:
        results = test_hourly_aggregation_for_date(date_str, config)
        all_results[date_str] = results
        
        if results['status'] != 'success':
            overall_success = False
            logger.error(f"❌ Test failed for {date_str}: {results.get('error', 'Unknown error')}")
        else:
            logger.info(f"✅ Test passed for {date_str}")
            logger.info(f"   Total rows: {results['total_rows']}")
            logger.info(f"   Missing features: {len(results['missing_features'])}")
            logger.info(f"   Regime test: {results['regime_test']}")
            
            if results['missing_features']:
                logger.warning(f"   Missing: {results['missing_features']}")
            
            # Log feature stats for critical features
            for feature in ['spread_mean', 'spread_std', 'ma_slope_ema_30s']:
                if feature in results['feature_stats']:
                    stats = results['feature_stats'][feature]
                    logger.info(f"   {feature}: {stats['nan_ratio']:.1%} NaN ({stats['nan_count']}/{stats['total_values']})")
    
    # Summary report
    logger.info("\n=== TEST SUMMARY ===")
    success_count = 0
    for date_str, results in all_results.items():
        if results['status'] == 'success':
            success_count += 1
            regime_status = "✅" if results['regime_test'] == 'ready' else "⚠️"
            logger.info(f"{date_str}: ✅ PASS ({results['total_rows']} rows) {regime_status}")
        else:
            logger.error(f"{date_str}: ❌ FAIL - {results.get('error', 'Unknown error')}")
    
    logger.info(f"\nResults: {success_count}/{len(test_dates)} dates passed")
    
    if overall_success and success_count == len(test_dates):
        logger.info("\n🎉 ALL TESTS PASSED - Hourly aggregation fix working across full dataset!")
        logger.info("✅ Critical features preserved in hourly aggregation")
        logger.info("✅ Data handler successfully processes all test dates")
        logger.info("✅ Task R-113 hourly aggregation fix validated for complete dataset")
        return 0
    else:
        logger.error(f"\n💥 SOME TESTS FAILED - {len(test_dates) - success_count} dates failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
