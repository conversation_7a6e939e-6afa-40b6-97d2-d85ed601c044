#!/usr/bin/env python3
"""
Test script to run a single backtest and verify all metric extraction.
"""
import os
import sys
import subprocess
import re
import time
from pathlib import Path

# Define the project root
PROJECT_ROOT = Path(__file__).parent.parent.resolve()

def create_test_override():
    """Create a test override file."""
    # Use the first override file from the Latin square design
    override_content = """microstructure:
  obi_smoothing_window: 8

regime:
  gms_obi_strong_confirm_thresh: 0.15
  gms_obi_weak_confirm_thresh: 0.15
  gms_spread_std_high_thresh: 2.6e-05
  gms_confirmation_bars: 1
"""
    
    # Create a unique filename
    override_dir = PROJECT_ROOT / "configs" / "overrides" / "grid_search"
    override_dir.mkdir(exist_ok=True, parents=True)
    
    override_path = override_dir / "test_metrics_run.yaml"
    
    # Write the override config to the file
    with open(override_path, 'w') as f:
        f.write(override_content)
    
    return override_path

def extract_metrics_from_log(log_file):
    """Extract metrics from log file."""
    try:
        # Extract metrics from log file
        with open(log_file, 'r') as f:
            log_content = f.read()
        
        # The log file contains ANSI escape codes for formatting, so we need to use more generic patterns
        
        # Look for Sharpe ratio using a generic pattern that works with ANSI codes
        sharpe_match = re.search(r'Sharpe Ratio.*?(\d+\.\d+)', log_content)
        if sharpe_match:
            sharpe = float(sharpe_match.group(1))
            print(f"Found Sharpe ratio: {sharpe}")
        else:
            sharpe = None
            print("Could not find Sharpe ratio")
        
        # Look for Max Drawdown using a generic pattern that works with ANSI codes
        dd_match = re.search(r'Max Drawdown.*?(\d+\.\d+)%', log_content)
        if dd_match:
            max_dd = float(dd_match.group(1)) / -100.0  # Convert percentage to decimal and make negative
            print(f"Found Max Drawdown: {max_dd}")
        else:
            max_dd = None
            print("Could not find Max Drawdown")
        
        # Look for ROI using a generic pattern that works with ANSI codes
        roi_match = re.search(r'Return on Initial \(ROI\).*?(\d+\.\d+)%', log_content)
        if roi_match:
            roi = float(roi_match.group(1)) / 100.0  # Convert percentage to decimal
            print(f"Found ROI: {roi}")
        else:
            roi = None
            print("Could not find ROI")
        
        # Look for Profit Factor using a generic pattern that works with ANSI codes
        pf_match = re.search(r'Profit Factor.*?(\d+\.\d+)', log_content)
        if pf_match:
            profit_factor = float(pf_match.group(1))
            print(f"Found Profit Factor: {profit_factor}")
        else:
            profit_factor = None
            print("Could not find Profit Factor")
        
        return sharpe, max_dd, roi, profit_factor
    
    except Exception as e:
        print(f"Error extracting metrics from log file: {e}")
        return None, None, None, None

def run_test():
    """Run a single test backtest and verify metric extraction."""
    # Create test override file
    override_path = create_test_override()
    print(f"Created test override file: {override_path}")
    
    # Record start time
    run_start_time = time.time()
    
    # Run the backtester with the override
    backtest_script = PROJECT_ROOT / "hyperliquid_bot" / "backtester" / "run_backtest.py"
    cmd = [
        sys.executable,  # Current Python interpreter
        str(backtest_script),
        "--override", str(override_path)
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    process = subprocess.run(cmd, check=True, capture_output=True, text=True)
    
    # Record end time
    run_end_time = time.time()
    
    # Find the log file that was created
    log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    log_files = [
        f for f in log_dir.glob("backtest_run_*.log") 
        if run_start_time <= os.path.getmtime(f) <= run_end_time
    ]
    
    if not log_files:
        print("Error: No log files found for this run")
        return
    
    # Use the most recent log file
    log_file = max(log_files, key=os.path.getmtime)
    print(f"Found log file: {log_file}")
    
    # Extract metrics
    sharpe, max_dd, roi, profit_factor = extract_metrics_from_log(log_file)
    
    if all(x is not None for x in [sharpe, max_dd, roi, profit_factor]):
        print("\nSuccessfully extracted all metrics:")
        print(f"Sharpe: {sharpe}")
        print(f"MaxDD: {max_dd}")
        print(f"ROI: {roi}")
        print(f"Profit Factor: {profit_factor}")
        print(f"Sharpe/DD Ratio: {sharpe / abs(max_dd)}")
    else:
        print("\nFailed to extract all metrics")

if __name__ == "__main__":
    run_test()
