#!/usr/bin/env python3
"""
Run One Week Modern System Test
===============================

Test with just one week to diagnose duplicate timestamp issue.
"""

import sys
from pathlib import Path
from datetime import datetime
import json

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine


def main():
    print("="*80)
    print("AERITH MODERN SYSTEM - ONE WEEK TEST")
    print("="*80)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Just one week
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 22)
    
    # Load configuration
    config_path = project_root / "configs/overrides/modern_system_v2_complete.yaml"
    config = load_config(config_path=str(config_path))
    
    print(f"📅 Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    print(f"⚙️  Config: modern_system_v2_complete.yaml")
    print()
    print("🎯 CALIBRATED SETTINGS:")
    print("  - Confidence threshold: 0.5")
    print("  - State persistence: DISABLED")
    print("  - Max transitions/hour: 60")
    print("  - Risk per trade: 25%")
    print()
    print("-"*80)
    
    try:
        # Create modern backtesting engine
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        # Run backtest
        print("\n🚀 Starting one week test...\n")
        results = engine.run_backtest()
        
        # Extract key metrics
        total_trades = results['performance']['total_trades']
        total_return = results['performance']['total_return']
        win_rate = results['performance']['win_rate']
        runtime = results['runtime_seconds']
        integrity = results.get('system_integrity', {})
        
        print("\n" + "="*80)
        print("📊 ONE WEEK RESULTS")
        print("="*80)
        print(f"Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        print(f"Runtime: {runtime:.1f} seconds")
        print()
        print("PERFORMANCE METRICS:")
        print(f"  - Total Trades: {total_trades}")
        print(f"  - Total Return: {total_return:.2%}")
        print(f"  - Win Rate: {win_rate:.2%}")
        print()
        
        # Extrapolate to full year (52 weeks)
        print("FULL YEAR PROJECTION (52x weekly):")
        print(f"  - Expected Trades: {total_trades * 52}")
        print(f"  - Expected ROI: {(1 + total_return)**52 - 1:.2%}")
        print()
        
        # System integrity
        if integrity.get('is_pure_modern', False):
            print("✅ System Integrity: PURE MODERN (no legacy fallbacks)")
        else:
            print("❌ System Integrity: LEGACY CONTAMINATION DETECTED")
            
        # Save results
        output_path = Path('modern_week_test_results.json')
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Detailed results saved to: {output_path}")
        
        # Trade details
        if results['trades']:
            print(f"\nGENERATED {len(results['trades'])} TRADES:")
            for i, trade in enumerate(results['trades'][:5]):  # Show first 5
                print(f"  {i+1}. {trade['timestamp']} - {trade['direction'].upper()} "
                      f"@ {trade['entry_price']:.2f} (regime: {trade['regime']})")
            if len(results['trades']) > 5:
                print(f"  ... and {len(results['trades']) - 5} more trades")
        
        print("\n" + "="*80)
        
    except Exception as e:
        print(f"\n❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()