#!/usr/bin/env python3
"""
Validation script for conservative modern system calibration.
Tests trade frequency and regime detection against target metrics.
"""
import os
import sys
from pathlib import Path
import pandas as pd
import numpy as np
import yaml
import json
from datetime import datetime
import logging

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_conservative_backtest():
    """Run backtest with conservative calibration."""
    logger.info("Running conservative calibration backtest...")
    
    # Create output directory
    output_dir = Path(__file__).parent.parent / "validation_results"
    output_dir.mkdir(exist_ok=True)
    
    # Run backtest with conservative config
    cmd = f"""
    cd {Path(__file__).parent.parent} && 
    python -m hyperliquid_bot.backtester.run_backtest \\
        --override configs/overrides/conservative_modern_calibration.yaml \\
        --output-dir {output_dir} \\
        --symbol BTC \\
        --start-date 2024-01-01 \\
        --end-date 2024-12-31
    """
    
    logger.info(f"Running command: {cmd}")
    result = os.system(cmd)
    
    if result != 0:
        logger.error("Backtest failed!")
        return None
        
    # Find the results file
    results_files = list(output_dir.glob("backtest_results_*.json"))
    if not results_files:
        logger.error("No results file found!")
        return None
        
    latest_results = max(results_files, key=lambda x: x.stat().st_mtime)
    logger.info(f"Found results file: {latest_results}")
    
    return latest_results


def analyze_trade_frequency(results_file):
    """Analyze trade frequency from backtest results."""
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    # Extract trade count
    trades = results.get('trades', [])
    total_trades = len(trades)
    
    # Analyze regime distribution
    regime_counts = {'BULL': 0, 'BEAR': 0, 'CHOP': 0}
    direction_counts = {'long': 0, 'short': 0}
    
    for trade in trades:
        regime = trade.get('entry_regime', 'Unknown')
        direction = trade.get('direction', 'unknown')
        
        if regime in regime_counts:
            regime_counts[regime] += 1
        if direction in direction_counts:
            direction_counts[direction] += 1
    
    logger.info(f"=== TRADE FREQUENCY ANALYSIS ===")
    logger.info(f"Total Trades: {total_trades}")
    logger.info(f"Target Range: 160-190 trades")
    logger.info(f"Within Target: {'✓' if 160 <= total_trades <= 190 else '✗'}")
    logger.info(f"")
    logger.info(f"Regime Distribution:")
    logger.info(f"  BULL: {regime_counts['BULL']} trades")
    logger.info(f"  BEAR: {regime_counts['BEAR']} trades")
    logger.info(f"  CHOP: {regime_counts['CHOP']} trades")
    logger.info(f"")
    logger.info(f"Direction Distribution:")
    logger.info(f"  Long: {direction_counts['long']} trades")
    logger.info(f"  Short: {direction_counts['short']} trades")
    logger.info(f"")
    
    # Check Bear regime detection
    bear_detection = regime_counts['BEAR'] > 0
    logger.info(f"Bear Regime Detection: {'✓' if bear_detection else '✗'}")
    
    return {
        'total_trades': total_trades,
        'within_target': 160 <= total_trades <= 190,
        'regime_counts': regime_counts,
        'direction_counts': direction_counts,
        'bear_detection': bear_detection
    }


def validate_regime_detection():
    """Validate regime detection against ground truth events."""
    logger.info("=== REGIME DETECTION VALIDATION ===")
    
    # Load ground truth events
    events_file = Path(__file__).parent.parent / "configs" / "regime_events_2024.yaml"
    with open(events_file, 'r') as f:
        events_data = yaml.safe_load(f)
    
    events = events_data['events']
    bear_events = [e for e in events if e['regime'] == 'BEAR']
    
    logger.info(f"Ground Truth Bear Events: {len(bear_events)}")
    for event in bear_events:
        logger.info(f"  - {event['name']}: {event['start']} to {event['end']}")
    
    # TODO: Compare with actual regime predictions from backtest
    # This would require parsing the regime states from backtest output
    
    return len(bear_events)


def generate_calibration_report(trade_analysis, bear_events_count):
    """Generate comprehensive calibration report."""
    
    report = {
        'calibration_date': datetime.now().isoformat(),
        'target_metrics': {
            'trade_frequency_range': [160, 190],
            'bear_detection_required': True,
            'conservative_approach': True
        },
        'actual_metrics': {
            'total_trades': trade_analysis['total_trades'],
            'within_target_range': trade_analysis['within_target'],
            'bear_trades_detected': trade_analysis['regime_counts']['BEAR'],
            'bear_detection_working': trade_analysis['bear_detection']
        },
        'regime_distribution': trade_analysis['regime_counts'],
        'direction_distribution': trade_analysis['direction_counts'],
        'ground_truth_bear_events': bear_events_count,
        'calibration_success': (
            trade_analysis['within_target'] and 
            trade_analysis['bear_detection']
        )
    }
    
    # Save report
    report_file = Path(__file__).parent.parent / "validation_results" / f"calibration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
    with open(report_file, 'w') as f:
        yaml.dump(report, f, default_flow_style=False, indent=2)
    
    logger.info(f"=== CALIBRATION REPORT ===")
    logger.info(f"Report saved to: {report_file}")
    logger.info(f"Calibration Success: {'✓' if report['calibration_success'] else '✗'}")
    
    if not report['calibration_success']:
        logger.info("=== RECOMMENDATIONS ===")
        if not trade_analysis['within_target']:
            if trade_analysis['total_trades'] > 190:
                logger.info("- Increase momentum thresholds further")
                logger.info("- Add stricter regime confidence requirements")
            elif trade_analysis['total_trades'] < 160:
                logger.info("- Decrease momentum thresholds slightly")
                logger.info("- Relax regime confidence requirements")
        
        if not trade_analysis['bear_detection']:
            logger.info("- Check state mapping for Weak_Bear_Trend")
            logger.info("- Verify Bear regime thresholds")
    
    return report_file


def main():
    """Run complete calibration validation."""
    logger.info("Starting conservative calibration validation...")
    
    # Step 1: Run backtest
    results_file = run_conservative_backtest()
    if not results_file:
        logger.error("Backtest failed, cannot continue validation")
        return
    
    # Step 2: Analyze trade frequency
    trade_analysis = analyze_trade_frequency(results_file)
    
    # Step 3: Validate regime detection
    bear_events_count = validate_regime_detection()
    
    # Step 4: Generate report
    report_file = generate_calibration_report(trade_analysis, bear_events_count)
    
    logger.info("\n" + "="*60)
    logger.info("CONSERVATIVE CALIBRATION VALIDATION COMPLETE")
    logger.info("="*60)
    logger.info(f"Results file: {results_file}")
    logger.info(f"Report file: {report_file}")
    logger.info("="*60)


if __name__ == "__main__":
    main()