#!/usr/bin/env python3
"""
General script to run backtest with any override configuration.
Lists available overrides and allows easy selection.
"""

import subprocess
import sys
from pathlib import Path
import argparse

def get_available_overrides(overrides_dir):
    """Get list of available override YAML files."""
    if not overrides_dir.exists():
        return []
    
    yaml_files = list(overrides_dir.glob("*.yaml")) + list(overrides_dir.glob("*.yml"))
    return sorted([f.stem for f in yaml_files])

def list_overrides(overrides_dir):
    """List all available override configurations."""
    overrides = get_available_overrides(overrides_dir)
    
    if not overrides:
        print("No override configurations found in configs/overrides/")
        return
    
    print("Available override configurations:")
    print("-" * 40)
    for i, override in enumerate(overrides, 1):
        print(f"{i:2d}. {override}")
    print("-" * 40)

def main():
    """Run backtest with selected override configuration."""
    # Get the project root
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # Paths
    backtest_script = project_root / "hyperliquid_bot" / "backtester" / "run_backtest.py"
    overrides_dir = project_root / "configs" / "overrides"
    
    # Parse arguments
    parser = argparse.ArgumentParser(
        description="Run backtest with override configuration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # List available overrides
  python run_override.py --list
  
  # Run with execution refinement
  python run_override.py execution_refinement_enabled
  
  # Run with specific override and timeframe
  python run_override.py my_override --timeframe 4h
  
  # Run with override number from list
  python run_override.py 1
        """
    )
    
    parser.add_argument('override', nargs='?', 
                        help='Override name or number (from --list)')
    parser.add_argument('--list', '-l', action='store_true',
                        help='List available override configurations')
    parser.add_argument('--timeframe', type=str, choices=['1h', '4h'],
                        help='Timeframe override')
    parser.add_argument('--run-id', type=str,
                        help='Unique identifier for this backtest run')
    parser.add_argument('--skip-validation-warnings', action='store_true',
                        help='Skip warnings about validation issues')
    
    args = parser.parse_args()
    
    # Handle --list option
    if args.list:
        list_overrides(overrides_dir)
        return 0
    
    # Check if override was provided
    if not args.override:
        print("Error: No override specified")
        print("\nUsage: python run_override.py [override_name]")
        print("\nRun with --list to see available overrides:")
        print("  python run_override.py --list")
        return 1
    
    # Get available overrides
    available_overrides = get_available_overrides(overrides_dir)
    
    if not available_overrides:
        print("Error: No override configurations found in configs/overrides/")
        return 1
    
    # Determine which override to use
    override_name = None
    
    # Check if input is a number
    if args.override.isdigit():
        index = int(args.override) - 1
        if 0 <= index < len(available_overrides):
            override_name = available_overrides[index]
        else:
            print(f"Error: Invalid override number: {args.override}")
            print(f"Valid range: 1-{len(available_overrides)}")
            return 1
    else:
        # Check if it's a valid override name
        if args.override in available_overrides:
            override_name = args.override
        else:
            # Try to find partial match
            matches = [o for o in available_overrides if args.override.lower() in o.lower()]
            if len(matches) == 1:
                override_name = matches[0]
            elif len(matches) > 1:
                print(f"Error: Multiple overrides match '{args.override}':")
                for match in matches:
                    print(f"  - {match}")
                return 1
            else:
                print(f"Error: No override found matching '{args.override}'")
                print("\nAvailable overrides:")
                for o in available_overrides:
                    print(f"  - {o}")
                return 1
    
    # Build override path
    override_path = overrides_dir / f"{override_name}.yaml"
    if not override_path.exists():
        override_path = overrides_dir / f"{override_name}.yml"
    
    # Set default run-id if not provided
    if not args.run_id:
        args.run_id = override_name.replace("_", "-")
    
    # Build the command
    cmd = [
        sys.executable,
        str(backtest_script),
        "--override", str(override_path),
        "--run-id", args.run_id
    ]
    
    # Add optional arguments
    if args.timeframe:
        cmd.extend(["--timeframe", args.timeframe])
    
    if args.skip_validation_warnings:
        cmd.append("--skip-validation-warnings")
    
    print(f"Running backtest with override: {override_name}")
    print(f"Override path: {override_path}")
    print(f"Run ID: {args.run_id}")
    if args.timeframe:
        print(f"Timeframe: {args.timeframe}")
    print("-" * 80)
    
    # Run the backtest
    try:
        result = subprocess.run(cmd, check=True)
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"\nBacktest failed with error code: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print("\nBacktest interrupted by user")
        return 1

if __name__ == "__main__":
    sys.exit(main())