#!/usr/bin/env python3
"""
Analyze Modern Regime Patterns
==============================

This script analyzes regime patterns from the modern system to help
calibrate thresholds for optimal trading performance.

It examines:
- Regime state distribution
- State duration statistics
- Transition patterns
- Correlation with price movements
"""

import argparse
import sys
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import json
from collections import Counter, defaultdict

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
from hyperliquid_bot.modern.data_loader import ModernDataLoader


def analyze_regime_patterns(start_date: datetime, end_date: datetime, config_path: str):
    """
    Analyze regime patterns over a period.
    
    Args:
        start_date: Analysis start date
        end_date: Analysis end date
        config_path: Path to config file
    """
    print("=" * 80)
    print("MODERN REGIME PATTERN ANALYSIS")
    print("=" * 80)
    
    # Load configuration
    config = load_config(config_path=config_path)
    
    # Create modern backtesting engine
    engine = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date
    )
    
    # Run a quick backtest to collect regime data
    print(f"Analyzing period: {start_date.date()} to {end_date.date()}")
    results = engine.run_backtest()
    
    # Extract regime history
    regime_history = results.get('regime_history', [])
    
    if not regime_history:
        print("No regime history collected!")
        return
    
    print(f"\nCollected {len(regime_history)} regime updates")
    
    # Convert to DataFrame for analysis
    regime_df = pd.DataFrame(regime_history)
    
    # 1. State Distribution
    print("\n" + "="*50)
    print("REGIME STATE DISTRIBUTION")
    print("="*50)
    
    state_counts = regime_df['state'].value_counts()
    total_states = len(regime_df)
    
    for state, count in state_counts.items():
        percentage = count / total_states * 100
        print(f"{state:20s}: {count:6d} ({percentage:5.1f}%)")
    
    # 2. State Duration Analysis
    print("\n" + "="*50)
    print("STATE DURATION STATISTICS (minutes)")
    print("="*50)
    
    # Calculate state durations
    state_durations = defaultdict(list)
    current_state = None
    state_start = None
    
    for _, row in regime_df.iterrows():
        if row['state'] != current_state:
            if current_state is not None and state_start is not None:
                duration = (row.get('timestamp', datetime.now()) - state_start).total_seconds() / 60
                state_durations[current_state].append(duration)
            current_state = row['state']
            state_start = row.get('timestamp', datetime.now())
    
    # Print duration statistics
    for state in sorted(state_durations.keys()):
        durations = state_durations[state]
        if durations:
            print(f"\n{state}:")
            print(f"  Mean:   {np.mean(durations):6.1f} minutes")
            print(f"  Median: {np.median(durations):6.1f} minutes")
            print(f"  Min:    {np.min(durations):6.1f} minutes")
            print(f"  Max:    {np.max(durations):6.1f} minutes")
    
    # 3. Regime Confidence Analysis
    print("\n" + "="*50)
    print("REGIME CONFIDENCE ANALYSIS")
    print("="*50)
    
    if 'confidence' in regime_df.columns:
        confidence_by_state = regime_df.groupby('state')['confidence'].agg(['mean', 'std', 'min', 'max'])
        print(confidence_by_state)
    else:
        print("No confidence data available")
    
    # 4. Feature Analysis
    print("\n" + "="*50)
    print("REGIME FEATURE ANALYSIS")
    print("="*50)
    
    # Extract features if available
    if 'features' in regime_df.columns and not regime_df['features'].empty:
        # Get first row with features to see structure
        sample_features = regime_df[regime_df['features'].notna()]['features'].iloc[0]
        
        if isinstance(sample_features, dict):
            feature_names = list(sample_features.keys())
            print(f"Available features: {', '.join(feature_names[:10])}")
            
            # Analyze key features
            for feature in ['momentum', 'volatility', 'volume_imbalance']:
                if feature in feature_names:
                    values = []
                    for features in regime_df['features']:
                        if isinstance(features, dict) and feature in features:
                            values.append(features[feature])
                    
                    if values:
                        print(f"\n{feature}:")
                        print(f"  Mean:   {np.mean(values):8.4f}")
                        print(f"  Std:    {np.std(values):8.4f}")
                        print(f"  Min:    {np.min(values):8.4f}")
                        print(f"  Max:    {np.max(values):8.4f}")
    
    # 5. Trading Opportunity Analysis
    print("\n" + "="*50)
    print("TRADING OPPORTUNITY ANALYSIS")
    print("="*50)
    
    trades = results.get('trades', [])
    print(f"Total trades generated: {len(trades)}")
    
    if trades:
        # Analyze trades by regime
        trades_by_regime = Counter(trade.get('regime', 'UNKNOWN') for trade in trades)
        print("\nTrades by regime:")
        for regime, count in trades_by_regime.most_common():
            print(f"  {regime:20s}: {count:4d}")
    
    # 6. Threshold Recommendations
    print("\n" + "="*50)
    print("THRESHOLD RECOMMENDATIONS")
    print("="*50)
    
    # Based on the analysis, suggest threshold adjustments
    if total_states > 0:
        chop_percentage = state_counts.get('CHOP', 0) / total_states * 100
        
        if chop_percentage > 60:
            print("⚠️  High CHOP percentage - thresholds may be too restrictive")
            print("   Consider:")
            print("   - Reducing momentum thresholds by 20-30%")
            print("   - Reducing volatility thresholds by 10-20%")
        elif chop_percentage < 20:
            print("⚠️  Low CHOP percentage - thresholds may be too loose")
            print("   Consider:")
            print("   - Increasing momentum thresholds by 20-30%")
            print("   - Increasing volatility thresholds by 10-20%")
        else:
            print("✅ CHOP percentage looks reasonable")
    
    if len(trades) == 0:
        print("\n❌ No trades generated - likely issues:")
        print("   1. Regime thresholds too restrictive")
        print("   2. Strategy filters too strict")
        print("   3. Risk management blocking trades")
    elif len(trades) < 50:
        print(f"\n⚠️  Low trade count ({len(trades)}) - consider:")
        print("   1. Slightly reducing regime thresholds")
        print("   2. Checking min_regime_confidence setting")
    elif len(trades) > 500:
        print(f"\n⚠️  High trade count ({len(trades)}) - consider:")
        print("   1. Increasing regime thresholds")
        print("   2. Adding additional filters")
    else:
        print(f"\n✅ Trade count ({len(trades)}) looks reasonable")
    
    # Save detailed results
    output_file = f"regime_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    analysis_results = {
        'period': {
            'start': start_date.isoformat(),
            'end': end_date.isoformat()
        },
        'state_distribution': state_counts.to_dict() if not state_counts.empty else {},
        'total_regime_updates': len(regime_history),
        'total_trades': len(trades),
        'recommendations': {
            'chop_percentage': chop_percentage if 'chop_percentage' in locals() else 0,
            'suggested_actions': []
        }
    }
    
    with open(output_file, 'w') as f:
        json.dump(analysis_results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to: {output_file}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Analyze modern regime patterns for threshold calibration"
    )
    
    parser.add_argument(
        '--start-date',
        type=str,
        default='2024-01-01',
        help='Start date (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--end-date',
        type=str,
        default='2024-01-07',
        help='End date (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        default='configs/overrides/modern_system.yaml',
        help='Config file path'
    )
    
    args = parser.parse_args()
    
    # Parse dates
    try:
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
    except ValueError as e:
        print(f"Invalid date format: {e}")
        sys.exit(1)
    
    # Run analysis
    analyze_regime_patterns(start_date, end_date, args.config)


if __name__ == '__main__':
    main()