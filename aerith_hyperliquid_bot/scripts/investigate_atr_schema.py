#!/usr/bin/env python3
"""
ATR Schema Investigation Script
Analyzes data schemas across different file types to identify ATR column inconsistencies.
"""

import pandas as pd
import numpy as np
import os
import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def analyze_parquet_schema(file_path, file_type="unknown"):
    """Analyze schema of a parquet file, focusing on ATR columns."""
    try:
        df = pd.read_parquet(file_path)
        
        # Find ATR-related columns
        atr_columns = [col for col in df.columns if 'atr' in col.lower()]
        
        result = {
            'file_path': file_path,
            'file_type': file_type,
            'total_columns': len(df.columns),
            'total_rows': len(df),
            'atr_columns': atr_columns,
            'schema_info': {}
        }
        
        # Analyze each ATR column
        for col in atr_columns:
            if col in df.columns:
                values = df[col].dropna()
                if len(values) > 0:
                    result['schema_info'][col] = {
                        'min': float(values.min()),
                        'max': float(values.max()),
                        'mean': float(values.mean()),
                        'median': float(values.median()),
                        'std': float(values.std()),
                        'nan_count': int(df[col].isna().sum()),
                        'nan_ratio': float(df[col].isna().sum() / len(df)),
                        'sample_values': values.head(5).tolist()
                    }
                else:
                    result['schema_info'][col] = {'status': 'all_nan'}
        
        return result
        
    except Exception as e:
        return {
            'file_path': file_path,
            'file_type': file_type,
            'error': str(e)
        }

def investigate_data_schemas():
    """Investigate schemas across different data file types."""
    
    data_root = Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data")
    
    results = []
    
    print("🔍 ATR Schema Investigation")
    print("=" * 50)
    
    # 1. Check features_1s files (modern system)
    print("\n📁 Analyzing features_1s files (Modern System)...")
    features_dir = data_root / "features_1s"
    if features_dir.exists():
        # Check a few sample files from different dates
        sample_dates = ["2025-03-02", "2025-03-05", "2025-03-10"]
        for date in sample_dates:
            date_dir = features_dir / date
            if date_dir.exists():
                sample_file = date_dir / "features_00.parquet"
                if sample_file.exists():
                    print(f"  Analyzing: {sample_file}")
                    result = analyze_parquet_schema(sample_file, "features_1s")
                    results.append(result)
                    break  # Just analyze one file per date
    
    # 2. Check raw2 files (legacy system)
    print("\n📁 Analyzing raw2 files (Legacy System)...")
    raw2_dir = data_root / "raw2"
    if raw2_dir.exists():
        # Check a few sample files
        raw2_files = list(raw2_dir.glob("*.parquet"))[:3]
        for file_path in raw2_files:
            print(f"  Analyzing: {file_path}")
            result = analyze_parquet_schema(file_path, "raw2")
            results.append(result)
    
    # 3. Check resampled_l2 files (legacy system)
    print("\n📁 Analyzing resampled_l2 files (Legacy System)...")
    resampled_dir = data_root / "resampled_l2" / "1h"
    if resampled_dir.exists():
        # Check a few sample files
        resampled_files = list(resampled_dir.glob("*.parquet"))[:3]
        for file_path in resampled_files:
            print(f"  Analyzing: {file_path}")
            result = analyze_parquet_schema(file_path, "resampled_l2_1h")
            results.append(result)
    
    return results

def print_analysis_results(results):
    """Print detailed analysis results."""
    
    print("\n" + "=" * 80)
    print("📊 ANALYSIS RESULTS")
    print("=" * 80)
    
    # Group results by file type
    by_type = {}
    for result in results:
        file_type = result.get('file_type', 'unknown')
        if file_type not in by_type:
            by_type[file_type] = []
        by_type[file_type].append(result)
    
    for file_type, type_results in by_type.items():
        print(f"\n🗂️  {file_type.upper()} FILES")
        print("-" * 40)
        
        for result in type_results:
            if 'error' in result:
                print(f"❌ ERROR: {result['file_path']}")
                print(f"   {result['error']}")
                continue
                
            print(f"\n📄 {Path(result['file_path']).name}")
            print(f"   Columns: {result['total_columns']}, Rows: {result['total_rows']:,}")
            print(f"   ATR Columns: {result['atr_columns']}")
            
            # Detailed ATR column analysis
            for col, info in result['schema_info'].items():
                if 'status' in info:
                    print(f"   🔸 {col}: {info['status']}")
                else:
                    print(f"   🔸 {col}:")
                    print(f"      Range: {info['min']:.6f} to {info['max']:.6f}")
                    print(f"      Mean: {info['mean']:.6f}, Median: {info['median']:.6f}")
                    print(f"      NaN: {info['nan_count']:,} ({info['nan_ratio']:.1%})")
                    print(f"      Sample: {[f'{v:.6f}' for v in info['sample_values'][:3]]}")

def detect_unit_conversion_bugs(results):
    """Detect potential unit conversion bugs between ATR columns."""
    
    print("\n" + "=" * 80)
    print("🚨 UNIT CONVERSION BUG DETECTION")
    print("=" * 80)
    
    issues_found = []
    
    for result in results:
        if 'error' in result or not result['schema_info']:
            continue
            
        file_path = result['file_path']
        schema_info = result['schema_info']
        
        # Check for multiple ATR columns in same file
        atr_cols = list(schema_info.keys())
        if len(atr_cols) > 1:
            print(f"\n📄 {Path(file_path).name}")
            print(f"   Found multiple ATR columns: {atr_cols}")
            
            # Compare values between columns
            for i, col1 in enumerate(atr_cols):
                for col2 in atr_cols[i+1:]:
                    info1 = schema_info[col1]
                    info2 = schema_info[col2]
                    
                    if 'mean' in info1 and 'mean' in info2:
                        ratio = info1['mean'] / info2['mean'] if info2['mean'] != 0 else float('inf')
                        
                        print(f"   🔍 {col1} vs {col2}:")
                        print(f"      Mean ratio: {ratio:.1f}x")
                        print(f"      {col1} mean: {info1['mean']:.6f}")
                        print(f"      {col2} mean: {info2['mean']:.6f}")
                        
                        # Flag potential unit conversion bugs
                        if abs(ratio - 100) < 5:  # Within 5 of 100x
                            issue = f"🚨 POTENTIAL 100x UNIT BUG: {col1} vs {col2} in {Path(file_path).name}"
                            print(f"      {issue}")
                            issues_found.append(issue)
                        elif ratio > 10 or ratio < 0.1:
                            issue = f"⚠️  LARGE SCALE DIFFERENCE: {col1} vs {col2} in {Path(file_path).name} ({ratio:.1f}x)"
                            print(f"      {issue}")
                            issues_found.append(issue)
        
        # Check for unrealistic ATR values
        for col, info in schema_info.items():
            if 'max' in info:
                if info['max'] > 0.1:  # 10% ATR is very high for Bitcoin
                    issue = f"⚠️  UNREALISTIC ATR VALUES: {col} max={info['max']:.3f} in {Path(file_path).name}"
                    print(f"\n   {issue}")
                    issues_found.append(issue)
    
    if issues_found:
        print(f"\n🚨 SUMMARY: {len(issues_found)} potential issues detected")
        for issue in issues_found:
            print(f"   • {issue}")
    else:
        print("\n✅ No obvious unit conversion bugs detected")

def main():
    """Main investigation function."""
    
    print("Starting ATR Schema Investigation...")
    
    # Run the investigation
    results = investigate_data_schemas()
    
    # Print results
    print_analysis_results(results)
    
    # Detect bugs
    detect_unit_conversion_bugs(results)
    
    print(f"\n✅ Investigation complete. Analyzed {len(results)} files.")

if __name__ == "__main__":
    main()
