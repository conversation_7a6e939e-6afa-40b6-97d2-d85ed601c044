#!/usr/bin/env python3
"""
Check what columns are available in enhanced hourly data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from pathlib import Path

# Check enhanced hourly data
enhanced_path = Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/enhanced_hourly/1h")

# Read a sample file
sample_file = enhanced_path / "2024-04-01_1h_enhanced.parquet"

if sample_file.exists():
    df = pd.read_parquet(sample_file)
    print("Enhanced hourly data columns:")
    for col in sorted(df.columns):
        # Get sample value
        sample = df[col].iloc[0] if len(df) > 0 else "N/A"
        print(f"  - {col}: {sample}")
    
    print(f"\nShape: {df.shape}")
    print(f"Index: {df.index.name}")
    
    # Check if ATR columns exist
    atr_cols = [col for col in df.columns if 'atr' in col.lower()]
    if atr_cols:
        print(f"\nATR columns found: {atr_cols}")
        for col in atr_cols:
            print(f"  {col}: min={df[col].min():.4f}, max={df[col].max():.4f}, nulls={df[col].isna().sum()}")
else:
    print(f"File not found: {sample_file}")