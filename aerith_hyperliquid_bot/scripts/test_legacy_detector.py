#!/usr/bin/env python3
"""
Test with legacy detector only (no enhanced detector)
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
import logging
import json

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_backtest_engine import RobustBacktestEngine

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Suppress verbose loggers
for logger_name in ['ModernDataLoader', 'ModernDataAdapter', 'ModernContinuousDetectorV2']:
    logging.getLogger(logger_name).setLevel(logging.WARNING)

print("=" * 60)
print("TESTING WITH LEGACY DETECTOR")
print("=" * 60)

# Load config
config = load_config('configs/overrides/modern_system_v2_complete.yaml')

# Force to use legacy detector
config.regime.detector_type = 'continuous_modern_v2'  # Use regular detector, not enhanced

# Test period (April 2024 - 1 week)
start_date = datetime(2024, 4, 1)
end_date = datetime(2024, 4, 8)

print(f"Test period: {start_date} to {end_date}")
print(f"Detector type: {config.regime.detector_type}")
print("=" * 60)

# Create backtest engine
engine = RobustBacktestEngine(
    config=config,
    start_date=start_date,
    end_date=end_date,
    use_regime_cache=False  # Don't use cache
)

# Run backtest
print("\nRunning backtest...")
results = engine.run_backtest()

# Print results
print("\n" + "=" * 60)
print("RESULTS")
print("=" * 60)
print(f"Total trades: {results['total_trades']}")
print(f"Winning trades: {results['winning_trades']}")
print(f"Losing trades: {results['losing_trades']}")
print(f"Win rate: {results['win_rate']:.2%}")
print(f"Total return: {results['total_return']:.2%}")
print(f"Sharpe ratio: {results['sharpe_ratio']:.2f}")
print(f"Max drawdown: {results['max_drawdown']:.2%}")

# Show first few trades
if results['trades']:
    print("\nFirst 5 trades:")
    for i, trade in enumerate(results['trades'][:5]):
        print(f"  {i+1}. {trade['direction']} @ {trade['entry_price']:.2f} "
              f"({trade['entry_time']}) -> P&L: {trade.get('pnl_pct', 0):.2%}")

# Save results
output_file = "test_legacy_detector_results.json"
with open(output_file, 'w') as f:
    json.dump(results, f, indent=2, default=str)
print(f"\nResults saved to: {output_file}")