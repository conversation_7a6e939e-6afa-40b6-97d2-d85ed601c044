#!/usr/bin/env python3
"""
Easy Modern Backtest Runner
===========================
Simple wrapper to run modern system backtest with the correct settings.
Uses modern_system_v2_complete.yaml by default.
"""

import subprocess
import sys
import argparse
from pathlib import Path
from datetime import datetime, timedelta

def main():
    parser = argparse.ArgumentParser(
        description="Run modern backtest easily",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Quick test (1 day)
  python3 scripts/run_modern_easy.py
  
  # Test 1 week
  python3 scripts/run_modern_easy.py --days 7
  
  # Test specific date range
  python3 scripts/run_modern_easy.py --start 2024-01-01 --end 2024-01-31
  
  # Use different config
  python3 scripts/run_modern_easy.py --config modern_calibrated.yaml
  
  # Debug mode
  python3 scripts/run_modern_easy.py --debug
        """
    )
    
    parser.add_argument(
        '--days', 
        type=int, 
        default=1,
        help='Number of days to test (default: 1)'
    )
    
    parser.add_argument(
        '--start',
        type=str,
        help='Start date (YYYY-MM-DD). Overrides --days'
    )
    
    parser.add_argument(
        '--end',
        type=str,
        help='End date (YYYY-MM-DD). Required if --start is used'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        default='modern_system_v2_complete.yaml',
        help='Config file name in configs/overrides/ (default: modern_system_v2_complete.yaml)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug output'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Determine date range
    if args.start and args.end:
        start_date = args.start
        end_date = args.end
    else:
        # Use default or days-based range
        start = datetime(2024, 1, 1)
        end = start + timedelta(days=args.days)
        start_date = start.strftime('%Y-%m-%d')
        end_date = end.strftime('%Y-%m-%d')
    
    # Build command
    cmd = [
        sys.executable,
        'scripts/run_modern_backtest.py',
        '--start-date', start_date,
        '--end-date', end_date,
        '--override', f'configs/overrides/{args.config}'
    ]
    
    if args.verbose:
        cmd.append('--verbose')
    
    # Print what we're doing
    print("=" * 80)
    print("MODERN SYSTEM BACKTEST - EASY RUNNER")
    print("=" * 80)
    print(f"Date range: {start_date} to {end_date}")
    print(f"Config: {args.config}")
    print(f"Debug: {args.debug}")
    print()
    
    if args.debug:
        print("Command:", ' '.join(cmd))
        print()
    
    # Check if config exists
    config_path = Path('configs/overrides') / args.config
    if not config_path.exists():
        print(f"❌ Config file not found: {config_path}")
        print("\nAvailable configs:")
        for f in sorted(Path('configs/overrides').glob('modern*.yaml')):
            print(f"  - {f.name}")
        sys.exit(1)
    
    print("Starting backtest...")
    print("-" * 80)
    
    try:
        # Run the backtest
        result = subprocess.run(cmd, check=True)
        
        print("-" * 80)
        print("✅ Backtest completed successfully!")
        
        # Check if results file exists
        results_file = Path('modern_backtest_results.json')
        if results_file.exists():
            print(f"\nResults saved to: {results_file}")
            
            # Quick summary
            import json
            with open(results_file) as f:
                results = json.load(f)
            
            perf = results.get('performance', {})
            print("\nQuick Summary:")
            print(f"  - Total Trades: {perf.get('total_trades', 0)}")
            print(f"  - Total Return: {perf.get('total_return', 0):.2%}")
            print(f"  - Win Rate: {perf.get('win_rate', 0):.2%}")
    
    except subprocess.CalledProcessError as e:
        print("-" * 80)
        print(f"❌ Backtest failed with exit code: {e.returncode}")
        
        print("\nTroubleshooting tips:")
        print("1. Check if data files exist for the date range")
        print("2. Try a shorter date range (--days 1)")
        print("3. Enable debug mode (--debug)")
        print("4. Check logs/ directory for error details")
        sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n\n⚠️  Backtest interrupted by user")
        sys.exit(1)


if __name__ == "__main__":
    main()