#!/usr/bin/env python3
"""
Debug Hourly Features Data
==========================

Check what columns are actually returned by load_hourly_features.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.data_loader import ModernDataLoader


def main():
    print("=== Debugging Hourly Features Data ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Create data loader
    data_loader = ModernDataLoader(config)
    
    # Test loading hourly features
    end_time = datetime(2024, 1, 15, 22, 0, 0)
    start_time = end_time - timedelta(hours=72)  # 72h lookback
    
    print(f"Loading hourly features from {start_time} to {end_time}...")
    
    hourly_data = data_loader.load_hourly_features(start_time, end_time)
    
    print(f"\nData shape: {hourly_data.shape}")
    print(f"Columns: {list(hourly_data.columns)}")
    
    # Check for required OHLCV columns
    required_ohlcv = ['open', 'high', 'low', 'close', 'volume']
    missing_ohlcv = [col for col in required_ohlcv if col not in hourly_data.columns]
    
    if missing_ohlcv:
        print(f"\n❌ MISSING OHLCV columns: {missing_ohlcv}")
        print("This explains why indicators can't be calculated!")
    else:
        print(f"\n✅ All OHLCV columns present")
    
    # Check for feature columns
    feature_cols = ['volume_imbalance', 'spread_mean', 'atr_14_sec', 'atr_percent_sec']
    available_features = [col for col in feature_cols if col in hourly_data.columns]
    
    print(f"\nAvailable features: {available_features}")
    
    if not hourly_data.empty:
        print(f"\nSample data (last 5 rows):")
        print(hourly_data.tail())
        
        # Check for NaN values
        nan_counts = hourly_data.isna().sum()
        cols_with_nan = nan_counts[nan_counts > 0]
        if not cols_with_nan.empty:
            print(f"\nColumns with NaN values:")
            print(cols_with_nan)
    
    # Now test what ModernSignalEngine would see
    from hyperliquid_bot.modern.signal_engine import ModernSignalEngine
    
    signal_engine = ModernSignalEngine(config)
    
    if not hourly_data.empty and len(hourly_data) > 1:
        print("\n=== Testing Signal Engine ===")
        
        # Try to calculate signals
        regime_features = {
            'current_state': 'Weak_Bear_Trend',
            'current_confidence': 0.8,
            'state_persistence': 0.7,
            'recent_transitions': 2,
            'risk_suppressed': False
        }
        
        try:
            signals = signal_engine.calculate_signals(hourly_data, regime_features)
            
            print(f"\nSignal calculation successful!")
            print(f"Signal keys: {list(signals.keys())}")
            
            # Check for indicators
            indicator_fields = ['ema_fast', 'ema_slow', 'atr_14']
            for field in indicator_fields:
                value = signals.get(field, 'MISSING')
                print(f"  {field}: {value}")
            
        except Exception as e:
            print(f"\n❌ Signal calculation failed: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()