#!/bin/bash
# <PERSON>ript to run a backtest for the period 2025-03-01 to 2025-03-22

# Set paths
FEATURE_DIR="hyperliquid_data/features_1s"
OHLC_DIR="hyperliquid_data/resampled_l2"
CONFIG_PATH="aerith_hyperliquid_bot/configs/base.yaml"
OUTPUT_PATH="results/tf3_backtest_20250301_to_20250322.json"

# Set date range
START_DATE="2025-03-01"
END_DATE="2025-03-22"

echo "Running backtest from $START_DATE to $END_DATE..."
echo "Feature directory: $FEATURE_DIR"
echo "OHLC directory: $OHLC_DIR"
echo "Config path: $CONFIG_PATH"
echo "Output path: $OUTPUT_PATH"

# Run the backtest
python -m aerith_hyperliquid_bot.scripts.smoke_test_tf_v3 \
    --start "$START_DATE" \
    --end "$END_DATE" \
    --config "$CONFIG_PATH" \
    --feature-dir "$FEATURE_DIR" \
    --ohlc-dir "$OHLC_DIR" \
    --output "$OUTPUT_PATH"

# Check exit status
if [ $? -eq 0 ]; then
    echo "✅ Backtest passed! TF-v3 executed at least one trade."
    
    # Display trade count and PnL from the output file
    echo "Extracting trade information from $OUTPUT_PATH..."
    
    # Use Python to extract and display trade information
    python -c "
import json
import sys

try:
    with open('$OUTPUT_PATH', 'r') as f:
        results = json.load(f)
    
    # Get trade count
    trade_count = 0
    total_pnl = 0
    
    for strategy_name, strategy_results in results.get('strategies', {}).items():
        if strategy_name == 'tf_v3':
            trade_count = strategy_results.get('trade_count', 0)
            
    # Get PnL from trades
    for trade in results.get('trades', []):
        if trade.get('strategy') == 'tf_v3':
            pnl = trade.get('pnl', 0)
            total_pnl += pnl
    
    print(f'TF-v3 executed {trade_count} trades with total PnL: {total_pnl:.2f}')
    
    # Print first 5 trades
    print('\\nSample trades:')
    for i, trade in enumerate(results.get('trades', [])[:5]):
        if trade.get('strategy') == 'tf_v3':
            entry_time = trade.get('entry_time', 'Unknown')
            exit_time = trade.get('exit_time', 'Unknown')
            direction = trade.get('direction', 'Unknown')
            pnl = trade.get('pnl', 0)
            print(f'  Trade {i+1}: {direction} from {entry_time} to {exit_time}, PnL: {pnl:.2f}')
    
except Exception as e:
    print(f'Error processing results: {e}')
    sys.exit(1)
"
else
    echo "❌ Backtest failed! TF-v3 did not execute any trades."
    exit 1
fi

echo "Task completed successfully."
