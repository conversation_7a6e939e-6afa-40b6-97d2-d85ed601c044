#!/usr/bin/env python3
"""
Simple script to run a backtest with the fixed OHLCV data.
This script directly uses the HistoricalDataHandler to load data and verify 
that log_ret and realised_vol columns are properly included.
"""

import sys
import logging
from pathlib import Path
from datetime import datetime, <PERSON><PERSON><PERSON>

# Add the project root to the Python path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

# Import necessary components
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.data.handler import HistoricalDataHandler

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(name)s: %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run a simple backtest to verify the OHLCV data."""
    try:
        # Load configuration
        config_path = project_root / 'config.yaml'
        if not config_path.exists():
            logger.error(f"Config file not found: {config_path}")
            return False
        
        config = load_config(str(config_path))
        logger.info(f"Configuration loaded from: {config_path}")
        
        # Set date range from config or use defaults
        start_date = config.backtest.custom_start_date
        end_date = config.backtest.custom_end_date
        
        # Check if dates are already datetime objects
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, "%Y-%m-%d")
        
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, "%Y-%m-%d")
        
        # Add 1 day to include the end date
        end_date = end_date + timedelta(days=1)
        
        logger.info(f"Using date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        
        # Initialize data handler
        data_handler = HistoricalDataHandler(config)
        logger.info("Data handler initialized.")
        
        # Load historical data
        logger.info(f"Loading historical data from {start_date} to {end_date}...")
        data_handler.load_historical_data(start_date=start_date, end_date=end_date)
        
        # Access the data directly from the data_handler
        ohlcv_data = data_handler.ohlcv_data
        
        if ohlcv_data is None or ohlcv_data.empty:
            logger.error("Failed to load OHLCV data.")
            return False
        
        logger.info(f"Successfully loaded OHLCV data. Shape: {ohlcv_data.shape}")
        logger.info(f"Columns in data: {ohlcv_data.columns.tolist()}")
        
        # Check for log_ret and realised_vol columns
        required_columns = ['log_ret', 'realised_vol']
        missing_columns = [col for col in required_columns if col not in ohlcv_data.columns]
        
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            return False
        
        logger.info("All required columns are present in the OHLCV data.")
        
        # Print some statistics about the log_ret and realised_vol columns
        logger.info(f"log_ret NaN count: {ohlcv_data['log_ret'].isna().sum()} / {len(ohlcv_data)}")
        logger.info(f"realised_vol NaN count: {ohlcv_data['realised_vol'].isna().sum()} / {len(ohlcv_data)}")
        
        # Display first few rows of data
        logger.info("\nFirst 5 rows of data:")
        print(ohlcv_data.head(5)[['open', 'high', 'low', 'close', 'log_ret', 'realised_vol']])
        
        return True
    
    except Exception as e:
        logger.error(f"Error during backtest: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
