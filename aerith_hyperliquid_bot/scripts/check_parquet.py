#!/usr/bin/env python3
"""
Simple script to check the contents of a Parquet file.
"""

import sys
import pandas as pd
from pathlib import Path
import pyarrow.parquet as pq

def check_parquet(file_path):
    """Check the contents of a Parquet file."""
    try:
        print(f"\n{'='*50}")
        print(f"EXAMINING FILE: {file_path}")
        print(f"{'='*50}")
        
        # First, examine the raw file metadata
        metadata = pq.read_metadata(file_path)
        print(f"File version: {metadata.format_version}")
        print(f"Number of row groups: {metadata.num_row_groups}")
        print(f"Number of rows: {metadata.num_rows}")
        print(f"Number of columns: {metadata.num_columns}")
        print("Column names from metadata:")
        for i in range(metadata.num_columns):
            print(f"  - {metadata.schema.names[i]}")
        
        # Read the Parquet file
        df = pd.read_parquet(file_path)
        
        # Print basic information
        print("\nDataFrame Info:")
        print(f"Shape: {df.shape}")
        print(f"Columns: {df.columns.tolist()}")
        print(f"Index type: {type(df.index)}")
        
        # Check for specific columns
        required_columns = ['open', 'high', 'low', 'close', 'log_ret', 'realised_vol']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"\nMissing required columns: {missing_columns}")
        else:
            print("\nAll required columns are present!")
            
        # Show sample data
        print("\nSample data (first 5 rows):")
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', 120)
        print(df.head(5))
        
        # Check for NaN values in log_ret and realised_vol
        if 'log_ret' in df.columns:
            nan_log_ret = df['log_ret'].isna().sum()
            print(f"\nNaN values in log_ret: {nan_log_ret} out of {len(df)} rows ({nan_log_ret/len(df)*100:.1f}%)")
        
        if 'realised_vol' in df.columns:
            nan_real_vol = df['realised_vol'].isna().sum()
            print(f"NaN values in realised_vol: {nan_real_vol} out of {len(df)} rows ({nan_real_vol/len(df)*100:.1f}%)")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python check_parquet.py <parquet_file_path>")
        sys.exit(1)
    
    file_path = Path(sys.argv[1])
    if not file_path.exists():
        print(f"Error: File not found: {file_path}")
        sys.exit(1)
    
    check_parquet(file_path)
