#!/usr/bin/env python3
"""
Convert raw JSON/TXT L2 book files to Arrow format.

This script scans for .txt files in the old format (e.g., /l2_raw/20250301/BTC_0_l2Book.txt)
and converts them to the new Arrow format (e.g., /l2_raw/2025-03-01/BTC_00_l2Book.arrow).
"""

import os
import sys
import json
import argparse
from pathlib import Path
from datetime import datetime
import pandas as pd
import numpy as np
import pyarrow as pa
import pyarrow.ipc as ipc
import logging

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def parse_date_from_path(file_path: str) -> str:
    """
    Parse date from old format path.

    Args:
        file_path: Path like /l2_raw/20250301/BTC_0_l2Book.txt

    Returns:
        Date string in YYYY-MM-DD format
    """
    path_parts = Path(file_path).parts
    for part in path_parts:
        if len(part) == 8 and part.isdigit():
            # Convert YYYYMMDD to YYYY-MM-DD
            year = part[:4]
            month = part[4:6]
            day = part[6:8]
            return f"{year}-{month}-{day}"
    raise ValueError(f"Could not parse date from path: {file_path}")


def parse_hour_from_filename(filename: str) -> int:
    """
    Parse hour from filename.

    Args:
        filename: Filename like BTC_0_l2Book.txt or BTC_23_l2Book.txt

    Returns:
        Hour as integer (0-23)
    """
    # Extract hour from filename like BTC_0_l2Book.txt
    parts = filename.split('_')
    if len(parts) >= 2:
        try:
            hour = int(parts[1])
            return hour
        except ValueError:
            pass
    raise ValueError(f"Could not parse hour from filename: {filename}")


def parse_hyperliquid_l2_line(line_data: dict) -> dict:
    """
    Parse a single line of Hyperliquid L2 book data.

    Args:
        line_data: Parsed JSON data from one line

    Returns:
        Dictionary with standardized L2 book data
    """
    try:
        # Extract timestamp from the nested structure
        # Format: {"time": "2025-03-01T02:00:01.087748816", "raw": {"data": {"time": 1740794399921, "levels": [...]}}}

        # Get timestamp in milliseconds from raw.data.time
        timestamp_ms = line_data['raw']['data']['time']

        # Get levels data: levels[0] = bids, levels[1] = asks
        levels = line_data['raw']['data']['levels']
        bid_levels = levels[0] if len(levels) > 0 else []
        ask_levels = levels[1] if len(levels) > 1 else []

        # Convert levels to numpy arrays of [price, size]
        bids = []
        asks = []

        for level in bid_levels:
            price = float(level['px'])
            size = float(level['sz'])
            bids.append([price, size])

        for level in ask_levels:
            price = float(level['px'])
            size = float(level['sz'])
            asks.append([price, size])

        # Get best bid and ask
        best_bid = bids[0][0] if bids else 0.0
        best_ask = asks[0][0] if asks else 0.0

        return {
            'timestamp': timestamp_ms,
            'best_bid': best_bid,
            'best_ask': best_ask,
            'bids': bids,
            'asks': asks
        }

    except (KeyError, IndexError, ValueError, TypeError) as e:
        logger.warning(f"Failed to parse L2 line: {e}")
        return None


def convert_txt_to_arrow(txt_file: str, arrow_file: str, overwrite: bool = False) -> bool:
    """
    Convert a single TXT file to Arrow format.

    Args:
        txt_file: Path to input TXT file
        arrow_file: Path to output Arrow file
        overwrite: Whether to overwrite existing files

    Returns:
        True if successful, False otherwise
    """
    if os.path.exists(arrow_file) and not overwrite:
        logger.info(f"Arrow file already exists, skipping: {arrow_file}")
        return True

    try:
        logger.info(f"Converting {txt_file} -> {arrow_file}")

        # Read and parse JSON lines from TXT file
        parsed_rows = []
        with open(txt_file, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:
                    try:
                        # Parse JSON line
                        line_data = json.loads(line)

                        # Parse Hyperliquid L2 format
                        parsed_data = parse_hyperliquid_l2_line(line_data)
                        if parsed_data:
                            parsed_rows.append(parsed_data)

                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse JSON line {line_num}: {e}")
                        continue
                    except Exception as e:
                        logger.warning(f"Failed to process line {line_num}: {e}")
                        continue

        if not parsed_rows:
            logger.warning(f"No valid data found in {txt_file}")
            return False

        # Convert to DataFrame
        df = pd.DataFrame(parsed_rows)

        # Add 'ts' column (convert timestamp to datetime)
        df['ts'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)

        # Ensure timestamp is timezone-naive UTC for consistency
        if df['ts'].dt.tz is not None:
            df['ts'] = df['ts'].dt.tz_convert('UTC').dt.tz_localize(None)

        # Convert bids/asks to lists of lists for Arrow compatibility
        # Arrow will handle the conversion to appropriate array format
        df['bids'] = df['bids'].apply(lambda x: x if isinstance(x, list) else [])
        df['asks'] = df['asks'].apply(lambda x: x if isinstance(x, list) else [])

        # Create output directory
        os.makedirs(os.path.dirname(arrow_file), exist_ok=True)

        # Convert to Arrow table and save
        table = pa.Table.from_pandas(df)

        with open(arrow_file, 'wb') as f:
            writer = ipc.new_file(f, table.schema)
            writer.write_table(table)
            writer.close()

        logger.info(f"Successfully converted {len(df)} rows to {arrow_file}")
        return True

    except Exception as e:
        logger.error(f"Error converting {txt_file}: {e}")
        return False


def scan_and_convert(base_dir: str, overwrite: bool = False) -> None:
    """
    Scan for TXT files and convert them to Arrow format.

    Args:
        base_dir: Base directory to scan for TXT files
        overwrite: Whether to overwrite existing Arrow files
    """
    base_path = Path(base_dir)

    # Find all TXT files
    txt_files = list(base_path.glob("**/*_l2Book.txt"))

    logger.info(f"Found {len(txt_files)} TXT files to convert")

    success_count = 0
    error_count = 0

    for txt_file in txt_files:
        try:
            # Parse date and hour
            date_str = parse_date_from_path(str(txt_file))
            hour = parse_hour_from_filename(txt_file.name)

            # Create new Arrow file path
            arrow_dir = base_path / date_str
            arrow_filename = f"BTC_{hour:02d}_l2Book.arrow"
            arrow_file = arrow_dir / arrow_filename

            # Convert file
            if convert_txt_to_arrow(str(txt_file), str(arrow_file), overwrite):
                success_count += 1
            else:
                error_count += 1

        except Exception as e:
            logger.error(f"Error processing {txt_file}: {e}")
            error_count += 1

    logger.info(f"Conversion complete: {success_count} successful, {error_count} errors")


def main():
    parser = argparse.ArgumentParser(description="Convert TXT L2 book files to Arrow format")
    parser.add_argument("--base-dir", default="/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/l2_raw",
                        help="Base directory containing TXT files")
    parser.add_argument("--overwrite", action="store_true",
                        help="Overwrite existing Arrow files")

    args = parser.parse_args()

    if not os.path.exists(args.base_dir):
        logger.error(f"Base directory does not exist: {args.base_dir}")
        sys.exit(1)

    scan_and_convert(args.base_dir, args.overwrite)


if __name__ == "__main__":
    main()
