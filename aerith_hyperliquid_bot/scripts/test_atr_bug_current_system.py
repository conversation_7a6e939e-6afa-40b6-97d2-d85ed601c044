#!/usr/bin/env python3
"""
Test ATR Bug in Current System
Tests the actual signal calculator to see if the 100x bug exists in runtime.
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_test_data():
    """Create test OHLCV data for signal calculation."""

    # Create 100 periods of test data
    dates = pd.date_range('2025-03-01', periods=100, freq='1h')

    # Create realistic Bitcoin price data
    base_price = 100000
    price_changes = np.random.normal(0, 0.01, 100)  # 1% volatility
    prices = [base_price]

    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)

    # Create OHLCV data
    data = []
    for date, price in zip(dates, prices):
        high = price * (1 + abs(np.random.normal(0, 0.005)))  # 0.5% high
        low = price * (1 - abs(np.random.normal(0, 0.005)))   # 0.5% low
        volume = np.random.uniform(1000, 5000)

        data.append({
            'timestamp': date,
            'open': price,
            'high': high,
            'low': low,
            'close': price,
            'volume': volume,
            'mid_price': price,
            'spread_mean': 0.5,
            'spread_std': 0.1,
            'obi_smoothed_5': 0.0,
            'bid_slope_5': 0.1,
            'ask_slope_5': -0.1,
            'book_asymmetry_5': 0.0,
        })

    df = pd.DataFrame(data)
    df = df.set_index('timestamp')

    return df

def test_atr_calculation_logic():
    """Test the ATR calculation logic directly."""

    print("🧪 Testing ATR Calculation Logic")
    print("=" * 60)

    # Create test data with atr_percent_sec
    test_data = pd.DataFrame({
        'atr_percent_sec': [0.005, 0.006, 0.007, 0.008, 0.009],  # 0.5% to 0.9%
        'close': [100000, 100500, 101000, 100800, 100200],
        'atr': [500, 600, 700, 800, 900]
    })

    print("Original test data:")
    print(f"atr_percent_sec: {test_data['atr_percent_sec'].tolist()}")

    # Test the signal calculator logic for creating atr_percent
    # This simulates the logic from lines 1325 and 1355 in calculator.py

    # Case 1: When atr_percent_sec exists but is "lower quality"
    print("\n📊 Case 1: atr_percent_sec exists (lower quality path)")
    test_data_case1 = test_data.copy()
    # This simulates line 1325: signals_df["atr_percent"] = signals_df["atr_percent_sec"] * 100
    test_data_case1["atr_percent"] = test_data_case1["atr_percent_sec"] * 100

    print(f"atr_percent_sec: {test_data_case1['atr_percent_sec'].tolist()}")
    print(f"atr_percent (x100): {test_data_case1['atr_percent'].tolist()}")

    ratio_case1 = test_data_case1['atr_percent'].mean() / test_data_case1['atr_percent_sec'].mean()
    print(f"Ratio: {ratio_case1:.1f}x")

    if abs(ratio_case1 - 100) < 5:
        print("🚨 100x UNIT CONVERSION BUG DETECTED in Case 1!")

    # Case 2: When calculating from scratch
    print("\n📊 Case 2: Calculate atr_percent from scratch")
    test_data_case2 = test_data.copy()
    # This simulates lines 1352-1355: (atr / close) * 100
    test_data_case2["atr_percent"] = (test_data_case2["atr"] / test_data_case2["close"]) * 100

    print(f"atr: {test_data_case2['atr'].tolist()}")
    print(f"close: {test_data_case2['close'].tolist()}")
    print(f"atr_percent (calculated): {test_data_case2['atr_percent'].tolist()}")

    # Compare with what it should be (without * 100)
    correct_atr_percent = test_data_case2["atr"] / test_data_case2["close"]
    print(f"Correct atr_percent (no x100): {correct_atr_percent.tolist()}")

    ratio_case2 = test_data_case2['atr_percent'].mean() / correct_atr_percent.mean()
    print(f"Ratio vs correct: {ratio_case2:.1f}x")

    if abs(ratio_case2 - 100) < 5:
        print("🚨 100x UNIT CONVERSION BUG DETECTED in Case 2!")

    # Case 3: Correct calculation (what it should be)
    print("\n📊 Case 3: Correct calculation (no x100)")
    test_data_case3 = test_data.copy()
    test_data_case3["atr_percent_correct"] = test_data_case3["atr"] / test_data_case3["close"]

    print(f"atr_percent_correct: {test_data_case3['atr_percent_correct'].tolist()}")

    # Compare with atr_percent_sec
    if 'atr_percent_sec' in test_data_case3.columns:
        ratio_case3 = test_data_case3['atr_percent_correct'].mean() / test_data_case3['atr_percent_sec'].mean()
        print(f"Ratio vs atr_percent_sec: {ratio_case3:.1f}x")

        if abs(ratio_case3 - 1) < 0.1:
            print("✅ Correct calculation matches atr_percent_sec!")
        else:
            print(f"⚠️  Unexpected ratio: {ratio_case3:.1f}x")

def test_with_feature_data():
    """Test with actual feature data that has atr_percent_sec."""

    print("\n" + "=" * 60)
    print("🧪 Testing with Actual Feature Data")
    print("=" * 60)

    # Load actual feature data
    feature_file = Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s/2025-03-02/features_06.parquet")

    if not feature_file.exists():
        print("❌ Feature file not found")
        return

    try:
        df = pd.read_parquet(feature_file)
        print(f"✅ Loaded feature data: {df.shape}")

        # Check ATR columns
        atr_columns = [col for col in df.columns if 'atr' in col.lower()]
        print(f"ATR columns: {atr_columns}")

        # Analyze existing ATR columns
        for col in atr_columns:
            if col in df.columns:
                values = df[col].dropna()
                if len(values) > 0:
                    print(f"\n📈 {col}:")
                    print(f"   Range: {values.min():.6f} to {values.max():.6f}")
                    print(f"   Mean: {values.mean():.6f}")
                    print(f"   Sample: {values.head(3).tolist()}")
                else:
                    print(f"\n📈 {col}: All NaN")

        # Check for the bug in existing data
        if 'atr_percent' in df.columns and 'atr_percent_sec' in df.columns:
            atr_pct = df['atr_percent'].dropna()
            atr_pct_sec = df['atr_percent_sec'].dropna()

            if len(atr_pct) > 0 and len(atr_pct_sec) > 0:
                ratio = atr_pct.mean() / atr_pct_sec.mean() if atr_pct_sec.mean() != 0 else float('inf')

                print(f"\n🚨 BUG CHECK (Existing Feature Data):")
                print(f"   atr_percent mean: {atr_pct.mean():.6f}")
                print(f"   atr_percent_sec mean: {atr_pct_sec.mean():.6f}")
                print(f"   Ratio: {ratio:.1f}x")

                if abs(ratio - 100) < 5:
                    print("   🚨 100x UNIT CONVERSION BUG DETECTED!")
                elif abs(ratio - 1) < 0.1:
                    print("   ✅ No unit conversion bug (values are similar)")
                else:
                    print(f"   ⚠️  Unexpected ratio: {ratio:.1f}x")
            else:
                print("\n🚨 BUG CHECK: Cannot compare - one or both columns have no data")
        else:
            print("\n🚨 BUG CHECK: Cannot compare - missing atr_percent or atr_percent_sec columns")

    except Exception as e:
        print(f"❌ Error testing with feature data: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function."""

    print("Starting ATR Bug Test...")

    # Test 1: ATR calculation logic
    test_atr_calculation_logic()

    # Test 2: Actual feature data analysis
    test_with_feature_data()

    print(f"\n✅ ATR bug testing complete.")

if __name__ == "__main__":
    main()
