#!/usr/bin/env python3
"""
Test script to verify metric extraction from log files.
"""
import os
import re
from pathlib import Path

def extract_metrics_from_log(log_file):
    """Extract metrics from log file."""
    try:
        # Extract metrics from log file
        with open(log_file, 'r') as f:
            log_content = f.read()
        
        # The log file contains ANSI escape codes for formatting, so we need to use more generic patterns
        
        # Look for Sharpe ratio using a generic pattern that works with ANSI codes
        sharpe_match = re.search(r'Sharpe Ratio.*?(\d+\.\d+)', log_content)
        if sharpe_match:
            sharpe = float(sharpe_match.group(1))
            print(f"Found Sharpe ratio: {sharpe}")
        else:
            sharpe = None
            print("Could not find Sharpe ratio")
        
        # Look for Max Drawdown using a generic pattern that works with ANSI codes
        dd_match = re.search(r'Max Drawdown.*?(\d+\.\d+)%', log_content)
        if dd_match:
            max_dd = float(dd_match.group(1)) / -100.0  # Convert percentage to decimal and make negative
            print(f"Found Max Drawdown: {max_dd}")
        else:
            max_dd = None
            print("Could not find Max Drawdown")
        
        # Look for ROI using a generic pattern that works with ANSI codes
        roi_match = re.search(r'ROI.*?(\d+\.\d+)%', log_content)
        if roi_match:
            roi = float(roi_match.group(1)) / 100.0  # Convert percentage to decimal
            print(f"Found ROI: {roi}")
        else:
            roi = None
            print("Could not find ROI")
        
        # Look for Profit Factor using a generic pattern that works with ANSI codes
        pf_match = re.search(r'Profit Factor.*?(\d+\.\d+)', log_content)
        if pf_match:
            profit_factor = float(pf_match.group(1))
            print(f"Found Profit Factor: {profit_factor}")
        else:
            profit_factor = None
            print("Could not find Profit Factor")
        
        # Print the relevant section of the log file
        print("\nExamining a section of the log file:")
        metrics_section = re.search(r'IMPORTANT METRICS.*?(?=\n\s*\n|\Z)', log_content, re.DOTALL)
        if metrics_section:
            print(metrics_section.group(0))
            print("\nRaw representation:")
            print(repr(metrics_section.group(0)))
        else:
            print("Could not find IMPORTANT METRICS section")
        
        return sharpe, max_dd, roi, profit_factor
    
    except Exception as e:
        print(f"Error extracting metrics from log file: {e}")
        return None, None, None, None

def main():
    """Test metric extraction on the most recent log file."""
    log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    log_files = list(log_dir.glob("backtest_run_*.log"))
    
    if not log_files:
        print("No log files found")
        return
    
    # Use the most recent log file
    log_file = max(log_files, key=os.path.getmtime)
    print(f"Testing metric extraction on: {log_file}")
    
    # Extract metrics
    sharpe, max_dd, roi, profit_factor = extract_metrics_from_log(log_file)
    
    if all(x is not None for x in [sharpe, max_dd, roi, profit_factor]):
        print("\nSuccessfully extracted all metrics:")
        print(f"Sharpe: {sharpe}")
        print(f"MaxDD: {max_dd}")
        print(f"ROI: {roi}")
        print(f"Profit Factor: {profit_factor}")
        print(f"Sharpe/DD Ratio: {sharpe / abs(max_dd)}")
    else:
        print("\nFailed to extract all metrics")

if __name__ == "__main__":
    main()
