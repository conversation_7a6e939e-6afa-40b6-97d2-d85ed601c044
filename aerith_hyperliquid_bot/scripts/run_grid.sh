#!/bin/bash
# run_grid.sh - Latin-square 2x2 grid search for parameter tuning
# This script runs a 2x2 Latin-square design for parameter tuning with 16 total runs

# Create results directory if it doesn't exist
RESULTS_DIR="./grid_search_results"
mkdir -p $RESULTS_DIR

# CSV header for results
echo "OBI_THR,OBI_WIN,SPD_THR,CONF_BARS,<PERSON>,<PERSON>,<PERSON>_DD_Ratio,Tag" > $RESULTS_DIR/grid_results.csv

# Define parameter values (low/high for each parameter)
# Using the values from 4knob_tuning.yaml as reference
OBI_THR_VALUES=("0.12" "0.20")
OBI_WIN_VALUES=("6" "14") 
SPD_THR_VALUES=("0.000026" "0.000044")
CONF_BARS_VALUES=("1" "2")

# Run the 2x2 Latin-square grid search (16 total runs)
for OBI_THR in "${OBI_THR_VALUES[@]}"; do
  for OBI_WIN in "${OBI_WIN_VALUES[@]}"; do
    for SPD_THR in "${SPD_THR_VALUES[@]}"; do
      for CONF_BARS in "${CONF_BARS_VALUES[@]}"; do
        # Create a tag for this run
        TAG="gs_${OBI_THR}_${OBI_WIN}_${SPD_THR}_${CONF_BARS}"
        
        echo "Running configuration: OBI_THR=$OBI_THR, OBI_WIN=$OBI_WIN, SPD_THR=$SPD_THR, CONF_BARS=$CONF_BARS"
        
        # Export environment variables for the YAML parser
        export OBI_THR=$OBI_THR
        export OBI_WIN=$OBI_WIN
        export SPD_THR=$SPD_THR
        export CONF_BARS=$CONF_BARS
        
        # Run the backtester with the grid search override
        python hyperliquid_bot/backtester/run_backtest.py \
          --override configs/overrides/1h_grid.yaml \
          --env OBI_THR OBI_WIN SPD_THR CONF_BARS \
          --tag $TAG
        
        # Extract Sharpe ratio and max drawdown from results
        # This is a placeholder - you'll need to adapt this to your actual output format
        # For example, if results are in JSON files in a specific directory:
        RESULTS_FILE=$(find ./results -name "*${TAG}*.json" -type f -print -quit)
        
        if [ -f "$RESULTS_FILE" ]; then
          # Extract metrics (adjust these commands based on your actual output format)
          SHARPE=$(grep -o '"sharpe_ratio":[^,]*' "$RESULTS_FILE" | cut -d':' -f2)
          MAX_DD=$(grep -o '"max_drawdown":[^,]*' "$RESULTS_FILE" | cut -d':' -f2)
          
          # Calculate Sharpe/DD ratio (avoid division by zero)
          if (( $(echo "$MAX_DD != 0" | bc -l) )); then
            SHARPE_DD_RATIO=$(echo "scale=4; $SHARPE / (-1 * $MAX_DD)" | bc)
          else
            SHARPE_DD_RATIO="N/A"
          fi
          
          # Append to results CSV
          echo "$OBI_THR,$OBI_WIN,$SPD_THR,$CONF_BARS,$SHARPE,$MAX_DD,$SHARPE_DD_RATIO,$TAG" >> $RESULTS_DIR/grid_results.csv
        else
          echo "Warning: Results file not found for tag $TAG"
        fi
        
        echo "Completed run with tag: $TAG"
        echo "-----------------------------------"
      done
    done
  done
done

# Sort results by Sharpe/DD ratio and display top 5
echo "Top 5 parameter combinations by Sharpe/DD ratio:"
sort -t, -k7,7nr $RESULTS_DIR/grid_results.csv | head -n 6

echo "Grid search complete. Full results available in $RESULTS_DIR/grid_results.csv"
