#!/usr/bin/env python3
"""
Regenerate full year regime cache properly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import argparse
import logging
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, List, Any

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.continuous_detector_v2 import ModernContinuousDetectorV2
from hyperliquid_bot.modern.data_loader import ModernDataLoader
from hyperliquid_bot.modern.data_aggregator import ModernDataAggregator


class FullYearRegimePrecomputer:
    """Pre-compute regime states for full year."""
    
    def __init__(self, config_path: str = 'configs/overrides/modern_system_v2_complete.yaml'):
        self.config = load_config(config_path)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize components
        self.detector = ModernContinuousDetectorV2(self.config)
        self.data_loader = ModernDataLoader(self.config)
        self.data_aggregator = ModernDataAggregator()
        
        # Output directory
        self.output_dir = Path('data/precomputed_regimes')
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def precompute_full_year(self, year: int) -> pd.DataFrame:
        """Precompute regime states for full year."""
        start_date = datetime(year, 1, 1)
        end_date = datetime(year + 1, 1, 1)
        
        self.logger.info(f"Pre-computing regimes for full year {year}")
        
        all_results = []
        current_date = start_date
        
        # Process month by month to manage memory
        while current_date < end_date:
            month_end = current_date.replace(day=1) + timedelta(days=32)
            month_end = month_end.replace(day=1)  # First of next month
            if month_end > end_date:
                month_end = end_date
            
            self.logger.info(f"Processing {current_date.strftime('%B %Y')}...")
            
            # Load 1-second features for the month
            features_1s = self.data_loader.load_features_1s(current_date, month_end)
            
            if features_1s.empty:
                self.logger.warning(f"No data for {current_date.strftime('%B %Y')}, skipping")
                current_date = month_end
                continue
            
            # Process the month
            month_results = self._process_month(features_1s, current_date, month_end)
            all_results.extend(month_results)
            
            self.logger.info(f"Processed {len(month_results)} hours for {current_date.strftime('%B %Y')}")
            
            current_date = month_end
        
        # Convert to DataFrame
        if not all_results:
            self.logger.error("No regime states computed!")
            return pd.DataFrame()
        
        df = pd.DataFrame(all_results)
        df.set_index('timestamp', inplace=True)
        df = df.sort_index()
        
        # Remove duplicates if any
        df = df[~df.index.duplicated(keep='first')]
        
        self.logger.info(f"Computed {len(df)} hourly regime states for year {year}")
        
        return df
    
    def _process_month(self, features_1s: pd.DataFrame, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """Process one month of data."""
        hourly_states = []
        
        # Process hourly
        current_hour = start_date.replace(hour=0, minute=0, second=0)
        
        while current_hour < end_date:
            hour_end = current_hour + timedelta(hours=1)
            
            # Get all data for this hour
            hour_data = features_1s[
                (features_1s.index >= current_hour) & 
                (features_1s.index < hour_end)
            ]
            
            if hour_data.empty:
                current_hour = hour_end
                continue
            
            # Simulate minute-by-minute updates for this hour
            for minute_offset in range(60):
                minute_time = current_hour + timedelta(minutes=minute_offset)
                
                # Get data up to this minute
                minute_data = hour_data[hour_data.index <= minute_time]
                
                if len(minute_data) < 60:  # Need at least 1 minute
                    continue
                
                # Aggregate last 60 seconds
                last_60s = minute_data.tail(60)
                if len(last_60s) < 60:
                    continue
                
                # Simple aggregation
                latest_features = {
                    'timestamp': minute_time,
                    'close': last_60s['close'].iloc[-1],
                    'volume': last_60s['volume'].sum(),
                    'volume_imbalance': last_60s['volume_imbalance'].mean(),
                    'spread_mean': last_60s['spread_mean'].mean(),
                    'spread_std': last_60s['spread_std'].mean(),
                    'atr_14_sec': last_60s['atr_14_sec'].iloc[-1] if 'atr_14_sec' in last_60s else 0,
                    'atr_percent_sec': last_60s['atr_percent_sec'].iloc[-1] if 'atr_percent_sec' in last_60s else 0,
                    'ma_slope': last_60s['ma_slope'].iloc[-1] if 'ma_slope' in last_60s else 0,
                    'ma_slope_ema_30s': last_60s['ma_slope_ema_30s'].iloc[-1] if 'ma_slope_ema_30s' in last_60s else 0,
                }
                
                # Update regime detector
                regime_state = self.detector.detect_regime(latest_features, minute_time)
            
            # After processing all 60 minutes, save the hourly state
            confidence = self.detector.get_confidence()
            
            hourly_state = {
                'timestamp': current_hour,
                'regime': regime_state,
                'confidence': confidence,
                'volatility': latest_features.get('atr_percent_sec', 0.0),
                'momentum': latest_features.get('ma_slope_ema_30s', 0.0),
                'volume_imbalance': latest_features.get('volume_imbalance', 0.0),
                'spread_volatility': latest_features.get('spread_std', 0.0),
                'regime_duration_hours': 1,
                'signal_quality': 1.0,
                'risk_suppressed': False
            }
            
            hourly_states.append(hourly_state)
            current_hour = hour_end
        
        return hourly_states
    
    def save_results(self, df: pd.DataFrame, year: int):
        """Save pre-computed regime states to parquet."""
        if df.empty:
            self.logger.error("Cannot save empty DataFrame")
            return
        
        output_file = self.output_dir / f"regimes_{year}.parquet"
        
        # Save to parquet with compression
        df.to_parquet(output_file, compression='snappy')
        
        # Calculate file size
        file_size_kb = output_file.stat().st_size / 1024
        
        self.logger.info(
            f"Saved {len(df)} regime states to {output_file} "
            f"({file_size_kb:.1f} KB)"
        )
        
        # Print summary statistics
        print("\nRegime Distribution:")
        print(df['regime'].value_counts())
        print(f"\nAverage confidence: {df['confidence'].mean():.3f}")
        print(f"Date range: {df.index.min()} to {df.index.max()}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Pre-compute regime states for full year"
    )
    parser.add_argument(
        '--year', 
        type=int, 
        default=2024,
        help='Year to precompute (default: 2024)'
    )
    parser.add_argument(
        '--config',
        type=str,
        default='configs/overrides/modern_system_v2_complete.yaml',
        help='Configuration file path'
    )
    parser.add_argument(
        '--log-level',
        type=str,
        default='INFO',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='Logging level'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Suppress verbose loggers
    for logger_name in ['ModernDataLoader', 'ModernDataAdapter']:
        logging.getLogger(logger_name).setLevel(logging.WARNING)
    
    print(f"\n{'='*60}")
    print(f"FULL YEAR REGIME PRE-COMPUTATION")
    print(f"{'='*60}")
    print(f"Year: {args.year}")
    print(f"Config: {args.config}")
    print(f"{'='*60}\n")
    
    # Create pre-computer
    precomputer = FullYearRegimePrecomputer(args.config)
    
    # Process the full year
    import time
    start_time = time.time()
    
    regime_states = precomputer.precompute_full_year(args.year)
    
    if not regime_states.empty:
        precomputer.save_results(regime_states, args.year)
    
    elapsed = time.time() - start_time
    print(f"\n{'='*60}")
    print(f"Pre-computation complete in {elapsed:.1f} seconds")
    print(f"{'='*60}\n")
    

if __name__ == "__main__":
    main()