#!/usr/bin/env python3
"""
Reset the TFV3Strategy state.
"""

import os
import sys
import logging
import json

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/reset_tf_v3_state.log', mode='w')
    ]
)

logger = logging.getLogger(__name__)

def reset_tf_v3_state():
    """Reset the TFV3Strategy state."""
    # Define the state file path
    state_file = os.path.join(
        os.path.dirname(__file__),
        '..',
        'hyperliquid_bot',
        'state',
        'tf_v3_tf_v3.json'
    )
    
    # Create the state directory if it doesn't exist
    state_dir = os.path.dirname(state_file)
    if not os.path.exists(state_dir):
        os.makedirs(state_dir)
        logger.info(f"Created state directory: {state_dir}")
    
    # Define the default state
    default_state = {
        'entry_price': None,
        'entry_time': None,
        'trail_price': None,
        'position_type': None,
        'last_update_time': None,
        'last_gms_snapshot': None,
        'historical_snapshots': []
    }
    
    # Write the default state to the file
    with open(state_file, 'w') as f:
        json.dump(default_state, f, indent=2)
    
    logger.info(f"Reset TFV3Strategy state: {state_file}")
    
    return True

if __name__ == '__main__':
    logger.info("Resetting TFV3Strategy state...")
    success = reset_tf_v3_state()
    if success:
        logger.info("✅ TFV3Strategy state reset successfully!")
        sys.exit(0)
    else:
        logger.error("❌ Failed to reset TFV3Strategy state!")
        sys.exit(1)
