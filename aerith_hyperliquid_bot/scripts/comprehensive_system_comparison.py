#!/usr/bin/env python3
"""
Comprehensive comparison of Legacy vs Modern systems focusing on:
1. Position management (single vs multiple)
2. Exit logic implementation 
3. Data differences (imbalance field)
4. Regime update frequencies
5. Hidden configurations
"""

import os
import sys
import yaml
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_backtester_position_logic():
    """Compare position management between Legacy and Modern backtesters"""
    print("\n=== POSITION MANAGEMENT ANALYSIS ===")
    
    # Check Legacy backtester
    legacy_bt = "hyperliquid_bot/backtester/backtester.py"
    modern_bt = "hyperliquid_bot/modern/backtester_engine.py"
    
    print("\n1. LEGACY BACKTESTER:")
    if os.path.exists(legacy_bt):
        with open(legacy_bt, 'r') as f:
            legacy_code = f.read()
        
        # Look for position tracking
        if 'self.position' in legacy_code and not 'self.positions' in legacy_code:
            print("   ✅ Uses single position tracking (self.position)")
        elif 'self.positions' in legacy_code:
            print("   ❌ Uses multiple position tracking (self.positions)")
        
        # Check for open position check
        if 'has_position' in legacy_code or 'self.position is not None' in legacy_code:
            print("   ✅ Checks for existing position before entry")
        else:
            print("   ⚠️  No clear position check before entry")
        
        # Check exit evaluation
        if '_evaluate_exit' in legacy_code:
            print("   ✅ Has exit evaluation method")
            # Extract exit logic
            import re
            exit_methods = re.findall(r'def\s+(\w*exit\w*)\s*\(', legacy_code)
            if exit_methods:
                print(f"   Exit methods found: {', '.join(exit_methods)}")
    
    print("\n2. MODERN BACKTESTER:")
    if os.path.exists(modern_bt):
        with open(modern_bt, 'r') as f:
            modern_code = f.read()
        
        # Look for position tracking
        if 'self.current_position' in modern_code:
            print("   ✅ Uses single position tracking (self.current_position)")
        elif 'self.positions' in modern_code:
            print("   ❌ Uses multiple position tracking (self.positions)")
        
        # Check exit evaluation
        if '_evaluate_position_exit' in modern_code:
            print("   ✅ Has exit evaluation method")
        else:
            print("   ❌ Missing exit evaluation")

def check_data_differences():
    """Check for imbalance field and other data differences"""
    print("\n\n=== DATA STRUCTURE ANALYSIS ===")
    
    # Find sample files
    data_dirs = {
        'legacy': '/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw2/BTC',
        'modern': '/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s/BTC'
    }
    
    for system, base_path in data_dirs.items():
        print(f"\n{system.upper()} DATA:")
        
        # Try to find any parquet file
        sample_file = None
        for root, dirs, files in os.walk(base_path):
            for file in files:
                if file.endswith('.parquet'):
                    sample_file = os.path.join(root, file)
                    break
            if sample_file:
                break
        
        if sample_file:
            try:
                df = pd.read_parquet(sample_file, engine='pyarrow')
                print(f"   File: {os.path.basename(sample_file)}")
                print(f"   Columns: {len(df.columns)}")
                
                # Check for imbalance field
                if 'imbalance' in df.columns:
                    print(f"   ✅ HAS 'imbalance' field! Range: [{df['imbalance'].min():.6f}, {df['imbalance'].max():.6f}]")
                    print(f"      Mean: {df['imbalance'].mean():.6f}, Std: {df['imbalance'].std():.6f}")
                else:
                    print(f"   ❌ NO 'imbalance' field")
                
                # Check order book columns
                book_cols = [col for col in df.columns if 'bid' in col or 'ask' in col]
                if book_cols:
                    max_level = max([int(c.split('_')[-1]) for c in book_cols if c.split('_')[-1].isdigit()], default=0)
                    print(f"   Order book depth: Level 1-{max_level}")
                
                # Show first few special columns
                special_cols = [col for col in df.columns if any(x in col for x in ['imbalance', 'obi', 'spread', 'volume'])]
                if special_cols:
                    print(f"   Special columns: {', '.join(special_cols[:10])}")
                    
            except Exception as e:
                print(f"   Error reading file: {e}")
        else:
            print(f"   No sample files found in {base_path}")

def analyze_exit_logic_details():
    """Deep dive into exit logic differences"""
    print("\n\n=== EXIT LOGIC DEEP ANALYSIS ===")
    
    # Legacy exit logic
    print("\n1. LEGACY EXIT LOGIC:")
    legacy_bt = "hyperliquid_bot/backtester/backtester.py"
    if os.path.exists(legacy_bt):
        with open(legacy_bt, 'r') as f:
            content = f.read()
        
        # Find exit evaluation method
        import re
        exit_method = re.search(r'def\s+_evaluate_exit.*?(?=\n    def|\Z)', content, re.DOTALL)
        if exit_method:
            method_lines = exit_method.group(0).split('\n')
            print("   Exit checks implemented:")
            
            if 'stop_loss' in exit_method.group(0):
                print("   ✅ Stop loss check")
            if 'take_profit' in exit_method.group(0):
                print("   ✅ Take profit check")
            if 'max_hold_time' in exit_method.group(0) or 'time' in exit_method.group(0):
                print("   ✅ Time-based exit")
            if 'signal' in exit_method.group(0):
                print("   ✅ Signal-based exit")
    
    # Modern exit logic
    print("\n2. MODERN EXIT LOGIC:")
    modern_bt = "hyperliquid_bot/modern/backtester_engine.py"
    if os.path.exists(modern_bt):
        with open(modern_bt, 'r') as f:
            content = f.read()
        
        exit_method = re.search(r'def\s+_evaluate_position_exit.*?(?=\n    def|\Z)', content, re.DOTALL)
        if exit_method:
            print("   Exit checks implemented:")
            
            if 'stop_loss' in exit_method.group(0):
                print("   ✅ Stop loss check")
            if 'take_profit' in exit_method.group(0):
                print("   ✅ Take profit check")
            if 'max_hold_time' in exit_method.group(0) or 'hours_held' in exit_method.group(0):
                print("   ✅ Time-based exit")
            if 'signal' in exit_method.group(0):
                print("   ✅ Signal-based exit")
            else:
                print("   ❌ No signal-based exit")

def check_regime_configurations():
    """Compare regime detector configurations"""
    print("\n\n=== REGIME DETECTOR CONFIGURATIONS ===")
    
    # Load base config
    with open('config.yaml', 'r') as f:
        base_config = yaml.safe_load(f)
    
    # Load modern config
    with open('configs/overrides/modern_system_v2_complete.yaml', 'r') as f:
        modern_config = yaml.safe_load(f)
    
    print("\n1. LEGACY REGIME CONFIG:")
    legacy_detector = base_config.get('regime', {}).get('detector_type', 'unknown')
    print(f"   Detector type: {legacy_detector}")
    
    if legacy_detector == 'granular_microstructure':
        gms_config = base_config.get('regime', {}).get('granular_microstructure', {})
        print(f"   Update frequency: {gms_config.get('cadence_sec', 'N/A')}s")
        print(f"   Output states: {gms_config.get('output_states', 'N/A')}")
    
    print("\n2. MODERN REGIME CONFIG:")
    modern_detector = modern_config.get('regime', {}).get('detector_type', 'unknown')
    print(f"   Detector type: {modern_detector}")
    
    if 'continuous' in modern_detector:
        print(f"   Update frequency: 60s (continuous)")
        print(f"   Should have ~60x more regime changes than Legacy")

def main():
    print("="*60)
    print("COMPREHENSIVE LEGACY VS MODERN SYSTEM COMPARISON")
    print("="*60)
    
    # Run all analyses
    analyze_backtester_position_logic()
    check_data_differences()
    analyze_exit_logic_details()
    check_regime_configurations()
    
    print("\n\n" + "="*60)
    print("CRITICAL FINDINGS SUMMARY:")
    print("="*60)
    
    print("\n🔍 KEY DIFFERENCES TO INVESTIGATE:")
    print("1. Position Management: Legacy likely enforces single position")
    print("2. Exit Logic: Check if Modern evaluates exits on every tick")
    print("3. Imbalance Field: Legacy may use pre-computed order flow imbalance")
    print("4. Regime Updates: Verify 60x frequency difference")
    print("5. Signal-based Exits: Modern may be missing strategy exit signals")
    
    print("\n🎯 ACTION ITEMS:")
    print("1. Implement single position constraint in Modern backtester")
    print("2. Add signal-based exit evaluation to Modern")
    print("3. Calculate 'imbalance' field for Modern from order book data")
    print("4. Verify regime update frequencies match expectations")

if __name__ == "__main__":
    main()