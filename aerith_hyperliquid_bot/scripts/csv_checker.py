import pandas as pd
from pathlib import Path

# --- Configuration ---
log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs/") # Updated log directory
# Find the latest losing trades analysis file (adjust pattern if needed)
analysis_files = sorted(log_dir.glob("losing_trades_analysis_*.csv"), reverse=True)

if not analysis_files:
    print(f"Error: No losing_trades_analysis CSV file found in logs directory: {log_dir}")
else:
    latest_analysis_file = analysis_files[0]
    print(f"Checking columns in: {latest_analysis_file}")
    try:
        df = pd.read_csv(latest_analysis_file, index_col=0) # Assuming first col is index
        print("\nColumns found in CSV:")
        print(list(df.columns))

        if 'entry_regime' in df.columns:
            print("\n'entry_regime' column IS present.")
            # Display value counts as a quick check
            print("\nValue counts for 'entry_regime':")
            print(df['entry_regime'].value_counts())
        else:
            print("\n'entry_regime' column IS MISSING.")

    except Exception as e:
        print(f"\nError reading or processing CSV: {e}")
        # Additional checks to help debug issues
        if latest_analysis_file.exists():
            print(f"File exists but couldn't be processed. File size: {latest_analysis_file.stat().st_size} bytes")
            # Try to read first few lines to check format
            try:
                with open(latest_analysis_file, 'r') as f:
                    print("\nFirst 5 lines of file:")
                    for i, line in enumerate(f):
                        if i < 5:
                            print(line.strip())
                        else:
                            break
            except Exception as read_err:
                print(f"Error reading file content: {read_err}")