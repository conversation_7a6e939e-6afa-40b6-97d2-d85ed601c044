#!/usr/bin/env python3
"""
Check what OBI columns exist and add missing ones to make detector work
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

import pandas as pd

# Check enhanced hourly data columns
enhanced_path = Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/enhanced_hourly/1h")
sample_file = enhanced_path / "2024-01-01_1h_enhanced.parquet"

if sample_file.exists():
    df = pd.read_parquet(sample_file)
    print("Enhanced hourly columns:")
    for col in sorted(df.columns):
        print(f"  - {col}")
    
    # Check for OBI-related columns
    obi_cols = [col for col in df.columns if 'obi' in col.lower()]
    print(f"\nOBI columns found: {obi_cols}")
    
    # Check legacy raw2 data for comparison
    legacy_path = Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw2")
    legacy_file = legacy_path / "BTC_agg_trades_20240101.parquet"
    
    if legacy_file.exists():
        legacy_df = pd.read_parquet(legacy_file)
        print(f"\nLegacy data columns ({len(legacy_df.columns)} total):")
        obi_legacy = [col for col in legacy_df.columns if 'obi' in col.lower()]
        print(f"Legacy OBI columns: {obi_legacy}")
    
    # Check what the detector needs vs what we have
    print("\n=== MISSING COLUMNS ===")
    required = ['obi_smoothed_5', 'atr_percent']
    for col in required:
        if col not in df.columns:
            print(f"❌ Missing: {col}")
        else:
            print(f"✓ Found: {col}")