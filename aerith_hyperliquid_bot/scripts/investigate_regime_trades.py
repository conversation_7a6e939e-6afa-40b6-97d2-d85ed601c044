#!/usr/bin/env python3
"""
Regime and Trade Direction Investigation
========================================

Investigates two critical issues:
1. Is there a diverse range of regimes being detected?
2. Does the strategy go long-only? If yes, why? Is there a bug preventing shorts?

This script analyzes the latest backtest results to understand:
- Regime distribution across all detected states
- Trade direction breakdown (long vs short)
- Regime-to-trade mapping logic
- Potential bugs in short position logic
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
import json
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import logging

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class RegimeTradeInvestigator:
    """Investigates regime diversity and trade direction patterns"""
    
    def __init__(self):
        self.project_root = project_root
        self.latest_signals_file = None
        self.latest_trades_file = None
        self.signals_df = None
        self.trades_data = None
        self.regime_stats = {}
        self.trade_stats = {}
        
    def find_latest_backtest_files(self):
        """Find the most recent backtest signals and trades files"""
        logs_dir = self.project_root.parent / "logs"
        
        # Find latest signals file
        signals_files = list(logs_dir.glob("backtest_signals_*.parquet"))
        if signals_files:
            self.latest_signals_file = max(signals_files, key=lambda x: x.stat().st_mtime)
            logger.info(f"Found latest signals file: {self.latest_signals_file.name}")
        
        # Find corresponding trades file
        if self.latest_signals_file:
            timestamp = self.latest_signals_file.name.replace("backtest_signals_", "").replace(".parquet", "")
            trades_file = logs_dir / f"backtest_trades_{timestamp}.json"
            if trades_file.exists():
                self.latest_trades_file = trades_file
                logger.info(f"Found corresponding trades file: {trades_file.name}")
            else:
                logger.warning(f"No trades file found for timestamp {timestamp}")
    
    def load_backtest_data(self):
        """Load signals and trades data"""
        if not self.latest_signals_file:
            logger.error("No signals file found")
            return False
            
        try:
            # Load signals data
            self.signals_df = pd.read_parquet(self.latest_signals_file)
            logger.info(f"Loaded signals data: {len(self.signals_df)} rows")
            
            # Load trades data if available
            if self.latest_trades_file:
                with open(self.latest_trades_file, 'r') as f:
                    self.trades_data = json.load(f)
                logger.info(f"Loaded trades data: {len(self.trades_data)} trades")
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading backtest data: {e}")
            return False
    
    def analyze_regime_diversity(self):
        """Analyze the diversity of detected regimes"""
        if self.signals_df is None:
            logger.error("No signals data available")
            return
            
        logger.info("Analyzing regime diversity...")
        
        # Check available regime columns
        regime_columns = [col for col in self.signals_df.columns if 'regime' in col.lower()]
        logger.info(f"Available regime columns: {regime_columns}")
        
        # Analyze raw GMS states if available
        if 'regime_state' in self.signals_df.columns:
            raw_regime_counts = self.signals_df['regime_state'].value_counts()
            self.regime_stats['raw_gms_states'] = raw_regime_counts.to_dict()
            
            logger.info("Raw GMS State Distribution:")
            for state, count in raw_regime_counts.items():
                pct = count / len(self.signals_df) * 100
                logger.info(f"  {state}: {count} ({pct:.1f}%)")
        
        # Analyze mapped regime states if available
        if 'regime_mapped' in self.signals_df.columns:
            mapped_regime_counts = self.signals_df['regime_mapped'].value_counts()
            self.regime_stats['mapped_regime_states'] = mapped_regime_counts.to_dict()
            
            logger.info("Mapped Regime Distribution:")
            for regime, count in mapped_regime_counts.items():
                pct = count / len(self.signals_df) * 100
                logger.info(f"  {regime}: {count} ({pct:.1f}%)")
        
        # Check for regime transitions
        if 'regime_state' in self.signals_df.columns:
            regime_changes = (self.signals_df['regime_state'] != self.signals_df['regime_state'].shift(1)).sum()
            self.regime_stats['regime_transitions'] = regime_changes
            logger.info(f"Total regime transitions: {regime_changes}")
        
        # Analyze by time period
        if 'regime_state' in self.signals_df.columns and 'timestamp' in self.signals_df.columns:
            self.signals_df['month'] = pd.to_datetime(self.signals_df['timestamp']).dt.to_period('M')
            monthly_regimes = self.signals_df.groupby('month')['regime_state'].value_counts()
            logger.info("Monthly regime distribution available for detailed analysis")
    
    def analyze_trade_directions(self):
        """Analyze trade direction patterns and potential long-only bias"""
        if not self.trades_data:
            logger.error("No trades data available")
            return
            
        logger.info("Analyzing trade directions...")
        
        # Extract trade directions
        directions = []
        regimes_at_entry = []
        entry_dates = []
        
        for trade in self.trades_data:
            direction = trade.get('type', 'unknown')  # Fixed: use 'type' field
            regime = trade.get('entry_regime', 'unknown')  # Fixed: use 'entry_regime' field
            entry_time = trade.get('entry_time', 'unknown')
            
            directions.append(direction)
            regimes_at_entry.append(regime)
            entry_dates.append(entry_time)
        
        # Count directions
        direction_counts = pd.Series(directions).value_counts()
        self.trade_stats['direction_distribution'] = direction_counts.to_dict()
        
        logger.info("Trade Direction Distribution:")
        for direction, count in direction_counts.items():
            pct = count / len(directions) * 100
            logger.info(f"  {direction}: {count} ({pct:.1f}%)")
        
        # Analyze regimes at trade entry
        regime_at_entry_counts = pd.Series(regimes_at_entry).value_counts()
        self.trade_stats['regime_at_entry'] = regime_at_entry_counts.to_dict()
        
        logger.info("Regimes at Trade Entry:")
        for regime, count in regime_at_entry_counts.items():
            pct = count / len(regimes_at_entry) * 100
            logger.info(f"  {regime}: {count} ({pct:.1f}%)")
        
        # Check for long-only bias
        long_trades = direction_counts.get('long', 0)
        short_trades = direction_counts.get('short', 0)
        total_trades = long_trades + short_trades
        
        if total_trades > 0:
            long_pct = long_trades / total_trades * 100
            short_pct = short_trades / total_trades * 100
            
            self.trade_stats['long_bias_analysis'] = {
                'long_percentage': long_pct,
                'short_percentage': short_pct,
                'is_long_only': short_trades == 0,
                'significant_bias': abs(long_pct - 50) > 30  # >80% or <20% in one direction
            }
            
            logger.info(f"Trade Bias Analysis:")
            logger.info(f"  Long trades: {long_pct:.1f}%")
            logger.info(f"  Short trades: {short_pct:.1f}%")
            
            if short_trades == 0:
                logger.warning("🚨 POTENTIAL BUG: Strategy appears to be LONG-ONLY!")
                logger.warning("   This suggests a bug preventing short positions")
            elif long_pct > 80:
                logger.warning(f"⚠️  SIGNIFICANT LONG BIAS: {long_pct:.1f}% long trades")
    
    def investigate_short_position_logic(self):
        """Investigate why short positions might not be generated"""
        if self.signals_df is None:
            logger.error("No signals data for short position analysis")
            return
            
        logger.info("Investigating short position logic...")
        
        # Check for BEAR regime periods
        if 'regime_mapped' in self.signals_df.columns:
            bear_periods = self.signals_df[self.signals_df['regime_mapped'] == 'BEAR']
            bear_count = len(bear_periods)
            bear_pct = bear_count / len(self.signals_df) * 100
            
            logger.info(f"BEAR regime periods: {bear_count} ({bear_pct:.1f}%)")
            
            if bear_count == 0:
                logger.warning("🚨 NO BEAR REGIMES DETECTED!")
                logger.warning("   This could explain the lack of short trades")
                
                # Check raw states for potential bear signals
                if 'regime_state' in self.signals_df.columns:
                    bear_raw_states = self.signals_df[
                        self.signals_df['regime_state'].str.contains('Bear', na=False, case=False)
                    ]
                    logger.info(f"Raw bear-related states: {len(bear_raw_states)}")
                    
                    if len(bear_raw_states) > 0:
                        raw_bear_states = bear_raw_states['regime_state'].value_counts()
                        logger.info("Raw bear states found:")
                        for state, count in raw_bear_states.items():
                            logger.info(f"  {state}: {count}")
                        logger.warning("   Bear states exist but not mapped to BEAR regime!")
        
        # Check signal generation logic
        signal_columns = [col for col in self.signals_df.columns if 'signal' in col.lower()]
        logger.info(f"Available signal columns: {signal_columns}")
        
        # Look for EMA crossover patterns that should trigger shorts
        if 'ema_fast' in self.signals_df.columns and 'ema_slow' in self.signals_df.columns:
            # Calculate crossovers
            self.signals_df['ema_fast_above_slow'] = self.signals_df['ema_fast'] > self.signals_df['ema_slow']
            self.signals_df['ema_crossover'] = self.signals_df['ema_fast_above_slow'] != self.signals_df['ema_fast_above_slow'].shift(1)
            
            # Count potential short signals (fast crossing below slow)
            short_crossovers = self.signals_df[
                (self.signals_df['ema_crossover']) & 
                (~self.signals_df['ema_fast_above_slow'])
            ]
            
            logger.info(f"Potential short signals (EMA fast below slow crossovers): {len(short_crossovers)}")
            
            if len(short_crossovers) > 0 and self.trade_stats.get('direction_distribution', {}).get('short', 0) == 0:
                logger.warning("🚨 SIGNAL GENERATION BUG DETECTED!")
                logger.warning("   EMA crossovers for shorts exist but no short trades generated")
    
    def create_regime_analysis_chart(self):
        """Create comprehensive regime and trade analysis charts"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # Chart 1: Raw GMS State Distribution
        if 'raw_gms_states' in self.regime_stats:
            ax1 = axes[0, 0]
            states = list(self.regime_stats['raw_gms_states'].keys())
            counts = list(self.regime_stats['raw_gms_states'].values())
            
            ax1.pie(counts, labels=states, autopct='%1.1f%%', startangle=90)
            ax1.set_title('Raw GMS State Distribution')
        
        # Chart 2: Mapped Regime Distribution  
        if 'mapped_regime_states' in self.regime_stats:
            ax2 = axes[0, 1]
            regimes = list(self.regime_stats['mapped_regime_states'].keys())
            counts = list(self.regime_stats['mapped_regime_states'].values())
            
            colors = {'BULL': 'green', 'BEAR': 'red', 'CHOP': 'gray', 'Uncertain': 'orange'}
            regime_colors = [colors.get(regime, 'blue') for regime in regimes]
            
            ax2.bar(regimes, counts, color=regime_colors, alpha=0.7)
            ax2.set_title('Mapped Regime Distribution')
            ax2.set_ylabel('Count')
            ax2.tick_params(axis='x', rotation=45)
        
        # Chart 3: Trade Direction Distribution
        if 'direction_distribution' in self.trade_stats:
            ax3 = axes[1, 0]
            directions = list(self.trade_stats['direction_distribution'].keys())
            counts = list(self.trade_stats['direction_distribution'].values())
            
            direction_colors = {'long': 'green', 'short': 'red'}
            colors = [direction_colors.get(d, 'blue') for d in directions]
            
            ax3.bar(directions, counts, color=colors, alpha=0.7)
            ax3.set_title('Trade Direction Distribution')
            ax3.set_ylabel('Number of Trades')
            
            # Add percentage labels
            total = sum(counts)
            for i, (direction, count) in enumerate(zip(directions, counts)):
                pct = count / total * 100
                ax3.text(i, count + max(counts) * 0.01, f'{pct:.1f}%', 
                        ha='center', va='bottom', fontweight='bold')
        
        # Chart 4: Regime at Trade Entry
        if 'regime_at_entry' in self.trade_stats:
            ax4 = axes[1, 1]
            regimes = list(self.trade_stats['regime_at_entry'].keys())
            counts = list(self.trade_stats['regime_at_entry'].values())
            
            ax4.bar(regimes, counts, alpha=0.7)
            ax4.set_title('Regimes at Trade Entry')
            ax4.set_ylabel('Number of Trades')
            ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # Save chart
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        chart_path = self.project_root / f"regime_trade_investigation_{timestamp}.png"
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        logger.info(f"Analysis chart saved to: {chart_path}")
        
        plt.show()
    
    def generate_investigation_report(self):
        """Generate comprehensive investigation report"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = self.project_root / f"regime_trade_investigation_report_{timestamp}.md"
        
        with open(report_path, 'w') as f:
            f.write("# Regime and Trade Direction Investigation Report\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Data sources
            f.write("## Data Sources\n\n")
            if self.latest_signals_file:
                f.write(f"- **Signals File:** {self.latest_signals_file.name}\n")
            if self.latest_trades_file:
                f.write(f"- **Trades File:** {self.latest_trades_file.name}\n")
            f.write(f"- **Total Signal Samples:** {len(self.signals_df) if self.signals_df is not None else 'N/A'}\n")
            f.write(f"- **Total Trades:** {len(self.trades_data) if self.trades_data else 'N/A'}\n\n")
            
            # Question 1: Regime Diversity
            f.write("## 1. Regime Diversity Analysis\n\n")
            
            if 'raw_gms_states' in self.regime_stats:
                f.write("### Raw GMS State Distribution\n\n")
                total_samples = sum(self.regime_stats['raw_gms_states'].values())
                for state, count in self.regime_stats['raw_gms_states'].items():
                    pct = count / total_samples * 100
                    f.write(f"- **{state}:** {count:,} ({pct:.1f}%)\n")
                f.write("\n")
            
            if 'mapped_regime_states' in self.regime_stats:
                f.write("### Mapped Regime Distribution\n\n")
                total_mapped = sum(self.regime_stats['mapped_regime_states'].values())
                for regime, count in self.regime_stats['mapped_regime_states'].items():
                    pct = count / total_mapped * 100
                    f.write(f"- **{regime}:** {count:,} ({pct:.1f}%)\n")
                f.write("\n")
            
            if 'regime_transitions' in self.regime_stats:
                f.write(f"### Regime Transitions\n\n")
                f.write(f"- **Total regime changes:** {self.regime_stats['regime_transitions']:,}\n\n")
            
            # Question 2: Trade Direction Analysis
            f.write("## 2. Trade Direction Analysis\n\n")
            
            if 'direction_distribution' in self.trade_stats:
                f.write("### Trade Direction Distribution\n\n")
                total_trades = sum(self.trade_stats['direction_distribution'].values())
                for direction, count in self.trade_stats['direction_distribution'].items():
                    pct = count / total_trades * 100
                    f.write(f"- **{direction.title()} trades:** {count} ({pct:.1f}%)\n")
                f.write("\n")
            
            if 'long_bias_analysis' in self.trade_stats:
                bias = self.trade_stats['long_bias_analysis']
                f.write("### Long/Short Bias Analysis\n\n")
                f.write(f"- **Long percentage:** {bias['long_percentage']:.1f}%\n")
                f.write(f"- **Short percentage:** {bias['short_percentage']:.1f}%\n")
                f.write(f"- **Is long-only:** {bias['is_long_only']}\n")
                f.write(f"- **Significant bias:** {bias['significant_bias']}\n\n")
                
                if bias['is_long_only']:
                    f.write("🚨 **CRITICAL FINDING:** Strategy appears to be LONG-ONLY!\n\n")
                    f.write("This suggests a potential bug preventing short position generation.\n\n")
            
            # Conclusions and Recommendations
            f.write("## Conclusions and Recommendations\n\n")
            
            # Regime diversity conclusion
            bear_regimes = self.regime_stats.get('mapped_regime_states', {}).get('BEAR', 0)
            bull_regimes = self.regime_stats.get('mapped_regime_states', {}).get('BULL', 0)
            
            if bear_regimes == 0:
                f.write("### ❌ Regime Diversity Issue\n\n")
                f.write("- **No BEAR regimes detected** - this indicates a problem with regime detection\n")
                f.write("- The system may not be properly identifying bearish market conditions\n")
                f.write("- Check GMS threshold calibration and state mapping configuration\n\n")
            elif bear_regimes < bull_regimes * 0.1:  # Less than 10% of bull regimes
                f.write("### ⚠️ Limited Regime Diversity\n\n")
                f.write(f"- BEAR regimes: {bear_regimes} vs BULL regimes: {bull_regimes}\n")
                f.write("- System shows strong bull bias in regime detection\n")
                f.write("- Consider adjusting momentum thresholds for better balance\n\n")
            else:
                f.write("### ✅ Good Regime Diversity\n\n")
                f.write("- System detects both BULL and BEAR regimes appropriately\n\n")
            
            # Trade direction conclusion
            short_trades = self.trade_stats.get('direction_distribution', {}).get('short', 0)
            
            if short_trades == 0:
                f.write("### 🚨 CRITICAL: Long-Only Bug\n\n")
                f.write("**IMMEDIATE ACTION REQUIRED:**\n")
                f.write("1. Check TF-v3 strategy logic for short signal generation\n")
                f.write("2. Verify EMA crossover detection (fast crossing below slow)\n")
                f.write("3. Ensure BEAR regime mapping is working correctly\n")
                f.write("4. Review signal filtering and regime gating logic\n\n")
                f.write("**Potential causes:**\n")
                f.write("- Bug in EMA crossover logic preventing short signals\n")
                f.write("- BEAR regime not being detected or mapped correctly\n")
                f.write("- Signal filtering rejecting all short position signals\n")
                f.write("- Position sizing logic preventing short trades\n\n")
            else:
                f.write("### ✅ Both Long and Short Trades Generated\n\n")
                f.write("- Strategy successfully generates both directions\n\n")
        
        logger.info(f"Investigation report saved to: {report_path}")
        return report_path
    
    def run_investigation(self):
        """Run the complete investigation"""
        logger.info("Starting regime and trade direction investigation...")
        
        # Find and load data
        self.find_latest_backtest_files()
        if not self.load_backtest_data():
            logger.error("Failed to load backtest data")
            return
        
        # Run analyses
        self.analyze_regime_diversity()
        self.analyze_trade_directions()
        self.investigate_short_position_logic()
        
        # Generate outputs
        self.create_regime_analysis_chart()
        report_path = self.generate_investigation_report()
        
        logger.info("Investigation complete!")
        logger.info(f"Report saved to: {report_path}")
        
        # Print key findings
        print("\n" + "="*80)
        print("REGIME AND TRADE DIRECTION INVESTIGATION - KEY FINDINGS")
        print("="*80)
        
        if self.regime_stats:
            print("\n📊 REGIME DIVERSITY:")
            if 'mapped_regime_states' in self.regime_stats:
                for regime, count in self.regime_stats['mapped_regime_states'].items():
                    total = sum(self.regime_stats['mapped_regime_states'].values())
                    pct = count / total * 100
                    print(f"   {regime}: {count} ({pct:.1f}%)")
        
        if self.trade_stats:
            print("\n🎯 TRADE DIRECTIONS:")
            if 'direction_distribution' in self.trade_stats:
                for direction, count in self.trade_stats['direction_distribution'].items():
                    total = sum(self.trade_stats['direction_distribution'].values())
                    pct = count / total * 100
                    print(f"   {direction.title()}: {count} ({pct:.1f}%)")
            
            if 'long_bias_analysis' in self.trade_stats:
                bias = self.trade_stats['long_bias_analysis']
                if bias['is_long_only']:
                    print("\n🚨 CRITICAL BUG DETECTED: STRATEGY IS LONG-ONLY!")
                    print("   This requires immediate investigation")
        
        print("\n" + "="*80)

def main():
    """Main investigation function"""
    investigator = RegimeTradeInvestigator()
    investigator.run_investigation()

if __name__ == "__main__":
    main()