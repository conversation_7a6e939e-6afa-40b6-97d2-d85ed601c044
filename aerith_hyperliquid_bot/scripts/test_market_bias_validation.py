#!/usr/bin/env python3
# Test script for validating the enhanced market bias validation functionality

import sys
import os
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_market_bias_validation")

# Add project root to path for imports
project_root = Path(os.path.abspath(os.path.dirname(__file__))).parent
sys.path.insert(0, str(project_root))

# Import necessary modules
from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.risk import RiskManager
import yaml

def load_config():
    """Load the configuration from base.yaml"""
    config_path = project_root / "configs" / "base.yaml"
    with open(config_path, 'r') as f:
        config_dict = yaml.safe_load(f)
    return Config.parse_obj(config_dict)

def test_market_bias_validation():
    """Test the enhanced market bias validation in RiskManager"""
    logger.info("Loading configuration...")
    config = load_config()
    
    # Ensure market bias is enabled for testing
    if not hasattr(config.regime, 'market_bias') or not hasattr(config.regime.market_bias, 'enabled'):
        logger.warning("Market bias not enabled in config, enabling for test...")
        if not hasattr(config.regime, 'market_bias'):
            logger.error("Cannot proceed: market_bias section missing in config")
            return
        config.regime.market_bias.enabled = True
    
    # Initialize the RiskManager
    logger.info("Initializing RiskManager...")
    risk_manager = RiskManager(config)
    
    # Create a series of test cases with intentionally mismatched mappings
    test_cases = [
        # Correct mappings
        {"regime": "Strong_Bull_Trend", "market_state": "BULL", "expected_consistent": True},
        {"regime": "Weak_Bear_Trend", "market_state": "CHOP", "expected_consistent": True},
        
        # Incorrect mappings to test validation
        {"regime": "Strong_Bull_Trend", "market_state": "BEAR", "expected_consistent": False},
        {"regime": "Weak_Bear_Trend", "market_state": "BEAR", "expected_consistent": False},
        
        # Edge cases
        {"regime": "Unknown_Regime", "market_state": "CHOP", "expected_consistent": True},  # Unknown regimes default to CHOP
        {"regime": "Strong_Bull_Trend", "market_state": "INVALID", "expected_consistent": False}  # Invalid 3-state
    ]
    
    # Run test cases
    for i, case in enumerate(test_cases):
        regime = case["regime"]
        market_state = case["market_state"]
        expected_consistent = case["expected_consistent"]
        
        logger.info(f"\nTest Case #{i+1}: '{regime}' -> '{market_state}' (Expected consistent: {expected_consistent})")
        
        # Call the validation method directly
        risk_manager._validate_market_bias(
            market_state=market_state,
            regime=regime,
            base_leverage=1.0,
            final_leverage=1.0, 
            market_risk_factor=1.0,
            direction_bias=1.0
        )
        
        # Note: In a real test we would capture logs and verify warnings are emitted correctly
        # but for this simple test, we'll just look at the logged output

    logger.info("\nValidation tests completed! Check the log output for warnings about inconsistent mappings.")

if __name__ == "__main__":
    test_market_bias_validation()
