#!/usr/bin/env python3

import re
import numpy as np
from pathlib import Path

def extract_detailed_metrics(log_file):
    """Extract detailed performance metrics from backtest log"""
    content = Path(log_file).read_text()
    
    metrics = {}
    
    # Extract basic performance metrics - handle ANSI color codes with escape sequences
    roi_match = re.search(r'Return on Initial \(ROI\):\s*.*?([\d.-]+)%', content)
    total_trades_match = re.search(r'Total Trades:\s*.*?(\d+)', content)
    win_rate_match = re.search(r'Win Rate:\s*.*?([\d.-]+)\s*%', content)
    avg_trade_match = re.search(r'Avg Trade P/L:\s*\$.*?([\d.-]+)', content)
    profit_factor_match = re.search(r'Profit Factor:\s*.*?([\d.-]+)', content)
    sharpe_match = re.search(r'Sharpe Ratio \(Daily\):\s*.*?([\d.-]+)', content)
    max_dd_match = re.search(r'Max Drawdown:\s*.*?([\d.-]+)%', content)
    total_pnl_match = re.search(r'Total P/L:\s*\$.*?([\d,.-]+)', content)
    
    if roi_match:
        metrics['roi'] = float(roi_match.group(1))
    if total_trades_match:
        metrics['total_trades'] = int(total_trades_match.group(1))
    if win_rate_match:
        metrics['win_rate'] = float(win_rate_match.group(1))
    if avg_trade_match:
        metrics['avg_trade_pnl'] = float(avg_trade_match.group(1))
    if profit_factor_match:
        metrics['profit_factor'] = float(profit_factor_match.group(1))
    if sharpe_match:
        metrics['sharpe_ratio'] = float(sharpe_match.group(1))
    if max_dd_match:
        metrics['max_drawdown'] = float(max_dd_match.group(1))
    if total_pnl_match:
        # Remove commas and convert to float
        pnl_str = total_pnl_match.group(1).replace(',', '')
        metrics['total_pnl'] = float(pnl_str)
    
    # Extract win/loss details
    avg_win_loss_match = re.search(r'Avg Win / Avg Loss:\s*\$([\d.-]+) / \$\-?([\d.-]+)', content)
    if avg_win_loss_match:
        metrics['avg_win'] = float(avg_win_loss_match.group(1))
        metrics['avg_loss'] = float(avg_win_loss_match.group(2))
    
    # Extract exit reason stats
    stop_loss_pattern = r'stop_loss\s+(\d+)\s+\[91m\$\s*([\d,.-]+)\[0m'
    take_profit_pattern = r'take_profit\s+(\d+)\s+\[92m\$\s*([\d,.-]+)\[0m'
    time_exit_pattern = r'time_exit\s+(\d+)\s+\[92m\$\s*([\d,.-]+)\[0m'
    
    stop_loss_match = re.search(stop_loss_pattern, content)
    take_profit_match = re.search(take_profit_pattern, content)
    time_exit_match = re.search(time_exit_pattern, content)
    
    if stop_loss_match:
        metrics['stop_loss_count'] = int(stop_loss_match.group(1))
        metrics['stop_loss_pnl'] = float(stop_loss_match.group(2).replace(',', ''))
    
    if take_profit_match:
        metrics['take_profit_count'] = int(take_profit_match.group(1))
        metrics['take_profit_pnl'] = float(take_profit_match.group(2).replace(',', ''))
    
    if time_exit_match:
        metrics['time_exit_count'] = int(time_exit_match.group(1))
        metrics['time_exit_pnl'] = float(time_exit_match.group(2).replace(',', ''))
    
    return metrics

def analyze_roi_sources():
    """Analyze the sources of ROI difference between systems"""
    
    # Use the specific recent logs we identified
    log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    
    # Modern system (continuous_gms): 160 trades, 106.74% ROI
    modern_log = log_dir / "backtest_run_20250715_034001.log"
    
    # Legacy system (granular_microstructure): 180 trades, 215.41% ROI
    legacy_log = log_dir / "backtest_run_20250715_033916.log"
    
    if not modern_log.exists() or not legacy_log.exists():
        print("Could not find the specified log files")
        return
    
    print(f"Analyzing:")
    print(f"Modern: {modern_log.name}")
    print(f"Legacy: {legacy_log.name}")
    print()
    
    # Extract metrics
    modern_metrics = extract_detailed_metrics(modern_log)
    legacy_metrics = extract_detailed_metrics(legacy_log)
    
    print("=" * 80)
    print("ROI DIFFERENCE ANALYSIS")
    print("=" * 80)
    
    # Basic comparison
    print(f"{'Metric':<25} {'Legacy':<15} {'Modern':<15} {'Difference':<15}")
    print("-" * 70)
    
    legacy_roi = legacy_metrics.get('roi', 0)
    modern_roi = modern_metrics.get('roi', 0)
    roi_diff = legacy_roi - modern_roi
    
    print(f"{'ROI %':<25} {legacy_roi:<15.2f} {modern_roi:<15.2f} {roi_diff:<15.2f}")
    print(f"{'Total Trades':<25} {legacy_metrics.get('total_trades', 0):<15} {modern_metrics.get('total_trades', 0):<15} {legacy_metrics.get('total_trades', 0) - modern_metrics.get('total_trades', 0):<15}")
    print(f"{'Win Rate %':<25} {legacy_metrics.get('win_rate', 0):<15.2f} {modern_metrics.get('win_rate', 0):<15.2f} {legacy_metrics.get('win_rate', 0) - modern_metrics.get('win_rate', 0):<15.2f}")
    
    print()
    # Additional metrics
    if 'profit_factor' in legacy_metrics and 'profit_factor' in modern_metrics:
        print(f"{'Profit Factor':<25} {legacy_metrics['profit_factor']:<15.2f} {modern_metrics['profit_factor']:<15.2f} {legacy_metrics['profit_factor'] - modern_metrics['profit_factor']:<15.2f}")
    if 'sharpe_ratio' in legacy_metrics and 'sharpe_ratio' in modern_metrics:
        print(f"{'Sharpe Ratio':<25} {legacy_metrics['sharpe_ratio']:<15.2f} {modern_metrics['sharpe_ratio']:<15.2f} {legacy_metrics['sharpe_ratio'] - modern_metrics['sharpe_ratio']:<15.2f}")
    if 'max_drawdown' in legacy_metrics and 'max_drawdown' in modern_metrics:
        print(f"{'Max Drawdown %':<25} {legacy_metrics['max_drawdown']:<15.2f} {modern_metrics['max_drawdown']:<15.2f} {legacy_metrics['max_drawdown'] - modern_metrics['max_drawdown']:<15.2f}")
    
    print()
    print("TRADE PnL ANALYSIS")
    print("-" * 50)
    
    # Trade PnL comparison
    if 'avg_trade_pnl' in legacy_metrics and 'avg_trade_pnl' in modern_metrics:
        print(f"{'Avg Trade PnL $':<25} {legacy_metrics['avg_trade_pnl']:<15.2f} {modern_metrics['avg_trade_pnl']:<15.2f} {legacy_metrics['avg_trade_pnl'] - modern_metrics['avg_trade_pnl']:<15.2f}")
    
    if 'avg_win' in legacy_metrics and 'avg_win' in modern_metrics:
        print(f"{'Avg Winning Trade $':<25} {legacy_metrics['avg_win']:<15.2f} {modern_metrics['avg_win']:<15.2f} {legacy_metrics['avg_win'] - modern_metrics['avg_win']:<15.2f}")
    
    if 'avg_loss' in legacy_metrics and 'avg_loss' in modern_metrics:
        print(f"{'Avg Losing Trade $':<25} {legacy_metrics['avg_loss']:<15.2f} {modern_metrics['avg_loss']:<15.2f} {legacy_metrics['avg_loss'] - modern_metrics['avg_loss']:<15.2f}")
    
    # Exit reason analysis
    print()
    print("EXIT REASON ANALYSIS")
    print("-" * 50)
    
    if 'stop_loss_count' in legacy_metrics and 'stop_loss_count' in modern_metrics:
        print(f"{'Stop Loss Count':<25} {legacy_metrics['stop_loss_count']:<15} {modern_metrics['stop_loss_count']:<15} {legacy_metrics['stop_loss_count'] - modern_metrics['stop_loss_count']:<15}")
        print(f"{'Stop Loss PnL $':<25} {legacy_metrics['stop_loss_pnl']:<15.2f} {modern_metrics['stop_loss_pnl']:<15.2f} {legacy_metrics['stop_loss_pnl'] - modern_metrics['stop_loss_pnl']:<15.2f}")
    
    if 'take_profit_count' in legacy_metrics and 'take_profit_count' in modern_metrics:
        print(f"{'Take Profit Count':<25} {legacy_metrics['take_profit_count']:<15} {modern_metrics['take_profit_count']:<15} {legacy_metrics['take_profit_count'] - modern_metrics['take_profit_count']:<15}")
        print(f"{'Take Profit PnL $':<25} {legacy_metrics['take_profit_pnl']:<15.2f} {modern_metrics['take_profit_pnl']:<15.2f} {legacy_metrics['take_profit_pnl'] - modern_metrics['take_profit_pnl']:<15.2f}")
    
    if 'time_exit_count' in legacy_metrics and 'time_exit_count' in modern_metrics:
        print(f"{'Time Exit Count':<25} {legacy_metrics['time_exit_count']:<15} {modern_metrics['time_exit_count']:<15} {legacy_metrics['time_exit_count'] - modern_metrics['time_exit_count']:<15}")
        print(f"{'Time Exit PnL $':<25} {legacy_metrics['time_exit_pnl']:<15.2f} {modern_metrics['time_exit_pnl']:<15.2f} {legacy_metrics['time_exit_pnl'] - modern_metrics['time_exit_pnl']:<15.2f}")
    
    # Calculate ROI contribution breakdown
    print()
    print("ROI BREAKDOWN ANALYSIS")
    print("-" * 50)
    
    if 'total_pnl' in legacy_metrics and 'total_pnl' in modern_metrics:
        # Calculate cumulative contribution
        legacy_total_pnl = legacy_metrics['total_pnl']
        modern_total_pnl = modern_metrics['total_pnl']
        
        print(f"Total PnL Legacy: ${legacy_total_pnl:.2f}")
        print(f"Total PnL Modern: ${modern_total_pnl:.2f}")
        print(f"PnL Difference: ${legacy_total_pnl - modern_total_pnl:.2f}")
        
        # ROI per dollar calculation (capital efficiency)
        legacy_roi_per_dollar = legacy_roi / legacy_total_pnl if legacy_total_pnl > 0 else 0
        modern_roi_per_dollar = modern_roi / modern_total_pnl if modern_total_pnl > 0 else 0
        
        print(f"ROI per PnL$ Legacy: {legacy_roi_per_dollar:.4f}")
        print(f"ROI per PnL$ Modern: {modern_roi_per_dollar:.4f}")
        
        # This suggests leverage/capital efficiency differences
        leverage_efficiency_diff = legacy_roi_per_dollar - modern_roi_per_dollar
        print(f"Capital Efficiency Diff: {leverage_efficiency_diff:.4f}")
        
        # Key insights
        print()
        print("KEY INSIGHTS")
        print("-" * 50)
        
        # Compare trade efficiency
        trade_diff = legacy_metrics.get('total_trades', 0) - modern_metrics.get('total_trades', 0)
        pnl_diff = legacy_total_pnl - modern_total_pnl
        
        print(f"• Legacy system generates {trade_diff} more trades (+{trade_diff/modern_metrics.get('total_trades', 1)*100:.1f}%)")
        print(f"• Legacy system generates ${pnl_diff:.2f} more PnL (+{pnl_diff/modern_total_pnl*100:.1f}%)")
        print(f"• ROI difference: {legacy_roi - modern_roi:.1f}% ({legacy_roi - modern_roi:.1f} percentage points)")
        
        # Analyze capital efficiency
        if abs(leverage_efficiency_diff) > 0.01:
            print(f"• ⚠️  CAPITAL EFFICIENCY DIFFERENCE: {leverage_efficiency_diff:.4f}")
            print(f"  This suggests different leverage, position sizing, or risk management")
        
        # Analyze performance per trade
        if 'avg_trade_pnl' in legacy_metrics and 'avg_trade_pnl' in modern_metrics:
            trade_eff_diff = legacy_metrics['avg_trade_pnl'] - modern_metrics['avg_trade_pnl']
            print(f"• Legacy system: ${legacy_metrics['avg_trade_pnl']:.2f} per trade vs Modern: ${modern_metrics['avg_trade_pnl']:.2f}")
            print(f"  Difference: ${trade_eff_diff:.2f} per trade (+{trade_eff_diff/modern_metrics['avg_trade_pnl']*100:.1f}%)")
        
        # Exit reason analysis
        if 'take_profit_pnl' in legacy_metrics and 'take_profit_pnl' in modern_metrics:
            tp_diff = legacy_metrics['take_profit_pnl'] - modern_metrics['take_profit_pnl']
            print(f"• Take profit difference: ${tp_diff:.2f} (+{tp_diff/modern_metrics['take_profit_pnl']*100:.1f}% more from legacy)")
        
        if 'stop_loss_pnl' in legacy_metrics and 'stop_loss_pnl' in modern_metrics:
            sl_diff = legacy_metrics['stop_loss_pnl'] - modern_metrics['stop_loss_pnl']
            print(f"• Stop loss difference: ${sl_diff:.2f} (Legacy stops are {abs(sl_diff)/abs(modern_metrics['stop_loss_pnl'])*100:.1f}% larger losses)")
            
        print()
        print("PROBABLE ROOT CAUSES:")
        print("-" * 30)
        print("1. Trade frequency: Legacy generates more trades, more opportunities")
        print("2. Trade sizing: Different position sizing or leverage between systems")  
        print("3. Exit timing: Different take profit/stop loss parameters")
        print("4. Regime detection: Different regime identification leading to different trade setups")

if __name__ == "__main__":
    analyze_roi_sources()