#!/usr/bin/env python3
"""
Debug Regime Features Passing - Simplified
==========================================

Focus on the exact issue: regime_features not passed to strategy.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from datetime import datetime

def main():
    print("=== Debugging Regime Features Passing ===\n")
    
    # Read the _prepare_strategy_signals method
    print("1. Examining hourly_evaluator._prepare_strategy_signals()...")
    
    with open('hyperliquid_bot/modern/hourly_evaluator.py', 'r') as f:
        lines = f.readlines()
    
    # Find where regime_features are handled
    print("\n2. Lines that mention 'regime_features' in _prepare_strategy_signals:")
    in_method = False
    for i, line in enumerate(lines):
        if "_prepare_strategy_signals" in line:
            in_method = True
        elif in_method and "def " in line and "_prepare_strategy_signals" not in line:
            in_method = False
        
        if in_method and "regime_features" in line.lower():
            print(f"   Line {i+1}: {line.strip()}")
    
    print("\n3. Lines that set signals dict values:")
    in_method = False
    for i, line in enumerate(lines):
        if "_prepare_strategy_signals" in line:
            in_method = True
        elif in_method and "def " in line and "_prepare_strategy_signals" not in line:
            in_method = False
        
        if in_method and "signals[" in line and "=" in line:
            print(f"   Line {i+1}: {line.strip()}")
    
    print("\n4. The issue:")
    print("   - The method receives 'regime_features' as a parameter")
    print("   - It sets signals['regime'] = regime_features.get('current_state')")
    print("   - It sets signals['risk_suppressed'] = regime_features.get('risk_suppressed')")
    print("   - BUT it NEVER sets signals['regime_features'] = regime_features")
    
    print("\n5. The fix needed at line ~265:")
    print("   # Add regime features dict to signals")
    print("   signals['regime_features'] = regime_features")
    
    print("\n6. Examining tf_v3_modern.evaluate_entry()...")
    
    with open('hyperliquid_bot/modern/tf_v3_modern.py', 'r') as f:
        lines = f.readlines()
    
    print("\n7. Lines in evaluate_entry that reference regime_features:")
    in_method = False
    for i, line in enumerate(lines):
        if "def evaluate_entry" in line:
            in_method = True
        elif in_method and "def " in line and "evaluate_entry" not in line:
            in_method = False
        
        if in_method and "regime_features" in line:
            print(f"   Line {i+1}: {line.strip()}")
    
    print("\n" + "="*50)
    print("CONCLUSION:")
    print("The bug is confirmed. The hourly_evaluator receives regime_features")
    print("but doesn't pass them in the signals dict to the strategy.")
    print("The strategy expects signals['regime_features'] to exist (line 106).")

if __name__ == "__main__":
    main()