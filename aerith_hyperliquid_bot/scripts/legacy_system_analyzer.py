#!/usr/bin/env python3
"""
Deep forensic analysis of Legacy system trades to understand:
1. What signals were actually used
2. What data sources (order book features?)
3. Position management rules
4. Exit logic details
5. Any hidden features or filters
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hyperliquid_bot.backtester.run_backtest import run_backtest
from hyperliquid_bot.data_loader import DataLoader
from hyperliquid_bot.backtester.backtester import Backtester
from hyperliquid_bot.backtester.gms_detector import GMSDetector
import yaml

class LegacySystemAnalyzer:
    def __init__(self, config):
        self.config = config
        self.trade_analysis = []
        self.feature_usage = {}
        self.hidden_rules = []
        
    def analyze_trade_entry(self, timestamp, data_row, regime_data, signals, trade_decision):
        """Capture ALL information about why a trade was taken"""
        analysis = {
            'timestamp': timestamp,
            'trade_id': len(self.trade_analysis) + 1,
            'direction': trade_decision.get('direction'),
            
            # Regime information
            'regime': {
                'state': regime_data.get('state'),
                'confidence': regime_data.get('confidence'),
                'duration': regime_data.get('duration_minutes'),
                'mom_ratio': regime_data.get('mom_ratio'),
                'spread_ratio': regime_data.get('spread_ratio'),
                'obi_ratio': regime_data.get('obi_ratio')
            },
            
            # Market data used
            'market_data': {
                'close': data_row.get('close'),
                'volume': data_row.get('volume'),
                'high_low_spread': data_row.get('high') - data_row.get('low'),
                'volatility': data_row.get('volatility', 0)
            },
            
            # Technical indicators
            'indicators': {
                'ema_fast': signals.get('ema_fast'),
                'ema_slow': signals.get('ema_slow'),
                'ema_diff': signals.get('ema_fast', 0) - signals.get('ema_slow', 0),
                'forecast': signals.get('forecast'),
                'rsi': signals.get('rsi')
            },
            
            # Order book features - THIS IS KEY!
            'order_book': {
                'imbalance': data_row.get('imbalance'),  # Legacy specific field!
                'bid_size_1': data_row.get('bid_sz_1'),
                'ask_size_1': data_row.get('ask_sz_1'),
                'spread': data_row.get('ask_px_1', 0) - data_row.get('bid_px_1', 0) if data_row.get('bid_px_1') else None,
                # Check for deeper book usage
                'uses_deep_book': any(data_row.get(f'bid_sz_{i}') for i in range(6, 21))
            },
            
            # Special conditions or filters
            'special_conditions': {
                'hour_of_day': timestamp.hour,
                'day_of_week': timestamp.weekday(),
                'volume_spike': data_row.get('volume', 0) > signals.get('avg_volume', 1) * 2,
                'high_volatility': data_row.get('volatility', 0) > self.config.get('max_volatility', float('inf'))
            },
            
            # Position context
            'position_context': {
                'existing_positions': 0,  # Need to track this
                'recent_pnl': 0,  # Need to track this
                'drawdown': 0  # Need to track this
            }
        }
        
        self.trade_analysis.append(analysis)
        
        # Track feature usage
        if data_row.get('imbalance') is not None:
            self.feature_usage['imbalance'] = self.feature_usage.get('imbalance', 0) + 1
        
        # Detect patterns
        self._detect_hidden_rules(analysis)
        
        return analysis
    
    def _detect_hidden_rules(self, trade):
        """Identify patterns that might be hidden rules"""
        # Time-based filters
        hour = trade['special_conditions']['hour_of_day']
        if 2 <= hour <= 6:  # Low liquidity hours
            if 'no_trades_2am_6am' not in self.hidden_rules:
                trades_in_period = sum(1 for t in self.trade_analysis 
                                     if 2 <= t['special_conditions']['hour_of_day'] <= 6)
                if trades_in_period == 0:
                    self.hidden_rules.append('no_trades_2am_6am')
        
        # Order book imbalance usage
        if trade['order_book']['imbalance'] is not None:
            if 'uses_order_book_imbalance' not in self.hidden_rules:
                self.hidden_rules.append('uses_order_book_imbalance')
    
    def run_forensic_backtest(self):
        """Run a modified backtest that captures everything"""
        print("Running forensic analysis on Legacy system...")
        
        # Monkey-patch the backtester to capture trade decisions
        original_backtester = Backtester
        analyzer = self
        
        class ForensicBacktester(original_backtester):
            def generate_signals(self, data, timestamp):
                signals = super().generate_signals(data, timestamp)
                
                # Capture the decision process
                if hasattr(self, 'last_trade_decision') and self.last_trade_decision:
                    regime_data = self.gms_detector.compute_regime(data)
                    analyzer.analyze_trade_entry(
                        timestamp, 
                        data.iloc[-1], 
                        regime_data.iloc[-1],
                        signals,
                        self.last_trade_decision
                    )
                
                return signals
        
        # Replace with forensic version temporarily
        import hyperliquid_bot.backtester.backtester
        hyperliquid_bot.backtester.backtester.Backtester = ForensicBacktester
        
        # Run backtest for January 2024
        results = run_backtest(
            symbol='BTC',
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 1, 31),
            config_path='config.yaml'
        )
        
        # Restore original
        hyperliquid_bot.backtester.backtester.Backtester = original_backtester
        
        return results
    
    def generate_report(self):
        """Generate comprehensive analysis report"""
        print("\n" + "="*60)
        print("LEGACY SYSTEM FORENSIC ANALYSIS REPORT")
        print("="*60)
        
        print(f"\n1. TRADES ANALYZED: {len(self.trade_analysis)}")
        
        print("\n2. FEATURE USAGE:")
        for feature, count in self.feature_usage.items():
            print(f"   - {feature}: {count} times ({count/max(1,len(self.trade_analysis))*100:.1f}%)")
        
        print("\n3. HIDDEN RULES DISCOVERED:")
        for rule in self.hidden_rules:
            print(f"   - {rule}")
        
        print("\n4. TRADE PATTERNS:")
        # Analyze time distribution
        hours = [t['special_conditions']['hour_of_day'] for t in self.trade_analysis]
        if hours:
            print(f"   - Most active hours: {pd.Series(hours).value_counts().head(3).to_dict()}")
        
        # Analyze regime usage
        regimes = [t['regime']['state'] for t in self.trade_analysis]
        if regimes:
            print(f"   - Regime distribution: {pd.Series(regimes).value_counts().to_dict()}")
        
        print("\n5. ORDER BOOK ANALYSIS:")
        imbalance_trades = [t for t in self.trade_analysis if t['order_book']['imbalance'] is not None]
        print(f"   - Trades using 'imbalance' field: {len(imbalance_trades)} ({len(imbalance_trades)/max(1,len(self.trade_analysis))*100:.1f}%)")
        
        deep_book_trades = [t for t in self.trade_analysis if t['order_book']['uses_deep_book']]
        print(f"   - Trades using deep book (L6-L20): {len(deep_book_trades)}")
        
        print("\n6. KEY INSIGHTS:")
        # Check for position limits
        print("   - Max concurrent positions: [Need to track in backtester]")
        
        # Check exit logic
        print("   - Exit strategy: [Need to analyze exit points]")
        
        # Save detailed results
        report = {
            'summary': {
                'total_trades': len(self.trade_analysis),
                'feature_usage': self.feature_usage,
                'hidden_rules': self.hidden_rules
            },
            'detailed_trades': self.trade_analysis[:10],  # First 10 trades
            'patterns': {
                'hourly_distribution': pd.Series(hours).value_counts().to_dict() if hours else {},
                'regime_distribution': pd.Series(regimes).value_counts().to_dict() if regimes else {},
                'uses_imbalance': len(imbalance_trades) > 0,
                'imbalance_percentage': len(imbalance_trades)/max(1,len(self.trade_analysis))*100
            }
        }
        
        with open('legacy_forensic_analysis.json', 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n\nDetailed report saved to legacy_forensic_analysis.json")
        
        # CRITICAL FINDING
        if len(imbalance_trades) > 0:
            print("\n" + "🚨"*20)
            print("CRITICAL DISCOVERY: Legacy uses 'imbalance' field!")
            print("This is NOT available in modern 1s data!")
            print("This could be the SECRET SAUCE!")
            print("🚨"*20)

def main():
    # Load config
    with open('config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    analyzer = LegacySystemAnalyzer(config)
    
    # Run forensic backtest
    results = analyzer.run_forensic_backtest()
    
    # Generate report
    analyzer.generate_report()
    
    print(f"\nBacktest Results:")
    print(f"Total Return: {results.get('total_return', 0):.2%}")
    print(f"Total Trades: {results.get('total_trades', 0)}")

if __name__ == "__main__":
    main()