#!/usr/bin/env python3
"""
R-112c GMS Input Diagnosis Script

This script instruments the continuous_gms detector to capture raw volatility
and momentum inputs during a backtest run, then analyzes the distributions
to understand why 95% of outputs are "Low_Vol_Range" (CHOP).

Usage:
    python scripts/diagnose_gms_inputs.py
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.backtester.backtester import Backtester
from hyperliquid_bot.core.gms_detector import ContinuousGMSDetector
import logging

# Global storage for captured data
captured_data = []

def capture_gms_inputs(original_update_method):
    """Decorator to capture GMS inputs during detector updates"""
    def wrapper(self, signals_row):
        # Extract the key inputs before processing
        atr_pct = signals_row.get('atr_percent', np.nan)
        ma_slope = signals_row.get('ma_slope', np.nan)
        timestamp = signals_row.get('timestamp', 'N/A')

        # Store the raw inputs
        captured_data.append({
            'timestamp': timestamp,
            'atr_percent': atr_pct,
            'ma_slope': ma_slope,
            'vol_low_thresh': self.vol_low_thresh,
            'vol_high_thresh': self.vol_high_thresh,
            'mom_weak_thresh': self.mom_weak_thresh,
            'mom_strong_thresh': self.mom_strong_thresh
        })

        # Call the original method
        return original_update_method(signals_row)

    return wrapper

def monkey_patch_gms_detector():
    """Monkey patch the GMS detector to capture inputs"""
    original_update = ContinuousGMSDetector.update
    ContinuousGMSDetector.update = capture_gms_inputs(original_update)

def run_diagnostic_backtest():
    """Run a short backtest to capture GMS inputs"""
    print("Loading configuration...")
    config = load_config("configs/base.yaml")

    # Ensure we're using continuous_gms + tf_v3
    config.regime.detector_type = 'continuous_gms'
    config.strategies.use_tf_v3 = True
    config.strategies.use_tf_v2 = False

    # Use a single day for quick diagnosis
    config.backtest.custom_start_date = "2025-03-01"
    config.backtest.custom_end_date = "2025-03-02"

    print("Monkey patching GMS detector...")
    monkey_patch_gms_detector()

    print("Running diagnostic backtest...")
    backtester = Backtester(config)

    try:
        results = backtester.run(
            start_date=config.backtest.custom_start_date,
            end_date=config.backtest.custom_end_date
        )
        print(f"Backtest completed. Captured {len(captured_data)} data points.")
        return results
    except Exception as e:
        print(f"Backtest failed: {e}")
        return None

def analyze_captured_data():
    """Analyze the captured GMS input data"""
    if not captured_data:
        print("No data captured!")
        return

    df = pd.DataFrame(captured_data)

    # Remove NaN values for analysis
    df_clean = df.dropna(subset=['atr_percent', 'ma_slope'])

    print(f"\n=== GMS INPUT ANALYSIS ===")
    print(f"Total data points: {len(df)}")
    print(f"Clean data points (no NaN): {len(df_clean)}")

    if len(df_clean) == 0:
        print("No clean data available for analysis!")
        return

    # Volatility analysis
    print(f"\n--- VOLATILITY (atr_percent) ---")
    atr_stats = df_clean['atr_percent'].describe()
    print(atr_stats)

    vol_low_thresh = df_clean['vol_low_thresh'].iloc[0]
    vol_high_thresh = df_clean['vol_high_thresh'].iloc[0]

    print(f"\nThresholds:")
    print(f"  vol_low_thresh:  {vol_low_thresh:.4f}")
    print(f"  vol_high_thresh: {vol_high_thresh:.4f}")

    low_vol_count = (df_clean['atr_percent'] <= vol_low_thresh).sum()
    mid_vol_count = ((df_clean['atr_percent'] > vol_low_thresh) &
                     (df_clean['atr_percent'] < vol_high_thresh)).sum()
    high_vol_count = (df_clean['atr_percent'] >= vol_high_thresh).sum()

    print(f"\nVolatility Buckets:")
    print(f"  Low Vol  (≤{vol_low_thresh:.4f}): {low_vol_count:4d} ({low_vol_count/len(df_clean)*100:.1f}%)")
    print(f"  Mid Vol  (mid-range):        {mid_vol_count:4d} ({mid_vol_count/len(df_clean)*100:.1f}%)")
    print(f"  High Vol (≥{vol_high_thresh:.4f}): {high_vol_count:4d} ({high_vol_count/len(df_clean)*100:.1f}%)")

    # Momentum analysis
    print(f"\n--- MOMENTUM (ma_slope) ---")
    mom_stats = df_clean['ma_slope'].describe()
    print(mom_stats)

    mom_weak_thresh = df_clean['mom_weak_thresh'].iloc[0]
    mom_strong_thresh = df_clean['mom_strong_thresh'].iloc[0]

    print(f"\nThresholds:")
    print(f"  mom_weak_thresh:   {mom_weak_thresh:.2f}")
    print(f"  mom_strong_thresh: {mom_strong_thresh:.2f}")

    weak_mom_count = (np.abs(df_clean['ma_slope']) < mom_weak_thresh).sum()
    mid_mom_count = ((np.abs(df_clean['ma_slope']) >= mom_weak_thresh) &
                     (np.abs(df_clean['ma_slope']) < mom_strong_thresh)).sum()
    strong_mom_count = (np.abs(df_clean['ma_slope']) >= mom_strong_thresh).sum()

    print(f"\nMomentum Buckets:")
    print(f"  Weak Mom   (<{mom_weak_thresh:.2f}):   {weak_mom_count:4d} ({weak_mom_count/len(df_clean)*100:.1f}%)")
    print(f"  Medium Mom (mid-range):     {mid_mom_count:4d} ({mid_mom_count/len(df_clean)*100:.1f}%)")
    print(f"  Strong Mom (≥{mom_strong_thresh:.2f}): {strong_mom_count:4d} ({strong_mom_count/len(df_clean)*100:.1f}%)")

    # Combined analysis for Low_Vol_Range classification
    low_vol_weak_mom = ((df_clean['atr_percent'] <= vol_low_thresh) &
                        (np.abs(df_clean['ma_slope']) < mom_weak_thresh)).sum()

    print(f"\n--- COMBINED ANALYSIS ---")
    print(f"Low Vol + Weak Mom (→ Low_Vol_Range): {low_vol_weak_mom:4d} ({low_vol_weak_mom/len(df_clean)*100:.1f}%)")

    return df_clean

def create_histogram_plots(df_clean):
    """Create histogram plots of the input distributions"""
    if df_clean is None or len(df_clean) == 0:
        return

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    # Volatility histogram
    ax1.hist(df_clean['atr_percent'], bins=50, alpha=0.7, color='blue', edgecolor='black')
    ax1.axvline(df_clean['vol_low_thresh'].iloc[0], color='red', linestyle='--',
                label=f'Low Thresh ({df_clean["vol_low_thresh"].iloc[0]:.4f})')
    ax1.axvline(df_clean['vol_high_thresh'].iloc[0], color='orange', linestyle='--',
                label=f'High Thresh ({df_clean["vol_high_thresh"].iloc[0]:.4f})')
    ax1.set_xlabel('ATR Percent')
    ax1.set_ylabel('Frequency')
    ax1.set_title('Volatility Distribution (atr_percent)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Momentum histogram
    ax2.hist(df_clean['ma_slope'], bins=50, alpha=0.7, color='green', edgecolor='black')
    ax2.axvline(df_clean['mom_weak_thresh'].iloc[0], color='red', linestyle='--',
                label=f'Weak Thresh ({df_clean["mom_weak_thresh"].iloc[0]:.2f})')
    ax2.axvline(-df_clean['mom_weak_thresh'].iloc[0], color='red', linestyle='--')
    ax2.axvline(df_clean['mom_strong_thresh'].iloc[0], color='orange', linestyle='--',
                label=f'Strong Thresh ({df_clean["mom_strong_thresh"].iloc[0]:.2f})')
    ax2.axvline(-df_clean['mom_strong_thresh'].iloc[0], color='orange', linestyle='--')
    ax2.set_xlabel('MA Slope')
    ax2.set_ylabel('Frequency')
    ax2.set_title('Momentum Distribution (ma_slope)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()

    # Save the plot
    artifacts_dir = project_root / "artifacts"
    artifacts_dir.mkdir(exist_ok=True)
    plot_path = artifacts_dir / "r112c_gms_input_histograms.png"
    plt.savefig(plot_path, dpi=150, bbox_inches='tight')
    print(f"\nHistogram saved to: {plot_path}")

    plt.show()

def generate_summary_report(df_clean):
    """Generate a markdown summary report"""
    artifacts_dir = project_root / "artifacts"
    artifacts_dir.mkdir(exist_ok=True)
    report_path = artifacts_dir / "r112c_state_diagnosis.md"

    if df_clean is None or len(df_clean) == 0:
        with open(report_path, 'w') as f:
            f.write("# R-112c GMS Input Diagnosis\n\n")
            f.write("**ERROR**: No clean data captured during backtest run.\n")
        return

    vol_low_thresh = df_clean['vol_low_thresh'].iloc[0]
    vol_high_thresh = df_clean['vol_high_thresh'].iloc[0]
    mom_weak_thresh = df_clean['mom_weak_thresh'].iloc[0]
    mom_strong_thresh = df_clean['mom_strong_thresh'].iloc[0]

    low_vol_count = (df_clean['atr_percent'] <= vol_low_thresh).sum()
    weak_mom_count = (np.abs(df_clean['ma_slope']) < mom_weak_thresh).sum()
    low_vol_weak_mom = ((df_clean['atr_percent'] <= vol_low_thresh) &
                        (np.abs(df_clean['ma_slope']) < mom_weak_thresh)).sum()

    with open(report_path, 'w') as f:
        f.write("# R-112c GMS Input Diagnosis\n\n")
        f.write("## Summary\n\n")
        f.write(f"Analysis of GMS detector inputs during 2025-03-01 to 2025-03-02 backtest.\n\n")
        f.write(f"**Total data points analyzed**: {len(df_clean)}\n\n")

        f.write("## Configuration Thresholds\n\n")
        f.write("| Metric | Low Threshold | High Threshold |\n")
        f.write("|--------|---------------|----------------|\n")
        f.write(f"| Volatility (ATR %) | {vol_low_thresh:.4f} | {vol_high_thresh:.4f} |\n")
        f.write(f"| Momentum (MA Slope) | {mom_weak_thresh:.2f} | {mom_strong_thresh:.2f} |\n\n")

        f.write("## Input Statistics\n\n")
        f.write("### Volatility (atr_percent)\n")
        f.write(f"- Mean: {df_clean['atr_percent'].mean():.6f}\n")
        f.write(f"- Median: {df_clean['atr_percent'].median():.6f}\n")
        f.write(f"- 5th percentile: {df_clean['atr_percent'].quantile(0.05):.6f}\n")
        f.write(f"- 95th percentile: {df_clean['atr_percent'].quantile(0.95):.6f}\n\n")

        f.write("### Momentum (ma_slope)\n")
        f.write(f"- Mean: {df_clean['ma_slope'].mean():.4f}\n")
        f.write(f"- Median: {df_clean['ma_slope'].median():.4f}\n")
        f.write(f"- 5th percentile: {df_clean['ma_slope'].quantile(0.05):.4f}\n")
        f.write(f"- 95th percentile: {df_clean['ma_slope'].quantile(0.95):.4f}\n\n")

        f.write("## Bucket Classification\n\n")
        f.write("| Bucket | Count | Percentage |\n")
        f.write("|--------|-------|------------|\n")
        f.write(f"| Low Vol (≤{vol_low_thresh:.4f}) | {low_vol_count} | {low_vol_count/len(df_clean)*100:.1f}% |\n")
        f.write(f"| Weak Mom (<{mom_weak_thresh:.2f}) | {weak_mom_count} | {weak_mom_count/len(df_clean)*100:.1f}% |\n")
        f.write(f"| **Low Vol + Weak Mom** | **{low_vol_weak_mom}** | **{low_vol_weak_mom/len(df_clean)*100:.1f}%** |\n\n")

        f.write("## Hypothesis\n\n")
        if vol_low_thresh > 0.1:  # If threshold is > 10%
            f.write("**CONFIGURATION ISSUE DETECTED**: The volatility thresholds appear to be misconfigured.\n")
            f.write(f"- Current low threshold: {vol_low_thresh:.4f} ({vol_low_thresh*100:.1f}%)\n")
            f.write(f"- Typical ATR values: {df_clean['atr_percent'].median():.6f} ({df_clean['atr_percent'].median()*100:.3f}%)\n\n")
            f.write("The thresholds are likely in percentage points (0.55 = 55%) rather than decimal form (0.0055 = 0.55%).\n")
            f.write("This causes virtually all periods to be classified as 'Low Vol', leading to 95% CHOP classification.\n\n")
        else:
            f.write("The volatility thresholds appear reasonable. The high CHOP classification may be due to:\n")
            f.write("1. Genuinely low volatility period in the market\n")
            f.write("2. Momentum thresholds being too high\n")
            f.write("3. Other factors in the GMS logic\n\n")

        f.write("## Files Generated\n\n")
        f.write("- `artifacts/r112c_gms_input_histograms.png` - Distribution plots\n")
        f.write("- `artifacts/r112c_state_diagnosis.md` - This report\n")

    print(f"\nSummary report saved to: {report_path}")

def main():
    """Main execution function"""
    print("=== R-112c GMS Input Diagnosis ===")

    # Run diagnostic backtest
    results = run_diagnostic_backtest()

    # Analyze captured data
    df_clean = analyze_captured_data()

    # Create visualizations
    if df_clean is not None and len(df_clean) > 0:
        create_histogram_plots(df_clean)

    # Generate summary report
    generate_summary_report(df_clean)

    print("\n=== Diagnosis Complete ===")

if __name__ == "__main__":
    main()
