#!/usr/bin/env python
# scripts/patch_tf_v3_for_backtest.py

"""
Patch the TF-v3 strategy to disable state loading during backtesting.

This script modifies the TF-v3 strategy implementation to disable state loading
during backtesting, which prevents the strategy from thinking it already has a position.
"""

import os
import re
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def patch_tf_v3_strategy():
    """Patch the TF-v3 strategy to disable state loading during backtesting."""
    # Get the project root directory
    project_root = Path(__file__).parent.parent.resolve()
    
    # Define the strategy file path
    strategy_file = project_root / "hyperliquid_bot" / "strategies" / "tf_v3.py"
    
    if not strategy_file.exists():
        logger.error(f"Strategy file not found: {strategy_file}")
        return
    
    # Read the strategy file
    with open(strategy_file, 'r') as f:
        content = f.read()
    
    # Create a backup of the original file
    backup_file = strategy_file.with_suffix('.py.bak')
    with open(backup_file, 'w') as f:
        f.write(content)
    logger.info(f"Created backup of strategy file: {backup_file}")
    
    # Patch the _load_state method to disable state loading during backtesting
    patched_content = re.sub(
        r'def _load_state\(self\) -> None:.*?try:.*?with open\(state_file, \'r\'\) as f:',
        'def _load_state(self) -> None:\n        """\n        Load strategy state from disk.\n\n        This method loads the strategy state from a JSON file in the strategy\'s\n        state directory. If the file doesn\'t exist, the state remains unchanged.\n        """\n        # Check if we are in backtest mode\n        if hasattr(self.config, "backtest") and self.config.backtest:\n            self.logger.info("Skipping state loading in backtest mode")\n            return\n\n        # Create state file path\n        state_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "state")\n        state_file = os.path.join(state_dir, f"tf_v3_{self.strategy_name}.json")\n\n        # Load state from file if it exists\n        if os.path.exists(state_file):\n            try:\n                with open(state_file, \'r\') as f:',
        content,
        flags=re.DOTALL
    )
    
    # Patch the _check_ema_alignment method to disable position check during backtesting
    patched_content = re.sub(
        r'# Check if we already have a position \(no pyramiding\).*?have_long = have_long or position_type == \'long\'.*?have_short = have_short or position_type == \'short\'',
        '# Check if we already have a position (no pyramiding)\n        have_long = False\n        have_short = False\n\n        # Skip position check in backtest mode\n        if not hasattr(self.config, "backtest") or not self.config.backtest:\n            # Check portfolio first\n            if self.portfolio and self.portfolio.position:\n                position_type = self.portfolio.position.get(\'type\')\n                have_long = position_type == \'long\'\n                have_short = position_type == \'short\'\n\n            # Also check state (in case portfolio is not available)\n            position_type = self.state.get(\'position_type\')\n            if position_type:\n                have_long = have_long or position_type == \'long\'\n                have_short = have_short or position_type == \'short\'',
        patched_content,
        flags=re.DOTALL
    )
    
    # Write the patched content back to the file
    with open(strategy_file, 'w') as f:
        f.write(patched_content)
    
    logger.info(f"Patched TF-v3 strategy file: {strategy_file}")

if __name__ == "__main__":
    patch_tf_v3_strategy()
