#!/usr/bin/env python3
"""
Validation script for hourly feature aggregation quality.

This script validates that the hourly aggregation fix from Task R-113
works correctly across the complete dataset by sampling dates and
checking feature quality.
"""

import sys
import pandas as pd
from pathlib import Path
from datetime import datetime
import logging

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def validate_date_features(date_str: str, config) -> dict:
    """
    Validate feature quality for a specific date.
    
    Args:
        date_str: Date in YYYY-MM-DD format
        config: Configuration object
        
    Returns:
        Dictionary with validation results
    """
    logger.info(f"Validating features for {date_str}")
    
    features_dir = Path(config.data_paths.feature_1s_dir) / date_str
    
    if not features_dir.exists():
        return {'status': 'missing', 'error': f"Features directory not found: {features_dir}"}
    
    # Get all feature files for this date
    feature_files = list(features_dir.glob("features_*.parquet"))
    if not feature_files:
        return {'status': 'missing', 'error': f"No feature files found in {features_dir}"}
    
    # Critical features to check
    critical_features = [
        'spread_mean', 'spread_std', 'ma_slope_ema_30s', 
        'atr_14_sec', 'atr_percent_sec', 'atr', 'atr_percent'
    ]
    
    # OBI features to check
    obi_features = ['raw_obi_5', 'obi_smoothed_5', 'obi_zscore_5']
    
    all_features = critical_features + obi_features
    
    results = {
        'status': 'success',
        'date': date_str,
        'total_files': len(feature_files),
        'total_rows': 0,
        'feature_stats': {},
        'missing_features': [],
        'high_nan_features': []
    }
    
    # Load and combine all hourly files for this date
    combined_df = pd.DataFrame()
    
    for feature_file in sorted(feature_files):
        try:
            df = pd.read_parquet(feature_file)
            combined_df = pd.concat([combined_df, df], ignore_index=True)
        except Exception as e:
            logger.warning(f"Error loading {feature_file}: {e}")
    
    if combined_df.empty:
        return {'status': 'error', 'error': 'No data loaded from feature files'}
    
    results['total_rows'] = len(combined_df)
    
    # Check each critical feature
    for feature in all_features:
        if feature not in combined_df.columns:
            results['missing_features'].append(feature)
        else:
            nan_count = combined_df[feature].isna().sum()
            nan_ratio = nan_count / len(combined_df)
            
            results['feature_stats'][feature] = {
                'nan_count': nan_count,
                'nan_ratio': nan_ratio,
                'total_values': len(combined_df)
            }
            
            # Flag features with high NaN ratios (>10% for most, >20% for early hours)
            threshold = 0.20 if feature.startswith('atr') else 0.10
            if nan_ratio > threshold:
                results['high_nan_features'].append((feature, f"{nan_ratio:.1%}"))
    
    return results


def main():
    """Main validation function."""
    logger.info("=== VALIDATING HOURLY FEATURE AGGREGATION QUALITY ===")
    
    # Load configuration
    try:
        config = load_config('configs/base.yaml')
    except Exception as e:
        logger.error(f"Failed to load configuration: {e}")
        sys.exit(1)
    
    # Sample dates from different periods
    sample_dates = [
        '2025-03-03',  # Early March
        '2025-03-12',  # Mid March  
        '2025-03-20'   # Late March
    ]
    
    all_results = {}
    overall_success = True
    
    for date_str in sample_dates:
        results = validate_date_features(date_str, config)
        all_results[date_str] = results
        
        if results['status'] != 'success':
            overall_success = False
            logger.error(f"❌ Validation failed for {date_str}: {results.get('error', 'Unknown error')}")
        else:
            logger.info(f"✅ Validation passed for {date_str}")
            logger.info(f"   Total rows: {results['total_rows']}")
            logger.info(f"   Missing features: {len(results['missing_features'])}")
            logger.info(f"   High NaN features: {len(results['high_nan_features'])}")
            
            if results['missing_features']:
                logger.warning(f"   Missing: {results['missing_features']}")
            if results['high_nan_features']:
                logger.warning(f"   High NaN: {results['high_nan_features']}")
    
    # Summary report
    logger.info("\n=== VALIDATION SUMMARY ===")
    for date_str, results in all_results.items():
        if results['status'] == 'success':
            logger.info(f"{date_str}: ✅ PASS ({results['total_rows']} rows)")
            
            # Log critical feature stats
            for feature in ['spread_mean', 'spread_std', 'ma_slope_ema_30s']:
                if feature in results['feature_stats']:
                    stats = results['feature_stats'][feature]
                    logger.info(f"  {feature}: {stats['nan_ratio']:.1%} NaN")
        else:
            logger.error(f"{date_str}: ❌ FAIL - {results.get('error', 'Unknown error')}")
    
    if overall_success:
        logger.info("\n🎉 ALL VALIDATIONS PASSED - Hourly aggregation fix working correctly!")
        return 0
    else:
        logger.error("\n💥 SOME VALIDATIONS FAILED - Check logs for details")
        return 1


if __name__ == "__main__":
    sys.exit(main())
