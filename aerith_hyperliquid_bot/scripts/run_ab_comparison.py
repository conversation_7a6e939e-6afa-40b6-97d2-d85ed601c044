#!/usr/bin/env python3
"""
A/B Backtest Comparison Tool
============================

Compare results from legacy vs enhanced detector backtests.
Run this after running both individual backtests.
"""

import sys
import json
from pathlib import Path
from datetime import datetime
import glob

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def load_latest_results(pattern):
    """Load the most recent results file matching pattern."""
    files = sorted(glob.glob(pattern), key=lambda x: Path(x).stat().st_mtime, reverse=True)
    if not files:
        return None
    
    with open(files[0], 'r') as f:
        return json.load(f), files[0]


def compare_results(legacy_results, enhanced_results):
    """Compare and analyze the two result sets."""
    print("\n" + "="*80)
    print("A/B Backtest Comparison Analysis")
    print("="*80)
    
    # Basic metrics comparison
    print("\n1. TRADE FREQUENCY")
    print("-" * 40)
    legacy_trades = legacy_results.get('total_trades', 0)
    enhanced_trades = enhanced_results.get('total_trades', 0)
    reduction = (legacy_trades - enhanced_trades) / legacy_trades * 100 if legacy_trades > 0 else 0
    
    print(f"Legacy Detector:   {legacy_trades} trades")
    print(f"Enhanced Detector: {enhanced_trades} trades")
    print(f"Trade Reduction:   {reduction:.1f}%")
    
    # Annualized trade frequency (assuming 365 days)
    print(f"\nAnnualized:")
    print(f"Legacy:   {legacy_trades:.0f} trades/year")
    print(f"Enhanced: {enhanced_trades:.0f} trades/year")
    
    # Win rate comparison
    print("\n2. WIN RATE")
    print("-" * 40)
    legacy_wr = legacy_results.get('win_rate', 0)
    enhanced_wr = enhanced_results.get('win_rate', 0)
    wr_improvement = enhanced_wr - legacy_wr
    
    print(f"Legacy Detector:   {legacy_wr:.2%}")
    print(f"Enhanced Detector: {enhanced_wr:.2%}")
    print(f"Improvement:       {wr_improvement:+.2%}")
    
    # Returns comparison
    print("\n3. RETURNS")
    print("-" * 40)
    legacy_return = legacy_results.get('total_return', 0)
    enhanced_return = enhanced_results.get('total_return', 0)
    
    print(f"Legacy Detector:   {legacy_return:+.2%}")
    print(f"Enhanced Detector: {enhanced_return:+.2%}")
    print(f"Difference:        {enhanced_return - legacy_return:+.2%}")
    
    # Risk metrics
    print("\n4. RISK METRICS")
    print("-" * 40)
    legacy_sharpe = legacy_results.get('sharpe_ratio', 0)
    enhanced_sharpe = enhanced_results.get('sharpe_ratio', 0)
    legacy_dd = legacy_results.get('max_drawdown', 0)
    enhanced_dd = enhanced_results.get('max_drawdown', 0)
    
    print(f"Sharpe Ratio:")
    print(f"  Legacy:   {legacy_sharpe:.2f}")
    print(f"  Enhanced: {enhanced_sharpe:.2f}")
    print(f"  Change:   {enhanced_sharpe - legacy_sharpe:+.2f}")
    
    print(f"\nMax Drawdown:")
    print(f"  Legacy:   {legacy_dd:.2%}")
    print(f"  Enhanced: {enhanced_dd:.2%}")
    print(f"  Change:   {enhanced_dd - legacy_dd:+.2%}")
    
    # Average return per trade
    print("\n5. PER-TRADE METRICS")
    print("-" * 40)
    if legacy_trades > 0:
        legacy_avg = legacy_results.get('average_return', 0)
        print(f"Legacy Avg Return/Trade:   {legacy_avg:.3%}")
    
    if enhanced_trades > 0:
        enhanced_avg = enhanced_results.get('average_return', 0)
        print(f"Enhanced Avg Return/Trade: {enhanced_avg:.3%}")
    
    # Quality filtering effectiveness
    print("\n6. QUALITY FILTERING ANALYSIS")
    print("-" * 40)
    if legacy_trades > 0:
        trades_filtered = legacy_trades - enhanced_trades
        filter_rate = trades_filtered / legacy_trades * 100
        print(f"Trades Filtered Out: {trades_filtered} ({filter_rate:.1f}%)")
        
        # Check if filtering improved quality
        if enhanced_wr > legacy_wr:
            print("✓ Quality filtering improved win rate")
        else:
            print("✗ Quality filtering did not improve win rate")
        
        if enhanced_trades > 0 and enhanced_avg > legacy_avg:
            print("✓ Quality filtering improved avg return per trade")
        else:
            print("✗ Quality filtering did not improve avg return per trade")
    
    # Final verdict
    print("\n7. FINAL ASSESSMENT")
    print("-" * 40)
    
    # Score the enhanced detector
    score = 0
    reasons = []
    
    if enhanced_wr > legacy_wr:
        score += 2
        reasons.append(f"Higher win rate (+{wr_improvement:.2%})")
    
    if enhanced_return > legacy_return:
        score += 3
        reasons.append(f"Higher total return (+{enhanced_return - legacy_return:.2%})")
    
    if enhanced_sharpe > legacy_sharpe:
        score += 2
        reasons.append(f"Better Sharpe ratio (+{enhanced_sharpe - legacy_sharpe:.2f})")
    
    if abs(enhanced_dd) < abs(legacy_dd):
        score += 1
        reasons.append(f"Lower drawdown ({enhanced_dd - legacy_dd:.2%})")
    
    if 150 <= enhanced_trades <= 200:  # Target range
        score += 2
        reasons.append(f"Trade frequency in target range ({enhanced_trades} trades/year)")
    
    print(f"Enhanced Detector Score: {score}/10")
    
    if score >= 7:
        print("\n✅ RECOMMENDATION: Use Enhanced Detector")
        print("Reasons:")
        for r in reasons:
            print(f"  - {r}")
    elif score >= 4:
        print("\n⚠️  RECOMMENDATION: Enhanced Detector shows promise but needs tuning")
        print("Consider adjusting quality threshold (currently 0.7)")
    else:
        print("\n❌ RECOMMENDATION: Keep Legacy Detector")
        print("Enhanced detector did not improve performance sufficiently")
    
    return score


def main():
    """Main comparison function."""
    print("Loading A/B backtest results...")
    
    # Load results
    legacy_data = load_latest_results("ab_results_legacy_*.json")
    enhanced_data = load_latest_results("ab_results_enhanced_*.json")
    
    if not legacy_data:
        print("ERROR: No legacy results found. Run run_ab_backtest_legacy.py first.")
        return
    
    if not enhanced_data:
        print("ERROR: No enhanced results found. Run run_ab_backtest_enhanced.py first.")
        return
    
    legacy_results, legacy_file = legacy_data
    enhanced_results, enhanced_file = enhanced_data
    
    print(f"\nLegacy results from: {Path(legacy_file).name}")
    print(f"Enhanced results from: {Path(enhanced_file).name}")
    
    # Run comparison
    score = compare_results(legacy_results, enhanced_results)
    
    # Save comparison report
    report = {
        'timestamp': datetime.now().isoformat(),
        'legacy_file': legacy_file,
        'enhanced_file': enhanced_file,
        'score': score,
        'legacy_metrics': {
            'trades': legacy_results.get('total_trades', 0),
            'win_rate': legacy_results.get('win_rate', 0),
            'return': legacy_results.get('total_return', 0),
            'sharpe': legacy_results.get('sharpe_ratio', 0),
            'drawdown': legacy_results.get('max_drawdown', 0)
        },
        'enhanced_metrics': {
            'trades': enhanced_results.get('total_trades', 0),
            'win_rate': enhanced_results.get('win_rate', 0),
            'return': enhanced_results.get('total_return', 0),
            'sharpe': enhanced_results.get('sharpe_ratio', 0),
            'drawdown': enhanced_results.get('max_drawdown', 0)
        }
    }
    
    report_file = f"ab_comparison_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n\nComparison report saved to: {report_file}")


if __name__ == "__main__":
    main()