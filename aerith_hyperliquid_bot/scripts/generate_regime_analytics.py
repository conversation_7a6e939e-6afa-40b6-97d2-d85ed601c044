#!/usr/bin/env python3
"""
Generate regime analytics and transition visualization from backtest results.
This script is part of Phase 5 of the GMS State Mapping Standardization Plan.
"""

import os
import sys
import argparse
import logging
import pandas as pd
from pathlib import Path
import glob
import re

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the project root to the Python path to ensure imports work
PROJECT_ROOT = Path(__file__).parent.parent.resolve()
sys.path.insert(0, str(PROJECT_ROOT))

# Default log directory
LOGS_DIR = "/Users/<USER>/Desktop/trading_bot_/logs"

def find_latest_backtest_signals(log_dir=LOGS_DIR):
    """Find the most recent signals file in the logs directory."""
    try:
        signal_files = glob.glob(os.path.join(log_dir, "backtest_signals_*.parquet"))
        if not signal_files:
            logger.warning(f"No matching signal files found in {log_dir}")
            return None
            
        # Extract timestamps from filenames
        timestamps = {re.search(r"(\d{8}_\d{6})", f).group(1): f for f in signal_files 
                     if re.search(r"(\d{8}_\d{6})", f)}
        if not timestamps:
            logger.warning("No valid timestamps found in signal files")
            return None
            
        # Get the latest timestamp
        latest_timestamp = max(timestamps.keys())
        latest_signals = timestamps[latest_timestamp]
        logger.info(f"Found latest signals file from {latest_timestamp}: {latest_signals}")
        return latest_signals
    except Exception as e:
        logger.error(f"Error finding latest signals file: {e}")
        return None

def main():
    """Main function to generate regime analytics."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Generate regime analytics and visualizations")
    parser.add_argument("--signals", "-s", default=None, help="Path to signals file (backtest_signals_*.parquet)")
    parser.add_argument("--output", "-o", default="./plots", help="Output directory for analytics")
    parser.add_argument("--detailed", "-d", action="store_true", help="Generate detailed analytics report")
    parser.add_argument("--no-viz", action="store_true", help="Skip generating visualizations")
    args = parser.parse_args()
    
    # Find signals file if not provided
    signals_path = args.signals
    if not signals_path:
        signals_path = find_latest_backtest_signals()
        if not signals_path:
            print("ERROR: Could not find signals file. Please specify with --signals")
            return 1
    
    # Ensure output directory exists
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Try to import the RegimeAnalytics class
    try:
        from hyperliquid_bot.utils.regime_analytics import RegimeAnalytics
        logger.info("Successfully imported RegimeAnalytics module")
    except ImportError as e:
        print(f"ERROR: Could not import RegimeAnalytics module: {e}")
        print("Please ensure you've properly implemented Phase 5 of the GMS State Mapping Standardization Plan.")
        return 1
    
    # Load signals data
    try:
        print(f"Loading signals data from {signals_path}...")
        signals_df = pd.read_parquet(signals_path)
        print(f"Loaded {len(signals_df)} data points")
        
        # Check for 'regime' column
        if 'regime' not in signals_df.columns:
            print("ERROR: No 'regime' column found in signals data")
            return 1
            
        print(f"Found {signals_df['regime'].nunique()} unique regime states")
    except Exception as e:
        print(f"ERROR: Failed to load signals data: {e}")
        return 1
    
    # Create RegimeAnalytics instance
    analytics = RegimeAnalytics(signals_df, output_dir=output_dir)
    
    # Generate analytics
    try:
        # Always generate regime transition timeline
        if not args.no_viz:
            print("Generating regime transition visualization...")
            transitions_path = analytics.generate_transition_timeline()
            print(f"Saved regime transition visualization to: {transitions_path}")
        
        # Generate detailed report if requested
        if args.detailed:
            print("Generating detailed regime analytics report...")
            report_path = analytics.generate_regime_analytics_report()
            print(f"Saved detailed regime analytics report to: {report_path}")
            
            # Display summary statistics
            print("\nRegime Statistics Summary:")
            results = analytics.analyze_regime_transitions()
            
            # Show regime counts
            print("\nRegime State Counts:")
            for regime, count in results['regime_counts'].items():
                print(f"  {regime}: {count}")
                
            # Show TIGHT_SPREAD information if present
            tight_spread_count = results['regime_counts'].get('TIGHT_SPREAD', 0)
            if tight_spread_count > 0:
                print(f"\nTIGHT_SPREAD Detections: {tight_spread_count}")
                print(f"TIGHT_SPREAD Transitions: {results['tight_spread_transition_count']}")
                
            # Show transition counts
            print(f"\nTotal State Transitions: {results['transition_count']}")
            
            # Show top 5 transition patterns
            print("\nTop 5 Transition Patterns:")
            for i, pattern in enumerate(results['transition_patterns'][:5], 1):
                print(f"  {i}. {pattern['from_state']} → {pattern['to_state']}: {pattern['count']} times")
    
    except Exception as e:
        print(f"ERROR: Failed to generate analytics: {e}")
        logger.exception("Detailed error:")
        return 1
        
    print("\nRegime analytics generation completed successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
