#!/usr/bin/env python3
"""
Systematic Debug Script for Modern Trading System
================================================

This script implements comprehensive logging and analysis to identify
why the modern system produces 0 trades. We test 5 key hypotheses:

1. Data Loading/Adaptation Issue
2. Detector State Logic Issue  
3. Signal Quality Gating Issue
4. Threshold Calibration Issue
5. Strategy Evaluation Issue

NO PATCHES - ONLY DIAGNOSIS
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Tuple

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.data_loader import ModernDataLoader
from hyperliquid_bot.modern.continuous_detector_v2 import ModernContinuousDetectorV2
from hyperliquid_bot.modern.adapters.data_adapter import ModernDataAdapter, AdapterConfig
from hyperliquid_bot.utils.state_mapping import *


class SystematicDebugger:
    """Comprehensive debugger for modern trading system."""
    
    def __init__(self, config_path: str):
        self.config = load_config(config_path)
        self.results = {
            'data_loading': {},
            'signal_adaptation': {},
            'detector_states': {},
            'signal_quality': {},
            'threshold_analysis': {},
            'strategy_evaluation': {}
        }
        
        # Setup logging
        self.setup_logging()
        
    def setup_logging(self):
        """Setup comprehensive logging."""
        log_file = f"logs/systematic_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # File handler
        fh = logging.FileHandler(log_file)
        fh.setLevel(logging.DEBUG)
        fh.setFormatter(formatter)
        
        # Console handler
        ch = logging.StreamHandler()
        ch.setLevel(logging.INFO)
        ch.setFormatter(formatter)
        
        # Setup root logger
        logger = logging.getLogger()
        logger.setLevel(logging.DEBUG)
        logger.addHandler(fh)
        logger.addHandler(ch)
        
        self.logger = logger
        self.log_file = log_file
        
    def run_full_diagnosis(self, start_date: str, end_date: str):
        """Run complete system diagnosis."""
        self.logger.info("="*80)
        self.logger.info("SYSTEMATIC DEBUG ANALYSIS - MODERN TRADING SYSTEM")
        self.logger.info("="*80)
        
        # Test each hypothesis
        self.test_hypothesis_1_data_loading(start_date, end_date)
        self.test_hypothesis_2_detector_states(start_date, end_date)
        self.test_hypothesis_3_signal_quality(start_date, end_date)
        self.test_hypothesis_4_threshold_calibration(start_date, end_date)
        self.test_hypothesis_5_strategy_evaluation(start_date, end_date)
        
        # Generate comprehensive report
        self.generate_report()
        
    def test_hypothesis_1_data_loading(self, start_date: str, end_date: str):
        """Test if data is loading and adapting correctly."""
        self.logger.info("\n" + "="*60)
        self.logger.info("HYPOTHESIS 1: Data Loading/Adaptation Issue")
        self.logger.info("="*60)
        
        try:
            # Initialize data loader
            loader = ModernDataLoader(self.config)
            
            # Load raw data
            self.logger.info("Loading raw features data...")
            raw_signals = []
            
            features_dir = Path(self.config.system.data_dir) / "features_1s"
            features_files = sorted(features_dir.glob("features_1s_*.parquet"))
            
            for file in features_files:
                df = pd.read_parquet(file)
                if not df.empty:
                    raw_signals.append({
                        'file': file.name,
                        'shape': df.shape,
                        'columns': list(df.columns),
                        'sample': df.iloc[0].to_dict() if len(df) > 0 else {}
                    })
            
            self.results['data_loading']['raw_files'] = len(raw_signals)
            self.results['data_loading']['raw_signals'] = raw_signals[:3]  # Sample
            
            # Test data adapter
            adapter_config = AdapterConfig(
                handle_missing_with_defaults=True,
                log_transformations=True,
                compute_derived_fields=True
            )
            adapter = ModernDataAdapter(adapter_config)
            
            # Test adaptation on sample data
            if raw_signals:
                sample_df = pd.read_parquet(features_dir / raw_signals[0]['file'])
                if len(sample_df) > 0:
                    sample_row = sample_df.iloc[0].to_dict()
                    
                    # Adapt signals
                    adapted = adapter.adapt_signals_dict(sample_row, datetime.now())
                    
                    # Log transformations
                    self.logger.info("\nField Transformations:")
                    for original, transformed in [
                        ('obi_smoothed', 'volume_imbalance'),
                        ('ma_slope_ema_30_sec', 'ma_slope_ema_30s'),
                        ('atr_percent_14_sec', 'atr_percent_sec')
                    ]:
                        orig_val = sample_row.get(original, 'MISSING')
                        trans_val = adapted.get(transformed, 'MISSING')
                        self.logger.info(f"  {original}: {orig_val} -> {transformed}: {trans_val}")
                    
                    self.results['signal_adaptation']['transformations'] = {
                        'original': sample_row,
                        'adapted': adapted
                    }
            
            # Load signals through loader
            self.logger.info("\nLoading signals through ModernDataLoader...")
            signals = loader.load_signals('ETH', pd.Timestamp(start_date), pd.Timestamp(end_date))
            
            if signals is not None and not signals.empty:
                self.results['data_loading']['loaded_signals'] = {
                    'shape': signals.shape,
                    'columns': list(signals.columns),
                    'date_range': (str(signals.index.min()), str(signals.index.max())),
                    'null_counts': signals.isnull().sum().to_dict()
                }
                
                # Check critical fields
                critical_fields = ['volume_imbalance', 'atr_percent_sec', 'ma_slope_ema_30s']
                for field in critical_fields:
                    if field in signals.columns:
                        non_null = signals[field].notna().sum()
                        pct = (non_null / len(signals)) * 100
                        self.logger.info(f"  {field}: {non_null}/{len(signals)} ({pct:.1f}% non-null)")
            else:
                self.logger.error("No signals loaded!")
                self.results['data_loading']['error'] = 'No signals loaded'
                
        except Exception as e:
            self.logger.error(f"Data loading test failed: {e}", exc_info=True)
            self.results['data_loading']['error'] = str(e)
            
    def test_hypothesis_2_detector_states(self, start_date: str, end_date: str):
        """Test if detector is transitioning between states correctly."""
        self.logger.info("\n" + "="*60)
        self.logger.info("HYPOTHESIS 2: Detector State Logic Issue")
        self.logger.info("="*60)
        
        try:
            # Initialize components
            loader = ModernDataLoader(self.config)
            detector = ModernContinuousDetectorV2(self.config, mode='backtest')
            
            # Load signals
            signals = loader.load_signals('ETH', pd.Timestamp(start_date), pd.Timestamp(end_date))
            
            if signals is None or signals.empty:
                self.logger.error("No signals to test detector!")
                return
                
            # Track state transitions
            state_history = []
            state_counts = {}
            
            # Process signals
            self.logger.info("Processing signals through detector...")
            for i, (timestamp, row) in enumerate(signals.iterrows()):
                if i % 1000 == 0:
                    self.logger.debug(f"Processing signal {i}/{len(signals)}")
                
                # Update detector
                update_result = detector.update(row.to_dict(), timestamp)
                
                if update_result:
                    state = update_result['state']
                    confidence = update_result['confidence']
                    signal_quality = update_result.get('signal_quality', 0)
                    
                    state_history.append({
                        'timestamp': timestamp,
                        'state': state,
                        'confidence': confidence,
                        'signal_quality': signal_quality
                    })
                    
                    # Count states
                    state_counts[state] = state_counts.get(state, 0) + 1
                    
                    # Log state changes
                    if len(state_history) > 1 and state != state_history[-2]['state']:
                        self.logger.info(
                            f"State change at {timestamp}: "
                            f"{state_history[-2]['state']} -> {state} "
                            f"(confidence: {confidence:.2f}, quality: {signal_quality:.2f})"
                        )
            
            # Analyze results
            self.logger.info(f"\nProcessed {len(state_history)} detector updates")
            self.logger.info("State distribution:")
            for state, count in sorted(state_counts.items(), key=lambda x: x[1], reverse=True):
                pct = (count / len(state_history)) * 100
                self.logger.info(f"  {state}: {count} ({pct:.1f}%)")
            
            # Check for trading states
            trading_states = [
                GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND,
                GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_WEAK_BEAR_TREND
            ]
            trading_count = sum(state_counts.get(s, 0) for s in trading_states)
            trading_pct = (trading_count / len(state_history)) * 100 if state_history else 0
            
            self.logger.info(f"\nTrading states: {trading_count}/{len(state_history)} ({trading_pct:.1f}%)")
            
            self.results['detector_states'] = {
                'total_updates': len(state_history),
                'state_counts': state_counts,
                'trading_states_pct': trading_pct,
                'state_changes': len([1 for i in range(1, len(state_history)) 
                                     if state_history[i]['state'] != state_history[i-1]['state']]),
                'sample_history': state_history[:10]  # First 10 entries
            }
            
        except Exception as e:
            self.logger.error(f"Detector state test failed: {e}", exc_info=True)
            self.results['detector_states']['error'] = str(e)
            
    def test_hypothesis_3_signal_quality(self, start_date: str, end_date: str):
        """Test if signal quality gating is too restrictive."""
        self.logger.info("\n" + "="*60)
        self.logger.info("HYPOTHESIS 3: Signal Quality Gating Issue")
        self.logger.info("="*60)
        
        try:
            loader = ModernDataLoader(self.config)
            detector = ModernContinuousDetectorV2(self.config, mode='backtest')
            
            signals = loader.load_signals('ETH', pd.Timestamp(start_date), pd.Timestamp(end_date))
            
            if signals is None or signals.empty:
                self.logger.error("No signals to test!")
                return
                
            # Track signal quality
            quality_scores = []
            quality_distribution = {'high': 0, 'medium': 0, 'low': 0, 'very_low': 0}
            
            # Sample signals for quality assessment
            sample_size = min(1000, len(signals))
            sample_indices = np.random.choice(len(signals), sample_size, replace=False)
            
            for idx in sample_indices:
                row = signals.iloc[idx]
                
                # Assess quality
                quality = detector._assess_signal_quality(row.to_dict())
                quality_scores.append(quality)
                
                # Categorize
                if quality >= 0.8:
                    quality_distribution['high'] += 1
                elif quality >= 0.6:
                    quality_distribution['medium'] += 1
                elif quality >= 0.3:
                    quality_distribution['low'] += 1
                else:
                    quality_distribution['very_low'] += 1
            
            # Analyze
            avg_quality = np.mean(quality_scores)
            self.logger.info(f"\nSignal Quality Analysis (n={sample_size}):")
            self.logger.info(f"  Average quality: {avg_quality:.3f}")
            self.logger.info(f"  Min quality: {min(quality_scores):.3f}")
            self.logger.info(f"  Max quality: {max(quality_scores):.3f}")
            
            self.logger.info("\nQuality distribution:")
            for category, count in quality_distribution.items():
                pct = (count / sample_size) * 100
                self.logger.info(f"  {category}: {count} ({pct:.1f}%)")
            
            # Check field availability
            self.logger.info("\nField availability analysis:")
            required_fields = ['volume_imbalance', 'atr_percent_sec', 'ma_slope_ema_30s',
                             'spread_mean', 'spread_std', 'close', 'volume']
            
            for field in required_fields:
                if field in signals.columns:
                    non_null = signals[field].notna().sum()
                    pct = (non_null / len(signals)) * 100
                    self.logger.info(f"  {field}: {pct:.1f}% available")
                else:
                    self.logger.info(f"  {field}: MISSING FROM DATAFRAME")
            
            self.results['signal_quality'] = {
                'average_quality': avg_quality,
                'quality_distribution': quality_distribution,
                'quality_stats': {
                    'min': min(quality_scores),
                    'max': max(quality_scores),
                    'std': np.std(quality_scores)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Signal quality test failed: {e}", exc_info=True)
            self.results['signal_quality']['error'] = str(e)
            
    def test_hypothesis_4_threshold_calibration(self, start_date: str, end_date: str):
        """Test if thresholds are properly calibrated for data ranges."""
        self.logger.info("\n" + "="*60)
        self.logger.info("HYPOTHESIS 4: Threshold Calibration Issue")
        self.logger.info("="*60)
        
        try:
            loader = ModernDataLoader(self.config)
            signals = loader.load_signals('ETH', pd.Timestamp(start_date), pd.Timestamp(end_date))
            
            if signals is None or signals.empty:
                self.logger.error("No signals to analyze!")
                return
            
            # Get detector thresholds
            detector = ModernContinuousDetectorV2(self.config, mode='backtest')
            
            thresholds = {
                'volatility': {
                    'low': detector.vol_low_thresh,
                    'high': detector.vol_high_thresh
                },
                'momentum': {
                    'weak': detector.mom_weak_thresh,
                    'strong': detector.mom_strong_thresh
                },
                'obi': {
                    'weak': detector.obi_weak_confirm_thresh,
                    'strong': detector.obi_strong_confirm_thresh
                },
                'spread': {
                    'std_high': detector.spread_std_high_thresh,
                    'mean_low': detector.spread_mean_low_thresh
                }
            }
            
            # Analyze data distributions vs thresholds
            self.logger.info("\nData Distribution vs Thresholds:")
            
            # Volatility (ATR%)
            if 'atr_percent_sec' in signals.columns:
                atr_data = signals['atr_percent_sec'].dropna()
                if len(atr_data) > 0:
                    self.logger.info(f"\nATR Percent (volatility):")
                    self.logger.info(f"  Data range: [{atr_data.min():.6f}, {atr_data.max():.6f}]")
                    self.logger.info(f"  Mean: {atr_data.mean():.6f}, Std: {atr_data.std():.6f}")
                    self.logger.info(f"  Thresholds: low={thresholds['volatility']['low']:.6f}, "
                                   f"high={thresholds['volatility']['high']:.6f}")
                    
                    # Check how often we exceed thresholds
                    low_vol_pct = (atr_data <= thresholds['volatility']['low']).sum() / len(atr_data) * 100
                    high_vol_pct = (atr_data >= thresholds['volatility']['high']).sum() / len(atr_data) * 100
                    normal_vol_pct = 100 - low_vol_pct - high_vol_pct
                    
                    self.logger.info(f"  Distribution: low={low_vol_pct:.1f}%, "
                                   f"normal={normal_vol_pct:.1f}%, high={high_vol_pct:.1f}%")
            
            # Momentum
            if 'ma_slope_ema_30s' in signals.columns:
                mom_data = signals['ma_slope_ema_30s'].dropna()
                if len(mom_data) > 0:
                    self.logger.info(f"\nMomentum (MA slope):")
                    self.logger.info(f"  Data range: [{mom_data.min():.6f}, {mom_data.max():.6f}]")
                    self.logger.info(f"  Mean: {mom_data.mean():.6f}, Std: {mom_data.std():.6f}")
                    self.logger.info(f"  Thresholds: weak=±{thresholds['momentum']['weak']:.6f}, "
                                   f"strong=±{thresholds['momentum']['strong']:.6f}")
                    
                    # Check distribution
                    abs_mom = mom_data.abs()
                    weak_mom_pct = ((abs_mom >= thresholds['momentum']['weak']) & 
                                   (abs_mom < thresholds['momentum']['strong'])).sum() / len(mom_data) * 100
                    strong_mom_pct = (abs_mom >= thresholds['momentum']['strong']).sum() / len(mom_data) * 100
                    neutral_pct = 100 - weak_mom_pct - strong_mom_pct
                    
                    self.logger.info(f"  Distribution: neutral={neutral_pct:.1f}%, "
                                   f"weak={weak_mom_pct:.1f}%, strong={strong_mom_pct:.1f}%")
            
            # OBI/Volume Imbalance
            if 'volume_imbalance' in signals.columns:
                obi_data = signals['volume_imbalance'].dropna()
                if len(obi_data) > 0:
                    self.logger.info(f"\nVolume Imbalance (OBI):")
                    self.logger.info(f"  Data range: [{obi_data.min():.4f}, {obi_data.max():.4f}]")
                    self.logger.info(f"  Mean: {obi_data.mean():.4f}, Std: {obi_data.std():.4f}")
                    self.logger.info(f"  Thresholds: weak=±{thresholds['obi']['weak']:.4f}, "
                                   f"strong=±{thresholds['obi']['strong']:.4f}")
                    
                    # Check distribution
                    abs_obi = obi_data.abs()
                    weak_obi_pct = ((abs_obi >= thresholds['obi']['weak']) & 
                                   (abs_obi < thresholds['obi']['strong'])).sum() / len(obi_data) * 100
                    strong_obi_pct = (abs_obi >= thresholds['obi']['strong']).sum() / len(obi_data) * 100
                    
                    self.logger.info(f"  Distribution: weak={weak_obi_pct:.1f}%, "
                                   f"strong={strong_obi_pct:.1f}%")
            
            self.results['threshold_analysis'] = {
                'thresholds': thresholds,
                'data_analysis': {
                    'atr_percent': {
                        'range': [float(atr_data.min()), float(atr_data.max())] if 'atr_data' in locals() else None,
                        'mean': float(atr_data.mean()) if 'atr_data' in locals() else None,
                        'distribution': {
                            'low_vol_pct': low_vol_pct if 'low_vol_pct' in locals() else None,
                            'high_vol_pct': high_vol_pct if 'high_vol_pct' in locals() else None
                        }
                    }
                }
            }
            
        except Exception as e:
            self.logger.error(f"Threshold calibration test failed: {e}", exc_info=True)
            self.results['threshold_analysis']['error'] = str(e)
            
    def test_hypothesis_5_strategy_evaluation(self, start_date: str, end_date: str):
        """Test if strategy evaluator is receiving proper signals."""
        self.logger.info("\n" + "="*60)
        self.logger.info("HYPOTHESIS 5: Strategy Evaluation Issue")
        self.logger.info("="*60)
        
        try:
            # This would require running a mini backtest with enhanced logging
            # For now, we'll check the configuration and allowed states
            
            detector = ModernContinuousDetectorV2(self.config, mode='backtest')
            
            # Check allowed states for different strategies
            self.logger.info("\nAllowed states by strategy type:")
            
            for strategy_type in ['trend_following', 'mean_reversion', 'other']:
                allowed_states = detector.get_allowed_states(strategy_type)
                self.logger.info(f"\n{strategy_type}:")
                for state in allowed_states:
                    self.logger.info(f"  - {state}")
            
            # Check hourly evaluator configuration
            hourly_config = self.config.strategies.hourly_trend_follow_v3
            self.logger.info(f"\nHourly evaluator configuration:")
            self.logger.info(f"  Strategy type: {hourly_config.strategy_type}")
            self.logger.info(f"  Min confidence: {hourly_config.min_confidence}")
            self.logger.info(f"  Base notional: {hourly_config.base_notional}")
            
            self.results['strategy_evaluation'] = {
                'allowed_states': {
                    'trend_following': detector.get_allowed_states('trend_following'),
                    'mean_reversion': detector.get_allowed_states('mean_reversion')
                },
                'hourly_config': {
                    'strategy_type': hourly_config.strategy_type,
                    'min_confidence': hourly_config.min_confidence
                }
            }
            
        except Exception as e:
            self.logger.error(f"Strategy evaluation test failed: {e}", exc_info=True)
            self.results['strategy_evaluation']['error'] = str(e)
            
    def generate_report(self):
        """Generate comprehensive debug report."""
        report_file = f"debug_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        self.logger.info("\n" + "="*80)
        self.logger.info("DIAGNOSIS COMPLETE")
        self.logger.info("="*80)
        
        # Summarize findings
        self.logger.info("\nKEY FINDINGS:")
        
        # Check each hypothesis
        issues_found = []
        
        # H1: Data loading
        if 'error' in self.results['data_loading']:
            issues_found.append("Data loading failed")
        elif self.results['data_loading'].get('loaded_signals', {}).get('shape', [0])[0] == 0:
            issues_found.append("No signals loaded")
            
        # H2: Detector states
        if self.results['detector_states'].get('trading_states_pct', 0) < 10:
            issues_found.append(f"Low trading state percentage: {self.results['detector_states'].get('trading_states_pct', 0):.1f}%")
            
        # H3: Signal quality
        if self.results['signal_quality'].get('average_quality', 0) < 0.5:
            issues_found.append(f"Low average signal quality: {self.results['signal_quality'].get('average_quality', 0):.3f}")
            
        # H4: Threshold calibration
        # Would need more analysis here
        
        # Print findings
        if issues_found:
            self.logger.info("\nISSUES IDENTIFIED:")
            for i, issue in enumerate(issues_found, 1):
                self.logger.info(f"  {i}. {issue}")
        else:
            self.logger.info("\nNo obvious issues found - need deeper analysis")
            
        # Save full report
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
            
        self.logger.info(f"\nFull report saved to: {report_file}")
        self.logger.info(f"Debug log saved to: {self.log_file}")


if __name__ == "__main__":
    # Run systematic debug
    config_path = "configs/overrides/modern_system_v2_complete.yaml"
    start_date = "2024-01-01"
    end_date = "2024-01-07"
    
    debugger = SystematicDebugger(config_path)
    debugger.run_full_diagnosis(start_date, end_date)