#!/usr/bin/env python3
"""
Check EMA calculation and compare with actual price movement.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

import pandas as pd
import numpy as np
from datetime import datetime

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_data_loader import RobustDataLoader
from hyperliquid_bot.features.indicators import calculate_ema

def main():
    """Check EMA calculations."""
    # Load config
    config_path = Path(__file__).parent.parent / "configs/overrides/modern_system_v2_complete.yaml"
    config = load_config(str(config_path))
    
    # Load data
    data_loader = RobustDataLoader(config)
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 31)
    
    print(f"Loading January 2024 data...")
    data = data_loader.load_data(start_date, end_date)
    
    if data.empty:
        print("ERROR: No data loaded")
        return
    
    print(f"Loaded {len(data)} hours of data")
    print(f"Price range: ${data['close'].min():.2f} - ${data['close'].max():.2f}")
    
    # Calculate EMAs
    ema_12 = calculate_ema(data, 12, price_col='close', shift=0)
    ema_26 = calculate_ema(data, 26, price_col='close', shift=0)
    
    # Also calculate with shift=1 (as used in strategy)
    ema_12_shifted = calculate_ema(data, 12, price_col='close', shift=1)
    ema_26_shifted = calculate_ema(data, 26, price_col='close', shift=1)
    
    # Add to dataframe
    data['ema_12'] = ema_12
    data['ema_26'] = ema_26
    data['ema_12_shifted'] = ema_12_shifted
    data['ema_26_shifted'] = ema_26_shifted
    data['forecast'] = ema_12 - ema_26
    data['forecast_shifted'] = ema_12_shifted - ema_26_shifted
    
    # Analyze periods
    print("\n=== PRICE MOVEMENT ANALYSIS ===")
    
    # Check overall trend
    start_price = data.iloc[0]['close']
    end_price = data.iloc[-1]['close']
    price_change = (end_price - start_price) / start_price * 100
    
    print(f"Start price: ${start_price:.2f}")
    print(f"End price: ${end_price:.2f}")
    print(f"Total change: {price_change:+.1f}%")
    
    # Check EMA crossovers
    crossovers = []
    for i in range(1, len(data)):
        prev_forecast = data.iloc[i-1]['forecast']
        curr_forecast = data.iloc[i]['forecast']
        
        if prev_forecast < 0 and curr_forecast > 0:
            crossovers.append(('bullish', data.index[i], data.iloc[i]['close']))
        elif prev_forecast > 0 and curr_forecast < 0:
            crossovers.append(('bearish', data.index[i], data.iloc[i]['close']))
    
    print(f"\n=== EMA CROSSOVERS ===")
    print(f"Total crossovers: {len(crossovers)}")
    for direction, timestamp, price in crossovers[:5]:  # Show first 5
        print(f"  {direction.upper()} crossover at {timestamp}: ${price:.2f}")
    
    # Check periods where EMA fast > slow
    bullish_hours = (data['ema_12'] > data['ema_26']).sum()
    bearish_hours = (data['ema_12'] < data['ema_26']).sum()
    
    print(f"\n=== EMA ALIGNMENT ===")
    print(f"Hours with EMA 12 > EMA 26: {bullish_hours} ({bullish_hours/len(data)*100:.1f}%)")
    print(f"Hours with EMA 12 < EMA 26: {bearish_hours} ({bearish_hours/len(data)*100:.1f}%)")
    
    # Check specific dates
    print(f"\n=== SAMPLE DATES ===")
    sample_dates = [
        datetime(2024, 1, 10, 12),
        datetime(2024, 1, 15, 12),
        datetime(2024, 1, 20, 12),
        datetime(2024, 1, 25, 12),
    ]
    
    for date in sample_dates:
        if date in data.index:
            row = data.loc[date]
            print(f"\n{date}:")
            print(f"  Close: ${row['close']:.2f}")
            print(f"  EMA 12: ${row['ema_12']:.2f}")
            print(f"  EMA 26: ${row['ema_26']:.2f}")
            print(f"  Forecast: {row['forecast']:.2f}")
            print(f"  Alignment: {'BULLISH' if row['ema_12'] > row['ema_26'] else 'BEARISH'}")
    
    # Check if shift=1 is causing the issue
    print(f"\n=== SHIFT IMPACT ===")
    print(f"Regular forecast > 0: {(data['forecast'] > 0).sum()} hours")
    print(f"Shifted forecast > 0: {(data['forecast_shifted'] > 0).sum()} hours")
    
    # Save a sample for inspection
    sample = data[['close', 'ema_12', 'ema_26', 'forecast', 'ema_12_shifted', 'ema_26_shifted', 'forecast_shifted']].iloc[100:110]
    print(f"\n=== SAMPLE DATA (rows 100-110) ===")
    print(sample.to_string())

if __name__ == "__main__":
    main()