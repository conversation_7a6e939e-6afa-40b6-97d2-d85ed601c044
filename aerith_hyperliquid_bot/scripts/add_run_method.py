#!/usr/bin/env python3
# Script to add the missing run method to the Backtester class

import sys
from pathlib import Path

# Path to the backtester.py file
backtester_path = Path("/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py")

# Read the current file content
with open(backtester_path, 'r') as f:
    content = f.read()

# Define the run method to add
run_method = '''
    def run(self, start_date: datetime, end_date: datetime):
        """
        Runs a backtest from start_date to end_date.
        
        Args:
            start_date: Start date for the backtest
            end_date: End date for the backtest
        """
        self.logger.info(f"--- Starting Backtest Run ---")
        self.start_time = time.time()
        
        try:
            self._load_and_prepare_data(start_date, end_date)
            self._run_simulation_loop()
            
            # Generate reports
            steps_processed = len(self.all_signals_df) - self.start_index if not self.all_signals_df.empty else 0
            self._compute_and_log_metrics(steps_processed)
            
        except Exception as e:
            self.logger.critical(f"Backtest run failed with unexpected error: {e}", exc_info=True)
            raise
            
        run_time = time.time() - self.start_time
        self.logger.info(f"--- Backtest Run Finished ({run_time:.2f} seconds) ---")
'''

# Find the right position to insert the run method
# We'll add it after the _run_simulation_loop method
target_line = "        self.logger.info(\"Simulation loop finished.\") # This should now be reached correctly"
if target_line in content:
    # Insert the run method after the target line
    new_content = content.replace(target_line, target_line + "\n" + run_method)
    
    # Write the updated content back to the file
    with open(backtester_path, 'w') as f:
        f.write(new_content)
    
    print(f"Successfully added run method to {backtester_path}")
else:
    print(f"Could not find the target line in {backtester_path}")
    sys.exit(1)
