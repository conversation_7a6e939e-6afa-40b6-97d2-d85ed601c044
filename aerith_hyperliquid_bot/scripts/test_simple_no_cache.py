#!/usr/bin/env python3
"""
Simple test to verify system works without cache
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_backtest_engine import RobustBacktestEngine

# Load config
config = load_config("configs/overrides/modern_system_v2_complete.yaml")

# Force enhanced detector
config.regime.detector_type = "enhanced"

print("Testing system without cache...")
print(f"Fallback confidence: {getattr(config.regime, 'fallback_confidence', 0.7)}")

# Run quick test
engine = RobustBacktestEngine(
    config=config,
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 3),  # Just 3 days
    use_regime_cache=False
)

results = engine.run_backtest()
print(f"\nCompleted! Trades: {len(results.get('trades', []))}")
print(f"Regime sources: {results.get('regime_sources', {})}")