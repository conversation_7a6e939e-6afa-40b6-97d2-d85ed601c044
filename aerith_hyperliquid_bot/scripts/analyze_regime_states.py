#!/usr/bin/env python3
"""
Analyze regime states from backtest to understand confidence patterns.
"""

import json
import pandas as pd
from collections import Counter
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def analyze_recent_backtest():
    """Analyze the most recent backtest results."""
    log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    
    # Find most recent backtest trades file
    trade_files = list(log_dir.glob("backtest_trades_*.json"))
    if not trade_files:
        logger.error("No backtest trade files found")
        return
    
    latest_file = max(trade_files, key=lambda p: p.stat().st_mtime)
    logger.info(f"Loading trades from: {latest_file}")
    
    # Load trades
    with open(latest_file, 'r') as f:
        trades = json.load(f)
    
    logger.info(f"\nTotal trades: {len(trades)}")
    
    # Count regime entries
    regime_counts = Counter(trade['entry_regime'] for trade in trades)
    
    logger.info("\n=== Regime Distribution ===")
    for regime, count in regime_counts.most_common():
        pct = (count / len(trades)) * 100
        logger.info(f"{regime}: {count} trades ({pct:.1f}%)")
    
    # Analyze trade patterns
    logger.info("\n=== Trade Analysis ===")
    
    # Convert to DataFrame for easier analysis
    df = pd.DataFrame(trades)
    
    # Trade types
    type_counts = df['type'].value_counts()
    logger.info(f"\nTrade types:")
    for trade_type, count in type_counts.items():
        pct = (count / len(df)) * 100
        logger.info(f"  {trade_type}: {count} ({pct:.1f}%)")
    
    # Win rate by regime
    logger.info("\n=== Win Rate by Regime ===")
    for regime in regime_counts.keys():
        regime_trades = df[df['entry_regime'] == regime]
        if len(regime_trades) > 0:
            wins = len(regime_trades[regime_trades['profit'] > 0])
            win_rate = (wins / len(regime_trades)) * 100
            avg_profit = regime_trades['profit'].mean()
            logger.info(f"{regime}: {win_rate:.1f}% win rate, avg profit: ${avg_profit:.2f}")
    
    # Based on the confidence logic in gms_detector.py:
    # - BULL regime trades would get 0.9 confidence if transitioning from WEAK_BULL
    # - All BULL trades (100% of trades) suggest stable bull market conditions
    logger.info("\n=== Confidence Analysis ===")
    logger.info("Based on the code analysis:")
    logger.info("- Natural progressions (WEAK_BULL->BULL, WEAK_BEAR->BEAR): 0.9 confidence")
    logger.info("- Trend reversals (BULL->BEAR, BEAR->BULL): 0.5 confidence")
    logger.info("- Other transitions: 0.6-0.8 confidence")
    logger.info("\nWith 100% BULL trades, the system likely saw:")
    logger.info("1. Persistent bullish conditions throughout 2024")
    logger.info("2. Many WEAK_BULL->BULL transitions (0.9 confidence)")
    logger.info("3. Few or no trend reversals")
    
    # Time analysis
    if 'entry_time' in df.columns:
        df['entry_time'] = pd.to_datetime(df['entry_time'])
        df['exit_time'] = pd.to_datetime(df['exit_time'])
        df['duration_hours'] = (df['exit_time'] - df['entry_time']).dt.total_seconds() / 3600
        
        logger.info(f"\n=== Trade Duration ===")
        logger.info(f"Mean duration: {df['duration_hours'].mean():.2f} hours")
        logger.info(f"Median duration: {df['duration_hours'].median():.2f} hours")
        logger.info(f"Max duration: {df['duration_hours'].max():.2f} hours")


if __name__ == "__main__":
    analyze_recent_backtest()