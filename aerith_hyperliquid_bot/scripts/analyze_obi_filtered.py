#!/usr/bin/env python3
"""
Script to analyze the relationship between raw_obi_zscore and obi_zscore_filtered.
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path

def analyze_obi_filtered(file_path):
    """Analyze the relationship between raw_obi_zscore and obi_zscore_filtered."""
    try:
        print(f"\n{'='*60}")
        print(f"ANALYZING OBI ZSCORE FILTERING in {file_path}")
        print(f"{'='*60}")
        
        # Read the Parquet file
        df = pd.read_parquet(file_path)
        
        # Check if required columns exist
        required_cols = ['raw_obi_zscore', 'obi_zscore_filtered']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"Error: Missing columns: {missing_cols}")
            print(f"Available columns: {df.columns.tolist()}")
            return
        
        # Basic stats for raw_obi_zscore
        print("\nRAW OBI ZSCORE STATISTICS:")
        print(f"Min: {df['raw_obi_zscore'].min()}")
        print(f"Max: {df['raw_obi_zscore'].max()}")
        print(f"Mean: {df['raw_obi_zscore'].mean()}")
        print(f"Std Dev: {df['raw_obi_zscore'].std()}")
        
        # Check obi_zscore_filtered data type
        print(f"\nOBI ZSCORE FILTERED TYPE: {df['obi_zscore_filtered'].dtype}")
        
        # Count True/False values
        true_count = df['obi_zscore_filtered'].sum()
        false_count = len(df) - true_count
        print(f"True values: {true_count} ({true_count/len(df)*100:.2f}%)")
        print(f"False values: {false_count} ({false_count/len(df)*100:.2f}%)")
        
        # Analyze when filtered is True
        if true_count > 0:
            filtered_true = df[df['obi_zscore_filtered']]
            print("\nWHEN OBI_ZSCORE_FILTERED IS TRUE:")
            print(f"Count: {len(filtered_true)}")
            print(f"raw_obi_zscore min: {filtered_true['raw_obi_zscore'].min()}")
            print(f"raw_obi_zscore max: {filtered_true['raw_obi_zscore'].max()}")
            print(f"raw_obi_zscore mean: {filtered_true['raw_obi_zscore'].mean()}")
            
            # Try to determine the threshold
            abs_min = filtered_true['raw_obi_zscore'].abs().min()
            print(f"Minimum absolute value when filtered=True: {abs_min}")
            
            # Check negative values
            neg_count = (filtered_true['raw_obi_zscore'] < 0).sum()
            pos_count = (filtered_true['raw_obi_zscore'] > 0).sum()
            print(f"Negative values when filtered=True: {neg_count} ({neg_count/len(filtered_true)*100:.2f}%)")
            print(f"Positive values when filtered=True: {pos_count} ({pos_count/len(filtered_true)*100:.2f}%)")
            
            # Distribution of values when filtered=True
            print("\nDISTRIBUTION WHEN FILTERED=TRUE:")
            for threshold in [-2.5, -2.0, -1.5, -1.0, 1.0, 1.5, 2.0, 2.5]:
                if threshold < 0:
                    count = (filtered_true['raw_obi_zscore'] <= threshold).sum()
                    print(f"Values <= {threshold}: {count} ({count/len(filtered_true)*100:.2f}%)")
                else:
                    count = (filtered_true['raw_obi_zscore'] >= threshold).sum()
                    print(f"Values >= {threshold}: {count} ({count/len(filtered_true)*100:.2f}%)")
        
        # Analyze when filtered is False
        if false_count > 0:
            filtered_false = df[~df['obi_zscore_filtered']]
            print("\nWHEN OBI_ZSCORE_FILTERED IS FALSE:")
            print(f"Count: {len(filtered_false)}")
            print(f"raw_obi_zscore min: {filtered_false['raw_obi_zscore'].min()}")
            print(f"raw_obi_zscore max: {filtered_false['raw_obi_zscore'].max()}")
            print(f"raw_obi_zscore mean: {filtered_false['raw_obi_zscore'].mean()}")
            
            # Distribution of values when filtered=False
            print("\nDISTRIBUTION WHEN FILTERED=FALSE:")
            for threshold in [-2.5, -2.0, -1.5, -1.0, 1.0, 1.5, 2.0, 2.5]:
                if threshold < 0:
                    count = (filtered_false['raw_obi_zscore'] <= threshold).sum()
                    print(f"Values <= {threshold}: {count} ({count/len(filtered_false)*100:.2f}%)")
                else:
                    count = (filtered_false['raw_obi_zscore'] >= threshold).sum()
                    print(f"Values >= {threshold}: {count} ({count/len(filtered_false)*100:.2f}%)")
        
        # Determine potential threshold
        print("\nTHRESHOLD ANALYSIS:")
        for threshold in np.arange(1.0, 2.6, 0.1):
            # Check positive threshold
            pos_above = (df['raw_obi_zscore'] >= threshold)
            pos_filtered = (df['obi_zscore_filtered'] & (df['raw_obi_zscore'] > 0))
            pos_match_pct = (pos_above == pos_filtered).mean() * 100
            
            # Check negative threshold
            neg_below = (df['raw_obi_zscore'] <= -threshold)
            neg_filtered = (df['obi_zscore_filtered'] & (df['raw_obi_zscore'] < 0))
            neg_match_pct = (neg_below == neg_filtered).mean() * 100
            
            # Combined match percentage
            abs_above = (df['raw_obi_zscore'].abs() >= threshold)
            abs_filtered = df['obi_zscore_filtered']
            abs_match_pct = (abs_above == abs_filtered).mean() * 100
            
            print(f"Threshold ±{threshold:.1f}: Match {abs_match_pct:.2f}% (Pos: {pos_match_pct:.2f}%, Neg: {neg_match_pct:.2f}%)")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python analyze_obi_filtered.py <parquet_file_path>")
        sys.exit(1)
    
    file_path = Path(sys.argv[1])
    if not file_path.exists():
        print(f"Error: File not found: {file_path}")
        sys.exit(1)
    
    analyze_obi_filtered(file_path)
