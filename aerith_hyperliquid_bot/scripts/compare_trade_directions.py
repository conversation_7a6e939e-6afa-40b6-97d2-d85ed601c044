#!/usr/bin/env python3
"""
Simple comparison of trade directions between legacy and modern systems
"""

import sys
import os
import subprocess
import json
import re
from datetime import datetime
from collections import defaultdict

# Add project root to path  
sys.path.append('/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot')

def run_backtest_and_extract_trades(system_mode, config_override=None):
    """Run backtest and extract trade information from logs"""
    
    print(f"\n🚀 Running {system_mode.upper()} system backtest...")
    
    # Prepare command
    cmd = [
        'python3', '-m', 'hyperliquid_bot.backtester.run_backtest',
        '--system', system_mode
    ]
    
    if config_override and system_mode == 'modern':
        cmd.extend(['--override', config_override])
    
    # Run with output capture
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=300,  # 5 minute timeout
            cwd='/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot'
        )
        
        output = result.stdout + result.stderr
        
        if result.returncode != 0:
            print(f"❌ Backtest failed with return code {result.returncode}")
            print("Error output:")
            print(result.stderr[:1000])  # First 1000 chars
            return None
            
    except subprocess.TimeoutExpired:
        print(f"❌ Backtest timed out after 5 minutes")
        return None
    except Exception as e:
        print(f"❌ Error running backtest: {e}")
        return None
    
    # Extract information from output
    trades_info = extract_trades_from_output(output)
    regime_info = extract_regime_info_from_output(output) 
    performance_info = extract_performance_from_output(output)
    
    return {
        'system_mode': system_mode,
        'trades': trades_info,
        'regimes': regime_info,
        'performance': performance_info,
        'raw_output': output[-2000:]  # Last 2000 chars for debugging
    }

def extract_trades_from_output(output):
    """Extract trade signals from backtest output"""
    
    trades = []
    regime_counts = defaultdict(int)
    
    # Pattern for trade signals: SIGNAL: Strat='...', Dir='...', Regime='...' at ...
    signal_pattern = r"SIGNAL: Strat='([^']+)', Dir='([^']+)', Regime='([^']+)' at ([^\s]+)"
    
    for match in re.finditer(signal_pattern, output):
        strategy = match.group(1)
        direction = match.group(2)
        regime = match.group(3)
        timestamp = match.group(4)
        
        trades.append({
            'strategy': strategy,
            'direction': direction,
            'regime': regime,
            'timestamp': timestamp
        })
        
        regime_counts[regime] += 1
    
    # Count by direction
    long_trades = len([t for t in trades if t['direction'] == 'long'])
    short_trades = len([t for t in trades if t['direction'] == 'short'])
    total_trades = len(trades)
    
    return {
        'total_trades': total_trades,
        'long_trades': long_trades,
        'short_trades': short_trades,
        'long_pct': (long_trades / total_trades * 100) if total_trades > 0 else 0,
        'short_pct': (short_trades / total_trades * 100) if total_trades > 0 else 0,
        'trades_by_regime': dict(regime_counts),
        'all_trades': trades
    }

def extract_regime_info_from_output(output):
    """Extract regime detection information"""
    
    regime_detections = defaultdict(int)
    
    # Pattern for regime detection: Detected Regime = '...' 
    regime_pattern = r"Detected Regime = '([^']+)'"
    
    for match in re.finditer(regime_pattern, output):
        regime = match.group(1)
        regime_detections[regime] += 1
    
    total_detections = sum(regime_detections.values())
    
    # Convert to percentages
    regime_percentages = {}
    for regime, count in regime_detections.items():
        regime_percentages[regime] = (count / total_detections * 100) if total_detections > 0 else 0
    
    return {
        'total_detections': total_detections,
        'regime_counts': dict(regime_detections),
        'regime_percentages': regime_percentages
    }

def extract_performance_from_output(output):
    """Extract performance metrics from output"""
    
    performance = {}
    
    # Look for final performance metrics
    patterns = {
        'final_balance': r'Final Balance.*?(\d+\.?\d*)',
        'total_return': r'Total Return.*?([+-]?\d+\.?\d*)%',
        'total_trades': r'Total.*?(\d+).*?trades',
        'win_rate': r'Win Rate.*?(\d+\.?\d*)%',
        'sharpe': r'Sharpe.*?([+-]?\d+\.?\d*)'
    }
    
    for metric, pattern in patterns.items():
        match = re.search(pattern, output, re.IGNORECASE)
        if match:
            try:
                performance[metric] = float(match.group(1))
            except ValueError:
                performance[metric] = match.group(1)
    
    return performance

def compare_results(legacy_result, modern_result):
    """Compare results between legacy and modern systems"""
    
    print(f"\n{'='*80}")
    print(f"🔍 COMPREHENSIVE SYSTEM COMPARISON")
    print(f"{'='*80}")
    
    if not legacy_result or not modern_result:
        print("❌ Cannot compare - one or both backtests failed")
        return
    
    legacy_trades = legacy_result['trades']
    modern_trades = modern_result['trades']
    
    print(f"\n📊 TRADE DISTRIBUTION COMPARISON:")
    print(f"{'Metric':<25} {'Legacy':<15} {'Modern':<15} {'Difference'}")
    print(f"{'-'*70}")
    
    metrics = [
        ('Total Trades', 'total_trades'),
        ('Long Trades', 'long_trades'),
        ('Short Trades', 'short_trades'),
        ('Long %', 'long_pct'),
        ('Short %', 'short_pct')
    ]
    
    for metric_name, key in metrics:
        legacy_val = legacy_trades.get(key, 0)
        modern_val = modern_trades.get(key, 0)
        
        if key.endswith('_pct'):
            diff = modern_val - legacy_val
            print(f"{metric_name:<25} {legacy_val:6.1f}%        {modern_val:6.1f}%        {diff:+6.1f}%")
        else:
            diff = modern_val - legacy_val
            print(f"{metric_name:<25} {legacy_val:6d}          {modern_val:6d}          {diff:+6d}")
    
    print(f"\n📈 REGIME DETECTION COMPARISON:")
    print(f"Top regimes detected by each system:")
    
    print(f"\nLegacy system regimes:")
    legacy_regimes = legacy_result['regimes']['regime_percentages']
    for regime, pct in sorted(legacy_regimes.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"  {regime:<20}: {pct:5.1f}%")
    
    print(f"\nModern system regimes:")
    modern_regimes = modern_result['regimes']['regime_percentages']
    for regime, pct in sorted(modern_regimes.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"  {regime:<20}: {pct:5.1f}%")
    
    print(f"\n🎯 KEY FINDINGS:")
    
    # Check for long-only issue
    if modern_trades['short_trades'] == 0 and legacy_trades['short_trades'] > 0:
        print(f"  🚨 CONFIRMED: Modern system is LONG-ONLY!")
        print(f"     Legacy: {legacy_trades['short_trades']} short trades")
        print(f"     Modern: {modern_trades['short_trades']} short trades")
        
        # Check which regimes generate shorts in legacy
        legacy_short_regimes = []
        for trade in legacy_trades['all_trades']:
            if trade['direction'] == 'short':
                if trade['regime'] not in legacy_short_regimes:
                    legacy_short_regimes.append(trade['regime'])
        
        if legacy_short_regimes:
            print(f"  📍 Legacy short trades come from regimes: {legacy_short_regimes}")
            
            # Check if modern system detects these regimes
            modern_regime_list = list(modern_regimes.keys())
            missing_regimes = set(legacy_short_regimes) - set(modern_regime_list)
            
            if missing_regimes:
                print(f"  ❌ Modern system MISSING regimes: {list(missing_regimes)}")
                print(f"     → This could explain the long-only issue!")
            else:
                print(f"  ✅ Modern system detects same regimes")
                print(f"     → Issue is likely in strategy logic, not regime detection")
                
                # Check if modern system has trades from these regimes
                modern_trades_from_bear_regimes = []
                for trade in modern_trades['all_trades']:
                    if trade['regime'] in legacy_short_regimes:
                        modern_trades_from_bear_regimes.append(trade)
                
                if not modern_trades_from_bear_regimes:
                    print(f"  🔍 Modern system detects bear regimes but doesn't trade them!")
                    print(f"     → Check state mapping: regime → BULL/BEAR/CHOP conversion")
                else:
                    long_from_bear = len([t for t in modern_trades_from_bear_regimes if t['direction'] == 'long'])
                    short_from_bear = len([t for t in modern_trades_from_bear_regimes if t['direction'] == 'short'])
                    print(f"  📊 Modern trades from bear regimes: {long_from_bear}L, {short_from_bear}S")
    
    elif abs(modern_trades['short_pct'] - legacy_trades['short_pct']) > 10:
        print(f"  ⚠️  Significant difference in short trade percentage")
        diff = modern_trades['short_pct'] - legacy_trades['short_pct']
        print(f"     Modern has {diff:+.1f}% short trades vs legacy")
    else:
        print(f"  ✅ Similar trade distribution between systems")
    
    # Performance comparison
    print(f"\n💰 PERFORMANCE COMPARISON:")
    legacy_perf = legacy_result['performance']
    modern_perf = modern_result['performance']
    
    perf_metrics = ['total_return', 'win_rate', 'sharpe']
    for metric in perf_metrics:
        if metric in legacy_perf and metric in modern_perf:
            legacy_val = legacy_perf[metric]
            modern_val = modern_perf[metric]
            diff = modern_val - legacy_val
            print(f"  {metric.replace('_', ' ').title():<15}: Legacy {legacy_val:8.1f} | Modern {modern_val:8.1f} | Diff {diff:+8.1f}")

def run_full_comparison():
    """Run both systems and compare"""
    
    print("🔍 STARTING TRADE DIRECTION COMPARISON ANALYSIS")
    print("=" * 80)
    
    # Run legacy system
    legacy_result = run_backtest_and_extract_trades('legacy')
    
    if not legacy_result:
        print("❌ Legacy system failed, aborting comparison")
        return
    
    # Run modern system
    modern_result = run_backtest_and_extract_trades('modern', 'configs/overrides/execution_refinement_enabled.yaml')
    
    if not modern_result:
        print("❌ Modern system failed, aborting comparison")
        return
    
    # Compare results
    compare_results(legacy_result, modern_result)
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/trade_direction_comparison_{timestamp}.json"
    
    comparison_data = {
        'timestamp': datetime.now().isoformat(),
        'legacy_result': legacy_result,
        'modern_result': modern_result
    }
    
    with open(results_file, 'w') as f:
        json.dump(comparison_data, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: {results_file}")
    
    return comparison_data

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "legacy":
            result = run_backtest_and_extract_trades('legacy')
            if result:
                print(json.dumps(result['trades'], indent=2))
        elif sys.argv[1] == "modern":
            result = run_backtest_and_extract_trades('modern', 'configs/overrides/execution_refinement_enabled.yaml')
            if result:
                print(json.dumps(result['trades'], indent=2))
        else:
            print("Usage: python compare_trade_directions.py [legacy|modern]")
    else:
        # Default: run full comparison
        run_full_comparison()