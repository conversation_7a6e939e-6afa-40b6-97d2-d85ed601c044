#!/usr/bin/env python3
"""
Step-by-step detector debugging
Trace exactly what UnifiedGMSDetector does vs manual logic.
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

def debug_detector_step_by_step():
    """Step through detector logic with actual problematic sample."""
    # Load signals
    import glob
    files = glob.glob('/Users/<USER>/Desktop/trading_bot_/logs/backtest_signals_*.parquet')
    if not files:
        print("No signals files found")
        return
    
    latest_file = max(files)
    print(f"Loading {latest_file}")
    df = pd.read_parquet(latest_file)
    
    # Find a known problematic sample (high momentum that returns Uncertain)
    high_momentum = df[df['ma_slope'].abs() >= 15.0]
    sample_row = high_momentum.iloc[0]  # First high momentum sample
    
    print(f"Debugging sample: {sample_row['timestamp']}")
    print(f"Expected result: Strong_Bull_Trend")
    print(f"Actual result: {sample_row.get('regime', 'N/A')}")
    
    # Extract signals
    signals = sample_row.to_dict()
    
    print(f"\n=== STEP 1: RAW SIGNALS ===")
    print(f"atr_percent: {signals.get('atr_percent', 'N/A')}")
    print(f"ma_slope: {signals.get('ma_slope', 'N/A')}")
    print(f"ma_slope_ema_30s: {signals.get('ma_slope_ema_30s', 'N/A')}")
    print(f"obi_smoothed_5: {signals.get('obi_smoothed_5', 'N/A')}")
    print(f"spread_mean: {signals.get('spread_mean', 'N/A')}")
    print(f"spread_std: {signals.get('spread_std', 'N/A')}")
    
    # Simulate detector logic step by step
    print(f"\n=== STEP 2: THRESHOLDS ===")
    # These should match what we saw in logs
    vol_thresh_low = 0.005
    vol_thresh_high = 0.015
    mom_thresh_weak = 5.0
    mom_thresh_strong = 15.0
    obi_weak_confirm_thresh = 0.11
    
    print(f"vol_thresh_low: {vol_thresh_low}")
    print(f"vol_thresh_high: {vol_thresh_high}")
    print(f"mom_thresh_weak: {mom_thresh_weak}")
    print(f"mom_thresh_strong: {mom_thresh_strong}")
    print(f"obi_weak_confirm_thresh: {obi_weak_confirm_thresh}")
    
    print(f"\n=== STEP 3: SIGNAL EXTRACTION (Detector Logic) ===")
    
    # ATR_PCT_COL = 'atr_percent'
    atr_pct = signals.get('atr_percent', np.nan)
    ma_slope = signals.get('ma_slope', np.nan)
    
    # CRITICAL: Check if detector uses ma_slope_ema_30s in continuous mode
    detector_mode = 'continuous'  # This is what we're running
    if detector_mode == 'continuous' and 'ma_slope_ema_30s' in signals:
        ma_slope_orig = ma_slope
        ma_slope = signals.get('ma_slope_ema_30s', ma_slope)
        print(f"DETECTOR USES ma_slope_ema_30s: {ma_slope} (original ma_slope: {ma_slope_orig})")
    else:
        print(f"DETECTOR USES ma_slope: {ma_slope}")
    
    # depth_levels = 5 (from logs)
    depth_levels = 5
    obi_col = f'obi_smoothed_{depth_levels}'
    obi_value = signals.get(obi_col, np.nan)
    spread_mean = signals.get('spread_mean', np.nan)
    spread_std = signals.get('spread_std', np.nan)
    
    print(f"atr_pct: {atr_pct}")
    print(f"ma_slope (used by detector): {ma_slope}")
    print(f"obi_value: {obi_value}")
    print(f"spread_mean: {spread_mean}")
    print(f"spread_std: {spread_std}")
    
    print(f"\n=== STEP 4: SIGNAL VALIDATION ===")
    
    # Validate core signals (detector logic)
    missing_signals = []
    for name, val in [('atr_pct', atr_pct), ('ma_slope', ma_slope), ('obi_value', obi_value), 
                      ('spread_mean', spread_mean), ('spread_std', spread_std)]:
        if pd.isna(val):
            missing_signals.append(name)
    
    if missing_signals:
        print(f"DETECTOR VALIDATION FAILS: Missing {missing_signals}")
        print(f"DETECTOR RESULT: Unknown")
        return
    else:
        print(f"DETECTOR VALIDATION PASSES: All core signals present")
    
    print(f"\n=== STEP 5: VOLATILITY CLASSIFICATION ===")
    
    if atr_pct >= vol_thresh_high:
        vol_regime = "High"
    elif atr_pct <= vol_thresh_low:
        vol_regime = "Low"
    else:
        vol_regime = "Medium"
    
    print(f"atr_pct {atr_pct:.6f} vs thresholds (low≤{vol_thresh_low}, high≥{vol_thresh_high})")
    print(f"vol_regime: {vol_regime}")
    
    print(f"\n=== STEP 6: MOMENTUM CLASSIFICATION ===")
    
    abs_ma_slope = abs(ma_slope)
    if abs_ma_slope >= mom_thresh_strong:
        mom_regime = "Strong"
    elif abs_ma_slope <= mom_thresh_weak:
        mom_regime = "Weak"
    else:
        mom_regime = "Medium"
    
    print(f"|ma_slope| {abs_ma_slope:.3f} vs thresholds (weak≤{mom_thresh_weak}, strong≥{mom_thresh_strong})")
    print(f"mom_regime: {mom_regime}")
    
    print(f"\n=== STEP 7: DIRECTION CLASSIFICATION ===")
    
    direction = "Bull" if ma_slope > 0 else "Bear"
    print(f"ma_slope {ma_slope:.3f} -> direction: {direction}")
    
    print(f"\n=== STEP 8: OBI CONFIRMATION ===")
    
    # Detector OBI confirmation logic
    if direction == "Bull":
        obi_confirms = obi_value >= obi_weak_confirm_thresh
        print(f"Bull direction: obi_value {obi_value:.6f} >= {obi_weak_confirm_thresh} = {obi_confirms}")
    else:  # Bear
        obi_confirms = obi_value <= -obi_weak_confirm_thresh
        print(f"Bear direction: obi_value {obi_value:.6f} <= {-obi_weak_confirm_thresh} = {obi_confirms}")
    
    print(f"obi_confirms: {obi_confirms}")
    
    print(f"\n=== STEP 9: STATE COMBINATION ===")
    
    # Reproduce _combine_classifications logic
    print(f"Inputs: vol_regime={vol_regime}, mom_regime={mom_regime}, direction={direction}, obi_confirms={obi_confirms}")
    
    if vol_regime == "High":
        if mom_regime == "Strong" and obi_confirms:
            final_state = f"Strong_{direction}_Trend"
        else:
            final_state = "High_Vol_Range"
    elif vol_regime == "Low":
        if mom_regime == "Weak":
            final_state = "Low_Vol_Range"
        else:
            final_state = f"Weak_{direction}_Trend"
    else:  # Medium
        if mom_regime == "Strong" and obi_confirms:
            final_state = f"Strong_{direction}_Trend"
        elif mom_regime == "Medium":
            final_state = f"Weak_{direction}_Trend"
        else:
            final_state = "Uncertain"
    
    print(f"Expected final_state: {final_state}")
    
    print(f"\n=== STEP 10: COMPARISON ===")
    print(f"Manual logic result: {final_state}")
    print(f"Actual detector result: {sample_row.get('regime', 'N/A')}")
    
    if str(final_state) != str(sample_row.get('regime', 'N/A')):
        print(f"*** MISMATCH CONFIRMED ***")
        
        # Check for potential issues
        print(f"\n=== POTENTIAL ISSUES ===")
        
        # Issue 1: ma_slope vs ma_slope_ema_30s
        orig_ma_slope = signals.get('ma_slope', np.nan)
        ema_ma_slope = signals.get('ma_slope_ema_30s', np.nan)
        if not pd.isna(orig_ma_slope) and not pd.isna(ema_ma_slope):
            if abs(orig_ma_slope - ema_ma_slope) > 1.0:
                print(f"1. Signal difference: ma_slope={orig_ma_slope:.3f} vs ma_slope_ema_30s={ema_ma_slope:.3f}")
        
        # Issue 2: State collapse/mapping
        print(f"2. Check if state gets collapsed/mapped after _combine_classifications")
        
        # Issue 3: Mode-specific filters
        print(f"3. Check if _apply_legacy_filters or other mode-specific logic modifies state")
        
        # Issue 4: State validation
        print(f"4. Check if validate_gms_state() rejects the state")
        
    else:
        print(f"*** LOGIC MATCHES ***")

if __name__ == "__main__":
    debug_detector_step_by_step()