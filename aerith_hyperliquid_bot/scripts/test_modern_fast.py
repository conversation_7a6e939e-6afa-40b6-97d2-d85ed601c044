#!/usr/bin/env python3
"""
Fast Modern System Test with Pre-computed Regimes
=================================================

This test uses pre-computed regime states for 100x faster backtesting.
Run 'python scripts/precompute_regimes.py' first to generate regime cache.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from datetime import datetime
from pathlib import Path
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
from hyperliquid_bot.modern.regime_cache import RegimeCache
import json

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Reduce noise from data loaders
for logger_name in ['ModernDataLoader', 'ModernDataAdapter', 'ModernDataContract']:
    logging.getLogger(logger_name).setLevel(logging.WARNING)

# Suppress pandas warnings
import warnings
warnings.filterwarnings('ignore')


def check_regime_cache(start_date: datetime, end_date: datetime) -> bool:
    """Check if regime cache is available for the date range."""
    cache = RegimeCache()
    
    print("Checking regime cache...")
    cache_info = cache.get_cache_info()
    print(f"  Cache directory: {cache_info['cache_dir']}")
    print(f"  Loaded years: {cache_info['loaded_years']}")
    
    if not cache.validate_cache(start_date, end_date):
        print("\n❌ Regime cache not found!")
        print("\nTo generate regime cache, run:")
        print(f"  python scripts/precompute_regimes.py --start {start_date.strftime('%Y-%m-%d')} --end {end_date.strftime('%Y-%m-%d')}")
        return False
    
    print("✅ Regime cache found and validated!")
    return True


def main():
    print("=== Fast Modern System Test ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Test with available data
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 20)  # 5 days
    
    print(f"Test period: {start_date.date()} to {end_date.date()}")
    print(f"Risk per trade: {config.portfolio.risk_per_trade:.2%}")
    print(f"Regime detector: {config.regime.detector_type}")
    print(f"Strategy: TF-v3 Modern\n")
    
    # Check regime cache first
    if not check_regime_cache(start_date, end_date):
        return 1
    
    # Create and run backtest
    try:
        print("\nInitializing backtest engine...")
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date,
            use_regime_cache=True  # Use pre-computed regimes!
        )
        
        print("Starting FAST backtest with pre-computed regimes...")
        print("-" * 50)
        
        import time
        start_time = time.time()
        
        results = engine.run_backtest()
        
        elapsed = time.time() - start_time
        
        # Display results
        print("\n" + "=" * 50)
        print("BACKTEST RESULTS")
        print("=" * 50)
        
        trades = results['trades']
        perf = results['performance']
        
        print(f"\nTotal trades: {len(trades)}")
        print(f"Runtime: {elapsed:.1f}s ({elapsed/120:.2f}s per hour)")
        
        if elapsed < 10:
            print("✅ FAST execution confirmed! (< 10 seconds)")
        else:
            print("⚠️  Still slower than expected")
        
        if trades:
            print(f"\n✅ SUCCESS! Generated {len(trades)} trades")
            print(f"Total return: {perf.get('total_return', 0):.2%}")
            print(f"Win rate: {perf.get('win_rate', 0):.2%}")
            
            # Show first 3 trades
            print("\nFirst 3 trades:")
            for i, trade in enumerate(trades[:3]):
                print(f"\n{i+1}. {trade['timestamp']}")
                print(f"   Direction: {trade['direction']}")
                print(f"   Entry: ${trade['entry_price']:.2f}")
                print(f"   Size: {trade['position_size']:.2%}")
                print(f"   Regime: {trade.get('regime', 'N/A')}")
                print(f"   Confidence: {trade.get('confidence', 0):.2f}")
            
            # Analyze regime distribution
            regime_counts = {}
            for trade in trades:
                regime = trade.get('regime', 'Unknown')
                regime_counts[regime] = regime_counts.get(regime, 0) + 1
            
            print("\nTrades by regime:")
            for regime, count in sorted(regime_counts.items()):
                print(f"  {regime}: {count} trades ({count/len(trades)*100:.1f}%)")
        else:
            print("\n❌ NO TRADES GENERATED!")
            print("\nDebugging information:")
            
            # Check regime distribution
            if results['regime_history']:
                states = [r['regime'] for r in results['regime_history'][:50]]
                unique_states = set(states)
                print(f"  Regime states detected: {unique_states}")
                print(f"  Total regime hours: {len(results['regime_history'])}")
                
                # Sample some regimes
                print("\n  Sample regimes:")
                for i in range(0, min(10, len(results['regime_history'])), 2):
                    r = results['regime_history'][i]
                    print(f"    {r['timestamp']}: {r['regime']} (conf: {r['confidence']:.2f})")
            
            print("\nPossible issues:")
            print("  1. TF-v3 entry conditions too strict")
            print("  2. Risk fraction too low (currently 25%)")
            print("  3. Indicators not calculating properly")
            print("  4. Regime states not allowing trades")
        
        # Save results
        output_file = f"modern_fast_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\nFull results saved to: {output_file}")
        
        # Summary
        print("\n" + "=" * 50)
        if trades and len(trades) >= 30:
            print("✅ Modern system working well!")
        elif trades:
            print(f"⚠️  Trade count ({len(trades)}) below expectations")
            print("   Consider adjusting thresholds or entry conditions")
        else:
            print("❌ System needs debugging - no trades generated")
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())