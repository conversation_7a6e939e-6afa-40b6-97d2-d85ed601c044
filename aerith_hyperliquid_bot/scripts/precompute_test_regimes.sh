#!/bin/bash
# Pre-compute regimes for testing period

echo "Pre-computing regimes for January 2024 test period..."
echo "This will take a few minutes but only needs to be done ONCE."
echo ""

# Create output directory
mkdir -p data/precomputed_regimes

# Pre-compute January 2024
python scripts/precompute_regimes.py \
    --start 2024-01-01 \
    --end 2024-02-01 \
    --config configs/overrides/modern_system_v2_complete.yaml \
    --log-level INFO

echo ""
echo "Pre-computation complete! You can now run fast backtests with:"
echo "  python scripts/test_modern_fast.py"