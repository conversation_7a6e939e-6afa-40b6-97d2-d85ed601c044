#!/usr/bin/env python3
"""
Test script to validate all visualization components.
"""

import json
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.resolve()
sys.path.insert(0, str(project_root))

from scripts.visualization import (
    PlotConfig,
    BacktestDataProcessor,
    PlotComponents,
    EquityCurveCalculator,
    PerformanceAnalytics
)


def test_components():
    """Test all visualization components."""
    print("="*80)
    print("TESTING VISUALIZATION COMPONENTS")
    print("="*80)
    
    # Test 1: Configuration
    print("\n1. Testing PlotConfig...")
    try:
        config = PlotConfig()
        assert config.default_log_dir == Path("/Users/<USER>/Desktop/trading_bot_/logs")
        assert config.appearance.dpi == 300
        assert config.show_equity_curve == True
        print("   ✅ PlotConfig: PASSED")
    except Exception as e:
        print(f"   ❌ PlotConfig: FAILED - {e}")
        return False
    
    # Test 2: Data Processor
    print("\n2. Testing BacktestDataProcessor...")
    try:
        processor = BacktestDataProcessor(config)
        signals_path, trades_path = processor.find_latest_backtest_files()
        
        if not signals_path or not trades_path:
            print("   ⚠️  No backtest files found in logs directory")
            return False
        
        # Load data
        signals_df = processor.load_signals_data(signals_path)
        trades_list = processor.load_trades_data(trades_path)
        
        print(f"   - Found signals: {Path(signals_path).name}")
        print(f"   - Found trades: {Path(trades_path).name}")
        print(f"   - Signals shape: {signals_df.shape}")
        print(f"   - Trades count: {len(trades_list)}")
        print("   ✅ BacktestDataProcessor: PASSED")
    except Exception as e:
        print(f"   ❌ BacktestDataProcessor: FAILED - {e}")
        return False
    
    # Test 3: Plot Components
    print("\n3. Testing PlotComponents...")
    try:
        plotter = PlotComponents(config)
        
        # Test style creation
        style = plotter.create_custom_style()
        assert 'marketcolors' in style
        assert style['y_on_right'] == True
        
        print("   - Custom style created")
        print("   ✅ PlotComponents: PASSED")
    except Exception as e:
        print(f"   ❌ PlotComponents: FAILED - {e}")
        return False
    
    # Test 4: Equity Curve Calculator
    print("\n4. Testing EquityCurveCalculator...")
    try:
        calc = EquityCurveCalculator(initial_capital=10000)
        
        # Test with subset of data
        equity_df = calc.calculate_equity_curve(
            trades_list[:10] if trades_list else [],
            signals_df.index[:1000]
        )
        
        # Calculate metrics
        metrics = calc.calculate_portfolio_metrics(equity_df)
        
        print(f"   - Initial capital: $10,000")
        print(f"   - Final equity: ${equity_df['equity'].iloc[-1]:,.2f}")
        print(f"   - Total return: {metrics['total_return']:.2f}%")
        print(f"   - Sharpe ratio: {metrics['sharpe_ratio']:.2f}")
        print("   ✅ EquityCurveCalculator: PASSED")
    except Exception as e:
        print(f"   ❌ EquityCurveCalculator: FAILED - {e}")
        return False
    
    # Test 5: Performance Analytics
    print("\n5. Testing PerformanceAnalytics...")
    try:
        analytics = PerformanceAnalytics()
        
        # Test trade metrics
        trade_metrics = analytics.calculate_trade_metrics(trades_list)
        
        print(f"   - Total trades: {trade_metrics['total_trades']}")
        print(f"   - Win rate: {trade_metrics['win_rate']:.2f}%")
        print(f"   - Profit factor: {trade_metrics['profit_factor']:.2f}")
        print(f"   - Average PnL: ${trade_metrics['average_pnl']:.2f}")
        
        # Test regime performance
        if 'regime' in signals_df.columns:
            regime_perf = analytics.calculate_regime_performance(trades_list, signals_df)
            print(f"   - Regime analysis: {len(regime_perf)} regimes analyzed")
        
        print("   ✅ PerformanceAnalytics: PASSED")
    except Exception as e:
        print(f"   ❌ PerformanceAnalytics: FAILED - {e}")
        return False
    
    # Test 6: Integration Test
    print("\n6. Testing Full Integration...")
    try:
        # This would normally call the main visualization function
        # but we'll just verify all components can work together
        
        # Filter data
        filtered_df = processor.filter_by_date_range(signals_df)
        assert len(filtered_df) > 0
        
        # Prepare indicators
        indicators = ['ma_slope'] if 'ma_slope' in signals_df.columns else []
        indicator_data = processor.prepare_indicator_data(signals_df, indicators)
        
        print(f"   - Data filtering: OK")
        print(f"   - Indicator preparation: {len(indicator_data)} indicators")
        print("   ✅ Integration Test: PASSED")
    except Exception as e:
        print(f"   ❌ Integration Test: FAILED - {e}")
        return False
    
    print("\n" + "="*80)
    print("ALL TESTS PASSED! ✅")
    print("="*80)
    print("\nThe visualization system is ready to use:")
    print("  - Auto mode: python scripts/visualization/visualize.py --auto")
    print("  - Full mode: python scripts/visualization/run_full.py")
    print("="*80)
    
    return True


if __name__ == "__main__":
    success = test_components()
    sys.exit(0 if success else 1)