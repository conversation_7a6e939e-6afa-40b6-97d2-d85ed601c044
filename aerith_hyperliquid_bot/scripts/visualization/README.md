# Backtest Visualization System

This enhanced visualization system provides comprehensive analysis and plotting capabilities for trading backtest results.

## Features

### Core Visualization
- **Price Charts**: Candlestick charts with regime-aware background shading
- **Technical Indicators**: EMAs, MA Slope, OBI, Fear/Greed Index
- **Trade Markers**: Entry/exit points with optional PnL labels
- **EMA Crossovers**: Bullish/bearish crossover detection and marking
- **Volume Bars**: Colored by price direction

### New Features
- **Equity Curve**: Real-time portfolio value tracking with drawdown visualization
- **Performance Metrics**: Sharpe ratio, max drawdown, win rate displayed on chart
- **Trade Distribution**: Histogram and cumulative PnL analysis
- **Regime Performance**: Performance breakdown by market regime
- **Detailed Analytics**: Comprehensive JSON report with risk metrics

## Architecture

### Modular Design
```
visualization/
├── __init__.py           # Package exports
├── config.py            # Configuration classes and constants
├── data_processor.py    # Data loading and validation
├── plot_components.py   # Modular plotting functions
├── equity_curve.py      # Portfolio calculations
└── analytics.py         # Performance analytics
```

### Key Components

1. **PlotConfig**: Centralized configuration for all visual elements
   - Color schemes for regimes, candles, indicators
   - Panel ratios and appearance settings
   - Feature toggles

2. **BacktestDataProcessor**: Handles data operations
   - File discovery and loading
   - Data validation
   - Date filtering and sampling
   - Indicator preparation

3. **PlotComponents**: Reusable plotting functions
   - Regime backgrounds
   - EMA crossovers
   - Trade markers
   - Volume coloring
   - Legend creation

4. **EquityCurveCalculator**: Portfolio metrics
   - Equity curve from trade history
   - Drawdown calculation
   - Performance metrics (Sharpe, returns, etc.)

5. **PerformanceAnalytics**: Advanced analysis
   - Trade distribution plots
   - Regime-based performance
   - Risk metrics (VaR, Sortino, Calmar)
   - Monthly returns

## Usage

### Basic Visualization
```bash
# Auto-detect latest backtest files
python scripts/visualization/visualize.py --auto

# Specific files
python scripts/visualization/visualize.py \
    --signals /path/to/signals.parquet \
    --trades /path/to/trades.json \
    --output /path/to/output.png
```

### Advanced Features
```bash
# With detailed analytics and regime transitions
python scripts/visualization/visualize.py --auto \
    --detailed-analytics \
    --regime-transitions \
    --trade-labels

# Limited data points with sampling
python scripts/visualization/visualize.py --auto \
    --max-points 5000 \
    --sample

# Run full analysis with one command
python scripts/visualization/run_full.py
```

### Configuration
The system respects settings from `configs/base.yaml`:
```yaml
visualization:
  panels:
    show_ma_slope: true
    show_obi: false
    show_fear_greed: false
  appearance:
    max_points: 20000
    price_panel_ratio: 4
    indicator_panel_ratio: 1
```

## Improvements Made

### Code Quality
- ✅ Organized imports following PEP 8
- ✅ Removed hardcoded paths
- ✅ Broke down 600+ line function into modular components
- ✅ Added comprehensive type hints
- ✅ Improved error handling with specific exceptions
- ✅ Eliminated code duplication

### Performance
- ✅ Efficient data sampling preserving trade points
- ✅ Optimized regime background drawing
- ✅ Cached calculations for metrics

### Maintainability
- ✅ Clear separation of concerns
- ✅ Reusable components
- ✅ Comprehensive documentation
- ✅ Consistent naming conventions
- ✅ Testable architecture

## Compatibility

The system maintains full backward compatibility while supporting:
- Legacy 7-state regime visualization
- Modern 3-state (BULL/BEAR/CHOP) mapping
- Both detector types (rule_based, continuous_gms)
- Various data formats and schemas

## Output Files

When run with full analytics, generates:
1. `*_visualization.png` - Main chart with all features
2. `*_comparison.png` - Price vs SMA comparison
3. `*_trade_distribution.png` - Trade analysis plots
4. `*_performance_report.json` - Detailed metrics
5. `*_regime_transitions.png` - Regime timeline (if available)