#!/usr/bin/env python3
"""
Convenience wrapper for the full visualization script.
This maintains backward compatibility with existing scripts and documentation.
"""

import subprocess
import sys
from pathlib import Path

# Point to the actual script
script = Path(__file__).parent / "run_full.py"

# Pass all arguments to the actual script
sys.exit(subprocess.call([sys.executable, str(script)] + sys.argv[1:]))