"""
Visualization package for backtest results.

This package provides modular components for creating comprehensive
visualizations of trading backtest results, including price charts,
indicators, regime backgrounds, trade markers, and equity curves.
"""

from .config import (
    PlotConfig,
    RegimeColors,
    ThreeStateColors,
    AppearanceConfig,
    DEFAULT_PLOT_CONFIG
)
from .data_processor import BacktestDataProcessor
from .plot_components import PlotComponents
from .equity_curve import EquityCurveCalculator
from .analytics import PerformanceAnalytics

__all__ = [
    'PlotConfig',
    'RegimeColors', 
    'ThreeStateColors',
    'AppearanceConfig',
    'DEFAULT_PLOT_CONFIG',
    'BacktestDataProcessor',
    'PlotComponents',
    'EquityCurveCalculator',
    'PerformanceAnalytics'
]