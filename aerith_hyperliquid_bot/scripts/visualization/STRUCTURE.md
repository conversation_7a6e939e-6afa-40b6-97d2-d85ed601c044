# Visualization Module Structure

## Directory Layout

```
scripts/
├── visualize_backtest.py      # Backward compatibility wrapper
├── run_full_visualization.py  # Backward compatibility wrapper
└── visualization/
    ├── __init__.py            # Package exports
    ├── README.md              # Documentation
    ├── STRUCTURE.md           # This file
    │
    ├── # Core Module Files
    ├── config.py              # Configuration classes
    ├── data_processor.py      # Data loading and validation
    ├── plot_components.py     # Plotting components
    ├── equity_curve.py        # Portfolio calculations
    ├── analytics.py           # Performance analytics
    │
    └── # Executable Scripts
        ├── visualize.py       # Main visualization script
        ├── run_full.py        # Full analysis script
        └── test_components.py # Component testing script
```

## Usage

### From Project Root
```bash
# Using backward compatibility wrappers (recommended)
python scripts/visualize_backtest.py --auto
python scripts/run_full_visualization.py

# Direct access
python scripts/visualization/visualize.py --auto
python scripts/visualization/run_full.py
python scripts/visualization/test_components.py
```

### Module Import
```python
from scripts.visualization import (
    PlotConfig,
    BacktestDataProcessor,
    PlotComponents,
    EquityCurveCalculator,
    PerformanceAnalytics
)
```

## Key Features

- **Auto-detection**: Automatically finds latest backtest in `/Users/<USER>/Desktop/trading_bot_/logs`
- **Backward Compatibility**: Wrapper scripts maintain existing command compatibility
- **Modular Design**: Reusable components for custom visualizations
- **Professional Analytics**: Equity curves, performance metrics, trade analysis