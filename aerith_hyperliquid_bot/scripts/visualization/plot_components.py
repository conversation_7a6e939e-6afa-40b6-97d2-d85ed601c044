"""
Modular plotting components for backtest visualization.

This module provides individual plotting functions that can be composed
to create comprehensive backtest visualizations.
"""

import logging
from typing import Dict, List, Optional, Tuple, Any

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.lines import Line2D
import mplfinance as mpf
import pandas as pd
import numpy as np

from .config import PlotConfig, PlotConstants


logger = logging.getLogger(__name__)


class PlotComponents:
    """Provides modular plotting components for backtest visualization."""
    
    def __init__(self, config: PlotConfig):
        """
        Initialize plot components.
        
        Args:
            config: Plot configuration object
        """
        self.config = config
        
    def create_custom_style(self) -> dict:
        """Create custom mplfinance style."""
        return mpf.make_mpf_style(
            marketcolors=mpf.make_marketcolors(
                up=self.config.candle_colors.up,
                down=self.config.candle_colors.down,
                edge='inherit',
                wick={
                    'up': self.config.candle_colors.up,
                    'down': self.config.candle_colors.down
                },
                ohlc='inherit',
                volume={
                    'up': self.config.candle_colors.up,
                    'down': self.config.candle_colors.down
                },
            ),
            gridstyle=self.config.appearance.grid_style,
            y_on_right=self.config.appearance.price_on_right,
            rc={
                'axes.linewidth': 1.5,
                'axes.edgecolor': 'black',
                'axes.grid': False,
                'figure.facecolor': 'white',
                'xtick.color': 'black',
                'ytick.color': 'black',
                'text.color': 'black',
                'lines.linewidth': self.config.candle_colors.wick_width,
                'patch.linewidth': self.config.candle_colors.edge_width,
            },
            base_mpf_style='yahoo',
        )
    
    def plot_regime_backgrounds(self, ax: plt.Axes, signals_df: pd.DataFrame,
                              regime_column: str = 'regime',
                              use_three_state: bool = False,
                              state_map: Optional[Dict[str, str]] = None) -> None:
        """
        Plot regime backgrounds on the given axes.
        
        Args:
            ax: Matplotlib axes to plot on
            signals_df: DataFrame with regime data
            regime_column: Name of regime column
            use_three_state: Whether to use 3-state colors
            state_map: Optional state mapping dictionary
        """
        if regime_column not in signals_df.columns:
            logger.warning(f"Regime column '{regime_column}' not found")
            return
        
        # Choose color scheme
        if use_three_state:
            colors = self.config.three_state_colors.to_dict()
            default_color = self.config.three_state_colors.default
        else:
            colors = self.config.regime_colors.to_dict()
            default_color = self.config.regime_colors.default
        
        # Apply state mapping if provided
        regime_series = signals_df[regime_column]
        if state_map:
            regime_series = regime_series.map(lambda x: state_map.get(x, x))
        
        # Group consecutive regimes
        regime_changes = regime_series != regime_series.shift(1)
        change_points = signals_df.index[regime_changes].tolist()
        
        # Ensure endpoints are included
        if not change_points or change_points[0] != signals_df.index[0]:
            change_points.insert(0, signals_df.index[0])
        if not change_points or change_points[-1] != signals_df.index[-1]:
            change_points.append(signals_df.index[-1])
        
        # Draw regime spans
        for i in range(len(change_points) - 1):
            start_dt = change_points[i]
            end_dt = change_points[i + 1]
            
            regime = regime_series.loc[start_dt]
            if pd.isna(regime):
                continue
            
            # Get indices for axvspan
            start_idx = signals_df.index.get_loc(start_dt)
            end_idx = signals_df.index.get_loc(end_dt)
            
            # Adjust end index for proper span
            if end_idx < len(signals_df) - 1:
                end_idx -= 0.5
            
            color = colors.get(regime, default_color)
            
            ax.axvspan(
                start_idx - 0.5,
                end_idx + 0.5,
                color=color,
                alpha=1.0,  # Alpha is included in color tuple
                zorder=PlotConstants.REGIME_ZORDER
            )
        
        logger.info(f"Plotted regime backgrounds for {len(change_points)-1} regime periods")
    
    def plot_ema_crossovers(self, ax: plt.Axes, signals_df: pd.DataFrame) -> List[Line2D]:
        """
        Plot EMA crossover markers.
        
        Args:
            ax: Matplotlib axes to plot on
            signals_df: DataFrame with EMA data
            
        Returns:
            List of legend elements for crossovers
        """
        legend_elements = []
        
        if 'tf_ewma_fast' not in signals_df.columns or 'tf_ewma_slow' not in signals_df.columns:
            logger.warning("EMA columns not found for crossover detection")
            return legend_elements
        
        fast_ema = signals_df['tf_ewma_fast']
        slow_ema = signals_df['tf_ewma_slow']
        
        # Detect crossovers
        cross_above = (fast_ema > slow_ema) & (fast_ema.shift(1) <= slow_ema.shift(1))
        cross_below = (fast_ema < slow_ema) & (fast_ema.shift(1) >= slow_ema.shift(1))
        
        # Get crossover indices
        cross_above_idx = signals_df.index[cross_above]
        cross_below_idx = signals_df.index[cross_below]
        
        # Convert to integer indices
        cross_above_iloc = [signals_df.index.get_loc(idx) for idx in cross_above_idx]
        cross_below_iloc = [signals_df.index.get_loc(idx) for idx in cross_below_idx]
        
        logger.info(f"Found {len(cross_above_iloc)} bullish and {len(cross_below_iloc)} bearish crossovers")
        
        # Plot crossovers
        colors = self.config.crossover_colors
        
        # Bullish crosses
        for idx in cross_above_iloc:
            price_level = (fast_ema.iloc[idx] + slow_ema.iloc[idx]) / 2
            # White outline
            ax.plot(idx, price_level, marker='X', color='white',
                   markersize=colors.outline_size, markeredgewidth=1.0)
            # Colored marker
            ax.plot(idx, price_level, marker='X', color=colors.bullish_cross,
                   markersize=colors.marker_size, markeredgewidth=0.5)
        
        # Bearish crosses
        for idx in cross_below_iloc:
            price_level = (fast_ema.iloc[idx] + slow_ema.iloc[idx]) / 2
            # White outline
            ax.plot(idx, price_level, marker='X', color='white',
                   markersize=colors.outline_size, markeredgewidth=1.0)
            # Colored marker
            ax.plot(idx, price_level, marker='X', color=colors.bearish_cross,
                   markersize=colors.marker_size, markeredgewidth=0.5)
        
        # Create legend elements
        if cross_above_iloc:
            legend_elements.append(
                Line2D([0], [0], marker='X', color='w',
                       markerfacecolor=colors.bullish_cross,
                       markersize=8, label='Bullish Cross')
            )
        if cross_below_iloc:
            legend_elements.append(
                Line2D([0], [0], marker='X', color='w',
                       markerfacecolor=colors.bearish_cross,
                       markersize=8, label='Bearish Cross')
            )
        
        return legend_elements
    
    def plot_trade_markers(self, ax: plt.Axes, signals_df: pd.DataFrame,
                          trades_list: List[Dict], show_labels: bool = False) -> List[Line2D]:
        """
        Plot trade entry and exit markers.
        
        Args:
            ax: Matplotlib axes to plot on
            signals_df: DataFrame with price data
            trades_list: List of trade dictionaries
            show_labels: Whether to show PnL labels
            
        Returns:
            List of legend elements for trades
        """
        if not trades_list:
            return []
        
        # Filter trades to visible range
        visible_trades = self._filter_visible_trades(signals_df, trades_list)
        logger.info(f"Plotting {len(visible_trades)} trades in visible range")
        
        # Plot each trade
        for trade in visible_trades:
            try:
                # Convert timestamps
                entry_time = pd.to_datetime(trade['entry_time'], unit='s', utc=True)
                exit_time = pd.to_datetime(trade['exit_time'], unit='s', utc=True)
                
                # Clip to visible range
                entry_time = max(entry_time, signals_df.index.min())
                exit_time = min(exit_time, signals_df.index.max())
                
                # Get indices
                entry_idx = signals_df.index.get_indexer([entry_time], method='nearest')[0]
                exit_idx = signals_df.index.get_indexer([exit_time], method='nearest')[0]
                
                if entry_idx < 0 or exit_idx < 0:
                    continue
                
                # Trade details
                entry_price = trade['entry']
                exit_price = trade['exit']
                trade_type = trade['type']
                
                # Entry marker
                entry_marker = '^' if trade_type == 'long' else 'v'
                entry_color = 'green' if trade_type == 'long' else 'red'
                ax.plot(entry_idx, entry_price, marker=entry_marker,
                       color=entry_color, markersize=5, alpha=0.6,
                       zorder=PlotConstants.TRADE_ZORDER)
                
                # Exit marker
                ax.plot(exit_idx, exit_price, marker='o',
                       color='blue', markersize=3, alpha=0.6,
                       zorder=PlotConstants.TRADE_ZORDER)
                
                # Connection line
                ax.plot([entry_idx, exit_idx], [entry_price, exit_price],
                       linestyle='-', color='blue', alpha=0.2, linewidth=0.8)
                
                # PnL label
                if show_labels and 'pnl' in trade:
                    pnl = trade['pnl']
                    label_color = 'green' if pnl > 0 else 'red'
                    ax.annotate(f"{pnl:.2f}",
                               xy=(exit_idx, exit_price),
                               xytext=(5, 5), textcoords='offset points',
                               color=label_color, fontsize=8)
                
            except Exception as e:
                logger.warning(f"Error plotting trade: {e}")
        
        # Create legend elements
        legend_elements = [
            Line2D([0], [0], marker='^', color='w', markerfacecolor='green',
                   markersize=6, label='Long Entry', linestyle='None'),
            Line2D([0], [0], marker='v', color='w', markerfacecolor='red',
                   markersize=6, label='Short Entry', linestyle='None'),
            Line2D([0], [0], marker='o', color='w', markerfacecolor='blue',
                   markersize=5, label='Trade Exit', linestyle='None')
        ]
        
        return legend_elements
    
    def _filter_visible_trades(self, signals_df: pd.DataFrame,
                              trades_list: List[Dict]) -> List[Dict]:
        """Filter trades to those visible in the current time range."""
        visible_trades = []
        min_time = signals_df.index.min().timestamp()
        max_time = signals_df.index.max().timestamp()
        
        for trade in trades_list:
            entry_time = trade['entry_time']
            exit_time = trade['exit_time']
            
            # Include if any part of trade is visible
            if (entry_time >= min_time and entry_time <= max_time) or \
               (exit_time >= min_time and exit_time <= max_time) or \
               (entry_time <= min_time and exit_time >= max_time):
                visible_trades.append(trade)
        
        return visible_trades
    
    def color_volume_bars(self, ax: plt.Axes, signals_df: pd.DataFrame) -> None:
        """
        Manually color volume bars based on price direction.
        
        Args:
            ax: Volume axes
            signals_df: DataFrame with OHLC data
        """
        try:
            up_color = self.config.candle_colors.up
            down_color = self.config.candle_colors.down
            
            for i, patch in enumerate(ax.patches):
                if i < len(signals_df):
                    if signals_df['close'].iloc[i] >= signals_df['open'].iloc[i]:
                        patch.set_facecolor(up_color)
                    else:
                        patch.set_facecolor(down_color)
                    patch.set_edgecolor(patch.get_facecolor())
            
            logger.info("Applied colors to volume bars")
        except Exception as e:
            logger.error(f"Error coloring volume bars: {e}")
    
    def create_comprehensive_legend(self, fig: plt.Figure,
                                   legend_elements: List[Line2D]) -> None:
        """
        Create a comprehensive legend for the figure.
        
        Args:
            fig: Matplotlib figure
            legend_elements: List of legend elements to include
        """
        if not legend_elements:
            return
        
        # Calculate columns
        n_items = len(legend_elements)
        ncol = max(
            PlotConstants.MIN_LEGEND_COLUMNS,
            min(PlotConstants.MAX_LEGEND_COLUMNS,
                (n_items + PlotConstants.LEGEND_ITEMS_PER_ROW - 1) // PlotConstants.LEGEND_ITEMS_PER_ROW)
        )
        
        # Create legend
        legend = fig.legend(
            handles=legend_elements,
            loc='upper center',
            bbox_to_anchor=(0.5, 0.96),
            ncol=ncol,
            frameon=True,
            fancybox=True,
            shadow=True,
            fontsize=9
        )
        
        # Adjust layout
        fig.subplots_adjust(top=0.85)
        
        logger.info(f"Created legend with {n_items} items in {ncol} columns")
    
    def format_time_axis(self, ax: plt.Axes, time_series: pd.DatetimeIndex) -> None:
        """
        Format time axis based on data frequency.
        
        Args:
            ax: Matplotlib axes
            time_series: DatetimeIndex to determine format
        """
        # Determine time format based on frequency
        time_diff = time_series.to_series().diff().median()
        
        if time_diff >= pd.Timedelta(days=1):
            date_format = PlotConstants.DAILY_FORMAT
        elif time_diff >= pd.Timedelta(hours=1):
            date_format = PlotConstants.HOURLY_FORMAT
        else:
            date_format = PlotConstants.MINUTE_FORMAT
        
        ax.xaxis.set_major_formatter(mdates.DateFormatter(date_format))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        logger.info(f"Formatted time axis with format: {date_format}")
    
    def add_indicator_panel(self, ax: plt.Axes, data: pd.Series,
                           ylabel: str, add_zero_line: bool = False) -> None:
        """
        Add formatting to indicator panel.
        
        Args:
            ax: Matplotlib axes for the indicator
            data: Indicator data series
            ylabel: Y-axis label
            add_zero_line: Whether to add horizontal line at zero
        """
        ax.set_ylabel(ylabel)
        ax.grid(False)
        
        if add_zero_line:
            ax.axhline(0, color='gray', linestyle='--', linewidth=0.8)
        
        # Remove top and right spines
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)