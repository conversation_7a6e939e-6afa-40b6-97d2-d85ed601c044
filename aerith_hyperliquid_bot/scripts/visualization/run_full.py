#!/usr/bin/env python3
"""
Run comprehensive backtest visualization with all features enabled.

This script demonstrates the full capabilities of the enhanced visualization system.
"""

import subprocess
import sys
from pathlib import Path
import argparse


def main():
    """Run full visualization with all analytics."""
    parser = argparse.ArgumentParser(
        description="Run comprehensive backtest visualization",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage (auto-detect files)
  python run_full_visualization.py
  
  # With specific date range
  python run_full_visualization.py --start 2024-01-01 --end 2024-12-31
  
  # With custom output directory
  python run_full_visualization.py --output-dir ./visualizations/
        """
    )
    
    parser.add_argument('--start', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end', help='End date (YYYY-MM-DD)')
    parser.add_argument('--output-dir', type=Path, help='Output directory for all files')
    parser.add_argument('--title', default='Full Backtest Analysis', help='Plot title')
    parser.add_argument('--no-analytics', action='store_true', help='Skip detailed analytics')
    parser.add_argument('--no-transitions', action='store_true', help='Skip regime transitions')
    
    args = parser.parse_args()
    
    # Build command
    script_path = Path(__file__).parent / 'visualize.py'
    cmd = [
        sys.executable,
        str(script_path),
        '--auto',  # Auto-detect latest files
        '--title', args.title,
        '--trade-labels',  # Show PnL on trades
    ]
    
    # Add analytics features
    if not args.no_analytics:
        cmd.append('--detailed-analytics')
    
    if not args.no_transitions:
        cmd.append('--regime-transitions')
    
    # Add date range
    if args.start:
        cmd.extend(['--start', args.start])
    if args.end:
        cmd.extend(['--end', args.end])
    
    # Add output directory
    if args.output_dir:
        args.output_dir.mkdir(parents=True, exist_ok=True)
        # Generate output paths with timestamp
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        base_name = f"backtest_analysis_{timestamp}"
        
        cmd.extend([
            '--output', str(args.output_dir / f"{base_name}_main.png"),
            '--comparison-output', str(args.output_dir / f"{base_name}_comparison.png")
        ])
    
    print("Running comprehensive visualization...")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 80)
    
    # Run the visualization
    try:
        result = subprocess.run(cmd, check=True)
        print("\n" + "="*80)
        print("✅ Visualization complete!")
        print("="*80)
        
        if args.output_dir:
            print(f"\nOutput files saved to: {args.output_dir}")
            print("\nGenerated files:")
            for file in sorted(args.output_dir.glob("*")):
                print(f"  - {file.name}")
        
        return result.returncode
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Visualization failed with error code: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print("\n⚠️  Visualization interrupted by user")
        return 1


if __name__ == "__main__":
    sys.exit(main())