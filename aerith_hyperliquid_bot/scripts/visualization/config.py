"""
Configuration classes and constants for backtest visualization.

This module provides structured configuration for plot appearance,
regime colors, and other visualization settings.
"""

from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional, Tuple


# Color type alias for RGBA tuples
Color = Tuple[float, float, float, float]


@dataclass
class RegimeColors:
    """Color mapping for 7-state regime visualization."""
    
    strong_bull_trend: Color = (0.0, 1.0, 0.0, 0.85)      # Green
    weak_bull_trend: Color = (0.56, 0.93, 0.56, 0.85)    # Light Green
    strong_bear_trend: Color = (1.0, 0.0, 0.0, 0.85)     # Red
    weak_bear_trend: Color = (1.0, 0.71, 0.76, 0.85)     # Light Red
    high_vol_range: Color = (1.0, 0.65, 0.0, 0.85)       # Orange
    low_vol_range: Color = (0.68, 0.85, 0.9, 0.85)       # Light Blue
    tight_spread: Color = (1.0, 1.0, 0.8, 0.85)          # Light Yellow
    uncertain: Color = (0.83, 0.83, 0.83, 0.85)          # Light Grey
    unknown: Color = (0.5, 0.0, 0.5, 0.85)               # Purple
    filter_off: Color = (1.0, 1.0, 1.0, 0.0)             # Transparent
    default: Color = (0.5, 0.5, 0.5, 0.8)                # Grey
    
    def to_dict(self) -> Dict[str, Color]:
        """Convert to dictionary format for legacy compatibility."""
        return {
            "Strong_Bull_Trend": self.strong_bull_trend,
            "Weak_Bull_Trend": self.weak_bull_trend,
            "Strong_Bear_Trend": self.strong_bear_trend,
            "Weak_Bear_Trend": self.weak_bear_trend,
            "High_Vol_Range": self.high_vol_range,
            "Low_Vol_Range": self.low_vol_range,
            "TIGHT_SPREAD": self.tight_spread,
            "Uncertain": self.uncertain,
            "Unknown": self.unknown,
            "Filter_Off": self.filter_off
        }


@dataclass
class ThreeStateColors:
    """Color mapping for 3-state regime visualization."""
    
    bull: Color = (0.0, 1.0, 0.0, 0.85)     # Green
    bear: Color = (1.0, 0.0, 0.0, 0.85)     # Red
    chop: Color = (1.0, 0.65, 0.0, 0.85)    # Orange
    default: Color = (0.5, 0.5, 0.5, 0.8)   # Grey
    
    def to_dict(self) -> Dict[str, Color]:
        """Convert to dictionary format for legacy compatibility."""
        return {
            "BULL": self.bull,
            "BEAR": self.bear,
            "CHOP": self.chop
        }


@dataclass
class CrossoverColors:
    """Colors for EMA crossover markers."""
    
    bullish_cross: str = '#9932CC'  # Purple
    bearish_cross: str = '#00008B'  # Deep Blue
    marker_size: int = 6
    outline_size: int = 8


@dataclass
class CandleColors:
    """Colors for candlestick charts."""
    
    up: str = '#00b060'      # Dark Green
    down: str = '#e14340'    # Dark Red
    edge_width: float = 2.0
    wick_width: float = 1.5


@dataclass
class IndicatorColors:
    """Colors for technical indicators."""
    
    ema_fast: str = '#00FFFF'  # Cyan
    ema_slow: str = '#FF00FF'  # Magenta
    line_width: float = 1.0


@dataclass
class AppearanceConfig:
    """Visual appearance configuration."""

    price_panel_ratio: int = 4
    indicator_panel_ratio: int = 1
    volume_panel_ratio: int = 1
    equity_panel_ratio: int = 2  # New for equity curve
    max_points: int = 5000  # Reduced from 20000 to improve chart readability
    figure_scale: float = 2.5
    figure_ratio: Tuple[int, int] = (16, 10)
    dpi: int = 300
    grid_style: str = ""
    price_on_right: bool = True


@dataclass
class PlotConfig:
    """Main configuration container for visualization."""
    
    # Color schemes
    regime_colors: RegimeColors = field(default_factory=RegimeColors)
    three_state_colors: ThreeStateColors = field(default_factory=ThreeStateColors)
    crossover_colors: CrossoverColors = field(default_factory=CrossoverColors)
    candle_colors: CandleColors = field(default_factory=CandleColors)
    indicator_colors: IndicatorColors = field(default_factory=IndicatorColors)
    
    # Appearance settings
    appearance: AppearanceConfig = field(default_factory=AppearanceConfig)
    
    # Panel visibility
    show_volume: bool = True
    show_ma_slope: bool = False
    show_obi: bool = False
    show_fear_greed: bool = False
    show_equity_curve: bool = True  # New feature
    
    # Trade display options
    show_trades: bool = True
    show_trade_labels: bool = False
    
    # Data limits
    max_candles_warn: int = 300
    
    # Paths
    default_log_dir: Path = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    
    def __post_init__(self):
        """Ensure paths are Path objects."""
        if isinstance(self.default_log_dir, str):
            self.default_log_dir = Path(self.default_log_dir)


# Default configuration instance
DEFAULT_PLOT_CONFIG = PlotConfig()


# Constants for better code organization
class PlotConstants:
    """Constants used throughout plotting."""
    
    # Panel indices
    PRICE_PANEL = 0
    VOLUME_PANEL = 1
    
    # Time formats
    DAILY_FORMAT = '%Y-%m-%d'
    HOURLY_FORMAT = '%Y-%m-%d %H:%M'
    MINUTE_FORMAT = '%H:%M:%S'
    
    # File patterns
    SIGNALS_PATTERN = "backtest_signals_*.parquet"
    TRADES_PATTERN = "backtest_trades_*.json"
    TIMESTAMP_PATTERN = r"(\d{8}_\d{6})"
    
    # Thresholds
    MIN_LEGEND_COLUMNS = 1
    MAX_LEGEND_COLUMNS = 7
    LEGEND_ITEMS_PER_ROW = 5
    
    # Z-order for plot layers
    REGIME_ZORDER = -10
    TRADE_ZORDER = 10
    CROSSOVER_ZORDER = 5