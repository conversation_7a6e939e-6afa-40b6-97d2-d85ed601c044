"""
Data processing and validation for backtest visualization.

This module handles loading, validating, and preparing data for visualization,
ensuring compatibility between different data formats and systems.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import pandas as pd

from .config import PlotConfig, PlotConstants


logger = logging.getLogger(__name__)


class DataValidationError(Exception):
    """Raised when data validation fails."""
    pass


class BacktestDataProcessor:
    """Handles data loading, validation, and preparation for visualization."""
    
    def __init__(self, config: PlotConfig):
        """
        Initialize the data processor.
        
        Args:
            config: Plot configuration object
        """
        self.config = config
        self.required_ohlc_columns = ['open', 'high', 'low', 'close']
        self.optional_columns = ['volume', 'regime', 'tf_ewma_fast', 'tf_ewma_slow']
        
    def find_latest_backtest_files(self, log_dir: Optional[Path] = None) -> Tuple[Optional[Path], Optional[Path]]:
        """
        Find the most recent signals and trades files in the log directory.
        
        Args:
            log_dir: Directory to search for files. Uses config default if None.
            
        Returns:
            Tuple of (signals_path, trades_path) or (None, None) if not found
        """
        log_dir = log_dir or self.config.default_log_dir
        
        # Ensure log_dir is a Path object
        if isinstance(log_dir, str):
            log_dir = Path(log_dir)
        
        if not log_dir.exists():
            logger.warning(f"Log directory does not exist: {log_dir}")
            return None, None
            
        try:
            # Find all signal and trade files
            signal_files = list(log_dir.glob(PlotConstants.SIGNALS_PATTERN))
            trade_files = list(log_dir.glob(PlotConstants.TRADES_PATTERN))
            
            if not signal_files or not trade_files:
                logger.warning(f"No matching backtest files found in {log_dir}")
                return None, None
                
            # Extract timestamps and match files
            signal_map = self._extract_timestamp_map(signal_files)
            trade_map = self._extract_timestamp_map(trade_files)
            
            common_timestamps = set(signal_map.keys()) & set(trade_map.keys())
            if not common_timestamps:
                logger.warning("No matching signal/trade file pairs found")
                return None, None
                
            # Get the latest timestamp
            latest_timestamp = max(common_timestamps)
            logger.info(f"Found latest backtest run from {latest_timestamp}")
            
            return signal_map[latest_timestamp], trade_map[latest_timestamp]
            
        except Exception as e:
            logger.error(f"Error finding latest backtest files: {e}")
            return None, None
    
    def _extract_timestamp_map(self, files: List[Path]) -> Dict[str, Path]:
        """Extract timestamp to file path mapping."""
        import re
        
        timestamp_map = {}
        for file_path in files:
            match = re.search(PlotConstants.TIMESTAMP_PATTERN, file_path.name)
            if match:
                timestamp_map[match.group(1)] = file_path
        return timestamp_map
    
    def load_signals_data(self, signals_path: Path) -> pd.DataFrame:
        """
        Load and validate signals data from parquet file.
        
        Args:
            signals_path: Path to signals parquet file
            
        Returns:
            Validated DataFrame with DatetimeIndex
            
        Raises:
            DataValidationError: If required columns are missing
        """
        logger.info(f"Loading signals data from: {signals_path}")
        
        try:
            df = pd.read_parquet(signals_path)
            
            # Ensure DatetimeIndex
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)
                logger.info("Converted DataFrame index to DatetimeIndex")
            
            # Ensure timezone consistency
            if df.index.tz is None:
                df.index = df.index.tz_localize('UTC')
            else:
                df.index = df.index.tz_convert('UTC')
            
            # Validate required columns
            missing_cols = [col for col in self.required_ohlc_columns if col not in df.columns]
            if missing_cols:
                raise DataValidationError(f"Missing required OHLC columns: {missing_cols}")
            
            # Log warnings for missing optional columns
            for col in self.optional_columns:
                if col not in df.columns:
                    logger.warning(f"Optional column '{col}' not found in data")
            
            # Check for volume data
            has_volume = 'volume' in df.columns and not df['volume'].isnull().all()
            logger.info(f"Volume data available: {has_volume}")
            
            logger.info(f"Signals data loaded successfully. Shape: {df.shape}")
            return df
            
        except Exception as e:
            logger.error(f"Failed to load signals data: {e}")
            raise
    
    def load_trades_data(self, trades_path: Path) -> List[Dict]:
        """
        Load and validate trades data from JSON file.
        
        Args:
            trades_path: Path to trades JSON file
            
        Returns:
            List of trade dictionaries
        """
        logger.info(f"Loading trades data from: {trades_path}")
        
        if not trades_path.exists():
            logger.warning(f"Trades file not found: {trades_path}")
            return []
        
        try:
            with open(trades_path, 'r') as f:
                trades_list = json.load(f)
            
            # Validate trade structure
            if trades_list:
                required_keys = ['entry_time', 'exit_time', 'entry', 'exit', 'type']
                sample_trade = trades_list[0]
                missing_keys = [key for key in required_keys if key not in sample_trade]
                if missing_keys:
                    logger.warning(f"Trades may be missing expected keys: {missing_keys}")
            
            logger.info(f"Trades data loaded successfully. Count: {len(trades_list)}")
            return trades_list
            
        except Exception as e:
            logger.error(f"Failed to load trades data: {e}")
            return []
    
    def filter_by_date_range(self, df: pd.DataFrame, 
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None) -> pd.DataFrame:
        """
        Filter DataFrame by date range.
        
        Args:
            df: DataFrame with DatetimeIndex
            start_date: Start date string (YYYY-MM-DD)
            end_date: End date string (YYYY-MM-DD)
            
        Returns:
            Filtered DataFrame
        """
        if start_date:
            start_dt = pd.to_datetime(start_date).tz_localize('UTC')
            df = df[df.index >= start_dt]
            logger.info(f"Filtered data from {start_date}")
        
        if end_date:
            end_dt = pd.to_datetime(end_date).tz_localize('UTC')
            # Include the whole end day
            end_dt = end_dt + pd.Timedelta(days=1) - pd.Timedelta(seconds=1)
            df = df[df.index <= end_dt]
            logger.info(f"Filtered data until {end_date}")
        
        if df.empty:
            logger.warning("No data remaining after date filtering")
        
        return df
    
    def limit_data_points(self, df: pd.DataFrame, trades_list: List[Dict],
                         max_points: Optional[int] = None,
                         use_sampling: bool = False) -> pd.DataFrame:
        """
        Limit the number of data points for performance.
        
        Args:
            df: DataFrame to limit
            trades_list: List of trades to preserve in sampling
            max_points: Maximum number of points
            use_sampling: Whether to sample or use most recent
            
        Returns:
            Limited DataFrame
        """
        if max_points is None:
            max_points = self.config.appearance.max_points
        
        if len(df) <= max_points:
            return df
        
        logger.info(f"Limiting dataset from {len(df)} to {max_points} points")
        
        if use_sampling:
            # Sample while preserving trade points
            trade_timestamps = self._extract_trade_timestamps(df, trades_list)
            
            # Regular sampling indices
            sample_indices = set(range(0, len(df), len(df) // max_points))
            
            # Add trade timestamp indices
            for ts in trade_timestamps:
                if ts in df.index:
                    idx = df.index.get_loc(ts)
                    sample_indices.add(idx)
                else:
                    # Find nearest
                    idx = df.index.get_indexer([ts], method='nearest')[0]
                    if idx >= 0:
                        sample_indices.add(idx)
            
            # Convert to list, sort, and limit
            sample_indices = sorted(list(sample_indices))[:max_points]
            df = df.iloc[sample_indices]
            logger.info(f"Sampled data with trade points preserved")
        else:
            # Use most recent points
            df = df.iloc[-max_points:]
            logger.info(f"Using the {max_points} most recent data points")
        
        return df
    
    def _extract_trade_timestamps(self, df: pd.DataFrame, 
                                 trades_list: List[Dict]) -> set:
        """Extract unique timestamps from trades within DataFrame range."""
        trade_timestamps = set()
        
        for trade in trades_list:
            entry_time = pd.to_datetime(trade['entry_time'], unit='s', utc=True)
            exit_time = pd.to_datetime(trade['exit_time'], unit='s', utc=True)
            
            # Add if within range
            if df.index.min() <= entry_time <= df.index.max():
                trade_timestamps.add(entry_time)
            if df.index.min() <= exit_time <= df.index.max():
                trade_timestamps.add(exit_time)
        
        return trade_timestamps
    
    def prepare_indicator_data(self, df: pd.DataFrame, 
                              indicators: List[str]) -> Dict[str, pd.Series]:
        """
        Prepare indicator data for plotting.
        
        Args:
            df: DataFrame containing indicator columns
            indicators: List of indicator names to prepare
            
        Returns:
            Dictionary mapping indicator names to Series
        """
        indicator_data = {}
        
        for indicator in indicators:
            if indicator not in df.columns:
                logger.warning(f"Indicator '{indicator}' not found in data")
                continue
            
            # Check if indicator has valid data
            if df[indicator].isnull().all():
                logger.warning(f"Indicator '{indicator}' contains only NaN values")
                continue
            
            indicator_data[indicator] = df[indicator]
            logger.info(f"Prepared indicator '{indicator}' for plotting")
        
        return indicator_data
    
    def get_regime_info(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        Get regime information from DataFrame.
        
        Args:
            df: DataFrame potentially containing regime column
            
        Returns:
            Tuple of (has_regime, unique_regimes)
        """
        if 'regime' not in df.columns:
            return False, []
        
        unique_regimes = df['regime'].dropna().unique().tolist()
        logger.info(f"Found {len(unique_regimes)} unique regimes")
        
        return True, unique_regimes