"""
Backtest visualization script with enhanced features.

This script provides comprehensive visualization of backtest results including:
- Price charts with regime backgrounds
- Technical indicators and EMAs
- Trade markers and crossovers
- Equity curve and performance metrics
- Support for both legacy and modern systems
"""

# Standard library imports
import argparse
import json
import logging
import sys
from pathlib import Path
from typing import Dict, List, Optional

# Third-party imports
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D
import mplfinance as mpf
import pandas as pd
import yaml

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent.resolve()
sys.path.insert(0, str(project_root))

# Local imports - visualization package
from scripts.visualization import (
    PlotConfig,
    BacktestDataProcessor,
    PlotComponents,
    EquityCurveCalculator,
    PerformanceAnalytics
)

# Try to import regime analytics for advanced features
try:
    from hyperliquid_bot.utils.regime_analytics import RegimeAnalytics
    from hyperliquid_bot.utils.state_mapping import get_state_map
    ANALYTICS_AVAILABLE = True
    print("Successfully imported RegimeAnalytics module")
except ImportError as e:
    print(f"Warning: Could not import Phase 5 analytics modules: {e}")
    print("Regime transition visualization will not be available.")
    ANALYTICS_AVAILABLE = False


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def plot_backtest_results(
    signals_df: pd.DataFrame,
    trades_list: List[Dict],
    output_path: Path,
    title: str = "Backtest Visualization",
    plot_indicators: Optional[List[str]] = None,
    args: Optional[argparse.Namespace] = None,
    viz_config: Optional[Dict] = None,
    gms_mapping_active: bool = False,
    gms_state_map: Optional[Dict[str, str]] = None,
    map_weak_bear_to_bear: bool = False
):
    """
    Generate comprehensive backtest visualization.
    
    Args:
        signals_df: DataFrame with signals data
        trades_list: List of trade dictionaries
        output_path: Path to save the plot
        title: Plot title
        plot_indicators: List of indicators to plot
        args: Command line arguments
        viz_config: Visualization configuration
        gms_mapping_active: Whether 3-state mapping is active
        gms_state_map: State mapping dictionary
        map_weak_bear_to_bear: Weak bear mapping flag
    """
    # Initialize configuration
    config = PlotConfig()
    if viz_config:
        # Update config with values from viz_config if provided
        if 'appearance' in viz_config:
            for key, value in viz_config['appearance'].items():
                if hasattr(config.appearance, key):
                    setattr(config.appearance, key, value)
    
    # Initialize components
    plotter = PlotComponents(config)
    
    # Process arguments
    if args is None:
        # Create a namespace object for backward compatibility
        args = argparse.Namespace()
        args.no_trades = False
        args.trade_labels = False
    
    # Check if we should show equity curve
    show_equity_curve = config.show_equity_curve and trades_list
    
    # Prepare data
    plot_df = signals_df.copy()
    
    # Determine panels
    panels = []
    panel_ratios = []
    
    # Main price panel
    panels.append('price')
    panel_ratios.append(config.appearance.price_panel_ratio)
    
    # Volume panel
    has_volume = 'volume' in plot_df.columns and not plot_df['volume'].isnull().all().item()
    if has_volume and config.show_volume:
        panels.append('volume')
        panel_ratios.append(config.appearance.volume_panel_ratio)
    
    # Indicator panels
    addplots = []
    if plot_indicators:
        processor = BacktestDataProcessor(config)
        indicator_data = processor.prepare_indicator_data(plot_df, plot_indicators)
        
        for i, (indicator, data) in enumerate(indicator_data.items()):
            panel_num = len(panels)
            panels.append(indicator)
            panel_ratios.append(config.appearance.indicator_panel_ratio)
            
            # Create ylabel
            ylabel_map = {
                'obi_smoothed': 'OBI',
                'ma_slope': 'MA Slope',
                'fear_greed_idx': 'Fear/Greed'
            }
            ylabel = ylabel_map.get(indicator, indicator.replace('_', ' ').title())
            
            addplots.append(mpf.make_addplot(
                data,
                panel=panel_num,
                ylabel=ylabel
            ))
    
    # Equity curve panel
    equity_df = None
    if show_equity_curve:
        calc = EquityCurveCalculator(initial_capital=10000)
        # Ensure we have DatetimeIndex
        if not isinstance(plot_df.index, pd.DatetimeIndex):
            plot_df.index = pd.to_datetime(plot_df.index)
        equity_df = calc.calculate_equity_curve(trades_list, plot_df.index)
        
        panel_num = len(panels)
        panels.append('equity')
        panel_ratios.append(config.appearance.equity_panel_ratio)
        
        addplots.append(mpf.make_addplot(
            equity_df['equity'],
            panel=panel_num,
            ylabel='Portfolio Value',
            color='purple',
            width=2
        ))
    
    # Add EMAs to main panel
    if 'tf_ewma_fast' in plot_df.columns:
        addplots.append(mpf.make_addplot(
            plot_df['tf_ewma_fast'],
            color=config.indicator_colors.ema_fast,
            width=config.indicator_colors.line_width
        ))
    
    if 'tf_ewma_slow' in plot_df.columns:
        addplots.append(mpf.make_addplot(
            plot_df['tf_ewma_slow'],
            color=config.indicator_colors.ema_slow,
            width=config.indicator_colors.line_width
        ))
    
    # Create plot
    kwargs = {}
    if len(plot_df) > config.max_candles_warn:
        kwargs['warn_too_much_data'] = config.appearance.max_points
    
    custom_style = plotter.create_custom_style()
    
    try:
        fig, axlist = mpf.plot(
            plot_df,
            type='candle',
            style=custom_style,
            title=title,
            ylabel='Price',
            volume=has_volume and config.show_volume,
            addplot=addplots,
            panel_ratios=tuple(panel_ratios),
            figscale=config.appearance.figure_scale,
            figratio=config.appearance.figure_ratio,
            returnfig=True,
            **kwargs
        )
    except Exception as e:
        logger.error(f"Error creating plot: {e}")
        # Fallback to simple plot
        fig, axlist = mpf.plot(
            plot_df,
            type='candle',
            style='yahoo',
            title=f"{title} (simplified)",
            ylabel='Price',
            volume=False,
            figscale=config.appearance.figure_scale,
            figratio=config.appearance.figure_ratio,
            returnfig=True
        )
    
    # Get main price axes
    main_ax = axlist[0]
    
    # Remove grids
    for ax in axlist:
        if hasattr(ax, 'grid'):
            ax.grid(False)
    
    # Plot regime backgrounds
    if 'regime' in plot_df.columns:
        plotter.plot_regime_backgrounds(
            main_ax, plot_df,
            regime_column='regime',
            use_three_state=gms_mapping_active,
            state_map=gms_state_map
        )
    
    # Plot EMA crossovers
    crossover_elements = plotter.plot_ema_crossovers(main_ax, plot_df)
    
    # Plot trade markers
    trade_elements = []
    if trades_list and not args.no_trades:
        trade_elements = plotter.plot_trade_markers(
            main_ax, plot_df, trades_list,
            show_labels=args.trade_labels
        )
    
    # Color volume bars if present
    if has_volume and config.show_volume:
        volume_ax_idx = 2 if len(panels) > 1 else 1
        if volume_ax_idx < len(axlist):
            plotter.color_volume_bars(axlist[volume_ax_idx], plot_df)
    
    # Create legend
    legend_elements = []
    
    # Add regime colors
    if 'regime' in plot_df.columns:
        regime_colors = config.three_state_colors if gms_mapping_active else config.regime_colors
        unique_regimes = plot_df['regime'].dropna().unique()

        # Apply state mapping if using 3-state colors but have 7-state regime names
        if gms_mapping_active and gms_state_map:
            # Map 7-state regimes to 3-state for color lookup
            mapped_regimes = []
            for regime in unique_regimes:
                mapped_regime = gms_state_map.get(regime, regime)
                mapped_regimes.append(mapped_regime)
            unique_regimes = list(set(mapped_regimes))  # Remove duplicates

        # Sort regimes for consistent legend order: BULL, BEAR, CHOP first
        def sort_regime_key(regime):
            if regime == 'BULL': return 0
            if regime == 'BEAR': return 1
            if regime == 'CHOP': return 2
            return 3

        sorted_regimes = sorted(unique_regimes, key=lambda r: (sort_regime_key(r), r))

        for regime in sorted_regimes:
            color = regime_colors.to_dict().get(regime, regime_colors.default)

            # Convert RGBA tuple to RGB for legend display
            if isinstance(color, tuple) and len(color) == 4:
                # Use RGB values with full opacity for clear legend visibility
                color_for_legend = (color[0], color[1], color[2])
            else:
                color_for_legend = color

            # Create clean regime label
            clean_label = regime.replace('_', ' ').title()

            legend_elements.append(
                Line2D([0], [0], marker='s', color='w',
                      markerfacecolor=color_for_legend, markersize=10,
                      markeredgecolor='black', markeredgewidth=0.5,
                      label=clean_label, linestyle='None')
            )
    
    # Add indicators
    if 'tf_ewma_fast' in plot_df.columns:
        legend_elements.append(
            Line2D([0], [0], color=config.indicator_colors.ema_fast,
                   lw=1.5, label='EMA Fast')
        )
    if 'tf_ewma_slow' in plot_df.columns:
        legend_elements.append(
            Line2D([0], [0], color=config.indicator_colors.ema_slow,
                   lw=1.5, label='EMA Slow')
        )
    
    # Add crossovers and trades
    legend_elements.extend(crossover_elements)
    legend_elements.extend(trade_elements)
    
    # Create comprehensive legend
    plotter.create_comprehensive_legend(fig, legend_elements)
    
    # Add performance metrics if equity curve is shown
    if equity_df is not None:
        # Calculate metrics
        calc = EquityCurveCalculator()
        metrics = calc.calculate_portfolio_metrics(equity_df)
        
        # Add text box with key metrics
        metrics_text = (
            f"Total Return: {metrics['total_return']:.2f}%\n"
            f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}\n"
            f"Max Drawdown: {metrics['max_drawdown']:.2f}%\n"
            f"Win Rate: {metrics['win_rate']:.2f}%"
        )
        
        # Place text box adjacent to main legend with precise alignment
        # Main legend is at bbox_to_anchor=(0.5, 0.96), adjust for perfect box alignment
        fig.text(0.56, 0.94, metrics_text,
                transform=fig.transFigure,
                fontsize=9,
                verticalalignment='top',
                horizontalalignment='left',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue',
                         edgecolor='navy', alpha=0.9))
    
    # Save plot
    try:
        fig.savefig(output_path, dpi=config.appearance.dpi, bbox_inches='tight')
        plt.close(fig)
        logger.info(f"Plot saved successfully to {output_path}")
    except Exception as e:
        logger.error(f"Failed to save plot: {e}")
        plt.close(fig)


def plot_price_vs_sma_comparison(
    signals_df: pd.DataFrame,
    sma_column_name: str,
    output_path: Path,
    title: str = "Price vs. GMS Slope SMA Comparison"
):
    """Generate price vs SMA comparison plot."""
    logger.info(f"Generating Price vs SMA comparison plot: {output_path}")
    
    if 'close' not in signals_df.columns:
        logger.error("Comparison plot requires 'close' column")
        return
    
    if sma_column_name not in signals_df.columns:
        logger.warning(f"SMA column '{sma_column_name}' not found")
        return
    
    fig, ax = plt.subplots(figsize=(16, 8))
    
    # Plot data
    ax.plot(signals_df.index, signals_df['close'],
           label='Actual Price (Close)', color='blue', linewidth=1.0)
    ax.plot(signals_df.index, signals_df[sma_column_name],
           label=f'SMA ({sma_column_name})', color='orange', linewidth=1.5)
    
    # Format plot
    ax.set_title(title, fontsize=16)
    ax.set_xlabel("Time", fontsize=12)
    ax.set_ylabel("Price", fontsize=12)
    ax.legend(fontsize=10)
    ax.grid(True, linestyle='--', alpha=0.6)
    
    # Format dates
    plotter = PlotComponents(PlotConfig())
    # Ensure DatetimeIndex
    if not isinstance(signals_df.index, pd.DatetimeIndex):
        signals_df.index = pd.to_datetime(signals_df.index)
    plotter.format_time_axis(ax, signals_df.index)
    
    fig.tight_layout()
    
    # Save plot
    try:
        fig.savefig(output_path, dpi=200, bbox_inches='tight')
        plt.close(fig)
        logger.info("Comparison plot saved successfully")
    except Exception as e:
        logger.error(f"Failed to save comparison plot: {e}")
        plt.close(fig)


def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Visualize Backtest Results")
    
    # File paths
    parser.add_argument("--signals", default=None, help="Path to signals parquet file")
    parser.add_argument("--trades", default=None, help="Path to trades JSON file")
    parser.add_argument("--output", default=None, help="Path to save the output plot")
    parser.add_argument("--comparison-output", default=None, help="Path for comparison plot")
    
    # Date range
    parser.add_argument("--start", default=None, help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end", default=None, help="End date (YYYY-MM-DD)")
    
    # Plot options
    parser.add_argument("--title", default="Backtest Visualization", help="Plot title")
    parser.add_argument("--indicators", nargs='*', default=None,
                       help="[DEPRECATED] Use config.yaml instead")
    parser.add_argument("--no-trades", action="store_true", help="Skip trade markers")
    parser.add_argument("--trade-labels", action="store_true", help="Add trade labels")
    parser.add_argument("--sma-col", default="sma_gms_slope", help="SMA column name")
    
    # Data options
    parser.add_argument("--auto", action="store_true", help="Auto-detect latest files")
    parser.add_argument("--max-points", type=int, default=None, help="Maximum data points")
    parser.add_argument("--sample", action="store_true", help="Sample data points")
    
    # Advanced features
    parser.add_argument("--regime-transitions", action="store_true",
                       help="Generate regime transition visualization")
    parser.add_argument("--detailed-analytics", action="store_true",
                       help="Generate detailed analytics report")
    
    # Configuration
    parser.add_argument("--config", default=None, help="Path to config YAML file")
    parser.add_argument("--logdir", default=None, help="Log directory path")
    
    args = parser.parse_args()
    
    # Load configuration
    config_path = Path(args.config) if args.config else project_root / "configs" / "base.yaml"
    config_dict = {}
    
    if config_path.exists():
        try:
            with open(config_path, 'r') as f:
                config_dict = yaml.safe_load(f) or {}
            logger.info(f"Loaded config from {config_path}")
        except Exception as e:
            logger.error(f"Error loading config: {e}")
    
    # Initialize data processor
    plot_config = PlotConfig()
    if args.logdir:
        plot_config.default_log_dir = Path(args.logdir)
    elif 'data_paths' in config_dict and 'log_dir' in config_dict['data_paths']:
        plot_config.default_log_dir = Path(config_dict['data_paths']['log_dir'])
    
    processor = BacktestDataProcessor(plot_config)
    
    # Find or use specified files
    if args.auto or (not args.signals and not args.trades):
        signals_path, trades_path = processor.find_latest_backtest_files()
        if not signals_path:
            logger.error("Could not find backtest files")
            return
    else:
        signals_path = Path(args.signals) if args.signals else None
        trades_path = Path(args.trades) if args.trades else None
    
    # Ensure we have signals_path
    if not signals_path:
        logger.error("No signals file specified or found")
        return
    
    # Set output paths
    if not args.output:
        output_path = signals_path.with_name(f"{signals_path.stem}_visualization.png")
    else:
        output_path = Path(args.output)
    
    if not args.comparison_output:
        comparison_output = signals_path.with_name(f"{signals_path.stem}_comparison.png")
    else:
        comparison_output = Path(args.comparison_output)
    
    # Load data
    try:
        signals_df = processor.load_signals_data(signals_path)
        trades_list = processor.load_trades_data(trades_path) if trades_path else []
    except Exception as e:
        logger.error(f"Error loading data: {e}")
        return
    
    # Filter by date range
    signals_df = processor.filter_by_date_range(signals_df, args.start, args.end)
    if signals_df.empty:
        logger.error("No data after filtering")
        return
    
    # Limit data points
    signals_df = processor.limit_data_points(
        signals_df, trades_list,
        max_points=args.max_points,
        use_sampling=args.sample
    )
    
    # Determine indicators from config
    indicators_to_plot = []
    viz_config = config_dict.get('visualization', {})
    if viz_config.get('panels', {}).get('show_ma_slope', False):
        indicators_to_plot.append('ma_slope')
    if viz_config.get('panels', {}).get('show_obi', False):
        indicators_to_plot.append('obi_smoothed')
    if viz_config.get('panels', {}).get('show_fear_greed', False):
        indicators_to_plot.append('fear_greed_idx')
    
    # Check for GMS mapping
    gms_mapping_active = config_dict.get('regime', {}).get('gms_use_three_state_mapping', False)
    map_weak_bear_to_bear = config_dict.get('regime', {}).get('map_weak_bear_to_bear', False)
    gms_state_map = None
    
    if gms_mapping_active and ANALYTICS_AVAILABLE:
        try:
            gms_state_map = get_state_map(map_weak_bear_to_bear=map_weak_bear_to_bear)
            logger.info("Loaded GMS state map")
        except Exception as e:
            logger.warning(f"Could not load state map: {e}")
    
    # Generate main plot
    plot_backtest_results(
        signals_df=signals_df,
        trades_list=trades_list,
        output_path=output_path,
        title=args.title,
        plot_indicators=indicators_to_plot,
        args=args,
        viz_config=viz_config,
        gms_mapping_active=gms_mapping_active,
        gms_state_map=gms_state_map,
        map_weak_bear_to_bear=map_weak_bear_to_bear
    )
    
    # Generate comparison plot
    plot_price_vs_sma_comparison(
        signals_df=signals_df,
        sma_column_name=args.sma_col,
        output_path=comparison_output,
        title=f"Price vs SMA ({args.sma_col}) Comparison"
    )
    
    # Generate analytics if requested
    if trades_list and (args.regime_transitions or args.detailed_analytics):
        analytics = PerformanceAnalytics()
        
        # Generate trade distribution plot
        dist_path = signals_path.with_name(f"{signals_path.stem}_trade_distribution.png")
        analytics.plot_trade_distribution(trades_list, save_path=str(dist_path))
        logger.info(f"Trade distribution saved to {dist_path}")
        
        # Generate performance report
        if args.detailed_analytics:
            calc = EquityCurveCalculator()
            # Ensure DatetimeIndex
            if not isinstance(signals_df.index, pd.DatetimeIndex):
                signals_df.index = pd.to_datetime(signals_df.index)
            equity_df = calc.calculate_equity_curve(trades_list, signals_df.index)
            
            report = analytics.generate_performance_report(
                equity_df, trades_list, signals_df
            )
            
            report_path = signals_path.with_name(f"{signals_path.stem}_performance_report.json")
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            logger.info(f"Performance report saved to {report_path}")
    
    # Generate regime transitions if available
    if args.regime_transitions and ANALYTICS_AVAILABLE and 'regime' in signals_df.columns:
        try:
            transitions_path = signals_path.with_name(f"{signals_path.stem}_regime_transitions.png")
            regime_analytics = RegimeAnalytics(
                signals_df,
                output_dir=output_path.parent,
                map_weak_bear_to_bear=map_weak_bear_to_bear
            )
            regime_analytics.generate_transition_timeline(str(transitions_path))
            logger.info(f"Regime transitions saved to {transitions_path}")
        except Exception as e:
            logger.error(f"Error generating regime transitions: {e}")
    
    logger.info("Visualization complete!")


if __name__ == "__main__":
    main()