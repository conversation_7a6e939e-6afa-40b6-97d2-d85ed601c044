#!/usr/bin/env python3
"""
Test script to verify threshold scaling fix.
"""

import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.core.detector_factory import get_regime_detector


def test_threshold_scaling():
    """Test that corrected thresholds work with decimal ATR values."""
    print("Testing Threshold Scaling Fix")
    print("="*60)
    
    # Load config
    config = load_config("configs/base.yaml")
    
    # Create detector (force legacy mode)
    config.regime.detector_type = 'granular_microstructure'
    
    # Remove any conflicting settings in gms section that might override
    if hasattr(config, 'gms'):
        if hasattr(config.gms, 'detector_type'):
            del config.gms.detector_type
        if hasattr(config.gms, 'mode'):
            del config.gms.mode
    
    detector = get_regime_detector(config)
    
    print(f"Created detector: {type(detector).__name__}")
    print(f"Mode: {getattr(detector, 'detector_mode', 'N/A')}")
    
    # Check threshold values
    thresholds = detector.thresholds
    print(f"\nCorrected thresholds:")
    print(f"  vol_high: {thresholds['vol_high']} (should be ~0.0092)")
    print(f"  vol_low: {thresholds['vol_low']} (should be ~0.0055)")
    print(f"  mom_strong: {thresholds['mom_strong']} (should be 100.0)")
    print(f"  mom_weak: {thresholds['mom_weak']} (should be 50.0)")
    
    # Test cases with realistic decimal ATR values
    test_cases = [
        {
            'name': 'Low Volatility (0.4%)',
            'atr_percent': 0.004,  # 0.4% - should be "Low" 
            'expected_vol': 'Low',
            'ma_slope': 25.0  # Weak momentum
        },
        {
            'name': 'Medium Volatility (0.7%)', 
            'atr_percent': 0.007,  # 0.7% - should be "Medium"
            'expected_vol': 'Medium',
            'ma_slope': 75.0  # Medium momentum
        },
        {
            'name': 'High Volatility (1.0%)',
            'atr_percent': 0.010,  # 1.0% - should be "High"
            'expected_vol': 'High', 
            'ma_slope': 150.0  # Strong momentum
        },
        {
            'name': 'Baseline Case (1.5%)',
            'atr_percent': 0.015,  # 1.5% - should be "High"
            'expected_vol': 'High',
            'ma_slope': 200.0  # Very strong momentum
        }
    ]
    
    print(f"\nThreshold boundaries:")
    print(f"  Low: ATR <= {thresholds['vol_low']:.4f} ({thresholds['vol_low']*100:.2f}%)")
    print(f"  Medium: {thresholds['vol_low']:.4f} < ATR < {thresholds['vol_high']:.4f}")  
    print(f"  High: ATR >= {thresholds['vol_high']:.4f} ({thresholds['vol_high']*100:.2f}%)")
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases):
        print(f"\n--- Test Case {i+1}: {test_case['name']} ---")
        
        # Create test signals
        signals = {
            'timestamp': 1640995200,
            'atr_percent': test_case['atr_percent'],
            'ma_slope': test_case['ma_slope'],
            'obi_smoothed_5': 0.15,
            'spread_mean': 0.0001,
            'spread_std': 0.00005,
            'close': 50000.0
        }
        
        print(f"ATR: {test_case['atr_percent']:.4f} ({test_case['atr_percent']*100:.2f}%)")
        print(f"Expected volatility classification: {test_case['expected_vol']}")
        
        try:
            regime = detector.get_regime(signals)
            print(f"Detected regime: {regime}")
            
            # Determine actual volatility classification from regime
            if 'High_Vol' in str(regime):
                actual_vol = 'High'
            elif 'Low_Vol' in str(regime):
                actual_vol = 'Low'
            elif any(trend in str(regime) for trend in ['Strong_Bull', 'Strong_Bear']):
                # Strong trend states indicate HIGH volatility + strong momentum
                actual_vol = 'High'
            elif any(trend in str(regime) for trend in ['Weak_Bull', 'Weak_Bear']):
                # Weak trend states indicate medium volatility 
                actual_vol = 'Medium'
            else:
                actual_vol = 'Unknown'
            
            print(f"Inferred volatility classification: {actual_vol}")
            
            if actual_vol == test_case['expected_vol']:
                print(f"✅ Correct volatility classification!")
                success_count += 1
            else:
                print(f"❌ Wrong volatility classification (expected {test_case['expected_vol']})")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print(f"\n" + "="*60)
    print(f"Threshold scaling test results: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
    
    if success_count >= len(test_cases) * 0.75:  # 75% success rate
        print("✅ Threshold scaling appears to be working correctly!")
        print("\nExpected improvements:")
        print("- More selective trade filtering (fewer trades)")
        print("- Better trade quality (higher ROI)")
        print("- Trade count closer to 184 baseline")
        print("- Improved risk-adjusted returns")
    else:
        print("❌ Threshold scaling needs further adjustment")
        print("\nConsider:")
        print("- Fine-tuning threshold values based on historical data")
        print("- Checking momentum threshold scaling")
        print("- Verifying ATR calculation is truly correct")
    
    return success_count >= len(test_cases) * 0.75


if __name__ == "__main__":
    test_threshold_scaling()