# File: scripts/analyze_mcp_correlation.py

import pandas as pd
import json
import argparse
import logging
from pathlib import Path
from sklearn.metrics import roc_auc_score
from typing import Optional, Tuple
import glob
import os
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__) # Use logger instance

# --- Helper Function (Copied from visualize_backtest.py) ---

def find_latest_backtest_files(log_dir: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Finds the most recent signals and trades files in the log directory.
    Returns (latest_signals_path, latest_trades_path)
    """
    try:
        # Get all signal and trade files
        signal_files = glob.glob(os.path.join(log_dir, "backtest_signals_*.parquet"))
        trade_files = glob.glob(os.path.join(log_dir, "backtest_trades_*.json"))

        if not signal_files or not trade_files:
            logger.warning(f"No matching backtest files found in {log_dir}")
            return None, None

        # Extract timestamps and match files
        timestamp_pattern = re.compile(r'(\d{8}_\d{6})')

        # Get timestamps from filenames
        signal_timestamps = {timestamp_pattern.search(f).group(1): f for f in signal_files if timestamp_pattern.search(f)}
        trade_timestamps = {timestamp_pattern.search(f).group(1): f for f in trade_files if timestamp_pattern.search(f)}

        # Find common timestamps (backtest runs with both signal and trade files)
        common_timestamps = set(signal_timestamps.keys()).intersection(set(trade_timestamps.keys()))

        if not common_timestamps:
            logger.warning("No matching signal/trade file pairs found")
            return None, None

        # Get the latest timestamp
        latest_timestamp = max(common_timestamps)
        latest_signals = signal_timestamps[latest_timestamp]
        latest_trades = trade_timestamps[latest_timestamp]

        logger.info(f"Found latest backtest run from {latest_timestamp}")
        return latest_signals, latest_trades

    except Exception as e:
        logger.error(f"Error finding latest backtest files: {e}")
        return None, None

# --- Core Analysis Function ---

def analyze_correlation(signals_file: Path, trades_file: Path, feature_column: str = 'fear_greed_idx'):
    """
    Analyzes correlation and AUROC of a feature column with trade performance.
    """
    logger.info(f"Loading signals data from: {signals_file}")
    signals_df = pd.read_parquet(signals_file)
    # Ensure index is DatetimeIndex and UTC for proper merging
    signals_df.index = pd.to_datetime(signals_df.index, utc=True)
    signals_df = signals_df.sort_index()

    if feature_column not in signals_df.columns:
        logger.error(f"Feature column '{feature_column}' not found in signals data. Aborting.")
        return

    logger.info(f"Loading trades data from: {trades_file}")
    try:
        with open(trades_file, 'r') as f:
            trades_list = json.load(f)
    except json.JSONDecodeError:
        logger.error(f"Failed to decode JSON from {trades_file}. Is the file valid?")
        return
    except FileNotFoundError:
        logger.error(f"Trades file not found: {trades_file}")
        return


    if not trades_list:
        logger.warning("No trades found in the JSON file. Cannot perform analysis.")
        return

    trades_df = pd.DataFrame(trades_list)

    # --- Data Type Conversion and Filtering ---
    # Parse entry_time and exit_time as UNIX seconds
    trades_df['entry_time'] = pd.to_datetime(trades_df['entry_time'], unit='s', utc=True)
    if 'exit_time' in trades_df.columns:
        trades_df['exit_time'] = pd.to_datetime(trades_df['exit_time'], unit='s', utc=True)
    # Use 'profit' column for numeric conversion
    trades_df['profit'] = pd.to_numeric(trades_df['profit'], errors='coerce')
    # Only drop based on 'profit'
    trades_df.dropna(subset=['profit'], inplace=True)

    if trades_df.empty:
        logger.warning("No valid trades remain after filtering. Cannot perform analysis.")
        return

    trades_df = trades_df.sort_values('entry_time')

    # --- Feature Engineering (Lags) ---
    # Lag 0: Use the feature value at the time of entry (or just before)
    # Lag +1h (Feature value 1 hour BEFORE entry):
    # We need the signal from the *previous* candle relative to the trade entry.
    signals_df[f'{feature_column}_lag1h'] = signals_df[feature_column].shift(1) # Assumes 1h frequency data


    # --- Merge Trades with Signals ---
    # Use merge_asof to get the signal value from the row corresponding to,
    # or immediately preceding, the trade entry time.
    analysis_df = pd.merge_asof(
        trades_df.sort_values('entry_time'),
        signals_df[[feature_column, f'{feature_column}_lag1h']].sort_index(),
        left_on='entry_time',
        right_index=True,
        direction='backward', # Find signal row at or before trade entry
        tolerance=pd.Timedelta('5 minutes') # Allow small tolerance for exact timestamp match issues
    )

    # --- Handle Potential Missing Feature Values After Merge ---
    missing_count = analysis_df[feature_column].isna().sum()
    missing_lag_count = analysis_df[f'{feature_column}_lag1h'].isna().sum()
    if missing_count > 0:
        logger.warning(f"Could not find matching '{feature_column}' for {missing_count} trades after merge_asof. Check data alignment/timestamps.")
    if missing_lag_count > 0:
         logger.warning(f"Could not find matching '{feature_column}_lag1h' for {missing_lag_count} trades after merge_asof.")

    # Drop rows where the merge failed to find the feature value
    analysis_df.dropna(subset=[feature_column, f'{feature_column}_lag1h'], inplace=True)

    if analysis_df.empty:
        logger.warning("No trades remain after merging with signals and dropping NaNs. Cannot perform analysis.")
        return

    # --- Calculate Metrics ---
    # Create binary success label (1 if profitable, 0 otherwise)
    analysis_df['is_profitable'] = (analysis_df['profit'] > 0).astype(int)

    results = {}

    for lag_suffix in ['', '_lag1h']:
        current_feature = f"{feature_column}{lag_suffix}"
        lag_label = "Lag 0h" if lag_suffix == '' else "Lag +1h" # +1h means feature from 1h prior

        logger.info(f"\n--- Analyzing {current_feature} ({lag_label}) ---")

        # Correlation with PnL (Pearson)
        try:
            corr_profit = analysis_df[current_feature].corr(analysis_df['profit'], method='pearson')
            logger.info(f"Pearson Correlation ({current_feature} vs profit): {corr_profit:.4f}")
        except Exception as e:
            logger.error(f"Could not calculate Pearson correlation for {current_feature}: {e}")
            corr_profit = pd.NA


        # AUROC (Area Under the ROC Curve)
        # Requires at least one sample from each class (profitable/unprofitable)
        if len(analysis_df['is_profitable'].unique()) > 1:
            try:
                auroc = roc_auc_score(analysis_df['is_profitable'], analysis_df[current_feature])
                logger.info(f"AUROC ({current_feature} vs is_profitable): {auroc:.4f}")
            except Exception as e:
                logger.error(f"Could not calculate AUROC for {current_feature}: {e}")
                auroc = pd.NA
        else:
            logger.warning(f"Skipping AUROC for {current_feature}: Only one class present in 'is_profitable'.")
            auroc = pd.NA

        results[lag_label] = {'corr_profit': corr_profit, 'auroc': auroc}

    # --- Check Promotion Rule ---
    logger.info("\n--- Promotion Rule Check ---")
    promoted = False
    for lag_label, metrics in results.items():
        corr = metrics.get('corr_profit', 0)
        auroc = metrics.get('auroc', 0)

        if pd.isna(corr) or pd.isna(auroc):
            logger.warning(f"Cannot check promotion rule for {lag_label} due to missing metrics.")
            continue

        logger.info(f"Metrics for {lag_label}: Correlation={abs(corr):.4f}, AUROC={auroc:.4f}")
        if abs(corr) > 0.20 and auroc > 0.55:
            logger.info(f"PASSED Promotion Rule ({lag_label}): |Corr| > 0.20 AND AUROC > 0.55")
            promoted = True
        else:
            logger.info(f"FAILED Promotion Rule ({lag_label})")

    if promoted:
        logger.info(f"'{feature_column}' shows potential based on promotion rule for at least one lag.")
    else:
        logger.info(f"'{feature_column}' does not meet the promotion criteria for analysed lags.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Analyze correlation of features with trade performance.")
    parser.add_argument("--signals", default=None, help="Path to the backtest signals parquet file.") # Made optional
    parser.add_argument("--trades", default=None, help="Path to the backtest trades JSON file.")   # Made optional
    parser.add_argument("--feature", default="fear_greed_idx", help="Name of the feature column in signals_df to analyze.")
    parser.add_argument("--auto", action="store_true", help="Automatically use the latest backtest files from the logs directory.") # Added auto flag
    args = parser.parse_args()

    # Determine log directory - use project root logs directory (NOT inside hyperliquid_bot)
    log_dir = "/Users/<USER>/Desktop/trading_bot_/logs"

    # Auto-detect latest files if --auto is specified or no explicit paths are provided
    auto_detect = args.auto or (args.signals is None and args.trades is None)
    if auto_detect:
        logger.info(f"Auto-detecting latest backtest files in {log_dir}")
        latest_signals, latest_trades = find_latest_backtest_files(log_dir)

        if latest_signals and latest_trades:
            if args.signals is None:
                args.signals = latest_signals
                logger.info(f"Using auto-detected signals file: {args.signals}")
            if args.trades is None:
                args.trades = latest_trades
                logger.info(f"Using auto-detected trades file: {args.trades}")
        else:
            # If auto-detect failed and paths weren't provided, we can't proceed
            if args.signals is None or args.trades is None:
                 logger.error("Could not auto-detect latest backtest files and paths were not specified manually. Aborting.")
                 exit(1) # Use exit(1) for error exit

    # --- Final Check for Required Arguments ---
    if args.signals is None or args.trades is None:
        logger.error("Required arguments missing. Specify --signals and --trades, or use --auto.")
        exit(1)

    signals_path = Path(args.signals)
    trades_path = Path(args.trades)

    if not signals_path.is_file():
        logger.error(f"Signals file not found: {signals_path}")
    elif not trades_path.is_file():
        logger.error(f"Trades file not found: {trades_path}")
    else:
        analyze_correlation(signals_path, trades_path, args.feature)