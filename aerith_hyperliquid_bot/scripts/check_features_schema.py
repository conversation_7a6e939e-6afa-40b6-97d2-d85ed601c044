#!/usr/bin/env python3
"""Quick script to check features_1s schema"""
import pandas as pd
import sys

# Load a sample file
df = pd.read_parquet(sys.argv[1])

print(f"Shape: {df.shape}")
print("\nColumns:")
for col in sorted(df.columns):
    print(f"  - {col}")

# Check for critical columns
print("\nChecking for critical columns:")
critical = ['timestamp', 'close', 'high', 'low', 'volume', 'imbalance', 'volume_imbalance']
for col in critical:
    if col in df.columns:
        print(f"  ✓ {col}")
    else:
        print(f"  ✗ {col}")