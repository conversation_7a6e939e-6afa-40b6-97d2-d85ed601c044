#!/usr/bin/env python3
"""
Debug GMS Trend Detection
Look for samples with high momentum to understand why trends aren't detected.
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path

def find_trend_samples():
    """Find samples with high momentum that should be detected as trends."""
    # Load signals
    import glob
    files = glob.glob('/Users/<USER>/Desktop/trading_bot_/logs/backtest_signals_*.parquet')
    if not files:
        print("No signals files found")
        return
    
    latest_file = max(files)
    print(f"Loading {latest_file}")
    df = pd.read_parquet(latest_file)
    
    # Manual thresholds
    vol_thresh_low = 0.005
    vol_thresh_high = 0.015
    mom_thresh_weak = 5.0
    mom_thresh_strong = 15.0
    
    print(f"Total rows: {len(df)}")
    
    # Find samples with high momentum
    high_momentum = df[df['ma_slope'].abs() >= mom_thresh_strong].copy()
    print(f"High momentum samples (|slope| >= {mom_thresh_strong}): {len(high_momentum)}")
    
    if len(high_momentum) == 0:
        print("No high momentum samples found!")
        # Show momentum distribution
        print("\nMomentum distribution:")
        print(df['ma_slope'].abs().describe())
        
        # Find the highest momentum samples
        highest_momentum = df.nlargest(10, df['ma_slope'].abs())
        print(f"\nTop 10 highest momentum samples:")
        for idx, row in highest_momentum.iterrows():
            print(f"  {row['timestamp']}: |slope|={abs(row['ma_slope']):.2f}")
        return
    
    # Test a few high momentum samples
    test_samples = high_momentum.head(10)
    
    print(f"\nTesting {len(test_samples)} high momentum samples...")
    
    for idx, row in test_samples.iterrows():
        print(f"\n--- High Momentum Sample: {row['timestamp']} ---")
        
        # Extract signals
        atr_pct = row.get('atr_percent', np.nan)
        ma_slope = row.get('ma_slope', np.nan)
        obi_5 = row.get('obi_smoothed_5', np.nan)
        spread_mean = row.get('spread_mean', np.nan)
        spread_std = row.get('spread_std', np.nan)
        
        print(f"Signals:")
        print(f"  atr_percent: {atr_pct:.6f}")
        print(f"  ma_slope: {ma_slope:.3f} (|slope|={abs(ma_slope):.3f})")
        print(f"  obi_smoothed_5: {obi_5:.6f}")
        print(f"  spread_mean: {spread_mean:.6f}")
        print(f"  spread_std: {spread_std:.6f}")
        
        # Check for missing data
        missing = []
        if pd.isna(atr_pct): missing.append('atr_percent')
        if pd.isna(ma_slope): missing.append('ma_slope')
        if pd.isna(obi_5): missing.append('obi_smoothed_5')
        if pd.isna(spread_mean): missing.append('spread_mean')
        if pd.isna(spread_std): missing.append('spread_std')
        
        if missing:
            print(f"Missing: {missing}")
            manual_state = "Unknown"
        else:
            # Manual classification
            if atr_pct >= vol_thresh_high:
                vol_regime = "High"
            elif atr_pct <= vol_thresh_low:
                vol_regime = "Low"
            else:
                vol_regime = "Medium"
            
            abs_ma_slope = abs(ma_slope)
            if abs_ma_slope >= mom_thresh_strong:
                mom_regime = "Strong"
            elif abs_ma_slope <= mom_thresh_weak:
                mom_regime = "Weak"
            else:
                mom_regime = "Medium"
            
            direction = "Bull" if ma_slope > 0 else "Bear"
            obi_confirms = (obi_5 > 0 and direction == "Bull") or (obi_5 < 0 and direction == "Bear")
            
            print(f"Classifications:")
            print(f"  vol_regime: {vol_regime} (threshold: low≤{vol_thresh_low}, high≥{vol_thresh_high})")
            print(f"  mom_regime: {mom_regime} (threshold: weak≤{mom_thresh_weak}, strong≥{mom_thresh_strong})")
            print(f"  direction: {direction}")
            print(f"  obi_confirms: {obi_confirms}")
            
            # Apply GMS logic
            if vol_regime == "High":
                if mom_regime == "Strong" and obi_confirms:
                    manual_state = f"Strong_{direction}_Trend"
                else:
                    manual_state = "High_Vol_Range"
            elif vol_regime == "Low":
                if mom_regime == "Weak":
                    manual_state = "Low_Vol_Range"
                else:
                    manual_state = f"Weak_{direction}_Trend"
            else:  # Medium
                if mom_regime == "Strong" and obi_confirms:
                    manual_state = f"Strong_{direction}_Trend"
                elif mom_regime == "Medium":
                    manual_state = f"Weak_{direction}_Trend"
                else:
                    manual_state = "Uncertain"
        
        print(f"Manual result: {manual_state}")
        
        # Check what the actual regime column shows (from the backtest)
        actual_regime = row.get('regime', 'N/A')
        print(f"Actual detector result: {actual_regime}")
        
        # Show discrepancy
        if str(actual_regime) != str(manual_state):
            print(f"*** DISCREPANCY: Manual={manual_state}, Actual={actual_regime} ***")
    
    # Summary of trend detection in high momentum samples
    if len(high_momentum) > 0:
        print(f"\n=== HIGH MOMENTUM ANALYSIS ===")
        print(f"Total high momentum samples: {len(high_momentum)}")
        
        # Manual classification of all high momentum samples
        manual_trends = 0
        for _, row in high_momentum.iterrows():
            atr_pct = row.get('atr_percent', np.nan)
            ma_slope = row.get('ma_slope', np.nan)
            obi_5 = row.get('obi_smoothed_5', np.nan)
            
            if not any(pd.isna(val) for val in [atr_pct, ma_slope, obi_5]):
                direction = "Bull" if ma_slope > 0 else "Bear"
                obi_confirms = (obi_5 > 0 and direction == "Bull") or (obi_5 < 0 and direction == "Bear")
                
                # Strong momentum + OBI confirmation = trend
                if obi_confirms:
                    manual_trends += 1
        
        print(f"Manual trend detection: {manual_trends}/{len(high_momentum)} ({manual_trends/len(high_momentum)*100:.1f}%)")
        
        # Check actual detector results
        actual_regimes = high_momentum['regime'].value_counts()
        print(f"Actual detector results in high momentum samples:")
        for regime, count in actual_regimes.items():
            print(f"  {regime}: {count} ({count/len(high_momentum)*100:.1f}%)")

if __name__ == "__main__":
    find_trend_samples()