# OBI Scalper & GMS/TF Integration PRD

## Success Criteria
- Sharpe ≥ 1.2 and Profit Factor ≥ 1.3 over 2024 IS; Sharpe ≥ 1 OOS 2023 after taker fee & 500ms latency sim.
- Legacy GMS + TF baseline metrics do not degrade by more than 1%.
- Unit-test suite passes (> 95% coverage for new modules).
- Live paper-trade (test-net) slippage ≤ 2× simulated average.

## Task Breakdown

### T-101: Feature Layer – multi-depth OBI
- Implement calc_obi() function in microstructure.py
- Add unit tests in test_microstructure.py
- Support arbitrary depth ranges and custom weights
- Equation: OBI = (Σ w_i·bid_i − Σ w_i·ask_i) / (Σ w_i·bid_i + Σ w_i·ask_i)

### T-102: Signals – smoothing & z-score
- Implement SignalEngine outputs for obi_smoothed_{depth} and obi_z_{depth}
- Ensure no performance regression in tests
- Document new config keys and default windows

### T-103: Config plumbing
- Update settings.py and base.yaml to accept new OBI parameters:
  - obi_depth_variants
  - obi_weight_scheme
- Document all new YAML configuration options

### T-104: OBIScalperStrategy skeleton
- Create new OBIScalperStrategy class in strategies/evaluator.py
- Implement evaluate() method with diagnostic logging
- Ensure it passes linter and unit tests

### T-105: Risk integration
- Implement position sizing for scalper strategy
  - Fixed-fraction or 1-min ATR based
- Add unit tests for position sizing within max_leverage limits
- Document risk parameters

### T-106: StrategyEvaluator wiring
- Update get_active_strategies() to activate scalper based on flags
- Add comments explaining regime logic
- Ensure proper integration with existing strategy selection

### T-107: 1-min Back-test & KPI report
- Run back-tester for 2023-24 period
- Generate CSV of KPIs
- Create change-log entry with metrics table
