#!/usr/bin/env python3
"""
Quick A/B Test - Q1 2024 Only
==============================

Run quick backtests for Q1 2024 to verify quality filtering effectiveness.
"""

import sys
import logging
from pathlib import Path
from datetime import datetime
import json
import time

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.robust_backtest_engine import RobustBacktestEngine


def setup_logging(test_name):
    """Configure logging to file and console."""
    log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    log_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = log_dir / f"quick_ab_{test_name}_{timestamp}.log"
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return log_file


def run_legacy_test():
    """Run legacy detector test for Q1 2024."""
    print("\n" + "="*60)
    print("Quick A/B Test: Legacy Detector (Q1 2024)")
    print("="*60)
    
    # Setup logging
    log_file = setup_logging("legacy")
    logger = logging.getLogger("QuickABLegacy")
    
    # Load configuration
    config = load_config("configs/overrides/modern_system_v2_complete.yaml")
    config.regime.detector_type = "granular_microstructure"
    
    # Q1 2024 dates
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 3, 31, 23, 59, 59)
    
    # Create and run backtest
    print("\nRunning legacy detector backtest for Q1 2024...")
    start_time = time.time()
    
    engine = RobustBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=True
    )
    
    results = engine.run_backtest()
    
    # Save results
    output_file = f"quick_ab_legacy_q1_2024.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    elapsed = time.time() - start_time
    print(f"\nCompleted in {elapsed:.1f}s")
    print(f"Results saved to: {output_file}")
    
    return results


def run_enhanced_test():
    """Run enhanced detector test for Q1 2024."""
    print("\n" + "="*60)
    print("Quick A/B Test: Enhanced Detector (Q1 2024)")
    print("="*60)
    
    # Setup logging
    log_file = setup_logging("enhanced")
    logger = logging.getLogger("QuickABEnhanced")
    
    # Load configuration
    config = load_config("configs/overrides/modern_system_v2_complete.yaml")
    config.regime.detector_type = "enhanced"
    
    # Configure enhanced detector settings
    enhanced_settings = {
        'quality_threshold': 0.7,
        'spread_score_weight': 0.4,
        'momentum_score_weight': 0.4,
        'volume_score_weight': 0.2
    }
    
    if not hasattr(config.regime, 'detector_settings'):
        config.regime.detector_settings = {}
    config.regime.detector_settings['enhanced'] = enhanced_settings
    
    # Q1 2024 dates
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 3, 31, 23, 59, 59)
    
    # Create and run backtest
    print("\nRunning enhanced detector backtest for Q1 2024...")
    start_time = time.time()
    
    engine = RobustBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=True
    )
    
    results = engine.run_backtest()
    
    # Save results
    output_file = f"quick_ab_enhanced_q1_2024.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    elapsed = time.time() - start_time
    print(f"\nCompleted in {elapsed:.1f}s")
    print(f"Results saved to: {output_file}")
    
    return results


def analyze_quality_filtering(legacy_results, enhanced_results):
    """Analyze which types of trades were filtered out."""
    print("\n" + "="*80)
    print("Quality Filtering Analysis - Focus on BAD Trade Reduction")
    print("="*80)
    
    # Extract trade lists
    legacy_trades = legacy_results.get('trades', [])
    enhanced_trades = enhanced_results.get('trades', [])
    
    print(f"\nTrade Count:")
    print(f"Legacy:   {len(legacy_trades)} trades")
    print(f"Enhanced: {len(enhanced_trades)} trades")
    print(f"Filtered: {len(legacy_trades) - len(enhanced_trades)} trades ({(1 - len(enhanced_trades)/len(legacy_trades))*100:.1f}%)")
    
    # Analyze trade quality
    if legacy_trades:
        # Legacy trade analysis
        legacy_winners = [t for t in legacy_trades if t.get('pnl_pct', 0) > 0]
        legacy_losers = [t for t in legacy_trades if t.get('pnl_pct', 0) <= 0]
        legacy_big_losers = [t for t in legacy_trades if t.get('pnl_pct', 0) < -0.01]  # < -1%
        
        print(f"\nLegacy Trade Breakdown:")
        print(f"Winners: {len(legacy_winners)} ({len(legacy_winners)/len(legacy_trades)*100:.1f}%)")
        print(f"Losers:  {len(legacy_losers)} ({len(legacy_losers)/len(legacy_trades)*100:.1f}%)")
        print(f"Big Losers (< -1%): {len(legacy_big_losers)}")
        
    if enhanced_trades:
        # Enhanced trade analysis
        enhanced_winners = [t for t in enhanced_trades if t.get('pnl_pct', 0) > 0]
        enhanced_losers = [t for t in enhanced_trades if t.get('pnl_pct', 0) <= 0]
        enhanced_big_losers = [t for t in enhanced_trades if t.get('pnl_pct', 0) < -0.01]
        
        print(f"\nEnhanced Trade Breakdown:")
        print(f"Winners: {len(enhanced_winners)} ({len(enhanced_winners)/len(enhanced_trades)*100:.1f}%)")
        print(f"Losers:  {len(enhanced_losers)} ({len(enhanced_losers)/len(enhanced_trades)*100:.1f}%)")
        print(f"Big Losers (< -1%): {len(enhanced_big_losers)}")
    
    # Analyze what was filtered out
    if legacy_trades and enhanced_trades:
        # Create sets of trade timestamps for comparison
        enhanced_times = {t['entry_time'] for t in enhanced_trades}
        filtered_trades = [t for t in legacy_trades if t['entry_time'] not in enhanced_times]
        
        if filtered_trades:
            filtered_winners = [t for t in filtered_trades if t.get('pnl_pct', 0) > 0]
            filtered_losers = [t for t in filtered_trades if t.get('pnl_pct', 0) <= 0]
            
            print(f"\n🔍 CRITICAL: What Quality Filter Removed:")
            print(f"Total filtered: {len(filtered_trades)}")
            print(f"Filtered winners: {len(filtered_winners)} ({len(filtered_winners)/len(filtered_trades)*100:.1f}%)")
            print(f"Filtered losers: {len(filtered_losers)} ({len(filtered_losers)/len(filtered_trades)*100:.1f}%)")
            
            # Calculate average P&L of filtered trades
            avg_filtered_pnl = sum(t.get('pnl_pct', 0) for t in filtered_trades) / len(filtered_trades)
            print(f"Average P&L of filtered trades: {avg_filtered_pnl:.3%}")
            
            # Check if we're filtering out bad trades
            if avg_filtered_pnl < 0:
                print("✅ Quality filter is removing net-negative trades!")
            else:
                print("⚠️  Quality filter might be removing profitable trades")
            
            # Show worst filtered trades
            filtered_sorted = sorted(filtered_trades, key=lambda x: x.get('pnl_pct', 0))
            print(f"\nWorst 5 filtered trades:")
            for i, trade in enumerate(filtered_sorted[:5]):
                print(f"  {i+1}. P&L: {trade.get('pnl_pct', 0):.2%} at {trade['entry_time']}")
    
    # Performance comparison
    print(f"\n📊 Performance Comparison:")
    print(f"Win Rate: {legacy_results.get('win_rate', 0):.2%} → {enhanced_results.get('win_rate', 0):.2%} "
          f"({enhanced_results.get('win_rate', 0) - legacy_results.get('win_rate', 0):+.2%})")
    print(f"Total Return: {legacy_results.get('total_return', 0):.2%} → {enhanced_results.get('total_return', 0):.2%} "
          f"({enhanced_results.get('total_return', 0) - legacy_results.get('total_return', 0):+.2%})")
    print(f"Sharpe: {legacy_results.get('sharpe_ratio', 0):.2f} → {enhanced_results.get('sharpe_ratio', 0):.2f} "
          f"({enhanced_results.get('sharpe_ratio', 0) - legacy_results.get('sharpe_ratio', 0):+.2f})")
    
    # Verdict
    print("\n📝 VERDICT:")
    if enhanced_results.get('win_rate', 0) > legacy_results.get('win_rate', 0):
        print("✅ Quality filtering IMPROVED win rate - removing more bad trades than good ones")
    else:
        print("❌ Quality filtering REDUCED win rate - might be too restrictive")
    
    if enhanced_results.get('sharpe_ratio', 0) > legacy_results.get('sharpe_ratio', 0):
        print("✅ Risk-adjusted returns IMPROVED - better trade quality")
    else:
        print("❌ Risk-adjusted returns DECLINED - review quality criteria")


def main():
    """Run quick A/B test for Q1 2024."""
    print("Running Quick A/B Test for Q1 2024...")
    print("This will complete much faster than full year backtests")
    
    # Run both tests
    legacy_results = run_legacy_test()
    enhanced_results = run_enhanced_test()
    
    # Analyze quality filtering effectiveness
    analyze_quality_filtering(legacy_results, enhanced_results)
    
    # Save comparison
    comparison = {
        'timestamp': datetime.now().isoformat(),
        'period': 'Q1 2024',
        'legacy': {
            'trades': legacy_results.get('total_trades', 0),
            'win_rate': legacy_results.get('win_rate', 0),
            'return': legacy_results.get('total_return', 0),
            'sharpe': legacy_results.get('sharpe_ratio', 0)
        },
        'enhanced': {
            'trades': enhanced_results.get('total_trades', 0),
            'win_rate': enhanced_results.get('win_rate', 0),
            'return': enhanced_results.get('total_return', 0),
            'sharpe': enhanced_results.get('sharpe_ratio', 0)
        }
    }
    
    with open('quick_ab_comparison_q1_2024.json', 'w') as f:
        json.dump(comparison, f, indent=2)
    
    print("\n\nQuick A/B test complete!")
    print("For full year results, run the full backtest scripts when you have time.")


if __name__ == "__main__":
    main()