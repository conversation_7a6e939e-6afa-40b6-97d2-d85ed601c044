#!/usr/bin/env python3
"""
Enhanced analytics for comparing trade direction distribution between legacy and modern systems
"""

import sys
import os
import json
from datetime import datetime
from collections import defaultdict
import pandas as pd

# Add project root to path
sys.path.append('/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot')

class TradeDirectionAnalytics:
    """Analytics class to track and compare trade directions between systems"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """Reset all tracking variables"""
        self.regime_counts = defaultdict(int)
        self.regime_mappings = defaultdict(int)
        self.trade_signals = []
        self.regime_to_trades = defaultdict(lambda: {'long': 0, 'short': 0})
        
    def track_regime(self, raw_regime, mapped_regime, timestamp):
        """Track regime detection and mapping"""
        self.regime_counts[raw_regime] += 1
        self.regime_mappings[f"{raw_regime} -> {mapped_regime}"] += 1
        
    def track_signal(self, signal_dir, regime_raw, regime_mapped, timestamp, strategy_name):
        """Track trade signal generation"""
        signal_data = {
            'timestamp': timestamp,
            'direction': signal_dir,
            'regime_raw': regime_raw,
            'regime_mapped': regime_mapped,
            'strategy': strategy_name
        }
        self.trade_signals.append(signal_data)
        self.regime_to_trades[regime_raw][signal_dir] += 1
        
    def get_summary(self):
        """Get comprehensive analytics summary"""
        total_signals = len(self.trade_signals)
        long_signals = len([s for s in self.trade_signals if s['direction'] == 'long'])
        short_signals = len([s for s in self.trade_signals if s['direction'] == 'short'])
        
        summary = {
            'total_signals': total_signals,
            'long_signals': long_signals,
            'short_signals': short_signals,
            'long_pct': (long_signals / total_signals * 100) if total_signals > 0 else 0,
            'short_pct': (short_signals / total_signals * 100) if total_signals > 0 else 0,
            'regime_counts': dict(self.regime_counts),
            'regime_mappings': dict(self.regime_mappings),
            'regime_to_trades': dict(self.regime_to_trades),
            'trade_signals': self.trade_signals
        }
        
        return summary
    
    def print_summary(self, system_name):
        """Print formatted analytics summary"""
        summary = self.get_summary()
        
        print(f"\n{'='*60}")
        print(f"📊 TRADE DIRECTION ANALYTICS - {system_name.upper()} SYSTEM")
        print(f"{'='*60}")
        
        print(f"\n🎯 TRADE SIGNAL DISTRIBUTION:")
        print(f"  Total signals: {summary['total_signals']}")
        print(f"  Long trades:   {summary['long_signals']:3d} ({summary['long_pct']:5.1f}%)")
        print(f"  Short trades:  {summary['short_signals']:3d} ({summary['short_pct']:5.1f}%)")
        
        if summary['total_signals'] == 0:
            print("  ⚠️  NO TRADES GENERATED!")
            return summary
            
        print(f"\n📈 REGIME DETECTION FREQUENCY:")
        total_regime_detections = sum(summary['regime_counts'].values())
        for regime, count in sorted(summary['regime_counts'].items(), key=lambda x: x[1], reverse=True):
            pct = (count / total_regime_detections * 100) if total_regime_detections > 0 else 0
            print(f"  {regime:<20}: {count:5d} ({pct:5.1f}%)")
            
        print(f"\n🔄 REGIME MAPPING ANALYSIS:")
        for mapping, count in sorted(summary['regime_mappings'].items(), key=lambda x: x[1], reverse=True):
            pct = (count / total_regime_detections * 100) if total_regime_detections > 0 else 0
            print(f"  {mapping:<35}: {count:5d} ({pct:5.1f}%)")
            
        print(f"\n⚡ TRADES BY REGIME STATE:")
        for regime, trades in summary['regime_to_trades'].items():
            total_trades = trades['long'] + trades['short']
            if total_trades > 0:
                long_pct = (trades['long'] / total_trades * 100)
                short_pct = (trades['short'] / total_trades * 100)
                print(f"  {regime:<20}: {trades['long']:2d}L ({long_pct:4.0f}%) | {trades['short']:2d}S ({short_pct:4.0f}%)")
        
        return summary
    
    def save_to_file(self, system_name, filepath=None):
        """Save analytics to JSON file"""
        if filepath is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filepath = f"/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/trade_analytics_{system_name}_{timestamp}.json"
            
        summary = self.get_summary()
        summary['system_name'] = system_name
        summary['timestamp'] = datetime.now().isoformat()
        
        with open(filepath, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
            
        print(f"\n💾 Analytics saved to: {filepath}")
        return filepath

# Global analytics instances
analytics_legacy = TradeDirectionAnalytics()
analytics_modern = TradeDirectionAnalytics()

def get_analytics(system_mode):
    """Get analytics instance for system"""
    return analytics_legacy if system_mode == 'legacy' else analytics_modern

def compare_systems(legacy_summary, modern_summary):
    """Compare analytics between legacy and modern systems"""
    print(f"\n{'='*80}")
    print(f"🔍 SYSTEM COMPARISON ANALYSIS")
    print(f"{'='*80}")
    
    print(f"\n📊 TRADE DISTRIBUTION COMPARISON:")
    print(f"{'Metric':<25} {'Legacy':<15} {'Modern':<15} {'Difference'}")
    print(f"{'-'*70}")
    
    metrics = [
        ('Total Signals', 'total_signals'),
        ('Long Trades', 'long_signals'),
        ('Short Trades', 'short_signals'),
        ('Long %', 'long_pct'),
        ('Short %', 'short_pct')
    ]
    
    for metric_name, key in metrics:
        legacy_val = legacy_summary.get(key, 0)
        modern_val = modern_summary.get(key, 0)
        
        if key.endswith('_pct'):
            diff = modern_val - legacy_val
            print(f"{metric_name:<25} {legacy_val:6.1f}%        {modern_val:6.1f}%        {diff:+6.1f}%")
        else:
            diff = modern_val - legacy_val
            print(f"{metric_name:<25} {legacy_val:6d}          {modern_val:6d}          {diff:+6d}")
    
    print(f"\n🎯 KEY FINDINGS:")
    
    # Analyze the differences
    if modern_summary['short_signals'] == 0 and legacy_summary['short_signals'] > 0:
        print(f"  🚨 CRITICAL: Modern system has ZERO short trades vs {legacy_summary['short_signals']} in legacy")
        print(f"     This confirms the long-only issue!")
        
        # Check which regimes generate shorts in legacy
        legacy_short_regimes = []
        for regime, trades in legacy_summary.get('regime_to_trades', {}).items():
            if trades.get('short', 0) > 0:
                legacy_short_regimes.append(regime)
                
        if legacy_short_regimes:
            print(f"  📍 Legacy short trades come from: {legacy_short_regimes}")
            
            # Check if modern system detects these regimes
            modern_regimes = set(modern_summary.get('regime_counts', {}).keys())
            missing_regimes = set(legacy_short_regimes) - modern_regimes
            
            if missing_regimes:
                print(f"  ❌ Modern system MISSING regimes: {list(missing_regimes)}")
            else:
                print(f"  ✅ Modern system detects same regimes, issue is in strategy logic")
    
    elif abs(modern_summary['short_pct'] - legacy_summary['short_pct']) > 10:
        print(f"  ⚠️  Significant difference in short trade percentage")
        diff = modern_summary['short_pct'] - legacy_summary['short_pct']
        print(f"     Modern has {diff:+.1f}% short trades vs legacy")
    else:
        print(f"  ✅ Similar trade distribution between systems")
    
    return {
        'legacy_shorts': legacy_summary['short_signals'],
        'modern_shorts': modern_summary['short_signals'],
        'issue_confirmed': modern_summary['short_signals'] == 0 and legacy_summary['short_signals'] > 0
    }

if __name__ == "__main__":
    # This script is meant to be imported, but can run standalone for testing
    print("Trade Direction Analytics Module")
    print("Import this module in backtester to enable analytics tracking")