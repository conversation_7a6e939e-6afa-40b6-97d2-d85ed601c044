import yaml
from pathlib import Path
import argparse
import logging
import pandas as pd
import mplfinance as mpf
import matplotlib.pyplot as plt
import matplotlib.dates as mdates # Import for date formatting
from typing import Dict, List, Optional, Tuple
import json
import glob
import os
import re
import numpy as np

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Updated config and log paths
CONFIG_PATH = Path(__file__).parent.parent / "configs" / "base.yaml"
LOGS_DIR = "/Users/<USER>/Desktop/trading_bot_/logs"

# Define colors for different regimes (customize as needed)
REGIME_COLORS = {
    "Strong_Bull_Trend": (0.0, 1.0, 0.0, 0.85),  # Less Transparent Green
    "Weak_Bull_Trend": (0.56, 0.93, 0.56, 0.85), # Less Transparent Lighter Green
    "Strong_Bear_Trend": (1.0, 0.0, 0.0, 0.85),  # Less Transparent Red
    "Weak_Bear_Trend": (1.0, 0.71, 0.76, 0.85),  # Less Transparent Lighter Red
    "High_Vol_Range": (1.0, 0.65, 0.0, 0.85),    # Less Transparent Orange
    "Low_Vol_Range": (0.68, 0.85, 0.9, 0.85),    # Less Transparent Light Blue
    "TIGHT_SPREAD": (1.0, 1.0, 0.8, 0.85),       # Less Transparent Light Yellow (NEW)
    "Uncertain": (0.83, 0.83, 0.83, 0.85),       # Less Transparent Light Grey
    "Unknown": (0.5, 0.0, 0.5, 0.85),            # Less Transparent Purple
    "Filter_Off": (1.0, 1.0, 1.0, 0.0),          # Still Transparent
}
DEFAULT_REGIME_COLOR = (0.5, 0.5, 0.5, 0.8)      # Fallback Grey (Less Transparent)

# Define colors for 3-state regimes
THREE_STATE_COLORS = {
    "BULL": (0.0, 1.0, 0.0, 0.85),  # Less Transparent Green
    "BEAR": (1.0, 0.0, 0.0, 0.85),  # Less Transparent Red
    "CHOP": (1.0, 0.65, 0.0, 0.85), # Less Transparent Orange
}
DEFAULT_THREE_STATE_COLOR = DEFAULT_REGIME_COLOR # Fallback Grey (Less Transparent)

# --- Helper Functions ---

def find_latest_backtest_files(log_dir: str = LOGS_DIR) -> Tuple[Optional[str], Optional[str]]:
    """
    Finds the most recent signals and trades files in the log directory.
    Returns (latest_signals_path, latest_trades_path)
    """
    try:
        # Get all signal and trade files
        signal_files = glob.glob(os.path.join(log_dir, "backtest_signals_*.parquet"))
        trade_files = glob.glob(os.path.join(log_dir, "backtest_trades_*.json"))
        
        if not signal_files or not trade_files:
            logger.warning(f"No matching backtest files found in {log_dir}")
            return None, None
            
        # Extract timestamps from filenames
        signal_timestamps = {re.search(r"(\d{8}_\d{6})", f).group(1): f for f in signal_files if re.search(r"(\d{8}_\d{6})", f)}
        trade_timestamps = {re.search(r"(\d{8}_\d{6})", f).group(1): f for f in trade_files if re.search(r"(\d{8}_\d{6})", f)}
        common_timestamps = set(signal_timestamps.keys()) & set(trade_timestamps.keys())
        if not common_timestamps:
            logger.warning("No matching signal/trade file pairs found")
            return None, None
            
        # Get the latest timestamp
        latest_timestamp = max(common_timestamps)
        latest_signals = signal_timestamps[latest_timestamp]
        latest_trades = trade_timestamps[latest_timestamp]
        logger.info(f"Found latest backtest run from {latest_timestamp}")
        return latest_signals, latest_trades
        
    except Exception as e:
        logger.error(f"Error finding latest backtest files: {e}")
        return None, None

# --- Core Plotting Function ---

def plot_backtest_results(
    signals_df: pd.DataFrame,
    trades_list: List[Dict],
    output_path: Path,
    title: str = "Backtest Visualization",
    plot_indicators: Optional[List[str]] = None,
    args: Optional[argparse.Namespace] = None,
    viz_config: Optional[Dict] = None,
    gms_mapping_active: bool = False, # New arg: Is 3-state mapping active?
    gms_state_map: Optional[Dict[str, str]] = None # New arg: The loaded map
):
    """
    Generates a plot visualizing backtest results including price, EMAs, regimes (7 or 3 state), and trades.

    Args:
        signals_df: DataFrame with Timestamp index, OHLC columns, indicator columns, and 'regime' column.
        trades_list: List of dictionaries, where each dict represents a closed trade.
                     Required keys: 'entry_time', 'exit_time', 'entry', 'exit', 'type' ('long'/'short').
        output_path: Path object for saving the plot image.
        title: Title for the plot.
        plot_indicators: List of additional column names from signals_df to plot in separate panels.
        args: Command line arguments (for trade display options).
        viz_config: Dictionary containing visualization settings from config.yaml.
    """
    # Initialize args if None to avoid attribute errors
    if args is None:
        class Args:
            no_trades = False
            trade_labels = False
        args = Args()

    # Initialize viz_config if None
    if viz_config is None:
        viz_config = {}
    appearance_config = viz_config.get('appearance', {})
    price_panel_ratio = appearance_config.get('price_panel_ratio', 4)
    indicator_panel_ratio = appearance_config.get('indicator_panel_ratio', 1)

    if signals_df.empty:
        logger.warning("Signals DataFrame is empty. Skipping plot.")
        return

    if not isinstance(signals_df.index, pd.DatetimeIndex):
         try:
              # Attempt conversion assuming index holds parsable datetime info
              signals_df.index = pd.to_datetime(signals_df.index)
              logger.info("Converted DataFrame index to DatetimeIndex.")
         except Exception as e:
              logger.error(f"Failed to convert DataFrame index to DatetimeIndex: {e}. Ensure index is datetime.")
              return


    logger.info(f"Plotting results to {output_path}...")

    # --- 1. Prepare Plot Panels ---
    base_ap = [] # Add-plots for the main price panel
    
    # Track panels and their content
    all_panels = []
    
    # First panel is always the main price panel
    main_panel = {"name": "price", "ratio": price_panel_ratio}
    all_panels.append(main_panel)

    # Check if volume data exists and if config requires it
    config_requires_volume = viz_config.get('require_volume', False)
    has_volume_data = 'volume' in signals_df.columns and not signals_df['volume'].isnull().all()
    plot_volume = has_volume_data # Plot if data exists, regardless of config requirement for now

    if not has_volume_data:
        logger.warning("Volume column not found or contains all NaNs. Volume plot will be skipped.")
    elif config_requires_volume and not has_volume_data:
         logger.error("Config requires volume, but it's missing in the data. Cannot plot volume.")
         plot_volume = False # Don't plot if required but missing
    elif not config_requires_volume and not has_volume_data:
         logger.info("Volume not required by config and not present in data. Skipping volume plot.")
         plot_volume = False
    else: # Volume exists
        logger.info(f"Volume data found. Config requires volume: {config_requires_volume}. Plotting volume.")
        plot_volume = True


    # Second panel is volume, but only if plotting it
    if plot_volume:
        volume_panel = {"name": "volume", "ratio": indicator_panel_ratio} # Use indicator ratio for volume too
        all_panels.append(volume_panel)
        volume_panel_index = 1 # Will be the second panel
    else:
        volume_panel_index = -1 # Indicates no volume panel

    # Add EMAs if present (these go on the main price panel)
    if 'tf_ewma_fast' in signals_df.columns:
        # Make fast EMA much thinner
        base_ap.append(mpf.make_addplot(signals_df['tf_ewma_fast'], color='#00FFFF', width=1.0))
    if 'tf_ewma_slow' in signals_df.columns:
        # Make slow EMA much thinner
        base_ap.append(mpf.make_addplot(signals_df['tf_ewma_slow'], color='#FF00FF', width=1.0))

    # Prepare additional panels for indicators like Slope, OBI, Fear/Greed
    indicator_panels = {}
    # Start panel IDs after price and potential volume
    panel_id_counter = 2 if plot_volume else 1

    # Check which indicators are available and requested
    if plot_indicators:
        logger.info(f"Requested indicators for plotting: {plot_indicators}") # Log requested indicators
        # First check which requested indicators are actually available
        available_indicators = [ind for ind in plot_indicators if ind in signals_df.columns]
        logger.info(f"Available indicators found in DataFrame columns: {available_indicators}") # Log available indicators

        if len(available_indicators) < len(plot_indicators):
            missing = set(plot_indicators) - set(available_indicators)
            logger.warning(f"Some requested indicators not found in data: {missing}")

        for indicator_name in available_indicators:
            # Add specific check for fear_greed_idx before the NaN check
            if indicator_name == 'fear_greed_idx': # Corrected column name
                if signals_df[indicator_name].isnull().all():
                    logger.warning(f"Indicator '{indicator_name}' exists but contains only NaN values. Skipping plot.")
                    continue
                else:
                    logger.info(f"Indicator '{indicator_name}' found with valid data. Proceeding to plot.")
            elif pd.isna(signals_df[indicator_name]).all(): # Original check for other indicators
                logger.warning(f"Indicator '{indicator_name}' contains only NaN values. Skipping.")
                continue

            # Add this panel
            this_panel = {"name": indicator_name, "ratio": indicator_panel_ratio}
            all_panels.append(this_panel)

            # Create a more readable label
            if indicator_name == 'obi_smoothed':
                ylabel = 'OBI'
            elif indicator_name == 'ma_slope':
                ylabel = 'MA Slope'
            elif indicator_name == 'fear_greed_idx': # Corrected column name
                 ylabel = 'Fear/Greed'
            else:
                ylabel = indicator_name.replace('_', ' ').title()

            indicator_panels[indicator_name] = {
                "panel": panel_id_counter, # Assign current panel ID
                "ylabel": ylabel,
                "data": signals_df[indicator_name]
            }

            # Add horizontal line at zero if it makes sense (e.g., for OBI, Slope)
            if 'obi' in indicator_name.lower() or 'slope' in indicator_name.lower():
                indicator_panels[indicator_name]['secondary_y'] = False # Keep on primary axis of panel
                # Need to add hlines after plot creation via axes object

            # Create the addplot
            base_ap.append(mpf.make_addplot(
                indicator_panels[indicator_name]['data'],
                panel=indicator_panels[indicator_name]['panel'],
                ylabel=indicator_panels[indicator_name]['ylabel'],
                secondary_y=indicator_panels[indicator_name].get('secondary_y', False)
            ))

            panel_id_counter += 1

    # --- 2. Create the Plot (Get Figure and Axes) ---
    # Build panel ratios tuple from all_panels list
    panel_ratios = tuple(panel["ratio"] for panel in all_panels)
    num_panels = len(all_panels)

    logger.info(f"Creating plot with {num_panels} panels: {[p['name'] for p in all_panels]}")
    logger.info(f"Panel ratios: {panel_ratios}")
    logger.info(f"Panel setup: num_panels={num_panels}, len(panel_ratios)={len(panel_ratios)}")

    # Add the kwarg to silence "too many data points" warning
    kwargs = {}
    max_points_config = appearance_config.get('max_points', 20000) # Get max points from config
    if len(signals_df) > 300:  # If we have more than 300 data points
        kwargs['warn_too_much_data'] = max_points_config # Use config value

    # --- Create a copy for coloring only - don't modify the volume data ---
    # Create a copy of the signals DataFrame to avoid modifying the original
    plot_df = signals_df.copy()
    
    # Create custom style for thicker candles
    custom_style = mpf.make_mpf_style(
        marketcolors=mpf.make_marketcolors(
            up='#00b060',        # Darker green for up candles
            down='#e14340',      # Darker red for down candles
            edge='inherit',
            wick={'up': '#00b060', 'down': '#e14340'}, # Match candle colors
            ohlc='inherit',
            volume={'up': '#00b060', 'down': '#e14340'},  # Color volume bars
        ),
        gridstyle="",           # No grid
        y_on_right=True,        # Price labels on right side
        rc={
            'axes.linewidth': 1.5,
            'axes.edgecolor': 'black',
            'axes.grid': False,
            'figure.facecolor': 'white',
            'xtick.color': 'black',
            'ytick.color': 'black',
            'text.color': 'black',
            'lines.linewidth': 1.5,
            # Set candle properties directly in rc
            'patch.linewidth': 2.0,  # Controls candle body edge width
        },
        base_mpf_style='yahoo',  # Base on yahoo style
    )
    
    # Create the plot
    try:
        # Use normal plot with standard panel ratios
        fig, axlist = mpf.plot(plot_df,
                              type='candle',
                              style=custom_style,
                              title=title,
                              ylabel='Price',
                              volume=plot_volume, # Conditionally enable volume based on earlier check
                              addplot=base_ap,
                              panel_ratios=panel_ratios, # Use calculated ratios
                              figscale=2.5,
                              figratio=(16, 10),
                              returnfig=True,
                              **kwargs
                             )
    except ValueError as e:
        logger.error(f"Panel configuration error: {e}")
        logger.error(f"Panel ratios used: {panel_ratios}")
        logger.error(f"Number of panels expected by mplfinance: {len(panel_ratios)}")
        logger.error(f"Number of addplots: {len(base_ap)}")
        logger.error(f"Trying fallback plot with simplified panel setup.")
        # Fallback to simplified plot
        fig, axlist = mpf.plot(signals_df,
                               type='candle',
                               style='yahoo',
                               title=f"{title} (simplified)",
                               ylabel='Price',
                               volume=plot_volume, # Conditionally enable volume in fallback
                               figscale=2.5, # Also increased in fallback plot
                               figratio=(16, 10),
                               returnfig=True,
                               **kwargs
                              )

    # Remove grid lines from all panels
    for ax in axlist:
        if hasattr(ax, 'grid'):
            ax.grid(False)

    # --- Manually Color Volume Bars (only if volume exists and was plotted) ---
    if plot_volume and volume_panel_index != -1:
        try:
            # Find the volume axes using the determined index
            # Indices: Price=0, PriceX=1, Volume=2, VolumeX=3, Indicator1=4, Indicator1X=5 ...
            volume_ax_index = volume_panel_index * 2 # The primary axes for the volume panel
            volume_ax = axlist[volume_ax_index]

            # Get the up/down colors from the style
            up_color = custom_style['marketcolors']['volume']['up']
            down_color = custom_style['marketcolors']['volume']['down']

            # Iterate through candles and corresponding volume bars
            for i, patch in enumerate(volume_ax.patches):
                if i < len(plot_df): # Ensure we don't go out of bounds
                    if plot_df['close'].iloc[i] >= plot_df['open'].iloc[i]:
                        patch.set_facecolor(up_color)
                    else:
                        patch.set_facecolor(down_color)
                    patch.set_edgecolor(patch.get_facecolor()) # Match edge color to face color
                else:
                    break # Should not happen if patches align with data
            logger.info("Manually applied colors to volume bars.")
        except IndexError:
            logger.warning(f"Could not find volume axes at expected index {volume_ax_index}. Skipping manual volume coloring.")
        except Exception as e:
            logger.error(f"Error during manual volume coloring: {e}")
    elif not plot_volume:
        logger.info("Volume data not present or not plotted, skipping manual volume coloring.")

    # --- 3. Post-Plot Adjustments (Regimes, Crossovers, Trades) ---
    # Find axes for indicator panels (mplfinance axes list structure can vary)
    indicator_axes = {}
    # Price is axlist[0], Volume might be axlist[1] if present
    current_panel_index = 1 if plot_volume else 0

    main_ax = axlist[0] # Price panel axes usually first
    # Find axes for indicator panels (mplfinance axes list structure can vary with volume)
    indicator_axes = {
         name: axlist[data['panel']*2] # Heuristic: panels often take 2 axes slots (main + x-axis labels)
         for name, data in indicator_panels.items()
         if data['panel']*2 < len(axlist) # Check index bounds
    }


    # Add zero lines to indicator panels if applicable
    for name, ax in indicator_axes.items():
         if 'obi' in name.lower() or 'slope' in name.lower():
              ax.axhline(0, color='gray', linestyle='--', linewidth=0.8)


    # --- 3. Add Regime Background Shading (Handles 7-state or 3-state) ---
    logger.info("Adding regime background shading...")
    regime_col_to_use = 'regime' # Default to original 7-state column
    colors_to_use = REGIME_COLORS
    default_color = DEFAULT_REGIME_COLOR
    regime_label_prefix = "" # For legend

    if 'regime' in signals_df.columns:
        # Apply mapping if active and map is valid
        if gms_mapping_active and gms_state_map:
            logger.info("Applying 3-state GMS mapping for visualization.")
            # Create a temporary column for the mapped regime
            signals_df['mapped_regime'] = signals_df['regime'].map(gms_state_map)
            # Fill any regimes not in the map with a default (e.g., 'CHOP' or keep original?)
            # Let's keep original for now if not mapped, but use 3-state colors if possible
            signals_df['mapped_regime'].fillna(signals_df['regime'], inplace=True) # Keep original if no map entry
            regime_col_to_use = 'mapped_regime'
            colors_to_use = THREE_STATE_COLORS # Use 3-state colors
            default_color = DEFAULT_THREE_STATE_COLOR
            regime_label_prefix = "Mapped " # Add prefix to legend labels
        else:
            logger.info("Using original 7-state regime for visualization.")

        # Debug: Print unique regimes being used and their colors
        unique_regimes = signals_df[regime_col_to_use].dropna().unique()
        logger.info(f"Found {len(unique_regimes)} unique regimes in '{regime_col_to_use}': {unique_regimes[:5]} {'...' if len(unique_regimes) > 5 else ''}")
        for regime in unique_regimes:
            color = colors_to_use.get(regime, default_color)
            logger.info(f"Regime: {regime}, Color: {color}")

        # Group consecutive regimes to draw fewer spans
        try:
            regime_series = signals_df[regime_col_to_use]
            regime_changes = regime_series != regime_series.shift(1)
            change_points = list(signals_df.index[regime_changes])
            if not change_points or change_points[0] != signals_df.index[0]:
                 change_points.insert(0, signals_df.index[0]) # Ensure first point is included

            # Add the end point if it's not already the last change point
            if not change_points or change_points[-1] != signals_df.index[-1]:
                 change_points.append(signals_df.index[-1])

            # Process each regime segment
            for i in range(len(change_points)-1):
                start_dt = change_points[i]
                end_dt = change_points[i+1]

                # Get the regime value for this segment (use the start point's regime)
                regime = regime_series.loc[start_dt]

                # Skip NaN regimes
                if pd.isna(regime):
                    continue

                # Get integer indices for axvspan
                start_idx = signals_df.index.get_loc(start_dt)
                # For the end index, we want the span to go up to *before* the next change
                # Get the index *before* the end_dt
                end_loc_temp = signals_df.index.get_loc(end_dt)
                # If the end_dt is the very last point, span up to it. Otherwise, span up to the point *before* it.
                end_idx = end_loc_temp if end_loc_temp == len(signals_df) - 1 else end_loc_temp

                color = colors_to_use.get(regime, default_color)

                # Draw the span using integer indices
                # Adjust indices by -0.5 for better alignment with candles
                main_ax.axvspan(
                    start_idx - 0.5,
                    end_idx - 0.5, # Span up to the bar *before* the change
                    color=color,
                    alpha=1.0, # Use full alpha defined in color tuple
                    zorder=-10
                )
                # logger.debug(f"Drew regime span for {regime} from idx {start_idx} to {end_idx}")

        except Exception as e:
            logger.error(f"Error drawing regime backgrounds: {e}", exc_info=True)
            # Fallback or skip? For now, just log the error.

    else:
        logger.warning("No 'regime' column found in signals data. Skipping regime background shading.")
        
    # --- 3.5. Add EMA Crossover Markers ---
    if 'tf_ewma_fast' in signals_df.columns and 'tf_ewma_slow' in signals_df.columns:
        logger.info("Detecting and marking EMA crossovers...")
        
        fast_ema = signals_df['tf_ewma_fast']
        slow_ema = signals_df['tf_ewma_slow']
        
        # Create signals for when fast crosses above or below slow
        cross_above = (fast_ema > slow_ema) & (fast_ema.shift(1) <= slow_ema.shift(1))
        cross_below = (fast_ema < slow_ema) & (fast_ema.shift(1) >= slow_ema.shift(1))
        
        # Get indices where crossovers happen
        cross_above_idx = signals_df.index[cross_above]
        cross_below_idx = signals_df.index[cross_below]
        
        # Convert datetime indices to integer indices for plotting
        cross_above_iloc = [signals_df.index.get_loc(idx) for idx in cross_above_idx]
        cross_below_iloc = [signals_df.index.get_loc(idx) for idx in cross_below_idx]
        
        logger.info(f"Found {len(cross_above_iloc)} upward and {len(cross_below_iloc)} downward EMA crossovers")
        
        # Define colors for crosses
        bullish_cross_color = '#9932CC'  # Purple for bullish crosses
        bearish_cross_color = '#00008B'  # Deep blue for bearish crosses
        
        # Store references for legend
        bullish_cross_marker = None
        bearish_cross_marker = None
        
        # Plot bullish crosses (Fast crosses above Slow) - with PURPLE color and white outline
        for idx in cross_above_iloc:
            # Get the price level where the cross happened (average of the EMAs at crossover)
            price_level = (signals_df['tf_ewma_fast'].iloc[idx] + signals_df['tf_ewma_slow'].iloc[idx]) / 2
            # First plot a slightly larger white X for the outline
            main_ax.plot(idx, price_level, marker='X', color='white', markersize=8,
                        markeredgewidth=1.0)
            # Then plot the colored X on top of it
            marker_line = main_ax.plot(idx, price_level, marker='X', color=bullish_cross_color, markersize=6, 
                        markeredgewidth=0.5)
            if bullish_cross_marker is None:
                bullish_cross_marker = marker_line[0]  # Save for legend
        
        # Plot bearish crosses (Fast crosses below Slow) - with DEEP BLUE color and white outline
        for idx in cross_below_iloc:
            # Get the price level where the cross happened (average of the EMAs at crossover)
            price_level = (signals_df['tf_ewma_fast'].iloc[idx] + signals_df['tf_ewma_slow'].iloc[idx]) / 2
            # First plot a slightly larger white X for the outline
            main_ax.plot(idx, price_level, marker='X', color='white', markersize=8,
                        markeredgewidth=1.0)
            # Then plot the colored X on top of it
            marker_line = main_ax.plot(idx, price_level, marker='X', color=bearish_cross_color, markersize=6, 
                        markeredgewidth=0.5)
            if bearish_cross_marker is None:
                bearish_cross_marker = marker_line[0]  # Save for legend
                
        # Add to legend elements for later
        if bullish_cross_marker is not None and bearish_cross_marker is not None:
            # Create legend elements
            crossover_legend_elements = [
                plt.Line2D([0], [0], marker='X', color='w', markerfacecolor=bullish_cross_color, 
                          markersize=8, label='Bullish Cross'),
                plt.Line2D([0], [0], marker='X', color='w', markerfacecolor=bearish_cross_color, 
                          markersize=8, label='Bearish Cross')
            ]
            
            # Save for later legend creation
            if not hasattr(main_ax, 'custom_legend_elements'):
                main_ax.custom_legend_elements = crossover_legend_elements
            else:
                main_ax.custom_legend_elements.extend(crossover_legend_elements)
    else:
        logger.warning("EMAs not found in data. Skipping crossover detection.")

    # --- 4. Add Trade Markers (Core Logic) ---
    if trades_list and not args.no_trades:
        logger.info("Adding trade markers...")
        
        # Filter trades to only include those within the time range we're displaying
        visible_trades = []
        min_time = signals_df.index.min().timestamp()
        max_time = signals_df.index.max().timestamp()
        
        for trade in trades_list:
            entry_time = trade['entry_time']
            exit_time = trade['exit_time']
            # Only include trades that have entry or exit within our visible range
            if (entry_time >= min_time and entry_time <= max_time) or \
               (exit_time >= min_time and exit_time <= max_time) or \
               (entry_time <= min_time and exit_time >= max_time):
                visible_trades.append(trade)
        
        logger.info(f"Plotting {len(visible_trades)} trades within visible time range (out of {len(trades_list)} total)")
        
        # Plot each trade with careful index matching
        for trade in visible_trades:
            try:
                # Convert trade timestamps to datetime objects
                entry_time_dt = pd.to_datetime(trade['entry_time'], unit='s', utc=True)
                exit_time_dt = pd.to_datetime(trade['exit_time'], unit='s', utc=True)
                
                # Make sure we only plot what's visible
                if entry_time_dt < signals_df.index.min():
                    entry_time_dt = signals_df.index.min()
                if exit_time_dt > signals_df.index.max():
                    exit_time_dt = signals_df.index.max()
                
                # Find the closest bars to our trade timestamps in the visible data
                entry_idx = signals_df.index.get_indexer([entry_time_dt], method='nearest')[0]
                exit_idx = signals_df.index.get_indexer([exit_time_dt], method='nearest')[0]
                
                # Skip if we couldn't find valid indices
                if entry_idx < 0 or exit_idx < 0:
                    logger.warning(f"Couldn't find valid index for trade at {entry_time_dt}/{exit_time_dt}. Skipping.")
                    continue
                
                # Get trade details
                entry_price = trade['entry']
                exit_price = trade['exit']
                trade_type = trade['type']
                
                # Plot entry marker (triangle up for long, down for short)
                entry_marker = '^' if trade_type == 'long' else 'v'
                entry_color = 'green' if trade_type == 'long' else 'red'
                
                # Plot against X axis as integers (positions in the dataframe)
                main_ax.plot(entry_idx, entry_price, marker=entry_marker, 
                            color=entry_color, markersize=5, alpha=0.6) # Reduced from 6 to 5
                
                # Plot exit marker (blue circle)
                main_ax.plot(exit_idx, exit_price, marker='o', 
                            color='blue', markersize=3, alpha=0.6) # Reduced from 4 to 3
                
                # Draw connecting line between entry and exit
                main_ax.plot([entry_idx, exit_idx], [entry_price, exit_price], 
                            linestyle='-', color='blue', alpha=0.2, linewidth=0.8) # Slightly more transparent line
                
                # Add PnL label if requested
                if args.trade_labels and 'pnl' in trade:
                    pnl = trade['pnl']
                    label_color = 'green' if pnl > 0 else 'red'
                    main_ax.annotate(f"{pnl:.2f}", 
                                    xy=(exit_idx, exit_price),
                                    xytext=(5, 5), textcoords='offset points',
                                    color=label_color, fontsize=8)
                    
            except Exception as e:
                logger.warning(f"Error plotting trade: {e}")
    else:
        logger.info("Skipping trade markers (none provided or disabled by --no-trades)")

    # --- 5. Create Comprehensive Legend ---
    logger.info("Creating comprehensive legend...")
    legend_elements = []

    # Regime colors (use the same logic as for shading)
    if 'regime' in signals_df.columns:
        regime_col = regime_col_to_use # 'regime' or 'mapped_regime'
        color_map = colors_to_use
        default_col = default_color
        label_prefix = regime_label_prefix

        unique_regimes_for_legend = list(signals_df[regime_col].dropna().unique())
        # Sort regimes: BULL, CHOP, BEAR first if present, then others alphabetically
        def sort_key(r):
            if r == 'BULL': return 0
            if r == 'CHOP': return 1
            if r == 'BEAR': return 2
            return 3 # Put others after
        unique_regimes_for_legend.sort(key=lambda r: (sort_key(r), r))


        for regime in unique_regimes_for_legend:
            color = color_map.get(regime, default_col)
            # Create nicer label
            label = regime.replace('_', ' ')
            legend_elements.append(plt.Line2D([0], [0], marker='s', color='w', # Use square marker
                                         markerfacecolor=color, markersize=8, # Smaller marker
                                         label=f"{label_prefix}{label}", linestyle='None')) # No line

    # Add EMA lines if plotted
    if 'tf_ewma_fast' in signals_df.columns:
         legend_elements.append(plt.Line2D([0], [0], color='#00FFFF', lw=1.5, label='EMA Fast'))
    if 'tf_ewma_slow' in signals_df.columns:
         legend_elements.append(plt.Line2D([0], [0], color='#FF00FF', lw=1.5, label='EMA Slow'))

    # Add crossover markers if plotted
    if hasattr(main_ax, 'custom_legend_elements'):
         legend_elements.extend(main_ax.custom_legend_elements)
    # else: # Add default crossover legend items if not added via markers (e.g., no crossovers found)
    #      legend_elements.append(plt.Line2D([0], [0], marker='X', color='w', markerfacecolor='#9932CC', markersize=8, label='Bullish Cross', linestyle='None'))
    #      legend_elements.append(plt.Line2D([0], [0], marker='X', color='w', markerfacecolor='#00008B', markersize=8, label='Bearish Cross', linestyle='None'))


    # Add trade markers if plotted
    if trades_list and not args.no_trades:
        legend_elements.append(plt.Line2D([0], [0], marker='^', color='w', markerfacecolor='green', markersize=6, label='Long Entry', linestyle='None'))
        legend_elements.append(plt.Line2D([0], [0], marker='v', color='w', markerfacecolor='red', markersize=6, label='Short Entry', linestyle='None'))
        legend_elements.append(plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', markersize=5, label='Trade Exit', linestyle='None'))

    # Determine number of columns dynamically (aim for ~5-7 items per row)
    ncol = max(1, min(7, (len(legend_elements) + 4) // 5))

    # Create the main legend placed ABOVE the plot, below the title
    legend = fig.legend(handles=legend_elements,
                        loc='upper center',         # Position relative to figure top-center
                        bbox_to_anchor=(0.5, 0.96),  # Anchor legend's top slightly HIGHER (closer to figure top)
                        ncol=ncol,                  # Dynamic columns
                        frameon=True,
                        fancybox=True,
                        shadow=True,
                        fontsize=9)

    # Adjust subplot parameters to push plot DOWN, making space for legend at the top
    fig.subplots_adjust(top=0.80) # Further reduce top margin to make more space above plot

    # --- 6. Save Plot ---
    try:
        logger.info("Saving plot...")
        fig.savefig(output_path, dpi=300, bbox_inches='tight') # Increased from 200 to 300 DPI
        plt.close(fig) # Close figure to free memory
        logger.info(f"Plot saved successfully to {output_path}")
    except Exception as e:
        logger.error(f"Failed to save plot to {output_path}: {e}")
        plt.close(fig)

# --- New Plotting Function for Price vs SMA ---

def plot_price_vs_sma_comparison(
    signals_df: pd.DataFrame,
    sma_column_name: str,
    output_path: Path,
    title: str = "Price vs. GMS Slope SMA Comparison"
):
    """
    Generates a plot comparing the 'close' price against a specified SMA.

    Args:
        signals_df: DataFrame with DatetimeIndex and 'close' column.
        sma_column_name: The exact name of the SMA column to plot.
        output_path: Path object for saving the plot image.
        title: Title for the plot.
    """
    logger.info(f"Attempting to generate Price vs SMA comparison plot: {output_path}")

    # --- Verify Required Columns ---
    if 'close' not in signals_df.columns:
        logger.error("Comparison plot requires 'close' column in signals_df. Skipping.")
        return
    if sma_column_name not in signals_df.columns:
        logger.warning(f"SMA column '{sma_column_name}' not found in signals_df. "
                       f"This column must be generated upstream (e.g., by SignalCalculator) "
                       f"and included in the signals data to create the comparison plot. Skipping.")
        # Explicitly state assumption based on user clarification
        logger.warning(f"Proceeding under the assumption that '{sma_column_name}' represents "
                       f"the SMA used for the GMS detector's slope calculation, as per clarification.")
        return # Skip plotting if the required SMA column is missing

    logger.info(f"Found required columns ('close', '{sma_column_name}'). Generating comparison plot...")

    fig, ax = plt.subplots(figsize=(16, 8)) # Create a new figure and axes

    # Plotting the data
    ax.plot(signals_df.index, signals_df['close'], label='Actual Price (Close)', color='blue', linewidth=1.0)
    ax.plot(signals_df.index, signals_df[sma_column_name], label=f'SMA ({sma_column_name})', color='orange', linewidth=1.5)

    # Formatting the plot
    ax.set_title(title, fontsize=16)
    ax.set_xlabel("Time", fontsize=12)
    ax.set_ylabel("Price", fontsize=12)
    ax.legend(fontsize=10)
    ax.grid(True, linestyle='--', alpha=0.6)

    # Improve date formatting on X-axis
    # Check timeframe difference to adjust format
    time_diff = signals_df.index.to_series().diff().median()
    if time_diff >= pd.Timedelta(days=1):
        date_format = '%Y-%m-%d'
    elif time_diff >= pd.Timedelta(hours=1):
         date_format = '%Y-%m-%d %H:%M' # Suitable for 1h, 4h
    else:
         date_format = '%H:%M:%S' # For shorter timeframes

    ax.xaxis.set_major_formatter(mdates.DateFormatter(date_format))
    plt.xticks(rotation=45) # Rotate labels for better readability

    # Adjust layout
    fig.tight_layout()

    # --- Save Plot ---
    try:
        logger.info(f"Saving comparison plot to {output_path}...")
        fig.savefig(output_path, dpi=200, bbox_inches='tight')
        plt.close(fig) # Close figure to free memory
        logger.info(f"Comparison plot saved successfully.")
    except Exception as e:
        logger.error(f"Failed to save comparison plot to {output_path}: {e}")
        plt.close(fig)

# --- Main Execution ---

def main():
    parser = argparse.ArgumentParser(description="Visualize Backtest Results")
    # Make signals/trades/output arguments optional with None default to allow auto-detection
    parser.add_argument("--signals", default=None, help="Path to the signals data file (Parquet)")
    parser.add_argument("--trades", default=None, help="Path to the trades data file (JSON)")
    parser.add_argument("--output", default=None, help="Path to save the output plot PNG file")
    parser.add_argument("--start", default=None, help="Start date (YYYY-MM-DD) for plotting range")
    parser.add_argument("--end", default=None, help="End date (YYYY-MM-DD) for plotting range")
    parser.add_argument("--title", default="Backtest Visualization", help="Plot title")
    # Remove default indicators from args, will be determined by config
    parser.add_argument("--indicators", nargs='*', default=None, help="[DEPRECATED] List of indicators to plot (now controlled by config.yaml)")
    parser.add_argument("--auto", action="store_true", help="Automatically use the latest backtest files")
    parser.add_argument("--max-points", type=int, default=None, help="Maximum number of data points to plot (e.g., 500). Helps with performance.")
    parser.add_argument("--sample", action="store_true", help="Sample data instead of using the most recent points when using --max-points")
    parser.add_argument("--no-trades", action="store_true", help="Skip adding trade markers to the plot")
    parser.add_argument("--trade-labels", action="store_true", help="Add trade labels to the plot")
    # Add argument for the comparison plot SMA column name
    parser.add_argument("--sma-col", default="sma_gms_slope", help="Name of the SMA column for the comparison plot (must exist in signals data)")
    # Add the missing argument for the comparison plot output path
    parser.add_argument("--comparison-output", default=None, help="Path to save the comparison plot PNG file (Price vs SMA)")
    parser.add_argument("--config", default=str(CONFIG_PATH), help="Path to config YAML file")
    parser.add_argument("--logdir", default=LOGS_DIR, help="Path to logs directory for auto-detection")

    args = parser.parse_args()

    # --- Load Configuration ---
    config_path = Path(args.config)
    config = {}
    gms_mapping_active = False
    gms_state_map = None
    try:
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        logger.info(f"Loaded config from {config_path}")

        # --- Load GMS Mapping (if applicable) ---
        gms_mapping_active = config.get('regime', {}).get('gms_use_three_state_mapping', False)
        detector_type = config.get('regime', {}).get('detector_type', 'rule_based')

        if gms_mapping_active and detector_type == 'granular_microstructure':
            # Construct path relative to the main config file's directory
            mapping_file_path_rel = config.get('regime', {}).get('gms_mapping_file', '../configs/gms_state_mapping.yaml') # Default relative path
            mapping_file_path = os.path.abspath(os.path.join(config_path.parent, mapping_file_path_rel))
            logger.info(f"GMS 3-state mapping is ACTIVE. Attempting to load map from: {mapping_file_path}")
            try:
                with open(mapping_file_path, 'r') as f_map:
                    yaml_data = yaml.safe_load(f_map)
                    if yaml_data and 'state_map' in yaml_data and isinstance(yaml_data['state_map'], dict):
                        gms_state_map = yaml_data['state_map']
                        logger.info(f"Successfully loaded GMS state map: {gms_state_map}")
                    else:
                        logger.error("Failed to load GMS state map: 'state_map' key missing or not a dictionary in YAML.")
                        gms_mapping_active = False # Disable mapping if file is invalid
            except FileNotFoundError:
                logger.error(f"GMS state mapping file not found at {mapping_file_path}. Disabling 3-state mapping.")
                gms_mapping_active = False # Disable mapping if file not found
            except yaml.YAMLError as e:
                logger.error(f"Error parsing GMS state mapping YAML file: {e}. Disabling 3-state mapping.")
                gms_mapping_active = False # Disable mapping on parse error
        else:
             logger.info(f"GMS 3-state mapping is INACTIVE (Flag: {gms_mapping_active}, Detector: {detector_type}).")

    except FileNotFoundError:
         logger.warning(f"Main config file not found at {config_path}. Proceeding with defaults. GMS mapping disabled.")
         gms_mapping_active = False
    except yaml.YAMLError as e:
         logger.error(f"Error parsing main config file {config_path}: {e}. Proceeding with defaults. GMS mapping disabled.")
         config = {}
         gms_mapping_active = False

    # Use logdir from argument or config, fallback to default
    log_dir = args.logdir or config.get("log_dir", LOGS_DIR)
    logger.info(f"Auto-detecting latest backtest files in {log_dir}")
    signals_path, trades_path = find_latest_backtest_files(log_dir)
    if signals_path is None or trades_path is None:
        logger.error("Could not auto-detect latest backtest files. Please specify paths manually.")
        return

    signals_path = Path(args.signals) if args.signals else Path(signals_path)
    trades_path = Path(args.trades) if args.trades else Path(trades_path)
    # Determine output paths, providing defaults if not specified
    if args.output:
        output_path = Path(args.output)
    else:
        # Default output path based on signals file name
        output_path = signals_path.with_name(f"{signals_path.stem}_visualization.png")
        logger.info(f"--output not specified, defaulting to: {output_path}")

    if args.comparison_output:
        comparison_output_path = Path(args.comparison_output)
    else:
        # Default comparison output path based on signals file name
        comparison_output_path = signals_path.with_name(f"{signals_path.stem}_comparison.png")
        logger.info(f"--comparison-output not specified, defaulting to: {comparison_output_path}")

    # --- Determine Indicators to Plot based on Config ---
    indicators_to_plot = []
    if config.get('visualization', {}).get('panels', {}).get('show_ma_slope', False): # Default to False if key missing
        indicators_to_plot.append('ma_slope')
    if config.get('visualization', {}).get('panels', {}).get('show_obi', False):
        indicators_to_plot.append('obi_smoothed')
    if config.get('visualization', {}).get('panels', {}).get('show_fear_greed', False):
        indicators_to_plot.append('fear_greed_idx') # Corrected column name

    # Warn if --indicators arg was used, as it's now ignored
    if args.indicators is not None:
        logger.warning("Command line argument --indicators is deprecated and ignored. Panel visibility is now controlled by config.yaml.")

    logger.info(f"Indicators selected for plotting based on config: {indicators_to_plot}")


    # --- Load Signals Data (Parquet) ---
    logger.info(f"Loading signals data from: {signals_path}")
    try:
        # Assuming parquet saved with index=True
        signals_df = pd.read_parquet(signals_path)
        # Ensure index is DatetimeIndex if not already
        if not isinstance(signals_df.index, pd.DatetimeIndex):
             # Attempt conversion assuming index holds parsable datetime info
             signals_df.index = pd.to_datetime(signals_df.index)
             logger.info("Converted DataFrame index to DatetimeIndex.")
        # Ensure timezone is consistent (e.g., UTC) - important for matching trades
        if signals_df.index.tz is None:
             signals_df.index = signals_df.index.tz_localize('UTC')
        else:
             signals_df.index = signals_df.index.tz_convert('UTC')

        # --- VERIFY REQUIRED COLUMNS ---
        required_cols = ['open', 'high', 'low', 'close'] # Base requirements ONLY
        # Check if volume is required by config visualization section
        volume_required_by_config = config.get('visualization', {}).get('require_volume', False)
        volume_present = 'volume' in signals_df.columns

        if volume_required_by_config:
            if not volume_present:
                logger.warning("Config requires 'volume' for visualization, but column is missing. Volume panel will not be shown.")
            else:
                logger.info("Volume column is required by config and present.")
        else:
            logger.info("Volume column is OPTIONAL for visualization based on config.")
            if not volume_present:
                logger.warning("Volume column not found in data. Volume plot elements will be skipped.")
            else:
                 logger.info("Optional volume column found.")

        if 'tf_ewma_fast' not in signals_df.columns: logger.warning("Column 'tf_ewma_fast' not found for plotting.")
        if 'tf_ewma_slow' not in signals_df.columns: logger.warning("Column 'tf_ewma_slow' not found for plotting.")
        if 'regime' not in signals_df.columns: logger.warning("Column 'regime' not found. Regime background shading will be skipped.")
        # Add checks for indicators_to_plot if needed (already handled inside plot func)

        # Check ONLY base requirements before exiting
        missing_req = [col for col in required_cols if col not in signals_df.columns]
        if missing_req:
             logger.error(f"Signals file missing required base columns: {missing_req}. Cannot plot.")
             return # Exit if basic OHLC missing

        logger.info(f"Signals data loaded successfully. Shape: {signals_df.shape}")

    except Exception as e:
        logger.error(f"Failed to load signals Parquet file: {e}", exc_info=True)
        return

    # --- Load Trades Data (JSON) ---
    logger.info(f"Loading trades data from: {trades_path}")
    trades_list = [] # Default to empty list
    if trades_path and trades_path.exists(): # Check if file exists, allow plotting without trades
        try:
            with open(trades_path, 'r') as f:
                trades_list = json.load(f)
            # --- VERIFY TRADE KEYS (optional but good practice) ---
            required_trade_keys = ['entry_time', 'exit_time', 'entry', 'exit', 'type']
            if trades_list and not all(key in trades_list[0] for key in required_trade_keys):
                 logger.warning(f"Trades file might be missing expected keys ({required_trade_keys}). Plotting may fail.")
            logger.info(f"Trades data loaded successfully. Count: {len(trades_list)}")
        except Exception as e:
            logger.error(f"Failed to load trades JSON file: {e}", exc_info=True)
            # Continue without trades if loading fails, but log error
    else:
         logger.warning(f"Trades file not found at {trades_path}. Plotting without trade markers.")


    # --- Filter Data by Date Range (Ensure timezone awareness) ---
    if args.start:
        start_dt = pd.to_datetime(args.start).tz_localize('UTC') # Assume UTC
        signals_df = signals_df[signals_df.index >= start_dt]
    if args.end:
        end_dt = pd.to_datetime(args.end).tz_localize('UTC') # Assume UTC
        # Ensure the end date includes the whole day if needed
        end_dt = end_dt + pd.Timedelta(days=1) - pd.Timedelta(seconds=1)
        signals_df = signals_df[signals_df.index <= end_dt]
        
    if signals_df.empty:
         logger.warning("No signals data remaining after date filtering. Cannot plot.")
         return
         
    # Handle large dataset - limit number of points if requested
    original_length = len(signals_df)
    max_points_arg = args.max_points
    max_points_config = config.get('visualization', {}).get('appearance', {}).get('max_points', None)
    # Prioritize command line arg over config for max_points
    max_points = max_points_arg if max_points_arg is not None else max_points_config

    if max_points and len(signals_df) > max_points:
        logger.info(f"Dataset is large ({len(signals_df)} points). Limiting to {max_points} points (Source: {'CLI' if max_points_arg is not None else 'Config'}).")

        # First identify timestamps of all trades to ensure we don't lose them
        trade_timestamps = set()
        for trade in trades_list:
            entry_time_dt = pd.to_datetime(trade['entry_time'], unit='s', utc=True)
            exit_time_dt = pd.to_datetime(trade['exit_time'], unit='s', utc=True)
            # Add timestamps if they're within our data range
            if entry_time_dt >= signals_df.index.min() and entry_time_dt <= signals_df.index.max():
                trade_timestamps.add(entry_time_dt)
            if exit_time_dt >= signals_df.index.min() and exit_time_dt <= signals_df.index.max():
                trade_timestamps.add(exit_time_dt)
                
        if args.sample:
            # Sample data at regular intervals but include trade timestamps
            sample_indices = set(range(0, len(signals_df), len(signals_df)//max_points))

            # Add indices of trade timestamps
            for ts in trade_timestamps:
                if ts in signals_df.index:
                    idx = signals_df.index.get_loc(ts)
                    sample_indices.add(idx)
                else:
                    # Find nearest
                    idx = signals_df.index.get_indexer([ts], method='nearest')[0]
                    if idx >= 0:
                        sample_indices.add(idx)
                        
            # Convert to list, sort, and take max_points
            sample_indices = sorted(list(sample_indices))[:max_points]
            signals_df = signals_df.iloc[sample_indices]
            logger.info(f"Sampled data at regular intervals with trade points included. New size: {len(signals_df)}")
        else:
            # Use the most recent points
            signals_df = signals_df.iloc[-max_points:]
            logger.info(f"Using the {max_points} most recent data points.")

    # --- Generate Plot ---
    # Create a copy before potentially modifying for the first plot
    signals_df_copy = signals_df.copy()

    plot_backtest_results(
        signals_df=signals_df_copy, # Use the copy for the main plot
        trades_list=trades_list,
        output_path=output_path,
        title=args.title,
        plot_indicators=indicators_to_plot,
        args=args,
        viz_config=config.get('visualization', {}),
        gms_mapping_active=gms_mapping_active, # Pass mapping status
        gms_state_map=gms_state_map           # Pass loaded map
    )

    # --- Generate Comparison Plot ---
    # Use the original signals_df for the comparison plot
    # The SMA column name is provided via args.sma_col
    plot_price_vs_sma_comparison(
        signals_df=signals_df,
        sma_column_name=args.sma_col,
        output_path=comparison_output_path,
        title=f"Price vs SMA ({args.sma_col}) Comparison"
    )

if __name__ == "__main__":
    main()