#!/usr/bin/env python3
"""
Merge regime cache quarters into full year file
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from pathlib import Path

# Temporary files from quarters
temp_files = []

# Read Q1
q1_file = Path("data/precomputed_regimes/regimes_2024_q1.parquet")
if q1_file.exists():
    q1 = pd.read_parquet(q1_file)
    print(f"Q1: {len(q1)} hours, {q1.index.min()} to {q1.index.max()}")
    temp_files.append(q1)
else:
    print("Q1 file not found")

# Read Q2
q2_file = Path("data/precomputed_regimes/regimes_2024_q2.parquet")
if q2_file.exists():
    q2 = pd.read_parquet(q2_file)
    print(f"Q2: {len(q2)} hours, {q2.index.min()} to {q2.index.max()}")
    temp_files.append(q2)
else:
    print("Q2 file not found")

# Read Q3
q3_file = Path("data/precomputed_regimes/regimes_2024_q3.parquet")
if q3_file.exists():
    q3 = pd.read_parquet(q3_file)
    print(f"Q3: {len(q3)} hours, {q3.index.min()} to {q3.index.max()}")
    temp_files.append(q3)
else:
    print("Q3 file not found")

# Read Q4 (current file)
q4_file = Path("data/precomputed_regimes/regimes_2024.parquet")
if q4_file.exists():
    q4 = pd.read_parquet(q4_file)
    print(f"Q4: {len(q4)} hours, {q4.index.min()} to {q4.index.max()}")
    temp_files.append(q4)
else:
    print("Q4 file not found")

# Merge all quarters
if temp_files:
    full_year = pd.concat(temp_files, axis=0)
    full_year = full_year.sort_index()
    
    # Remove duplicates if any
    full_year = full_year[~full_year.index.duplicated(keep='first')]
    
    print(f"\nMerged: {len(full_year)} hours total")
    print(f"Date range: {full_year.index.min()} to {full_year.index.max()}")
    
    # Save merged file
    output_file = Path("data/precomputed_regimes/regimes_2024.parquet")
    full_year.to_parquet(output_file, compression='snappy')
    
    print(f"\nSaved to: {output_file}")
    print(f"File size: {output_file.stat().st_size / 1024:.1f} KB")
    
    # Verify specific dates
    print("\nVerifying specific dates:")
    test_dates = ['2024-04-02', '2024-07-15', '2024-10-15']
    for date in test_dates:
        mask = full_year.index.date == pd.to_datetime(date).date()
        count = mask.sum()
        print(f"  {date}: {count} hours")
else:
    print("No files to merge!")