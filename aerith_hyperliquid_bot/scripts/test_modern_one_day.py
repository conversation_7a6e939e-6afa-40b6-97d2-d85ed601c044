#!/usr/bin/env python3
"""
Test Modern System - One Day Only
=================================

Run just one day to test if the system works.
"""

import sys
from pathlib import Path
from datetime import datetime
import json

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

# Apply duplicate timestamp fix
from fix_duplicate_timestamps import patch_modern_data_loader
patch_modern_data_loader()

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine


def main():
    print("="*60)
    print("MODERN SYSTEM - ONE DAY TEST")
    print("="*60)
    
    # Just test Jan 15, 2024 - we know this day generated trades
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 16)
    
    # Load configuration
    config_path = project_root / "configs/overrides/modern_system_v2_complete.yaml"
    config = load_config(config_path=str(config_path))
    
    print(f"📅 Date: {start_date.strftime('%Y-%m-%d')}")
    print(f"⚙️  Config: modern_system_v2_complete.yaml")
    print()
    
    try:
        # Create modern backtesting engine
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        # Run backtest
        print("🚀 Starting backtest...\n")
        results = engine.run_backtest()
        
        # Show results
        total_trades = results['performance']['total_trades']
        total_return = results['performance']['total_return']
        
        print("\n" + "="*60)
        print("📊 RESULTS")
        print("="*60)
        print(f"Total Trades: {total_trades}")
        print(f"Total Return: {total_return:.4%}")
        
        if total_trades > 0:
            print("\n✅ SUCCESS! Modern system is generating trades.")
            
            # Show trade details
            print(f"\nTRADE DETAILS:")
            for i, trade in enumerate(results['trades'], 1):
                print(f"{i}. {trade['timestamp']} - {trade['direction'].upper()} "
                      f"@ {trade['entry_price']:.2f} (regime: {trade['regime']})")
        else:
            print("\n❌ FAILURE! Still generating 0 trades.")
        
        # Save results
        with open('modern_one_day_test.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: modern_one_day_test.json")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()