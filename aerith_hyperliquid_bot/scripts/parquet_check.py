import pandas as pd
import os
import glob
import re
from datetime import timedelta
import pyarrow.parquet as pq

def analyze_parquet_file(file_path):
    """Analyze a single parquet file for L2 data availability"""
    try:
        # Read all columns from the parquet file
        df = pd.read_parquet(file_path)
        
        # Extract filename for reporting
        filename = os.path.basename(file_path)
        
        # Basic stats
        print(f"\n{'='*70}")
        print(f"FILE: {filename}")
        print(f"{'='*70}")
        
        # Check if timestamp column exists
        if 'timestamp' not in df.columns:
            print("ERROR: No 'timestamp' column found in the parquet file!")
            return
            
        # Ensure timestamp is datetime
        if not pd.api.types.is_datetime64_dtype(df['timestamp']):
            print("Converting timestamp column to datetime...")
            df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Get date range
        min_time = df['timestamp'].min()
        max_time = df['timestamp'].max()
        date_str = min_time.strftime('%Y-%m-%d')
        
        print(f"Date covered: {date_str}")
        print(f"Time range: {min_time.strftime('%H:%M:%S')} to {max_time.strftime('%H:%M:%S')}")
        print(f"Duration: {max_time - min_time}")
        print(f"Total records: {len(df)}")
        
        # Check for hourly coverage
        hours_in_day = 24
        expected_hours = set(range(hours_in_day))
        actual_hours = set(df['timestamp'].dt.hour.unique())
        missing_hours = sorted(expected_hours - actual_hours)
        
        print(f"\n{'='*20} TIME COVERAGE {'='*20}")
        print("Hourly coverage:")
        print(f"- Hours present: {sorted(actual_hours)}")
        if missing_hours:
            print(f"- MISSING HOURS: {missing_hours}")
        else:
            print("- Complete hourly coverage")
        
        # ENHANCED: Check for all depth levels
        print(f"\n{'='*20} ORDER BOOK DEPTH {'='*20}")
        all_cols = df.columns.tolist()
        
        # Find all depth columns
        bid_price_cols = sorted([col for col in all_cols if col.startswith('bid_price_')])
        bid_size_cols = sorted([col for col in all_cols if col.startswith('bid_size_')])
        ask_price_cols = sorted([col for col in all_cols if col.startswith('ask_price_')])
        ask_size_cols = sorted([col for col in all_cols if col.startswith('ask_size_')])
        
        # Show depth coverage
        print("Order book depth levels found:")
        print(f"- Bid price levels ({len(bid_price_cols)}): {bid_price_cols}")
        print(f"- Bid size levels ({len(bid_size_cols)}): {bid_size_cols}")
        print(f"- Ask price levels ({len(ask_price_cols)}): {ask_price_cols}")
        print(f"- Ask size levels ({len(ask_size_cols)}): {ask_size_cols}")
        
        # ENHANCED: Check for metrics
        print(f"\n{'='*20} DERIVED METRICS {'='*20}")
        metric_patterns = [
            'volume_proxy', 'imbalance', 'bid_ask_ratio', 
            'weighted_mid', 'slope', 'concentration', 'volatility',
            'efficiency', 'asymmetry'
        ]
        found_metrics = []
        for pattern in metric_patterns:
            matching_cols = [col for col in all_cols if pattern in col]
            if matching_cols:
                found_metrics.extend(matching_cols)
        
        if found_metrics:
            print(f"Found {len(found_metrics)} derived metrics:")
            for metric in sorted(found_metrics):
                # Count non-NaN values and show percentage
                valid_count = df[metric].count()
                valid_pct = (valid_count / len(df)) * 100
                print(f"- {metric}: {valid_count:,} values ({valid_pct:.1f}%)")
                
                # Basic stats if numeric
                if pd.api.types.is_numeric_dtype(df[metric]):
                    # Handle potential all-NaN cases
                    if valid_count > 0:
                        print(f"  Range: {df[metric].min():.6f} to {df[metric].max():.6f}, Mean: {df[metric].mean():.6f}")
        else:
            print("No derived metrics found in the file")
        
        # Time gap analysis
        print(f"\n{'='*20} TIME GAP ANALYSIS {'='*20}")
        df = df.sort_values('timestamp')
        df['time_diff'] = df['timestamp'].diff()
        
        # Get statistics on gaps
        median_gap = df['time_diff'].median()
        max_gap = df['time_diff'].max()
        large_gaps = df[df['time_diff'] > timedelta(minutes=5)]
        
        print("Timestamp gaps:")
        print(f"- Median gap between records: {median_gap}")
        print(f"- Maximum gap: {max_gap}")
        
        if not large_gaps.empty:
            print(f"- Found {len(large_gaps)} gaps > 5 minutes:")
            for _, row in large_gaps.head(5).iterrows():
                gap_start = row['timestamp'] - row['time_diff']
                print(f"  Gap from {gap_start} to {row['timestamp']} ({row['time_diff']})")
            if len(large_gaps) > 5:
                print(f"  ... and {len(large_gaps)-5} more gaps")
                
        # Check first hour specifically
        first_hour = min_time.replace(minute=0, second=0, microsecond=0)
        next_hour = first_hour + timedelta(hours=1)
        first_hour_data = df[(df['timestamp'] >= first_hour) & (df['timestamp'] < next_hour)]
        
        print(f"\nFirst hour coverage ({first_hour.strftime('%H:00')} - {next_hour.strftime('%H:00')}):")
        print(f"- Records in first hour: {len(first_hour_data)}")
        
        if len(first_hour_data) > 0:
            first_record_time = first_hour_data['timestamp'].min()
            minutes_into_hour = (first_record_time - first_hour).total_seconds() / 60
            print(f"- First record: {first_record_time.strftime('%H:%M:%S')} ({minutes_into_hour:.1f} minutes into hour)")
        else:
            print("- NO DATA for first hour")
            
        # Return some key stats for multi-file analysis
        return {
            'date': date_str,
            'min_time': min_time,
            'max_time': max_time,
            'missing_hours': missing_hours,
            'record_count': len(df),
            'metrics_count': len(found_metrics) if 'found_metrics' in locals() else 0,
            'depth_levels': max(len(bid_price_cols), len(ask_price_cols))
        }
            
    except Exception as e:
        print(f"\nError analyzing {file_path}: {e}")
        import traceback
        traceback.print_exc()
        return None

def display_metrics_summary(file_path):
    """Display detailed summary of metrics in a parquet file"""
    print(f"\n{'='*70}")
    print(f"METRICS ANALYSIS FOR: {os.path.basename(file_path)}")
    print(f"{'='*70}")
    
    try:
        # Read parquet file
        df = pd.read_parquet(file_path)
        
        # Identify metrics columns vs raw data columns
        all_cols = df.columns.tolist()
        
        # Standard L2 columns (raw data)
        l2_patterns = ['bid_price_', 'bid_size_', 'ask_price_', 'ask_size_', 'timestamp', 'mid_price', 'spread']
        raw_cols = []
        for pattern in l2_patterns:
            raw_cols.extend([col for col in all_cols if col.startswith(pattern)])
        
        # Metrics are everything else
        metric_cols = [col for col in all_cols if col not in raw_cols]
        
        if not metric_cols:
            print("No metrics columns found in this file")
            return
        
        print(f"Found {len(metric_cols)} metrics columns\n")
        
        # Group metrics by type
        metric_groups = {
            "Volume-Based": ["volume", "imbalance", "bid_ask_ratio"],
            "Price-Based": ["weighted", "mid", "impact"],
            "Liquidity Structure": ["slope", "concentration"],
            "Volatility & Efficiency": ["volatility", "efficiency", "asymmetry"]
        }
        
        # Display metrics by group
        for group_name, patterns in metric_groups.items():
            group_metrics = []
            for pattern in patterns:
                group_metrics.extend([col for col in metric_cols if pattern in col])
            
            if group_metrics:
                print(f"\n--- {group_name} Metrics ---")
                for metric in sorted(group_metrics):
                    # Get basic stats
                    valid_count = df[metric].count()
                    valid_pct = (valid_count / len(df)) * 100
                    
                    print(f"{metric}:")
                    print(f"- Valid values: {valid_count:,}/{len(df):,} ({valid_pct:.1f}%)")
                    
                    if valid_count > 0:
                        # Calculate stats safely
                        try:
                            stats = {
                                'min': df[metric].min(),
                                'max': df[metric].max(),
                                'mean': df[metric].mean(),
                                'median': df[metric].median(),
                                'std': df[metric].std()
                            }
                            print(f"- Range: {stats['min']:.6f} to {stats['max']:.6f}")
                            print(f"- Mean: {stats['mean']:.6f}, Median: {stats['median']:.6f}, StdDev: {stats['std']:.6f}")
                            
                            # Check for unusual values
                            if abs(stats['mean']) > 1000 or abs(stats['std'] / stats['mean']) > 10:
                                print("  ⚠️ Possible unusual values detected")
                        except Exception as e:
                            print(f"- Error calculating stats: {e}")
        
        # Correlation analysis between key metrics
        print(f"\n{'='*30} CORRELATION ANALYSIS {'='*30}")
        try:
            key_metrics = [col for col in metric_cols if any(p in col for p in ['imbalance', 'bid_ask_ratio', 'weighted_mid', 'slope', 'volatility'])]
            if len(key_metrics) >= 2:
                corr_matrix = df[key_metrics].corr()
                print("Correlation between key metrics:")
                pd.set_option('display.precision', 2)
                print(corr_matrix)
            else:
                print("Not enough metrics for correlation analysis")
        except Exception as e:
            print(f"Error in correlation analysis: {e}")
            
    except Exception as e:
        print(f"Error analyzing metrics: {e}")
        import traceback
        traceback.print_exc()

def analyze_depth_in_parquet(file_path):
    """Analyze a parquet file specifically for L2 depth information"""
    print(f"\n{'='*70}")
    print(f"DEPTH ANALYSIS FOR: {os.path.basename(file_path)}")
    print(f"{'='*70}")
    
    try:
        # Read ALL columns from the parquet file
        df_sample = pd.read_parquet(file_path)
        
        # Get all column names and print them for verification
        all_cols = df_sample.columns.tolist()
        print(f"Total columns in file: {len(all_cols)}")
        print(f"All columns: {all_cols}")
        
        # Check for depth level columns
        bid_price_cols = sorted([col for col in all_cols if col.startswith('bid_price_')])
        bid_size_cols = sorted([col for col in all_cols if col.startswith('bid_size_')])
        ask_price_cols = sorted([col for col in all_cols if col.startswith('ask_price_')])
        ask_size_cols = sorted([col for col in all_cols if col.startswith('ask_size_')])
        
        max_bid_depth = len(bid_price_cols)
        max_ask_depth = len(ask_price_cols)
        
        print("\nDepth levels found:")
        print(f"- Bid price levels: {max_bid_depth} {bid_price_cols}")
        print(f"- Bid size levels: {len(bid_size_cols)} {bid_size_cols}")
        print(f"- Ask price levels: {max_ask_depth} {ask_price_cols}")
        print(f"- Ask size levels: {len(ask_size_cols)} {ask_size_cols}")
        
        # Check if depth columns actually contain data
        if max_bid_depth >= 5:
            # Sample the data presence for level 5
            non_na_bid5 = df_sample['bid_price_5'].notna().sum()
            pct_bid5 = (non_na_bid5 / len(df_sample)) * 100
            print("\nData presence at level 5:")
            print(f"- bid_price_5: {non_na_bid5}/{len(df_sample)} records ({pct_bid5:.1f}%)")
        
        # Check for random sample of values at different levels
        if max_bid_depth >= 5 and max_ask_depth >= 5:
            print("\nSample values from random record:")
            sample_idx = min(1000, len(df_sample) - 1)  # Get a record somewhere in the file
            sample_row = df_sample.iloc[sample_idx]
            sample_time = sample_row['timestamp']
            print(f"Record at {sample_time}:")
            
            # Print bid levels
            print("Bid Levels (Price, Size):")
            for level in range(1, min(6, max_bid_depth + 1)):
                price_col = f'bid_price_{level}'
                size_col = f'bid_size_{level}'
                price = sample_row.get(price_col, 'N/A')
                size = sample_row.get(size_col, 'N/A')
                print(f"  Level {level}: {price}, {size}")
                
            # Print ask levels
            print("Ask Levels (Price, Size):")
            for level in range(1, min(6, max_ask_depth + 1)):
                price_col = f'ask_price_{level}'
                size_col = f'ask_size_{level}'
                price = sample_row.get(price_col, 'N/A')
                size = sample_row.get(size_col, 'N/A')
                print(f"  Level {level}: {price}, {size}")
                
        return {
            'file': os.path.basename(file_path),
            'max_bid_depth': max_bid_depth,
            'max_ask_depth': max_ask_depth,
            'has_level_5': max_bid_depth >= 5 and max_ask_depth >= 5
        }
            
    except Exception as e:
        print(f"Error in depth analysis: {e}")
        return None

def analyze_depth_in_text_file(file_path):
    """Analyze a text file specifically for L2 order book depth information"""
    print(f"\n{'='*70}")
    print(f"TEXT FILE DEPTH ANALYSIS FOR: {os.path.basename(file_path)}")
    print(f"{'='*70}")
    
    try:
        # Read the text file
        with open(file_path, 'r') as f:
            content = f.read()
            
        # Basic stats
        print(f"File size: {os.path.getsize(file_path) / (1024*1024):.2f} MB")
        
        # Try to detect file format based on content patterns
        print("\nAttempting to detect file format...")
        
        # Check for common patterns in different text file formats
        json_pattern = re.search(r'[\{\[].*?[\}\]]', content[:1000])
        csv_pattern = re.search(r'^[^,\n]*,[^,\n]*,[^,\n]*', content[:1000])
        pipe_delimited = re.search(r'^[^\|\n]*\|[^\|\n]*\|[^\|\n]*', content[:1000])
        
        if json_pattern:
            print("Detected JSON-like format")
            format_type = "json"
        elif csv_pattern:
            print("Detected CSV-like format")
            format_type = "csv"
        elif pipe_delimited:
            print("Detected pipe-delimited format")
            format_type = "pipe"
        else:
            print("Could not automatically detect format, assuming whitespace-delimited")
            format_type = "whitespace"
        
        # Print sample lines for inspection
        print("\nSample content (first 3 lines):")
        lines = content.split('\n')[:3]
        for i, line in enumerate(lines):
            if line.strip():
                print(f"Line {i+1}: {line[:100]}...")
            
        # JSON-specific analysis
        if format_type == "json":
            print("\nAnalyzing JSON format orderbook data...")
            
            # Extract first valid JSON object
            import json
            valid_lines = [line for line in lines if line.strip()]
            if valid_lines:
                try:
                    # Parse the first line as JSON
                    sample_line = valid_lines[0]
                    if sample_line.startswith('{}"time"'):  # Fix malformed JSON
                        sample_line = sample_line[2:]
                    
                    sample_obj = json.loads(sample_line)
                    print("Successfully parsed JSON object")
                    
                    # Analyze structure
                    print("\nJSON Structure Analysis:")
                    
                    # Check for Hyperliquid format
                    if "raw" in sample_obj and "data" in sample_obj["raw"]:
                        data = sample_obj["raw"]["data"]
                        print("Found Hyperliquid orderbook format: raw -> data -> levels")
                        
                        # Check for levels array (Hyperliquid-specific)
                        if "levels" in data and isinstance(data["levels"], list):
                            levels = data["levels"]
                            print(f"Found 'levels' array with {len(levels)} sides")
                            
                            # In Hyperliquid format, typically:
                            # levels[0] = bids
                            # levels[1] = asks
                            if len(levels) >= 2:
                                bids = levels[0]
                                asks = levels[1]
                                print(f"Found {len(bids)} bid levels and {len(asks)} ask levels")
                                
                                # Print sample of the bids/asks
                                if bids:
                                    print("\nSample bids (first 5):")
                                    for i, bid in enumerate(bids[:5]):
                                        print(f"  Level {i+1}: Price={bid['px']}, Size={bid['sz']}, Orders={bid['n']}")
                                
                                if asks:
                                    print("\nSample asks (first 5):")
                                    for i, ask in enumerate(asks[:5]):
                                        print(f"  Level {i+1}: Price={ask['px']}, Size={ask['sz']}, Orders={ask['n']}")
                    
                    # Count maximum levels by examining multiple records
                    print("\nAnalyzing depth across multiple records...")
                    max_ask_levels = 0
                    max_bid_levels = 0
                    records_analyzed = 0
                    
                    # Sample multiple records (up to 100)
                    for line in lines[:100]:
                        if not line.strip():
                            continue
                            
                        try:
                            # Fix malformed JSON if needed
                            if line.startswith('{}"time"'):
                                line = line[2:]
                                
                            obj = json.loads(line)
                            if ("raw" in obj and "data" in obj["raw"] and 
                                "levels" in obj["raw"]["data"] and 
                                isinstance(obj["raw"]["data"]["levels"], list) and
                                len(obj["raw"]["data"]["levels"]) >= 2):
                                
                                levels = obj["raw"]["data"]["levels"]
                                bids = levels[0]
                                asks = levels[1]
                                max_bid_levels = max(max_bid_levels, len(bids))
                                max_ask_levels = max(max_ask_levels, len(asks))
                                records_analyzed += 1
                        except Exception as e:
                            print(f"Error parsing record: {e}")
                            continue
                    
                    print(f"Analyzed {records_analyzed} records")
                    print(f"Maximum bid levels found: {max_bid_levels}")
                    print(f"Maximum ask levels found: {max_ask_levels}")
                    
                    # Overall depth assessment
                    if max_ask_levels >= 5 and max_bid_levels >= 5:
                        print("\nCONCLUSION: Data has sufficient depth (5+ levels) for OBI calculation")
                    else:
                        print("\nCONCLUSION: Data may have insufficient depth for 5-level OBI calculation")
                    
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON: {e}")
            
        # Provide guidance based on findings
        print("\nAnalysis Summary:")
        if format_type == "json":
            print("- JSON format Hyperliquid orderbook data detected")
            if 'max_bid_levels' in locals():
                if max_bid_levels >= 5 and max_ask_levels >= 5:
                    print(f"- Found sufficient depth (bids: {max_bid_levels}, asks: {max_ask_levels} levels)")
                else:
                    print(f"- Limited depth detected (bids: {max_bid_levels}, asks: {max_ask_levels} levels)")
            
        return {
            'file': os.path.basename(file_path),
            'format': format_type,
            'max_bid_levels': max_bid_levels if 'max_bid_levels' in locals() else 0,
            'max_ask_levels': max_ask_levels if 'max_ask_levels' in locals() else 0
        }
            
    except Exception as e:
        print(f"Error analyzing text file: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_parquet_schema(file_path):
    """Show the raw schema of a parquet file"""
    print(f"Checking schema for: {file_path}")
    try:
        parquet_file = pq.ParquetFile(file_path)
        schema = parquet_file.schema
        print(f"Total columns in schema: {len(schema.names)}")
        print("All columns in parquet file:")
        
        # Group columns by type
        column_groups = {
            "Timestamp": [],
            "Order Book Price": [],
            "Order Book Size": [],
            "Price References": [],
            "Derived Metrics": []
        }
        
        for col in schema.names:
            if col == 'timestamp':
                column_groups["Timestamp"].append(col)
            elif any(col.startswith(p) for p in ['bid_price_', 'ask_price_']):
                column_groups["Order Book Price"].append(col)
            elif any(col.startswith(p) for p in ['bid_size_', 'ask_size_']):
                column_groups["Order Book Size"].append(col)
            elif col in ['mid_price', 'spread', 'weighted_mid']:
                column_groups["Price References"].append(col)
            else:
                column_groups["Derived Metrics"].append(col)
        
        # Print organized column list
        for group, columns in column_groups.items():
            if columns:
                print(f"\n{group} ({len(columns)}):")
                for col in sorted(columns):
                    print(f"- {col}")
                    
        # Get file size
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
        print(f"\nFile size: {file_size_mb:.2f} MB")
        
    except Exception as e:
        print(f"Error checking schema: {e}")

def main():
    # Add options for different types of analysis
    print("\n1. Basic parquet file analysis (ENHANCED)")
    print("2. Depth analysis for L2 data in parquet file")
    print("3. Depth analysis for L2 data in text file")
    print("4. Check parquet schema (ENHANCED)")
    print("5. Metrics summary and analysis")
    choice = input("Select option (1/2/3/4/5): ").strip()
    
    if choice == '5':
        # Metrics analysis
        file_path = input("Enter parquet file path: ").strip()
        if not file_path:
            print("Please provide a valid parquet file path")
            return
            
        display_metrics_summary(file_path)
    elif choice == '4':
        # Check parquet schema (enhanced)
        file_path = input("Enter parquet file path: ").strip()
        if not file_path:
            print("Please provide a valid parquet file path")
            return
            
        check_parquet_schema(file_path)
    elif choice == '3':
        # Text file depth analysis
        file_path = input("Enter text file path: ").strip()
        if not file_path:
            print("Please provide a valid text file path")
            return
            
        analyze_depth_in_text_file(file_path)
    elif choice == '2':
        # Parquet depth analysis
        file_path = input("Enter specific parquet file path: ").strip()
        if not file_path:
            print("Please provide a valid parquet file path")
            return
            
        analyze_depth_in_parquet(file_path)
    else:
        # Enhanced analysis code
        file_path = input("Enter specific parquet file path (or leave empty to scan a directory): ").strip()
        
        if file_path:
            # Analyze single file
            analyze_parquet_file(file_path)
        else:
            # Scan a directory
            directory = input("Enter directory path containing parquet files: ").strip()
            if not directory:
                directory = "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw2"
                print(f"Using default directory: {directory}")
            
            # Find all parquet files
            parquet_files = sorted(glob.glob(os.path.join(directory, "*.parquet")))
            
            if not parquet_files:
                print(f"No parquet files found in {directory}")
                return
                
            print(f"Found {len(parquet_files)} parquet files")
            
            # Analyze each file
            summary = []
            for file_path in parquet_files:
                result = analyze_parquet_file(file_path)
                if result:
                    summary.append(result)
                    
            # Print overall summary
            if summary:
                print("\n\n" + "="*80)
                print("OVERALL SUMMARY:")
                print("="*80)
                
                complete_days = sum(1 for s in summary if not s['missing_hours'])
                incomplete_days = len(summary) - complete_days
                
                print(f"Total files analyzed: {len(summary)}")
                print(f"Complete days (no missing hours): {complete_days}")
                print(f"Incomplete days: {incomplete_days}")
                
                depth_summary = {}
                for s in summary:
                    depth = s.get('depth_levels', 0)
                    depth_summary[depth] = depth_summary.get(depth, 0) + 1
                
                print("\nDepth level distribution:")
                for depth, count in sorted(depth_summary.items()):
                    print(f"- {depth} levels: {count} files ({count/len(summary)*100:.1f}%)")
                
                metrics_summary = {}
                for s in summary:
                    metrics = s.get('metrics_count', 0)
                    metrics_summary[metrics] = metrics_summary.get(metrics, 0) + 1
                
                print("\nMetrics distribution:")
                for metric_count, file_count in sorted(metrics_summary.items()):
                    print(f"- {metric_count} metrics: {file_count} files ({file_count/len(summary)*100:.1f}%)")

if __name__ == "__main__":
    main()
