#!/usr/bin/env python3
"""
Verify Modern Data Pipeline
==========================

This script verifies that the modern system's data pipeline is working correctly:
1. Checks features_1s/ directory structure
2. Verifies data completeness for 2024
3. Confirms schema includes required fields
4. Tests data loading performance

Usage:
    python3 scripts/verify_modern_data.py
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import logging

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_features_1s_directory(base_path: Path) -> <PERSON><PERSON>[bool, List[str]]:
    """Check if features_1s directory exists and has expected structure."""
    issues = []
    
    features_path = base_path / "features_1s"
    if not features_path.exists():
        issues.append(f"Features directory not found: {features_path}")
        return False, issues
    
    # Check for date directories
    date_dirs = sorted([d for d in features_path.iterdir() if d.is_dir()])
    if not date_dirs:
        issues.append("No date directories found in features_1s/")
        return False, issues
    
    logger.info(f"Found {len(date_dirs)} date directories")
    
    # Check structure of first date directory
    sample_date_dir = date_dirs[0]
    hour_files = list(sample_date_dir.glob("features_*.parquet"))
    
    if not hour_files:
        issues.append(f"No feature files found in {sample_date_dir}")
        return False, issues
    
    logger.info(f"Sample directory {sample_date_dir.name} has {len(hour_files)} hourly files")
    
    return True, issues


def verify_data_completeness(base_path: Path, year: int = 2024) -> Tuple[float, List[str]]:
    """Check data completeness for specified year."""
    issues = []
    features_path = base_path / "features_1s"
    
    start_date = datetime(year, 1, 1)
    end_date = datetime(year, 12, 31)
    
    total_days = (end_date - start_date).days + 1
    found_days = 0
    missing_dates = []
    
    current_date = start_date
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        date_dir = features_path / date_str
        
        if date_dir.exists():
            found_days += 1
        else:
            missing_dates.append(date_str)
        
        current_date += timedelta(days=1)
    
    completeness = found_days / total_days * 100
    
    if missing_dates:
        # Only report first and last few missing dates to avoid spam
        if len(missing_dates) > 10:
            sample = missing_dates[:5] + ['...'] + missing_dates[-5:]
            issues.append(f"Missing {len(missing_dates)} dates, including: {sample}")
        else:
            issues.append(f"Missing dates: {missing_dates}")
    
    logger.info(f"Data completeness for {year}: {completeness:.1f}% ({found_days}/{total_days} days)")
    
    return completeness, issues


def check_required_fields(base_path: Path) -> Tuple[bool, List[str], List[str]]:
    """Verify that data files contain all required fields AFTER loading."""
    issues = []
    
    # Required fields for modern system AFTER column mapping
    required_fields = [
        'timestamp', 'open', 'high', 'low', 'close', 'volume',
        'volume_imbalance',  # Mapped from obi_smoothed
        'spread_mean', 'spread_std',
        'atr', 'atr_percent',  # Mapped from atr_14_sec, atr_percent_sec
        'ma_slope', 'ma_slope_ema_30s',   # Momentum indicators
    ]
    
    # Optional fields
    optional_fields = ['adx', 'funding_rate', 'obi_smoothed_10']
    
    try:
        # Test loading through the modern data loader
        from hyperliquid_bot.modern.data_loader import ModernDataLoader
        from hyperliquid_bot.config.settings import load_config
        
        config = load_config()
        config.data_paths.feature_1s_dir = str(base_path / "features_1s")
        
        loader = ModernDataLoader(config)
        
        # Load a small sample
        start_date = datetime(2024, 3, 1)
        end_date = datetime(2024, 3, 1, 1, 0, 0)  # Just 1 hour
        
        df = loader.load_data(start_date, end_date)
        
        if df.empty:
            issues.append("Loaded DataFrame is empty")
            return False, issues, []
        
        actual_fields = list(df.columns)
        
        # Check for required fields in LOADED data
        missing_fields = []
        for field in required_fields:
            if field not in actual_fields:
                missing_fields.append(field)
        
        # Note optional missing fields
        missing_optional = []
        for field in optional_fields:
            if field not in actual_fields:
                missing_optional.append(field)
        
        if missing_fields:
            issues.append(f"Missing required fields: {missing_fields}")
        
        if missing_optional:
            logger.info(f"Missing optional fields: {missing_optional}")
        
        logger.info(f"Schema check - Found {len(actual_fields)} fields after loading")
        logger.info(f"Sample fields: {sorted(actual_fields)[:15]}...")
        
        return len(missing_fields) == 0, issues, actual_fields
        
    except Exception as e:
        issues.append(f"Error checking loaded data schema: {str(e)}")
        return False, issues, []


def test_data_loading_performance(base_path: Path) -> Tuple[float, List[str]]:
    """Test how quickly we can load data."""
    issues = []
    
    try:
        # Import modern data loader
        from hyperliquid_bot.modern.data_loader import ModernDataLoader
        
        # Create config
        config = load_config()
        config.data_paths.feature_1s_dir = str(base_path / "features_1s")
        
        # Initialize loader
        loader = ModernDataLoader(config)
        
        # Test loading one week of data
        start_date = datetime(2024, 3, 1)
        end_date = datetime(2024, 3, 7)
        
        import time
        start_time = time.time()
        
        df = loader.load_data(start_date, end_date)
        
        load_time = time.time() - start_time
        
        if df.empty:
            issues.append("Loaded DataFrame is empty")
            return 0.0, issues
        
        rows_per_second = len(df) / load_time
        
        logger.info(f"Loading performance:")
        logger.info(f"  - Loaded {len(df):,} rows in {load_time:.2f} seconds")
        logger.info(f"  - Speed: {rows_per_second:,.0f} rows/second")
        logger.info(f"  - Data shape: {df.shape}")
        
        # Check for data quality
        if df.isna().sum().sum() > 0:
            na_counts = df.isna().sum()
            na_cols = na_counts[na_counts > 0]
            issues.append(f"Found NaN values in columns: {list(na_cols.index)}")
        
        return load_time, issues
        
    except ImportError as e:
        issues.append(f"Cannot import ModernDataLoader: {str(e)}")
        return 0.0, issues
    except Exception as e:
        issues.append(f"Error during loading test: {str(e)}")
        return 0.0, issues


def verify_field_mappings() -> List[str]:
    """Verify critical field mappings between legacy and modern."""
    mappings = []
    
    mappings.append("Field Mapping Verification:")
    mappings.append("  Legacy 'imbalance' → Modern 'volume_imbalance'")
    mappings.append("  Legacy 'atr' → Modern 'atr_14_sec' (1-second)")
    mappings.append("  Legacy 'atr_percent' → Modern 'atr_percent_sec'")
    mappings.append("  Legacy 'obi' → Modern 'obi_smoothed_10' (or similar)")
    
    return mappings


def main():
    """Run all verification checks."""
    logger.info("=== Modern Data Pipeline Verification ===\n")
    
    # Load config to get data paths
    config = load_config()
    # Use the parent of feature_1s_dir as base path
    feature_1s_path = Path(config.data_paths.feature_1s_dir)
    base_path = feature_1s_path.parent
    
    all_issues = []
    
    # 1. Check directory structure
    logger.info("1. Checking features_1s directory structure...")
    exists, issues = check_features_1s_directory(base_path)
    all_issues.extend(issues)
    
    if not exists:
        logger.error("CRITICAL: features_1s directory not found. Cannot proceed.")
        return 1
    
    # 2. Verify data completeness
    logger.info("\n2. Verifying data completeness for 2024...")
    completeness, issues = verify_data_completeness(base_path, 2024)
    all_issues.extend(issues)
    
    if completeness < 50:
        logger.warning(f"WARNING: Only {completeness:.1f}% data coverage for 2024")
    
    # 3. Check required fields
    logger.info("\n3. Checking schema and required fields...")
    schema_ok, issues, actual_fields = check_required_fields(base_path)
    all_issues.extend(issues)
    
    # 4. Test loading performance
    logger.info("\n4. Testing data loading performance...")
    load_time, issues = test_data_loading_performance(base_path)
    all_issues.extend(issues)
    
    # 5. Show field mappings
    logger.info("\n5. Field mapping information:")
    for mapping in verify_field_mappings():
        logger.info(mapping)
    
    # Summary
    logger.info("\n=== VERIFICATION SUMMARY ===")
    logger.info(f"Directory exists: {'✓' if exists else '✗'}")
    logger.info(f"Data completeness: {completeness:.1f}%")
    logger.info(f"Schema valid: {'✓' if schema_ok else '✗'}")
    logger.info(f"Loading works: {'✓' if load_time > 0 else '✗'}")
    
    if all_issues:
        logger.info(f"\nFound {len(all_issues)} issues:")
        for i, issue in enumerate(all_issues, 1):
            logger.warning(f"  {i}. {issue}")
        
        if any('CRITICAL' in issue or 'Missing required fields' in issue for issue in all_issues):
            logger.error("\nCRITICAL issues found. Modern system may not work correctly.")
            return 1
    else:
        logger.info("\n✓ All checks passed! Modern data pipeline is ready.")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())