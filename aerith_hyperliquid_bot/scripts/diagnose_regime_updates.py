#!/usr/bin/env python3
"""
Diagnostic script to verify if Modern system is actually updating regimes every 60s
or if it's using cached hourly regimes (the smoking gun).
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
from hyperliquid_bot.modern.data_loader import ModernDataLoader
from hyperliquid_bot.modern.registry import ModernSystemRegistry
import yaml

class RegimeDiagnosticEngine:
    def __init__(self, config):
        self.config = config
        self.regime_update_count = 0
        self.regime_changes = []
        self.last_regime = None
        self.update_timestamps = []
        
        # Initialize components
        self.data_loader = ModernDataLoader(config)
        self.registry = ModernSystemRegistry()
        self.registry.initialize(config)
        
    def run_diagnostic(self, start_date: datetime, end_date: datetime):
        """Run diagnostic to count actual regime updates"""
        print(f"\n=== REGIME UPDATE DIAGNOSTIC ===")
        print(f"Period: {start_date} to {end_date}")
        print(f"Expected updates (60s): ~{int((end_date - start_date).total_seconds() / 60)}")
        print(f"Expected updates (hourly): ~{int((end_date - start_date).total_seconds() / 3600)}")
        print("-" * 50)
        
        # Track regime calculations
        detector = self.registry.get_detector()
        
        # Check if detector has caching
        if hasattr(detector, 'cache') or hasattr(detector, '_cache'):
            print("⚠️  WARNING: Detector has cache attribute!")
        
        # Simulate minute-by-minute
        current = start_date
        hourly_regimes = {}
        
        while current <= end_date:
            # Load data for this minute
            try:
                # Check what data is actually loaded
                data = self.data_loader.load_data('BTC', current, current + timedelta(minutes=1))
                
                if data is not None and not data.empty:
                    # Force regime calculation (bypass any cache)
                    regime_result = detector.compute_regime_live(data)
                    
                    if isinstance(regime_result, tuple):
                        regime, confidence = regime_result
                    else:
                        regime = regime_result
                        confidence = None
                    
                    # Track update
                    self.regime_update_count += 1
                    self.update_timestamps.append(current)
                    
                    # Track hourly regime
                    hour_key = current.replace(minute=0, second=0, microsecond=0)
                    if hour_key not in hourly_regimes:
                        hourly_regimes[hour_key] = []
                    hourly_regimes[hour_key].append(regime)
                    
                    # Track changes
                    if regime != self.last_regime and self.last_regime is not None:
                        self.regime_changes.append({
                            'time': current,
                            'from': self.last_regime,
                            'to': regime,
                            'confidence': confidence
                        })
                        print(f"  Regime change at {current}: {self.last_regime} → {regime}")
                    
                    self.last_regime = regime
                    
            except Exception as e:
                pass  # Skip errors for missing data
            
            # Move to next minute
            current += timedelta(minutes=1)
            
            # Progress update every hour
            if current.minute == 0:
                hours_done = (current - start_date).total_seconds() / 3600
                print(f"\r  Progress: {hours_done:.0f} hours processed...", end='')
        
        print("\n")
        
        # Analyze results
        self._analyze_results(hourly_regimes)
        
    def _analyze_results(self, hourly_regimes):
        """Analyze diagnostic results"""
        print("\n=== DIAGNOSTIC RESULTS ===")
        print(f"Total regime updates: {self.regime_update_count}")
        print(f"Total regime changes: {len(self.regime_changes)}")
        
        # Check update frequency
        if self.update_timestamps:
            # Calculate average time between updates
            deltas = []
            for i in range(1, len(self.update_timestamps)):
                delta = (self.update_timestamps[i] - self.update_timestamps[i-1]).total_seconds()
                deltas.append(delta)
            
            if deltas:
                avg_delta = np.mean(deltas)
                print(f"\nAverage seconds between updates: {avg_delta:.1f}")
                
                if avg_delta > 300:  # More than 5 minutes
                    print("❌ CRITICAL: Updates are NOT happening every 60s!")
                    print("   This confirms Modern is using cached/hourly regimes!")
                elif avg_delta > 120:  # More than 2 minutes
                    print("⚠️  WARNING: Updates are less frequent than expected")
                else:
                    print("✅ Updates appear to be happening frequently")
        
        # Check regime stability within hours
        print("\n=== HOURLY REGIME STABILITY ===")
        stable_hours = 0
        changing_hours = 0
        
        for hour, regimes in hourly_regimes.items():
            unique_regimes = set(regimes)
            if len(unique_regimes) == 1:
                stable_hours += 1
            else:
                changing_hours += 1
                print(f"  {hour}: {len(unique_regimes)} different regimes within hour")
        
        print(f"\nStable hours (no regime change): {stable_hours}")
        print(f"Changing hours (regime changed): {changing_hours}")
        
        if changing_hours == 0:
            print("\n❌ SMOKING GUN: No intra-hour regime changes!")
            print("   Modern is definitely using hourly cached regimes!")
        elif changing_hours < stable_hours * 0.1:  # Less than 10% of hours have changes
            print("\n⚠️  WARNING: Very few intra-hour changes")
            print("   Suggests mostly using cached hourly data")
        
        # Show regime distribution
        if self.regime_changes:
            all_regimes = [self.last_regime] + [r['from'] for r in self.regime_changes]
            regime_counts = pd.Series(all_regimes).value_counts()
            total = sum(regime_counts.values)
            
            print("\n=== REGIME DISTRIBUTION ===")
            for regime, count in regime_counts.items():
                pct = count / total * 100
                print(f"{regime}: {pct:.1f}%")

def main():
    # Load configs
    with open('config.yaml', 'r') as f:
        base_config = yaml.safe_load(f)
    
    with open('configs/overrides/modern_system_v2_complete.yaml', 'r') as f:
        modern_config = yaml.safe_load(f)
    
    config = {**base_config, **modern_config}
    
    # Run diagnostic for 1 day
    start_date = datetime(2024, 1, 15)  # Mid-January for good data
    end_date = datetime(2024, 1, 16)    # Just 1 day for diagnostic
    
    print("="*60)
    print("REGIME UPDATE FREQUENCY DIAGNOSTIC")
    print("="*60)
    print("\nThis will verify if Modern system is:")
    print("1. Actually updating every 60s (expected)")
    print("2. Using cached hourly regimes (the bug)")
    
    engine = RegimeDiagnosticEngine(config)
    engine.run_diagnostic(start_date, end_date)
    
    print("\n" + "="*60)
    print("CONCLUSION:")
    print("="*60)
    print("\nIf you see:")
    print("- ~1440 updates → System is updating every minute ✅")
    print("- ~24 updates → System is using hourly cache ❌")
    print("- No intra-hour regime changes → Definitely cached ❌")

if __name__ == "__main__":
    main()