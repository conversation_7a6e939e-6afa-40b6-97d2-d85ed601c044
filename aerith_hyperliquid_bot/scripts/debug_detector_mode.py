#!/usr/bin/env python3
"""
Debug script to understand why detector mode resolution is failing.
"""

import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.core.detector_factory import get_regime_detector

def debug_detector_mode():
    """Debug the detector mode resolution process."""
    print("Debug Detector Mode Resolution")
    print("="*50)
    
    # Load config
    config = load_config("configs/base.yaml")
    
    print(f"config.regime.detector_type: {getattr(config.regime, 'detector_type', 'NOT_SET')}")
    print(f"hasattr(config, 'gms'): {hasattr(config, 'gms')}")
    
    if hasattr(config, 'gms'):
        print(f"hasattr(config.gms, 'mode'): {hasattr(config.gms, 'mode')}")
        print(f"hasattr(config.gms, 'detector_type'): {hasattr(config.gms, 'detector_type')}")
        if hasattr(config.gms, 'detector_type'):
            print(f"config.gms.detector_type VALUE: '{getattr(config.gms, 'detector_type', 'NOT_SET')}'")
        print(f"config.gms attributes: {[attr for attr in dir(config.gms) if not attr.startswith('_')]}")
    
    # Create detector
    detector = get_regime_detector(config)
    
    print(f"\nCreated detector: {type(detector).__name__}")
    print(f"Detector mode: {getattr(detector, 'detector_mode', 'N/A')}")
    print(f"Detector type: {getattr(detector, 'detector_type', 'N/A')}")
    
    # Check thresholds being used
    if hasattr(detector, 'thresholds'):
        print(f"\nThresholds in use:")
        for k, v in detector.thresholds.items():
            print(f"  {k}: {v}")
    
    print(f"\nAdaptive thresholds enabled: {getattr(detector, 'adaptive_vol_threshold', None) is not None}")
    print(f"Cadence: {getattr(detector, 'cadence_sec', 'N/A')}s")

if __name__ == "__main__":
    debug_detector_mode()