#!/usr/bin/env python3
"""
Tune Modern System Thresholds
=============================

This script helps tune the modern system thresholds by testing
different combinations and finding optimal settings.
"""

import sys
from pathlib import Path
from datetime import datetime
import json
import yaml
from typing import Dict, List, Tuple

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine


def test_threshold_combination(
    base_config_path: str,
    threshold_overrides: Dict[str, float],
    start_date: datetime,
    end_date: datetime
) -> Dict:
    """
    Test a specific threshold combination.
    
    Args:
        base_config_path: Base config file
        threshold_overrides: Threshold values to override
        start_date: Test start date
        end_date: Test end date
        
    Returns:
        Results dictionary
    """
    # Load base config
    with open(base_config_path, 'r') as f:
        config_dict = yaml.safe_load(f)
    
    # Apply overrides
    for key, value in threshold_overrides.items():
        if '.' in key:
            # Handle nested keys
            parts = key.split('.')
            current = config_dict
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            current[parts[-1]] = value
        else:
            config_dict[key] = value
    
    # Create temporary config file
    temp_config = Path(f"temp_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml")
    with open(temp_config, 'w') as f:
        yaml.dump(config_dict, f)
    
    try:
        # Load config and run test
        config = load_config(config_path=str(temp_config))
        
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        results = engine.run_backtest()
        
        # Extract key metrics
        metrics = {
            'thresholds': threshold_overrides,
            'total_trades': results['performance']['total_trades'],
            'total_return': results['performance']['total_return'],
            'win_rate': results['performance']['win_rate'],
            'regime_updates': len(results.get('regime_history', [])),
        }
        
        # Get regime distribution
        if results.get('regime_history'):
            states = [r['state'] for r in results['regime_history']]
            total = len(states)
            metrics['chop_percentage'] = states.count('CHOP') / total * 100 if total > 0 else 0
            metrics['bull_percentage'] = states.count('BULL') / total * 100 if total > 0 else 0
            metrics['bear_percentage'] = states.count('BEAR') / total * 100 if total > 0 else 0
        
        return metrics
        
    finally:
        # Clean up temp config
        if temp_config.exists():
            temp_config.unlink()


def main():
    """Main tuning function."""
    print("=" * 80)
    print("MODERN SYSTEM THRESHOLD TUNING")
    print("=" * 80)
    
    # Base configuration
    base_config = 'configs/overrides/modern_system.yaml'
    
    # Test period (1 week for quick tests)
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 8)
    
    # Define threshold combinations to test
    test_combinations = [
        # Current settings
        {
            'name': 'Current',
            'overrides': {
                'regime.gms_mom_strong_thresh': 2.5,
                'regime.gms_mom_weak_thresh': 0.5,
                'regime.gms_vol_high_thresh': 0.015,
                'regime.gms_vol_low_thresh': 0.005,
            }
        },
        # More sensitive momentum
        {
            'name': 'Sensitive Momentum',
            'overrides': {
                'regime.gms_mom_strong_thresh': 1.5,
                'regime.gms_mom_weak_thresh': 0.3,
                'regime.gms_vol_high_thresh': 0.015,
                'regime.gms_vol_low_thresh': 0.005,
            }
        },
        # Calibrated settings
        {
            'name': 'Calibrated',
            'overrides': {
                'regime.gms_mom_strong_thresh': 50.0,
                'regime.gms_mom_weak_thresh': 20.0,
                'regime.gms_vol_high_thresh': 0.012,
                'regime.gms_vol_low_thresh': 0.004,
            }
        },
        # More restrictive
        {
            'name': 'Restrictive',
            'overrides': {
                'regime.gms_mom_strong_thresh': 75.0,
                'regime.gms_mom_weak_thresh': 30.0,
                'regime.gms_vol_high_thresh': 0.018,
                'regime.gms_vol_low_thresh': 0.006,
            }
        },
        # Balanced
        {
            'name': 'Balanced',
            'overrides': {
                'regime.gms_mom_strong_thresh': 40.0,
                'regime.gms_mom_weak_thresh': 15.0,
                'regime.gms_vol_high_thresh': 0.010,
                'regime.gms_vol_low_thresh': 0.003,
            }
        }
    ]
    
    results = []
    
    print(f"Testing {len(test_combinations)} threshold combinations...")
    print(f"Period: {start_date.date()} to {end_date.date()}\n")
    
    for i, test in enumerate(test_combinations):
        print(f"Test {i+1}/{len(test_combinations)}: {test['name']}")
        print("-" * 40)
        
        try:
            metrics = test_threshold_combination(
                base_config,
                test['overrides'],
                start_date,
                end_date
            )
            
            metrics['name'] = test['name']
            results.append(metrics)
            
            print(f"✅ Trades: {metrics['total_trades']}, "
                  f"Return: {metrics['total_return']:.2%}, "
                  f"CHOP: {metrics.get('chop_percentage', 0):.1f}%\n")
            
        except Exception as e:
            print(f"❌ Failed: {e}\n")
            results.append({
                'name': test['name'],
                'error': str(e)
            })
    
    # Display results summary
    print("\n" + "=" * 80)
    print("RESULTS SUMMARY")
    print("=" * 80)
    
    print(f"\n{'Name':<20} {'Trades':<10} {'Return':<10} {'Win Rate':<10} {'CHOP %':<10}")
    print("-" * 70)
    
    for result in results:
        if 'error' not in result:
            print(f"{result['name']:<20} "
                  f"{result['total_trades']:<10} "
                  f"{result['total_return']:<10.2%} "
                  f"{result.get('win_rate', 0):<10.2%} "
                  f"{result.get('chop_percentage', 0):<10.1f}")
        else:
            print(f"{result['name']:<20} ERROR")
    
    # Find best configuration
    best = None
    best_score = -999
    
    for result in results:
        if 'error' not in result and result['total_trades'] > 0:
            # Score based on return and reasonable trade count
            trade_penalty = abs(result['total_trades'] - 20) * 0.001  # Target ~20 trades/week
            score = result['total_return'] - trade_penalty
            
            if score > best_score:
                best = result
                best_score = score
    
    if best:
        print(f"\n✅ BEST CONFIGURATION: {best['name']}")
        print(f"   Momentum thresholds: {best['thresholds']['regime.gms_mom_strong_thresh']}/{best['thresholds']['regime.gms_mom_weak_thresh']}")
        print(f"   Volatility thresholds: {best['thresholds']['regime.gms_vol_high_thresh']}/{best['thresholds']['regime.gms_vol_low_thresh']}")
        print(f"   Results: {best['total_trades']} trades, {best['total_return']:.2%} return")
        
        # Save best config
        output_file = 'configs/overrides/modern_optimized.yaml'
        
        # Load base config and apply best thresholds
        with open(base_config, 'r') as f:
            config_dict = yaml.safe_load(f)
        
        for key, value in best['thresholds'].items():
            parts = key.split('.')
            current = config_dict
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            current[parts[-1]] = value
        
        # Add optimization metadata
        config_dict['_optimization_metadata'] = {
            'optimized_date': datetime.now().isoformat(),
            'test_period': f"{start_date.date()} to {end_date.date()}",
            'performance': {
                'trades': best['total_trades'],
                'return': best['total_return'],
                'win_rate': best.get('win_rate', 0)
            }
        }
        
        with open(output_file, 'w') as f:
            yaml.dump(config_dict, f, default_flow_style=False)
        
        print(f"\n💾 Optimized config saved to: {output_file}")
    
    # Save all results
    results_file = f"threshold_tuning_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"💾 All results saved to: {results_file}")


if __name__ == '__main__':
    main()