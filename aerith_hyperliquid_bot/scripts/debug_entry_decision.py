#!/usr/bin/env python3
"""
Debug Entry Decision Process
============================

Trace through the exact steps of entry decision making.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from datetime import datetime
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.tf_v3_modern import ModernTFV3Strategy

# Monkey patch to add debug logging
original_evaluate_entry = ModernTFV3Strategy.evaluate_entry

def debug_evaluate_entry(self, signals, regime):
    print("\n=== DEBUG: evaluate_entry called ===")
    print(f"Regime: {regime}")
    
    # Check regime_features
    regime_features = signals.get('regime_features', {})
    print(f"regime_features exists: {'regime_features' in signals}")
    if regime_features:
        print(f"  current_state: {regime_features.get('current_state')}")
        print(f"  current_confidence: {regime_features.get('current_confidence')}")
        print(f"  state_duration_minutes: {regime_features.get('state_duration_minutes')}")
        print(f"  risk_suppressed: {regime_features.get('risk_suppressed')}")
    
    # Check key signals
    print(f"\nKey signals:")
    print(f"  close: {signals.get('close')}")
    print(f"  ema_fast: {signals.get('ema_fast')}")
    print(f"  ema_slow: {signals.get('ema_slow')}")
    print(f"  ema_baseline: {signals.get('ema_baseline')}")
    print(f"  atr_14: {signals.get('atr_14')}")
    print(f"  atr_percent: {signals.get('atr_percent')}")
    
    # Step through conditions
    print(f"\nCondition checks:")
    
    # 1. Regime allowed?
    trend_states = self.trend_states
    regime_allowed = regime in trend_states
    print(f"1. Regime in trend_states: {regime_allowed}")
    
    if not regime_allowed:
        print("   -> Exit: regime not allowed")
        return None
    
    # 2. Regime stable?
    if regime_features:
        stable = self._is_regime_stable(regime_features)
        print(f"2. Regime stable: {stable}")
        if not stable:
            duration = regime_features.get('state_duration_minutes', 0)
            changes = regime_features.get('state_changes_1h', 0)
            confidence = regime_features.get('current_confidence', 0)
            print(f"   Duration: {duration} < {self.min_regime_duration_minutes}?")
            print(f"   Changes: {changes} > {self.max_regime_changes_1h}?")
            print(f"   Confidence: {confidence} < {self.min_regime_confidence}?")
            print("   -> Exit: regime not stable")
            return None
    else:
        print("2. No regime_features to check stability!")
        return None
    
    # 3. Risk suppressed?
    risk_suppressed = regime_features.get('risk_suppressed', False)
    print(f"3. Risk suppressed: {risk_suppressed}")
    if risk_suppressed:
        print("   -> Exit: risk suppressed")
        return None
    
    # 4. Indicators valid?
    ema_fast = signals.get('ema_fast')
    ema_slow = signals.get('ema_slow')
    ema_baseline = signals.get('ema_baseline')
    close_price = signals.get('close')
    
    indicators_valid = all(not pd.isna(x) for x in [ema_fast, ema_slow, ema_baseline, close_price])
    print(f"4. Indicators valid: {indicators_valid}")
    if not indicators_valid:
        print("   -> Exit: missing indicators")
        return None
    
    # 5. Entry direction
    bullish = ema_fast > ema_slow and close_price > ema_baseline
    bearish = ema_fast < ema_slow and close_price < ema_baseline
    
    print(f"5. Entry signals:")
    print(f"   EMA fast ({ema_fast:.2f}) vs slow ({ema_slow:.2f})")
    print(f"   Close ({close_price:.2f}) vs baseline ({ema_baseline:.2f})")
    print(f"   Bullish crossover: {bullish}")
    print(f"   Bearish crossover: {bearish}")
    
    # Call original
    result = original_evaluate_entry(self, signals, regime)
    
    if result:
        print(f"\n✅ ENTRY DECISION: {result}")
    else:
        print("\n❌ No entry decision")
    
    return result

# Apply monkey patch
ModernTFV3Strategy.evaluate_entry = debug_evaluate_entry

def main():
    print("=== Debug Entry Decision Process ===\n")
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Create strategy
    strategy = ModernTFV3Strategy(config)
    
    # Test with perfect signals including regime_features
    test_signals = {
        'close': 42000,
        'ema_fast': 41900,  # < ema_slow for short
        'ema_slow': 42100,
        'ema_baseline': 42200,  # close < baseline for short
        'atr_14': 200.0,
        'atr_percent': 0.5,
        'rsi': 40,
        'bb_upper': 42500,
        'bb_middle': 42000,
        'bb_lower': 41500,
        'volume': 1000000,
        'regime_features': {
            'current_state': 'Weak_Bear_Trend',
            'current_confidence': 0.8,
            'state_duration_minutes': 30,
            'risk_suppressed': False,
            'state_changes_1h': 2,
            'avg_momentum_1h': -0.001
        }
    }
    
    print("Testing with complete signals...")
    result = strategy.evaluate_entry(test_signals, 'Weak_Bear_Trend')
    
    # Now test without regime_features
    print("\n" + "="*50)
    print("\nTesting WITHOUT regime_features (simulating the bug)...")
    test_signals_no_rf = test_signals.copy()
    del test_signals_no_rf['regime_features']
    
    result2 = strategy.evaluate_entry(test_signals_no_rf, 'Weak_Bear_Trend')

if __name__ == "__main__":
    main()