#!/usr/bin/env python3
"""
Trace why we're only getting long trades after temporal alignment fixes.
Check EMA values, forecast calculations, and regime states.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from datetime import datetime
import pandas as pd
import json

# Load the backtest results
results_file = Path(__file__).parent.parent / "2024_backtest_with_fixes.json"
with open(results_file, 'r') as f:
    results = json.load(f)

print("\n" + "="*80)
print("TRACING LONG BIAS ISSUE - WHY NO SHORT TRADES?")
print("="*80)

# Analyze trades
trades = results.get('trades', [])
long_trades = [t for t in trades if t.get('direction') == 'long']
short_trades = [t for t in trades if t.get('direction') == 'short']

print(f"\nTrade Distribution:")
print(f"  Total trades: {len(trades)}")
print(f"  Long trades: {len(long_trades)} ({len(long_trades)/len(trades)*100:.1f}%)")
print(f"  Short trades: {len(short_trades)} ({len(short_trades)/len(trades)*100:.1f}%)")

# Check regime distribution in trades
regime_counts = {}
for trade in trades:
    regime = trade.get('entry_regime', 'Unknown')
    regime_counts[regime] = regime_counts.get(regime, 0) + 1

print(f"\nRegime Distribution in Trades:")
for regime, count in sorted(regime_counts.items()):
    print(f"  {regime}: {count} trades ({count/len(trades)*100:.1f}%)")

# Look for Bear regime trades
bear_regime_trades = [t for t in trades if 'Bear' in t.get('entry_regime', '')]
print(f"\nBear Regime Trades: {len(bear_regime_trades)}")
if bear_regime_trades:
    print("  Found bear regime trades but still took long positions!")
    for i, trade in enumerate(bear_regime_trades[:3]):
        print(f"\n  Example {i+1}:")
        print(f"    Entry time: {trade['entry_time']}")
        print(f"    Regime: {trade['entry_regime']}")
        print(f"    Direction: {trade['direction']} (should be short!)")

# Check signal details for some trades
print("\n" + "-"*80)
print("CHECKING SIGNAL DETAILS")
print("-"*80)

# Get a sample of trades from different months
sample_indices = [0, 50, 100, 150, 200] if len(trades) > 200 else range(min(5, len(trades)))

for idx in sample_indices:
    if idx < len(trades):
        trade = trades[idx]
        signal = trade.get('signal', {})
        
        print(f"\nTrade {idx+1} - {trade['entry_time']}:")
        print(f"  Regime: {trade['entry_regime']}")
        print(f"  Direction: {trade['direction']}")
        print(f"  Entry reason: {signal.get('entry_reason', 'N/A')}")
        print(f"  Confidence: {signal.get('confidence', 'N/A')}")
        print(f"  Regime confidence: {signal.get('regime_confidence', 'N/A')}")

# Theory: The strategy might be checking for wrong regime names
print("\n" + "-"*80)
print("HYPOTHESIS: REGIME NAME MISMATCH")
print("-"*80)

print("\nThe modern strategy expects regimes like:")
print("  - 'BULL' or 'BEAR' (simple names)")
print("\nBut the detector outputs:")
print("  - 'Strong_Bull_Trend', 'Weak_Bull_Trend', etc.")
print("\nThis mismatch might cause the strategy to only see bull regimes!")

# Check if we ever tried to enter shorts
print("\n" + "-"*80)
print("FORECAST ANALYSIS")
print("-"*80)

print("\nNote: The strategy requires:")
print("  - For LONG: EMA fast > slow AND forecast > threshold")
print("  - For SHORT: EMA fast < slow AND forecast < -threshold")
print("\nIf EMAs are always bullish (fast > slow), we'll never get short signals!")

# Summary
print("\n" + "="*80)
print("ROOT CAUSE ANALYSIS")
print("="*80)

print("\n1. REGIME DETECTION: Working correctly (we see bear regimes)")
print("2. SIGNAL GENERATION: Problem is here!")
print("   - Even in bear regimes, strategy takes long positions")
print("   - This suggests EMA conditions are overriding regime signals")
print("\n3. LIKELY CAUSE: After temporal alignment fixes:")
print("   - EMAs (20/50) are slower to react than regimes")
print("   - In 2024 bull market, EMAs stayed mostly bullish")
print("   - Even when regime turned bearish, EMAs still showed fast > slow")
print("   - Strategy requires BOTH regime AND EMA alignment for shorts")
print("\n4. THE FIX: Need to adjust entry logic to:")
print("   - Trust regime more than EMAs in strong regimes")
print("   - OR use faster EMAs that align better with 60s regime updates")
print("   - OR allow regime-only entries without full EMA confirmation")