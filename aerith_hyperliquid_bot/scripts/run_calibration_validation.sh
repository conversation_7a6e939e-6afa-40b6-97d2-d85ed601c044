#!/bin/bash
# Conservative Calibration Validation Script
# Runs the complete validation pipeline for the conservative modern system

echo "=================================="
echo "CONSERVATIVE CALIBRATION VALIDATION"
echo "=================================="
echo "Goal: Calibrate modern system to achieve 160-190 trades/year"
echo "Key Changes:"
echo "  - Momentum thresholds: 2.5/0.5 → 25.0/12.5 (10-25x increase)"
echo "  - State mapping: Weak_Bear_Trend → BEAR (enables Bear detection)"
echo "  - Strategy: TF-v2 → TF-v3 (modern pipeline)"
echo "  - Enhanced filtering for conservative approach"
echo ""

# Step 1: Test configurations
echo "Step 1: Testing configuration files..."
python scripts/test_calibration_configs.py
if [ $? -ne 0 ]; then
    echo "❌ Configuration tests failed! Please fix before proceeding."
    exit 1
fi
echo "✅ Configuration tests passed"
echo ""

# Step 2: Run validation backtest
echo "Step 2: Running validation backtest..."
echo "This will test the calibrated system on 2024 data..."
python scripts/validate_conservative_calibration.py
if [ $? -ne 0 ]; then
    echo "❌ Validation backtest failed!"
    exit 1
fi
echo "✅ Validation backtest completed"
echo ""

echo "=================================="
echo "VALIDATION COMPLETE"
echo "=================================="
echo "Check the validation_results/ directory for:"
echo "  - Backtest results (JSON)"
echo "  - Calibration report (YAML)"
echo ""
echo "Key metrics to verify:"
echo "  ✓ Trade frequency: 160-190 trades/year"
echo "  ✓ Bear regime detection: >0 Bear trades"
echo "  ✓ Regime accuracy: >70% on ground truth events"
echo "=================================="