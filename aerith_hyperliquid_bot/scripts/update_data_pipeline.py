#!/usr/bin/env python3
"""
Master script to update the complete data pipeline with Task R-101 ATR improvements.

This script:
1. Converts any remaining TXT files to Arrow format
2. Regenerates 1-second feature files with improved ATR calculation
3. Verifies the results

Usage:
    python scripts/update_data_pipeline.py --start-date 2025-03-01 --end-date 2025-03-22
"""

import os
import sys
import argparse
from pathlib import Path
from datetime import datetime, timedelta
import logging

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_txt_to_arrow_conversion(base_dir: str, overwrite: bool = False) -> bool:
    """
    Run TXT to Arrow conversion.

    Args:
        base_dir: Base directory containing TXT files
        overwrite: Whether to overwrite existing files

    Returns:
        True if successful
    """
    try:
        logger.info("=== STEP 1: Converting TXT files to Arrow format ===")

        import subprocess

        cmd = [
            sys.executable, "scripts/convert_txt_to_arrow.py",
            "--base-dir", base_dir
        ]

        if overwrite:
            cmd.append("--overwrite")

        result = subprocess.run(
            cmd,
            cwd=str(project_root),
            capture_output=True,
            text=True
        )

        if result.returncode == 0:
            logger.info("TXT to Arrow conversion completed successfully")
            logger.info(result.stdout)
            return True
        else:
            logger.error(f"TXT to Arrow conversion failed: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"Error running TXT to Arrow conversion: {e}")
        return False


def run_feature_regeneration(start_date: str, end_date: str, overwrite: bool = False) -> bool:
    """
    Run 1-second feature regeneration.

    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        overwrite: Whether to overwrite existing files

    Returns:
        True if successful
    """
    try:
        logger.info("=== STEP 2: Regenerating 1-second features with improved ATR ===")

        import subprocess

        cmd = [
            sys.executable, "scripts/regenerate_features_with_atr.py",
            "--start-date", start_date,
            "--end-date", end_date
        ]

        if overwrite:
            cmd.append("--overwrite")

        result = subprocess.run(
            cmd,
            cwd=str(project_root),
            capture_output=True,
            text=True
        )

        if result.returncode == 0:
            logger.info("Feature regeneration completed successfully")
            logger.info(result.stdout)
            return True
        else:
            logger.error(f"Feature regeneration failed: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"Error running feature regeneration: {e}")
        return False


def run_verification(start_date: str, end_date: str) -> bool:
    """
    Run ATR injection verification.

    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format

    Returns:
        True if verification passed
    """
    try:
        logger.info("=== STEP 3: Verifying ATR injection ===")

        import subprocess

        cmd = [
            sys.executable, "scripts/regenerate_features_with_atr.py",
            "--start-date", start_date,
            "--end-date", end_date,
            "--verify-only"
        ]

        result = subprocess.run(
            cmd,
            cwd=str(project_root),
            capture_output=True,
            text=True
        )

        if result.returncode == 0:
            logger.info("ATR verification completed successfully")
            logger.info(result.stdout)
            return True
        else:
            logger.error(f"ATR verification failed: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"Error running ATR verification: {e}")
        return False


def check_data_availability(start_date: str, end_date: str, config) -> dict:
    """
    Check what data is available for the date range.

    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        config: Configuration object

    Returns:
        Dictionary with availability information
    """
    logger.info("=== CHECKING DATA AVAILABILITY ===")

    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")

    raw_l2_dir = Path(config.data_paths.raw_l2_dir)
    features_1s_dir = Path(config.data_paths.feature_1s_dir)
    ohlcv_base_path = Path(config.data_paths.ohlcv_base_path) / "1h"

    availability = {
        'dates_with_arrow': [],
        'dates_with_txt': [],
        'dates_with_features': [],
        'dates_with_hourly_ohlcv': [],
        'dates_missing_data': []
    }

    current_dt = start_dt
    while current_dt <= end_dt:
        date_str = current_dt.strftime("%Y-%m-%d")

        # Check Arrow files
        arrow_dir = raw_l2_dir / date_str
        if arrow_dir.exists() and list(arrow_dir.glob("BTC_*_l2Book.arrow")):
            availability['dates_with_arrow'].append(date_str)

        # Check TXT files (old format)
        old_date_str = current_dt.strftime("%Y%m%d")
        txt_dir = raw_l2_dir / old_date_str
        if txt_dir.exists() and list(txt_dir.glob("BTC_*_l2Book.txt")):
            availability['dates_with_txt'].append(date_str)

        # Check 1-second features
        features_dir = features_1s_dir / date_str
        if features_dir.exists() and list(features_dir.glob("features_*.parquet")):
            availability['dates_with_features'].append(date_str)

        # Check hourly OHLCV
        ohlcv_file = ohlcv_base_path / f"{date_str}_1h.parquet"
        if ohlcv_file.exists():
            availability['dates_with_hourly_ohlcv'].append(date_str)

        # Check if date has no data at all
        has_arrow = date_str in availability['dates_with_arrow']
        has_txt = date_str in availability['dates_with_txt']
        if not has_arrow and not has_txt:
            availability['dates_missing_data'].append(date_str)

        current_dt += timedelta(days=1)

    # Log summary
    logger.info(f"Dates with Arrow files: {len(availability['dates_with_arrow'])}")
    logger.info(f"Dates with TXT files: {len(availability['dates_with_txt'])}")
    logger.info(f"Dates with 1s features: {len(availability['dates_with_features'])}")
    logger.info(f"Dates with hourly OHLCV: {len(availability['dates_with_hourly_ohlcv'])}")
    logger.info(f"Dates missing data: {len(availability['dates_missing_data'])}")

    if availability['dates_missing_data']:
        logger.warning(f"Missing data for dates: {availability['dates_missing_data']}")

    return availability


def main():
    parser = argparse.ArgumentParser(description="Update complete data pipeline with Task R-101 improvements")
    parser.add_argument("--start-date", default="2025-03-01",
                        help="Start date in YYYY-MM-DD format")
    parser.add_argument("--end-date", default="2025-03-22",
                        help="End date in YYYY-MM-DD format")
    parser.add_argument("--overwrite", action="store_true",
                        help="Overwrite existing files")
    parser.add_argument("--skip-conversion", action="store_true",
                        help="Skip TXT to Arrow conversion")
    parser.add_argument("--skip-features", action="store_true",
                        help="Skip feature regeneration")
    parser.add_argument("--verify-only", action="store_true",
                        help="Only run verification, skip processing")
    parser.add_argument("--convert-only", action="store_true",
                        help="Only convert TXT to Arrow, skip feature generation")

    args = parser.parse_args()

    # Load configuration
    try:
        config = load_config('configs/base.yaml')
    except Exception as e:
        logger.error(f"Failed to load configuration: {e}")
        sys.exit(1)

    logger.info(f"=== UPDATING DATA PIPELINE FOR {args.start_date} TO {args.end_date} ===")

    # Check data availability
    availability = check_data_availability(args.start_date, args.end_date, config)

    if args.verify_only:
        # Only run verification
        success = run_verification(args.start_date, args.end_date)
        sys.exit(0 if success else 1)

    success = True

    # Step 1: Convert TXT to Arrow (if needed)
    if not args.skip_conversion:
        if availability['dates_with_txt']:
            logger.info(f"Found {len(availability['dates_with_txt'])} dates with TXT files to convert")
            if not run_txt_to_arrow_conversion(str(config.data_paths.raw_l2_dir), args.overwrite):
                success = False
        else:
            logger.info("No TXT files found to convert")
    else:
        logger.info("Skipping TXT to Arrow conversion (--skip-conversion)")

    if args.convert_only:
        logger.info("=== TXT TO ARROW CONVERSION COMPLETED ===")
        sys.exit(0 if success else 1)

    # Step 2: Regenerate features
    if not args.skip_features and success:
        if not run_feature_regeneration(args.start_date, args.end_date, args.overwrite):
            success = False
    else:
        logger.info("Skipping feature regeneration")

    # Step 3: Verify results
    if success:
        if not run_verification(args.start_date, args.end_date):
            success = False

    if success:
        logger.info("=== DATA PIPELINE UPDATE COMPLETED SUCCESSFULLY ===")
        logger.info("✅ All 1-second feature files now have proper ATR values")
        logger.info("✅ TF-v3 and Continuous GMS strategies can now use ATR features")
        logger.info("✅ Raw TXT files converted to Arrow format")
        logger.info("✅ Pipeline ready for future raw data processing")
    else:
        logger.error("=== DATA PIPELINE UPDATE FAILED ===")
        sys.exit(1)


if __name__ == "__main__":
    main()
