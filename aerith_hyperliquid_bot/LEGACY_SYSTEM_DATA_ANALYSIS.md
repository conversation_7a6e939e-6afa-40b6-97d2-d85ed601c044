# LEGACY SYSTEM DATA LOADING ANALYSIS

## 🔍 **COMPREHENSIVE DATA LOADING INVESTIGATION**

Based on the diagnostic run of the Legacy System (GranularMicrostructureRegimeDetector + TF-v2), here's exactly what data is being loaded and processed:

---

## 📊 **<PERSON>ATA SOURCES ACCESSED**

### 1. **PRIMARY DATA SOURCE: 1h OHLCV Files**
- **Path**: `/hyperliquid_data/resampled_l2/1h/`
- **Files Loaded**: 9 files for the test period (2024-01-01 to 2024-01-10)
- **Initial Shape**: (216, 9) - 216 hourly candles with 9 columns
- **Columns**: `['open', 'high', 'low', 'close', 'log_ret', 'realised_vol', 'bid_slope', 'ask_slope', 'book_asymmetry']`

### 2. **SECONDARY DATA SOURCE: L2 Raw Data (MAJOR PERFORMANCE BOTTLENECK)**
- **Path**: `/hyperliquid_data/raw2/`
- **Files Loaded**: 9 massive L2 files (one per day)
- **Total L2 Records Processed**: **1,177,638 rows** across 9 days
- **Average per day**: ~130,000 L2 snapshots per day
- **Columns**: 37 columns per L2 snapshot, reduced to 36 after processing

### 3. **ATTEMPTED DATA SOURCE: 1-Second Features (FAILED)**
- **Path**: `/hyperliquid_data/features_1s/`
- **Status**: ❌ **NO DATA FOUND** for 2024 dates
- **Impact**: System falls back to calculating features from L2 data
- **Available**: Only 2025 data (22 directories with 24 files each)

---

## ⚡ **PERFORMANCE BOTTLENECK IDENTIFIED**

### **The Core Problem: Massive L2 Data Processing**

For each day in the backtest period, the system:

1. **Loads entire daily L2 file** (~130K rows × 37 columns = ~4.8M data points per day)
2. **Processes each L2 snapshot** to calculate microstructure features
3. **Merges with hourly OHLCV** using `merge_asof` operation
4. **Calculates features for each hour** from the L2 data

**Example from logs:**
```
20240101: 133,438 L2 rows → processed to 24 hourly features
20240102: 132,910 L2 rows → processed to 24 hourly features
20240103: 131,902 L2 rows → processed to 24 hourly features
...
Total: 1,177,638 L2 rows processed for just 216 hourly candles
```

### **Processing Time Breakdown:**
- **Total Load Time**: 3.53 seconds for 9 days
- **Per Day**: ~0.39 seconds average
- **L2 Processing**: ~3.49 seconds (99% of total time)
- **OHLCV Loading**: ~0.04 seconds (1% of total time)

---

## 🎯 **KEY FINDINGS**

### ✅ **What's Working Correctly:**
1. **Detector Selection**: Now correctly using `GranularMicrostructureRegimeDetector`
2. **OHLCV Loading**: Fast and efficient (729 files available, loads only needed ones)
3. **Feature Calculation**: Raw microstructure features calculated correctly

### ❌ **Major Performance Issues:**

#### 1. **Unnecessary L2 Data Processing**
- **Problem**: System loads and processes **1.17M L2 snapshots** to generate **216 hourly features**
- **Ratio**: 5,450 L2 snapshots per hourly candle
- **Alternative**: Pre-calculated 1h features already exist in `resampled_l2/1h/` files

#### 2. **Missing 1-Second Feature Data**
- **Problem**: No 1-second feature files for 2024 dates
- **Impact**: Forces fallback to expensive L2 processing
- **Available**: Only 2025 data exists

#### 3. **Redundant Feature Calculation**
- **Problem**: Calculating features that already exist in the 1h files
- **Evidence**: `bid_slope`, `ask_slope`, `book_asymmetry` already in OHLCV files
- **Waste**: Additional `raw_*` features calculated from L2 data

---

## 📈 **FINAL DATA STRUCTURE**

### **Output Shape**: (216, 16)
- **216 rows**: 9 days × 24 hours = 216 hourly candles
- **16 columns**: 9 from OHLCV + 7 from L2 processing

### **Column Breakdown:**
```
OHLCV Columns (9):
├── open, high, low, close
├── log_ret, realised_vol  
└── bid_slope, ask_slope, book_asymmetry (pre-calculated)

L2-Derived Columns (7):
├── best_bid, best_ask
├── raw_obi_5, raw_depth_ratio_5, raw_depth_pressure_5
└── raw_spread_abs, raw_spread_rel
```

### **Data Quality Issues:**
- **8/216 (3.7%) NaN values** in L2-derived features
- **107/216 (49.5%) NaN values** in `realised_vol`
- **Missing**: `fear_greed_idx` column (expected but not found)

---

## 🚀 **OPTIMIZATION OPPORTUNITIES**

### **Immediate Performance Gains:**

1. **Skip L2 Processing for Legacy System**
   - The Legacy System already has all needed features in the 1h files
   - Could reduce load time from 3.53s to ~0.04s (98.9% improvement)

2. **Use Pre-calculated Features**
   - `bid_slope`, `ask_slope`, `book_asymmetry` already available
   - No need to recalculate from L2 data

3. **Generate Missing 2024 Feature Files**
   - Create 1-second feature files for 2024 dates
   - Would eliminate L2 processing entirely

### **Root Cause:**
The system was designed to support both Legacy and Modern systems, but the data loading logic always attempts to load the most comprehensive dataset, even when the Legacy System only needs the pre-calculated 1h features.

---

## 📋 **RECOMMENDATIONS**

1. **Immediate Fix**: Add configuration flag to skip L2 processing for Legacy System
2. **Medium-term**: Generate 2024 1-second feature files to eliminate L2 dependency
3. **Long-term**: Implement smart data loading based on detector type requirements

The Legacy System should be able to run in **under 0.1 seconds** for data loading instead of the current 3.5+ seconds. 