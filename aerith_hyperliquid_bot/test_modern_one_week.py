#!/usr/bin/env python3
"""
Test Modern system with 60s regime updates for just 1 week.
Shorter period for faster results to confirm the fix works.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import json
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
from hyperliquid_bot.config.settings import load_config

def main():
    print("="*60)
    print("MODERN SYSTEM - 1 WEEK TEST (No Cache)")
    print("="*60)
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Just 1 week for faster results
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 22)
    
    print(f"\nTest Period: {start_date.date()} to {end_date.date()} (1 week)")
    print("\nExpected behavior:")
    print("✅ ~10,080 regime updates (60 per hour)")
    print("✅ Different regime distribution than cached")
    print("✅ Better performance due to fresh regimes")
    print("-"*60)
    
    # Run without cache
    print("\n🚀 Starting backtest...")
    engine = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=False  # THE FIX!
    )
    
    # Track regime updates
    regime_count = 0
    regime_changes = 0
    last_regime = None
    
    # Hook into regime updates if possible
    if hasattr(engine, 'regime_history'):
        print("📊 Will track regime updates...\n")
    
    results = engine.run_backtest()
    
    if results:
        print("\n" + "="*60)
        print("RESULTS:")
        print("="*60)
        
        # Key metrics
        total_return = results.get('total_return', 0)
        total_trades = results.get('total_trades', 0)
        win_rate = results.get('win_rate', 0)
        
        print(f"\nPerformance (1 week):")
        print(f"  Return: {total_return:.2%}")
        print(f"  Trades: {total_trades}")
        print(f"  Win Rate: {win_rate:.1%}")
        
        # Check regime updates
        if hasattr(engine, 'regime_history') and engine.regime_history:
            regime_count = len(engine.regime_history)
            
            # Count changes
            for i, regime in enumerate(engine.regime_history):
                current = regime.get('regime', regime.get('state'))
                if i > 0 and current != last_regime:
                    regime_changes += 1
                last_regime = current
            
            print(f"\nRegime Updates:")
            print(f"  Total Updates: {regime_count}")
            print(f"  Expected: ~10,080 (168 hours × 60)")
            print(f"  Actual Rate: {regime_count / 168:.1f} per hour")
            print(f"  Regime Changes: {regime_changes}")
        
        # Save results
        with open('test_one_week_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print("\n📊 ANALYSIS:")
        if regime_count > 1000:  # More than hourly
            print("✅ Confirmed: Regime updates happening frequently!")
            print("   This proves 60s updates are working")
        else:
            print("❌ Still using hourly updates")
        
        # Annualized estimate
        weekly_return = total_return
        annual_estimate = ((1 + weekly_return) ** 52) - 1
        print(f"\nAnnualized estimate: {annual_estimate:.1%}")
        print(f"vs Cached version: +41.78%")
        print(f"vs Legacy target: +215%")
        
        if annual_estimate > 0.5:  # >50% annual
            print("\n🎉 SIGNIFICANT IMPROVEMENT!")
            print("The fix is working!")

if __name__ == "__main__":
    main()