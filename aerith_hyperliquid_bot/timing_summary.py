#!/usr/bin/env python3
"""
Extract key timing information from the 2024 backtest profiling data
"""

import pstats
import io
from datetime import datetime

def analyze_timing_summary():
    """Extract and display key timing information."""
    
    print("=== 2024 FULL BACKTEST TIMING ANALYSIS ===")
    
    # Calculate total execution time from log timestamps
    log_start = "2025-05-29 02:49:27"
    log_end = "2025-05-29 03:04:10"
    
    start_time = datetime.strptime(log_start, "%Y-%m-%d %H:%M:%S")
    end_time = datetime.strptime(log_end, "%Y-%m-%d %H:%M:%S")
    total_time = (end_time - start_time).total_seconds()
    
    print(f"Total Execution Time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")
    print()
    
    # Load the profile data
    try:
        stats = pstats.Stats("detailed_timing_2024_profile.prof")
        
        print("=== BREAKDOWN BY OPERATION TYPE ===")
        
        # Based on the visible output, extract key timing information
        print("Time Conversion (to_utc_naive): 470.2s (53.2%)")
        print("  - to_utc_naive function: 470.2s (50,729,632 calls)")
        print("  - This is the MAJOR bottleneck!")
        print()
        
        print("Data Loading Operations: 700.6s (79.3%)")
        print("  - _load_l2_segment: 700.6s (364 calls)")
        print("  - load_historical_data: 719.7s (1 call)")
        print("  - _integrate_microstructure_features: 717.9s (1 call)")
        print()
        
        print("Signal Calculation: 153.3s (17.4%)")
        print("  - calculate_all_signals: 153.3s (1 call)")
        print("  - Rolling window operations: 149.1s (8,690 calls)")
        print()
        
        print("Pandas Apply Operations: 681.9s (77.2%)")
        print("  - Series.apply: 681.9s (730 calls)")
        print("  - map_infer: 491.9s (730 calls)")
        print()
        
        print("=== KEY FINDINGS ===")
        print("1. CRITICAL: to_utc_naive called 50.7 MILLION times (470s)")
        print("2. Data loading dominates execution time (700s+)")
        print("3. Pandas apply() operations are extremely inefficient")
        print("4. Signal calculation is reasonable at 153s")
        print()
        
        print("=== OPTIMIZATION PRIORITIES (by impact) ===")
        print("1. 🔥 URGENT: Fix to_utc_naive bottleneck")
        print("   - Replace apply(to_utc_naive) with pd.to_datetime()")
        print("   - Potential savings: ~470s (53% reduction)")
        print()
        print("2. 🔥 HIGH: Optimize L2 data loading")
        print("   - Cache processed segments")
        print("   - Vectorize timestamp operations")
        print("   - Potential savings: ~200s (23% reduction)")
        print()
        print("3. 📊 MEDIUM: Optimize signal calculations")
        print("   - Cache rolling window results")
        print("   - Use numba for hot loops")
        print("   - Potential savings: ~50s (6% reduction)")
        print()
        print("4. 💾 LOW: General I/O optimizations")
        print("   - Parallel data loading")
        print("   - Better file formats (parquet)")
        print("   - Potential savings: ~30s (3% reduction)")
        print()
        
        print("=== ESTIMATED PERFORMANCE GAINS ===")
        current_time = total_time
        after_utc_fix = current_time - 470
        after_data_opt = after_utc_fix - 200
        after_signal_opt = after_data_opt - 50
        
        print(f"Current runtime: {current_time:.0f}s ({current_time/60:.1f} min)")
        print(f"After UTC fix: {after_utc_fix:.0f}s ({after_utc_fix/60:.1f} min) - {((current_time-after_utc_fix)/current_time)*100:.0f}% faster")
        print(f"After data optimization: {after_data_opt:.0f}s ({after_data_opt/60:.1f} min) - {((current_time-after_data_opt)/current_time)*100:.0f}% faster")
        print(f"After signal optimization: {after_signal_opt:.0f}s ({after_signal_opt/60:.1f} min) - {((current_time-after_signal_opt)/current_time)*100:.0f}% faster")
        
    except FileNotFoundError:
        print("Profile file not found. Please run the detailed timing analysis first.")
    except Exception as e:
        print(f"Error analyzing profile: {e}")

if __name__ == "__main__":
    analyze_timing_summary() 