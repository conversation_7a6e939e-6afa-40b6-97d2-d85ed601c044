#!/usr/bin/env python3
"""
Debug script to analyze momentum distribution and thresholds
"""

import re
import numpy as np
import pandas as pd

def analyze_momentum_from_log(log_file):
    """Extract and analyze momentum values from the log file."""
    print("=== MOMENTUM THRESHOLD ANALYSIS ===")
    print()
    
    # Extract all ma_slope values
    ma_slope_values = []
    with open(log_file, 'r') as f:
        for line in f:
            if 'ma_slope:' in line:
                # Extract the value after 'ma_slope: '
                match = re.search(r'ma_slope: ([0-9\.-]+)', line)
                if match:
                    ma_slope_values.append(float(match.group(1)))
    
    if not ma_slope_values:
        print("No ma_slope values found in log file")
        return
    
    # Convert to numpy array for analysis
    values = np.array(ma_slope_values)
    abs_values = np.abs(values)
    
    # Current thresholds
    current_strong = 0.2
    current_weak = 0.05
    
    print(f"Total momentum samples: {len(values)}")
    print(f"Raw momentum stats:")
    print(f"  Min: {values.min():.6f}")
    print(f"  Max: {values.max():.6f}")
    print(f"  Mean: {values.mean():.6f}")
    print(f"  Median: {np.median(values):.6f}")
    print(f"  Std: {values.std():.6f}")
    print()
    
    print(f"Absolute momentum stats:")
    print(f"  Min: {abs_values.min():.6f}")
    print(f"  Max: {abs_values.max():.6f}")
    print(f"  Mean: {abs_values.mean():.6f}")
    print(f"  Median: {np.median(abs_values):.6f}")
    print(f"  Std: {abs_values.std():.6f}")
    print()
    
    # Percentile analysis
    percentiles = [5, 10, 20, 30, 40, 50, 60, 70, 80, 90, 95, 99]
    print("Absolute momentum percentiles:")
    for p in percentiles:
        print(f"  {p}%: {np.percentile(abs_values, p):.6f}")
    print()
    
    # Current threshold analysis
    strong_count = np.sum(abs_values >= current_strong)
    weak_count = np.sum(abs_values <= current_weak)
    medium_count = len(abs_values) - strong_count - weak_count
    
    print(f"Current threshold classification:")
    print(f"  Strong (>= {current_strong}): {strong_count} ({strong_count/len(abs_values)*100:.1f}%)")
    print(f"  Medium: {medium_count} ({medium_count/len(abs_values)*100:.1f}%)")
    print(f"  Weak (<= {current_weak}): {weak_count} ({weak_count/len(abs_values)*100:.1f}%)")
    print()
    
    # Suggested thresholds for better distribution
    # Target: ~20% strong, ~60% medium, ~20% weak
    suggested_strong = np.percentile(abs_values, 80)  # Top 20%
    suggested_weak = np.percentile(abs_values, 20)   # Bottom 20%
    
    print(f"Suggested thresholds for better distribution:")
    print(f"  Strong threshold: {suggested_strong:.6f} (top 20%)")
    print(f"  Weak threshold: {suggested_weak:.6f} (bottom 20%)")
    print()
    
    # Test suggested thresholds
    strong_count_new = np.sum(abs_values >= suggested_strong)
    weak_count_new = np.sum(abs_values <= suggested_weak)
    medium_count_new = len(abs_values) - strong_count_new - weak_count_new
    
    print(f"Suggested threshold classification:")
    print(f"  Strong (>= {suggested_strong:.6f}): {strong_count_new} ({strong_count_new/len(abs_values)*100:.1f}%)")
    print(f"  Medium: {medium_count_new} ({medium_count_new/len(abs_values)*100:.1f}%)")
    print(f"  Weak (<= {suggested_weak:.6f}): {weak_count_new} ({weak_count_new/len(abs_values)*100:.1f}%)")
    print()
    
    # Look for zero values specifically
    zero_count = np.sum(abs_values == 0.0)
    print(f"Zero momentum values: {zero_count} ({zero_count/len(abs_values)*100:.1f}%)")
    print()
    
    # Check if we have enough strong momentum periods to generate trades
    print(f"Analysis:")
    print(f"  Current strong threshold ({current_strong}) captures only {strong_count/len(abs_values)*100:.1f}% of periods")
    print(f"  This is why we're getting mostly 'Uncertain' states instead of 'Strong_Bull_Trend' or 'Strong_Bear_Trend'")
    print(f"  The system expects 160-180 trades but is getting only 1 trade due to insufficient strong momentum periods")
    print()
    
    return {
        'total_samples': len(values),
        'current_strong_pct': strong_count/len(abs_values)*100,
        'current_weak_pct': weak_count/len(abs_values)*100,
        'suggested_strong': suggested_strong,
        'suggested_weak': suggested_weak,
        'zero_count': zero_count
    }

if __name__ == "__main__":
    log_file = "/Users/<USER>/Desktop/trading_bot_/logs/backtest_run_execution_refinement_20250715_183455.log"
    analyze_momentum_from_log(log_file)