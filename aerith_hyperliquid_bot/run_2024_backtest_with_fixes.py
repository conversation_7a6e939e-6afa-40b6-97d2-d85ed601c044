#!/usr/bin/env python3
"""
Run full 2024 backtest with temporal alignment fixes.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from datetime import datetime
import json
import logging

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtest_engine import RobustBacktestEngine

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """Run the 2024 backtest."""
    print("\n" + "="*80)
    print("RUNNING 2024 BACKTEST WITH TEMPORAL ALIGNMENT FIXES")
    print("="*80)
    
    # Load config
    config_path = Path(__file__).parent / "configs/overrides/modern_system_v2_complete.yaml"
    config = load_config(str(config_path))
    
    # Set up dates for full 2024
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 12, 31, 23, 59, 59)
    
    print(f"\nBacktest Period: {start_date.date()} to {end_date.date()}")
    print(f"Using enhanced detector with:")
    print(f"  - EMA periods: 20/50 (matches legacy)")
    print(f"  - Regime confidence scaling")
    print(f"  - Early entry logic for strong regimes")
    
    # Create and run backtest engine
    engine = RobustBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=True  # Use cache if available
    )
    
    # Run the backtest
    results = engine.run_backtest()
    
    # Display results
    print("\n" + "="*80)
    print("BACKTEST RESULTS - 2024 FULL YEAR")
    print("="*80)
    
    print(f"\nPerformance Metrics:")
    print(f"  Total Trades: {results.get('total_trades', 0)}")
    print(f"  Winning Trades: {results.get('winning_trades', 0)}")
    print(f"  Losing Trades: {results.get('losing_trades', 0)}")
    print(f"  Win Rate: {results.get('win_rate', 0):.1%}")
    print(f"  Total Return: {results.get('total_return', 0):.2%}")
    print(f"  Average Return per Trade: {results.get('average_return', 0):.2%}")
    print(f"  Sharpe Ratio: {results.get('sharpe_ratio', 0):.2f}")
    print(f"  Max Drawdown: {results.get('max_drawdown', 0):.2%}")
    
    # Analyze trade distribution
    if 'trades' in results and results['trades']:
        trades = results['trades']
        long_trades = [t for t in trades if t.get('direction') == 'long']
        short_trades = [t for t in trades if t.get('direction') == 'short']
        
        print(f"\nTrade Distribution:")
        print(f"  Long Trades: {len(long_trades)} ({len(long_trades)/len(trades)*100:.1f}%)")
        print(f"  Short Trades: {len(short_trades)} ({len(short_trades)/len(trades)*100:.1f}%)")
        
        # Check if we fixed the short-only problem
        if len(long_trades) == 0:
            print("\n⚠️  WARNING: Still no long trades generated!")
        elif len(long_trades) < len(short_trades) * 0.5:
            print("\n⚠️  WARNING: Long trades still significantly underrepresented")
        else:
            print("\n✅ Trade distribution looks more balanced now")
    
    # Regime source breakdown
    if 'regime_sources' in results:
        sources = results['regime_sources']
        total = sources.get('total', 1)
        print(f"\nRegime Detection Sources:")
        print(f"  Cache: {sources.get('cache_used', 0)} ({sources.get('cache_used', 0)/total*100:.1f}%)")
        print(f"  Detector: {sources.get('detector_used', 0)} ({sources.get('detector_used', 0)/total*100:.1f}%)")
        print(f"  Fallback: {sources.get('fallback_used', 0)} ({sources.get('fallback_used', 0)/total*100:.1f}%)")
    
    # Save results
    output_file = Path(__file__).parent / "2024_backtest_with_fixes.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\nDetailed results saved to: {output_file}")
    
    # Compare with previous results
    print("\n" + "="*80)
    print("COMPARISON WITH PREVIOUS RESULTS")
    print("="*80)
    
    print("\nBefore fixes:")
    print("  - Total trades: 84")
    print("  - All trades: 100% shorts") 
    print("  - Total return: -44.43%")
    print("  - Win rate: 32.14%")
    
    print("\nAfter fixes:")
    print(f"  - Total trades: {results.get('total_trades', 0)}")
    if 'trades' in results and results['trades']:
        long_pct = len(long_trades) / len(trades) * 100
        short_pct = len(short_trades) / len(trades) * 100
        print(f"  - Trade mix: {long_pct:.0f}% longs, {short_pct:.0f}% shorts")
    print(f"  - Total return: {results.get('total_return', 0):.2%}")
    print(f"  - Win rate: {results.get('win_rate', 0):.1%}")
    
    # Provide recommendations
    print("\n" + "="*80)
    print("RECOMMENDATIONS")
    print("="*80)
    
    if results.get('total_return', 0) < 0:
        print("\n⚠️  Still negative returns. Consider:")
        print("  1. Further reduce forecast threshold")
        print("  2. Adjust confidence thresholds (currently 0.8 for scaling)")
        print("  3. Review stop loss/take profit ratios")
        print("  4. Check if early entry logic is too restrictive")
    
    if results.get('total_trades', 0) < 100:
        print("\n⚠️  Low trade count. Consider:")
        print("  1. Lower min_regime_confidence (currently 0.65)")
        print("  2. Reduce min_regime_duration_minutes (currently 30)")
        print("  3. Make early entry conditions less strict")

if __name__ == "__main__":
    main()