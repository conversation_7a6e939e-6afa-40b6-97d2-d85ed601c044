# Crypto Trading Bot Strategy Optimization Request

## Context
I have a Python-based crypto trading bot for Hyperliquid with two systems:
- **Legacy System**: 180 trades/year, +215% ROI (working perfectly)
- **Modern System**: estimated ~500 trades/year, -3% ROI (newly built, underperforming)

Both systems use the same data (1-second BTC/USD) but different architectures. The modern system is correctly generating trades but with poor performance.

## Technical Stack
- Python 3.11, pandas, numpy
- 1-second granularity market data (OHLCV + microstructure features)
- Regime detection (8 states: Bull/Bear/Chop + variations)
- EMA crossover strategy with regime confirmation
- 25% risk per trade, ATR-based stops

## Current Issues (Priority Order)

### 1. Poor Strategy Performance
The modern system generates trades but loses money (-3% annually vs +215% legacy).

**Current entry logic:**
```python
# Enter LONG: Fast EMA > Slow EMA + Bull regime
# Enter SHORT: Fast EMA < Slow EMA + Bear regime
# Regime confidence threshold: 0.5
# State persistence check: DISABLED (always ~0 in crypto)
```

**Key difference from legacy**: Modern system evaluates every hour, legacy uses different timing.

### 2. Data Loading Performance
Loading 1 month of 1-second data takes ~5 minutes. Full year times out.

**Current approach:**
```python
# Load all 1-second data for date range
# Resample to hourly bars
# Problem: Loading 30 days = 2.6M rows per load
```

### 3. Duplicate Timestamp Errors
When loading data across multiple days, getting "cannot reindex on axis with duplicate labels" errors, despite deduplication logic.

## Questions

1. **Strategy Optimization**: Given that crypto regimes change every 60 seconds (state_persistence ~0), what alternative stability metrics should we use? Should we abandon regime stability checks entirely?

2. **Entry/Exit Timing**: The legacy system's success might be due to different entry timing. What systematic approach would you recommend to identify optimal entry conditions?

3. **Data Loading**: For 1-second data backtesting, what's the best practice for efficient data loading? Should we pre-aggregate to minutes?

4. **Parameter Tuning**: What's the most efficient approach to tune multiple parameters (EMA periods, confidence thresholds, regime states) without overfitting?

## Constraints
- Must maintain complete separation between legacy and modern systems
- Cannot modify the regime detection (it's working correctly)
- Need to support full year backtests (8.7M data points)
- Must use 1-second data (no compromise on granularity)

## Goal
Achieve 100+ trades/year with positive ROI, ideally matching legacy's 180 trades @ +215% ROI.