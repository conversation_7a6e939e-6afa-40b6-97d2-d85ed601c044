# Conservative Modern System Calibration Analysis

## Executive Summary

This analysis addresses the regime detection differences between legacy and modern systems, providing a calibration solution to achieve conservative trading (160-190 trades) while maintaining the modern pipeline architecture.

## Problem Statement

### Legacy System Performance
- **Trade Frequency**: ~180 trades/year (conservative)
- **Regime Detection**: Detects Bull/Bear regimes properly
- **Momentum Thresholds**: 100.0/50.0 (high thresholds = conservative)
- **Update Frequency**: 1 hour (3600s)
- **Strategy**: TF-v2

### Modern System Issues
- **Trade Frequency**: ~270 trades/year (too aggressive)
- **Regime Detection**: Only Bull regimes, missing Bear
- **Momentum Thresholds**: 2.5/0.5 (very low = aggressive)  
- **Update Frequency**: 1 minute (60s)
- **Strategy**: TF-v3 (disabled)

## Root Cause Analysis

### 1. Momentum Threshold Scaling Issue
```yaml
Legacy (granular_microstructure):
  gms_mom_strong_thresh: 100.0
  gms_mom_weak_thresh: 50.0
  cadence_sec: 3600

Modern (continuous_gms):
  gms_mom_strong_thresh: 2.5    # 40x lower!
  gms_mom_weak_thresh: 0.5      # 100x lower!
  cadence_sec: 60
```

The modern system's momentum thresholds are dramatically lower, causing excessive signal generation.

### 2. State Mapping Issue
```yaml
# Current mapping
Weak_Bear_Trend: 'CHOP'  # Should be 'BEAR'!

# Fixed mapping  
Weak_Bear_Trend: 'BEAR'  # Enable Bear regime detection
```

### 3. Missing Conservative Filters
- No regime confidence requirements
- No regime duration stability checks
- No momentum confirmation filters

## Calibration Solution

### 1. Conservative Momentum Thresholds
```yaml
continuous_gms:
  gms_mom_strong_thresh: 25.0   # 10x increase from 2.5
  gms_mom_weak_thresh: 12.5     # 25x increase from 0.5
```

**Rationale**: While not directly comparable to legacy values due to different calculation methods, these increases should significantly reduce signal frequency.

### 2. Fixed State Mapping
```yaml
state_map:
  Weak_Bear_Trend: 'BEAR'  # Changed from 'CHOP'
  Strong_Bear_Trend: 'BEAR'
```

**Impact**: Enables proper Bear regime detection and short trading opportunities.

### 3. Conservative Risk Management
```yaml
tf_v3:
  risk_frac: 0.02              # 2% vs 25% (much more conservative)
  max_notional: 20000          # Lower position limits
  min_regime_confidence: 0.7   # Require high confidence
  min_regime_duration: 5.0     # 5 minutes stability
```

### 4. Enhanced Filtering Pipeline
```yaml
tf_v3:
  require_momentum_confirmation: true
  momentum_lookback_bars: 3
  min_momentum_threshold: 0.002
  regime_transition_exits: true
```

## Modern Pipeline Architecture Preserved

The solution maintains the intended 3-layer architecture:

### Strategic Layer (1h frequency)
- Primary regime signals from continuous GMS
- Conservative momentum thresholds reduce false signals

### Tactical Layer (1m frequency) 
- Real-time momentum confirmation
- Regime confidence validation
- Risk suppression checks

### Execution Layer (1s frequency)
- Optimal entry timing
- ATR-based trailing stops
- Time decay exits

## Expected Outcomes

### Trade Frequency
- **Target**: 160-190 trades/year
- **Method**: 10-25x momentum threshold increases should dramatically reduce signals
- **Validation**: Backtest comparison required

### Regime Detection
- **Bear Regimes**: Now properly detected via fixed state mapping
- **Bull Regimes**: Maintained with higher confidence requirements
- **CHOP Regimes**: Enhanced filtering reduces false breakouts

### Risk Management
- **Position Sizing**: Much more conservative (2% vs 25%)
- **Confidence Scaling**: Dynamic sizing based on regime confidence
- **Transition Exits**: Automatic exits on adverse regime changes

## Implementation Files

### 1. Main Calibration Config
```
configs/overrides/conservative_modern_calibration.yaml
```
- Switches to TF-v3 strategy
- Sets conservative thresholds
- Enables all filtering enhancements

### 2. Fixed State Mapping
```
configs/gms_state_mapping_conservative.yaml
```
- Maps Weak_Bear_Trend to BEAR
- Maintains conservative CHOP mapping for ranging states

### 3. Validation Script
```
scripts/validate_conservative_calibration.py
```
- Runs backtest with calibrated settings
- Analyzes trade frequency vs target
- Validates Bear regime detection
- Generates comprehensive report

## Next Steps

### 1. Validation Testing
```bash
python scripts/validate_conservative_calibration.py
```

### 2. Iterative Calibration
If initial results don't meet targets:
- **Too many trades**: Increase momentum thresholds further
- **Too few trades**: Slightly decrease thresholds
- **No Bear detection**: Verify state mapping implementation

### 3. Out-of-Sample Testing
- Test on 2024 H2 data for regime detection accuracy
- Compare against ground truth events
- Measure regime transition detection latency

## Risk Considerations

### 1. Overfitting Risk
- Fixed thresholds avoid adaptive overfitting
- Validation on out-of-sample data required

### 2. Market Regime Changes
- Thresholds calibrated on 2024 data
- May need recalibration for different market conditions

### 3. Implementation Risk
- Thorough testing required before live deployment
- Gradual rollout with monitoring recommended

## Success Metrics

### Primary Metrics
- [ ] Trade frequency: 160-190 trades/year
- [ ] Bear regime detection: >0 Bear trades
- [ ] Regime accuracy: >70% on ground truth events

### Secondary Metrics  
- [ ] Risk-adjusted returns maintained
- [ ] Maximum drawdown controlled
- [ ] Regime transition exit effectiveness

---

**Calibration Date**: 2025-07-14  
**Status**: Ready for validation testing  
**Next Review**: After validation results analysis