# Modern System Root Cause Analysis

## Executive Summary

The modern system produces 0 trades because the detector thresholds are calibrated 10-30x too low for the actual data ranges, causing the system to spend 96.5% of time in `High_Vol_Range` state (non-trading for trend following).

## Root Cause

### 1. Threshold Mismatch

**Volatility (ATR%)**:
- Config thresholds: low=0.0002, high=0.0006
- Actual data 25th/75th percentiles: 0.0061, 0.0094
- **Result**: Almost all data exceeds "high volatility" threshold

**Momentum (MA Slope)**:
- Config thresholds: weak=0.00003, strong=0.0001
- Actual data 50th/90th percentiles: 0.0005, 0.0022
- **Result**: Most momentum values exceed thresholds

**OBI Confirmation**:
- Config thresholds: weak=0.05, strong=0.15
- Actual data 75th/90th percentiles: 0.36, 0.52
- **Result**: OBI rarely confirms trends

### 2. State Distribution

With miscalibrated thresholds:
- 7921 hours (96.5%) in `High_Vol_Range` → NO TRADES (TF doesn't trade in ranging markets)
- 157 hours (1.9%) in `Weak_Bull_Trend` → Trading allowed
- 141 hours (1.7%) in `Weak_Bear_Trend` → Trading allowed

Only 3.6% of time in trading states!

### 3. Logic Flow

1. Data has ATR% of 0.007 (typical value)
2. Detector sees this > 0.0006 (high volatility threshold)
3. Classifies as HIGH_VOL state
4. Combined with spread analysis → `High_Vol_Range` state
5. TF-v3 strategy only trades in trend states, not range states
6. Result: 0 trades

## Solution

Calibrate thresholds based on actual data distribution:

```yaml
# Volatility thresholds (based on percentiles)
gms_vol_low_thresh: 0.006    # 25th percentile
gms_vol_high_thresh: 0.009   # 75th percentile

# Momentum thresholds (based on percentiles)  
gms_mom_weak_thresh: 0.0005   # 50th percentile
gms_mom_strong_thresh: 0.002   # 90th percentile

# OBI thresholds (based on percentiles)
gms_obi_weak_confirm_thresh: 0.36   # 75th percentile
gms_obi_strong_confirm_thresh: 0.52  # 90th percentile
```

## Validation

The fix is correct because:
1. Debug showed detector logic works (100% trading states on small sample)
2. Backtest shows evaluator is being called
3. Only issue is state classification due to thresholds
4. No code bugs, just calibration issue