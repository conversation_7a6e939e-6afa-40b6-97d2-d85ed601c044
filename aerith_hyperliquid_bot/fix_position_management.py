#!/usr/bin/env python3
"""
Analyze and fix position management in modern system.

The issue: Modern system allows multiple concurrent positions, 
while legacy system only allows one position at a time.
"""

import json

# Load the backtest results
with open('modern_backtest_results.json', 'r') as f:
    results = json.load(f)

print("=== POSITION MANAGEMENT ANALYSIS ===\n")

# Simulate position tracking
position = None
filtered_trades = []
skipped_trades = 0

for trade in results['trades']:
    timestamp = trade['timestamp']
    
    if position is None:
        # No position, can enter
        position = trade
        filtered_trades.append(trade)
        print(f"✓ ENTER {trade['direction']} at {timestamp}")
    else:
        # Already have position, skip
        skipped_trades += 1
        print(f"✗ SKIP {trade['direction']} at {timestamp} (already in {position['direction']} from {position['timestamp']})")
        
    # For simplicity, assume position exits after 4-8 hours
    # In reality, this would be based on exit conditions
    # This is just to demonstrate the concept

print(f"\n\nSummary:")
print(f"Original trades: {len(results['trades'])}")
print(f"Filtered trades: {len(filtered_trades)}")
print(f"Skipped trades: {skipped_trades}")
print(f"\nThis would reduce annual trades from {len(results['trades']) * 365} to {len(filtered_trades) * 365}")

print("\n\n=== RECOMMENDED FIX ===")
print("""
The modern backtester needs to track position state and only evaluate entries when flat.

In ModernBacktestEngine._evaluate_trading_opportunity(), add:

1. Track current position:
   - self.current_position = None (initialize in __init__)
   
2. Check before evaluating entry:
   if self.current_position is not None:
       return None  # Skip entry evaluation
       
3. Update position on entry:
   if entry_signal:
       self.current_position = {
           'entry_time': hour_boundary,
           'entry_price': entry_signal['price'],
           'direction': entry_signal['direction'],
           ...
       }
       
4. Clear position on exit:
   if exit_reason:
       self.current_position = None

This simple change would prevent multiple concurrent positions and 
significantly reduce trade frequency.
""")