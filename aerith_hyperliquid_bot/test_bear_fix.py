#!/usr/bin/env python3
"""
Direct test of the bear mapping fix - bypassing the singleton cache issue
"""

import sys
sys.path.append('/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot')

from hyperliquid_bot.utils.state_mapping import GMS_STATE_WEAK_BEAR_TREND, GMS_3STATE_BEAR, GMS_3STATE_CHOP

def test_bear_mapping_direct():
    """Test the bear mapping logic directly"""
    
    print("🔍 DIRECT BEAR MAPPING TEST")
    print("=" * 50)
    
    # Simulate the state mapping logic
    print(f"\nTesting {GMS_STATE_WEAK_BEAR_TREND} mapping:")
    
    print(f"\n1️⃣ Legacy mode (map_weak_bear_to_bear=False):")
    print(f"   {GMS_STATE_WEAK_BEAR_TREND} → {GMS_3STATE_CHOP} (NO short trades)")
    
    print(f"\n2️⃣ Modern mode (map_weak_bear_to_bear=True):")  
    print(f"   {GMS_STATE_WEAK_BEAR_TREND} → {GMS_3STATE_BEAR} (SHORT trades enabled!)")
    
    print(f"\n🎯 IMPACT ON TRADING:")
    print(f"   If continuous_gms detector generates Weak_Bear_Trend states,")
    print(f"   but map_weak_bear_to_bear=False, then:")
    print(f"   ❌ NO short trades from weak bear trends")
    print(f"   ✅ Only Strong_Bear_Trend generates shorts")
    
    print(f"\n🔧 THE FIX:")
    print(f"   Set map_weak_bear_to_bear=True in config")
    print(f"   → Weak_Bear_Trend generates SHORT trades")  
    print(f"   → More balanced long/short distribution")
    
    # Show the potential impact
    print(f"\n📊 POTENTIAL IMPROVEMENT:")
    print(f"   If 30% of bear regimes are 'Weak_Bear_Trend':")
    print(f"   • Legacy: 70% of bear periods trade (Strong only)")
    print(f"   • Modern: 100% of bear periods trade (Strong + Weak)")
    print(f"   • Improvement: +43% more short trades")
    
    print(f"\n🚨 CURRENT PROBLEM:")
    print(f"   Config override not loading properly!")
    print(f"   - execution_refinement_enabled.yaml has map_weak_bear_to_bear=True")
    print(f"   - But backtest shows 'Map Weak Bear to Bear = False'")
    print(f"   - Path issue: aerith_hyperliquid_bot/aerith_hyperliquid_bot/configs/...")

if __name__ == "__main__":
    test_bear_mapping_direct()
    
    print(f"\n🔧 IMMEDIATE ACTIONS:")
    print(f"1. Fix config path so override loads properly")
    print(f"2. Verify map_weak_bear_to_bear=True is applied") 
    print(f"3. Run backtest and check for more short trades")
    print(f"4. If still long-only, check detector threshold calibration")