#!/usr/bin/env python3
"""
Diagnose why the modern system generates zero trades - Version 2.

This script will:
1. Check regime distribution
2. Verify allowed states for trading
3. Check entry conditions
4. Analyze signal generation
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from datetime import datetime, timedelta
import pandas as pd

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.data_loader import <PERSON><PERSON><PERSON><PERSON><PERSON>oader
from hyperliquid_bot.modern.enhanced_regime_detector import EnhancedRegimeDetector
from hyperliquid_bot.legacy.detector import LegacyGranularMicrostructureDetector

def main():
    """Run diagnostic test to understand why no trades are generated."""
    print("\n" + "="*80)
    print("DIAGNOSTIC: WHY ZERO TRADES? (v2)")
    print("="*80)
    
    # Load config
    config_path = Path(__file__).parent / "configs/overrides/modern_system_v2_complete.yaml"
    config = load_config(str(config_path))
    
    print("\n1. CHECKING CONFIGURATION:")
    print("-" * 40)
    print(f"Detector type: {config.regime.detector_type}")
    print(f"Min regime confidence: {getattr(config.tf_v3, 'min_regime_confidence', 0.4)}")
    print(f"Min regime duration: {getattr(config.tf_v3, 'min_regime_duration_minutes', 10)} minutes")
    print(f"Forecast threshold: {getattr(config.tf_v3, 'forecast_threshold', 'NOT SET')}")
    print(f"Risk per trade: {config.portfolio.risk_per_trade:.2%}")
    
    # Legacy thresholds
    print("\nLegacy Detector Thresholds:")
    print(f"  Momentum (weak/strong): {config.regime.gms_mom_weak_thresh} / {config.regime.gms_mom_strong_thresh}")
    print(f"  OBI (weak/strong): {config.microstructure.gms_obi_weak_confirm_thresh} / {config.microstructure.gms_obi_strong_confirm_thresh}")
    
    # Check what states allow trading
    print("\n2. ALLOWED TRADING STATES:")
    print("-" * 40)
    
    # Create detectors
    enhanced_detector = EnhancedRegimeDetector(config=config)
    legacy_detector = LegacyGranularMicrostructureDetector(config=config)
    
    # Check legacy detector allowed states
    legacy_allowed = legacy_detector.get_allowed_states('trend_following')
    print(f"Legacy allowed states for trend_following: {legacy_allowed}")
    
    # Test period - one week
    start_date = datetime(2024, 2, 1)
    end_date = datetime(2024, 2, 7, 23, 59, 59)
    
    print(f"\nTest period: {start_date} to {end_date}")
    
    # Load data
    print("\n3. LOADING DATA:")
    print("-" * 40)
    data_loader = RobustDataLoader(config)
    data = data_loader.load_data(start_date, end_date)
    print(f"Loaded {len(data)} hours of data")
    
    # Collect regime statistics
    regime_counts = {}
    confidence_values = []
    regime_history = []
    
    print("\n4. DETECTING REGIMES:")
    print("-" * 40)
    
    # Process each hour
    for idx, (timestamp, row) in enumerate(data.iterrows()):
        # Prepare signals
        signals = {
            'atr_percent': row.get('atr_percent_sec', 0),
            'ma_slope': row.get('ma_slope', 0),
            'obi_smoothed_5': row.get('volume_imbalance', 0),
            'spread_mean': row.get('spread_mean', 0),
            'spread_std': row.get('spread_std', 0),
            'volume': row.get('volume', 0),
            'close': row.get('close', 0)
        }
        
        # Detect regime using enhanced detector
        regime = enhanced_detector.detect_regime(signals, timestamp)
        confidence = enhanced_detector.get_confidence()
        
        # Record
        regime_counts[regime] = regime_counts.get(regime, 0) + 1
        confidence_values.append(confidence)
        regime_history.append({
            'timestamp': timestamp,
            'regime': regime,
            'confidence': confidence,
            'ma_slope': signals['ma_slope'],
            'obi': signals['obi_smoothed_5'],
            'spread_mean': signals['spread_mean']
        })
        
        # Log first few
        if idx < 10:
            print(f"  Hour {idx}: {regime:20s} (conf: {confidence:.2f}, ma_slope: {signals['ma_slope']:.1f})")
    
    # Analyze results
    print("\n5. REGIME DISTRIBUTION:")
    print("-" * 40)
    total_hours = sum(regime_counts.values())
    
    tradeable_hours = 0
    for regime, count in sorted(regime_counts.items(), key=lambda x: x[1], reverse=True):
        pct = count / total_hours * 100
        is_allowed = regime in legacy_allowed
        if is_allowed:
            tradeable_hours += count
        print(f"  {regime:20s}: {count:4d} ({pct:5.1f}%) {'✓ TRADEABLE' if is_allowed else ''}")
    
    print(f"\nTotal tradeable hours: {tradeable_hours}/{total_hours} ({tradeable_hours/total_hours*100:.1f}%)")
    
    # Confidence analysis
    print("\n6. CONFIDENCE ANALYSIS:")
    print("-" * 40)
    if confidence_values:
        min_conf_required = getattr(config.tf_v3, 'min_regime_confidence', 0.4)
        avg_conf = sum(confidence_values) / len(confidence_values)
        high_conf = sum(1 for c in confidence_values if c >= min_conf_required)
        print(f"  Average confidence: {avg_conf:.2f}")
        print(f"  High confidence hours: {high_conf}/{len(confidence_values)} ({high_conf/len(confidence_values)*100:.1f}%)")
        print(f"  Min required: {min_conf_required}")
    
    # Duration analysis
    print("\n7. REGIME DURATION ANALYSIS:")
    print("-" * 40)
    
    min_duration_hours = getattr(config.tf_v3, 'min_regime_duration_minutes', 10) / 60
    print(f"  Minimum duration required: {min_duration_hours:.1f} hours")
    
    # Find sequences of tradeable regimes
    sequences = []
    current_seq = None
    
    for entry in regime_history:
        if entry['regime'] in legacy_allowed and entry['confidence'] >= min_conf_required:
            if current_seq is None or current_seq['regime'] != entry['regime']:
                # Start new sequence
                if current_seq is not None:
                    sequences.append(current_seq)
                current_seq = {
                    'regime': entry['regime'],
                    'start': entry['timestamp'],
                    'end': entry['timestamp'],
                    'hours': 1
                }
            else:
                # Continue sequence
                current_seq['end'] = entry['timestamp']
                current_seq['hours'] += 1
        else:
            # End current sequence
            if current_seq is not None:
                sequences.append(current_seq)
                current_seq = None
    
    # Don't forget last sequence
    if current_seq is not None:
        sequences.append(current_seq)
    
    # Filter by duration
    long_sequences = [s for s in sequences if s['hours'] >= min_duration_hours]
    
    print(f"\n  Found {len(sequences)} tradeable sequences")
    print(f"  Sequences meeting duration requirement: {len(long_sequences)}")
    
    if long_sequences:
        print("\n  Long-duration tradeable sequences:")
        for seq in long_sequences[:5]:  # Show first 5
            print(f"    {seq['regime']} from {seq['start']} for {seq['hours']} hours")
    
    # Check data quality
    print("\n8. DATA QUALITY CHECK:")
    print("-" * 40)
    
    # Check for NaN values
    nan_counts = {}
    for col in ['atr_percent_sec', 'ma_slope', 'volume_imbalance', 'spread_mean', 'spread_std']:
        if col in data.columns:
            nan_count = data[col].isna().sum()
            nan_counts[col] = nan_count
            print(f"  {col}: {nan_count} NaN values ({nan_count/len(data)*100:.1f}%)")
    
    # Summary
    print("\n" + "="*80)
    print("DIAGNOSTIC SUMMARY:")
    print("="*80)
    
    if tradeable_hours == 0:
        print("❌ PROBLEM: No tradeable regimes detected!")
        print("   Only Strong_Bull_Trend and Strong_Bear_Trend allow trading")
        print("   Current thresholds may be too strict")
    elif len(long_sequences) == 0:
        print("❌ PROBLEM: No regimes last long enough!")
        print(f"   Min duration required: {min_duration_hours:.1f} hours")
        print("   Consider reducing min_regime_duration_minutes")
    else:
        print(f"✓ Found {len(long_sequences)} tradeable sequences")
        print(f"✓ {tradeable_hours} tradeable hours total")
        print("→ The issue may be in signal generation or entry logic")
        print("→ Check if the strategy is generating signals during these regimes")
    
    # Additional insights
    if 'Strong_Bull_Trend' in regime_counts or 'Strong_Bear_Trend' in regime_counts:
        strong_trends = regime_counts.get('Strong_Bull_Trend', 0) + regime_counts.get('Strong_Bear_Trend', 0) 
        print(f"\nStrong trends detected: {strong_trends} hours ({strong_trends/total_hours*100:.1f}%)")
    else:
        print("\n⚠️ NO strong trends detected - detector may need threshold adjustment")

if __name__ == "__main__":
    main()