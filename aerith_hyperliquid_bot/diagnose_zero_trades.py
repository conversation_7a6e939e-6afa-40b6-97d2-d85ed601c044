#!/usr/bin/env python3
"""
Diagnose why the modern system generates zero trades.

This script will:
1. Check regime distribution
2. Verify allowed states for trading
3. Check entry conditions
4. Analyze signal generation
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import logging
from datetime import datetime, timedelta
import pandas as pd

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtest_engine import RobustBacktestEngine
from hyperliquid_bot.modern.enhanced_regime_detector import EnhancedRegimeDetector

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """Run diagnostic test to understand why no trades are generated."""
    print("\n" + "="*80)
    print("DIAGNOSTIC: WHY ZERO TRADES?")
    print("="*80)
    
    # Load config
    config_path = Path(__file__).parent / "configs/overrides/modern_system_v2_complete.yaml"
    config = load_config(str(config_path))
    
    print("\n1. CHECKING CONFIGURATION:")
    print("-" * 40)
    print(f"Detector type: {config.regime.detector_type}")
    print(f"Min regime confidence: {getattr(config.tf_v3, 'min_regime_confidence', 0.4)}")
    print(f"Min regime duration: {getattr(config.tf_v3, 'min_regime_duration_minutes', 10)} minutes")
    print(f"Forecast threshold: {getattr(config.tf_v3, 'forecast_threshold', 'NOT SET')}")
    print(f"Risk per trade: {config.portfolio.risk_per_trade:.2%}")
    
    # Check what states allow trading
    print("\n2. ALLOWED TRADING STATES:")
    print("-" * 40)
    
    # Create detector to check allowed states
    detector = EnhancedRegimeDetector(config=config)
    
    # Check legacy detector allowed states
    legacy_allowed = detector.legacy_detector.get_allowed_states('trend_following')
    print(f"Legacy detector allowed states for trend_following: {legacy_allowed}")
    
    # Test period - one week
    start_date = datetime(2024, 2, 1)
    end_date = datetime(2024, 2, 7, 23, 59, 59)
    
    print(f"\nTest period: {start_date} to {end_date}")
    
    # Create engine WITHOUT strict mode to see all regimes
    engine = RobustBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=False,
        strict=False  # Allow fallbacks to see all regimes
    )
    
    # Collect regime statistics
    regime_counts = {}
    confidence_values = []
    
    print("\n3. COLLECTING REGIME DATA:")
    print("-" * 40)
    
    # Process each hour
    hour_timestamps = engine._generate_hourly_timestamps()
    
    for hour_idx, timestamp in enumerate(hour_timestamps):
        try:
            # Update regime state
            engine._update_regime_state(timestamp)
            
            # Get current regime
            current_state = engine.regime_manager.get_current_state()
            if current_state:
                regime = current_state.state
                confidence = current_state.confidence
                
                regime_counts[regime] = regime_counts.get(regime, 0) + 1
                confidence_values.append(confidence)
                
                # Log first few regimes
                if hour_idx < 10:
                    print(f"  Hour {hour_idx}: {regime} (conf: {confidence:.2f})")
        except Exception as e:
            print(f"  Error at hour {hour_idx}: {e}")
    
    # Analyze results
    print("\n4. REGIME DISTRIBUTION:")
    print("-" * 40)
    total_hours = sum(regime_counts.values())
    for regime, count in sorted(regime_counts.items(), key=lambda x: x[1], reverse=True):
        pct = count / total_hours * 100
        is_allowed = regime in legacy_allowed
        print(f"  {regime:20s}: {count:4d} ({pct:5.1f}%) {'✓ TRADEABLE' if is_allowed else ''}")
    
    # Confidence analysis
    print("\n5. CONFIDENCE ANALYSIS:")
    print("-" * 40)
    if confidence_values:
        min_conf_required = getattr(config.tf_v3, 'min_regime_confidence', 0.4)
        avg_conf = sum(confidence_values) / len(confidence_values)
        high_conf = sum(1 for c in confidence_values if c >= min_conf_required)
        print(f"  Average confidence: {avg_conf:.2f}")
        print(f"  High confidence hours: {high_conf}/{len(confidence_values)} ({high_conf/len(confidence_values)*100:.1f}%)")
        print(f"  Min required: {min_conf_required}")
    
    # Check if any hours meet trading conditions
    print("\n6. TRADING OPPORTUNITY ANALYSIS:")
    print("-" * 40)
    
    tradeable_regimes = 0
    high_conf_tradeable = 0
    
    for regime, count in regime_counts.items():
        if regime in legacy_allowed:
            tradeable_regimes += count
            
    print(f"  Total tradeable regime hours: {tradeable_regimes}/{total_hours} ({tradeable_regimes/total_hours*100:.1f}%)")
    
    # Duration analysis
    print("\n7. REGIME DURATION ANALYSIS:")
    print("-" * 40)
    print("  Checking if regimes last long enough for entry...")
    
    # Simple duration check
    last_regime = None
    duration = 0
    long_duration_regimes = 0
    
    for hour_idx, timestamp in enumerate(hour_timestamps):
        try:
            engine._update_regime_state(timestamp)
            current_state = engine.regime_manager.get_current_state()
            
            if current_state:
                if current_state.state == last_regime:
                    duration += 1
                else:
                    # Check if previous regime was long enough and tradeable
                    min_duration_hours = getattr(config.tf_v3, 'min_regime_duration_minutes', 10) / 60
                    if duration >= min_duration_hours:
                        if last_regime in legacy_allowed:
                            long_duration_regimes += 1
                            print(f"    ✓ {last_regime} lasted {duration} hours")
                    
                    last_regime = current_state.state
                    duration = 1
        except:
            pass
    
    print(f"  Regimes meeting duration requirement: {long_duration_regimes}")
    
    # Summary
    print("\n" + "="*80)
    print("DIAGNOSTIC SUMMARY:")
    print("="*80)
    
    if tradeable_regimes == 0:
        print("❌ PROBLEM: No tradeable regimes detected!")
        print("   Only Strong_Bull_Trend and Strong_Bear_Trend allow trading")
    elif long_duration_regimes == 0:
        print("❌ PROBLEM: No regimes last long enough!")
        print(f"   Min duration required: {getattr(config.tf_v3, 'min_regime_duration_minutes', 10)} minutes")
    else:
        print("✓ Tradeable regimes found")
        print("✓ Some regimes meet duration requirements")
        print("→ Check signal generation and entry logic")

if __name__ == "__main__":
    main()