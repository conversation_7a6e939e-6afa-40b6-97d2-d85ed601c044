# aerith_hyperliquid_bot/pyproject.toml

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "hyperliquid_bot"
version = "0.1.0"
description = "Trading bot framework for Hyperliquid DEX"
readme = "README.md"
requires-python = ">=3.10" # Specify your minimum Python version
# Add dependencies here as we identify them
dependencies = [
    "pandas",
    "numpy",
    "pandas-ta",
    "pydantic",
    "PyYAML",
    "matplotlib",
    "hurst",
    "httpx",  # Add httpx for HTTP requests
    # Add other direct dependencies here
    "deepmerge",
]

[tool.setuptools.packages.find]
where = ["."]  # Look for packages in the current directory
include = ["hyperliquid_bot*"]  # Include the main package
exclude = ["tests*"]  # Exclude the tests directory from the install

# Optional: Configure pytest if needed
# [tool.pytest.ini_options]
# pythonpath = ["."]
# testpaths = ["tests"]