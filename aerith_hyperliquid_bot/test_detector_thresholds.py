#!/usr/bin/env python3
"""Quick test to check detector thresholds"""

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.continuous_detector import ModernContinuousDetector

config = load_config()
detector = ModernContinuousDetector(config)

print("Detector thresholds:")
print(f"  vol_high_thresh: {detector.vol_high_thresh}")
print(f"  vol_low_thresh: {detector.vol_low_thresh}")
print(f"  mom_strong_thresh: {detector.mom_strong_thresh}")
print(f"  mom_weak_thresh: {detector.mom_weak_thresh}")
print(f"  obi_strong_confirm_thresh: {detector.obi_strong_confirm_thresh}")
print(f"  obi_weak_confirm_thresh: {detector.obi_weak_confirm_thresh}")

# Test signals
signals = {
    'ma_slope_ema_30s': 6.0,
    'volume_imbalance': 0.25,
    'atr_percent_sec': 0.03,
    'spread_mean': 0.0002,
    'spread_std': 0.00005
}

print("\nTest signals:")
for k, v in signals.items():
    print(f"  {k}: {v}")

# Test state determination
state = detector._determine_state(signals)
print(f"\nDetected state: {state}")

# Debug checks
print("\nDebug checks:")
print(f"  Is strong momentum (6.0 >= {detector.mom_strong_thresh})? {6.0 >= detector.mom_strong_thresh}")
print(f"  Is strong OBI (0.25 >= {detector.obi_strong_confirm_thresh})? {0.25 >= detector.obi_strong_confirm_thresh}")
print(f"  Is high vol (0.03 >= {detector.vol_high_thresh})? {0.03 >= detector.vol_high_thresh}")
print(f"  Is low vol (0.03 <= {detector.vol_low_thresh})? {0.03 <= detector.vol_low_thresh}")