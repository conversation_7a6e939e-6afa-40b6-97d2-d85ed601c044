#!/usr/bin/env python3
"""
Debug ATR normalization issue - verify ma_slope calculation
"""

import pandas as pd
import numpy as np
from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.data.handler import HistoricalDataHandler
from hyperliquid_bot.signals.calculator import SignalEngine
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s [%(levelname)s] %(name)s: %(message)s')
logger = logging.getLogger(__name__)

def debug_atr_normalization():
    """Debug ATR normalization by checking actual values"""
    
    # Load config using the same method as scripts
    import os
    import sys
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    # Use the same config loading as execution refinement script
    from hyperliquid_bot.config.settings import Config
    
    # Load base config
    base_config_path = "configs/base.yaml"
    override_config_path = "configs/overrides/execution_refinement_enabled.yaml"
    
    config = Config.from_yaml(base_config_path)
    if override_config_path:
        config = config.apply_overrides(override_config_path)
    
    # Create data handler
    data_handler = HistoricalDataHandler(config)
    
    # Get data
    ohlcv_data = data_handler.get_ohlcv_data()
    print(f"Data shape: {ohlcv_data.shape}")
    print(f"Data columns: {ohlcv_data.columns.tolist()}")
    
    # Check for ATR column
    atr_columns = [col for col in ohlcv_data.columns if 'atr' in col.lower()]
    print(f"ATR columns: {atr_columns}")
    
    if 'atr' in ohlcv_data.columns:
        atr_data = ohlcv_data['atr']
        print(f"ATR stats: min={atr_data.min():.6f}, max={atr_data.max():.6f}, mean={atr_data.mean():.6f}")
        print(f"ATR NaN count: {atr_data.isna().sum()}")
        print(f"ATR zero count: {(atr_data == 0).sum()}")
        
        # Check SMA calculation
        ma_slope_period = config.indicators.gms_ma_slope_period
        print(f"MA slope period: {ma_slope_period}")
        
        if ma_slope_period > 1:
            # Calculate SMA
            sma_data = ohlcv_data['close'].rolling(window=ma_slope_period).mean()
            print(f"SMA calculated successfully, NaN count: {sma_data.isna().sum()}")
            
            # Calculate absolute slope
            abs_ma_slope = sma_data - sma_data.shift(1)
            print(f"Absolute slope stats: min={abs_ma_slope.min():.6f}, max={abs_ma_slope.max():.6f}, mean={abs_ma_slope.mean():.6f}")
            print(f"Absolute slope NaN count: {abs_ma_slope.isna().sum()}")
            
            # Calculate ATR-normalized slope
            atr_safe = atr_data.replace(0, np.nan)
            atr_normalized_slope = abs_ma_slope / atr_safe
            print(f"ATR-normalized slope stats: min={atr_normalized_slope.min():.6f}, max={atr_normalized_slope.max():.6f}, mean={atr_normalized_slope.mean():.6f}")
            print(f"ATR-normalized slope NaN count: {atr_normalized_slope.isna().sum()}")
            
            # Show first few non-NaN values
            valid_normalized = atr_normalized_slope.dropna()
            if len(valid_normalized) > 0:
                print(f"First 10 valid ATR-normalized slopes:")
                for i, (timestamp, value) in enumerate(valid_normalized.head(10).items()):
                    print(f"  {timestamp}: {value:.6f}")
                    
                # Check how many exceed thresholds
                strong_threshold = 2.5
                weak_threshold = 0.5
                
                strong_count = (np.abs(valid_normalized) >= strong_threshold).sum()
                weak_count = (np.abs(valid_normalized) <= weak_threshold).sum()
                medium_count = len(valid_normalized) - strong_count - weak_count
                
                print(f"Momentum classification:")
                print(f"  Strong (>= {strong_threshold}): {strong_count} ({strong_count/len(valid_normalized)*100:.1f}%)")
                print(f"  Weak (<= {weak_threshold}): {weak_count} ({weak_count/len(valid_normalized)*100:.1f}%)")
                print(f"  Medium: {medium_count} ({medium_count/len(valid_normalized)*100:.1f}%)")
            else:
                print("No valid ATR-normalized slopes found!")
    
    # Now test with SignalEngine
    print("\n=== Testing SignalEngine ===")
    signal_engine = SignalEngine(config, data_handler)
    signals = signal_engine.calculate_all_signals()
    
    if 'ma_slope' in signals.columns:
        ma_slope = signals['ma_slope']
        print(f"SignalEngine ma_slope stats: min={ma_slope.min():.6f}, max={ma_slope.max():.6f}, mean={ma_slope.mean():.6f}")
        print(f"SignalEngine ma_slope NaN count: {ma_slope.isna().sum()}")
        print(f"SignalEngine ma_slope zero count: {(ma_slope == 0).sum()}")
        
        # Show first few non-zero values
        non_zero_slopes = ma_slope[ma_slope != 0]
        if len(non_zero_slopes) > 0:
            print(f"First 10 non-zero ma_slope values:")
            for i, (timestamp, value) in enumerate(non_zero_slopes.head(10).items()):
                print(f"  {timestamp}: {value:.6f}")
        else:
            print("All ma_slope values are zero!")

if __name__ == "__main__":
    debug_atr_normalization()