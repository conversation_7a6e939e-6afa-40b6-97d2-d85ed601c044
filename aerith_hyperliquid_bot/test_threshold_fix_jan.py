#!/usr/bin/env python3
"""
Quick test of threshold fix - January 2024 only
"""

import subprocess
import sys
import json
from pathlib import Path

def main():
    print("Testing threshold fix with January 2024...")
    
    cmd = [
        sys.executable,
        "scripts/run_modern_backtest.py",
        "--start-date", "2024-01-01", 
        "--end-date", "2024-01-31",
        "--override", "configs/overrides/modern_system_v2_complete.yaml",
        "--output", "jan_threshold_test.json"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print("Failed!")
        print(result.stderr)
        return 1
        
    # Load results
    with open("jan_threshold_test.json") as f:
        data = json.load(f)
        
    perf = data['performance']
    
    print(f"\nResults:")
    print(f"Total trades: {perf.get('total_trades', 0)}")
    print(f"Total return: {perf.get('total_return', 0):.2%}")
    print(f"Win rate: {perf.get('win_rate', 0):.2%}")
    
    print(f"\nBefore fix: 23 trades, -2.13% (Jan)")
    print(f"After fix: {perf.get('total_trades', 0)} trades, {perf.get('total_return', 0):.2%}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())