#!/usr/bin/env python3
"""
Compare ALL parameters between Legacy and Modern systems.
Based on O3's insight that "same detector" != same parameters.
"""

import json
import logging
from datetime import datetime
from hyperliquid_bot.config.settings import load_config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def dump_detector_params(detector_instance):
    """Extract all parameters from a detector instance."""
    params = {}
    for key, value in detector_instance.__dict__.items():
        if not key.startswith('_'):
            try:
                # Try to serialize to ensure it's a simple type
                json.dumps(value)
                params[key] = value
            except:
                params[key] = str(type(value))
    return params

def compare_configs():
    """Compare configuration between legacy and modern systems."""
    
    # Load configurations
    base_config = load_config('configs/base.yaml')
    modern_config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    print("=" * 80)
    print("CONFIGURATION COMPARISON")
    print("=" * 80)
    
    # 1. Check detector configuration
    print("\n1. DETECTOR CONFIGURATION:")
    print("-" * 40)
    
    # Legacy uses granular_microstructure with specific thresholds
    legacy_detector_config = base_config.regime.granular_microstructure
    print("LEGACY (granular_microstructure):")
    print(f"  - Vol thresholds: {legacy_detector_config.gms_vol_low_thresh} / {legacy_detector_config.gms_vol_high_thresh}")
    print(f"  - Mom thresholds: {legacy_detector_config.gms_mom_weak_thresh} / {legacy_detector_config.gms_mom_strong_thresh}")
    print(f"  - Cadence: {legacy_detector_config.cadence_sec}s")
    print(f"  - OBI confirm: {base_config.microstructure.gms_obi_weak_confirm_thresh} / {base_config.microstructure.gms_obi_strong_confirm_thresh}")
    
    # Modern claims to use same detector but...
    # Check if modern has granular_microstructure config
    if hasattr(modern_config.regime, 'granular_microstructure'):
        modern_detector_config = modern_config.regime.granular_microstructure
        print("\nMODERN (granular_microstructure):")
        print(f"  - Vol thresholds: {modern_detector_config.gms_vol_low_thresh} / {modern_detector_config.gms_vol_high_thresh}")
        print(f"  - Mom thresholds: {modern_detector_config.gms_mom_weak_thresh} / {modern_detector_config.gms_mom_strong_thresh}")
        print(f"  - Cadence: {modern_detector_config.cadence_sec}s")
    elif hasattr(modern_config.regime, 'continuous_gms'):
        modern_detector_config = modern_config.regime.continuous_gms
        print("\nMODERN (continuous_gms - DIFFERENT DETECTOR!):")
        print(f"  - Vol thresholds: {modern_detector_config.gms_vol_low_thresh} / {modern_detector_config.gms_vol_high_thresh}")
        print(f"  - Mom thresholds: {modern_detector_config.gms_mom_weak_thresh} / {modern_detector_config.gms_mom_strong_thresh}")
        print(f"  - Cadence: {modern_detector_config.cadence_sec}s")
    
    # 2. Check strategy parameters
    print("\n2. STRATEGY PARAMETERS:")
    print("-" * 40)
    
    # Legacy TF-v2
    print("LEGACY (TF-v2):")
    print(f"  - EMA periods: {base_config.indicators.tf_ewma_fast} / {base_config.indicators.tf_ewma_slow}")
    print(f"  - ATR stop mult: {base_config.indicators.tf_atr_stop_mult}")
    print(f"  - ATR target mult: {base_config.indicators.tf_atr_target_mult}")
    print(f"  - Base leverage: {base_config.indicators.tf_leverage_base}")
    
    # Modern TF-v3
    print("\nMODERN (TF-v3):")
    print(f"  - EMA periods: {modern_config.tf_v3.ema_fast} / {modern_config.tf_v3.ema_slow}")
    print(f"  - ATR trail k: {modern_config.tf_v3.atr_trail_k}")
    print(f"  - Risk fraction: {modern_config.tf_v3.risk_frac}")
    print(f"  - Max hold time: {modern_config.tf_v3.max_trade_life_h}h")
    
    # 3. Check regime thresholds that might be overridden
    print("\n3. CRITICAL THRESHOLDS:")
    print("-" * 40)
    
    # Entry requirements
    print("ENTRY REQUIREMENTS:")
    print(f"  - Min regime confidence: {getattr(modern_config.regime, 'min_regime_confidence', 'DEFAULT')}")
    print(f"  - Min regime duration: {getattr(modern_config.regime, 'min_regime_duration_minutes', 'DEFAULT')} min")
    print(f"  - Forecast threshold: {getattr(modern_config.indicators, 'forecast_threshold', 'DEFAULT')}")
    
    # 4. Check position management
    print("\n4. POSITION MANAGEMENT:")
    print("-" * 40)
    print(f"  - Risk per trade: {base_config.portfolio.risk_per_trade}")
    print(f"  - Max leverage: {base_config.portfolio.max_leverage}")
    print(f"  - Max hold time: {base_config.portfolio.max_hold_time_hours}h")
    
    # 5. CRITICAL FINDING - Check if modern is using wrong detector!
    print("\n5. DETECTOR TYPE MISMATCH CHECK:")
    print("-" * 40)
    print(f"Base detector type: {base_config.regime.detector_type}")
    print(f"Modern detector type: {modern_config.regime.detector_type}")
    print(f"GMS detector type: {base_config.gms.detector_type}")
    
    if base_config.regime.detector_type != modern_config.regime.detector_type:
        print("\n⚠️  WARNING: DETECTOR TYPE MISMATCH!")
        print("This could be the root cause of performance differences!")

if __name__ == "__main__":
    compare_configs()