=== Legacy System Performance Profile Analysis ===
Generated at: 2025-05-28 05:25:36.707101

=== Top 15 Functions by Cumulative Time ===
         23316384 function calls (23067127 primitive calls) in 15.319 seconds

   Ordered by: cumulative time
   List reduced from 10362 to 15 due to restriction <15>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.000    0.000   15.321   15.321 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/profile_legacy_system.py:18(run_legacy_backtest)
        1    0.000    0.000   13.025   13.025 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/run_backtest.py:179(main)
        1    0.000    0.000   12.970   12.970 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:1239(run)
        1    0.000    0.000   12.400   12.400 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:114(_load_and_prepare_data)
        1    0.001    0.001    9.469    9.469 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/data/handler.py:140(load_historical_data)
        1    0.033    0.033    9.382    9.382 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/data/handler.py:507(_integrate_microstructure_features)
       20    0.034    0.002    8.886    0.444 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/data/handler.py:371(_load_l2_segment)
       42    0.000    0.000    8.267    0.197 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/series.py:4661(apply)
       42    0.000    0.000    8.267    0.197 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/apply.py:1108(apply)
       42    0.001    0.000    8.267    0.197 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/apply.py:1159(apply_standard)
       92    0.000    0.000    8.258    0.090 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/arrays/_mixins.py:82(method)
       20    0.000    0.000    8.257    0.413 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/arrays/datetimelike.py:812(map)
       20    0.103    0.005    8.256    0.413 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/indexes/extension.py:156(map)
       20    0.012    0.001    6.685    0.334 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/indexes/base.py:6340(map)
       20    0.000    0.000    5.782    0.289 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/base.py:796(_map_values)



=== Top 15 Functions by Total Time ===
         23316384 function calls (23067127 primitive calls) in 15.319 seconds

   Ordered by: internal time
   List reduced from 10362 to 15 due to restriction <15>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
  2426031    4.932    0.000    5.484    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/utils/time.py:8(to_utc_naive)
       63    1.465    0.023    1.465    0.023 {pandas._libs.tslibs.vectorized.ints_to_pydatetime}
      205    0.759    0.004    0.760    0.004 {built-in method _imp.create_dynamic}
     2045    0.670    0.000    0.833    0.000 {pandas._libs.lib.maybe_convert_objects}
  2425603    0.384    0.000    0.467    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pytz/__init__.py:130(timezone)
     3151    0.346    0.000    0.346    0.000 {method 'copy' of 'numpy.ndarray' objects}
4552689/4537321    0.281    0.000    0.483    0.000 {built-in method builtins.isinstance}
       42    0.281    0.007    5.760    0.137 {pandas._libs.lib.map_infer}
     1344    0.201    0.000    0.201    0.000 {method 'read' of '_io.BufferedReader' objects}
       40    0.168    0.004    0.168    0.004 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pyarrow/parquet/core.py:1420(read)
       40    0.133    0.003    0.133    0.003 {built-in method pandas._libs.tslib.array_to_datetime}
     1328    0.112    0.000    0.112    0.000 {built-in method marshal.loads}
       20    0.103    0.005    8.256    0.413 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/indexes/extension.py:156(map)
       40    0.097    0.002    0.123    0.003 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pyarrow/pandas_compat.py:782(table_to_dataframe)
        1    0.095    0.095    0.146    0.146 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/matplotlib/backend_bases.py:2822(create_with_canvas)



=== Data Loading Related Functions ===
         23316384 function calls (23067127 primitive calls) in 15.319 seconds

   Ordered by: cumulative time
   List reduced from 10362 to 348 due to restriction <'.*load.*|.*read.*|.*parquet.*|.*arrow.*|.*features.*'>
   List reduced from 348 to 20 due to restriction <20>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.000    0.000   12.400   12.400 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:114(_load_and_prepare_data)
        1    0.001    0.001    9.469    9.469 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/data/handler.py:140(load_historical_data)
        1    0.033    0.033    9.382    9.382 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/data/handler.py:507(_integrate_microstructure_features)
       20    0.034    0.002    8.886    0.444 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/data/handler.py:371(_load_l2_segment)
  1675/54    0.005    0.000    2.332    0.043 <frozen importlib._bootstrap>:1167(_find_and_load)
  1613/13    0.005    0.000    2.332    0.179 <frozen importlib._bootstrap>:1122(_find_and_load_unlocked)
  1543/14    0.005    0.000    2.330    0.166 <frozen importlib._bootstrap>:666(_load_unlocked)
       40    0.000    0.000    0.347    0.009 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/io/parquet.py:447(read_parquet)
       40    0.005    0.000    0.344    0.009 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/io/parquet.py:211(read)
       40    0.000    0.000    0.211    0.005 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pyarrow/parquet/core.py:1776(read_table)
     1344    0.201    0.000    0.201    0.000 {method 'read' of '_io.BufferedReader' objects}
       40    0.168    0.004    0.168    0.004 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pyarrow/parquet/core.py:1420(read)
      463    0.007    0.000    0.130    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/data/handler.py:456(_calculate_features_from_row)
       40    0.097    0.002    0.123    0.003 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pyarrow/pandas_compat.py:782(table_to_dataframe)
     1328    0.112    0.000    0.112    0.000 {built-in method marshal.loads}
        1    0.000    0.000    0.085    0.085 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/data/handler.py:170(_load_ohlcv)
        1    0.000    0.000    0.080    0.080 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/compat/pyarrow.py:1(<module>)
        1    0.000    0.000    0.079    0.079 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pyarrow/__init__.py:1(<module>)
        1    0.000    0.000    0.066    0.066 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/arrays/arrow/__init__.py:1(<module>)
        1    0.000    0.000    0.065    0.065 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/arrays/arrow/array.py:1(<module>)



=== Microstructure Related Functions ===
         23316384 function calls (23067127 primitive calls) in 15.319 seconds

   Ordered by: cumulative time
   List reduced from 10362 to 45 due to restriction <'.*microstructure.*|.*gms.*|.*regime.*|.*detector.*'>
   List reduced from 45 to 20 due to restriction <20>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.033    0.033    9.382    9.382 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/data/handler.py:507(_integrate_microstructure_features)
        1    0.000    0.000    0.057    0.057 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/detector.py:1(<module>)
        1    0.000    0.000    0.054    0.054 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py:1(<module>)
      311    0.001    0.000    0.040    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py:1004(get_regime)
      311    0.003    0.000    0.034    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py:363(update)
      311    0.005    0.000    0.030    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py:433(_determine_state)
      463    0.001    0.000    0.002    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/features/microstructure.py:23(calculate_order_book_imbalance)
      463    0.000    0.000    0.002    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/features/microstructure.py:238(calculate_depth_metrics)
      463    0.001    0.000    0.002    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/features/microstructure.py:197(calculate_book_depth)
      311    0.001    0.000    0.002    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py:1048(<listcomp>)
        1    0.000    0.000    0.002    0.002 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/utils/system_validation.py:17(validate_gms_state_mapping_config)
      311    0.001    0.000    0.001    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py:290(required_signals)
      463    0.000    0.000    0.001    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/features/microstructure.py:141(calculate_bid_ask_spread)
      311    0.001    0.000    0.001    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py:243(_resolve_obi_column)
        1    0.000    0.000    0.001    0.001 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/detector.py:913(get_regime_detector)
        1    0.000    0.000    0.001    0.001 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py:74(__init__)
        1    0.000    0.000    0.000    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/config/settings.py:725(validate_strategy_and_detector_support)
      311    0.000    0.000    0.000    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/utils/state_mapping.py:260(get_valid_gms_states)
     2664    0.000    0.000    0.000    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/features/microstructure.py:65(<genexpr>)
     2664    0.000    0.000    0.000    0.000 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/features/microstructure.py:66(<genexpr>)


