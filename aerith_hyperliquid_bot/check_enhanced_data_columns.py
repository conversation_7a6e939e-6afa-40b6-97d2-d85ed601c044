#!/usr/bin/env python3
"""
Check what columns are available in the enhanced hourly data
and compare with what the legacy detector expects.
"""

import pandas as pd
import numpy as np

# Path to enhanced hourly data
enhanced_file = "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/enhanced_hourly/1h/2024-01-01_1h_enhanced.parquet"

# Load the data
print("Loading enhanced hourly data...")
df = pd.read_parquet(enhanced_file)

print(f"\nData shape: {df.shape}")
print(f"Date range: {df.index[0]} to {df.index[-1]}")

# List all columns
print("\nAll columns in enhanced hourly data:")
for i, col in enumerate(sorted(df.columns)):
    print(f"{i+1:3d}. {col}")

# Check for the specific fields the detector needs
print("\n" + "="*80)
print("CHECKING FOR LEGACY DETECTOR REQUIRED FIELDS:")
print("="*80)

required_fields = {
    'atr_percent': 'ATR as percentage of price',
    'ma_slope': 'Moving average slope',
    'obi_smoothed_5': 'Order book imbalance smoothed (5 levels)',
    'spread_mean': 'Mean spread',
    'spread_std': 'Spread standard deviation'
}

# Also check variations
variations = {
    'atr_percent': ['atr_percent', 'atr_percent_sec', 'atr_pct', 'atr'],
    'ma_slope': ['ma_slope', 'ma_slope_sec', 'ema_slope', 'sma_slope'],
    'obi_smoothed_5': ['obi_smoothed_5', 'obi_5', 'obi', 'volume_imbalance', 'imbalance'],
    'spread_mean': ['spread_mean', 'spread_avg', 'mean_spread'],
    'spread_std': ['spread_std', 'spread_stdev', 'std_spread']
}

found_mappings = {}

for field, description in required_fields.items():
    print(f"\n{field} ({description}):")
    
    # Check exact match
    if field in df.columns:
        print(f"  ✓ FOUND: {field}")
        sample_values = df[field].dropna().head(5).values
        print(f"    Sample values: {sample_values}")
        print(f"    Non-NaN count: {df[field].notna().sum()}/{len(df)}")
        found_mappings[field] = field
    else:
        print(f"  ✗ NOT FOUND: {field}")
        
        # Check variations
        print("  Checking variations...")
        found_variation = False
        for var in variations.get(field, []):
            if var in df.columns:
                print(f"    ✓ FOUND VARIATION: {var}")
                sample_values = df[var].dropna().head(5).values
                print(f"      Sample values: {sample_values}")
                print(f"      Non-NaN count: {df[var].notna().sum()}/{len(df)}")
                found_mappings[field] = var
                found_variation = True
                break
        
        if not found_variation:
            print("    No variations found")

# Print summary
print("\n" + "="*80)
print("SUMMARY:")
print("="*80)
print(f"Found {len(found_mappings)}/{len(required_fields)} required fields")
print("\nMappings found:")
for required, actual in found_mappings.items():
    if required != actual:
        print(f"  {required} -> {actual}")
    else:
        print(f"  {required} (exact match)")

# Check for any OBI-related columns
print("\n" + "="*80)
print("ALL OBI/IMBALANCE RELATED COLUMNS:")
print("="*80)
obi_cols = [col for col in df.columns if 'obi' in col.lower() or 'imbalance' in col.lower()]
for col in obi_cols:
    print(f"  {col}")
    
# Check for spread-related columns
print("\n" + "="*80)
print("ALL SPREAD RELATED COLUMNS:")
print("="*80)
spread_cols = [col for col in df.columns if 'spread' in col.lower()]
for col in spread_cols:
    print(f"  {col}")

# Show first few rows of relevant columns
print("\n" + "="*80)
print("SAMPLE DATA FROM FOUND COLUMNS:")
print("="*80)
if found_mappings:
    cols_to_show = list(found_mappings.values()) + ['close', 'volume']
    cols_to_show = [col for col in cols_to_show if col in df.columns]
    print(df[cols_to_show].head())