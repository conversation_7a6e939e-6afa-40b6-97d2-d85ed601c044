# Novel Features Implementation Plan

## Executive Summary
We have access to 20-level order book data that is currently underutilized. By implementing advanced microstructure features, we can potentially improve regime detection accuracy and trading performance without needing external data sources.

## Priority 1: Order Book Pressure Features (High Impact, Quick Implementation)

### 1.1 Weighted Order Book Imbalance (WOBI)
```python
def calculate_wobi(bid_prices, bid_sizes, ask_prices, ask_sizes, levels=20):
    """
    Calculate weighted order book imbalance using distance from mid-price
    Weight decreases exponentially with distance from best bid/ask
    """
    mid_price = (bid_prices[0] + ask_prices[0]) / 2
    
    # Calculate weights based on price distance
    bid_weights = np.exp(-np.abs(bid_prices - mid_price) / mid_price * 100)
    ask_weights = np.exp(-np.abs(ask_prices - mid_price) / mid_price * 100)
    
    # Weighted volumes
    weighted_bid_vol = np.sum(bid_sizes * bid_weights)
    weighted_ask_vol = np.sum(ask_sizes * ask_weights)
    
    # WOBI: -1 to 1 scale
    wobi = (weighted_bid_vol - weighted_ask_vol) / (weighted_bid_vol + weighted_ask_vol)
    return wobi
```

### 1.2 Order Book Concentration Index (OBCI)
```python
def calculate_obci(sizes, levels=5):
    """
    Measures how concentrated liquidity is in top levels
    High OBCI = liquidity concentrated near touch
    Low OBCI = liquidity distributed deeper
    """
    total_size = np.sum(sizes)
    top_n_size = np.sum(sizes[:levels])
    return top_n_size / total_size if total_size > 0 else 0
```

### 1.3 Book Pressure Gradient (BPG)
```python
def calculate_bpg(bid_sizes, ask_sizes):
    """
    Measures the gradient of liquidity from L1 to L20
    Positive = increasing liquidity depth
    Negative = decreasing liquidity depth
    """
    bid_gradient = np.polyfit(range(len(bid_sizes)), bid_sizes, 1)[0]
    ask_gradient = np.polyfit(range(len(ask_sizes)), ask_sizes, 1)[0]
    return (bid_gradient - ask_gradient) / 2
```

## Priority 2: Multi-Timeframe OBI Features

### 2.1 OBI Divergence Score
```python
def calculate_obi_divergence(obi_5, obi_20):
    """
    Detects when short-term and long-term OBI diverge
    High divergence = potential regime change
    """
    divergence = abs(obi_5 - obi_20)
    # Normalize by their average magnitude
    avg_magnitude = (abs(obi_5) + abs(obi_20)) / 2
    return divergence / avg_magnitude if avg_magnitude > 0 else 0
```

### 2.2 OBI Momentum
```python
def calculate_obi_momentum(obi_series, window=30):
    """
    Rate of change in OBI - detects accelerating order flow
    """
    obi_change = obi_series.diff(window)
    obi_momentum = obi_change / obi_series.rolling(window).std()
    return obi_momentum
```

## Priority 3: Advanced Volatility Features

### 3.1 Volatility Regime Classifier
```python
def classify_volatility_regime(atr_1s, atr_14s, realised_vol):
    """
    Combines multiple volatility measures to classify regime
    Returns: 'explosive', 'trending', 'mean_reverting', 'quiet'
    """
    vol_ratio = atr_1s / atr_14s
    vol_acceleration = (atr_1s - atr_14s) / atr_14s
    
    if vol_ratio > 1.5 and vol_acceleration > 0.1:
        return 'explosive'
    elif vol_ratio > 1.1:
        return 'trending'
    elif vol_ratio < 0.9:
        return 'mean_reverting'
    else:
        return 'quiet'
```

### 3.2 Volatility Surface Feature
```python
def calculate_vol_surface_skew(bid_prices, ask_prices, spreads):
    """
    Measures asymmetry in implied volatility from spread patterns
    """
    # Use spread as proxy for implied vol at each level
    spread_gradient = np.gradient(spreads)
    price_gradient = np.gradient((bid_prices + ask_prices) / 2)
    
    # Vol skew approximation
    vol_skew = spread_gradient / price_gradient
    return np.mean(vol_skew[~np.isnan(vol_skew)])
```

## Priority 4: Market Quality Indicators

### 4.1 Liquidity Quality Score (LQS)
```python
def calculate_lqs(spread, spread_std, book_depth, volume):
    """
    Composite score of market quality
    Higher = better trading conditions
    """
    # Normalize components
    spread_score = 1 / (1 + spread)  # Lower spread = higher score
    stability_score = 1 / (1 + spread_std)  # Lower volatility = higher score
    depth_score = np.log1p(book_depth) / 10  # Log scale for depth
    activity_score = np.log1p(volume) / 15  # Log scale for volume
    
    # Weighted combination
    lqs = (0.3 * spread_score + 
           0.3 * stability_score + 
           0.2 * depth_score + 
           0.2 * activity_score)
    return lqs
```

### 4.2 Hidden Liquidity Indicator
```python
def detect_hidden_liquidity(sizes, prices, levels=20):
    """
    Detects gaps in order book that suggest hidden orders
    """
    # Calculate expected size distribution
    expected_sizes = np.exp(-np.arange(levels) * 0.1) * sizes[0]
    
    # Find anomalies
    size_ratios = sizes / expected_sizes
    anomaly_score = np.std(size_ratios)
    
    # Check for price gaps
    price_diffs = np.diff(prices)
    expected_diff = np.median(price_diffs)
    gap_score = np.sum(price_diffs > 2 * expected_diff)
    
    return anomaly_score * (1 + gap_score * 0.1)
```

## Implementation Strategy

### Phase 1 (Week 1):
1. Create `modern_features.py` module
2. Implement WOBI, OBCI, and BPG
3. Add to ModernDataAdapter
4. Backtest impact on regime detection

### Phase 2 (Week 2):
1. Implement OBI divergence and momentum
2. Add volatility regime classifier
3. Integrate with ModernContinuousDetector
4. Measure regime detection improvements

### Phase 3 (Week 3):
1. Implement market quality indicators
2. Create feature importance analysis
3. Optimize feature combinations
4. Full system backtest

## Expected Benefits

1. **Better Regime Detection**: 
   - WOBI provides more nuanced order flow signal
   - OBI divergence catches regime transitions earlier
   
2. **Improved Entry Timing**:
   - Book pressure gradient identifies momentum quality
   - Liquidity quality score filters bad market conditions
   
3. **Risk Management**:
   - Volatility regime classifier helps size positions
   - Hidden liquidity indicator warns of potential slippage

## Success Metrics

1. **Regime Detection Accuracy**: Target 15% improvement
2. **False Signal Reduction**: Target 20% fewer bad entries
3. **Sharpe Ratio**: Target 0.2 improvement
4. **Win Rate**: Target 5% improvement

## Code Integration Points

1. **ModernDataAdapter**: Add feature computation methods
2. **ModernContinuousDetector**: Use new features for state determination
3. **ModernSignalEngine**: Incorporate features in signal generation
4. **ModernTFV3Strategy**: Use features for entry/exit decisions