#!/usr/bin/env python3
"""
Test the threshold fixes for the modern system.
Compare performance before and after fixing the 100,000x scale error.
"""

import logging
from datetime import datetime
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_threshold_fixes():
    """Test modern system with fixed thresholds."""
    
    # Test period - Q4 2024 where we have data
    start_date = datetime(2024, 10, 1)
    end_date = datetime(2024, 12, 31)
    
    logger.info("=" * 80)
    logger.info("THRESHOLD FIX VERIFICATION TEST")
    logger.info("=" * 80)
    logger.info(f"Test period: {start_date.date()} to {end_date.date()}")
    logger.info("Testing modern system with FIXED thresholds:")
    logger.info("  - Momentum: 50.0/100.0 (was 0.0003/0.001)")
    logger.info("  - Spread: 0.00005/0.000045 (was 2.5/8.0)")
    
    from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
    from hyperliquid_bot.config.settings import load_config
    
    # Load the FIXED config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Verify thresholds are fixed
    logger.info("\nVerifying threshold values:")
    logger.info(f"  mom_weak_thresh: {config.regime.gms_mom_weak_thresh}")
    logger.info(f"  mom_strong_thresh: {config.regime.gms_mom_strong_thresh}")
    logger.info(f"  spread_std_high: {config.regime.gms_spread_std_high_thresh}")
    logger.info(f"  spread_mean_low: {config.regime.gms_spread_mean_low_thresh}")
    
    # Run backtest with fixed thresholds
    logger.info("\nRunning backtest with FIXED thresholds...")
    engine = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=True  # Use hourly cache
    )
    
    results = engine.run_backtest()
    
    # Extract key metrics
    perf = results['performance']
    trades = perf['total_trades']
    returns = perf['total_return']
    win_rate = perf.get('win_rate', 0)
    
    logger.info("\n" + "=" * 80)
    logger.info("RESULTS WITH FIXED THRESHOLDS")
    logger.info("=" * 80)
    logger.info(f"Total trades: {trades}")
    logger.info(f"Total return: {returns:.2%}")
    logger.info(f"Win rate: {win_rate:.2%}")
    logger.info(f"Runtime: {results['runtime_seconds']:.1f}s")
    
    # Compare to targets
    logger.info("\n" + "=" * 80)
    logger.info("COMPARISON TO TARGETS")
    logger.info("=" * 80)
    logger.info(f"Legacy baseline: 180 trades, +215% ROI")
    logger.info(f"Modern broken: 222 trades, +41.78% ROI")
    logger.info(f"Modern fixed: {trades} trades, {returns:.2%} ROI")
    
    # Success criteria
    success = False
    if returns > 1.0:  # > 100% ROI
        logger.info("\n✅ SUCCESS: Returns exceed 100%!")
        success = True
    elif returns > 0.5:  # > 50% ROI
        logger.info("\n⚠️  PARTIAL SUCCESS: Returns improved but still below target")
    else:
        logger.info("\n❌ FAILED: Returns did not improve significantly")
    
    # Save detailed results
    results_data = {
        'test_date': datetime.now().isoformat(),
        'test_period': f"{start_date.date()} to {end_date.date()}",
        'threshold_fixes': {
            'momentum': 'Fixed 100,000x scale error',
            'spread': 'Fixed unit conversion error'
        },
        'results': {
            'total_trades': trades,
            'total_return': returns,
            'win_rate': win_rate,
            'runtime_seconds': results['runtime_seconds']
        },
        'comparison': {
            'legacy_baseline': {'trades': 180, 'roi': 2.15},
            'modern_broken': {'trades': 222, 'roi': 0.4178},
            'modern_fixed': {'trades': trades, 'roi': returns}
        },
        'success': success
    }
    
    with open('threshold_fix_results.json', 'w') as f:
        json.dump(results_data, f, indent=2)
    
    logger.info(f"\nDetailed results saved to: threshold_fix_results.json")
    
    # Recommendations
    logger.info("\n" + "=" * 80)
    logger.info("RECOMMENDATIONS")
    logger.info("=" * 80)
    
    if success:
        logger.info("1. Threshold fixes are working! Performance greatly improved.")
        logger.info("2. Consider fine-tuning thresholds further for optimal results.")
        logger.info("3. Test with hourly cadence (3600s) to reduce noise.")
    else:
        logger.info("1. Threshold fixes helped but more work needed.")
        logger.info("2. Consider switching to legacy detector: 'granular_microstructure'")
        logger.info("3. Check position management and exit logic.")
        logger.info("4. May need to adjust entry requirements (confidence, duration).")

if __name__ == "__main__":
    test_threshold_fixes()