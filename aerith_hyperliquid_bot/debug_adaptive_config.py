#!/usr/bin/env python3
"""
Debug script to check adaptive threshold configuration loading
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.core.gms_detector import ContinuousGMSDetector

def debug_config():
    """Debug the configuration loading for adaptive thresholds"""
    print("=== Debugging Adaptive Threshold Configuration ===")

    # Load config
    config = load_config('configs/base.yaml')

    print(f"Config loaded successfully: {config is not None}")

    # Check gms section
    gms_config = getattr(config, 'gms', None)
    print(f"GMS config exists: {gms_config is not None}")

    if gms_config:
        auto_thresholds = getattr(gms_config, 'auto_thresholds', None)
        print(f"auto_thresholds setting: {auto_thresholds}")
        print(f"auto_thresholds type: {type(auto_thresholds)}")

        # Print all gms config attributes
        print("\nGMS config attributes:")
        for attr in dir(gms_config):
            if not attr.startswith('_'):
                value = getattr(gms_config, attr)
                print(f"  {attr}: {value}")

    # Try to create detector with detailed debugging
    print("\n=== Creating ContinuousGMSDetector ===")
    try:
        # Add debug prints to see what's happening during initialization
        print(f"Config object type: {type(config)}")
        print(f"Config has gms attr: {hasattr(config, 'gms')}")

        if hasattr(config, 'gms'):
            print(f"Config.gms type: {type(config.gms)}")
            print(f"Config.gms.auto_thresholds: {getattr(config.gms, 'auto_thresholds', 'NOT_FOUND')}")

        # Create detector with debug
        detector = ContinuousGMSDetector(config)
        print(f"Detector created successfully")

        # Check detector's internal state
        print(f"Detector.cfg_gms: {detector.cfg_gms}")
        print(f"Detector.cfg_gms type: {type(detector.cfg_gms)}")
        if detector.cfg_gms:
            print(f"Detector.cfg_gms.auto_thresholds: {getattr(detector.cfg_gms, 'auto_thresholds', 'NOT_FOUND')}")

        print(f"Adaptive vol threshold: {detector.adaptive_vol_threshold is not None}")
        print(f"Adaptive mom threshold: {detector.adaptive_mom_threshold is not None}")

        if detector.adaptive_vol_threshold:
            print(f"Vol threshold config: low_pct={detector.adaptive_vol_threshold.low_pct}, high_pct={detector.adaptive_vol_threshold.high_pct}")
        if detector.adaptive_mom_threshold:
            print(f"Mom threshold config: low_pct={detector.adaptive_mom_threshold.low_pct}, high_pct={detector.adaptive_mom_threshold.high_pct}")

    except Exception as e:
        print(f"Error creating detector: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_config()
