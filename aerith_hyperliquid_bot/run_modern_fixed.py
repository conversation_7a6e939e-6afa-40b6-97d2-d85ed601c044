#!/usr/bin/env python3
"""
Run Modern system with PROPER 60s regime updates (no cache).
This should significantly improve performance by using fresh regime data.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import json
import yaml
from pathlib import Path

# Import after path setup
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
from hyperliquid_bot.config.settings import load_config

def run_modern_fixed():
    """Run modern system without regime cache to test true 60s updates"""
    
    print("="*60)
    print("MODERN SYSTEM - FIXED (60s Regime Updates)")
    print("="*60)
    
    # Load configuration
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Set test period - 1 month for meaningful results
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 31)
    
    print(f"\nTest Period: {start_date.date()} to {end_date.date()}")
    print("\nKey Changes:")
    print("✅ Regime cache DISABLED (use_regime_cache=False)")
    print("✅ Calculating regimes every 60 seconds")
    print("✅ Trading hourly with CURRENT regime (not stale)")
    print("-"*60)
    
    # Initialize engine WITHOUT cache
    print("\n🚀 Initializing Modern engine...")
    engine = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=False  # ← THE FIX!
    )
    
    print("✅ Engine initialized - will calculate regimes in real-time")
    print("   Note: This will be slower but more accurate")
    
    # Run backtest
    print("\n📊 Running backtest...")
    print("   Simulating 60 regime updates per hour...")
    print("   This may take a few minutes...\n")
    
    results = engine.run_backtest()
    
    # Process results
    if results:
        print("\n" + "="*60)
        print("RESULTS - MODERN FIXED:")
        print("="*60)
        
        # Extract key metrics
        total_return = results.get('total_return', 0)
        total_trades = results.get('total_trades', 0)
        win_rate = results.get('win_rate', 0)
        sharpe_ratio = results.get('sharpe_ratio', 0)
        max_drawdown = results.get('max_drawdown', 0)
        
        # Calculate profit per trade
        profit_per_trade = (total_return / total_trades * 100) if total_trades > 0 else 0
        
        print(f"\nPerformance Metrics:")
        print(f"  Total Return: {total_return:.2%}")
        print(f"  Total Trades: {total_trades}")
        print(f"  Win Rate: {win_rate:.1%}")
        print(f"  Profit/Trade: {profit_per_trade:.2%}")
        print(f"  Sharpe Ratio: {sharpe_ratio:.2f}")
        print(f"  Max Drawdown: {max_drawdown:.2%}")
        
        # Check regime statistics
        if hasattr(engine, 'regime_history') and engine.regime_history:
            regimes = [r.get('regime', r.get('state')) for r in engine.regime_history]
            unique_regimes = set(regimes)
            print(f"\nRegime Statistics:")
            print(f"  Total Updates: {len(regimes)}")
            print(f"  Unique States: {len(unique_regimes)}")
            
            # Count regime changes
            changes = sum(1 for i in range(1, len(regimes)) if regimes[i] != regimes[i-1])
            print(f"  Regime Changes: {changes}")
            print(f"  Updates/Hour: {len(regimes) / ((end_date - start_date).days * 24):.1f}")
        
        # Save results
        results_file = 'modern_fixed_results.json'
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n📁 Full results saved to: {results_file}")
        
        # Compare with previous results
        print("\n" + "="*60)
        print("COMPARISON:")
        print("="*60)
        print("\nModern (Cached Hourly):")
        print("  - Annual ROI: +41.78%")
        print("  - Trades: 222")
        print("  - Win Rate: 43.7%")
        print("  - Profit/Trade: 0.19%")
        
        print("\nModern (Fixed 60s):")
        print(f"  - Monthly ROI: {total_return:.2%}")
        print(f"  - Trades: {total_trades}")
        print(f"  - Win Rate: {win_rate:.1%}")
        print(f"  - Profit/Trade: {profit_per_trade:.2%}")
        
        print("\nLegacy (Target):")
        print("  - Annual ROI: +215%")
        print("  - Trades: 180")
        print("  - Win Rate: ~50%")
        print("  - Profit/Trade: 1.19%")
        
        # Analysis
        print("\n📈 ANALYSIS:")
        if profit_per_trade > 0.19:
            improvement = (profit_per_trade - 0.19) / 0.19 * 100
            print(f"✅ Profit per trade IMPROVED by {improvement:.0f}%!")
            print("   This confirms stale regimes were the problem!")
        else:
            print("❌ No improvement - need to investigate further")
        
        if changes > 5:
            print(f"✅ Regime changes: {changes} (vs ~3-5 with hourly)")
            print("   Confirms real 60s updates are happening")
        
        print("\n🎯 NEXT STEPS:")
        if profit_per_trade > 0.5:
            print("1. Performance improved significantly!")
            print("2. Consider implementing this fix permanently")
            print("3. Investigate 'imbalance' field for further gains")
        else:
            print("1. Check if regime updates are truly happening every 60s")
            print("2. Verify trade timing aligns with fresh regimes")
            print("3. Look for other differences vs Legacy")
            
    else:
        print("\n❌ No results returned - check for errors")

if __name__ == "__main__":
    try:
        run_modern_fixed()
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()