#!/usr/bin/env python3
"""
Quick test to run modern backtest and debug any issues
"""

import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine

def main():
    print("=" * 80)
    print("MODERN SYSTEM QUICK TEST")
    print("=" * 80)
    
    # Load config
    config_path = project_root / "configs" / "overrides" / "modern_system_v2_complete.yaml"
    print(f"Loading config: {config_path}")
    config = load_config(config_path=str(config_path))
    
    # Quick test with just 1 day
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 2)
    
    print(f"\nTest period: {start_date} to {end_date}")
    print(f"Risk per trade: {config.portfolio.risk_per_trade}")
    print(f"Detector type: {config.regime.detector_type}")
    print(f"Strategy: TF-v3 = {config.strategies.use_tf_v3}")
    
    # Create engine
    print("\nCreating backtesting engine...")
    engine = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date
    )
    
    # Run backtest
    print("\nRunning backtest...")
    try:
        results = engine.run_backtest()
        
        print("\n" + "=" * 80)
        print("RESULTS:")
        print("=" * 80)
        print(f"Total trades: {results['performance']['total_trades']}")
        print(f"Total return: {results['performance']['total_return']:.2%}")
        print(f"Runtime: {results['runtime_seconds']:.1f} seconds")
        
        if results['trades']:
            print(f"\nFirst trade:")
            trade = results['trades'][0]
            print(f"  - Time: {trade['timestamp']}")
            print(f"  - Direction: {trade['direction']}")
            print(f"  - Entry: {trade['entry_price']}")
            print(f"  - Size: {trade['position_size']:.2%}")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())