#!/usr/bin/env python3
"""
Data Loading Diagnos<PERSON> Script for Legacy System
Analyzes exactly what data is loaded and processed during a Legacy System backtest.
"""

import sys
import logging
from pathlib import Path
from datetime import datetime
import time

# Add the project root to Python path
project_root = Path(__file__).parent.resolve()
sys.path.insert(0, str(project_root))

def setup_diagnostic_logging():
    """Set up detailed logging for data loading analysis."""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s [%(levelname)-7s] %(name)-25s: %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # Set specific loggers to capture data loading details
    logging.getLogger("hyperliquid_bot.data.handler").setLevel(logging.DEBUG)
    logging.getLogger("hyperliquid_bot.data.feature_store").setLevel(logging.DEBUG)
    logging.getLogger("RegimeFactory").setLevel(logging.INFO)

def analyze_data_loading():
    """Run a minimal Legacy System setup and analyze data loading patterns."""
    print("=== LEGACY SYSTEM DATA LOADING DIAGNOSTIC ===")
    
    setup_diagnostic_logging()
    logger = logging.getLogger(__name__)
    
    # Import after logging setup
    from hyperliquid_bot.config.settings import Config
    from hyperliquid_bot.data.handler import HistoricalDataHandler
    import yaml
    from deepmerge import always_merger
    
    # Load configuration
    base_config_path = Path('configs/base.yaml')
    override_config_path = Path('configs/legacy_profile.yaml')
    
    logger.info(f"Loading base config: {base_config_path}")
    with open(base_config_path, 'r') as f:
        base_cfg = yaml.safe_load(f)
    
    logger.info(f"Loading override config: {override_config_path}")
    with open(override_config_path, 'r') as f:
        override_cfg = yaml.safe_load(f)
    
    # Merge configurations
    merged_cfg = always_merger.merge(base_cfg, override_cfg)
    config = Config(**merged_cfg)
    
    logger.info("=== CONFIGURATION ANALYSIS ===")
    logger.info(f"Detector Type: {config.regime.detector_type}")
    logger.info(f"Timeframe: {config.timeframe}")
    logger.info(f"Start Date: {config.backtest.custom_start_date}")
    logger.info(f"End Date: {config.backtest.custom_end_date}")
    logger.info(f"L2 Data Root: {config.data_paths.l2_data_root}")
    logger.info(f"OHLCV Base Path: {config.data_paths.ohlcv_base_path}")
    logger.info(f"Feature 1s Dir: {config.data_paths.feature_1s_dir}")
    
    # Initialize data handler
    logger.info("=== INITIALIZING DATA HANDLER ===")
    start_time = time.time()
    data_handler = HistoricalDataHandler(config)
    init_time = time.time() - start_time
    logger.info(f"Data handler initialization took: {init_time:.2f} seconds")
    
    # Load historical data
    logger.info("=== LOADING HISTORICAL DATA ===")
    start_date = config.backtest.custom_start_date
    end_date = config.backtest.custom_end_date
    
    load_start_time = time.time()
    data_handler.load_historical_data(start_date, end_date)
    load_total_time = time.time() - load_start_time
    
    logger.info(f"Total data loading took: {load_total_time:.2f} seconds")
    
    # Analyze loaded data
    logger.info("=== DATA ANALYSIS ===")
    combined_data = data_handler.get_ohlcv_data()
    
    logger.info(f"Final combined data shape: {combined_data.shape}")
    logger.info(f"Date range: {combined_data.index.min()} to {combined_data.index.max()}")
    logger.info(f"Columns ({len(combined_data.columns)}): {list(combined_data.columns)}")
    
    # Analyze data sources
    logger.info("=== DATA SOURCE ANALYSIS ===")
    
    # Check for OHLCV columns
    ohlcv_cols = ['open', 'high', 'low', 'close', 'volume']
    present_ohlcv = [col for col in ohlcv_cols if col in combined_data.columns]
    logger.info(f"OHLCV columns present: {present_ohlcv}")
    
    # Check for raw microstructure features (from L2 processing)
    raw_micro_cols = [col for col in combined_data.columns if col.startswith('raw_')]
    logger.info(f"Raw microstructure columns ({len(raw_micro_cols)}): {raw_micro_cols}")
    
    # Check for 1-second aggregated features
    feature_1s_cols = [col for col in combined_data.columns if any(x in col for x in ['atr_14_sec', 'atr_percent_sec', 'spread_mean', 'spread_std', 'ma_slope', 'obi_'])]
    logger.info(f"1-second aggregated features ({len(feature_1s_cols)}): {feature_1s_cols}")
    
    # Check for Fear & Greed data
    fg_cols = [col for col in combined_data.columns if 'fear_greed' in col]
    logger.info(f"Fear & Greed columns: {fg_cols}")
    
    # Analyze NaN counts
    logger.info("=== NaN ANALYSIS ===")
    for col in combined_data.columns:
        nan_count = combined_data[col].isna().sum()
        nan_pct = (nan_count / len(combined_data)) * 100
        if nan_count > 0:
            logger.warning(f"Column '{col}': {nan_count}/{len(combined_data)} ({nan_pct:.1f}%) NaN values")
        else:
            logger.debug(f"Column '{col}': 0 NaN values")
    
    # Check data paths that were accessed
    logger.info("=== DATA PATH ANALYSIS ===")
    
    # Check what OHLCV files exist
    ohlcv_path = Path(config.data_paths.ohlcv_base_path) / config.timeframe
    if ohlcv_path.exists():
        ohlcv_files = list(ohlcv_path.glob("*.parquet"))
        logger.info(f"OHLCV files available in {ohlcv_path}: {len(ohlcv_files)} files")
        for f in sorted(ohlcv_files)[:5]:  # Show first 5
            logger.debug(f"  - {f.name}")
        if len(ohlcv_files) > 5:
            logger.debug(f"  ... and {len(ohlcv_files) - 5} more files")
    
    # Check what L2 files exist
    l2_path = Path(config.data_paths.l2_data_root)
    if l2_path.exists():
        l2_files = list(l2_path.glob("*_raw2.parquet"))
        logger.info(f"L2 files available in {l2_path}: {len(l2_files)} files")
        for f in sorted(l2_files)[:5]:  # Show first 5
            logger.debug(f"  - {f.name}")
        if len(l2_files) > 5:
            logger.debug(f"  ... and {len(l2_files) - 5} more files")
    
    # Check what 1-second feature files exist
    feature_1s_path = Path(config.data_paths.feature_1s_dir)
    if feature_1s_path.exists():
        feature_dirs = [d for d in feature_1s_path.iterdir() if d.is_dir()]
        logger.info(f"1-second feature directories available: {len(feature_dirs)}")
        for d in sorted(feature_dirs)[:5]:  # Show first 5
            feature_files = list(d.glob("features_*.parquet"))
            logger.debug(f"  - {d.name}: {len(feature_files)} feature files")
        if len(feature_dirs) > 5:
            logger.debug(f"  ... and {len(feature_dirs) - 5} more directories")
    
    logger.info("=== DIAGNOSTIC COMPLETE ===")
    logger.info(f"Total execution time: {time.time() - load_start_time:.2f} seconds")
    
    return {
        'config': config,
        'data_shape': combined_data.shape,
        'columns': list(combined_data.columns),
        'load_time': load_total_time,
        'raw_micro_cols': raw_micro_cols,
        'feature_1s_cols': feature_1s_cols,
        'ohlcv_cols': present_ohlcv,
        'fg_cols': fg_cols
    }

if __name__ == "__main__":
    try:
        results = analyze_data_loading()
        print("\n=== SUMMARY ===")
        print(f"Data shape: {results['data_shape']}")
        print(f"Total columns: {len(results['columns'])}")
        print(f"Load time: {results['load_time']:.2f} seconds")
        print(f"Raw microstructure features: {len(results['raw_micro_cols'])}")
        print(f"1-second aggregated features: {len(results['feature_1s_cols'])}")
    except Exception as e:
        print(f"Error during diagnostic: {e}")
        import traceback
        traceback.print_exc() 