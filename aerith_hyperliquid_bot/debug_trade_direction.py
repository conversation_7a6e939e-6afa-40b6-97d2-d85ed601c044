#!/usr/bin/env python3
"""
Quick analysis of trade direction distribution: Modern vs Legacy
Focuses on the user's concern about long-only trades in modern system
"""

import sys
import os
import pandas as pd
import numpy as np
sys.path.append('/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot')

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.data.handler import HistoricalDataHandler
from hyperliquid_bot.signals import SignalEngine  
from hyperliquid_bot.core import get_regime_detector
from hyperliquid_bot.strategies import StrategyEvaluator
from hyperliquid_bot.utils.state_mapping import map_gms_state, get_bear_states, get_bull_states
from datetime import datetime

def analyze_regime_distribution():
    """Analyze what regimes each system detects and how they map to trade directions"""
    
    print("=== TRADE DIRECTION ANALYSIS ===")
    print("Investigating why modern system may be long-only vs legacy balanced approach\n")
    
    # Test both systems on a sample period
    configs = {
        'legacy': '/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/configs/base.yaml',
        'modern': '/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/configs/overrides/conservative_modern.yaml'
    }
    
    results = {}
    
    for system_name, config_path in configs.items():
        print(f"\n--- Analyzing {system_name.upper()} System ---")
        
        try:
            # Load config
            if system_name == 'legacy':
                config = Config(config_path)
                # Force legacy settings
                config.regime.detector_type = 'granular_microstructure'
                config.strategies.use_tf_v2 = True
                config.strategies.use_tf_v3 = False
            else:
                # Use conservative modern config
                config = Config('/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/configs/base.yaml')
                # Override for modern system
                config.regime.detector_type = 'continuous_gms'
                config.strategies.use_tf_v2 = False
                config.strategies.use_tf_v3 = True
                # Conservative thresholds
                config.regime.gms_mom_strong_thresh = 75.0
                config.regime.gms_mom_weak_thresh = 35.0
                config.regime.gms_vol_high_thresh = 0.015
                config.regime.gms_vol_low_thresh = 0.005
            
            print(f"Detector: {config.regime.detector_type}")
            print(f"Strategies: TF-v2={getattr(config.strategies, 'use_tf_v2', False)}, TF-v3={getattr(config.strategies, 'use_tf_v3', False)}")
            
            # Load sample data (Jan 2024 - 2 weeks)
            data_handler = HistoricalDataHandler(config)
            start_date = datetime(2024, 1, 1)
            end_date = datetime(2024, 1, 15)
            
            print(f"Loading data: {start_date.date()} to {end_date.date()}")
            raw_data = data_handler.load_historical_data(start_date=start_date, end_date=end_date)
            
            if raw_data is None or len(raw_data) == 0:
                print(f"❌ No data loaded for {system_name}")
                continue
                
            print(f"Data shape: {raw_data.shape}")
            
            # Calculate signals
            signal_engine = SignalEngine(config, data_handler)
            signals_df = signal_engine.calculate_signals(raw_data)
            print(f"Signals shape: {signals_df.shape}")
            
            # Get regime detector
            regime_detector = get_regime_detector(config)
            print(f"Regime detector: {type(regime_detector).__name__}")
            
            # Analyze regimes over the sample period
            regime_counts = {}
            bull_count = 0
            bear_count = 0
            chop_count = 0
            
            sample_size = min(500, len(signals_df))  # Analyze first 500 hours
            
            for i in range(sample_size):
                row = signals_df.iloc[i]
                signals_dict = row.to_dict()
                
                # Get regime
                regime_result = regime_detector.get_regime(signals_dict)
                
                # Handle different return formats
                if isinstance(regime_result, dict):
                    regime_state = regime_result.get('state', 'Unknown')
                else:
                    regime_state = regime_result
                
                # Count raw regime states
                regime_counts[regime_state] = regime_counts.get(regime_state, 0) + 1
                
                # Map to 3-state for trading
                mapped_state = map_gms_state(regime_state, map_weak_bear_to_bear=False)
                
                if mapped_state == 'BULL':
                    bull_count += 1
                elif mapped_state == 'BEAR':
                    bear_count += 1
                else:
                    chop_count += 1
            
            total_analyzed = bull_count + bear_count + chop_count
            
            results[system_name] = {
                'regime_counts': regime_counts,
                'bull_pct': (bull_count / total_analyzed) * 100 if total_analyzed > 0 else 0,
                'bear_pct': (bear_count / total_analyzed) * 100 if total_analyzed > 0 else 0,
                'chop_pct': (chop_count / total_analyzed) * 100 if total_analyzed > 0 else 0,
                'total_analyzed': total_analyzed
            }
            
            print(f"\n📊 Regime Distribution (first {sample_size} hours):")
            print(f"  🟢 BULL: {bull_count:3d} ({bull_count/total_analyzed*100:5.1f}%)")
            print(f"  🔴 BEAR: {bear_count:3d} ({bear_count/total_analyzed*100:5.1f}%)")
            print(f"  🟡 CHOP: {chop_count:3d} ({chop_count/total_analyzed*100:5.1f}%)")
            
            print(f"\n📈 Raw Regime States:")
            for state, count in sorted(regime_counts.items(), key=lambda x: x[1], reverse=True):
                pct = (count / total_analyzed) * 100
                print(f"  {state}: {count:3d} ({pct:5.1f}%)")
                
        except Exception as e:
            print(f"❌ Error analyzing {system_name}: {e}")
            import traceback
            traceback.print_exc()
    
    # Compare results
    print(f"\n{'='*50}")
    print("🔍 COMPARISON ANALYSIS")
    print(f"{'='*50}")
    
    if 'legacy' in results and 'modern' in results:
        legacy = results['legacy']
        modern = results['modern']
        
        print(f"\nBULL/BEAR/CHOP Distribution:")
        print(f"{'System':<10} {'BULL':<8} {'BEAR':<8} {'CHOP':<8}")
        print(f"{'─'*40}")
        print(f"{'Legacy':<10} {legacy['bull_pct']:5.1f}%   {legacy['bear_pct']:5.1f}%   {legacy['chop_pct']:5.1f}%")
        print(f"{'Modern':<10} {modern['bull_pct']:5.1f}%   {modern['bear_pct']:5.1f}%   {modern['chop_pct']:5.1f}%")
        
        bear_diff = modern['bear_pct'] - legacy['bear_pct']
        print(f"\n🎯 KEY FINDING:")
        print(f"Bear regime difference: {bear_diff:+.1f}% (Modern vs Legacy)")
        
        if abs(bear_diff) > 10:
            print(f"⚠️  SIGNIFICANT DIFFERENCE! Modern system shows {bear_diff:+.1f}% bear regimes vs legacy")
            
            if bear_diff < -10:
                print("🔧 LIKELY CAUSE: Modern system not detecting bear regimes properly")
                print("   - Check threshold calibration")
                print("   - Check state mapping configuration") 
                print("   - Verify map_weak_bear_to_bear setting")
        else:
            print(f"✅ Bear regime detection is similar between systems")
            print(f"🔧 Issue may be in strategy logic, not regime detection")
    
    # Show expected bear states for reference
    print(f"\n📚 Reference: Expected BEAR states from mapping:")
    bear_states = get_bear_states(map_weak_bear_to_bear=False)
    bull_states = get_bull_states(map_weak_bear_to_bear=False)
    print(f"  BEAR states: {bear_states}")
    print(f"  BULL states: {bull_states}")
    print(f"  Note: map_weak_bear_to_bear=False means Weak_Bear_Trend → CHOP (not BEAR)")

if __name__ == "__main__":
    analyze_regime_distribution()