#!/usr/bin/env python3
"""
Analyze March 2025 data to determine appropriate thresholds for continuous_gms detector.
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from hyperliquid_bot.data.handler import HistoricalDataHandler
from hyperliquid_bot.signals.calculator import SignalEngine
from hyperliquid_bot.config.settings import load_config, Config
import yaml
from deepmerge import always_merger

def analyze_march_data():
    """Analyze the March 2025 data to understand value distributions."""

    print("=== March 2025 Data Analysis for GMS Threshold Tuning ===")

    # Load configuration with override
    base_config_path = "configs/base.yaml"
    override_config_path = "configs/r112p_override.yaml"

    # Load base config
    with open(base_config_path, 'r') as f:
        base_config_data = yaml.safe_load(f)

    # Load override config
    with open(override_config_path, 'r') as f:
        override_config_data = yaml.safe_load(f)

    # Merge override into base
    config_data = always_merger.merge(base_config_data, override_config_data)

    # Create Config object
    config = Config(**config_data)

    # Initialize data handler
    data_handler = HistoricalDataHandler(config)

    # Load March data
    print("Loading March 2025 data...")
    start_date = pd.Timestamp("2025-03-01")
    end_date = pd.Timestamp("2025-03-23")  # Exclusive end

    # Load historical data first
    data_handler.load_historical_data(start_date, end_date)

    # Then get the loaded data
    ohlcv_data = data_handler.get_ohlcv_data()
    print(f"Loaded data shape: {ohlcv_data.shape}")
    print(f"Date range: {ohlcv_data.index[0]} to {ohlcv_data.index[-1]}")

    # Calculate signals
    print("\nCalculating signals...")
    signal_engine = SignalEngine(config, data_handler)
    signals_df = signal_engine.calculate_all_signals()

    print(f"Signals calculated. Shape: {signals_df.shape}")
    print(f"Available columns: {list(signals_df.columns)}")

    # Analyze key metrics for GMS detector
    print("\n=== KEY METRICS ANALYSIS ===")

    # 1. ATR Percent (Volatility)
    if 'atr_percent_sec' in signals_df.columns:
        atr_pct = signals_df['atr_percent_sec'].dropna()
        print(f"\n1. ATR Percent (Volatility) - {len(atr_pct)} values:")
        print(f"   Min: {atr_pct.min():.6f}")
        print(f"   25th percentile: {atr_pct.quantile(0.25):.6f}")
        print(f"   Median: {atr_pct.median():.6f}")
        print(f"   75th percentile: {atr_pct.quantile(0.75):.6f}")
        print(f"   Max: {atr_pct.max():.6f}")
        print(f"   Current thresholds: Low=0.0100, High=0.0300")

        # Suggest new thresholds
        low_thresh = atr_pct.quantile(0.33)
        high_thresh = atr_pct.quantile(0.67)
        print(f"   SUGGESTED: Low={low_thresh:.6f}, High={high_thresh:.6f}")

    # 2. Momentum (MA Slope)
    if 'ma_slope' in signals_df.columns:
        ma_slope = signals_df['ma_slope'].dropna()
        print(f"\n2. Momentum (MA Slope) - {len(ma_slope)} values:")
        print(f"   Min: {ma_slope.min():.6f}")
        print(f"   25th percentile: {ma_slope.quantile(0.25):.6f}")
        print(f"   Median: {ma_slope.median():.6f}")
        print(f"   75th percentile: {ma_slope.quantile(0.75):.6f}")
        print(f"   Max: {ma_slope.max():.6f}")
        print(f"   Current thresholds: Weak=0.50, Strong=2.50")

        # Suggest new thresholds based on actual data
        weak_thresh = abs(ma_slope.quantile(0.33))
        strong_thresh = abs(ma_slope.quantile(0.67))
        print(f"   SUGGESTED: Weak={weak_thresh:.6f}, Strong={strong_thresh:.6f}")

    # 3. OBI
    if 'raw_obi_5' in signals_df.columns:
        obi = signals_df['raw_obi_5'].dropna()
        print(f"\n3. OBI - {len(obi)} values:")
        print(f"   Min: {obi.min():.6f}")
        print(f"   25th percentile: {obi.quantile(0.25):.6f}")
        print(f"   Median: {obi.median():.6f}")
        print(f"   75th percentile: {obi.quantile(0.75):.6f}")
        print(f"   Max: {obi.max():.6f}")
        print(f"   Current thresholds: Weak=0.110, Strong=0.200")

        # Suggest new thresholds
        weak_thresh = abs(obi.quantile(0.33))
        strong_thresh = abs(obi.quantile(0.67))
        print(f"   SUGGESTED: Weak={weak_thresh:.6f}, Strong={strong_thresh:.6f}")

    # 4. Spread Statistics
    if 'spread_mean' in signals_df.columns and 'spread_std' in signals_df.columns:
        spread_mean = signals_df['spread_mean'].dropna()
        spread_std = signals_df['spread_std'].dropna()

        print(f"\n4. Spread Mean - {len(spread_mean)} values:")
        print(f"   Min: {spread_mean.min():.8f}")
        print(f"   25th percentile: {spread_mean.quantile(0.25):.8f}")
        print(f"   Median: {spread_mean.median():.8f}")
        print(f"   75th percentile: {spread_mean.quantile(0.75):.8f}")
        print(f"   Max: {spread_mean.max():.8f}")
        print(f"   Current threshold: Low=0.000100")

        mean_low_thresh = spread_mean.quantile(0.33)
        print(f"   SUGGESTED: Low={mean_low_thresh:.8f}")

        print(f"\n5. Spread Std - {len(spread_std)} values:")
        print(f"   Min: {spread_std.min():.8f}")
        print(f"   25th percentile: {spread_std.quantile(0.25):.8f}")
        print(f"   Median: {spread_std.median():.8f}")
        print(f"   75th percentile: {spread_std.quantile(0.75):.8f}")
        print(f"   Max: {spread_std.max():.8f}")
        print(f"   Current threshold: High=0.000500")

        std_high_thresh = spread_std.quantile(0.67)
        print(f"   SUGGESTED: High={std_high_thresh:.8f}")

    # Generate suggested configuration
    print("\n=== SUGGESTED THRESHOLD CONFIGURATION ===")
    print("Add this to your override configuration:")
    print()
    print("continuous_gms:")
    if 'atr_percent_sec' in signals_df.columns:
        atr_pct = signals_df['atr_percent_sec'].dropna()
        low_vol = atr_pct.quantile(0.33)
        high_vol = atr_pct.quantile(0.67)
        print(f"  gms_vol_low_thresh: {low_vol:.6f}")
        print(f"  gms_vol_high_thresh: {high_vol:.6f}")

    if 'ma_slope' in signals_df.columns:
        ma_slope = signals_df['ma_slope'].dropna()
        weak_mom = abs(ma_slope.quantile(0.33))
        strong_mom = abs(ma_slope.quantile(0.67))
        print(f"  gms_mom_weak_thresh: {weak_mom:.6f}")
        print(f"  gms_mom_strong_thresh: {strong_mom:.6f}")

    if 'raw_obi_5' in signals_df.columns:
        obi = signals_df['raw_obi_5'].dropna()
        weak_obi = abs(obi.quantile(0.33))
        strong_obi = abs(obi.quantile(0.67))
        print(f"  gms_obi_weak_thresh: {weak_obi:.6f}")
        print(f"  gms_obi_strong_thresh: {strong_obi:.6f}")

    if 'spread_mean' in signals_df.columns and 'spread_std' in signals_df.columns:
        spread_mean = signals_df['spread_mean'].dropna()
        spread_std = signals_df['spread_std'].dropna()
        mean_low = spread_mean.quantile(0.33)
        std_high = spread_std.quantile(0.67)
        print(f"  gms_spread_mean_low_thresh: {mean_low:.8f}")
        print(f"  gms_spread_std_high_thresh: {std_high:.8f}")

if __name__ == "__main__":
    analyze_march_data()
