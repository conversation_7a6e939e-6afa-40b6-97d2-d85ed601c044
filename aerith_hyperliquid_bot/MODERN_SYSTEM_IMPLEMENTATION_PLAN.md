# Modern System Implementation Plan: 60s Regime Updates + Hourly Trading

## Executive Summary

The modern system architecture requires a fundamental shift from the current implementation. The system must:
1. Update regime states every 60 seconds for market awareness
2. Evaluate trading opportunities only hourly
3. Use three distinct data frequencies for different purposes
4. Properly simulate this hybrid timing in the backtester
5. Prepare architecture for future live trading deployment

**Current Status**: 0 trades generated due to architectural misalignment
**Key Insight**: cadence_sec: 60 means regime STATE updates, NOT trading frequency

## Core Architecture Principles

### 1. Temporal Separation of Concerns
```
Regime Detection: 60-second updates (market state awareness)
Trading Evaluation: 60-minute intervals (strategy decisions)
Execution Refinement: 1-minute granularity (entry/exit optimization)
```

### 2. Data Usage Hierarchy
```
features_1s/ → 60s regime updates → Current market state
resampled_l2/1h/ → Hourly strategy → Trading signals
features_1s/ → 1m refinement → Precise entry/exit
```

### 3. Backtesting vs Live Trading Architecture

#### Backtesting Mode
```python
# Must simulate different timeframes:
for each hour in backtest:
    # Simulate 60 GMS updates
    for minute in range(60):
        gms.update(features_1s[minute])  # Update state
    
    # Hour complete - check for trade
    if minute == 59:
        signal = tf_v3.evaluate(hourly_bar, gms.current_state)
```

#### Live Trading Mode (Future)
```python
# Natural flow with timers:
every_60_seconds():
    gms.update(latest_features)  # WebSocket → features
    
every_3600_seconds():  
    signal = tf_v3.evaluate(latest_hourly, gms.current_state)
```

## Critical: Look-Ahead Bias Prevention

### Zero-Tolerance Policy for Look-Ahead Bias

The modern system MUST NOT peek at future data at any point. This is CRUCIAL for:
1. **Data Aggregation**: Correct labeling (right label, left closed)
2. **Regime Detection**: Only past data for state updates
3. **Trading Signals**: Decisions based on completed bars only
4. **Execution Refinement**: Future improvements not visible

### Look-Ahead Bias Test Requirements

```python
# Every new component MUST pass these tests:
1. test_data_aggregation_labels - Resampling uses correct timestamps
2. test_regime_detector_causality - Regime can't see future trends
3. test_execution_refinement_timing - Can't see future spreads
4. test_indicator_calculation_causality - Indicators are causal
5. test_backtester_trade_entry_timing - Trades at correct time
```

### Key Prevention Patterns

```python
# CORRECT: Right label, left closed (no look-ahead)
df.resample('1h', label='right', closed='left')

# WRONG: Would include future data in current bar
df.resample('1h', label='left', closed='right')

# CORRECT: Only use data up to current timestamp
available_data = df[df.index <= current_time]

# WRONG: Includes future data
all_data = df  # Dangerous if not filtered by time
```

## Implementation Phases

### Phase 1: Data Pipeline Verification (2-3 hours)

#### Step 1.1: Verify Features_1s Data Availability
```python
# Create script: scripts/verify_modern_data.py
# Tasks:
- Check features_1s/ directory structure
- Verify data completeness for 2024
- Confirm schema includes required fields
- Test data loading performance
```

#### Step 1.2: Create Data Aggregation Layer (WITH LOOK-AHEAD PREVENTION)
```python
# Create: modern/data_aggregator.py
class ModernDataAggregator:
    """CRITICAL: All aggregation MUST prevent look-ahead bias"""
    
    def aggregate_to_60s(self, data_1s, current_time):
        """Aggregate 1s data to 60s windows
        
        MUST use label='right', closed='left' to prevent look-ahead
        """
        # Filter to only past data
        past_data = data_1s[data_1s.index <= current_time]
        
        # Aggregate with proper labeling
        return past_data.resample('60s', label='right', closed='left').agg({
            'open': 'first',
            'high': 'max', 
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        })
    
    def get_hourly_bar(self, data, current_time):
        """Get completed hourly bar at current time
        
        MUST NOT include incomplete bar data
        """
        # Only return completed bars
        completed_bars = data[data.index < current_time]
        return completed_bars.resample('1h', label='right', closed='left').last()
```

#### Step 1.3: Schema Alignment
```python
# Update: modern/data_loader.py
# Tasks:
- Ensure volume_imbalance → imbalance mapping
- Verify all required features present
- Add data validation checks
- Handle missing data gracefully
```

### Phase 2: Regime Detection Architecture (3-4 hours)

#### Step 2.1: Create Regime State Manager
```python
# Create: modern/regime_state_manager.py
class RegimeStateManager:
    """Maintains 60-second regime state history for both backtest and live trading"""
    
    def __init__(self, mode='backtest'):
        self.mode = mode  # 'backtest' or 'live'
        self.state_history = []  # [(timestamp, state, confidence)]
        self.current_state = GMS_STATE_UNCERTAIN
        self.state_duration = 0
        
        # TODO: Live trading placeholder
        if mode == 'live':
            # Future: Initialize WebSocket connection
            # Future: Set up 60-second timer
            pass
        
    def update(self, timestamp, features):
        """Called every 60 seconds with new features
        
        Backtest: Called by simulation loop
        Live: Called by timer/WebSocket handler
        """
        
    def get_dominant_state(self, start_time, end_time):
        """Returns dominant state over time window"""
        
    def get_state_transitions(self, window_minutes=60):
        """Returns state changes in last N minutes"""
```

#### Step 2.2: Modify Modern Detector ✅ COMPLETED
```python
# Update: modern/detector.py
# Tasks:
- Remove hourly cadence assumption ✅
- Ensure 60-second update capability ✅
- Add state persistence methods ✅
- Improve logging for debugging ✅

# Completed:
- Created ModernContinuousDetector with 60s cadence
- Integrated with RegimeStateManager
- Added comprehensive test suite
- Fixed config threshold issues (momentum: 50.0/100.0)
```

#### Step 2.3: Implement Adaptive Threshold Optimization ⚠️ RECONSIDERED
```python
# Decision: SKIP adaptive thresholds for modern backtesting
# Reasons:
1. Potential look-ahead bias concerns
2. 655s runtime bottleneck in legacy system
3. Fixed thresholds are simpler and more predictable
4. Modern detector already uses fixed thresholds

# Alternative approach:
- Use fixed, well-calibrated thresholds in backtest mode
- Reserve adaptive thresholds for live trading only
- This ensures deterministic backtests and faster runtime

# Future enhancement (for live trading):
- Implement streaming adaptive thresholds
- Update thresholds with each new data point
- No priming needed - starts from first live data
```

### Phase 3: Trading Strategy Architecture (4-5 hours)

#### Step 3.1: Create Hourly Strategy Evaluator ✅ COMPLETED
```python
# Create: modern/hourly_evaluator.py
class HourlyStrategyEvaluator:
    """Evaluates trading opportunities hourly using regime history"""
    
    def __init__(self, regime_manager, strategy, mode='backtest'):
        self.regime_manager = regime_manager
        self.strategy = strategy
        self.mode = mode
        self.last_evaluation = None
        
        # TODO: Live trading placeholder
        if mode == 'live':
            # Future: Set up hourly timer
            # Future: Connect to order management system
            pass
        
    def should_evaluate(self, timestamp):
        """Returns True if hour boundary reached
        
        Backtest: Check simulated time
        Live: Check system clock
        """
        
    def evaluate(self, hourly_bar, regime_history):
        """Generate trading signals using:
        - Current hourly bar
        - Last 60 regime states
        - Market microstructure
        """
        
    async def evaluate_live(self):
        """TODO: Live trading evaluation
        - Fetch latest hourly bar
        - Get regime history from manager
        - Generate and execute signals
        """
        pass

# Completed:
- Created HourlyStrategyEvaluator class 
- Integrated with RegimeStateManager
- Added risk management (position sizing, volatility adjustment)
- Created comprehensive test suite (10 tests passing)
- Fixed config field name (risk_per_trade not risk_frac)
```

#### Step 3.2: Update TF-v3 Strategy ✅ COMPLETED
```python
# Update: modern/strategy.py (TF-v3)
# Tasks:
- Accept regime history as input ✅
- Use dominant regime for decisions ✅
- Consider regime stability ✅
- Implement proper risk sizing ✅

# Completed:
- Created ModernTFV3Strategy (tf_v3_modern.py)
- Accepts regime features from HourlyStrategyEvaluator
- Checks regime stability (duration, changes, confidence)
- Only trades in trending regimes (BULL/BEAR)
- Created comprehensive test suite (14 tests passing)
```

#### Step 3.3: Fix Risk Management Parameters ✅ COMPLETED
```yaml
# Update: configs/overrides/modern_system.yaml
tf_v3:
  risk_frac: 0.25  # Match legacy 25% (was 2%)
  
  # Regime-based risk scaling
  strong_trend_risk_mult: 1.2   # Was 1.5
  weak_trend_risk_mult: 1.0     # Was 0.8
  range_risk_mult: 0.8          # Was 0.5

# Completed:
- Updated risk_frac from 0.02 to 0.25 (2% → 25%)
- Adjusted regime multipliers for 25% base:
  - Strong trend: 1.2 (30% total risk)
  - Weak trend: 1.0 (25% total risk)
  - Range: 0.8 (20% total risk)
- Risk levels now match legacy system for fair comparison
```

### Phase 4: Backtester Integration (5-6 hours)

#### Step 4.1: Create Modern Backtesting Engine ✅ COMPLETED
```python
# Create: modern/backtester_engine.py
class ModernBacktestEngine:
    """Handles 60s regime + hourly trading simulation
    
    This engine properly simulates the different timeframes required
    for the modern system architecture.
    """
    
    def __init__(self):
        self.mode = 'backtest'  # Always backtest for this engine
        
    def run_backtest(self, start_date, end_date):
        """Simulate modern system with proper timing
        
        Critical: Must simulate 60 GMS updates per hour!
        CRITICAL: Must prevent ALL look-ahead bias!
        """
        # 1. Load all 1-second data
        all_data = self.load_features_1s(start_date, end_date)
        
        # 2. For each hour in chronological order:
        for hour_start in hourly_timestamps:
            hour_end = hour_start + timedelta(hours=1)
            
            # a. Simulate 60 regime updates (NO LOOK-AHEAD)
            for minute in range(60):
                current_time = hour_start + timedelta(minutes=minute)
                
                # Only use data up to current_time
                available_data = all_data[all_data.index <= current_time]
                features_60s = self.aggregate_to_60s(available_data, current_time)
                
                # Update regime with only past data
                regime_state = gms.update(features_60s.iloc[-1])
            
            # b. Build regime history (last 60 states)
            regime_history = gms.get_state_history(60)
            
            # c. Evaluate trading at hour end (ONLY ON COMPLETED BAR)
            completed_hourly_bar = self.get_completed_bar(all_data, hour_end)
            signal = tf_v3.evaluate(completed_hourly_bar, regime_history)
            
            # d. Refine execution with 1m data (ONLY PAST DATA)
            if signal:
                exec_window_data = all_data[
                    (all_data.index >= hour_end) & 
                    (all_data.index < hour_end + timedelta(minutes=5))
                ]
                entry_price = self.refine_execution(exec_window_data)
        
    def prepare_live_trading_hooks(self):
        """TODO: Placeholder for live trading preparation
        
        Future implementation will:
        - Initialize WebSocket connections
        - Set up timer callbacks
        - Connect to order management
        - Enable real-time monitoring
        """
        raise NotImplementedError("Live trading not yet implemented")
```

#### Step 4.2: Modify Main Backtester ✅ COMPLETED
```python
# Update: backtester/backtester.py
# Tasks:
- Detect modern system mode
- Route to ModernBacktestEngine
- Maintain backward compatibility
- Add performance profiling

# Completed:
- Created run_backtest_modern_patch.py with routing logic
- Added system_mode field to Config class
- Created integrate_modern_engine.py script
- Modern engine routing based on:
  - system_mode == "modern" in config
  - OR detector_type == "continuous_modern" (fallback)
- Updated run_modern_backtest.py script with new architecture
```

#### Step 4.3: Create Execution Refinement ✅ COMPLETED
```python
# Create: modern/execution_refiner.py
# Tasks:
- Use 1-minute data for entry/exit
- Optimize based on microstructure
- Reduce slippage estimates
- Improve fill assumptions

# Completed:
- Created ExecutionRefiner class with smart order timing
- Analyzes 5-minute window of 1-second data
- Optimizes entry based on:
  - Bid-ask spread dynamics
  - Volume patterns
  - Short-term momentum
  - Order book imbalance
- Calculates execution quality score
- Tracks price improvement metrics
- Integrated with ModernBacktestEngine
- Added performance analysis for execution quality
```

### Phase 5: Threshold Calibration (3-4 hours)

#### Step 5.1: Analyze Historical Regime Patterns
```python
# Create: scripts/analyze_regime_patterns.py
# Tasks:
- Count regime states over 2024
- Measure state durations
- Identify transition patterns
- Compare with legacy detector
```

#### Step 5.2: Calibrate Detection Thresholds
```yaml
# Create: configs/overrides/modern_calibrated.yaml
regime:
  detector_type: "continuous_gms"
  cadence_sec: 60  # CRITICAL: NOT 3600!
  
  # Start conservative, then tune
  gms_vol_high_thresh: 0.02      # Was 0.03
  gms_vol_low_thresh: 0.008      # Was 0.01
  gms_mom_strong_thresh: 5.0     # Was 2.5
  gms_mom_weak_thresh: 1.0       # Was 0.5
  
  # OBI thresholds
  gms_obi_strong_confirm_thresh: 0.12  # Was 0.15
  gms_obi_weak_confirm_thresh: 0.06    # Was 0.08

tf_v3:
  risk_frac: 0.25  # CRITICAL: Match legacy, not 0.02!
  min_regime_confidence: 0.5  # Not too restrictive
```

#### Step 5.3: Progressive Testing
```bash
# Test sequence:
1. Test with 1 month data
2. Verify regime updates working
3. Confirm hourly evaluation
4. Check trade generation
5. Expand to full 2024
```

### Phase 6: Validation & Optimization (2-3 hours)

#### Step 6.1: Create Comprehensive Test Suite (INCLUDING LOOK-AHEAD TESTS)
```python
# Create: tests/test_modern_system.py
# Tests:
- Regime updates every 60s
- Trades only on hourly boundaries
- State persistence across updates
- Performance benchmarks
- NO LOOK-AHEAD BIAS IN ANY COMPONENT

# Expand: tests/test_look_ahead_bias.py
# Add modern system specific tests:

def test_modern_60s_aggregation_no_look_ahead(self):
    """Test 60s aggregation doesn't see future data"""
    # Create 1s data with future spike
    # Verify 60s aggregation doesn't include spike early
    
def test_modern_regime_history_causality(self):
    """Test regime history only includes past states"""
    # Simulate regime updates
    # Verify history at time T doesn't include T+1 state
    
def test_modern_hourly_evaluation_timing(self):
    """Test hourly evaluation uses only completed bars"""
    # Create incomplete hourly bar
    # Verify evaluation doesn't use incomplete data
    
def test_modern_execution_refinement_window(self):
    """Test execution window doesn't see beyond 5 minutes"""
    # Create data with price improvement at minute 6
    # Verify refinement doesn't see it
```

#### Step 6.2: Performance Profiling
```python
# Create: scripts/profile_modern_system.py
# Profile:
- Data loading time
- Regime detection overhead
- Strategy evaluation cost
- Total backtest runtime
```

#### Step 6.3: Create Comparison Framework
```python
# Create: scripts/compare_systems.py
# Compare:
- Trade count
- ROI
- Sharpe ratio
- Regime state distribution
- Execution quality
```

## Critical Success Factors

### 1. Correct Temporal Architecture
- ✅ Regime updates: Every 60 seconds
- ✅ Trade evaluation: Every 60 minutes
- ✅ Clear separation of concerns

### 2. Proper Data Usage
- ✅ 1-second features for regime detection
- ✅ Hourly bars for strategy signals
- ✅ 1-minute data for execution refinement

### 3. Performance Targets
- ✅ Generate 100-200 trades (not 0 or 957)
- ✅ Achieve >100% ROI
- ✅ Backtest runtime <60 seconds

### 4. Risk Management
- ✅ Fix risk_frac: 0.25 (not 0.02)
- ✅ Appropriate position sizing
- ✅ Regime-aware risk scaling

## Common Pitfalls to Avoid

### 1. Timing Confusion
❌ Don't evaluate trades every 60 seconds
❌ Don't update regime only hourly
❌ Don't mix update vs evaluation logic

### 2. Data Misuse
❌ Don't use hourly data for 60s regime updates
❌ Don't ignore 1-second features
❌ Don't skip data validation

### 3. Threshold Issues
❌ Don't copy legacy thresholds directly
❌ Don't use extreme values initially
❌ Don't ignore adaptive components

## Testing Checkpoints

### Checkpoint 0: Look-Ahead Bias Prevention (MUST PASS FIRST)
- [ ] All existing look-ahead tests pass
- [ ] Modern-specific look-ahead tests pass
- [ ] Data aggregation uses correct labels
- [ ] No future data leakage anywhere

### Checkpoint 1: Data Pipeline (Hours 2-3)
- [ ] Features_1s loads correctly
- [ ] 60-second aggregation works (WITH NO LOOK-AHEAD)
- [ ] Schema compatibility verified
- [ ] No missing data issues

### Checkpoint 2: Regime Detection (Hours 5-6)
- [ ] Regime updates every 60s
- [ ] State history maintained
- [ ] Reasonable state distribution
- [ ] Transitions look realistic

### Checkpoint 3: Trading Logic (Hours 9-10)
- [ ] Trades only at hour boundaries
- [ ] Uses regime history correctly
- [ ] Generates 50+ trades on test data
- [ ] Risk sizing appropriate

### Checkpoint 4: Full System (Hours 14-15)
- [ ] Complete 2024 backtest runs
- [ ] 100-200 trades generated
- [ ] Positive ROI achieved
- [ ] Runtime <60 seconds

## Implementation Order

1. **Start**: Verify data pipeline works
2. **Then**: Fix regime detection timing
3. **Next**: Implement hourly evaluation
4. **After**: Calibrate thresholds
5. **Finally**: Optimize performance

## Quick Debug Commands

```bash
# FIRST: Run look-ahead bias tests
python3 -m pytest tests/test_look_ahead_bias.py -v

# Test data loading
python3 -c "from hyperliquid_bot.modern.data_loader import ModernDataLoader; print('Data loads successfully')"

# Test regime detection
python3 scripts/test_regime_updates.py --debug

# Test with minimal data
python3 -m hyperliquid_bot.backtester.run_backtest \
  --override configs/overrides/modern_system.yaml \
  --start-date 2024-01-01 \
  --end-date 2024-01-07

# Profile performance
python3 -m cProfile -o modern.prof scripts/run_modern_backtest.py

# Verify no look-ahead in results
python3 scripts/verify_backtest_causality.py --results backtest_results.json
```

## Success Metrics

### Minimum Viable Product
- ✅ 50+ trades in January 2024
- ✅ Positive ROI
- ✅ Regime updates working
- ✅ No critical errors

### Target Performance
- ✅ 100-200 trades for 2024
- ✅ 100%+ ROI
- ✅ Sharpe > 2.0
- ✅ Runtime < 60s

### Stretch Goals
- ✅ Outperform legacy system
- ✅ Better risk-adjusted returns
- ✅ More stable performance
- ✅ Ready for production

## Architecture for Future Live Trading

### Live Trading Components (Placeholders)

```python
# Create: modern/live/
modern/live/
├── __init__.py
├── websocket_handler.py      # TODO: Real-time data ingestion
├── feature_calculator.py     # TODO: Real-time feature computation
├── order_manager.py          # TODO: Order execution and management
├── risk_monitor.py          # TODO: Real-time risk monitoring
└── system_coordinator.py     # TODO: Orchestrate all components
```

### Key Differences: Backtest vs Live

| Component | Backtest Mode | Live Mode (Future) |
|-----------|--------------|-------------------|
| Data Source | Historical files | WebSocket streams |
| Timing | Simulated loops | Real-time timers |
| Regime Updates | Batch simulation | 60s timer callbacks |
| Trade Evaluation | End of hour check | Hourly timer |
| Execution | Instant fills | Order management |
| Risk Checks | Pre-computed | Real-time monitoring |

### Live Trading Preparation Checklist

```python
# TODO: Before going live, ensure:
1. WebSocket connection stability
2. Feature calculation latency < 500ms
3. Order execution infrastructure
4. Risk management kill switches
5. Monitoring and alerting
6. Paper trading validation
7. Gradual position size scaling
```

## Critical Configuration Fixes

```yaml
# The TWO most critical fixes:
1. cadence_sec: 60    # NOT 3600! (regime updates, not trading)
2. risk_frac: 0.25    # NOT 0.02! (match legacy for testing)
```

## Final Notes

The key insight is that **cadence_sec: 60** means regime state updates, not trading frequency. The modern system must maintain continuous market awareness through 60-second regime updates while making trading decisions only at sensible hourly intervals.

This hybrid approach should provide:
1. Better market state awareness
2. More informed trading decisions  
3. Improved execution quality
4. Higher risk-adjusted returns
5. **ZERO look-ahead bias** (verified by comprehensive tests)

The implementation will require careful attention to timing, data flow, and the interaction between components. Follow the phases sequentially and validate at each checkpoint before proceeding.

**Critical Reminders**:
1. This implementation focuses on backtesting first, with clear placeholders for live trading
2. EVERY component must pass look-ahead bias tests before integration
3. Use `label='right', closed='left'` for ALL resampling operations
4. Only use data up to current timestamp in ALL calculations
5. Test early, test often, test comprehensively

**Look-Ahead Bias is a Fatal Flaw** - Better to be overly cautious than to have any leakage of future information.

---
*Plan Created: January 17, 2025*
*Estimated Time: 15-20 hours*
*Priority: Critical*
*Mode: Backtesting with Live Trading Preparation*
*Look-Ahead Prevention: MANDATORY*