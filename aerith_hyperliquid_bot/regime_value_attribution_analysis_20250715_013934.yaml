analysis_metadata:
  purpose: Investigate regime detection value vs pure EMA crossover
  questions_investigated:
  - Is TF-v3 regime aware as intended?
  - Is ROI due to BTC bull market/EMA crossovers OR regime classification?
  timestamp: '20250715_013934'
test_results:
  btc_buy_hold:
    description: "BTC buy-and-hold ($42,000 \u2192 $95,000)"
    total_return: 126.19047619047619
    trades: 1
  pure_ema:
    error: 'Backtester.run() missing 2 required positional arguments: ''start_date''
      and ''end_date'''
  regime_gated:
    error: 'Backtester.run() missing 2 required positional arguments: ''start_date''
      and ''end_date'''
