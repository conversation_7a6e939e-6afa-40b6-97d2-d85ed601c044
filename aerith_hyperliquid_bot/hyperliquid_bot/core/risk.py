# hyperliquid_bot/core/risk.py

import logging
import pandas as pd
import numpy as np
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, TYPE_CHECKING

# Import Config and Portfolio for type hinting and access
from hyperliquid_bot.config.settings import Config
# from hyperliquid_bot.portfolio import Portfolio # Assuming Portfolio is importable

# Import the state mapping utility
from hyperliquid_bot.utils.state_mapping import map_gms_state

# Use TYPE_CHECKING for Portfolio to avoid circular import if necessary
if TYPE_CHECKING:
    from hyperliquid_bot.portfolio import Portfolio

logger = logging.getLogger(__name__)

class RiskManager:
    """
    Manages position sizing and leverage based on strategy, regime,
    portfolio state, and configuration settings. Uses ATR-based risk sizing.
    """
    def __init__(self, config: Config):
        """
        Initializes the RiskManager.

        Args:
            config: The main configuration object.
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)

        # Store the weak bear mapping toggle from config if available
        self.map_weak_bear_to_bear = False
        if hasattr(config, 'regime') and hasattr(config.regime, 'map_weak_bear_to_bear'):
            self.map_weak_bear_to_bear = config.regime.map_weak_bear_to_bear
            if self.map_weak_bear_to_bear:
                self.logger.info("Weak_Bear_Trend will map to BEAR (map_weak_bear_to_bear=True)")

        self.logger.info("Initialized RiskManager (using ATR-based sizing logic)") # Updated log message

    @staticmethod
    def get_fixed_fraction_position_size(account_equity: float, risk_pct: float) -> float:
        """
        Calculate position size based on a fixed fraction of account equity.

        Args:
            account_equity: Current account equity in USD
            risk_pct: Risk percentage as a decimal (e.g., 0.005 for 0.5%)

        Returns:
            The dollar amount to risk based on the fixed fraction
        """
        if account_equity <= 0:
            return 0.0

        if risk_pct <= 0 or risk_pct > 1.0:
            # Cap risk percentage at 100% and ensure it's positive
            risk_pct = max(0.0, min(risk_pct, 1.0))

        return account_equity * risk_pct

    # --- Replace calculate_position with the logic from risk_prev.py ---
    def calculate_position(self, portfolio: 'Portfolio', signals: dict, strategy_name: str, regime: str, strategy_info: Optional[Dict] = None) -> Tuple[Optional[float], Optional[float]]:
        """
        Calculates position size (in base currency units) and leverage using ATR-based risk.

        Applies dynamic risk adjustments based on regime if configured.
        Performs margin checks to ensure the position is feasible.

        Args:
            portfolio: The portfolio object containing account balance and other details.
            signals: Dictionary containing the latest calculated signals (needs price, ATRs).
            strategy_name: The name of the strategy triggering the entry (e.g., "trend_following").
            regime: The current market regime string (e.g., "Volatile_Chop").
            strategy_info: Optional dictionary containing additional info (not used in this implementation).

        Returns:
            A tuple containing (position_size, leverage).
            Returns (None, None) if sizing fails or position is not feasible.
        """
        cfg = self.config # Alias for configuration settings
        balance = portfolio.balance # Get current cash balance
        self.logger.debug(f"Calculating position: Bal=${balance:.2f}, Strat={strategy_name}, Regime={regime}")

        # --- Determine Strategy-Specific Parameters ---
        atr = np.nan
        stop_mult = np.nan
        leverage_base = np.nan # Base leverage for margin check reference

        # --- NOTE: This assumes MV strategy is disabled or uses similar ATR logic ---
        # --- If enabling MV later, add its specific ATR/stop_mult/leverage_base here ---
        if strategy_name == "trend_following":
            atr = signals.get("atr_tf")
            stop_mult = cfg.indicators.tf_atr_stop_mult
            leverage_base = cfg.indicators.tf_leverage_base
        elif strategy_name == "mean_reversion": # Keep MR logic even if disabled
            atr = signals.get("atr_mr")
            stop_mult = cfg.indicators.mr_atr_stop_mult
            leverage_base = cfg.indicators.mr_leverage_base
        elif strategy_name == "mean_variance":
            atr = signals.get("atr_mv") # Need atr_mv signal
            stop_mult = cfg.indicators.mv_atr_stop_mult
            leverage_base = cfg.indicators.mv_leverage_base # Use mv_leverage_base
        elif strategy_name == "tf_v3":
            # Use the ATR from signals for TF-v3
            atr = signals.get("atr_14_sec") or signals.get("atr")
            # Use default values for stop_mult and leverage_base if not available in config
            stop_mult = 3.0  # Default value
            leverage_base = 3.0  # Default value

            # Try to get values from config if available
            if hasattr(cfg, 'tf_v3'):
                if hasattr(cfg.tf_v3, 'atr_trail_k'):
                    stop_mult = cfg.tf_v3.atr_trail_k
                if hasattr(cfg.tf_v3, 'max_leverage'):
                    leverage_base = cfg.tf_v3.max_leverage

            self.logger.info(f"TF-v3 Risk Parameters: ATR={atr}, Stop Mult={stop_mult}, Leverage Base={leverage_base}")
        else:
            self.logger.error(f"Unknown strategy '{strategy_name}' provided for position sizing.")
            return None, None

        # --- Validate Inputs ---
        price = signals.get("close") # Use close price for sizing calculations
        if pd.isna(price) or price <= 0:
            self.logger.warning(f"Cannot size position: Invalid price ({price}).")
            return None, None
        if pd.isna(atr) or atr <= 1e-9:
            self.logger.warning(f"Cannot size position: Invalid ATR ({atr}) for strategy '{strategy_name}'.")
            return None, None
        if pd.isna(stop_mult) or stop_mult <= 0:
            self.logger.error(f"Cannot size position: Invalid stop multiplier ({stop_mult}) for strategy '{strategy_name}'.")
            return None, None
        if pd.isna(leverage_base) or leverage_base < 1:
            # Use default max leverage if strategy base is invalid? Or fail? Let's fail for safety.
            self.logger.error(f"Cannot size position: Invalid base leverage ({leverage_base}) for strategy '{strategy_name}'.")
            return None, None
        if balance <= 0:
            self.logger.error(f"Cannot size position: Balance is zero or negative (${balance:.2f}).")
            return None, None

        # --- Dynamic Risk Adjustment ---
        # Start with base leverage for the strategy as our reference point
        base_leverage = leverage_base
        risk_factor = 1.0 # Default risk factor applied to risk_per_trade

        # Log the configuration settings for debugging
        use_dynamic_risk = hasattr(cfg.regime, 'dynamic_risk_adjustment') and cfg.regime.dynamic_risk_adjustment
        self.logger.info(f"CONFIG STATE: Dynamic Risk Adjustment = {use_dynamic_risk}")

        # DYNAMIC RISK APPROACH
        # Calculate two separate components, then combine at the end:
        # 1. Base leverage without any adjustments
        # 2. Regime-adjusted leverage factor (if enabled)
        # 3. Regime-adjusted risk factor (for position size)

        # --- Keep track of original values for logging ---
        original_base_leverage = base_leverage

        # --- STEP 1: Regime-based adjustments (ONLY if enabled) ---
        # Check if current regime should trigger dynamic risk adjustment
        regime_leverage_factor = 1.0  # Default (no effect)
        apply_regime_adjustment = False

        if use_dynamic_risk:
            supported_regimes = ["Volatile_Chop", "Low_Vol_Chop", "Ranging", "High_Vol_Range",
                            "Strong_Bull_Trend", "Strong_Bear_Trend", "Weak_Bull_Trend", "Weak_Bear_Trend"]

            if regime in supported_regimes:
                apply_regime_adjustment = True
                self.logger.info(f"REGIME ADJUSTMENT: Triggered by regime: {regime}")

                # Select appropriate factors based on regime
                if regime in ["Strong_Bull_Trend", "Strong_Bear_Trend"]:
                    risk_factor = getattr(cfg.regime, 'strong_trend_risk_factor', 0.7)
                    regime_leverage_factor = getattr(cfg.regime, 'strong_trend_leverage_factor', 0.8)
                    adjustment_type = "strong trend"
                elif regime in ["Weak_Bull_Trend", "Weak_Bear_Trend"]:
                    risk_factor = getattr(cfg.regime, 'weak_trend_risk_scale', 0.8)
                    regime_leverage_factor = 1.0  # No effect on leverage by default for weak trends
                    adjustment_type = "weak trend"
                else:  # Choppy markets
                    risk_factor = getattr(cfg.regime, 'chop_risk_factor', 0.5)
                    regime_leverage_factor = getattr(cfg.regime, 'chop_leverage_factor', 0.5)
                    adjustment_type = "choppy market"

                self.logger.info(f"REGIME ADJUSTMENT: Using {adjustment_type} factors: risk_factor={risk_factor:.2f}, leverage_factor={regime_leverage_factor:.2f}")
            else:
                self.logger.debug(f"Regime '{regime}' doesn't trigger adjustment. Using standard risk/leverage factors.")
        else:
            self.logger.debug(f"Regime-based risk adjustment is DISABLED. Using risk_factor={risk_factor:.2f}")

        # --- STEP 3: CALCULATE FINAL LEVERAGE
        final_leverage = base_leverage

        # Apply regime leverage adjustment IF enabled (completely independent of dynamic leverage)
        if use_dynamic_risk and apply_regime_adjustment:
            # Apply the regime adjustment directly to the base leverage
            final_leverage = base_leverage * regime_leverage_factor

        # --- STEP 4: Apply Market Bias Adjustments (COMPLETELY OPTIONAL) ---
        # This only applies if market_bias.enabled = true in the config

        market_state = "CHOP" # Default market state
        if hasattr(cfg.regime, 'market_bias') and hasattr(cfg.regime.market_bias, 'enabled') and cfg.regime.market_bias.enabled:
            self.logger.info(f"MARKET BIAS: Checking market bias adjustment based on regime '{regime}'")

            # Import the standardized state mapping utility
            # (Added at the top of the file: from hyperliquid_bot.utils.state_mapping import map_gms_state)
            try:
                # Use the standardized mapping utility to determine market state
                market_state = map_gms_state(regime)
                self.logger.debug(f"Using standardized state mapping: '{regime}' -> '{market_state}'")
            except Exception as e:
                # Fallback to default if there's any error with the mapping utility
                market_state = "CHOP"  # Default fallback state
                self.logger.warning(f"Error mapping regime '{regime}' (using CHOP as fallback): {str(e)}")

            self.logger.info(f"MARKET BIAS: Mapped regime '{regime}' to market state '{market_state}'")

            # Get direction from strategy_info if available
            direction = "unknown"
            if strategy_info and 'direction' in strategy_info:
                direction = strategy_info['direction'].lower()  # Normalize to lowercase

            # Apply market bias-specific factors
            base_leverage_market = final_leverage  # Start from the current final_leverage
            market_leverage_factor = 1.0
            market_risk_factor = 1.0
            direction_bias = 1.0

            if market_state in ["BULL", "BEAR", "CHOP"]:
                self.logger.info(f"MARKET BIAS: Applying adjustments for {market_state} market")

                # Apply market-specific factors based on market state
                if market_state == "BULL":
                    market_leverage_factor = getattr(cfg.regime.market_bias, 'bull_leverage_factor', 1.5)
                    market_risk_factor = getattr(cfg.regime.market_bias, 'bull_risk_factor', 1.2)

                    # Apply direction-specific bias if direction is known
                    if direction == "long":
                        direction_bias = getattr(cfg.regime.market_bias, 'bull_long_bias', 1.0)
                    elif direction == "short":
                        direction_bias = getattr(cfg.regime.market_bias, 'bull_short_bias', 0.5)

                elif market_state == "BEAR":
                    market_leverage_factor = getattr(cfg.regime.market_bias, 'bear_leverage_factor', 0.7)
                    market_risk_factor = getattr(cfg.regime.market_bias, 'bear_risk_factor', 0.8)

                    # Apply direction-specific bias if direction is known
                    if direction == "long":
                        direction_bias = getattr(cfg.regime.market_bias, 'bear_long_bias', 0.5)
                    elif direction == "short":
                        direction_bias = getattr(cfg.regime.market_bias, 'bear_short_bias', 1.0)

                else:  # CHOP
                    market_leverage_factor = getattr(cfg.regime.market_bias, 'chop_leverage_factor', 0.5)
                    market_risk_factor = getattr(cfg.regime.market_bias, 'chop_risk_factor', 0.5)

                # Apply adjustments
                market_adjusted_leverage = base_leverage_market * market_leverage_factor

                # Apply direction bias if direction is known
                if direction != "unknown":
                    market_adjusted_leverage *= direction_bias
                    self.logger.info(f"MARKET BIAS: Applied direction bias for {direction} in {market_state} market: {direction_bias:.2f}x")

                # Update final leverage with market bias adjustment
                final_leverage = market_adjusted_leverage

                # Update risk factor with market bias risk factor
                risk_factor *= market_risk_factor
                self.logger.info(f"MARKET BIAS: Applied market risk factor: {market_risk_factor:.2f}x, resulting in total risk factor: {risk_factor:.2f}x")

                # For detailed debugging/analysis
                self._validate_market_bias(
                    market_state=market_state,
                    regime=regime,
                    base_leverage=base_leverage_market,
                    final_leverage=final_leverage,
                    market_risk_factor=market_risk_factor,
                    direction_bias=direction_bias if direction != "unknown" else 1.0
                )

        # Respect global leverage bounds regardless of which adjustments were made
        final_leverage = max(cfg.indicators.min_leverage, min(final_leverage, cfg.portfolio.max_leverage))

        # Set the final calculated leverage for use in position sizing
        leverage = final_leverage

        # Log detailed information about the final calculation
        components = []
        if use_dynamic_risk and apply_regime_adjustment:
            components.append("Regime-Based")
        if hasattr(cfg.regime, 'market_bias') and hasattr(cfg.regime.market_bias, 'enabled') and cfg.regime.market_bias.enabled:
            components.append("Market-Bias-Based")
        if not components:
            components.append("None (Base Only)")

        self.logger.info(f"FINAL LEVERAGE: {leverage:.2f}x from Base={original_base_leverage:.2f}x with adjustments: {', '.join(components)}")

        # --- Validate Final Risk Settings ---
        self._validate_dynamic_risk_settings(regime, risk_factor, leverage)


        # --- Calculate Stop Distance and Risk Amount ---
        stop_distance = stop_mult * atr
        if stop_distance <= 1e-9:
            self.logger.warning(f"Cannot size position: Calculated stop distance is too small ({stop_distance:.4g}).")
            return None, None

        # --- Weak Trend Risk Scaling ---
        risk_scale = 1.0
        if regime == "Weak_Bull_Trend" and not cfg.regime.gms_filter_allow_weak_bull_trend:
            risk_scale = cfg.regime.weak_bull_risk_scale
            self.logger.info(f"Weak Bull Trend detected with filter off; applying risk scale {risk_scale:.2f}")
        elif regime == "Weak_Bear_Trend" and not cfg.regime.gms_filter_allow_weak_bear_trend:
            risk_scale = cfg.regime.weak_bear_risk_scale
            self.logger.info(f"Weak Bear Trend detected with filter off; applying risk scale {risk_scale:.2f}")
        else:
            self.logger.debug(f"Risk scale remains {risk_scale:.2f} for regime {regime}")

        # Calculate the maximum dollar amount to risk on this trade based on config, dynamic factor, and weak-trend scaling
        risk_amount_per_trade = balance * cfg.portfolio.risk_per_trade * risk_factor * risk_scale
        if risk_amount_per_trade <= 0:
            self.logger.warning(f"Cannot size position: Calculated risk amount is zero or negative (${risk_amount_per_trade:.2f}).")
            return None, None

        self.logger.debug(f"Risk Calculation: StopDist={stop_distance:.4f}, RiskAmt=${risk_amount_per_trade:.2f}")

        # --- Calculate Initial Position Size based on Risk ---
        # size = amount_to_risk / risk_per_unit (where risk_per_unit is stop_distance)
        size = risk_amount_per_trade / stop_distance
        if size <= 1e-9:
            self.logger.warning(f"Calculated initial size is zero or negative ({size:.8f}). No trade.")
            return None, None
        self.logger.debug(f"Initial size based on risk: {size:.8f}")

        # --- Margin Check using IM ---
        # Use the capped reference leverage calculated earlier for the IM check
        reference_leverage_for_im = leverage
        mark_price = portfolio.get_mark_price(signals)
        if mark_price is None:
             self.logger.error("Cannot perform margin check: Mark price unavailable.")
             return None, None

        notional_value = size * mark_price
        im_required = (notional_value / reference_leverage_for_im) if reference_leverage_for_im > 0 else float('inf')

        account_value = portfolio.calculate_account_value(signals) # Includes current unrealized PnL if any

        # Margin available for *this new* trade (assuming cross margin)
        margin_buffer = self.config.portfolio.margin_buffer_pct
        available_margin_for_new_trade = account_value * (1 - margin_buffer)

        self.logger.debug(f"Margin Check: IM Required=${im_required:.2f} (Notional ${notional_value:.2f} / Ref Lev {reference_leverage_for_im:.1f}x)")
        self.logger.debug(f"Margin Check: Account Value=${account_value:.2f}, Available for New Trade=${available_margin_for_new_trade:.2f}")

        if im_required > available_margin_for_new_trade:
            # Check if we are in backtest mode
            if self.config.is_backtest:
                # In backtest mode, use configurable minimum size to ensure the trade is executed
                self.logger.warning(f"Insufficient Margin for New Trade: Required ${im_required:.2f} > Available ${available_margin_for_new_trade:.2f}. Using minimum size for backtest.")
                # Use configurable minimum trade size to ensure the trade is executed
                min_trade_size = getattr(cfg.portfolio, 'min_trade_size', 0.001)
                size = min_trade_size
                # Skip the margin check and proceed with the trade
                self.logger.info(f"Backtest mode: Using minimum size {size:.8f} (from config) to ensure trade execution")
                # Return the size and leverage
                return size, leverage
            else:
                # In live mode, reduce the size based on available margin
                self.logger.warning(f"Insufficient Margin for New Trade: Required ${im_required:.2f} > Available ${available_margin_for_new_trade:.2f}. Reducing size.")
            # Reduce size proportionally based on available margin ratio
            if im_required <= 0: # Avoid division by zero
                 self.logger.error("IM Required is zero or negative, cannot calculate reduction factor.")
                 return None, None
            reduction_factor = available_margin_for_new_trade / im_required
            original_size = size
            size = size * reduction_factor * 0.98 # Reduce slightly more to be safe
            self.logger.info(f"Reduced size from {original_size:.8f} to {size:.8f} due to margin constraints.")

            if size <= 1e-9:
                 self.logger.error("Position size reduced to zero or negative after margin adjustment.")
                 return None, None

            # Recalculate potential loss and re-check risk (important!)
            max_potential_loss_adjusted = size * stop_distance
            # Allow slightly higher loss due to reduction buffer, but not exceeding original risk amount significantly
            if max_potential_loss_adjusted > risk_amount_per_trade * 1.05: # Allow 5% tolerance
                 self.logger.error(f"Post-Margin-Reduction Risk Check FAILED: Adjusted Max Loss ${max_potential_loss_adjusted:.2f} > Risk Amt ${risk_amount_per_trade:.2f} (with tolerance). Aborting.")
                 return None, None
            else:
                 self.logger.debug(f"Post-Margin-Reduction Risk Check OK: Adjusted Max Loss ${max_potential_loss_adjusted:.2f}")
        else:
            # Initial size is feasible, perform final risk check
            self.logger.debug("Margin Check OK.")
            max_potential_loss_initial = size * stop_distance
            # Check if calculated loss slightly exceeds target due to floating point, allow small tolerance
            if max_potential_loss_initial > risk_amount_per_trade * 1.01: # Allow 1% tolerance
                 self.logger.error(f"Final Risk Check FAILED: Initial Max Loss ${max_potential_loss_initial:.2f} > Risk Amount ${risk_amount_per_trade:.2f} (with tolerance). Aborting.")
                 return None, None

        # --- Determine Final Leverage ---
        # The actual leverage is determined by the final size and balance
        # final_leverage = (size * price) / balance # Use entry price estimate for leverage calc - This was re-calculating final_leverage, but it's already set
        # Cap the *reported* or *intended* leverage based on config, though size is risk-based
        # final_leverage = max(cfg.indicators.min_leverage, min(final_leverage, cfg.portfolio.max_leverage)) # Already capped

        self.logger.info(f"Final Calculated Position: Strategy={strategy_name}, Size={size:.8f}, Effective Leverage={leverage:.2f}x")
        # Return the calculated size and the capped effective leverage
        return size, leverage

    def _validate_market_bias(self, market_state: str, regime: str, base_leverage: float, final_leverage: float, market_risk_factor: float, direction_bias: float):
        """
        Validate that market bias settings are properly applied.

        Args:
            market_state: Simplified market state (BULL, BEAR, CHOP)
            regime: Detailed market regime
            base_leverage: Leverage before market bias adjustments.
            final_leverage: Leverage after market bias adjustments.
            market_risk_factor: Risk factor applied due to market bias.
            direction_bias: Directional bias factor applied.
        """
        cfg = self.config

        # Check if market bias should be enabled
        if not hasattr(cfg.regime, 'market_bias') or not hasattr(cfg.regime.market_bias, 'enabled'):
            self.logger.warning("VALIDATION: Market bias settings not available in config!")
            return

        if not cfg.regime.market_bias.enabled:
            self.logger.warning("VALIDATION: Market bias is disabled but seems to be applied!")
            return

        # Validate state mapping consistency (only if standardized mapping is available)
        try:
            from hyperliquid_bot.utils.state_mapping import map_gms_state, validate_3state

            # Confirm that the state is a valid 3-state value
            if not validate_3state(market_state):
                self.logger.warning(f"VALIDATION: Invalid market state '{market_state}' provided!")

            # Verify consistency between regime and mapped state
            expected_market_state = map_gms_state(regime, map_weak_bear_to_bear=self.map_weak_bear_to_bear)
            if expected_market_state != market_state:
                self.logger.warning(f"VALIDATION: State mapping inconsistency detected! Regime '{regime}' should map to '{expected_market_state}' but got '{market_state}' (map_weak_bear_to_bear={self.map_weak_bear_to_bear})")
                # Check if the inconsistency is specifically due to Weak_Bear_Trend mapping
                if regime == "Weak_Bear_Trend" and ((expected_market_state == "BEAR" and market_state == "CHOP") or (expected_market_state == "CHOP" and market_state == "BEAR")):
                    self.logger.warning("This inconsistency is likely due to the Weak_Bear_Trend mapping toggle. Check your map_weak_bear_to_bear configuration consistency.")
        except ImportError:
            self.logger.warning("VALIDATION: State mapping utility not available - skipping mapping consistency check")

        self.logger.info(f"Market State: {market_state}")
        self.logger.info(f"Base Leverage: {base_leverage:.2f}x")

        # Specific direction bias
        direction_enabled = hasattr(cfg.regime.market_bias, 'direction_bias_enabled') and cfg.regime.market_bias.direction_bias_enabled
        self.logger.info(f"Direction-Based Bias: {'Enabled' if direction_enabled else 'Disabled'}")

        if direction_enabled and direction_bias != 1.0:
            self.logger.info(f"Direction Risk Bias: {direction_bias:.2f}x")

        # Get market state specific risk for validation
        market_cfg = getattr(cfg.regime.market_bias, market_state.lower(), None)
        expected_risk = getattr(market_cfg, 'risk_factor', 1.0) if market_cfg else 1.0

        # Validate market risk factor
        risk_diff = abs(market_risk_factor - expected_risk)
        if risk_diff < 0.0001:  # Close enough to expected value
            self.logger.info(f"Market Risk Factor: {market_risk_factor:.2f}x (Correct)")
        else:
            self.logger.warning(f"Market Risk Factor: {market_risk_factor:.2f}x (INCONSISTENT - Expected {expected_risk:.2f}x)")

        # Leverage change validation
        leverage_change_pct = ((final_leverage / base_leverage) - 1.0) * 100 if base_leverage > 1e-9 else 0
        leverage_direction = "increase" if leverage_change_pct > 0 else "decrease"
        self.logger.info(f"Leverage Change: {abs(leverage_change_pct):.1f}% {leverage_direction} from {base_leverage:.2f}x to {final_leverage:.2f}x")

        self.logger.info("--------- END MARKET BIAS VALIDATION ---------")

        if market_state == "BULL":
            expected_lev_factor = getattr(cfg.regime.market_bias, 'bull_leverage_factor', 1.5)
            expected_risk_factor = getattr(cfg.regime.market_bias, 'bull_risk_factor', 1.2)
        elif market_state == "BEAR":
            expected_lev_factor = getattr(cfg.regime.market_bias, 'bear_leverage_factor', 0.7)
            expected_risk_factor = getattr(cfg.regime.market_bias, 'bear_risk_factor', 0.8)
        else:  # CHOP
            expected_lev_factor = getattr(cfg.regime.market_bias, 'chop_leverage_factor', 0.5)
            expected_risk_factor = getattr(cfg.regime.market_bias, 'chop_risk_factor', 0.5)

        # Account for direction bias in the expected leverage calculation
        expected_leverage = base_leverage * expected_lev_factor * direction_bias
        self.logger.info(f"  • Expected Risk Factor: {expected_risk_factor}")
        self.logger.info(f"  • Expected Leverage Factor: {expected_lev_factor}")
        self.logger.info(f"  • Direction Bias: {direction_bias:.2f}")
        self.logger.info(f"  • Expected Adjusted Leverage: {expected_leverage:.2f}x")
        self.logger.info(f"  • Actual Final Leverage: {final_leverage:.2f}x")

        leverage_diff = abs(final_leverage - expected_leverage) / expected_leverage if expected_leverage > 1e-9 else (1.0 if abs(final_leverage - expected_leverage) > 1e-9 else 0.0)
        if leverage_diff < 0.05:  # Within 5% of expected
            self.logger.info("  • Leverage Application: CORRECT")
        else:
            self.logger.warning("  • Leverage Application: POSSIBLE ERROR")

        # Add a summary line about the applied adjustments
        self.logger.info(f"  • Market Bias Summary: {market_state} market, {base_leverage:.2f}x→{final_leverage:.2f}x leverage, {market_risk_factor:.2f}x risk")
        self.logger.info("--------- END VALIDATION ---------")

    def _validate_market_bias(self, market_state: str, regime: str, base_leverage: float, final_leverage: float, market_risk_factor: float, direction_bias: float):
        """
        Validate that market bias settings are properly applied.

        Args:
            market_state: Simplified market state (BULL, BEAR, CHOP)
            regime: Detailed market regime
            base_leverage: Leverage before market bias adjustments.
            final_leverage: Leverage after market bias adjustments.
            market_risk_factor: Risk factor applied due to market bias.
            direction_bias: Directional bias factor applied.
        """
        cfg = self.config

        # Check if market bias should be enabled
        if not hasattr(cfg.regime, 'market_bias') or not hasattr(cfg.regime.market_bias, 'enabled'):
            self.logger.warning("VALIDATION: Market bias settings not available in config!")
            return

        if not cfg.regime.market_bias.enabled:
            self.logger.warning("VALIDATION: Market bias is disabled but seems to be applied!")
            return

        # Validate state mapping consistency (only if standardized mapping is available)
        try:
            from hyperliquid_bot.utils.state_mapping import map_gms_state, validate_3state

            # Confirm that the state is a valid 3-state value
            if not validate_3state(market_state):
                self.logger.warning(f"VALIDATION: Invalid market state '{market_state}' provided!")

            # Verify consistency between regime and mapped state
            expected_market_state = map_gms_state(regime, map_weak_bear_to_bear=self.map_weak_bear_to_bear)
            if expected_market_state != market_state:
                self.logger.warning(f"VALIDATION: State mapping inconsistency detected! Regime '{regime}' should map to '{expected_market_state}' but got '{market_state}' (map_weak_bear_to_bear={self.map_weak_bear_to_bear})")
                # Check if the inconsistency is specifically due to Weak_Bear_Trend mapping
                if regime == "Weak_Bear_Trend" and ((expected_market_state == "BEAR" and market_state == "CHOP") or (expected_market_state == "CHOP" and market_state == "BEAR")):
                    self.logger.warning("This inconsistency is likely due to the Weak_Bear_Trend mapping toggle. Check your map_weak_bear_to_bear configuration consistency.")
        except ImportError:
            self.logger.warning("VALIDATION: State mapping utility not available - skipping mapping consistency check")

        # --- Validation Messages for Review ---
        self.logger.info("---------- MARKET BIAS VALIDATION ----------")
        self.logger.info(f"GMS Regime: {regime}")
        self.logger.info(f"Market State: {market_state}")
        self.logger.info(f"Base Leverage: {base_leverage:.2f}x")

        # Specific direction bias
        direction_enabled = hasattr(cfg.regime.market_bias, 'direction_bias_enabled') and cfg.regime.market_bias.direction_bias_enabled
        self.logger.info(f"Direction-Based Bias: {'Enabled' if direction_enabled else 'Disabled'}")

        if direction_enabled and direction_bias != 1.0:
            self.logger.info(f"Direction Risk Bias: {direction_bias:.2f}x")

        # Get market state specific risk for validation
        market_cfg = getattr(cfg.regime.market_bias, market_state.lower(), None)
        expected_risk = getattr(market_cfg, 'risk_factor', 1.0) if market_cfg else 1.0

        # Validate market risk factor
        risk_diff = abs(market_risk_factor - expected_risk)
        if risk_diff < 0.0001:  # Close enough to expected value
            self.logger.info(f"Market Risk Factor: {market_risk_factor:.2f}x (Correct)")
        else:
            self.logger.warning(f"Market Risk Factor: {market_risk_factor:.2f}x (INCONSISTENT - Expected {expected_risk:.2f}x)")

        # Leverage change validation
        leverage_change_pct = ((final_leverage / base_leverage) - 1.0) * 100 if base_leverage > 1e-9 else 0
        leverage_direction = "increase" if leverage_change_pct > 0 else "decrease"
        self.logger.info(f"Leverage Change: {abs(leverage_change_pct):.1f}% {leverage_direction} from {base_leverage:.2f}x to {final_leverage:.2f}x")

        self.logger.info("--------- END MARKET BIAS VALIDATION ---------")

        if market_state == "BULL":
            expected_lev_factor = getattr(cfg.regime.market_bias, 'bull_leverage_factor', 1.5)
            expected_risk_factor = getattr(cfg.regime.market_bias, 'bull_risk_factor', 1.2)
        elif market_state == "BEAR":
            expected_lev_factor = getattr(cfg.regime.market_bias, 'bear_leverage_factor', 0.7)
            expected_risk_factor = getattr(cfg.regime.market_bias, 'bear_risk_factor', 0.8)
        else:  # CHOP
            expected_lev_factor = getattr(cfg.regime.market_bias, 'chop_leverage_factor', 0.5)
            expected_risk_factor = getattr(cfg.regime.market_bias, 'chop_risk_factor', 0.5)

        # Account for direction bias in the expected leverage calculation
        expected_leverage = base_leverage * expected_lev_factor * direction_bias
        self.logger.info(f"  • Expected Risk Factor: {expected_risk_factor}")
        self.logger.info(f"  • Expected Leverage Factor: {expected_lev_factor}")
        self.logger.info(f"  • Direction Bias: {direction_bias:.2f}")
        self.logger.info(f"  • Expected Adjusted Leverage: {expected_leverage:.2f}x")
        self.logger.info(f"  • Actual Final Leverage: {final_leverage:.2f}x")

        leverage_diff = abs(final_leverage - expected_leverage) / expected_leverage if expected_leverage > 1e-9 else (1.0 if abs(final_leverage - expected_leverage) > 1e-9 else 0.0)
        if leverage_diff < 0.05:  # Within 5% of expected
            self.logger.info("  • Leverage Validation: PASSED (within 5% of expected)")
        else:
            self.logger.warning(f"  • Leverage Validation: FAILED - Expected {expected_leverage:.2f}x, Got {final_leverage:.2f}x, Diff: {leverage_diff*100:.1f}%")

    def _validate_dynamic_risk_settings(self, regime: str, risk_factor: float, leverage: float):
        """
        Validate that dynamic risk settings are properly configured and applied.
        Used for debugging to ensure settings are being correctly recognized.

        Args:
            regime: Current market regime
            risk_factor: Applied risk factor
            leverage: Applied leverage
        """
        cfg = self.config

        # --------- VALIDATION SUMMARY ---------
        self.logger.info("VALIDATION SUMMARY:")

        # Check configuration settings
        dynamic_risk_enabled = hasattr(cfg.regime, 'dynamic_risk_adjustment') and cfg.regime.dynamic_risk_adjustment

        # Validate configuration status
        self.logger.info(f"  • Dynamic Risk Flag: {dynamic_risk_enabled} (Regime-based adjustment)")

        # Check if the current regime can trigger adjustment
        supported_regimes = [
            "Volatile_Chop", "Low_Vol_Chop", "Ranging", "High_Vol_Range",
            "Strong_Bull_Trend", "Strong_Bear_Trend", "Weak_Bull_Trend", "Weak_Bear_Trend"
        ]
        regime_applies = regime in supported_regimes
        self.logger.info(f"  • Current Regime '{regime}': {'Valid for adjustment' if regime_applies else 'Not valid for adjustment'}")

        # Get base leverage for comparison
        # Assuming 'tf_leverage_base' is a sensible default if strategy specific one isn't passed or known here.
        # This might need adjustment if different strategies have vastly different base leverages.
        base_leverage_for_validation = cfg.indicators.tf_leverage_base # Defaulting to TF for validation context
        # A more robust way might be to pass original_base_leverage to this validation function.
        self.logger.info(f"  • Base Leverage (for validation context): {base_leverage_for_validation:.2f}x")

        # Validate regime-specific risk & leverage factors
        if dynamic_risk_enabled and regime_applies:
            if regime in ["Strong_Bull_Trend", "Strong_Bear_Trend"]:
                expected_risk_factor = getattr(cfg.regime, 'strong_trend_risk_factor', 0.7)
                expected_lev_factor = getattr(cfg.regime, 'strong_trend_leverage_factor', 0.8)
                regime_type = "Strong Trend"
            elif regime in ["Weak_Bull_Trend", "Weak_Bear_Trend"]:
                expected_risk_factor = getattr(cfg.regime, 'weak_trend_risk_scale', 0.8)
                expected_lev_factor = 1.0  # Default no effect on leverage
                regime_type = "Weak Trend"
            else:  # Choppy/Ranging
                expected_risk_factor = getattr(cfg.regime, 'chop_risk_factor', 0.5)
                expected_lev_factor = getattr(cfg.regime, 'chop_leverage_factor', 0.5)
                regime_type = "Choppy/Ranging"

            # Validate risk factor application
            self.logger.info(f"  • {regime_type} Risk Factor (Expected): {expected_risk_factor:.2f}")
            if abs(risk_factor - expected_risk_factor) < 0.001:  # Close enough to expected
                self.logger.info(f"  • Risk Factor Application: CORRECT (Applied {risk_factor:.2f})")
            else:
                self.logger.warning(f"  • Risk Factor Application: ERROR - Applied {risk_factor:.2f}, Expected {expected_risk_factor:.2f}")

            # Log what leverage adjustment should be from regime
            self.logger.info(f"  • {regime_type} Leverage Factor (Expected): {expected_lev_factor:.2f}")

            # Estimate the expected leverage with regime adjustment (applied to the validation base leverage)
            expected_regime_lev = base_leverage_for_validation * expected_lev_factor

            # Check if leverage is close to expected (allowing for rounding/precision differences)
            # Note: 'leverage' here is the final_leverage which might include market_bias adjustments.
            # This part of validation primarily checks the regime component if no other factors were dominant.
            leverage_diff_from_regime_expectation = abs(leverage - expected_regime_lev) / expected_regime_lev if expected_regime_lev > 1e-9 else (1.0 if abs(leverage - expected_regime_lev) > 1e-9 else 0.0)

            # This comparison is tricky if market bias also modified leverage.
            # The log will show the applied leverage and the purely regime-expected one.
            self.logger.info(f"  • Regime Leverage Effect (Expected if ONLY regime applied to base): ~{expected_regime_lev:.2f}x")
            self.logger.info(f"  • Actual Applied Leverage (after all adjustments): {leverage:.2f}x")
            if leverage_diff_from_regime_expectation < 0.05:
                 self.logger.info("  • Leverage vs Pure Regime Expectation: Close")
            else:
                 self.logger.info("  • Leverage vs Pure Regime Expectation: Differs (may be due to other factors like market bias or capping)")

        else:
            self.logger.info(f"  • Applied Risk Factor: {risk_factor:.2f} (Default or no regime adjustment)")

        # Validate final leverage is within bounds
        min_leverage = getattr(cfg.indicators, 'min_leverage', 1.0)
        max_leverage = cfg.portfolio.max_leverage
        if leverage >= min_leverage and leverage <= max_leverage:
            self.logger.info(f"  • Final Leverage: {leverage:.2f}x (within bounds: [{min_leverage:.1f}-{max_leverage:.1f}])")
        else:
            self.logger.warning(f"  • Final Leverage: {leverage:.2f}x (ERROR: outside bounds [{min_leverage:.1f}-{max_leverage:.1f}])")

        # Show leverage change from base (using validation base_leverage)
        leverage_pct_change = ((leverage / base_leverage_for_validation) - 1.0) * 100 if base_leverage_for_validation > 1e-9 else 0
        change_direction = "increase" if leverage_pct_change > 0 else "reduction"
        self.logger.info(f"  • Leverage Change from Validation Base: {abs(leverage_pct_change):.1f}% {change_direction}")

        # Summary of what components affected the final leverage
        # This is a simplified check based on flags, not a deep dive into numeric contributions
        components_active = []
        if dynamic_risk_enabled and regime_applies:
            components_active.append("Regime-Based")
        if hasattr(cfg.regime, 'market_bias') and hasattr(cfg.regime.market_bias, 'enabled') and cfg.regime.market_bias.enabled:
             components_active.append("Market-Bias-Based")
        if not components_active:
            components_active.append("None - Base Leverage Only (or no active adjustments)")

        self.logger.info(f"  • Potentially Active Leverage Components: {', '.join(components_active)}")
        self.logger.info("--------- END VALIDATION ---------")