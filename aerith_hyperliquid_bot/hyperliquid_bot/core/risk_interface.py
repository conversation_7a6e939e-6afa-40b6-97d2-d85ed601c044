"""
Risk Manager Interface Module

This module provides a forward-compatible interface for accessing risk management
functionality. It wraps the current RiskManager (v1) implementation but is designed
to be compatible with future RiskManager v2 implementations.

Key features:
- Provides a clean interface for strategies to query available notional
- Abstracts away implementation details of the underlying risk management system
- Ensures forward compatibility with RiskManager v2 (planned for T-114)
"""

import logging
from typing import Optional, Dict, Any

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.portfolio.portfolio import Portfolio

logger = logging.getLogger(__name__)

class RiskManagerInterface:
    """
    Interface for accessing risk management functionality.

    This class wraps the current RiskManager implementation and provides
    a forward-compatible interface for strategies to use.
    """

    def __init__(self, config: Config, portfolio: Portfolio):
        """
        Initialize the RiskManagerInterface.

        Args:
            config: The application configuration
            portfolio: The portfolio instance
        """
        self.config = config
        self.portfolio = portfolio
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info("Initialized RiskManagerInterface")

    def available_notional(self, signals: Optional[Dict[str, Any]] = None) -> float:
        """
        Calculate the available notional value for new positions.

        This method provides a forward-compatible way to query how much notional value
        is available for new positions, taking into account current portfolio state,
        risk limits, and margin requirements.

        Args:
            signals: Optional dictionary of current market signals
                    (used for calculating account value with unrealized PnL)

        Returns:
            float: The available notional value for new positions
        """
        # In RiskManager v1, we calculate this based on account value and a margin buffer
        # This logic is similar to what's in RiskManager.calculate_position

        # Get account value (includes unrealized PnL if signals provided)
        if signals:
            account_value = self.portfolio.calculate_account_value(signals)
        else:
            # If no signals provided, use balance + reserved margin (no unrealized PnL)
            account_value = self.portfolio.balance + self.portfolio.reserved_margin

        # Apply a margin buffer (same as in RiskManager.calculate_position)
        margin_buffer = 0.05  # 5% buffer
        available_margin = account_value * (1 - margin_buffer)

        # In cross margin mode, we can use the entire available margin as notional
        # In isolated margin mode, we need to consider leverage
        if self.portfolio.margin_mode == 'cross':
            # For cross margin, available notional is limited by max leverage
            max_leverage = self.config.portfolio.max_leverage
            available_notional = available_margin * max_leverage
        else:
            # For isolated margin, available notional is just the available margin
            # multiplied by the asset's max leverage
            asset_max_leverage = self.config.portfolio.asset_max_leverage
            available_notional = available_margin * asset_max_leverage

        self.logger.debug(f"Available notional: ${available_notional:.2f} (Account value: ${account_value:.2f}, Available margin: ${available_margin:.2f})")

        return max(0.0, available_notional)
