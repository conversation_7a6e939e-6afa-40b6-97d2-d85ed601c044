# hyperliquid_bot/core/detector.py

import logging
from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, Union

# Import the Config class
from hyperliquid_bot.config.settings import Config

# Import standardized state constants and validation from utility
from hyperliquid_bot.utils.state_mapping import (
    # State constants
    GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND,
    GMS_STATE_HIGH_VOL_RANGE, GMS_STATE_LOW_VOL_RANGE,
    GMS_STATE_UNCERTAIN, GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_UNKNOWN, get_valid_gms_states
)

# Import the ContinuousGMSDetector will be done after the RegimeDetectorInterface is defined
# Import the Hurst calculation function (assuming it exists and works)
try:
    from hyperliquid_bot.features.statistical import calculate_hurst_exponent
except ImportError:
    # Define a dummy function if the import fails, allowing Hurst detector to be defined but fail at runtime if used
    logging.warning("Could not import 'calculate_hurst_exponent'. Hurst detector will fail if used.")
    def calculate_hurst_exponent(*args, **kwargs): return None

# Module-level docstring
"""
Market Regime Detection Component.

Defines the interface and provides implementations for identifying the
current market regime (e.g., Trending, Ranging, Volatile Chop, Granular States).
Designed for extensibility with different detection methods.
"""

# --- Regime Detection Interface ---

class RegimeDetectorInterface(ABC):
    """
    Abstract Base Class for all regime detection implementations.
    Ensures that any detector provides a standard method to get the regime.
    """
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"Initialized {self.__class__.__name__}")

    @abstractmethod
    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None) -> str:
        """
        Determines the current market regime based on the provided signals
        and potentially recent price history.

        Args:
            signals: A dictionary containing the latest calculated signals and indicators.
                     Expected keys depend on the specific implementation. Must contain 'timestamp'.
            price_history: Optional pandas Series of recent close prices ending at or
                           just before the timestamp in 'signals'. Required by some detectors (e.g., Hurst).

        Returns:
            A string representing the detected regime (e.g., "Trending", "Ranging",
            "MeanReverting", "Persistent", "RandomWalk", "Filter_Off", "Unknown",
            "Strong_Bull_Trend", "Weak_Bull_Trend", "Strong_Bear_Trend", "Weak_Bear_Trend",
            "High_Vol_Range", "Low_Vol_Range", "TIGHT_SPREAD", "Uncertain").
        """
        pass

    @property
    @abstractmethod
    def required_signals(self) -> list[str]:
        """
        Returns a list of signal keys required *from the signals dictionary*
        by this specific detector implementation. Price history is handled separately.
        """
        pass

# --- Concrete Implementation: Rule-Based Detector ---

class RuleBasedRegimeDetector(RegimeDetectorInterface):
    """
    Implements regime detection based on configurable rules using indicators
    like ADX, Forecast, Chop Index, BBW, etc. (Benchmark Detector)
    """

    def __init__(self, config: Config):
        super().__init__(config)
        # Validate that necessary config options are compatible
        if self.config.regime.use_enhanced_detection:
             # Simplified check: Ensure TF strategy is enabled if forecast is needed implicitly
             if not self.config.strategies.use_tf_v2 and \
                not self.config.regime.use_chop_index_for_chop and \
                not self.config.regime.use_bbw_for_chop_detection:
                 self.logger.warning("Enhanced regime detection enabled, but may implicitly rely on Forecast (from TF strategy) "
                                     "or requires Chop Index/BBW. Check config.")

    @property
    def required_signals(self) -> list[str]:
        """Returns the list of signals needed by the rule-based detector."""
        signals_needed = ['timestamp'] # Always need timestamp for logging/context
        cfg = self.config

        if not cfg.regime.use_filter:
            return signals_needed # Still need timestamp

        # Always need ADX if filter is on
        signals_needed.append("adx")

        if cfg.regime.use_enhanced_detection:
            # Enhanced needs forecast (which depends on TF indicators)
            # Check if TF is enabled, otherwise forecast won't be calculated
            if cfg.strategies.use_tf_v2:
                signals_needed.append("forecast")
            else:
                 # Log warning if enhanced is on but TF is off, as forecast won't exist
                 self.logger.warning("Enhanced detection enabled but Trend Following strategy is disabled. 'forecast' signal will be unavailable.")


            signals_needed.append("close") # Need price for ATR/Price fallback

            # Add signals for the chosen chop indicator
            if cfg.regime.use_chop_index_for_chop:
                signals_needed.append("chop_index")
            elif cfg.regime.use_bbw_for_chop_detection:
                # Check if MR strategy is enabled (assuming BBW comes from MR Keltner/Bollinger calc)
                # This dependency is a bit implicit, might need refinement
                if cfg.strategies.use_mean_reversion: # Or check specific indicator flags
                     signals_needed.extend(["bbw", "bbm"]) # BBW needs bbm for calculation check
                else:
                     self.logger.warning("BBW selected for chop detection, but Mean Reversion strategy (which typically calculates it) is disabled.")

            else: # Fallback uses ATR/Price
                # Need ATR_TF (or potentially ATR_MR if TF is off?)
                # Assume SignalCalculator provides 'atr_tf' if TF strategy is enabled
                if cfg.strategies.use_tf_v2:
                     signals_needed.append("atr_tf")
                # Add fallback ATR if TF is off? Requires SignalCalculator logic change.
                # else: signals_needed.append("atr") # Assuming a generic 'atr' exists

        # Remove duplicates and return
        return list(set(signals_needed))


    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None) -> str:
        """Calls the appropriate rule-based regime detection method based on config."""
        # price_history is ignored by this detector
        _ = price_history # Mark as unused

        if not self.config.regime.use_filter:
            return "Filter_Off"

        # Basic check for required signals
        current_req_signals = self.required_signals
        if not all(sig in signals and pd.notna(signals[sig]) for sig in current_req_signals if sig != 'timestamp'): # Check for NaNs too, ignore timestamp for NaN check
             missing_or_nan = [s for s in current_req_signals if s not in signals or (s != 'timestamp' and pd.isna(signals[s]))]
             self.logger.warning(f"RuleBased: Missing/NaN signals {missing_or_nan}. Returning 'Unknown'.")
             return "Unknown"

        adx = signals.get("adx") # Already checked for NaN above

        if self.config.regime.use_enhanced_detection:
            # Enhanced check needs forecast, handle potential unavailability if TF is off
            forecast = signals.get("forecast")
            if forecast is None or pd.isna(forecast):
                 # This case happens if TF is disabled or if forecast calculation failed
                 self.logger.warning("Enhanced detection: Forecast unavailable/NaN. Falling back to simple ADX.")
                 regime = self._determine_regime_simple(adx)
            else:
                 # Proceed with enhanced detection
                 regime = self._determine_regime_enhanced(signals)
        else: # Use simple detection
            regime = self._determine_regime_simple(adx) # Pass only ADX value

        return regime

    def _determine_regime_simple(self, adx: float) -> str:
        """Basic regime detection using only ADX."""
        # Assumes adx is not NaN (checked in get_regime)
        regime = "Trending" if adx >= self.config.indicators.adx_threshold else "Ranging"
        return regime

    def _determine_regime_enhanced(self, signals: dict) -> str:
        """Enhanced regime using ADX, Forecast, and configurable Volatility/Chop indicator."""
        cfg = self.config # Use local alias for brevity
        adx = signals.get("adx")
        forecast = signals.get("forecast")
        price = signals.get("close")
        chop_index = signals.get("chop_index")
        bbw = signals.get("bbw")
        atr_tf = signals.get("atr_tf")

        # 1. Strong Trend Check (High ADX and High Forecast Magnitude)
        if adx >= cfg.indicators.high_volatility_adx_threshold and abs(forecast) >= cfg.indicators.low_forecast_threshold:
            return "Strong_Trend"

        # 2. Standard Trend Check (ADX Threshold)
        if adx >= cfg.indicators.adx_threshold:
            return "Trending"

        # 3. Ranging or Volatile Chop Check (Low ADX)
        is_choppy = False
        chop_indicator_used = "None"
        chop_value = np.nan

        try:
            # Check Chop Index first if configured
            if cfg.regime.use_chop_index_for_chop:
                if chop_index is not None and not pd.isna(chop_index):
                    chop_value = chop_index
                    chop_indicator_used = f"ChopIndex({chop_value:.1f})"
                    if chop_value > cfg.indicators.chop_index_high_thresh:
                        is_choppy = True
                else:
                    self.logger.warning("Chop Index selected but value is NaN/None.")
                    # chop_indicator_used = "ChopIndex(NaN)" # F841

            # Check BBW if Chop Index not used or NaN
            elif cfg.regime.use_bbw_for_chop_detection:
                if bbw is not None and not pd.isna(bbw):
                    chop_value = bbw
                    # chop_indicator_used = f"BBW({chop_value:.4f})" # F841
                    if chop_value > cfg.indicators.bbw_thresh:
                        is_choppy = True
                else:
                    self.logger.warning("BBW selected for chop but value is NaN/None.")
                    # chop_indicator_used = "BBW(NaN)" # F841

            # Fallback to ATR/Price ratio if other chop indicators not used or NaN
            else:
                if atr_tf is not None and not pd.isna(atr_tf) and \
                   price is not None and not pd.isna(price) and price > 1e-9:
                    atr_ratio = atr_tf / price
                    chop_value = atr_ratio
                    # chop_indicator_used = f"ATR/P({chop_value:.4f})" # F841
                    if atr_ratio > cfg.indicators.volatility_thresh_percent:
                        is_choppy = True
                else:
                    self.logger.warning("ATR/Price fallback for chop failed (NaN/None values).")
                    # chop_indicator_used = "ATR/P(NaN)" # F841: Variable assigned but never used

        except Exception as e:
             self.logger.warning(f"Error during chop check: {e}")

        has_low_forecast = abs(forecast) < cfg.indicators.low_forecast_threshold
        return "Volatile_Chop" if is_choppy or has_low_forecast else "Ranging"


# --- Concrete Implementation: Hurst-Based Detector ---

class HurstRegimeDetector(RegimeDetectorInterface):
    """
    Implements regime detection based on the Hurst exponent. (Inactive by default)
    """
    def __init__(self, config: Config):
        super().__init__(config)
        self.lookback = config.regime.hurst_lookback_periods
        self.trending_threshold = config.regime.hurst_trending_threshold
        self.ranging_threshold = config.regime.hurst_ranging_threshold
        self.min_series_length = config.regime.hurst_min_series_length
        if not (0 < self.ranging_threshold < self.trending_threshold < 1):
             raise ValueError(f"Invalid Hurst thresholds in config: ranging={self.ranging_threshold}, trending={self.trending_threshold}. "
                              "Must satisfy 0 < ranging < trending < 1.")
        self.logger.info(f"Hurst Config: Lookback={self.lookback}, Range={self.ranging_threshold}, Trend={self.trending_threshold}, MinLen={self.min_series_length}")

    @property
    def required_signals(self) -> list[str]:
        """Hurst detector only needs the timestamp from the signals dict."""
        # The primary data (price history) is passed separately.
        return ['timestamp']

    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None) -> str:
        """
        Determines the market regime based on the Hurst exponent of the provided price history.
        """
        if not self.config.regime.use_filter:
            return "Filter_Off"

        if price_history is None:
            self.logger.warning("Hurst detector requires price_history, but it was not provided. Returning 'Unknown'.")
            return "Unknown"

        if price_history.empty:
            self.logger.warning("Hurst detector received an empty price_history series. Returning 'Unknown'.")
            return "Unknown"

        # Calculate Hurst exponent
        # Pass the min_length from config to the calculation function
        hurst_value = calculate_hurst_exponent(price_history, min_length=self.min_series_length)

        if hurst_value is None:
            self.logger.warning(f"Hurst exponent calculation failed for timestamp {signals.get('timestamp', 'N/A')}. Returning 'Unknown'.")
            return "Unknown"

        # Determine regime based on thresholds
        if hurst_value > self.trending_threshold:
            regime = "Persistent" # Trending
        elif hurst_value < self.ranging_threshold:
            regime = "MeanReverting" # Ranging / Anti-persistent
        else:
            regime = "RandomWalk" # Between thresholds

        return regime


# --- NEW Concrete Implementation: Granular Microstructure Detector ---

class GranularMicrostructureRegimeDetector(RegimeDetectorInterface):
    """
    Implements regime detection using a combination of microstructure features
    (OBI, Spread), faster momentum (MA Slope), and volatility (ATR%).

    Outputs standardized state names that are compatible with the centralized mapping system:
    - Strong_Bull_Trend: Strong momentum up with confirmation
    - Weak_Bull_Trend: Weaker momentum up or less confirmation
    - High_Vol_Range: Choppy/ranging market with high volatility
    - Low_Vol_Range: Choppy/ranging market with low volatility
    - Uncertain: Conflicting signals or transition state
    - Weak_Bear_Trend: Weaker momentum down or less confirmation
    - Strong_Bear_Trend: Strong momentum down with confirmation
    - TIGHT_SPREAD: Special case when spread is too tight for reliable signals
    """
    def __init__(self, config: Config):
        super().__init__(config)
        # Store config sections for quick access
        self.cfg_regime = config.regime
        self.cfg_micro = config.microstructure
        self.cfg_indicators = config.indicators
        self.depth_levels = self.cfg_micro.depth_levels # Use unified depth_levels parameter

        # Store relevant parameters from config using getattr for safety
        self.vol_high_thresh = getattr(self.cfg_regime, 'gms_vol_high_thresh', 0.06)
        self.vol_low_thresh = getattr(self.cfg_regime, 'gms_vol_low_thresh', 0.02)
        self.mom_strong_thresh = getattr(self.cfg_regime, 'gms_mom_strong_thresh', 5.0)
        self.mom_weak_thresh = getattr(self.cfg_regime, 'gms_mom_weak_thresh', 1.0)
        self.spread_std_high_thresh = getattr(self.cfg_regime, 'gms_spread_std_high_thresh', 0.0005)
        self.spread_mean_low_thresh = getattr(self.cfg_regime, 'gms_spread_mean_low_thresh', 0.0001)
        # --- Load Primary Adaptive Spread Config ---
        self.spread_mean_mode = getattr(self.cfg_regime, 'gms_spread_mean_thresh_mode', 'fixed')
        self.spread_std_mode = getattr(self.cfg_regime, 'gms_spread_std_thresh_mode', 'fixed')
        self.spread_mean_low_pctile = getattr(self.cfg_regime, 'gms_spread_mean_low_percentile', 0.25)
        self.spread_std_high_pctile = getattr(self.cfg_regime, 'gms_spread_std_high_percentile', 0.75)
        # --- End Load ---
        # --- Load Volatility Threshold Mode ---
        self.vol_thresh_mode = getattr(self.cfg_regime, 'gms_vol_thresh_mode', 'fixed')
        self.logger.debug(f"Loaded volatility threshold mode: {self.vol_thresh_mode}")
        # --- End Load ---
        self.obi_strong_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_strong_confirm_thresh', 0.2)
        self.obi_weak_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_weak_confirm_thresh', 0.05)
        self.use_adx_confirmation = getattr(self.cfg_regime, 'gms_use_adx_confirmation', False)
        self.adx_threshold = getattr(self.cfg_indicators, 'adx_threshold', 30.0)
        self.use_funding_confirmation = getattr(self.cfg_regime, 'gms_use_funding_confirmation', False)
        self.funding_extreme_pos_thresh = getattr(self.cfg_regime, 'gms_funding_extreme_positive_thresh', 0.001)
        self.funding_extreme_neg_thresh = getattr(self.cfg_regime, 'gms_funding_extreme_negative_thresh', -0.001)

        # --- NEW Parameters ---
        # Use gms_obi_zscore_threshold for consistency
        self.obi_zscore_threshold = getattr(self.cfg_regime, 'gms_obi_zscore_threshold', 0) # Default 0 means disabled
        # Ensure spread_percentile_gate is numeric, defaulting to 0 if config value is None or missing
        spread_pct_gate_cfg = getattr(self.cfg_regime, 'gms_spread_percentile_gate', None)
        self.spread_percentile_gate = spread_pct_gate_cfg if isinstance(spread_pct_gate_cfg, (int, float)) else 0
        # Ensure other potentially None config values have correct defaults
        depth_slope_cfg = getattr(self.cfg_regime, 'gms_depth_slope_thin_limit', None)
        self.depth_slope_thin_limit = depth_slope_cfg if isinstance(depth_slope_cfg, (int, float)) else -np.inf
        depth_skew_cfg = getattr(self.cfg_regime, 'gms_depth_skew_thresh', None)
        self.depth_skew_thresh = depth_skew_cfg if isinstance(depth_skew_cfg, (int, float)) else -np.inf
        # More explicit default setting for spread_trend_lookback
        spread_trend_cfg = getattr(self.cfg_regime, 'gms_spread_trend_lookback', None) # Default to None in getattr
        self.spread_trend_lookback = spread_trend_cfg if isinstance(spread_trend_cfg, (int, float)) else 0 # Ensure it's 0 if None
        adaptive_obi_cfg = getattr(self.cfg_regime, 'gms_adaptive_obi_base', None)
        self.adaptive_obi_base = adaptive_obi_cfg if isinstance(adaptive_obi_cfg, (int, float)) else 0
        # Ensure confirmation_bars is an integer >= 0, defaulting to 0 (disabled) if config value is None or <= 0
        conf_bars_from_config = getattr(self.cfg_regime, 'gms_confirmation_bars', None) # Default to None in getattr
        # Handle both int and float types, convert to int if needed
        if isinstance(conf_bars_from_config, (int, float)) and conf_bars_from_config > 0:
            self.confirmation_bars = int(conf_bars_from_config)  # Convert to int if it's a float
        else:
            self.confirmation_bars = 0
        # --- Load new adaptive tight spread fallback config ---
        self.tight_spread_fallback_percentile = getattr(self.cfg_regime, 'gms_tight_spread_fallback_percentile', None)
        # self.tight_spread_percentile_window = getattr(self.cfg_regime, 'gms_tight_spread_percentile_window', 24) # Window not directly used here, but could load for logging

        self.logger.info("Granular Microstructure Detector Initialized.")
        # Log key thresholds being used
        self.logger.info(f"  - Vol Thresh Mode: {self.vol_thresh_mode}")
        if self.vol_thresh_mode == 'fixed':
            self.logger.info(f"  - Vol Thresh (Low/High ATR%): {self.vol_low_thresh:.4f} / {self.vol_high_thresh:.4f}")
        else:  # 'percentile' mode
            vol_low_pct = getattr(self.cfg_regime, 'gms_vol_low_percentile', 25)
            vol_high_pct = getattr(self.cfg_regime, 'gms_vol_high_percentile', 75)
            self.logger.info(f"  - Vol Percentile (Low/High): {vol_low_pct} / {vol_high_pct}")
        self.logger.info(f"  - Mom Thresh (Weak/Strong MA Slope): {self.mom_weak_thresh:.2f} / {self.mom_strong_thresh:.2f}")
        self.logger.info(f"  - OBI Confirm Thresh (Weak/Strong): {self.obi_weak_confirm_thresh:.3f} / {self.obi_strong_confirm_thresh:.3f}")
        self.logger.info(f"  - Depth Levels: {self.depth_levels}") # Updated: Log depth_levels
        self.logger.info(f"  - Spread Thresh (Mean Low / Std High): {self.spread_mean_low_thresh:.6f} / {self.spread_std_high_thresh:.6f}")
        self.logger.info(f"  - Use ADX Confirm: {self.use_adx_confirmation} (Thresh: {self.adx_threshold})")
        self.logger.info(f"  - Use Funding Confirm: {self.use_funding_confirmation} (Thresh +/-: {self.funding_extreme_neg_thresh:.4f} / {self.funding_extreme_pos_thresh:.4f})")
        # Log NEW parameters
        self.logger.info(f"  - OBI Z-Score Thresh: {self.obi_zscore_threshold} (>0 means active)")
        self.logger.info(f"  - Spread Percentile Gate: {self.spread_percentile_gate} (>0 means active)")
        self.logger.info(f"  - Depth Slope Limit: {self.depth_slope_thin_limit} (> -inf means active)")
        self.logger.info(f"  - Depth Skew Thresh: {self.depth_skew_thresh} (> -inf means active)")
        self.logger.info(f"  - Spread Trend Lookback: {self.spread_trend_lookback} (>0 means active)")
        self.logger.info(f"  - Adaptive OBI Base: {self.adaptive_obi_base} (>0 means active)")
        self.logger.info(f"  - Confirmation Bars: {self.confirmation_bars} (>0 means active)")
        self.logger.info(f"  - Tight Spread Fallback Pct: {self.tight_spread_fallback_percentile} (None or 0 means disabled)")

        # Initialize state for confirmation logic
        self.previous_regime = None
        self.confirmation_counter = 0

    @property
    def required_signals(self) -> list[str]:
        """Returns the list of signals needed by the granular detector."""
        # Base signals always needed
        signals_needed = [
            'timestamp',
            'atr_percent',      # Volatility
            'ma_slope',         # Momentum
            f'obi_smoothed_{self.depth_levels}',     # Base OBI Confirmation
            'spread_mean',      # Base Spread Context (Range)
            'spread_std'        # Base Spread Context (Chop)
        ]
        # Add optional signals based on config
        if self.use_adx_confirmation:
            signals_needed.append('adx')
        if self.use_funding_confirmation:
            signals_needed.append('funding_rate')
        # Add signals for NEW conditional logic based on whether the feature is active
        if self.adaptive_obi_base is not None and self.adaptive_obi_base > 0:
            signals_needed.extend(['vol_short_term', 'vol_long_term']) # Need volatility components for adaptive OBI
        if self.obi_zscore_threshold is not None and self.obi_zscore_threshold > 0:
            signals_needed.append(f'obi_zscore_{self.depth_levels}') # Need the Z-score signal if Z-score OBI is active
        # Fallback OBI uses suffixed 'obi_smoothed_X', already included in base

        if self.depth_slope_thin_limit is not None and self.depth_slope_thin_limit > -np.inf:
            signals_needed.append('depth_slope') # Placeholder signal if depth slope check is active
        if self.depth_skew_thresh is not None and self.depth_skew_thresh > -np.inf:
            signals_needed.append('depth_skew') # Approximated signal if depth skew check is active
        # Check the gate value loaded in __init__ which handles None -> 0
        if self.spread_percentile_gate is not None and self.spread_percentile_gate > 0: # Check > 0 as gate is 0-100
            signals_needed.append('raw_spread_percentile') # Need the percentile rank signal if percentile gate is active
        # Check the lookback value loaded in __init__
        if self.spread_trend_lookback is not None and self.spread_trend_lookback > 0:
            signals_needed.append('spread_trend') # Need the trend signal if spread trend is active
        # Fallback spread uses 'spread_mean' and 'spread_std', already included in base

        # --- Add spread_mean_pctile if adaptive tight spread fallback is enabled ---
        # Use the value loaded in __init__ for the check
        if self.tight_spread_fallback_percentile is not None and self.tight_spread_fallback_percentile > 0:
            signals_needed.append('spread_mean_pctile') # This is the FALLBACK percentile
        # --- End ---

        # --- Add PRIMARY spread percentiles if their modes are 'percentile' ---
        if self.spread_mean_mode == 'percentile':
            signals_needed.append('spread_mean_primary_pctile')
        if self.spread_std_mode == 'percentile':
            signals_needed.append('spread_std_primary_pctile')
        # --- End ---

        # --- Add volatility percentile if mode is 'percentile' ---
        if self.vol_thresh_mode == 'percentile':
            signals_needed.append('atr_percent_pctile')
        # --- End ---

        return list(set(signals_needed)) # Return unique list

    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None) -> str:
        """
        Determines the market regime based on the granular microstructure logic,
        incorporating conditional checks, adaptive tight spread fallback, and confirmation bars.

        All output values are standardized according to the state_mapping.py utility module,
        ensuring consistency across the codebase.

        Returns:
            str: One of the standardized state values defined in state_mapping.py:
                 - Basic states: Strong_Bull_Trend, Weak_Bull_Trend, Strong_Bear_Trend,
                   Weak_Bear_Trend, High_Vol_Range, Low_Vol_Range, TIGHT_SPREAD, Uncertain
                 - Special states: Unknown, Filter_Off
        """
        _ = price_history # Not used by this detector

        if not self.cfg_regime.use_filter:
            return "Filter_Off"

        # --- Retrieve Signals Safely & Check NaNs ---
        # Check only for NaNs in signals that are *actually required* by the current configuration
        required_now = ['timestamp', 'atr_percent', 'ma_slope', f'obi_smoothed_{self.depth_levels}', 'spread_mean', 'spread_std'] # Base requirements
        if self.use_adx_confirmation:
            required_now.append('adx')
        if self.use_funding_confirmation:
            required_now.append('funding_rate')
        if self.adaptive_obi_base is not None and self.adaptive_obi_base > 0:
            required_now.extend(['vol_short_term', 'vol_long_term'])
        if self.obi_zscore_threshold is not None and self.obi_zscore_threshold > 0:
            required_now.append(f'obi_zscore_{self.depth_levels}')
        if self.depth_slope_thin_limit is not None and self.depth_slope_thin_limit > -np.inf:
            required_now.append('depth_slope') # Placeholder, might be NaN
        if self.depth_skew_thresh is not None and self.depth_skew_thresh > -np.inf:
            required_now.append('depth_skew') # Placeholder, might be NaN
        # Use the parameters loaded in __init__ for checks
        if self.spread_percentile_gate is not None and self.spread_percentile_gate > 0:
            required_now.append('raw_spread_percentile')
        if self.spread_trend_lookback is not None and self.spread_trend_lookback > 0:
            required_now.append('spread_trend')
        # Add primary percentile signals if needed
        if self.spread_mean_mode == 'percentile':
            required_now.append('spread_mean_primary_pctile')
        if self.spread_std_mode == 'percentile':
            required_now.append('spread_std_primary_pctile')
        # Add fallback percentile if needed
        if self.tight_spread_fallback_percentile is not None and self.tight_spread_fallback_percentile > 0:
            required_now.append('spread_mean_pctile')


        # Check for missing or NaN values in the currently required signals
        missing_or_nan = []
        current_required_signals = self.required_signals # Get the dynamically determined list
        for sig in set(current_required_signals): # Use set to avoid duplicate checks
            if sig == 'timestamp':
                continue # Ignore timestamp for NaN check
            value = signals.get(sig, np.nan) # Use np.nan as default for missing keys
            # Special handling for placeholders: Allow NaN if the signal is depth_slope or depth_skew
            if sig in ['depth_slope', 'depth_skew'] and pd.isna(value):
                continue # Allow NaN for placeholders for now
            # Allow NaN for spread_mean_pctile if the feature is disabled (it shouldn't be in required_signals then, but double-check)
            if sig == 'spread_mean_pctile' and (self.tight_spread_fallback_percentile is None or self.tight_spread_fallback_percentile <= 0):
                 continue # Allow NaN if feature disabled
            if pd.isna(value):
                missing_or_nan.append(sig)

        if missing_or_nan:
            self.logger.warning(f"GMS Cannot determine regime: Missing/NaN required signals {sorted(missing_or_nan)} for active config.")
            # Reset confirmation state if we can't determine regime
            self.previous_regime = None
            self.confirmation_counter = 0
            return "Unknown"

        # Extract core signals
        atr_pct = signals.get('atr_percent', np.nan)
        ma_slope = signals.get('ma_slope', np.nan)
        obi_smooth = signals.get(f'obi_smoothed_{self.depth_levels}', np.nan)
        spread_std = signals.get('spread_std', np.nan)
        spread_mean = signals.get('spread_mean', np.nan)
        # Extract optional signals
        adx = signals.get('adx', np.nan)
        funding_rate = signals.get('funding_rate', np.nan)
        raw_obi_zscore = signals.get(f'obi_zscore_{self.depth_levels}', np.nan)
        raw_spread_percentile = signals.get('raw_spread_percentile', np.nan) # Percentile rank of current spread
        spread_trend = signals.get('spread_trend', np.nan)
        depth_slope = signals.get('depth_slope', np.nan) # Approximated
        depth_skew = signals.get('depth_skew', np.nan)   # Approximated
        vol_short_term = signals.get('vol_short_term', np.nan)
        vol_long_term = signals.get('vol_long_term', np.nan)
        # Primary spread percentiles (used if mode is 'percentile')
        # spread_mean_primary_pctile = signals.get('spread_mean_primary_pctile', np.nan) # F841 - Already commented
        # spread_std_primary_pctile = signals.get('spread_std_primary_pctile', np.nan) # F841 - Already commented
        # Fallback spread percentile (used for 'Uncertain' refinement)
        # spread_mean_fallback_pctile = signals.get('spread_mean_pctile', np.nan) # F841 - Already commented


        # --- Determine OBI Confirmation (Prioritized Logic) ---
        obi_condition = 'NONE' # Default: No confirmation
        obi_logic_used = "N/A"
        try:
            # Priority 1: Adaptive OBI
            if self.adaptive_obi_base is not None and self.adaptive_obi_base > 0:
                obi_logic_used = "Adaptive"
                # We already checked for NaNs in vol_short_term, vol_long_term if this path is active
                if vol_long_term > 1e-9: # Avoid division by zero or near-zero
                    vol_ratio = vol_short_term / vol_long_term
                    if vol_ratio >= 0: # Ensure ratio is non-negative before sqrt
                        adaptive_thresh = self.adaptive_obi_base * np.sqrt(vol_ratio)
                        # self.logger.debug(f"Adaptive OBI Thresh: {adaptive_thresh:.4f} (Base:{self.adaptive_obi_base}, V_short:{vol_short_term:.4f}, V_long:{vol_long_term:.4f})")
                        if obi_smooth > adaptive_thresh:
                            obi_condition = 'STRONG' # Adaptive uses one threshold for now
                        elif obi_smooth < -adaptive_thresh:
                            obi_condition = 'STRONG' # Symmetric for now
                        # No 'WEAK' state defined for adaptive yet
                    else:
                        self.logger.warning(f"Negative volatility ratio ({vol_ratio:.2f}) for adaptive OBI. Skipping.")
                else:
                     self.logger.warning(f"Near-zero long-term volatility ({vol_long_term:.4f}) for adaptive OBI. Skipping.")

            # Priority 2: Z-Score OBI
            elif self.obi_zscore_threshold is not None and self.obi_zscore_threshold > 0:
                obi_logic_used = "Z-Score"
                # We already checked for NaNs in raw_obi_zscore if this path is active
                try:
                    # Add safety checks for raw_obi_zscore
                    if pd.isna(raw_obi_zscore) or not np.isfinite(raw_obi_zscore):
                        # Fall back to regular OBI logic
                        obi_logic_used = "Fallback Threshold"
                        if obi_smooth > self.obi_strong_confirm_thresh: obi_condition = 'STRONG'
                        elif obi_smooth > self.obi_weak_confirm_thresh: obi_condition = 'WEAK'
                        elif obi_smooth < -self.obi_strong_confirm_thresh: obi_condition = 'STRONG'
                        elif obi_smooth < -self.obi_weak_confirm_thresh: obi_condition = 'WEAK'
                    elif abs(raw_obi_zscore) >= self.obi_zscore_threshold:
                        obi_condition = 'STRONG' # Z-score uses one threshold for strong confirmation
                    # No 'WEAK' state defined for Z-score yet
                except Exception as e:
                    # Fall back to regular OBI logic
                    self.logger.warning(f"Z-Score OBI processing error: {e}. Falling back to threshold-based OBI.")
                    obi_logic_used = "Fallback Threshold"
                    if obi_smooth > self.obi_strong_confirm_thresh: obi_condition = 'STRONG'
                    elif obi_smooth > self.obi_weak_confirm_thresh: obi_condition = 'WEAK'
                    elif obi_smooth < -self.obi_strong_confirm_thresh: obi_condition = 'STRONG'
                    elif obi_smooth < -self.obi_weak_confirm_thresh: obi_condition = 'WEAK'

            # Priority 3: Fallback Threshold OBI
            else:
                obi_logic_used = "Fallback Threshold"
                # We already checked for NaNs in obi_smooth
                if obi_smooth > self.obi_strong_confirm_thresh:
                    obi_condition = 'STRONG'
                elif obi_smooth > self.obi_weak_confirm_thresh:
                    obi_condition = 'WEAK'
                elif obi_smooth < -self.obi_strong_confirm_thresh:
                    obi_condition = 'STRONG'
                elif obi_smooth < -self.obi_weak_confirm_thresh:
                    obi_condition = 'WEAK'

        except Exception as e:
            self.logger.error(f"Error during OBI condition check (using {obi_logic_used}): {e}", exc_info=True)
            obi_condition = 'NONE' # Fallback on error

        # --- Determine Market Condition (Prioritized Logic) ---
        market_condition = 'NORMAL' # Default
        market_logic_used = "N/A"
        try:
            # Priority 1: Depth Slope (Thin Market) - Use the limit loaded in __init__
            if self.depth_slope_thin_limit is not None and self.depth_slope_thin_limit > -np.inf:
                 market_logic_used = "Depth Slope"
                 # NaN check already done if this path is active, but depth_slope might still be NaN (allowed placeholder)
                 if not pd.isna(depth_slope):
                      # Log the comparison (DEBUG log commented out after verification)
                      is_thin = depth_slope < self.depth_slope_thin_limit
                      # self.logger.debug(f"[Depth Check] Slope: {depth_slope:.6f}, Limit: {self.depth_slope_thin_limit:.6f}, Is Thin: {is_thin}")
                      if is_thin:
                           market_condition = 'THIN_LIQUIDITY'
                 # else: self.logger.debug("Depth Slope is NaN (Placeholder).")

            # Priority 2: Depth Skew (Placeholder) - Only check if not already THIN
            if market_condition == 'NORMAL' and self.depth_skew_thresh is not None and self.depth_skew_thresh > -np.inf:
                 market_logic_used = "Depth Skew"
                 # NaN check already done if this path is active, but depth_skew might still be NaN (allowed placeholder)
                 if not pd.isna(depth_skew):
                      # Log the values even if logic is placeholder (DEBUG log commented out after verification)
                      # self.logger.debug(f"[Depth Check] Skew: {depth_skew:.6f}, Thresh: {self.depth_skew_thresh:.6f} (Placeholder Logic)")
                      # Placeholder: Add actual logic using self.depth_skew_thresh if needed
                      # Example: if abs(depth_skew) > self.depth_skew_thresh: market_condition = 'SKEWED_BOOK'
                      # --- Implementing Skew Logic ---
                      if abs(depth_skew) > self.depth_skew_thresh:
                           market_condition = 'SKEWED_BOOK'
                           # Optional: Add logging if desired
                           # self.logger.debug(f"Setting market_condition to SKEWED_BOOK based on skew {depth_skew:.4f} > thresh {self.depth_skew_thresh:.4f}")
                 # else: self.logger.debug("Depth Skew is NaN (Placeholder).")

            # Priority 3: Spread Percentile Gate - Only check if NORMAL so far
            if market_condition == 'NORMAL' and self.spread_percentile_gate is not None and self.spread_percentile_gate > 0:
                 market_logic_used = "Spread Percentile"
                 # NaN check already done if this path is active
                 threshold = 100 - self.spread_percentile_gate
                 if raw_spread_percentile > threshold:
                      market_condition = 'WIDE_SPREAD'

            # Priority 4: Spread Trend (Placeholder) - Only check if NORMAL so far
            if market_condition == 'NORMAL' and self.spread_trend_lookback is not None and self.spread_trend_lookback > 0:
                 market_logic_used = "Spread Trend"
                 # NaN check already done if this path is active
                 # Placeholder: Add actual logic using spread_trend if needed
                 # Example: if spread_trend > some_threshold: market_condition = 'TRENDING_SPREAD_UP'
                 # self.logger.debug(f"Spread Trend check active (Value: {spread_trend:.8f}, Lookback: {self.spread_trend_lookback}) - Placeholder logic.")
                 pass

            # Priority 5: Fallback Spread Mean/Std - Only check if NORMAL so far
            if market_condition == 'NORMAL':
                 market_logic_used = "Fallback Spread"
                 # NaN checks already done for spread_std and spread_mean
                 if spread_std >= self.spread_std_high_thresh:
                      market_condition = 'CHOPPY' # High spread volatility indicates chop
                      # self.logger.debug(f"Market Condition: CHOPPY (Spread Std {spread_std:.6f} >= {self.spread_std_high_thresh:.6f})")
                 elif spread_mean <= self.spread_mean_low_thresh:
                      # Tight spreads might indicate Low Vol Range, but let Volatility check handle that state primarily
                      # market_condition = 'TIGHT_SPREAD' # Optional: Could define this state
                      pass # Don't override to 'TIGHT' here, let Vol check decide Low_Vol_Range

        except Exception as e:
            self.logger.error(f"Error during Market condition check (using {market_logic_used}): {e}", exc_info=True)
            market_condition = 'NORMAL' # Fallback on error


        # --- Determine Potential Regime based on Vol, Mom, OBI, Market Cond ---
        potential_regime = GMS_STATE_UNCERTAIN # Default
        # NaN checks for core signals (atr_pct, ma_slope) already performed

        # 1. Check Volatility Ranges First (Can override momentum checks)

        # --- High Vol Range Spread Check ---
        is_spread_std_high = False
        if self.spread_std_mode == 'percentile':
            spread_std_pctile = signals.get('spread_std_primary_pctile', np.nan)
            if not pd.isna(spread_std_pctile): # Ensure pandas (pd) is imported
                is_spread_std_high = spread_std_pctile >= self.spread_std_high_pctile
        else: # 'fixed' mode
            # spread_std already retrieved and NaN checked earlier
            if not pd.isna(spread_std):
                is_spread_std_high = spread_std >= self.spread_std_high_thresh
        # --- End High Vol Range Spread Check ---

        # --- Volatility Check using either percentile or fixed thresholds ---
        is_vol_high = False
        is_vol_low = False

        if self.vol_thresh_mode == 'percentile':
            # Use percentile-based thresholds
            atr_pct_pctile = signals.get('atr_percent_pctile', np.nan)
            if not pd.isna(atr_pct_pctile):
                vol_high_pct = getattr(self.cfg_regime, 'gms_vol_high_percentile', 75) / 100.0
                vol_low_pct = getattr(self.cfg_regime, 'gms_vol_low_percentile', 25) / 100.0
                is_vol_high = atr_pct_pctile >= vol_high_pct
                is_vol_low = atr_pct_pctile <= vol_low_pct
        else:
            # Use fixed thresholds (original behavior)
            is_vol_high = atr_pct >= self.vol_high_thresh
            is_vol_low = atr_pct <= self.vol_low_thresh
        # --- End Volatility Check ---

        # Check High Volatility condition
        if is_vol_high:
            # High Volatility: Check if momentum is weak OR spread std is high
            if abs(ma_slope) < self.mom_weak_thresh or is_spread_std_high:
                 potential_regime = GMS_STATE_HIGH_VOL_RANGE
            # Else (High Vol + Momentum + Not Choppy/High Spread) -> Let momentum logic decide Trend below

        # Check Low Volatility condition
        elif is_vol_low:
            # Low Volatility: Check if momentum is weak AND spread mean is low AND market is not wide spread
            # --- Low Vol Range Spread Check ---
            is_spread_mean_low = False
            if self.spread_mean_mode == 'percentile':
                spread_mean_pctile = signals.get('spread_mean_primary_pctile', np.nan)
                if not pd.isna(spread_mean_pctile): # Ensure pandas (pd) is imported
                    is_spread_mean_low = spread_mean_pctile < self.spread_mean_low_pctile
            else: # 'fixed' mode
                # spread_mean already retrieved and NaN checked earlier
                if not pd.isna(spread_mean):
                    is_spread_mean_low = spread_mean <= self.spread_mean_low_thresh
            # --- End Low Vol Range Spread Check ---

            if abs(ma_slope) < self.mom_weak_thresh and is_spread_mean_low and market_condition != 'WIDE_SPREAD':
                 potential_regime = GMS_STATE_LOW_VOL_RANGE
            # Else (Low Vol + Momentum or Wide Spread or High Spread Mean) -> Let momentum logic decide Weak Trend or Uncertain below
        # 2. Check Momentum (if not already assigned a Range regime)
        if potential_regime == GMS_STATE_UNCERTAIN:
            is_bullish = ma_slope > 0
            is_strong_mom = abs(ma_slope) >= self.mom_strong_thresh
            is_weak_mom = abs(ma_slope) >= self.mom_weak_thresh # Includes strong

            # Optional confirmations (NaN checks already done if these are required)
            adx_confirms = (not self.use_adx_confirmation) or (adx >= self.adx_threshold)
            funding_confirms = (not self.use_funding_confirmation) or \
                               (is_bullish and funding_rate < self.funding_extreme_pos_thresh) or \
                               (not is_bullish and funding_rate > self.funding_extreme_neg_thresh)

            if is_strong_mom:
                # Strong Momentum: Requires STRONG OBI and confirmations for Strong Trend
                if is_bullish and obi_condition == 'STRONG' and adx_confirms and funding_confirms:
                    potential_regime = GMS_STATE_STRONG_BULL_TREND
                elif not is_bullish and obi_condition == 'STRONG' and adx_confirms and funding_confirms:
                    potential_regime = GMS_STATE_STRONG_BEAR_TREND
                # Downgrade to Weak Trend if strong mom but OBI/confirmations don't fully align
                elif is_bullish and obi_condition in ['STRONG', 'WEAK']:
                    potential_regime = GMS_STATE_WEAK_BULL_TREND
                elif not is_bullish and obi_condition in ['STRONG', 'WEAK']:
                    potential_regime = "Weak_Bear_Trend"
                # else: remains Uncertain if strong mom but no OBI alignment

            elif is_weak_mom: # Weak but not Strong momentum
                # Weak Momentum: Requires at least WEAK OBI for Weak Trend
                if is_bullish and obi_condition in ['STRONG', 'WEAK']:
                    potential_regime = "Weak_Bull_Trend"
                elif not is_bullish and obi_condition in ['STRONG', 'WEAK']:
                    potential_regime = "Weak_Bear_Trend"
                # else: remains Uncertain if weak mom but no OBI alignment

            # else: remains Uncertain if momentum is negligible (abs(ma_slope) < self.mom_weak_thresh)

        # --- Refine Regime with Depth/Trend Signals (if active) ---
        refined_reason = ""
        # Check Depth Slope (Thin Market) - Use the limit loaded in __init__
        if self.depth_slope_thin_limit is not None and self.depth_slope_thin_limit > -np.inf:
            if pd.notna(depth_slope) and depth_slope < self.depth_slope_thin_limit:
                refined_reason += f" DepthSlopeThin({depth_slope:.4f}<{self.depth_slope_thin_limit:.4f})"
                # Optional: Modify regime, e.g., regime = "Uncertain" if regime == "Low_Vol_Range"
                # For now, just log the condition.

        # Check Depth Skew - Use the threshold loaded in __init__
        if self.depth_skew_thresh is not None and self.depth_skew_thresh > -np.inf:
             if pd.notna(depth_skew) and abs(depth_skew) > self.depth_skew_thresh:
                 skew_dir = "AskHeavy" if depth_skew > 0 else "BidHeavy"
                 refined_reason += f" DepthSkew({skew_dir}:{depth_skew:.2f}, Th:{self.depth_skew_thresh:.2f})"
                 # Optional: Modify regime, e.g., weaken trend confirmation if skew opposes momentum.

        # Check Spread Trend - Use the lookback loaded in __init__
        if self.spread_trend_lookback is not None and self.spread_trend_lookback > 0:
             if pd.notna(spread_trend):
                 # Define a small threshold to consider trend significant (avoid noise)
                 # This threshold might need tuning or be made configurable
                 trend_thresh = getattr(self.cfg_regime, 'gms_spread_trend_significance_thresh', 1e-7) # Example small value
                 if spread_trend > trend_thresh:
                     refined_reason += f" SpreadWidening({spread_trend:.6f})"
                     # Optional: If ranging, maybe push towards High_Vol_Range?
                     # if regime == "Low_Vol_Range": regime = "High_Vol_Range"
                 elif spread_trend < -trend_thresh:
                     refined_reason += f" SpreadTightening({spread_trend:.6f})"
                     # Optional: If High_Vol_Range, maybe push towards Low_Vol_Range?
                     # if regime == "High_Vol_Range": regime = "Low_Vol_Range"

        if refined_reason:
            # Log using potential_regime as the regime before this refinement step
            self.logger.debug(f"  Regime Refinement Signals:{refined_reason} (Regime Before Refinement: {potential_regime})")
            # Note: Regime variable (potential_regime) itself is not modified here yet, only logged.
            # Modifications based on these signals can be added within the checks above if needed.
        # --- End Refinement ---


        # --- Final Fallback Logic (Check for Tight Spread Override) ---
        # This happens if primary logic resulted in "Uncertain"
        final_potential_regime = potential_regime # Start with the result from above

        if final_potential_regime == "Uncertain":
            # Check for Tight Spread Fallback Override (only if enabled)
            # Use the value loaded in __init__
            if self.tight_spread_fallback_percentile is not None and self.tight_spread_fallback_percentile > 0:
                spread_pctile = signals.get('spread_mean_pctile') # Signal was added conditionally
                # Check if percentile signal is valid (not NaN)
                if spread_pctile is not None and not pd.isna(spread_pctile):
                    if spread_pctile < self.tight_spread_fallback_percentile:
                        self.logger.info(f"GMS Fallback Override: Spread Mean Pctile ({spread_pctile:.3f}) < Thresh ({self.tight_spread_fallback_percentile:.3f}). Overriding 'Uncertain' with 'TIGHT_SPREAD'.")
                        final_potential_regime = "TIGHT_SPREAD" # Override state
                    # Else: Spread is not below percentile threshold, keep 'Uncertain'
                else:
                    # This case should be rare if required_signals logic is correct and SignalEngine adds NaN column when disabled
                    self.logger.warning(f"Tight Spread Fallback check failed: spread_mean_pctile signal is missing or NaN (Value: {spread_pctile}). Keeping 'Uncertain'.")
                    # Keep 'Uncertain' if percentile is missing/NaN

        # Log the factors leading to the potential regime determination (including potential override)
        self.logger.info(
            f"GMS Factors @ {signals.get('timestamp', 'N/A')}: "
            f"Vol={atr_pct:.4f} (L:{self.vol_low_thresh:.4f}/H:{self.vol_high_thresh:.4f}), "
            f"Mom={ma_slope:.2f} (W:{self.mom_weak_thresh:.2f}/S:{self.mom_strong_thresh:.2f}), "
            f"OBI='{obi_condition}' (Logic:{obi_logic_used}), "
            f"Mkt='{market_condition}' (Logic:{market_logic_used}) "
            f"=> Initial Potential='{potential_regime}', Final Potential='{final_potential_regime}'" # Log both
        )


        # --- Apply Confirmation Bars Logic (if enabled) ---
        # Use the potentially overridden state (final_potential_regime) for confirmation
        potential_regime_for_confirm = final_potential_regime
        final_regime = potential_regime_for_confirm # Default if no confirmation needed

        if self.confirmation_bars > 0:
            # Confirmation is enabled
            if potential_regime_for_confirm == self.previous_regime:
                # Regime is the same as the last potential one, increment counter
                self.confirmation_counter += 1
            else:
                # Regime changed
                self.previous_regime = potential_regime_for_confirm # Update previous regime regardless
                self.confirmation_counter = 1 # Reset counter to 1

                if self.confirmation_bars == 1:
                    # If confirmation_bars is 1, confirm and return immediately
                    final_regime = potential_regime_for_confirm
                    self.logger.info(f"New regime '{final_regime}' detected and CONFIRMED immediately (confirmation_bars=1).")
                    return final_regime # Return the confirmed regime now
                else:
                    # If confirmation_bars > 1, return Uncertain for the first bar
                    final_regime = "Uncertain"
                    self.logger.info(f"New regime detected: '{potential_regime_for_confirm}'. Counter reset to 1/{self.confirmation_bars}. Returning 'Uncertain' for this bar.")
                    return final_regime # Return Uncertain only if N > 1

            # Check if the counter meets the threshold
            if self.confirmation_counter >= self.confirmation_bars:
                # Confirmed! Final regime is the potential regime
                final_regime = potential_regime_for_confirm # Use the potentially overridden state
                self.logger.info(f"Regime '{final_regime}' CONFIRMED (Counter: {self.confirmation_counter}/{self.confirmation_bars})")
            else:
                # Not confirmed yet. Return "Uncertain"
                final_regime = "Uncertain"
                self.logger.info(f"Regime UNCONFIRMED (Potential: '{potential_regime_for_confirm}', Counter: {self.confirmation_counter}/{self.confirmation_bars}) -> Uncertain")
        # else: Confirmation disabled, final_regime remains potential_regime_for_confirm

        # --- PHASE 4: VALIDATE FINAL OUTPUT STATE ---
        valid_output_states = get_valid_gms_states()  # Get all valid GMS states including special states

        if final_regime not in valid_output_states:
            self.logger.error(
                f"GMS Detector produced an invalid/unknown state string: '{final_regime}'. "
                f"This indicates an issue in the internal GMS logic or a missing constant definition. "
                f"Falling back to '{GMS_STATE_UNKNOWN}'."
            )
            final_regime = GMS_STATE_UNKNOWN  # Use the constant for unknown state

        return final_regime


# --- Factory Function (Updated) ---

# REMOVED: get_regime_detector function has been moved to detector_factory.py
# for clean separation between legacy and modern systems.
# Update imports to use: from hyperliquid_bot.core.detector_factory import get_regime_detector

# Import the ContinuousGMSDetector after RegimeDetectorInterface is defined
from hyperliquid_bot.core.gms_detector import ContinuousGMSDetector


# The _validate_unified_gms_config function has been removed
# as part of the system isolation. Validation is now handled
# by each system's components directly.