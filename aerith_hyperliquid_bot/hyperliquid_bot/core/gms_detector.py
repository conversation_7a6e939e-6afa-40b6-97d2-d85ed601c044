# hyperliquid_bot/core/gms_detector.py

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, Union, Any
from pathlib import Path
import pandas as pd
import numpy as np
import pyarrow as pa
import pyarrow.dataset as ds

# Import the Config class
from hyperliquid_bot.config.settings import Config

# Import the helper function for dynamic OBI column resolution
from hyperliquid_bot.utils.feature_naming import obi_col

# Import the ABC and abstractmethod for creating the interface
from abc import ABC, abstractmethod

# Import standardized state constants and validation from utility
from hyperliquid_bot.utils.state_mapping import (
    # State constants
    GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND,
    GMS_STATE_HIGH_VOL_RANGE, GMS_STATE_LOW_VOL_RANGE,
    GMS_STATE_UNCERTAIN, GMS_STATE_WEAK_BEAR_TREND,
    GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_TIGHT_SPREAD,
    GMS_STATE_UNKNOWN, get_valid_gms_states,
    # Mapping functions
    get_state_map, map_gms_state
)

# Define a minimal RegimeDetectorInterface for this file to avoid circular imports
class RegimeDetectorInterface(ABC):
    """Abstract base class for regime detectors."""

    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)

    @property
    @abstractmethod
    def required_signals(self) -> list[str]:
        """Returns the list of signals needed by the detector."""
        pass

    @abstractmethod
    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None) -> Union[str, Dict[str, Any]]:
        """Determines the current market regime."""
        pass

class ContinuousGMSDetector(RegimeDetectorInterface):
    """
    Implements continuous regime detection using a combination of microstructure features
    (OBI, Spread), faster momentum (MA Slope), and volatility (ATR%).

    Key features:
    - Recomputes every <config.gms.cadence_sec> seconds (default 60)
    - Accepts arbitrary depth (5 or 20) via config.microstructure.obi_levels
    - Outputs the raw 8 states *plus* a `risk_suppressed` boolean
    - Supports collapse-maps (3-state, 4-state) via YAML mapping file

    Outputs standardized state names that are compatible with the centralized mapping system:
    - Strong_Bull_Trend: Strong momentum up with confirmation
    - Weak_Bull_Trend: Weaker momentum up or less confirmation
    - High_Vol_Range: Choppy/ranging market with high volatility
    - Low_Vol_Range: Choppy/ranging market with low volatility
    - Uncertain: Conflicting signals or transition state
    - Weak_Bear_Trend: Weaker momentum down or less confirmation
    - Strong_Bear_Trend: Strong momentum down with confirmation
    - TIGHT_SPREAD: Special case when spread is too tight for reliable signals
    """
    def __init__(self, config: Config):
        super().__init__(config)
        # Store config sections for quick access
        self.cfg_regime = config.regime
        self.cfg_micro = config.microstructure
        self.cfg_indicators = config.indicators
        self.cfg_gms = getattr(config, 'gms', None)  # New GMS config section
        # For risk_suppressed calculation - use portfolio config instead of core
        self.cfg_portfolio = getattr(config, 'portfolio', None)

        # Store depth levels from config
        self.depth_levels = self.cfg_micro.depth_levels

        # Get detector-specific settings with fallback to flat structure
        detector_type = getattr(self.cfg_regime, 'detector_type', 'continuous_gms')
        detector_settings = self.cfg_regime.get_detector_settings(detector_type)

        # Get operational settings (cadence, output_states, etc.) with fallback to gms section
        operational_settings = self.cfg_regime.get_detector_operational_settings(detector_type, self.cfg_gms)

        # Store cadence settings from detector-specific section or fallback
        self.cadence_sec = operational_settings.get('cadence_sec', 60)

        # Store relevant parameters from detector-specific config with fallback to getattr for safety
        self.vol_high_thresh = detector_settings.get('gms_vol_high_thresh', getattr(self.cfg_regime, 'gms_vol_high_thresh', 0.06))
        self.vol_low_thresh = detector_settings.get('gms_vol_low_thresh', getattr(self.cfg_regime, 'gms_vol_low_thresh', 0.02))
        self.mom_strong_thresh = detector_settings.get('gms_mom_strong_thresh', getattr(self.cfg_regime, 'gms_mom_strong_thresh', 5.0))
        self.mom_weak_thresh = detector_settings.get('gms_mom_weak_thresh', getattr(self.cfg_regime, 'gms_mom_weak_thresh', 1.0))
        self.spread_std_high_thresh = detector_settings.get('gms_spread_std_high_thresh', getattr(self.cfg_regime, 'gms_spread_std_high_thresh', 0.0005))
        self.spread_mean_low_thresh = detector_settings.get('gms_spread_mean_low_thresh', getattr(self.cfg_regime, 'gms_spread_mean_low_thresh', 0.0001))

        # Load Primary Adaptive Spread Config
        self.spread_mean_mode = getattr(self.cfg_regime, 'gms_spread_mean_thresh_mode', 'fixed')
        self.spread_std_mode = getattr(self.cfg_regime, 'gms_spread_std_thresh_mode', 'fixed')
        self.spread_mean_low_pctile = getattr(self.cfg_regime, 'gms_spread_mean_low_percentile', 0.25)
        self.spread_std_high_pctile = getattr(self.cfg_regime, 'gms_spread_std_high_percentile', 0.75)

        # Load Volatility Threshold Mode
        self.vol_thresh_mode = getattr(self.cfg_regime, 'gms_vol_thresh_mode', 'fixed')

        # Initialize adaptive thresholds (Task R-112k)
        self.adaptive_vol_threshold = None
        self.adaptive_mom_threshold = None
        if self.cfg_gms and getattr(self.cfg_gms, 'auto_thresholds', False):
            from hyperliquid_bot.utils.adaptive_threshold import AdaptiveThreshold

            # Calculate window length from seconds to number of samples
            # Assuming 1-second cadence for the data
            window_sec = getattr(self.cfg_gms, 'percentile_window_sec', 86400)
            window_len = window_sec  # 1 sample per second

            # Get percentile settings
            vol_low_pct = getattr(self.cfg_gms, 'vol_low_pct', 0.15)
            vol_high_pct = getattr(self.cfg_gms, 'vol_high_pct', 0.50)
            mom_low_pct = getattr(self.cfg_gms, 'mom_low_pct', 0.15)
            mom_high_pct = getattr(self.cfg_gms, 'mom_high_pct', 0.50)

            # Initialize adaptive threshold instances
            self.adaptive_vol_threshold = AdaptiveThreshold(vol_low_pct, vol_high_pct, window_len)
            self.adaptive_mom_threshold = AdaptiveThreshold(mom_low_pct, mom_high_pct, window_len)

            # Store minimum history requirement
            self.min_history_rows = getattr(self.cfg_gms, 'min_history_rows', 10000)

        # OBI thresholds
        self.obi_strong_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_strong_confirm_thresh', 0.2)
        self.obi_weak_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_weak_confirm_thresh', 0.05)

        # Confirmation settings
        self.use_adx_confirmation = getattr(self.cfg_regime, 'gms_use_adx_confirmation', False)
        self.adx_threshold = getattr(self.cfg_indicators, 'adx_threshold', 30.0)
        self.use_funding_confirmation = getattr(self.cfg_regime, 'gms_use_funding_confirmation', False)
        self.funding_extreme_pos_thresh = getattr(self.cfg_regime, 'gms_funding_extreme_positive_thresh', 0.001)
        self.funding_extreme_neg_thresh = getattr(self.cfg_regime, 'gms_funding_extreme_negative_thresh', -0.001)

        # NEW Parameters
        # Use gms_obi_zscore_threshold for consistency
        self.obi_zscore_threshold = getattr(self.cfg_regime, 'gms_obi_zscore_threshold', 0) # Default 0 means disabled

        # Ensure spread_percentile_gate is numeric, defaulting to 0 if config value is None or missing
        spread_pct_gate_cfg = getattr(self.cfg_regime, 'gms_spread_percentile_gate', None)
        self.spread_percentile_gate = spread_pct_gate_cfg if isinstance(spread_pct_gate_cfg, (int, float)) else 0

        # Ensure other potentially None config values have correct defaults
        depth_slope_cfg = getattr(self.cfg_regime, 'gms_depth_slope_thin_limit', None)
        self.depth_slope_thin_limit = depth_slope_cfg if isinstance(depth_slope_cfg, (int, float)) else -np.inf
        depth_skew_cfg = getattr(self.cfg_regime, 'gms_depth_skew_thresh', None)
        self.depth_skew_thresh = depth_skew_cfg if isinstance(depth_skew_cfg, (int, float)) else -np.inf

        # More explicit default setting for spread_trend_lookback
        spread_trend_cfg = getattr(self.cfg_regime, 'gms_spread_trend_lookback', None) # Default to None in getattr
        self.spread_trend_lookback = spread_trend_cfg if isinstance(spread_trend_cfg, (int, float)) else 0 # Ensure it's 0 if None
        adaptive_obi_cfg = getattr(self.cfg_regime, 'gms_adaptive_obi_base', None)
        self.adaptive_obi_base = adaptive_obi_cfg if isinstance(adaptive_obi_cfg, (int, float)) else 0

        # Ensure confirmation_bars is an integer >= 0, defaulting to 0 (disabled) if config value is None or <= 0
        conf_bars_from_config = getattr(self.cfg_regime, 'gms_confirmation_bars', None) # Default to None in getattr
        # Handle both int and float types, convert to int if needed
        if isinstance(conf_bars_from_config, (int, float)) and conf_bars_from_config > 0:
            self.confirmation_bars = int(conf_bars_from_config)  # Convert to int if it's a float
        else:
            self.confirmation_bars = 0

        # Load new adaptive tight spread fallback config
        self.tight_spread_fallback_percentile = getattr(self.cfg_regime, 'gms_tight_spread_fallback_percentile', None)

        # Risk suppression thresholds from detector-specific settings
        self.risk_suppressed_notional_frac = operational_settings.get('risk_suppressed_notional_frac', 0.25)
        self.risk_suppressed_pnl_atr_mult = operational_settings.get('risk_suppressed_pnl_atr_mult', 1.5)

        # Disable risk suppression for backtest
        self.disable_risk_suppression = True

        # ATR column configuration
        self.ATR_COL = 'atr_14_sec'  # Default ATR column name
        self.ATR_PCT_COL = 'atr_percent_sec'  # Default ATR percent column name

        # State collapse map settings from detector-specific settings
        self.output_states = operational_settings.get('output_states', 8)  # 8 = raw, 4 or 3 after collapse
        self.use_four_state_mapping = operational_settings.get('use_four_state_mapping', False)
        self.state_collapse_map_file = operational_settings.get('state_collapse_map_file', 'configs/gms_state_mapping.yaml')

        # Initialize state tracking
        self.current_state = GMS_STATE_UNCERTAIN
        self.last_state_change_ts = time.time()
        self.previous_regime = None
        self.confirmation_counter = 0
        self.last_update_ts = 0
        self.risk_suppressed = False
        
        # Initialize regime confidence and duration tracking (Phase 2)
        self.regime_start_time = time.time()
        self.regime_confidence = 1.0
        self.regime_transition_count = 0
        self.previous_state = GMS_STATE_UNCERTAIN

        # Log initialization
        self.logger.info("Continuous GMS Detector Initialized.")
        self.logger.info(f"  - Cadence: {self.cadence_sec} seconds")
        self.logger.info(f"  - Depth Levels: {self.depth_levels}")
        self.logger.info(f"  - Output States: {self.output_states}")

        # Log threshold mode and settings
        # Priority: Adaptive thresholds supersede legacy vol_thresh_mode
        if self.adaptive_vol_threshold is not None and self.adaptive_mom_threshold is not None:
            self.logger.info(f"  - Threshold Mode: ADAPTIVE (auto_thresholds=True)")
            self.logger.info(f"  - Adaptive Window: {getattr(self.cfg_gms, 'percentile_window_sec', 86400)} seconds")
            self.logger.info(f"  - Vol Percentiles: {getattr(self.cfg_gms, 'vol_low_pct', 0.15):.2f} / {getattr(self.cfg_gms, 'vol_high_pct', 0.50):.2f}")
            self.logger.info(f"  - Mom Percentiles: {getattr(self.cfg_gms, 'mom_low_pct', 0.15):.2f} / {getattr(self.cfg_gms, 'mom_high_pct', 0.50):.2f}")
            self.logger.info(f"  - Min History Rows: {getattr(self.cfg_gms, 'min_history_rows', 10000)}")
        elif self.vol_thresh_mode == 'fixed':
            self.logger.info(f"  - Threshold Mode: FIXED")
            self.logger.info(f"  - Vol Thresh (Low/High ATR%): {self.vol_low_thresh:.4f} / {self.vol_high_thresh:.4f}")
            self.logger.info(f"  - Mom Thresh (Weak/Strong MA Slope): {self.mom_weak_thresh:.2f} / {self.mom_strong_thresh:.2f}")
        else:  # 'percentile' mode
            self.logger.info(f"  - Threshold Mode: PERCENTILE")
            vol_low_pct = getattr(self.cfg_regime, 'gms_vol_low_percentile', 25)
            vol_high_pct = getattr(self.cfg_regime, 'gms_vol_high_percentile', 75)
            self.logger.info(f"  - Vol Percentile (Low/High): {vol_low_pct} / {vol_high_pct}")
            self.logger.info(f"  - Mom Thresh (Weak/Strong MA Slope): {self.mom_weak_thresh:.2f} / {self.mom_strong_thresh:.2f}")

        self.logger.info(f"  - OBI Confirm Thresh (Weak/Strong): {self.obi_weak_confirm_thresh:.3f} / {self.obi_strong_confirm_thresh:.3f}")
        self.logger.info(f"  - Spread Thresh (Mean Low / Std High): {self.spread_mean_low_thresh:.6f} / {self.spread_std_high_thresh:.6f}")
        self.logger.info(f"  - Use ADX Confirm: {self.use_adx_confirmation} (Thresh: {self.adx_threshold})")
        self.logger.info(f"  - Use Funding Confirm: {self.use_funding_confirmation} (Thresh +/-: {self.funding_extreme_neg_thresh:.4f} / {self.funding_extreme_pos_thresh:.4f})")
        self.logger.info(f"  - Risk Suppression: Notional Frac={self.risk_suppressed_notional_frac}, PnL ATR Mult={self.risk_suppressed_pnl_atr_mult}")
        self.logger.info(f"  - State Collapse: 4-State={self.use_four_state_mapping}, Map File={self.state_collapse_map_file}")

        # Prime adaptive thresholds with historical data if configured (Task R-112m)
        # This must be done after ATR column configuration is set
        if self.adaptive_vol_threshold is not None and self.adaptive_mom_threshold is not None:
            priming_hours = getattr(self.cfg_gms, 'priming_hours', 0)
            if priming_hours > 0:
                self._prime_adaptive_thresholds(priming_hours)

    def _resolve_obi_column(self, signals: dict, column_type: str) -> str:
        """
        Dynamically resolve OBI column name based on available signals and depth.

        Args:
            signals: Dictionary of available signals
            column_type: Type of OBI column ('smoothed' or 'zscore')

        Returns:
            str: The resolved OBI column name

        Raises:
            KeyError: If no suitable OBI column is found
        """
        available_columns = list(signals.keys())

        if column_type == 'smoothed':
            # Try to find obi_smoothed_X column
            candidates = [f"obi_smoothed_{self.depth_levels}"]
            # Also try with different depths as fallback
            for depth in [5, 10, 20]:
                if depth != self.depth_levels:
                    candidates.append(f"obi_smoothed_{depth}")
        elif column_type == 'zscore':
            # Try to find obi_zscore_X column
            candidates = [f"obi_zscore_{self.depth_levels}"]
            # Also try with different depths as fallback
            for depth in [5, 10, 20]:
                if depth != self.depth_levels:
                    candidates.append(f"obi_zscore_{depth}")
        else:
            raise ValueError(f"Unknown OBI column type: {column_type}")

        # Find the first available candidate
        for candidate in candidates:
            if candidate in available_columns:
                if candidate != f"obi_{column_type}_{self.depth_levels}":
                    self.logger.debug(f"Using fallback OBI column: {candidate} (expected: obi_{column_type}_{self.depth_levels})")
                return candidate

        # If no candidate found, raise descriptive error
        available_obi_cols = [c for c in available_columns if 'obi' in c.lower()]
        raise KeyError(
            f"No {column_type} OBI column found for depth {self.depth_levels}. "
            f"Tried: {candidates}. Available OBI columns: {available_obi_cols}"
        )

    @property
    def required_signals(self) -> list[str]:
        """Returns the list of signals needed by the continuous GMS detector."""
        # Check if we're in legacy granular_microstructure mode
        detector_type = getattr(self.cfg_gms, 'detector_type', 'continuous_gms')

        # Base signals always needed
        signals_needed = [
            'timestamp',
            self.ATR_PCT_COL,   # Volatility (from 1-hour ATR)
            'ma_slope',         # Momentum
            # Note: OBI columns will be resolved dynamically at runtime
            # Only include the primary depth level in required signals
            f'obi_smoothed_{self.depth_levels}',
            'spread_mean',      # Base Spread Context (Range)
            'spread_std'        # Base Spread Context (Chop)
        ]

        # Add Z-score OBI signals if needed (only primary depth)
        if self.obi_zscore_threshold is not None and self.obi_zscore_threshold > 0:
            signals_needed.append(f'obi_zscore_{self.depth_levels}')

        # Add legacy ATR signals for backward compatibility
        signals_needed.append('atr_percent')

        # Add optional signals based on config
        if self.use_adx_confirmation:
            signals_needed.append('adx')
        if self.use_funding_confirmation:
            signals_needed.append('funding_rate')

        # Add signals for conditional logic based on whether the feature is active
        if self.adaptive_obi_base is not None and self.adaptive_obi_base > 0:
            signals_needed.extend(['vol_short_term', 'vol_long_term']) # Need volatility components for adaptive OBI

        if self.depth_slope_thin_limit is not None and self.depth_slope_thin_limit > -np.inf:
            signals_needed.append('depth_slope') # Placeholder signal if depth slope check is active
        if self.depth_skew_thresh is not None and self.depth_skew_thresh > -np.inf:
            signals_needed.append('depth_skew') # Approximated signal if depth skew check is active

        # Check the gate value loaded in __init__ which handles None -> 0
        if self.spread_percentile_gate is not None and self.spread_percentile_gate > 0: # Check > 0 as gate is 0-100
            signals_needed.append('raw_spread_percentile') # Need the percentile rank signal if percentile gate is active

        # Check the lookback value loaded in __init__
        if self.spread_trend_lookback is not None and self.spread_trend_lookback > 0:
            signals_needed.append('spread_trend') # Need the trend signal if spread trend is active

        # Add spread_mean_pctile if adaptive tight spread fallback is enabled
        if self.tight_spread_fallback_percentile is not None and self.tight_spread_fallback_percentile > 0:
            signals_needed.append('spread_mean_pctile') # This is the FALLBACK percentile

        # Add PRIMARY spread percentiles if their modes are 'percentile'
        if self.spread_mean_mode == 'percentile':
            signals_needed.append('spread_mean_primary_pctile')
        if self.spread_std_mode == 'percentile':
            signals_needed.append('spread_std_primary_pctile')

        # Add volatility percentile if mode is 'percentile'
        if self.vol_thresh_mode == 'percentile':
            signals_needed.append('atr_percent_pctile')

        # Add signals for risk suppression calculation - only for continuous_gms mode
        signals_needed.append('close')
        signals_needed.append(self.ATR_COL)  # Use the 1-hour ATR column
        signals_needed.append('atr')  # Keep legacy ATR for backward compatibility

        # Only add unrealised_pnl for continuous_gms mode
        if detector_type != 'granular_microstructure':
            signals_needed.append('unrealised_pnl')

        return list(set(signals_needed)) # Return unique list

    def update(self, signals_row: dict) -> None:
        """
        Update the detector with new data. This method should be called each minute
        with the latest signals.

        Args:
            signals_row: A dictionary containing the latest calculated signals and indicators.
        """
        current_time = time.time()
        detector_type = getattr(self.cfg_gms, 'detector_type', 'continuous_gms')

        # Check if ATR columns are NaN - if so, return None without warning spam
        if detector_type == 'continuous_gms':
            atr_value = signals_row.get(self.ATR_COL)
            atr_pct_value = signals_row.get(self.ATR_PCT_COL)

            if pd.isna(atr_value) or pd.isna(atr_pct_value):
                # Don't spam logs, just return silently
                return None

        # For granular_microstructure mode, always update regardless of cadence
        # For continuous_gms mode, check cadence
        if detector_type == 'continuous_gms' and current_time - self.last_update_ts < self.cadence_sec:
            return

        # Update the last update timestamp
        self.last_update_ts = current_time

        # Process the signals and update the current state
        try:
            # Determine the new state
            new_state = self._determine_state(signals_row)

            # Update state tracking
            if new_state != self.current_state:
                self.last_state_change_ts = current_time
                self.logger.info(f"GMS state changed from '{self.current_state}' to '{new_state}'")
                
                # Update regime tracking (Phase 2)
                self.previous_state = self.current_state
                self.regime_start_time = current_time
                self.regime_transition_count += 1
                
                # Calculate initial confidence based on transition
                self._update_regime_confidence(new_state, signals_row)

            self.current_state = new_state

            # Calculate risk suppression flag
            self.risk_suppressed = self._calculate_risk_suppressed(signals_row)

            # Log snapshot age once per hour (every 3600 seconds)
            timestamp = signals_row.get('timestamp')
            if timestamp is not None:
                # Calculate age in seconds
                if isinstance(timestamp, (int, float)):
                    timestamp = datetime.fromtimestamp(timestamp)

                current_datetime = datetime.now()

                # Ensure both timestamps have the same timezone
                try:
                    age_sec = (current_datetime - timestamp).total_seconds()
                except TypeError as e:
                    self.logger.warning(f"Timezone error calculating age: {e}. Using 0 as age.")
                    age_sec = 0

                # Log once per hour (using integer division to check if the hour has changed)
                hour_key = int(current_time / 3600)
                last_hour_key = getattr(self, '_last_hour_logged', None)

                if last_hour_key is None or hour_key != last_hour_key:
                    self._last_hour_logged = hour_key
                    self.logger.debug(f"GMS age {age_sec:.1f}s  state={self.current_state}")

        except Exception as e:
            self.logger.error(f"Error updating GMS state: {e}", exc_info=True)

    def _determine_state(self, signals: dict) -> str:
        """
        Determines the market regime based on the granular microstructure logic,
        incorporating conditional checks, adaptive tight spread fallback, and confirmation bars.

        All output values are standardized according to the state_mapping.py utility module,
        ensuring consistency across the codebase.

        Returns:
            str: One of the standardized state values defined in state_mapping.py
        """
        if not self.cfg_regime.use_filter:
            return "Filter_Off"

        # Check if we're in legacy granular_microstructure mode
        detector_type = getattr(self.cfg_gms, 'detector_type', 'continuous_gms')

        # --- Retrieve Signals Safely & Check NaNs ---
        # Check only for NaNs in signals that are *actually required* by the current configuration
        # Use ma_slope_ema_30s when auto_thresholds is enabled, fallback to legacy ma_slope
        momentum_col = 'ma_slope_ema_30s' if self.adaptive_mom_threshold is not None else 'ma_slope'
        required_now = ['timestamp', 'atr_percent', momentum_col, f'obi_smoothed_{self.depth_levels}', 'spread_mean', 'spread_std'] # Base requirements
        if self.use_adx_confirmation:
            required_now.append('adx')
        if self.use_funding_confirmation:
            required_now.append('funding_rate')
        if self.adaptive_obi_base is not None and self.adaptive_obi_base > 0:
            required_now.extend(['vol_short_term', 'vol_long_term'])
        if self.obi_zscore_threshold is not None and self.obi_zscore_threshold > 0:
            required_now.append(f'obi_zscore_{self.depth_levels}')
        if self.depth_slope_thin_limit is not None and self.depth_slope_thin_limit > -np.inf:
            required_now.append('depth_slope') # Placeholder, might be NaN
        if self.depth_skew_thresh is not None and self.depth_skew_thresh > -np.inf:
            required_now.append('depth_skew') # Placeholder, might be NaN

        # Use the parameters loaded in __init__ for checks
        if self.spread_percentile_gate is not None and self.spread_percentile_gate > 0:
            required_now.append('raw_spread_percentile')
        if self.spread_trend_lookback is not None and self.spread_trend_lookback > 0:
            required_now.append('spread_trend')

        # Add primary percentile signals if needed
        if self.spread_mean_mode == 'percentile':
            required_now.append('spread_mean_primary_pctile')
        if self.spread_std_mode == 'percentile':
            required_now.append('spread_std_primary_pctile')

        # Add fallback percentile if needed
        if self.tight_spread_fallback_percentile is not None and self.tight_spread_fallback_percentile > 0:
            required_now.append('spread_mean_pctile')

        # For granular_microstructure mode, we don't need to check for unrealised_pnl
        if detector_type == 'granular_microstructure' and 'unrealised_pnl' in required_now:
            required_now.remove('unrealised_pnl')

        # Check for missing or NaN values in the currently required signals
        missing_or_nan = []
        for sig in set(required_now): # Use set to avoid duplicate checks
            if sig == 'timestamp':
                continue # Ignore timestamp for NaN check
            value = signals.get(sig, np.nan) # Use np.nan as default for missing keys
            # Special handling for placeholders: Allow NaN if the signal is depth_slope or depth_skew
            if sig in ['depth_slope', 'depth_skew'] and pd.isna(value):
                continue # Allow NaN for placeholders for now
            # Allow NaN for spread_mean_pctile if the feature is disabled (it shouldn't be in required_signals then, but double-check)
            if sig == 'spread_mean_pctile' and (self.tight_spread_fallback_percentile is None or self.tight_spread_fallback_percentile <= 0):
                 continue # Allow NaN if feature disabled
            # Special handling for ATR: We expect it to be NaN for the first 13 bars
            # The strategy layer will handle this by raising SkipSignal
            if sig in ['atr', 'atr_percent', self.ATR_COL, self.ATR_PCT_COL] and pd.isna(value):
                continue # Allow NaN for ATR during warmup
            if pd.isna(value):
                missing_or_nan.append(sig)

        # For granular_microstructure mode, we're more lenient with missing signals
        if missing_or_nan and detector_type != 'granular_microstructure':
            self.logger.warning(f"GMS Cannot determine regime: Missing/NaN required signals {sorted(missing_or_nan)} for active config.")
            # Reset confirmation state if we can't determine regime
            self.previous_regime = None
            self.confirmation_counter = 0
            return "Unknown"
        elif missing_or_nan and detector_type == 'granular_microstructure':
            # For granular_microstructure, we log the warning but continue with the calculation
            self.logger.warning(f"GMS Missing signals {sorted(missing_or_nan)} but continuing in granular_microstructure mode.")
            # We don't reset confirmation state in granular_microstructure mode

        # Extract core signals
        # Try to get ATR from the 1-hour ATR column first, fall back to legacy ATR if needed
        atr_pct = signals.get(self.ATR_PCT_COL, np.nan)
        if pd.isna(atr_pct):
            # Fall back to legacy ATR
            atr_pct = signals.get('atr_percent', np.nan)
            self.logger.debug(f"Using legacy atr_percent={atr_pct} (1-hour ATR not available)")

        # Use ma_slope_ema_30s when auto_thresholds is enabled, fallback to legacy ma_slope
        if self.adaptive_mom_threshold is not None:
            # auto_thresholds=True: use the new EMA-based momentum calculation
            ma_slope = signals.get('ma_slope_ema_30s', np.nan)
            if pd.isna(ma_slope):
                # Fallback to legacy ma_slope if new column is missing
                ma_slope = signals.get('ma_slope', np.nan)
                self.logger.debug("ma_slope_ema_30s not available, falling back to legacy ma_slope")
        else:
            # auto_thresholds=False: use legacy ma_slope
            ma_slope = signals.get('ma_slope', np.nan)

        # Dynamically resolve OBI columns
        try:
            obi_smooth_col = self._resolve_obi_column(signals, 'smoothed')
            obi_smooth = signals.get(obi_smooth_col, np.nan)
        except KeyError as e:
            self.logger.warning(f"Could not resolve smoothed OBI column: {e}")
            obi_smooth = np.nan

        spread_std = signals.get('spread_std', np.nan)
        spread_mean = signals.get('spread_mean', np.nan)

        # Extract optional signals
        adx = signals.get('adx', np.nan)
        funding_rate = signals.get('funding_rate', np.nan)

        # Dynamically resolve Z-score OBI column if needed
        raw_obi_zscore = np.nan
        if self.obi_zscore_threshold is not None and self.obi_zscore_threshold > 0:
            try:
                obi_zscore_col = self._resolve_obi_column(signals, 'zscore')
                raw_obi_zscore = signals.get(obi_zscore_col, np.nan)
            except KeyError as e:
                self.logger.debug(f"Could not resolve Z-score OBI column: {e}")
                raw_obi_zscore = np.nan
        raw_spread_percentile = signals.get('raw_spread_percentile', np.nan) # Percentile rank of current spread
        spread_trend = signals.get('spread_trend', np.nan)
        depth_slope = signals.get('depth_slope', np.nan) # Approximated
        depth_skew = signals.get('depth_skew', np.nan)   # Approximated
        vol_short_term = signals.get('vol_short_term', np.nan)
        vol_long_term = signals.get('vol_long_term', np.nan)

        # --- Determine OBI Confirmation (Prioritized Logic) ---
        obi_condition = 'NONE' # Default: No confirmation
        obi_logic_used = "N/A"
        try:
            # Priority 1: Adaptive OBI
            if self.adaptive_obi_base is not None and self.adaptive_obi_base > 0:
                obi_logic_used = "Adaptive"
                # We already checked for NaNs in vol_short_term, vol_long_term if this path is active
                if vol_long_term > 1e-9: # Avoid division by zero or near-zero
                    vol_ratio = vol_short_term / vol_long_term
                    if vol_ratio >= 0: # Ensure ratio is non-negative before sqrt
                        adaptive_thresh = self.adaptive_obi_base * np.sqrt(vol_ratio)
                        if obi_smooth > adaptive_thresh:
                            obi_condition = 'STRONG' # Adaptive uses one threshold for now
                        elif obi_smooth < -adaptive_thresh:
                            obi_condition = 'STRONG' # Symmetric for now
                        # No 'WEAK' state defined for adaptive yet
                    else:
                        self.logger.warning(f"Negative volatility ratio ({vol_ratio:.2f}) for adaptive OBI. Skipping.")
                else:
                     self.logger.warning(f"Near-zero long-term volatility ({vol_long_term:.4f}) for adaptive OBI. Skipping.")

            # Priority 2: Z-Score OBI
            elif self.obi_zscore_threshold is not None and self.obi_zscore_threshold > 0:
                obi_logic_used = "Z-Score"
                # We already checked for NaNs in raw_obi_zscore if this path is active
                try:
                    # Add safety checks for raw_obi_zscore
                    if pd.isna(raw_obi_zscore) or not np.isfinite(raw_obi_zscore):
                        # Fall back to regular OBI logic
                        obi_logic_used = "Fallback Threshold"
                        if obi_smooth > self.obi_strong_confirm_thresh: obi_condition = 'STRONG'
                        elif obi_smooth > self.obi_weak_confirm_thresh: obi_condition = 'WEAK'
                        elif obi_smooth < -self.obi_strong_confirm_thresh: obi_condition = 'STRONG'
                        elif obi_smooth < -self.obi_weak_confirm_thresh: obi_condition = 'WEAK'
                    elif abs(raw_obi_zscore) >= self.obi_zscore_threshold:
                        obi_condition = 'STRONG' # Z-score uses one threshold for strong confirmation
                    # No 'WEAK' state defined for Z-score yet
                except Exception as e:
                    # Fall back to regular OBI logic
                    self.logger.warning(f"Z-Score OBI processing error: {e}. Falling back to threshold-based OBI.")
                    obi_logic_used = "Fallback Threshold"
                    if obi_smooth > self.obi_strong_confirm_thresh: obi_condition = 'STRONG'
                    elif obi_smooth > self.obi_weak_confirm_thresh: obi_condition = 'WEAK'
                    elif obi_smooth < -self.obi_strong_confirm_thresh: obi_condition = 'STRONG'
                    elif obi_smooth < -self.obi_weak_confirm_thresh: obi_condition = 'WEAK'

            # Priority 3: Fallback Threshold OBI
            else:
                obi_logic_used = "Fallback Threshold"
                # We already checked for NaNs in obi_smooth
                if obi_smooth > self.obi_strong_confirm_thresh:
                    obi_condition = 'STRONG'
                elif obi_smooth > self.obi_weak_confirm_thresh:
                    obi_condition = 'WEAK'
                elif obi_smooth < -self.obi_strong_confirm_thresh:
                    obi_condition = 'STRONG'
                elif obi_smooth < -self.obi_weak_confirm_thresh:
                    obi_condition = 'WEAK'

        except Exception as e:
            self.logger.error(f"Error during OBI condition check (using {obi_logic_used}): {e}", exc_info=True)
            obi_condition = 'NONE' # Fallback on error

        # --- Determine Market Condition (Prioritized Logic) ---
        market_condition = 'NORMAL' # Default
        market_logic_used = "N/A"
        try:
            # Priority 1: Depth Slope (Thin Market) - Use the limit loaded in __init__
            if self.depth_slope_thin_limit is not None and self.depth_slope_thin_limit > -np.inf:
                 market_logic_used = "Depth Slope"
                 # NaN check already done if this path is active, but depth_slope might still be NaN (allowed placeholder)
                 if not pd.isna(depth_slope):
                      # Log the comparison (DEBUG log commented out after verification)
                      is_thin = depth_slope < self.depth_slope_thin_limit
                      if is_thin:
                           market_condition = 'THIN_LIQUIDITY'
                 # else: self.logger.debug("Depth Slope is NaN (Placeholder).")

            # Priority 2: Depth Skew (Placeholder) - Only check if not already THIN
            if market_condition == 'NORMAL' and self.depth_skew_thresh is not None and self.depth_skew_thresh > -np.inf:
                 market_logic_used = "Depth Skew"
                 # NaN check already done if this path is active, but depth_skew might still be NaN (allowed placeholder)
                 if not pd.isna(depth_skew):
                      # Placeholder: Add actual logic using self.depth_skew_thresh if needed
                      # Example: if abs(depth_skew) > self.depth_skew_thresh: market_condition = 'SKEWED_BOOK'
                      # --- Implementing Skew Logic ---
                      if abs(depth_skew) > self.depth_skew_thresh:
                           market_condition = 'SKEWED_BOOK'

            # Priority 3: Spread Percentile Gate - Only check if NORMAL so far
            if market_condition == 'NORMAL' and self.spread_percentile_gate is not None and self.spread_percentile_gate > 0:
                 market_logic_used = "Spread Percentile"
                 # NaN check already done if this path is active
                 threshold = 100 - self.spread_percentile_gate
                 if raw_spread_percentile > threshold:
                      market_condition = 'WIDE_SPREAD'

            # Priority 4: Spread Trend (Placeholder) - Only check if NORMAL so far
            if market_condition == 'NORMAL' and self.spread_trend_lookback is not None and self.spread_trend_lookback > 0:
                 market_logic_used = "Spread Trend"
                 # NaN check already done if this path is active
                 # Placeholder: Add actual logic using spread_trend if needed
                 # Example: if spread_trend > some_threshold: market_condition = 'TRENDING_SPREAD_UP'
                 pass

            # Priority 5: Fallback Spread Mean/Std - Only check if NORMAL so far
            if market_condition == 'NORMAL':
                 market_logic_used = "Fallback Spread"
                 # NaN checks already done for spread_std and spread_mean
                 if spread_std >= self.spread_std_high_thresh:
                      market_condition = 'CHOPPY' # High spread volatility indicates chop
                 elif spread_mean <= self.spread_mean_low_thresh:
                      # Tight spreads might indicate Low Vol Range, but let Volatility check handle that state primarily
                      pass # Don't override to 'TIGHT' here, let Vol check decide Low_Vol_Range

        except Exception as e:
            self.logger.error(f"Error during Market condition check (using {market_logic_used}): {e}", exc_info=True)
            market_condition = 'NORMAL' # Fallback on error

        # --- Determine Potential Regime based on Vol, Mom, OBI, Market Cond ---
        potential_regime = GMS_STATE_UNCERTAIN # Default

        # 1. Check Volatility Ranges First (Can override momentum checks)

        # --- High Vol Range Spread Check ---
        is_spread_std_high = False
        if self.spread_std_mode == 'percentile':
            spread_std_pctile = signals.get('spread_std_primary_pctile', np.nan)
            if not pd.isna(spread_std_pctile): # Ensure pandas (pd) is imported
                is_spread_std_high = spread_std_pctile >= self.spread_std_high_pctile
        else: # 'fixed' mode
            # spread_std already retrieved and NaN checked earlier
            if not pd.isna(spread_std):
                is_spread_std_high = spread_std >= self.spread_std_high_thresh
        # --- End High Vol Range Spread Check ---

        # --- Volatility Check using adaptive, percentile, or fixed thresholds ---
        is_vol_high = False
        is_vol_low = False

        if self.adaptive_vol_threshold is not None:
            # Use adaptive thresholds (Task R-112k)
            vol_low_thresh, vol_high_thresh = self.adaptive_vol_threshold.update(atr_pct)

            if vol_low_thresh is not None and vol_high_thresh is not None:
                # Check if we have sufficient history
                buffer_size = self.adaptive_vol_threshold.get_buffer_size()
                if buffer_size >= self.min_history_rows:
                    is_vol_high = atr_pct >= vol_high_thresh
                    is_vol_low = atr_pct <= vol_low_thresh
                    self.logger.debug(f"Adaptive Vol Thresholds: {vol_low_thresh:.4f} / {vol_high_thresh:.4f} (buffer: {buffer_size})")
                else:
                    # Fallback to static thresholds during warm-up
                    is_vol_high = atr_pct >= self.vol_high_thresh
                    is_vol_low = atr_pct <= self.vol_low_thresh
                    self.logger.debug(f"Adaptive Vol Warm-up: Using static thresholds (buffer: {buffer_size} < {self.min_history_rows})")
            else:
                # Fallback to static thresholds if adaptive calculation fails
                is_vol_high = atr_pct >= self.vol_high_thresh
                is_vol_low = atr_pct <= self.vol_low_thresh
                self.logger.debug("Adaptive Vol: Using static fallback (adaptive calculation returned None)")

        elif self.vol_thresh_mode == 'percentile':
            # Use legacy percentile-based thresholds (only when auto_thresholds is false)
            atr_pct_pctile = signals.get('atr_percent_pctile', np.nan)
            if not pd.isna(atr_pct_pctile):
                vol_high_pct = getattr(self.cfg_regime, 'gms_vol_high_percentile', 75) / 100.0
                vol_low_pct = getattr(self.cfg_regime, 'gms_vol_low_percentile', 25) / 100.0
                is_vol_high = atr_pct_pctile >= vol_high_pct
                is_vol_low = atr_pct_pctile <= vol_low_pct
        else:
            # Use fixed thresholds (original behavior, only when auto_thresholds is false)
            is_vol_high = atr_pct >= self.vol_high_thresh
            is_vol_low = atr_pct <= self.vol_low_thresh
        # --- End Volatility Check ---

        # --- Determine adaptive momentum thresholds if enabled ---
        mom_weak_thresh_current = self.mom_weak_thresh
        mom_strong_thresh_current = self.mom_strong_thresh

        if self.adaptive_mom_threshold is not None:
            # Use adaptive momentum thresholds (Task R-112k)
            mom_low_thresh, mom_high_thresh = self.adaptive_mom_threshold.update(abs(ma_slope))

            if mom_low_thresh is not None and mom_high_thresh is not None:
                # Check if we have sufficient history
                buffer_size = self.adaptive_mom_threshold.get_buffer_size()
                if buffer_size >= self.min_history_rows:
                    mom_weak_thresh_current = mom_low_thresh
                    mom_strong_thresh_current = mom_high_thresh
                    self.logger.debug(f"Adaptive Mom Thresholds: {mom_low_thresh:.4f} / {mom_high_thresh:.4f} (buffer: {buffer_size})")
                else:
                    self.logger.debug(f"Adaptive Mom Warm-up: Using static thresholds (buffer: {buffer_size} < {self.min_history_rows})")
            else:
                self.logger.debug("Adaptive Mom: Using static fallback (adaptive calculation returned None)")

        # Check High Volatility condition
        if is_vol_high:
            # High Volatility: Check if momentum is weak OR spread std is high
            if abs(ma_slope) < mom_weak_thresh_current or is_spread_std_high:
                 potential_regime = GMS_STATE_HIGH_VOL_RANGE
            # Else (High Vol + Momentum + Not Choppy/High Spread) -> Let momentum logic decide Trend below

        # Check Low Volatility condition
        elif is_vol_low:
            # Low Volatility: Check if momentum is weak AND spread mean is low AND market is not wide spread
            # --- Low Vol Range Spread Check ---
            is_spread_mean_low = False
            if self.spread_mean_mode == 'percentile':
                spread_mean_pctile = signals.get('spread_mean_primary_pctile', np.nan)
                if not pd.isna(spread_mean_pctile): # Ensure pandas (pd) is imported
                    is_spread_mean_low = spread_mean_pctile < self.spread_mean_low_pctile
            else: # 'fixed' mode
                # spread_mean already retrieved and NaN checked earlier
                if not pd.isna(spread_mean):
                    is_spread_mean_low = spread_mean <= self.spread_mean_low_thresh
            # --- End Low Vol Range Spread Check ---

            if abs(ma_slope) < mom_weak_thresh_current and is_spread_mean_low and market_condition != 'WIDE_SPREAD':
                 potential_regime = GMS_STATE_LOW_VOL_RANGE
            # Else (Low Vol + Momentum or Wide Spread or High Spread Mean) -> Let momentum logic decide Weak Trend or Uncertain below

        # 2. Check Momentum (if not already assigned a Range regime)
        if potential_regime == GMS_STATE_UNCERTAIN:
            is_bullish = ma_slope > 0
            is_strong_mom = abs(ma_slope) >= mom_strong_thresh_current
            is_weak_mom = abs(ma_slope) >= mom_weak_thresh_current # Includes strong

            # Optional confirmations (NaN checks already done if these are required)
            adx_confirms = (not self.use_adx_confirmation) or (adx >= self.adx_threshold)
            funding_confirms = (not self.use_funding_confirmation) or \
                               (is_bullish and funding_rate < self.funding_extreme_pos_thresh) or \
                               (not is_bullish and funding_rate > self.funding_extreme_neg_thresh)

            if is_strong_mom:
                # Strong Momentum: Requires STRONG OBI and confirmations for Strong Trend
                if is_bullish and obi_condition == 'STRONG' and adx_confirms and funding_confirms:
                    potential_regime = GMS_STATE_STRONG_BULL_TREND
                elif not is_bullish and obi_condition == 'STRONG' and adx_confirms and funding_confirms:
                    potential_regime = GMS_STATE_STRONG_BEAR_TREND
                # Downgrade to Weak Trend if strong mom but OBI/confirmations don't fully align
                elif is_bullish and obi_condition in ['STRONG', 'WEAK']:
                    potential_regime = GMS_STATE_WEAK_BULL_TREND
                elif not is_bullish and obi_condition in ['STRONG', 'WEAK']:
                    potential_regime = "Weak_Bear_Trend"
                # else: remains Uncertain if strong mom but no OBI alignment

            elif is_weak_mom: # Weak but not Strong momentum
                # Weak Momentum: Requires at least WEAK OBI for Weak Trend
                if is_bullish and obi_condition in ['STRONG', 'WEAK']:
                    potential_regime = "Weak_Bull_Trend"
                elif not is_bullish and obi_condition in ['STRONG', 'WEAK']:
                    potential_regime = "Weak_Bear_Trend"
                # else: remains Uncertain if weak mom but no OBI alignment

            # else: remains Uncertain if momentum is negligible (abs(ma_slope) < self.mom_weak_thresh)

        # --- Final Fallback Logic (Check for Tight Spread Override) ---
        # This happens if primary logic resulted in "Uncertain"
        final_potential_regime = potential_regime # Start with the result from above

        if final_potential_regime == "Uncertain":
            # Check for Tight Spread Fallback Override (only if enabled)
            # Use the value loaded in __init__
            if self.tight_spread_fallback_percentile is not None and self.tight_spread_fallback_percentile > 0:
                spread_pctile = signals.get('spread_mean_pctile') # Signal was added conditionally
                # Check if percentile signal is valid (not NaN)
                if spread_pctile is not None and not pd.isna(spread_pctile):
                    if spread_pctile < self.tight_spread_fallback_percentile:
                        self.logger.info(f"GMS Fallback Override: Spread Mean Pctile ({spread_pctile:.3f}) < Thresh ({self.tight_spread_fallback_percentile:.3f}). Overriding 'Uncertain' with 'TIGHT_SPREAD'.")
                        final_potential_regime = "TIGHT_SPREAD" # Override state
                    # Else: Spread is not below percentile threshold, keep 'Uncertain'
                else:
                    # This case should be rare if required_signals logic is correct and SignalEngine adds NaN column when disabled
                    self.logger.warning(f"Tight Spread Fallback check failed: spread_mean_pctile signal is missing or NaN (Value: {spread_pctile}). Keeping 'Uncertain'.")
                    # Keep 'Uncertain' if percentile is missing/NaN

        # Log the factors leading to the potential regime determination (including potential override)
        # Use current thresholds (adaptive if enabled, static otherwise)
        vol_low_display = self.vol_low_thresh
        vol_high_display = self.vol_high_thresh
        if self.adaptive_vol_threshold is not None:
            vol_low_thresh, vol_high_thresh = self.adaptive_vol_threshold.update(atr_pct)
            if vol_low_thresh is not None and vol_high_thresh is not None:
                buffer_size = self.adaptive_vol_threshold.get_buffer_size()
                if buffer_size >= self.min_history_rows:
                    vol_low_display = vol_low_thresh
                    vol_high_display = vol_high_thresh

        self.logger.info(
            f"GMS Factors @ {signals.get('timestamp', 'N/A')}: "
            f"Vol={atr_pct:.4f} (L:{vol_low_display:.4f}/H:{vol_high_display:.4f}), "
            f"Mom={ma_slope:.2f} (W:{mom_weak_thresh_current:.2f}/S:{mom_strong_thresh_current:.2f}), "
            f"OBI='{obi_condition}' (Logic:{obi_logic_used}), "
            f"Mkt='{market_condition}' (Logic:{market_logic_used}) "
            f"=> Initial Potential='{potential_regime}', Final Potential='{final_potential_regime}'" # Log both
        )

        # --- Apply Confirmation Bars Logic (if enabled) ---
        # Use the potentially overridden state (final_potential_regime) for confirmation
        potential_regime_for_confirm = final_potential_regime
        final_regime = potential_regime_for_confirm # Default if no confirmation needed

        if self.confirmation_bars > 0:
            # Confirmation is enabled
            if potential_regime_for_confirm == self.previous_regime:
                # Regime is the same as the last potential one, increment counter
                self.confirmation_counter += 1
            else:
                # Regime changed
                self.previous_regime = potential_regime_for_confirm # Update previous regime regardless
                self.confirmation_counter = 1 # Reset counter to 1

                if self.confirmation_bars == 1:
                    # If confirmation_bars is 1, confirm and return immediately
                    final_regime = potential_regime_for_confirm
                    self.logger.info(f"New regime '{final_regime}' detected and CONFIRMED immediately (confirmation_bars=1).")
                    return final_regime # Return the confirmed regime now
                else:
                    # If confirmation_bars > 1, return Uncertain for the first bar
                    final_regime = "Uncertain"
                    self.logger.info(f"New regime detected: '{potential_regime_for_confirm}'. Counter reset to 1/{self.confirmation_bars}. Returning 'Uncertain' for this bar.")
                    return final_regime # Return Uncertain only if N > 1

            # Check if the counter meets the threshold
            if self.confirmation_counter >= self.confirmation_bars:
                # Confirmed! Final regime is the potential regime
                final_regime = potential_regime_for_confirm # Use the potentially overridden state
                self.logger.info(f"Regime '{final_regime}' CONFIRMED (Counter: {self.confirmation_counter}/{self.confirmation_bars})")
            else:
                # Not confirmed yet. Return "Uncertain"
                final_regime = "Uncertain"
                self.logger.info(f"Regime UNCONFIRMED (Potential: '{potential_regime_for_confirm}', Counter: {self.confirmation_counter}/{self.confirmation_bars}) -> Uncertain")
        # else: Confirmation disabled, final_regime remains potential_regime_for_confirm

        # --- PHASE 4: VALIDATE FINAL OUTPUT STATE ---
        valid_output_states = get_valid_gms_states()  # Get all valid GMS states including special states

        if final_regime not in valid_output_states:
            self.logger.error(
                f"GMS Detector produced an invalid/unknown state string: '{final_regime}'. "
                f"This indicates an issue in the internal GMS logic or a missing constant definition. "
                f"Falling back to '{GMS_STATE_UNKNOWN}'."
            )
            final_regime = GMS_STATE_UNKNOWN  # Use the constant for unknown state

        return final_regime

    def _calculate_risk_suppressed(self, signals: dict) -> bool:
        """
        Calculate the risk_suppressed flag based on notional value and unrealized PnL.

        Args:
            signals: A dictionary containing the latest calculated signals and indicators.

        Returns:
            bool: True if risk should be suppressed, False otherwise.
        """
        # Check if we're in legacy granular_microstructure mode
        detector_type = getattr(self.cfg_gms, 'detector_type', 'continuous_gms')
        if detector_type == 'granular_microstructure':
            # In legacy mode, don't suppress risk
            return False

        # Check if risk suppression is disabled
        if hasattr(self, 'disable_risk_suppression') and self.disable_risk_suppression:
            return False

        # Extract required signals with safe conversion to float
        try:
            close = float(signals.get('close', 0.0))
        except (TypeError, ValueError):
            close = 0.0

        try:
            # Handle the case where unrealised_pnl might not be present
            if 'unrealised_pnl' not in signals:
                unrealised_pnl = 0.0
            else:
                unrealised_pnl = float(signals.get('unrealised_pnl', 0.0))
        except (TypeError, ValueError):
            unrealised_pnl = 0.0

        try:
            # Try to get ATR from the 1-hour ATR column first, fall back to legacy ATR if needed
            atr = float(signals.get(self.ATR_COL, np.nan))
            if pd.isna(atr):
                # Fall back to legacy ATR
                atr = float(signals.get('atr', 0.0))
                self.logger.debug(f"Using legacy atr={atr} for risk suppression (1-hour ATR not available)")
        except (TypeError, ValueError):
            atr = 0.0

        # Check if we have valid values
        if close <= 0 or pd.isna(close):
            self.logger.warning("Invalid close price for risk_suppressed calculation")
            return False

        # Calculate notional threshold
        if self.cfg_portfolio:
            notional_threshold = (
                self.cfg_portfolio.initial_balance *
                self.cfg_portfolio.max_leverage *
                self.risk_suppressed_notional_frac
            )
        else:
            # Fallback values if portfolio config is not available
            notional_threshold = 10000.0 * 10.0 * self.risk_suppressed_notional_frac

        # Calculate PnL threshold
        pnl_threshold = atr * self.risk_suppressed_pnl_atr_mult

        # Check conditions
        notional_exceeded = close >= notional_threshold
        pnl_exceeded = False  # Default to False if unrealised_pnl is not available

        # Only check PnL if we have a valid value
        if unrealised_pnl != 0.0 and not pd.isna(unrealised_pnl):
            pnl_exceeded = abs(unrealised_pnl) >= pnl_threshold

        # Risk is suppressed if either condition is met
        risk_suppressed = notional_exceeded or pnl_exceeded

        if risk_suppressed:
            reason = []
            if notional_exceeded:
                reason.append(f"notional ({close:.2f}) >= threshold ({notional_threshold:.2f})")
            if pnl_exceeded:
                reason.append(f"unrealised_pnl ({unrealised_pnl:.2f}) >= ATR*mult ({pnl_threshold:.2f})")
            self.logger.info(f"Risk suppressed: {', '.join(reason)}")

        return risk_suppressed
    
    def _update_regime_confidence(self, new_state: str, signals: Dict) -> None:
        """
        Update regime confidence based on state transition and market conditions.
        
        Args:
            new_state: The new regime state
            signals: Current market signals
        """
        # Base confidence on transition type
        if self.previous_state == GMS_STATE_UNCERTAIN or self.previous_state == GMS_STATE_UNKNOWN:
            # Transitioning from uncertain state - moderate confidence
            self.regime_confidence = 0.6
        elif self.previous_state == new_state:
            # Same state (shouldn't happen, but just in case)
            self.regime_confidence = min(1.0, self.regime_confidence + 0.1)
        else:
            # State change - confidence based on transition
            transition_key = f"{self.previous_state}->{new_state}"
            
            # Define confidence for different transitions
            transition_confidence = {
                # Natural progressions have higher confidence
                f"{GMS_STATE_WEAK_BULL_TREND}->{GMS_STATE_STRONG_BULL_TREND}": 0.9,
                f"{GMS_STATE_STRONG_BULL_TREND}->{GMS_STATE_WEAK_BULL_TREND}": 0.8,
                f"{GMS_STATE_WEAK_BEAR_TREND}->{GMS_STATE_STRONG_BEAR_TREND}": 0.9,
                f"{GMS_STATE_STRONG_BEAR_TREND}->{GMS_STATE_WEAK_BEAR_TREND}": 0.8,
                
                # Trend reversals have lower confidence
                f"{GMS_STATE_STRONG_BULL_TREND}->{GMS_STATE_STRONG_BEAR_TREND}": 0.5,
                f"{GMS_STATE_STRONG_BEAR_TREND}->{GMS_STATE_STRONG_BULL_TREND}": 0.5,
                
                # Transitions to/from range states
                f"{GMS_STATE_HIGH_VOL_RANGE}->{GMS_STATE_LOW_VOL_RANGE}": 0.7,
                f"{GMS_STATE_LOW_VOL_RANGE}->{GMS_STATE_HIGH_VOL_RANGE}": 0.7,
            }
            
            # Get confidence for this transition, default to 0.6
            self.regime_confidence = transition_confidence.get(transition_key, 0.6)
            
        # Adjust confidence based on volatility
        atr_pct = signals.get(self.ATR_PCT_COL, 0.02)
        if atr_pct > 0.04:  # High volatility
            self.regime_confidence *= 0.8
        elif atr_pct < 0.01:  # Low volatility
            self.regime_confidence *= 0.9
            
        # Ensure confidence stays in valid range
        self.regime_confidence = max(0.3, min(1.0, self.regime_confidence))
        
        self.logger.debug(f"Regime confidence updated to {self.regime_confidence:.2f} for state {new_state}")

    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None) -> Union[str, Dict[str, Any]]:
        """
        Determines the current market regime based on the provided signals
        and potentially recent price history.

        This method is API-compatible with RegimeDetectorInterface but returns
        a dictionary with state and risk_suppressed flag instead of just a string.

        Args:
            signals: A dictionary containing the latest calculated signals and indicators.
            price_history: Optional pandas Series of recent close prices (not used by this detector).

        Returns:
            A dictionary containing:
                - "state": The detected regime state string
                - "risk_suppressed": Boolean flag indicating if risk should be suppressed
            OR
            A string containing the regime state (for legacy granular_microstructure mode)
        """
        # Check if we're in continuous_gms mode and ATR columns are NaN
        detector_type = getattr(self.cfg_gms, 'detector_type', 'continuous_gms')
        if detector_type == 'continuous_gms':
            atr_value = signals.get(self.ATR_COL)
            atr_pct_value = signals.get(self.ATR_PCT_COL)

            if pd.isna(atr_value) or pd.isna(atr_pct_value):
                # Return None without warning spam
                return {'state': 'Unknown', 'risk_suppressed': False}

        # Check for required signals (excluding unrealised_pnl which is optional)
        required_for_state = [s for s in self.required_signals if s != 'unrealised_pnl']

        # Skip ATR check if we have either the new ATR column or the legacy ATR column
        has_atr = (self.ATR_COL in signals and not pd.isna(signals.get(self.ATR_COL))) or \
                  ('atr' in signals and not pd.isna(signals.get('atr')))
        has_atr_pct = (self.ATR_PCT_COL in signals and not pd.isna(signals.get(self.ATR_PCT_COL))) or \
                      ('atr_percent' in signals and not pd.isna(signals.get('atr_percent')))

        # Filter out ATR columns if we have at least one valid ATR value
        if has_atr:
            required_for_state = [s for s in required_for_state if s != 'atr' and s != self.ATR_COL]
        if has_atr_pct:
            required_for_state = [s for s in required_for_state if s != 'atr_percent' and s != self.ATR_PCT_COL]

        missing_signals = [s for s in required_for_state if s not in signals or pd.isna(signals.get(s))]

        if missing_signals:
            self.logger.warning(f"GMS Cannot determine regime: Missing/NaN required signals {missing_signals} for active config.")
            # Return a default state
            self.current_state = GMS_STATE_UNKNOWN

            # Check if we're in legacy granular_microstructure mode
            if detector_type == 'granular_microstructure':
                return 'Unknown'  # Return string for legacy mode
            else:
                return {'state': 'Unknown', 'risk_suppressed': self.risk_suppressed}

        # Update the detector with the latest signals
        try:
            self.update(signals)
        except Exception as e:
            self.logger.error(f"Error updating GMS detector: {e}")
            self.current_state = GMS_STATE_UNKNOWN

            # Check if we're in legacy granular_microstructure mode
            detector_type = getattr(self.cfg_gms, 'detector_type', 'continuous_gms')
            if detector_type == 'granular_microstructure':
                return 'Unknown'  # Return string for legacy mode
            else:
                return {'state': 'Unknown', 'risk_suppressed': self.risk_suppressed}

        # Check if we're in legacy granular_microstructure mode
        detector_type = getattr(self.cfg_gms, 'detector_type', 'continuous_gms')
        if detector_type == 'granular_microstructure':
            return self.current_state  # Return string for legacy mode
        else:
            # For continuous_gms: use collapsed states if output_states < 8
            if self.output_states < 8:
                return self.get_collapsed_regime()
            else:
                # Return the current state with confidence and duration (Phase 2)
                regime_duration_min = (time.time() - self.regime_start_time) / 60.0
                return {
                    "state": self.current_state,
                    "risk_suppressed": self.risk_suppressed,
                    "regime_confidence": self.regime_confidence,
                    "regime_duration_minutes": regime_duration_min
                }

    def get_collapsed_regime(self, use_four_state: Optional[bool] = None) -> Dict[str, Any]:
        """
        Returns the current regime state collapsed to a simpler representation
        (3-state or 4-state) based on the mapping file.

        Args:
            use_four_state: Optional override for the use_four_state_mapping config.
                            If None, uses the value from config.

        Returns:
            A dictionary containing:
                - "state": The collapsed regime state string (BULL, BEAR, CHOP, or TSP if 4-state)
                - "risk_suppressed": Boolean flag indicating if risk should be suppressed
        """
        # Determine whether to use 4-state mapping
        use_four = use_four_state if use_four_state is not None else self.use_four_state_mapping

        # Get the current raw state
        raw_state = self.current_state

        # Calculate regime duration for all returns
        regime_duration_min = (time.time() - self.regime_start_time) / 60.0
        
        # Special states don't need mapping
        if raw_state in [GMS_STATE_UNKNOWN, "Filter_Off"]:
            return {
                "state": raw_state,
                "risk_suppressed": self.risk_suppressed,
                "regime_confidence": self.regime_confidence,
                "regime_duration_minutes": regime_duration_min
            }

        # For 4-state mapping, handle TIGHT_SPREAD specially
        if use_four and raw_state == GMS_STATE_TIGHT_SPREAD:
            return {
                "state": "TSP",  # Special 4th state for tight spread
                "risk_suppressed": self.risk_suppressed,
                "regime_confidence": self.regime_confidence,
                "regime_duration_minutes": regime_duration_min
            }

        # For all other states, use the mapping from the YAML file
        # Get the mapping with the weak_bear_to_bear setting from config
        map_weak_bear_to_bear = getattr(self.cfg_regime, 'map_weak_bear_to_bear', False)
        mapped_state = map_gms_state(raw_state, map_weak_bear_to_bear=map_weak_bear_to_bear)

        return {
            "state": mapped_state,
            "risk_suppressed": self.risk_suppressed,
            "regime_confidence": self.regime_confidence,
            "regime_duration_minutes": regime_duration_min
        }

    def _prime_adaptive_thresholds(self, priming_hours: int) -> None:
        """
        Prime adaptive threshold buffers with historical data.

        This method loads historical feature data from the specified number of hours
        before the backtest start time and feeds it to the adaptive threshold instances
        to pre-populate their rolling windows.

        Args:
            priming_hours: Number of hours of historical data to load for priming
        """
        try:
            # Get the backtest start time from config
            if hasattr(self.config, 'backtest') and hasattr(self.config.backtest, 'custom_start_date'):
                start_time = self.config.backtest.custom_start_date
            else:
                self.logger.warning("[GMS] No backtest start date in config, skipping adaptive threshold priming")
                return

            if start_time is None:
                self.logger.warning("[GMS] Backtest start date is None, skipping adaptive threshold priming")
                return

            # Calculate priming time range
            priming_start = start_time - timedelta(hours=priming_hours)
            priming_end = start_time

            self.logger.info(f"[GMS] Priming adaptive thresholds with {priming_hours} hours of data ({priming_start} to {priming_end})")

            # Load historical feature data
            feature_data = self._load_historical_features(priming_start, priming_end)

            if feature_data is None or feature_data.empty:
                self.logger.warning("[GMS] No historical feature data found for priming, continuing with empty buffers")
                return

            # Sort by timestamp to ensure chronological order
            feature_data = feature_data.sort_values('timestamp')

            # Prime the adaptive thresholds
            primed_rows = 0
            for _, row in feature_data.iterrows():
                # Get volatility metric (ATR percent)
                vol_metric = row.get(self.ATR_PCT_COL)
                if pd.notna(vol_metric):
                    self.adaptive_vol_threshold.update(vol_metric)

                # Get momentum metric (MA slope) - use new column if available, fallback to legacy
                mom_metric = row.get('ma_slope_ema_30s')
                if pd.isna(mom_metric):
                    mom_metric = row.get('ma_slope')
                if pd.notna(mom_metric):
                    self.adaptive_mom_threshold.update(abs(mom_metric))  # Use absolute value for momentum

                primed_rows += 1

            # Get current threshold values for logging
            vol_stats = self.adaptive_vol_threshold.get_buffer_stats()
            mom_stats = self.adaptive_mom_threshold.get_buffer_stats()

            self.logger.info(f"[GMS] Primed thresholds with {primed_rows} rows")
            self.logger.info(f"[GMS] Vol buffer: {vol_stats['size']} samples, range: {vol_stats.get('min', 'N/A'):.6f} - {vol_stats.get('max', 'N/A'):.6f}")
            self.logger.info(f"[GMS] Mom buffer: {mom_stats['size']} samples, range: {mom_stats.get('min', 'N/A'):.6f} - {mom_stats.get('max', 'N/A'):.6f}")

        except Exception as e:
            self.logger.error(f"[GMS] Error priming adaptive thresholds: {e}", exc_info=True)

    def _load_historical_features(self, start_time: datetime, end_time: datetime) -> Optional[pd.DataFrame]:
        """
        Load historical feature data for the specified time range.

        Args:
            start_time: Start of time range (inclusive)
            end_time: End of time range (exclusive)

        Returns:
            DataFrame with historical feature data, or None if no data found
        """
        try:
            # Get feature directory from config
            feature_dir = Path(self.config.data_paths.feature_1s_dir)
            if not feature_dir.exists():
                self.logger.warning(f"[GMS] Feature directory does not exist: {feature_dir}")
                return None

            # Collect data from all relevant dates
            all_data = []
            current_date = start_time.date()
            end_date = end_time.date()

            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')
                date_dir = feature_dir / date_str

                if date_dir.exists():
                    # Load all hourly files for this date
                    for hour_file in sorted(date_dir.glob('features_*.parquet')):
                        try:
                            # Use pyarrow for efficient column selection
                            columns_needed = ['timestamp', self.ATR_PCT_COL, 'ma_slope', 'ma_slope_ema_30s']

                            # Read only the columns we need
                            df_hour = pd.read_parquet(hour_file, columns=columns_needed)

                            if not df_hour.empty:
                                # Convert timestamp to datetime if needed
                                if 'timestamp' in df_hour.columns:
                                    if not pd.api.types.is_datetime64_any_dtype(df_hour['timestamp']):
                                        df_hour['timestamp'] = pd.to_datetime(df_hour['timestamp'])

                                    # Filter to time range
                                    mask = (df_hour['timestamp'] >= start_time) & (df_hour['timestamp'] < end_time)
                                    df_filtered = df_hour[mask]

                                    if not df_filtered.empty:
                                        all_data.append(df_filtered)

                        except Exception as e:
                            self.logger.debug(f"[GMS] Could not load {hour_file}: {e}")
                            continue

                current_date += timedelta(days=1)

            if not all_data:
                return None

            # Combine all data
            combined_df = pd.concat(all_data, ignore_index=True)

            # Sort by timestamp
            combined_df = combined_df.sort_values('timestamp')

            self.logger.debug(f"[GMS] Loaded {len(combined_df)} rows of historical feature data")
            return combined_df

        except Exception as e:
            self.logger.error(f"[GMS] Error loading historical features: {e}", exc_info=True)
            return None
