"""
Interfaces for core components.

This module defines clean interfaces following SOLID principles for
better architecture and testability.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
from datetime import datetime


class IRegimeProvider(ABC):
    """
    Interface for regime data access.
    
    This interface abstracts regime detection and historical data access,
    allowing strategies to depend on the interface rather than concrete
    implementations.
    """
    
    @abstractmethod
    def get_current_regime(self, timestamp: Optional[Union[datetime, float, int]] = None) -> Dict[str, Any]:
        """
        Get the current or latest regime snapshot.
        
        Args:
            timestamp: Optional timestamp to get regime at specific time.
                      If None, returns the most recent snapshot.
        
        Returns:
            Dict containing at minimum:
                - 'state': str (regime state like 'BULL', 'BEAR', 'CHOP')
                - 'risk_suppressed': bool
                - 'timestamp': datetime or float
            May also contain:
                - 'regime_confidence': float (0.0-1.0)
                - 'regime_duration_minutes': float
                - Other regime-specific data
        """
        pass
    
    @abstractmethod
    def get_regime_history(self, lookback: int) -> List[Dict[str, Any]]:
        """
        Get historical regime snapshots.
        
        Args:
            lookback: Number of historical snapshots to return
        
        Returns:
            List of regime snapshots in chronological order (oldest first)
        """
        pass
    
    @abstractmethod
    def update(self, signals: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update regime detection with new market signals.
        
        Args:
            signals: Dictionary of market signals including OHLCV and microstructure data
        
        Returns:
            The newly created regime snapshot
        """
        pass
    
    @abstractmethod
    def get_regime_at_timestamp(self, timestamp: Union[datetime, float, int]) -> Optional[Dict[str, Any]]:
        """
        Get the regime snapshot that was active at a specific timestamp.
        
        Args:
            timestamp: The timestamp to query
        
        Returns:
            Regime snapshot active at that time, or None if no data available
        """
        pass


class IStrategyComponent(ABC):
    """
    Base interface for strategy components that need regime data.
    
    This allows strategies to declare their dependency on regime data
    in a clean, testable way.
    """
    
    @property
    @abstractmethod
    def regime_provider(self) -> Optional[IRegimeProvider]:
        """Get the regime provider instance."""
        pass
    
    @regime_provider.setter
    @abstractmethod
    def regime_provider(self, provider: IRegimeProvider) -> None:
        """Set the regime provider instance."""
        pass


class IRegimeDetector(ABC):
    """
    Interface for regime detection systems.
    
    Both legacy (granular_microstructure) and modern (continuous_gms)
    detectors must implement this interface.
    """
    
    @abstractmethod
    def detect_regime(self, signals: Dict[str, Any], timestamp: Optional[datetime] = None) -> str:
        """
        Detect market regime from signals.
        
        Args:
            signals: Dictionary of market signals and indicators
            timestamp: Current timestamp (optional)
            
        Returns:
            Regime state: 'BULL', 'BEAR', 'CHOP', etc.
        """
        pass
    
    @abstractmethod
    def get_allowed_states(self, strategy_type: str) -> List[str]:
        """
        Get list of regime states that allow trading for a strategy.
        
        Args:
            strategy_type: Type of strategy ('trend_following', 'mean_reversion', etc.)
            
        Returns:
            List of allowed regime states
        """
        pass
    
    @abstractmethod
    def get_confidence(self) -> float:
        """
        Get confidence level of current regime detection.
        
        Returns:
            Confidence value between 0.0 and 1.0
        """
        pass
    
    @abstractmethod
    def get_raw_state(self, features: Dict[str, Any], timestamp: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Get raw regime state with all details.
        
        Args:
            features: Market features
            timestamp: Current timestamp
            
        Returns:
            Dict with 'state', 'risk_suppressed', and other details
        """
        pass


class IStrategy(ABC):
    """
    Interface for trading strategies.
    
    Both TF-v2 (legacy) and TF-v3 (modern) strategies must implement this interface.
    """
    
    @abstractmethod
    def evaluate_entry(self, signals: Dict[str, Any], regime: str) -> Optional[Dict[str, Any]]:
        """
        Evaluate whether to enter a position.
        
        Args:
            signals: Market signals and indicators
            regime: Current market regime
            
        Returns:
            Entry decision dict with 'direction', 'confidence', etc., or None
        """
        pass
    
    @abstractmethod
    def check_exit(self, signals: Dict[str, Any], position: Dict[str, Any]) -> Optional[str]:
        """
        Check if current position should be exited.
        
        Args:
            signals: Current market signals
            position: Current position information
            
        Returns:
            Exit reason string or None
        """
        pass
    
    @abstractmethod
    def get_position_size(self, signals: Dict[str, Any], direction: str) -> float:
        """
        Calculate position size for entry.
        
        Args:
            signals: Market signals including ATR
            direction: 'long' or 'short'
            
        Returns:
            Position size as fraction of account
        """
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """Get strategy identifier."""
        pass


class IDataLoader(ABC):
    """
    Interface for data loading systems.
    
    Allows different data sources (raw2/ vs features_1s/) to be used
    interchangeably.
    """
    
    @abstractmethod
    def load_data(self, start_date: datetime, end_date: datetime) -> Any:
        """
        Load market data for specified date range.
        
        Args:
            start_date: Start of date range
            end_date: End of date range (exclusive)
            
        Returns:
            DataFrame with OHLCV and feature columns
        """
        pass
    
    @abstractmethod
    def get_required_columns(self) -> List[str]:
        """
        Get list of required columns for this data loader.
        
        Returns:
            List of column names
        """
        pass
    
    @abstractmethod
    def validate_data(self, df: Any) -> tuple[bool, List[str]]:
        """
        Validate loaded data meets requirements.
        
        Args:
            df: DataFrame to validate
            
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        pass


class IRiskManager(ABC):
    """
    Interface for risk management systems.
    
    Handles position sizing, leverage, and risk limits.
    """
    
    @abstractmethod
    def calculate_position_size(self, 
                              signal_strength: float,
                              volatility: float,
                              regime: str,
                              account_balance: float) -> Dict[str, float]:
        """
        Calculate safe position size.
        
        Args:
            signal_strength: Strength of trading signal (0-1)
            volatility: Current market volatility (ATR)
            regime: Current market regime
            account_balance: Current account balance
            
        Returns:
            Dict with 'size', 'leverage', 'risk_amount'
        """
        pass
    
    @abstractmethod
    def check_risk_limits(self, position_size: float, current_positions: List[Dict]) -> bool:
        """
        Check if new position violates risk limits.
        
        Args:
            position_size: Proposed position size
            current_positions: List of current open positions
            
        Returns:
            True if position is allowed, False otherwise
        """
        pass
    
    @abstractmethod
    def get_stop_loss(self, entry_price: float, direction: str, volatility: float) -> float:
        """
        Calculate stop loss price.
        
        Args:
            entry_price: Entry price for position
            direction: 'long' or 'short'
            volatility: Current ATR
            
        Returns:
            Stop loss price
        """
        pass


class IExecutionEngine(ABC):
    """
    Interface for execution systems.
    
    Handles order execution, slippage, and fill simulation.
    """
    
    @abstractmethod
    def simulate_fill(self, 
                     order_type: str,
                     direction: str, 
                     size: float,
                     timestamp: datetime,
                     market_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Simulate order fill with slippage.
        
        Args:
            order_type: 'market' or 'limit'
            direction: 'buy' or 'sell'
            size: Position size
            timestamp: Order timestamp
            market_data: Current market data including spread
            
        Returns:
            Dict with 'fill_price', 'slippage', 'fees'
        """
        pass
    
    @abstractmethod
    def estimate_transaction_costs(self, size: float, price: float) -> float:
        """
        Estimate total transaction costs.
        
        Args:
            size: Position size
            price: Expected price
            
        Returns:
            Total cost including fees and expected slippage
        """
        pass


class ISystemComponent(ABC):
    """
    Base interface for all system components.
    
    Provides common functionality like configuration and logging.
    """
    
    @abstractmethod
    def get_component_name(self) -> str:
        """Get component identifier for logging."""
        pass
    
    @abstractmethod
    def validate_config(self, config: Any) -> tuple[bool, List[str]]:
        """
        Validate component configuration.
        
        Args:
            config: Configuration object
            
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        pass
    
    @abstractmethod
    def get_diagnostics(self) -> Dict[str, Any]:
        """
        Get component diagnostics and statistics.
        
        Returns:
            Dict of diagnostic information
        """
        pass