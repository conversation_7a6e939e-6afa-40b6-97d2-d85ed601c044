"""
Configuration Validator - Detects configuration fallbacks and inheritance issues.

This module ensures that all configuration values are explicitly set and not
falling back to unintended defaults. It provides clear visibility when values
are using schema defaults instead of base configuration values.
"""
import logging
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, field
from pathlib import Path
import yaml

logger = logging.getLogger(__name__)


@dataclass
class ConfigIssue:
    """Represents a configuration issue found during validation."""
    path: str  # Configuration path (e.g., "tf_v3.risk_frac")
    expected_source: str  # Where value should come from (e.g., "base.yaml")
    actual_source: str  # Where value actually came from (e.g., "schema default")
    expected_value: Any  # What value was expected
    actual_value: Any  # What value was actually used
    severity: str = "ERROR"  # ERROR, WARNING, INFO


class ConfigValidator:
    """
    Validates configuration to detect silent fallbacks and inheritance issues.
    
    This validator ensures that configuration values come from intended sources
    and not from unintended schema defaults or fallback mechanisms.
    """
    
    def __init__(self, base_config_path: Optional[Path] = None, strict_mode: bool = False):
        """
        Initialize the configuration validator.
        
        Args:
            base_config_path: Path to base configuration file (base.yaml)
            strict_mode: If True, raise exceptions on critical issues
        """
        self.base_config_path = base_config_path
        self.strict_mode = strict_mode
        self.logger = logging.getLogger(self.__class__.__name__)
        self.issues: List[ConfigIssue] = []
        self._base_config: Dict[str, Any] = {}
        self._load_base_config()
        
    def _load_base_config(self) -> None:
        """Load base configuration if path is provided."""
        if self.base_config_path and self.base_config_path.exists():
            with open(self.base_config_path, 'r') as f:
                self._base_config = yaml.safe_load(f)
                self.logger.info(f"Loaded base config from {self.base_config_path}")
    
    def validate_config(self, config: Any, override_config: Optional[Dict[str, Any]] = None) -> List[ConfigIssue]:
        """
        Validate configuration for fallback issues.
        
        Args:
            config: The loaded configuration object (Settings instance)
            override_config: The override configuration dictionary
            
        Returns:
            List of configuration issues found
        """
        self.issues = []
        
        # Critical validations
        self._validate_risk_fraction(config, override_config)
        self._validate_thresholds(config, override_config)
        self._validate_detector_consistency(config, override_config)
        self._validate_leverage_settings(config, override_config)
        
        # Log summary
        self._log_validation_summary()
        
        return self.issues
    
    def _validate_risk_fraction(self, config: Any, override_config: Optional[Dict[str, Any]]) -> None:
        """Validate risk fraction configuration."""
        # Check TF-v3 risk fraction
        if hasattr(config, 'tf_v3') and hasattr(config.tf_v3, 'risk_frac'):
            actual_value = config.tf_v3.risk_frac
            base_value = self._base_config.get('tf_v3', {}).get('risk_frac')
            override_value = override_config.get('tf_v3', {}).get('risk_frac') if override_config else None
            
            # Determine source
            if override_value is not None and override_value == actual_value:
                actual_source = "override config"
            elif base_value is not None and base_value == actual_value:
                actual_source = "base config"
            else:
                actual_source = "schema default"
                
            # Check for fallback to schema default
            if actual_source == "schema default" and base_value is not None:
                self.issues.append(ConfigIssue(
                    path="tf_v3.risk_frac",
                    expected_source="base config",
                    actual_source="schema default",
                    expected_value=base_value,
                    actual_value=actual_value,
                    severity="ERROR"
                ))
                self._handle_fallback("tf_v3.risk_frac", actual_source, "base config", actual_value, base_value)
    
    def _validate_thresholds(self, config: Any, override_config: Optional[Dict[str, Any]]) -> None:
        """Validate momentum and volatility thresholds."""
        threshold_paths = [
            ('regime.continuous_gms.gms_mom_strong_thresh', 'gms.gms_mom_strong_thresh'),
            ('regime.continuous_gms.gms_mom_weak_thresh', 'gms.gms_mom_weak_thresh'),
            ('regime.continuous_gms.gms_vol_high_thresh', 'gms.gms_vol_high_thresh'),
            ('regime.continuous_gms.gms_vol_low_thresh', 'gms.gms_vol_low_thresh'),
        ]
        
        for regime_path, gms_path in threshold_paths:
            self._check_threshold_consistency(config, override_config, regime_path, gms_path)
    
    def _check_threshold_consistency(self, config: Any, override_config: Optional[Dict[str, Any]], 
                                    regime_path: str, gms_path: str) -> None:
        """Check if threshold values are consistent between regime and gms sections."""
        regime_value = self._get_nested_value(config, regime_path)
        gms_value = self._get_nested_value(config, gms_path)
        
        if regime_value is not None and gms_value is not None and regime_value != gms_value:
            self.issues.append(ConfigIssue(
                path=f"{regime_path} vs {gms_path}",
                expected_source="consistent values",
                actual_source="mismatched values",
                expected_value=regime_value,
                actual_value=gms_value,
                severity="WARNING"
            ))
            self.logger.warning(f"⚠️ CONFIG MISMATCH: {regime_path}={regime_value} but {gms_path}={gms_value}")
    
    def _validate_detector_consistency(self, config: Any, override_config: Optional[Dict[str, Any]]) -> None:
        """Validate detector type consistency."""
        regime_detector = self._get_nested_value(config, 'regime.detector_type')
        gms_detector = self._get_nested_value(config, 'gms.detector_type')
        
        if regime_detector and gms_detector and regime_detector != gms_detector:
            self.issues.append(ConfigIssue(
                path="detector_type",
                expected_source="consistent detector type",
                actual_source="mismatched detector types",
                expected_value=regime_detector,
                actual_value=gms_detector,
                severity="ERROR"
            ))
            self._handle_fallback("detector_type", f"regime={regime_detector}", f"gms={gms_detector}", 
                                regime_detector, gms_detector)
    
    def _validate_leverage_settings(self, config: Any, override_config: Optional[Dict[str, Any]]) -> None:
        """Validate leverage configuration consistency."""
        # Check max leverage consistency
        portfolio_max = self._get_nested_value(config, 'portfolio.max_leverage')
        tf_v3_max = self._get_nested_value(config, 'tf_v3.max_leverage')
        
        if portfolio_max and tf_v3_max and portfolio_max < tf_v3_max:
            self.issues.append(ConfigIssue(
                path="max_leverage",
                expected_source="portfolio.max_leverage >= tf_v3.max_leverage",
                actual_source="tf_v3.max_leverage exceeds portfolio limit",
                expected_value=f"tf_v3.max_leverage <= {portfolio_max}",
                actual_value=tf_v3_max,
                severity="WARNING"
            ))
    
    def _get_nested_value(self, obj: Any, path: str) -> Any:
        """Get nested value from object using dot notation path."""
        parts = path.split('.')
        current = obj
        
        for part in parts:
            if hasattr(current, part):
                current = getattr(current, part)
            elif isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return None
        
        return current
    
    def _handle_fallback(self, path: str, actual: str, expected: str, 
                        actual_value: Any = None, expected_value: Any = None) -> None:
        """Handle configuration fallback detection."""
        if actual_value is not None and expected_value is not None:
            msg = f"❌ CONFIG FALLBACK: {path} using {actual}={actual_value} instead of {expected}={expected_value}"
        else:
            msg = f"❌ CONFIG FALLBACK: {path} using {actual} instead of {expected}"
        
        self.logger.error(msg)
        
        if self.strict_mode:
            raise ValueError(msg)
    
    def _log_validation_summary(self) -> None:
        """Log validation summary with clear status."""
        if not self.issues:
            self.logger.info("✅ No configuration fallbacks detected - using intended settings")
            print("[INFO] Backtester: ✅ No configuration fallbacks detected - using intended settings")
        else:
            error_count = sum(1 for issue in self.issues if issue.severity == "ERROR")
            warning_count = sum(1 for issue in self.issues if issue.severity == "WARNING")
            
            self.logger.error(f"❌ Configuration validation found {error_count} errors, {warning_count} warnings")
            
            # Log each issue
            for issue in self.issues:
                if issue.severity == "ERROR":
                    self.logger.error(f"  ❌ {issue.path}: Expected {issue.expected_source}, got {issue.actual_source}")
                elif issue.severity == "WARNING":
                    self.logger.warning(f"  ⚠️ {issue.path}: {issue.actual_source}")
    
    def get_validation_report(self) -> str:
        """Generate a detailed validation report."""
        if not self.issues:
            return "✅ Configuration validation passed - no fallbacks detected"
        
        report_lines = ["Configuration Validation Report", "=" * 50]
        
        # Group by severity
        errors = [i for i in self.issues if i.severity == "ERROR"]
        warnings = [i for i in self.issues if i.severity == "WARNING"]
        
        if errors:
            report_lines.append(f"\n❌ ERRORS ({len(errors)}):")
            for issue in errors:
                report_lines.append(f"  - {issue.path}")
                report_lines.append(f"    Expected: {issue.expected_source} = {issue.expected_value}")
                report_lines.append(f"    Actual:   {issue.actual_source} = {issue.actual_value}")
        
        if warnings:
            report_lines.append(f"\n⚠️ WARNINGS ({len(warnings)}):")
            for issue in warnings:
                report_lines.append(f"  - {issue.path}")
                report_lines.append(f"    Issue: {issue.actual_source}")
        
        return "\n".join(report_lines)


def validate_config_no_fallbacks(config: Any, base_config_path: Optional[Path] = None,
                                override_config: Optional[Dict[str, Any]] = None,
                                strict_mode: bool = False) -> bool:
    """
    Convenience function to validate configuration has no fallbacks.
    
    Args:
        config: The configuration object to validate
        base_config_path: Path to base configuration file
        override_config: Override configuration dictionary
        strict_mode: If True, raise exceptions on issues
        
    Returns:
        True if no critical issues found, False otherwise
    """
    validator = ConfigValidator(base_config_path=base_config_path, strict_mode=strict_mode)
    issues = validator.validate_config(config, override_config)
    
    # Return True only if no ERROR-level issues
    return not any(issue.severity == "ERROR" for issue in issues)