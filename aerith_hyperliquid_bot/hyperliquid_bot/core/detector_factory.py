"""
Regime Detector Factory with System Isolation
============================================
This factory routes to the appropriate system based on configuration.
"""

import logging
from typing import Optional

from .interfaces import IRegimeDetector
from ..config.settings import Config


logger = logging.getLogger(__name__)


def get_regime_detector(config: Config) -> IRegimeDetector:
    """
    Factory function to instantiate the appropriate regime detector.
    
    This routes to either legacy or modern system based on detector type.
    """
    detector_type = getattr(config.regime, 'detector_type', 'rule_based').lower()
    logger.info(f"Creating regime detector: {detector_type}")
    
    # Route to legacy system
    if detector_type == 'granular_microstructure':
        # Import legacy components when needed
        from ..legacy import detector as legacy_detector
        from ..legacy.registry import get_legacy_registry
        
        # Trigger registration by importing
        _ = legacy_detector
        
        # Get from legacy registry
        registry = get_legacy_registry()
        detector_class = registry.get(IRegimeDetector, "granular_microstructure")
        
        logger.info("Using LEGACY granular microstructure detector")
        return detector_class(config)
    
    # Route to modern system
    elif detector_type == 'continuous_gms':
        # Import modern components when needed
        from ..modern import detector as modern_detector
        from ..modern.registry import get_modern_registry
        
        # Trigger registration by importing
        _ = modern_detector
        
        # Get from modern registry
        registry = get_modern_registry()
        detector_class = registry.get(IRegimeDetector, "continuous_gms")
        
        logger.info("Using MODERN continuous GMS detector")
        return detector_class(config)
    
    # Route to modern system V2 (overhauled)
    elif detector_type == 'continuous_modern_v2':
        # Import modern components when needed
        from ..modern import continuous_detector_v2
        from ..modern.registry import get_modern_registry
        
        # Trigger registration by importing
        _ = continuous_detector_v2
        
        # Get from modern registry
        registry = get_modern_registry()
        detector_class = registry.get(IRegimeDetector, "continuous_modern_v2")
        
        logger.info("Using MODERN continuous detector V2 (overhauled)")
        return detector_class(config)
    
    # Keep other detectors for backward compatibility
    elif detector_type == 'hurst':
        from .detector import HurstRegimeDetector
        logger.info("Using Hurst regime detector")
        return HurstRegimeDetector(config)
    
    elif detector_type == 'rule_based':
        from .detector import RuleBasedRegimeDetector
        logger.info("Using rule-based regime detector")
        return RuleBasedRegimeDetector(config)
    
    else:
        raise ValueError(f"Unknown regime detector type: '{detector_type}'")