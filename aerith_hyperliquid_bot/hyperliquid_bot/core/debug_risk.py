"""
Enhanced Risk Manager for debugging market bias settings

This module extends the core risk manager with detailed logging
to help debug and verify the behavior of market bias settings.
"""

import logging
from hyperliquid_bot.core.risk import RiskManager as OriginalRiskManager
from typing import Dict, Optional, Tuple


class DebugRiskManager(OriginalRiskManager):
    """Enhanced RiskManager with additional debug logging for market bias testing"""
    
    def __init__(self, config):
        """Initialize with standard RiskManager parameters"""
        super().__init__(config)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info("DEBUG RISK MANAGER INITIALIZED - Enhanced logging enabled")
    
    def calculate_position(self, portfolio, signals: dict, strategy_name: str, regime: str, strategy_info: Optional[Dict] = None) -> Tuple[Optional[float], Optional[float]]:
        """
        Enhanced version of calculate_position with detailed debug logging
        
        This maintains identical logic to the original method but adds extensive
        logging at key decision points to track market bias application.
        """
        # Log initial call parameters
        self.logger.info(f"=== MARKET BIAS DEBUG === Strategy: {strategy_name}, Regime: {regime}")
        self.logger.info(f"Strategy Info: {strategy_info}")
        
        # Extract config for logging
        cfg = self.config
        market_bias_enabled = hasattr(cfg.regime, 'market_bias') and hasattr(cfg.regime.market_bias, 'enabled') and cfg.regime.market_bias.enabled
        self.logger.info(f"Market Bias Enabled: {market_bias_enabled}")
        
        if market_bias_enabled:
            # Log market bias configuration
            self.logger.info(f"BULL Risk Factor: {getattr(cfg.regime.market_bias, 'bull_risk_factor', 1.0)}")
            self.logger.info(f"BEAR Risk Factor: {getattr(cfg.regime.market_bias, 'bear_risk_factor', 1.0)}")
            self.logger.info(f"CHOP Risk Factor: {getattr(cfg.regime.market_bias, 'chop_risk_factor', 1.0)}")
            
            self.logger.info(f"BULL Leverage Factor: {getattr(cfg.regime.market_bias, 'bull_leverage_factor', 1.0)}")
            self.logger.info(f"BEAR Leverage Factor: {getattr(cfg.regime.market_bias, 'bear_leverage_factor', 1.0)}")
            self.logger.info(f"CHOP Leverage Factor: {getattr(cfg.regime.market_bias, 'chop_leverage_factor', 1.0)}")
            
            self.logger.info(f"BULL Long Bias: {getattr(cfg.regime.market_bias, 'bull_long_bias', 1.0)}")
            self.logger.info(f"BULL Short Bias: {getattr(cfg.regime.market_bias, 'bull_short_bias', 1.0)}")
            self.logger.info(f"BEAR Long Bias: {getattr(cfg.regime.market_bias, 'bear_long_bias', 1.0)}")
            self.logger.info(f"BEAR Short Bias: {getattr(cfg.regime.market_bias, 'bear_short_bias', 1.0)}")
        
        # Extract direction before the main calculation
        direction = "unknown"
        if strategy_info and 'direction' in strategy_info:
            direction = strategy_info['direction'].lower()  # Normalize to lowercase
        self.logger.info(f"DIRECTION DEBUG: Strategy '{strategy_name}' provided direction: '{direction}'")
        
        # Call original method to perform the actual calculation
        size, leverage = super().calculate_position(
            portfolio, signals, strategy_name, regime, strategy_info
        )
        
        # Log final results
        self.logger.info(f"=== FINAL RESULT === Size: {size}, Leverage: {leverage}")
        
        return size, leverage
    
    def _validate_market_bias(self, market_state: str, regime: str, base_leverage: float, final_leverage: float, market_risk_factor: float, direction_bias: float):
        """Enhanced validation with more detailed logging"""
        
        # Call original validation method
        super()._validate_market_bias(
            market_state, regime, base_leverage, final_leverage, market_risk_factor, direction_bias
        )
        
        # Add additional debug information
        self.logger.info(f"MARKET BIAS VALIDATION: State={market_state}, Original Regime={regime}")
        self.logger.info(f"MARKET BIAS VALIDATION: Base Leverage={base_leverage:.2f}x → Final Leverage={final_leverage:.2f}x")
        self.logger.info(f"MARKET BIAS VALIDATION: Market Risk Factor={market_risk_factor:.2f}x, Direction Bias={direction_bias:.2f}x")
        
        # Calculate and log the combined effect
        combined_factor = market_risk_factor * direction_bias
        self.logger.info(f"MARKET BIAS VALIDATION: Combined Multiplier Effect={combined_factor:.2f}x")


# Usage Example:
# Replace standard RiskManager with DebugRiskManager in backtester initialization:
#
# from hyperliquid_bot.core.debug_risk import DebugRiskManager
# 
# # In Backtester.__init__:
# self.risk_manager = DebugRiskManager(config=config, logger=logger)
