"""
Unified GMS Detector Implementation
Merges GranularMicrostructureRegimeDetector and ContinuousGMSDetector functionality.

This implementation provides:
- Zero code duplication between legacy and continuous modes
- 100% backward compatibility with existing configurations
- Optimized performance for both detector modes
- Unified configuration interface
"""

import logging
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime, timedelta

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.utils.state_mapping import validate_gms_state


logger = logging.getLogger(__name__)


class RegimeDetectorInterface(ABC):
    """Base interface for regime detectors."""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None) -> Union[str, Dict[str, Any]]:
        """Determine the current market regime based on signals."""
        pass
    
    @property
    @abstractmethod
    def required_signals(self) -> List[str]:
        """Return list of required signal names."""
        pass


class UnifiedGMSDetector(RegimeDetectorInterface):
    """
    Unified GMS detector supporting both legacy and continuous modes.
    
    Modes:
    - 'legacy' or 'granular_microstructure': Replicates GranularMicrostructureRegimeDetector behavior
    - 'continuous' or 'continuous_gms': Provides modern continuous functionality
    
    Configuration Priority:
    1. gms.mode (new unified setting)
    2. gms.detector_type 
    3. regime.detector_type
    4. Default to 'continuous'
    """
    
    def __init__(self, config: Config):
        super().__init__(config)
        
        # Core configuration resolution
        self.detector_mode = self._resolve_detector_mode(config)
        self.detector_type = self._resolve_detector_type(config)
        
        # Configuration objects
        self.cfg_gms = self._resolve_gms_config(config)
        self.cfg_regime = config.regime
        self.cfg_micro = config.microstructure
        
        # Threshold resolution
        self.thresholds = self._resolve_thresholds(config)
        
        # Mode-specific initialization
        if self.detector_mode == 'legacy':
            self._init_legacy_mode()
        else:
            self._init_continuous_mode()
        
        # Common initialization
        self._init_common_components()
        
        self.logger.info(
            f"Initialized UnifiedGMSDetector in {self.detector_mode} mode "
            f"(type: {self.detector_type})"
        )
        self.logger.debug(f"Thresholds: {self.thresholds}")
    
    def _resolve_detector_mode(self, config: Config) -> str:
        """
        Resolve detector mode with multiple fallback paths for compatibility.
        
        Priority order:
        1. gms.mode (new unified setting)
        2. gms.detector_type (legacy setting)
        3. regime.detector_type (oldest setting)
        4. Default to 'continuous'
        """
        # Priority 1: New unified mode setting
        if hasattr(config, 'gms') and hasattr(config.gms, 'mode'):
            mode = config.gms.mode.lower()
            if mode in ['legacy', 'granular_microstructure', 'static']:
                return 'legacy'
            elif mode in ['continuous', 'continuous_gms', 'adaptive']:
                return 'continuous'
        
        # Priority 2: Legacy detector_type in gms section
        if hasattr(config, 'gms') and hasattr(config.gms, 'detector_type'):
            detector_type = config.gms.detector_type.lower()
            if detector_type == 'granular_microstructure':
                return 'legacy'
            elif detector_type == 'continuous_gms':
                return 'continuous'
        
        # Priority 3: Legacy detector_type in regime section
        if hasattr(config.regime, 'detector_type'):
            detector_type = config.regime.detector_type.lower()
            if detector_type == 'granular_microstructure':
                return 'legacy'
            elif detector_type == 'continuous_gms':
                return 'continuous'
        
        # Default to continuous mode
        self.logger.warning("No detector mode specified, defaulting to continuous")
        return 'continuous'
    
    def _resolve_detector_type(self, config: Config) -> str:
        """Resolve detector type for backward compatibility."""
        if self.detector_mode == 'legacy':
            return 'granular_microstructure'
        else:
            return 'continuous_gms'
    
    def _resolve_gms_config(self, config: Config) -> Any:
        """Resolve GMS configuration with fallback hierarchy."""
        # Try new unified gms section first
        if hasattr(config, 'gms'):
            return config.gms
        
        # Fallback to detector-specific sections in regime
        if self.detector_mode == 'legacy':
            return getattr(config.regime, 'granular_microstructure', None)
        else:
            return getattr(config.regime, 'continuous_gms', None)
    
    def _resolve_thresholds(self, config: Config) -> Dict[str, float]:
        """
        Resolve threshold values with unified configuration support.
        
        Priority:
        1. gms.thresholds.<mode> (new unified structure)
        2. regime.<detector_type>.gms_* (detector-specific)
        3. regime.gms_* (fallback)
        4. Mode-specific defaults
        """
        thresholds = {}
        
        # Mode-specific defaults
        if self.detector_mode == 'legacy':
            defaults = {
                'vol_high': 0.0092, 'vol_low': 0.0055,  # CRITICAL: Scaled for decimal ATR units
                'mom_strong': 100.0, 'mom_weak': 50.0,
                'spread_std_high': 0.000050, 'spread_mean_low': 0.000045
            }
        else:
            defaults = {
                'vol_high': 0.03, 'vol_low': 0.01,
                'mom_strong': 2.5, 'mom_weak': 0.5,
                'spread_std_high': 0.0005, 'spread_mean_low': 0.0001
            }
        
        # Try unified threshold configuration first
        if (hasattr(config, 'gms') and hasattr(config.gms, 'thresholds')):
            mode_thresholds = getattr(config.gms.thresholds, self.detector_mode, None)
            if mode_thresholds:
                return {
                    'vol_high': getattr(mode_thresholds, 'vol_high', defaults['vol_high']),
                    'vol_low': getattr(mode_thresholds, 'vol_low', defaults['vol_low']),
                    'mom_strong': getattr(mode_thresholds, 'mom_strong', defaults['mom_strong']),
                    'mom_weak': getattr(mode_thresholds, 'mom_weak', defaults['mom_weak']),
                    'spread_std_high': getattr(mode_thresholds, 'spread_std_high', defaults['spread_std_high']),
                    'spread_mean_low': getattr(mode_thresholds, 'spread_mean_low', defaults['spread_mean_low']),
                }
        
        # Fallback to detector-specific configuration
        detector_config = None
        if self.detector_mode == 'legacy':
            detector_config = getattr(config.regime, 'granular_microstructure', None)
        else:
            detector_config = getattr(config.regime, 'continuous_gms', None)
        
        if detector_config:
            return {
                'vol_high': getattr(detector_config, 'gms_vol_high_thresh', 
                                   getattr(config.regime, 'gms_vol_high_thresh', defaults['vol_high'])),
                'vol_low': getattr(detector_config, 'gms_vol_low_thresh',
                                  getattr(config.regime, 'gms_vol_low_thresh', defaults['vol_low'])),
                'mom_strong': getattr(detector_config, 'gms_mom_strong_thresh',
                                     getattr(config.regime, 'gms_mom_strong_thresh', defaults['mom_strong'])),
                'mom_weak': getattr(detector_config, 'gms_mom_weak_thresh',
                                   getattr(config.regime, 'gms_mom_weak_thresh', defaults['mom_weak'])),
                'spread_std_high': getattr(detector_config, 'gms_spread_std_high_thresh',
                                          getattr(config.regime, 'gms_spread_std_high_thresh', defaults['spread_std_high'])),
                'spread_mean_low': getattr(detector_config, 'gms_spread_mean_low_thresh',
                                          getattr(config.regime, 'gms_spread_mean_low_thresh', defaults['spread_mean_low'])),
            }
        
        # Final fallback to regime-level settings
        return {
            'vol_high': getattr(config.regime, 'gms_vol_high_thresh', defaults['vol_high']),
            'vol_low': getattr(config.regime, 'gms_vol_low_thresh', defaults['vol_low']),
            'mom_strong': getattr(config.regime, 'gms_mom_strong_thresh', defaults['mom_strong']),
            'mom_weak': getattr(config.regime, 'gms_mom_weak_thresh', defaults['mom_weak']),
            'spread_std_high': getattr(config.regime, 'gms_spread_std_high_thresh', defaults['spread_std_high']),
            'spread_mean_low': getattr(config.regime, 'gms_spread_mean_low_thresh', defaults['spread_mean_low']),
        }
    
    def _init_legacy_mode(self):
        """Initialize legacy mode (granular_microstructure) settings."""
        self.cadence_sec = 3600  # Hourly updates
        self.output_format = 'string'
        self.data_source = 'raw2'
        self.depth_levels = 5  # Fixed depth for legacy
        
        # Legacy-specific settings
        self.use_adx_confirmation = getattr(self.cfg_regime, 'gms_use_adx_confirmation', False)
        self.use_funding_confirmation = getattr(self.cfg_regime, 'gms_use_funding_confirmation', False)
        
        # No adaptive thresholds in legacy mode
        self.adaptive_vol_threshold = None
        self.adaptive_mom_threshold = None
        
        # Performance optimization for legacy
        if self.cfg_gms:
            self.skip_l2_processing = getattr(self.cfg_gms, 'skip_l2_raw_processing_if_1h_features_exist', True)
        else:
            self.skip_l2_processing = True
        
        self.logger.info(
            f"Initialized legacy mode: 3600s cadence, fixed thresholds, "
            f"raw2 data source, skip_l2={self.skip_l2_processing}"
        )
    
    def _init_continuous_mode(self):
        """Initialize continuous mode (continuous_gms) settings."""
        self.cadence_sec = getattr(self.cfg_gms, 'cadence_sec', 60) if self.cfg_gms else 60
        self.output_format = 'dict'
        self.data_source = 'features_1s'
        self.depth_levels = getattr(self.cfg_micro, 'depth_levels', 5)
        
        # Continuous-specific settings
        if self.cfg_gms:
            self.output_states = getattr(self.cfg_gms, 'output_states', 8)
            self.risk_suppressed_notional_frac = getattr(self.cfg_gms, 'risk_suppressed_notional_frac', 0.25)
            self.risk_suppressed_pnl_atr_mult = getattr(self.cfg_gms, 'risk_suppressed_pnl_atr_mult', 1.5)
        else:
            self.output_states = 8
            self.risk_suppressed_notional_frac = 0.25
            self.risk_suppressed_pnl_atr_mult = 1.5
        
        # Initialize adaptive thresholds if enabled
        self._init_adaptive_thresholds()
        
        self.logger.info(
            f"Initialized continuous mode: {self.cadence_sec}s cadence, "
            f"depth_levels={self.depth_levels}, "
            f"adaptive_thresholds={self.adaptive_vol_threshold is not None}"
        )
    
    def _init_adaptive_thresholds(self):
        """Initialize adaptive thresholds for continuous mode ONLY."""
        # CRITICAL: Legacy mode should NEVER use adaptive thresholds
        if self.detector_mode == 'legacy':
            self.adaptive_vol_threshold = None
            self.adaptive_mom_threshold = None
            self.logger.info("Legacy mode: adaptive thresholds disabled (using fixed thresholds only)")
            return
        
        # Skip if no gms config
        if not self.cfg_gms:
            self.adaptive_vol_threshold = None
            self.adaptive_mom_threshold = None
            return
        
        # Check if adaptive thresholds are enabled (continuous mode only)
        adaptive_config = getattr(self.cfg_gms, 'adaptive_thresholds', None)
        if not adaptive_config:
            # Legacy check for auto_thresholds (continuous mode only)
            if getattr(self.cfg_gms, 'auto_thresholds', False):
                self.logger.warning("Using legacy auto_thresholds setting for continuous mode - consider updating to adaptive_thresholds")
                # Create minimal adaptive config from legacy settings
                adaptive_config = type('obj', (object,), {
                    'enabled': True,
                    'percentile_window_sec': getattr(self.cfg_gms, 'percentile_window_sec', 86400),
                    'vol_low_pct': getattr(self.cfg_gms, 'vol_low_pct', 0.15),
                    'vol_high_pct': getattr(self.cfg_gms, 'vol_high_pct', 0.50),
                    'mom_low_pct': getattr(self.cfg_gms, 'mom_low_pct', 0.15),
                    'mom_high_pct': getattr(self.cfg_gms, 'mom_high_pct', 0.50),
                    'priming_hours': 1
                })()
            else:
                self.adaptive_vol_threshold = None
                self.adaptive_mom_threshold = None
                return
        
        if not getattr(adaptive_config, 'enabled', False):
            self.adaptive_vol_threshold = None
            self.adaptive_mom_threshold = None
            return
        
        try:
            # Try to import optimized version first
            try:
                from hyperliquid_bot.utils.optimized_adaptive_threshold import OptimizedAdaptiveThreshold
                AdaptiveThresholdClass = OptimizedAdaptiveThreshold
                self.logger.info("Using OptimizedAdaptiveThreshold")
            except ImportError:
                # Fallback to standard version
                from hyperliquid_bot.utils.adaptive_threshold import AdaptiveThreshold
                AdaptiveThresholdClass = AdaptiveThreshold
                self.logger.info("Using standard AdaptiveThreshold")
            
            # Use adaptive threshold implementation
            window_sec = getattr(adaptive_config, 'percentile_window_sec', 86400)
            
            self.adaptive_vol_threshold = AdaptiveThresholdClass(
                getattr(adaptive_config, 'vol_low_pct', 0.15),
                getattr(adaptive_config, 'vol_high_pct', 0.50),
                window_sec
            )
            
            self.adaptive_mom_threshold = AdaptiveThresholdClass(
                getattr(adaptive_config, 'mom_low_pct', 0.15),
                getattr(adaptive_config, 'mom_high_pct', 0.50),
                window_sec
            )
            
            # Store priming config
            self.priming_hours = getattr(adaptive_config, 'priming_hours', 1)
            
            self.logger.info(
                f"Initialized adaptive thresholds: "
                f"window={window_sec}s, priming_hours={self.priming_hours}"
            )
            
        except Exception as e:
            self.logger.error(f"Error initializing adaptive thresholds: {e}, falling back to fixed")
            self.adaptive_vol_threshold = None
            self.adaptive_mom_threshold = None
    
    def _init_common_components(self):
        """Initialize components common to both modes."""
        # Signal column names (mode-aware)
        # Both modes now use 'atr_percent' with consistent decimal units
        self.ATR_COL = 'atr_percent'
        self.ATR_PCT_COL = 'atr_percent'
        
        # State tracking
        self.current_state = "Unknown"
        self.risk_suppressed = False
        self.last_update_time = 0
        
        # OBI thresholds from microstructure config
        self.obi_strong_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_strong_confirm_thresh', 0.20)
        self.obi_weak_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_weak_confirm_thresh', 0.11)
        
        # State collapse mapping
        self.state_collapse_map = None
        if self.cfg_gms and hasattr(self.cfg_gms, 'state_collapse_map_file'):
            self._load_state_collapse_map(self.cfg_gms.state_collapse_map_file)
        
        # Risk suppression tracking (continuous mode)
        if self.detector_mode == 'continuous':
            self.risk_suppression_history = []
            self.last_risk_check_time = 0
    
    def _load_state_collapse_map(self, map_file: str):
        """Load state collapse mapping from file."""
        try:
            import yaml
            with open(map_file, 'r') as f:
                self.state_collapse_map = yaml.safe_load(f)
            self.logger.info(f"Loaded state collapse map from {map_file}")
        except Exception as e:
            self.logger.warning(f"Failed to load state collapse map: {e}")
            self.state_collapse_map = None
    
    @property
    def required_signals(self) -> List[str]:
        """Returns the list of signals needed by the unified detector."""
        # Base signals always needed
        signals_needed = [
            'timestamp',
            self.ATR_PCT_COL,   # Volatility (mode-specific)
            'ma_slope',         # Momentum
            f'obi_smoothed_{self.depth_levels}',  # Depth-specific OBI
            'spread_mean',      # Base Spread Context
            'spread_std'        # Base Spread Context
        ]
        
        # Mode-specific additional signals
        if self.detector_mode == 'legacy':
            # Legacy mode may need additional signals
            if self.use_adx_confirmation:
                signals_needed.append('adx')
            if self.use_funding_confirmation:
                signals_needed.append('funding_rate')
        else:
            # Continuous mode additional signals
            if self.adaptive_mom_threshold is not None:
                # Prefer enhanced momentum signal if available
                signals_needed.append('ma_slope_ema_30s')
            
            # Risk suppression signals
            signals_needed.extend(['close', 'position_size_usd', 'realized_pnl_usd'])
        
        return signals_needed
    
    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None) -> Union[str, Dict[str, Any]]:
        """
        Unified regime detection with mode-specific output formatting.
        
        Returns:
            str: Regime state for legacy mode
            dict: {"state": str, "risk_suppressed": bool} for continuous mode
        """
        # Check for required signals and handle missing data
        if not self._validate_signals(signals):
            if self.detector_mode == 'legacy':
                return "Unknown"
            else:
                return {"state": "Unknown", "risk_suppressed": False}
        
        # Update adaptive thresholds if enabled
        if self.detector_mode == 'continuous' and self.adaptive_vol_threshold is not None:
            self._update_adaptive_thresholds(signals)
        
        # Core regime detection logic
        state = self._determine_state(signals)
        
        # Update internal state
        self.current_state = state
        self.last_update_time = signals.get('timestamp', 0)
        
        # Mode-specific output formatting
        if self.detector_mode == 'legacy':
            return state  # String output for legacy mode
        else:
            # Calculate risk suppression for continuous mode
            self.risk_suppressed = self._calculate_risk_suppression(signals)
            
            # Handle state collapse if configured
            if self.output_states < 8 and self.state_collapse_map:
                state = self._collapse_state(state)
            
            return {
                "state": state,
                "risk_suppressed": self.risk_suppressed
            }
    
    def _validate_signals(self, signals: dict) -> bool:
        """Validate required signals are present and not NaN."""
        required = self.required_signals
        
        for signal in required:
            if signal == 'timestamp':
                continue  # Skip timestamp validation
            
            # Some signals are optional based on mode
            if signal in ['position_size_usd', 'realized_pnl_usd', 'ma_slope_ema_30s']:
                continue  # These are optional
            
            value = signals.get(signal, np.nan)
            if pd.isna(value):
                if self.detector_mode == 'legacy':
                    # Legacy mode is more lenient with missing signals
                    self.logger.debug(f"Missing signal in legacy mode: {signal}")
                    continue
                else:
                    # Continuous mode requires core signals
                    if signal in ['close', self.ATR_PCT_COL, 'ma_slope']:
                        self.logger.warning(f"Missing required signal: {signal}")
                        return False
        
        return True
    
    def _update_adaptive_thresholds(self, signals: dict):
        """Update adaptive thresholds with current signal values."""
        try:
            # Update volatility threshold
            atr_value = signals.get(self.ATR_PCT_COL, np.nan)
            if not pd.isna(atr_value):
                self.adaptive_vol_threshold.update(atr_value)
            
            # Update momentum threshold
            mom_signal = 'ma_slope_ema_30s' if 'ma_slope_ema_30s' in signals else 'ma_slope'
            mom_value = signals.get(mom_signal, np.nan)
            if not pd.isna(mom_value):
                self.adaptive_mom_threshold.update(abs(mom_value))
        
        except Exception as e:
            self.logger.error(f"Error updating adaptive thresholds: {e}")
    
    def _determine_state(self, signals: dict) -> str:
        """
        Core regime detection logic unified for both modes.
        
        This implements the granular microstructure logic with mode-specific
        threshold resolution and signal handling.
        """
        if not getattr(self.cfg_regime, 'use_filter', True):
            return "Filter_Off"
        
        # Get current threshold values (fixed or adaptive)
        vol_thresholds = self._get_current_vol_thresholds()
        mom_thresholds = self._get_current_mom_thresholds()
        
        # Extract signals with fallbacks
        atr_pct = signals.get(self.ATR_PCT_COL, np.nan)
        ma_slope = signals.get('ma_slope', np.nan)
        
        # Use enhanced momentum signal if available (continuous mode)
        if self.detector_mode == 'continuous' and 'ma_slope_ema_30s' in signals:
            ma_slope = signals.get('ma_slope_ema_30s', ma_slope)
        
        obi_col = f'obi_smoothed_{self.depth_levels}'
        obi_value = signals.get(obi_col, np.nan)
        spread_mean = signals.get('spread_mean', np.nan)
        spread_std = signals.get('spread_std', np.nan)
        
        # Validate core signals
        if any(pd.isna(val) for val in [atr_pct, ma_slope, obi_value, spread_mean, spread_std]):
            return "Unknown"
        
        # Volatility classification
        if atr_pct >= vol_thresholds['high']:
            vol_regime = "High"
        elif atr_pct <= vol_thresholds['low']:
            vol_regime = "Low"
        else:
            vol_regime = "Medium"
        
        # Momentum classification
        abs_ma_slope = abs(ma_slope)
        if abs_ma_slope >= mom_thresholds['strong']:
            mom_regime = "Strong"
        elif abs_ma_slope <= mom_thresholds['weak']:
            mom_regime = "Weak"
        else:
            mom_regime = "Medium"
        
        # Direction classification
        direction = "Bull" if ma_slope > 0 else "Bear"
        
        # OBI confirmation
        obi_confirms = self._check_obi_confirmation(obi_value, direction)
        
        # Note: Spread regime classification removed as it was mainly used for TIGHT_SPREAD
        # which disables all strategies. We now focus on vol/momentum-based state mapping.
        
        # Combine classifications into final state
        final_state = self._combine_classifications(
            vol_regime, mom_regime, direction, obi_confirms
        )
        
        # Apply mode-specific filters
        if self.detector_mode == 'legacy':
            final_state = self._apply_legacy_filters(final_state, signals)
        
        # Special states don't need validation
        if final_state in ["Unknown", "Filter_Off"]:
            return final_state
            
        # Validate state if needed
        if validate_gms_state(final_state):
            return final_state
        else:
            self.logger.warning(f"Invalid GMS state: {final_state}, returning Unknown")
            return "Unknown"
    
    def _get_current_vol_thresholds(self) -> Dict[str, float]:
        """Get current volatility thresholds (fixed or adaptive)."""
        if self.adaptive_vol_threshold is not None:
            try:
                # Note: AdaptiveThreshold.update() returns current thresholds
                # We can get the current values without updating by calling with None
                # But since we don't have a get method, just use fixed thresholds for now
                # TODO: Implement proper adaptive threshold interface
                pass
            except Exception as e:
                self.logger.error(f"Error getting adaptive vol thresholds: {e}")
        
        # Use fixed thresholds (adaptive interface needs improvement)
        return {
            'low': self.thresholds['vol_low'],
            'high': self.thresholds['vol_high']
        }
    
    def _get_current_mom_thresholds(self) -> Dict[str, float]:
        """Get current momentum thresholds (fixed or adaptive)."""
        if self.adaptive_mom_threshold is not None:
            try:
                # Note: AdaptiveThreshold.update() returns current thresholds
                # We can get the current values without updating by calling with None
                # But since we don't have a get method, just use fixed thresholds for now
                # TODO: Implement proper adaptive threshold interface
                pass
            except Exception as e:
                self.logger.error(f"Error getting adaptive mom thresholds: {e}")
        
        # Use fixed thresholds (adaptive interface needs improvement)
        return {
            'weak': self.thresholds['mom_weak'],
            'strong': self.thresholds['mom_strong']
        }
    
    def _check_obi_confirmation(self, obi_value: float, direction: str) -> bool:
        """Check if OBI confirms the direction."""
        if direction == "Bull":
            return obi_value >= self.obi_weak_confirm_thresh
        else:  # Bear
            return obi_value <= -self.obi_weak_confirm_thresh
    
    def _classify_spread_regime(self, spread_mean: float, spread_std: float) -> str:
        """Classify spread regime based on mean and standard deviation."""
        if spread_std >= self.thresholds['spread_std_high']:
            return "Volatile"
        elif spread_mean <= self.thresholds['spread_mean_low']:
            return "Tight"
        else:
            return "Normal"
    
    def _combine_classifications(
        self,
        vol_regime: str,
        mom_regime: str,
        direction: str,
        obi_confirms: bool
    ) -> str:
        """
        Combine individual classifications into final GMS state.
        
        This follows the original GMS logic for state determination.
        Maps to the expected state constants from state_mapping.
        """
        # High volatility states
        if vol_regime == "High":
            if mom_regime == "Strong":
                if obi_confirms:
                    # Map to standard states
                    if direction == "Bull":
                        return "Strong_Bull_Trend"
                    else:
                        return "Strong_Bear_Trend"
                else:
                    return "High_Vol_Range"
            else:
                return "High_Vol_Range"
        
        # Low volatility states  
        elif vol_regime == "Low":
            # CRITICAL FIX: Don't return TIGHT_SPREAD as it disables ALL strategies
            # Instead, return Low_Vol_Range for low volatility conditions
            if mom_regime == "Weak":
                return "Low_Vol_Range"
            else:
                # Map to weak trend states for stronger momentum
                if direction == "Bull":
                    return "Weak_Bull_Trend"
                else:
                    return "Weak_Bear_Trend"
        
        # Medium volatility states
        else:
            if mom_regime == "Strong" and obi_confirms:
                # Map to strong trend states
                if direction == "Bull":
                    return "Strong_Bull_Trend"
                else:
                    return "Strong_Bear_Trend"
            elif mom_regime == "Medium":
                # Map to weak trend states
                if direction == "Bull":
                    return "Weak_Bull_Trend"
                else:
                    return "Weak_Bear_Trend"
            else:
                return "Uncertain"
    
    def _apply_legacy_filters(self, state: str, signals: dict) -> str:
        """Apply legacy mode-specific filters."""
        # ADX confirmation
        if self.use_adx_confirmation:
            adx_value = signals.get('adx', np.nan)
            if not pd.isna(adx_value) and adx_value < 25:
                # Low ADX indicates ranging market
                if "Trending" in state:
                    state = state.replace("Trending", "Ranging")
        
        # Funding rate filter
        if self.use_funding_confirmation:
            funding_rate = signals.get('funding_rate', np.nan)
            if not pd.isna(funding_rate):
                # Extreme funding can indicate overheated market
                if abs(funding_rate) > 0.001:  # 0.1% threshold
                    if "Normal_Market" in state:
                        state = "High_Vol_Ranging"
        
        return state
    
    def _calculate_risk_suppression(self, signals: dict) -> bool:
        """
        Calculate risk suppression for continuous mode.
        
        Risk is suppressed when:
        - Position size exceeds threshold of volatility-adjusted notional
        - Recent realized PnL exceeds ATR multiple
        """
        if self.detector_mode != 'continuous':
            return False
        
        try:
            # Get required signals
            atr_pct = signals.get(self.ATR_PCT_COL, np.nan)
            position_size = signals.get('position_size_usd', 0)
            realized_pnl = signals.get('realized_pnl_usd', 0)
            close_price = signals.get('close', np.nan)
            
            if pd.isna(atr_pct) or pd.isna(close_price):
                return False
            
            # Check position size threshold
            atr_dollar = close_price * atr_pct
            position_threshold = atr_dollar * self.risk_suppressed_notional_frac
            
            if abs(position_size) > position_threshold:
                self.logger.debug(f"Risk suppressed: position size {position_size} > threshold {position_threshold}")
                return True
            
            # Check PnL threshold
            pnl_threshold = atr_dollar * self.risk_suppressed_pnl_atr_mult
            
            if abs(realized_pnl) > pnl_threshold:
                self.logger.debug(f"Risk suppressed: realized PnL {realized_pnl} > threshold {pnl_threshold}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error calculating risk suppression: {e}")
            return False
    
    def _collapse_state(self, state: str) -> str:
        """Collapse 8-state to 3 or 4-state model if configured."""
        if not self.state_collapse_map:
            return state
        
        # Use state mapping
        for pattern, collapsed_state in self.state_collapse_map.items():
            if pattern in state:
                return collapsed_state
        
        # Default mapping if not found
        if "Bull" in state:
            return "BULL"
        elif "Bear" in state:
            return "BEAR"
        else:
            return "CHOP"
    
    def prime_adaptive_thresholds(self, historical_data: pd.DataFrame):
        """
        Prime adaptive thresholds with historical data.
        
        This method supports both batch priming (optimized) and 
        sequential priming (fallback) based on available implementation.
        """
        if self.adaptive_vol_threshold is None:
            return
        
        try:
            # Check if optimized batch priming is available
            if hasattr(self.adaptive_vol_threshold, 'batch_prime'):
                # Use optimized batch priming
                vol_data = historical_data[self.ATR_PCT_COL].dropna()
                mom_col = 'ma_slope_ema_30s' if 'ma_slope_ema_30s' in historical_data else 'ma_slope'
                mom_data = historical_data[mom_col].abs().dropna()
                
                self.logger.info("Using optimized batch priming for adaptive thresholds")
                self.adaptive_vol_threshold.batch_prime(vol_data.values)
                self.adaptive_mom_threshold.batch_prime(mom_data.values)
            else:
                # Fallback to sequential priming
                self.logger.info("Using sequential priming for adaptive thresholds")
                for _, row in historical_data.iterrows():
                    if not pd.isna(row.get(self.ATR_PCT_COL)):
                        self.adaptive_vol_threshold.update(row[self.ATR_PCT_COL])
                    
                    mom_col = 'ma_slope_ema_30s' if 'ma_slope_ema_30s' in row else 'ma_slope'
                    if not pd.isna(row.get(mom_col)):
                        self.adaptive_mom_threshold.update(abs(row[mom_col]))
            
            self.logger.info("Adaptive threshold priming completed")
            
        except Exception as e:
            self.logger.error(f"Error priming adaptive thresholds: {e}")
            # Fallback to fixed thresholds
            self.adaptive_vol_threshold = None
            self.adaptive_mom_threshold = None