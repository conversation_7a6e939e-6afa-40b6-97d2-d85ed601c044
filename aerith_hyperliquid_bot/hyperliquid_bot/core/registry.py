"""
Component Registry for Trading Bot System.

This registry allows components to be registered and retrieved by type and name,
enabling clean separation between legacy and modern systems.
"""
import logging
from typing import Dict, Type, Any, Optional, List
from .interfaces import (
    IRegimeDetector, IStrategy, IDataLoader, 
    IRiskManager, IExecutionEngine, ISystemComponent
)


logger = logging.getLogger(__name__)


class ComponentRegistry:
    """
    Central registry for all system components.
    
    Components are registered by interface type and name, allowing
    multiple implementations of the same interface to coexist.
    """
    
    def __init__(self):
        """Initialize empty registry."""
        self._components: Dict[Type, Dict[str, Any]] = {
            IRegimeDetector: {},
            IStrategy: {},
            IDataLoader: {},
            IRiskManager: {},
            IExecutionEngine: {},
            ISystemComponent: {}
        }
        self._metadata: Dict[str, Dict[str, Any]] = {}
        
    def register(self, 
                interface_type: Type,
                name: str,
                component_class: Type,
                metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Register a component implementation.
        
        Args:
            interface_type: The interface this component implements
            name: Unique name for this implementation
            component_class: The class implementing the interface
            metadata: Optional metadata about the component
        """
        if interface_type not in self._components:
            raise ValueError(f"Unknown interface type: {interface_type}")
            
        if name in self._components[interface_type]:
            logger.warning(f"Overwriting existing component: {interface_type.__name__}.{name}")
            
        self._components[interface_type][name] = component_class
        
        # Store metadata
        full_name = f"{interface_type.__name__}.{name}"
        self._metadata[full_name] = metadata or {}
        
        logger.info(f"Registered component: {full_name}")
        
    def get(self, interface_type: Type, name: str) -> Type:
        """
        Get a registered component class.
        
        Args:
            interface_type: The interface type
            name: The component name
            
        Returns:
            The component class
            
        Raises:
            KeyError: If component not found
        """
        if interface_type not in self._components:
            raise KeyError(f"Unknown interface type: {interface_type}")
            
        if name not in self._components[interface_type]:
            available = list(self._components[interface_type].keys())
            raise KeyError(
                f"Component '{name}' not found for {interface_type.__name__}. "
                f"Available: {available}"
            )
            
        return self._components[interface_type][name]
        
    def create(self, 
              interface_type: Type,
              name: str,
              config: Any,
              **kwargs) -> Any:
        """
        Create an instance of a registered component.
        
        Args:
            interface_type: The interface type
            name: The component name
            config: Configuration object
            **kwargs: Additional arguments for constructor
            
        Returns:
            Instance of the component
        """
        component_class = self.get(interface_type, name)
        
        try:
            instance = component_class(config, **kwargs)
            logger.info(f"Created instance of {interface_type.__name__}.{name}")
            return instance
        except Exception as e:
            logger.error(f"Failed to create {interface_type.__name__}.{name}: {e}")
            raise
            
    def list_components(self, interface_type: Optional[Type] = None) -> Dict[str, List[str]]:
        """
        List all registered components.
        
        Args:
            interface_type: Optional filter by interface type
            
        Returns:
            Dict mapping interface names to component lists
        """
        if interface_type:
            return {
                interface_type.__name__: list(self._components[interface_type].keys())
            }
        else:
            return {
                iface.__name__: list(components.keys())
                for iface, components in self._components.items()
                if components
            }
            
    def get_metadata(self, interface_type: Type, name: str) -> Dict[str, Any]:
        """
        Get metadata for a component.
        
        Args:
            interface_type: The interface type
            name: The component name
            
        Returns:
            Component metadata
        """
        full_name = f"{interface_type.__name__}.{name}"
        return self._metadata.get(full_name, {})
        
    def clear(self) -> None:
        """Clear all registrations."""
        for component_dict in self._components.values():
            component_dict.clear()
        self._metadata.clear()
        logger.info("Registry cleared")


# Global registry instance
_registry = ComponentRegistry()


def register_component(interface_type: Type,
                      name: str,
                      component_class: Type,
                      metadata: Optional[Dict[str, Any]] = None) -> None:
    """
    Register a component in the global registry.
    
    Args:
        interface_type: The interface this component implements
        name: Unique name for this implementation
        component_class: The class implementing the interface
        metadata: Optional metadata about the component
    """
    _registry.register(interface_type, name, component_class, metadata)


def get_component(interface_type: Type, name: str) -> Type:
    """
    Get a component class from the global registry.
    
    Args:
        interface_type: The interface type
        name: The component name
        
    Returns:
        The component class
    """
    return _registry.get(interface_type, name)


def create_component(interface_type: Type,
                    name: str,
                    config: Any,
                    **kwargs) -> Any:
    """
    Create a component instance from the global registry.
    
    Args:
        interface_type: The interface type
        name: The component name
        config: Configuration object
        **kwargs: Additional constructor arguments
        
    Returns:
        Component instance
    """
    return _registry.create(interface_type, name, config, **kwargs)


def list_components(interface_type: Optional[Type] = None) -> Dict[str, List[str]]:
    """
    List all components in the global registry.
    
    Args:
        interface_type: Optional filter by interface type
        
    Returns:
        Dict mapping interface names to component lists
    """
    return _registry.list_components(interface_type)


def get_registry() -> ComponentRegistry:
    """Get the global registry instance."""
    return _registry


# Registration decorators for convenience
def regime_detector(name: str, **metadata):
    """Decorator to register a regime detector."""
    def decorator(cls):
        register_component(IRegimeDetector, name, cls, metadata)
        return cls
    return decorator


def strategy(name: str, **metadata):
    """Decorator to register a strategy."""
    def decorator(cls):
        register_component(IStrategy, name, cls, metadata)
        return cls
    return decorator


def data_loader(name: str, **metadata):
    """Decorator to register a data loader."""
    def decorator(cls):
        register_component(IDataLoader, name, cls, metadata)
        return cls
    return decorator


def risk_manager(name: str, **metadata):
    """Decorator to register a risk manager."""
    def decorator(cls):
        register_component(IRiskManager, name, cls, metadata)
        return cls
    return decorator


def execution_engine(name: str, **metadata):
    """Decorator to register an execution engine."""
    def decorator(cls):
        register_component(IExecutionEngine, name, cls, metadata)
        return cls
    return decorator