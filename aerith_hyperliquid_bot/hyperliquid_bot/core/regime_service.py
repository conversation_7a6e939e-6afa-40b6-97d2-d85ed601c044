"""
Regime Detector Service Module

Provides a singleton service for managing regime detection across the system.
This ensures a single source of truth for regime data and clean separation
of concerns.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List, Union

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.interfaces import IRegimeProvider
from hyperliquid_bot.core.detector import RegimeDetectorInterface
from hyperliquid_bot.core.detector_factory import get_regime_detector


class RegimeDetectorService(IRegimeProvider):
    """
    Singleton service managing regime detection.
    
    This service provides a single source of truth for regime data across
    the entire system. It maintains historical snapshots and provides a
    clean interface for strategies to access regime information.
    
    Following SOLID principles:
    - Single Responsibility: Manages regime detection and history
    - Open/Closed: Can be extended without modifying existing code
    - Dependency Inversion: Implements IRegimeProvider interface
    """
    
    _instance = None
    _initialized = False
    
    def __new__(cls, config: Config, detector: Optional[RegimeDetectorInterface] = None):
        """
        Ensure singleton instance.
        
        Args:
            config: Application configuration
            detector: Optional pre-configured regime detector
        """
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, config: Config, detector: Optional[RegimeDetectorInterface] = None):
        """
        Initialize the service (only once due to singleton).
        
        Args:
            config: Application configuration
            detector: Optional pre-configured regime detector
        """
        # Prevent re-initialization
        if RegimeDetectorService._initialized:
            return
            
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Use provided detector or create one
        self.detector = detector
        if self.detector is None:
            self.detector = get_regime_detector(config)
            self.logger.info("Created new regime detector")
        else:
            self.logger.info("Using provided regime detector")
        
        # Initialize snapshot cache
        self._snapshots: List[Dict[str, Any]] = []
        self._max_history = 1000  # Configurable cache size
        
        # Mark as initialized
        RegimeDetectorService._initialized = True
        self.logger.info("RegimeDetectorService initialized")
    
    def get_current_regime(self, timestamp: Optional[Union[datetime, float, int]] = None) -> Dict[str, Any]:
        """
        Get the current or most recent regime snapshot.
        
        Args:
            timestamp: Optional timestamp to get regime at specific time.
                      If None, returns the most recent snapshot.
        
        Returns:
            Regime snapshot dictionary
        """
        # No snapshots available - return safe default
        if not self._snapshots:
            self.logger.debug("No regime snapshots available, returning default")
            return {
                'state': 'Unknown',
                'risk_suppressed': True,  # Conservative default
                'timestamp': timestamp or datetime.now(),
                'source': 'default'
            }
        
        # Return most recent if no timestamp specified
        if timestamp is None:
            return self._snapshots[-1].copy()
        
        # Convert timestamp to datetime for comparison
        if isinstance(timestamp, (int, float)):
            timestamp = datetime.fromtimestamp(timestamp)
        
        # Find snapshot at or before requested timestamp
        valid_snapshots = []
        for snapshot in self._snapshots:
            snapshot_time = snapshot.get('timestamp')
            
            # Convert snapshot timestamp if needed
            if isinstance(snapshot_time, (int, float)):
                snapshot_time = datetime.fromtimestamp(snapshot_time)
            
            # Handle timezone awareness
            if hasattr(timestamp, 'tzinfo') and hasattr(snapshot_time, 'tzinfo'):
                if timestamp.tzinfo is not None and snapshot_time.tzinfo is None:
                    snapshot_time = snapshot_time.replace(tzinfo=timestamp.tzinfo)
                elif timestamp.tzinfo is None and snapshot_time.tzinfo is not None:
                    timestamp = timestamp.replace(tzinfo=snapshot_time.tzinfo)
            
            # Check if snapshot is valid for requested time
            try:
                if snapshot_time <= timestamp:
                    valid_snapshots.append(snapshot)
            except TypeError:
                self.logger.warning(f"Type error comparing timestamps: {type(snapshot_time)} vs {type(timestamp)}")
        
        # Return most recent valid snapshot or first available
        if valid_snapshots:
            return valid_snapshots[-1].copy()
        else:
            self.logger.debug(f"No snapshots before {timestamp}, returning oldest")
            return self._snapshots[0].copy() if self._snapshots else {
                'state': 'Unknown',
                'risk_suppressed': True,
                'timestamp': timestamp,
                'source': 'default'
            }
    
    def get_regime_history(self, lookback: int) -> List[Dict[str, Any]]:
        """
        Get historical regime snapshots.
        
        Args:
            lookback: Number of historical snapshots to return
        
        Returns:
            List of regime snapshots in chronological order
        """
        if lookback <= 0:
            return []
        
        # Return copies to prevent external modification
        return [s.copy() for s in self._snapshots[-lookback:]]
    
    def update(self, signals: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update regime detection with new market signals.
        
        Args:
            signals: Dictionary of market signals
        
        Returns:
            The newly created regime snapshot
        """
        # Get timestamp from signals or use current time
        timestamp = signals.get('timestamp', datetime.now())
        
        # Convert timestamp to datetime if needed
        if isinstance(timestamp, (int, float)):
            timestamp = datetime.fromtimestamp(timestamp)
        
        # Get regime from detector
        regime = self.detector.get_regime(signals)
        
        # Create snapshot based on detector return type
        if isinstance(regime, dict):
            snapshot = regime.copy()
        else:
            # Legacy string return - convert to dict
            snapshot = {
                'state': regime,
                'risk_suppressed': False  # Default for legacy
            }
        
        # Ensure required fields
        snapshot['timestamp'] = timestamp
        if 'regime_timestamp' not in snapshot:
            snapshot['regime_timestamp'] = timestamp
        
        # Add metadata
        snapshot['source'] = 'detector'
        snapshot['signal_keys'] = list(signals.keys())
        
        # Cache snapshot
        self._snapshots.append(snapshot)
        
        # Maintain cache size limit
        if len(self._snapshots) > self._max_history:
            self._snapshots = self._snapshots[-self._max_history:]
        
        self.logger.debug(f"Updated regime: {snapshot.get('state')} at {timestamp}")
        
        return snapshot.copy()
    
    def get_regime_at_timestamp(self, timestamp: Union[datetime, float, int]) -> Optional[Dict[str, Any]]:
        """
        Get the regime snapshot that was active at a specific timestamp.
        
        Args:
            timestamp: The timestamp to query
        
        Returns:
            Regime snapshot active at that time, or None if no data available
        """
        if not self._snapshots:
            return None
        
        # Use get_current_regime logic but return None instead of default
        snapshot = self.get_current_regime(timestamp)
        
        # Check if this is a default response
        if snapshot.get('source') == 'default':
            return None
        
        return snapshot
    
    def clear_history(self) -> None:
        """Clear historical snapshots (useful for testing)."""
        self._snapshots.clear()
        self.logger.info("Cleared regime history")
    
    @classmethod
    def reset(cls) -> None:
        """
        Reset singleton instance.
        
        This should only be used in testing or when switching between
        backtest runs.
        """
        cls._instance = None
        cls._initialized = False
        
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about regime detection.
        
        Returns:
            Dictionary with statistics like snapshot count, regime distribution, etc.
        """
        if not self._snapshots:
            return {
                'snapshot_count': 0,
                'regime_distribution': {},
                'oldest_snapshot': None,
                'newest_snapshot': None
            }
        
        # Calculate regime distribution
        regime_counts = {}
        for snapshot in self._snapshots:
            state = snapshot.get('state', 'Unknown')
            regime_counts[state] = regime_counts.get(state, 0) + 1
        
        return {
            'snapshot_count': len(self._snapshots),
            'regime_distribution': regime_counts,
            'oldest_snapshot': self._snapshots[0].get('timestamp'),
            'newest_snapshot': self._snapshots[-1].get('timestamp'),
            'cache_capacity': self._max_history,
            'cache_usage_pct': (len(self._snapshots) / self._max_history) * 100
        }