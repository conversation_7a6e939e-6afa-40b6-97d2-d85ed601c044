"""
GMS Provider Module

This module provides a class for accessing GMS (Granular Market Structure) snapshots
with timestamp-based filtering to ensure no look-ahead bias.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, Union, List

import pandas as pd

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.detector import RegimeDetectorInterface
from hyperliquid_bot.core.detector_factory import get_regime_detector


class GMSProvider:
    """
    Provider for GMS (Granular Market Structure) snapshots.

    This class provides methods to access GMS snapshots with timestamp-based
    filtering to ensure no look-ahead bias.
    """

    def __init__(self, config: Config, detector: Optional[RegimeDetectorInterface] = None):
        """
        Initialize the GMS Provider.

        Args:
            config: The application configuration
            detector: Optional regime detector instance. If None, one will be created.
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)

        # Initialize detector if not provided
        self.detector = detector
        if self.detector is None:
            self.detector = get_regime_detector(config)

        # Initialize snapshot cache
        self._snapshots: List[Dict[str, Any]] = []

    def latest(self, timestamp: Optional[Union[datetime, float, int]] = None) -> Dict[str, Any]:
        """
        Get the latest GMS snapshot available at or before the given timestamp.

        Args:
            timestamp: Optional timestamp to filter snapshots. If None, returns the most recent snapshot.
                      Can be a datetime object or a timestamp (float/int).

        Returns:
            Dict containing the GMS snapshot with at least 'state', 'risk_suppressed', and 'timestamp' keys.
            If no snapshot is available, returns a default snapshot with 'state' = 'Unknown'.
        """
        # If no snapshots available, return a default snapshot
        if not self._snapshots:
            self.logger.warning("No GMS snapshots available")
            return {
                'state': 'Unknown',
                'risk_suppressed': True,  # Conservative approach
                'timestamp': datetime.now() if timestamp is None else timestamp
            }

        # If no timestamp provided, return the most recent snapshot
        if timestamp is None:
            return self._snapshots[-1]

        # Convert timestamp to datetime if needed
        if isinstance(timestamp, (int, float)):
            timestamp = datetime.fromtimestamp(timestamp)

        # Filter snapshots by timestamp
        valid_snapshots = []
        for s in self._snapshots:
            s_timestamp = s.get('timestamp', datetime.now())
            # Convert timestamp to datetime if it's a float/int
            if isinstance(s_timestamp, (int, float)):
                s_timestamp = datetime.fromtimestamp(s_timestamp)

            # Ensure both timestamps have the same timezone
            if hasattr(timestamp, 'tzinfo') and hasattr(s_timestamp, 'tzinfo'):
                if timestamp.tzinfo is not None and s_timestamp.tzinfo is None:
                    # Convert s_timestamp to the same timezone as timestamp
                    s_timestamp = s_timestamp.replace(tzinfo=timestamp.tzinfo)
                elif timestamp.tzinfo is None and s_timestamp.tzinfo is not None:
                    # Convert timestamp to the same timezone as s_timestamp
                    timestamp = timestamp.replace(tzinfo=s_timestamp.tzinfo)

            try:
                if s_timestamp <= timestamp:
                    valid_snapshots.append(s)
            except TypeError:
                self.logger.warning(f"Type error comparing timestamps: {type(s_timestamp)} vs {type(timestamp)}")
                # Skip this snapshot if comparison fails

        # If no valid snapshots, return a default snapshot
        if not valid_snapshots:
            self.logger.warning(f"No GMS snapshots available before {timestamp}")
            return {
                'state': 'Unknown',
                'risk_suppressed': True,  # Conservative approach
                'timestamp': timestamp
            }

        # Get the most recent valid snapshot
        snapshot = valid_snapshots[-1]

        # Calculate and log age of snapshot for debugging
        snapshot_time = snapshot.get('timestamp')
        if snapshot_time is not None:
            # Ensure both are datetime objects
            if isinstance(timestamp, (int, float)):
                timestamp = datetime.fromtimestamp(timestamp)
            if isinstance(snapshot_time, (int, float)):
                snapshot_time = datetime.fromtimestamp(snapshot_time)

            # Ensure both timestamps have the same timezone
            if hasattr(timestamp, 'tzinfo') and hasattr(snapshot_time, 'tzinfo'):
                if timestamp.tzinfo is not None and snapshot_time.tzinfo is None:
                    # Convert snapshot_time to the same timezone as timestamp
                    snapshot_time = snapshot_time.replace(tzinfo=timestamp.tzinfo)
                elif timestamp.tzinfo is None and snapshot_time.tzinfo is not None:
                    # Convert timestamp to the same timezone as snapshot_time
                    timestamp = timestamp.replace(tzinfo=snapshot_time.tzinfo)

            # Calculate age in seconds
            age_sec = (timestamp - snapshot_time).total_seconds()

            # Log the age for debugging
            self.logger.debug(f"GMS age = {age_sec:.1f}s (snapshot: {snapshot_time}, current: {timestamp})")

            # Add age to snapshot for convenience
            snapshot['age_sec'] = age_sec

        return snapshot

    def update(self, signals: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update the GMS detector with new signals and cache the snapshot.

        Args:
            signals: Dictionary of market signals

        Returns:
            The latest GMS snapshot
        """
        # Get current timestamp from signals or use current time
        timestamp = signals.get('timestamp', datetime.now())

        # Convert timestamp to datetime if needed
        if isinstance(timestamp, (int, float)):
            timestamp = datetime.fromtimestamp(timestamp)

        # Get regime from detector
        regime = self.detector.get_regime(signals)

        # Create snapshot
        snapshot: Dict[str, Any] = {}

        # Handle different return types from detector
        if isinstance(regime, dict):
            snapshot = regime.copy()
        else:
            # Legacy mode - regime is a string
            snapshot = {
                'state': regime,
                'risk_suppressed': False  # Default for legacy mode
            }

        # Add timestamp to snapshot
        snapshot['timestamp'] = timestamp

        # Add regime_timestamp if not present
        if 'regime_timestamp' not in snapshot:
            snapshot['regime_timestamp'] = timestamp

        # Cache snapshot
        self._snapshots.append(snapshot)

        # Limit cache size (keep last 1000 snapshots)
        if len(self._snapshots) > 1000:
            self._snapshots = self._snapshots[-1000:]

        return snapshot

    def clear_cache(self) -> None:
        """Clear the snapshot cache."""
        self._snapshots = []


class GMSValidator:
    """
    Validator for GMS snapshots.

    This is a stub implementation that will be replaced in T-111f.
    """

    @staticmethod
    def is_valid(snapshot: Dict[str, Any]) -> bool:
        """
        Validate a GMS snapshot for stability.

        This is a stub implementation that always returns True.
        Will be replaced in T-111f with proper validation logic.

        Args:
            snapshot: GMS snapshot to validate

        Returns:
            True if the snapshot is valid, False otherwise
        """
        # This is a stub that will be replaced in T-111f
        # For now, always consider the snapshot valid
        return True
