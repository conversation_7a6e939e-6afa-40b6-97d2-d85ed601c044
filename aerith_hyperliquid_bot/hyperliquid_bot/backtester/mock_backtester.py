#!/usr/bin/env python3
"""
Mock implementation of the Backtester class for regression testing.

This module provides a mock implementation of the Backtester class that returns
empty results for regression testing.
"""

import logging
from datetime import datetime
import sys
from pathlib import Path
import json

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mock_backtester")

class MockBacktester:
    """Mock implementation of the Backtester class for regression testing."""
    
    def __init__(self, config):
        """Initialize the mock backtester."""
        self.config = config
        self.logger = logging.getLogger("mock_backtester")
        self.logger.info("Initialized MockBacktester")
        
        # Initialize portfolio with empty trades
        self.portfolio = MockPortfolio()
        
        # Store the strategy name
        self.strategy_name = config.strategy_name if hasattr(config, 'strategy_name') else 'unknown'
    
    def run(self, start_date, end_date):
        """Run the mock backtest."""
        self.logger.info(f"Running mock backtest from {start_date} to {end_date}")
        self.logger.info(f"Strategy: {self.strategy_name}")
        
        # Log that no trades were executed
        self.logger.info("No trades were executed during this backtest period.")
        
        # Return empty results
        return {
            'strategy': self.strategy_name,
            'trades': [],
            'metrics': {
                'return': 0.0,
                'sharpe': 0.0,
                'trade_count': 0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'profit_factor': 0.0
            }
        }

class MockPortfolio:
    """Mock implementation of the Portfolio class for regression testing."""
    
    def __init__(self):
        """Initialize the mock portfolio."""
        self.initial_balance = 10000.0
        self.balance = 10000.0
        self.trades = []
        self.equity_timestamps = []
        self.equity_curve = []
    
    def get_metrics(self):
        """Get portfolio metrics."""
        return {
            'return': 0.0,
            'sharpe': 0.0,
            'trade_count': 0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'profit_factor': 0.0
        }

def get_mock_backtester(original_class):
    """
    Return a function that returns a MockBacktester instance.
    
    This function is used to replace the original Backtester class with the MockBacktester.
    """
    def mock_backtester_factory(config):
        """Factory function that returns a MockBacktester instance."""
        logger.info(f"Creating MockBacktester instead of {original_class.__name__}")
        return MockBacktester(config)
    
    return mock_backtester_factory

def apply_mock():
    """Apply the mock implementation to the Backtester class."""
    from aerith_hyperliquid_bot.hyperliquid_bot.backtester.backtester import Backtester
    
    # Save the original class
    original_backtester = Backtester
    
    # Replace the Backtester class with the mock implementation
    sys.modules['aerith_hyperliquid_bot.hyperliquid_bot.backtester.backtester'].Backtester = get_mock_backtester(original_backtester)
    
    logger.info("Applied mock implementation to Backtester class")
    
    return original_backtester

def restore_original(original_class):
    """Restore the original Backtester class."""
    from aerith_hyperliquid_bot.hyperliquid_bot.backtester.backtester import Backtester
    
    # Restore the original class
    sys.modules['aerith_hyperliquid_bot.hyperliquid_bot.backtester.backtester'].Backtester = original_class
    
    logger.info("Restored original Backtester class")

if __name__ == "__main__":
    original = apply_mock()
    logger.info("Mock backtester applied. You can now run your backtest.")
    
    # Example of how to restore the original class
    # restore_original(original)
