"""
Modern Backtester Routing Patch
================================

This module contains the routing logic to use the modern backtesting engine
when system_mode is set to "modern" in the configuration.

To integrate this into run_backtest.py:
1. Import this module
2. Check config for system_mode
3. Route to modern engine if system_mode == "modern"
"""

import logging
from datetime import datetime
from typing import <PERSON>ple
from pathlib import Path

from ..config.settings import Config
from ..modern.backtester_engine import ModernBacktestEngine


def should_use_modern_engine(config: Config) -> bool:
    """
    Check if we should use the modern backtesting engine.
    
    Args:
        config: Configuration object
        
    Returns:
        True if modern engine should be used
    """
    # Check for system_mode in config
    if hasattr(config, 'system_mode'):
        return config.system_mode == 'modern'
    
    # Also check regime detector type as fallback
    if hasattr(config, 'regime') and hasattr(config.regime, 'detector_type'):
        return config.regime.detector_type == 'continuous_modern'
    
    return False


def run_modern_backtest(config: Config, 
                       start_date: datetime, 
                       end_date: datetime,
                       logger: logging.Logger) -> None:
    """
    Run backtest using the modern engine.
    
    Args:
        config: Configuration object
        start_date: Backtest start date
        end_date: Backtest end date
        logger: Logger instance
    """
    logger.info("=" * 80)
    logger.info("USING MODERN BACKTESTING ENGINE")
    logger.info("=" * 80)
    logger.info("Architecture:")
    logger.info("  - 60-second regime updates for market awareness")
    logger.info("  - Hourly trading evaluation for position decisions")
    logger.info("  - Modern continuous detector with fixed thresholds")
    logger.info("  - Modern TF-v3 strategy with regime history")
    logger.info("=" * 80)
    
    try:
        # Create modern backtesting engine
        engine = ModernBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date
        )
        
        # Run backtest
        results = engine.run_backtest()
        
        # Log results
        logger.info("=" * 80)
        logger.info("MODERN BACKTEST RESULTS")
        logger.info("=" * 80)
        logger.info(f"Total Trades: {results['performance']['total_trades']}")
        logger.info(f"Total Return: {results['performance']['total_return']:.2%}")
        logger.info(f"Win Rate: {results['performance']['win_rate']:.2%}")
        logger.info(f"Runtime: {results['runtime_seconds']:.1f} seconds")
        logger.info("=" * 80)
        
        # Print first few trades
        if results['trades']:
            logger.info("First 5 trades:")
            for i, trade in enumerate(results['trades'][:5]):
                logger.info(
                    f"  {i+1}. {trade['timestamp']} - "
                    f"{trade['direction'].upper()} @ {trade['entry_price']:.2f} "
                    f"(size: {trade['position_size']:.2%})"
                )
        else:
            logger.warning("No trades generated - check regime thresholds!")
            
    except Exception as e:
        logger.error(f"Modern backtest failed: {e}", exc_info=True)
        raise


def patch_main_backtester():
    """
    Instructions for patching the main backtester.
    
    In run_backtest.py, replace the backtester initialization section with:
    
    ```python
    # 9. Initialize and Run Backtester (using validated config)
    try:
        # Check if we should use modern engine
        from hyperliquid_bot.backtester.run_backtest_modern_patch import (
            should_use_modern_engine, run_modern_backtest
        )
        
        if should_use_modern_engine(config):
            # Use modern backtesting engine
            run_modern_backtest(config, start_date, end_date, logger)
        else:
            # Use legacy backtesting engine
            logger.info("Initializing Backtester instance...")
            backtester = Backtester(config)
            logger.info("Running backtest...")
            backtester.run(start_date, end_date)
            
    except Exception as e:
        # Catch critical errors during backtester init or run
        logger.critical(f"Critical error during backtester initialization or run: {e}", exc_info=True)
        print(f"CRITICAL ERROR: {e}. Check logs for details.")
        sys.exit(1)
    ```
    """
    pass