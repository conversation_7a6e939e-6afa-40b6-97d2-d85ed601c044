# aerith_hyperliquid_bot/run_backtest.py

import logging
import logging.config
import sys
import os
from datetime import datetime, timedelta  # Added missing import
from pathlib import Path
import re # Import re for filename parsing
import argparse # For command-line arguments
import yaml     # For loading raw config files
from deepmerge import always_merger # For merging config dicts

# Ensure the package root is in the Python path
# This allows importing from hyperliquid_bot even when run from the root directory
#project_root = Path(__file__).parent.resolve()
#sys.path.insert(0, str(project_root))

# Import necessary components from our package
from hyperliquid_bot.config.settings import Config # Only import Config model now
from hyperliquid_bot.backtester import Backtester
# Import system validation module for startup validation
from hyperliquid_bot.utils.system_validation import perform_startup_validation

# --- Global Logger Setup ---
# Configure logging early, before components are initialized

def setup_logging(config: Config, run_id: str | None = None):
    """Configures logging for the application."""
    import sys

    log_dir = config.data_paths.log_dir
    if not log_dir.exists():
        try:
            log_dir.mkdir(parents=True, exist_ok=True)
            print(f"Created log directory: {log_dir}")
        except Exception as e:
            print(f"Error creating log directory {log_dir}: {e}. Logging to console only.")
            logging.basicConfig(level=logging.INFO, format='[%(levelname)-7s] %(name)-20s: %(message)s')
            return

    if run_id:
        # Sanitize run_id for filename (replace spaces, slashes, etc.)
        safe_run_id = "".join(c if c.isalnum() or c in ('-', '_') else '_' for c in run_id)
        log_file_name = f"backtest_run_{safe_run_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    else:
        log_file_name = f"backtest_run_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    log_file_path = log_dir / log_file_name

    # Set specific logger levels BEFORE configuring the root logger
    # This will silence matplotlib's debug messages
    logging.getLogger("matplotlib").setLevel(logging.WARNING)
    logging.getLogger("matplotlib.font_manager").setLevel(logging.WARNING)
    logging.getLogger("matplotlib.pyplot").setLevel(logging.WARNING)
    logging.getLogger("PIL").setLevel(logging.WARNING)  # Also silence Pillow if used by matplotlib

    # Create a root logger with no handlers
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)  # Set root logger to DEBUG to capture everything

    # Remove any existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Create file handler with detailed format
    file_format = '%(asctime)s [%(levelname)-7s] %(name)-25s: %(message)s'
    file_formatter = logging.Formatter(file_format, datefmt='%Y-%m-%d %H:%M:%S')
    file_handler = logging.FileHandler(log_file_path, mode='w')
    file_handler.setLevel(logging.DEBUG)  # Log everything to file
    file_handler.setFormatter(file_formatter)

    # Create console handler with simpler format
    console_format = '[%(levelname)-7s] %(name)-20s: %(message)s'
    console_formatter = logging.Formatter(console_format)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)  # Only show INFO and above on console
    console_handler.setFormatter(console_formatter)

    # Add both handlers to root logger
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    # Set module-specific log levels - add or adjust as needed
    logging.getLogger("Backtester").setLevel(logging.DEBUG)
    logging.getLogger("Portfolio").setLevel(logging.INFO)
    logging.getLogger("ExecutionSimulator").setLevel(logging.INFO)
    logging.getLogger("RiskManager").setLevel(logging.INFO)
    logging.getLogger("StrategyEvaluator").setLevel(logging.INFO)
    logging.getLogger("MeanReversionStrategy").setLevel(logging.INFO)
    logging.getLogger("RuleBasedRegimeDetector").setLevel(logging.INFO)
    logging.getLogger("SignalEngine").setLevel(logging.DEBUG)
    logging.getLogger("hyperliquid_bot.data.handler").setLevel(logging.DEBUG)

    root_logger.info(f"Logging initialized. Log file: {log_file_path}")


# --- Function to Determine Backtest Dates ---

def determine_backtest_dates(config: Config) -> tuple[datetime, datetime]:
    """Determines the start and end dates for the backtest based on config settings."""
    preset = config.backtest.period_preset
    logger = logging.getLogger(__name__) # Use logger from main scope

    if preset == 'custom':
        start_date = config.backtest.custom_start_date
        end_date = config.backtest.custom_end_date
        if start_date is None or end_date is None:
            raise ValueError("Custom start/end date required when period_preset is 'custom'")
        logger.info(f"Using custom date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        # Add one day to end_date because slicing/range is often exclusive of the end point
        return start_date, end_date + timedelta(days=1)

    elif preset == 'full':
        logger.info("Determining date range for 'full' preset...")
        # Corrected attribute name based on error message
        data_dir = config.data_paths.ohlcv_base_path / config.timeframe
        if not data_dir.exists() or not data_dir.is_dir():
            raise FileNotFoundError(f"Data directory for 'full' preset not found: {data_dir}")

        dates = []
        # Updated regex to match 'YYYY-MM-DD_*.parquet' format
        date_pattern = re.compile(r"^(\d{4}-\d{2}-\d{2})_.*\.parquet$")
        for filename in os.listdir(data_dir):
            match = date_pattern.match(filename)
            if match:
                try:
                    dates.append(datetime.strptime(match.group(1), '%Y-%m-%d'))
                except ValueError:
                    logger.warning(f"Could not parse date from filename: {filename}")

        if not dates:
            raise ValueError(f"No valid data files found in {data_dir} to determine 'full' date range.")

        start_date = min(dates)
        end_date = max(dates)
        logger.info(f"Determined 'full' date range from data files: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        # Add one day to end_date for inclusive range in loading
        return start_date, end_date + timedelta(days=1)

    elif preset.isdigit() and len(preset) == 4:
        # Assume it's a year like '2024'
        year = int(preset)
        start_date = datetime(year, 1, 1)
        end_date = datetime(year + 1, 1, 1) # End date is exclusive (Jan 1st of next year)
        logger.info(f"Using date range for year {preset}: {start_date.strftime('%Y-%m-%d')} to {(end_date - timedelta(days=1)).strftime('%Y-%m-%d')}")
        return start_date, end_date

    elif (match := re.match(r"^(\d{4})Q([1-4])$", preset)):
        year = int(match.group(1))
        quarter = int(match.group(2))
        logger.info(f"Determining date range for quarter {year}Q{quarter}...")

        start_month = (quarter - 1) * 3 + 1
        start_date = datetime(year, start_month, 1)

        # Calculate end date (last day of the quarter)
        end_month = start_month + 2
        # Find the last day of the end month
        # First day of the next month, then subtract one day
        if end_month == 12:
            next_month_start = datetime(year + 1, 1, 1)
        else:
            next_month_start = datetime(year, end_month + 1, 1)
        # end_date is the last day of the quarter (inclusive)
        end_date_inclusive = next_month_start - timedelta(days=1)
        # Add one day for exclusive slicing
        end_date_exclusive = next_month_start

        logger.info(f"Using date range for {year}Q{quarter}: {start_date.strftime('%Y-%m-%d')} to {end_date_inclusive.strftime('%Y-%m-%d')}")
        return start_date, end_date_exclusive

    else:
        # This case should ideally be caught by Pydantic validation in settings.py
        raise ValueError(f"Invalid period_preset value: {preset}")


# --- Main Execution Block ---

def main():
    """Main function to run the backtest."""
    print("--- Starting Hyperliquid Backtester ---")

    # 1. Parse Command Line Arguments
    parser = argparse.ArgumentParser(description="Run Hyperliquid Backtester")
    parser.add_argument('--override', type=str, default=None,
                        help='Path to a YAML configuration file for overriding base settings')
    parser.add_argument('--timeframe', type=str, default=None, choices=['1h', '4h'],
                        help='Timeframe override (e.g., 1h, 4h). Overrides config file setting.')
    parser.add_argument('--run-id', type=str, default=None,
                        help='Unique identifier for this backtest run, used in log filename.')
    parser.add_argument('--skip-validation-warnings', action='store_true',
                        help='Skip warnings about validation issues and continue running the backtest.')
    args = parser.parse_args()
    print(f"Command line arguments: {args}")

    # 2. Load Base Configuration
    base_config_rel_path = Path('configs/base.yaml') # New default relative path
    project_root_dir = Path(__file__).parent.parent.parent.resolve() # Project root (aerith_hyperliquid_bot)
    base_config_path = project_root_dir / base_config_rel_path

    # Check if base config exists at the primary location relative to project root
    if not base_config_path.is_file():
        # Fallback: Check relative to current working directory (if script is run from elsewhere)
        cwd_path = Path.cwd() / base_config_rel_path
        if cwd_path.is_file():
            base_config_path = cwd_path
        else:
            # Fallback: Check relative to the script's directory (less common but possible)
            script_dir_path = Path(__file__).parent.resolve() / base_config_rel_path
            if script_dir_path.is_file():
                 base_config_path = script_dir_path
            else:
                 print(f"ERROR: Base config file not found at expected locations: "
                       f"{base_config_path} or {cwd_path} or {script_dir_path}")
                 sys.exit(1)

    print(f"Using base configuration file: {base_config_path}")
    try:
        with open(base_config_path, 'r') as f:
            base_cfg = yaml.safe_load(f)
        if not base_cfg:
            raise ValueError(f"Base config file is empty or invalid: {base_config_path}")
    except FileNotFoundError:
        print(f"ERROR: Base config file not found at {base_config_path}")
        sys.exit(1)
    except yaml.YAMLError as e:
        print(f"ERROR: Failed to parse base config YAML: {base_config_path}\n{e}")
        sys.exit(1)
    except Exception as e:
        print(f"ERROR: Unexpected error loading base config: {e}")
        sys.exit(1)

    # 3. Load Override Configuration (if provided) and Merge
    merged_cfg = base_cfg # Default to base if no override
    if args.override:
        override_path = Path(args.override).resolve() # Resolve to absolute path
        if not override_path.is_file():
            print(f"WARNING: Override config file not found: {override_path}. Using base config only.")
        else:
            print(f"Loading override configuration from: {override_path}")
            try:
                with open(override_path, 'r') as f:
                    override_cfg = yaml.safe_load(f)
                if not override_cfg:
                     print(f"WARNING: Override config file is empty or invalid: {override_path}. Ignoring override.")
                else:
                    # Perform deep merge
                    print("Merging base and override configurations...")
                    # Use deepcopy to avoid modifying base_cfg if needed later, though not strictly necessary here
                    # merged_cfg = always_merger.merge(deepcopy(base_cfg), override_cfg)
                    merged_cfg = always_merger.merge(base_cfg, override_cfg) # Simpler merge
                    print("Merge complete.")
            except yaml.YAMLError as e:
                print(f"ERROR: Failed to parse override config YAML: {override_path}. Using base config only.\n{e}")
            except Exception as e:
                print(f"ERROR: Unexpected error loading override config: {override_path}. Using base config only.\n{e}")

    # 4. Apply Timeframe Override (if provided via CLI) BEFORE validation
    if args.timeframe:
        print(f"Overriding timeframe with CLI argument: {args.timeframe}")
        merged_cfg['timeframe'] = args.timeframe # Update the dictionary

    # 5. Validate Merged Configuration using Pydantic
    try:
        print("Validating final configuration...")
        config = Config(**merged_cfg)
        print("Configuration validated successfully.")
    except Exception as e:
        print(f"CRITICAL ERROR: Failed to validate merged configuration: {e}")
        # Optionally print the merged_cfg structure to help debug validation errors
        # import json; print("--- Merged Config Data (for debugging) ---"); print(json.dumps(merged_cfg, indent=2, default=str)); print("--- End Merged Config ---")
        sys.exit(1)

    # 6. Setup Logging (using validated config)
    try:
        setup_logging(config, run_id=args.run_id) # Pass run_id to logging setup
    except Exception as e:
        print(f"ERROR: Failed to set up logging: {e}")
        # Continue without file logging if setup fails, basicConfig might still work for console

    logger = logging.getLogger(__name__) # Get logger for this main script AFTER setup

    # 7. Determine Start and End Dates (using validated config)
    try:
        start_date, end_date = determine_backtest_dates(config)
    except (ValueError, FileNotFoundError) as e:
        logger.critical(f"Error determining backtest dates: {e}", exc_info=True)
        print(f"CRITICAL ERROR: Could not determine backtest dates: {e}")
        sys.exit(1)

    # 8. Log Key Config Settings (using validated config)
    logger.info(f"CONFIG: Base Config Path = {base_config_path}")
    if args.override and override_path.is_file():
        logger.info(f"CONFIG: Override Config Path = {override_path}")
    else:
        logger.info("CONFIG: Override Config Path = Not provided or not found")

    # Run configuration validator to check for fallbacks - FIRST TIME AT TOP
    from hyperliquid_bot.core.config_validator import ConfigValidator
    
    logger.info("=" * 80)
    logger.info("CONFIGURATION FALLBACK CHECK - START OF LOG")
    logger.info("=" * 80)
    
    validator = ConfigValidator(base_config_path=base_config_path, strict_mode=False)
    override_dict = override_cfg if args.override and 'override_cfg' in locals() else None
    fallback_issues = validator.validate_config(config, override_dict)
    
    if not fallback_issues:
        logger.info("✅ No configuration fallbacks detected - using intended settings")
    else:
        error_count = sum(1 for issue in fallback_issues if issue.severity == "ERROR")
        warning_count = sum(1 for issue in fallback_issues if issue.severity == "WARNING")
        logger.error(f"❌ Configuration validation found {error_count} errors, {warning_count} warnings")
        
        for issue in fallback_issues:
            if issue.severity == "ERROR":
                logger.error(f"  ❌ {issue.path}: Expected {issue.expected_source}={issue.expected_value}, got {issue.actual_source}={issue.actual_value}")
            elif issue.severity == "WARNING":
                logger.warning(f"  ⚠️ {issue.path}: {issue.actual_source}")
    
    logger.info("=" * 80)
    logger.info("END OF CONFIGURATION FALLBACK CHECK")
    logger.info("=" * 80)

    # Run system validation at startup
    logger.info("Running system-wide configuration validation...")
    all_valid, validation_results = perform_startup_validation(config)

    if not all_valid:
        # Log a summary of validation issues
        issue_count = sum(len(issues) for issues in validation_results.values())
        logger.warning(f"System validation found {issue_count} potential issue(s)")

        # If there are critical issues, ask for confirmation to continue
        critical_validations = ["gms_state_mapping"]
        has_critical_issues = any(val_name in critical_validations and issues
                                 for val_name, issues in validation_results.items())

        if has_critical_issues and not args.skip_validation_warnings:
            print("WARNING: Critical configuration issues detected. See log for details.")
            print("You can run with --skip-validation-warnings to bypass this check.")
            user_input = input("Do you want to continue anyway? (y/N): ")
            if user_input.lower() != 'y':
                logger.info("User chose to abort due to validation issues")
                print("Aborting backtest run due to validation issues")
                sys.exit(0)
            logger.info("User chose to continue despite validation issues")
    else:
        logger.info("All system validation checks passed successfully")

    logger.info(f"CONFIG: Timeframe = {config.timeframe} (Effective)") # Log the final timeframe used
    logger.info(f"CONFIG: Backtest Period = {config.backtest.period_preset}")
    logger.info(f"CONFIG: Determined Start Date = {start_date.strftime('%Y-%m-%d')} (Exclusive end: {end_date.strftime('%Y-%m-%d')})")
    enabled_strats = [k for k, v in config.strategies.model_dump().items() if v]
    logger.info(f"CONFIG: Enabled Strategies = {enabled_strats}")
    logger.info(f"CONFIG: Regime Filter = {config.regime.use_filter}, Detector = {config.regime.detector_type}, Enhanced = {getattr(config.regime, 'use_enhanced_detection', False)}")
    logger.info(f"CONFIG: Strict Filtering = {config.regime.use_strict_strategy_filtering}")
    logger.info(f"CONFIG: Chop Filter = {getattr(config.regime, 'use_chop_filter', False)}")

    # Log state mapping configuration
    map_weak_bear_to_bear = getattr(config.regime, 'map_weak_bear_to_bear', False)
    logger.info(f"CONFIG: GMS State Mapping - Map Weak_Bear_Trend to {'BEAR' if map_weak_bear_to_bear else 'CHOP'}")
    logger.info(f"CONFIG: Map Weak Bear to Bear = {map_weak_bear_to_bear}")

    # Try to import state mapping and check actual mapping
    try:
        from hyperliquid_bot.utils.state_mapping import get_state_map
        state_map = get_state_map(map_weak_bear_to_bear=map_weak_bear_to_bear)
        weak_bear_mapped_to = state_map.get('Weak_Bear_Trend', 'Unknown')
        logger.info(f"CONFIG: Verified Weak_Bear_Trend mapping: {weak_bear_mapped_to}")
    except ImportError:
        logger.warning("Could not import state mapping utilities to verify configuration")
    except Exception as e:
        logger.warning(f"Error verifying state mapping configuration: {e}")


    # Enhanced logging for dynamic risk settings
    logger.info(f"CONFIG: Dynamic Risk Adjustment = {getattr(config.regime, 'dynamic_risk_adjustment', False)}")

    # Log detailed risk factor settings
    if hasattr(config.regime, 'dynamic_risk_adjustment') and config.regime.dynamic_risk_adjustment:
        logger.info(f"CONFIG: Chop Risk Factor = {getattr(config.regime, 'chop_risk_factor', 0.5)}")
        logger.info(f"CONFIG: Chop Leverage Factor = {getattr(config.regime, 'chop_leverage_factor', 0.5)}")
        logger.info(f"CONFIG: Strong Trend Risk Factor = {getattr(config.regime, 'strong_trend_risk_factor', 0.7)}")
        logger.info(f"CONFIG: Strong Trend Leverage Factor = {getattr(config.regime, 'strong_trend_leverage_factor', 0.8)}")
        logger.info(f"CONFIG: Weak Trend Risk Scale = {getattr(config.regime, 'weak_trend_risk_scale', 0.8)}")

    # Log leverage limits
    logger.info(f"CONFIG: Max Leverage = {config.portfolio.max_leverage}, Asset Max Leverage = {config.portfolio.asset_max_leverage}")
    logger.info(f"CONFIG: Min Leverage = {getattr(config.indicators, 'min_leverage', 1.0)}")

    # Add warning about possible attribute access issues
    try:
        if not hasattr(config.regime, 'dynamic_risk_adjustment'):
            logger.warning("MISSING CONFIG: 'dynamic_risk_adjustment' not defined in config.regime. This may cause issues.")
    except Exception as e:
        logger.error(f"Error checking dynamic risk config attributes: {e}")


    if not enabled_strats:
         logger.warning("No strategies enabled in config. Backtest will run but likely execute no trades.")

    # 9. Initialize and Run Backtester (using validated config)
    try:
        logger.info("Initializing Backtester instance...")
        backtester = Backtester(config)

        logger.info("Running backtest...")

        # Run the backtest using the Backtester's run method
        backtester.run(start_date, end_date)

    except Exception as e:
         # Catch critical errors during backtester init or run
         logger.critical(f"Critical error during backtester initialization or run: {e}", exc_info=True)
         print(f"CRITICAL ERROR: {e}. Check logs for details.")
         sys.exit(1)

    logger.info("Backtest run completed.")
    
    # Run configuration validator again at the END of the log
    logger.info("=" * 80)
    logger.info("CONFIGURATION FALLBACK CHECK - END OF LOG")
    logger.info("=" * 80)
    
    fallback_issues_end = validator.validate_config(config, override_dict)
    
    if not fallback_issues_end:
        logger.info("✅ No configuration fallbacks detected - using intended settings")
    else:
        error_count = sum(1 for issue in fallback_issues_end if issue.severity == "ERROR")
        warning_count = sum(1 for issue in fallback_issues_end if issue.severity == "WARNING")
        logger.error(f"❌ Configuration validation found {error_count} errors, {warning_count} warnings")
        
        for issue in fallback_issues_end:
            if issue.severity == "ERROR":
                logger.error(f"  ❌ {issue.path}: Expected {issue.expected_source}={issue.expected_value}, got {issue.actual_source}={issue.actual_value}")
            elif issue.severity == "WARNING":
                logger.warning(f"  ⚠️ {issue.path}: {issue.actual_source}")
    
    logger.info("=" * 80)
    logger.info("END OF CONFIGURATION FALLBACK CHECK")
    logger.info("=" * 80)

    print("--- Backtest Script Finished ---")


if __name__ == "__main__":
    # Command line arguments are now handled within main()
    main()