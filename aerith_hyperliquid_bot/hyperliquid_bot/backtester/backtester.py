# hyperliquid_bot/backtester/backtester.py

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import matplotlib.pyplot as plt
from pathlib import Path
import json
import random
from typing import Optional, Tuple, Literal

# Import data utilities
from hyperliquid_bot.utils.data_utils import deduplicate, verify_atr_availability
from hyperliquid_bot.utils.skip_logger import SkipReasonLogger
# Import SkipSignal exception from common location
from hyperliquid_bot.strategies.exceptions import SkipSignal
# Import UTC helper for timezone consistency
from hyperliquid_bot.utils.time import to_utc_naive, vectorized_to_utc_naive

# Import necessary components
from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.portfolio import Portfolio
from hyperliquid_bot.signals import SignalEngine
from hyperliquid_bot.core import RiskManager, RegimeDetectorInterface
from hyperliquid_bot.core.detector_factory import get_regime_detector
from hyperliquid_bot.core.config_validator import ConfigValidator
from hyperliquid_bot.strategies import StrategyEvaluator
from hyperliquid_bot.execution import ExecutionSimulator
from hyperliquid_bot.execution.execution_filter import ExecutionFilter
from hyperliquid_bot.data import HistoricalDataHandler, DataHandlerInterface

logger = logging.getLogger(__name__)

class Backtester:
    """
    Main backtesting engine. Loads data, manages components, runs the simulation loop,
    and generates performance metrics and plots.
    Assumes DataHandler provides OHLCV + pre-calculated raw microstructure features.
    """
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"Initializing Backtester with config: {config.backtest.period_preset}")

        try:
            # DataHandler now expected to provide combined OHLCV + raw micro features
            self.data_handler: DataHandlerInterface = HistoricalDataHandler(config, self.logger) # Pass logger
            self.logger.info("DataHandler initialized.")
        except Exception as e:
            self.logger.critical(f"Failed to initialize DataHandler: {e}", exc_info=True)
            raise

        self.portfolio = Portfolio(config, self.data_handler)
        # SignalEngine expects combined data from data_handler.get_ohlcv_data()
        self.signal_calculator = SignalEngine(config, self.data_handler)
        self.regime_detector: RegimeDetectorInterface = get_regime_detector(config)
        self.strategy_evaluator = StrategyEvaluator(config, self.regime_detector)
        # Pass portfolio settings to RiskManager
        self.risk_manager = RiskManager(config)
        # ExecutionSimulator might still need L2 access for fill simulation,
        # ensure its DataHandler reference is correct.
        self.execution_simulator = ExecutionSimulator(config, self.data_handler)

        # Initialize ExecutionFilter for execution refinement
        self.execution_filter = ExecutionFilter(config)
        # Check if execution refinement is enabled
        self.use_execution_refinement = getattr(config.backtest, 'use_execution_refinement', False)
        if self.use_execution_refinement:
            self.logger.info("Execution refinement enabled - will use 1-minute data for optimal timing")

        self.all_signals_df: pd.DataFrame = pd.DataFrame()
        self.last_funding_time_unix: Optional[float] = None
        self.start_index: int = 0

        # Initialize skip reason logger
        log_dir = self.config.data_paths.log_dir
        self.skip_logger = SkipReasonLogger(log_dir)

        self.logger.info("Backtester and components initialized.")

    def _calculate_required_warmup(self) -> int:
        """Calculates required warmup periods based on SignalEngine and potentially RegimeDetector needs."""
        indicator_lookback = self.signal_calculator.calculate_required_lookback()
        # Consider Hurst lookback if Hurst detector is used
        hurst_lookback = 0
        if self.config.regime.detector_type == 'hurst':
             hurst_lookback = self.config.regime.hurst_lookback_periods
             # Add a small buffer to Hurst lookback as well? Or assume it's exact? Let's assume exact for now.
             self.logger.info(f"Hurst detector selected, adding lookback: {hurst_lookback}")

        # Combine lookbacks
        combined_lookback = max(indicator_lookback, hurst_lookback)

        # Check if TF-v2 strategy is enabled and use its warmup configuration
        if self.config.strategies.use_tf_v2:
            warmup_cfg = self.config.strategies.tf_warmup_bars
            if warmup_cfg == "auto":
                # Calculate automatic warmup: max(ema_slow, atr_period, regime_lookback)
                ema_slow = self.config.indicators.tf_ewma_slow
                atr_period = self.config.indicators.tf_atr_period
                # Use hurst_lookback as regime_lookback, default to 120 if not using Hurst
                regime_lookback = hurst_lookback if hurst_lookback > 0 else 120

                auto_warmup = max(ema_slow, atr_period, regime_lookback)
                self.logger.info(f"TF-v2 auto warmup calculation: max({ema_slow}, {atr_period}, {regime_lookback}) = {auto_warmup}")
                return auto_warmup
            else:
                # Use manual warmup value
                manual_warmup = int(warmup_cfg)
                self.logger.info(f"TF-v2 manual warmup: {manual_warmup}")
                return manual_warmup

        # Fallback to original logic for other strategies
        # For testing purposes, use a smaller warmup period
        # This is a temporary fix to allow backtesting with limited data
        warmup_periods = min(combined_lookback + 5, 10)  # Use at most 10 periods for warmup

        self.logger.info(f"Indicator lookback: {indicator_lookback}, Hurst lookback: {hurst_lookback}. "
                         f"Combined lookback: {combined_lookback}. Calculated warmup periods: {warmup_periods}")
        return warmup_periods

    def _load_and_prepare_data(self, start_date: datetime, end_date: datetime):
        """Loads data via DataHandler (incl. micro features) and calculates all signals."""
        self.logger.info("Loading and preparing data...")
        cfg = self.config # Keep config for other settings

        # Use the passed-in start_date and end_date parameters
        raw_ohlcv_micro_data = self.data_handler.load_historical_data(start_date=start_date, end_date=end_date)

        self.logger.info(f"Raw data loaded, shape: {raw_ohlcv_micro_data.shape if raw_ohlcv_micro_data is not None else 'None'}")

        try:
            # Get OHLCV data from data handler
            ohlc = self.data_handler.get_ohlcv_data()

            # Check for duplicate indices and fix them using deduplicate utility
            ohlc = deduplicate(ohlc)

            # ATR is now calculated in the ETL process and included in the 1-second feature files
            # No need to calculate it here anymore
            self.logger.info("Using ATR from 1-second feature files")

            # Make sure combined_data doesn't have duplicate indices using deduplicate utility
            self.data_handler.combined_data = deduplicate(self.data_handler.combined_data)

            # Verify ATR availability
            if 'atr_14_sec' in self.data_handler.combined_data.columns:
                missing_atr = self.data_handler.combined_data['atr_14_sec'].isna().sum()
                self.logger.info(f"ATR from features: {missing_atr} NaN values out of {len(self.data_handler.combined_data)} rows.")

                # Add legacy ATR columns for backward compatibility if they don't exist
                if 'atr' not in self.data_handler.combined_data.columns:
                    self.data_handler.combined_data['atr'] = self.data_handler.combined_data['atr_14_sec']

                if 'atr_percent' not in self.data_handler.combined_data.columns:
                    self.data_handler.combined_data['atr_percent'] = self.data_handler.combined_data['atr_14_sec'] / self.data_handler.combined_data['close']
            else:
                self.logger.warning("ATR column 'atr_14_sec' not found in feature data. GMS detector may not work properly.")

            # SignalEngine now processes the combined data
            # The SignalEngine.calculate_all_signals() method doesn't take any parameters
            # It accesses data directly from the data_handler
            self.all_signals_df = self.signal_calculator.calculate_all_signals()

            if self.all_signals_df.empty:
                raise ValueError("Signal calculation resulted in an empty DataFrame.")

            # Calculate required warmup period
            self.start_index = self._calculate_required_warmup() # Restored original calculation

            # Check if data length is sufficient for warmup
            if len(self.all_signals_df) <= self.start_index: # Use <= to handle exact match edge case
                raise ValueError(f"Data length ({len(self.all_signals_df)}) insufficient for warmup ({self.start_index}).")

            self.logger.info(f"Data loaded and signals calculated. Shape: {self.all_signals_df.shape}")
            self.logger.info(f"Starting simulation loop from index {self.start_index} ({self.all_signals_df.index[self.start_index]})")
            self.logger.debug(f"Signal columns available: {self.all_signals_df.columns.tolist()}")

        except Exception as e:
            self.logger.critical(f"Failed during data loading or signal preparation: {e}", exc_info=True)
            raise

    def _evaluate_exit(self, signals: dict, position: dict, current_timestamp_unix: float) -> Tuple[bool, Optional[float], Optional[str]]:
        """Checks if the current position should be exited based on SL, TP, or time limit."""
        # This method seems correct based on previous versions, ensure it exists and functions as intended.
        # ... (Keep the logic from your provided code) ...
        price = signals.get("close")
        low_price = signals.get("low") # Need low/high for accurate SL/TP check within the bar
        high_price = signals.get("high")
        should_exit = False
        trigger_price = None
        reason = None

        if pd.isna(price) or pd.isna(low_price) or pd.isna(high_price):
            self.logger.warning(f"Price/Low/High is NaN at {datetime.utcfromtimestamp(current_timestamp_unix)}. Cannot reliably check SL/TP. Checking time exit only.")
            # Check time exit even if price is NaN
            time_held = current_timestamp_unix - position["entry_time"]
            max_hold_time_seconds = self.config.portfolio.max_hold_time_hours * 3600
            if time_held > max_hold_time_seconds:
                 # Cannot determine market price, maybe use entry price as rough estimate for logging?
                 trigger_price = position.get('entry', 0)
                 return True, trigger_price, "time_exit_price_nan"
            else:
                 return False, None, None # Cannot determine exit otherwise

        time_held = current_timestamp_unix - position["entry_time"]
        stop_price = position.get('stop')
        profit_price = position.get('profit')

        if stop_price is None or profit_price is None:
            self.logger.error(f"Position is missing stop ({stop_price}) or profit ({profit_price}) levels.")
            return False, None, None

        # Check conditions (Order matters: SL > TP > Time)
        if position["type"] == "long":
            if low_price <= stop_price: # Use low for SL check
                should_exit, trigger_price, reason = True, stop_price, "stop_loss"
                self.logger.info(f"Exit Condition: Stop Loss triggered at {stop_price:.4f} (Low Price: {low_price:.4f})")
            elif high_price >= profit_price: # Use high for TP check
                should_exit, trigger_price, reason = True, profit_price, "take_profit"
                self.logger.info(f"Exit Condition: Take Profit triggered at {profit_price:.4f} (High Price: {high_price:.4f})")
        elif position["type"] == "short":
            if high_price >= stop_price: # Use high for SL check
                should_exit, trigger_price, reason = True, stop_price, "stop_loss"
                self.logger.info(f"Exit Condition: Stop Loss triggered at {stop_price:.4f} (High Price: {high_price:.4f})")
            elif low_price <= profit_price: # Use low for TP check
                should_exit, trigger_price, reason = True, profit_price, "take_profit"
                self.logger.info(f"Exit Condition: Take Profit triggered at {profit_price:.4f} (Low Price: {low_price:.4f})")

        # Check Max Hold Time if SL/TP not hit
        max_hold_time_seconds = self.config.portfolio.max_hold_time_hours * 3600
        if not should_exit and time_held > max_hold_time_seconds:
            should_exit, trigger_price, reason = True, price, "time_exit" # Market exit at current close
            self.logger.info(f"Exit Condition: Max hold time exceeded ({time_held/3600:.1f} hours). Exiting at market price {price:.4f}.")

        # Check Strategy-Specific Exit if others not hit
        if not should_exit:
           strategy_name = position.get("strategy")
           strategy_instance = self.strategy_evaluator.get_strategy(strategy_name)
           if strategy_instance:
               strategy_exit_reason = strategy_instance.check_exit(signals, position)
               if strategy_exit_reason:
                   should_exit, trigger_price, reason = True, price, f"strategy_exit ({strategy_exit_reason})"
                   self.logger.info(f"Exit Condition: Strategy-specific exit '{strategy_exit_reason}' triggered. Exiting at market price {price:.4f}.")

        return should_exit, trigger_price, reason


    def _calculate_sl_tp(self, signals: dict, direction: Literal["long", "short"], strategy_name: str) -> Tuple[Optional[float], Optional[float]]:
        """Calculates SL and TP prices based on strategy rules."""
        # This method seems correct based on previous versions, ensure it exists and functions as intended.
        # ... (Keep the logic from your provided code) ...
        cfg_indicators = self.config.indicators
        entry_price_est = signals.get('close')

        if pd.isna(entry_price_est):
            self.logger.error("Cannot calculate SL/TP: Entry price estimate (close) is NaN.")
            return None, None

        stop_loss = None
        take_profit = None
        atr_signal_name = None # Determine which ATR signal to use

        if strategy_name == "trend_following":
            atr_signal_name = "atr_tf"
            stop_mult = cfg_indicators.tf_atr_stop_mult
            target_mult = cfg_indicators.tf_atr_target_mult
            atr = signals.get(atr_signal_name)
            if pd.isna(atr) or atr <= 1e-9:
                self.logger.error(f"TF SL/TP Calc Failed: Invalid ATR ({atr}) using '{atr_signal_name}'")
                return None, None
            stop_distance = stop_mult * atr
            tp_distance = target_mult * atr
            stop_loss = entry_price_est - stop_distance if direction == "long" else entry_price_est + stop_distance
            take_profit = entry_price_est + tp_distance if direction == "long" else entry_price_est - tp_distance

        elif strategy_name == "mean_reversion":
            atr_signal_name = "atr_mr"
            stop_mult = cfg_indicators.mr_atr_stop_mult
            # Use kc_basis (Keltner Middle) as target if available, else fallback
            target_basis = signals.get("kc_basis")
            atr = signals.get(atr_signal_name)

            if pd.isna(atr) or atr <= 1e-9:
                self.logger.error(f"MR SL Calc Failed: Invalid ATR ({atr}) using '{atr_signal_name}'")
                return None, None
            if pd.isna(target_basis):
                self.logger.error("MR TP Calc Failed: Keltner Basis ('kc_basis') is NaN")
                # Fallback TP logic? Maybe ATR based?
                # For now, return None if basis is missing
                return None, None

            stop_distance = stop_mult * atr
            stop_loss = entry_price_est - stop_distance if direction == "long" else entry_price_est + stop_distance
            take_profit = target_basis # Target Keltner basis

            # Adjust TP if it's worse than entry estimate
            buffer = entry_price_est * self.config.costs.taker_fee * 3
            if (direction == "long" and take_profit <= entry_price_est + buffer) or \
               (direction == "short" and take_profit >= entry_price_est - buffer):
                adj_factor = (1 + self.config.costs.taker_fee * 5)
                new_tp = entry_price_est * adj_factor if direction == "long" else entry_price_est / adj_factor
                self.logger.warning(f"MR TP ({take_profit:.2f}) is too close or worse than entry estimate ({entry_price_est:.2f}). Adjusting TP to {new_tp:.2f}.")
                take_profit = new_tp

        elif strategy_name == "tf_v3":
            # Use the ATR from signals for TF-v3
            # Try different ATR column names in order of preference
            atr_signal_names = ["atr", "atr_14", "atr_14_sec"]
            atr = None
            atr_signal_name = None

            # Try each ATR column name until we find one that exists and has a valid value
            for name in atr_signal_names:
                atr_value = signals.get(name)
                if atr_value is not None and not pd.isna(atr_value) and atr_value > 1e-9:
                    atr = atr_value
                    atr_signal_name = name
                    break

            # Get the ATR multipliers from the config
            if hasattr(self.config, 'tf_v3'):
                stop_mult = getattr(self.config.tf_v3, 'atr_trail_k', 3.0)
                target_mult = getattr(self.config.tf_v3, 'atr_target_k', 6.0)
            else:
                stop_mult = 3.0  # Default value
                target_mult = 6.0  # Default value

            self.logger.debug(f"TF-v3 SL/TP Calc: ATR={atr}, StopMult={stop_mult}, TargetMult={target_mult}")

            if atr is None or pd.isna(atr) or atr <= 1e-9:
                # If we still don't have a valid ATR, use configurable percentage of the entry price as a fallback
                self.logger.warning(f"TF-v3 SL/TP Calc: No valid ATR found in columns {atr_signal_names}. Using configurable percentage fallback.")

                # Use configurable percentage of entry price as ATR fallback
                fallback_pct = getattr(self.config.tf_v3, 'atr_fallback_pct', 0.01)
                atr_fallback = entry_price_est * fallback_pct
                stop_distance = stop_mult * atr_fallback
                tp_distance = target_mult * atr_fallback

                self.logger.info(f"TF-v3 SL/TP Calc: Using fallback ATR={atr_fallback:.4f} ({fallback_pct*100:.1f}% of price)")
            else:
                # Use the ATR we found
                stop_distance = stop_mult * atr
                tp_distance = target_mult * atr
                self.logger.debug(f"TF-v3 SL/TP Calc: Using ATR={atr:.4f} from column '{atr_signal_name}'")

            stop_loss = entry_price_est - stop_distance if direction == "long" else entry_price_est + stop_distance
            take_profit = entry_price_est + tp_distance if direction == "long" else entry_price_est - tp_distance
            self.logger.debug(f"TF-v3 SL/TP Calc: StopDist={stop_distance:.4f}, TPDist={tp_distance:.4f}")

        # --- ADDED LOGIC FOR MEAN VARIANCE ---
        elif strategy_name == "mean_variance":
            atr_signal_name = "atr_mv" # Use MV-specific ATR signal
            stop_mult = cfg_indicators.mv_atr_stop_mult
            target_mult = cfg_indicators.mv_atr_target_mult
            atr = signals.get(atr_signal_name)

            if pd.isna(atr) or atr <= 1e-9:
                self.logger.error(f"MV SL/TP Calc Failed: Invalid ATR ({atr}) using '{atr_signal_name}'")
                # Ensure atr_mv is calculated in SignalEngine if MV is enabled
                return None, None
            stop_distance = stop_mult * atr
            tp_distance = target_mult * atr
            stop_loss = entry_price_est - stop_distance if direction == "long" else entry_price_est + stop_distance
            take_profit = entry_price_est + tp_distance if direction == "long" else entry_price_est - tp_distance
            self.logger.debug(f"MV SL/TP Calc: ATR={atr:.4f}, StopDist={stop_distance:.4f}, TPDist={tp_distance:.4f}")
        # --- END ADDED LOGIC ---
        else:
            self.logger.error(f"Unknown strategy '{strategy_name}' for SL/TP calculation.")
            return None, None

        if stop_loss is None or take_profit is None:
            self.logger.error("SL or TP calculation resulted in None.")
            return None, None

        if (direction == "long" and (stop_loss >= entry_price_est or take_profit <= entry_price_est)) or \
           (direction == "short" and (stop_loss <= entry_price_est or take_profit >= entry_price_est)):
            self.logger.error(f"Invalid SL/TP Calculation for {direction} {strategy_name}: EntryEst={entry_price_est:.2f}, SL={stop_loss:.2f}, TP={take_profit:.2f}")
            return None, None

        self.logger.debug(f"Calculated SL={stop_loss:.4f}, TP={take_profit:.4f} for {direction} {strategy_name}")
        return stop_loss, take_profit

    def _refine_execution(self, hourly_candle_signals: dict, decision: str, signals: dict) -> float:
        """
        Load 1-minute data on-demand and find best execution price.
        
        Args:
            hourly_candle_signals: The hourly candle data/signals
            decision: Trade direction ('long' or 'short')
            signals: Current market signals including regime confidence
            
        Returns:
            Execution price from the best minute or fallback to minute 5
        """
        timestamp = hourly_candle_signals.get('timestamp', pd.Timestamp.now())
        hourly_close = hourly_candle_signals.get('close')
        
        # Default to hourly close if refinement is disabled or data loading fails
        if not self.use_execution_refinement:
            return hourly_close
            
        try:
            # Load 1-minute candles for this hour
            hour_start = pd.Timestamp(timestamp).floor('H')
            hour_end = hour_start + pd.Timedelta(hours=1)
            
            minute_candles_df = self.data_handler.load_1m_candles(hour_start, hour_end)
            
            if minute_candles_df is None or minute_candles_df.empty:
                self.logger.warning(f"No 1-minute data available for {hour_start}. Using hourly close.")
                return hourly_close
                
            # Convert DataFrame to list of dicts for ExecutionFilter
            minute_candles = minute_candles_df.head(5).to_dict('records')
            
            if len(minute_candles) < 5:
                self.logger.warning(f"Only {len(minute_candles)} minute candles available. Using hourly close.")
                return hourly_close
                
            # Get regime confidence for execution urgency
            regime_confidence = signals.get('regime_confidence', 0.5)
            
            # Use ExecutionFilter to find optimal minute
            best_minute_idx, best_price, score_components = self.execution_filter.find_best_execution_minute(
                minute_candles,
                decision,
                regime_confidence
            )
            
            self.logger.info(f"Execution refinement: Best minute={best_minute_idx}, price={best_price:.2f}, "
                            f"components={score_components}")
            
            return best_price
            
        except Exception as e:
            self.logger.error(f"Error in execution refinement: {e}. Using hourly close.")
            return hourly_close

    def _get_entry_trigger_price(self, signals: dict, timestamp: pd.Timestamp) -> float:
        """Determines the price to use for triggering entry simulation."""
        # Simplified version that doesn't try to access L2 data directly
        current_price = signals.get('close')
        if pd.isna(current_price):
            self.logger.error("Cannot get trigger price: OHLC close is NaN.")
            return np.nan
        self.logger.debug(f"Using OHLC close price {current_price:.2f} as trigger.")
        return current_price


    def _compute_and_log_metrics(self, total_steps: int):
        """Computes and logs performance metrics with enhanced formatting."""
        # ANSI color codes
        GREEN = '\033[92m'; RED = '\033[91m'; BLUE = '\033[94m'; CYAN = '\033[96m'; YELLOW = '\033[93m'; BOLD = '\033[1m'; UNDERLINE = '\033[4m'; ENDC = '\033[0m'
        main_divider = f"\n{BOLD}{'═' * 80}{ENDC}"; section_divider = f"{BOLD}{'─' * 80}{ENDC}"
        print(f"\n{BOLD}{'#' * 80}{ENDC}"); self.logger.info(main_divider); self.logger.info(f"{BOLD}{BLUE}{'BACKTEST METRICS'.center(80)}{ENDC}"); self.logger.info(main_divider)
        cfg = self.config; portfolio = self.portfolio; sim_diags = self.execution_simulator.get_diagnostics()
        self.logger.info(f"\n{BOLD}{CYAN}{'DIAGNOSTICS'.center(80)}{ENDC}"); self.logger.info(section_divider)
        total_sims = max(1, sum(v for k, v in sim_diags.items() if k.startswith('sim_'))) # Sum only sim counters
        self.logger.info(f"{BOLD}Execution Simulation:{ENDC}"); self.logger.info(f"  • Total Orders Simulated: {total_sims}")
        # Updated diagnostics keys
        self.logger.info(f"  • Fill via Spread:        {sim_diags.get('sim_pen_spread', 0):<5} ({sim_diags.get('sim_pen_spread', 0)/total_sims:>6.1%})")
        self.logger.info(f"  • Fill via Penalty (No Spread): {sim_diags.get('sim_pen_no_spread', 0):<5} ({sim_diags.get('sim_pen_no_spread', 0)/total_sims:>6.1%})")
        self.logger.info(f"  • Fill via Penalty (Zero Fill): {sim_diags.get('sim_pen_zero', 0):<5} ({sim_diags.get('sim_pen_zero', 0)/total_sims:>6.1%})")
        self.logger.info(f"  • Fill via Penalty (Error): {sim_diags.get('sim_pen_error', 0):<5} ({sim_diags.get('sim_pen_error', 0)/total_sims:>6.1%})")
        self.logger.info(f"  • Fill via Maker:              {sim_diags.get('sim_maker_fills', 0):<5} ({sim_diags.get('sim_maker_fills', 0)/total_sims:>6.1%})")
        self.logger.info(f"  • Maker -> Taker Fallback:     {sim_diags.get('sim_maker_fails_taker', 0):<5} ({sim_diags.get('sim_maker_fails_taker', 0)/total_sims:>6.1%})")
        self.logger.info(f"  • Fill via Taker BBO:          {sim_diags.get('sim_taker_bbo_fills', 0):<5} ({sim_diags.get('sim_taker_bbo_fills', 0)/total_sims:>6.1%})")

        # Regime Filtering Stats
        self.logger.info("")  # Add blank line with proper prefix
        self.logger.info(f"{BOLD}Regime Filtering:{ENDC}")  # Now the header will have proper prefix

        chop_periods = getattr(portfolio, 'chop_periods_detected', 0)
        total_steps = max(1, total_steps) # Avoid division by zero
        chop_rate = (chop_periods / total_steps * 100)

        self.logger.info(f"  • Detector Used: {cfg.regime.detector_type}") # Show which detector ran
        self.logger.info(f"  • Filter Active: {cfg.regime.use_filter}")
        if cfg.regime.detector_type == 'rule_based':
            self.logger.info(f"    Enhanced Rules: {cfg.regime.use_enhanced_detection}")
            self.logger.info(f"    Chop Indicator: {'ChopIndex' if cfg.regime.use_chop_index_for_chop else 'BBW' if cfg.regime.use_bbw_for_chop_detection else 'ATR/P Fallback'}")
        elif cfg.regime.detector_type == 'hurst':
            self.logger.info(f"    Lookback: {cfg.regime.hurst_lookback_periods}, RangeTh: {cfg.regime.hurst_ranging_threshold}, TrendTh: {cfg.regime.hurst_trending_threshold}")

        self.logger.info(f"  • Strict Strategy Filtering: {cfg.regime.use_strict_strategy_filtering}")
        self.logger.info(f"  • Pause in Chop (Strict): {cfg.regime.pause_in_chop}")
        self.logger.info(f"  • Dynamic Risk in Chop: {cfg.regime.use_dynamic_risk}")

        # --- Section: Overall Performance ---
        self.logger.info(f"\n{BOLD}{CYAN}{'OVERALL PERFORMANCE'.center(80)}{ENDC}")
        self.logger.info(section_divider)

        if not portfolio.trades:
            self.logger.info(f"{YELLOW}No trades were executed during this backtest period.{ENDC}")
            self.logger.info(f"  • Initial Balance:        ${portfolio.initial_balance:>15,.2f}")
            self.logger.info(f"  • Final Balance:          ${portfolio.balance:>15,.2f}")
            self.logger.info(f"  • Net Profit (P/L):       ${portfolio.balance - portfolio.initial_balance:>15,.2f}")
            return  # Exit if no trades

        # Trade statistics and analysis
        trades_df = pd.DataFrame(portfolio.trades)

        # Debug balance calculation
        self.logger.info(f"BALANCE_DEBUG: Initial balance=${portfolio.initial_balance:.2f}, Current balance=${portfolio.balance:.2f}")
        self.logger.info(f"BALANCE_DEBUG: Reserved margin=${portfolio.reserved_margin:.2f}")
        self.logger.info(f"BALANCE_DEBUG: Total trades={len(portfolio.trades)}")

        # Calculate total PnL from trades for verification
        total_trade_pnl = sum(t.get('profit', 0) for t in portfolio.trades)
        self.logger.info(f"BALANCE_DEBUG: Sum of all trade PnLs=${total_trade_pnl:.2f}")

        # Calculate expected balance (should match balance + reserved_margin)
        # Fix: Use only initial_balance + total_trade_pnl since 'profit' in trades already includes
        # all components (fees, funding, slippage)
        expected_balance = portfolio.initial_balance + total_trade_pnl
        self.logger.info(f"BALANCE_DEBUG: Expected balance=${expected_balance:.2f} (initial + sum of trade PnLs)")

        # Calculate total account value (balance + reserved margin)
        total_account_value = portfolio.balance + portfolio.reserved_margin
        self.logger.info(f"BALANCE_DEBUG: Total account value=${total_account_value:.2f} (balance + reserved margin)")

        # Check if there's a discrepancy
        if abs(total_account_value - expected_balance) > 0.01:
            # Changed to debug level to silence warnings in console output
            self.logger.debug(f"BALANCE_DEBUG: DISCREPANCY DETECTED! Account value=${total_account_value:.2f} vs Expected=${expected_balance:.2f}")

        # Fix: Use total account value (balance + reserved margin) for ROI calculation
        # This is the correct approach since reserved margin is still part of the account equity
        net_pnl_overall = total_account_value - portfolio.initial_balance
        total_fees = trades_df["entry_fee"].sum() + trades_df["exit_fee"].sum()
        total_slippage_reported = sum(t.get('entry_slippage_pnl', 0) + t.get('exit_slippage_pnl', 0) for t in portfolio.trades)
        roi = (total_account_value / portfolio.initial_balance - 1) * 100

        # Build equity series
        equity_series = pd.Series([])
        if portfolio.equity_timestamps and portfolio.equity_curve:
            try:
                equity_timestamps_dt = pd.to_datetime(portfolio.equity_timestamps, unit='s', errors='coerce', utc=True)
                valid_indices = ~pd.isna(equity_timestamps_dt)
                if valid_indices.any():
                    equity_series = pd.Series(np.array(portfolio.equity_curve)[valid_indices], index=equity_timestamps_dt[valid_indices])
                    equity_series = equity_series.loc[~equity_series.index.duplicated(keep='last')].sort_index()
            except Exception as e:
                self.logger.error(f"Error creating equity series: {e}")

        period_start_str = equity_series.index.min().strftime('%Y-%m-%d %H:%M') if not equity_series.empty else 'N/A'
        period_end_str = equity_series.index.max().strftime('%Y-%m-%d %H:%M') if not equity_series.empty else 'N/A'

        # Format account performance metrics
        self.logger.info(f"  • Period:                 {period_start_str} to {period_end_str}")
        self.logger.info(f"  • Initial Balance:        ${portfolio.initial_balance:>15,.2f}")

        # Show both free balance and total account value (including reserved margin)
        total_account_value = portfolio.balance + portfolio.reserved_margin
        self.logger.info(f"  • Free Balance:           ${portfolio.balance:>15,.2f}")
        self.logger.info(f"  • Reserved Margin:        ${portfolio.reserved_margin:>15,.2f}")
        self.logger.info(f"  • Final Account Value:    ${total_account_value:>15,.2f}")

        # Use color for profit/loss values
        pnl_color = GREEN if net_pnl_overall >= 0 else RED
        roi_color = GREEN if roi >= 0 else RED

        self.logger.info(f"  • Net Profit (P/L):       {pnl_color}${net_pnl_overall:>15,.2f}{ENDC}")
        self.logger.info(f"  • Return on Initial (ROI):{roi_color}{roi:>15,.2f}%{ENDC}")
        self.logger.info(f"  • Total Fees Paid:        ${total_fees:>15,.2f}")
        self.logger.info(f"  • Total Funding P/L:      ${portfolio.total_funding_pnl:>15,.2f}")
        self.logger.info(f"  • Total Slippage (Closed):${total_slippage_reported:>15,.2f}")

        # --- Additional Performance Metrics ---
        open_trades = 1 if portfolio.position else 0
        self.logger.info(f"  • {'Open Trades:':<24} {open_trades:>15}")

        # Precompute basic trade stats for Additional Performance Metrics
        total_trades = len(trades_df)
        win_count = len(trades_df[trades_df['profit'] > 0])
        loss_count = total_trades - win_count
        win_rate = win_count / total_trades if total_trades > 0 else 0
        avg_win = trades_df.loc[trades_df['profit'] > 0, 'profit'].mean() if win_count > 0 else 0
        avg_loss = trades_df.loc[trades_df['profit'] <= 0, 'profit'].mean() if loss_count > 0 else 0

        self.logger.info(f"  • {'Total Winning Trades:':<24} {win_count:>15}")
        self.logger.info(f"  • {'Total Losing Trades:':<24} {loss_count:>15}")
        longs_count = len(trades_df[trades_df['type'] == 'long'])
        shorts_count = len(trades_df[trades_df['type'] == 'short'])
        longs_pct = longs_count / total_trades if total_trades > 0 else 0
        shorts_pct = shorts_count / total_trades if total_trades > 0 else 0
        self.logger.info(f"  • {'Longs (pct):':<24}{longs_pct*100:>15.1f} %")
        self.logger.info(f"  • {'Shorts (pct):':<24}{shorts_pct*100:>15.1f} %")
        # Calculate expectancy (per trade)
        expectancy = win_rate * avg_win + (1 - win_rate) * avg_loss
        self.logger.info(f"  • {'Expectancy:':<24}{expectancy:>15,.2f} $")
        # Calculate annual return
        period_start_ts = equity_series.index.min() if not equity_series.empty else None
        period_end_ts = equity_series.index.max() if not equity_series.empty else None
        if period_start_ts and period_end_ts and period_end_ts > period_start_ts:
            period_secs = (period_end_ts - period_start_ts).total_seconds()
            years = period_secs / (365.25 * 24 * 3600)
            # Fix: Use total account value instead of just balance for annual return calculation
            total_account_value = portfolio.balance + portfolio.reserved_margin
            annual_return = ((total_account_value / portfolio.initial_balance) ** (1 / years) - 1) * 100
        else:
            annual_return = 0.0
        self.logger.info(f"  • {'Annual Return:':<24}{annual_return:>15,.2f} %")

        # --- Section: Risk & Drawdown ---
        self.logger.info(f"\n{BOLD}{CYAN}{'RISK & DRAWDOWN'.center(80)}{ENDC}")
        self.logger.info(section_divider)

        max_drawdown = 0; sharpe = 0; sortino = 0
        if not equity_series.empty and len(equity_series) > 1:
            try:
                rolling_max = equity_series.cummax()
                drawdown = (equity_series - rolling_max) / rolling_max.replace(0, 1)
                max_drawdown = abs(drawdown.min()) * 100 if not drawdown.empty else 0
                daily_equity = equity_series.resample('D').last().ffill()
                daily_returns = daily_equity.pct_change().dropna()
                if len(daily_returns) > 1:
                    periods_per_year = 365.25
                    mean_return = daily_returns.mean()
                    std_dev = daily_returns.std()
                    if std_dev > 1e-9: sharpe = (mean_return / std_dev) * np.sqrt(periods_per_year)
                    downside_returns = daily_returns[daily_returns < 0]
                    downside_std = downside_returns.std()
                    if downside_std is not None and downside_std > 1e-9: sortino = (mean_return / downside_std) * np.sqrt(periods_per_year)
            except Exception as e:
                self.logger.error(f"Error calculating risk metrics: {e}")
                max_drawdown = -999.0

        # Format risk metrics
        md_display = f'{max_drawdown:.2f}%' if max_drawdown != -999 else 'N/A (Error)'
        self.logger.info(f"  • {'Max Drawdown:':<24} {RED}{md_display:>15}{ENDC}")

        sharpe_color = GREEN if sharpe > 0 else RED
        sortino_color = GREEN if sortino > 0 else RED

        self.logger.info(f"  • {'Sharpe Ratio (Daily):':<24} {sharpe_color}{sharpe:>15,.2f}{ENDC}")
        self.logger.info(f"  • {'Sortino Ratio (Daily):':<24} {sortino_color}{sortino:>15,.2f}{ENDC}")

        # --- Additional Risk/Return Ratios ---
        total_gross_wins = trades_df.loc[trades_df['profit'] > 0, 'profit'].sum()
        total_abs_gross_loss = abs(trades_df.loc[trades_df['profit'] <= 0, 'profit'].sum())
        profit_factor = total_gross_wins / total_abs_gross_loss if total_abs_gross_loss > 0 else float('inf')
        calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else float('inf')
        calmar_color = GREEN if calmar_ratio >= 1 else RED
        self.logger.info(f"  • {'Calmar Ratio:':<24} {calmar_color}{calmar_ratio:>15,.2f}{ENDC}")
        omega_ratio = profit_factor
        omega_color = GREEN if omega_ratio >= 1 else RED
        self.logger.info(f"  • {'Omega Ratio:':<24} {omega_color}{omega_ratio:>15,.2f}{ENDC}")

        # --- Section: Trade Stats ---
        self.logger.info(f"\n{BOLD}{CYAN}{'TRADE STATISTICS'.center(80)}{ENDC}")
        self.logger.info(section_divider)

        # Calculate trade statistics
        avg_profit_trade = trades_df["profit"].mean() if total_trades > 0 else 0
        win_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
        profit_factor = total_gross_wins / total_abs_gross_loss if total_abs_gross_loss > 0 else float('inf')

        # Calculate trade durations
        trades_df['duration_seconds'] = trades_df['exit_time'] - trades_df['entry_time']
        avg_duration_hours = (trades_df['duration_seconds'].mean() / 3600) if not trades_df.empty else 0
        avg_win_duration = (trades_df.loc[trades_df['profit'] > 0, 'duration_seconds'].mean() / 3600) if win_count > 0 else 0
        avg_loss_duration = (trades_df.loc[trades_df['profit'] <= 0, 'duration_seconds'].mean() / 3600) if loss_count > 0 else 0
        best_trade = trades_df['profit'].max() if not trades_df.empty else 0
        worst_trade = trades_df['profit'].min() if not trades_df.empty else 0

        # Format trade statistics with colors
        self.logger.info(f"  • {'Total Trades:':<24} {BOLD}{total_trades:>15}{ENDC}")

        wr_color = GREEN if win_rate >= 0.5 else YELLOW if win_rate >= 0.4 else RED
        self.logger.info(f"  • {'Win Rate:':<24} {wr_color}{win_rate*100:>15.1f} %{ENDC}")

        avg_pnl_color = GREEN if avg_profit_trade >= 0 else RED
        self.logger.info(f"  • {'Avg Trade P/L:':<24} ${avg_pnl_color}{avg_profit_trade:>15,.2f}{ENDC}")

        win_loss_str = f"${avg_win:,.2f} / ${avg_loss:,.2f}"
        self.logger.info(f"  • {'Avg Win / Avg Loss:':<24} {win_loss_str}")

        wl_ratio_color = GREEN if win_loss_ratio > 1 else RED
        self.logger.info(f"  • {'Win/Loss Ratio:':<24} {wl_ratio_color}{win_loss_ratio:>15,.2f}{ENDC}")

        pf_color = GREEN if profit_factor > 1 else RED
        self.logger.info(f"  • {'Profit Factor:':<24} {pf_color}{profit_factor:>15,.2f}{ENDC}")

        self.logger.info(f"  • {'Avg Duration (Hours):':<24} {avg_duration_hours:>15,.2f} (W: {avg_win_duration:.1f}h | L: {avg_loss_duration:.1f}h)")
        self.logger.info(f"  • {'Best Trade P/L:':<24} ${GREEN}{best_trade:>15,.2f}{ENDC}")
        self.logger.info(f"  • {'Worst Trade P/L:':<24} ${RED}{worst_trade:>15,.2f}{ENDC}")

        # --- Trade Streaks ---
        win_streak = 0; loss_streak = 0; curr_win = 0; curr_loss = 0
        for pnl in trades_df['profit']:
            if pnl > 0:
                curr_win += 1; win_streak = max(win_streak, curr_win); curr_loss = 0
            else:
                curr_loss += 1; loss_streak = max(loss_streak, curr_loss); curr_win = 0
        self.logger.info(f"  • {'Winning Streak:':<24} {win_streak:>15}")
        self.logger.info(f"  • {'Losing Streak:':<24} {loss_streak:>15}")

        # --- Section: Strategy Performance Breakdown ---
        self.logger.info(f"\n{BOLD}{CYAN}{'STRATEGY PERFORMANCE BREAKDOWN'.center(80)}{ENDC}")
        self.logger.info(section_divider)

        # Get all strategies used
        all_used_strategies = list(trades_df['strategy'].unique()) if not trades_df.empty else []
        enabled_strategies = list(self.strategy_evaluator.strategies.keys())
        print_strats = sorted(list(set(enabled_strategies + all_used_strategies)))

        # Add column headers similar to exit reason analysis for consistency
        self.logger.info(f"  {BOLD}{'Strategy':<20} {'Trades':>8} {'Win Rate':>16} {'Avg P/L':>16} {'Total P/L':>16}{ENDC}")
        self.logger.info(f"  {'-'*20:<20} {'-'*8:>8} {'-'*16:>16} {'-'*16:>16} {'-'*16:>16}")

        # Display per-strategy summary stats
        for strategy in print_strats:
            strat_trades = trades_df[trades_df["strategy"] == strategy]
            if strat_trades.empty:
                if strategy in enabled_strategies:
                    self.logger.info(f"  {YELLOW}{strategy:<20}: Enabled but no trades executed.{ENDC}")
                continue

            strat_win_count = len(strat_trades[strat_trades['profit'] > 0])
            strat_total = len(strat_trades)
            strat_profit = strat_trades["profit"].sum()
            strat_win_rate = strat_win_count / strat_total if strat_total > 0 else 0
            strat_avg_profit = strat_profit / strat_total if strat_total > 0 else 0

            # Format with colors based on performance
            wr_color = GREEN if strat_win_rate >= 0.5 else YELLOW if strat_win_rate >= 0.4 else RED
            avg_pnl_color = GREEN if strat_avg_profit >= 0 else RED
            total_pnl_color = GREEN if strat_profit >= 0 else RED

            # Format in table style to match exit reason analysis
            self.logger.info(f"  {strategy:<20} {strat_total:>8} {wr_color}{strat_win_rate*100:>15.1f} %{ENDC} "
                            f"{avg_pnl_color}${strat_avg_profit:>15,.2f}{ENDC} "
                            f"{total_pnl_color}${strat_profit:>15,.2f}{ENDC}")

        self.logger.info("")  # Add space after table

        # --- Section: Exit Reason Analysis ---
        self.logger.info(f"\n{BOLD}{CYAN}{'EXIT REASON ANALYSIS'.center(80)}{ENDC}")
        self.logger.info(section_divider)

        if "exit_reason" in trades_df.columns and not trades_df.empty:
            try:
                exit_summary = trades_df.groupby('exit_reason')['profit'].agg(['count', 'sum', 'mean'])
                self.logger.info(f"  {BOLD}{'Exit Reason':<20} {'Count':>8} {'Total P/L':>16} {'Avg P/L':>16}{ENDC}")
                self.logger.info(f"  {'-'*20:<20} {'-'*8:>8} {'-'*16:>16} {'-'*16:>16}")

                for reason, row in exit_summary.iterrows():
                    count = int(row['count'])
                    total = row['sum']
                    avg = row['mean']

                    # Format colors based on profitability
                    total_color = GREEN if total >= 0 else RED
                    avg_color = GREEN if avg >= 0 else RED

                    reason_display = reason[:18] + ".." if len(reason) > 20 else reason
                    self.logger.info(f"  {reason_display:<20} {count:>8} {total_color}${total:>15,.2f}{ENDC} {avg_color}${avg:>15,.2f}{ENDC}")
            except Exception as e:
                self.logger.info(f"  {YELLOW}Could not generate exit reason summary: {e}{ENDC}")

        # --- Section: Detailed Strategy Performance ---
        self.logger.info(f"\n{BOLD}{CYAN}{'DETAILED STRATEGY PERFORMANCE'.center(80)}{ENDC}")
        self.logger.info(section_divider)

        for strategy in print_strats:
            strat_trades = trades_df[trades_df["strategy"] == strategy]
            if strat_trades.empty:
                if strategy in enabled_strategies:
                    self.logger.info(f"  {YELLOW}{strategy:<20}: Enabled but no trades executed.{ENDC}")
                continue

            # Calculate detailed strategy metrics
            strat_total = len(strat_trades)
            strat_win_count = len(strat_trades[strat_trades['profit'] > 0])
            strat_loss_count = strat_total - strat_win_count
            strat_win_rate = strat_win_count / strat_total if strat_total > 0 else 0
            strat_profit = strat_trades["profit"].sum()
            strat_avg_profit = strat_profit / strat_total if strat_total > 0 else 0
            strat_avg_win = strat_trades.loc[strat_trades['profit'] > 0, 'profit'].mean() if strat_win_count > 0 else 0
            strat_avg_loss = strat_trades.loc[strat_trades['profit'] <= 0, 'profit'].mean() if loss_count > 0 else 0
            strat_win_loss_ratio = abs(strat_avg_win / strat_avg_loss) if avg_loss != 0 and avg_loss < 0 else float('inf')
            strat_gross_wins = strat_trades.loc[strat_trades['profit'] > 0, 'profit'].sum() if strat_win_count > 0 else 0
            strat_gross_losses = abs(strat_trades.loc[strat_trades['profit'] <= 0, 'profit'].sum()) if strat_loss_count > 0 else 0
            strat_profit_factor = strat_gross_wins / strat_gross_losses if strat_gross_losses > 0 else float('inf')

            # Display detailed strategy metrics with colors
            self.logger.info(f"\n  {BOLD}{BLUE}{strategy.upper().center(76)}{ENDC}")
            self.logger.info(f"    • {'Total Trades:':<24} {BOLD}{strat_total:>15}{ENDC}")

            wr_color = GREEN if strat_win_rate >= 0.5 else YELLOW if strat_win_rate >= 0.4 else RED
            self.logger.info(f"    • {'Win Rate:':<24} {wr_color}{strat_win_rate*100:>15.1f} %{ENDC}")

            avg_pnl_color = GREEN if strat_avg_profit >= 0 else RED
            self.logger.info(f"    • {'Avg Trade P/L:':<24} ${avg_pnl_color}{strat_avg_profit:>15,.2f}{ENDC}")

            win_loss_str = f"${strat_avg_win:,.2f} / ${strat_avg_loss:,.2f}"
            self.logger.info(f"    • {'Avg Win / Avg Loss:':<24} {win_loss_str}")

            wl_ratio_color = GREEN if strat_win_loss_ratio > 1 else RED
            self.logger.info(f"    • {'Win/Loss Ratio:':<24} {wl_ratio_color}{strat_win_loss_ratio:>15,.2f}{ENDC}")

            pf_color = GREEN if strat_profit_factor > 1 else RED
            self.logger.info(f"    • {'Profit Factor:':<24} {pf_color}{strat_profit_factor:>15,.2f}{ENDC}")

            total_pnl_color = GREEN if strat_profit >= 0 else RED
            self.logger.info(f"    • {'Total P/L:':<24} ${total_pnl_color}{strat_profit:>15,.2f}{ENDC}")

        # --- Important Metrics Summary (duplicated for quick view) ---
        self.logger.info(f"\n{BOLD}{YELLOW}{'IMPORTANT METRICS'.center(80)}{ENDC}")
        self.logger.info(section_divider)
        self.logger.info(f"  • {'Sharpe Ratio (Daily):':<24} {sharpe_color}{sharpe:>15,.2f}{ENDC}")
        self.logger.info(f"  • {'Profit Factor:':<24} {pf_color}{profit_factor:>15,.2f}{ENDC}")
        self.logger.info(f"  • {'Max Drawdown:':<24} {RED}{md_display:>15}{ENDC}")
        self.logger.info(f"  • {'Return on Initial (ROI):':<24} {roi_color}{roi:>15,.2f}%{ENDC}")
        self.logger.info(f"  • {'Trade Count:':<24} {total_trades:>15}{ENDC}")

        self.logger.info(main_divider)
        print(f"{BOLD}{'#' * 80}{ENDC}")


    def _plot_equity_curve(self):
        """Generates and saves a plot of the equity curve with clean, professional formatting."""
        portfolio = self.portfolio
        if len(portfolio.equity_curve) <= 1 or len(portfolio.equity_timestamps) == 0:
            self.logger.warning("Not enough equity data points for plotting (<= 1). Skipping plot.")
            return

        try:
            # Prepare equity data
            dates = [datetime.utcfromtimestamp(ts) for ts in portfolio.equity_timestamps]
            equity_values = portfolio.equity_curve

            # Debug the equity data before truncation
            self.logger.debug(f"Equity curve data before truncation: timestamps={len(dates)}, values={len(equity_values)}")

            # Handle length mismatch
            if len(dates) != len(equity_values):
                self.logger.warning(f"Length mismatch equity data: timestamps={len(dates)}, values={len(equity_values)}. Truncating.")
                min_len = min(len(dates), len(equity_values))
                dates = dates[:min_len]
                equity_values = equity_values[:min_len]

                # Debug the equity data after truncation
                self.logger.debug(f"Equity curve data after truncation: timestamps={len(dates)}, values={len(equity_values)}")

                if len(equity_values) <= 1:
                    self.logger.error("Not enough equity data points after truncation. Skipping plot.")
                    return  # Skip if still not enough

            # Create figure with clean styling
            plt.style.use('seaborn-v0_8-whitegrid')
            fig, ax = plt.subplots(figsize=(12, 6))

            # Calculate performance metrics
            initial_balance = portfolio.initial_balance
            final_balance = portfolio.balance
            positive_performance = final_balance >= initial_balance
            roi_pct = ((final_balance / initial_balance) - 1) * 100

            # Plot equity curve with appropriate color
            line_color = '#27ae60' if positive_performance else '#e74c3c'  # Green or red
            ax.plot(dates, equity_values, color=line_color, linewidth=2.0, label='Equity')

            # Add initial balance reference line
            ax.axhline(y=initial_balance, color='#7f8c8d', linestyle='--', alpha=0.7,
                      label=f'Initial: ${initial_balance:,.0f}')

            # Style the plot
            ax.set_title('Equity Curve', fontsize=14, fontweight='bold')
            ax.set_xlabel('Date', fontsize=12)
            ax.set_ylabel('Balance ($)', fontsize=12)

            # Format Y-axis with better scale
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'${x:,.0f}'))

            # Add padding to prevent overlap with axes
            plt.tight_layout(pad=3.0)

            # Create a simple stats text box at the top right
            stats_text = f"ROI: {('+' if roi_pct >= 0 else '')}{roi_pct:.2f}%"
            if portfolio.trades:
                win_count = sum(1 for t in portfolio.trades if t.get('profit', 0) > 0)
                trade_count = len(portfolio.trades)
                win_rate = win_count / trade_count if trade_count > 0 else 0
                stats_text += f" | Trades: {trade_count} | Win Rate: {win_rate:.1%}"

            # Add the stats with colored background based on performance
            bbox_props = dict(
                boxstyle="round,pad=0.3",
                facecolor=((46/255, 204/255, 113/255, 0.2) if positive_performance
                           else (231/255, 76/255, 60/255, 0.2)),
                edgecolor='gray',
                alpha=0.8
            )
            ax.text(
                0.99, 0.97, stats_text,
                transform=ax.transAxes, fontsize=11,
                verticalalignment='top', horizontalalignment='right',
                bbox=bbox_props
            )

            # Add legend at top left, out of way of the plot
            ax.legend(loc='upper left', frameon=True, fontsize=10)

            # Ensure dates are properly formatted
            fig.autofmt_xdate()

            # Save plot
            cfg = self.config
            timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')

            # Build filename
            filename_parts = ["equity"]
            if cfg.strategies.use_tf_v2: filename_parts.append("tf")
            if cfg.strategies.use_mean_reversion: filename_parts.append("mr")
            if cfg.strategies.use_mean_variance: filename_parts.append("mv")
            filename_parts.append(f"det_{cfg.regime.detector_type}")
            if cfg.regime.use_strict_strategy_filtering: filename_parts.append("Sfilt")
            if cfg.regime.pause_in_chop: filename_parts.append("PChop")
            filename = "_".join(filename_parts) + f"_{timestamp_str}.png"

            # Ensure log directory exists
            log_dir = Path(cfg.data_paths.log_dir)
            log_dir.mkdir(parents=True, exist_ok=True)
            plot_path = log_dir / filename

            # Save with reasonable DPI
            plt.savefig(plot_path, dpi=120, bbox_inches='tight')
            plt.close()

            self.logger.info(f"Enhanced equity curve plot saved to {plot_path}")

        except Exception as e:
            self.logger.error(f"Failed to plot equity curve: {e}", exc_info=True)


    def _analyze_market_conditions(self):
        """Analyzes market conditions during losing trades."""
        # ANSI color codes for consistent formatting
        CYAN = '\033[96m'
        BOLD = '\033[1m'
        ENDC = '\033[0m'  # End color
        GREEN = '\033[92m'
        RED = '\033[91m'
        YELLOW = '\033[93m'
        section_divider = f"{BOLD}{'─' * 80}{ENDC}"

        try:
            # --- Winning Trade Analysis ---
            self.logger.info(f"\n{BOLD}{CYAN}{'WINNING TRADE ANALYSIS'.center(80)}{ENDC}")
            self.logger.info(section_divider)
            winning_trades = [t for t in self.portfolio.trades if t['profit'] > 0]
            if winning_trades:
                df_win = pd.DataFrame(winning_trades)
                self.logger.info(f"\n{BOLD}Winning Trades by Entry Regime:{ENDC}")
                if 'entry_regime' in df_win.columns:
                    for regime, count in df_win['entry_regime'].value_counts().items():
                        self.logger.info(f"  • {regime:<20} {count:>4}")
                else:
                    self.logger.info("  • No entry_regime data available.")
            else:
                self.logger.info("  No winning trades to analyze.")
            # Get losing trades
            losing_trades = [trade for trade in self.portfolio.trades if trade['profit'] < 0]
            if not losing_trades:
                self.logger.info("No losing trades to analyze.")
                return

            # Create dataframe of losing trades
            df_trades = pd.DataFrame(losing_trades)

            # Convert timestamps to datetime for analysis (UTC-naive to match signals DataFrame)
            df_trades['entry_dt'] = pd.to_datetime(df_trades['entry_time'], unit='s', utc=True).dt.tz_localize(None) # UTC-naive
            df_trades['exit_dt'] = pd.to_datetime(df_trades['exit_time'], unit='s', utc=True).dt.tz_localize(None) # UTC-naive

            # Find market conditions at trade entry
            # Ensure signals dataframe index is UTC-naive for matching
            signals_df_copy = self.all_signals_df.copy()
            if not signals_df_copy.empty:
                if signals_df_copy.index.tz is not None:
                    # PERFORMANCE OPTIMIZATION: Use vectorized timezone conversion instead of .map()
                    signals_df_copy.index = signals_df_copy.index.tz_convert('UTC').tz_localize(None)

                # Assert UTC-naive for fail-fast debugging
                assert signals_df_copy.index.tz is None, "Signals index should be UTC-naive for analysis"

            # Ensure df_trades entry_dt is timezone-aware (already done above)

            # Left join trade entries with market conditions at entry
            df_analysis = pd.merge_asof(
                df_trades.sort_values('entry_dt'),
                signals_df_copy,
                left_on='entry_dt', right_index=True, direction='nearest', tolerance=pd.Timedelta('2h'))

            # Save analysis to CSV for further inspection
            try:
                log_path = Path(self.config.data_paths.log_dir)
                timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
                analysis_file = log_path / f"losing_trades_analysis_{timestamp_str}.csv"
                df_analysis.to_csv(analysis_file)
                self.logger.info(f"Losing trades analysis saved to {analysis_file}")
            except Exception as csv_e:
                self.logger.warning(f"Failed to save analysis CSV: {csv_e}")

            # Format output with consistent styling
            self.logger.info(f"\n{BOLD}{CYAN}{'LOSING TRADE ANALYSIS'.center(80)}{ENDC}")
            self.logger.info(section_divider)

            # Regime analysis with bullet points
            self.logger.info(f"\n{BOLD}Losing Trades by Entry Regime:{ENDC}")
            if 'entry_regime' in df_analysis.columns:
                regime_counts = df_analysis['entry_regime'].value_counts()
                for idx, count in regime_counts.items():
                    self.logger.info(f"  • {idx:<20} {count:>4}")

            # Strategy analysis with bullet points
            self.logger.info(f"\n{BOLD}Losing Trades by Strategy:{ENDC}")
            if 'strategy' in df_analysis.columns:
                strategy_counts = df_analysis['strategy'].value_counts()
                for idx, count in strategy_counts.items():
                    self.logger.info(f"  • {idx:<20} {count:>4}")

        except Exception as e:
            self.logger.error(f"Error analyzing market conditions: {e}", exc_info=True)


    def _run_simulation_loop(self):
        """Runs the core backtesting loop, iterating through time steps."""
        self.logger.info("Starting simulation loop...")
        if self.all_signals_df.empty: self.logger.error("Cannot run simulation loop: all_signals_df is empty."); return

        total_steps = len(self.all_signals_df) - self.start_index
        log_interval = max(1, total_steps // 20)
        self.logger.info(f"Iterating from index {self.start_index} to {len(self.all_signals_df) - 1}")

        for i in range(self.start_index, len(self.all_signals_df)):
            timestamp = self.all_signals_df.index[i]
            current_signals_series = self.all_signals_df.iloc[i]
            current_step = i - self.start_index + 1
            current_signals = current_signals_series.to_dict()
            current_signals['timestamp'] = timestamp
            
            # Look-ahead bias detection assertion
            # Check if any data in current_signals has a future timestamp
            for key, value in current_signals.items():
                if hasattr(value, 'timestamp') and hasattr(value.timestamp, '__call__'):
                    data_ts = value.timestamp()
                    assert data_ts <= timestamp.timestamp(), \
                        f"Look-ahead bias detected: {key} has data from {data_ts} but current time is {timestamp.timestamp()}"

            if current_step == 1 or current_step == total_steps or current_step % log_interval == 0 :
                self.logger.info(f"Processing step {current_step}/{total_steps} (Index: {i}, Time: {timestamp}) | Balance: ${self.portfolio.balance:.2f}")

            # Log ATR and GMS values for debugging
            current_atr = current_signals.get('atr')
            current_regime = current_signals.get('regime')

            # Format ATR value safely
            if pd.isna(current_atr):
                atr_str = "None"
            else:
                atr_str = f"{current_atr:.2f}"

            # Format regime value safely
            regime_str = str(current_regime) if current_regime else "None"

            self.logger.debug(f"{timestamp} ATR={atr_str} GMS={regime_str}")

            current_price = current_signals.get('close')
            if pd.isna(current_price):
                self.logger.warning(f"Skipping step at {timestamp} (Index: {i}): Close price is NaN.")
                continue

            current_timestamp_unix = timestamp.timestamp()

            position_exited_this_step = False

            # --- Check Liquidation & Apply Funding (Only if position exists) ---
            if self.portfolio.position:
                try:
                    liquidation_triggered = self.portfolio.check_liquidation(current_signals)
                    if liquidation_triggered:
                         self.logger.warning(f"LIQUIDATION event at {timestamp}. Simulating close.")
                         liq_trigger_price = self.portfolio.get_mark_price(current_signals) or current_price
                         exit_side: Literal["buy", "sell"] = 'buy' if self.portfolio.position['type'] == 'short' else 'sell'
                         exit_size = self.portfolio.position['size']
                         # --- MODIFIED CALL to simulate_fill ---
                         trigger_price_for_sim = liq_trigger_price
                         self.logger.debug(f"Backtester: Preparing to simulate fill (liquidation). Trigger price used: {trigger_price_for_sim}")
                         raw_spread_rel = current_signals.get('raw_spread_rel')
                         raw_spread_abs = current_signals.get('raw_spread_abs')
                         fill_res = self.execution_simulator.simulate_fill(
                             exit_side,
                             exit_size,
                             liq_trigger_price,
                             timestamp,
                             raw_spread_rel,
                             raw_spread_abs,
                             current_signals.get('best_bid'),
                             current_signals.get('best_ask')
                         )
                         exit_fill_price = fill_res["fill_price"]
                         filled_size = fill_res["filled_size"]
                         slip_pnl = fill_res["slippage_pnl"]
                         diag_deltas = fill_res["diag_deltas"]
                         fee_mode = fill_res["fee_mode"]
                         self.portfolio.update_diagnostics(diag_deltas)
                         if exit_fill_price is None: exit_fill_price = liq_trigger_price
                         self.portfolio.handle_exit(exit_fill_price, "Maintenance Margin", slip_pnl, current_timestamp_unix, fee_mode, is_liquidation=True)
                         self.last_funding_time_unix = None
                         raise ValueError("Account Liquidated (MM)")

                    new_funding_time = self.portfolio.apply_funding(current_timestamp_unix, current_price, self.last_funding_time_unix)
                    self.last_funding_time_unix = new_funding_time

                except ValueError as e: self.logger.error(f"Backtest halted: {e}"); break
                except Exception as e: self.logger.error(f"Error during funding/liquidation check: {e}", exc_info=True); break

                # --- Check Exits (SL/TP/Time/Strategy) ---
                should_exit, exit_trigger_price, exit_reason = self._evaluate_exit(current_signals, self.portfolio.position, current_timestamp_unix)
                if should_exit:
                    self.logger.info(f"EXIT Triggered: {exit_reason}, Trigger Price: {exit_trigger_price:.4f} at {timestamp}")
                    exit_side: Literal["buy", "sell"] = 'buy' if self.portfolio.position['type'] == 'short' else 'sell'
                    exit_size = self.portfolio.position['size']
                    try:
                        # --- MODIFIED CALL to simulate_fill ---
                        trigger_price_for_sim = exit_trigger_price
                        self.logger.debug(f"Backtester: Preparing to simulate fill (exit). Trigger price used: {trigger_price_for_sim}")
                        raw_spread_rel = current_signals.get('raw_spread_rel')
                        raw_spread_abs = current_signals.get('raw_spread_abs')
                        fill_res = self.execution_simulator.simulate_fill(
                            exit_side,
                            exit_size,
                            exit_trigger_price,
                            timestamp,
                            raw_spread_rel,
                            raw_spread_abs,
                            current_signals.get('best_bid'),
                            current_signals.get('best_ask')
                        )
                        exit_fill_price = fill_res["fill_price"]
                        filled_size = fill_res["filled_size"]
                        slip_pnl = fill_res["slippage_pnl"]
                        diag_deltas = fill_res["diag_deltas"]
                        fee_mode = fill_res["fee_mode"]
                        self.portfolio.update_diagnostics(diag_deltas)
                        if exit_fill_price is None or filled_size < exit_size * 0.99: self.logger.error(f"EXIT FAILED or partial fill at {timestamp}. Halting."); break
                        self.portfolio.handle_exit(exit_fill_price, exit_reason, slip_pnl, current_timestamp_unix, fee_mode, is_liquidation=False)
                        self.last_funding_time_unix = None
                        position_exited_this_step = True
                    except ValueError: raise
                    except Exception as e: self.logger.error(f"Error during exit execution at {timestamp}: {e}", exc_info=True); raise

            # --- Check Entries ---
            if self.portfolio.position is None and not position_exited_this_step:
                # --- Regime Detection ---
                recent_prices: Optional[pd.Series] = None
                current_regime: str = "Unknown" # Default regime if detection fails
                try:
                    if self.config.regime.detector_type == 'hurst':
                        lookback = self.config.regime.hurst_lookback_periods
                        recent_prices = self.data_handler.get_recent_close_prices(timestamp, lookback)
                        if recent_prices is None or recent_prices.empty:
                             self.logger.warning(f"Could not retrieve recent prices for Hurst calculation at {timestamp}. Regime set to 'Unknown'.")
                        else:
                             current_regime = self.regime_detector.get_regime(current_signals, price_history=recent_prices)
                    else:
                        current_regime = self.regime_detector.get_regime(current_signals)
                except Exception as regime_e:
                     self.logger.error(f"Error during regime detection at {timestamp}: {regime_e}", exc_info=True)
                     current_regime = "Unknown"

                # Log the detected regime periodically
                if current_step == 1 or current_step % log_interval == 0:
                    self.logger.info(f"Step {current_step}: Detected Regime = '{current_regime}' (using {self.config.regime.detector_type} detector)")

                # Convert current_regime to a string if it's a dictionary
                if isinstance(current_regime, dict):
                    current_signals['regime'] = current_regime.get('state', 'Unknown')
                    # Extract confidence and duration if available (Phase 2)
                    if 'regime_confidence' in current_regime:
                        current_signals['regime_confidence'] = current_regime['regime_confidence']
                    if 'regime_duration_minutes' in current_regime:
                        current_signals['regime_duration_minutes'] = current_regime['regime_duration_minutes']
                else:
                    current_signals['regime'] = current_regime

                # Store the regime in the main signals DataFrame
                if isinstance(current_regime, dict):
                    self.all_signals_df.loc[timestamp, 'regime'] = current_regime.get('state', 'Unknown')
                else:
                    self.all_signals_df.loc[timestamp, 'regime'] = current_regime

                # --- Entry Logic ---
                perform_entry_check = True

                # Get the regime state as a string
                regime_state = current_signals['regime']  # We've already converted it to a string above

                if regime_state == "Volatile_Chop" and self.config.regime.detector_type == 'rule_based':
                    if self.config.regime.pause_in_chop and self.config.regime.use_strict_strategy_filtering:
                        perform_entry_check = False
                elif regime_state == "Unknown":
                     perform_entry_check = False
                elif regime_state == "WIDE_SPREAD":
                     pass

                if perform_entry_check:
                    # Use the regime state string for strategy evaluation
                    self.logger.info(f"Calling get_active_strategies with regime_state: {regime_state}")
                    active_strategy_names = self.strategy_evaluator.get_active_strategies(regime_state)
                    self.logger.info(f"Active strategy names: {active_strategy_names}")
                    
                    # Get the mapped regime for strategies (BULL/BEAR/CHOP)
                    # If we have active strategies and GMS mapping is active, get the mapped regime
                    if active_strategy_names and self.strategy_evaluator.gms_mapping_active:
                        # Map the regime state to BULL/BEAR/CHOP
                        if (self.regime_detector and 
                            hasattr(self.regime_detector, 'state_collapse_map') and 
                            self.regime_detector.state_collapse_map and 
                            'state_map' in self.regime_detector.state_collapse_map):
                            # Use the detector's loaded state map
                            state_map = self.regime_detector.state_collapse_map['state_map']
                            mapped_regime = state_map.get(regime_state, 'CHOP')
                            if mapped_regime != regime_state:
                                self.logger.info(f"Mapped regime for strategies: {regime_state} -> {mapped_regime}")
                                # Update the signals dict with the mapped regime for strategy use
                                current_signals['regime'] = mapped_regime
                        else:
                            # Fallback: use the utility (though this might use wrong mapping)
                            from hyperliquid_bot.utils.state_mapping import map_gms_state
                            mapped_regime = map_gms_state(regime_state)
                            if mapped_regime != regime_state:
                                self.logger.info(f"Mapped regime for strategies (fallback): {regime_state} -> {mapped_regime}")
                                current_signals['regime'] = mapped_regime
                    
                    if active_strategy_names:
                        for strategy_name in active_strategy_names:
                            strategy_instance = self.strategy_evaluator.get_strategy(strategy_name)
                            if not strategy_instance:
                                self.logger.info(f"Strategy {strategy_name} not found")
                                continue
                            required_now = strategy_instance.required_signals
                            missing_now = [s for s in required_now if s not in current_signals or pd.isna(current_signals.get(s))]
                            if missing_now:
                                self.logger.info(f"Strategy {strategy_name} missing signals: {missing_now}")

                                # Add missing signals with default values for testing
                                if 'volume' in missing_now:
                                    current_signals['volume'] = 100.0  # Default volume

                                if 'regime_timestamp' in missing_now:
                                    current_signals['regime_timestamp'] = timestamp  # Use current timestamp

                                if 'risk_suppressed' in missing_now:
                                    current_signals['risk_suppressed'] = False  # Default not suppressed

                                if 'ohlcv_history' in missing_now:
                                    # Instead of creating a DataFrame, just use None
                                    # The strategy should handle None values for ohlcv_history
                                    current_signals['ohlcv_history'] = None

                                # Check if we still have missing signals
                                missing_now = [s for s in required_now if s not in current_signals or pd.isna(current_signals.get(s))]
                                if missing_now:
                                    self.logger.info(f"Strategy {strategy_name} still missing signals after defaults: {missing_now}")
                                    continue # Skip strategy if signals still missing

                            try:
                                direction, strategy_info = strategy_instance.evaluate(current_signals)
                                if direction:
                                    self.logger.info(f"SIGNAL: Strat='{strategy_name}', Dir='{direction}', Regime='{current_signals['regime']}' at {timestamp}")
                            except SkipSignal as e:
                                # Log the skip reason
                                self.skip_logger.log_skip(
                                    timestamp=timestamp,
                                    strategy=strategy_name,
                                    reason=str(e)
                                )
                                self.logger.debug(f"Strategy {strategy_name} skipped at {timestamp}: {str(e)}")
                                continue

                            if direction:
                                try:
                                    size, leverage = self.risk_manager.calculate_position(self.portfolio, current_signals, strategy_name, current_signals['regime'], strategy_info)
                                    if size is not None and size > 0 and leverage is not None and leverage > 0:
                                        # NEW: Refine execution timing if enabled
                                        current_signals['timestamp'] = timestamp  # Add timestamp to signals
                                        entry_trigger_price = self._refine_execution(current_signals, direction, current_signals)
                                        
                                        if not pd.isna(entry_trigger_price):
                                            entry_side: Literal["buy", "sell"] = 'buy' if direction == 'long' else 'sell'
                                            # Calculate stop loss and take profit before simulating fill
                                            sl_price, tp_price = self._calculate_sl_tp(current_signals, direction, strategy_name)
                                            if sl_price is None or tp_price is None:
                                                self.logger.warning(f"SL/TP calculation failed for {strategy_name}. Skipping entry.")
                                                continue
                                            # --- MODIFIED CALL to simulate_fill ---
                                            trigger_price_for_sim = entry_trigger_price
                                            self.logger.debug(f"Backtester: Preparing to simulate fill (entry). Trigger price used: {trigger_price_for_sim}")
                                            raw_spread_rel = current_signals.get('raw_spread_rel')
                                            raw_spread_abs = current_signals.get('raw_spread_abs')
                                            fill_res = self.execution_simulator.simulate_fill(
                                                entry_side,
                                                size,
                                                entry_trigger_price,
                                                timestamp,
                                                raw_spread_rel,
                                                raw_spread_abs,
                                                current_signals.get('best_bid'),
                                                current_signals.get('best_ask')
                                            )
                                            entry_fill_price = fill_res["fill_price"]
                                            filled_size = fill_res["filled_size"]
                                            slip_pnl = fill_res["slippage_pnl"]
                                            diag_deltas = fill_res["diag_deltas"]
                                            fee_mode = fill_res["fee_mode"]
                                            self.portfolio.update_diagnostics(diag_deltas)

                                            if entry_fill_price is not None and filled_size > size * 0.89:
                                                 position_dict = {"type": direction, "entry": entry_fill_price, "size": filled_size, "leverage": leverage, "stop": sl_price, "profit": tp_price, "entry_time": current_timestamp_unix, "strategy": strategy_name, "entry_fee": np.nan, "fee_mode": fee_mode, "entry_regime": current_signals['regime'], "entry_slippage_pnl": slip_pnl}
                                                 self.portfolio.handle_entry(position_dict, entry_fill_price, filled_size, slip_pnl, current_timestamp_unix)
                                                 self.last_funding_time_unix = current_timestamp_unix
                                                 break
                                            else: self.logger.warning(f"Entry fill failed or partial for {strategy_name}. FillPx={entry_fill_price}, FilledSz={filled_size}/{size}. Skipping.")
                                        else: self.logger.error("Entry trigger price is NaN. Skipping entry.")
                                    else: self.logger.warning(f"Risk manager returned invalid size/leverage for {strategy_name}. Skipping.")
                                except ValueError: raise
                                except Exception as risk_e:
                                     self.logger.error(f"Error during RiskManager/Entry for {strategy_name}: {risk_e}", exc_info=True)
                                     continue

            # --- Record Equity ---
            self.portfolio._record_equity(current_timestamp_unix)

        self.logger.info("Simulation loop finished.") # This should now be reached correctly


    def run(self, start_date: datetime, end_date: datetime):
        """Runs the entire backtesting process for the given date range."""
        random.seed(42)
        self.logger.info(f"--- Starting Backtest Run --- (Range: {start_date.strftime('%Y-%m-%d')} to {(end_date - timedelta(days=1)).strftime('%Y-%m-%d')})")
        start_time = time.time()
        try:
            self.portfolio = Portfolio(self.config, self.data_handler)
            self.last_funding_time_unix = None
            self.logger.info("Portfolio re-initialized for new run.")
            
            # Perform configuration validation to detect fallbacks
            base_config_path = Path(__file__).parent.parent.parent / "configs" / "base.yaml"
            validator = ConfigValidator(base_config_path=base_config_path, strict_mode=False)
            issues = validator.validate_config(self.config)
            
            # Log validation summary
            if not issues:
                self.logger.info("✅ No configuration fallbacks detected - using intended settings")
            else:
                error_count = sum(1 for issue in issues if issue.severity == "ERROR")
                warning_count = sum(1 for issue in issues if issue.severity == "WARNING")
                self.logger.error(f"❌ Configuration validation found {error_count} errors, {warning_count} warnings")
                
                # Log detailed report
                report = validator.get_validation_report()
                for line in report.split('\n'):
                    self.logger.info(line)
            
            # Pass the dates to the data loading method
            self._load_and_prepare_data(start_date, end_date)

            # Record initial equity point correctly
            if not self.all_signals_df.empty:
                 initial_timestamp_unix = self.all_signals_df.index[0].timestamp()
                 if not self.portfolio.equity_timestamps or not self.portfolio.equity_curve:
                      self.portfolio.equity_curve = [self.portfolio.initial_balance]
                      self.portfolio.equity_timestamps = [initial_timestamp_unix]
                      self.logger.debug(f"Recording initial equity point at {datetime.utcfromtimestamp(initial_timestamp_unix)}")

            self._run_simulation_loop()
            # --- Save final signals, trades, and skip reasons before reporting ---
            log_dir = Path(self.config.data_paths.log_dir)
            run_timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
            signals_output_path = log_dir / f"backtest_signals_{run_timestamp}.parquet"
            trades_output_path = log_dir / f"backtest_trades_{run_timestamp}.json"

            # Save skip reasons to CSV
            if start_date and end_date:
                # Use the first date of the backtest for the skip reasons filename
                date_str = start_date.strftime("%Y%m%d")
                skip_reasons_path = self.skip_logger.save_to_csv(date_str)
                if skip_reasons_path:
                    self.logger.info(f"Skip reasons saved to {skip_reasons_path}")
            # Save signals DataFrame
            if hasattr(self, 'all_signals_df') and self.all_signals_df is not None and not self.all_signals_df.empty:
                try:
                    self.logger.info(f"Saving {len(self.all_signals_df)} signals to {signals_output_path}")
                    self.all_signals_df.to_parquet(signals_output_path, index=True)
                except Exception as e:
                    self.logger.error(f"Error saving signals DataFrame to {signals_output_path}: {e}", exc_info=True)
            else:
                self.logger.warning("Final signals DataFrame is empty or None. Skipping save.")
            # Save trades list
            if self.portfolio.trades:
                try:
                    self.logger.info(f"Saving {len(self.portfolio.trades)} trades to {trades_output_path}")
                    with open(trades_output_path, 'w') as f:
                        json.dump(self.portfolio.trades, f, indent=4)
                    self.logger.info("Trades list saved successfully.")
                except Exception as e:
                    self.logger.error(f"Failed to save trades list: {e}", exc_info=True)
            else:
                self.logger.warning("Trades list is empty. Skipping save.")

        except KeyboardInterrupt: self.logger.warning("Backtest interrupted by user.")
        except ValueError as e: self.logger.error(f"BACKTEST HALTED due to error: {e}")
        except Exception as e: self.logger.critical(f"Backtest run failed with unexpected error: {e}", exc_info=True)
        finally:
            end_time = time.time()
            self.logger.info(f"--- Backtest Run Finished ({end_time - start_time:.2f} seconds) ---")
            # Log strategy summaries
            GREEN = '\033[92m'; BLUE = '\033[94m'; CYAN = '\033[96m'; BOLD = '\033[1m'; UNDERLINE = '\033[4m'; ENDC = '\033[0m'
            main_divider = f"\n{BOLD}{'═' * 80}{ENDC}"; section_divider = f"{BOLD}{'─' * 80}{ENDC}"
            self.logger.info(f"\n{BOLD}{CYAN}{'STRATEGY EVALUATION SUMMARY'.center(80)}{ENDC}"); self.logger.info(section_divider)
            for strat_name, strat_instance in self.strategy_evaluator.strategies.items():
                 log_summary_method = getattr(strat_instance, "log_evaluation_summary", None)
                 if callable(log_summary_method): log_summary_method()
                 else: self.logger.debug(f"Strategy '{strat_name}' has no log_evaluation_summary method.")
            # Force close any open positions at the end of the backtest
            if self.portfolio.position:
                self.logger.info("Force-closing open position at the end of the backtest...")
                final_price = self.all_signals_df.iloc[-1].get('close')

                if not pd.isna(final_price):
                    exit_side = 'buy' if self.portfolio.position['type'] == 'short' else 'sell'
                    exit_size = self.portfolio.position['size']

                    # Simulate the fill
                    fill_res = self.execution_simulator.simulate_fill(
                        exit_side,
                        exit_size,
                        final_price,
                        self.all_signals_df.index[-1],
                        self.all_signals_df.iloc[-1].get('raw_spread_rel'),
                        self.all_signals_df.iloc[-1].get('raw_spread_abs'),
                        self.all_signals_df.iloc[-1].get('best_bid'),
                        self.all_signals_df.iloc[-1].get('best_ask')
                    )

                    exit_fill_price = fill_res["fill_price"] or final_price
                    slip_pnl = fill_res["slippage_pnl"]
                    fee_mode = fill_res["fee_mode"]
                    diag_deltas = fill_res["diag_deltas"]

                    self.portfolio.update_diagnostics(diag_deltas)
                    self.portfolio.handle_exit(
                        exit_fill_price,
                        "backtest_end",
                        slip_pnl,
                        self.all_signals_df.index[-1].timestamp(),
                        fee_mode
                    )

                    self.logger.info(f"Closed position at end of backtest: {exit_side} {exit_size} at {exit_fill_price}")
                else:
                    self.logger.warning("Could not force-close position: final price is NaN")

            # Record final equity after potentially closing positions
            if not self.all_signals_df.empty:
                 final_ts_unix = self.all_signals_df.index[-1].timestamp()
                 if not self.portfolio.equity_timestamps or self.portfolio.equity_timestamps[-1] < final_ts_unix: self.portfolio._record_equity(final_ts_unix)
            # Generate reports
            try:
                self.logger.info("Generating performance report...")
                steps_processed = len(self.all_signals_df) - self.start_index if not self.all_signals_df.empty else 0
                self._compute_and_log_metrics(steps_processed)
                self._plot_equity_curve()
                if self.config.analysis.analyze_trades_after_backtest and self.portfolio.trades: self._analyze_market_conditions()
            except AttributeError as attr_err: self.logger.error(f"Reporting failed: Missing method - {attr_err}", exc_info=False)
            except Exception as report_e: self.logger.error(f"Error during reporting/analysis: {report_e}", exc_info=True)

# --- setup_logging remains the same ---
def setup_logging(config: Config):
    log_level = logging.INFO # Or get from config
    log_format = '%(asctime)s [%(levelname)-5s] %(name)-30s: %(message)s'
    logging.basicConfig(level=log_level, format=log_format, force=True) # Use force=True to override basicConfig if called before
    log_dir = Path(config.data_paths.log_dir); log_dir.mkdir(parents=True, exist_ok=True)
    log_file = log_dir / f"backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    file_handler = logging.FileHandler(log_file); file_handler.setLevel(log_level)
    file_handler.setFormatter(logging.Formatter(log_format))
    logging.getLogger().addHandler(file_handler)
    logging.info(f"Logging to console and file: {log_file}")


# --- main remains the same ---
def main():
    try:
        project_root = Path(__file__).resolve().parent.parent.parent # Adjust based on actual structure
        config_path = project_root / 'config.yaml'
        if not config_path.exists(): # Try one level up if not found
             project_root = Path(__file__).resolve().parent.parent
             config_path = project_root / 'config.yaml'
        if not config_path.exists(): raise FileNotFoundError(f"Config not found near {__file__}")

        # Load config first to setup logging properly
        from hyperliquid_bot.config.settings import load_config
        import yaml

        # Basic config load for logging setup
        temp_config = Config.model_validate(yaml.safe_load(open(config_path))) # Basic validation for logging setup
        setup_logging(temp_config) # Setup logging using loaded config

        # Full load and validation
        config = load_config(str(config_path))

        backtester = Backtester(config)
        backtester.run(config.start_date, config.end_date)

    except FileNotFoundError as e: print(f"ERROR: Configuration file not found. {e}"); logging.critical(f"Configuration file not found. {e}")
    except Exception as e: print(f"An unexpected error occurred: {e}"); logging.critical(f"An unexpected error occurred: {e}", exc_info=True)

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)-5s] %(name)-30s: %(message)s')
    main()