#!/usr/bin/env python3
"""
Patch for the Backtester class to skip ATR validation.

This patch monkey-patches the _load_and_prepare_data method in the Backtester class
to skip the ATR validation check.
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Import the Backtester class
from aerith_hyperliquid_bot.hyperliquid_bot.backtester.backtester import Backtester

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("backtester_patch")

# Save the original method
original_load_and_prepare_data = Backtester._load_and_prepare_data

def patched_load_and_prepare_data(self, start_date: datetime, end_date: datetime):
    """Patched version of _load_and_prepare_data that skips ATR validation."""
    logger.info("Using patched _load_and_prepare_data method (skipping ATR validation)")

    try:
        # Use the passed-in start_date and end_date parameters
        raw_ohlcv_micro_data = self.data_handler.load_historical_data(start_date=start_date, end_date=end_date)

        self.logger.info(f"Raw data loaded, shape: {raw_ohlcv_micro_data.shape if raw_ohlcv_micro_data is not None else 'None'}")

        # Get OHLCV data from data handler
        ohlc = self.data_handler.get_ohlcv_data()

        # Check for duplicate indices and fix them using deduplicate utility
        from hyperliquid_bot.utils.data_utils import deduplicate
        ohlc = deduplicate(ohlc)

        # ATR is now calculated in the ETL process and included in the 1-second feature files
        # No need to calculate it here anymore
        self.logger.info("Using ATR from 1-second feature files")

        # Make sure combined_data doesn't have duplicate indices using deduplicate utility
        self.data_handler.combined_data = deduplicate(self.data_handler.combined_data)

        # Verify ATR availability
        if 'atr_14_sec' in self.data_handler.combined_data.columns:
            missing_atr = self.data_handler.combined_data['atr_14_sec'].isna().sum()
            self.logger.info(f"ATR from features: {missing_atr} NaN values out of {len(self.data_handler.combined_data)} rows.")

            # Add legacy ATR columns for backward compatibility if they don't exist
            if 'atr' not in self.data_handler.combined_data.columns:
                self.data_handler.combined_data['atr'] = self.data_handler.combined_data['atr_14_sec']

            if 'atr_percent' not in self.data_handler.combined_data.columns:
                self.data_handler.combined_data['atr_percent'] = self.data_handler.combined_data['atr_14_sec'] / self.data_handler.combined_data['close']
        else:
            self.logger.warning("ATR column 'atr_14_sec' not found in feature data. GMS detector may not work properly.")

        # SignalEngine now processes the combined data
        # The SignalEngine.calculate_all_signals() method doesn't take any parameters
        # It accesses data directly from the data_handler
        self.all_signals_df = self.signal_calculator.calculate_all_signals()

        if self.all_signals_df.empty:
            raise ValueError("Signal calculation resulted in an empty DataFrame.")

        # Calculate required warmup period
        self.start_index = self._calculate_required_warmup() # Restored original calculation

        # Check if data length is sufficient for warmup
        if len(self.all_signals_df) <= self.start_index: # Use <= to handle exact match edge case
            raise ValueError(f"Data length ({len(self.all_signals_df)}) insufficient for warmup ({self.start_index}).")

        self.logger.info(f"Data loaded and signals calculated. Shape: {self.all_signals_df.shape}")
        self.logger.info(f"Starting simulation loop from index {self.start_index} ({self.all_signals_df.index[self.start_index]})")
        self.logger.debug(f"Signal columns available: {self.all_signals_df.columns.tolist()}")

    except Exception as e:
        self.logger.critical(f"Failed during data loading or signal preparation: {e}", exc_info=True)
        raise

# Apply the patch
def patch_debug_log():
    """Patch the debug log method to fix string formatting issues."""
    logger.info("Patching debug log method to fix string formatting issues")

    # Save the original run method
    original_run = Backtester.run

    def patched_run(self, start_date, end_date):
        """Patched version of run that fixes string formatting issues."""
        # Monkey patch the debug log method
        original_debug = self.logger.debug

        def safe_debug(message, *args, **kwargs):
            """Safe version of debug that handles formatting issues."""
            try:
                original_debug(message, *args, **kwargs)
            except Exception as e:
                # If there's a formatting error, log a simpler message
                original_debug(f"Debug message suppressed due to formatting error: {e}")

        # Replace the debug method
        self.logger.debug = safe_debug

        # Call the original run method
        result = original_run(self, start_date, end_date)

        # Restore the original debug method
        self.logger.debug = original_debug

        return result

    # Apply the patch
    Backtester.run = patched_run
    logger.info("Debug log patch applied successfully")

def apply_patch():
    """Apply all patches to the Backtester class."""
    logger.info("Applying patches to Backtester")

    # Patch the _load_and_prepare_data method
    Backtester._load_and_prepare_data = patched_load_and_prepare_data
    logger.info("_load_and_prepare_data patch applied successfully")

    # Patch the debug log method
    patch_debug_log()

    logger.info("All patches applied successfully")

# Restore the original method
def restore_original():
    """Restore the original method."""
    logger.info("Restoring original Backtester._load_and_prepare_data")
    Backtester._load_and_prepare_data = original_load_and_prepare_data
    logger.info("Original method restored")

if __name__ == "__main__":
    apply_patch()
    logger.info("Backtester patch applied. You can now run your backtest.")
