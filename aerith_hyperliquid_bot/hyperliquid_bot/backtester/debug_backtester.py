"""
Debug-enhanced backtester for market bias testing

This module extends the standard Backtester with debugging features
specifically for market bias component testing.
"""

import logging
from hyperliquid_bot.backtester.backtester import Backtester
from hyperliquid_bot.core.debug_risk import DebugRiskManager

class DebugBacktester(Backtester):
    """Enhanced Backtester for market bias debugging"""
    
    def __init__(self, config):
        """Initialize with debug-specific settings"""
        super().__init__(config)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Replace standard risk manager with debug version
        self.logger.info("🔍 Initializing Debug Backtester with enhanced logging")
        self.risk_manager = DebugRiskManager(config=config)
        self.logger.info("🔍 Using DebugRiskManager for enhanced market bias logging")
    
    def _process_signals_for_entry(self, current_signals, strategy_name, strategy_info):
        """
        Enhanced version that ensures direction information is included in strategy_info
        """
        # First ensure strategy_info is a dictionary
        if strategy_info is None:
            strategy_info = {}
            
        # Get position signal to determine direction
        position_signal = current_signals.get(f"{strategy_name}_position", 0)
        
        # Add direction to strategy_info if not already present
        if 'direction' not in strategy_info:
            direction = 'long' if position_signal > 0 else 'short' if position_signal < 0 else 'flat'
            strategy_info['direction'] = direction
            self.logger.info(f"DIRECTION DEBUG: Adding direction '{direction}' to strategy_info for {strategy_name}")
        
        # Call parent method with enhanced strategy_info
        return super()._process_signals_for_entry(current_signals, strategy_name, strategy_info)
