# hyperliquid_bot/utils/regime_analytics.py
# Analytics tools for GMS state transitions and regime analysis

import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Optional, Any
from pathlib import Path
import json
import os

# Import state mapping utilities
from hyperliquid_bot.utils.state_mapping import (
    get_state_map, map_gms_state,
    GMS_STATE_TIGHT_SPREAD
)

# Setup logging
logger = logging.getLogger(__name__)

class RegimeAnalytics:
    """
    Class for analyzing regime/state transitions and patterns in backtest data.
    Provides detailed analytics on GMS state behavior without modifying core logic.
    """
    
    def __init__(self, signals_df: pd.DataFrame, output_dir: Optional[Path] = None, map_weak_bear_to_bear: bool = False):
        """
        Initialize the regime analytics with signals dataframe.
        
        Args:
            signals_df: DataFrame containing backtest signals with 'regime' column
            output_dir: Directory to save analytics outputs (defaults to logs directory)
            map_weak_bear_to_bear: Whether to map Weak_Bear_Trend to BEAR instead of CHOP
        """
        self.signals_df = signals_df
        self.output_dir = output_dir or Path("/Users/<USER>/Desktop/trading_bot_/logs")
        self.map_weak_bear_to_bear = map_weak_bear_to_bear
        self.state_map = get_state_map(map_weak_bear_to_bear=map_weak_bear_to_bear)
        
        # Log the mapping configuration being used
        logger.info(f"RegimeAnalytics initialized - Weak_Bear_Trend mapping to {'BEAR' if map_weak_bear_to_bear else 'CHOP'}")
        if map_weak_bear_to_bear:
            weak_bear_mapped_to = self.state_map.get('Weak_Bear_Trend', 'Unknown')
            logger.info(f"Confirmed Weak_Bear_Trend is mapped to: {weak_bear_mapped_to}")

        self._validate_input_data()
        
    def _validate_input_data(self) -> None:
        """Validates that the input data has the necessary columns."""
        required_columns = ['regime']
        missing_columns = [col for col in required_columns if col not in self.signals_df.columns]
        if missing_columns:
            logger.warning(f"Missing required columns in signals data: {missing_columns}")
            raise ValueError(f"Missing required columns for regime analytics: {missing_columns}")
    
    def analyze_regime_transitions(self) -> Dict[str, Any]:
        """
        Analyzes transitions between regime states in the backtest data.
        
        Returns:
            Dict containing analytics results
        """
        # Ensure the dataframe is sorted by timestamp
        if not self.signals_df.index.is_monotonic_increasing:
            self.signals_df = self.signals_df.sort_index()
        
        results = {}
        
        # Get regime data and create a 7-state to 3-state mapping
        regimes = self.signals_df['regime']
        regimes_3state = regimes.apply(lambda x: map_gms_state(x, map_weak_bear_to_bear=self.map_weak_bear_to_bear))
        
        # Add 3-state mapping to dataframe for reference
        self.signals_df['regime_3state'] = regimes_3state
        
        # 1. Count of each regime state
        regime_counts = regimes.value_counts().to_dict()
        regime_3state_counts = regimes_3state.value_counts().to_dict()
        
        results['regime_counts'] = regime_counts
        results['regime_3state_counts'] = regime_3state_counts
        
        # 2. Analyze state transitions
        transitions = []
        for i in range(1, len(regimes)):
            if regimes.iloc[i] != regimes.iloc[i-1]:
                transitions.append((
                    regimes.index[i].isoformat(),
                    regimes.iloc[i-1],
                    regimes.iloc[i],
                    map_gms_state(regimes.iloc[i-1], map_weak_bear_to_bear=self.map_weak_bear_to_bear),
                    map_gms_state(regimes.iloc[i], map_weak_bear_to_bear=self.map_weak_bear_to_bear)
                ))
        
        results['transitions'] = transitions
        results['transition_count'] = len(transitions)
        
        # 3. Analyze transition patterns
        transition_patterns = {}
        for _, from_state, to_state, from_3state, to_3state in transitions:
            key = f"{from_state} → {to_state}"
            if key not in transition_patterns:
                transition_patterns[key] = {
                    'count': 0,
                    'from_state': from_state,
                    'to_state': to_state,
                    'from_3state': from_3state,
                    'to_3state': to_3state
                }
            transition_patterns[key]['count'] += 1
        
        # Convert to list and sort by count
        transition_patterns_list = list(transition_patterns.values())
        transition_patterns_list.sort(key=lambda x: x['count'], reverse=True)
        results['transition_patterns'] = transition_patterns_list
        
        # 4. Special analysis for TIGHT_SPREAD transitions
        tight_spread_transitions = [t for t in transitions if 
                                   t[1] == GMS_STATE_TIGHT_SPREAD or 
                                   t[2] == GMS_STATE_TIGHT_SPREAD]
        
        results['tight_spread_transitions'] = tight_spread_transitions
        results['tight_spread_transition_count'] = len(tight_spread_transitions)
        
        # 5. Calculate state durations
        state_changes = regimes.ne(regimes.shift()).cumsum()
        state_durations = regimes.groupby([state_changes, regimes]).size()
        
        # Convert to more usable format
        duration_stats = {}
        for state in regimes.unique():
            durations = [state_durations[group, state] for group in state_durations.index.levels[0] 
                        if (group, state) in state_durations.index]
            if durations:
                duration_stats[state] = {
                    'mean': np.mean(durations),
                    'median': np.median(durations),
                    'min': np.min(durations),
                    'max': np.max(durations),
                    'total_instances': len(durations)
                }
        
        results['state_durations'] = duration_stats
        
        return results
    
    def generate_regime_analytics_report(self) -> str:
        """
        Generates a complete report on regime analytics.
        
        Returns:
            Path to the generated report file
        """
        # Run the analysis
        analytics = self.analyze_regime_transitions()
        
        # Create report filename
        timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
        report_filename = f"regime_analytics_{timestamp}.json"
        report_path = self.output_dir / report_filename
        
        # Prepare data for JSON serialization
        for key, value in analytics.items():
            if isinstance(value, np.integer):
                analytics[key] = int(value)
            elif isinstance(value, np.floating):
                analytics[key] = float(value)
        
        # Convert transition timestamps to strings if needed
        if 'transitions' in analytics:
            for i, transition in enumerate(analytics['transitions']):
                if not isinstance(transition[0], str):
                    analytics['transitions'][i] = (
                        str(transition[0]),
                        transition[1],
                        transition[2],
                        transition[3],
                        transition[4]
                    )
        
        # Save the report
        try:
            os.makedirs(self.output_dir, exist_ok=True)
            with open(report_path, 'w') as f:
                json.dump(analytics, f, indent=2)
            logger.info(f"Saved regime analytics report to {report_path}")
            return str(report_path)
        except Exception as e:
            logger.error(f"Failed to save regime analytics report: {e}")
            return ""
    
    def generate_transition_timeline(self, output_path: Optional[str] = None) -> str:
        """
        Generates a timeline visualization of regime transitions.
        
        Args:
            output_path: Optional specific path for the output file
            
        Returns:
            Path to the generated visualization file
        """
        if self.signals_df.empty:
            logger.warning("Cannot generate transition timeline: signals dataframe is empty")
            return ""
        
        # Create the figure with better layout
        fig, ax = plt.subplots(figsize=(16, 8))
        
        # Define colors for different regime types - matching the main visualization
        regime_colors_7state = {
            'Strong_Bull_Trend': (0.0, 1.0, 0.0, 0.85),      # Green
            'Weak_Bull_Trend': (0.56, 0.93, 0.56, 0.85),    # Light Green
            'Strong_Bear_Trend': (1.0, 0.0, 0.0, 0.85),     # Red
            'Weak_Bear_Trend': (1.0, 0.71, 0.76, 0.85),     # Light Red
            'High_Vol_Range': (1.0, 0.65, 0.0, 0.85),       # Orange
            'Low_Vol_Range': (0.68, 0.85, 0.9, 0.85),       # Light Blue
            'TIGHT_SPREAD': (1.0, 1.0, 0.8, 0.85),          # Light Yellow
            'Uncertain': (0.83, 0.83, 0.83, 0.85),          # Light Grey
        }
        
        regime_colors_3state = {
            'BULL': (0.0, 1.0, 0.0, 0.85),     # Green
            'BEAR': (1.0, 0.0, 0.0, 0.85),     # Red
            'CHOP': (1.0, 0.65, 0.0, 0.85),    # Orange
        }
        
        # Plot price line if available
        if 'close' in self.signals_df.columns:
            price_line = ax.plot(self.signals_df.index, self.signals_df['close'], 
                               color='blue', linewidth=2, label='Price', zorder=10)
            ax.set_ylabel('Price', fontsize=12)
        
        # Create a twin axis for regime visualization
        ax2 = ax.twinx()
        ax2.set_ylim(0, 1)
        ax2.set_ylabel('Regime State', fontsize=12)
        
        # Draw regime backgrounds - simplified to 3-state view
        regimes = self.signals_df['regime']
        
        # Map to 3-state for cleaner visualization
        regimes_3state = regimes.map(lambda x: map_gms_state(x, map_weak_bear_to_bear=self.map_weak_bear_to_bear))
        
        regime_changes = regimes_3state.ne(regimes_3state.shift()).cumsum()
        
        # Group consecutive regimes
        for regime_id in regime_changes.unique():
            mask = regime_changes == regime_id
            if mask.sum() == 0:
                continue
                
            start_idx = self.signals_df.index[mask].min()
            end_idx = self.signals_df.index[mask].max()
            regime_3state = regimes_3state[mask].iloc[0]
            
            if pd.isna(regime_3state):
                continue
            
            # Get color based on 3-state regime
            color = regime_colors_3state.get(regime_3state, (0.5, 0.5, 0.5, 0.5))
            
            # Draw regime band on secondary axis
            ax2.axvspan(start_idx, end_idx, ymin=0, ymax=1, 
                       color=color[:3], alpha=color[3], zorder=1)
        
        # Add regime transition markers
        transitions = []
        for i in range(1, len(regimes)):
            if regimes.iloc[i] != regimes.iloc[i-1] and not pd.isna(regimes.iloc[i]):
                transitions.append({
                    'timestamp': regimes.index[i],
                    'from_state': regimes.iloc[i-1],
                    'to_state': regimes.iloc[i]
                })
        
        # Mark major transitions with vertical lines
        for transition in transitions:
            ax.axvline(x=transition['timestamp'], color='black', 
                      alpha=0.3, linestyle='--', linewidth=1, zorder=5)
        
        # Create legend
        from matplotlib.patches import Patch
        legend_elements = []
        
        # Show 3-state regime counts
        regimes_3state_counts = regimes_3state.value_counts()
        
        for regime in ['BULL', 'BEAR', 'CHOP']:
            if regime in regimes_3state_counts:
                color = regime_colors_3state.get(regime, (0.5, 0.5, 0.5, 0.5))
                count = regimes_3state_counts[regime]
                pct = (count / len(regimes_3state)) * 100
                legend_elements.append(
                    Patch(facecolor=color[:3], alpha=color[3], 
                         label=f'{regime} ({pct:.1f}%)')
                )
        
        # Add price line to legend if it exists
        if 'close' in self.signals_df.columns:
            legend_elements.insert(0, price_line[0])
        
        # Place legend in center top
        ax.legend(handles=legend_elements, loc='upper center', 
                 bbox_to_anchor=(0.5, 0.98), fontsize=10, ncol=4,
                 frameon=True, fancybox=True, shadow=True)
        
        # Format the plot
        ax.set_xlabel('Date', fontsize=12)
        ax.set_title('Regime Transition Timeline', fontsize=16, fontweight='bold')
        ax.grid(True, alpha=0.3)
        
        # Format x-axis dates
        import matplotlib.dates as mdates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.AutoDateLocator())
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        # Add transition count text
        transition_text = f"Total Regime Transitions: {len(transitions)}"
        ax.text(0.98, 0.02, transition_text, transform=ax.transAxes,
               fontsize=10, ha='right', va='bottom',
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
        
        plt.tight_layout()
        
        # Save the figure
        if not output_path:
            timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            output_path = str(self.output_dir / f"regime_transitions_{timestamp}.png")
        
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            logger.info(f"Saved regime transition timeline to {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"Failed to save regime transition timeline: {e}")
            plt.close(fig)
            return ""
