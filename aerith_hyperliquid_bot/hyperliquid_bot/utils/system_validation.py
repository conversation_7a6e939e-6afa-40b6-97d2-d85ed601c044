# hyperliquid_bot/utils/system_validation.py
# System-wide validation module for configuration validation

import logging
from typing import Dict, List, Optional, Tuple, Any

# Import state mapping utilities
from hyperliquid_bot.utils.state_mapping import (
    get_state_map, ALL_GMS_STATES, ALL_3STATE_STATES,
    GMS_STATE_TIGHT_SPREAD, GMS_3STATE_CHOP, GMS_STATE_WEAK_BEAR_TREND,
    GMS_3STATE_BEAR
)

# Setup logging
logger = logging.getLogger(__name__)

def validate_gms_state_mapping_config(config_dir: Optional[str] = None, config: Any = None) -> Tuple[bool, List[str]]:
    """
    Validates the GMS state mapping configuration for consistency and completeness.
    
    Args:
        config_dir: Optional directory containing the configuration files
        config: Optional loaded configuration object for accessing regime settings
        
    Returns:
        Tuple of (is_valid, list_of_issues)
    """
    issues = []
    
    # Check if we need to apply the Weak_Bear_Trend mapping toggle
    map_weak_bear_to_bear = False
    if config and hasattr(config, 'regime') and hasattr(config.regime, 'map_weak_bear_to_bear'):
        map_weak_bear_to_bear = config.regime.map_weak_bear_to_bear
    
    # Get the loaded state map with appropriate toggle
    state_map = get_state_map(map_weak_bear_to_bear=map_weak_bear_to_bear)
    
    # Check 1: Missing states
    missing_states = set(ALL_GMS_STATES) - set(state_map.keys())
    if missing_states:
        issues.append(f"Missing GMS states in mapping configuration: {missing_states}")
    
    # Check 2: Invalid target states
    invalid_targets = set(state_map.values()) - ALL_3STATE_STATES
    if invalid_targets:
        issues.append(f"Invalid 3-state targets in mapping: {invalid_targets}")
    
    # Check 3: TIGHT_SPREAD mapping
    if GMS_STATE_TIGHT_SPREAD in state_map:
        if state_map[GMS_STATE_TIGHT_SPREAD] != GMS_3STATE_CHOP:
            issues.append(f"TIGHT_SPREAD is mapped to {state_map[GMS_STATE_TIGHT_SPREAD]}, " 
                         f"but should be mapped to {GMS_3STATE_CHOP} for consistency")
    else:
        issues.append("TIGHT_SPREAD state is missing from state mapping configuration")
        
    # Check 4: Weak_Bear_Trend mapping based on configuration
    if GMS_STATE_WEAK_BEAR_TREND in state_map:
        expected_target = GMS_3STATE_BEAR if map_weak_bear_to_bear else GMS_3STATE_CHOP
        actual_target = state_map[GMS_STATE_WEAK_BEAR_TREND]
        if actual_target != expected_target:
            issues.append(f"Weak_Bear_Trend is mapped to {actual_target}, but should be mapped to {expected_target} " 
                         f"based on the map_weak_bear_to_bear setting ({map_weak_bear_to_bear})")
    else:
        issues.append("Weak_Bear_Trend state is missing from state mapping configuration")
    
    return len(issues) == 0, issues

def validate_config_consistency(config: Any) -> Tuple[bool, List[str]]:
    """
    Validates that the application configuration is consistent and has necessary values.
    
    Args:
        config: The loaded configuration object
        
    Returns:
        Tuple of (is_valid, list_of_issues)
    """
    issues = []
    
    # Check regime settings
    if hasattr(config, 'regime'):
        # Check for dynamic risk adjustment settings
        if getattr(config.regime, 'dynamic_risk_adjustment', False):
            required_attrs = [
                'chop_risk_factor', 
                'chop_leverage_factor',
                'strong_trend_risk_factor',
                'strong_trend_leverage_factor',
                'weak_trend_risk_scale'
            ]
            for attr in required_attrs:
                if not hasattr(config.regime, attr):
                    issues.append(f"Dynamic risk adjustment is enabled but '{attr}' is missing")
        
        # Check for three-state mapping
        if hasattr(config.regime, 'gms_use_three_state_mapping'):
            if config.regime.gms_use_three_state_mapping:
                # Verify TIGHT_SPREAD handling
                if not hasattr(config.regime, 'gms_tight_spread_fallback_percentile'):
                    issues.append("Three-state mapping is enabled but 'gms_tight_spread_fallback_percentile' is missing")
                
                # Log information about Weak_Bear_Trend mapping setting
                if hasattr(config.regime, 'map_weak_bear_to_bear'):
                    target_state = "BEAR" if config.regime.map_weak_bear_to_bear else "CHOP"
                    logger.info(f"Weak_Bear_Trend is configured to map to {target_state} based on map_weak_bear_to_bear={config.regime.map_weak_bear_to_bear}")
                else:
                    logger.info("Weak_Bear_Trend will map to default state (CHOP) as map_weak_bear_to_bear is not configured")
    else:
        issues.append("Missing 'regime' section in configuration")
    
    return len(issues) == 0, issues

def perform_startup_validation(config: Any) -> Tuple[bool, Dict[str, List[str]]]:
    """
    Performs all system validations at startup.
    
    Args:
        config: The loaded configuration object
        
    Returns:
        Tuple of (all_valid, {validation_name: issues})
    """
    validation_results = {}
    
    # Validate GMS state mapping - pass the config to check for map_weak_bear_to_bear setting
    gms_valid, gms_issues = validate_gms_state_mapping_config(config=config)
    validation_results["gms_state_mapping"] = gms_issues
    
    # Validate config consistency
    config_valid, config_issues = validate_config_consistency(config)
    validation_results["config_consistency"] = config_issues
    
    # Log validation results
    all_valid = gms_valid and config_valid
    
    if all_valid:
        logger.info("All system validations passed successfully")
    else:
        logger.warning("System validation found issues:")
        for validation_name, issues in validation_results.items():
            if issues:
                for issue in issues:
                    logger.warning(f"[{validation_name}] {issue}")
    
    return all_valid, validation_results
