#!/usr/bin/env python3
import re
import os
import pandas as pd
from pathlib import Path

class LogAnalyzer:
    def __init__(self, log_path=None, losing_trades_path=None, load_csv=True):
        self.log_content = ""
        self.log_lines = []
        self.log_path = log_path
        self.losing_trades_df = None
        self.pnl_column = None  # We'll detect the PnL column name
        self.load_csv = load_csv  # Control whether to load CSV
        self.stats = {}
        self.performance_metrics = {}
        
        if log_path and os.path.exists(log_path):
            self.load_log(log_path)
        
        if load_csv and losing_trades_path and os.path.exists(losing_trades_path):
            self.load_losing_trades(losing_trades_path)
    
    def load_log(self, log_path):
        """Load the log file content"""
        print(f"Loading log file: {log_path}...")
        try:
            with open(log_path, 'r', encoding='utf-8') as file:
                self.log_content = file.read()
                self.log_lines = self.log_content.splitlines()
                print(f"✅ Loaded {len(self.log_lines):,} log lines")
                self.log_path = log_path
                # Extract basic stats
                self.extract_log_stats()
                # Extract performance metrics
                self.extract_performance_metrics()
        except Exception as e:
            print(f"Error loading log file: {e}")
    
    def load_losing_trades(self, csv_path):
        """Load the losing trades CSV file"""
        print(f"Loading losing trades CSV: {csv_path}...")
        try:
            self.losing_trades_df = pd.read_csv(csv_path)
            print(f"✅ Loaded {len(self.losing_trades_df)} losing trade records")
            print(f"Columns available: {', '.join(self.losing_trades_df.columns.tolist())}")
            
            # Try to find PnL column - different naming conventions might be used
            possible_pnl_columns = ['realized_pnl', 'pnl', 'profit_loss', 'profit', 'realized_profit', 'profit_and_loss']
            for col in possible_pnl_columns:
                if col in self.losing_trades_df.columns:
                    self.pnl_column = col
                    print(f"Using '{col}' as PnL column")
                    break
                    
            if not self.pnl_column:
                # Look for columns containing 'pnl' or 'profit'
                for col in self.losing_trades_df.columns:
                    if 'pnl' in col.lower() or 'profit' in col.lower():
                        self.pnl_column = col
                        print(f"Using '{col}' as PnL column")
                        break
                        
            if not self.pnl_column:
                print("⚠️ Could not find a PnL column in the CSV file")
                
        except Exception as e:
            print(f"Error loading losing trades CSV: {e}")
    
    def extract_log_stats(self):
        """Extract basic statistics from the log"""
        self.stats = {
            'errors': len(re.findall(r'ERROR', self.log_content)),
            'warnings': len(re.findall(r'WARNING', self.log_content)),
            'info': len(re.findall(r'INFO', self.log_content)),
            'debug': len(re.findall(r'DEBUG', self.log_content)),
            'total_lines': len(self.log_lines)
        }
        
        # Extract timestamp range
        timestamps = re.findall(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})', self.log_content)
        if timestamps:
            self.stats['first_timestamp'] = timestamps[0]
            self.stats['last_timestamp'] = timestamps[-1]
        
        # Get backtest configuration if available
        config_matches = re.findall(r'Configuration loaded:.*', self.log_content)
        if config_matches:
            self.stats['config'] = config_matches[0]
            
    def extract_performance_metrics(self):
        """Extract key performance metrics from log content"""
        self.performance_metrics = {
            'final_balance': None,
            'roi': None,
            'sharpe_ratio': None,
            'sortino_ratio': None,
            'max_drawdown': None,
            'total_trades': None,
            'win_rate': None
        }
        
        # Find Final Balance
        balance_match = re.search(r'Final Balance:\s+\$\s*([\d,]+\.\d+)', self.log_content)
        if balance_match:
            try:
                self.performance_metrics['final_balance'] = float(balance_match.group(1).replace(',', ''))
            except ValueError:
                pass
        
        # Find ROI
        roi_match = re.search(r'Return on Initial \(ROI\):\s+([-\d.]+)%', self.log_content)
        if roi_match:
            try:
                self.performance_metrics['roi'] = float(roi_match.group(1))
            except ValueError:
                pass
        
        # Find Sharpe Ratio
        sharpe_match = re.search(r'Sharpe Ratio \(Daily\):\s+([-\d.]+)', self.log_content)
        if sharpe_match:
            try:
                self.performance_metrics['sharpe_ratio'] = float(sharpe_match.group(1))
            except ValueError:
                pass
        
        # Find Sortino Ratio
        sortino_match = re.search(r'Sortino Ratio \(Daily\):\s+([-\d.]+)', self.log_content)
        if sortino_match:
            try:
                self.performance_metrics['sortino_ratio'] = float(sortino_match.group(1))
            except ValueError:
                pass
        
        # Find Max Drawdown
        drawdown_match = re.search(r'Max Drawdown:\s+([-\d.]+)%', self.log_content)
        if drawdown_match:
            try:
                self.performance_metrics['max_drawdown'] = float(drawdown_match.group(1))
            except ValueError:
                pass
        
        # Find Total Trades
        trades_match = re.search(r'Total Trades:\s+(\d+)', self.log_content)
        if trades_match:
            try:
                self.performance_metrics['total_trades'] = int(trades_match.group(1))
            except ValueError:
                pass
        
        # Find Win Rate
        win_rate_match = re.search(r'Win Rate:\s+([\d.]+)%', self.log_content)
        if win_rate_match:
            try:
                self.performance_metrics['win_rate'] = float(win_rate_match.group(1))
            except ValueError:
                pass
    
    def show_stats(self):
        """Display basic statistics about the log file"""
        if not self.log_content:
            print("No log file loaded")
            return
        
        print("\n===== LOG STATISTICS =====")
        print(f"Log file: {self.log_path}")
        print(f"Total lines: {self.stats['total_lines']:,}")
        print(f"Time range: {self.stats.get('first_timestamp', 'N/A')} to {self.stats.get('last_timestamp', 'N/A')}")
        print(f"Log levels: ERROR={self.stats['errors']}, WARNING={self.stats['warnings']}, INFO={self.stats['info']}, DEBUG={self.stats['debug']}")
        
        # Display performance metrics
        print("\n===== PERFORMANCE METRICS =====")
        metrics = self.performance_metrics
        print(f"Final Balance:     ${metrics['final_balance']:,.2f}" if metrics['final_balance'] is not None else "Final Balance:     N/A")
        print(f"Return on Initial: {metrics['roi']:.2f}%" if metrics['roi'] is not None else "Return on Initial: N/A")
        print(f"Sharpe Ratio:      {metrics['sharpe_ratio']:.2f}" if metrics['sharpe_ratio'] is not None else "Sharpe Ratio:      N/A")
        print(f"Sortino Ratio:     {metrics['sortino_ratio']:.2f}" if metrics['sortino_ratio'] is not None else "Sortino Ratio:     N/A")
        print(f"Maximum Drawdown:  {metrics['max_drawdown']:.2f}%" if metrics['max_drawdown'] is not None else "Maximum Drawdown:  N/A")
        print(f"Total Trades:      {metrics['total_trades']:,}" if metrics['total_trades'] is not None else "Total Trades:      N/A")
        print(f"Win Rate:          {metrics['win_rate']:.1f}%" if metrics['win_rate'] is not None else "Win Rate:          N/A")
        
        if self.losing_trades_df is not None:
            print(f"\nLosing trades: {len(self.losing_trades_df)} records")
            if self.pnl_column:
                total_pnl = self.losing_trades_df[self.pnl_column].sum()
                print(f"Total PnL of losing trades: {total_pnl:,.2f}")
    
    def analyze_losing_trades(self):
        """Analyze patterns in losing trades"""
        if self.losing_trades_df is None:
            print("No losing trades CSV loaded")
            return
        
        print("\n===== LOSING TRADES ANALYSIS =====")
        print(f"Total losing trades: {len(self.losing_trades_df)}")
        print(f"Available columns: {', '.join(self.losing_trades_df.columns.tolist())}")
        
        # Group by entry reason if available
        entry_reason_col = None
        for col in ['entry_reason', 'entry_signal', 'signal', 'reason']:
            if col in self.losing_trades_df.columns:
                entry_reason_col = col
                break
                
        if entry_reason_col:
            entry_groups = self.losing_trades_df.groupby(entry_reason_col)
            print(f"\nLosses by {entry_reason_col}:")
            for reason, group in entry_groups:
                if self.pnl_column:
                    total_pnl = group[self.pnl_column].sum()
                    avg_pnl = group[self.pnl_column].mean()
                    print(f"  {reason}: {len(group)} trades, Total PnL={total_pnl:,.2f}, Avg PnL={avg_pnl:,.2f}")
                else:
                    print(f"  {reason}: {len(group)} trades")
        
        # Worst trades
        if self.pnl_column:
            print("\nTop 5 worst trades:")
            worst_trades = self.losing_trades_df.nsmallest(5, self.pnl_column)
            
            # Identify time column
            time_col = None
            for col in ['exit_time', 'close_time', 'timestamp', 'time', 'date']:
                if col in worst_trades.columns:
                    time_col = col
                    break
            
            # Identify reason columns
            entry_col = entry_reason_col
            exit_col = None
            for col in ['exit_reason', 'close_reason', 'exit_signal']:
                if col in worst_trades.columns:
                    exit_col = col
                    break
            
            for _, trade in worst_trades.iterrows():
                time_info = trade.get(time_col, 'N/A') if time_col else 'N/A'
                pnl_value = trade[self.pnl_column]
                entry_info = trade.get(entry_col, 'N/A') if entry_col else 'N/A'
                exit_info = trade.get(exit_col, 'N/A') if exit_col else 'N/A'
                
                print(f"  {time_info}: {pnl_value:,.2f} - Entry: {entry_info}, Exit: {exit_info}")
        else:
            print("\nCannot identify worst trades without PnL column")
    
    def search_log(self, query, context_lines=2):
        """Search the log for a specific query and return matches with context"""
        if not self.log_content:
            print("No log file loaded")
            return []
        
        # Convert query to lowercase for case-insensitive search
        query_lower = query.lower()
        results = []
        
        for i, line in enumerate(self.log_lines):
            if query_lower in line.lower():
                # Get context lines around the match
                start = max(0, i - context_lines)
                end = min(len(self.log_lines), i + context_lines + 1)
                context = self.log_lines[start:end]
                results.append({
                    'line_number': i + 1,
                    'match': line,
                    'context': context
                })
        
        return results
    
    def answer_question(self, question):
        """Attempt to answer a question about the log content"""
        question_lower = question.lower()
        
        # Check if log is loaded
        if not self.log_content:
            return "No log file loaded. Please load a log file first."
        
        # Check for specific questions
        if "error" in question_lower or "issues" in question_lower:
            error_lines = [line for line in self.log_lines if "ERROR" in line]
            if error_lines:
                return f"Found {len(error_lines)} errors in the log. Here are the first 3:\n" + "\n".join(error_lines[:3])
            else:
                return "No errors found in the log file."
        
        elif "mean variance" in question_lower:
            mean_var_results = self.search_log("mean variance")
            mean_var_results.extend(self.search_log("mean_variance"))
            if mean_var_results:
                return f"Mean variance appears in {len(mean_var_results)} locations in the logs. Some examples:\n" + "\n".join([r['match'] for r in mean_var_results[:3]])
            else:
                return "Mean variance does not appear in the logs."
        
        elif "backtest" in question_lower and ("result" in question_lower or "performance" in question_lower):
            perf_results = self.search_log("Backtest complete")
            perf_results.extend(self.search_log("Performance Summary"))
            perf_results.extend(self.search_log("Final Portfolio Value"))
            if perf_results:
                return "Backtest results found:\n" + "\n".join(["\n".join(r['context']) for r in perf_results[:2]])
            else:
                return "Could not find specific backtest results in the log."
        
        elif "losing trades" in question_lower or "worst trades" in question_lower:
            if self.losing_trades_df is not None:
                worst_trades = self.losing_trades_df.nsmallest(3, self.pnl_column)
                result = f"Analyzed {len(self.losing_trades_df)} losing trades.\n\nWorst trades:\n"
                for _, trade in worst_trades.iterrows():
                    result += f"PnL: {trade[self.pnl_column]:.2f}, Entry: {trade.get('entry_reason', 'N/A')}, Exit: {trade.get('exit_reason', 'N/A')}\n"
                return result
            else:
                return "Losing trades CSV not loaded."
        
        # General keyword search approach for other questions
        keywords = re.findall(r'\b\w+\b', question_lower)
        important_keywords = [k for k in keywords if len(k) > 3 and k not in ['what', 'where', 'when', 'does', 'show', 'about', 'have', 'logs', 'file', 'there']]
        
        if important_keywords:
            print(f"Searching for keywords: {important_keywords}")
            all_results = []
            for keyword in important_keywords:
                results = self.search_log(keyword)
                all_results.extend(results)
            
            if all_results:
                return f"Found {len(all_results)} relevant entries. Sample matches:\n" + "\n\n".join(["\n".join(r['context'][:3]) for r in all_results[:3]])
            else:
                return f"No matches found for keywords: {important_keywords}"
        
        return "I'm not sure how to answer that question based on the log content."

def find_most_recent_file(directory, pattern):
    """Find the most recent file matching the given pattern in the directory."""
    try:
        files = list(Path(directory).glob(pattern))
        if not files:
            return None
        # Sort by modification time, newest first
        return sorted(files, key=lambda x: x.stat().st_mtime, reverse=True)[0]
    except Exception as e:
        print(f"Error finding recent file matching '{pattern}': {e}")
        return None

def interactive_mode():
    """Run the log analyzer in interactive mode"""
    logs_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs/")
    
    print("=== Interactive Log Analyzer ===")
    
    # Auto-find most recent files
    most_recent_log = find_most_recent_file(logs_dir, "backtest_run_*.log")
    most_recent_csv = find_most_recent_file(logs_dir, "losing_trades_analysis_*.csv")
    
    log_exists = most_recent_log and most_recent_log.exists()
    trades_exists = most_recent_csv and most_recent_csv.exists()
    
    if log_exists:
        print(f"Found most recent log: {most_recent_log.name}")
    if trades_exists:
        print(f"Found most recent trades CSV: {most_recent_csv.name}")
    
    # Ask about CSV loading
    load_csv = True
    if trades_exists:
        load_csv_input = input("Load trades CSV? (Y/n): ").strip().lower()
        load_csv = load_csv_input != 'n'
    
    # Initialize analyzer with auto-detected files
    analyzer = LogAnalyzer(
        log_path=str(most_recent_log) if log_exists else None,
        losing_trades_path=str(most_recent_csv) if trades_exists and load_csv else None,
        load_csv=load_csv
    )
    
    # Show initial stats
    analyzer.show_stats()
    if load_csv and analyzer.losing_trades_df is not None:
        analyzer.analyze_losing_trades()
    
    # Interactive loop
    print("\nType 'help' for available commands, 'exit' to quit")
    while True:
        try:
            user_input = input("\n> ").strip()
            
            if user_input.lower() == 'exit' or user_input.lower() == 'quit':
                print("Exiting log analyzer")
                break
                
            elif user_input.lower() == 'help':
                print("\nAvailable commands:")
                print("  stats       - Show basic log statistics")
                print("  load log    - Load a log file")
                print("  latest log  - Load most recent log file")
                print("  load csv    - Load a losing trades CSV file")
                print("  latest csv  - Load most recent CSV file")
                print("  toggle csv  - Enable/disable CSV loading")
                print("  search X    - Search for X in the logs")
                print("  analyze     - Analyze losing trades patterns")
                print("  metrics     - Show detailed performance metrics")
                print("  exit        - Exit the analyzer")
                print("\nOr simply ask a question about the logs")
            
            elif user_input.lower() == 'stats':
                analyzer.show_stats()
                
            elif user_input.lower() == 'metrics':
                # Show detailed metrics
                metrics = analyzer.performance_metrics
                print("\n===== DETAILED PERFORMANCE METRICS =====")
                for key, value in metrics.items():
                    if value is not None:
                        print(f"{key.replace('_', ' ').title()}: {value:,.2f}" + ("%" if key in ['roi', 'win_rate', 'max_drawdown'] else ""))
            
            elif user_input.lower().startswith('load log'):
                parts = user_input.split(' ', 2)
                path = parts[2] if len(parts) > 2 else input("Enter log file path: ")
                analyzer.load_log(path)
                
            elif user_input.lower() == 'latest log':
                most_recent = find_most_recent_file(logs_dir, "backtest_run_*.log")
                if most_recent:
                    print(f"Loading most recent log: {most_recent.name}")
                    analyzer.load_log(str(most_recent))
                else:
                    print("No log files found")
                
            elif user_input.lower().startswith('load csv'):
                parts = user_input.split(' ', 2)
                path = parts[2] if len(parts) > 2 else input("Enter losing trades CSV path: ")
                analyzer.load_csv = True
                analyzer.load_losing_trades(path)
                
            elif user_input.lower() == 'latest csv':
                most_recent = find_most_recent_file(logs_dir, "losing_trades_analysis_*.csv")
                if most_recent:
                    print(f"Loading most recent CSV: {most_recent.name}")
                    analyzer.load_csv = True
                    analyzer.load_losing_trades(str(most_recent))
                else:
                    print("No CSV files found")
                
            elif user_input.lower() == 'toggle csv':
                analyzer.load_csv = not analyzer.load_csv
                print(f"CSV loading is now {'enabled' if analyzer.load_csv else 'disabled'}")
                
                if analyzer.load_csv and not analyzer.losing_trades_df and trades_exists:
                    print("Reloading CSV file...")
                    analyzer.load_losing_trades(str(most_recent_csv))
                elif not analyzer.load_csv and analyzer.losing_trades_df is not None:
                    print("Unloading CSV data...")
                    analyzer.losing_trades_df = None
                    analyzer.pnl_column = None
                
            elif user_input.lower() == 'analyze':
                analyzer.analyze_losing_trades()
                
            elif user_input.lower().startswith('search '):
                query = user_input[7:]
                results = analyzer.search_log(query, context_lines=2)
                print(f"\nFound {len(results)} matches:")
                for i, result in enumerate(results[:10], 1):
                    print(f"\n--- Match {i}/{min(len(results), 10)} (Line {result['line_number']}) ---")
                    print("\n".join(result['context']))
                if len(results) > 10:
                    print(f"\n... and {len(results) - 10} more matches")
            
            else:
                # Treat as a question
                answer = analyzer.answer_question(user_input)
                print(f"\n{answer}")
                
        except KeyboardInterrupt:
            print("\nExiting log analyzer")
            break
            
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    interactive_mode()