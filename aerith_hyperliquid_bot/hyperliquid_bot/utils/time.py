# hyperliquid_bot/utils/time.py
# Time utilities for consistent timezone handling

import pandas as pd
from typing import Union


def vectorized_to_utc_naive(series: pd.Series) -> pd.Series:
    """
    Vectorized version of to_utc_naive for pandas Series.
    Converts an entire Series of timestamps to UTC-naive format efficiently.

    Args:
        series: pandas Series containing timestamps in various formats

    Returns:
        pd.Series: UTC-naive timestamps (tzinfo=None) representing UTC time

    Performance: This replaces millions of .apply(to_utc_naive) calls with
    a single vectorized operation, providing massive performance improvements.
    """
    # Handle empty series
    if series.empty:
        return series

    # Ensure it's datetime type
    if not pd.api.types.is_datetime64_any_dtype(series):
        # Convert to datetime with UTC timezone, then make naive
        series = pd.to_datetime(series, utc=True, errors='coerce').dt.tz_localize(None)
    else:
        # Already datetime - handle timezone conversion if needed
        if hasattr(series.dt, 'tz') and series.dt.tz is not None:
            series = series.dt.tz_convert('UTC').dt.tz_localize(None)

    return series


def to_utc_naive(ts: Union[pd.Timestamp, str, int, float]) -> pd.Timestamp:
    """
    Convert ts to a pandas Timestamp that is UTC-naïve (tzinfo None).
    Accepts epoch ints/floats, ISO strings, or Timestamp.

    Args:
        ts: Input timestamp in various formats:
            - pd.Timestamp (tz-aware or tz-naive)
            - str: ISO format string (e.g., "2025-03-01T12:00:00Z")
            - int/float: Epoch timestamp (seconds or milliseconds)

    Returns:
        pd.Timestamp: UTC-naive timestamp (tzinfo=None) representing UTC time

    Examples:
        >>> to_utc_naive("2025-03-01T12:00:00Z")
        Timestamp('2025-03-01 12:00:00')

        >>> to_utc_naive(1709294400000)  # epoch ms
        Timestamp('2024-03-01 12:00:00')

        >>> to_utc_naive(pd.Timestamp("2025-03-01 12:00:00", tz="UTC"))
        Timestamp('2025-03-01 12:00:00')
    """
    if isinstance(ts, pd.Timestamp):
        # Handle pandas Timestamp
        if ts.tz is not None:
            # Convert to UTC and remove timezone info
            return ts.tz_convert('UTC').tz_localize(None)
        else:
            # Already naive, assume it's UTC
            return ts

    elif isinstance(ts, str):
        # Handle ISO string
        parsed = pd.to_datetime(ts, utc=True)
        return parsed.tz_localize(None)

    elif isinstance(ts, (int, float)):
        # Handle epoch timestamp
        # Detect if it's milliseconds (> year 2000 in seconds)
        if ts > 946684800:  # Jan 1, 2000 in seconds
            if ts > 946684800000:  # Likely milliseconds
                parsed = pd.to_datetime(ts, unit='ms', utc=True)
            else:  # Likely seconds
                parsed = pd.to_datetime(ts, unit='s', utc=True)
        else:
            # Very small number, assume seconds
            parsed = pd.to_datetime(ts, unit='s', utc=True)

        return parsed.tz_localize(None)

    else:
        raise TypeError(f"Unsupported timestamp type: {type(ts)}")
