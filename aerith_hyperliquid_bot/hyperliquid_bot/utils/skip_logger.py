# hyperliquid_bot/utils/skip_logger.py
# Utility for logging SkipSignal reasons

import logging
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional

# Setup logging
logger = logging.getLogger(__name__)

class SkipReasonLogger:
    """
    Logger for SkipSignal reasons.
    
    Collects and logs reasons why signals were skipped during backtesting.
    Can output to CSV for analysis.
    """
    
    def __init__(self, log_dir: Optional[str] = None):
        """
        Initialize the SkipReasonLogger.
        
        Args:
            log_dir: Directory to save log files (default: logs/)
        """
        self.log_dir = Path(log_dir) if log_dir else Path("logs")
        self.log_dir.mkdir(exist_ok=True, parents=True)
        self.skip_reasons: List[Dict] = []
        logger.info(f"SkipReasonLogger initialized. Log directory: {self.log_dir}")
    
    def log_skip(self, timestamp: datetime, strategy: str, reason: str) -> None:
        """
        Log a skip reason.
        
        Args:
            timestamp: Timestamp when the skip occurred
            strategy: Name of the strategy that skipped
            reason: Reason for skipping
        """
        self.skip_reasons.append({
            "timestamp": timestamp,
            "strategy": strategy,
            "reason": reason
        })
        logger.debug(f"Skip logged: {timestamp} - {strategy} - {reason}")
    
    def save_to_csv(self, date_str: Optional[str] = None) -> str:
        """
        Save skip reasons to a CSV file.
        
        Args:
            date_str: Date string to include in filename (default: current date)
            
        Returns:
            Path to the saved CSV file
        """
        if not self.skip_reasons:
            logger.warning("No skip reasons to save.")
            return ""
        
        # Create DataFrame from skip reasons
        df = pd.DataFrame(self.skip_reasons)
        
        # Use provided date or current date
        if not date_str:
            date_str = datetime.now().strftime("%Y%m%d")
        
        # Create filename
        filename = f"skip_reasons_{date_str}.csv"
        filepath = self.log_dir / filename
        
        # Save to CSV
        df.to_csv(filepath, index=False)
        logger.info(f"Skip reasons saved to {filepath}")
        
        return str(filepath)
