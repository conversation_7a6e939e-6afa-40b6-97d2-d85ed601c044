# hyperliquid_bot/utils/state_mapping.py

import os
import yaml
import logging
from typing import Dict, List, Optional, Set

# Setup logging
logger = logging.getLogger(__name__)

# Define official GMS states as constants for reference
# 7-State Model - Full enumeration of all expected states from GMS Detector
GMS_STATE_STRONG_BULL_TREND = "Strong_Bull_Trend"
GMS_STATE_WEAK_BULL_TREND = "Weak_Bull_Trend"
GMS_STATE_HIGH_VOL_RANGE = "High_Vol_Range"
GMS_STATE_LOW_VOL_RANGE = "Low_Vol_Range"
GMS_STATE_UNCERTAIN = "Uncertain"
GMS_STATE_WEAK_BEAR_TREND = "Weak_Bear_Trend"
GMS_STATE_STRONG_BEAR_TREND = "Strong_Bear_Trend"
GMS_STATE_TIGHT_SPREAD = "TIGHT_SPREAD"  # Special case from GMS Detector

# Special states from GMS Detector
GMS_STATE_UNKNOWN = "Unknown"
GMS_STATE_FILTER_OFF = "Filter_Off"

# 3-State Model - The consolidated states
GMS_3STATE_BULL = "BULL"
GMS_3STATE_BEAR = "BEAR"
GMS_3STATE_CHOP = "CHOP"

# Define sets of states for validation and reference
ALL_GMS_STATES = {
    GMS_STATE_STRONG_BULL_TREND,
    GMS_STATE_WEAK_BULL_TREND,
    GMS_STATE_HIGH_VOL_RANGE,
    GMS_STATE_LOW_VOL_RANGE,
    GMS_STATE_UNCERTAIN,
    GMS_STATE_WEAK_BEAR_TREND,
    GMS_STATE_STRONG_BEAR_TREND,
    GMS_STATE_TIGHT_SPREAD
}

ALL_3STATE_STATES = {
    GMS_3STATE_BULL,
    GMS_3STATE_BEAR,
    GMS_3STATE_CHOP
}

# Default mapping based on current understanding (for fallback only)
DEFAULT_STATE_MAP = {
    GMS_STATE_STRONG_BULL_TREND: GMS_3STATE_BULL,
    GMS_STATE_WEAK_BULL_TREND: GMS_3STATE_BULL,
    GMS_STATE_HIGH_VOL_RANGE: GMS_3STATE_CHOP,
    GMS_STATE_LOW_VOL_RANGE: GMS_3STATE_CHOP,
    GMS_STATE_UNCERTAIN: GMS_3STATE_CHOP,
    GMS_STATE_WEAK_BEAR_TREND: GMS_3STATE_CHOP,  # Note: Intentionally CHOP not BEAR
    GMS_STATE_STRONG_BEAR_TREND: GMS_3STATE_BEAR,
    GMS_STATE_TIGHT_SPREAD: GMS_3STATE_CHOP  # Special case: map to CHOP
}

class StateMapLoader:
    """Handles loading and validation of GMS state mappings from configuration files."""
    
    def __init__(self):
        self._state_map: Dict[str, str] = {}
        self._map_loaded = False
        self._yaml_path: Optional[str] = None
        self._map_weak_bear_to_bear = False  # Default to False (mapping to CHOP)
    
    def load_map(self, config_dir: Optional[str] = None, map_weak_bear_to_bear: bool = False) -> Dict[str, str]:
        """
        Load state mapping from YAML file.
        
        Args:
            config_dir: Directory containing the mapping file (default: None, will use default locations)
            map_weak_bear_to_bear: If True, maps Weak_Bear_Trend to BEAR instead of CHOP (default: False)
            
        Returns:
            Dict mapping 7-state GMS values to 3-state consolidated values
        """
        if self._map_loaded:
            return self._state_map
        
        # Store the weak bear mapping configuration
        self._map_weak_bear_to_bear = map_weak_bear_to_bear
        
        # Try multiple locations for the mapping file
        potential_paths = []
        
        # If config_dir provided, check there first
        if config_dir:
            potential_paths.append(os.path.join(config_dir, "gms_state_mapping.yaml"))

        # Add default locations
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
        configs_dir = os.path.join(project_root, "configs")
        potential_paths.append(os.path.join(configs_dir, "gms_state_mapping.yaml"))
        
        # Try each path
        for path in potential_paths:
            if os.path.exists(path):
                try:
                    with open(path, 'r') as f:
                        yaml_content = yaml.safe_load(f)
                        if yaml_content and 'state_map' in yaml_content:
                            self._state_map = yaml_content['state_map']
                            self._yaml_path = path
                            self._map_loaded = True
                            logger.info(f"Loaded GMS state mapping from {path}")
                            self._validate_loaded_map()
                            return self._state_map
                except Exception as e:
                    logger.warning(f"Error loading state mapping from {path}: {e}")
                    continue
        
        # Fallback to default mapping if file not found or invalid
        logger.warning("Could not load GMS state mapping from file, using default mapping")
        self._state_map = DEFAULT_STATE_MAP.copy()
        self._map_loaded = True
        return self._state_map
    
    def get_map(self) -> Dict[str, str]:
        """Get the current state map, loading it first if not already loaded."""
        if not self._map_loaded:
            return self.load_map()
        return self._state_map
    
    def _validate_loaded_map(self) -> None:
        """Validate the loaded state map for completeness and correctness."""
        # Check if all known GMS states are mapped
        missing_states = ALL_GMS_STATES - set(self._state_map.keys())
        if missing_states:
            logger.warning(f"Missing mappings for GMS states: {missing_states}")
            # Add defaults for missing states
            for state in missing_states:
                if state in DEFAULT_STATE_MAP:
                    self._state_map[state] = DEFAULT_STATE_MAP[state]
                    logger.info(f"Added default mapping for {state}: {DEFAULT_STATE_MAP[state]}")
                    
        # Apply the Weak_Bear_Trend mapping toggle if enabled
        if self._map_weak_bear_to_bear:
            self._state_map[GMS_STATE_WEAK_BEAR_TREND] = GMS_3STATE_BEAR
            logger.info(f"Applied custom mapping: {GMS_STATE_WEAK_BEAR_TREND} -> {GMS_3STATE_BEAR} (config override)")
        else:
            # Ensure the default mapping (to CHOP) is applied if not already set
            if self._state_map.get(GMS_STATE_WEAK_BEAR_TREND) != GMS_3STATE_CHOP:
                self._state_map[GMS_STATE_WEAK_BEAR_TREND] = GMS_3STATE_CHOP
                logger.info(f"Restored default mapping: {GMS_STATE_WEAK_BEAR_TREND} -> {GMS_3STATE_CHOP}")
        
        # Check that all mapped values are valid 3-state values
        invalid_targets = set(self._state_map.values()) - ALL_3STATE_STATES
        if invalid_targets:
            logger.warning(f"Invalid 3-state targets in mapping: {invalid_targets}")
            # Fix invalid targets by using defaults
            for state, target in self._state_map.items():
                if target in invalid_targets:
                    if state in DEFAULT_STATE_MAP:
                        self._state_map[state] = DEFAULT_STATE_MAP[state]
                        logger.info(f"Replaced invalid mapping for {state}: {target} -> {DEFAULT_STATE_MAP[state]}")

# Global instance for singleton-like access
_state_map_loader = StateMapLoader()

def get_state_map(map_weak_bear_to_bear: bool = False) -> Dict[str, str]:
    """
    Get the GMS state mapping dictionary.
    
    Args:
        map_weak_bear_to_bear: If True, maps Weak_Bear_Trend to BEAR instead of CHOP (default: False)
    
    Returns:
        Dict mapping 7-state GMS values to 3-state consolidated values
    """
    return _state_map_loader.load_map(map_weak_bear_to_bear=map_weak_bear_to_bear)

def map_gms_state(gms_state: str, map_weak_bear_to_bear: bool = False) -> str:
    """
    Map a 7-state GMS value to its corresponding 3-state value.
    
    Args:
        gms_state: The raw GMS state from the detector
        map_weak_bear_to_bear: If True, maps Weak_Bear_Trend to BEAR instead of CHOP (default: False)
        
    Returns:
        The mapped 3-state value (BULL, BEAR, CHOP)
    """
    state_map = get_state_map(map_weak_bear_to_bear=map_weak_bear_to_bear)
    
    # Handle direct mapping from state_map
    if gms_state in state_map:
        mapped_state = state_map[gms_state]
        return mapped_state
    
    # Handle fallback for unknown states
    logger.warning(f"Unknown GMS state: {gms_state}, defaulting to CHOP")
    return GMS_3STATE_CHOP  # Default to CHOP for unknown states

def get_states_by_category(map_weak_bear_to_bear: bool = False) -> Dict[str, List[str]]:
    """
    Get GMS states categorized by their 3-state mapping.
    
    Args:
        map_weak_bear_to_bear: If True, maps Weak_Bear_Trend to BEAR instead of CHOP (default: False)
        
    Returns:
        Dict with keys 'BULL', 'BEAR', 'CHOP' and values of lists of state names
    """
    state_map = get_state_map(map_weak_bear_to_bear=map_weak_bear_to_bear)
    result: Dict[str, List[str]] = {
        GMS_3STATE_BULL: [],
        GMS_3STATE_BEAR: [],
        GMS_3STATE_CHOP: []
    }
    
    for gms_state, category in state_map.items():
        if category in result:
            result[category].append(gms_state)
    
    return result

def validate_gms_state(state: str) -> bool:
    """
    Check if a string is a valid GMS state.
    
    Args:
        state: State name to validate
        
    Returns:
        True if valid, False otherwise
    """
    return state in ALL_GMS_STATES

def validate_3state(state: str) -> bool:
    """
    Check if a string is a valid 3-state value.
    
    Args:
        state: State name to validate
        
    Returns:
        True if valid, False otherwise
    """
    return state in ALL_3STATE_STATES

def get_bull_states(map_weak_bear_to_bear: bool = False) -> List[str]:
    """Get all GMS states that map to BULL."""
    states_by_category = get_states_by_category(map_weak_bear_to_bear=map_weak_bear_to_bear)
    return states_by_category.get(GMS_3STATE_BULL, [])

def get_bear_states(map_weak_bear_to_bear: bool = False) -> List[str]:
    """Get all GMS states that map to BEAR."""
    states_by_category = get_states_by_category(map_weak_bear_to_bear=map_weak_bear_to_bear)
    return states_by_category.get(GMS_3STATE_BEAR, [])

def get_chop_states(map_weak_bear_to_bear: bool = False) -> List[str]:
    """Get all GMS states that map to CHOP."""
    states_by_category = get_states_by_category(map_weak_bear_to_bear=map_weak_bear_to_bear)
    return states_by_category.get(GMS_3STATE_CHOP, [])

def get_valid_gms_states() -> Set[str]:
    """
    Get all valid GMS states including the base states and special states.
    
    Returns:
        Set of all valid GMS state strings
    """
    # Combine standard states and special states
    valid_states = ALL_GMS_STATES.copy()
    valid_states.add(GMS_STATE_UNKNOWN)
    valid_states.add(GMS_STATE_FILTER_OFF)
    return valid_states
