"""
Feature naming utilities for dynamic column resolution.

This module provides utilities to dynamically resolve feature column names
based on configuration, eliminating hardcoded column references.
"""

from typing import List, Union
import pandas as pd


def obi_col(columns: Union[List[str], pd.Index], depth: int) -> str:
    """
    Resolve OBI column name dynamically based on available columns and depth.
    
    Args:
        columns: List of available column names or pandas Index
        depth: OBI depth level (e.g., 5, 10, 20)
        
    Returns:
        str: The resolved OBI column name
        
    Raises:
        KeyError: If no OBI column for the specified depth is found
        
    Examples:
        >>> cols = ['timestamp', 'raw_obi_5', 'price']
        >>> obi_col(cols, 5)
        'raw_obi_5'
        
        >>> cols = ['timestamp', 'obi_10', 'price']
        >>> obi_col(cols, 10)
        'obi_10'
    """
    # Convert pandas Index to list if needed
    if hasattr(columns, 'tolist'):
        columns = columns.tolist()
    
    # Try different naming patterns in order of preference
    candidates = [f"raw_obi_{depth}", f"obi_{depth}"]
    
    for col in candidates:
        if col in columns:
            return col
    
    # If no match found, raise descriptive error
    available_obi_cols = [c for c in columns if 'obi' in c.lower()]
    raise KeyError(
        f"OBI column for depth {depth} not found in DataFrame. "
        f"Tried: {candidates}. Available OBI columns: {available_obi_cols}"
    )


def get_obi_column_name(depth: int, prefer_raw: bool = True) -> List[str]:
    """
    Get candidate OBI column names for a given depth.
    
    Args:
        depth: OBI depth level
        prefer_raw: If True, prefer 'raw_obi_X' over 'obi_X'
        
    Returns:
        List of candidate column names in order of preference
    """
    if prefer_raw:
        return [f"raw_obi_{depth}", f"obi_{depth}"]
    else:
        return [f"obi_{depth}", f"raw_obi_{depth}"]
