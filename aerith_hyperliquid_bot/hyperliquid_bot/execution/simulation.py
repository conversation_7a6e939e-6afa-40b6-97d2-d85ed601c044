# hyperliquid_bot/execution/simulation.py

import logging
import pandas as pd
from datetime import timed<PERSON>ta
from typing import Optional, Dict, Any, Literal
import random

# Import the Config class
from hyperliquid_bot.config.settings import Config
# Import DataHandlerInterface only for type hinting if needed
from hyperliquid_bot.data import DataHandlerInterface

logger = logging.getLogger(__name__)

# Module-level docstring
"""
Execution Simulation Component.

Simulates order execution based on trigger price and available microstructure
features (like spread) passed from the backtester. Calculates fill price,
filled size, and slippage. Includes handling for missing data using penalty factors.
Allows switching between spread-based slippage and minimal slippage test mode via config.
"""

class ExecutionSimulator:
    """
    Simulates the execution of market orders based on provided signals.

    Calculates fill price, filled quantity, and slippage impact using
    passed-in raw spread data or penalty factors. Can operate in minimal
    slippage mode for diagnostics. Tracks simulation-specific diagnostic counters.
    """
    def __init__(self, config: Config, data_handler: DataHandlerInterface):
        """
        Initializes the ExecutionSimulator.

        Args:
            config: The main configuration object.
            data_handler: An instance conforming to DataHandlerInterface (currently unused here).
        """
        self.config = config
        # self.data_handler = data_handler # Not currently needed here
        self.logger = logging.getLogger(self.__class__.__name__)

        # Counters specific to simulation results
        self.sim_pen_spread_count = 0 # Count fills using spread calculation (normal or minimal mode)
        self.sim_pen_no_spread_count = 0 # Count fills using penalty factor because spread was unusable
        self.sim_penalty_zero_fill_count = 0 # Kept for errors
        self.sim_penalty_error_count = 0 # Kept for errors
        # Deprecated counters (can be removed later if desired)
        self.sim_vwap_count = 0
        self.sim_penalty_no_data_count = 0
        self.sim_penalty_partial_fill_count = 0
        # New counters for maker/taker simulation outcomes
        self.sim_maker_fill_count = 0
        self.sim_maker_fail_taker_count = 0
        self.sim_taker_bbo_count = 0

        self.logger.info("Initialized (Execution simulation uses spread data or penalty factor)")

    def get_diagnostics(self) -> Dict[str, int]:
         """Returns a dictionary of the simulation diagnostic counters."""
         diags = {
             "sim_pen_spread": self.sim_pen_spread_count,
             "sim_pen_no_spread": self.sim_pen_no_spread_count,
             "sim_pen_zero": self.sim_penalty_zero_fill_count,
             "sim_pen_error": self.sim_penalty_error_count,
             "sim_maker_fills": self.sim_maker_fill_count,
             "sim_maker_fails_taker": self.sim_maker_fail_taker_count,
             "sim_taker_bbo_fills": self.sim_taker_bbo_count,
             # Deprecated counters
             "sim_vwap": self.sim_vwap_count,
             "sim_pen_nodata": self.sim_penalty_no_data_count,
             "sim_pen_partial": self.sim_penalty_partial_fill_count,
         }
         # Return only non-zero counters
         return {k: v for k, v in diags.items() if v > 0}

    def simulate_fill(
        self,
        order_side: Literal["buy", "sell"],
        order_size: float,
        trigger_price: float,
        current_timestamp: pd.Timestamp, # Timestamp of the signal/trigger
        raw_spread_rel: Optional[float] = None,
        raw_spread_abs: Optional[float] = None,  # Keep abs in case needed later
        best_bid: Optional[float] = None,
        best_ask: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Simulates filling a market order based on trigger price and spread,
        incorporating latency and a simple penalty model. Uses minimal slippage
        if config flag is set.

        Args:
            order_side: 'buy' or 'sell'.
            order_size: The size of the order.
            trigger_price: The price used as the basis for execution (e.g., OHLC close).
            current_timestamp: The timestamp of the signal generation.
            raw_spread_rel: The raw relative spread at the time of the signal.
            raw_spread_abs: The raw absolute spread at the time of the signal.
            best_bid: The best bid price at the time of the signal.
            best_ask: The best ask price at the time of the signal.

        Returns:
            Dict: {
                "fill_price": fill_price,
                "filled_size": filled_size,
                "slippage_pnl": slippage_pnl,
                "fee_mode": fee_mode,
                "diag_deltas": diag_deltas
            }
                   fill_price can be None if simulation fails critically.
                   slippage_pnl is calculated relative to the trigger_price.
        """
        cfg = self.config
        sim_cfg = cfg.simulation
        cost_cfg = cfg.costs
        # --- Initial Setup ---
        fee_mode = "taker"
        fill_price = None
        slippage_pnl = 0.0
        maker_fill_achieved = False

        # --- 1. Apply Latency and Diagnostics ---
        execution_timestamp = current_timestamp + timedelta(seconds=sim_cfg.latency_seconds)
        self.logger.debug(f"Simulating fill for order at {current_timestamp}, execution at {execution_timestamp} (Latency={sim_cfg.latency_seconds}s)")
        diag_deltas = {'sim_total': 1}

        # Basic validation: ensure trigger_price is valid
        if pd.isna(trigger_price) or trigger_price <= 0:
            self.logger.error(f"Invalid trigger price ({trigger_price}) at {current_timestamp}. Cannot simulate.")
            diag_deltas['sim_pen_error'] = 1
            self.sim_penalty_error_count += 1
            return {
                'fill_price': trigger_price,
                'filled_size': 0.0,
                'slippage_pnl': 0.0,
                'fee_mode': fee_mode,
                'diag_deltas': diag_deltas
            }

        # --- 2. Mid-Price Reference ---
        l2_available = (
            best_bid is not None and best_ask is not None and
            not pd.isna(best_bid) and not pd.isna(best_ask) and
            best_bid > 0 and best_ask > 0
        )
        if l2_available:
            execution_mid_price = (best_bid + best_ask) / 2.0
        else:
            execution_mid_price = trigger_price

        # --- 3. Decision Logic: Maker Attempt or Forced Taker? ---
        if not sim_cfg.force_taker_execution and sim_cfg.attempt_maker_orders and l2_available:
            self.logger.debug("Attempting probabilistic maker fill.")
            # Determine resting price
            if sim_cfg.maker_placement_type == 'mid':
                resting_price = execution_mid_price
            elif sim_cfg.maker_placement_type == 'best_passive':
                resting_price = best_bid if order_side=='buy' else best_ask
            else:
                self.logger.warning(f"Unknown maker_placement_type: {sim_cfg.maker_placement_type}. Defaulting to mid.")
                resting_price = execution_mid_price
            self.logger.debug(f"Maker order placed at: {resting_price:.4f}")
            # Probabilistic Fill Loop
            for i, bucket in enumerate(sim_cfg.maker_time_buckets_seconds):
                prob = sim_cfg.maker_fill_probabilities[i]
                draw = random.random()
                self.logger.debug(f"Bucket <= {bucket}s: Prob={prob:.2f}, Draw={draw:.4f}")
                if draw < prob:
                    fill_price = resting_price
                    fee_mode = "maker"
                    maker_fill_achieved = True
                    self.logger.info(f"MAKER fill success at {fill_price:.4f} within {bucket}s.")
                    diag_deltas['sim_maker_fills'] = diag_deltas.get('sim_maker_fills', 0) + 1
                    self.sim_maker_fill_count += 1
                    # --- CORRECT SLIPPAGE CALCULATION FOR MAKER FILL ---
                    if order_side == "buy":
                        slippage_pnl = (execution_mid_price - fill_price) * order_size
                    else:  # sell
                        slippage_pnl = (fill_price - execution_mid_price) * order_size
                    # --- END CORRECTION ---
                    break
            if not maker_fill_achieved:
                self.logger.info("Maker fill expired. Falling back to TAKER.")
                diag_deltas['sim_maker_fails_taker'] = diag_deltas.get('sim_maker_fails_taker', 0) + 1
                self.sim_maker_fail_taker_count += 1

        # --- 4. Taker Execution (if maker didn't fill) ---
        if not maker_fill_achieved:
            if l2_available:
                fill_price = best_ask if order_side=='buy' else best_bid
                fee_mode = "taker"
                self.logger.debug(f"TAKER BBO: {fill_price:.4f}")
                diag_deltas['sim_taker_bbo_fills'] = diag_deltas.get('sim_taker_bbo_fills', 0) + 1
                self.sim_taker_bbo_count += 1
            else:
                self.logger.warning("L2 unavailable. TAKER penalty fill.")
                penalty = cost_cfg.l2_penalty_factor
                fill_price = trigger_price * penalty if order_side=='buy' else trigger_price/penalty
                diag_deltas['sim_pen_no_spread'] = diag_deltas.get('sim_pen_no_spread', 0) + 1
                self.sim_pen_no_spread_count += 1

        # --- 5. Final Checks & Slippage ---
        if fill_price is None or pd.isna(fill_price) or fill_price <= 0:
            self.logger.error(f"Critical failure! Using trigger price {trigger_price}.")
            fill_price = trigger_price
            execution_mid_price = trigger_price
            diag_deltas['sim_pen_error'] = diag_deltas.get('sim_pen_error', 0) + 1; self.sim_penalty_error_count += 1

        # Calculate Slippage PnL vs Mid/Reference
        if order_side == 'buy':
            slippage_pnl = (execution_mid_price - fill_price) * order_size
        else:
            slippage_pnl = (fill_price - execution_mid_price) * order_size
        self.logger.debug(f"SIM_CALC_DEBUG (Final): Side={order_side}, Mid={execution_mid_price:.4f}, Fill={fill_price:.4f}, Fee={fee_mode}, PnL={slippage_pnl:.4f}")

        # --- 6. Return Results ---
        return {
            'fill_price': fill_price,
            'filled_size': order_size,
            'slippage_pnl': slippage_pnl,
            'fee_mode': fee_mode,
            'diag_deltas': diag_deltas
        }