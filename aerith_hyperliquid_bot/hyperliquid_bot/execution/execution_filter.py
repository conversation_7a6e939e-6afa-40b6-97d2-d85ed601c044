"""
ExecutionFilter Module

Provides execution timing optimization based on regime context and market microstructure.
Uses regime confidence to determine execution urgency and spread requirements.

Key features:
- Immediate execution for high confidence regimes
- Wait for better spreads in low confidence regimes
- 1-minute momentum confirmation
- 5-minute execution window with scoring
"""

import logging
from typing import Dict, Optional, Tuple, List
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from hyperliquid_bot.config.settings import Config


class ExecutionFilter:
    """
    Execution timing optimization based on regime context.
    
    This filter uses regime confidence and market microstructure to:
    1. Decide execution urgency (immediate vs wait)
    2. Score execution quality within a time window
    3. Confirm short-term momentum before entry
    """
    
    def __init__(self, config: Config):
        """
        Initialize the ExecutionFilter.
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Get execution configuration
        self.exec_config = getattr(config, 'execution', None)
        if self.exec_config is None:
            # Use default values if no execution config
            self.high_confidence_threshold = 0.8
            self.execution_window_minutes = 5
            self.min_execution_score = 35
            self.momentum_lookback_seconds = 60
        else:
            self.high_confidence_threshold = getattr(self.exec_config, 'high_confidence_threshold', 0.8)
            self.execution_window_minutes = getattr(self.exec_config, 'execution_window_minutes', 5)
            self.min_execution_score = getattr(self.exec_config, 'min_execution_score', 35)
            self.momentum_lookback_seconds = getattr(self.exec_config, 'momentum_lookback_seconds', 60)
            
        self.logger.info(f"Initialized ExecutionFilter: high_conf_threshold={self.high_confidence_threshold}, "
                        f"window={self.execution_window_minutes}min, min_score={self.min_execution_score}")
        
    def should_execute_now(self, signals: Dict, regime_confidence: float) -> Tuple[bool, str]:
        """
        Determine if we should execute immediately based on regime confidence.
        
        Args:
            signals: Current market signals
            regime_confidence: Current regime confidence (0-1)
            
        Returns:
            Tuple of (should_execute, reason)
        """
        # High confidence regimes: execute immediately
        if regime_confidence >= self.high_confidence_threshold:
            return True, f"High confidence regime ({regime_confidence:.2f})"
            
        # Low confidence: check spread quality
        spread = signals.get('spread_mean', 0)
        spread_threshold = signals.get('spread_mean_low_thresh', 0.0001)
        
        if spread <= spread_threshold:
            return True, f"Tight spread in low confidence regime ({spread:.6f})"
            
        return False, f"Wait for better conditions (confidence={regime_confidence:.2f}, spread={spread:.6f})"
        
    def calculate_execution_score(self, signals: Dict, direction: str) -> Tuple[float, Dict]:
        """
        Calculate execution quality score (0-100+).
        
        Components:
        - Spread quality (40%): Tighter spreads score higher
        - OBI alignment (30%): Imbalance aligned with direction scores higher  
        - Price stability (30%): Lower volatility scores higher
        - Bonus points for improving conditions
        
        Args:
            signals: Current market signals
            direction: Trade direction ('long' or 'short')
            
        Returns:
            Tuple of (score, components)
        """
        score = 0.0
        components = {}
        
        # 1. Spread quality (40 points max)
        spread = signals.get('spread_mean', 0.001)
        spread_low = signals.get('spread_mean_low_thresh', 0.0001)
        spread_high = signals.get('spread_std_high_thresh', 0.0005)
        
        if spread <= spread_low:
            spread_score = 40.0
        elif spread >= spread_high:
            spread_score = 0.0
        else:
            # Linear scaling between low and high
            spread_score = 40.0 * (1.0 - (spread - spread_low) / (spread_high - spread_low))
        
        score += spread_score
        components['spread_quality'] = spread_score
        
        # 2. OBI alignment (30 points max)
        obi = signals.get('obi_smoothed_5', 0.0)
        
        if direction == 'long':
            # For longs, positive OBI is good
            obi_score = 30.0 * max(0.0, min(1.0, obi * 5))  # Scale OBI to 0-1
        else:
            # For shorts, negative OBI is good
            obi_score = 30.0 * max(0.0, min(1.0, -obi * 5))
            
        score += obi_score
        components['obi_alignment'] = obi_score
        
        # 3. Price stability (30 points max)
        volatility = signals.get('atr_percent', 0.02)
        
        if volatility <= 0.01:
            vol_score = 30.0
        elif volatility >= 0.04:
            vol_score = 0.0
        else:
            # Linear scaling
            vol_score = 30.0 * (1.0 - (volatility - 0.01) / 0.03)
            
        score += vol_score
        components['price_stability'] = vol_score
        
        # 4. Bonus: Spread velocity (up to 10 points)
        spread_trend = signals.get('spread_trend', 0.0)
        if spread_trend < 0:  # Tightening spread
            bonus_spread = min(10.0, abs(spread_trend) * 100)
            score += bonus_spread
            components['spread_velocity_bonus'] = bonus_spread
            
        # 5. Bonus: OBI momentum (up to 15 points)
        obi_zscore = signals.get('obi_zscore_5', 0.0)
        if direction == 'long' and obi_zscore > 1.0:
            bonus_obi = min(15.0, (obi_zscore - 1.0) * 5)
            score += bonus_obi
            components['obi_momentum_bonus'] = bonus_obi
        elif direction == 'short' and obi_zscore < -1.0:
            bonus_obi = min(15.0, (abs(obi_zscore) - 1.0) * 5)
            score += bonus_obi
            components['obi_momentum_bonus'] = bonus_obi
            
        # 6. Regime adjustment (-10 to +5 points)
        regime = signals.get('regime', 'UNKNOWN')
        if regime in ['High_Vol_Range', 'UNCERTAIN']:
            score -= 10.0
            components['regime_penalty'] = -10.0
        elif regime in ['BULL', 'BEAR']:
            score += 5.0
            components['regime_bonus'] = 5.0
            
        components['total_score'] = score
        
        return score, components
        
    def check_momentum_confirmation(self, minute_candle: Dict, direction: str) -> bool:
        """
        Check if the current minute's momentum confirms the signal direction.
        
        Args:
            minute_candle: Current 1-minute candle data
            direction: Trade direction ('long' or 'short')
            
        Returns:
            True if momentum confirms direction
        """
        # Simple momentum check using the minute candle
        open_price = minute_candle.get('open', 0)
        close_price = minute_candle.get('close', 0)
        
        if open_price == 0:
            return True
            
        momentum = (close_price - open_price) / open_price
        
        # Check if momentum aligns with direction
        min_momentum = 0.0001  # 0.01% minimum momentum
        
        if direction == 'long':
            confirmed = momentum > -min_momentum  # Allow neutral/positive
        else:  # short
            confirmed = momentum < min_momentum   # Allow neutral/negative
            
        if not confirmed:
            self.logger.info(f"Momentum not confirmed: {momentum:.4f} for {direction} signal")
            
        return confirmed
        
    def find_best_execution_minute(self, minute_candles: List[Dict], direction: str, 
                                  regime_confidence: float) -> Tuple[int, float, Dict]:
        """
        Find the best minute to execute within the first 5 minutes.
        
        Args:
            minute_candles: List of 1-minute candles (up to 5)
            direction: Trade direction ('long' or 'short')
            regime_confidence: Current regime confidence
            
        Returns:
            Tuple of (best_minute_index, best_price, score_components)
        """
        # High confidence: execute immediately
        if regime_confidence >= self.high_confidence_threshold:
            self.logger.info(f"High confidence ({regime_confidence:.2f}), executing immediately")
            return 0, minute_candles[0]['close'], {'immediate_execution': True}
            
        best_score = 0.0
        best_index = 0
        best_price = minute_candles[0]['close']
        best_components = {}
        
        # Check first 5 minutes (or less if not available)
        minutes_to_check = min(len(minute_candles), 5)
        
        for i in range(minutes_to_check):
            candle = minute_candles[i]
            
            # Simple scoring based on minute candle data
            score = 0.0
            components = {}
            
            # 1. Spread proxy (use high-low as spread estimate)
            high_low_spread = (candle['high'] - candle['low']) / candle['close'] if candle['close'] > 0 else 0.01
            spread_score = max(0, 40 * (1 - high_low_spread / 0.001))  # Better score for tighter range
            score += spread_score
            components['spread_estimate'] = spread_score
            
            # 2. Momentum check
            if self.check_momentum_confirmation(candle, direction):
                score += 30
                components['momentum_confirmed'] = 30
            else:
                components['momentum_confirmed'] = 0
                
            # 3. Price favorability
            if direction == 'long':
                # For longs, lower price within the minute is better
                price_score = 30 * (1 - (candle['close'] - candle['low']) / (candle['high'] - candle['low'] + 0.0001))
            else:
                # For shorts, higher price within the minute is better
                price_score = 30 * ((candle['close'] - candle['low']) / (candle['high'] - candle['low'] + 0.0001))
            score += price_score
            components['price_favorability'] = price_score
            
            components['total_score'] = score
            
            # Update best if this minute is better
            if score > best_score:
                best_score = score
                best_index = i
                best_price = candle['close']
                best_components = components
                
            # If score is good enough, execute now
            if score >= self.min_execution_score:
                self.logger.info(f"Good execution found at minute {i}: score={score:.1f}")
                return i, candle['close'], components
                
        # If no good score found, return the best we found
        self.logger.info(f"No ideal execution found, best score={best_score:.1f} at minute {best_index}")
        return best_index, best_price, best_components
            
    def get_execution_window_seconds(self) -> int:
        """Get execution window in seconds."""
        return self.execution_window_minutes * 60