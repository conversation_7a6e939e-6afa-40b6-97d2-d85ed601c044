"""
Modern Execution Refinement Module
==================================

This module optimizes trade execution using high-frequency data (1-second)
within a limited time window after signal generation.

Key Features:
- Uses 1-second data for precise entry timing
- Analyzes microstructure within 5-minute window
- Optimizes based on spread, volume, and momentum
- Reduces slippage through smart order timing
- ZERO look-ahead bias in all operations

The execution refiner helps bridge the gap between hourly signals
and actual market entry, improving fill quality.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)


class ExecutionRefiner:
    """
    Refines trade execution using high-frequency microstructure data.
    
    This class analyzes 1-second data within a short window after signal
    generation to find optimal entry points based on:
    - Bid-ask spread dynamics
    - Volume patterns
    - Short-term momentum
    - Order book imbalance (if available)
    """
    
    def __init__(self, 
                 window_minutes: int = 5,
                 min_volume_percentile: float = 20.0,
                 max_spread_percentile: float = 80.0,
                 momentum_weight: float = 0.3):
        """
        Initialize execution refiner.
        
        Args:
            window_minutes: Execution window size (default 5 minutes)
            min_volume_percentile: Minimum volume percentile to execute
            max_spread_percentile: Maximum spread percentile to execute
            momentum_weight: Weight for momentum in timing score
        """
        self.window_minutes = window_minutes
        self.min_volume_percentile = min_volume_percentile
        self.max_spread_percentile = max_spread_percentile
        self.momentum_weight = momentum_weight
        self.logger = logger
        
        self.logger.info(
            f"ExecutionRefiner initialized:\n"
            f"  - Window: {window_minutes} minutes\n"
            f"  - Min volume percentile: {min_volume_percentile}\n"
            f"  - Max spread percentile: {max_spread_percentile}\n"
            f"  - Momentum weight: {momentum_weight}"
        )
    
    def refine_entry(self,
                    signal: Dict[str, Any],
                    window_data: pd.DataFrame,
                    current_time: datetime) -> Dict[str, Any]:
        """
        Refine entry timing using high-frequency data.
        
        CRITICAL: Only uses data within execution window, no look-ahead!
        
        Args:
            signal: Trading signal dictionary
            window_data: 1-second data for execution window
            current_time: Current timestamp (signal generation time)
            
        Returns:
            Refined execution dictionary with optimal entry
        """
        if window_data.empty:
            self.logger.warning("No execution window data available")
            return self._use_signal_price(signal, current_time)
        
        # Ensure no look-ahead bias
        window_end = current_time + timedelta(minutes=self.window_minutes)
        window_data = window_data[
            (window_data.index >= current_time) & 
            (window_data.index < window_end)
        ].copy()
        
        if len(window_data) < 10:  # Need at least 10 seconds
            self.logger.warning(f"Insufficient window data: {len(window_data)} rows")
            return self._use_signal_price(signal, current_time)
        
        # Calculate execution metrics
        metrics = self._calculate_execution_metrics(window_data, signal['direction'])
        
        # Find optimal entry point
        optimal_idx = self._find_optimal_entry(metrics, signal['direction'])
        
        # Get execution details
        exec_time = window_data.index[optimal_idx]
        exec_price = window_data.iloc[optimal_idx]['close']
        
        # Calculate execution quality metrics
        quality_metrics = self._calculate_quality_metrics(
            window_data, optimal_idx, signal
        )
        
        return {
            'timestamp': exec_time,
            'price': exec_price,
            'signal_price': signal.get('signal_price', window_data.iloc[0]['close']),
            'improvement': exec_price - signal.get('signal_price', exec_price),
            'execution_seconds': (exec_time - current_time).total_seconds(),
            'quality_score': quality_metrics['score'],
            'spread_at_exec': quality_metrics['spread'],
            'volume_at_exec': quality_metrics['volume'],
            'momentum_at_exec': quality_metrics['momentum']
        }
    
    def _calculate_execution_metrics(self, 
                                   data: pd.DataFrame,
                                   direction: str) -> pd.DataFrame:
        """
        Calculate execution quality metrics for each timestamp.
        
        Args:
            data: Window data
            direction: Trade direction ('long' or 'short')
            
        Returns:
            DataFrame with execution metrics
        """
        metrics = pd.DataFrame(index=data.index)
        
        # 1. Spread calculation (if bid/ask available)
        if 'bid' in data.columns and 'ask' in data.columns:
            metrics['spread'] = data['ask'] - data['bid']
            metrics['spread_pct'] = metrics['spread'] / data['close'] * 100
        else:
            # Estimate spread from high-low
            metrics['spread'] = data['high'] - data['low']
            metrics['spread_pct'] = metrics['spread'] / data['close'] * 100
        
        # 2. Volume metrics
        metrics['volume'] = data['volume']
        metrics['volume_ma'] = metrics['volume'].rolling(window=10, min_periods=1).mean()
        metrics['volume_ratio'] = metrics['volume'] / metrics['volume_ma'].mean()
        
        # 3. Short-term momentum
        metrics['momentum'] = data['close'].pct_change(5).fillna(0) * 100
        
        # 4. Order book imbalance (if available)
        if 'volume_imbalance' in data.columns:
            metrics['obi'] = data['volume_imbalance']
        else:
            metrics['obi'] = 0.0
        
        # 5. Price favorability
        if direction == 'long':
            # For longs, lower price is better
            metrics['price_score'] = 1 - (data['close'] - data['close'].min()) / (
                data['close'].max() - data['close'].min() + 1e-10
            )
        else:
            # For shorts, higher price is better
            metrics['price_score'] = (data['close'] - data['close'].min()) / (
                data['close'].max() - data['close'].min() + 1e-10
            )
        
        # 6. Calculate percentiles for filtering
        metrics['spread_percentile'] = metrics['spread_pct'].rank(pct=True) * 100
        metrics['volume_percentile'] = metrics['volume'].rank(pct=True) * 100
        
        return metrics
    
    def _find_optimal_entry(self,
                           metrics: pd.DataFrame,
                           direction: str) -> int:
        """
        Find optimal entry point based on combined metrics.
        
        Args:
            metrics: Execution metrics DataFrame
            direction: Trade direction
            
        Returns:
            Index of optimal entry point
        """
        # Create composite score
        scores = pd.DataFrame(index=metrics.index)
        
        # 1. Spread score (lower is better)
        scores['spread_score'] = 1 - metrics['spread_percentile'] / 100
        
        # 2. Volume score (higher is better)
        scores['volume_score'] = metrics['volume_percentile'] / 100
        
        # 3. Momentum score (depends on direction)
        if direction == 'long':
            # For longs, positive momentum is good
            scores['momentum_score'] = metrics['momentum'].clip(-1, 1) / 2 + 0.5
        else:
            # For shorts, negative momentum is good
            scores['momentum_score'] = -metrics['momentum'].clip(-1, 1) / 2 + 0.5
        
        # 4. Price score (already calculated)
        scores['price_score'] = metrics['price_score']
        
        # 5. OBI score (if available)
        if 'obi' in metrics.columns:
            if direction == 'long':
                scores['obi_score'] = metrics['obi'].clip(-1, 1) / 2 + 0.5
            else:
                scores['obi_score'] = -metrics['obi'].clip(-1, 1) / 2 + 0.5
        else:
            scores['obi_score'] = 0.5
        
        # Apply filters
        valid_mask = (
            (metrics['volume_percentile'] >= self.min_volume_percentile) &
            (metrics['spread_percentile'] <= self.max_spread_percentile)
        )
        
        if not valid_mask.any():
            # If no points pass filters, relax constraints
            valid_mask = metrics['volume_percentile'] >= 10
        
        # Calculate weighted composite score
        weights = {
            'spread_score': 0.25,
            'volume_score': 0.20,
            'momentum_score': self.momentum_weight,
            'price_score': 0.15,
            'obi_score': 0.10
        }
        
        # Normalize weights to sum to 1
        total_weight = sum(weights.values())
        weights = {k: v/total_weight for k, v in weights.items()}
        
        # Calculate composite score
        scores['composite'] = sum(
            scores[col] * weight 
            for col, weight in weights.items()
        )
        
        # Apply validity mask
        scores.loc[~valid_mask, 'composite'] = -999
        
        # Find best entry point
        optimal_idx = scores['composite'].argmax()
        
        self.logger.debug(
            f"Optimal entry at index {optimal_idx}: "
            f"score={scores.iloc[optimal_idx]['composite']:.3f}"
        )
        
        return optimal_idx
    
    def _calculate_quality_metrics(self,
                                  data: pd.DataFrame,
                                  exec_idx: int,
                                  signal: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate execution quality metrics.
        
        Args:
            data: Window data
            exec_idx: Execution index
            signal: Original signal
            
        Returns:
            Quality metrics dictionary
        """
        exec_row = data.iloc[exec_idx]
        
        # Basic metrics
        spread = exec_row.get('ask', exec_row['high']) - exec_row.get('bid', exec_row['low'])
        volume = exec_row['volume']
        
        # Momentum at execution
        if exec_idx >= 5:
            momentum = (exec_row['close'] / data.iloc[exec_idx-5]['close'] - 1) * 100
        else:
            momentum = 0.0
        
        # Calculate quality score (0-100)
        spread_score = max(0, 100 - spread / exec_row['close'] * 1000)  # Lower spread = higher score
        volume_score = min(100, volume / data['volume'].mean() * 50)    # Higher volume = higher score
        
        # Direction-adjusted momentum score
        if signal['direction'] == 'long':
            momentum_score = min(100, max(0, momentum * 20 + 50))
        else:
            momentum_score = min(100, max(0, -momentum * 20 + 50))
        
        quality_score = (spread_score * 0.4 + volume_score * 0.3 + momentum_score * 0.3)
        
        return {
            'score': quality_score,
            'spread': spread,
            'volume': volume,
            'momentum': momentum
        }
    
    def _use_signal_price(self, 
                         signal: Dict[str, Any],
                         current_time: datetime) -> Dict[str, Any]:
        """
        Fallback to signal price when refinement not possible.
        
        Args:
            signal: Trading signal
            current_time: Current timestamp
            
        Returns:
            Basic execution dictionary
        """
        return {
            'timestamp': current_time,
            'price': signal.get('signal_price', 0),
            'signal_price': signal.get('signal_price', 0),
            'improvement': 0.0,
            'execution_seconds': 0.0,
            'quality_score': 50.0,  # Neutral score
            'spread_at_exec': 0.0,
            'volume_at_exec': 0.0,
            'momentum_at_exec': 0.0
        }
    
    def analyze_execution_performance(self,
                                    executions: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Analyze execution performance across multiple trades.
        
        Args:
            executions: List of execution dictionaries
            
        Returns:
            Performance statistics
        """
        if not executions:
            return {}
        
        # Convert to DataFrame for analysis
        df = pd.DataFrame(executions)
        
        # Calculate statistics
        stats = {}
        
        # Only calculate stats for fields that exist
        if 'improvement' in df.columns:
            stats['avg_improvement'] = df['improvement'].mean()
            stats['positive_improvement_pct'] = (df['improvement'] > 0).mean() * 100
        
        if 'execution_seconds' in df.columns:
            stats['avg_execution_seconds'] = df['execution_seconds'].mean()
            
        if 'quality_score' in df.columns:
            stats['avg_quality_score'] = df['quality_score'].mean()
            
        if 'spread_at_exec' in df.columns:
            stats['avg_spread'] = df['spread_at_exec'].mean()
            
        if 'momentum_at_exec' in df.columns:
            stats['avg_momentum'] = df['momentum_at_exec'].mean()
        
        # Breakdown by direction
        if 'direction' in df.columns:
            for direction in ['long', 'short']:
                dir_df = df[df['direction'] == direction]
                if not dir_df.empty:
                    if 'improvement' in df.columns:
                        stats[f'{direction}_avg_improvement'] = dir_df['improvement'].mean()
                    stats[f'{direction}_count'] = len(dir_df)
        
        return stats