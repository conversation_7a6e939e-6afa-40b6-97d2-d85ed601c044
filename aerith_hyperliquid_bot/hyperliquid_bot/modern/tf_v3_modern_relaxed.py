"""
Modern TF-v3 Strategy with Relaxed Entry Conditions
===================================================

This variant allows trading in both Strong and Weak trend states,
making it less restrictive than the original which only trades
in Strong trends (0.9% of time).

Key changes:
- Allows Weak_Bull_Trend and Weak_Bear_Trend states
- Still respects quality filtering
- Uses same risk management
"""

from typing import Dict, Any, Optional, List
import logging

from ..strategies.tf_v3_modern import ModernTFV3Strategy
from .registry import modern_strategy

@modern_strategy("tf_v3_modern_relaxed", version="1.0", experimental=False)
class ModernTFV3StrategyRelaxed(ModernTFV3Strategy):
    """
    Relaxed version of Modern TF-v3 that trades in Weak trends too.
    
    The original only trades in Strong trends (0.9% of time).
    This version also trades in Weak trends (18.4% of time total).
    """
    
    def __init__(self, config: dict, strategy_id: str = "tf_v3_modern_relaxed"):
        """Initialize with parent class."""
        super().__init__(config, strategy_id)
        self.logger = logging.getLogger(f"{self.__class__.__name__}[{strategy_id}]")
        
        # Log the relaxed configuration
        self.logger.info(
            "Relaxed TF-v3 Strategy initialized:\n"
            "  - Allows Strong AND Weak trend states\n"
            "  - Expected ~20x more trading opportunities"
        )
    
    def get_allowed_regimes(self) -> List[str]:
        """
        Get list of allowed regime states for trading.
        
        RELAXED: Includes both Strong and Weak trends.
        """
        return [
            "Strong_Bull_Trend",
            "Weak_Bull_Trend", 
            "Strong_Bear_Trend",
            "Weak_Bear_Trend"
        ]
    
    def should_enter_position(self, state: Dict[str, Any]) -> bool:
        """
        Determine if we should enter a position.
        
        Uses relaxed regime requirements.
        """
        # Get current regime
        regime = state.get('regime', 'Unknown')
        
        # Check if regime allows trading (relaxed)
        if regime not in self.get_allowed_regimes():
            self.logger.debug(f"Regime {regime} not in allowed list (relaxed)")
            return False
            
        # Check risk suppression
        if state.get('risk_suppressed', False):
            self.logger.debug("Risk suppressed - no entry")
            return False
            
        # All other checks remain the same
        return super().should_enter_position(state)