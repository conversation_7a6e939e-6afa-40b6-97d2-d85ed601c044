"""
Modern TF-v3 Strategy
=====================

This is the modernized version of TF-v3 that:
- Accepts regime history from HourlyStrategyEvaluator
- Uses the last 60 regime states for informed decisions
- Considers regime stability and transitions
- Maintains compatibility with the IStrategy interface

Key improvements over legacy TF-v3:
- Uses 60-second regime history instead of single snapshot
- Better regime stability detection
- Improved entry timing based on regime transitions
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Literal, Any
import pandas as pd
import numpy as np

from ..config.settings import Config
from ..core.interfaces import IStrategy
from ..strategies.exceptions import SkipSignal
from ..features.indicators import calculate_ema, calculate_atr
from ..utils.state_mapping import (
    GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND,
    GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_WEAK_BEAR_TREND,
    GMS_STATE_HIGH_VOL_RANGE, GMS_STATE_LOW_VOL_RANGE,
    GMS_STATE_UNCERTAIN, GMS_STATE_UNKNOWN
)
from .registry import modern_strategy


@modern_strategy("tf_v3_modern", version="3.0", experimental=True)
class ModernTFV3Strategy(IStrategy):
    """
    Modern TF-v3 Strategy that uses regime history.
    
    This strategy:
    1. Uses regime history from the last hour (60 states)
    2. Only trades in trending regimes (BULL/BEAR)
    3. Considers regime stability before entry
    4. Uses EMA crossover with regime confirmation
    5. Implements ATR-based stops and time-decay exits
    """
    
    def __init__(self, config: Config, strategy_name: str = "tf_v3_modern"):
        """Initialize modern TF-v3 strategy."""
        self.config = config
        self.tf_v3_config = config.tf_v3
        self.strategy_name = strategy_name
        self.logger = logging.getLogger(f"{self.__class__.__name__}[{strategy_name}]")
        
        # State tracking
        self.position = None
        self.entry_price = None
        self.entry_time = None
        self.trail_price = None
        self.position_type = None
        
        # Config defaults
        self.base_position_size = getattr(self.tf_v3_config, 'base_position_size', 0.02)
        self.min_regime_duration_minutes = getattr(self.tf_v3_config, 'min_regime_duration_minutes', 30)  # Increased from 10
        self.max_regime_changes_1h = getattr(self.tf_v3_config, 'max_regime_changes_1h', 3)  # Reduced from 5
        self.min_regime_confidence = getattr(self.tf_v3_config, 'min_regime_confidence', 0.65)  # Increased from 0.6
        
        # Required signals
        self.required_signals = [
            'close', 'high', 'low', 'volume',
            'ema_fast', 'ema_slow', 'ema_baseline',
            'atr_14', 'atr_percent',
            'rsi', 'bb_upper', 'bb_lower', 'bb_middle'
        ]
        
        # Allowed regime states for trading
        self.trend_states = [
            GMS_STATE_STRONG_BULL_TREND,
            GMS_STATE_WEAK_BULL_TREND,
            GMS_STATE_STRONG_BEAR_TREND,
            GMS_STATE_WEAK_BEAR_TREND
        ]
        
        self.logger.info(
            f"Modern TF-v3 Strategy initialized:\n"
            f"  - EMA periods: {self.tf_v3_config.ema_fast}/{self.tf_v3_config.ema_slow}\n"
            f"  - ATR multiplier: {self.tf_v3_config.atr_trail_k}\n"
            f"  - Max hold time: {self.tf_v3_config.max_trade_life_h} hours"
        )
    
    def evaluate_entry(self, 
                      signals: Dict[str, Any], 
                      regime: str) -> Optional[Dict[str, Any]]:
        """
        Evaluate entry opportunity using signals and regime.
        
        Args:
            signals: Market signals including regime_features
            regime: Current regime state
            
        Returns:
            Entry decision dict or None
        """
        # Extract regime features
        regime_features = signals.get('regime_features', {})
        
        # 1. Check if regime allows trading
        if regime not in self.trend_states:
            self.logger.debug(f"Regime {regime} not in trend states, skipping")
            return None
        
        # 2. Check regime stability
        if not self._is_regime_stable(regime_features):
            self.logger.debug("Regime not stable enough for entry")
            return None
        
        # 3. Check risk suppression
        if regime_features.get('risk_suppressed', False):
            self.logger.debug("Risk suppressed, skipping entry")
            return None
        
        # 4. Check basic entry conditions
        ema_fast = signals.get('ema_fast')  # 20 period
        ema_slow = signals.get('ema_slow')   # 50 period
        ema_baseline = signals.get('ema_baseline')
        close_price = signals.get('close')
        forecast = signals.get('forecast', 0)  # CRITICAL: Get forecast indicator
        
        if any(pd.isna(x) for x in [ema_fast, ema_slow, ema_baseline, close_price, forecast]):
            self.logger.debug("Missing required indicators")
            return None
        
        # CRITICAL FIX: Calculate FAST EMAs (8/21) for crypto-appropriate entry timing
        # Get historical data from signals if available
        ohlcv_data = signals.get('ohlcv_data')
        if ohlcv_data is not None and len(ohlcv_data) >= 21:
            ema_8 = ohlcv_data['close'].ewm(span=8, adjust=False).mean().iloc[-1]
            ema_21 = ohlcv_data['close'].ewm(span=21, adjust=False).mean().iloc[-1]
            self.logger.debug(f"Fast EMAs: EMA8={ema_8:.2f}, EMA21={ema_21:.2f}")
        else:
            # Fallback: approximate fast EMAs from current values
            ema_8 = close_price  # Use close as proxy
            ema_21 = ema_fast   # Use 20 EMA as proxy for 21
            self.logger.debug("Using approximate fast EMAs")
        
        # 5. Determine entry direction based on EMA crossover + FORECAST CONFIRMATION
        direction = None
        
        # CRITICAL FIX: Require forecast confirmation like legacy system
        # This is what reduces trades from 1000+ to ~180!
        
        # Calculate forecast threshold based on close price
        # Require stronger forecast values to reduce trade frequency
        close_price = signals.get('close', 1.0)
        base_forecast_threshold = close_price * 0.0001  # 0.01% of price as minimum forecast
        
        # REGIME CONFIDENCE SCALING - High confidence = lower threshold
        regime_confidence = regime_features.get('current_confidence', 0.5)
        if regime_confidence > 0.8:
            forecast_threshold = base_forecast_threshold * 0.7  # 30% more lenient
        else:
            forecast_threshold = base_forecast_threshold
        
        # Check for strong regime early entry opportunities
        is_strong_regime = regime in [GMS_STATE_STRONG_BULL_TREND, GMS_STATE_STRONG_BEAR_TREND]
        ema_distance = abs(ema_fast - ema_slow)
        ema_about_to_cross = ema_distance < (close_price * 0.0005)  # Within 0.05% of crossing
        
        # Bullish entry conditions
        if regime in [GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND]:
            # Standard entry: Fast > Slow AND forecast > threshold
            if ema_fast > ema_slow and forecast > forecast_threshold:
                direction = 'long'
                self.logger.info(f"LONG signal: EMA {ema_fast:.2f} > {ema_slow:.2f}, forecast={forecast:.4f} > {forecast_threshold:.4f} in {regime}")
            
            # REGIME-DOMINANT ENTRY: In strong bull trends with high confidence
            # Trust the regime even if EMAs haven't fully crossed yet
            elif (regime == GMS_STATE_STRONG_BULL_TREND and regime_confidence > 0.85 and
                  forecast > 0):  # Just need positive forecast, not full threshold
                # Additional safety: Check that we're not in strong downtrend based on price
                price_momentum = (close_price - signals.get('ema_baseline', close_price)) / close_price
                if price_momentum > -0.01:  # Not more than 1% below baseline
                    direction = 'long'
                    self.logger.info(f"REGIME-DOMINANT LONG: Strong bull regime (conf={regime_confidence:.2f}), forecast={forecast:.4f}")
            
            # EARLY ENTRY for strong bull with high confidence
            elif (is_strong_regime and regime_confidence > 0.9 and 
                  ema_about_to_cross and forecast > forecast_threshold * 0.5):
                # Check momentum direction
                ema_momentum = signals.get('ema_fast', 0) - signals.get('ema_fast_prev', signals.get('ema_fast', 0))
                if ema_momentum > 0:  # EMAs converging in right direction
                    direction = 'long'
                    self.logger.info(f"EARLY LONG: EMAs about to cross, strong regime (conf={regime_confidence:.2f})")
        
        # Bearish entry conditions
        elif regime in [GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_WEAK_BEAR_TREND]:
            # NEW APPROACH: Use FAST EMAs (8/21) for entry in crypto markets
            
            # Strong Bear: Only need fast EMAs to confirm
            if regime == GMS_STATE_STRONG_BEAR_TREND and ema_8 < ema_21:
                direction = 'short'
                self.logger.info(f"SHORT signal (Strong Bear): Fast EMA8={ema_8:.2f} < EMA21={ema_21:.2f}, regime conf={regime_confidence:.2f}")
            
            # Weak Bear: Need both fast EMAs AND either slow EMAs or strong forecast
            elif regime == GMS_STATE_WEAK_BEAR_TREND and ema_8 < ema_21:
                # Additional confirmation: either slow EMAs agree OR forecast is strongly negative
                if ema_fast < ema_slow or forecast < -forecast_threshold:
                    direction = 'short' 
                    self.logger.info(f"SHORT signal (Weak Bear): Fast EMA8<21, confirmed by slow EMAs or forecast")
            
            # Fallback: Traditional logic if fast EMAs not bearish yet
            elif ema_fast < ema_slow and forecast < -forecast_threshold:
                direction = 'short'
                self.logger.info(f"SHORT signal (Traditional): EMA20<50 and forecast<-threshold")
        
        if direction is None:
            return None
        
        # 6. Calculate entry confidence
        confidence = self._calculate_entry_confidence(
            signals, regime, regime_features, direction
        )
        
        # 7. Prepare entry decision
        entry_decision = {
            'direction': direction,
            'confidence': confidence,
            'position_size': self._calculate_position_size(signals, confidence),
            'entry_reason': 'ema_crossover_regime_confirmed',
            'regime': regime,
            'regime_confidence': regime_features.get('current_confidence', 0.0),
            'regime_persistence': regime_features.get('state_persistence', 0.0),
            'is_trending': regime_features.get('is_trending', False)
        }
        
        self.logger.info(
            f"Entry signal: {direction} "
            f"(confidence: {confidence:.2f}, regime: {regime})"
        )
        
        return entry_decision
    
    def check_exit(self, 
                   signals: Dict[str, Any], 
                   position: Dict[str, Any]) -> Optional[str]:
        """
        Check if position should be exited.
        
        Args:
            signals: Current market signals
            position: Current position information
            
        Returns:
            Exit reason string or None
        """
        # Extract basic info
        current_price = signals.get('close')
        entry_price = position.get('entry_price')
        position_type = position.get('direction')
        entry_time = position.get('entry_time')
        
        if any(pd.isna(x) for x in [current_price, entry_price]):
            return None
        
        # 1. Check stop loss
        stop_loss = position.get('stop_loss')
        if stop_loss:
            if position_type == 'long' and current_price <= stop_loss:
                return 'stop_loss_hit'
            elif position_type == 'short' and current_price >= stop_loss:
                return 'stop_loss_hit'
        
        # 2. Check trailing stop
        atr = signals.get('atr_14', 0)
        if atr > 0:
            trail_distance = atr * self.tf_v3_config.atr_trail_k
            
            if position_type == 'long':
                trail_stop = current_price - trail_distance
                if hasattr(self, 'trail_price') and self.trail_price:
                    trail_stop = max(trail_stop, self.trail_price)
                self.trail_price = trail_stop
                
                if current_price <= trail_stop:
                    return 'trailing_stop_hit'
            
            elif position_type == 'short':
                trail_stop = current_price + trail_distance
                if hasattr(self, 'trail_price') and self.trail_price:
                    trail_stop = min(trail_stop, self.trail_price)
                self.trail_price = trail_stop
                
                if current_price >= trail_stop:
                    return 'trailing_stop_hit'
        
        # 3. Check time decay exit
        if entry_time:
            if isinstance(entry_time, str):
                entry_time = pd.to_datetime(entry_time)
            
            current_time = signals.get('timestamp', datetime.now())
            if isinstance(current_time, (int, float)):
                current_time = pd.to_datetime(current_time, unit='s')
            
            hold_time = (current_time - entry_time).total_seconds() / 3600
            
            if hold_time >= self.tf_v3_config.max_trade_life_h:
                return 'max_hold_time_exceeded'
        
        # 4. Check regime change exit
        current_regime = signals.get('regime_state')
        if current_regime and current_regime not in self.trend_states:
            return 'regime_change_exit'
        
        # 5. Check EMA reversal
        ema_fast = signals.get('ema_fast')
        ema_slow = signals.get('ema_slow')
        
        if position_type == 'long' and ema_fast < ema_slow:
            return 'ema_reversal_exit'
        elif position_type == 'short' and ema_fast > ema_slow:
            return 'ema_reversal_exit'
        
        return None
    
    def get_position_size(self, 
                         signals: Dict[str, Any], 
                         direction: str) -> float:
        """
        Calculate position size.
        
        This is a simple implementation. The actual sizing
        should be done by the HourlyStrategyEvaluator.
        
        Args:
            signals: Market signals
            direction: Trade direction
            
        Returns:
            Base position size
        """
        # Return base size, let evaluator handle risk management
        return self.base_position_size
    
    def get_strategy_name(self) -> str:
        """Get strategy name."""
        return self.strategy_name
    
    def get_allowed_states(self, strategy_type: str) -> List[str]:
        """Get allowed regime states."""
        if strategy_type == 'trend_following':
            return self.trend_states
        return []
    
    def _is_regime_stable(self, regime_features: Dict[str, Any]) -> bool:
        """
        Check if regime is stable enough for entry.
        
        CALIBRATED FOR 2024 DATA: Relaxed thresholds to allow ~100-200 trades/year
        while maintaining quality.
        
        Args:
            regime_features: Regime feature dictionary
            
        Returns:
            True if regime is stable
        """
        # Check confidence (increased requirement for stability)
        confidence = regime_features.get('current_confidence', 0.0)
        if confidence < self.min_regime_confidence:  # Use config value (0.65)
            return False
        
        # Check recent transitions (more restrictive)
        # recent_transitions counts state changes in last ~10 minutes
        recent_transitions = regime_features.get('recent_transitions', 0)
        # Scale to hourly equivalent (6x)
        state_changes_equiv = recent_transitions * 6
        # More restrictive: Allow fewer transitions
        if state_changes_equiv > self.max_regime_changes_1h * 6:  # 3*6 = 18
            return False
        
        # CALIBRATED: State persistence check DISABLED
        # In crypto markets, regimes change rapidly - persistence is always near 0
        # Only use confidence and transition checks
        # state_persistence = regime_features.get('state_persistence', 0.0)
        # if state_persistence < 0.2:
        #     return False
        
        # Risk suppression check (keep strict)
        if regime_features.get('risk_suppressed', False):
            return False
        
        return True
    
    def _calculate_entry_confidence(self,
                                   signals: Dict[str, Any],
                                   regime: str,
                                   regime_features: Dict[str, Any],
                                   direction: str) -> float:
        """
        Calculate entry confidence score.
        
        Args:
            signals: Market signals
            regime: Current regime
            regime_features: Regime features
            direction: Trade direction
            
        Returns:
            Confidence score [0-1]
        """
        confidence = 0.5  # Base confidence
        
        # Regime confidence contribution
        regime_confidence = regime_features.get('current_confidence', 0.0)
        confidence += regime_confidence * 0.2
        
        # State persistence contribution (0-1 scale)
        persistence = regime_features.get('state_persistence', 0.0)
        if persistence > 0.8:
            confidence += 0.2
        elif persistence > 0.6:
            confidence += 0.1
        
        # Momentum alignment
        avg_momentum = regime_features.get('avg_momentum', 0.0)
        if direction == 'long' and avg_momentum > 0:
            confidence += min(avg_momentum * 100, 0.1)  # Scale up since momentum is small
        elif direction == 'short' and avg_momentum < 0:
            confidence += min(abs(avg_momentum) * 100, 0.1)
        
        # EMA separation
        ema_fast = signals.get('ema_fast', 0)
        ema_slow = signals.get('ema_slow', 0)
        if ema_slow > 0:
            separation = abs(ema_fast - ema_slow) / ema_slow
            confidence += min(separation * 10, 0.1)
        
        return min(confidence, 1.0)
    
    def _calculate_position_size(self,
                               signals: Dict[str, Any],
                               confidence: float) -> float:
        """
        Calculate base position size.
        
        Args:
            signals: Market signals
            confidence: Entry confidence
            
        Returns:
            Base position size (before risk management)
        """
        base_size = self.base_position_size
        
        # Scale by confidence
        size = base_size * (0.5 + 0.5 * confidence)
        
        # Scale by volatility (inverse relationship)
        # CRITICAL FIX: atr_percent is already a percentage (e.g., 0.5 = 0.5%)
        # Don't divide by 100 again!
        atr_percent = signals.get('atr_percent', 1.0)  # Default to 1%
        if atr_percent > 0:
            # Target 1% volatility, scale inversely
            # If volatility is 0.5%, use 2x size; if 2%, use 0.5x size
            vol_scalar = min(1.0 / atr_percent, 1.5)  # Cap at 1.5x
            vol_scalar = max(vol_scalar, 0.5)  # Floor at 0.5x
            size *= vol_scalar
        
        return size