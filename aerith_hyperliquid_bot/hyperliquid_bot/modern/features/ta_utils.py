# hyperliquid_bot/features/ta_utils.py

import logging
import numpy as np
import pandas as pd
from typing import Optional, Union, Tuple

logger = logging.getLogger(__name__)

def calculate_atr(
    high: Union[pd.Series, np.ndarray],
    low: Union[pd.Series, np.ndarray],
    close: Union[pd.Series, np.ndarray],
    length: int = 14,
    prev_close: Optional[Union[pd.Series, np.ndarray]] = None,
) -> np.ndarray:
    """
    Calculate Average True Range (ATR) for a given price series.
    
    Args:
        high: High prices
        low: Low prices
        close: Close prices
        length: ATR period (default: 14)
        prev_close: Previous close prices. If None, will be derived from close
    
    Returns:
        numpy array with ATR values (first 'length' values will be NaN)
    """
    if length <= 0:
        logger.warning(f"Invalid ATR length: {length}. Must be > 0.")
        return np.full_like(close, np.nan)
    
    # Convert inputs to numpy arrays if they're pandas Series
    high_np = high.values if isinstance(high, pd.Series) else high
    low_np = low.values if isinstance(low, pd.Series) else low
    close_np = close.values if isinstance(close, pd.Series) else close
    
    # Get previous close (shifted by 1)
    if prev_close is None:
        prev_close_np = np.roll(close_np, 1)
        prev_close_np[0] = close_np[0]  # First value has no previous
    else:
        prev_close_np = prev_close.values if isinstance(prev_close, pd.Series) else prev_close
    
    # Calculate True Range
    tr1 = high_np - low_np  # Current high - current low
    tr2 = np.abs(high_np - prev_close_np)  # Current high - previous close
    tr3 = np.abs(low_np - prev_close_np)  # Current low - previous close
    
    # True Range is the maximum of the three
    tr = np.maximum(np.maximum(tr1, tr2), tr3)
    
    # Calculate ATR using Wilder's smoothing method
    atr = np.full_like(tr, np.nan)
    
    # First value is just the first TR
    if len(tr) >= length:
        # First ATR is simple average of first 'length' TRs
        atr[length-1] = np.mean(tr[:length])
        
        # Subsequent values use Wilder's smoothing
        for i in range(length, len(tr)):
            atr[i] = ((length - 1) * atr[i-1] + tr[i]) / length
    
    return atr

def calculate_atr_percent(
    high: Union[pd.Series, np.ndarray],
    low: Union[pd.Series, np.ndarray],
    close: Union[pd.Series, np.ndarray],
    length: int = 14,
) -> np.ndarray:
    """
    Calculate ATR as a percentage of close price.
    
    Args:
        high: High prices
        low: Low prices
        close: Close prices
        length: ATR period (default: 14)
    
    Returns:
        numpy array with ATR percent values
    """
    atr_values = calculate_atr(high, low, close, length)
    
    # Convert to numpy arrays if they're pandas Series
    close_np = close.values if isinstance(close, pd.Series) else close
    
    # Calculate ATR percent (ATR / close)
    with np.errstate(divide='ignore', invalid='ignore'):
        atr_pct = np.where(close_np > 0, atr_values / close_np, np.nan)
    
    return atr_pct

def calculate_realized_volatility(
    prices: Union[pd.Series, np.ndarray],
    window: int = 1,
    use_log_returns: bool = True
) -> np.ndarray:
    """
    Calculate realized volatility over a rolling window.
    
    Args:
        prices: Price series
        window: Window size for calculation
        use_log_returns: If True, use log returns; otherwise use simple returns
    
    Returns:
        numpy array with realized volatility values
    """
    # Convert to numpy array if it's a pandas Series
    prices_np = prices.values if isinstance(prices, pd.Series) else prices
    
    # Calculate returns
    if use_log_returns:
        # Log returns: ln(P_t / P_{t-1})
        returns = np.log(prices_np[1:] / prices_np[:-1])
    else:
        # Simple returns: (P_t - P_{t-1}) / P_{t-1}
        returns = (prices_np[1:] - prices_np[:-1]) / prices_np[:-1]
    
    # Absolute returns for volatility
    abs_returns = np.abs(returns)
    
    # Create result array (same length as input)
    result = np.full_like(prices_np, np.nan)
    
    # For window=1, just use absolute returns directly
    if window == 1:
        result[1:] = abs_returns
        return result
    
    # For larger windows, calculate rolling values
    for i in range(window, len(prices_np)):
        window_returns = abs_returns[i-window:i]
        result[i] = np.mean(window_returns)
    
    return result
