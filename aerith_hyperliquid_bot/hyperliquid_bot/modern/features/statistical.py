# hyperliquid_bot/features/statistical.py

import logging
import pandas as pd
import numpy as np
from typing import Optional, Union

# Import the hurst library (ensure it's installed: pip install hurst)
try:
    from hurst import compute_Hc
except ImportError:
    logging.error("The 'hurst' library is not installed. Please install it: pip install hurst")
    # Define a dummy function or raise an error if the library is critical at import time
    def compute_Hc(*args, **kwargs):
        raise ImportError("The 'hurst' library is required but not installed.")

logger = logging.getLogger(__name__)

"""
Statistical Feature Calculations.

Functions to compute statistical properties of time series data,
such as the Hurst exponent.
"""

def calculate_hurst_exponent(
    price_series: Union[pd.Series, np.ndarray, list],
    min_length: int = 100,
    kind: str = 'price',
    simplified: bool = True
) -> Optional[float]:
    """
    Calculates the Hurst exponent for a given price series using the 'hurst' library.

    The Hurst exponent (H) helps determine the nature of a time series:
    - H < 0.5: Mean-reverting (anti-persistent)
    - H = 0.5: Geometric random walk (uncorrelated)
    - H > 0.5: Trending (persistent)

    Args:
        price_series: A pandas Series, numpy array, or list of prices.
        min_length: The minimum number of data points required to attempt calculation.
                    The 'hurst' library might have its own internal minimums.
        kind: The kind of series ('price', 'change', 'random_walk'). Defaults to 'price'.
              Passed directly to compute_Hc.
        simplified: Whether to use the simplified calculation. Defaults to True.
                    Passed directly to compute_Hc.

    Returns:
        The calculated Hurst exponent as a float, or None if the calculation fails
        (e.g., insufficient data, non-numeric data, library error).
    """
    if price_series is None:
        logger.warning("Cannot calculate Hurst: Input price_series is None.")
        return None

    # Convert to numpy array if it's a pandas Series
    if isinstance(price_series, pd.Series):
        if price_series.empty:
            logger.warning("Cannot calculate Hurst: Input pandas Series is empty.")
            return None
        # Drop NaNs before calculation, as they can cause issues
        series_np = price_series.dropna().to_numpy()
    elif isinstance(price_series, list):
        series_np = np.array(price_series)
        # Remove potential NaNs/None from list conversion
        if np.isnan(series_np).any():
             logger.warning("Input list contains NaNs. Removing them before Hurst calculation.")
             series_np = series_np[~np.isnan(series_np)]
    elif isinstance(price_series, np.ndarray):
        series_np = price_series
        # Remove potential NaNs
        if np.isnan(series_np).any():
             logger.warning("Input numpy array contains NaNs. Removing them before Hurst calculation.")
             series_np = series_np[~np.isnan(series_np)]
    else:
        logger.error(f"Cannot calculate Hurst: Invalid input type '{type(price_series)}'. Expected Series, ndarray, or list.")
        return None

    # Check length after handling NaNs
    if len(series_np) < min_length:
        logger.warning(f"Cannot calculate Hurst: Series length ({len(series_np)}) is less than minimum required ({min_length}).")
        return None

    # Check if data is numeric (redundant after NaN removal, but safe)
    if not np.issubdtype(series_np.dtype, np.number):
        logger.error(f"Cannot calculate Hurst: Series data type is not numeric ({series_np.dtype}).")
        return None

    # Check for constant series (Hurst is undefined or problematic)
    if np.all(series_np == series_np[0]):
        logger.warning("Cannot calculate Hurst: Input series is constant.")
        # Depending on interpretation, could return 0.5, but None is safer
        return None
    if np.std(series_np) < 1e-9: # Check for near-constant series
         logger.warning("Cannot calculate Hurst: Input series has near-zero standard deviation (constant).")
         return None


    try:
        # Calculate the Hurst exponent using the library function
        H, c, data = compute_Hc(series_np, kind=kind, simplified=simplified)

        # Validate the result
        if H is None or not isinstance(H, (float, np.float64)) or np.isnan(H):
            logger.warning(f"Hurst calculation returned invalid result: H={H}. Series length={len(series_np)}")
            return None
        if H < 0 or H > 1:
             logger.warning(f"Hurst calculation returned value outside expected range [0, 1]: H={H:.4f}. Clamping or returning None? Returning None for now.")
             # Decide on clamping vs returning None. None is safer as it indicates an issue.
             return None


        logger.debug(f"Calculated Hurst exponent: H={H:.4f} (c={c:.4f}) from series of length {len(series_np)}")
        return float(H)

    except Exception as e:
        # Catch potential errors from the hurst library or other issues
        logger.error(f"Error calculating Hurst exponent: {e}", exc_info=False) # Set exc_info=False to avoid overly verbose logs unless debugging
        logger.debug(f"Failed Hurst calculation on series (first 5): {series_np[:5]}, length: {len(series_np)}") # Log context on error
        return None

# --- Example Usage (for testing this module directly) ---
if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s [%(levelname)-5s] %(name)-25s: %(message)s')

    # Example 1: Random Walk (H should be ~0.5)
    random_walk = np.log(np.random.randn(500)+10).cumsum()
    h_random = calculate_hurst_exponent(random_walk, min_length=50)
    logger.info(f"Example 1 (Random Walk): H = {h_random}")

    # Example 2: Mean Reverting (H should be < 0.5)
    # Simple Ornstein-Uhlenbeck process simulation
    n = 500
    theta = 0.5 # Speed of reversion
    mu = 0      # Long-term mean
    sigma = 0.1 # Volatility
    dt = 0.01
    x = np.zeros(n)
    x[0] = 0
    for i in range(1, n):
        dx = theta * (mu - x[i-1]) * dt + sigma * np.sqrt(dt) * np.random.randn()
        x[i] = x[i-1] + dx
    h_mean_revert = calculate_hurst_exponent(x, min_length=50)
    logger.info(f"Example 2 (Mean Reverting): H = {h_mean_revert}")

    # Example 3: Trending (H should be > 0.5)
    # Fractional Brownian Motion approximation (simple way)
    try:
        from fbm import FBM
        f = FBM(n=499, hurst=0.7, length=1, method='daviesharte')
        fbm_sample = f.fbm()
        h_trend = calculate_hurst_exponent(fbm_sample, min_length=50)
        logger.info(f"Example 3 (Trending - fBM H=0.7): H = {h_trend}")
    except ImportError:
        logger.warning("The 'fbm' library is not installed. Skipping Example 3.")

    # Example 4: Insufficient Data
    short_series = [1, 2, 3, 4, 5]
    h_short = calculate_hurst_exponent(short_series, min_length=50)
    logger.info(f"Example 4 (Short Series): H = {h_short}")

    # Example 5: Constant Data
    constant_series = [5] * 200
    h_constant = calculate_hurst_exponent(constant_series, min_length=50)
    logger.info(f"Example 5 (Constant Series): H = {h_constant}")

    # Example 6: Series with NaNs
    nan_series = pd.Series([1, 2, np.nan, 4, 5, 6, 7, 8, 9, 10] * 20) # Create a longer series
    h_nan = calculate_hurst_exponent(nan_series, min_length=50)
    logger.info(f"Example 6 (Series with NaNs): H = {h_nan}")