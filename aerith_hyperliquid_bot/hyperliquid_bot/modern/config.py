"""
Modern System Configuration
===========================
Experimental configuration for the modern trading system.
This can be freely modified without affecting the legacy system.
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class ModernMicrostructureConfig:
    """Modern microstructure configuration."""
    depth_levels: int = 5
    obi_levels: int = 5
    obi_smoothing_window: int = 10
    obi_smoothing_alpha: float = 0.2
    
    # Enhanced OBI parameters
    obi_zscore_window: int = 60
    obi_zscore_threshold: float = 2.0
    
    # Modern confirmation thresholds
    gms_obi_strong_confirm_thresh: float = 0.15
    gms_obi_weak_confirm_thresh: float = 0.05
    
    # TF-v3 filters
    tf_filter_obi_threshold_long: float = 0.05
    tf_filter_obi_threshold_short: float = -0.05
    tf_filter_funding_threshold_long: float = 0.001
    tf_filter_funding_threshold_short: float = -0.001


@dataclass
class ModernIndicatorConfig:
    """Modern indicator configuration."""
    # TF-v3 EMAs (different from legacy)
    tf_fast_window: int = 8
    tf_slow_window: int = 21
    tf_medium_window: int = 13
    use_tf_medium_ewma: bool = True
    
    # Enhanced indicators
    forecast_window: int = 50
    atr_window: int = 14
    tf_max_entry_volatility_pct: float = 0.025
    low_forecast_threshold: float = 0.005
    
    # Additional modern indicators
    sma_window: int = 30
    ma_slope_window: int = 30
    adx_window: int = 14
    adx_threshold: float = 25.0
    
    # Volatility bands
    bb_window: int = 20
    bb_std: float = 2.0


@dataclass
class ModernRegimeConfig:
    """Modern regime detection configuration."""
    detector_type: str = "continuous_gms"
    use_filter: bool = True
    use_enhanced_filter: bool = True
    use_chop_filter: bool = True
    
    # Modern thresholds (calibrated for continuous detection)
    gms_vol_high_thresh: float = 0.015
    gms_vol_low_thresh: float = 0.005
    gms_mom_strong_thresh: float = 2.5
    gms_mom_weak_thresh: float = 0.5
    
    # Adaptive thresholds
    gms_use_adaptive_thresholds: bool = True
    gms_adaptive_window: int = 720  # 30 days at 1h
    gms_adaptive_percentiles: Dict[str, float] = field(default_factory=lambda: {
        'vol_high': 0.85,
        'vol_low': 0.25,
        'mom_strong': 0.80,
        'mom_weak': 0.40
    })
    
    # Enhanced spread analysis
    gms_spread_std_high_thresh: float = 0.0003
    gms_spread_mean_low_thresh: float = 0.00005
    gms_spread_mean_thresh_mode: str = "adaptive"
    gms_spread_std_thresh_mode: str = "adaptive"
    gms_spread_percentile_gate: float = 0.20
    
    # Modern features
    gms_depth_slope_thin_limit: float = 0.001
    gms_depth_skew_thresh: float = 0.15
    gms_spread_trend_lookback: int = 24
    
    # State mapping
    use_strict_strategy_filtering: bool = False
    gms_use_three_state_mapping: bool = True
    map_weak_bear_to_bear: bool = True  # Different from legacy
    
    # Confirmations
    gms_use_adx_confirmation: bool = True
    gms_use_funding_confirmation: bool = True
    gms_confirmation_bars: int = 2
    
    # Risk suppression
    gms_vol_extreme_thresh: float = 0.025
    gms_spread_extreme_thresh: float = 0.001


@dataclass  
class ModernStrategyConfig:
    """Modern strategy configuration."""
    use_tf_v2: bool = False
    use_tf_v3: bool = True
    use_mean_reversion: bool = False
    use_mean_reversion_microstructure: bool = False
    
    # TF-v3 specific
    tf_warmup_bars: int = 50
    
    # Enhanced filters
    tf_use_obi_filter: bool = True
    tf_use_funding_filter: bool = True
    
    # Additional strategy parameters
    tf_regime_confidence_min: float = 0.7
    tf_regime_duration_min: float = 30.0  # minutes


@dataclass
class ModernTFV3Config:
    """Modern TF-v3 configuration."""
    # Risk management (2% per trade for modern)
    risk_frac: float = 0.02
    max_notional: float = 100000.0
    
    # Enhanced EMAs
    ema_fast: int = 8
    ema_slow: int = 21
    atr_period: int = 14
    
    # Dynamic stops/targets
    atr_trail_k: float = 1.5
    stop_loss_atr_mult: float = 1.5
    take_profit_atr_mult: float = 3.0
    use_dynamic_stops: bool = True
    use_dynamic_tp: bool = True
    
    # GMS integration
    gms_max_age_sec: int = 60  # 1 minute for continuous
    min_regime_confidence: float = 0.7
    min_regime_duration: float = 5.0


@dataclass
class ModernDataConfig:
    """Modern data configuration."""
    base_path: str = "/Volumes/M2Crypto/hyperliquid_data"
    
    @property
    def features_1s_path(self) -> str:
        """Modern 1-second features path."""
        return f"{self.base_path}/features_1s"
    
    @property  
    def ohlcv_base_path(self) -> str:
        """OHLCV data path (shared with legacy)."""
        return f"{self.base_path}/resampled_l2"
    
    @property
    def cache_path(self) -> str:
        """Cache for processed features."""
        return f"{self.base_path}/cache/modern"


@dataclass
class ModernRiskConfig:
    """Modern risk configuration."""
    base_leverage: float = 5.0
    max_leverage: float = 10.0
    min_leverage: float = 1.0
    asset_max_leverage: float = 50.0
    
    # Dynamic risk adjustment
    dynamic_risk_adjustment: bool = True
    bull_bear_risk_factor: float = 1.2
    chop_risk_factor: float = 0.8
    vol_risk_adjustment: bool = True
    
    # Enhanced position management
    max_positions: int = 5
    max_position_size_pct: float = 0.4
    max_correlated_positions: int = 3
    
    # Risk metrics
    max_drawdown_pct: float = 0.20
    daily_loss_limit_pct: float = 0.05
    
    # Margin
    margin_mode: str = "cross"
    maintenance_margin_buffer: float = 0.03


@dataclass
class ModernConfig:
    """Complete modern system configuration."""
    # Sub-configurations
    microstructure: ModernMicrostructureConfig = field(default_factory=ModernMicrostructureConfig)
    indicators: ModernIndicatorConfig = field(default_factory=ModernIndicatorConfig)
    regime: ModernRegimeConfig = field(default_factory=ModernRegimeConfig)
    strategies: ModernStrategyConfig = field(default_factory=ModernStrategyConfig)
    tf_v3: ModernTFV3Config = field(default_factory=ModernTFV3Config)
    data_paths: ModernDataConfig = field(default_factory=ModernDataConfig)
    risk: ModernRiskConfig = field(default_factory=ModernRiskConfig)
    
    # System identification
    system_mode: str = "modern"
    
    # Modern features
    use_websocket: bool = False
    use_cache: bool = True
    cache_ttl_seconds: int = 3600
    
    # Monitoring
    enable_metrics: bool = True
    metrics_port: int = 9090
    
    # Other configs
    timezone: str = "UTC"
    log_level: str = "INFO"
    backtest_start: str = "2024-01-01"
    backtest_end: str = "2024-12-31"
    
    @classmethod
    def load_experimental(cls) -> "ModernConfig":
        """Load experimental modern configuration."""
        return cls()
    
    def validate(self) -> bool:
        """Validate modern configuration consistency."""
        checks = [
            # Detector
            self.regime.detector_type == "continuous_gms",
            
            # Strategy
            self.strategies.use_tf_v2 == False,
            self.strategies.use_tf_v3 == True,
            
            # Risk (2% for modern)
            self.tf_v3.risk_frac == 0.02,
            
            # Modern features enabled
            self.strategies.tf_use_obi_filter == True,
        ]
        
        return all(checks)
    
    def apply_overrides(self, overrides: Dict[str, Any]) -> None:
        """Apply configuration overrides for experimentation."""
        # This allows modern system to be easily configured
        # without affecting the frozen legacy config
        for key, value in overrides.items():
            if hasattr(self, key):
                setattr(self, key, value)