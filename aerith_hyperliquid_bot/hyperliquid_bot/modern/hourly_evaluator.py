"""
Hourly Strategy Evaluator
=========================

This module evaluates trading opportunities on an hourly basis using:
- Current hourly bar data
- Last 60 regime states (1 per minute)
- Market microstructure information

The evaluator ensures temporal separation between regime detection (60s)
and trading decisions (hourly) to prevent overfitting.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Any, List
import pandas as pd
import numpy as np

from ..config.settings import Config
from .regime_state_manager import RegimeStateManager
from ..core.interfaces import IStrategy
from .signal_engine import ModernSignalEngine


class HourlyStrategyEvaluator:
    """
    Evaluates trading opportunities hourly using regime history.
    
    This class orchestrates the interaction between:
    - Regime state history (60-second updates)
    - Strategy evaluation (hourly)
    - Risk management
    
    It supports both backtesting and live trading modes.
    """
    
    def __init__(self, 
                 config: Config,
                 regime_manager: RegimeStateManager,
                 strategy: IStrategy,
                 mode: str = "backtest"):
        """
        Initialize hourly evaluator.
        
        Args:
            config: System configuration
            regime_manager: Regime state manager instance
            strategy: Trading strategy instance
            mode: Operating mode ("backtest" or "live")
        """
        self.config = config
        self.regime_manager = regime_manager
        self.strategy = strategy
        self.mode = mode
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize signal engine for indicator calculations
        self.signal_engine = ModernSignalEngine(config)
        
        # Track last evaluation time
        self.last_evaluation_time = None
        
        # Evaluation settings
        self.evaluation_interval = timedelta(hours=1)
        self.regime_lookback_hours = 1  # Look at last hour of regime states
        
        # Risk parameters from config
        self.risk_frac = config.portfolio.risk_per_trade  # e.g., 0.02 for 2%
        self.max_leverage = config.portfolio.max_leverage
        
        # TODO: Live trading placeholders
        if mode == "live":
            # Future: Set up hourly timer
            # Future: Connect to order management system
            # Future: Initialize position tracking
            pass
        
        self.logger.info(
            f"Hourly Strategy Evaluator initialized:\n"
            f"  - Mode: {mode}\n"
            f"  - Strategy: {strategy.get_strategy_name()}\n"
            f"  - Risk fraction: {self.risk_frac}\n"
            f"  - Max leverage: {self.max_leverage}"
        )
    
    def should_evaluate(self, timestamp: datetime) -> bool:
        """
        Check if it's time to evaluate strategy.
        
        In backtest mode: Check if hour boundary crossed
        In live mode: Would check system clock
        
        Args:
            timestamp: Current timestamp
            
        Returns:
            True if should evaluate, False otherwise
        """
        if self.last_evaluation_time is None:
            # First evaluation
            return True
        
        # Check if we've crossed an hour boundary
        time_since_last = timestamp - self.last_evaluation_time
        
        if self.mode == "backtest":
            # In backtest, strictly enforce hourly boundaries
            current_hour = timestamp.replace(minute=0, second=0, microsecond=0)
            last_hour = self.last_evaluation_time.replace(minute=0, second=0, microsecond=0)
            return current_hour > last_hour
        else:
            # In live mode, allow some flexibility (e.g., 59-61 minutes)
            return time_since_last >= timedelta(minutes=59)
    
    def evaluate(self, 
                hourly_bar: Dict[str, Any],
                current_signals: Dict[str, Any],
                timestamp: datetime,
                ohlcv_history: Optional[pd.DataFrame] = None) -> Optional[Dict[str, Any]]:
        """
        Evaluate trading opportunity using hourly bar and regime history.
        
        Args:
            hourly_bar: OHLCV data for the hour
            current_signals: Current market signals (microstructure, etc.)
            timestamp: Current timestamp
            ohlcv_history: Historical OHLCV data for indicator calculation
            
        Returns:
            Trading decision dict or None if no trade
        """
        if not self.should_evaluate(timestamp):
            return None
        
        self.last_evaluation_time = timestamp
        
        try:
            # Get regime history for the past hour
            regime_features = self.regime_manager.get_regime_features_for_strategy(
                timestamp=timestamp,
                lookback_hours=self.regime_lookback_hours
            )
            
            if not regime_features:
                self.logger.warning("No regime features available, skipping evaluation")
                return None
            
            # Get current regime state
            current_regime = regime_features.get('current_state', 'UNKNOWN')
            
            # Check if regime allows trading
            allowed_states = self.strategy.get_allowed_states('trend_following')
            if current_regime not in allowed_states:
                self.logger.debug(f"Regime {current_regime} not in allowed states, skipping")
                return None
            
            # Prepare signals for strategy with OHLCV history
            strategy_signals = self._prepare_strategy_signals(
                hourly_bar, current_signals, regime_features, ohlcv_history
            )
            
            # Check if we have an enhanced detector with quality scoring
            if hasattr(self.regime_manager, 'detector') and hasattr(self.regime_manager.detector, 'evaluate_with_quality'):
                # Use enhanced detector's quality evaluation
                quality_eval = self.regime_manager.detector.evaluate_with_quality(
                    signals=strategy_signals,
                    timestamp=timestamp
                )
                
                # Check if quality meets threshold
                if not quality_eval.get('execute', True):
                    self.logger.info(
                        f"Quality filter blocked trade: quality={quality_eval['quality']:.3f}, "
                        f"details={quality_eval.get('quality_details', {})}"
                    )
                    return None
                
                # Add quality info to strategy signals
                strategy_signals['quality_score'] = quality_eval['quality']
                strategy_signals['quality_details'] = quality_eval.get('quality_details', {})
            
            # Evaluate entry opportunity
            entry_decision = self.strategy.evaluate_entry(
                signals=strategy_signals,
                regime=current_regime
            )
            
            if entry_decision is None:
                return None
            
            # Add risk management
            entry_decision = self._apply_risk_management(
                entry_decision, strategy_signals
            )
            
            # Add metadata
            entry_decision['timestamp'] = timestamp
            entry_decision['regime'] = current_regime
            entry_decision['regime_confidence'] = regime_features.get('current_confidence', 0.0)
            entry_decision['evaluation_mode'] = 'hourly'
            
            # Add quality score if available
            if 'quality_score' in strategy_signals:
                entry_decision['quality_score'] = strategy_signals['quality_score']
                entry_decision['quality_details'] = strategy_signals['quality_details']
            
            self.logger.info(
                f"Entry signal generated: {entry_decision['direction']} "
                f"(confidence: {entry_decision['confidence']:.2f}, "
                f"size: {entry_decision['position_size']:.4f}"
                + (f", quality: {entry_decision.get('quality_score', 'N/A')}" if 'quality_score' in entry_decision else "") +
                ")"
            )
            
            return entry_decision
            
        except Exception as e:
            self.logger.error(f"Error during evaluation: {e}", exc_info=True)
            return None
    
    def _prepare_strategy_signals(self,
                                 hourly_bar: Dict[str, Any],
                                 current_signals: Dict[str, Any],
                                 regime_features: Dict[str, Any],
                                 ohlcv_history: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """
        Prepare comprehensive signals for strategy evaluation using ModernSignalEngine.
        
        This method:
        1. Prepares OHLCV data with history for indicator calculation
        2. Uses ModernSignalEngine to calculate all technical indicators
        3. Adds microstructure signals and regime information
        4. Validates all required signals are present
        
        Args:
            hourly_bar: Current hourly OHLCV data
            current_signals: Current market signals (microstructure, etc.)
            regime_features: Regime state features from RegimeStateManager
            ohlcv_history: Optional historical OHLCV data for indicators
            
        Returns:
            Complete signals dictionary for strategy evaluation
        """
        # Log what we're preparing
        self.logger.debug(
            f"Preparing signals for timestamp: {hourly_bar.get('timestamp')}, "
            f"regime: {regime_features.get('current_state')}"
        )
        
        # Prepare OHLCV DataFrame for signal engine
        if ohlcv_history is not None and not ohlcv_history.empty:
            # Use provided history
            ohlcv_df = ohlcv_history.copy()
        else:
            # Create minimal DataFrame from current bar
            # In production, we should have proper history
            self.logger.warning("No OHLCV history provided, using current bar only")
            ohlcv_df = pd.DataFrame([hourly_bar])
            if 'timestamp' in ohlcv_df.columns:
                ohlcv_df.set_index('timestamp', inplace=True)
        
        # Calculate all technical indicators using signal engine
        signals = self.signal_engine.calculate_signals(ohlcv_df, regime_features)
        
        # Override with current bar data (ensures we use latest values)
        signals.update({
            'timestamp': hourly_bar.get('timestamp'),
            'open': hourly_bar.get('open'),
            'high': hourly_bar.get('high'),
            'low': hourly_bar.get('low'),
            'close': hourly_bar.get('close'),
            'volume': hourly_bar.get('volume', 0),
        })
        
        # Add current microstructure signals
        if current_signals:
            # Only add non-conflicting signals
            for key, value in current_signals.items():
                if key not in signals:
                    signals[key] = value
        
        # CRITICAL FIX: Map ATR from pre-computed features_1s fields
        if 'atr_14_sec' in hourly_bar:
            signals['atr_14'] = hourly_bar.get('atr_14_sec')
            signals['atr_percent'] = hourly_bar.get('atr_percent_sec', 0.0)
        
        # CRITICAL FIX: Map OBI for legacy detector
        if 'volume_imbalance' in hourly_bar:
            signals['obi_smoothed_5'] = hourly_bar.get('volume_imbalance')
            signals['obi_smoothed'] = hourly_bar.get('volume_imbalance')
        
        # Map additional fields from enhanced hourly data
        if 'ma_slope' in hourly_bar:
            signals['ma_slope'] = hourly_bar.get('ma_slope')
        if 'spread_mean' in hourly_bar:
            signals['spread_mean'] = hourly_bar.get('spread_mean')
        if 'spread_std' in hourly_bar:
            signals['spread_std'] = hourly_bar.get('spread_std')
        
        # Ensure regime information is properly set
        signals['regime'] = regime_features.get('current_state')  # For compatibility
        signals['regime_timestamp'] = regime_features.get('timestamp')
        signals['risk_suppressed'] = regime_features.get('risk_suppressed', False)
        
        # CRITICAL FIX: Pass full regime_features dict to strategy
        signals['regime_features'] = regime_features
        
        # Validate signals
        if not self.signal_engine.validate_signals(signals):
            self.logger.warning("Signal validation failed, some indicators may be missing")
        
        # Log signal summary for debugging
        close_val = signals.get('close', 0)
        volume_val = signals.get('volume', 0) 
        ema_fast_val = signals.get('ema_fast')
        ema_slow_val = signals.get('ema_slow')
        atr_val = signals.get('atr_14')
        
        # Format values for logging
        close_str = f"{close_val:.2f}" if not pd.isna(close_val) else "NaN"
        volume_str = f"{volume_val:.0f}" if not pd.isna(volume_val) else "NaN"
        ema_fast_str = f"{ema_fast_val:.2f}" if not pd.isna(ema_fast_val) else "NaN"
        ema_slow_str = f"{ema_slow_val:.2f}" if not pd.isna(ema_slow_val) else "NaN"
        atr_str = f"{atr_val:.4f}" if not pd.isna(atr_val) else "NaN"
        
        self.logger.info(
            f"Signals prepared: close={close_str}, "
            f"volume={volume_str}, "
            f"ema_fast={ema_fast_str}, "
            f"ema_slow={ema_slow_str}, "
            f"atr_14={atr_str}, "
            f"regime={signals.get('regime')}, "
            f"risk_suppressed={signals.get('risk_suppressed')}"
        )
        
        return signals
    
    def _apply_risk_management(self,
                              entry_decision: Dict[str, Any],
                              signals: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply risk management to entry decision.
        
        Ensures:
        - Position size respects risk_frac (0.25)
        - Leverage doesn't exceed max_leverage
        - Stop loss is properly set
        """
        # Get ATR for volatility-based sizing
        atr = signals.get('atr_14', 1.0)
        close_price = signals.get('close', 100.0)
        
        # Base position size from strategy
        base_size = entry_decision.get('position_size', 0.02)
        
        # Apply risk fraction scaling
        # risk_frac = risk_per_trade from config (e.g., 0.02 = 2%)
        risk_adjusted_size = min(base_size, self.risk_frac)
        
        # Volatility adjustment
        atr_percent = atr / close_price
        original_size = risk_adjusted_size
        if atr_percent > 0.02:  # High volatility
            risk_adjusted_size *= 0.8
            self.logger.debug(f"High volatility adjustment: {original_size:.4f} -> {risk_adjusted_size:.4f}")
        elif atr_percent < 0.01:  # Low volatility
            risk_adjusted_size *= 1.2
            self.logger.debug(f"Low volatility adjustment: {original_size:.4f} -> {risk_adjusted_size:.4f}")
        
        # Ensure we never exceed risk_per_trade after adjustments
        before_cap = risk_adjusted_size
        risk_adjusted_size = min(risk_adjusted_size, self.risk_frac)
        if before_cap != risk_adjusted_size:
            self.logger.debug(f"Capped at risk_frac: {before_cap:.4f} -> {risk_adjusted_size:.4f}")
        
        # Apply leverage limits
        implied_leverage = risk_adjusted_size * 10  # Assuming 10x available
        if implied_leverage > self.max_leverage:
            risk_adjusted_size = self.max_leverage / 10
        
        # Update entry decision
        entry_decision['position_size'] = risk_adjusted_size
        entry_decision['risk_frac_used'] = risk_adjusted_size
        entry_decision['implied_leverage'] = risk_adjusted_size * 10
        
        # Set stop loss based on ATR
        direction = entry_decision['direction']
        stop_distance = atr * 2.0  # 2 ATR stop
        
        if direction == 'long':
            entry_decision['stop_loss'] = close_price - stop_distance
        else:
            entry_decision['stop_loss'] = close_price + stop_distance
        
        return entry_decision
    
    def check_exit(self,
                  position: Dict[str, Any],
                  current_signals: Dict[str, Any],
                  timestamp: datetime) -> Optional[str]:
        """
        Check if position should be exited.
        
        This can be called more frequently than hourly evaluation
        for risk management purposes.
        
        Args:
            position: Current position information
            current_signals: Current market signals
            timestamp: Current timestamp
            
        Returns:
            Exit reason string or None
        """
        # Get current regime
        current_state = self.regime_manager.get_current_state()
        if current_state is None:
            return "no_regime_data"
        
        # Prepare signals
        signals = current_signals.copy()
        signals['regime_state'] = current_state.state
        signals['regime_confidence'] = current_state.confidence
        
        # Let strategy check exit
        return self.strategy.check_exit(signals, position)
    
    async def evaluate_live(self):
        """
        Live trading evaluation (placeholder for future implementation).
        
        This would:
        - Run on a timer (every hour)
        - Fetch real-time market data
        - Generate and submit orders
        - Monitor positions
        """
        # TODO: Implement live trading logic
        # - Connect to exchange API
        # - Fetch current hourly bar
        # - Get real-time signals
        # - Submit orders if conditions met
        # - Update position tracking
        pass
    
    def get_diagnostics(self) -> Dict[str, Any]:
        """Get evaluator diagnostics."""
        current_state = self.regime_manager.get_current_state()
        
        return {
            'mode': self.mode,
            'last_evaluation': self.last_evaluation_time,
            'current_regime': current_state.state if current_state else None,
            'regime_confidence': current_state.confidence if current_state else 0.0,
            'risk_frac': self.risk_frac,
            'max_leverage': self.max_leverage,
            'strategy': self.strategy.get_strategy_name()
        }