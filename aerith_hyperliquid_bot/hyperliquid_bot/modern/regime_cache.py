"""
Regime Cache Manager
====================

Manages pre-computed regime states for efficient backtesting.
Loads regime states from parquet files and provides fast lookups.

This is part of the performance optimization that separates
expensive regime computation from strategy backtesting.
"""

import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, Any
import pandas as pd
import numpy as np


class RegimeCache:
    """
    Cache manager for pre-computed regime states.
    
    This class loads pre-computed regime states from parquet files
    and provides O(1) lookups during backtesting.
    """
    
    def __init__(self, cache_dir: str = 'data/precomputed_regimes'):
        """
        Initialize regime cache.
        
        Args:
            cache_dir: Directory containing pre-computed regime parquet files
        """
        self.cache_dir = Path(cache_dir)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.regime_data: Dict[int, pd.DataFrame] = {}  # Year -> DataFrame
        
        if not self.cache_dir.exists():
            self.logger.warning(f"Cache directory not found: {self.cache_dir}")
            self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"RegimeCache initialized with directory: {self.cache_dir}")
    
    def load_year(self, year: int) -> bool:
        """
        Load pre-computed regimes for a specific year.
        
        Args:
            year: Year to load
            
        Returns:
            True if loaded successfully, False otherwise
        """
        if year in self.regime_data:
            self.logger.debug(f"Year {year} already loaded")
            return True
        
        regime_file = self.cache_dir / f"regimes_{year}.parquet"
        
        if not regime_file.exists():
            self.logger.error(f"Regime file not found: {regime_file}")
            self.logger.info("Run 'python scripts/precompute_regimes.py' first!")
            return False
        
        try:
            # Load parquet file
            df = pd.read_parquet(regime_file)
            
            # Ensure timestamp index for fast lookups
            if 'timestamp' in df.columns:
                df.set_index('timestamp', inplace=True)
            
            self.regime_data[year] = df
            
            self.logger.info(
                f"Loaded {len(df)} regime states for year {year} "
                f"({regime_file.stat().st_size / 1024:.1f} KB)"
            )
            
            # Log summary
            regime_counts = df['regime'].value_counts()
            self.logger.debug(f"Regime distribution: {dict(regime_counts)}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load regime file: {e}")
            return False
    
    def get_regime_at_time(self, timestamp: datetime) -> Optional[Dict[str, Any]]:
        """
        Get regime state at a specific timestamp.
        
        Args:
            timestamp: Timestamp to query
            
        Returns:
            Regime state dict or None if not found
        """
        year = timestamp.year
        
        # Load year if not already loaded
        if year not in self.regime_data:
            if not self.load_year(year):
                return None
        
        df = self.regime_data[year]
        
        # Find exact timestamp first
        if timestamp in df.index:
            row = df.loc[timestamp]
            return self._row_to_dict(row, timestamp)
        
        # Find most recent regime before timestamp
        mask = df.index <= timestamp
        if not mask.any():
            self.logger.warning(f"No regime data before {timestamp}")
            return None
        
        # Get the most recent regime
        idx = df.index[mask][-1]
        row = df.loc[idx]
        
        self.logger.debug(
            f"Using regime from {idx} for query time {timestamp} "
            f"(lag: {(timestamp - idx).total_seconds():.0f}s)"
        )
        
        return self._row_to_dict(row, timestamp)
    
    def _row_to_dict(self, row: pd.Series, query_time: datetime) -> Dict[str, Any]:
        """Convert DataFrame row to regime state dict."""
        return {
            'timestamp': query_time,  # Use query time, not regime time
            'regime': row['regime'],
            'confidence': row.get('confidence', 0.0),
            'volatility': row.get('volatility', 0.0),
            'momentum': row.get('momentum', 0.0),
            'volume_imbalance': row.get('volume_imbalance', 0.0),
            'spread_volatility': row.get('spread_volatility', 0.0),
            'regime_duration_hours': row.get('regime_duration_hours', 0),
            'signal_quality': row.get('signal_quality', 0.0),
            'risk_suppressed': row.get('risk_suppressed', False)
        }
    
    def get_regime_history(self, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        Get regime history for a time range.
        
        Args:
            start_time: Start of range (inclusive)
            end_time: End of range (exclusive)
            
        Returns:
            DataFrame with regime states in the range
        """
        # Collect data from all relevant years
        years = range(start_time.year, end_time.year + 1)
        
        dfs = []
        for year in years:
            if year not in self.regime_data:
                if not self.load_year(year):
                    continue
            
            df = self.regime_data[year]
            mask = (df.index >= start_time) & (df.index < end_time)
            if mask.any():
                dfs.append(df[mask])
        
        if not dfs:
            self.logger.warning(f"No regime data found for {start_time} to {end_time}")
            return pd.DataFrame()
        
        return pd.concat(dfs)
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about loaded cache."""
        info = {
            'cache_dir': str(self.cache_dir),
            'loaded_years': list(self.regime_data.keys()),
            'total_states': sum(len(df) for df in self.regime_data.values()),
            'memory_usage_mb': sum(
                df.memory_usage(deep=True).sum() / 1024 / 1024 
                for df in self.regime_data.values()
            )
        }
        
        # Add regime distribution
        all_regimes = []
        for df in self.regime_data.values():
            all_regimes.extend(df['regime'].tolist())
        
        if all_regimes:
            info['regime_distribution'] = pd.Series(all_regimes).value_counts().to_dict()
        
        return info
    
    def validate_cache(self, start_date: datetime, end_date: datetime) -> bool:
        """
        Validate that cache covers the requested date range.
        
        Args:
            start_date: Start of backtest period
            end_date: End of backtest period
            
        Returns:
            True if cache is complete, False otherwise
        """
        # Check all required years
        years_needed = range(start_date.year, end_date.year + 1)
        
        missing_years = []
        for year in years_needed:
            regime_file = self.cache_dir / f"regimes_{year}.parquet"
            if not regime_file.exists():
                missing_years.append(year)
        
        if missing_years:
            self.logger.error(
                f"Missing pre-computed regimes for years: {missing_years}\n"
                f"Run: python scripts/precompute_regimes.py --start {min(missing_years)}-01-01 "
                f"--end {max(missing_years)+1}-01-01"
            )
            return False
        
        return True