"""
Modern Trading System Components
================================

This package contains the modern implementations of the trading system:
- Continuous GMS detector with adaptive thresholds
- Enhanced TF-v3 strategy with OBI/funding filters  
- 1-second feature data loader

These components represent the enhanced, experimental system.
"""

from .detector import ModernContinuousGMSDetector
from .strategy import ModernTFV3Strategy
from .data_loader import RobustDataLoader
from .data_aggregator import ModernDataAggregator
from .regime_state_manager import RegimeStateManager
from .continuous_detector import ModernContinuousDetector
from .continuous_detector_v2 import ModernContinuousDetectorV2
from .hourly_evaluator import HourlyStrategyEvaluator
from .tf_v3_modern import ModernTFV3Strategy as ModernTFV3StrategyNew
from .backtest_engine import RobustBacktestEngine
from .execution_refiner import ExecutionRefiner
from .enhanced_regime_detector import EnhancedRegimeDetector

__all__ = [
    'ModernContinuousGMSDetector',
    'ModernTFV3Strategy', 
    'RobustDataLoader',
    'ModernDataAggregator',
    'RegimeStateManager',
    'ModernContinuousDetector',
    'ModernContinuousDetectorV2',
    'HourlyStrategyEvaluator',
    'ModernTFV3StrategyNew',
    'RobustBacktestEngine',
    'ExecutionRefiner',
    'EnhancedRegimeDetector',
]