"""
Robust Backtesting Engine for Modern System
===========================================

Key improvements over the original modern backtester:
1. Adaptive warmup based on available data
2. Graceful handling of missing regime data
3. Better position management (addresses the root cause issue)
4. Comprehensive error handling without crashes

This engine ensures the backtest always runs to completion.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np
from pathlib import Path

from ..config.settings import Config
from ..portfolio.portfolio import Portfolio
from ..features.indicators import calculate_indicators_for_tf_v3
from .data_aggregator import ModernDataAggregator
from .data_loader import RobustDataLoader
from .regime_state_manager import RegimeStateManager
from .hourly_evaluator import HourlyStrategyEvaluator
from .registry import get_modern_detector, get_modern_strategy
from .execution_refiner import ExecutionRefiner
from .regime_cache import RegimeCache


class RobustBacktestEngine:
    """
    Production-ready backtesting engine with comprehensive error handling.
    
    Key features:
    - Adaptive warmup period based on data availability
    - Graceful degradation when data is missing
    - Proper position management (only one position at a time)
    - Never crashes - always completes the backtest
    """
    
    def __init__(self, config: Config,
                 start_date: datetime,
                 end_date: datetime,
                 data_dir: Optional[Path] = None,
                 use_regime_cache: bool = True,
                 strict: bool = True):
        """Initialize robust backtesting engine.
        
        Args:
            config: Configuration object
            start_date: Backtest start date
            end_date: Backtest end date
            data_dir: Optional data directory override
            use_regime_cache: Whether to use pre-computed regimes
            strict: If True, raises exceptions on fallbacks instead of continuing
        """
        self.config = config
        self.start_date = start_date
        self.end_date = end_date
        self.data_dir = data_dir or Path(config.data_paths.l2_data_root)
        self.mode = 'backtest'
        self.use_regime_cache = use_regime_cache
        self.strict = strict
        
        # Initialize logger
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize components with robust data loader
        self.data_aggregator = ModernDataAggregator()
        self.data_loader = RobustDataLoader(config)  # Use our new robust loader
        
        # Initialize regime management
        self.regime_manager = RegimeStateManager(mode='backtest')
        
        # Always create detector for fallback regime detection
        detector_type = config.regime.detector_type
        if detector_type == "enhanced":
            from .enhanced_regime_detector import EnhancedRegimeDetector
            self.detector = EnhancedRegimeDetector(config=config)
        else:
            self.detector = get_modern_detector(detector_type, config=config)
        
        # Check if detector was created successfully
        if self.detector is None:
            error_msg = f"Failed to create detector '{detector_type}' - not registered in modern system"
            if self.strict:
                raise ValueError(error_msg)
            else:
                self.logger.critical(error_msg)
                self.logger.warning("System will use price-based fallback detection only!")
        
        # Pass detector to regime manager for quality scoring
        self.regime_manager.detector = self.detector
        
        if use_regime_cache:
            self.regime_cache = RegimeCache()
            # Check cache availability but don't fail if missing
            self.cache_available = self.regime_cache.validate_cache(start_date, end_date)
            if not self.cache_available:
                self.logger.warning(
                    "Pre-computed regimes not available. Will calculate on-the-fly using detector. "
                    "For better performance, run 'python scripts/precompute_regimes.py'"
                )
        else:
            self.regime_cache = None
            self.cache_available = False
        
        # Create strategy
        self.strategy = get_modern_strategy(
            "tf_v3_modern",
            config=config
        )
        
        # Create portfolio with position tracking
        self.portfolio = Portfolio(config)
        
        # Create hourly evaluator
        self.hourly_evaluator = HourlyStrategyEvaluator(
            config=config,
            regime_manager=self.regime_manager,
            strategy=self.strategy,
            mode='backtest'
        )
        
        # Calculate warmup but make it adaptive
        self.requested_warmup_hours = self.hourly_evaluator.signal_engine.calculate_required_lookback()
        self.actual_warmup_hours = self.requested_warmup_hours  # Will be adjusted based on data
        
        # Create execution refiner
        self.execution_refiner = ExecutionRefiner(
            window_minutes=5,
            min_volume_percentile=20.0,
            max_spread_percentile=80.0,
            momentum_weight=0.3
        )
        
        # Position tracking (CRITICAL for fixing multiple positions issue)
        self.current_position = None  # Track active position
        self.trades = []  # All trades (active and closed)
        self.completed_trades = []  # Closed trades with P&L
        self.regime_history = []
        self.performance_metrics = {}
        
        # Track regime source usage
        self.fallback_count = 0  # Price-based fallback
        self.detector_count = 0  # Detector calculated regime
        self.cache_count = 0     # Pre-computed cache
        
        self.logger.info(
            f"Robust Backtesting Engine initialized:\n"
            f"  - Period: {start_date} to {end_date}\n"
            f"  - Requested warmup: {self.requested_warmup_hours} hours\n"
            f"  - Regime cache: {'Available' if self.cache_available else 'Not available'}\n"
            f"  - Strategy: tf_v3_modern\n"
            f"  - Risk per trade: {config.portfolio.risk_per_trade:.2%}"
        )
    
    def run_backtest(self) -> Dict[str, Any]:
        """
        Run the backtest with comprehensive error handling.
        
        GUARANTEE: This method will never crash and always return results.
        """
        self.logger.info("Starting robust backtest...")
        
        try:
            # Load data with our robust loader INCLUDING WARMUP PERIOD
            self.logger.info("Loading data with robust data loader...")
            # CRITICAL: Load extra data for warmup period
            warmup_start = self.start_date - timedelta(hours=self.requested_warmup_hours)
            self.logger.info(f"Loading data from {warmup_start} to {self.end_date} (includes {self.requested_warmup_hours}h warmup)")
            self.data = self.data_loader.load_data(warmup_start, self.end_date)
            
            if self.data.empty:
                # This should never happen with robust loader, but just in case
                self.logger.error("No data loaded - returning empty results")
                return self._create_empty_results()
            
            # Validate and adjust warmup based on available data
            self._adjust_warmup_period(self.data)
            
            # Generate hourly timestamps for processing
            self.hour_timestamps = self._generate_hourly_timestamps()
            hourly_timestamps = self.hour_timestamps
            self.logger.info(f"Processing {len(hourly_timestamps)} hours")
            
            # Track timing
            import time
            start_time = time.time()
            hours_processed = 0
            
            # Process each hour
            for hour_idx, hour_start in enumerate(hourly_timestamps):
                try:
                    # Update portfolio metrics
                    self._update_portfolio_tracking(hour_start)
                    
                    # Always update regime state (even during warmup)
                    self._update_regime_state(hour_start)
                    
                    # Skip trading evaluation if still in warmup
                    if hour_idx < self.actual_warmup_hours:
                        continue
                    
                    # Get hourly data
                    hour_data = self._get_hour_data(self.data, hour_start)
                    if hour_data is None:
                        continue
                    
                    # Check for exit conditions first (if we have a position)
                    if self.current_position is not None:
                        should_exit = self._check_exit_conditions(hour_data)
                        if should_exit:
                            self._close_position(hour_data)
                    
                    # Only check for entry if no current position
                    if self.current_position is None:
                        # Evaluate strategy
                        # Convert Series to dict for hourly_bar
                        hourly_bar = hour_data.to_dict() if hasattr(hour_data, 'to_dict') else hour_data
                        
                        # Get current signals (microstructure features)
                        current_signals = {
                            'volume_imbalance': hour_data.get('volume_imbalance', 0),
                            'trade_intensity': hour_data.get('trade_intensity', 0),
                            'price_momentum': hour_data.get('price_momentum', 0)
                        }
                        
                        signal = self.hourly_evaluator.evaluate(
                            hourly_bar=hourly_bar,
                            current_signals=current_signals,
                            timestamp=hour_start,
                            ohlcv_history=self.data  # Pass full data for indicator calculation
                        )
                        
                        # Log first few hours after warmup for debugging
                        if hour_idx >= self.actual_warmup_hours and hour_idx < self.actual_warmup_hours + 3:
                            current_regime = self.regime_manager.get_current_state()
                            regime_state = current_regime.state if current_regime else "None"
                            self.logger.info(
                                f"Hour {hour_idx} ({hour_start}): regime={regime_state}, signal={signal}"
                            )
                        
                        if signal and (signal.get('action') in ['long', 'short'] or signal.get('direction') in ['long', 'short']):
                            # Normalize to use 'action' key
                            if 'direction' in signal and 'action' not in signal:
                                signal['action'] = signal['direction']
                            self._open_position(signal, hour_data)
                    
                    hours_processed += 1
                    
                    # Progress update every 1000 hours
                    if hours_processed % 1000 == 0:
                        elapsed = time.time() - start_time
                        rate = hours_processed / elapsed
                        self.logger.info(
                            f"Progress: {hours_processed}/{len(hourly_timestamps)} hours "
                            f"({rate:.0f} hours/sec)"
                        )
                        
                except Exception as e:
                    self.logger.error(f"Error processing hour {hour_start}: {e}")
                    # Continue processing - don't let one error stop the backtest
                    continue
            
            # Close any remaining position at end
            if self.current_position is not None:
                last_data = self.data.iloc[-1]
                self._close_position(last_data, reason="Backtest end")
            
            # Calculate final metrics
            results = self._calculate_results()
            
            # Log summary
            total_time = time.time() - start_time
            total_regime_updates = self.cache_count + self.detector_count + self.fallback_count
            
            self.logger.info(
                f"Backtest complete in {total_time:.1f}s\n"
                f"  - Hours processed: {hours_processed}\n"
                f"  - Total trades: {len(self.completed_trades)}\n"
                f"  - Final P&L: {results.get('total_return', 0):.2%}\n"
                f"\n"
                f"REGIME SOURCE BREAKDOWN:\n"
                f"  - Pre-computed cache: {self.cache_count} ({self.cache_count/total_regime_updates*100:.1f}%)\n"
                f"  - Detector calculated: {self.detector_count} ({self.detector_count/total_regime_updates*100:.1f}%)\n"
                f"  - Price-based fallback: {self.fallback_count} ({self.fallback_count/total_regime_updates*100:.1f}%)\n"
                f"  - Total regime updates: {total_regime_updates}"
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Critical error in backtest: {e}", exc_info=True)
            # Return partial results instead of crashing
            return self._create_error_results(str(e))
    
    def _adjust_warmup_period(self, data: pd.DataFrame):
        """Validate warmup period based on loaded data."""
        if data.empty:
            self.actual_warmup_hours = 0
            return
        
        # Check how much data we actually have before start date
        data_start = data.index[0]
        data_end = data.index[-1]
        hours_available = int((self.start_date - data_start).total_seconds() / 3600)
        
        if hours_available < self.requested_warmup_hours:
            self.logger.warning(
                f"Only {hours_available} hours available for warmup "
                f"(requested {self.requested_warmup_hours}). Data starts at {data_start}"
            )
            # Use what we have, minimum 10 hours
            self.actual_warmup_hours = max(hours_available, 10)
        else:
            self.actual_warmup_hours = self.requested_warmup_hours
        
        self.logger.info(
            f"Using warmup period: {self.actual_warmup_hours} hours\n"
            f"  Data range: {data_start} to {data_end}\n"
            f"  Backtest start: {self.start_date}"
        )
    
    def _update_regime_state(self, timestamp: datetime):
        """Update regime state - simple and direct."""
        try:
            # Optional: Try cache if available for speed
            if self.use_regime_cache and self.cache_available and self.regime_cache:
                regime_state = self.regime_cache.get_regime_at_time(timestamp)
                if regime_state:
                    self.regime_manager.update_state(
                        timestamp=timestamp,
                        state=regime_state['regime'],
                        confidence=regime_state['confidence'],
                        features=regime_state.get('features', {})
                    )
                    self.cache_count += 1
                    return
            
            # Otherwise: Calculate regime directly (always works)
            hour_idx = self.hour_timestamps.index(timestamp) if timestamp in self.hour_timestamps else -1
            if hour_idx >= 0 and hour_idx < len(self.data):
                hour_data = self.data.iloc[hour_idx]
                
                # Prepare signals for detector - use actual fields from enhanced hourly data
                detection_signals = {
                    'atr_percent': hour_data.get('atr_percent_sec', 0),
                    'ma_slope': hour_data.get('ma_slope', 0),
                    'obi_smoothed_5': hour_data.get('volume_imbalance', 0),
                    'spread_mean': hour_data.get('spread_mean', 0),
                    'spread_std': hour_data.get('spread_std', 0),
                    'volume': hour_data.get('volume', 0),
                    # Add additional fields the detector might use
                    'close': hour_data.get('close', 0),
                    'atr_percent_sec': hour_data.get('atr_percent_sec', 0),
                    'volume_imbalance': hour_data.get('volume_imbalance', 0)
                }
                
                # Detect regime
                if self.detector is None:
                    if self.strict:
                        raise RuntimeError(f"No detector available for regime detection at {timestamp}")
                    regime = 'Unknown'
                    confidence = 0.0
                else:
                    regime = self.detector.detect_regime(detection_signals, timestamp)
                    confidence = self.detector.get_confidence()
                
                # If detector returns Unknown or low confidence, use simple price-based fallback
                min_confidence = getattr(self.config.regime, 'min_confidence_threshold', 0.4)
                if regime == 'Unknown' or confidence < min_confidence:
                    if self.strict:
                        raise RuntimeError(
                            f"Detector failed to provide valid regime at {timestamp}. "
                            f"Got regime='{regime}' with confidence={confidence:.2f} "
                            f"(min required: {min_confidence})"
                        )
                    self.logger.critical(
                        f"Using price-based fallback at {timestamp} - "
                        f"detector returned '{regime}' with confidence {confidence:.2f}"
                    )
                    regime, confidence = self._get_price_based_regime(hour_data, timestamp)
                    self.fallback_count += 1
                else:
                    self.detector_count += 1
                
                # Update state
                self.regime_manager.update_state(
                    timestamp=timestamp,
                    state=regime,
                    confidence=confidence,
                    features=detection_signals
                )
            
        except Exception as e:
            if self.strict:
                # Re-raise the original exception in strict mode
                raise
            self.logger.critical(f"Error updating regime state: {e}")
            # Simple fallback on error - use price-based regime
            try:
                hour_idx = self.hour_timestamps.index(timestamp) if timestamp in self.hour_timestamps else -1
                if hour_idx >= 0 and hour_idx < len(self.data):
                    hour_data = self.data.iloc[hour_idx]
                    regime, confidence = self._get_price_based_regime(hour_data, timestamp)
                    self.regime_manager.update_state(
                        timestamp=timestamp,
                        state=regime,
                        confidence=confidence,
                        features={}
                    )
                    self.fallback_count += 1
            except:
                # Last resort - neutral regime
                self.regime_manager.update_state(
                    timestamp=timestamp,
                    state='Neutral',
                    confidence=0.5,
                    features={}
                )
    
    def _get_price_based_regime(self, hour_data: pd.Series, timestamp: datetime) -> Tuple[str, float]:
        """
        Simple price-based regime detection as fallback.
        Uses configurable parameters from YAML.
        """
        try:
            # Get configuration values
            fallback_confidence = getattr(self.config.regime, 'fallback_confidence', 0.7)
            price_change_threshold = getattr(self.config.regime, 'price_change_threshold', 0.001)
            neutral_factor = getattr(self.config.regime, 'neutral_confidence_factor', 0.8)
            
            # Calculate price change over last 10 hours
            lookback_hours = 10
            hour_idx = self.hour_timestamps.index(timestamp) if timestamp in self.hour_timestamps else -1
            
            if hour_idx >= lookback_hours:
                past_price = self.data.iloc[hour_idx - lookback_hours]['close']
                current_price = hour_data['close']
                price_change = (current_price - past_price) / past_price
                
                # Determine regime based on price change
                if abs(price_change) < price_change_threshold:
                    # Market is flat/neutral
                    regime = "Neutral"
                    confidence = fallback_confidence * neutral_factor
                elif price_change > price_change_threshold:
                    # Bullish
                    regime = "Weak_Bull_Trend"
                    confidence = fallback_confidence
                else:
                    # Bearish
                    regime = "Weak_Bear_Trend"
                    confidence = fallback_confidence
                
                self.logger.debug(
                    f"Price-based regime: {regime} (change: {price_change:.3%}, conf: {confidence:.2f})"
                )
                return regime, confidence
                
            else:
                # Not enough history - default to neutral
                return "Neutral", fallback_confidence * neutral_factor
                
        except Exception as e:
            self.logger.warning(f"Error in price-based regime: {e}")
            return "Neutral", 0.5
    
    def _check_exit_conditions(self, hour_data: pd.Series) -> bool:
        """
        Check if current position should be closed.
        
        This is a critical missing piece in the original modern system!
        """
        if self.current_position is None:
            return False
        
        current_price = hour_data.get('close', 0)
        if current_price <= 0:
            return False
        
        position = self.current_position
        entry_price = position['entry_price']
        
        # Calculate current P&L
        if position['direction'] == 'long':
            pnl_pct = (current_price - entry_price) / entry_price
        else:
            pnl_pct = (entry_price - current_price) / entry_price
        
        # Exit conditions
        # 1. Stop loss (default to 2% if not configured)
        stop_loss = getattr(self.config.portfolio, 'stop_loss_percent', 0.02)
        if pnl_pct <= -stop_loss:
            self.logger.info(f"Stop loss triggered: {pnl_pct:.2%}")
            return True
        
        # 2. Take profit (default to 4% if not configured)
        take_profit = getattr(self.config.portfolio, 'take_profit_percent', 0.04)
        if pnl_pct >= take_profit:
            self.logger.info(f"Take profit triggered: {pnl_pct:.2%}")
            return True
        
        # 3. Time-based exit (optional)
        hours_held = (hour_data.name - position['entry_time']).total_seconds() / 3600
        max_hold_hours = getattr(self.config.portfolio, 'max_hold_hours', 72)
        if hours_held >= max_hold_hours:
            self.logger.info(f"Time exit triggered: {hours_held:.0f} hours")
            return True
        
        # 4. Regime change exit (optional)
        current_regime = self.regime_manager.get_current_state()
        if current_regime and current_regime != position.get('entry_regime'):
            # Only exit if regime becomes unfavorable
            if position['direction'] == 'long' and current_regime == 'Bear':
                self.logger.info("Regime exit triggered: Bull -> Bear")
                return True
            elif position['direction'] == 'short' and current_regime == 'Bull':
                self.logger.info("Regime exit triggered: Bear -> Bull")
                return True
        
        return False
    
    def _open_position(self, signal: Dict[str, Any], hour_data: pd.Series):
        """Open a new position."""
        if self.current_position is not None:
            self.logger.warning("Attempted to open position while one exists - skipping")
            return
        
        # Get direction from signal (handle both 'action' and 'direction' keys)
        direction = signal.get('action') or signal.get('direction')
        if not direction:
            self.logger.error("No direction in signal - skipping")
            return
            
        # Create position
        position = {
            'entry_time': hour_data.name,
            'entry_price': hour_data['close'],
            'direction': direction,
            'size': signal.get('size', 1.0),
            'entry_regime': self.regime_manager.get_current_state().state if self.regime_manager.get_current_state() else 'Unknown',
            'signal': signal,
            'status': 'active'
        }
        
        self.current_position = position
        self.trades.append(position)
        
        self.logger.info(
            f"Opened {position['direction']} position at {position['entry_price']:.2f} "
            f"(regime: {position['entry_regime']})"
        )
    
    def _close_position(self, hour_data: pd.Series, reason: str = "Exit signal"):
        """Close current position and calculate P&L."""
        if self.current_position is None:
            return
        
        position = self.current_position
        exit_price = hour_data.get('close', hour_data.get('close'))
        
        # Calculate P&L
        if position['direction'] == 'long':
            pnl_pct = (exit_price - position['entry_price']) / position['entry_price']
        else:
            pnl_pct = (position['entry_price'] - exit_price) / position['entry_price']
        
        # Account for fees
        total_fees = 2 * self.config.costs.taker_fee
        pnl_pct -= total_fees
        
        # Update position
        position.update({
            'exit_time': hour_data.name if hasattr(hour_data, 'name') else self.end_date,
            'exit_price': exit_price,
            'pnl_pct': pnl_pct,
            'pnl_usd': pnl_pct * position['size'] * position['entry_price'],
            'exit_reason': reason,
            'status': 'closed'
        })
        
        self.completed_trades.append(position)
        self.current_position = None
        
        self.logger.info(
            f"Closed {position['direction']} position at {exit_price:.2f} "
            f"(P&L: {pnl_pct:.2%}, reason: {reason})"
        )
    
    def _update_portfolio_tracking(self, timestamp: datetime):
        """Update portfolio value tracking."""
        # This would integrate with Portfolio class for full tracking
        # For now, we track trades separately
        pass
    
    def _get_hour_data(self, data: pd.DataFrame, timestamp: datetime) -> Optional[pd.Series]:
        """Get data for specific hour with error handling."""
        try:
            if timestamp in data.index:
                return data.loc[timestamp]
            else:
                # Try to find closest data within 1 hour
                mask = (data.index >= timestamp - timedelta(hours=1)) & \
                       (data.index <= timestamp + timedelta(hours=1))
                if mask.any():
                    return data[mask].iloc[0]
            return None
        except Exception as e:
            self.logger.debug(f"Error getting hour data: {e}")
            return None
    
    def _generate_hourly_timestamps(self) -> List[datetime]:
        """Generate list of hourly timestamps for backtest period."""
        timestamps = []
        current = self.start_date
        
        while current < self.end_date:
            timestamps.append(current)
            current += timedelta(hours=1)
        
        return timestamps
    
    def _calculate_results(self) -> Dict[str, Any]:
        """Calculate comprehensive backtest results."""
        if not self.completed_trades:
            return self._create_empty_results()
        
        # Convert to DataFrame for analysis
        trades_df = pd.DataFrame(self.completed_trades)
        
        # Calculate metrics
        total_trades = len(trades_df)
        winning_trades = (trades_df['pnl_pct'] > 0).sum()
        losing_trades = (trades_df['pnl_pct'] < 0).sum()
        
        total_return = (1 + trades_df['pnl_pct']).prod() - 1
        avg_return = trades_df['pnl_pct'].mean()
        
        # Calculate Sharpe ratio (simplified)
        if len(trades_df) > 1:
            returns_std = trades_df['pnl_pct'].std()
            sharpe = (avg_return * np.sqrt(252 * 24)) / returns_std if returns_std > 0 else 0
        else:
            sharpe = 0
        
        # Maximum drawdown
        cumulative_returns = (1 + trades_df['pnl_pct']).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': winning_trades / total_trades if total_trades > 0 else 0,
            'total_return': total_return,
            'average_return': avg_return,
            'sharpe_ratio': sharpe,
            'max_drawdown': max_drawdown,
            'trades': self.completed_trades,
            'data_quality': {
                'warmup_hours_used': self.actual_warmup_hours,
                'warmup_hours_requested': self.requested_warmup_hours,
                'regime_cache_available': self.cache_available
            },
            'regime_sources': {
                'cache_used': self.cache_count,
                'detector_used': self.detector_count,
                'fallback_used': self.fallback_count,
                'total': self.cache_count + self.detector_count + self.fallback_count
            },
            'period': {
                'start': self.start_date.isoformat(),
                'end': self.end_date.isoformat()
            }
        }
    
    def _create_empty_results(self) -> Dict[str, Any]:
        """Create empty results structure."""
        return {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0,
            'total_return': 0,
            'average_return': 0,
            'sharpe_ratio': 0,
            'max_drawdown': 0,
            'trades': [],
            'data_quality': {
                'warmup_hours_used': 0,
                'warmup_hours_requested': self.requested_warmup_hours,
                'regime_cache_available': self.cache_available
            },
            'period': {
                'start': self.start_date.isoformat(),
                'end': self.end_date.isoformat()
            }
        }
    
    def _create_error_results(self, error_msg: str) -> Dict[str, Any]:
        """Create results structure for error case."""
        results = self._create_empty_results()
        results['error'] = error_msg
        results['partial_results'] = True
        results['completed_trades'] = len(self.completed_trades)
        return results