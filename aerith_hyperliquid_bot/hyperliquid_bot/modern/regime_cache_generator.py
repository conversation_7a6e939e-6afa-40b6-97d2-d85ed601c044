#!/usr/bin/env python3
"""
Regime Cache Generator for Modern System
========================================
Generates pre-computed regime states at configurable intervals (default 60s).
This is part of the modern system architecture to enable fast backtesting
with high-frequency regime updates.

Usage:
    python -m hyperliquid_bot.modern.regime_cache_generator --year 2024 --interval 60
    python -m hyperliquid_bot.modern.regime_cache_generator --start 2025-01-01 --end 2025-03-31
"""

import argparse
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
from tqdm import tqdm
import multiprocessing as mp
from functools import partial

from ..config.settings import load_config
from ..core.detector import GranularMicrostructureRegimeDetector
from .data_loader import ModernDataLoader


class RegimeCacheGenerator:
    """
    Generates pre-computed regime states for fast backtesting.
    
    This solves the performance issue of calculating regimes in real-time
    by pre-computing them at the desired interval (e.g., 60 seconds).
    """
    
    def __init__(self, config: Dict[str, Any], interval_seconds: int = 60):
        """
        Initialize the regime cache generator.
        
        Args:
            config: Configuration dictionary
            interval_seconds: Interval between regime calculations (default 60s)
        """
        self.config = config
        self.interval_seconds = interval_seconds
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize components
        self.data_loader = ModernDataLoader(config)
        self.detector = GranularMicrostructureRegimeDetector(config)
        
        # Cache directory
        cache_dir = getattr(config.data_paths, 'cache_dir', 'data/precomputed_regimes')
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"Regime Cache Generator initialized:")
        self.logger.info(f"  - Interval: {interval_seconds} seconds")
        self.logger.info(f"  - Cache directory: {self.cache_dir}")
    
    def generate_for_period(self, 
                           start_date: datetime, 
                           end_date: datetime,
                           symbol: str = 'BTC',
                           parallel: bool = True,
                           num_workers: Optional[int] = None) -> pd.DataFrame:
        """
        Generate regime cache for a specific period.
        
        Args:
            start_date: Start date for generation
            end_date: End date for generation
            symbol: Trading symbol (default 'BTC')
            parallel: Use parallel processing
            num_workers: Number of parallel workers (default: CPU count)
            
        Returns:
            DataFrame with regime states
        """
        self.logger.info(f"Generating regime cache for {symbol}")
        self.logger.info(f"Period: {start_date.date()} to {end_date.date()}")
        
        # Generate list of days to process
        days = []
        current = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        while current <= end:
            days.append(current)
            current += timedelta(days=1)
        
        self.logger.info(f"Processing {len(days)} days...")
        
        if parallel and len(days) > 1:
            # Parallel processing
            num_workers = num_workers or mp.cpu_count()
            self.logger.info(f"Using {num_workers} parallel workers")
            
            with mp.Pool(num_workers) as pool:
                process_func = partial(self._process_single_day, symbol=symbol)
                results = []
                
                # Use tqdm for progress bar
                with tqdm(total=len(days), desc="Processing days") as pbar:
                    for result in pool.imap(process_func, days):
                        results.extend(result)
                        pbar.update(1)
        else:
            # Sequential processing
            results = []
            for day in tqdm(days, desc="Processing days"):
                day_results = self._process_single_day(day, symbol)
                results.extend(day_results)
        
        # Convert to DataFrame
        df = pd.DataFrame(results)
        
        if df.empty:
            self.logger.warning("No regime data generated!")
            return df
        
        # Sort by timestamp and set index
        df.sort_values('timestamp', inplace=True)
        df.set_index('timestamp', inplace=True)
        
        # Log statistics
        self._log_statistics(df)
        
        return df
    
    def _process_single_day(self, date: datetime, symbol: str) -> List[Dict[str, Any]]:
        """
        Process regime calculations for a single day.
        
        Args:
            date: Date to process
            symbol: Trading symbol
            
        Returns:
            List of regime states for the day
        """
        results = []
        
        try:
            # Load full day of 1s data with lookback
            lookback_start = date - timedelta(hours=24)
            day_end = date + timedelta(days=1) - timedelta(seconds=1)
            
            # Load features - use load_features_1s for actual 1-second data
            features_1s = self.data_loader.load_features_1s(
                start_time=lookback_start,
                end_time=day_end
            )
            
            if features_1s is None or features_1s.empty:
                self.logger.warning(f"No data available for {date.date()}")
                return results
            
            # Calculate regimes at specified intervals
            minutes_per_day = 24 * 60
            intervals_per_day = minutes_per_day * 60 // self.interval_seconds
            
            for i in range(intervals_per_day):
                try:
                    # Calculate timestamp for this interval
                    interval_time = date + timedelta(seconds=i * self.interval_seconds)
                    
                    # Skip if beyond available data
                    if interval_time > features_1s.index[-1]:
                        break
                    
                    # Get data up to this point (no look-ahead)
                    data_slice = features_1s[features_1s.index <= interval_time]
                    
                    if len(data_slice) < 100:  # Need minimum data
                        continue
                    
                    # Calculate regime using the correct method
                    # Extract latest features as dict
                    latest_row = data_slice.iloc[-1]
                    features_dict = latest_row.to_dict()
                    
                    # Call get_regime with features dict
                    regime_state = self.detector.get_regime(features_dict)
                    
                    # Get confidence if available
                    confidence = features_dict.get('signal_quality', 0.5)
                    
                    # Store features for additional data
                    features = features_dict
                    
                    # Store result
                    results.append({
                        'timestamp': interval_time,
                        'regime': regime_state,
                        'confidence': confidence,
                        'momentum': features.get('momentum', 0.0),
                        'volatility': features.get('volatility', 0.0),
                        'volume_imbalance': features.get('volume_imbalance', 0.0),
                        'spread_volatility': features.get('spread_volatility', 0.0),
                        'signal_quality': features.get('signal_quality', 0.0),
                        'risk_suppressed': features.get('risk_suppressed', False)
                    })
                    
                except Exception as e:
                    self.logger.debug(f"Error processing interval {interval_time}: {e}")
                    continue
            
        except Exception as e:
            self.logger.error(f"Error processing day {date.date()}: {e}")
        
        return results
    
    def _log_statistics(self, df: pd.DataFrame):
        """Log statistics about the generated cache."""
        self.logger.info("\n" + "="*50)
        self.logger.info("CACHE GENERATION COMPLETE")
        self.logger.info("="*50)
        
        # Basic stats
        self.logger.info(f"Total entries: {len(df):,}")
        self.logger.info(f"Date range: {df.index.min()} to {df.index.max()}")
        
        # Calculate actual interval
        if len(df) > 1:
            intervals = df.index.to_series().diff().dropna()
            avg_interval = intervals.mean().total_seconds()
            self.logger.info(f"Average interval: {avg_interval:.1f} seconds")
        
        # Regime distribution
        self.logger.info("\nRegime distribution:")
        regime_counts = df['regime'].value_counts()
        for regime, count in regime_counts.items():
            pct = count / len(df) * 100
            self.logger.info(f"  {regime}: {count:,} ({pct:.1f}%)")
        
        # Regime changes
        regime_changes = (df['regime'] != df['regime'].shift()).sum() - 1
        self.logger.info(f"\nTotal regime changes: {regime_changes:,}")
        self.logger.info(f"Changes per day: {regime_changes / ((df.index.max() - df.index.min()).days):.1f}")
    
    def save_cache(self, df: pd.DataFrame, filename: Optional[str] = None):
        """
        Save the regime cache to disk.
        
        Args:
            df: DataFrame with regime states
            filename: Optional custom filename
        """
        if filename is None:
            # Generate filename based on content
            year = df.index[0].year
            interval = self.interval_seconds
            filename = f"regimes_{year}_{interval}s.parquet"
        
        filepath = self.cache_dir / filename
        
        # Save to parquet
        df.to_parquet(filepath, compression='snappy')
        
        self.logger.info(f"Cache saved to: {filepath}")
        self.logger.info(f"File size: {filepath.stat().st_size / 1024 / 1024:.1f} MB")
    
    def generate_for_year(self, year: int, **kwargs) -> pd.DataFrame:
        """
        Convenience method to generate cache for a full year.
        
        Args:
            year: Year to generate
            **kwargs: Additional arguments for generate_for_period
            
        Returns:
            DataFrame with regime states
        """
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31, 23, 59, 59)
        
        df = self.generate_for_period(start_date, end_date, **kwargs)
        
        # Save with year-based filename
        if not df.empty:
            self.save_cache(df, f"regimes_{year}_{self.interval_seconds}s.parquet")
        
        return df


def main():
    """Command-line interface for regime cache generation."""
    parser = argparse.ArgumentParser(
        description='Generate regime cache for modern system backtesting'
    )
    
    # Date arguments
    date_group = parser.add_mutually_exclusive_group(required=True)
    date_group.add_argument('--year', type=int, help='Generate for entire year')
    date_group.add_argument('--start', type=str, help='Start date (YYYY-MM-DD)')
    
    parser.add_argument('--end', type=str, help='End date (YYYY-MM-DD, required with --start)')
    
    # Other arguments
    parser.add_argument('--interval', type=int, default=60,
                       help='Interval in seconds between regime calculations (default: 60)')
    parser.add_argument('--symbol', type=str, default='BTC',
                       help='Trading symbol (default: BTC)')
    parser.add_argument('--config', type=str, default='configs/overrides/modern_system_v2_complete.yaml',
                       help='Config file path')
    parser.add_argument('--workers', type=int, default=None,
                       help='Number of parallel workers (default: CPU count)')
    parser.add_argument('--no-parallel', action='store_true',
                       help='Disable parallel processing')
    parser.add_argument('--output', type=str, default=None,
                       help='Output filename (default: auto-generated)')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.DEBUG if args.verbose else logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Load configuration
    config = load_config(args.config)
    
    # Create generator
    generator = RegimeCacheGenerator(config, interval_seconds=args.interval)
    
    # Generate cache
    if args.year:
        df = generator.generate_for_year(
            year=args.year,
            symbol=args.symbol,
            parallel=not args.no_parallel,
            num_workers=args.workers
        )
    else:
        if not args.end:
            parser.error("--end is required when using --start")
        
        start_date = datetime.strptime(args.start, '%Y-%m-%d')
        end_date = datetime.strptime(args.end, '%Y-%m-%d')
        
        df = generator.generate_for_period(
            start_date=start_date,
            end_date=end_date,
            symbol=args.symbol,
            parallel=not args.no_parallel,
            num_workers=args.workers
        )
        
        if not df.empty and args.output:
            generator.save_cache(df, args.output)
        elif not df.empty:
            # Auto-generate filename
            generator.save_cache(df)
    
    print("\nCache generation complete!")
    print(f"Use this cache in your backtest with: use_regime_cache=True")
    print(f"Update config to point to the new {args.interval}s cache file")


if __name__ == '__main__':
    main()