"""
Modern Data Contract and Schema Validation
==========================================

This module defines the data contract between the features_1s data
and the modern system components. It handles:
- Field mappings (e.g., volume_imbalance -> obi_smoothed)
- Schema validation
- Data quality checks
- Type enforcement

Based on data discovery from 2024-01-01 to 2024-12-31
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union, Set
from dataclasses import dataclass, field
from datetime import datetime
import logging
import warnings


@dataclass
class FieldDefinition:
    """Definition of a data field including type, validation, and mapping."""
    name: str
    dtype: type
    required: bool = True
    actual_name: Optional[str] = None  # Actual column name in data
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    allow_null: bool = False
    default_value: Optional[Any] = None
    compute_from: Optional[List[str]] = None  # Fields to compute from if missing


class ModernDataContract:
    """
    Data contract for the modern trading system.
    
    This class defines the expected data schema and provides validation
    and transformation capabilities to ensure data quality.
    """
    
    # Field definitions based on data discovery
    FIELD_DEFINITIONS = {
        # Core timestamp
        'timestamp': FieldDefinition(
            name='timestamp',
            dtype=datetime,
            required=True,
            allow_null=False
        ),
        
        # Price data
        'close': FieldDefinition(
            name='close',
            dtype=float,
            required=True,
            min_value=0.0,
            allow_null=False
        ),
        'high': FieldDefinition(
            name='high',
            dtype=float,
            required=True,
            min_value=0.0,
            allow_null=False
        ),
        'low': FieldDefinition(
            name='low',
            dtype=float,
            required=True,
            min_value=0.0,
            allow_null=False
        ),
        'volume': FieldDefinition(
            name='volume',
            dtype=float,
            required=True,
            min_value=0.0,
            allow_null=False,
            default_value=0.0
        ),
        
        # Order book imbalance - KEY MAPPING
        'volume_imbalance': FieldDefinition(
            name='volume_imbalance',
            dtype=float,
            required=True,
            actual_name='obi_smoothed',  # Maps to obi_smoothed in actual data
            min_value=-1.0,
            max_value=1.0,
            allow_null=False,
            default_value=0.0
        ),
        
        # Spread metrics
        'spread_mean': FieldDefinition(
            name='spread_mean',
            dtype=float,
            required=True,
            min_value=0.0,
            allow_null=False
        ),
        'spread_std': FieldDefinition(
            name='spread_std',
            dtype=float,
            required=True,
            min_value=0.0,
            allow_null=False
        ),
        
        # Volatility metrics
        'atr_percent_sec': FieldDefinition(
            name='atr_percent_sec',
            dtype=float,
            required=True,
            min_value=0.0,
            max_value=1.0,  # 100% as decimal
            allow_null=False
        ),
        'atr_14_sec': FieldDefinition(
            name='atr_14_sec',
            dtype=float,
            required=False,  # Optional for risk calculations
            min_value=0.0,
            allow_null=True
        ),
        
        # Momentum indicators
        'ma_slope_ema_30s': FieldDefinition(
            name='ma_slope_ema_30s',
            dtype=float,
            required=True,
            allow_null=False
        ),
        
        # Optional indicators
        'adx': FieldDefinition(
            name='adx',
            dtype=float,
            required=False,
            min_value=0.0,
            max_value=100.0,
            allow_null=True,
            default_value=50.0  # Neutral ADX
        ),
        'funding_rate': FieldDefinition(
            name='funding_rate',
            dtype=float,
            required=False,
            allow_null=True,
            default_value=0.0
        ),
        'unrealised_pnl': FieldDefinition(
            name='unrealised_pnl',
            dtype=float,
            required=False,
            allow_null=True,
            default_value=0.0
        ),
        
        # Additional useful fields from discovery
        'spread': FieldDefinition(
            name='spread',
            dtype=float,
            required=False,
            min_value=0.0,
            allow_null=True
        ),
        'ma_slope': FieldDefinition(
            name='ma_slope',
            dtype=float,
            required=False,
            allow_null=True
        ),
        'raw_obi_5': FieldDefinition(
            name='raw_obi_5',
            dtype=float,
            required=False,
            min_value=-1.0,
            max_value=1.0,
            allow_null=True
        ),
        'raw_obi_20': FieldDefinition(
            name='raw_obi_20',
            dtype=float,
            required=False,
            min_value=-1.0,
            max_value=1.0,
            allow_null=True
        ),
    }
    
    # Field name mappings (expected -> actual)
    FIELD_MAPPINGS = {
        # Map enhanced hourly data fields to what legacy detector expects
        'obi_smoothed_5': 'volume_imbalance',  # Enhanced data has volume_imbalance
        'obi_smoothed': 'volume_imbalance',    # Generic mapping
        'atr_percent': 'atr_percent_sec',      # Enhanced data has atr_percent_sec
        # Add more mappings here if discovered
    }
    
    # Reverse mappings (actual -> expected)
    REVERSE_MAPPINGS = {v: k for k, v in FIELD_MAPPINGS.items()}
    
    def __init__(self, strict_mode: bool = False):
        """
        Initialize the data contract.
        
        Args:
            strict_mode: If True, validation failures raise exceptions.
                        If False, validation issues are logged as warnings.
        """
        self.strict_mode = strict_mode
        self.logger = logging.getLogger(self.__class__.__name__)
        self._validation_cache = {}
    
    def get_required_fields(self) -> List[str]:
        """Get list of required field names."""
        return [
            field_def.name 
            for field_def in self.FIELD_DEFINITIONS.values() 
            if field_def.required
        ]
    
    def get_actual_column_name(self, expected_name: str) -> str:
        """
        Get the actual column name in the data for an expected field name.
        
        Args:
            expected_name: The expected field name (e.g., 'volume_imbalance')
            
        Returns:
            The actual column name in the data (e.g., 'obi_smoothed')
        """
        field_def = self.FIELD_DEFINITIONS.get(expected_name)
        if field_def and field_def.actual_name:
            return field_def.actual_name
        return self.FIELD_MAPPINGS.get(expected_name, expected_name)
    
    def get_expected_field_name(self, actual_name: str) -> str:
        """
        Get the expected field name for an actual column in the data.
        
        Args:
            actual_name: The actual column name (e.g., 'obi_smoothed')
            
        Returns:
            The expected field name (e.g., 'volume_imbalance')
        """
        return self.REVERSE_MAPPINGS.get(actual_name, actual_name)
    
    def validate_dataframe(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate a dataframe against the contract.
        
        Args:
            df: DataFrame to validate
            
        Returns:
            Dictionary with validation results:
            {
                'valid': bool,
                'missing_required': List[str],
                'type_mismatches': Dict[str, str],
                'value_errors': Dict[str, str],
                'warnings': List[str]
            }
        """
        result = {
            'valid': True,
            'missing_required': [],
            'type_mismatches': {},
            'value_errors': {},
            'warnings': []
        }
        
        # Check for required fields
        for field_name, field_def in self.FIELD_DEFINITIONS.items():
            if not field_def.required:
                continue
                
            actual_col = self.get_actual_column_name(field_name)
            
            if actual_col not in df.columns:
                result['missing_required'].append(field_name)
                result['valid'] = False
                continue
            
            # Type validation
            col_dtype = df[actual_col].dtype
            if not self._is_compatible_dtype(col_dtype, field_def.dtype):
                result['type_mismatches'][field_name] = f"Expected {field_def.dtype}, got {col_dtype}"
            
            # Value range validation
            if field_def.min_value is not None or field_def.max_value is not None:
                col_data = df[actual_col].dropna()
                if len(col_data) > 0:
                    if field_def.min_value is not None and col_data.min() < field_def.min_value:
                        result['value_errors'][field_name] = f"Values below minimum {field_def.min_value}"
                    if field_def.max_value is not None and col_data.max() > field_def.max_value:
                        result['value_errors'][field_name] = f"Values above maximum {field_def.max_value}"
            
            # Null validation
            if not field_def.allow_null and df[actual_col].isna().any():
                null_pct = df[actual_col].isna().sum() / len(df) * 100
                result['warnings'].append(f"{field_name} has {null_pct:.2f}% null values")
        
        # Set overall validity
        result['valid'] = (
            len(result['missing_required']) == 0 and
            len(result['type_mismatches']) == 0 and
            len(result['value_errors']) == 0
        )
        
        # Handle strict mode
        if self.strict_mode and not result['valid']:
            error_msg = f"Data validation failed: {result}"
            raise ValueError(error_msg)
        elif not result['valid']:
            self.logger.warning(f"Data validation issues: {result}")
        
        return result
    
    def transform_to_expected_schema(self, df: pd.DataFrame, copy: bool = True) -> pd.DataFrame:
        """
        Transform a dataframe to match the expected schema.
        
        This includes:
        - Renaming columns from actual to expected names
        - Adding missing optional fields with defaults
        - Converting data types
        
        Args:
            df: Input dataframe
            copy: Whether to copy the dataframe before transformation
            
        Returns:
            Transformed dataframe with expected schema
        """
        if copy:
            df = df.copy()
        
        # Apply field mappings
        rename_map = {}
        for expected_name, actual_name in self.FIELD_MAPPINGS.items():
            if actual_name in df.columns and expected_name not in df.columns:
                rename_map[actual_name] = expected_name
        
        if rename_map:
            df = df.rename(columns=rename_map)
            if hasattr(self, 'logger'):
                self.logger.info(f"Applied field mappings: {rename_map}")
        
        # Add missing optional fields with defaults
        for field_name, field_def in self.FIELD_DEFINITIONS.items():
            if field_name not in df.columns:
                if not field_def.required and field_def.default_value is not None:
                    df[field_name] = field_def.default_value
                    self.logger.debug(f"Added missing field '{field_name}' with default value")
                elif field_def.compute_from:
                    # TODO: Implement field computation logic
                    pass
        
        # Ensure correct data types
        for field_name, field_def in self.FIELD_DEFINITIONS.items():
            if field_name in df.columns:
                if field_def.dtype == datetime and df[field_name].dtype != 'datetime64[ns]':
                    df[field_name] = pd.to_datetime(df[field_name])
                elif field_def.dtype == float and not pd.api.types.is_float_dtype(df[field_name]):
                    df[field_name] = pd.to_numeric(df[field_name], errors='coerce')
        
        return df
    
    def extract_signals_dict(self, row: pd.Series) -> Dict[str, Any]:
        """
        Extract a signals dictionary from a dataframe row.
        
        This ensures all required fields are present with proper names.
        
        Args:
            row: Pandas Series representing a single row
            
        Returns:
            Dictionary of signals with expected field names
        """
        signals = {}
        
        for field_name, field_def in self.FIELD_DEFINITIONS.items():
            # Try expected name first, then actual name
            value = None
            if field_name in row.index:
                value = row[field_name]
            else:
                actual_name = self.get_actual_column_name(field_name)
                if actual_name in row.index:
                    value = row[actual_name]
            
            # Handle missing values
            if pd.isna(value):
                if field_def.default_value is not None:
                    value = field_def.default_value
                elif field_def.required and not field_def.allow_null:
                    self.logger.warning(f"Required field '{field_name}' is null")
                    value = None
            
            # Add to signals dict
            if value is not None or field_def.allow_null:
                signals[field_name] = value
        
        return signals
    
    def _is_compatible_dtype(self, actual_dtype: Any, expected_type: type) -> bool:
        """Check if actual dtype is compatible with expected type."""
        if expected_type == float:
            return pd.api.types.is_numeric_dtype(actual_dtype)
        elif expected_type == datetime:
            return pd.api.types.is_datetime64_any_dtype(actual_dtype)
        elif expected_type == str:
            return pd.api.types.is_string_dtype(actual_dtype)
        return True
    
    def get_validation_summary(self, df: pd.DataFrame) -> str:
        """Get a human-readable validation summary."""
        result = self.validate_dataframe(df)
        
        summary = []
        summary.append(f"Data Validation {'PASSED' if result['valid'] else 'FAILED'}")
        summary.append(f"Shape: {df.shape}")
        
        if result['missing_required']:
            summary.append(f"Missing Required Fields: {', '.join(result['missing_required'])}")
        
        if result['type_mismatches']:
            summary.append("Type Mismatches:")
            for field, issue in result['type_mismatches'].items():
                summary.append(f"  - {field}: {issue}")
        
        if result['value_errors']:
            summary.append("Value Errors:")
            for field, issue in result['value_errors'].items():
                summary.append(f"  - {field}: {issue}")
        
        if result['warnings']:
            summary.append("Warnings:")
            for warning in result['warnings']:
                summary.append(f"  - {warning}")
        
        return '\n'.join(summary)