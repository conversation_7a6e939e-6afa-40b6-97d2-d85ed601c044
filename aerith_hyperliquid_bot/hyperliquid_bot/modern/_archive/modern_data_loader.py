"""
Modern Data Loader
====================
This loader handles 1-second feature data from the features_1s/ directory.
It provides pre-computed microstructure features for the modern system.

Key Features:
- Loads from features_1s/YYYY-MM-DD/features_HH.parquet files
- Provides 1-second granularity data
- Pre-computed features including OBI, spread metrics, etc.
- Optimized for continuous monitoring

FROZEN: This represents the modern data loading implementation.
"""

import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np
import pyarrow.parquet as pq

from ..core.interfaces import IDataLoader
from .registry import modern_data_loader
from ..config.settings import Config
from .adapters.data_adapter import ModernDataAdapter, AdapterConfig


@modern_data_loader("features_1s", version="1.0", experimental=True)
class ModernDataLoader(IDataLoader):
    """
    Modern data loader for 1-second feature files.
    
    This loader reads pre-computed features from:
    - features_1s/YYYY-MM-DD/features_HH.parquet
    
    The features include:
    - Microstructure metrics (OBI, spread, depth)
    - Technical indicators (EMAs, ATR)
    - Market state indicators
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Data paths
        self.feature_base_path = Path(config.data_paths.feature_1s_dir)
        self.ohlcv_base_path = Path(config.data_paths.ohlcv_base_path)
        
        # Enhanced hourly data path (new)
        self.enhanced_hourly_path = self.feature_base_path.parent / "enhanced_hourly" / "1h"
        self.use_enhanced_hourly = self.enhanced_hourly_path.exists()
        
        # Validate paths
        if not self.feature_base_path.exists():
            raise ValueError(f"Feature directory not found: {self.feature_base_path}")
        
        # Initialize data adapter for field transformations
        adapter_config = AdapterConfig(
            handle_missing_with_defaults=True,
            log_transformations=True,
            compute_derived_fields=True
        )
        self.data_adapter = ModernDataAdapter(adapter_config)
        
        self.logger.info(
            f"Modern Data Loader initialized:\n"
            f"  - Feature path: {self.feature_base_path}\n"
            f"  - OHLCV path: {self.ohlcv_base_path}\n"
            f"  - Enhanced hourly: {'ENABLED' if self.use_enhanced_hourly else 'DISABLED'}\n"
            f"  - Data adapter: Enabled with field mappings"
        )
    
    def load_data(self, start_date: datetime, end_date: datetime, 
                  symbols: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Load 1-second feature data for the specified date range.
        
        Args:
            start_date: Start date (inclusive)
            end_date: End date (exclusive)  
            symbols: Optional list of symbols (not used for single asset)
            
        Returns:
            DataFrame with all features and OHLCV data
        """
        self.logger.info(f"Loading modern features from {start_date} to {end_date}")
        
        # First load OHLCV data
        ohlcv_df = self._load_ohlcv_data(start_date, end_date)
        
        # Then load and merge feature data
        features_df = self._load_feature_data(start_date, end_date)
        
        # Merge on timestamp
        if not ohlcv_df.empty and not features_df.empty:
            # Ensure both have datetime index
            if not isinstance(ohlcv_df.index, pd.DatetimeIndex):
                ohlcv_df['timestamp'] = pd.to_datetime(ohlcv_df['timestamp'])
                ohlcv_df.set_index('timestamp', inplace=True)
            
            if not isinstance(features_df.index, pd.DatetimeIndex):
                features_df['timestamp'] = pd.to_datetime(features_df['timestamp'])
                features_df.set_index('timestamp', inplace=True)
            
            # Merge with forward fill for features
            merged_df = pd.merge_asof(
                ohlcv_df.sort_index(),
                features_df.sort_index(),
                left_index=True,
                right_index=True,
                direction='backward',
                tolerance=pd.Timedelta('1h')
            )
            
            # Add timestamp column back
            merged_df['timestamp'] = merged_df.index
            
            # Ensure 'open' column exists (derive from close if missing)
            if 'open' not in merged_df.columns and 'close' in merged_df.columns:
                # For hourly data, open is the close of the previous bar
                merged_df['open'] = merged_df['close'].shift(1)
                # Fill first value
                merged_df['open'].iloc[0] = merged_df['close'].iloc[0]
            
            self.logger.info(f"Loaded {len(merged_df)} rows of modern data")
            return merged_df
        
        elif not ohlcv_df.empty:
            # Return OHLCV only if no features available
            self.logger.warning("No feature data found, returning OHLCV only")
            ohlcv_df['timestamp'] = ohlcv_df.index
            return ohlcv_df
        
        else:
            self.logger.error("No data found for the specified date range")
            return pd.DataFrame()
    
    def _load_ohlcv_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Load OHLCV data from resampled files."""
        all_data = []
        
        # Use 1h timeframe for OHLCV
        timeframe_dir = self.ohlcv_base_path / "1h"
        if not timeframe_dir.exists():
            self.logger.warning(f"OHLCV directory not found: {timeframe_dir}")
            return pd.DataFrame()
        
        # Iterate through dates
        current_date = start_date.date()
        end_date_only = end_date.date()
        
        while current_date < end_date_only:
            # Look for file pattern YYYY-MM-DD_1h.parquet
            file_pattern = f"{current_date.strftime('%Y-%m-%d')}_1h.parquet"
            file_path = timeframe_dir / file_pattern
            
            if file_path.exists():
                try:
                    df = pd.read_parquet(file_path)
                    if not df.empty:
                        all_data.append(df)
                        self.logger.debug(f"Loaded OHLCV file: {file_path}")
                except Exception as e:
                    self.logger.error(f"Error loading {file_path}: {e}")
            
            current_date += timedelta(days=1)
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # Ensure timestamp column and index
            if 'timestamp' in combined_df.columns:
                combined_df['timestamp'] = pd.to_datetime(combined_df['timestamp'])
                combined_df.set_index('timestamp', inplace=True)
            
            # Filter to exact date range
            mask = (combined_df.index >= start_date) & (combined_df.index < end_date)
            filtered_df = combined_df[mask]
            
            self.logger.info(f"Loaded {len(filtered_df)} OHLCV rows")
            return filtered_df
        
        return pd.DataFrame()
    
    def load_resampled_data(self, start_time: datetime, end_time: datetime, 
                           timeframe: str = '1h') -> pd.DataFrame:
        """
        Load resampled OHLCV data for a specific time range.
        
        Args:
            start_time: Start time (inclusive)
            end_time: End time (exclusive)
            timeframe: Resampling timeframe (default: 1h)
            
        Returns:
            DataFrame with resampled OHLCV data
        """
        return self._load_ohlcv_data(start_time, end_time)
    
    def _load_enhanced_hourly_data(self, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        Load pre-computed enhanced hourly data.
        
        This provides ~3,600x performance improvement by avoiding
        on-the-fly resampling of 1-second data.
        
        Args:
            start_time: Start time (inclusive)
            end_time: End time (exclusive)
            
        Returns:
            DataFrame with enhanced hourly features
        """
        all_data = []
        
        # Iterate through dates
        current_date = start_time.date()
        end_date_only = end_time.date()
        
        while current_date <= end_date_only:
            # Look for file pattern YYYY-MM-DD_1h_enhanced.parquet
            file_name = f"{current_date.strftime('%Y-%m-%d')}_1h_enhanced.parquet"
            file_path = self.enhanced_hourly_path / file_name
            
            if file_path.exists():
                try:
                    df = pd.read_parquet(file_path)
                    if not df.empty:
                        all_data.append(df)
                        self.logger.debug(f"Loaded enhanced hourly file: {file_path}")
                except Exception as e:
                    self.logger.error(f"Error loading {file_path}: {e}")
                    # Fall back to on-the-fly resampling for this date
                    self.logger.warning(f"Falling back to 1s resampling for {current_date}")
                    date_start = datetime.combine(current_date, datetime.min.time())
                    date_end = date_start + timedelta(days=1)
                    fallback_df = self._resample_1s_to_hourly(date_start, date_end)
                    if not fallback_df.empty:
                        all_data.append(fallback_df)
            else:
                # If enhanced data doesn't exist, fall back to resampling
                self.logger.warning(f"Enhanced hourly data not found for {current_date}, using fallback")
                date_start = datetime.combine(current_date, datetime.min.time())
                date_end = date_start + timedelta(days=1)
                fallback_df = self._resample_1s_to_hourly(date_start, date_end)
                if not fallback_df.empty:
                    all_data.append(fallback_df)
            
            current_date += timedelta(days=1)
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=False)
            
            # Sort by index to ensure chronological order
            combined_df = combined_df.sort_index()
            
            # Ensure timestamp column exists
            if 'timestamp' not in combined_df.columns:
                combined_df['timestamp'] = combined_df.index
            
            # Filter to exact time range
            mask = (combined_df.index >= start_time) & (combined_df.index < end_time)
            filtered_df = combined_df[mask]
            
            # Ensure all required columns exist
            required_cols = ['open', 'high', 'low', 'close', 'volume', 'volume_imbalance']
            missing_cols = [col for col in required_cols if col not in filtered_df.columns]
            if missing_cols:
                self.logger.warning(f"Missing columns in enhanced data: {missing_cols}")
            
            self.logger.info(
                f"Loaded {len(filtered_df)} enhanced hourly bars "
                f"(3,600x faster than 1s resampling)"
            )
            
            return filtered_df
        
        # If no data found, return empty DataFrame
        self.logger.warning("No enhanced hourly data found, returning empty DataFrame")
        return pd.DataFrame()
    
    def _resample_1s_to_hourly(self, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        Helper method to resample 1s data to hourly (fallback).
        
        This is extracted from the original load_hourly_features method
        to support fallback when enhanced data is missing.
        """
        # Load 1-second features
        features_1s = self._load_feature_data(start_time, end_time)
        
        if features_1s.empty:
            self.logger.warning("No 1-second features found for resampling")
            # Return empty DataFrame with correct structure
            return self._create_empty_hourly_dataframe(start_time, end_time)
        
        # Load OHLCV data for price fields
        ohlcv_hourly = self._load_ohlcv_data(start_time, end_time)
        
        # Resample features to hourly
        # Use proper aggregation for each field type
        agg_dict = {
            # Price fields - if present in features
            'close': 'last' if 'close' in features_1s.columns else None,
            'high': 'max' if 'high' in features_1s.columns else None,
            'low': 'min' if 'low' in features_1s.columns else None,
            'volume': 'sum' if 'volume' in features_1s.columns else None,
            
            # Microstructure features - use mean for most
            'volume_imbalance': 'mean' if 'volume_imbalance' in features_1s.columns else None,
            'obi_smoothed': 'mean' if 'obi_smoothed' in features_1s.columns else None,
            'spread_mean': 'mean' if 'spread_mean' in features_1s.columns else None,
            'spread_std': 'mean' if 'spread_std' in features_1s.columns else None,
            
            # ATR - use last value of the hour
            'atr_14_sec': 'last' if 'atr_14_sec' in features_1s.columns else None,
            'atr_percent_sec': 'last' if 'atr_percent_sec' in features_1s.columns else None,
            
            # Momentum - use last value
            'ma_slope': 'last' if 'ma_slope' in features_1s.columns else None,
            'ma_slope_ema_30s': 'last' if 'ma_slope_ema_30s' in features_1s.columns else None,
            
            # Additional fields
            'adx': 'last' if 'adx' in features_1s.columns else None,
            'funding_rate': 'last' if 'funding_rate' in features_1s.columns else None,
            'unrealised_pnl': 'last' if 'unrealised_pnl' in features_1s.columns else None,
        }
        
        # Remove None values
        agg_dict = {k: v for k, v in agg_dict.items() if v is not None}
        
        if not agg_dict:
            self.logger.error("No valid columns found for aggregation")
            return pd.DataFrame()
        
        # Ensure features_1s has datetime index
        if 'timestamp' in features_1s.columns and not isinstance(features_1s.index, pd.DatetimeIndex):
            features_1s = features_1s.set_index('timestamp')
        elif not isinstance(features_1s.index, pd.DatetimeIndex):
            self.logger.error("No timestamp column or datetime index found in features_1s")
            return pd.DataFrame()
        
        # Handle duplicate timestamps before resampling
        if features_1s.index.duplicated().any():
            dup_count = features_1s.index.duplicated().sum()
            self.logger.warning(f"Found {dup_count} duplicate timestamps, keeping last occurrence")
            features_1s = features_1s[~features_1s.index.duplicated(keep='last')]
        
        # Resample with proper labeling to prevent look-ahead bias
        hourly_features = features_1s.resample('1h', label='right', closed='left').agg(agg_dict)
        
        # Add open price (first value of each hour)
        if 'close' in features_1s.columns:
            hourly_features['open'] = features_1s['close'].resample(
                '1h', label='right', closed='left'
            ).first()
        
        # Drop any rows with all NaN
        hourly_features = hourly_features.dropna(how='all')
        
        # Ensure volume_imbalance exists after resampling
        if 'obi_smoothed' in hourly_features.columns and 'volume_imbalance' not in hourly_features.columns:
            hourly_features['volume_imbalance'] = hourly_features['obi_smoothed']
            self.logger.info("Renamed obi_smoothed to volume_imbalance after resampling")
        
        # Add timestamp column
        hourly_features['timestamp'] = hourly_features.index
        
        self.logger.info(
            f"Resampled {len(features_1s)} 1s rows to {len(hourly_features)} hourly bars"
        )
        
        return hourly_features
    
    def load_hourly_features(self, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        Load hourly features for TF-v3 strategy.
        
        This method uses pre-computed enhanced hourly data if available,
        otherwise falls back to resampling 1-second data on the fly.
        
        Args:
            start_time: Start time (inclusive)
            end_time: End time (exclusive)
            
        Returns:
            DataFrame with hourly bars containing all required features
        """
        # Check if we have enhanced hourly data
        if self.use_enhanced_hourly:
            self.logger.info(f"Loading enhanced hourly features from {start_time} to {end_time}")
            return self._load_enhanced_hourly_data(start_time, end_time)
        
        # Fall back to on-the-fly resampling
        self.logger.info(f"Loading and resampling features to hourly from {start_time} to {end_time}")
        return self._resample_1s_to_hourly(start_time, end_time)
    
    def load_features_1s(self, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        Load 1-second feature data for a specific time range.
        
        Args:
            start_time: Start time (inclusive)
            end_time: End time (exclusive)
            
        Returns:
            DataFrame with 1-second feature data
        """
        return self._load_feature_data(start_time, end_time)
    
    def _load_feature_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Load 1-second feature data."""
        all_data = []
        
        # Iterate through dates
        current_date = start_date.date()
        end_date_only = end_date.date()
        
        while current_date <= end_date_only:
            date_dir = self.feature_base_path / current_date.strftime('%Y-%m-%d')
            
            if date_dir.exists():
                # Load all hourly files for this date
                for hour_file in sorted(date_dir.glob('features_*.parquet')):
                    try:
                        # Read with specific columns if needed
                        df = pd.read_parquet(hour_file)
                        
                        if not df.empty:
                            # Ensure timestamp column
                            if 'timestamp' in df.columns:
                                df['timestamp'] = pd.to_datetime(df['timestamp'])
                            
                            all_data.append(df)
                            self.logger.debug(f"Loaded feature file: {hour_file}")
                    
                    except Exception as e:
                        self.logger.error(f"Error loading {hour_file}: {e}")
                        continue
            
            current_date += timedelta(days=1)
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # Handle duplicate timestamps before setting as index
            if 'timestamp' in combined_df.columns:
                # Convert to datetime first
                combined_df['timestamp'] = pd.to_datetime(combined_df['timestamp'])
                
                # Check for duplicates
                dup_mask = combined_df.duplicated(subset=['timestamp'], keep=False)
                if dup_mask.any():
                    dup_count = dup_mask.sum()
                    self.logger.warning(f"Found {dup_count} duplicate timestamps in features_1s, keeping last occurrence")
                    combined_df = combined_df.drop_duplicates(subset=['timestamp'], keep='last')
                    combined_df = combined_df.sort_values('timestamp').reset_index(drop=True)
                
                # Now set timestamp as index
                combined_df.set_index('timestamp', inplace=True)
            
            # Filter to exact date range
            mask = (combined_df.index >= start_date) & (combined_df.index < end_date)
            filtered_df = combined_df[mask]
            
            # Apply data adapter transformation
            self.logger.info("Applying ModernDataAdapter transformations...")
            adapted_df = self.data_adapter.adapt_features_dataframe(filtered_df)
            
            # Log adapter statistics
            stats = self.data_adapter.get_adapter_statistics()
            self.logger.info(
                f"Data transformation complete:\n"
                f"  - Rows processed: {stats['total_rows_processed']}\n"
                f"  - Fields mapped: {stats['fields_mapped']}\n"
                f"  - Null values handled: {stats['null_values_handled']}"
            )
            
            # Ensure 'open' column exists in features too
            if 'open' not in adapted_df.columns and 'close' in adapted_df.columns:
                # Group by hour and set open as first close value
                adapted_df['hour'] = adapted_df.index.floor('h')
                adapted_df['open'] = adapted_df.groupby('hour')['close'].transform('first')
                adapted_df.drop('hour', axis=1, inplace=True)
            
            self.logger.info(f"Loaded {len(adapted_df)} feature rows")
            return adapted_df
        
        return pd.DataFrame()
    
    def _get_column_mapping(self) -> Dict[str, str]:
        """Get column name mappings for compatibility.
        
        DEPRECATED: Now handled by ModernDataAdapter.
        Kept for backward compatibility only.
        """
        return {
            # OBI fields - use smoothed version as volume_imbalance
            'obi_smoothed': 'volume_imbalance',  # Primary mapping
            'obi_smoothed_5': 'volume_imbalance_5',
            'obi_smoothed_20': 'volume_imbalance_20',
            # Keep original OBI names too
            'raw_obi_5': 'obi_5',
            'raw_obi_20': 'obi_20',
            # ATR mappings - already in correct format
            'atr_14_sec': 'atr',
            'atr_percent_sec': 'atr_percent',
            # Spread is already correct
            # 'spread_mean' and 'spread_std' are already correct
            # Momentum indicators already correct
            # 'ma_slope' and 'ma_slope_ema_30s' are already correct
        }
    
    def load_signals(self, timestamp: datetime) -> Dict[str, Any]:
        """
        Load signals for a specific timestamp.
        
        Args:
            timestamp: The timestamp to load signals for
            
        Returns:
            Dictionary of signals at that timestamp
        """
        # Determine which file contains this timestamp
        date_dir = self.feature_base_path / timestamp.strftime('%Y-%m-%d')
        hour_file = date_dir / f"features_{timestamp.hour:02d}.parquet"
        
        if not hour_file.exists():
            self.logger.warning(f"Feature file not found: {hour_file}")
            return {}
        
        try:
            # Read the specific hour file
            df = pd.read_parquet(hour_file)
            
            # Convert timestamp column
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            # Find the closest timestamp
            if timestamp in df.index:
                row = df.loc[timestamp]
            else:
                # Use the last available data before timestamp
                mask = df.index <= timestamp
                if mask.any():
                    row = df[mask].iloc[-1]
                else:
                    return {}
            
            # Convert to dictionary
            signals = row.to_dict() if hasattr(row, 'to_dict') else dict(row)
            
            # Apply data adapter transformation
            adapted_signals = self.data_adapter.adapt_signals_dict(signals, timestamp)
            
            # Add timestamp back
            adapted_signals['timestamp'] = timestamp
            
            return adapted_signals
            
        except Exception as e:
            self.logger.error(f"Error loading signals for {timestamp}: {e}")
            return {}
    
    def get_feature_columns(self) -> List[str]:
        """Get list of available feature columns."""
        # Sample a file to get column names
        sample_date = datetime.now().date() - timedelta(days=1)
        date_dir = self.feature_base_path / sample_date.strftime('%Y-%m-%d')
        
        if date_dir.exists():
            hour_files = list(date_dir.glob('features_*.parquet'))
            if hour_files:
                try:
                    df = pd.read_parquet(hour_files[0])
                    return list(df.columns)
                except Exception:
                    pass
        
        # Return expected columns if can't sample
        return [
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'volume_imbalance', 'obi', 'obi_zscore', 
            'spread', 'spread_volatility',
            'depth_imbalance', 'trade_flow_imbalance',
            'atr', 'atr_percent', 'volatility',
            'ma_slope', 'ma_slope_ema_30s',
            'adx', 'rsi', 'funding_rate'
        ]
    
    def get_name(self) -> str:
        """Get data loader name."""
        return "Modern Features 1s Loader"
    
    def supports_streaming(self) -> bool:
        """Check if loader supports streaming updates."""
        return False  # File-based loader
    
    def get_required_columns(self) -> List[str]:
        """Get list of required columns for this data loader."""
        return [
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'volume_imbalance', 'obi_smoothed', 'spread_mean', 'spread_std',
            'atr_14_sec', 'atr_percent_sec', 'ma_slope', 'ma_slope_ema_30s'
        ]
    
    def validate_data(self, df: pd.DataFrame) -> tuple[bool, List[str]]:
        """Validate loaded data meets requirements."""
        issues = []
        
        # Check if DataFrame is empty
        if df.empty:
            issues.append("DataFrame is empty")
            return False, issues
        
        # Check for required columns
        required = self.get_required_columns()
        missing_cols = [col for col in required if col not in df.columns]
        if missing_cols:
            issues.append(f"Missing columns: {missing_cols}")
        
        # Check for NaN values in critical columns
        critical_cols = ['timestamp', 'open', 'high', 'low', 'close']
        for col in critical_cols:
            if col in df.columns and df[col].isna().any():
                nan_count = df[col].isna().sum()
                issues.append(f"Column '{col}' has {nan_count} NaN values")
        
        # Check timestamp ordering
        if 'timestamp' in df.columns:
            if not df['timestamp'].is_monotonic_increasing:
                issues.append("Timestamps are not monotonically increasing")
        
        is_valid = len(issues) == 0
        return is_valid, issues
    
    def _create_empty_hourly_dataframe(self, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        Create an empty DataFrame with the correct structure for hourly data.
        This ensures the system doesn't crash when data is missing.
        """
        # Generate hourly timestamps
        timestamps = pd.date_range(start=start_time, end=end_time, freq='h', inclusive='left')
        
        if len(timestamps) == 0:
            return pd.DataFrame()
        
        # Create DataFrame with all required columns
        df = pd.DataFrame(index=timestamps)
        
        # Add required columns with NaN values
        required_columns = [
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'volume_imbalance', 'spread_mean', 'spread_std',
            'atr_14_sec', 'atr_percent_sec', 'ma_slope', 'ma_slope_ema_30s'
        ]
        
        for col in required_columns:
            if col == 'timestamp':
                df[col] = df.index
            else:
                df[col] = np.nan
        
        return df