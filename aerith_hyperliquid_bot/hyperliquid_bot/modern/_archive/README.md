# Archived Modern System Components

This directory contains deprecated components from the modern trading system that have been superseded by the Robust versions.

## Archived Files

### modern_data_loader.py (formerly data_loader.py)
- **Reason for deprecation**: Lacks fallback mechanisms, marked as experimental
- **Replaced by**: robust_data_loader.py (renamed to data_loader.py)
- **Key issues**: 
  - No fallback chain for missing data
  - Would crash if features_1s data unavailable
  - 668 lines of code vs 425 in Robust version

### modern_backtester_engine.py (formerly backtester_engine.py)
- **Reason for deprecation**: Missing position management improvements, basic error handling
- **Replaced by**: robust_backtest_engine.py (renamed to backtest_engine.py)
- **Key issues**:
  - No adaptive warmup
  - Poor error handling
  - Missing position management fixes
  - 913 lines of code vs 673 in Robust version

## Failed Detector Experiments (to be archived)

The following detectors will be moved here after integration testing:
- continuous_gms.py - v1.0 experimental, unprofitable
- continuous_detector.py - v2.0 experimental, unprofitable  
- continuous_detector_v2.py - v3.0, still unprofitable despite claims

## Important Notes

**DO NOT USE THESE COMPONENTS** - They represent failed experiments and inferior implementations. The production system uses:
- data_loader.py (formerly robust_data_loader.py)
- backtest_engine.py (formerly robust_backtest_engine.py)
- enhanced detector (wraps proven legacy logic)

These files are preserved for historical reference only.