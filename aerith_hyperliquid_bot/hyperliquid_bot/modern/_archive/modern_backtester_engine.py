"""
Modern Backtesting Engine
=========================

Handles 60s regime updates + hourly trading simulation for the modern system.

This engine properly simulates the different timeframes required:
- 60-second regime state updates for market awareness
- Hourly trading evaluation for position decisions
- 1-minute execution refinement for optimal entries

Critical: ALL operations MUST prevent look-ahead bias!
"""

import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np
from pathlib import Path

from ..config.settings import Config
from ..portfolio.portfolio import Portfolio
from ..features.indicators import calculate_indicators_for_tf_v3
from .data_aggregator import ModernDataAggregator
from .data_loader import ModernDataLoader
from .regime_state_manager import RegimeStateManager
from .hourly_evaluator import HourlyStrategyEvaluator
from .registry import get_modern_detector, get_modern_strategy
from .execution_refiner import ExecutionRefiner
from .regime_cache import RegimeCache


class ModernBacktestEngine:
    """
    Modern backtesting engine that simulates 60s regime + hourly trading.
    
    This engine:
    1. Loads 1-second feature data for the entire period
    2. For each hour, simulates 60 regime updates (one per minute)
    3. At hour boundaries, evaluates trading opportunities
    4. Uses 1-minute data for execution refinement
    5. Tracks all trades and performance metrics
    """
    
    def __init__(self, config: Config, 
                 start_date: datetime, 
                 end_date: datetime,
                 data_dir: Optional[Path] = None,
                 use_regime_cache: bool = True):
        """
        Initialize modern backtesting engine.
        
        Args:
            config: Configuration object
            start_date: Backtest start date
            end_date: Backtest end date
            data_dir: Optional data directory override
            use_regime_cache: Whether to use pre-computed regimes (recommended)
        """
        self.config = config
        self.start_date = start_date
        self.end_date = end_date
        self.data_dir = data_dir or Path(config.data_paths.l2_data_root)
        self.mode = 'backtest'
        self.use_regime_cache = use_regime_cache
        
        # Initialize logger
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize components
        self.data_aggregator = ModernDataAggregator()
        self.data_loader = ModernDataLoader(config)
        
        # Initialize regime management
        if use_regime_cache:
            # Use pre-computed regimes for fast backtesting
            self.regime_cache = RegimeCache()
            if not self.regime_cache.validate_cache(start_date, end_date):
                raise ValueError(
                    "Pre-computed regimes not available for backtest period. "
                    "Run 'python scripts/precompute_regimes.py' first!"
                )
            self.regime_manager = RegimeStateManager(mode='backtest')
            self.regime_detector = None  # Not needed with cache
            self.logger.info("Using pre-computed regime cache for 100x faster backtesting!")
        else:
            # Traditional approach - simulate regime updates (slow)
            self.regime_manager = RegimeStateManager(mode='backtest')
            self.regime_detector = get_modern_detector(
                config.regime.detector_type,
                config=config
            )
            self.regime_cache = None
            self.logger.warning("Running without regime cache - this will be SLOW!")
        
        # Create strategy
        self.strategy = get_modern_strategy(
            "tf_v3_modern",  # Modern TF-v3
            config=config
        )
        
        # Create portfolio
        self.portfolio = Portfolio(config)
        
        # Create hourly evaluator
        self.hourly_evaluator = HourlyStrategyEvaluator(
            config=config,
            regime_manager=self.regime_manager,
            strategy=self.strategy,
            mode='backtest'
        )
        
        # Calculate required warmup period from signal engine
        self.warmup_hours = self.hourly_evaluator.signal_engine.calculate_required_lookback()
        self.logger.info(f"Calculated warmup period: {self.warmup_hours} hours")
        
        # Create execution refiner
        self.execution_refiner = ExecutionRefiner(
            window_minutes=5,
            min_volume_percentile=20.0,
            max_spread_percentile=80.0,
            momentum_weight=0.3
        )
        
        # Performance tracking
        self.trades = []  # Active trades being tracked
        self.completed_trades = []  # Closed trades with actual P&L
        self.regime_history = []
        self.performance_metrics = {}
        
        self.logger.info(
            f"Modern Backtesting Engine initialized:\n"
            f"  - Period: {start_date} to {end_date}\n"
            f"  - Regime Source: {'Pre-computed cache' if use_regime_cache else 'Live simulation'}\n"
            f"  - Strategy: tf_v3_modern\n"
            f"  - Trading Cadence: 60 minutes\n"
            f"  - Risk per trade: {config.portfolio.risk_per_trade:.2%}"
        )
    
    def run_backtest(self) -> Dict[str, Any]:
        """
        Run the modern backtest with proper timing simulation.
        
        Critical: Must simulate 60 GMS updates per hour!
        CRITICAL: Must prevent ALL look-ahead bias!
        
        Returns:
            Backtest results dictionary
        """
        self.logger.info("Starting modern backtest...")
        
        # Generate hourly timestamps for the period
        hourly_timestamps = self._generate_hourly_timestamps()
        self.logger.info(f"Processing {len(hourly_timestamps)} hours")
        
        # Track timing
        import time
        start_time = time.time()
        hours_processed = 0
        
        # Calculate when we can start trading (after warmup)
        trading_start_time = self.start_date + timedelta(hours=self.warmup_hours)
        self.logger.info(f"Trading will begin at {trading_start_time} (after {self.warmup_hours}h warmup)")
        
        # Process each hour
        for hour_idx, hour_start in enumerate(hourly_timestamps):
            hour_end = hour_start + timedelta(hours=1)
            
            # 1. Update regime state for this hour
            if self.use_regime_cache:
                # Fast path: lookup pre-computed regime
                regime_state = self.regime_cache.get_regime_at_time(hour_start)
                if regime_state:
                    # Update regime manager with cached state
                    self.regime_manager.update_state(
                        timestamp=hour_start,
                        state=regime_state['regime'],
                        confidence=regime_state['confidence'],
                        features={
                            'momentum': regime_state.get('momentum', 0.0),
                            'volatility': regime_state.get('volatility', 0.0),
                            'volume_imbalance': regime_state.get('volume_imbalance', 0.0),
                            'spread_volatility': regime_state.get('spread_volatility', 0.0),
                            'signal_quality': regime_state.get('signal_quality', 0.0)
                        }
                    )
                    self.regime_history.append(regime_state)
                    self.logger.debug(
                        f"Hour {hour_start}: Loaded regime {regime_state['regime']} "
                        f"(confidence: {regime_state['confidence']:.2f})"
                    )
                else:
                    self.logger.warning(f"No cached regime for {hour_start}")
            else:
                # Slow path: simulate 60 regime updates
                regime_states = self._simulate_hourly_regime_updates(
                    hour_start, hour_end
                )
                
                # Log regime summary for this hour
                if regime_states:
                    unique_states = set(s['state'] for s in regime_states)
                    self.logger.debug(
                        f"Hour {hour_start}: {len(regime_states)} regime updates, "
                        f"states: {unique_states}"
                    )
            
            # 2. At hour boundary, evaluate trading
            # Skip trading during warmup period
            if hour_idx > 0 and hour_start >= trading_start_time:
                
                # CRITICAL FIX: Check for position exits FIRST
                position_exited_this_step = False
                if self.portfolio.position:
                    exit_result = self._evaluate_position_exit(hour_end)
                    if exit_result:
                        self._handle_position_exit(exit_result, hour_end)
                        position_exited_this_step = True
                        self.logger.info(
                            f"Position CLOSED: {exit_result['reason']} at {exit_result['exit_price']:.2f} "
                            f"(PnL: {exit_result['pnl_pct']:.2%})"
                        )
                
                # CRITICAL FIX: Only evaluate entries if no position exists (like legacy system)
                if self.portfolio.position is None and not position_exited_this_step:
                    trade_signal = self._evaluate_trading_opportunity(
                        hour_end
                    )
                    
                    if trade_signal:
                        # 3. Refine execution with 1-minute data
                        executed_trade = self._refine_execution(
                            trade_signal, hour_end
                        )
                        
                        if executed_trade:
                            # Handle entry through portfolio (like legacy system)
                            self._handle_position_entry(executed_trade, hour_end)
                            self.trades.append(executed_trade)
                            self.logger.info(
                                f"Trade #{len(self.trades)}: "
                                f"{executed_trade['direction']} at {executed_trade['entry_price']:.2f} "
                                f"(size: {executed_trade['position_size']:.2%})"
                            )
                    else:
                        # Log why no trade was generated
                        if hour_idx % 24 == 0:  # Log daily
                            self.logger.debug(f"No trade signal at {hour_end}")
                else:
                    # Log why entry was skipped
                    if self.portfolio.position:
                        if hour_idx % 24 == 0:  # Log daily
                            self.logger.debug(f"Entry skipped: position exists ({self.portfolio.position['type']})")
                    elif position_exited_this_step:
                        self.logger.debug(f"Entry skipped: position just exited")
            
            # Progress tracking
            hours_processed += 1
            if hours_processed % 24 == 0:  # Daily progress
                elapsed = time.time() - start_time
                rate = hours_processed / elapsed
                remaining = (len(hourly_timestamps) - hours_processed) / rate
                
                self.logger.info(
                    f"Progress: {hours_processed}/{len(hourly_timestamps)} hours "
                    f"({hours_processed/len(hourly_timestamps)*100:.1f}%) "
                    f"- {len(self.trades)} trades - "
                    f"ETA: {remaining/60:.1f} minutes"
                )
        
        # Calculate final metrics
        total_time = time.time() - start_time
        self.performance_metrics = self._calculate_performance_metrics()
        
        # Add execution performance analysis (use all trades for execution analysis)
        all_trades_for_exec = self.completed_trades + self.trades
        if all_trades_for_exec:
            exec_stats = self.execution_refiner.analyze_execution_performance(all_trades_for_exec)
            self.performance_metrics['execution_stats'] = exec_stats
        
        self.logger.info(
            f"\nBacktest Complete:\n"
            f"  - Total Time: {total_time:.1f} seconds\n"
            f"  - Hours Processed: {hours_processed}\n"
            f"  - Total Trades: {len(self.completed_trades) + len(self.trades)}\n"
            f"  - Completed Trades: {len(self.completed_trades)}\n"
            f"  - Open Trades: {len(self.trades)}\n"
            f"  - Final ROI: {self.performance_metrics.get('total_return', 0):.2%}"
        )
        
        # Log execution quality if available
        if 'execution_stats' in self.performance_metrics:
            exec_stats = self.performance_metrics['execution_stats']
            self.logger.info(
                f"\nExecution Quality:\n"
                f"  - Avg Quality Score: {exec_stats.get('avg_quality_score', 0):.1f}\n"
                f"  - Avg Price Improvement: {exec_stats.get('avg_improvement', 0):.4f}\n"
                f"  - Positive Improvement %: {exec_stats.get('positive_improvement_pct', 0):.1f}%"
            )
        
        # CRITICAL: Add fallback detection
        self._log_system_integrity()
        
        # Return completed trades (with actual P&L) + any open trades
        all_trades = self.completed_trades.copy()
        if self.trades:  # Add any still-open trades
            all_trades.extend(self.trades)
        
        return {
            'trades': all_trades,  # All trades (completed + open)
            'performance': self.performance_metrics,
            'regime_history': self.regime_history,
            'runtime_seconds': total_time,
            'system_integrity': self._check_system_integrity()
        }
    
    def _generate_hourly_timestamps(self) -> List[datetime]:
        """
        Generate hourly timestamps for the backtest period.
        
        Returns:
            List of hourly datetime objects
        """
        timestamps = []
        current = self.start_date.replace(minute=0, second=0, microsecond=0)
        
        while current < self.end_date:
            timestamps.append(current)
            current += timedelta(hours=1)
        
        return timestamps
    
    def _simulate_hourly_regime_updates(self, 
                                       hour_start: datetime,
                                       hour_end: datetime) -> List[Dict[str, Any]]:
        """
        Simulate 60 regime updates for one hour.
        
        CRITICAL: Only use data up to current_time to prevent look-ahead!
        
        Args:
            hour_start: Start of the hour
            hour_end: End of the hour
            
        Returns:
            List of regime states for this hour
        """
        regime_states = []
        
        # Load data for this hour (and necessary lookback)
        lookback_start = hour_start - timedelta(hours=24)  # Default 24h lookback
        hour_data = self.data_loader.load_features_1s(
            lookback_start, hour_end
        )
        
        if hour_data.empty:
            self.logger.warning(f"No data available for hour {hour_start}")
            return regime_states
        
        # Simulate minute-by-minute updates
        for minute in range(60):
            current_time = hour_start + timedelta(minutes=minute)
            
            # CRITICAL: Only use data up to current_time
            available_data = hour_data[hour_data.index <= current_time].copy()
            
            if len(available_data) < 60:  # Need at least 1 minute of data
                continue
            
            # Aggregate to 60s for regime detection
            features_60s = self.data_aggregator.aggregate_to_60s(
                available_data, current_time
            )
            
            if features_60s.empty:
                continue
            
            # Get latest 60s bar
            latest_features = features_60s.iloc[-1].to_dict()
            
            # Update regime detector
            regime_state = self.regime_detector.detect_regime(
                latest_features, current_time
            )
            
            # CRITICAL: Update regime manager with the detected state!
            # The regime manager tracks history for strategy evaluation
            self.regime_manager.update_state(
                timestamp=current_time,
                state=regime_state,
                confidence=0.8,  # Default confidence
                features={
                    'momentum': latest_features.get('ma_slope_ema_30s', 0.0),
                    'volatility': latest_features.get('atr_percent_sec', 0.0),
                    'volume_imbalance': latest_features.get('volume_imbalance', 0.0),
                    'spread_mean': latest_features.get('spread_mean', 0.0),
                    'spread_std': latest_features.get('spread_std', 0.0)
                }
            )
            
            # Convert to result dict for tracking
            regime_result = {
                'state': regime_state,
                'timestamp': current_time,
                'confidence': 0.8  # Default confidence
            }
            
            if regime_result:
                regime_states.append({
                    'timestamp': current_time,
                    'state': regime_result['state'],
                    'confidence': regime_result.get('confidence', 0.0),
                    'features': regime_result.get('features', {})
                })
                
                # Track for analysis
                self.regime_history.append(regime_result)
        
        return regime_states
    
    def _evaluate_trading_opportunity(self, 
                                     hour_boundary: datetime) -> Optional[Dict[str, Any]]:
        """
        Evaluate trading opportunity at hour boundary.
        
        CRITICAL: Only use completed hourly bar!
        FIXED: Now loads feature-rich hourly bars instead of just OHLCV!
        
        Args:
            hour_boundary: The hour boundary timestamp
            
        Returns:
            Trade signal dict or None
        """
        # Load hourly features (not just OHLCV!)
        # Use calculated warmup period instead of hardcoded value
        lookback_start = hour_boundary - timedelta(hours=self.warmup_hours)
        
        # CRITICAL FIX: Use load_hourly_features to get feature-rich hourly bars
        hourly_data = self.data_loader.load_hourly_features(
            lookback_start, hour_boundary
        )
        
        if hourly_data.empty or len(hourly_data) < 2:
            self.logger.debug(f"Insufficient hourly data at {hour_boundary}: {len(hourly_data)} bars")
            return None
        
        # CRITICAL: Only use completed bars (exclude current incomplete hour)
        completed_bars = hourly_data[hourly_data.index < hour_boundary].copy()
        
        if completed_bars.empty:
            self.logger.debug(f"No completed bars before {hour_boundary}")
            return None
        
        # Get latest completed bar with all features
        latest_bar = completed_bars.iloc[-1]
        
        # Log what features we have
        self.logger.debug(
            f"Evaluating at {hour_boundary} with features: "
            f"volume_imbalance={latest_bar.get('volume_imbalance', 'N/A'):.4f}, "
            f"spread_mean={latest_bar.get('spread_mean', 'N/A'):.4f}, "
            f"atr_14_sec={latest_bar.get('atr_14_sec', 'N/A'):.2f}"
        )
        
        # Check if we should evaluate
        if not self.hourly_evaluator.should_evaluate(hour_boundary):
            return None
        
        # Prepare current signals from latest feature-rich bar
        # Now these fields should actually exist!
        current_signals = {
            'spread_mean': latest_bar.get('spread_mean', 0.0),
            'spread_std': latest_bar.get('spread_std', 0.0),
            'volume_imbalance': latest_bar.get('volume_imbalance', 0.0),
            'atr_14_sec': latest_bar.get('atr_14_sec', 0.0),
            'atr_percent_sec': latest_bar.get('atr_percent_sec', 0.0),
            'ma_slope': latest_bar.get('ma_slope', 0.0),
            'ma_slope_ema_30s': latest_bar.get('ma_slope_ema_30s', 0.0),
            # Also map ATR fields for compatibility
            'atr_14': latest_bar.get('atr_14_sec', 0.0),
            'atr_percent': latest_bar.get('atr_percent_sec', 0.0),
        }
        
        # Pass completed bars as OHLCV history for indicator calculation
        # The HourlyStrategyEvaluator will use ModernSignalEngine internally
        trade_signal = self.hourly_evaluator.evaluate(
            hourly_bar=latest_bar.to_dict(),
            current_signals=current_signals,
            timestamp=hour_boundary,
            ohlcv_history=completed_bars  # Pass full history with features
        )
        
        if trade_signal:
            self.logger.info(
                f"Trade signal generated at {hour_boundary}: "
                f"{trade_signal.get('direction', 'N/A')} with "
                f"confidence {trade_signal.get('confidence', 0):.2f}"
            )
        
        return trade_signal
    
    def _refine_execution(self,
                         trade_signal: Dict[str, Any],
                         execution_start: datetime) -> Optional[Dict[str, Any]]:
        """
        Refine execution using 1-minute data.
        
        SIMPLIFIED FOR BACKTESTING: Just use the signal price (hourly close).
        In live trading, we would load 1s data for optimization.
        
        Args:
            trade_signal: Trading signal from hourly evaluation
            execution_start: Start of execution window
            
        Returns:
            Executed trade dict or None
        """
        # SIMPLIFIED: For backtesting, just execute at the signal price
        # This avoids loading 5 minutes of 1s data for every trade
        entry_price = trade_signal.get('price', 0)
        if entry_price <= 0:
            # Get from current market data if not in signal
            hourly_data = self.data_loader.load_hourly_features(
                execution_start - timedelta(hours=1), 
                execution_start
            )
            if not hourly_data.empty:
                entry_price = hourly_data.iloc[-1]['close']
        
        if entry_price <= 0:
            return None
        
        executed_trade = {
            'timestamp': execution_start,
            'direction': trade_signal['direction'],
            'entry_price': entry_price,
            'position_size': trade_signal['position_size'],
            'stop_loss': trade_signal.get('stop_loss'),
            'take_profit': trade_signal.get('take_profit'),
            'regime': trade_signal.get('regime'),
            'confidence': trade_signal.get('confidence', 0.5),
            'entry_reason': trade_signal.get('entry_reason', 'regime_signal'),
            # Simplified execution metrics for backtesting
            'signal_price': entry_price,
            'price_improvement': 0.0,
            'execution_delay_seconds': 0,
            'execution_quality_score': 100.0
        }
        
        self.logger.debug(
            f"Execution (simplified): {trade_signal['direction']} @ "
            f"{entry_price:.2f} at {execution_start}"
        )
        
        return executed_trade
    
    def _evaluate_position_exit(self, current_time: datetime) -> Optional[Dict[str, Any]]:
        """
        Evaluate if current position should be exited.
        
        Implements proper exit logic that was missing from modern system.
        Uses same exit conditions as legacy TF-v3 strategy.
        
        Args:
            current_time: Current evaluation time
            
        Returns:
            Exit signal dict or None
        """
        if not self.portfolio.position:
            return None
        
        position = self.portfolio.position
        entry_time = datetime.fromtimestamp(position['entry_time'])
        entry_price = position['entry']
        direction = position['type']
        
        # Load current market data
        lookback_start = current_time - timedelta(hours=2)
        hourly_data = self.data_loader.load_hourly_features(lookback_start, current_time)
        
        if hourly_data.empty:
            return None
        
        # Get current price from latest completed bar
        completed_bars = hourly_data[hourly_data.index < current_time]
        if completed_bars.empty:
            return None
        
        current_price = completed_bars.iloc[-1]['close']
        
        # Calculate current PnL
        if direction == 'long':
            pnl_pct = (current_price - entry_price) / entry_price
        else:  # short
            pnl_pct = (entry_price - current_price) / entry_price
        
        # Exit conditions (matching legacy TF-v3)
        max_hold_hours = getattr(self.config.tf_v3, 'max_trade_life_h', 24)
        hours_held = (current_time - entry_time).total_seconds() / 3600
        
        # 1. Time-based exit
        if hours_held >= max_hold_hours:
            return {
                'reason': 'time_limit',
                'exit_price': current_price,
                'pnl_pct': pnl_pct,
                'hours_held': hours_held
            }
        
        # 2. Stop loss (simple 2% for now, should use ATR)
        stop_loss_pct = 0.02
        if pnl_pct < -stop_loss_pct:
            return {
                'reason': 'stop_loss',
                'exit_price': current_price,
                'pnl_pct': pnl_pct,
                'hours_held': hours_held
            }
        
        # 3. Take profit (simple 4% for now, should use ATR)
        take_profit_pct = 0.04
        if pnl_pct > take_profit_pct:
            return {
                'reason': 'take_profit',
                'exit_price': current_price,
                'pnl_pct': pnl_pct,
                'hours_held': hours_held
            }
        
        # 4. Regime change exit (if regime no longer supports position)
        current_regime = self.regime_manager.get_current_state()
        if current_regime and getattr(current_regime, 'state', None) in ['CHOP', 'High_Volatility_Chop']:
            return {
                'reason': 'regime_change',
                'exit_price': current_price,
                'pnl_pct': pnl_pct,
                'hours_held': hours_held
            }
        
        return None
    
    def _handle_position_entry(self, trade_signal: Dict[str, Any], timestamp: datetime) -> None:
        """
        Handle position entry through portfolio system.
        
        Integrates with legacy Portfolio class for proper position tracking.
        
        Args:
            trade_signal: Trade execution details
            timestamp: Entry timestamp
        """
        # Create position dict for portfolio (matching legacy field names)
        position_dict = {
            'timestamp': timestamp.isoformat(),
            'type': trade_signal['direction'],  # Portfolio expects 'type' not 'direction'
            'entry': trade_signal['entry_price'],  # Portfolio expects 'entry' not 'entry_price'
            'size': trade_signal['position_size'],  # Portfolio expects 'size' not 'position_size'
            'entry_time': timestamp.timestamp(),
            'leverage': 1.0,  # Default leverage
            'strategy': 'tf_v3_modern',
            'stop_loss': trade_signal.get('stop_loss'),
            'take_profit': trade_signal.get('take_profit'),
            'regime': trade_signal.get('regime'),
            'confidence': trade_signal.get('confidence', 0.5),
            'entry_reason': trade_signal.get('entry_reason', 'strategy_signal')
        }
        
        # Handle entry through portfolio (with position checking)
        self.portfolio.handle_entry(
            position_dict=position_dict,
            fill_price=trade_signal['entry_price'],
            filled_size=trade_signal['position_size'],
            slippage_pnl=0.0,  # Simplified for backtesting
            timestamp_unix=timestamp.timestamp()
        )
        
        self.logger.debug(
            f"Position OPENED: {trade_signal['direction']} @ {trade_signal['entry_price']:.2f} "
            f"(size: {trade_signal['position_size']:.2%})"
        )
    
    def _handle_position_exit(self, exit_result: Dict[str, Any], timestamp: datetime) -> None:
        """
        Handle position exit through portfolio system.
        
        Properly closes position and tracks PnL.
        
        Args:
            exit_result: Exit details from _evaluate_position_exit
            timestamp: Exit timestamp
        """
        if not self.portfolio.position:
            self.logger.warning("Attempted to exit position but no position exists")
            return
        
        # Store position details before exit
        position_data = {
            'entry_time': datetime.fromtimestamp(self.portfolio.position['entry_time']),
            'exit_time': timestamp,
            'direction': self.portfolio.position['type'],
            'entry_price': self.portfolio.position['entry'],
            'exit_price': exit_result['exit_price'],
            'position_size': self.portfolio.position['size'],
            'leverage': self.portfolio.position.get('leverage', 1.0),
            'exit_reason': exit_result['reason']
        }
        
        # Handle exit through portfolio
        self.portfolio.handle_exit(
            fill_price=exit_result['exit_price'],
            exit_reason=exit_result['reason'],
            slippage_pnl=0.0,  # Simplified for backtesting
            current_timestamp_unix=timestamp.timestamp()
        )
        
        # Calculate actual P&L
        if position_data['direction'] == 'long':
            pnl_pct = (position_data['exit_price'] - position_data['entry_price']) / position_data['entry_price']
        else:  # short
            pnl_pct = (position_data['entry_price'] - position_data['exit_price']) / position_data['entry_price']
        
        # Account for fees (same as legacy: 0.1% per side)
        fee_rate = 0.001  # 0.1%
        pnl_pct -= 2 * fee_rate  # Entry + exit fees
        
        # Add return to position data
        position_data['return'] = pnl_pct
        position_data['pnl'] = pnl_pct * position_data['position_size']
        
        # Move from active trades to completed trades
        # Find matching trade in self.trades
        for i, trade in enumerate(self.trades):
            if (abs(trade['entry_price'] - position_data['entry_price']) < 0.01 and
                trade['direction'] == position_data['direction']):
                # Update trade with exit info
                trade.update({
                    'exit_price': position_data['exit_price'],
                    'exit_time': position_data['exit_time'],
                    'exit_reason': position_data['exit_reason'],
                    'return': position_data['return'],
                    'pnl': position_data['pnl']
                })
                # Move to completed trades
                self.completed_trades.append(trade)
                self.trades.pop(i)
                break
        
        self.logger.debug(
            f"Position CLOSED: {exit_result['reason']} @ {exit_result['exit_price']:.2f} "
            f"(PnL: {pnl_pct:.2%}, held: {exit_result['hours_held']:.1f}h)"
        )
    
    def _calculate_performance_metrics(self) -> Dict[str, float]:
        """
        Calculate final performance metrics using actual completed trades.
        
        Returns:
            Performance metrics dictionary
        """
        # Use completed trades for metrics (trades with actual exits)
        all_trades = self.completed_trades.copy()
        
        # Add any open trades (shouldn't have any at end of backtest)
        if self.trades:
            self.logger.warning(f"{len(self.trades)} trades still open at end of backtest")
            all_trades.extend(self.trades)
        
        if not all_trades:
            return {
                'total_trades': 0,
                'total_return': 0.0,
                'win_rate': 0.0,
                'avg_trade_return': 0.0,
                'best_trade': 0.0,
                'worst_trade': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0
            }
        
        # Calculate metrics from actual P&L
        total_return = 0.0
        wins = 0
        returns = []
        
        for trade in all_trades:
            if 'return' in trade:  # Only completed trades have returns
                trade_return = trade['return']
                returns.append(trade_return)
                total_return += trade_return  # Simple sum of returns
                if trade_return > 0:
                    wins += 1
        
        # Calculate aggregate metrics
        num_completed = len(returns)
        
        metrics = {
            'total_trades': len(all_trades),
            'completed_trades': num_completed,
            'total_return': total_return,
            'win_rate': wins / num_completed if num_completed > 0 else 0.0,
            'avg_trade_return': sum(returns) / num_completed if num_completed > 0 else 0.0,
            'best_trade': max(returns) if returns else 0.0,
            'worst_trade': min(returns) if returns else 0.0,
            'sharpe_ratio': 0.0,  # TODO: Implement proper Sharpe calculation
            'max_drawdown': 0.0   # TODO: Implement drawdown calculation
        }
        
        # Log warning if many trades uncompleted
        if num_completed < len(all_trades):
            uncompleted = len(all_trades) - num_completed
            self.logger.warning(
                f"{uncompleted} trades ({uncompleted/len(all_trades)*100:.1f}%) "
                f"did not complete during backtest"
            )
        
        return metrics
    
    def _check_system_integrity(self) -> Dict[str, Any]:
        """
        Check for any legacy system fallbacks or violations.
        
        Returns:
            Dictionary with integrity check results
        """
        integrity = {
            'is_pure_modern': True,
            'legacy_imports': [],
            'violations': [],
            'warnings': []
        }
        
        # Check strategy type
        strategy_name = self.strategy.get_strategy_name()
        if 'modern' not in strategy_name.lower():
            integrity['is_pure_modern'] = False
            integrity['violations'].append(f"Non-modern strategy: {strategy_name}")
        
        # Check detector type
        if hasattr(self, 'regime_detector') and self.regime_detector is not None:
            detector_class = self.regime_detector.__class__.__name__
            if 'continuous' not in detector_class.lower() and 'modern' not in detector_class.lower():
                integrity['is_pure_modern'] = False
                integrity['violations'].append(f"Legacy detector: {detector_class}")
        
        # Check for legacy imports in current modules
        import sys
        legacy_patterns = [
            'hyperliquid_bot.strategies.tf_v3',
            'hyperliquid_bot.strategies.tf_v2',
            'hyperliquid_bot.backtester.backtester',
            'hyperliquid_bot.data.handler',
        ]
        
        for module_name, module in sys.modules.items():
            if module and hasattr(module, '__file__'):
                for pattern in legacy_patterns:
                    if pattern in str(module_name):
                        # Check if it's imported by modern modules
                        if 'modern' in str(getattr(module, '__file__', '')):
                            integrity['is_pure_modern'] = False
                            integrity['legacy_imports'].append(module_name)
        
        # Check data source
        if hasattr(self.data_loader, 'feature_base_path'):
            path_str = str(self.data_loader.feature_base_path)
            if 'raw2' in path_str:
                integrity['warnings'].append("Using legacy raw2 data path")
        
        return integrity
    
    def _log_system_integrity(self):
        """Log system integrity check results."""
        integrity = self._check_system_integrity()
        
        self.logger.info("\n" + "="*60)
        self.logger.info("SYSTEM INTEGRITY CHECK")
        self.logger.info("="*60)
        
        if integrity['is_pure_modern']:
            self.logger.info("✅ PURE MODERN SYSTEM - No legacy fallbacks detected")
        else:
            self.logger.warning("⚠️  LEGACY FALLBACK DETECTED!")
            
        if integrity['violations']:
            self.logger.error("❌ VIOLATIONS:")
            for violation in integrity['violations']:
                self.logger.error(f"   - {violation}")
                
        if integrity['legacy_imports']:
            self.logger.warning("⚠️  Legacy imports found:")
            for imp in integrity['legacy_imports']:
                self.logger.warning(f"   - {imp}")
                
        if integrity['warnings']:
            self.logger.warning("⚠️  Warnings:")
            for warning in integrity['warnings']:
                self.logger.warning(f"   - {warning}")
        
        self.logger.info("="*60)
    
    def prepare_live_trading_hooks(self):
        """
        TODO: Placeholder for live trading preparation.
        
        Future implementation will:
        - Initialize WebSocket connections
        - Set up timer callbacks
        - Connect to order management
        - Enable real-time monitoring
        """
        raise NotImplementedError("Live trading not yet implemented")