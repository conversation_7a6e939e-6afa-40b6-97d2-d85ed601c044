"""
Modern Component Registry
=========================
Isolated registry for modern system components.
This allows free experimentation without affecting legacy components.
"""

import logging
from typing import Type, Any, Optional

from ..core.registry import ComponentRegistry, get_registry
from ..core.interfaces import IRegimeDetector, IStrategy, IDataLoader


logger = logging.getLogger(__name__)


# Create isolated modern registry
_modern_registry = ComponentRegistry()


def get_modern_registry() -> ComponentRegistry:
    """Get the modern component registry."""
    return _modern_registry


# Modern-specific decorators
def modern_detector(name: str, version: str = "1.0", experimental: bool = True):
    """
    Decorator to register a modern regime detector.
    
    Args:
        name: Unique name for the detector
        version: Version string
        experimental: Whether this component is experimental
    """
    def decorator(cls: Type[IRegimeDetector]) -> Type[IRegimeDetector]:
        metadata = {
            'version': version,
            'experimental': experimental,
            'system': 'modern'
        }
        _modern_registry.register(IRegimeDetector, name, cls, metadata)
        
        # Also register in global registry with modern_ prefix
        global_name = f"modern_{name}"
        get_registry().register(IRegimeDetector, global_name, cls, metadata)
        
        return cls
    return decorator


# Alias for consistency
modern_regime_detector = modern_detector


def modern_strategy(name: str, version: str = "1.0", experimental: bool = True):
    """
    Decorator to register a modern strategy.
    
    Args:
        name: Unique name for the strategy
        version: Version string
        experimental: Whether this component is experimental
    """
    def decorator(cls: Type[IStrategy]) -> Type[IStrategy]:
        metadata = {
            'version': version,
            'experimental': experimental,
            'system': 'modern'
        }
        _modern_registry.register(IStrategy, name, cls, metadata)
        
        # Also register in global registry with modern_ prefix
        global_name = f"modern_{name}"
        get_registry().register(IStrategy, global_name, cls, metadata)
        
        return cls
    return decorator


def modern_data_loader(name: str, version: str = "1.0", experimental: bool = True, 
                      production_ready: bool = False):
    """
    Decorator to register a modern data loader.
    
    Args:
        name: Unique name for the data loader
        version: Version string
        experimental: Whether this component is experimental
        production_ready: Whether this component is production ready
    """
    def decorator(cls: Type[IDataLoader]) -> Type[IDataLoader]:
        metadata = {
            'version': version,
            'experimental': experimental,
            'production_ready': production_ready,
            'system': 'modern'
        }
        _modern_registry.register(IDataLoader, name, cls, metadata)
        
        # Also register in global registry with modern_ prefix
        global_name = f"modern_{name}"
        get_registry().register(IDataLoader, global_name, cls, metadata)
        
        return cls
    return decorator


def get_modern_component(interface_type: Type, name: str) -> Optional[Type]:
    """
    Get a component from the modern registry.
    
    Args:
        interface_type: The interface type
        name: The component name
        
    Returns:
        The component class or None if not found
    """
    try:
        return _modern_registry.get(interface_type, name)
    except KeyError:
        logger.warning(f"Modern component not found: {interface_type.__name__}.{name}")
        return None


def get_modern_detector(name: str, **kwargs):
    """
    Get a modern regime detector by name and instantiate it.
    
    Args:
        name: Detector name (e.g., 'continuous_modern')
        **kwargs: Arguments to pass to detector constructor
        
    Returns:
        Instantiated detector or None
    """
    from ..core.interfaces import IRegimeDetector
    
    detector_class = get_modern_component(IRegimeDetector, name)
    if detector_class:
        return detector_class(**kwargs)
    return None


def get_modern_strategy(name: str, **kwargs):
    """
    Get a modern strategy by name and instantiate it.
    
    Args:
        name: Strategy name (e.g., 'tf_v3_modern')
        **kwargs: Arguments to pass to strategy constructor
        
    Returns:
        Instantiated strategy or None
    """
    from ..core.interfaces import IStrategy
    
    strategy_class = get_modern_component(IStrategy, name)
    if strategy_class:
        return strategy_class(**kwargs)
    return None