"""
Modern Continuous GMS Detector
=================================
This is the modern regime detection system with continuous monitoring,
adaptive thresholds, and sophisticated state detection.

Key Features:
- 60-second detection cadence
- Adaptive thresholds based on market conditions  
- Risk suppression based on position size and PnL
- 8-state granular classification
- Enhanced signal processing

FROZEN: This represents the modern detector implementation.
"""

import logging
import time
from datetime import datetime
from typing import Dict, Optional, Union, Any
import numpy as np
import pandas as pd

from ..core.interfaces import IRegimeDetector
from .registry import modern_detector
from ..config.settings import Config
from ..utils.state_mapping import (
    GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND,
    GMS_STATE_HIGH_VOL_RANGE, GMS_STATE_LOW_VOL_RANGE,
    GMS_STATE_UNCERTAIN, GMS_STATE_WEAK_BEAR_TREND,
    GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_TIGHT_SPREAD,
    GMS_STATE_UNKNOWN, get_valid_gms_states,
    map_gms_state
)


@modern_detector("continuous_gms", version="1.0", experimental=True)
class ModernContinuousGMSDetector(IRegimeDetector):
    """
    Modern implementation of the Continuous GMS Detector.
    
    This detector uses:
    - Lower threshold values (2.5/0.5 for momentum vs legacy 100.0/50.0)
    - Features from features_1s/ directory
    - 60-second detection cadence
    - Adaptive thresholds
    - Risk suppression logic
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Core configuration
        self.cfg_regime = config.regime
        self.cfg_micro = config.microstructure
        self.cfg_indicators = config.indicators
        self.cfg_portfolio = getattr(config, 'portfolio', None)
        
        # Detection cadence
        self.cadence_sec = 60  # Modern: 60 seconds vs legacy 3600 seconds
        
        # Modern thresholds (different scale!)
        self.vol_high_thresh = 0.015  # 1.5% ATR
        self.vol_low_thresh = 0.005   # 0.5% ATR
        self.mom_strong_thresh = 2.5   # Strong momentum
        self.mom_weak_thresh = 0.5     # Weak momentum
        self.spread_std_high_thresh = 0.0005
        self.spread_mean_low_thresh = 0.0001
        
        # OBI confirmation thresholds
        self.obi_strong_confirm_thresh = 0.15  # Tighter than legacy
        self.obi_weak_confirm_thresh = 0.08    # More selective
        self.depth_levels = self.cfg_micro.depth_levels
        
        # Enhanced features
        self.use_adx_confirmation = True
        self.use_funding_confirmation = True
        self.adx_threshold = getattr(self.cfg_indicators, 'adx_threshold', 30.0)
        self.funding_extreme_pos_thresh = 0.001
        self.funding_extreme_neg_thresh = -0.001
        
        # Risk suppression
        self.risk_suppressed_notional_frac = 0.25
        self.risk_suppressed_pnl_atr_mult = 1.5
        self.risk_suppressed = False
        
        # ATR columns (modern uses 1-second features)
        self.ATR_COL = 'atr_14_sec'
        self.ATR_PCT_COL = 'atr_percent_sec'
        
        # State tracking
        self.current_state = GMS_STATE_UNCERTAIN
        self.last_state_change_ts = time.time()
        self.last_update_ts = 0
        self.regime_start_time = time.time()
        self.regime_confidence = 1.0
        
        # Adaptive thresholds (optional)
        self.adaptive_vol_threshold = None
        self.adaptive_mom_threshold = None
        self._init_adaptive_thresholds()
        
        self.logger.info(
            f"Modern Continuous GMS Detector initialized:\n"
            f"  - Cadence: {self.cadence_sec}s\n"
            f"  - Vol thresholds: {self.vol_low_thresh:.3f} / {self.vol_high_thresh:.3f}\n"
            f"  - Mom thresholds: {self.mom_weak_thresh:.1f} / {self.mom_strong_thresh:.1f}\n"
            f"  - Depth levels: {self.depth_levels}\n"
            f"  - Adaptive: {self.adaptive_vol_threshold is not None}"
        )
    
    def _init_adaptive_thresholds(self):
        """Initialize adaptive threshold components if configured."""
        # CRITICAL: Adaptive thresholds DISABLED due to 655s performance bottleneck
        # TODO: Re-enable after optimization
        use_adaptive = False  # Hardcoded to False
        
        # Original check (disabled for now)
        # use_adaptive = (hasattr(self.cfg_regime, 'gms_use_adaptive_thresholds') and 
        #                self.cfg_regime.gms_use_adaptive_thresholds)
        
        if use_adaptive:
            try:
                from .utils.adaptive_threshold import AdaptiveThreshold
                
                # Default percentile settings
                vol_low_pct = 0.15
                vol_high_pct = 0.50
                mom_low_pct = 0.15
                mom_high_pct = 0.50
                window_len = 86400  # 24 hours in seconds
                
                self.adaptive_vol_threshold = AdaptiveThreshold(
                    vol_low_pct, vol_high_pct, window_len
                )
                self.adaptive_mom_threshold = AdaptiveThreshold(
                    mom_low_pct, mom_high_pct, window_len
                )
                
                self.min_history_rows = 10000
                self.logger.info("Adaptive thresholds initialized")
                
            except ImportError:
                self.logger.warning("AdaptiveThreshold not available, using fixed thresholds")
    
    @property
    def required_signals(self) -> list[str]:
        """Return list of required signals for modern detector."""
        signals = [
            'timestamp',
            self.ATR_PCT_COL,           # Modern ATR from 1s features
            'ma_slope_ema_30s',         # Modern momentum calculation
            f'obi_smoothed_{self.depth_levels}',
            'spread_mean',
            'spread_std',
            'close',
            self.ATR_COL,
            'unrealised_pnl',           # For risk suppression
        ]
        
        # Optional confirmations
        if self.use_adx_confirmation:
            signals.append('adx')
        if self.use_funding_confirmation:
            signals.append('funding_rate')
        
        # Add fallback columns
        signals.extend(['atr_percent', 'ma_slope', 'atr'])  # Legacy fallbacks
        
        return list(set(signals))
    
    def detect_regime(self, signals: Dict[str, Any], timestamp: Optional[datetime] = None) -> str:
        """
        Detect the current market regime from signals.
        
        Args:
            signals: Dictionary of market signals
            timestamp: Optional timestamp (not used in modern detector)
            
        Returns:
            str: Detected regime state
        """
        # Update detector state
        self._update(signals)
        
        # Return current state
        return self.current_state
    
    def get_raw_state(self, features: Dict[str, Any], timestamp: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Get raw state with additional metadata.
        
        Args:
            features: Dictionary of market features
            timestamp: Optional timestamp
            
        Returns:
            Dict with state, risk_suppressed flag, and confidence
        """
        # Update with features
        self._update(features)
        
        # Calculate regime duration
        regime_duration_min = (time.time() - self.regime_start_time) / 60.0
        
        return {
            "state": self.current_state,
            "risk_suppressed": self.risk_suppressed,
            "regime_confidence": self.regime_confidence,
            "regime_duration_minutes": regime_duration_min
        }
    
    def _update(self, signals: Dict[str, Any]) -> None:
        """Update detector with new signals."""
        current_time = time.time()
        
        # Check cadence
        if current_time - self.last_update_ts < self.cadence_sec:
            return
        
        self.last_update_ts = current_time
        
        try:
            # Determine new state
            new_state = self._determine_state(signals)
            
            # Update state tracking
            if new_state != self.current_state:
                self.last_state_change_ts = current_time
                self.logger.info(f"Modern GMS state changed: {self.current_state} -> {new_state}")
                
                # Update confidence
                self._update_regime_confidence(new_state, signals)
                self.regime_start_time = current_time
            
            self.current_state = new_state
            
            # Calculate risk suppression
            self.risk_suppressed = self._calculate_risk_suppressed(signals)
            
        except Exception as e:
            self.logger.error(f"Error updating modern GMS state: {e}", exc_info=True)
    
    def _determine_state(self, signals: Dict[str, Any]) -> str:
        """
        Determine market state using modern logic.
        
        Key differences from legacy:
        - Uses ma_slope_ema_30s for momentum
        - Lower threshold values
        - Enhanced confirmation logic
        - Adaptive threshold support
        """
        if not self.cfg_regime.use_filter:
            return "Filter_Off"
        
        # Extract core signals with modern columns
        atr_pct = signals.get(self.ATR_PCT_COL, np.nan)
        if pd.isna(atr_pct):
            atr_pct = signals.get('atr_percent', np.nan)  # Fallback
        
        # Modern momentum calculation
        ma_slope = signals.get('ma_slope_ema_30s', np.nan)
        if pd.isna(ma_slope):
            ma_slope = signals.get('ma_slope', np.nan)  # Fallback
        
        # OBI and spread signals
        obi_smooth = signals.get(f'obi_smoothed_{self.depth_levels}', np.nan)
        spread_std = signals.get('spread_std', np.nan)
        spread_mean = signals.get('spread_mean', np.nan)
        
        # Check for missing signals
        if pd.isna(atr_pct) or pd.isna(ma_slope) or pd.isna(obi_smooth):
            self.logger.warning(f"Missing core signals for modern GMS detection")
            return GMS_STATE_UNKNOWN
        
        # Determine OBI confirmation
        obi_condition = self._get_obi_condition(obi_smooth)
        
        # Check volatility with adaptive thresholds if available
        is_vol_high, is_vol_low = self._check_volatility(atr_pct)
        
        # Get momentum thresholds (adaptive if enabled)
        mom_weak_thresh = self.mom_weak_thresh
        mom_strong_thresh = self.mom_strong_thresh
        
        if self.adaptive_mom_threshold:
            low_thresh, high_thresh = self.adaptive_mom_threshold.update(abs(ma_slope))
            if low_thresh is not None and high_thresh is not None:
                mom_weak_thresh = low_thresh
                mom_strong_thresh = high_thresh
        
        # Determine potential regime
        potential_regime = GMS_STATE_UNCERTAIN
        
        # High volatility range check
        if is_vol_high:
            if abs(ma_slope) < mom_weak_thresh or spread_std >= self.spread_std_high_thresh:
                potential_regime = GMS_STATE_HIGH_VOL_RANGE
        
        # Low volatility range check
        elif is_vol_low:
            if abs(ma_slope) < mom_weak_thresh and spread_mean <= self.spread_mean_low_thresh:
                potential_regime = GMS_STATE_LOW_VOL_RANGE
        
        # Momentum-based regime detection
        if potential_regime == GMS_STATE_UNCERTAIN:
            is_bullish = ma_slope > 0
            is_strong_mom = abs(ma_slope) >= mom_strong_thresh
            is_weak_mom = abs(ma_slope) >= mom_weak_thresh
            
            # Get confirmations
            adx_confirms = self._check_adx_confirmation(signals)
            funding_confirms = self._check_funding_confirmation(signals, is_bullish)
            
            if is_strong_mom and obi_condition == 'STRONG' and adx_confirms and funding_confirms:
                potential_regime = (GMS_STATE_STRONG_BULL_TREND if is_bullish 
                                   else GMS_STATE_STRONG_BEAR_TREND)
            elif is_weak_mom and obi_condition in ['STRONG', 'WEAK']:
                potential_regime = (GMS_STATE_WEAK_BULL_TREND if is_bullish 
                                   else GMS_STATE_WEAK_BEAR_TREND)
        
        # Validate output state
        if potential_regime not in get_valid_gms_states():
            self.logger.error(f"Invalid state produced: {potential_regime}")
            return GMS_STATE_UNKNOWN
        
        self.logger.debug(
            f"Modern GMS: Vol={atr_pct:.4f}, Mom={ma_slope:.2f}, "
            f"OBI={obi_condition} => {potential_regime}"
        )
        
        return potential_regime
    
    def _get_obi_condition(self, obi_smooth: float) -> str:
        """Determine OBI confirmation condition."""
        if pd.isna(obi_smooth):
            return 'NONE'
        
        if abs(obi_smooth) > self.obi_strong_confirm_thresh:
            return 'STRONG'
        elif abs(obi_smooth) > self.obi_weak_confirm_thresh:
            return 'WEAK'
        else:
            return 'NONE'
    
    def _check_volatility(self, atr_pct: float) -> tuple[bool, bool]:
        """Check volatility conditions with adaptive threshold support."""
        if self.adaptive_vol_threshold:
            low_thresh, high_thresh = self.adaptive_vol_threshold.update(atr_pct)
            if low_thresh is not None and high_thresh is not None:
                buffer_size = self.adaptive_vol_threshold.get_buffer_size()
                if buffer_size >= self.min_history_rows:
                    return atr_pct >= high_thresh, atr_pct <= low_thresh
        
        # Fallback to fixed thresholds
        return atr_pct >= self.vol_high_thresh, atr_pct <= self.vol_low_thresh
    
    def _check_adx_confirmation(self, signals: Dict[str, Any]) -> bool:
        """Check ADX confirmation."""
        if not self.use_adx_confirmation:
            return True
        
        adx = signals.get('adx', np.nan)
        if pd.isna(adx):
            return True  # Allow if ADX not available
        
        return adx >= self.adx_threshold
    
    def _check_funding_confirmation(self, signals: Dict[str, Any], is_bullish: bool) -> bool:
        """Check funding rate confirmation."""
        if not self.use_funding_confirmation:
            return True
        
        funding_rate = signals.get('funding_rate', np.nan)
        if pd.isna(funding_rate):
            return True  # Allow if funding not available
        
        if is_bullish:
            return funding_rate < self.funding_extreme_pos_thresh
        else:
            return funding_rate > self.funding_extreme_neg_thresh
    
    def _calculate_risk_suppressed(self, signals: Dict[str, Any]) -> bool:
        """Calculate risk suppression based on position and PnL."""
        try:
            close = float(signals.get('close', 0.0))
            unrealised_pnl = float(signals.get('unrealised_pnl', 0.0))
            atr = float(signals.get(self.ATR_COL, 0.0))
            
            if close <= 0 or pd.isna(close):
                return False
            
            # Calculate thresholds
            if self.cfg_portfolio:
                notional_threshold = (
                    self.cfg_portfolio.initial_balance *
                    self.cfg_portfolio.max_leverage *
                    self.risk_suppressed_notional_frac
                )
            else:
                notional_threshold = 10000.0 * 10.0 * self.risk_suppressed_notional_frac
            
            pnl_threshold = atr * self.risk_suppressed_pnl_atr_mult
            
            # Check conditions
            notional_exceeded = close >= notional_threshold
            pnl_exceeded = abs(unrealised_pnl) >= pnl_threshold if unrealised_pnl != 0 else False
            
            risk_suppressed = notional_exceeded or pnl_exceeded
            
            if risk_suppressed:
                reasons = []
                if notional_exceeded:
                    reasons.append(f"notional ({close:.2f}) >= {notional_threshold:.2f}")
                if pnl_exceeded:
                    reasons.append(f"pnl ({unrealised_pnl:.2f}) >= {pnl_threshold:.2f}")
                self.logger.info(f"Risk suppressed: {', '.join(reasons)}")
            
            return risk_suppressed
            
        except Exception as e:
            self.logger.error(f"Error calculating risk suppression: {e}")
            return False
    
    def _update_regime_confidence(self, new_state: str, signals: Dict[str, Any]) -> None:
        """Update regime confidence based on transition."""
        # Base confidence on transition type
        if self.current_state in [GMS_STATE_UNCERTAIN, GMS_STATE_UNKNOWN]:
            self.regime_confidence = 0.6
        else:
            # Natural progressions have higher confidence
            transition_confidence = {
                (GMS_STATE_WEAK_BULL_TREND, GMS_STATE_STRONG_BULL_TREND): 0.9,
                (GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND): 0.8,
                (GMS_STATE_WEAK_BEAR_TREND, GMS_STATE_STRONG_BEAR_TREND): 0.9,
                (GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_WEAK_BEAR_TREND): 0.8,
                # Reversals have lower confidence
                (GMS_STATE_STRONG_BULL_TREND, GMS_STATE_STRONG_BEAR_TREND): 0.5,
                (GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_STRONG_BULL_TREND): 0.5,
            }
            
            self.regime_confidence = transition_confidence.get(
                (self.current_state, new_state), 0.6
            )
        
        # Adjust for volatility
        atr_pct = signals.get(self.ATR_PCT_COL, 0.02)
        if atr_pct > 0.04:  # High volatility
            self.regime_confidence *= 0.8
        elif atr_pct < 0.01:  # Low volatility
            self.regime_confidence *= 0.9
        
        self.regime_confidence = max(0.3, min(1.0, self.regime_confidence))
    
    def get_allowed_states(self, strategy_type: str = "") -> list[str]:
        """Get list of allowed regime states."""
        # For trend following strategies, only allow BULL/BEAR
        if strategy_type == "trend_following":
            return ["BULL", "BEAR"]
        # Otherwise return all valid states
        return get_valid_gms_states()
    
    def get_confidence(self) -> float:
        """Get current regime confidence."""
        return self.regime_confidence
    
    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None) -> str:
        """
        Backward compatibility method for old API.
        
        The backtester expects get_regime but the interface uses detect_regime.
        This bridges the gap for modern detector.
        """
        return self.detect_regime(signals)