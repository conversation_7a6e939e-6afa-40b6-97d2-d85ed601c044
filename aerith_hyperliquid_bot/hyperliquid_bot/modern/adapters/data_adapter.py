"""
Modern Data Adapter
===================

This adapter provides clean transformation between raw features_1s data
and the expected format for modern system components. It handles:
- Field name mappings (e.g., obi_smoothed -> volume_imbalance)
- NaN handling with graceful defaults
- Data type conversions
- Signal enhancement and computation

Key design principles:
- No hardcoded values (all configurable)
- Explicit logging of transformations
- Graceful degradation for missing data
- Performance optimized for backtesting
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging
from dataclasses import dataclass

from ..contracts.data_schema import ModernDataContract, FieldDefinition


@dataclass
class AdapterConfig:
    """Configuration for data adapter behavior."""
    handle_missing_with_defaults: bool = True
    log_transformations: bool = True
    compute_derived_fields: bool = True
    nan_handling_strategy: str = 'interpolate'  # 'interpolate', 'forward_fill', 'default'
    
    # Thresholds for data quality warnings
    max_null_percentage: float = 5.0
    max_gap_seconds: int = 10
    
    # Performance settings
    cache_transformed_data: bool = True
    batch_size: int = 3600  # 1 hour of 1-second data


class ModernDataAdapter:
    """
    Transforms raw features_1s data to match modern system expectations.
    
    This adapter ensures clean separation between data sources and
    system components, allowing the modern system to work with data
    in its expected format regardless of the actual schema.
    """
    
    def __init__(self, config: Optional[AdapterConfig] = None):
        """
        Initialize the data adapter.
        
        Args:
            config: Adapter configuration (uses defaults if None)
        """
        self.config = config or AdapterConfig()
        self.contract = ModernDataContract(strict_mode=False)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Cache for transformed data
        self._transform_cache = {} if self.config.cache_transformed_data else None
        
        # Statistics tracking
        self.stats = {
            'total_rows_processed': 0,
            'null_values_handled': 0,
            'fields_mapped': 0,
            'derived_fields_computed': 0,
            'warnings_generated': 0
        }
    
    def adapt_features_dataframe(self, df: pd.DataFrame, validate: bool = True) -> pd.DataFrame:
        """
        Transform a features_1s dataframe to expected schema.
        
        Args:
            df: Raw features_1s dataframe
            validate: Whether to validate data quality
            
        Returns:
            Transformed dataframe with expected schema
        """
        self.logger.debug(f"Adapting dataframe with shape {df.shape}")
        
        # Create a copy to avoid modifying original
        df_adapted = df.copy()
        
        # Step 1: Validate input data quality
        if validate:
            validation_result = self._validate_input_quality(df_adapted)
            if validation_result['warnings']:
                for warning in validation_result['warnings']:
                    self.logger.warning(warning)
                    self.stats['warnings_generated'] += 1
        
        # Step 2: Apply field mappings
        df_adapted = self._apply_field_mappings(df_adapted)
        
        # Step 3: Handle missing and NaN values
        df_adapted = self._handle_missing_values(df_adapted)
        
        # Step 4: Compute derived fields if enabled
        if self.config.compute_derived_fields:
            df_adapted = self._compute_derived_fields(df_adapted)
        
        # Step 5: Ensure data types are correct
        df_adapted = self._ensure_correct_dtypes(df_adapted)
        
        # Step 6: Final validation
        if validate:
            final_validation = self.contract.validate_dataframe(df_adapted)
            if not final_validation['valid']:
                self.logger.warning(f"Final validation issues: {final_validation}")
        
        # Update statistics
        self.stats['total_rows_processed'] += len(df_adapted)
        
        return df_adapted
    
    def adapt_signals_dict(self, signals: Dict[str, Any], timestamp: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Transform a signals dictionary to expected format.
        
        This is used for real-time signal transformation during backtesting.
        
        Args:
            signals: Raw signals dictionary
            timestamp: Optional timestamp for context
            
        Returns:
            Transformed signals dictionary
        """
        adapted_signals = {}
        
        # First copy all original signals
        adapted_signals = signals.copy()
        
        # Apply field mappings - handle the key mapping case
        for expected_name, actual_name in self.contract.FIELD_MAPPINGS.items():
            if actual_name in signals and expected_name not in signals:
                adapted_signals[expected_name] = signals[actual_name]
        
        # Ensure all required fields are present
        for field_name, field_def in self.contract.FIELD_DEFINITIONS.items():
            if field_def.required and field_name not in adapted_signals:
                # Try to get from original signals with actual name
                actual_name = self.contract.get_actual_column_name(field_name)
                if actual_name in signals:
                    adapted_signals[field_name] = signals[actual_name]
                elif field_def.default_value is not None:
                    adapted_signals[field_name] = field_def.default_value
                    if self.config.log_transformations:
                        self.logger.debug(f"Using default value for {field_name}: {field_def.default_value}")
        
        # Handle NaN values
        for key, value in adapted_signals.items():
            if pd.isna(value):
                field_def = self.contract.FIELD_DEFINITIONS.get(key)
                if field_def and field_def.default_value is not None:
                    adapted_signals[key] = field_def.default_value
                    self.stats['null_values_handled'] += 1
        
        # Add timestamp if not present
        if 'timestamp' not in adapted_signals and timestamp:
            adapted_signals['timestamp'] = timestamp
        
        return adapted_signals
    
    def _apply_field_mappings(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply field name mappings to dataframe."""
        rename_map = {}
        
        # Build rename map based on contract mappings
        for expected_name, actual_name in self.contract.FIELD_MAPPINGS.items():
            if actual_name in df.columns and expected_name not in df.columns:
                rename_map[actual_name] = expected_name
                self.stats['fields_mapped'] += 1
        
        if rename_map:
            df = df.rename(columns=rename_map)
            if self.config.log_transformations:
                self.logger.info(f"Applied field mappings: {rename_map}")
        
        return df
    
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """Handle missing and NaN values in dataframe."""
        for field_name, field_def in self.contract.FIELD_DEFINITIONS.items():
            if field_name not in df.columns:
                continue
            
            # Count nulls
            null_count = df[field_name].isna().sum()
            if null_count > 0:
                null_percentage = (null_count / len(df)) * 100
                
                # Decide handling strategy
                if field_def.allow_null:
                    continue  # Leave nulls as-is
                
                if self.config.nan_handling_strategy == 'interpolate':
                    # Interpolate numeric fields
                    if pd.api.types.is_numeric_dtype(df[field_name]):
                        df[field_name] = df[field_name].interpolate(method='linear', limit_direction='both')
                        remaining_nulls = df[field_name].isna().sum()
                        if remaining_nulls > 0 and field_def.default_value is not None:
                            df[field_name].fillna(field_def.default_value, inplace=True)
                
                elif self.config.nan_handling_strategy == 'forward_fill':
                    df[field_name] = df[field_name].fillna(method='ffill')
                    # Handle any remaining nulls at the beginning
                    df[field_name] = df[field_name].fillna(method='bfill')
                
                elif self.config.nan_handling_strategy == 'default':
                    if field_def.default_value is not None:
                        df[field_name].fillna(field_def.default_value, inplace=True)
                
                # Log if significant nulls were handled
                if null_percentage > self.config.max_null_percentage:
                    self.logger.warning(
                        f"Field '{field_name}' had {null_percentage:.2f}% null values"
                    )
                
                self.stats['null_values_handled'] += null_count
        
        return df
    
    def _compute_derived_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """Compute any missing derived fields."""
        # Example: If volume_imbalance is still missing after mapping, compute it
        if 'volume_imbalance' not in df.columns:
            # Check for alternative OBI fields
            if 'raw_obi_5' in df.columns:
                df['volume_imbalance'] = df['raw_obi_5'].rolling(window=5, min_periods=1).mean()
                self.stats['derived_fields_computed'] += 1
                self.logger.info("Computed volume_imbalance from raw_obi_5")
            elif 'raw_obi_20' in df.columns:
                df['volume_imbalance'] = df['raw_obi_20'].rolling(window=5, min_periods=1).mean()
                self.stats['derived_fields_computed'] += 1
                self.logger.info("Computed volume_imbalance from raw_obi_20")
        
        # Compute spread if missing
        if 'spread' not in df.columns and all(col in df.columns for col in ['best_ask', 'best_bid']):
            df['spread'] = df['best_ask'] - df['best_bid']
            self.stats['derived_fields_computed'] += 1
        
        # Compute relative spread if missing
        if 'spread_relative' not in df.columns and all(col in df.columns for col in ['spread', 'mid_price']):
            df['spread_relative'] = df['spread'] / df['mid_price']
            self.stats['derived_fields_computed'] += 1
        
        return df
    
    def _ensure_correct_dtypes(self, df: pd.DataFrame) -> pd.DataFrame:
        """Ensure all fields have correct data types."""
        for field_name, field_def in self.contract.FIELD_DEFINITIONS.items():
            if field_name not in df.columns:
                continue
            
            # Convert timestamps
            if field_def.dtype == datetime and df[field_name].dtype != 'datetime64[ns]':
                df[field_name] = pd.to_datetime(df[field_name])
            
            # Convert floats
            elif field_def.dtype == float and not pd.api.types.is_float_dtype(df[field_name]):
                df[field_name] = pd.to_numeric(df[field_name], errors='coerce')
        
        return df
    
    def _validate_input_quality(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """Validate input data quality and return warnings."""
        warnings = []
        
        # Check for timestamp gaps
        if 'timestamp' in df.columns:
            df_sorted = df.sort_values('timestamp')
            time_diffs = df_sorted['timestamp'].diff()
            
            # Find gaps larger than threshold
            large_gaps = time_diffs[time_diffs > pd.Timedelta(seconds=self.config.max_gap_seconds)]
            if len(large_gaps) > 0:
                warnings.append(
                    f"Found {len(large_gaps)} timestamp gaps larger than {self.config.max_gap_seconds}s"
                )
        
        # Check for suspicious values
        if 'close' in df.columns:
            # Check for zero prices
            zero_prices = (df['close'] == 0).sum()
            if zero_prices > 0:
                warnings.append(f"Found {zero_prices} zero price values")
            
            # Check for extreme price jumps
            price_changes = df['close'].pct_change().abs()
            extreme_changes = (price_changes > 0.1).sum()  # >10% changes
            if extreme_changes > 0:
                warnings.append(f"Found {extreme_changes} extreme price changes (>10%)")
        
        return {'warnings': warnings}
    
    def get_adapter_statistics(self) -> Dict[str, Any]:
        """Get statistics about adapter operations."""
        return {
            **self.stats,
            'config': {
                'nan_handling': self.config.nan_handling_strategy,
                'compute_derived': self.config.compute_derived_fields,
                'cache_enabled': self.config.cache_transformed_data
            }
        }
    
    def reset_statistics(self):
        """Reset adapter statistics."""
        self.stats = {
            'total_rows_processed': 0,
            'null_values_handled': 0,
            'fields_mapped': 0,
            'derived_fields_computed': 0,
            'warnings_generated': 0
        }
        if self._transform_cache is not None:
            self._transform_cache.clear()