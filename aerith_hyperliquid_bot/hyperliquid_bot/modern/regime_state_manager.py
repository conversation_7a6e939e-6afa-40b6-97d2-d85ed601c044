"""
Modern Regime State Manager
===========================

This module manages regime state history for the modern system with:
- 60-second regime state updates
- State history for trading decisions
- Support for both backtesting and live trading
- Zero look-ahead bias

Key Features:
- Maintains rolling window of regime states
- Provides state statistics for strategy
- Handles state transitions
- Thread-safe for live trading

CRITICAL: All state updates must respect time boundaries
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from collections import deque, defaultdict
import pandas as pd
import numpy as np
from threading import Lock
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class RegimeState(Enum):
    """Regime states with clear definitions."""
    BULL = "BULL"
    BEAR = "BEAR"
    CHOP = "CHOP"
    BULL_VOLATILE = "BULL_VOLATILE"
    BEAR_VOLATILE = "BEAR_VOLATILE"
    WEAK_BULL = "WEAK_BULL"
    WEAK_BEAR = "WEAK_BEAR"
    NEUTRAL = "NEUTRAL"
    
    @classmethod
    def is_bullish(cls, state: str) -> bool:
        """Check if state is bullish."""
        return state in [cls.BULL.value, cls.WEAK_BULL.value, cls.BULL_VOLATILE.value]
    
    @classmethod
    def is_bearish(cls, state: str) -> bool:
        """Check if state is bearish."""
        return state in [cls.BEAR.value, cls.WEAK_BEAR.value, cls.BEAR_VOLATILE.value]
    
    @classmethod
    def is_neutral(cls, state: str) -> bool:
        """Check if state is neutral/choppy."""
        return state in [cls.CHOP.value, cls.NEUTRAL.value]


@dataclass
class RegimeSnapshot:
    """Single regime state snapshot."""
    timestamp: datetime
    state: str
    confidence: float
    momentum: float
    volatility: float
    volume_imbalance: float
    features: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'timestamp': self.timestamp,
            'state': self.state,
            'confidence': self.confidence,
            'momentum': self.momentum,
            'volatility': self.volatility,
            'volume_imbalance': self.volume_imbalance,
            'features': self.features.copy()
        }


class RegimeStateManager:
    """
    Manages regime state history for both backtest and live trading.
    
    This manager maintains a rolling window of regime states and provides
    statistics for trading decisions. It ensures no look-ahead bias by
    strictly respecting time boundaries.
    """
    
    def __init__(
        self,
        history_hours: int = 24,
        state_update_seconds: int = 60,
        mode: str = "backtest"
    ):
        """
        Initialize regime state manager.
        
        Args:
            history_hours: Hours of history to maintain (default 24)
            state_update_seconds: Seconds between state updates (default 60)
            mode: Operating mode ("backtest" or "live")
        """
        self.history_hours = history_hours
        self.state_update_seconds = state_update_seconds
        self.mode = mode
        self.logger = logger
        
        # State storage
        self._states: deque = deque(maxlen=history_hours * 3600 // state_update_seconds)
        self._state_transitions: List[Tuple[datetime, str, str]] = []
        
        # Statistics tracking
        self._state_counts = defaultdict(int)
        self._state_durations = defaultdict(timedelta)
        self._last_state_time = {}
        
        # Thread safety for live mode
        self._lock = Lock() if mode == "live" else None
        
        # Current state
        self._current_state: Optional[RegimeSnapshot] = None
        self._last_update_time: Optional[datetime] = None
        
        self.logger.info(
            f"Regime State Manager initialized:\n"
            f"  - Mode: {mode}\n"
            f"  - History: {history_hours} hours\n"
            f"  - Update interval: {state_update_seconds} seconds"
        )
    
    def update_state(
        self,
        timestamp: datetime,
        state: str,
        confidence: float,
        features: Dict[str, Any]
    ) -> bool:
        """
        Update regime state at given timestamp.
        
        Args:
            timestamp: Current timestamp
            state: Regime state (BULL, BEAR, etc.)
            confidence: State confidence [0-1]
            features: Raw features used for detection
            
        Returns:
            True if state was updated, False if skipped
        """
        # Validate timestamp
        if self._last_update_time and timestamp <= self._last_update_time:
            self.logger.warning(
                f"Ignoring out-of-order update: {timestamp} <= {self._last_update_time}"
            )
            return False
        
        # Extract key metrics from features
        momentum = features.get('momentum', 0.0)
        volatility = features.get('volatility', 0.0)
        volume_imbalance = features.get('volume_imbalance', 0.0)
        
        # Create snapshot
        snapshot = RegimeSnapshot(
            timestamp=timestamp,
            state=state,
            confidence=confidence,
            momentum=momentum,
            volatility=volatility,
            volume_imbalance=volume_imbalance,
            features=features.copy()
        )
        
        # Update with thread safety if needed
        if self._lock:
            with self._lock:
                self._update_internal(snapshot)
        else:
            self._update_internal(snapshot)
        
        return True
    
    def _update_internal(self, snapshot: RegimeSnapshot):
        """Internal state update logic."""
        # Track state transition
        if self._current_state and self._current_state.state != snapshot.state:
            self._state_transitions.append(
                (snapshot.timestamp, self._current_state.state, snapshot.state)
            )
            
            # Update duration tracking
            if self._current_state.state in self._last_state_time:
                duration = snapshot.timestamp - self._last_state_time[self._current_state.state]
                self._state_durations[self._current_state.state] += duration
        
        # Update tracking
        self._states.append(snapshot)
        self._state_counts[snapshot.state] += 1
        self._last_state_time[snapshot.state] = snapshot.timestamp
        self._current_state = snapshot
        self._last_update_time = snapshot.timestamp
        
        self.logger.debug(
            f"State updated: {snapshot.timestamp} -> {snapshot.state} "
            f"(confidence: {snapshot.confidence:.2f})"
        )
    
    def get_current_state(self) -> Optional[RegimeSnapshot]:
        """Get current regime state."""
        if self._lock:
            with self._lock:
                return self._current_state
        return self._current_state
    
    def get_state_at_time(self, timestamp: datetime) -> Optional[RegimeSnapshot]:
        """
        Get regime state at specific timestamp.
        
        CRITICAL: Only returns states from BEFORE timestamp to prevent look-ahead.
        
        Args:
            timestamp: Target timestamp
            
        Returns:
            Most recent state before timestamp, or None
        """
        if self._lock:
            with self._lock:
                return self._get_state_at_time_internal(timestamp)
        return self._get_state_at_time_internal(timestamp)
    
    def _get_state_at_time_internal(self, timestamp: datetime) -> Optional[RegimeSnapshot]:
        """Internal logic for getting state at time."""
        # Find most recent state before timestamp
        for state in reversed(self._states):
            if state.timestamp <= timestamp:
                return state
        return None
    
    def get_state_history(
        self,
        end_time: datetime,
        hours: Optional[int] = None
    ) -> List[RegimeSnapshot]:
        """
        Get regime state history up to end_time.
        
        Args:
            end_time: End timestamp (exclusive)
            hours: Hours of history to return (default: all available)
            
        Returns:
            List of regime snapshots in chronological order
        """
        if hours:
            start_time = end_time - timedelta(hours=hours)
        else:
            start_time = datetime.min
        
        if self._lock:
            with self._lock:
                return self._get_history_internal(start_time, end_time)
        return self._get_history_internal(start_time, end_time)
    
    def _get_history_internal(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> List[RegimeSnapshot]:
        """Internal logic for getting history."""
        history = []
        for state in self._states:
            if start_time <= state.timestamp < end_time:
                history.append(state)
        return history
    
    def get_state_statistics(
        self,
        end_time: datetime,
        hours: int = 24
    ) -> Dict[str, Any]:
        """
        Get regime state statistics for specified period.
        
        Args:
            end_time: End of analysis period
            hours: Hours to analyze
            
        Returns:
            Dictionary with state statistics
        """
        history = self.get_state_history(end_time, hours)
        
        if not history:
            return {
                'total_states': 0,
                'state_counts': {},
                'state_percentages': {},
                'transitions': 0,
                'dominant_state': None,
                'stability_score': 0.0
            }
        
        # Count states
        state_counts = defaultdict(int)
        for snapshot in history:
            state_counts[snapshot.state] += 1
        
        total_states = len(history)
        
        # Calculate percentages
        state_percentages = {
            state: count / total_states * 100
            for state, count in state_counts.items()
        }
        
        # Count transitions
        transitions = 0
        for i in range(1, len(history)):
            if history[i].state != history[i-1].state:
                transitions += 1
        
        # Find dominant state
        dominant_state = max(state_counts.items(), key=lambda x: x[1])[0]
        
        # Calculate stability score (fewer transitions = more stable)
        max_transitions = total_states - 1
        stability_score = 1.0 - (transitions / max_transitions if max_transitions > 0 else 0)
        
        return {
            'total_states': total_states,
            'state_counts': dict(state_counts),
            'state_percentages': state_percentages,
            'transitions': transitions,
            'dominant_state': dominant_state,
            'stability_score': stability_score,
            'average_confidence': np.mean([s.confidence for s in history]),
            'volatility_range': (
                min(s.volatility for s in history),
                max(s.volatility for s in history)
            )
        }
    
    def get_regime_features_for_strategy(
        self,
        timestamp: datetime,
        lookback_hours: int = 4
    ) -> Dict[str, Any]:
        """
        Get regime features for strategy evaluation.
        
        This provides aggregated regime information for the strategy to use
        in making trading decisions.
        
        Args:
            timestamp: Current timestamp
            lookback_hours: Hours to look back for features
            
        Returns:
            Dictionary of regime-based features
        """
        history = self.get_state_history(timestamp, lookback_hours)
        
        if not history:
            return self._get_default_features()
        
        # Get current state
        current_state = history[-1] if history else None
        
        # Count recent states
        recent_states = [s.state for s in history[-20:]]  # Last 20 minutes
        bullish_count = sum(1 for s in recent_states if RegimeState.is_bullish(s))
        bearish_count = sum(1 for s in recent_states if RegimeState.is_bearish(s))
        
        # Calculate momentum trend
        momentum_values = [s.momentum for s in history[-10:]]
        momentum_trend = np.polyfit(range(len(momentum_values)), momentum_values, 1)[0] if len(momentum_values) > 1 else 0
        
        # Get recent transitions
        recent_transitions = []
        for i in range(1, min(len(history), 10)):
            if history[-i].state != history[-i-1].state:
                recent_transitions.append(
                    (history[-i-1].state, history[-i].state)
                )
        
        features = {
            # Current state info
            'current_state': current_state.state if current_state else None,
            'current_confidence': current_state.confidence if current_state else 0.0,
            'current_momentum': current_state.momentum if current_state else 0.0,
            
            # Recent state distribution
            'recent_bullish_pct': bullish_count / len(recent_states) * 100 if recent_states else 0,
            'recent_bearish_pct': bearish_count / len(recent_states) * 100 if recent_states else 0,
            
            # Momentum analysis
            'momentum_trend': momentum_trend,
            'avg_momentum': np.mean([s.momentum for s in history]),
            'momentum_volatility': np.std([s.momentum for s in history]),
            
            # Volatility analysis
            'avg_volatility': np.mean([s.volatility for s in history]),
            'max_volatility': max(s.volatility for s in history),
            
            # Volume imbalance
            'avg_volume_imbalance': np.mean([s.volume_imbalance for s in history]),
            
            # Stability
            'recent_transitions': len(recent_transitions),
            'is_trending': bullish_count > 15 or bearish_count > 15,  # 75% in one direction
            
            # State persistence
            'state_persistence': self._calculate_state_persistence(history)
        }
        
        return features
    
    def _calculate_state_persistence(self, history: List[RegimeSnapshot]) -> float:
        """Calculate how persistent states are (0-1, higher = more persistent)."""
        if len(history) < 2:
            return 0.0
        
        same_state_count = 0
        for i in range(1, len(history)):
            if history[i].state == history[i-1].state:
                same_state_count += 1
        
        return same_state_count / (len(history) - 1)
    
    def _get_default_features(self) -> Dict[str, Any]:
        """Get default features when no history available."""
        return {
            'current_state': None,
            'current_confidence': 0.0,
            'current_momentum': 0.0,
            'recent_bullish_pct': 0.0,
            'recent_bearish_pct': 0.0,
            'momentum_trend': 0.0,
            'avg_momentum': 0.0,
            'momentum_volatility': 0.0,
            'avg_volatility': 0.0,
            'max_volatility': 0.0,
            'avg_volume_imbalance': 0.0,
            'recent_transitions': 0,
            'is_trending': False,
            'state_persistence': 0.0
        }
    
    def clear_history(self):
        """Clear all state history (useful for backtesting reset)."""
        if self._lock:
            with self._lock:
                self._clear_internal()
        else:
            self._clear_internal()
    
    def _clear_internal(self):
        """Internal clear logic."""
        self._states.clear()
        self._state_transitions.clear()
        self._state_counts.clear()
        self._state_durations.clear()
        self._last_state_time.clear()
        self._current_state = None
        self._last_update_time = None
        
        self.logger.info("Regime state history cleared")
    
    def get_summary(self) -> str:
        """Get human-readable summary of current state."""
        if not self._current_state:
            return "No regime state available"
        
        stats = self.get_state_statistics(
            self._current_state.timestamp,
            hours=1
        )
        
        return (
            f"Current State: {self._current_state.state} "
            f"(confidence: {self._current_state.confidence:.2f})\n"
            f"Last Update: {self._current_state.timestamp}\n"
            f"1hr Stats: {stats['transitions']} transitions, "
            f"stability: {stats['stability_score']:.2f}"
        )