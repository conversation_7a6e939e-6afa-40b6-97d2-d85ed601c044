"""
Modern Continuous Detector with 60s Updates
===========================================

This is a modernized version of ContinuousGMSDetector that:
- Updates regime states every 60 seconds
- Uses RegimeStateManager for state history
- Integrates with ModernDataAggregator for look-ahead prevention
- Supports both backtesting and live trading modes

Key Differences from Legacy:
- Cleaner separation of concerns
- No adaptive threshold priming (moved to separate phase)
- Uses regime state manager for history
- Simplified state determination logic
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Union, Any, List
import pandas as pd
import numpy as np
import time

from ..config.settings import Config
from .regime_state_manager import RegimeStateManager, RegimeState
from .registry import modern_regime_detector

# Import state constants
from ..utils.state_mapping import (
    GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND,
    GMS_STATE_HIGH_VOL_RANGE, GMS_STATE_LOW_VOL_RANGE,
    GMS_STATE_UNCERTAIN, GMS_STATE_WEAK_BEAR_TREND,
    GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_TIGHT_SPREAD,
    GMS_STATE_UNKNOWN
)


@modern_regime_detector("continuous_modern", version="2.0", experimental=True)
class ModernContinuousDetector:
    """
    Modern implementation of continuous regime detection.
    
    This detector:
    - Updates every 60 seconds (configurable)
    - Maintains state history via RegimeStateManager
    - Provides clean separation between backtest and live modes
    - Simplifies threshold logic (adaptive thresholds in separate phase)
    """
    
    def __init__(self, config: Config, mode: str = "backtest"):
        """
        Initialize modern continuous detector.
        
        Args:
            config: System configuration
            mode: Operating mode ("backtest" or "live")
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.mode = mode
        
        # Configuration sections
        self.cfg_regime = config.regime
        self.cfg_micro = config.microstructure
        self.cfg_indicators = config.indicators
        self.cfg_gms = getattr(config, 'gms', None)
        
        # Get detector settings
        detector_settings = self.cfg_regime.get_detector_settings('continuous_modern')
        operational_settings = self.cfg_regime.get_detector_operational_settings(
            'continuous_modern', self.cfg_gms
        )
        
        # Core parameters
        self.cadence_sec = operational_settings.get('cadence_sec', 60)
        self.depth_levels = self.cfg_micro.depth_levels
        
        # Thresholds (simplified - no adaptive in this phase)
        self.vol_high_thresh = detector_settings.get('gms_vol_high_thresh', 0.06)
        self.vol_low_thresh = detector_settings.get('gms_vol_low_thresh', 0.02)
        self.mom_strong_thresh = detector_settings.get('gms_mom_strong_thresh', 5.0)
        self.mom_weak_thresh = detector_settings.get('gms_mom_weak_thresh', 1.0)
        self.spread_std_high_thresh = detector_settings.get('gms_spread_std_high_thresh', 0.0005)
        self.spread_mean_low_thresh = detector_settings.get('gms_spread_mean_low_thresh', 0.0001)
        
        # OBI thresholds
        self.obi_strong_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_strong_confirm_thresh', 0.2)
        self.obi_weak_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_weak_confirm_thresh', 0.05)
        
        # Optional confirmations
        self.use_adx_confirmation = getattr(self.cfg_regime, 'gms_use_adx_confirmation', False)
        self.adx_threshold = getattr(self.cfg_indicators, 'adx_threshold', 30.0)
        self.use_funding_confirmation = getattr(self.cfg_regime, 'gms_use_funding_confirmation', False)
        self.funding_extreme_pos_thresh = getattr(self.cfg_regime, 'gms_funding_extreme_positive_thresh', 0.001)
        self.funding_extreme_neg_thresh = getattr(self.cfg_regime, 'gms_funding_extreme_negative_thresh', -0.001)
        
        # Risk suppression (disabled for backtest)
        self.risk_suppressed_notional_frac = operational_settings.get('risk_suppressed_notional_frac', 0.25)
        self.risk_suppressed_pnl_atr_mult = operational_settings.get('risk_suppressed_pnl_atr_mult', 1.5)
        self.disable_risk_suppression = (mode == "backtest")
        
        # Initialize regime state manager
        self.state_manager = RegimeStateManager(
            history_hours=24,
            state_update_seconds=self.cadence_sec,
            mode=mode
        )
        
        # Tracking
        self.last_update_time = None
        self.current_state = GMS_STATE_UNCERTAIN
        self.risk_suppressed = False
        
        self.logger.info(
            f"Modern Continuous Detector initialized:\n"
            f"  - Mode: {mode}\n"
            f"  - Cadence: {self.cadence_sec} seconds\n"
            f"  - Depth: {self.depth_levels}\n"
            f"  - Vol thresholds: {self.vol_low_thresh:.4f} / {self.vol_high_thresh:.4f}\n"
            f"  - Mom thresholds: {self.mom_weak_thresh:.2f} / {self.mom_strong_thresh:.2f}\n"
            f"  - Risk suppression: {'disabled' if self.disable_risk_suppression else 'enabled'}"
        )
    
    @property
    def required_signals(self) -> List[str]:
        """Get list of required signals."""
        signals = [
            'timestamp',
            'close', 'high', 'low', 'volume',
            'volume_imbalance',  # Modern field name
            'spread_mean', 'spread_std',
            'atr_percent_sec',   # 1-second ATR
            'ma_slope_ema_30s',  # EMA-based momentum
        ]
        
        # Optional signals
        if self.use_adx_confirmation:
            signals.append('adx')
        if self.use_funding_confirmation:
            signals.append('funding_rate')
        
        # For risk suppression
        if not self.disable_risk_suppression:
            signals.extend(['atr_14_sec', 'unrealised_pnl'])
        
        return list(set(signals))
    
    def update(self, signals: Dict[str, Any], timestamp: datetime) -> Optional[Dict[str, Any]]:
        """
        Update detector with new signals.
        
        Args:
            signals: Dictionary of market signals
            timestamp: Current timestamp
            
        Returns:
            Regime update dict or None if not time to update
        """
        # Check cadence
        if self.last_update_time is not None:
            time_since_last = (timestamp - self.last_update_time).total_seconds()
            if time_since_last < self.cadence_sec:
                return None
        
        self.last_update_time = timestamp
        
        try:
            # Determine new state
            new_state = self._determine_state(signals)
            
            # Calculate confidence based on signal strength
            confidence = self._calculate_confidence(signals, new_state)
            
            # Update state manager
            self.state_manager.update_state(
                timestamp=timestamp,
                state=new_state,
                confidence=confidence,
                features=signals
            )
            
            # Calculate risk suppression
            self.risk_suppressed = self._calculate_risk_suppressed(signals)
            
            # Log state changes
            if new_state != self.current_state:
                self.logger.info(
                    f"State change: {self.current_state} -> {new_state} "
                    f"(confidence: {confidence:.2f})"
                )
            
            self.current_state = new_state
            
            # Get regime features for strategy
            regime_features = self.state_manager.get_regime_features_for_strategy(
                timestamp=timestamp,
                lookback_hours=4
            )
            
            return {
                'state': new_state,
                'confidence': confidence,
                'risk_suppressed': self.risk_suppressed,
                'features': regime_features,
                'timestamp': timestamp
            }
            
        except Exception as e:
            self.logger.error(f"Error updating detector: {e}", exc_info=True)
            return None
    
    def _determine_state(self, signals: Dict[str, Any]) -> str:
        """
        Determine regime state from signals.
        
        Simplified logic without adaptive thresholds or complex gates.
        """
        # Extract core signals
        atr_pct = signals.get('atr_percent_sec', 0.02)
        momentum = signals.get('ma_slope_ema_30s', 0.0)
        obi = signals.get('volume_imbalance', 0.0)
        spread_mean = signals.get('spread_mean', 0.0001)
        spread_std = signals.get('spread_std', 0.0001)
        
        # Check for missing data - only check if signal is None or explicitly missing
        if 'volume_imbalance' not in signals:
            return GMS_STATE_UNKNOWN
        if any(pd.isna(v) for v in [atr_pct, momentum]):
            return GMS_STATE_UNKNOWN
        
        # Determine volatility regime
        is_high_vol = atr_pct >= self.vol_high_thresh
        is_low_vol = atr_pct <= self.vol_low_thresh
        
        # Determine momentum strength
        abs_momentum = abs(momentum)
        is_strong_momentum = abs_momentum >= self.mom_strong_thresh
        is_weak_momentum = abs_momentum >= self.mom_weak_thresh
        is_bullish = momentum > 0
        
        # Determine OBI confirmation
        abs_obi = abs(obi)
        has_strong_obi = abs_obi >= self.obi_strong_confirm_thresh
        has_weak_obi = abs_obi >= self.obi_weak_confirm_thresh
        obi_aligned = (is_bullish and obi > 0) or (not is_bullish and obi < 0)
        
        # Check spread conditions
        is_choppy = spread_std >= self.spread_std_high_thresh
        is_tight_spread = spread_mean <= self.spread_mean_low_thresh
        
        # State determination logic
        
        # 1. High volatility conditions
        if is_high_vol:
            if is_choppy or abs_momentum < self.mom_weak_thresh:
                return GMS_STATE_HIGH_VOL_RANGE
            # Fall through to trend logic
        
        # 2. Low volatility conditions
        elif is_low_vol:
            if abs_momentum < self.mom_weak_thresh and is_tight_spread:
                return GMS_STATE_LOW_VOL_RANGE
            # Fall through to trend logic
        
        # 3. Tight spread special case
        if is_tight_spread and abs_momentum < self.mom_weak_thresh:
            return GMS_STATE_TIGHT_SPREAD
        
        # 4. Trend determination
        if is_strong_momentum:
            # Strong momentum - check OBI alignment
            if obi_aligned and has_strong_obi:
                # Strong trend with strong confirmation
                if is_bullish:
                    return GMS_STATE_STRONG_BULL_TREND
                else:
                    return GMS_STATE_STRONG_BEAR_TREND
            elif obi_aligned:
                # Strong momentum but weaker OBI - still a trend
                if is_bullish:
                    return GMS_STATE_WEAK_BULL_TREND
                else:
                    return GMS_STATE_WEAK_BEAR_TREND
        
        elif is_weak_momentum:
            # Weak momentum - more lenient on OBI
            if obi_aligned:
                # Weak trend with any OBI alignment
                if is_bullish:
                    return GMS_STATE_WEAK_BULL_TREND
                else:
                    return GMS_STATE_WEAK_BEAR_TREND
        
        # 5. Default to uncertain
        return GMS_STATE_UNCERTAIN
    
    def _calculate_confidence(self, signals: Dict[str, Any], state: str) -> float:
        """
        Calculate confidence in the detected state.
        
        Args:
            signals: Market signals
            state: Detected state
            
        Returns:
            Confidence score [0-1]
        """
        confidence = 0.5  # Base confidence
        
        # Extract signals
        momentum = abs(signals.get('ma_slope_ema_30s', 0.0))
        obi = abs(signals.get('volume_imbalance', 0.0))
        atr_pct = signals.get('atr_percent_sec', 0.02)
        
        # Adjust based on signal strength
        if state in [GMS_STATE_STRONG_BULL_TREND, GMS_STATE_STRONG_BEAR_TREND]:
            # Strong trends need strong signals
            if momentum >= self.mom_strong_thresh:
                confidence += 0.25  # Increased from 0.2
            if obi >= self.obi_strong_confirm_thresh:
                confidence += 0.25  # Increased from 0.2
            if atr_pct > self.vol_low_thresh and atr_pct < self.vol_high_thresh:
                confidence += 0.1  # Normal volatility is good for trends
        
        elif state in [GMS_STATE_HIGH_VOL_RANGE, GMS_STATE_LOW_VOL_RANGE]:
            # Range states need consistent volatility
            if state == GMS_STATE_HIGH_VOL_RANGE and atr_pct >= self.vol_high_thresh:
                confidence += 0.3
            elif state == GMS_STATE_LOW_VOL_RANGE and atr_pct <= self.vol_low_thresh:
                confidence += 0.3
            if momentum < self.mom_weak_thresh:
                confidence += 0.2  # Low momentum confirms range
        
        # Cap confidence
        return min(1.0, max(0.1, confidence))
    
    def _calculate_risk_suppressed(self, signals: Dict[str, Any]) -> bool:
        """Calculate risk suppression flag."""
        if self.disable_risk_suppression:
            return False
        
        # Extract signals
        close = signals.get('close', 0.0)
        unrealised_pnl = signals.get('unrealised_pnl', 0.0)
        atr = signals.get('atr_14_sec', 1.0)
        
        # Calculate thresholds
        # TODO: Get portfolio config values
        notional_threshold = 10000.0 * 10.0 * self.risk_suppressed_notional_frac
        pnl_threshold = atr * self.risk_suppressed_pnl_atr_mult
        
        # Check conditions
        notional_exceeded = close >= notional_threshold
        pnl_exceeded = abs(unrealised_pnl) >= pnl_threshold
        
        return notional_exceeded or pnl_exceeded
    
    def get_regime(self, signals: Dict[str, Any], price_history: Optional[pd.Series] = None) -> Dict[str, Any]:
        """
        Get current regime state.
        
        Interface method for compatibility with IRegimeDetector.
        """
        timestamp = signals.get('timestamp', datetime.now())
        if isinstance(timestamp, (int, float)):
            timestamp = pd.Timestamp(timestamp, unit='s')
        
        # Try to update
        update_result = self.update(signals, timestamp)
        
        # Get current state from manager
        current_snapshot = self.state_manager.get_current_state()
        
        if current_snapshot:
            regime_features = self.state_manager.get_regime_features_for_strategy(
                timestamp=timestamp,
                lookback_hours=4
            )
            
            return {
                'state': current_snapshot.state,
                'confidence': current_snapshot.confidence,
                'risk_suppressed': self.risk_suppressed,
                'features': regime_features
            }
        else:
            # No state yet
            return {
                'state': GMS_STATE_UNKNOWN,
                'confidence': 0.0,
                'risk_suppressed': False,
                'features': {}
            }
    
    def get_state_history(self, end_time: datetime, hours: int = 24) -> List[Dict[str, Any]]:
        """
        Get regime state history.
        
        Args:
            end_time: End of history period
            hours: Hours of history to retrieve
            
        Returns:
            List of regime states
        """
        snapshots = self.state_manager.get_state_history(end_time, hours)
        
        return [
            {
                'timestamp': s.timestamp,
                'state': s.state,
                'confidence': s.confidence,
                'momentum': s.momentum,
                'volatility': s.volatility,
                'volume_imbalance': s.volume_imbalance
            }
            for s in snapshots
        ]
    
    def reset(self):
        """Reset detector state (for backtesting)."""
        self.state_manager.clear_history()
        self.last_update_time = None
        self.current_state = GMS_STATE_UNCERTAIN
        self.risk_suppressed = False
        self.logger.info("Modern detector state reset")
    
    # Interface methods for compatibility
    def detect_regime(self, signals: Dict[str, Any], timestamp: Optional[datetime] = None) -> str:
        """
        Detect market regime from signals (interface method).
        
        Args:
            signals: Dictionary of market signals and indicators
            timestamp: Current timestamp (optional)
            
        Returns:
            Regime state: 'BULL', 'BEAR', 'CHOP', etc.
        """
        result = self.get_regime(signals)
        if isinstance(result, dict):
            return result.get('state', GMS_STATE_UNKNOWN)
        return result
    
    def get_allowed_states(self, strategy_type: str) -> List[str]:
        """
        Get list of regime states that allow trading for a strategy.
        
        Args:
            strategy_type: Type of strategy ('trend_following', 'mean_reversion', etc.)
            
        Returns:
            List of allowed regime states
        """
        if strategy_type == 'trend_following':
            return [
                GMS_STATE_STRONG_BULL_TREND,
                GMS_STATE_WEAK_BULL_TREND,
                GMS_STATE_STRONG_BEAR_TREND,
                GMS_STATE_WEAK_BEAR_TREND
            ]
        elif strategy_type == 'mean_reversion':
            return [
                GMS_STATE_HIGH_VOL_RANGE,
                GMS_STATE_LOW_VOL_RANGE
            ]
        else:
            # Default: all states except UNKNOWN
            return [
                GMS_STATE_STRONG_BULL_TREND,
                GMS_STATE_WEAK_BULL_TREND,
                GMS_STATE_HIGH_VOL_RANGE,
                GMS_STATE_LOW_VOL_RANGE,
                GMS_STATE_UNCERTAIN,
                GMS_STATE_WEAK_BEAR_TREND,
                GMS_STATE_STRONG_BEAR_TREND,
                GMS_STATE_TIGHT_SPREAD
            ]
    
    def get_confidence(self) -> float:
        """
        Get confidence level of current regime detection.
        
        Returns:
            Confidence value between 0.0 and 1.0
        """
        current = self.state_manager.get_current_state()
        return current.confidence if current else 0.0
    
    def get_raw_state(self, features: Dict[str, Any], timestamp: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Get raw state information.
        
        Args:
            features: Feature dictionary
            timestamp: Optional timestamp
            
        Returns:
            Dictionary with raw state information
        """
        # Determine state from features
        state = self._determine_state(features)
        confidence = self._calculate_confidence(features, state)
        
        return {
            'state': state,
            'confidence': confidence,
            'risk_suppressed': self._calculate_risk_suppressed(features),
            'timestamp': timestamp or datetime.now()
        }