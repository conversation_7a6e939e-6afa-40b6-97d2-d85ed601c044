"""
Modern Continuous Detector V2 - Clean Architecture
==================================================

This is the improved version of ContinuousGMSDetector that:
- NO hardcoded default values
- Uses ModernDataAdapter for field transformations
- Graceful degradation for missing optional fields
- All thresholds from configuration
- Comprehensive logging for debugging
- Clean separation of concerns

Key improvements:
- Data adapter integration
- No silent failures from hardcoded defaults
- Explicit handling of missing data
- Configurable behavior for all aspects
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Union, Any, List, Tuple
import pandas as pd
import numpy as np

from ..config.settings import Config
from .regime_state_manager import RegimeStateManager, RegimeState
from .registry import modern_regime_detector
from .adapters.data_adapter import ModernDataAdapter, AdapterConfig
from .contracts.data_schema import ModernDataContract

# Import state constants
from ..utils.state_mapping import (
    GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND,
    GMS_STATE_HIGH_VOL_RANGE, GMS_STATE_LOW_VOL_RANGE,
    GMS_STATE_UNCERTAIN, GMS_STATE_WEAK_BEAR_TREND,
    GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_TIGHT_SPREAD,
    GMS_STATE_UNKNOWN
)


@modern_regime_detector("continuous_modern_v2", version="3.0", experimental=False)
class ModernContinuousDetectorV2:
    """
    Clean implementation of continuous regime detection.
    
    This detector:
    - Uses data adapter for all field transformations
    - No hardcoded values anywhere
    - Graceful degradation for missing optional fields
    - Comprehensive configuration
    - Full observability via logging
    """
    
    def __init__(self, config: Config, mode: str = "backtest"):
        """
        Initialize modern continuous detector V2.
        
        Args:
            config: System configuration
            mode: Operating mode ("backtest" or "live")
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.mode = mode
        
        # Initialize data adapter
        adapter_config = AdapterConfig(
            handle_missing_with_defaults=True,
            log_transformations=False,  # Too verbose for every update
            compute_derived_fields=True,
            nan_handling_strategy='interpolate'
        )
        self.data_adapter = ModernDataAdapter(adapter_config)
        self.data_contract = ModernDataContract()
        
        # Configuration sections
        self.cfg_regime = config.regime
        self.cfg_micro = config.microstructure
        self.cfg_indicators = config.indicators
        self.cfg_gms = getattr(config, 'gms', None)
        
        # Get detector settings from config
        detector_settings = self._get_detector_settings()
        operational_settings = self._get_operational_settings()
        
        # Core parameters (NO DEFAULTS!)
        self.cadence_sec = operational_settings['cadence_sec']
        self.depth_levels = self.cfg_micro.depth_levels
        
        # Volatility thresholds
        self.vol_high_thresh = detector_settings['gms_vol_high_thresh']
        self.vol_low_thresh = detector_settings['gms_vol_low_thresh']
        
        # Momentum thresholds
        self.mom_strong_thresh = detector_settings['gms_mom_strong_thresh']
        self.mom_weak_thresh = detector_settings['gms_mom_weak_thresh']
        
        # Spread thresholds
        self.spread_std_high_thresh = detector_settings['gms_spread_std_high_thresh']
        self.spread_mean_low_thresh = detector_settings['gms_spread_mean_low_thresh']
        
        # OBI thresholds (from microstructure section)
        self.obi_strong_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_strong_confirm_thresh', 0.15)
        self.obi_weak_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_weak_confirm_thresh', 0.05)
        
        # Optional features configuration
        self.use_adx_confirmation = detector_settings.get('gms_use_adx_confirmation', False)
        self.adx_threshold = detector_settings.get('adx_threshold', 30.0) if self.use_adx_confirmation else None
        
        self.use_funding_confirmation = detector_settings.get('gms_use_funding_confirmation', False)
        self.funding_extreme_pos_thresh = detector_settings.get('gms_funding_extreme_positive_thresh', 0.001)
        self.funding_extreme_neg_thresh = detector_settings.get('gms_funding_extreme_negative_thresh', -0.001)
        
        # Risk suppression configuration
        self.disable_risk_suppression = (mode == "backtest")
        if not self.disable_risk_suppression:
            self.risk_suppressed_notional_frac = operational_settings['risk_suppressed_notional_frac']
            self.risk_suppressed_pnl_atr_mult = operational_settings['risk_suppressed_pnl_atr_mult']
        
        # Graceful degradation settings
        self.min_confidence_for_trend = detector_settings.get('min_confidence_for_trend', 0.6)
        self.allow_partial_signals = detector_settings.get('allow_partial_signals', True)
        
        # Initialize regime state manager
        self.state_manager = RegimeStateManager(
            history_hours=24,
            state_update_seconds=self.cadence_sec,
            mode=mode
        )
        
        # Tracking
        self.last_update_time = None
        self.current_state = GMS_STATE_UNCERTAIN
        self.risk_suppressed = False
        self.last_signal_quality = 1.0  # Track signal quality
        
        self._log_initialization()
    
    def _get_detector_settings(self) -> Dict[str, Any]:
        """Get detector settings with validation."""
        settings = self.cfg_regime.get_detector_settings('continuous_modern_v2')
        
        # Validate required settings exist
        required = [
            'gms_vol_high_thresh', 'gms_vol_low_thresh',
            'gms_mom_strong_thresh', 'gms_mom_weak_thresh',
            'gms_spread_std_high_thresh', 'gms_spread_mean_low_thresh'
        ]
        
        missing = [key for key in required if key not in settings]
        if missing:
            raise ValueError(f"Missing required detector settings: {missing}")
        
        return settings
    
    def _get_operational_settings(self) -> Dict[str, Any]:
        """Get operational settings with validation."""
        settings = self.cfg_regime.get_detector_operational_settings(
            'continuous_modern_v2', self.cfg_gms
        )
        
        # Validate required settings
        if 'cadence_sec' not in settings:
            raise ValueError("Missing required operational setting: cadence_sec")
        
        return settings
    
    def _log_initialization(self):
        """Log initialization details."""
        self.logger.info(
            f"Modern Continuous Detector V2 initialized:\n"
            f"  Mode: {self.mode}\n"
            f"  Cadence: {self.cadence_sec} seconds\n"
            f"  Thresholds:\n"
            f"    - Volatility: {self.vol_low_thresh:.4f} / {self.vol_high_thresh:.4f}\n"
            f"    - Momentum: {self.mom_weak_thresh:.2f} / {self.mom_strong_thresh:.2f}\n"
            f"    - OBI: {self.obi_weak_confirm_thresh:.3f} / {self.obi_strong_confirm_thresh:.3f}\n"
            f"    - Spread std/mean: {self.spread_std_high_thresh:.5f} / {self.spread_mean_low_thresh:.5f}\n"
            f"  Features:\n"
            f"    - ADX confirmation: {self.use_adx_confirmation}\n"
            f"    - Funding confirmation: {self.use_funding_confirmation}\n"
            f"    - Risk suppression: {'disabled' if self.disable_risk_suppression else 'enabled'}\n"
            f"    - Partial signals: {self.allow_partial_signals}"
        )
    
    def update(self, signals: Dict[str, Any], timestamp: datetime) -> Optional[Dict[str, Any]]:
        """
        Update detector with new signals.
        
        Args:
            signals: Dictionary of market signals (raw, unadapted)
            timestamp: Current timestamp
            
        Returns:
            Regime update dict or None if not time to update
        """
        # Check cadence
        if self.last_update_time is not None:
            time_since_last = (timestamp - self.last_update_time).total_seconds()
            if time_since_last < self.cadence_sec:
                return None
        
        self.last_update_time = timestamp
        
        try:
            # Adapt signals using data adapter
            adapted_signals = self.data_adapter.adapt_signals_dict(signals, timestamp)
            
            # Validate signal quality
            signal_quality = self._assess_signal_quality(adapted_signals)
            self.last_signal_quality = signal_quality
            
            # Determine new state with quality-aware logic
            new_state, state_confidence = self._determine_state_with_confidence(
                adapted_signals, signal_quality
            )
            
            # Update state manager
            self.state_manager.update_state(
                timestamp=timestamp,
                state=new_state,
                confidence=state_confidence,
                features=adapted_signals
            )
            
            # Calculate risk suppression
            self.risk_suppressed = self._calculate_risk_suppressed(adapted_signals)
            
            # Log state changes
            if new_state != self.current_state:
                self.logger.info(
                    f"State change: {self.current_state} -> {new_state} "
                    f"(confidence: {state_confidence:.2f}, signal_quality: {signal_quality:.2f})"
                )
            
            self.current_state = new_state
            
            # Get regime features for strategy
            regime_features = self.state_manager.get_regime_features_for_strategy(
                timestamp=timestamp,
                lookback_hours=4
            )
            
            return {
                'state': new_state,
                'confidence': state_confidence,
                'risk_suppressed': self.risk_suppressed,
                'features': regime_features,
                'timestamp': timestamp,
                'signal_quality': signal_quality
            }
            
        except Exception as e:
            self.logger.error(f"Error updating detector: {e}", exc_info=True)
            return None
    
    def _assess_signal_quality(self, signals: Dict[str, Any]) -> float:
        """
        Assess the quality of available signals.
        
        Returns:
            Quality score between 0 and 1
        """
        required_fields = self.data_contract.get_required_fields()
        available_count = 0
        total_weight = 0
        
        # Weight different signals by importance
        field_weights = {
            'volume_imbalance': 0.25,
            'atr_percent_sec': 0.20,
            'ma_slope_ema_30s': 0.20,
            'spread_mean': 0.10,
            'spread_std': 0.10,
            'close': 0.05,
            'volume': 0.05,
            'timestamp': 0.05
        }
        
        for field in required_fields:
            weight = field_weights.get(field, 0.1)
            total_weight += weight
            
            if field in signals and signals[field] is not None and not pd.isna(signals[field]):
                available_count += weight
        
        quality = available_count / total_weight if total_weight > 0 else 0.0
        
        if quality < 0.5:
            self.logger.warning(f"Low signal quality: {quality:.2f}")
        
        return quality
    
    def _determine_state_with_confidence(
        self, signals: Dict[str, Any], signal_quality: float
    ) -> Tuple[str, float]:
        """
        Determine regime state and confidence from signals.
        
        Returns:
            Tuple of (state, confidence)
        """
        # Check minimum signal quality
        if signal_quality < 0.3:
            self.logger.warning("Signal quality too low for state determination")
            return GMS_STATE_UNKNOWN, 0.0
        
        # Extract signals with None checks
        atr_pct = signals.get('atr_percent_sec')
        momentum = signals.get('ma_slope_ema_30s')
        obi = signals.get('volume_imbalance')
        spread_mean = signals.get('spread_mean')
        spread_std = signals.get('spread_std')
        
        # Check for critical missing data
        critical_missing = []
        if atr_pct is None or pd.isna(atr_pct):
            critical_missing.append('atr_percent_sec')
        if momentum is None or pd.isna(momentum):
            critical_missing.append('ma_slope_ema_30s')
        if obi is None or pd.isna(obi):
            critical_missing.append('volume_imbalance')
        
        if critical_missing:
            if not self.allow_partial_signals:
                self.logger.warning(f"Critical signals missing: {critical_missing}")
                return GMS_STATE_UNKNOWN, 0.0
            else:
                self.logger.info(f"Proceeding with partial signals, missing: {critical_missing}")
        
        # Initialize state and confidence
        state = GMS_STATE_UNCERTAIN
        base_confidence = signal_quality * 0.5  # Base confidence from signal quality
        
        # Volatility analysis
        vol_state, vol_confidence = self._analyze_volatility(atr_pct)
        
        # Momentum analysis
        mom_state, mom_confidence = self._analyze_momentum(momentum, obi)
        
        # Spread analysis
        spread_state, spread_confidence = self._analyze_spread(spread_mean, spread_std)
        
        # Initialize confidence with base value
        confidence = base_confidence
        
        # Combine analyses
        if vol_state == 'HIGH_VOL' and spread_state == 'CHOPPY':
            state = GMS_STATE_HIGH_VOL_RANGE
            confidence = base_confidence + (vol_confidence + spread_confidence) * 0.25
        
        elif vol_state == 'LOW_VOL' and mom_state == 'NEUTRAL':
            if spread_state == 'TIGHT':
                state = GMS_STATE_TIGHT_SPREAD
                confidence = base_confidence + (vol_confidence + spread_confidence) * 0.3
            else:
                state = GMS_STATE_LOW_VOL_RANGE
                confidence = base_confidence + vol_confidence * 0.5
        
        elif mom_state in ['STRONG_BULL', 'WEAK_BULL', 'STRONG_BEAR', 'WEAK_BEAR']:
            # Map momentum states to regime states
            state_map = {
                'STRONG_BULL': GMS_STATE_STRONG_BULL_TREND,
                'WEAK_BULL': GMS_STATE_WEAK_BULL_TREND,
                'STRONG_BEAR': GMS_STATE_STRONG_BEAR_TREND,
                'WEAK_BEAR': GMS_STATE_WEAK_BEAR_TREND
            }
            state = state_map[mom_state]
            confidence = base_confidence + mom_confidence * 0.5
            
            # Reduce confidence for trends in extreme volatility
            if vol_state == 'HIGH_VOL':
                confidence *= 0.8
        else:
            # Default case - maintain uncertain state with base confidence
            state = GMS_STATE_UNCERTAIN
            confidence = base_confidence
        
        # Cap confidence
        confidence = min(1.0, max(0.0, confidence))
        
        # Apply minimum confidence threshold for trend states
        if state in [GMS_STATE_STRONG_BULL_TREND, GMS_STATE_STRONG_BEAR_TREND]:
            if confidence < self.min_confidence_for_trend:
                # Downgrade to weak trend
                if state == GMS_STATE_STRONG_BULL_TREND:
                    state = GMS_STATE_WEAK_BULL_TREND
                else:
                    state = GMS_STATE_WEAK_BEAR_TREND
        
        return state, confidence
    
    def _analyze_volatility(self, atr_pct: Optional[float]) -> Tuple[str, float]:
        """Analyze volatility regime."""
        if atr_pct is None or pd.isna(atr_pct):
            return 'NORMAL', 0.0
        
        if atr_pct >= self.vol_high_thresh:
            confidence = min(1.0, (atr_pct - self.vol_high_thresh) / self.vol_high_thresh)
            return 'HIGH_VOL', confidence
        elif atr_pct <= self.vol_low_thresh:
            confidence = min(1.0, (self.vol_low_thresh - atr_pct) / self.vol_low_thresh)
            return 'LOW_VOL', confidence
        else:
            return 'NORMAL', 0.5
    
    def _analyze_momentum(
        self, momentum: Optional[float], obi: Optional[float]
    ) -> Tuple[str, float]:
        """Analyze momentum and direction."""
        if momentum is None or pd.isna(momentum):
            return 'NEUTRAL', 0.0
        
        abs_momentum = abs(momentum)
        is_bullish = momentum > 0
        
        # OBI confirmation
        obi_aligned = False
        obi_strength = 0.0
        if obi is not None and not pd.isna(obi):
            obi_aligned = (is_bullish and obi > 0) or (not is_bullish and obi < 0)
            obi_strength = abs(obi)
        
        # Determine momentum state
        if abs_momentum >= self.mom_strong_thresh:
            if obi_aligned and obi_strength >= self.obi_strong_confirm_thresh:
                confidence = 0.9
                state = 'STRONG_BULL' if is_bullish else 'STRONG_BEAR'
            else:
                confidence = 0.6
                state = 'WEAK_BULL' if is_bullish else 'WEAK_BEAR'
        elif abs_momentum >= self.mom_weak_thresh:
            confidence = 0.5 if obi_aligned else 0.3
            state = 'WEAK_BULL' if is_bullish else 'WEAK_BEAR'
        else:
            return 'NEUTRAL', 0.5
        
        return state, confidence
    
    def _analyze_spread(
        self, spread_mean: Optional[float], spread_std: Optional[float]
    ) -> Tuple[str, float]:
        """Analyze spread characteristics."""
        if spread_mean is None or spread_std is None:
            return 'NORMAL', 0.0
        
        if pd.isna(spread_mean) or pd.isna(spread_std):
            return 'NORMAL', 0.0
        
        if spread_std >= self.spread_std_high_thresh:
            confidence = min(1.0, spread_std / (self.spread_std_high_thresh * 2))
            return 'CHOPPY', confidence
        elif spread_mean <= self.spread_mean_low_thresh:
            confidence = min(1.0, self.spread_mean_low_thresh / (spread_mean + 1e-10))
            return 'TIGHT', confidence
        else:
            return 'NORMAL', 0.5
    
    def _calculate_risk_suppressed(self, signals: Dict[str, Any]) -> bool:
        """Calculate risk suppression flag."""
        if self.disable_risk_suppression:
            return False
        
        # Extract required signals
        close = signals.get('close')
        unrealised_pnl = signals.get('unrealised_pnl')
        atr = signals.get('atr_14_sec')
        
        # Check if we have required data
        if any(v is None or pd.isna(v) for v in [close, atr]):
            return False
        
        # Calculate thresholds
        # TODO: Get actual portfolio values from config
        notional_threshold = 10000.0 * 10.0 * self.risk_suppressed_notional_frac
        pnl_threshold = atr * self.risk_suppressed_pnl_atr_mult
        
        # Check conditions
        notional_exceeded = close >= notional_threshold
        pnl_exceeded = False
        if unrealised_pnl is not None and not pd.isna(unrealised_pnl):
            pnl_exceeded = abs(unrealised_pnl) >= pnl_threshold
        
        return notional_exceeded or pnl_exceeded
    
    # Interface methods for compatibility
    def get_regime(self, signals: Dict[str, Any], price_history: Optional[pd.Series] = None) -> Dict[str, Any]:
        """Get current regime state (interface method)."""
        timestamp = signals.get('timestamp', datetime.now())
        if isinstance(timestamp, (int, float)):
            timestamp = pd.Timestamp(timestamp, unit='s')
        
        # Try to update
        update_result = self.update(signals, timestamp)
        
        if update_result:
            return update_result
        
        # Return current state if not time to update
        current_snapshot = self.state_manager.get_current_state()
        if current_snapshot:
            return {
                'state': current_snapshot.state,
                'confidence': current_snapshot.confidence,
                'risk_suppressed': self.risk_suppressed,
                'features': {},
                'signal_quality': self.last_signal_quality
            }
        else:
            return {
                'state': GMS_STATE_UNKNOWN,
                'confidence': 0.0,
                'risk_suppressed': False,
                'features': {},
                'signal_quality': 0.0
            }
    
    def detect_regime(self, signals: Dict[str, Any], timestamp: Optional[datetime] = None) -> str:
        """Detect market regime from signals (interface method)."""
        result = self.get_regime(signals)
        return result.get('state', GMS_STATE_UNKNOWN)
    
    def get_allowed_states(self, strategy_type: str) -> List[str]:
        """Get list of regime states that allow trading for a strategy."""
        if strategy_type == 'trend_following':
            return [
                GMS_STATE_STRONG_BULL_TREND,
                GMS_STATE_WEAK_BULL_TREND,
                GMS_STATE_STRONG_BEAR_TREND,
                GMS_STATE_WEAK_BEAR_TREND
            ]
        elif strategy_type == 'mean_reversion':
            return [
                GMS_STATE_HIGH_VOL_RANGE,
                GMS_STATE_LOW_VOL_RANGE
            ]
        else:
            # Default: all states except UNKNOWN
            return [
                GMS_STATE_STRONG_BULL_TREND,
                GMS_STATE_WEAK_BULL_TREND,
                GMS_STATE_HIGH_VOL_RANGE,
                GMS_STATE_LOW_VOL_RANGE,
                GMS_STATE_UNCERTAIN,
                GMS_STATE_WEAK_BEAR_TREND,
                GMS_STATE_STRONG_BEAR_TREND,
                GMS_STATE_TIGHT_SPREAD
            ]
    
    def get_confidence(self) -> float:
        """Get confidence level of current regime detection."""
        current = self.state_manager.get_current_state()
        return current.confidence if current else 0.0
    
    def reset(self):
        """Reset detector state (for backtesting)."""
        self.state_manager.clear_history()
        self.last_update_time = None
        self.current_state = GMS_STATE_UNCERTAIN
        self.risk_suppressed = False
        self.last_signal_quality = 1.0
        self.data_adapter.reset_statistics()
        self.logger.info("Modern detector V2 state reset")