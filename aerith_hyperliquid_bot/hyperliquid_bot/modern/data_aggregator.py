"""
Modern Data Aggregator with Look-Ahead Prevention
================================================

This module handles data aggregation for the modern system with strict
look-ahead bias prevention. All aggregation operations use proper labeling
to ensure no future data leaks into past calculations.

Key Features:
- 1-second to 60-second aggregation for regime updates
- Hourly bar aggregation for trading signals
- 1-minute data for execution refinement
- ZERO look-ahead bias guaranteed

CRITICAL: All resampling MUST use label='right', closed='left'
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np
from pathlib import Path

logger = logging.getLogger(__name__)


class ModernDataAggregator:
    """
    Data aggregator for modern system with look-ahead prevention.
    
    This class ensures all data aggregation operations prevent look-ahead bias
    by using proper resampling labels and time filtering.
    """
    
    def __init__(self):
        """Initialize the data aggregator."""
        self.logger = logger
        
        # Critical settings for look-ahead prevention
        self.resample_label = 'right'
        self.resample_closed = 'left'
        
        self.logger.info(
            f"Modern Data Aggregator initialized with look-ahead prevention:\n"
            f"  - Resample label: {self.resample_label}\n"
            f"  - Resample closed: {self.resample_closed}"
        )
    
    def aggregate_to_60s(self, data_1s: pd.DataFrame, current_time: datetime) -> pd.DataFrame:
        """
        Aggregate 1-second data to 60-second windows.
        
        CRITICAL: Uses label='right', closed='left' to prevent look-ahead bias.
        
        Args:
            data_1s: DataFrame with 1-second data (must have datetime index)
            current_time: Current timestamp (only data up to this time is used)
            
        Returns:
            DataFrame with 60-second aggregated data
        """
        # CRITICAL: Filter to only past data
        # Note: We use < current_time, not <=, because with right labeling,
        # the bar at current_time would include data FROM current_time
        past_data = data_1s[data_1s.index < current_time].copy()
        
        if past_data.empty:
            self.logger.warning(f"No data available before {current_time}")
            return pd.DataFrame()
        
        # Aggregate with proper labeling to prevent look-ahead
        agg_dict = {
            'close': lambda x: x.iloc[-1] if len(x) > 0 else None,
            'high': 'max',
            'low': 'min',
            'volume': 'sum',
            # OBI fields - use mean for aggregation
            'obi_smoothed': 'mean',
            'obi_smoothed_5': 'mean',
            'obi_smoothed_20': 'mean',
            # Spread fields
            'spread_mean': 'mean',
            'spread_std': 'mean',
            # ATR fields
            'atr_14_sec': lambda x: x.iloc[-1] if len(x) > 0 else None,
            'atr_percent_sec': lambda x: x.iloc[-1] if len(x) > 0 else None,
            # Momentum
            'ma_slope': lambda x: x.iloc[-1] if len(x) > 0 else None,
            'ma_slope_ema_30s': lambda x: x.iloc[-1] if len(x) > 0 else None
        }
        
        # Only aggregate columns that exist
        agg_dict_filtered = {k: v for k, v in agg_dict.items() if k in past_data.columns}
        
        if not agg_dict_filtered:
            self.logger.warning(f"No matching columns found. Available columns: {list(past_data.columns)}")
            return pd.DataFrame()
        
        # CRITICAL: Use right label, left closed
        # This means: 10:29:00-10:29:59 data -> labeled as 10:30:00
        df_60s = past_data.resample(
            '60s',
            label=self.resample_label,
            closed=self.resample_closed
        ).agg(agg_dict_filtered)
        
        # Check if resample produced any data
        if df_60s.empty or len(df_60s) == 0:
            self.logger.warning(f"Resample produced empty dataframe for {current_time}")
            return pd.DataFrame()
        
        # Drop any rows with NaN index
        df_60s = df_60s[df_60s.index.notna()]
        
        # Add open price (first value in each 60s window)
        if 'close' in past_data.columns:
            open_series = past_data['close'].resample(
                '60s',
                label=self.resample_label,
                closed=self.resample_closed
            ).apply(lambda x: x.iloc[0] if len(x) > 0 else None)
            df_60s['open'] = open_series
        
        # No need to filter since we already filtered past_data
        
        self.logger.debug(
            f"Aggregated {len(past_data)} 1s rows to {len(df_60s)} 60s rows "
            f"(up to {current_time})"
        )
        
        return df_60s
    
    def get_hourly_bar(self, data: pd.DataFrame, current_time: datetime) -> pd.Series:
        """
        Get the most recent completed hourly bar.
        
        CRITICAL: Only returns COMPLETED bars to prevent look-ahead.
        
        Args:
            data: DataFrame with price data
            current_time: Current timestamp
            
        Returns:
            Series with completed hourly bar data, or empty Series if none
        """
        # Only use data from completed hours
        completed_time = current_time.replace(minute=0, second=0, microsecond=0)
        completed_data = data[data.index < completed_time].copy()
        
        if completed_data.empty:
            self.logger.warning(f"No completed data before {completed_time}")
            return pd.Series()
        
        # Only aggregate columns that exist
        agg_dict = {}
        for col, func in [('open', 'first'), ('high', 'max'), ('low', 'min'), 
                         ('close', 'last'), ('volume', 'sum')]:
            if col in completed_data.columns:
                agg_dict[col] = func
        
        if not agg_dict:
            return pd.Series()
        
        # Aggregate to hourly bars
        hourly_bars = completed_data.resample(
            '1h',
            label=self.resample_label,
            closed=self.resample_closed
        ).agg(agg_dict)
        
        # Remove any empty bars
        hourly_bars = hourly_bars.dropna(how='all')
        
        if hourly_bars.empty:
            return pd.Series()
        
        # Return the most recent completed bar
        return hourly_bars.iloc[-1]
    
    def get_execution_window_data(
        self, 
        data_1s: pd.DataFrame, 
        signal_time: datetime,
        window_minutes: int = 5
    ) -> pd.DataFrame:
        """
        Get 1-minute data for execution refinement window.
        
        CRITICAL: Window starts AT signal time, not before.
        
        Args:
            data_1s: 1-second data
            signal_time: Time when signal was generated
            window_minutes: Execution window size (default 5 minutes)
            
        Returns:
            DataFrame with 1-minute bars for execution window
        """
        window_start = signal_time
        window_end = signal_time + timedelta(minutes=window_minutes)
        
        # Filter data to execution window
        window_data = data_1s[
            (data_1s.index >= window_start) & 
            (data_1s.index < window_end)
        ].copy()
        
        if window_data.empty:
            self.logger.warning(f"No data in execution window {window_start} to {window_end}")
            return pd.DataFrame()
        
        # Aggregate to 1-minute bars
        agg_dict = {}
        # Only aggregate columns that exist
        for col, func in [
            ('close', 'last'), ('high', 'max'), ('low', 'min'),
            ('volume', 'sum'), ('spread_mean', 'mean'), ('obi_smoothed', 'mean')
        ]:
            if col in window_data.columns:
                agg_dict[col] = func
        
        # Don't include open in the main agg_dict, handle it separately
        
        if not agg_dict:
            return pd.DataFrame()
            
        df_1m = window_data.resample(
            '1min',
            label=self.resample_label,
            closed=self.resample_closed
        ).agg(agg_dict)
        
        # Add open column if we have close data
        if 'close' in window_data.columns:
            df_1m['open'] = window_data['close'].resample(
                '1min',
                label=self.resample_label,
                closed=self.resample_closed
            ).apply(lambda x: x.iloc[0] if len(x) > 0 else None)
        
        return df_1m
    
    def validate_no_look_ahead(
        self, 
        original_data: pd.DataFrame,
        aggregated_data: pd.DataFrame,
        current_time: datetime
    ) -> bool:
        """
        Validate that aggregated data contains no look-ahead bias.
        
        Args:
            original_data: Original 1-second data
            aggregated_data: Aggregated data to validate
            current_time: Current timestamp
            
        Returns:
            True if no look-ahead detected, False otherwise
        """
        # Check 1: No data beyond current time
        # Note: With 'right' label, the last bar may be labeled at current_time
        # but should only contain data UP TO current_time
        if not aggregated_data.empty:
            latest_time = aggregated_data.index.max()
            # Allow the label to be at current_time (due to right labeling)
            # but not beyond
            if latest_time > current_time:
                self.logger.error(
                    f"Look-ahead detected: Aggregated data extends to {latest_time}, "
                    f"beyond current time {current_time}"
                )
                return False
        
        # Check 2: Verify labeling (spot check)
        if len(aggregated_data) > 0 and len(original_data) > 0:
            # Get a sample aggregated bar
            sample_bar_time = aggregated_data.index[0]
            
            # Find corresponding raw data
            if '60s' in str(aggregated_data.index.freq) or 'T' in str(aggregated_data.index.freq):
                # For minute or second aggregation
                bar_start = sample_bar_time - pd.Timedelta(minutes=1)
                bar_end = sample_bar_time
                
                raw_in_bar = original_data[
                    (original_data.index > bar_start) & 
                    (original_data.index <= bar_end)
                ]
                
                if len(raw_in_bar) > 0:
                    # The bar label should be at or after the last raw data point
                    last_raw_time = raw_in_bar.index.max()
                    if sample_bar_time < last_raw_time:
                        self.logger.error(
                            f"Look-ahead detected: Bar labeled {sample_bar_time} "
                            f"contains data up to {last_raw_time}"
                        )
                        return False
        
        return True
    
    def prepare_features_for_regime(
        self,
        data_60s: pd.DataFrame,
        current_time: datetime
    ) -> Dict[str, Any]:
        """
        Prepare feature dictionary for regime detection.
        
        Args:
            data_60s: 60-second aggregated data
            current_time: Current timestamp
            
        Returns:
            Dictionary of features for regime detector
        """
        # Get the most recent 60s bar
        available_data = data_60s[data_60s.index <= current_time]
        
        if available_data.empty:
            return {}
        
        latest_bar = available_data.iloc[-1]
        
        # Map fields to expected names
        features = {
            'timestamp': current_time,
            'close': latest_bar.get('close', np.nan),
            'high': latest_bar.get('high', np.nan),
            'low': latest_bar.get('low', np.nan),
            'volume': latest_bar.get('volume', 0.0),
            # Map OBI fields
            'volume_imbalance': latest_bar.get('obi_smoothed', np.nan),
            'obi_smoothed_10': latest_bar.get('obi_smoothed', np.nan),
            'obi_smoothed_5': latest_bar.get('obi_smoothed_5', np.nan),
            'obi_smoothed_20': latest_bar.get('obi_smoothed_20', np.nan),
            # Spread fields
            'spread_mean': latest_bar.get('spread_mean', np.nan),
            'spread_std': latest_bar.get('spread_std', np.nan),
            # ATR fields
            'atr_14_sec': latest_bar.get('atr_14_sec', np.nan),
            'atr_percent_sec': latest_bar.get('atr_percent_sec', np.nan),
            # Momentum
            'ma_slope': latest_bar.get('ma_slope', np.nan),
            'ma_slope_ema_30s': latest_bar.get('ma_slope_ema_30s', np.nan),
            # Additional fields that might be needed
            'unrealised_pnl': latest_bar.get('unrealised_pnl', 0.0)
        }
        
        return features
    
    def get_name(self) -> str:
        """Get aggregator name."""
        return "Modern Data Aggregator (Look-Ahead Safe)"