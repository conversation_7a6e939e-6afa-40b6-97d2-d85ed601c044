"""
Modern Signal Engine
====================

Calculates all technical indicators required by modern strategies with:
- No hardcoded values (all from config)
- Professional pandas/pandas_ta calculations
- Robust warm-up period handling
- Look-ahead bias prevention (shift=1)
- Elite algotrading practices

This engine prepares signals specifically for the Modern TF-v3 strategy
which expects pre-calculated indicators rather than raw data.
"""

import logging
from typing import Dict, Any, Optional
import pandas as pd
import numpy as np
try:
    import pandas_ta as ta
except ImportError:
    ta = None
    logging.warning("pandas_ta not available, using fallback calculations")

from ..config.settings import Config
from ..features.indicators import calculate_ema, calculate_atr


class ModernSignalEngine:
    """
    Modern signal engine for calculating technical indicators.
    
    Key features:
    - All parameters from configuration (no hardcoded values)
    - Uses pandas_ta when available, fallback to custom implementations
    - Handles warm-up periods properly
    - Prevents look-ahead bias with shift=1
    - Provides all signals required by Modern TF-v3
    """
    
    def __init__(self, config: Config):
        """
        Initialize signal engine with configuration.
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.tf_v3_config = config.tf_v3
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Extract all parameters from config - NO HARDCODED VALUES
        self.ema_fast = getattr(self.tf_v3_config, 'ema_fast', 12)
        self.ema_slow = getattr(self.tf_v3_config, 'ema_slow', 26)
        # CRITICAL FIX: Use ema_slow as baseline (TF-v3 uses slow EMA as baseline)
        self.ema_baseline = getattr(self.tf_v3_config, 'ema_baseline', self.ema_slow)
        self.atr_period = getattr(self.tf_v3_config, 'atr_period', 14)
        self.rsi_period = getattr(self.tf_v3_config, 'rsi_length', 14)
        self.bb_period = getattr(self.tf_v3_config, 'bb_length', 20)
        self.bb_std = getattr(self.tf_v3_config, 'bb_std', 2.0)
        
        # Warm-up configuration
        self.warmup_mode = getattr(self.tf_v3_config, 'warmup_mode', 'auto')
        self.min_warmup_periods = getattr(self.tf_v3_config, 'min_warmup_periods', 50)
        
        # Look-ahead prevention
        self.shift_periods = getattr(self.tf_v3_config, 'shift_periods', 1)
        
        self.logger.info(
            f"ModernSignalEngine initialized:\n"
            f"  - EMA periods: {self.ema_fast}/{self.ema_slow}/{self.ema_baseline}\n"
            f"  - ATR period: {self.atr_period}\n"
            f"  - RSI period: {self.rsi_period}\n"
            f"  - BB period: {self.bb_period} (std: {self.bb_std})\n"
            f"  - Warm-up mode: {self.warmup_mode}\n"
            f"  - Shift periods: {self.shift_periods}"
        )
    
    def calculate_required_lookback(self) -> int:
        """
        Calculate the warm-up period needed for all indicators.
        
        Follows the pattern from legacy SignalEngine:
        - Takes the maximum of all indicator periods
        - Applies a buffer factor of 1.5x
        - Adds additional safety buffer
        
        Returns:
            Number of periods needed for warm-up
        """
        # Get all indicator periods
        periods = [
            self.ema_fast,
            self.ema_slow,
            self.ema_baseline,
            self.atr_period,
            self.rsi_period,
            self.bb_period
        ]
        
        # Maximum period needed
        max_period = max(periods)
        
        if self.warmup_mode == 'auto':
            # Follow legacy pattern: max * 1.5 + buffer
            warmup_needed = int(max_period * 1.5) + 50
        else:
            # Manual mode - use configured value
            warmup_needed = max(self.min_warmup_periods, max_period)
        
        self.logger.debug(f"Calculated warm-up period: {warmup_needed} bars")
        return warmup_needed
    
    def calculate_signals(self,
                         ohlcv_df: pd.DataFrame,
                         regime_features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate all signals required by Modern TF-v3.
        
        Uses pandas_ta when available, falls back to custom implementations.
        All calculations include look-ahead prevention with shift.
        
        Args:
            ohlcv_df: DataFrame with OHLCV data (must have sufficient history)
            regime_features: Current regime information
            
        Returns:
            Dictionary with all calculated signals
        """
        if ohlcv_df.empty or len(ohlcv_df) < 2:
            self.logger.warning("Insufficient data for signal calculation")
            return self._empty_signals()
        
        # Ensure we have a copy to avoid modifying original
        df = ohlcv_df.copy()
        
        # Calculate all indicators
        try:
            # EMAs - using custom implementation for consistency
            ema_fast = calculate_ema(df, self.ema_fast, shift=self.shift_periods)
            ema_slow = calculate_ema(df, self.ema_slow, shift=self.shift_periods)
            ema_baseline = calculate_ema(df, self.ema_baseline, shift=self.shift_periods)
            
            # CRITICAL: Calculate forecast indicator (missing piece!)
            # This is what reduces trades from 1000+ to ~180
            forecast = ema_fast - ema_slow
            
            # ATR - check if pre-computed values are available
            if 'atr_14_sec' in df.columns and not df['atr_14_sec'].isna().all():
                # Use pre-computed ATR from enhanced data
                atr = df['atr_14_sec'].copy()
                atr_percent = df['atr_percent_sec'].copy() if 'atr_percent_sec' in df.columns else (atr / df['close']) * 100
                # Apply shift to pre-computed values to avoid look-ahead
                if self.shift_periods > 0:
                    atr = atr.shift(self.shift_periods)
                    atr_percent = atr_percent.shift(self.shift_periods)
                self.logger.debug("Using pre-computed ATR from enhanced data")
            else:
                # Calculate ATR
                atr = calculate_atr(df, self.atr_period, shift=self.shift_periods)
                # ATR Percent (volatility measure)
                atr_percent = (atr / df['close']) * 100
            
            # RSI - use pandas_ta if available, otherwise calculate manually
            rsi = None
            use_manual_rsi = False
            
            if ta is not None:
                try:
                    rsi = ta.rsi(df['close'], length=self.rsi_period)
                    if rsi is not None and self.shift_periods > 0:
                        rsi = rsi.shift(self.shift_periods)
                    elif rsi is None:
                        # pandas_ta failed, fall back to manual calculation
                        use_manual_rsi = True
                except Exception:
                    # pandas_ta failed, fall back to manual calculation
                    use_manual_rsi = True
            else:
                use_manual_rsi = True
            
            if use_manual_rsi:
                # Manual RSI calculation
                delta = df['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_period).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                if self.shift_periods > 0:
                    rsi = rsi.shift(self.shift_periods)
            
            # Bollinger Bands - use pandas_ta if available, otherwise calculate manually
            if ta is not None:
                bb = ta.bbands(df['close'], length=self.bb_period, std=self.bb_std)
                if bb is not None and not bb.empty:
                    bb_lower = bb[f'BBL_{self.bb_period}_{self.bb_std}']
                    bb_middle = bb[f'BBM_{self.bb_period}_{self.bb_std}']
                    bb_upper = bb[f'BBU_{self.bb_period}_{self.bb_std}']
                    if self.shift_periods > 0:
                        bb_lower = bb_lower.shift(self.shift_periods)
                        bb_middle = bb_middle.shift(self.shift_periods)
                        bb_upper = bb_upper.shift(self.shift_periods)
                else:
                    # Fallback to manual calculation
                    bb_middle = df['close'].rolling(window=self.bb_period).mean()
                    bb_std = df['close'].rolling(window=self.bb_period).std()
                    bb_upper = bb_middle + (bb_std * self.bb_std)
                    bb_lower = bb_middle - (bb_std * self.bb_std)
                    if self.shift_periods > 0:
                        bb_lower = bb_lower.shift(self.shift_periods)
                        bb_middle = bb_middle.shift(self.shift_periods)
                        bb_upper = bb_upper.shift(self.shift_periods)
            else:
                # Manual Bollinger Bands calculation
                bb_middle = df['close'].rolling(window=self.bb_period).mean()
                bb_std = df['close'].rolling(window=self.bb_period).std()
                bb_upper = bb_middle + (bb_std * self.bb_std)
                bb_lower = bb_middle - (bb_std * self.bb_std)
                if self.shift_periods > 0:
                    bb_lower = bb_lower.shift(self.shift_periods)
                    bb_middle = bb_middle.shift(self.shift_periods)
                    bb_upper = bb_upper.shift(self.shift_periods)
            
            # Get the latest values (last row)
            # CRITICAL FIX: When using shift=1, we need to get the second-to-last row
            # for indicators, as the last row will have NaN due to the shift
            latest_idx = -1
            indicator_idx = -2 if self.shift_periods > 0 and len(df) > 1 else -1
            
            # Prepare signals dictionary
            signals = {
                # OHLCV data (current candle - always use latest)
                'timestamp': df.index[latest_idx] if hasattr(df.index, '__getitem__') else None,
                'open': df['open'].iloc[latest_idx],
                'high': df['high'].iloc[latest_idx],
                'low': df['low'].iloc[latest_idx],
                'close': df['close'].iloc[latest_idx],
                'volume': df['volume'].iloc[latest_idx] if 'volume' in df.columns else 0,
                
                # EMAs - use indicator_idx to handle shift
                'ema_fast': ema_fast.iloc[indicator_idx] if not ema_fast.empty and len(ema_fast) > abs(indicator_idx) else np.nan,
                'ema_slow': ema_slow.iloc[indicator_idx] if not ema_slow.empty and len(ema_slow) > abs(indicator_idx) else np.nan,
                'ema_baseline': ema_baseline.iloc[indicator_idx] if not ema_baseline.empty and len(ema_baseline) > abs(indicator_idx) else np.nan,
                
                # CRITICAL: Include forecast indicator
                'forecast': forecast.iloc[indicator_idx] if not forecast.empty and len(forecast) > abs(indicator_idx) else np.nan,
                
                # Add previous EMA values for momentum calculation (early entry logic)
                'ema_fast_prev': ema_fast.iloc[indicator_idx-1] if not ema_fast.empty and len(ema_fast) > abs(indicator_idx-1) and indicator_idx > 0 else np.nan,
                'ema_slow_prev': ema_slow.iloc[indicator_idx-1] if not ema_slow.empty and len(ema_slow) > abs(indicator_idx-1) and indicator_idx > 0 else np.nan,
                
                # ATR - use indicator_idx to handle shift
                'atr_14': atr.iloc[indicator_idx] if not atr.empty and len(atr) > abs(indicator_idx) else np.nan,
                'atr_percent': atr_percent.iloc[indicator_idx] if not atr_percent.empty and len(atr_percent) > abs(indicator_idx) else np.nan,
                
                # RSI - use indicator_idx to handle shift
                'rsi': rsi.iloc[indicator_idx] if not rsi.empty and len(rsi) > abs(indicator_idx) else np.nan,
                
                # Bollinger Bands - use indicator_idx to handle shift
                'bb_upper': bb_upper.iloc[indicator_idx] if not bb_upper.empty and len(bb_upper) > abs(indicator_idx) else np.nan,
                'bb_middle': bb_middle.iloc[indicator_idx] if not bb_middle.empty and len(bb_middle) > abs(indicator_idx) else np.nan,
                'bb_lower': bb_lower.iloc[indicator_idx] if not bb_lower.empty and len(bb_lower) > abs(indicator_idx) else np.nan,
                
                # Regime information
                'regime_features': regime_features,
                'regime_state': regime_features.get('current_state'),
                'regime_confidence': regime_features.get('current_confidence', 0.0),
                'regime_duration_minutes': regime_features.get('state_duration_minutes', 0),
                'risk_suppressed': regime_features.get('risk_suppressed', False),
                
                # Additional regime stats
                'dominant_regime_1h': regime_features.get('dominant_state_1h'),
                'regime_changes_1h': regime_features.get('state_changes_1h', 0),
                'avg_momentum_1h': regime_features.get('avg_momentum_1h', 0.0),
                'avg_volatility_1h': regime_features.get('avg_volatility_1h', 0.0),
                
                # Include OHLCV data for strategy to calculate fast EMAs
                'ohlcv_data': df[['open', 'high', 'low', 'close', 'volume']] if not df.empty else None,
            }
            
            # Log signal quality (skip DataFrame objects)
            nan_count = sum(1 for k, v in signals.items() 
                          if k != 'ohlcv_data' and k != 'regime_features' and pd.isna(v))
            if nan_count > 0:
                self.logger.debug(f"Signals contain {nan_count} NaN values")
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Error calculating signals: {e}", exc_info=True)
            return self._empty_signals()
    
    def _empty_signals(self) -> Dict[str, Any]:
        """Return empty signals dictionary with NaN values."""
        return {
            'timestamp': None,
            'open': np.nan,
            'high': np.nan,
            'low': np.nan,
            'close': np.nan,
            'volume': 0,
            'ema_fast': np.nan,
            'ema_slow': np.nan,
            'ema_baseline': np.nan,
            'forecast': np.nan,
            'atr_14': np.nan,
            'atr_percent': np.nan,
            'rsi': np.nan,
            'bb_upper': np.nan,
            'bb_middle': np.nan,
            'bb_lower': np.nan,
            'regime_features': {},
            'regime_state': None,
            'regime_confidence': 0.0,
            'regime_duration_minutes': 0,
            'risk_suppressed': False,
            'dominant_regime_1h': None,
            'regime_changes_1h': 0,
            'avg_momentum_1h': 0.0,
            'avg_volatility_1h': 0.0,
            'ema_fast_prev': np.nan,
            'ema_slow_prev': np.nan,
            'ohlcv_data': None,
        }
    
    def validate_signals(self, signals: Dict[str, Any]) -> bool:
        """
        Validate that all required signals are present and valid.
        
        Args:
            signals: Signals dictionary to validate
            
        Returns:
            True if all required signals are present and valid
        """
        # Check required numeric signals
        required_numeric = [
            'close', 'high', 'low', 'volume',
            'ema_fast', 'ema_slow', 'ema_baseline',
            'atr_14', 'atr_percent', 'rsi',
            'bb_upper', 'bb_middle', 'bb_lower'
        ]
        
        for signal in required_numeric:
            if signal not in signals:
                self.logger.warning(f"Missing required signal: {signal}")
                return False
            if pd.isna(signals[signal]):
                self.logger.warning(f"Signal {signal} is NaN")
                return False
        
        # Check regime information
        if not signals.get('regime_state'):
            self.logger.warning("Missing regime state")
            return False
        
        return True