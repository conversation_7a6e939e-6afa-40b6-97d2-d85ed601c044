"""
Robust Data Loader for Modern System
=====================================

This loader combines the best of both worlds:
- Modern architecture and performance optimizations
- Legacy robustness and defensive programming

Key improvements over legacy:
1. Unified fallback chain instead of scattered error handling
2. Clear data availability checking upfront
3. Better logging and diagnostics
4. Maintains modern adapter pattern for transformations

CRITICAL: This loader NEVER crashes - always returns valid data.
"""

import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np

from ..core.interfaces import IDataLoader
from ..config.settings import Config
from .registry import modern_data_loader
from .adapters.data_adapter import ModernDataAdapter, AdapterConfig


@modern_data_loader("robust", version="2.0", production_ready=True)
class RobustDataLoader(IDataLoader):
    """
    Production-ready data loader that handles all real-world scenarios.
    
    Fallback chain:
    1. Enhanced hourly data (fastest)
    2. 1-second features with resampling
    3. Legacy raw2 format
    4. Synthetic data (last resort)
    
    This loader will NEVER return an empty DataFrame.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Data paths - check what's actually available
        self.feature_1s_path = Path(config.data_paths.feature_1s_dir)
        self.enhanced_hourly_path = self.feature_1s_path.parent / "enhanced_hourly" / "1h"
        self.legacy_l2_path = Path(config.data_paths.l2_data_root)
        self.ohlcv_path = Path(config.data_paths.ohlcv_base_path) / "1h"
        
        # Check data availability upfront
        self.has_enhanced = self.enhanced_hourly_path.exists()
        self.has_features_1s = self.feature_1s_path.exists()
        self.has_legacy = self.legacy_l2_path.exists()
        self.has_ohlcv = self.ohlcv_path.exists()
        
        # Initialize data adapter
        adapter_config = AdapterConfig(
            handle_missing_with_defaults=True,
            log_transformations=True,
            compute_derived_fields=True
        )
        self.data_adapter = ModernDataAdapter(adapter_config)
        
        # Track last known price for synthetic data
        self.last_known_price = 50000.0  # BTC default
        
        self.logger.info(
            f"Robust Data Loader initialized:\n"
            f"  - Enhanced hourly: {'✓' if self.has_enhanced else '✗'} {self.enhanced_hourly_path}\n"
            f"  - Features 1s: {'✓' if self.has_features_1s else '✗'} {self.feature_1s_path}\n"
            f"  - Legacy raw2: {'✓' if self.has_legacy else '✗'} {self.legacy_l2_path}\n"
            f"  - OHLCV: {'✓' if self.has_ohlcv else '✗'} {self.ohlcv_path}"
        )
        
    def load_data(self, start_date: datetime, end_date: datetime,
                  symbols: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Load data with comprehensive fallback mechanism.
        
        GUARANTEE: Always returns a valid DataFrame, never empty.
        """
        self.logger.info(f"Loading data from {start_date} to {end_date}")
        
        # Check what data is actually available in the date range
        available_start = self._find_earliest_available_data(start_date)
        if available_start > start_date:
            self.logger.warning(
                f"Requested data from {start_date}, but earliest available is {available_start}. "
                f"Adjusting start date."
            )
            start_date = available_start
        
        # Try each data source in order of preference
        
        # 1. Enhanced hourly (fastest)
        if self.has_enhanced:
            df = self._try_load_enhanced_hourly(start_date, end_date)
            if not df.empty:
                self.logger.info(f"Loaded {len(df)} rows from enhanced hourly data")
                return df
        
        # 2. Features 1s with resampling
        if self.has_features_1s:
            df = self._try_load_features_1s(start_date, end_date)
            if not df.empty:
                self.logger.info(f"Loaded {len(df)} rows from 1s features (resampled)")
                return df
        
        # 3. Legacy raw2 format
        if self.has_legacy:
            df = self._try_load_legacy_format(start_date, end_date)
            if not df.empty:
                self.logger.info(f"Loaded {len(df)} rows from legacy raw2 data")
                return df
        
        # 4. Ultimate fallback: synthetic data
        self.logger.warning(
            f"No real data available for {start_date} to {end_date}. "
            f"Creating synthetic data to prevent crashes."
        )
        return self._create_synthetic_data(start_date, end_date)
    
    def load_hourly_features(self, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """Load hourly features using the same robust mechanism."""
        return self.load_data(start_time, end_time)
    
    def _find_earliest_available_data(self, requested_start: datetime) -> datetime:
        """Find the earliest date we actually have data for."""
        earliest = requested_start
        
        # Check each data source
        for path in [self.enhanced_hourly_path, self.feature_1s_path, 
                     self.legacy_l2_path, self.ohlcv_path]:
            if path.exists():
                try:
                    # Look for date patterns in filenames
                    dates = []
                    for file in path.iterdir():
                        if file.is_file() and file.suffix == '.parquet':
                            # Try to extract date from filename
                            parts = file.stem.split('_')
                            for part in parts:
                                try:
                                    # Try YYYY-MM-DD format
                                    if '-' in part:
                                        date = datetime.strptime(part, '%Y-%m-%d')
                                        dates.append(date)
                                    # Try YYYYMMDD format
                                    elif len(part) == 8 and part.isdigit():
                                        date = datetime.strptime(part, '%Y%m%d')
                                        dates.append(date)
                                except ValueError:
                                    continue
                    
                    if dates:
                        source_earliest = min(dates)
                        if source_earliest < earliest:
                            earliest = source_earliest
                            
                except Exception as e:
                    self.logger.debug(f"Error scanning {path}: {e}")
        
        return earliest
    
    def _try_load_enhanced_hourly(self, start: datetime, end: datetime) -> pd.DataFrame:
        """Try to load enhanced hourly data."""
        try:
            all_data = []
            current = start.date()
            
            while current <= end.date():
                file_name = f"{current.strftime('%Y-%m-%d')}_1h_enhanced.parquet"
                file_path = self.enhanced_hourly_path / file_name
                
                if file_path.exists():
                    df = pd.read_parquet(file_path)
                    if not df.empty:
                        all_data.append(df)
                
                current += timedelta(days=1)
            
            if all_data:
                combined = pd.concat(all_data, ignore_index=False)
                combined = combined.sort_index()
                
                # Ensure required columns
                combined = self._ensure_required_columns(combined)
                
                # Filter to date range
                mask = (combined.index >= start) & (combined.index < end)
                return combined[mask]
                
        except Exception as e:
            self.logger.error(f"Error loading enhanced hourly: {e}")
        
        return pd.DataFrame()
    
    def _try_load_features_1s(self, start: datetime, end: datetime) -> pd.DataFrame:
        """Try to load 1s features and resample to hourly."""
        try:
            all_data = []
            current = start.date()
            
            while current <= end.date():
                date_dir = self.feature_1s_path / current.strftime('%Y-%m-%d')
                
                if date_dir.exists():
                    for hour_file in sorted(date_dir.glob('features_*.parquet')):
                        try:
                            df = pd.read_parquet(hour_file)
                            if not df.empty:
                                all_data.append(df)
                        except Exception as e:
                            self.logger.debug(f"Error reading {hour_file}: {e}")
                
                current += timedelta(days=1)
            
            if all_data:
                combined = pd.concat(all_data, ignore_index=True)
                
                # Handle timestamps and duplicates
                if 'timestamp' in combined.columns:
                    combined['timestamp'] = pd.to_datetime(combined['timestamp'])
                    combined = combined.drop_duplicates(subset=['timestamp'], keep='last')
                    combined = combined.set_index('timestamp')
                
                # Resample to hourly
                hourly = self._resample_to_hourly(combined)
                
                # Apply adapter
                if not hourly.empty:
                    hourly = self.data_adapter.adapt_features_dataframe(hourly)
                
                # Filter to date range
                mask = (hourly.index >= start) & (hourly.index < end)
                return hourly[mask]
                
        except Exception as e:
            self.logger.error(f"Error loading 1s features: {e}")
        
        return pd.DataFrame()
    
    def _try_load_legacy_format(self, start: datetime, end: datetime) -> pd.DataFrame:
        """Try to load legacy raw2 format with microstructure features."""
        try:
            # Import legacy loader components
            from ..legacy.data_loader import LegacyDataLoader
            
            # Create temporary legacy loader
            legacy_loader = LegacyDataLoader(self.config)
            
            # Use legacy loading mechanism
            df = legacy_loader.load_data(start, end)
            
            if not df.empty:
                # Ensure we have required columns
                df = self._ensure_required_columns(df)
                return df
                
        except Exception as e:
            self.logger.error(f"Error loading legacy format: {e}")
        
        return pd.DataFrame()
    
    def _resample_to_hourly(self, df: pd.DataFrame) -> pd.DataFrame:
        """Resample 1-second data to hourly."""
        if df.empty:
            return df
        
        # Define aggregation rules
        agg_dict = {
            # OHLCV
            'open': 'first',
            'high': 'max',
            'low': 'min', 
            'close': 'last',
            'volume': 'sum',
            
            # Microstructure - use mean
            'volume_imbalance': 'mean',
            'obi_smoothed': 'mean',
            'spread_mean': 'mean',
            'spread_std': 'mean',
            
            # Technical indicators - use last
            'atr_14_sec': 'last',
            'atr_percent_sec': 'last',
            'ma_slope': 'last',
            'ma_slope_ema_30s': 'last',
        }
        
        # Only include columns that exist
        agg_dict = {k: v for k, v in agg_dict.items() if k in df.columns}
        
        if not agg_dict:
            self.logger.error("No valid columns to aggregate")
            return pd.DataFrame()
        
        # Resample
        hourly = df.resample('1h', label='right', closed='left').agg(agg_dict)
        
        # Drop rows with all NaN
        hourly = hourly.dropna(how='all')
        
        # Add timestamp column
        hourly['timestamp'] = hourly.index
        
        return hourly
    
    def _create_synthetic_data(self, start: datetime, end: datetime) -> pd.DataFrame:
        """
        Create synthetic data as last resort.
        
        This is better than crashing! The system can still run with NaN features.
        """
        # Generate hourly timestamps
        timestamps = pd.date_range(start=start, end=end, freq='h', inclusive='left')
        
        if len(timestamps) == 0:
            # Edge case: very short time range
            timestamps = [start]
        
        # Create DataFrame
        df = pd.DataFrame(index=timestamps)
        
        # Add OHLCV data (using last known or default)
        df['open'] = self.last_known_price
        df['high'] = self.last_known_price
        df['low'] = self.last_known_price
        df['close'] = self.last_known_price
        df['volume'] = 0.0
        
        # Add required microstructure features as NaN
        feature_columns = [
            'volume_imbalance', 'spread_mean', 'spread_std',
            'obi_5', 'obi_20', 'depth_ratio_5', 'depth_pressure_5',
            'atr_14_sec', 'atr_percent_sec', 'ma_slope', 'ma_slope_ema_30s'
        ]
        
        for col in feature_columns:
            df[col] = np.nan
        
        # Add metadata
        df['timestamp'] = df.index
        df['is_synthetic'] = True
        
        # Log what we created
        self.logger.warning(
            f"Created {len(df)} hours of synthetic data. "
            f"Price={self.last_known_price}, Features=NaN"
        )
        
        return df
    
    def _ensure_required_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Ensure DataFrame has all required columns."""
        if df.empty:
            return df
        
        # Required columns
        required = {
            'timestamp': lambda: df.index,
            'open': lambda: df['close'] if 'close' in df else self.last_known_price,
            'high': lambda: df['close'] if 'close' in df else self.last_known_price,
            'low': lambda: df['close'] if 'close' in df else self.last_known_price,
            'close': lambda: self.last_known_price,
            'volume': lambda: 0.0,
            'volume_imbalance': lambda: np.nan,
            'spread_mean': lambda: np.nan,
            'atr_14_sec': lambda: np.nan,
            'atr_percent_sec': lambda: np.nan,
        }
        
        # Add missing columns
        for col, default_func in required.items():
            if col not in df.columns:
                df[col] = default_func()
        
        # Update last known price if we have close prices
        if 'close' in df.columns and not df['close'].isna().all():
            last_close = df['close'].dropna().iloc[-1]
            if last_close > 0:
                self.last_known_price = last_close
        
        return df
    
    def get_required_columns(self) -> List[str]:
        """Get list of required columns."""
        return [
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'volume_imbalance', 'spread_mean', 'spread_std',
            'atr_14_sec', 'atr_percent_sec'
        ]
    
    def validate_data(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate loaded data."""
        issues = []
        
        if df.empty:
            issues.append("DataFrame is empty")
            return False, issues
        
        # Check for required OHLCV columns
        ohlcv_cols = ['open', 'high', 'low', 'close']
        for col in ohlcv_cols:
            if col not in df.columns:
                issues.append(f"Missing required column: {col}")
            elif df[col].isna().all():
                issues.append(f"Column '{col}' is all NaN")
        
        # Check if data is synthetic
        if 'is_synthetic' in df.columns and df['is_synthetic'].any():
            synthetic_count = df['is_synthetic'].sum()
            issues.append(f"Contains {synthetic_count} hours of synthetic data")
        
        # Warning for missing features (not critical)
        if 'volume_imbalance' in df.columns and df['volume_imbalance'].isna().all():
            self.logger.warning("All microstructure features are NaN - strategy may not work properly")
        
        is_valid = len([i for i in issues if not i.startswith("Contains")]) == 0
        return is_valid, issues