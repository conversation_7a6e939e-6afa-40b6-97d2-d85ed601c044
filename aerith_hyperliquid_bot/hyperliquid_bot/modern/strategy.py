"""
Modern TF-v3 Strategy
=======================
This is the modern trend following strategy with enhanced features
including OBI filtering, funding filters, and dynamic risk management.

Key Features:
- EMA crossover signals
- Regime gating (BULL/BEAR only)
- Risk suppression checks
- OBI filtering support
- Funding rate filtering
- Dynamic stop losses
- ATR-based position sizing

FROZEN: This represents the modern strategy implementation.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Literal, Any
import pandas as pd
import numpy as np

from ..core.interfaces import IStrategy
from .registry import modern_strategy
from ..config.settings import Config
from ..features.indicators import calculate_ema, calculate_atr


@modern_strategy("tf_v3", version="1.0", experimental=True)  
class ModernTFV3Strategy(IStrategy):
    """
    Modern implementation of the Trend Following v3 Strategy.
    
    This strategy enhances the basic trend following with:
    - OBI (Order Book Imbalance) filtering
    - Funding rate filtering
    - Dynamic risk management
    - Regime confidence checks
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Core configuration
        self.tf_v3_config = config.tf_v3
        self.regime_config = config.regime
        
        # Strategy parameters
        self.ema_fast = self.tf_v3_config.ema_fast  # Default: 8
        self.ema_slow = self.tf_v3_config.ema_slow  # Default: 21
        self.atr_period = self.tf_v3_config.atr_period  # Default: 14
        
        # Risk parameters (CRITICAL: 2% risk per trade)
        self.risk_frac = self.tf_v3_config.risk_frac  # Default: 0.02
        self.max_notional = self.tf_v3_config.max_notional
        
        # Stop loss configuration
        self.atr_trail_k = self.tf_v3_config.atr_trail_k  # Default: 1.5
        self.stop_loss_atr_mult = getattr(self.tf_v3_config, 'stop_loss_atr_mult', 1.5)
        self.use_dynamic_stops = getattr(self.tf_v3_config, 'use_dynamic_stops', True)
        
        # Take profit configuration
        self.take_profit_atr_mult = getattr(self.tf_v3_config, 'take_profit_atr_mult', 3.0)
        self.use_dynamic_tp = getattr(self.tf_v3_config, 'use_dynamic_tp', True)
        
        # Enhanced filters
        self.use_obi_filter = getattr(config.strategies, 'tf_use_obi_filter', True)
        self.use_funding_filter = getattr(config.strategies, 'tf_use_funding_filter', True)
        
        # Regime filters
        self.min_regime_confidence = getattr(self.tf_v3_config, 'min_regime_confidence', 0.0)
        self.min_regime_duration = getattr(self.tf_v3_config, 'min_regime_duration', 0.0)
        
        # GMS snapshot parameters
        self.gms_max_age_sec = self.tf_v3_config.gms_max_age_sec  # Default: 600
        
        # State tracking
        self.state = {
            'entry_price': None,
            'entry_time': None,
            'position_type': None,
            'trail_price': None,
            'stop_loss': None,
            'take_profit': None,
        }
        
        # Counters for diagnostics
        self.eval_count = 0
        self.fail_missing_signal = 0
        self.fail_regime_gate = 0
        self.fail_risk_suppressed = 0
        self.fail_gms_stale = 0
        self.fail_ema_alignment = 0
        self.fail_obi_filter = 0
        self.fail_funding_filter = 0
        self.success_entry_long = 0
        self.success_entry_short = 0
        
        self.logger.info(
            f"Modern TF-v3 Strategy initialized:\n"
            f"  - EMAs: {self.ema_fast}/{self.ema_slow}\n"
            f"  - Risk fraction: {self.risk_frac:.1%}\n"
            f"  - Stop loss: {self.stop_loss_atr_mult}x ATR\n"
            f"  - Take profit: {self.take_profit_atr_mult}x ATR\n"
            f"  - OBI filter: {self.use_obi_filter}\n"
            f"  - Funding filter: {self.use_funding_filter}"
        )
    
    @property
    def required_signals(self) -> List[str]:
        """Return list of required signals."""
        signals = [
            'timestamp',
            'open', 'high', 'low', 'close', 'volume',
            'regime',  # Mapped regime from evaluator
            'regime_timestamp',
            'risk_suppressed',
            f'atr_{self.atr_period}',  # Or calculated internally
            'gms_snapshot',  # Full GMS state
        ]
        
        # Optional enhanced signals
        if self.use_obi_filter:
            signals.extend([
                'obi_smoothed',  # Order book imbalance
                'obi_zscore',
            ])
        
        if self.use_funding_filter:
            signals.append('funding_rate')
        
        return signals
    
    def evaluate(self, signals: Dict[str, Any], portfolio: Any = None) -> Tuple[Optional[str], Optional[Dict]]:
        """
        Evaluate market conditions for entry signals.
        
        Args:
            signals: Market signals dictionary
            portfolio: Portfolio instance (optional)
            
        Returns:
            Tuple of (direction, info) or (None, None)
        """
        self.eval_count += 1
        
        # Check for required signals
        missing = [s for s in self.required_signals if s not in signals or pd.isna(signals.get(s))]
        if missing:
            self.fail_missing_signal += 1
            self.logger.debug(f"Missing signals: {missing}")
            return None, None
        
        # Get GMS snapshot
        gms_snapshot = signals.get('gms_snapshot', {})
        
        # 1. Check GMS staleness
        current_time = signals.get('timestamp')
        snapshot_time = gms_snapshot.get('timestamp', current_time)
        
        if isinstance(current_time, (int, float)):
            current_time = datetime.fromtimestamp(current_time)
        if isinstance(snapshot_time, (int, float)):
            snapshot_time = datetime.fromtimestamp(snapshot_time)
        
        age_seconds = (current_time - snapshot_time).total_seconds()
        if age_seconds > self.gms_max_age_sec:
            self.fail_gms_stale += 1
            self.logger.debug(f"GMS stale: {age_seconds:.1f}s > {self.gms_max_age_sec}s")
            return None, None
        
        # 2. Check risk suppression
        if gms_snapshot.get('risk_suppressed', False):
            self.fail_risk_suppressed += 1
            self.logger.debug("Risk suppressed")
            return None, None
        
        # 3. Check regime (use mapped regime from evaluator)
        regime = signals.get('regime', 'Unknown')
        if regime not in ['BULL', 'BEAR']:
            self.fail_regime_gate += 1
            self.logger.debug(f"Neutral regime: {regime}")
            return None, None
        
        # 4. Check regime confidence if configured
        if self.min_regime_confidence > 0:
            confidence = gms_snapshot.get('regime_confidence', 1.0)
            if confidence < self.min_regime_confidence:
                self.logger.debug(f"Low confidence: {confidence:.2f} < {self.min_regime_confidence}")
                return None, None
        
        # 5. Check regime duration if configured
        if self.min_regime_duration > 0:
            duration_min = gms_snapshot.get('regime_duration_minutes', 0.0)
            if duration_min < self.min_regime_duration:
                self.logger.debug(f"Regime too new: {duration_min:.1f}min < {self.min_regime_duration}min")
                return None, None
        
        # 6. Calculate indicators
        indicators = self._calculate_indicators(signals)
        
        # 7. Check EMA alignment
        direction = self._check_ema_alignment(indicators, regime)
        if direction is None:
            self.fail_ema_alignment += 1
            return None, None
        
        # 8. Apply enhanced filters
        if self.use_obi_filter and not self._check_obi_filter(signals, direction):
            self.fail_obi_filter += 1
            self.logger.debug(f"OBI filter failed for {direction}")
            return None, None
        
        if self.use_funding_filter and not self._check_funding_filter(signals, direction):
            self.fail_funding_filter += 1
            self.logger.debug(f"Funding filter failed for {direction}")
            return None, None
        
        # 9. Calculate position info
        position_info = self._calculate_position_info(indicators, direction)
        
        # Update state
        self.state['entry_price'] = signals.get('close')
        self.state['entry_time'] = signals.get('timestamp')
        self.state['position_type'] = direction
        self.state['trail_price'] = position_info.get('initial_stop')
        self.state['stop_loss'] = position_info.get('stop_loss')
        self.state['take_profit'] = position_info.get('take_profit')
        
        # Track success
        if direction == 'long':
            self.success_entry_long += 1
        else:
            self.success_entry_short += 1
        
        self.logger.info(
            f"{direction.upper()} signal in {regime} regime: "
            f"price={self.state['entry_price']:.2f}, "
            f"SL={self.state['stop_loss']:.2f}, "
            f"TP={self.state['take_profit']:.2f}"
        )
        
        return direction, position_info
    
    def _calculate_indicators(self, signals: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate EMAs and ATR."""
        result = signals.copy()
        
        # Get OHLCV history if available
        ohlcv_history = signals.get('ohlcv_history')
        
        if ohlcv_history is None or ohlcv_history.empty:
            # Use current values only
            result[f'ema_{self.ema_fast}'] = signals.get('close')
            result[f'ema_{self.ema_slow}'] = signals.get('close')
            result[f'atr_{self.atr_period}'] = signals.get(f'atr_{self.atr_period}', 0.0)
        else:
            # Calculate with history
            ema_fast = calculate_ema(ohlcv_history, self.ema_fast, price_col='close', shift=1)
            ema_slow = calculate_ema(ohlcv_history, self.ema_slow, price_col='close', shift=1)
            atr = calculate_atr(ohlcv_history, self.atr_period, shift=1)
            
            result[f'ema_{self.ema_fast}'] = ema_fast.iloc[-1] if not ema_fast.empty else None
            result[f'ema_{self.ema_slow}'] = ema_slow.iloc[-1] if not ema_slow.empty else None
            result[f'atr_{self.atr_period}'] = atr.iloc[-1] if not atr.empty else None
        
        # Fallback to direct ATR if calculation failed
        if pd.isna(result[f'atr_{self.atr_period}']):
            direct_atr = signals.get(f'atr_{self.atr_period}')
            if not pd.isna(direct_atr):
                result[f'atr_{self.atr_period}'] = direct_atr
            else:
                # Try atr_14_sec
                atr_sec = signals.get('atr_14_sec')
                if not pd.isna(atr_sec):
                    result[f'atr_{self.atr_period}'] = atr_sec
        
        return result
    
    def _check_ema_alignment(self, signals: Dict[str, Any], regime: str) -> Optional[str]:
        """Check if EMAs align with regime."""
        ema_fast = signals.get(f'ema_{self.ema_fast}')
        ema_slow = signals.get(f'ema_{self.ema_slow}')
        
        if pd.isna(ema_fast) or pd.isna(ema_slow):
            return None
        
        # Check crossover alignment
        if regime == 'BULL' and ema_fast > ema_slow:
            return 'long'
        elif regime == 'BEAR' and ema_fast < ema_slow:
            return 'short'
        
        return None
    
    def _check_obi_filter(self, signals: Dict[str, Any], direction: str) -> bool:
        """Check OBI filter if enabled."""
        obi = signals.get('obi_smoothed', 0.0)
        obi_zscore = signals.get('obi_zscore', 0.0)
        
        # Use z-score if available, otherwise raw OBI
        obi_value = obi_zscore if abs(obi_zscore) > 0 else obi
        
        # OBI should align with direction
        if direction == 'long':
            return obi_value > 0.05  # Slight buy pressure
        else:
            return obi_value < -0.05  # Slight sell pressure
    
    def _check_funding_filter(self, signals: Dict[str, Any], direction: str) -> bool:
        """Check funding rate filter if enabled."""
        funding = signals.get('funding_rate', 0.0)
        
        # Funding should not be extreme against position
        if direction == 'long':
            return funding < 0.001  # Not extremely positive
        else:
            return funding > -0.001  # Not extremely negative
    
    def _calculate_position_info(self, signals: Dict[str, Any], direction: str) -> Dict[str, Any]:
        """Calculate position sizing and risk parameters."""
        close = signals.get('close')
        atr = signals.get(f'atr_{self.atr_period}', 0.0)
        
        # Calculate stops and targets
        if direction == 'long':
            stop_loss = close - (atr * self.stop_loss_atr_mult)
            take_profit = close + (atr * self.take_profit_atr_mult)
            initial_stop = close - (atr * self.atr_trail_k)
        else:
            stop_loss = close + (atr * self.stop_loss_atr_mult)
            take_profit = close - (atr * self.take_profit_atr_mult)
            initial_stop = close + (atr * self.atr_trail_k)
        
        # Calculate position size based on risk
        risk_per_unit = abs(close - stop_loss)
        
        # This is placeholder - actual position sizing would use portfolio balance
        position_notional = 10000 * self.risk_frac / (risk_per_unit / close)
        
        # Apply max notional cap
        if self.max_notional > 0:
            position_notional = min(position_notional, self.max_notional)
        
        return {
            'position_notional': position_notional,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'initial_stop': initial_stop,
            'risk_per_unit': risk_per_unit,
            'atr': atr,
        }
    
    def should_exit(self, position: Dict[str, Any], current_signals: Dict[str, Any]) -> bool:
        """
        Check if position should be exited.
        
        This is a simplified version - full implementation would include:
        - Trailing stop updates
        - Time-based exits
        - Regime change exits
        """
        if not position or 'direction' not in position:
            return False
        
        direction = position['direction']
        entry_price = position.get('entry_price', 0)
        current_price = current_signals.get('close', entry_price)
        
        # Check stop loss
        stop_loss = self.state.get('stop_loss')
        if stop_loss:
            if direction == 'long' and current_price <= stop_loss:
                return True
            elif direction == 'short' and current_price >= stop_loss:
                return True
        
        # Check take profit
        take_profit = self.state.get('take_profit')
        if take_profit:
            if direction == 'long' and current_price >= take_profit:
                return True
            elif direction == 'short' and current_price <= take_profit:
                return True
        
        # Check regime change
        current_regime = current_signals.get('regime', 'Unknown')
        if current_regime not in ['BULL', 'BEAR']:
            return True  # Exit on neutral regime
        
        return False
    
    def get_name(self) -> str:
        """Get strategy name."""
        return "Modern TF-v3"
    
    def get_required_indicators(self) -> list[str]:
        """Get list of required technical indicators."""
        return [
            f'ema_{self.ema_fast}',
            f'ema_{self.ema_slow}',
            f'atr_{self.atr_period}',
        ]
    
    def evaluate_entry(self, signals: Dict[str, Any], regime: str) -> Optional[Dict[str, Any]]:
        """
        Evaluate whether to enter a position.
        
        This wraps the evaluate() method to match the interface.
        """
        direction, info = self.evaluate(signals)
        if direction is None:
            return None
        
        return {
            'direction': direction,
            'confidence': 0.8,  # Default confidence
            'position_info': info,
        }
    
    def check_exit(self, signals: Dict[str, Any], position: Dict[str, Any]) -> Optional[str]:
        """
        Check if current position should be exited.
        
        This wraps the should_exit() method to match the interface.
        """
        if self.should_exit(position, signals):
            return "Exit signal triggered"
        return None
    
    def get_position_size(self, signals: Dict[str, Any], direction: str) -> float:
        """
        Calculate position size for entry.
        
        Returns the risk fraction configured for the strategy.
        """
        return self.risk_frac
    
    def get_strategy_name(self) -> str:
        """Get strategy identifier."""
        return "modern_tf_v3"