#!/usr/bin/env python
# hyperliquid_bot/strategies/strategy_factory.py

"""
Strategy Factory for creating strategy instances.
"""

import logging
from typing import Dict, Optional, Set

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.strategies.evaluator import StrategyInterface, TrendFollowingStrategy, MeanReversionStrategy, MeanVarianceStrategy
from hyperliquid_bot.strategies.tf_v3 import TFV3Strategy
from hyperliquid_bot.strategies.obi_scalper_strategy import OBIScalperStrategy

# Supported strategies as per Task R-103
SUPPORTED_STRATEGIES: Dict[str, type] = {
    "tf_v2": TrendFollowingStrategy,
    "tf_v3": TFV3Strategy,
    "mean_reversion": MeanReversionStrategy,
    "mean_variance": MeanVarianceStrategy,
    "obi_scalper": OBIScalperStrategy,
}

# Optional alias for backward compatibility
STRATEGY_ALIASES: Dict[str, str] = {
    "trend_following": "tf_v2",  # Legacy name maps to tf_v2
}

# Supported detector types as per Task R-103
SUPPORTED_DETECTORS: Set[str] = {
    "granular_microstructure",
    "continuous_gms",
    "continuous_modern_v2",
    "rule_based",
    "enhanced"  # Enhanced detector that wraps legacy logic
}

class StrategyFactory:
    """
    Factory class for creating strategy instances.
    """

    def __init__(self, config: Config):
        """
        Initialize the strategy factory.

        Args:
            config: Configuration object
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.strategies: Dict[str, StrategyInterface] = {}

    def create_strategy(self, strategy_name: str) -> Optional[StrategyInterface]:
        """
        Create a strategy instance by name.

        Args:
            strategy_name: Name of the strategy to create

        Returns:
            Strategy instance or None if not found
        """
        self.logger.info(f"Creating strategy: {strategy_name}")

        # Check for aliases first
        resolved_name = STRATEGY_ALIASES.get(strategy_name, strategy_name)

        # Validate strategy name
        if resolved_name not in SUPPORTED_STRATEGIES:
            self.logger.error(f"Unsupported strategy: '{strategy_name}'. Supported strategies: {list(SUPPORTED_STRATEGIES.keys())}")
            raise ValueError(f"Unsupported strategy '{strategy_name}'. Supported strategies: {list(SUPPORTED_STRATEGIES.keys())}")

        # Create strategy instance
        strategy_class = SUPPORTED_STRATEGIES[resolved_name]
        try:
            return strategy_class(self.config, resolved_name)
        except Exception as e:
            self.logger.error(f"Failed to create strategy '{resolved_name}': {e}")
            raise
