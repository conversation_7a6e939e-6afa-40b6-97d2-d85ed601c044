# hyperliquid_bot/strategies/evaluator.py

import logging
from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Literal, Tuple

# Import the Config class
from hyperliquid_bot.config.settings import Config

# Import the state mapping utility
from hyperliquid_bot.utils.state_mapping import get_state_map, map_gms_state, validate_3state
# Import feature naming utilities
from hyperliquid_bot.utils.feature_naming import get_obi_column_name

# Strategy classes are imported at the end of this file or in _initialize_strategies
# to avoid circular imports

# Module-level docstring
"""
Strategy Evaluation Component.

Defines interfaces and implementations for evaluating trading strategies
based on calculated signals and market regime. Determines entry signals.
Includes diagnostic counters for evaluation analysis.
"""

# --- Strategy Interface ---
class StrategyInterface(ABC):
    """
    Abstract Base Class for all trading strategy implementations.
    """
    def __init__(self, config: Config, strategy_name: str):
        self.config = config
        self.strategy_name = strategy_name
        self.logger = logging.getLogger(f"{self.__class__.__name__}[{strategy_name}]")
        # Initialize counters common to all strategies
        self.eval_count = 0
        self.fail_missing_signal = 0
        self.success_entry_long = 0
        self.success_entry_short = 0
        self.logger.info(f"Initialized {self.__class__.__name__} for strategy '{strategy_name}'")

    @property
    @abstractmethod
    def required_signals(self) -> List[str]:
        pass

    @abstractmethod
    def evaluate(self, signals: Dict) -> Tuple[Optional[Literal["long", "short"]], Optional[Dict]]:
        # Optional: Add price_history if strategies need it
        # def evaluate(self, signals: Dict, price_history: Optional[pd.Series] = None) -> Tuple[Optional[Literal["long", "short"]], Optional[Dict]]:
        pass

    @abstractmethod
    def log_evaluation_summary(self):
        """Logs a summary of evaluation results and failures."""
        pass

    def check_exit(self, signals: Dict, position: Dict) -> Optional[str]:
        # Optional: Add price_history if strategy exit needs it
        # def check_exit(self, signals: Dict, position: Dict, price_history: Optional[pd.Series] = None) -> Optional[str]:
        return None # Default implementation

# --- Concrete Strategy Implementations ---

class TrendFollowingStrategy(StrategyInterface):
    """
    Implements the Trend Following strategy logic with optional OBI and Funding Rate filters.
    Includes diagnostic counters.
    """
    def __init__(self, config: Config, strategy_name: str):
        super().__init__(config, strategy_name)
        # Add specific counters
        self.fail_condition = 0 # Counter for when signals present but condition not met
        self.fail_forecast_filter = 0 # Counter for failing the forecast magnitude filter
        self.fail_volatility_filter = 0 # Counter for failing the volatility filter
        # NEW Filter Counters
        self.fail_obi_filter_long = 0
        self.fail_obi_filter_short = 0
        self.fail_funding_filter_long = 0
        self.fail_funding_filter_short = 0
        # Log filter status
        self.logger.info(f"  - OBI Filter Enabled: {self.config.strategies.tf_use_obi_filter}")
        self.logger.info(f"  - Funding Filter Enabled: {self.config.strategies.tf_use_funding_filter}")


    @property
    def required_signals(self) -> List[str]:
        """Define signals needed for TF logic and optional filters."""
        signals_needed = ["forecast", "tf_ewma_fast", "tf_ewma_slow", "regime", "close", "atr_tf"]
        if self.config.indicators.use_tf_medium_ewma:
            signals_needed.append("tf_ewma_medium")

        # Add signals for optional filters if enabled
        if self.config.strategies.tf_use_obi_filter:
            # Use the dynamic OBI signal name based on configured depth
            obi_depth = self.config.microstructure.obi_levels
            signals_needed.append(f"obi_smoothed_{obi_depth}")
            # Also add legacy name as fallback
            signals_needed.append("obi_smoothed")
        if self.config.strategies.tf_use_funding_filter:
            signals_needed.append("funding_rate")

        return list(set(signals_needed)) # Use set to avoid duplicates

    def evaluate(self, signals: Dict) -> Tuple[Optional[Literal["long", "short"]], Optional[Dict]]:
        """
        Evaluates the Trend Following strategy, applying optional OBI and Funding filters.
        Returns: (direction, strategy_info) - strategy_info is always None for TF.
        """
        self.eval_count += 1
        cfg_indicators = self.config.indicators
        cfg_regime = self.config.regime
        cfg_strategies = self.config.strategies # For filter toggles
        cfg_micro = self.config.microstructure # For filter thresholds

        # --- 1. Check for required signals ---
        current_req_signals = self.required_signals
        missing_or_nan_signals = [s for s in current_req_signals if pd.isna(signals.get(s))]
        if missing_or_nan_signals:
            self.fail_missing_signal += 1
            # Logging trend-following evaluation context
            current_regime = signals.get('regime')
            self.logger.info(
                f"TF Evaluate @ {signals.get('timestamp')}: Regime={current_regime}, "
                f"Missing signals: {missing_or_nan_signals}"
            )
            return None, None

        # --- 2. Extract Core Signals ---
        fc = signals["forecast"]
        fast = signals["tf_ewma_fast"]
        slow = signals["tf_ewma_slow"]
        current_regime = signals["regime"]
        price = signals["close"]
        atr = signals["atr_tf"]

        # DEBUG: Log the EMA and forecast values for every evaluation
        self.logger.info(
            f"TF DEBUG @ {signals.get('timestamp')}: Regime={current_regime}, "
            f"Fast={fast:.2f}, Slow={slow:.2f}, "
            f"Forecast={fc:.2f}, Fast-Slow={fast-slow:.2f}"
        )

        # --- 3. Determine Base Entry Signal ---
        base_signal: Optional[Literal["long", "short"]] = None
        if cfg_indicators.use_tf_medium_ewma:
            med = signals.get("tf_ewma_medium") # Already checked for NaN above if required
            # DEBUG: Log medium EMA if used
            self.logger.info(f"TF DEBUG Medium EMA: {med:.2f}, Fast-Med={fast-med:.2f}, Med-Slow={med-slow:.2f}")

            if fast > med > slow and fc > 0:
                base_signal = "long"
                self.logger.info("TF DEBUG: LONG signal generated - Fast > Med > Slow and FC > 0")
            elif fast < med < slow and fc < 0:
                base_signal = "short"
                self.logger.info("TF DEBUG: SHORT signal generated - Fast < Med < Slow and FC < 0")
            else:
                self.logger.info(
                    f"TF DEBUG: NO signal - Medium EMA conditions not met: "
                    f"Fast={fast:.2f}, Med={med:.2f}, Slow={slow:.2f}, FC={fc:.2f}, "
                    f"Fast>Med>Slow={fast>med>slow}, FC>0={fc>0}"
                )
        else:
            if fast > slow and fc > 0:
                base_signal = "long"
                self.logger.info("TF DEBUG: LONG signal generated - Fast > Slow and FC > 0")
            elif fast < slow and fc < 0:
                base_signal = "short"
                self.logger.info("TF DEBUG: SHORT signal generated - Fast < Slow and FC < 0")
            else:
                self.logger.info(
                    f"TF DEBUG: NO signal - Basic EMA conditions not met: "
                    f"Fast={fast:.2f}, Slow={slow:.2f}, FC={fc:.2f}, "
                    f"Fast>Slow={fast>slow}, FC>0={fc>0}, Fast<Slow={fast<slow}, FC<0={fc<0}"
                )

        # If no base signal, exit
        if base_signal is None:
            self.fail_condition += 1
            self.logger.debug(f"TF No Base Signal @ {signals.get('timestamp')}: Fast={signals.get('tf_ewma_fast', 'N/A'):.2f}, Slow={signals.get('tf_ewma_slow', 'N/A'):.2f}, FC={signals.get('forecast', 'N/A'):.2f}")
            return None, None

        # --- 4. Apply Hurst-Specific Filters (if applicable) ---
        # These filters only apply if Hurst detector is active AND regime is Persistent
        apply_hurst_filters = (cfg_regime.detector_type == 'hurst' and current_regime == 'Persistent')
        if apply_hurst_filters:
            # 4a. Forecast Filter
            forecast_threshold = cfg_indicators.low_forecast_threshold
            if abs(fc) < forecast_threshold:
                self.fail_forecast_filter += 1
                return None, None # Filtered out

            # 4b. Volatility Filter
            max_vol_pct = cfg_indicators.tf_max_entry_volatility_pct
            current_vol_pct = atr / price if price > 1e-9 else np.inf
            if current_vol_pct >= max_vol_pct:
                 self.fail_volatility_filter += 1
                 return None, None # Filtered out

        # --- 5. Apply Concurrent OBI and Funding Filters (if enabled) ---
        final_signal = base_signal # Start with the base signal

        # 5a. OBI Filter
        if cfg_strategies.tf_use_obi_filter:
            # Use the dynamic OBI signal name based on configured depth
            obi_depth = cfg_micro.obi_levels
            obi_signal_name = f"obi_smoothed_{obi_depth}"
            obi = signals.get(obi_signal_name)

            # Fallback to legacy name if the dynamic one isn't available
            if pd.isna(obi):
                self.logger.warning(f"Dynamic OBI signal '{obi_signal_name}' not found, falling back to 'obi_smoothed'")
                obi = signals.get("obi_smoothed")

            obi_long_thresh = cfg_micro.tf_filter_obi_threshold_long
            obi_short_thresh = cfg_micro.tf_filter_obi_threshold_short
            obi_passed = False

            if final_signal == "long":
                if obi >= obi_long_thresh:
                    obi_passed = True
                else:
                    self.fail_obi_filter_long += 1
            elif final_signal == "short":
                if obi <= obi_short_thresh:
                    obi_passed = True
                else:
                    self.fail_obi_filter_short += 1

            if not obi_passed:
                final_signal = None # Block signal if OBI filter failed

        # 5b. Funding Filter (only apply if signal hasn't been blocked by OBI)
        if final_signal is not None and cfg_strategies.tf_use_funding_filter:
            funding = signals.get("funding_rate") # Already checked for NaN if required
            funding_long_thresh = cfg_micro.tf_filter_funding_threshold_long
            funding_short_thresh = cfg_micro.tf_filter_funding_threshold_short
            funding_passed = False

            if final_signal == "long":
                # Allow long if funding is LESS THAN OR EQUAL TO the threshold
                # (e.g., don't long if funding is extremely positive/expensive)
                if funding <= funding_long_thresh:
                    funding_passed = True
                else:
                    self.fail_funding_filter_long += 1
            elif final_signal == "short":
                # Allow short if funding is GREATER THAN OR EQUAL TO the threshold
                # (e.g., don't short if funding is extremely negative/expensive)
                if funding >= funding_short_thresh:
                    funding_passed = True
                else:
                    self.fail_funding_filter_short += 1

            if not funding_passed:
                final_signal = None # Block signal if Funding filter failed

        # --- 6. Return Final Signal ---
        if final_signal == "long":
            self.success_entry_long += 1
            self.logger.info("TF DEBUG: Final LONG signal returned after all filters")
            return "long", None
        elif final_signal == "short":
            self.success_entry_short += 1
            self.logger.info("TF DEBUG: Final SHORT signal returned after all filters")
            return "short", None
        else:
            # Signal was blocked by base condition, Hurst filters, OBI filter, or Funding filter
            self.logger.info(f"TF DEBUG: Signal was blocked by filters. Base signal was {base_signal}")
            return None, None


    def log_evaluation_summary(self):
        """Logs a summary for the Trend Following strategy, including filter blocks."""
        if self.eval_count == 0: return
        total_success = self.success_entry_long + self.success_entry_short
        total_hurst_fails = self.fail_forecast_filter + self.fail_volatility_filter
        total_obi_fails = self.fail_obi_filter_long + self.fail_obi_filter_short
        total_funding_fails = self.fail_funding_filter_long + self.fail_funding_filter_short
        total_fails = self.fail_missing_signal + self.fail_condition + total_hurst_fails + total_obi_fails + total_funding_fails

        lines_data = []
        lines_data.append(("Strategy:", self.strategy_name, 0))
        lines_data.append(("Total Evaluations:", str(self.eval_count), 0))

        entry_detail = f"(Long: {self.success_entry_long}, Short: {self.success_entry_short})"
        lines_data.append(("Entry Signals:", f"{total_success} {entry_detail}", 0))

        signal_rate = total_success / self.eval_count if self.eval_count > 0 else 0
        lines_data.append(("Signal Rate:", f"{signal_rate:.1%}", 0))

        if total_fails > 0:
            fail_rate = total_fails / self.eval_count if self.eval_count > 0 else 0
            lines_data.append(("Failures/Blocks:", f"{total_fails} ({fail_rate:.1%})", 0))

            if self.fail_missing_signal > 0:
                fail_perc = self.fail_missing_signal / self.eval_count
                lines_data.append(("- Missing Signal:", f"{self.fail_missing_signal} ({fail_perc:.1%})", 1))

            if self.fail_condition > 0:
                fail_perc = self.fail_condition / self.eval_count
                lines_data.append(("- Base Condition:", f"{self.fail_condition} ({fail_perc:.1%})", 1))

            if total_hurst_fails > 0: # Only show if Hurst detector might be active
                 fail_perc = total_hurst_fails / self.eval_count
                 lines_data.append(("- Hurst Filters:", f"{total_hurst_fails} ({fail_perc:.1%})", 1))

            if self.config.strategies.tf_use_obi_filter and total_obi_fails > 0:
                 fail_perc = total_obi_fails / self.eval_count
                 lines_data.append(("- OBI Filter:", f"{total_obi_fails} ({fail_perc:.1%})", 1))

            if self.config.strategies.tf_use_funding_filter and total_funding_fails > 0:
                 fail_perc = total_funding_fails / self.eval_count
                 lines_data.append(("- Funding Filter:", f"{total_funding_fails} ({fail_perc:.1%})", 1))


        label_padding_width = 26
        for item in lines_data:
            label, value, indent = item
            indent_str = "  " * indent
            formatted_label = f"{indent_str}{label}".ljust(label_padding_width)
            self.logger.info(f"  • {formatted_label}{value}")

    def check_exit(self, signals: Dict, position: Dict) -> Optional[str]:
        # No change to exit logic for TF based on these filters
        return super().check_exit(signals, position)


class MeanReversionStrategy(StrategyInterface):
    """
    # Deprecated – kept for future use (Task R-103)

    Implements Mean Reversion using Keltner Channels and Confirmation Logic.
    NOTE: This strategy is currently disabled based on PRD v1.3 due to poor performance.
    Kept here for reference or potential future reactivation.

    1. Identifies potential entry zones when price touches/breaches Keltner Channels.
    2. Enters only on confirmation: price closing back inside band OR RSI crossing back from extreme.
    3. Applies optional OBI filter at confirmation.
    """
    def __init__(self, config: Config, strategy_name: str):
        super().__init__(config, strategy_name)
        # --- State Variables for Confirmation Logic ---
        self.in_potential_long_zone: bool = False
        self.in_potential_short_zone: bool = False

        # --- Diagnostic Counters ---
        # eval_count, fail_missing_signal, success_entry_long/short inherited
        self.zone_entered_long = 0
        self.zone_entered_short = 0
        self.fail_confirmation_long = 0
        self.fail_confirmation_short = 0
        self.fail_obi_filter_long = 0
        self.fail_obi_filter_short = 0
        # --- End Counters ---
        self.logger.info("Initialized Mean Reversion Strategy (Keltner Confirmation Logic) - Currently Inactive.")

    @property
    def required_signals(self) -> List[str]:
        # Uses Keltner channels, RSI, and optionally OBI. kc_basis needed for exit.
        # Ensure SignalCalculator provides these if MR is enabled.
        signals = ["close", "kc_lower", "kc_basis", "kc_upper", "rsi"]
        if self.config.indicators.mr_require_obi_filter:
            # Use dynamic OBI column name based on config
            obi_candidates = get_obi_column_name(self.config.microstructure.depth_levels)
            signals.extend(obi_candidates)  # Add both candidates
        return signals

    def evaluate(self, signals: Dict) -> Tuple[Optional[Literal["long", "short"]], Optional[Dict]]:
        """
        Implements Mean Reversion using Keltner Channels and Confirmation Logic.
        Returns: (direction, strategy_info) - strategy_info is always None for MR.
        """
        self.eval_count += 1
        cfg_indicators = self.config.indicators

        # --- 1. Check Signals ---
        current_req_signals = self.required_signals
        missing_signals = [s for s in current_req_signals if pd.isna(signals.get(s))]

        if missing_signals:
            self.fail_missing_signal += 1
            # self.logger.debug(f"MR Eval Fail: Missing signals {missing_signals}") # Reduce noise
            return None, None

        # --- 2. Extract Signals ---
        price = signals["close"]
        kc_lower = signals["kc_lower"]
        kc_upper = signals["kc_upper"]
        kc_basis = signals["kc_basis"] # Needed for zone reset logic
        rsi = signals["rsi"]

        # Use dynamic OBI column name based on config
        obi = None
        if self.config.indicators.mr_require_obi_filter:
            obi_candidates = get_obi_column_name(self.config.microstructure.depth_levels)
            for candidate in obi_candidates:
                obi = signals.get(candidate)
                if obi is not None and not pd.isna(obi):
                    break

        has_valid_obi = obi is not None and not pd.isna(obi)

        # --- 3. Update Potential Entry Zones ---
        touched_lower = price <= kc_lower
        touched_upper = price >= kc_upper

        if touched_lower:
            if not self.in_potential_long_zone: self.logger.debug(f"MR Entered Potential Long Zone: Price <= KCL ({kc_lower:.2f})")
            self.in_potential_long_zone = True
            self.zone_entered_long += 1 # Count zone entry attempts
            self.in_potential_short_zone = False # Can't be in both
        elif touched_upper:
            if not self.in_potential_short_zone: self.logger.debug(f"MR Entered Potential Short Zone: Price >= KCU ({kc_upper:.2f})")
            self.in_potential_short_zone = True
            self.zone_entered_short += 1 # Count zone entry attempts
            self.in_potential_long_zone = False # Can't be in both
        else:
            # Price is within bands, reset zones if needed (prevents stale zones)
            if self.in_potential_long_zone and price >= kc_basis:
                 # self.logger.debug("MR Exited Potential Long Zone (Price >= Basis)") # Reduce noise
                 self.in_potential_long_zone = False
            if self.in_potential_short_zone and price <= kc_basis:
                 # self.logger.debug("MR Exited Potential Short Zone (Price <= Basis)") # Reduce noise
                 self.in_potential_short_zone = False


        # --- 4. Check for Entry Confirmation ---
        entry_signal: Optional[Literal["long", "short"]] = None

        # Check Long Confirmation
        if self.in_potential_long_zone:
            confirmation_met = (price > kc_lower) or (rsi > cfg_indicators.mr_rsi_oversold)
            if confirmation_met:
                obi_filter_passed = True
                if cfg_indicators.mr_require_obi_filter:
                    if not has_valid_obi:
                        # self.logger.debug("MR Long Blocked: OBI filter required but OBI is missing/NaN.") # Reduce noise
                        obi_filter_passed = False
                        self.fail_obi_filter_long += 1
                    elif obi <= cfg_indicators.mr_obi_long_threshold:
                        # self.logger.debug(f"MR Long Blocked by OBI Filter: OBI={obi:.3f} <= Threshold={cfg_indicators.mr_obi_long_threshold:.3f}") # Reduce noise
                        obi_filter_passed = False
                        self.fail_obi_filter_long += 1
                if obi_filter_passed:
                    # self.logger.info(f"MR Signal: Long Entry Triggered (Confirmation + OBI Passed/Skipped)") # Reduce noise
                    entry_signal = "long"
                    self.success_entry_long += 1
            else:
                 self.fail_confirmation_long += 1

        # Check Short Confirmation
        elif self.in_potential_short_zone:
            confirmation_met = (price < kc_upper) or (rsi < cfg_indicators.mr_rsi_overbought)
            if confirmation_met:
                obi_filter_passed = True
                if cfg_indicators.mr_require_obi_filter:
                    if not has_valid_obi:
                        # self.logger.debug("MR Short Blocked: OBI filter required but OBI is missing/NaN.") # Reduce noise
                        obi_filter_passed = False
                        self.fail_obi_filter_short += 1
                    elif obi >= cfg_indicators.mr_obi_short_threshold:
                        # self.logger.debug(f"MR Short Blocked by OBI Filter: OBI={obi:.3f} >= Threshold={cfg_indicators.mr_obi_short_threshold:.3f}") # Reduce noise
                        obi_filter_passed = False
                        self.fail_obi_filter_short += 1
                if obi_filter_passed:
                    # self.logger.info(f"MR Signal: Short Entry Triggered (Confirmation + OBI Passed/Skipped)") # Reduce noise
                    entry_signal = "short"
                    self.success_entry_short += 1
            else:
                 self.fail_confirmation_short += 1

        # --- 5. Reset State on Entry and Return ---
        if entry_signal is not None:
            self.in_potential_long_zone = False
            self.in_potential_short_zone = False
            return entry_signal, None
        else:
            return None, None

    def log_evaluation_summary(self):
        """Logs a summary for the Mean Reversion strategy."""
        if self.eval_count == 0: return
        total_success = self.success_entry_long + self.success_entry_short
        total_obi_fails = self.fail_obi_filter_long + self.fail_obi_filter_short
        total_conf_fails = self.fail_confirmation_long + self.fail_confirmation_short
        total_zone_entries = self.zone_entered_long + self.zone_entered_short
        total_fails = self.fail_missing_signal + total_conf_fails + total_obi_fails

        # Prepare data for formatting
        lines_data = []
        lines_data.append(("Strategy", f"{self.strategy_name} (Keltner Confirm)", 0))
        lines_data.append(("Total Evaluations", str(self.eval_count), 0))

        entry_detail = f"(Long: {self.success_entry_long}, Short: {self.success_entry_short})"
        lines_data.append(("Entry Signals", f"{total_success} {entry_detail}", 0))

        signal_rate = total_success / self.eval_count if self.eval_count > 0 else 0
        lines_data.append(("Signal Rate", f"{signal_rate:.1%}", 0))

        zone_detail = f"(Long: {self.zone_entered_long}, Short: {self.zone_entered_short})"
        lines_data.append(("Zone Entries", f"{total_zone_entries} {zone_detail}", 0))

        zone_conversion = total_success / total_zone_entries if total_zone_entries > 0 else 0
        lines_data.append(("Zone Conversion Rate", f"{zone_conversion:.1%}", 0))

        if total_fails > 0:
            fail_rate = total_fails / self.eval_count if self.eval_count > 0 else 0
            lines_data.append(("Failures/Blocks", f"{total_fails} ({fail_rate:.1%})", 0))

            if self.fail_missing_signal > 0:
                fail_perc = self.fail_missing_signal / self.eval_count if self.eval_count > 0 else 0
                lines_data.append(("- Missing Signal", f"{self.fail_missing_signal} ({fail_perc:.1%})", 1))

            if total_zone_entries > 0: # Percentages relative to zone entries for these failures
                if total_conf_fails > 0:
                    fail_perc = total_conf_fails / total_zone_entries if total_zone_entries > 0 else 0
                    lines_data.append(("- Confirmation", f"{total_conf_fails} ({fail_perc:.1%})", 1))

                if total_obi_fails > 0:
                    fail_perc = total_obi_fails / total_zone_entries if total_zone_entries > 0 else 0
                    lines_data.append(("- OBI Filter", f"{total_obi_fails} ({fail_perc:.1%})", 1))

        # Use a fixed width for consistent alignment between strategies
        label_padding_width = 26 # Fixed width = max label/indent + 2 spaces

        # Print formatted lines
        for item in lines_data:
            label = item[0]
            value = item[1]
            indent = item[2] # Indent level is always present now
            indent_str = "  " * indent
            # Pad the label part (indent + label) to the fixed width
            formatted_label = f"{indent_str}{label}:".ljust(label_padding_width)
            self.logger.info(f"  • {formatted_label}{value}")

    def check_exit(self, signals: Dict, position: Dict) -> Optional[str]:
        # Example using Keltner basis (middle line)
        if 'kc_basis' in signals and pd.notna(signals['kc_basis']):
            price = signals.get('close')
            kc_basis = signals['kc_basis']
            pos_type = position.get('type')

            if pd.isna(price): return None # Cannot check exit if price is NaN

            if pos_type == 'long' and price >= kc_basis:
                return "mr_exit_cross_basis"
            if pos_type == 'short' and price <= kc_basis:
                return "mr_exit_cross_basis"
        return super().check_exit(signals, position)


# --- NEW Mean Variance Strategy ---

class MeanVarianceStrategy(StrategyInterface):
    """
    # Deprecated – kept for future use (Task R-103)

    Implements a strategy that determines trade direction based on a funding-adjusted
    mean-reversion edge proxy and applies OBI filtering. Sizing is handled by RiskManager (Kelly).
    """
    def __init__(self, config: Config, strategy_name: str):
        super().__init__(config, strategy_name)
        # Add specific counters
        self.fail_edge_threshold = 0
        self.fail_obi_filter = 0
        self.logger.info("Initialized Mean Variance (Kelly) Strategy.")

    @property
    def required_signals(self) -> List[str]:
        """Define signals needed for edge calculation and filtering."""
        # Uses mv_edge_proxy now instead of forecast
        signals = ["close", "mv_edge_proxy", "funding_rate"] # Core signals for edge
        if self.config.indicators.mv_require_obi_filter:
            # Use dynamic OBI column name based on config
            obi_candidates = get_obi_column_name(self.config.microstructure.depth_levels)
            signals.extend(obi_candidates)  # Add both candidates
        return list(set(signals))

    def evaluate(self, signals: Dict) -> Tuple[Optional[Literal["long", "short"]], Optional[Dict]]:
        """
        Determine trade direction based on expected edge and filters.
        Returns: (direction, strategy_info) where strategy_info contains calculated edge.
        """
        self.eval_count += 1
        cfg_indicators = self.config.indicators
        strategy_info = {} # Dictionary to hold extra info for RiskManager

        # --- 1. Check Signals ---
        current_req_signals = self.required_signals
        missing_signals = [s for s in current_req_signals if pd.isna(signals.get(s))]
        if missing_signals:
            self.fail_missing_signal += 1
            return None, None # Return None for direction and info

        # --- 2. Extract Signals ---
        price = signals["close"] # Still needed? Maybe not directly here.
        edge_proxy = signals["mv_edge_proxy"] # Use the new proxy
        funding_rate = signals["funding_rate"]

        # Use dynamic OBI column name based on config
        obi = None
        if cfg_indicators.mv_require_obi_filter:
            obi_candidates = get_obi_column_name(self.config.microstructure.depth_levels)
            for candidate in obi_candidates:
                obi = signals.get(candidate)
                if obi is not None and not pd.isna(obi):
                    break

        has_valid_obi = obi is not None and not pd.isna(obi)

        # --- 3. Calculate Expected Edge (Adjusted for Funding) ---
        # Positive edge_proxy means price is below EMA (upward reversion expected)
        # Negative edge_proxy means price is above EMA (downward reversion expected)
        expected_edge_long = edge_proxy - funding_rate
        expected_edge_short = -edge_proxy + funding_rate # If edge_proxy is negative, -proxy is positive

        # --- 4. Check Entry Thresholds ---
        long_edge_sufficient = expected_edge_long > cfg_indicators.mv_min_edge_threshold
        short_edge_sufficient = expected_edge_short > cfg_indicators.mv_min_edge_threshold

        if not long_edge_sufficient and not short_edge_sufficient:
            self.fail_edge_threshold += 1
            return None, None

        # --- 5. Apply OBI Filter (if enabled) ---
        long_obi_passed = True
        short_obi_passed = True
        if cfg_indicators.mv_require_obi_filter:
            if not has_valid_obi:
                self.logger.warning("MV Eval: OBI filter required but OBI is missing/NaN.")
                long_obi_passed = False
                short_obi_passed = False
                self.fail_obi_filter += 1
            else:
                if obi <= cfg_indicators.mv_obi_long_threshold:
                    long_obi_passed = False
                if obi >= cfg_indicators.mv_obi_short_threshold:
                    short_obi_passed = False
                if (long_edge_sufficient and not long_obi_passed) or (short_edge_sufficient and not short_obi_passed):
                     self.fail_obi_filter += 1

        # --- 6. Determine Final Direction & Store Edge ---
        final_direction: Optional[Literal["long", "short"]] = None
        edge_to_use = None

        # Prioritize direction with higher edge if both are valid? Or just pick one?
        # Let's pick the one with the higher edge.
        if long_edge_sufficient and long_obi_passed and short_edge_sufficient and short_obi_passed:
             if expected_edge_long >= expected_edge_short: # Favor long in tie
                  final_direction = "long"
                  edge_to_use = expected_edge_long
             else:
                  final_direction = "short"
                  edge_to_use = expected_edge_short
        elif long_edge_sufficient and long_obi_passed:
            final_direction = "long"
            edge_to_use = expected_edge_long
        elif short_edge_sufficient and short_obi_passed:
            final_direction = "short"
            edge_to_use = expected_edge_short
        # Else: No direction meets all criteria

        if final_direction:
            if final_direction == "long": self.success_entry_long += 1
            else: self.success_entry_short += 1
            strategy_info['expected_edge'] = edge_to_use # Store the edge for RiskManager
            return final_direction, strategy_info
        else:
            # Ensure failure counters are incremented if edge was sufficient but OBI failed
            # (already handled inside OBI filter logic)
            return None, None


    def check_exit(self, signals: Dict, position: Dict) -> Optional[str]:
        """Exit if the expected edge proxy drops below the exit threshold."""
        cfg_indicators = self.config.indicators
        pos_type = position.get('type')

        # Required signals for exit check - use mv_edge_proxy now
        req_exit_signals = ["mv_edge_proxy", "funding_rate"]
        if any(pd.isna(signals.get(s)) for s in req_exit_signals):
            self.logger.warning(f"MV CheckExit Fail: Missing signals {req_exit_signals}")
            return None # Cannot determine exit condition

        edge_proxy = signals["mv_edge_proxy"]
        funding_rate = signals["funding_rate"]
        exit_threshold = cfg_indicators.mv_min_exit_edge_threshold

        if pos_type == "long":
            expected_edge_long = edge_proxy - funding_rate
            if expected_edge_long < exit_threshold:
                self.logger.info(f"MV Exit Signal: Long edge {expected_edge_long:.5f} < threshold {exit_threshold:.5f}")
                return "mv_exit_edge_low"
        elif pos_type == "short":
            expected_edge_short = -edge_proxy + funding_rate
            if expected_edge_short < exit_threshold:
                self.logger.info(f"MV Exit Signal: Short edge {expected_edge_short:.5f} < threshold {exit_threshold:.5f}")
                return "mv_exit_edge_low"

        return None # No exit signal based on edge

    def log_evaluation_summary(self):
        """Logs a summary for the Mean Variance strategy."""
        if self.eval_count == 0: return
        total_success = self.success_entry_long + self.success_entry_short
        total_fails = self.fail_missing_signal + self.fail_edge_threshold + self.fail_obi_filter

        # Prepare data for formatting
        lines_data = []
        lines_data.append(("Strategy:", self.strategy_name, 0))
        lines_data.append(("Total Evaluations:", str(self.eval_count), 0))

        entry_detail = f"(Long: {self.success_entry_long}, Short: {self.success_entry_short})"
        lines_data.append(("Entry Signals:", f"{total_success} {entry_detail}", 0))

        signal_rate = total_success / self.eval_count if self.eval_count > 0 else 0
        lines_data.append(("Signal Rate:", f"{signal_rate:.1%}", 0))

        if total_fails > 0:
            fail_rate = total_fails / self.eval_count if self.eval_count > 0 else 0
            lines_data.append(("Failures/Blocks:", f"{total_fails} ({fail_rate:.1%})", 0))

            if self.fail_missing_signal > 0:
                fail_perc = self.fail_missing_signal / self.eval_count if self.eval_count > 0 else 0
                lines_data.append(("- Missing Signal:", f"{self.fail_missing_signal} ({fail_perc:.1%})", 1))

            if self.fail_edge_threshold > 0:
                fail_perc = self.fail_edge_threshold / self.eval_count if self.eval_count > 0 else 0
                lines_data.append(("- Edge Threshold:", f"{self.fail_edge_threshold} ({fail_perc:.1%})", 1))

            if self.fail_obi_filter > 0:
                fail_perc = self.fail_obi_filter / self.eval_count if self.eval_count > 0 else 0
                lines_data.append(("- OBI Filter:", f"{self.fail_obi_filter} ({fail_perc:.1%})", 1))

        # Use a fixed width for consistent alignment between strategies
        label_padding_width = 26 # Fixed width = max label/indent + 2 spaces

        # Print formatted lines
        for item in lines_data:
            label = item[0]
            value = item[1]
            indent = item[2] # Indent level is always present now
            indent_str = "  " * indent
            # Pad the label part (indent + label) to the fixed width
            formatted_label = f"{indent_str}{label}".ljust(label_padding_width)
            self.logger.info(f"  • {formatted_label}{value}")


# --- Strategy Evaluator / Factory ---
class StrategyEvaluator:
    """
    Manages and evaluates different trading strategies based on configuration.
    Acts as a factory to provide configured strategy instances.
    Determines which strategies are active based on the current market regime.
    """
    def __init__(self, config: Config, regime_detector=None):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.strategies: Dict[str, StrategyInterface] = {}
        self.regime_detector = regime_detector  # Store reference to regime detector
        self._initialize_strategies()
        # The centralized mapping utility handles loading and caching the map internally
        # We no longer need to store the state map in the evaluator
        self.gms_mapping_active: bool = getattr(self.config.regime, 'gms_use_three_state_mapping', False)

        # Store the weak bear mapping toggle from config
        self.map_weak_bear_to_bear: bool = getattr(self.config.regime, 'map_weak_bear_to_bear', False)
        if self.map_weak_bear_to_bear:
            self.logger.info("Weak_Bear_Trend will map to BEAR (map_weak_bear_to_bear=True)")
        else:
            self.logger.info("Weak_Bear_Trend will map to CHOP (map_weak_bear_to_bear=False, default)")


        if self.gms_mapping_active and self.config.regime.detector_type in ['granular_microstructure', 'continuous_gms']:
            self.logger.info("GMS 3-state mapping is ACTIVE. Using centralized mapping utility.")
            try:
                # The utility will handle loading and validating the mapping
                # We don't need to do anything else here - it will be loaded on first use
                state_map = get_state_map()
                if state_map:
                    self.logger.info("Successfully verified access to GMS state map")
                else:
                    self.logger.error("Failed to access GMS state map through utility. Disabling 3-state mapping.")
                    self.gms_mapping_active = False
            except Exception as e:
                self.logger.error(f"Error loading state mapping via utility: {e}. Disabling 3-state mapping.")
                self.gms_mapping_active = False
        else:
             self.logger.info(f"GMS 3-state mapping is INACTIVE (Flag: {self.gms_mapping_active}, Detector: {self.config.regime.detector_type}).")



        self.logger.info(f"Initialized with strategies: {list(self.strategies.keys())}")
        self.logger.info(f"Regime detector type: {config.regime.detector_type}")
        self.logger.info(f"Strict strategy filtering: {config.regime.use_strict_strategy_filtering}")
        if config.regime.detector_type == 'rule_based':
             self.logger.info(f"Pause in chop (rule-based): {config.regime.pause_in_chop}")

    def _initialize_strategies(self):
        """Creates instances of strategies enabled in the config."""
        if self.config.strategies.use_tf_v2:
            self.strategies["trend_following"] = TrendFollowingStrategy(self.config, "trend_following")
        if self.config.strategies.use_mean_reversion:
            self.strategies["mean_reversion"] = MeanReversionStrategy(self.config, "mean_reversion")
        if self.config.strategies.use_mean_variance:
            self.strategies["mean_variance"] = MeanVarianceStrategy(self.config, "mean_variance")
        if self.config.strategies.use_obi_scalper:
            self.strategies["obi_scalper"] = OBIScalperStrategy(self.config, "obi_scalper")
        if hasattr(self.config.strategies, 'use_tf_v3') and self.config.strategies.use_tf_v3:
            # Pass portfolio reference to TFV3Strategy for risk management
            from hyperliquid_bot.backtester.backtester import Backtester
            portfolio = None
            # Try to get portfolio from backtester if available
            try:
                # This is a hack to get the portfolio reference - in a real implementation,
                # we would pass the portfolio to the StrategyEvaluator constructor
                import inspect
                for frame_info in inspect.stack():
                    frame = frame_info.frame
                    if 'self' in frame.f_locals and isinstance(frame.f_locals['self'], Backtester):
                        portfolio = frame.f_locals['self'].portfolio
                        break
            except Exception as e:
                self.logger.warning(f"Could not get portfolio reference for TFV3Strategy: {e}")

            # Initialize TFV3Strategy with portfolio if available
            from hyperliquid_bot.strategies.tf_v3 import TFV3Strategy
            self.strategies["tf_v3"] = TFV3Strategy(self.config, "tf_v3", portfolio)

    def get_strategy(self, strategy_name: str) -> Optional[StrategyInterface]:
        return self.strategies.get(strategy_name)

    def get_active_strategies(self, current_regime: str) -> List[str]:
        """
        Determines which strategies should be active for the current market regime,
        considering the configured detector type and strict filtering settings.
        Maps granular regimes to strategies.
        """
        cfg = self.config
        enabled_strategy_names = list(self.strategies.keys())
        active_names: List[str] = []

        # --- Handle cases where filtering is off or regime is unknown/invalid ---
        if not cfg.regime.use_filter or current_regime == "Filter_Off":
            self.logger.debug(f"Regime filter OFF or regime='Filter_Off'. Evaluating all enabled: {enabled_strategy_names}")
            return enabled_strategy_names
        if current_regime in ["Unknown", "Uncertain"]:
             self.logger.debug(f"Regime is '{current_regime}', no strategies will be activated.")
             return []

        # --- Check for OBI Scalper active in all regimes flag ---
        if "obi_scalper" in self.strategies and cfg.strategies.obi_scalper_active_in_all_regimes:
            active_names.append("obi_scalper")
            self.logger.debug("OBI Scalper activated in all regimes per configuration flag")

        # --- Apply Strict Filtering based on Detector Type ---
        if cfg.regime.use_strict_strategy_filtering:
            detector_type = cfg.regime.detector_type

            # --- Rule-Based Detector Mapping ---
            if detector_type == 'rule_based':
                if current_regime == "Volatile_Chop" and cfg.regime.pause_in_chop:
                    self.logger.debug("Rule-Based: Strict ON, Pause in Chop ON. Pausing trading.")
                    if cfg.strategies.obi_scalper_active_in_all_regimes:
                        # Keep only OBI scalper if active in all regimes even during pause
                        return ["obi_scalper"] if "obi_scalper" in self.strategies else []
                    else:
                        return []
                if current_regime in ["Trending", "Strong_Trend"]:
                    if "trend_following" in self.strategies: active_names.append("trend_following")
                elif current_regime == "Ranging":
                    if "mean_variance" in self.strategies: active_names.append("mean_variance")
                    # Note: MR strategy is currently inactive by default

            # --- Hurst Detector Mapping ---
            elif detector_type == 'hurst':
                if current_regime == "Persistent": # Trending
                    if "trend_following" in self.strategies: active_names.append("trend_following")
                elif current_regime == "MeanReverting": # Ranging
                    if "mean_variance" in self.strategies: active_names.append("mean_variance")
                    # Note: MR strategy is currently inactive by default

            # --- Granular Microstructure Detector Mapping ---
            elif detector_type in ['granular_microstructure', 'continuous_gms', 'continuous_modern_v2']:
                # Special handling for continuous_gms/modern_v2 when regime is already BULL or BEAR
                if detector_type in ['continuous_gms', 'continuous_modern_v2'] and current_regime in ['BULL', 'BEAR']:
                    self.logger.info(f"Direct regime mapping for {detector_type}: {current_regime}")
                    if "trend_following" in self.strategies: active_names.append("trend_following")
                    if "tf_v3" in self.strategies: active_names.append("tf_v3")
                    return active_names
                # --- GMS 3-State Mapping Logic (Updated to use centralized utility) ---
                if self.gms_mapping_active:
                    # For continuous_gms/modern_v2 with output_states < 8, states are already collapsed
                    # Skip mapping to avoid double-mapping collapsed states
                    if detector_type in ['continuous_gms', 'continuous_modern_v2'] and current_regime in ['BULL', 'BEAR', 'CHOP']:
                        regime_to_evaluate = current_regime
                        self.logger.debug(f"Continuous GMS: Using already collapsed state '{current_regime}'")
                    else:
                        try:
                            # Use detector's state mapping if available, otherwise fall back to utility
                            if (self.regime_detector and 
                                hasattr(self.regime_detector, 'state_collapse_map') and 
                                self.regime_detector.state_collapse_map and 
                                'state_map' in self.regime_detector.state_collapse_map):
                                # Use the detector's loaded state map
                                state_map = self.regime_detector.state_collapse_map['state_map']
                                mapped_regime = state_map.get(current_regime, 'CHOP')
                                self.logger.debug(f"Using detector's state map: {current_regime} -> {mapped_regime}")
                            else:
                                # Fall back to centralized mapping utility
                                mapped_regime = map_gms_state(current_regime, map_weak_bear_to_bear=self.map_weak_bear_to_bear)
                                self.logger.debug(f"Using utility state map: {current_regime} -> {mapped_regime}")
                            
                            if mapped_regime != current_regime:
                                self.logger.info(f"GMS Mapping: Raw='{current_regime}' -> Mapped='{mapped_regime}'")
                            regime_to_evaluate = mapped_regime

                            # Validate that the mapped regime is a valid 3-state value
                            if not validate_3state(mapped_regime):
                                self.logger.warning(f"Mapped regime '{mapped_regime}' is not a valid 3-state value. Using raw regime.")
                                regime_to_evaluate = current_regime
                        except Exception as e:
                            # If any error occurs, log it and use the raw regime as fallback
                            self.logger.error(f"Error in GMS state mapping: {e}. Using raw regime as fallback.")
                            regime_to_evaluate = current_regime
                    # Explicitly handle TIGHT_SPREAD even if mapped (e.g., if map includes it or raw state is used as fallback)
                    if regime_to_evaluate == 'TIGHT_SPREAD':
                         self.logger.debug("Strict Filtering ON (3-State Map): Regime is TIGHT_SPREAD. No strategies active.")
                         # active_names remains empty
                    elif regime_to_evaluate in ['BULL', 'BEAR']:
                         if "trend_following" in self.strategies: active_names.append("trend_following")
                         if "tf_v3" in self.strategies: active_names.append("tf_v3")
                    elif regime_to_evaluate == 'CHOP':
                         if "mean_variance" in self.strategies: active_names.append("mean_variance")
                         # Activate OBI Scalper in CHOP if the flag is set
                         if "obi_scalper" in self.strategies and cfg.strategies.gms_activate_obi_scalper_in_chop:
                             active_names.append("obi_scalper")
                             self.logger.debug("OBI Scalper activated in CHOP regime per configuration flag")
                    elif regime_to_evaluate in ['THIN_LIQUIDITY', 'SKEWED_BOOK']:
                         # Explicitly no strategies active in these regimes
                         self.logger.debug(f"Strict Filtering ON (3-State Map): Regime is {regime_to_evaluate}. No strategies active.")
                         pass # Keep active_names empty
                    else: # Else (e.g., mapped to Uncertain or original state was Uncertain): active_names remains empty
                         pass

                # --- Original 7-State Logic + TIGHT_SPREAD ---
                else:
                    self.logger.debug(f"GMS Mapping Inactive. Evaluating based on raw regime: '{current_regime}'")
                    # Add debug logging
                    self.logger.info(f"GMS Mapping Inactive. Evaluating based on raw regime: '{current_regime}'")

                    # Check if the regime is a dictionary with a 'state' key
                    if isinstance(current_regime, dict) and 'state' in current_regime:
                        regime_state = current_regime.get('state')
                        self.logger.info(f"Regime is a dictionary with state: {regime_state}")
                        if regime_state in ["Strong_Bull_Trend", "Strong_Bear_Trend"]:
                            if "trend_following" in self.strategies: active_names.append("trend_following")
                            if "tf_v3" in self.strategies: active_names.append("tf_v3")
                            self.logger.info(f"Added tf_v3 to active strategies for regime: {regime_state}")
                        elif regime_state == "Weak_Bull_Trend" and cfg.regime.gms_filter_allow_weak_bull_trend:
                            if "trend_following" in self.strategies: active_names.append("trend_following")
                            if "tf_v3" in self.strategies: active_names.append("tf_v3")
                        elif regime_state == "Weak_Bear_Trend" and cfg.regime.gms_filter_allow_weak_bear_trend:
                            if "trend_following" in self.strategies: active_names.append("trend_following")
                            if "tf_v3" in self.strategies: active_names.append("tf_v3")
                    # Handle string regimes
                    elif current_regime in ["Strong_Bull_Trend", "Strong_Bear_Trend"]:
                         if "trend_following" in self.strategies: active_names.append("trend_following")
                         if "tf_v3" in self.strategies: active_names.append("tf_v3")
                         self.logger.info(f"Added tf_v3 to active strategies for regime: {current_regime}")
                    elif current_regime == "Weak_Bull_Trend" and cfg.regime.gms_filter_allow_weak_bull_trend:
                          if "trend_following" in self.strategies: active_names.append("trend_following")
                          if "tf_v3" in self.strategies: active_names.append("tf_v3")
                    elif current_regime == "Weak_Bear_Trend" and cfg.regime.gms_filter_allow_weak_bear_trend:
                          if "trend_following" in self.strategies: active_names.append("trend_following")
                          if "tf_v3" in self.strategies: active_names.append("tf_v3")
                    elif current_regime == "TIGHT_SPREAD": # Added explicit check for TIGHT_SPREAD
                         # active_names remains empty
                         pass
                    elif current_regime == "WIDE_SPREAD": # Add explicit check for WIDE_SPREAD
                         # active_names remains empty
                         pass
                    elif current_regime in ["High_Vol_Range", "Low_Vol_Range"]:
                         if "mean_variance" in self.strategies: active_names.append("mean_variance")
                         # Also activate TF-v3 in Low_Vol_Range for testing
                         if current_regime == "Low_Vol_Range" and "tf_v3" in self.strategies:
                             active_names.append("tf_v3")
                             self.logger.info(f"Added tf_v3 to active strategies for regime: {current_regime}")
                    # Else (Uncertain/Unknown): No strategies active implicitly
                    pass

            else:
                self.logger.error(f"Unknown detector type '{detector_type}' in get_active_strategies.")
                return []

            self.logger.debug(f"Detector='{detector_type}', Regime='{current_regime}', StrictFilter=True -> Active: {active_names}")
            return active_names

        # --- Strict Filtering is OFF ---
        else:
            self.logger.debug(f"Strict filtering OFF. Evaluating all enabled strategies regardless of regime '{current_regime}': {enabled_strategy_names}")
            return enabled_strategy_names

# Import strategy classes after StrategyInterface is defined to avoid circular imports
from hyperliquid_bot.strategies.obi_scalper_strategy import OBIScalperStrategy
