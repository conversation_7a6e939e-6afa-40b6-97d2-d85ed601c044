# hyperliquid_bot/strategies/obi_scalper.py

import logging
from typing import Dict, List, Optional, Tuple, Literal
import pandas as pd
import numpy as np

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.strategies.evaluator import StrategyInterface
from hyperliquid_bot.core.risk import RiskManager

# Define a simple dataclass for OBIScalperConfig inputs
from dataclasses import dataclass

@dataclass
class OBIScalperConfig:
    """Configuration parameters specific to OBI Scalper strategy."""
    # Risk parameters
    risk_pct: float = 0.5  # Risk percentage (0.5 = 0.5%)
    stop_ticks: int = 5    # Number of ticks for stop loss distance

class OBIScalperStrategy(StrategyInterface):
    """
    OBI Scalper strategy that looks for order book imbalance opportunities.

    This is a skeleton implementation that will be enhanced with entry logic
    in future tasks. Currently it just tracks evaluation metrics but takes no actions.
    """

    def __init__(self, config: Config, strategy_name: str):
        """Initialize the OBI Scalper with configuration settings."""
        super().__init__(config, strategy_name)
        self.config = config
        self.logger = logging.getLogger(f"{self.__class__.__name__}[{strategy_name}]")

        # Initialize specific tracking counters for OBI Scalper
        self.fail_condition = 0         # Basic condition failure
        self.fail_obi_threshold = 0     # OBI threshold not met
        self.fail_spread_filter = 0     # Spread too wide
        self.fail_zscore_filter = 0     # Z-score filter failure
        self.fail_regime_filter = 0     # Inappropriate market regime

        # Log initialization
        self.logger.info(f"Initialized {self.__class__.__name__} strategy")

    @property
    def required_signals(self) -> List[str]:
        """Define the signals required by this strategy."""
        # Determine the OBI level from config
        obi_levels = self.config.microstructure.obi_levels

        # Core signals needed for the strategy
        signals = [
            "close",                        # Current price
            "regime",                       # Market regime
            f"obi_smoothed_{obi_levels}",   # Smoothed OBI
            f"obi_zscore_{obi_levels}",     # OBI Z-Score for filtering extremes
            "spread_relative"               # Relative spread for filtering
        ]

        # Return the list of unique signals
        return list(set(signals))

    def _get_direction(self, signals: Dict) -> Optional[Literal["long", "short"]]:
        """
        Determine trade direction based on OBI signals.

        This is a helper method for testing and future implementation.
        Currently returns None as this is a skeleton implementation.

        Args:
            signals: Dictionary of market signals

        Returns:
            Direction ("long", "short") or None if no signal
        """
        # STUB: This will be implemented in future tasks
        # For now, return None to indicate no trade
        return None

    def evaluate(self, signals: Dict) -> Tuple[Optional[Literal["long", "short"]], Optional[Dict]]:
        """
        Evaluate market conditions for potential entry signals.

        Implements risk management for the OBI Scalper strategy:
        - Calculates position size using fixed fraction of account equity
        - Sets stop loss based on configured number of ticks

        Args:
            signals: Dictionary of market signals including OBI and regime

        Returns:
            Tuple of (direction, info) where direction is the trade direction
            and info contains position sizing and risk management details
        """
        self.eval_count += 1

        # Check for required signals
        current_req_signals = self.required_signals
        missing_signals = [s for s in current_req_signals if pd.isna(signals.get(s))]

        if missing_signals:
            self.fail_missing_signal += 1
            self.logger.debug(f"Missing signals for OBI Scalper evaluation: {missing_signals}")
            return None, None

        # Get trade direction from the helper method
        direction = self._get_direction(signals)

        # If we don't have a valid entry signal, return None
        if direction is None:
            self.fail_condition += 1
            return None, None

        # Get current price and tick size from signals
        # (In a real implementation, these would come from the signals dictionary)
        current_price = signals.get('close', 0)
        tick_size = signals.get('tick_size', self.config.strategies.OBIScalperStrategy.defaults.tick_size)

        # Calculate position size using fixed fraction risk
        account_equity = signals.get('account_equity', 10000)  # Default to 10000 if not available
        risk_pct = self.config.strategies.scalper_risk_pct / 100.0  # Convert from percentage to decimal

        # Use the RiskManager to calculate position size
        risk_amount = RiskManager.get_fixed_fraction_position_size(account_equity, risk_pct)

        # Calculate stop loss price based on direction and configured stop ticks
        stop_ticks = self.config.strategies.scalper_stop_ticks
        stop_distance = stop_ticks * tick_size

        # Ensure stop distance is at least one tick
        if stop_distance < tick_size:
            self.logger.warning(f"Stop distance {stop_distance} is less than minimum tick size {tick_size}. Trade rejected.")
            return None, None

        # Calculate stop loss price based on direction
        stop_loss_price = 0
        if direction == "long":
            stop_loss_price = current_price - stop_distance
        elif direction == "short":
            stop_loss_price = current_price + stop_distance

        # Calculate position size in contracts based on risk amount and stop distance
        # position_size = risk_amount / stop_distance

        # Prepare the trade info dictionary
        trade_info = {
            'risk_amount': risk_amount,
            'stop_loss_price': stop_loss_price,
            'stop_distance': stop_distance,
            'risk_pct': risk_pct,
            # Additional fields would be added in a real implementation
        }

        # Log the trade details
        self.logger.info(f"OBI Scalper {direction} signal: Risk=${risk_amount:.2f}, Stop={stop_loss_price:.4f}")

        # Increment success counter based on direction
        if direction == "long":
            self.success_entry_long += 1
        elif direction == "short":
            self.success_entry_short += 1

        return direction, trade_info

    def check_exit(self, signals: Dict, position: Dict) -> Optional[str]:
        """
        Check if exit conditions are met for an open position.

        This is a skeleton implementation that will be enhanced in future tasks.
        Currently delegates to the parent class (returns None).

        Args:
            signals: Dictionary of market signals
            position: Dictionary with position details

        Returns:
            Optional exit reason string, or None if no exit signal
        """
        # For now, delegate to parent class (returns None)
        return super().check_exit(signals, position)

    def log_evaluation_summary(self):
        """Log evaluation statistics for the OBI Scalper strategy."""
        if self.eval_count == 0:
            return  # Nothing to report

        total_success = self.success_entry_long + self.success_entry_short
        total_fails = (
            self.fail_missing_signal +
            self.fail_condition +
            self.fail_obi_threshold +
            self.fail_spread_filter +
            self.fail_zscore_filter +
            self.fail_regime_filter
        )

        lines_data = []
        lines_data.append(("Strategy:", self.strategy_name, 0))
        lines_data.append(("Total Evaluations:", str(self.eval_count), 0))

        entry_detail = f"(Long: {self.success_entry_long}, Short: {self.success_entry_short})"
        lines_data.append(("Entry Signals:", f"{total_success} {entry_detail}", 0))

        signal_rate = total_success / self.eval_count if self.eval_count > 0 else 0
        lines_data.append(("Signal Rate:", f"{signal_rate:.1%}", 0))

        if total_fails > 0:
            fail_rate = total_fails / self.eval_count if self.eval_count > 0 else 0
            lines_data.append(("Failures/Blocks:", f"{total_fails} ({fail_rate:.1%})", 0))

            if self.fail_missing_signal > 0:
                fail_perc = self.fail_missing_signal / self.eval_count
                lines_data.append(("- Missing Signal:", f"{self.fail_missing_signal} ({fail_perc:.1%})", 1))

            if self.fail_condition > 0:
                fail_perc = self.fail_condition / self.eval_count
                lines_data.append(("- Base Condition:", f"{self.fail_condition} ({fail_perc:.1%})", 1))

            if self.fail_obi_threshold > 0:
                fail_perc = self.fail_obi_threshold / self.eval_count
                lines_data.append(("- OBI Threshold:", f"{self.fail_obi_threshold} ({fail_perc:.1%})", 1))

            if self.fail_spread_filter > 0:
                fail_perc = self.fail_spread_filter / self.eval_count
                lines_data.append(("- Spread Filter:", f"{self.fail_spread_filter} ({fail_perc:.1%})", 1))

            if self.fail_zscore_filter > 0:
                fail_perc = self.fail_zscore_filter / self.eval_count
                lines_data.append(("- ZScore Filter:", f"{self.fail_zscore_filter} ({fail_perc:.1%})", 1))

            if self.fail_regime_filter > 0:
                fail_perc = self.fail_regime_filter / self.eval_count
                lines_data.append(("- Regime Filter:", f"{self.fail_regime_filter} ({fail_perc:.1%})", 1))

        # Format and log the lines
        label_padding_width = 26  # Fixed width for consistent alignment
        for item in lines_data:
            label, value, indent = item
            indent_str = "  " * indent
            formatted_label = f"{indent_str}{label}".ljust(label_padding_width)
            self.logger.info(f"  • {formatted_label}{value}")
