# hyperliquid_bot/strategies/obi_scalper_strategy.py

import logging
import uuid
import collections
import json
from typing import Dict, List, Optional, Tuple, Literal, Any
import pandas as pd
import numpy as np

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.strategies.evaluator import StrategyInterface


class OBIScalperStrategy(StrategyInterface):
    """
    # Deprecated – kept for future use (Task R-103)

    OBI Scalper strategy that uses Order Book Imbalance (OBI) signals for scalping.

    This strategy:
    1. Monitors OBI (Order Book Imbalance) at specified levels
    2. Enters trades when OBI exceeds thresholds and signs match between L1_3 and L1_10
    3. Man<PERSON> exits via take profit, stop loss, or timeout
    4. Applies volatility and spread filters to avoid trading in unfavorable conditions
    5. Respects market regime constraints
    """

    def __init__(self, config: Config, strategy_name: str):
        """Initialize the OBI Scalper with configuration settings."""
        super().__init__(config, strategy_name)
        self.id = strategy_name
        self.logger = logging.getLogger(f"{self.__class__.__name__}[{strategy_name}]")

        # Load strategy parameters from config
        strategy_config = config.strategies.OBIScalperStrategy.defaults

        # Core parameters
        self.vol_veto_threshold = strategy_config.vol_veto_threshold
        self.spread_veto_threshold = strategy_config.spread_veto_threshold
        self.obi_l1_3_trigger = strategy_config.obi_l1_3_trigger
        self.tp_ticks = strategy_config.tp_ticks
        self.sl_ticks = strategy_config.sl_ticks
        self.timeout_seconds = strategy_config.timeout_seconds
        self.allowed_gms_states = strategy_config.allowed_gms_states
        self.zero_sign_eps = strategy_config.zero_sign_eps

        # Get tick size from configuration
        # In a real implementation, this would come from exchange metadata
        # For now, we'll use the configured value
        self.tick_size = config.strategies.OBIScalperStrategy.defaults.tick_size

        # Active trade tracking
        self.active_trade_id = None
        self.entry_timestamp = None
        self.entry_price = None

        # Initialize counters for diagnostics
        self.eval_count = 0
        self.fail_missing_signal = 0
        self.fail_vol_veto = 0
        self.fail_spread_veto = 0
        self.fail_regime_gate = 0
        self.fail_obi_threshold = 0
        self.fail_sign_mismatch = 0
        self.success_entry_long = 0
        self.success_entry_short = 0
        self.exit_tp_count = 0
        self.exit_sl_count = 0
        self.exit_timeout_count = 0

        # Initialize diagnostic counter for T-106A
        self.diag = collections.Counter()

        # Log initialization
        self.logger.info(f"Initialized {self.__class__.__name__} strategy with parameters:")
        self.logger.info(f"  vol_veto_threshold: {self.vol_veto_threshold}")
        self.logger.info(f"  spread_veto_threshold: {self.spread_veto_threshold}")
        self.logger.info(f"  obi_l1_3_trigger: {self.obi_l1_3_trigger}")
        self.logger.info(f"  tp_ticks: {self.tp_ticks}")
        self.logger.info(f"  sl_ticks: {self.sl_ticks}")
        self.logger.info(f"  timeout_seconds: {self.timeout_seconds}")
        self.logger.info(f"  allowed_gms_states: {self.allowed_gms_states}")
        self.logger.info(f"  zero_sign_eps: {self.zero_sign_eps}")
        self.logger.info(f"  tick_size: {self.tick_size}")

    def _safe_sign(self, x: float, eps: float = None) -> int:
        """
        Calculate the sign of a value with a threshold for zero.

        Args:
            x: The value to determine the sign of
            eps: Epsilon threshold for considering a value as zero (default: self.zero_sign_eps)

        Returns:
            1 for positive, -1 for negative, 0 for values within +/- eps of zero
        """
        # Use the provided epsilon or default to the instance variable
        eps = eps if eps is not None else self.zero_sign_eps
        return 0 if abs(x) <= eps else (1 if x > 0 else -1)

    @property
    def required_signals(self) -> List[str]:
        """Define the signals required by this strategy."""
        signals = [
            "close",              # Current price
            "best_bid",           # Best bid price
            "best_ask",           # Best ask price
            "regime",             # Market regime
            "realised_vol_1s",    # 1-second realized volatility
            "spread_relative",    # Relative spread
            "raw_obi_L1_3",       # Raw OBI for levels 1-3
            "raw_obi_L1_10",      # Raw OBI for levels 1-10
            "tick_size",          # Tick size for the instrument
        ]

        return list(set(signals))

    def create_entry_signal(self, side: str, size: float, entry_price: float,
                           tp_price: float, sl_price: float, strategy_id: str) -> Dict[str, Any]:
        """
        Create an entry signal dictionary.

        Args:
            side: Trade direction ("LONG" or "SHORT")
            size: Position size
            entry_price: Entry price
            tp_price: Take profit price
            sl_price: Stop loss price
            strategy_id: Strategy identifier

        Returns:
            Dictionary containing entry signal details
        """
        signal_id = str(uuid.uuid4())[:8]  # Generate a unique ID for this signal

        return {
            "id": signal_id,
            "type": "entry",
            "side": side,
            "size": size,
            "price": entry_price,
            "tp_price": tp_price,
            "sl_price": sl_price,
            "strategy_id": strategy_id
        }

    def create_exit_signal(self, trade_id: str, reason: str) -> Dict[str, Any]:
        """
        Create an exit signal dictionary.

        Args:
            trade_id: ID of the trade to exit
            reason: Reason for exit (e.g., "TP", "SL", "TIMEOUT")

        Returns:
            Dictionary containing exit signal details
        """
        return {
            "type": "exit",
            "trade_id": trade_id,
            "reason": reason
        }

    def evaluate(self, signal_data: Dict, current_position=None, current_timestamp=None) -> List[Dict]:
        """
        Evaluate market conditions for potential entry or exit signals.

        Args:
            signal_data: Dictionary of market signals
            current_position: Current position information (unused in this implementation)
            current_timestamp: Current timestamp in seconds

        Returns:
            List of signal dictionaries (entry or exit)
        """
        self.eval_count += 1
        self.diag['bars_seen'] += 1  # T-106A: Increment bars seen counter
        signals = []

        # Check for required signals
        missing_signals = [s for s in self.required_signals if s not in signal_data or pd.isna(signal_data.get(s))]
        if missing_signals:
            self.fail_missing_signal += 1
            self.diag['missing_signals'] += 1  # T-106A: Increment missing signals counter
            self.logger.debug(f"Missing signals for OBI Scalper evaluation: {missing_signals}")
            return signals

        # Get tick size from signals if available
        if "tick_size" in signal_data and not pd.isna(signal_data["tick_size"]):
            self.tick_size = signal_data["tick_size"]

        # 0) Regime gate
        gms_state = signal_data.get("regime")
        if gms_state not in self.allowed_gms_states:
            self.fail_regime_gate += 1
            self.diag['regime_gate'] += 1  # T-106A: Increment regime gate counter
            self.logger.debug(f"Regime gate: {gms_state} not in allowed states {self.allowed_gms_states}")
            return signals

        # 1) Hard vetoes
        if signal_data["realised_vol_1s"] > self.vol_veto_threshold:
            self.fail_vol_veto += 1
            self.diag['vol_veto'] += 1  # T-106A: Increment volatility veto counter
            self.logger.debug(f"Volatility veto: {signal_data['realised_vol_1s']} > {self.vol_veto_threshold}")
            return signals

        if signal_data["spread_relative"] > self.spread_veto_threshold:
            self.fail_spread_veto += 1
            self.diag['spread_veto'] += 1  # T-106A: Increment spread veto counter
            self.logger.debug(f"Spread veto: {signal_data['spread_relative']} > {self.spread_veto_threshold}")
            return signals

        # 2) Exit management (if active trade belongs to this strategy)
        if self.active_trade_id is not None:
            price = signal_data["close"]  # last trade price

            # Check for exit conditions
            exit_reason = None

            # Check if we have valid entry data
            if self.entry_price is not None:
                # Get the side of the active trade (LONG or SHORT)
                # In a real implementation, we would store this with the trade
                # For now, we'll infer it from the signals we're checking

                # Check for timeout first (applies to both LONG and SHORT)
                if current_timestamp is not None and self.entry_timestamp is not None and (current_timestamp - self.entry_timestamp) >= self.timeout_seconds:
                    exit_reason = "TIMEOUT"
                    self.exit_timeout_count += 1
                    self.diag['exit_timeout'] += 1  # T-106A: Increment timeout exit counter
                    self.logger.debug(f"Timeout: {current_timestamp - self.entry_timestamp} >= {self.timeout_seconds} seconds")

                # If no timeout, check TP/SL conditions
                # For simplicity in this implementation, we'll check both LONG and SHORT conditions
                # In a real implementation, we would know the side of the trade

                # LONG position TP/SL checks
                elif price >= (self.entry_price + (self.tp_ticks * self.tick_size)):
                    exit_reason = "TP"
                    self.exit_tp_count += 1
                    self.diag['exit_tp'] += 1  # T-106A: Increment TP exit counter
                    self.logger.debug(f"LONG TP hit: price {price} >= entry {self.entry_price} + {self.tp_ticks} ticks")
                elif price <= (self.entry_price - (self.sl_ticks * self.tick_size)):
                    exit_reason = "SL"
                    self.exit_sl_count += 1
                    self.diag['exit_sl'] += 1  # T-106A: Increment SL exit counter
                    self.logger.debug(f"LONG SL hit: price {price} <= entry {self.entry_price} - {self.sl_ticks} ticks")

                # SHORT position TP/SL checks (commented out for now as our tests only test LONG positions)
                # elif price <= (self.entry_price - (self.tp_ticks * self.tick_size)):
                #     exit_reason = "TP"
                #     self.exit_tp_count += 1
                #     self.diag['exit_tp'] += 1  # T-106A: Increment TP exit counter
                #     self.logger.debug(f"SHORT TP hit: price {price} <= entry {self.entry_price} - {self.tp_ticks} ticks")
                # elif price >= (self.entry_price + (self.sl_ticks * self.tick_size)):
                #     exit_reason = "SL"
                #     self.exit_sl_count += 1
                #     self.diag['exit_sl'] += 1  # T-106A: Increment SL exit counter
                #     self.logger.debug(f"SHORT SL hit: price {price} >= entry {self.entry_price} + {self.sl_ticks} ticks")

            if exit_reason:
                self.logger.info(f"Exit signal: {exit_reason} at price {price}")
                signals.append(self.create_exit_signal(self.active_trade_id, exit_reason))
                self.active_trade_id = None
                self.entry_timestamp = None
                self.entry_price = None
                return signals  # do NOT seek new entry this bar

        # 3) Entry logic (if flat for this strategy)
        if self.active_trade_id is None:
            l1_3 = signal_data["raw_obi_L1_3"]
            l1_10 = signal_data["raw_obi_L1_10"]

            # Check OBI threshold and sign confirmation
            side = None
            if l1_3 > self.obi_l1_3_trigger and self._safe_sign(l1_3) == self._safe_sign(l1_10):
                side = "LONG"
                self.success_entry_long += 1
                self.diag['entry_long'] += 1  # T-106A: Increment long entry counter
            elif l1_3 < -self.obi_l1_3_trigger and self._safe_sign(l1_3) == self._safe_sign(l1_10):
                side = "SHORT"
                self.success_entry_short += 1
                self.diag['entry_short'] += 1  # T-106A: Increment short entry counter
            else:
                if abs(l1_3) <= self.obi_l1_3_trigger:
                    self.fail_obi_threshold += 1
                    self.diag['obi_threshold'] += 1  # T-106A: Increment OBI threshold failure counter
                else:
                    self.fail_sign_mismatch += 1
                    self.diag['sign_mismatch'] += 1  # T-106A: Increment sign mismatch counter

            if side:
                # Calculate entry parameters
                entry_price = signal_data["best_ask"] if side == "LONG" else signal_data["best_bid"]
                size = self.risk_manager.calculate_scalper_position_size(entry_price, self.sl_ticks * self.tick_size)
                tp_price = entry_price + self.tp_ticks * self.tick_size if side == "LONG" else entry_price - self.tp_ticks * self.tick_size
                sl_price = entry_price - self.sl_ticks * self.tick_size if side == "LONG" else entry_price + self.sl_ticks * self.tick_size

                # Create entry signal
                sig = self.create_entry_signal(side, size, entry_price, tp_price, sl_price, strategy_id=self.id)
                signals.append(sig)

                # Update trade tracking
                self.active_trade_id = sig["id"]
                self.entry_timestamp = current_timestamp
                self.entry_price = entry_price

                self.logger.info(f"Entry signal: {side} at price {entry_price}, size {size}, TP {tp_price}, SL {sl_price}")

        return signals

    def log_evaluation_summary(self):
        """Logs a summary of evaluation results and failures."""
        if self.eval_count == 0:
            return

        total_success = self.success_entry_long + self.success_entry_short
        total_exits = self.exit_tp_count + self.exit_sl_count + self.exit_timeout_count
        # Calculate total failures for logging
        total_failures = (self.fail_missing_signal + self.fail_vol_veto + self.fail_spread_veto +
                         self.fail_regime_gate + self.fail_obi_threshold + self.fail_sign_mismatch)
        failure_rate = total_failures / self.eval_count if self.eval_count > 0 else 0

        self.logger.info(f"OBIScalperStrategy Evaluation Summary:")
        self.logger.info(f"  Total Evaluations: {self.eval_count}")
        self.logger.info(f"  Entry Signals: {total_success} (Long: {self.success_entry_long}, Short: {self.success_entry_short})")
        self.logger.info(f"  Exit Signals: {total_exits} (TP: {self.exit_tp_count}, SL: {self.exit_sl_count}, Timeout: {self.exit_timeout_count})")
        self.logger.info(f"  Signal Rate: {total_success / self.eval_count:.1%}")
        self.logger.info(f"  Failure Rate: {failure_rate:.1%}")
        self.logger.info(f"  Failures by Type:")
        self.logger.info(f"    Missing Signals: {self.fail_missing_signal}")
        self.logger.info(f"    Volatility Veto: {self.fail_vol_veto}")
        self.logger.info(f"    Spread Veto: {self.fail_spread_veto}")
        self.logger.info(f"    Regime Gate: {self.fail_regime_gate}")
        self.logger.info(f"    OBI Threshold: {self.fail_obi_threshold}")
        self.logger.info(f"    Sign Mismatch: {self.fail_sign_mismatch}")

        # T-106A: Log diagnostic counter as JSON
        self.log_diagnostic_counters()

    def log_diagnostic_counters(self):
        """Log the diagnostic counters as JSON for T-106A."""
        if not self.diag:
            self.logger.info("No diagnostic data collected.")
            return

        diag_json = json.dumps(self.diag, indent=2, sort_keys=True)
        self.logger.info(f"OBIScalperStrategy Diagnostic Counters (T-106A):\n{diag_json}")
