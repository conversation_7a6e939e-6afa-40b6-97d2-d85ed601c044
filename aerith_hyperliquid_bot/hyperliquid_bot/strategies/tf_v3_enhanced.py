# Enhanced TF-v3 Strategy with Explicit Regime-Aware Filtering
# This module extends the base TF-v3 strategy with additional regime awareness features

from hyperliquid_bot.strategies.tf_v3 import TFV3Strategy
from typing import Dict, Optional, Literal, Tuple, Any
import pandas as pd


class TFV3EnhancedStrategy(TFV3Strategy):
    """
    Enhanced TF-v3 strategy with explicit regime-aware filtering and tracking.
    
    Key enhancements:
    1. Explicit opposing signal filtering with detailed tracking
    2. Regime confidence-based adjustments (Phase 2)
    3. Better logging and metrics for filtered signals
    """
    
    def __init__(self, config, state=None, portfolio=None, risk_manager_interface=None):
        super().__init__(config, state, portfolio, risk_manager_interface)
        
        # Initialize enhanced tracking metrics
        self.filtered_signals = {
            'bull_filtered_shorts': 0,
            'bear_filtered_longs': 0,
            'chop_filtered_all': 0,
            'low_confidence_filtered': 0,
            'total_signals_evaluated': 0,
            'total_signals_passed': 0
        }
        
        # Configuration for regime-aware features
        self.regime_aware_config = {
            'skip_opposing_signals': True,
            'min_confidence': 0.6,
            'min_duration_minutes': 15,
            'confidence_position_scaling': False  # Phase 2
        }
        
    def evaluate(self, signals: Dict) -> <PERSON>ple[Optional[Literal["long", "short"]], Optional[Dict]]:
        """
        Enhanced evaluate method with explicit regime-aware filtering.
        """
        self.filtered_signals['total_signals_evaluated'] += 1
        
        # First, check if we should evaluate at all (hourly boundaries only)
        timestamp = signals.get('timestamp')
        if timestamp and hasattr(timestamp, 'minute') and timestamp.minute != 0:
            self.logger.debug(f"Skipping evaluation - not on hourly boundary: {timestamp}")
            return None, None
            
        # Call parent evaluate to get base signal
        direction, strategy_info = super().evaluate(signals)
        
        # If parent already filtered, return
        if direction is None:
            return None, None
            
        # Get regime information
        regime = signals.get('regime', 'Unknown')
        regime_confidence = signals.get('regime_confidence', 1.0)
        regime_duration = signals.get('regime_duration_minutes', float('inf'))
        
        # Apply enhanced regime-aware filtering
        if self.regime_aware_config['skip_opposing_signals']:
            # Explicit opposing signal filtering
            if direction == 'long' and regime == 'BEAR':
                self.filtered_signals['bear_filtered_longs'] += 1
                self.logger.info(
                    f"[REGIME FILTER] Skipping LONG signal in BEAR regime "
                    f"(confidence: {regime_confidence:.2f}, duration: {regime_duration}min)"
                )
                self._log_regime_filter_event('bear_filtered_long', direction, regime, regime_confidence)
                return None, None
                
            elif direction == 'short' and regime == 'BULL':
                self.filtered_signals['bull_filtered_shorts'] += 1
                self.logger.info(
                    f"[REGIME FILTER] Skipping SHORT signal in BULL regime "
                    f"(confidence: {regime_confidence:.2f}, duration: {regime_duration}min)"
                )
                self._log_regime_filter_event('bull_filtered_short', direction, regime, regime_confidence)
                return None, None
        
        # Check minimum confidence threshold
        if regime_confidence < self.regime_aware_config['min_confidence']:
            self.filtered_signals['low_confidence_filtered'] += 1
            self.logger.info(
                f"[CONFIDENCE FILTER] Skipping {direction.upper()} signal - "
                f"regime confidence {regime_confidence:.2f} < {self.regime_aware_config['min_confidence']}"
            )
            self._log_regime_filter_event('low_confidence', direction, regime, regime_confidence)
            return None, None
            
        # Check minimum regime duration
        if regime_duration < self.regime_aware_config['min_duration_minutes']:
            self.logger.info(
                f"[DURATION FILTER] Skipping {direction.upper()} signal - "
                f"regime duration {regime_duration}min < {self.regime_aware_config['min_duration_minutes']}min"
            )
            self._log_regime_filter_event('short_duration', direction, regime, regime_confidence)
            return None, None
            
        # Signal passed all filters
        self.filtered_signals['total_signals_passed'] += 1
        self.logger.info(
            f"[SIGNAL PASSED] {direction.upper()} signal in {regime} regime "
            f"(confidence: {regime_confidence:.2f}, duration: {regime_duration}min)"
        )
        
        # Phase 2: Confidence-based position scaling (optional)
        if self.regime_aware_config['confidence_position_scaling'] and strategy_info:
            strategy_info = self._apply_confidence_scaling(strategy_info, regime_confidence, regime_duration)
            
        return direction, strategy_info
        
    def _apply_confidence_scaling(self, strategy_info: Dict, confidence: float, duration: float) -> Dict:
        """
        Apply position size scaling based on regime confidence and duration.
        Phase 2 enhancement - disabled by default.
        """
        position_scale = 1.0
        
        # Scale by confidence
        if confidence < 0.6:
            position_scale = 0.5  # Half size for low confidence
        elif confidence < 0.8:
            position_scale = 0.75  # 3/4 size for medium confidence
            
        # Scale by duration
        if duration < 15:
            position_scale *= 0.7  # Reduce size for new regimes
            
        # Apply scaling
        if 'position_notional' in strategy_info:
            original_size = strategy_info['position_notional']
            strategy_info['position_notional'] = original_size * position_scale
            strategy_info['position_scale'] = position_scale
            strategy_info['scale_reason'] = f"confidence={confidence:.2f}, duration={duration}min"
            
            self.logger.info(
                f"[POSITION SCALED] {position_scale:.2f}x - "
                f"Original: ${original_size:,.2f}, Scaled: ${strategy_info['position_notional']:,.2f}"
            )
            
        return strategy_info
        
    def _log_regime_filter_event(self, filter_type: str, direction: str, regime: str, confidence: float):
        """Log detailed regime filter events for analysis."""
        self._log_event(
            'REGIME_FILTER',
            f"Filtered {direction} signal",
            {
                'filter_type': filter_type,
                'direction': direction,
                'regime': regime,
                'confidence': confidence,
                'timestamp': pd.Timestamp.now()
            }
        )
        
    def get_filter_stats(self) -> Dict[str, Any]:
        """Get comprehensive filtering statistics."""
        stats = self.filtered_signals.copy()
        
        # Calculate filter rate
        if stats['total_signals_evaluated'] > 0:
            stats['filter_rate'] = 1 - (stats['total_signals_passed'] / stats['total_signals_evaluated'])
            stats['pass_rate'] = stats['total_signals_passed'] / stats['total_signals_evaluated']
        else:
            stats['filter_rate'] = 0
            stats['pass_rate'] = 0
            
        return stats
        
    def print_filter_summary(self):
        """Print a summary of filtered signals."""
        stats = self.get_filter_stats()
        
        print("\n=== TF-v3 Enhanced Regime Filter Summary ===")
        print(f"Total signals evaluated: {stats['total_signals_evaluated']}")
        print(f"Total signals passed: {stats['total_signals_passed']}")
        print(f"Filter rate: {stats['filter_rate']:.1%}")
        print(f"\nFiltered signals breakdown:")
        print(f"  - BULL filtered shorts: {stats['bull_filtered_shorts']}")
        print(f"  - BEAR filtered longs: {stats['bear_filtered_longs']}")
        print(f"  - CHOP filtered all: {stats['chop_filtered_all']}")
        print(f"  - Low confidence filtered: {stats['low_confidence_filtered']}")
        print("=" * 44)