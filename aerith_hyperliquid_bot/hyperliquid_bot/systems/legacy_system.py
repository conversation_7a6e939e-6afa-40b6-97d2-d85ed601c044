"""
Legacy Trading System Composition.

This module composes the FROZEN legacy components into a complete trading system
that produces the baseline performance of 180 trades with 215% ROI.

DO NOT MODIFY this system unless you fully understand the implications.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.legacy.registry import get_legacy_registry
from hyperliquid_bot.core.interfaces import IRegimeDetector, IStrategy, IDataLoader


class LegacyTradingSystem:
    """
    Complete legacy trading system that combines:
    - LegacyGranularMicrostructureDetector for regime detection
    - LegacyTFV2Strategy for trading signals
    - LegacyDataLoader for raw2/ data loading
    
    This system is FROZEN at the exact configuration that produces:
    - 180 trades
    - 215% ROI
    - 25% risk per trade
    """
    
    def __init__(self, config: Config):
        """Initialize the legacy trading system."""
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Get component registry
        self.registry = get_legacy_registry()
        
        # Initialize components
        self._init_components()
        
        self.logger.info("Legacy Trading System Initialized")
        self.logger.info("  - FRO<PERSON><PERSON> at 180 trades, 215% ROI baseline")
        self.logger.info("  - DO NOT MODIFY without understanding implications")
        
    def _init_components(self):
        """Initialize all legacy components from the registry."""
        try:
            # Create data loader
            self.data_loader = self.registry.create(
                IDataLoader,
                "legacy_raw2",
                self.config
            )
            self.logger.info("  ✓ Legacy Data Loader created")
            
            # Create regime detector
            self.regime_detector = self.registry.create(
                IRegimeDetector,
                "legacy_granular_microstructure",
                self.config
            )
            self.logger.info("  ✓ Legacy Regime Detector created")
            
            # Create strategy
            self.strategy = self.registry.create(
                IStrategy,
                "legacy_tf_v2",
                self.config
            )
            self.logger.info("  ✓ Legacy TF-v2 Strategy created")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize legacy components: {e}")
            raise
            
    def validate_configuration(self) -> bool:
        """
        Validate that configuration matches legacy requirements.
        
        Returns:
            True if configuration is valid for legacy system
        """
        issues = []
        
        # Check detector type
        if self.config.regime.detector_type != "granular_microstructure":
            issues.append(f"Wrong detector type: {self.config.regime.detector_type} (expected: granular_microstructure)")
            
        # Check strategy type
        if not self.config.strategies.use_tf_v2:
            issues.append("TF-v2 strategy not enabled")
            
        # Check risk fraction - it's at the root level, not under strategies
        if hasattr(self.config, 'tf_v3') and hasattr(self.config.tf_v3, 'risk_frac'):
            risk_frac = self.config.tf_v3.risk_frac
            if abs(risk_frac - 0.25) > 0.001:  # Allow small floating point differences
                issues.append(f"Wrong risk fraction: {risk_frac} (expected: 0.25)")
        else:
            issues.append("Risk fraction not configured")
            
        # Check momentum thresholds
        mom_strong = getattr(self.config.regime, 'gms_mom_strong_thresh', None)
        mom_weak = getattr(self.config.regime, 'gms_mom_weak_thresh', None)
        
        if mom_strong != 100.0:
            issues.append(f"Wrong strong momentum threshold: {mom_strong} (expected: 100.0)")
        if mom_weak != 50.0:
            issues.append(f"Wrong weak momentum threshold: {mom_weak} (expected: 50.0)")
            
        # Check volatility thresholds
        vol_high = getattr(self.config.regime, 'gms_vol_high_thresh', None)
        vol_low = getattr(self.config.regime, 'gms_vol_low_thresh', None)
        
        if vol_high != 0.0092:
            issues.append(f"Wrong high volatility threshold: {vol_high} (expected: 0.0092)")
        if vol_low != 0.0055:
            issues.append(f"Wrong low volatility threshold: {vol_low} (expected: 0.0055)")
            
        # Log validation results
        if issues:
            self.logger.error("Legacy system configuration validation FAILED:")
            for issue in issues:
                self.logger.error(f"  ❌ {issue}")
            return False
        else:
            self.logger.info("✅ Legacy system configuration validation PASSED")
            return True
            
    def run_backtest(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Run a backtest using the legacy system.
        
        This is a simplified interface that delegates to the actual backtester.
        
        Args:
            start_date: Start date for backtest
            end_date: End date for backtest
            
        Returns:
            Backtest results dictionary
        """
        # Validate configuration first
        if not self.validate_configuration():
            raise ValueError("Legacy system configuration validation failed")
            
        # This would typically delegate to the backtester
        # For now, just return a placeholder
        self.logger.info(f"Would run backtest from {start_date} to {end_date}")
        return {
            'status': 'not_implemented',
            'message': 'Backtest execution should use the main backtester'
        }
        
    def get_diagnostics(self) -> Dict[str, Any]:
        """
        Get diagnostic information about the legacy system.
        
        Returns:
            Dictionary with component information and health checks
        """
        diagnostics = {
            'system': 'legacy',
            'expected_trades': 180,
            'expected_roi': 2.15,  # 215%
            'risk_per_trade': 0.25,  # 25%
            'components': {}
        }
        
        # Get component diagnostics
        try:
            diagnostics['components']['data_loader'] = {
                'type': 'legacy_raw2',
                'status': 'initialized',
                'data_dir': str(self.data_loader.data_dir) if hasattr(self.data_loader, 'data_dir') else 'unknown'
            }
        except:
            diagnostics['components']['data_loader'] = {'status': 'error'}
            
        try:
            diagnostics['components']['regime_detector'] = {
                'type': 'legacy_granular_microstructure',
                'status': 'initialized',
                'required_signals': self.regime_detector.required_signals
            }
        except:
            diagnostics['components']['regime_detector'] = {'status': 'error'}
            
        try:
            diagnostics['components']['strategy'] = {
                'type': 'legacy_tf_v2',
                'status': 'initialized',
                'required_signals': self.strategy.required_signals
            }
        except:
            diagnostics['components']['strategy'] = {'status': 'error'}
            
        return diagnostics


def create_legacy_system(config: Config) -> LegacyTradingSystem:
    """
    Factory function to create a legacy trading system.
    
    Args:
        config: System configuration
        
    Returns:
        Configured legacy trading system
    """
    return LegacyTradingSystem(config)