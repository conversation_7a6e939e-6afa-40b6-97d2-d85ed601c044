"""
Modern Trading System Composition
===================================
This module composes all modern components into a complete trading system.

Components:
- ModernContinuousGMSDetector: Continuous regime detection with adaptive thresholds
- ModernTFV3Strategy: Enhanced trend following with OBI/funding filters
- ModernDataLoader: 1-second feature data loading

IMPORTANT: This system is currently experiencing 100% regime gate failures
and needs debugging in Phase 3.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from ..config.settings import Config
from ..core.registry import get_component, ComponentRegistry
from ..core.interfaces import IRegimeDetector, IStrategy, IDataLoader


class ModernTradingSystem:
    """
    Complete modern trading system combining all modern components.
    
    This system uses:
    - Continuous GMS detection (60s cadence)
    - Enhanced TF-v3 strategy
    - 1-second feature data
    - Adaptive thresholds
    - Risk suppression
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Validate configuration
        self._validate_config()
        
        # Initialize components from registry
        self.regime_detector = self._init_regime_detector()
        self.strategy = self._init_strategy()
        self.data_loader = self._init_data_loader()
        
        # System state
        self.last_regime_update = None
        self.current_regime = "Unknown"
        self.regime_history = []
        
        self.logger.info(
            "Modern Trading System initialized:\n"
            f"  - Detector: {self.regime_detector.__class__.__name__}\n"
            f"  - Strategy: {self.strategy.__class__.__name__}\n"
            f"  - Data Loader: {self.data_loader.__class__.__name__}"
        )
    
    def _validate_config(self):
        """Validate configuration for modern system."""
        # Skip system mode check as it's not in the Config model
        
        # Check regime detector
        if self.config.regime.detector_type != "continuous_gms":
            raise ValueError(
                f"Modern system requires continuous_gms detector, "
                f"got: {self.config.regime.detector_type}"
            )
        
        # Check strategy
        if not self.config.strategies.use_tf_v3:
            raise ValueError("Modern system requires TF-v3 strategy enabled")
        
        # Validate critical thresholds
        expected_thresholds = {
            'gms_vol_high_thresh': 0.015,
            'gms_vol_low_thresh': 0.005,
            'gms_mom_strong_thresh': 2.5,
            'gms_mom_weak_thresh': 0.5,
        }
        
        for key, expected in expected_thresholds.items():
            actual = getattr(self.config.regime, key, None)
            if actual != expected:
                self.logger.warning(
                    f"Threshold mismatch: {key} = {actual} "
                    f"(expected {expected})"
                )
        
        # Check risk fraction
        if self.config.tf_v3.risk_frac != 0.02:
            self.logger.warning(
                f"Risk fraction: {self.config.tf_v3.risk_frac} "
                f"(expected 0.02 for modern system)"
            )
    
    def _init_regime_detector(self) -> IRegimeDetector:
        """Initialize modern regime detector."""
        # Import to trigger registration
        import hyperliquid_bot.modern.detector
        
        # Get from registry
        detector_class = get_component(
            IRegimeDetector,
            "modern_continuous_gms"
        )
        
        if detector_class is None:
            raise ValueError("Failed to get modern regime detector from registry")
        
        # Instantiate with config
        return detector_class(self.config)
    
    def _init_strategy(self) -> IStrategy:
        """Initialize modern strategy."""
        # Import to trigger registration
        import hyperliquid_bot.modern.strategy
        
        # Get from registry
        strategy_class = get_component(
            IStrategy,
            "modern_tf_v3"
        )
        
        if strategy_class is None:
            raise ValueError("Failed to get modern strategy from registry")
        
        # Instantiate with config
        return strategy_class(self.config)
    
    def _init_data_loader(self) -> IDataLoader:
        """Initialize modern data loader."""
        # Import to trigger registration
        import hyperliquid_bot.modern.data_loader
        
        # Get from registry
        loader_class = get_component(
            IDataLoader,
            "modern_features_1s"
        )
        
        if loader_class is None:
            raise ValueError("Failed to get modern data loader from registry")
        
        # Instantiate with config
        return loader_class(self.config)
    
    def process_signals(self, signals: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process market signals through the modern system.
        
        Args:
            signals: Market signals dictionary
            
        Returns:
            Dictionary with trading decision and metadata
        """
        result = {
            'timestamp': signals.get('timestamp'),
            'action': None,
            'direction': None,
            'regime': None,
            'regime_raw': None,
            'risk_suppressed': False,
            'confidence': 0.0,
        }
        
        try:
            # 1. Update regime detection
            regime_state = self.regime_detector.get_raw_state(signals)
            
            # Extract regime information
            if isinstance(regime_state, dict):
                self.current_regime = regime_state.get('state', 'Unknown')
                result['regime_raw'] = self.current_regime
                result['risk_suppressed'] = regime_state.get('risk_suppressed', False)
                result['confidence'] = regime_state.get('regime_confidence', 0.0)
                
                # Map regime for strategy (3-state)
                result['regime'] = self._map_regime(self.current_regime)
            else:
                # Legacy string response
                self.current_regime = regime_state
                result['regime_raw'] = regime_state
                result['regime'] = self._map_regime(regime_state)
            
            # Update regime history
            self.regime_history.append({
                'timestamp': signals.get('timestamp'),
                'regime': result['regime'],
                'raw_regime': result['regime_raw'],
                'confidence': result['confidence']
            })
            
            # Limit history size
            if len(self.regime_history) > 1000:
                self.regime_history = self.regime_history[-1000:]
            
            # 2. Prepare signals for strategy
            strategy_signals = signals.copy()
            strategy_signals['regime'] = result['regime']
            strategy_signals['risk_suppressed'] = result['risk_suppressed']
            strategy_signals['gms_snapshot'] = {
                'state': self.current_regime,
                'risk_suppressed': result['risk_suppressed'],
                'regime_confidence': result['confidence'],
                'timestamp': signals.get('timestamp'),
            }
            
            # 3. Evaluate strategy
            direction, info = self.strategy.evaluate(strategy_signals)
            
            if direction:
                result['action'] = 'enter'
                result['direction'] = direction
                result['position_info'] = info
                
                self.logger.info(
                    f"Modern system signal: {direction} in {result['regime']} "
                    f"(raw: {result['regime_raw']}, conf: {result['confidence']:.2f})"
                )
            
        except Exception as e:
            self.logger.error(f"Error processing signals: {e}", exc_info=True)
            result['error'] = str(e)
        
        return result
    
    def _map_regime(self, raw_regime: str) -> str:
        """
        Map raw 8-state regime to 3-state for strategy.
        
        This mapping is critical for the strategy to work correctly.
        """
        # Use centralized mapping
        from ..utils.state_mapping import map_gms_state
        
        # Get mapping configuration
        map_weak_bear_to_bear = getattr(
            self.config.regime, 
            'map_weak_bear_to_bear', 
            False
        )
        
        mapped = map_gms_state(raw_regime, map_weak_bear_to_bear)
        
        # Log mapping for debugging
        if raw_regime != mapped:
            self.logger.debug(f"Regime mapped: {raw_regime} -> {mapped}")
        
        return mapped
    
    def get_diagnostics(self) -> Dict[str, Any]:
        """Get system diagnostics for debugging."""
        diagnostics = {
            'system': 'modern',
            'components': {
                'detector': self.regime_detector.__class__.__name__,
                'strategy': self.strategy.__class__.__name__,
                'loader': self.data_loader.__class__.__name__,
            },
            'current_regime': self.current_regime,
            'regime_history_length': len(self.regime_history),
        }
        
        # Add detector diagnostics
        if hasattr(self.regime_detector, 'adaptive_vol_threshold'):
            diagnostics['adaptive_thresholds'] = {
                'enabled': self.regime_detector.adaptive_vol_threshold is not None
            }
        
        # Add strategy diagnostics
        if hasattr(self.strategy, 'eval_count'):
            diagnostics['strategy_stats'] = {
                'evaluations': self.strategy.eval_count,
                'regime_gates_failed': getattr(self.strategy, 'fail_regime_gate', 0),
                'risk_suppressed': getattr(self.strategy, 'fail_risk_suppressed', 0),
                'successful_longs': getattr(self.strategy, 'success_entry_long', 0),
                'successful_shorts': getattr(self.strategy, 'success_entry_short', 0),
            }
        
        return diagnostics
    
    def analyze_regime_failures(self) -> Dict[str, Any]:
        """
        Analyze why regime gate failures are occurring.
        
        This is a debugging method to help understand the 100% failure issue.
        """
        analysis = {
            'total_evaluations': 0,
            'regime_distribution': {},
            'failure_reasons': {},
        }
        
        # Count regime occurrences
        for entry in self.regime_history:
            regime = entry.get('regime', 'Unknown')
            analysis['regime_distribution'][regime] = \
                analysis['regime_distribution'].get(regime, 0) + 1
        
        # Calculate percentages
        total = sum(analysis['regime_distribution'].values())
        if total > 0:
            analysis['regime_percentages'] = {
                regime: count / total * 100
                for regime, count in analysis['regime_distribution'].items()
            }
        
        # Check for common issues
        if 'BULL' not in analysis['regime_distribution'] and \
           'BEAR' not in analysis['regime_distribution']:
            analysis['issue'] = "No BULL or BEAR regimes detected"
            analysis['likely_cause'] = "All regimes mapping to CHOP"
        
        return analysis