import pandas as pd
import numpy as np
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_obi_zscore(data: pd.DataFrame, raw_obi_col: str, window: int, min_periods: int) -> pd.Series:
    """
    Calculates the rolling Z-score for the Order Book Imbalance (OBI).

    Args:
        data (pd.DataFrame): DataFrame containing the raw OBI data. Must have a DatetimeIndex.
        raw_obi_col (str): The name of the column in 'data' containing the raw OBI values.
        window (int): The rolling window size for mean and standard deviation calculation.
        min_periods (int): The minimum number of observations in the window required.

    Returns:
        pd.Series: A Series containing the calculated OBI Z-scores, indexed like the input data.
                   Returns a Series of np.nan if the input column is not found or another
                   critical error occurs.
    """
    logger.info(f"Calculating OBI Z-score with window={window}, min_periods={min_periods}, column='{raw_obi_col}'")

    if raw_obi_col not in data.columns:
        logger.error(f"Error: Raw OBI column '{raw_obi_col}' not found in DataFrame.")
        return pd.Series(np.nan, index=data.index, name=f'obi_zscore_{window}')

    obi_series = data[raw_obi_col]
    initial_nan_count = obi_series.isna().sum()
    logger.info(f"Input '{raw_obi_col}' contains {initial_nan_count} NaN values.")

    # Calculate rolling mean and standard deviation
    rolling_mean = obi_series.rolling(window=window, min_periods=min_periods).mean()
    rolling_std = obi_series.rolling(window=window, min_periods=min_periods).std()

    # Handle potential division by zero or near-zero std dev
    zero_std_mask = (rolling_std == 0) | rolling_std.isna()
    zero_std_count = zero_std_mask.sum()
    if zero_std_count > 0:
        logger.warning(f"Found {zero_std_count} zero or NaN values in rolling_std (window={window}). Replacing with NaN before division.")
        rolling_std = rolling_std.replace(0, np.nan) # Replace exact zeros with NaN

    # Calculate Z-score
    obi_zscore = (obi_series - rolling_mean) / rolling_std

    # Log NaNs before filling
    nans_before_fill = obi_zscore.isna().sum()
    logger.info(f"OBI Z-score Series (window={window}) has {nans_before_fill} NaN values before final fill.")

    # Fill remaining NaNs with 0.0 as per original logic
    obi_zscore_filled = obi_zscore.fillna(0.0)

    # Log NaNs after filling
    nans_after_fill = obi_zscore_filled.isna().sum()
    logger.info(f"Final OBI Z-score Series (window={window}) has {nans_after_fill} NaN values after fillna(0.0).")

    return obi_zscore_filled.rename(f'obi_zscore_{window}')

if __name__ == '__main__':
    # Example Usage (for testing purposes)
    dates = pd.to_datetime(['2023-01-01 00:00:00', '2023-01-01 00:01:00', '2023-01-01 00:02:00',
                            '2023-01-01 00:03:00', '2023-01-01 00:04:00', '2023-01-01 00:05:00',
                            '2023-01-01 00:06:00', '2023-01-01 00:07:00'])
    test_data = pd.DataFrame({
        'raw_obi': [0.5, 0.6, 0.55, 0.65, 0.7, 0.7, 0.7, 0.4, np.nan, 0.8],
        'other_col': range(10)
    }, index=pd.to_datetime(['2023-01-01 00:00:00', '2023-01-01 00:01:00', '2023-01-01 00:02:00',
                             '2023-01-01 00:03:00', '2023-01-01 00:04:00', '2023-01-01 00:05:00',
                             '2023-01-01 00:06:00', '2023-01-01 00:07:00', '2023-01-01 00:08:00',
                             '2023-01-01 00:09:00']))

    logger.info("--- Running Example Test Case ---")
    zscore_result = calculate_obi_zscore(test_data, 'raw_obi', window=5, min_periods=3)
    print("\nTest DataFrame:")
    print(test_data)
    print("\nCalculated OBI Z-score:")
    print(zscore_result)

    logger.info("--- Testing Error Case (Column Not Found) ---")
    error_result = calculate_obi_zscore(test_data, 'non_existent_col', window=5, min_periods=3)
    print("\nResult for non-existent column:")
    print(error_result)

    logger.info("--- Testing Zero Standard Deviation Case ---")
    zero_std_data = pd.DataFrame({
        'raw_obi': [0.5, 0.5, 0.5, 0.5, 0.5, 0.6, 0.7, 0.8]
    }, index=pd.to_datetime(['2023-01-01 00:00:00', '2023-01-01 00:01:00', '2023-01-01 00:02:00',
                             '2023-01-01 00:03:00', '2023-01-01 00:04:00', '2023-01-01 00:05:00',
                             '2023-01-01 00:06:00', '2023-01-01 00:07:00']))
    zero_std_result = calculate_obi_zscore(zero_std_data, 'raw_obi', window=4, min_periods=3)
    print("\nTest DataFrame (Zero Std Dev):")
    print(zero_std_data)
    print("\nCalculated OBI Z-score (Zero Std Dev):")
    print(zero_std_result)