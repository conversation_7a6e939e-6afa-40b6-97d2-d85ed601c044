# hyperliquid_bot/signals/calculator.py

import logging
import pandas as pd
import numpy as np
import time  # Added for performance timing
from scipy.stats import percentileofscore  # Added percentileofscore

# from scipy.ndimage import gaussian_filter1d # Optional: If needed for advanced smoothing later
# import bottleneck as bn # Optional: If needed for faster rolling calculations later
import pandas_ta as ta  # Use pandas_ta

# Import the Config class
from hyperliquid_bot.config.settings import Config

# Import DataHandlerInterface for type hinting (though we assume modified output)
from hyperliquid_bot.data import DataHandlerInterface

# Import the new OBI Z-score calculator function

# Import the dedicated depth metrics calculator function
from .depth_metrics_calculator import calculate_depth_metrics
# Import data utilities
from hyperliquid_bot.utils.data_utils import deduplicate
# Import feature naming utilities
from hyperliquid_bot.utils.feature_naming import obi_col

logger = logging.getLogger(__name__)


# === helper: safe ATR =======================================================
def _add_atr(df: pd.DataFrame, length: int, out_col: str) -> None:
    """
    Adds an ATR column to *df* with pandas_ta and renames it to *out_col*.
    Works with any pandas‑ta version (default column is 'ATR_{length}' or 'ATRr_{length}').
    If pandas-ta fails, falls back to manual calculation.
    """
    if length <= 1:
        df[out_col] = np.nan
        return

    # Use deduplicate utility to handle duplicate indices
    df_clean = deduplicate(df.copy())

    try:
        # Calculate ATR on the clean DataFrame
        df_clean.ta.atr(length=length, append=True)  # writes 'ATR_{length}' or 'ATRr_{length}'

        # Try both possible column naming conventions
        possible_cols = [f"ATR_{length}", f"ATRr_{length}"]
        found_col = None

        for col in possible_cols:
            if col in df_clean.columns:
                found_col = col
                break

        if found_col:
            # Create a new column in the original DataFrame
            df_out = df.copy()
            df_out[out_col] = np.nan  # Initialize with NaN

            # Copy the ATR values from the clean DataFrame
            for idx in df_clean.index:
                if idx in df.index:
                    df_out.loc[idx, out_col] = df_clean.loc[idx, found_col]

            # Copy the new column back to the original DataFrame
            df[out_col] = df_out[out_col]
            logger.debug(f"ATR column '{found_col}' calculated and added as '{out_col}'")
        else:
            logger.warning(f"ATR column not found after ta.atr(); checked {possible_cols}. Falling back to manual calculation.")
            _calculate_atr_manually(df, length, out_col)
    except Exception as e:
        logger.warning(f"Error calculating ATR with pandas-ta: {e}. Falling back to manual calculation.")
        _calculate_atr_manually(df, length, out_col)


def _calculate_atr_manually(df: pd.DataFrame, length: int, out_col: str) -> None:
    """
    Manually calculates ATR when pandas-ta fails.

    Args:
        df: DataFrame with OHLC data
        length: ATR period
        out_col: Output column name
    """
    try:
        # Calculate True Range
        high_low = df['high'] - df['low']
        high_close_prev = np.abs(df['high'] - df['close'].shift(1))
        low_close_prev = np.abs(df['low'] - df['close'].shift(1))

        # True Range is the maximum of the three
        tr = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1)

        # Calculate ATR as rolling average of True Range
        df[out_col] = tr.rolling(window=length).mean()

        # Forward fill NaN values after the warmup period
        # This ensures we have values to work with but doesn't back-fill
        first_valid_idx = df[out_col].first_valid_index()
        if first_valid_idx is not None:
            df[out_col] = df[out_col].fillna(method='ffill')

        logger.info(f"Manual ATR calculation complete. NaN count: {df[out_col].isna().sum()}")
    except Exception as e:
        logger.error(f"Manual ATR calculation failed: {e}")
        df[out_col] = np.nan


# =============================================================================


# Helper function for percentile rank (to handle potential edge cases or future customization)
def rolling_percentile_rank(
    series: pd.Series, window: int, min_periods: int
) -> pd.Series:
    """
    Rolling percentile rank in **0–1** range, evaluated against the
    *history up to the previous bar* to avoid look‑ahead bias.
    """
    if series.isnull().all():
        return pd.Series(np.nan, index=series.index)

    def _pct(arr):
        # need at least 2 valid points to compute a percentile
        clean = pd.Series(arr[:-1]).dropna()
        if len(clean) == 0:
            return np.nan
        pct = percentileofscore(clean, arr.iloc[-1]) / 100.0
        return np.clip(pct, 0.0, 1.0)     # guarantees 0 ≤ pct ≤ 1

    return series.rolling(window=window, min_periods=min_periods).apply(_pct, raw=False)


# === helper: percentile of history (unbiased) ===============================
def _pct_of_history(arr: pd.Series) -> float:
    """
    Percentile of the *last* value versus the *previous* observations (0–1).
    Returns NaN until ≥2 valid points exist.
    """
    clean = pd.Series(arr[:-1]).dropna()
    if clean.empty:
        return np.nan
    pct = percentileofscore(clean, arr.iloc[-1]) / 100.0
    return np.clip(pct, 0.0, 1.0)     # guarantees 0 ≤ pct ≤ 1
# =============================================================================


class SignalEngine:
    """
    Calculates technical indicators and performs time-series operations on signals.

    Assumes the input DataFrame from DataHandler includes pre-calculated raw,
    instantaneous microstructure features (e.g., raw_obi_X, raw_spread_rel)
    alongside standard OHLCV data.

    This class calculates:
    - Standard TA indicators (EMAs, ADX, RSI, Keltner, etc.) using pandas-ta.
    - Smoothed OBI from raw OBI.
    - Rolling statistics (mean, std) on raw spread metrics.
    - Momentum indicators (ROC, MA Slope) for granular regime detection.
    - Volatility indicators (ATR Percent) for granular regime detection.
    - Funding rate proxy.
    """

    def __init__(self, config: Config, data_handler: DataHandlerInterface):
        """
        Initializes the SignalCalculator.

        Args:
            config: The main configuration object.
            data_handler: An instance conforming to DataHandlerInterface (expected
                          to provide OHLCV + raw microstructure features).
        """
        self.config = config
        self.data_handler = data_handler
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(
            "Initialized SignalCalculator (using pandas-ta, expects raw microstructure features)"
        )

    def smooth_signal(self, series: pd.Series, window: int, mode: str) -> pd.Series:
        """
        Applies smoothing to a signal series.

        Args:
            series (pd.Series): The input signal series.
            window (int): The rolling window size for smoothing.
            mode (str): The smoothing mode, either "sma" or "ema".

        Returns:
            pd.Series: The smoothed signal series.
        """
        if not isinstance(series, pd.Series):
            logger.error(f"smooth_signal: input 'series' must be a pandas Series. Got {type(series)}")
            return pd.Series([np.nan] * len(series), index=series.index if hasattr(series, 'index') else None, name=series.name if hasattr(series, 'name') else None)

        if window <= 1:
            logger.debug(f"smooth_signal: window {window} <= 1, returning original series:")
            return series.copy()

        min_periods = max(1, window // 2)

        if mode.lower() == "sma":
            smoothed_series = series.rolling(window=window, min_periods=min_periods).mean()
        elif mode.lower() == "ema":
            smoothed_series = series.ewm(span=window, adjust=False, min_periods=min_periods).mean()
        else:
            logger.warning(f"smooth_signal: Unknown smoothing mode '{mode}'. Returning original series.")
            return series.copy()

        return smoothed_series

    def zscore_signal(self, series: pd.Series, lookback: int) -> pd.Series:
        """
        Calculates the rolling Z-score for a given series.

        Args:
            series (pd.Series): The input signal series.
            lookback (int): The rolling window size for mean and standard deviation calculation.

        Returns:
            pd.Series: A Series containing the calculated Z-scores.
                       Initial values will be NaN due to the lookback window.
        """
        if not isinstance(series, pd.Series):
            logger.error(f"zscore_signal: input 'series' must be a pandas Series. Got {type(series)}")
            return pd.Series([np.nan] * len(series), index=series.index if hasattr(series, 'index') else None, name=series.name if hasattr(series, 'name') else None)

        if lookback <= 1:
            logger.warning(f"zscore_signal: lookback window {lookback} <= 1. Z-score is undefined. Returning NaNs:")
            return pd.Series(np.nan, index=series.index, name=series.name if hasattr(series, 'name') else None)

        min_periods = max(1, lookback // 2)

        rolling_mean = series.rolling(window=lookback, min_periods=min_periods).mean()
        rolling_std = series.rolling(window=lookback, min_periods=min_periods).std()

        rolling_std_safe = rolling_std.replace(0, np.nan)

        z_scores = (series - rolling_mean) / rolling_std_safe

        return z_scores
    
    def calculate_all_signals(self) -> pd.DataFrame:
        """
        Calculates all configured technical indicators and derived signals
        on the input DataFrame (OHLCV + raw microstructure features).

        Returns:
            pandas DataFrame: The input DataFrame with added columns for each
                              calculated indicator/signal (smoothed, rolled, etc.).
                              Returns an empty DataFrame on critical error.
        """
        cfg_indicators = self.config.indicators
        cfg_strategies = self.config.strategies
        cfg_micro = self.config.microstructure  # Added for microstructure settings
        cfg_regime = self.config.regime  # Added for regime detector settings

        # Get data (assuming it contains OHLCV + raw microstructure columns)
        ohlcv_micro_data = self.data_handler.get_ohlcv_data()

        # Check for duplicate indices and fix them
        if not ohlcv_micro_data.index.is_unique:
            self.logger.warning("Found duplicate indices in input data. Fixing by keeping the last occurrence.")
            ohlcv_micro_data = ohlcv_micro_data[~ohlcv_micro_data.index.duplicated(keep='last')]

        # --- DEBUG: Check columns at SignalEngine START ---
        self.logger.info(
            f"[DEBUG SE Start] Received input data with shape: {ohlcv_micro_data.shape}. Columns: {ohlcv_micro_data.columns.tolist()}"
        )
        self.logger.info(
            f"First few timestamps: {ohlcv_micro_data.index.min()} to {ohlcv_micro_data.index.max()}"
        )
        self.logger.info(
            f"Input data memory usage: {ohlcv_micro_data.memory_usage(deep=True).sum() / (1024*1024):.2f} MB"
        )
        self.logger.info("Checking for NaNs before calculations...")

        if ohlcv_micro_data.empty:
            self.logger.error(
                "Cannot calculate signals: Input data (OHLCV+Micro) is empty."
            )
            return pd.DataFrame()

        self.logger.info(
            f"Calculating signals on input data with shape: {ohlcv_micro_data.shape}"
        )
        self.logger.debug(f"Input columns: {ohlcv_micro_data.columns.tolist()}")

        # Use a copy to avoid modifying the original DataFrame stored in DataHandler
        signals_df = ohlcv_micro_data.copy()

        # --- Verify presence of essential columns ---
        # Conditionally require 'volume' based on config
        required_base_cols = ["open", "high", "low", "close"]
        if self.config.indicators.require_volume_for_signals:
            required_base_cols.append("volume")
            self.logger.debug("SignalEngine configured to require 'volume' column.")
        else:
            self.logger.debug("SignalEngine configured NOT to require 'volume' column.")

        # Add required raw microstructure columns based on config (adjust names as needed)
        required_raw_micro_cols = []
        depth_levels = cfg_micro.depth_levels
        # Use helper function to dynamically resolve OBI column name
        try:
            raw_obi_col = obi_col(signals_df.columns, depth_levels)
            required_raw_micro_cols.append(raw_obi_col)
        except KeyError as e:
            self.logger.error(f"Failed to resolve OBI column for depth {depth_levels}: {e}")
            return pd.DataFrame()
        raw_depth_ratio_col = f"raw_depth_ratio_{depth_levels}"
        raw_depth_pressure_col = f"raw_depth_pressure_{depth_levels}"
        required_raw_micro_cols.extend([raw_depth_ratio_col, raw_depth_pressure_col])
        required_raw_micro_cols.extend(["raw_spread_abs", "raw_spread_rel"])

        missing_cols = [
            col
            for col in required_base_cols + required_raw_micro_cols
            if col not in signals_df.columns
        ]
        if missing_cols:
            self.logger.error(
                f"CRITICAL: Missing required columns in input data before signal calculation: {missing_cols}. "
                "Ensure DataHandler provides OHLCV and raw microstructure features."
            )
            return pd.DataFrame()

        try:
            # --- Calculate Base ATR (needed by multiple components) - Preserve existing high-quality data ---
            # Check if high-quality ATR statistics already exist (e.g., from feature files)
            preserve_atr_14_sec = False
            preserve_atr_percent_sec = False

            if ("atr_14_sec" in signals_df.columns
                and signals_df["atr_14_sec"].isna().mean() <= 0.02):
                # Keep existing high-quality atr_14_sec column
                self.logger.info(f"Preserving existing high-quality 'atr_14_sec' column (NaN ratio: {signals_df['atr_14_sec'].isna().mean():.3f})")
                preserve_atr_14_sec = True
                # Use existing ATR as the main ATR column
                if "atr" not in signals_df.columns or signals_df["atr"].isna().all():
                    signals_df["atr"] = signals_df["atr_14_sec"]
                    self.logger.info("Using existing 'atr_14_sec' as main 'atr' column")

            if ("atr_percent_sec" in signals_df.columns
                and signals_df["atr_percent_sec"].isna().mean() <= 0.02):
                # Keep existing high-quality atr_percent_sec column
                self.logger.info(f"Preserving existing high-quality 'atr_percent_sec' column (NaN ratio: {signals_df['atr_percent_sec'].isna().mean():.3f})")
                preserve_atr_percent_sec = True
                # Use existing ATR percent as the main ATR percent column
                if "atr_percent" not in signals_df.columns or signals_df["atr_percent"].isna().all():
                    signals_df["atr_percent"] = signals_df["atr_percent_sec"]
                    self.logger.info("Using existing 'atr_percent_sec' as main 'atr_percent' column")

            # Determine the maximum ATR period needed across enabled strategies AND granular detector
            # Define these variables outside the conditional block to avoid UnboundLocalError
            tf_atr_period = (
                cfg_indicators.tf_atr_period
                if cfg_strategies.use_tf_v2
                else 0
            )
            mr_atr_period = (
                cfg_indicators.mr_atr_period if cfg_strategies.use_mean_reversion else 0
            )
            mv_atr_period = (
                cfg_indicators.mv_atr_period if cfg_strategies.use_mean_variance else 0
            )
            gms_atr_period = (
                cfg_indicators.gms_atr_percent_period
                if cfg_regime.detector_type == "granular_microstructure"
                else 0
            )
            atr_period = max(
                tf_atr_period, mr_atr_period, mv_atr_period, gms_atr_period, 1
            )

            # Only calculate ATR if we don't have high-quality existing data
            if not preserve_atr_14_sec:
                self.logger.debug(f"Calculating main ATR with period {atr_period}")
                _add_atr(signals_df, atr_period, "atr")
                if "atr" in signals_df.columns:
                    self.logger.debug(
                        f"Main ATR calculated. NaN count: {signals_df['atr'].isna().sum()}"
                    )
                else:
                    self.logger.debug("Main ATR calculation skipped.")
            else:
                self.logger.info("Skipping ATR calculation - using existing high-quality ATR from feature files")

            # --- Microstructure Signal Processing ---
            self.logger.debug("Processing raw microstructure signals...")

# --- NEW OBI Processing: Iterate over all raw_obi_* columns ---
            self.logger.info("Starting new OBI signal processing for all depths...")
            # Use the unified depth_levels parameter
            depth_levels = cfg_micro.depth_levels
            # raw_obi_col already resolved above using helper function
            obi_columns = [col for col in signals_df.columns if col.startswith("raw_obi_")]

            if not obi_columns:
                self.logger.warning("No 'raw_obi_*' columns found to process.")

            for raw_obi_col_name in obi_columns:
                self.logger.debug(f"Processing OBI column: {raw_obi_col_name}")

                # Extract suffix (e.g., '5', '10_custom_weights')
                suffix = raw_obi_col_name.replace("raw_obi_", "")
                smoothed_col_name = f"obi_smoothed_{suffix}"
                zscore_col_name = f"obi_zscore_{suffix}"

                # 1. Smoothing
                smoothing_window = cfg_micro.obi_smoothing_window
                smoothing_type = cfg_micro.obi_smoothing_type

                if smoothing_window is not None and smoothing_window > 1:
                    self.logger.debug(f"Applying {smoothing_type.upper()} smoothing to {raw_obi_col_name} with window {smoothing_window} -> {smoothed_col_name}")
                    signals_df[smoothed_col_name] = self.smooth_signal(
                        series=signals_df[raw_obi_col_name],
                        window=smoothing_window,
                        mode=smoothing_type
                    )
                    nan_count = signals_df[smoothed_col_name].isna().sum()
                    self.logger.debug(f"Column '{smoothed_col_name}' created. NaN count: {nan_count}/{len(signals_df)}")
                else:
                    self.logger.warning(f"Skipping smoothing for {raw_obi_col_name} (window: {smoothing_window}). Using raw series as smoothed.")
                    signals_df[smoothed_col_name] = signals_df[raw_obi_col_name].copy()
                    # Ensure the column exists even if smoothing is skipped, for z-score step

                # 2. Z-score (on the smoothed series)
                zscore_window = cfg_micro.obi_zscore_window

                if zscore_window is not None and zscore_window > 1:
                    if smoothed_col_name in signals_df:
                        self.logger.debug(f"Applying Z-score normalization to {smoothed_col_name} with lookback {zscore_window} -> {zscore_col_name}")
                        signals_df[zscore_col_name] = self.zscore_signal(
                            series=signals_df[smoothed_col_name],
                            lookback=zscore_window
                        )
                        nan_count = signals_df[zscore_col_name].isna().sum()
                        self.logger.debug(f"Column '{zscore_col_name}' created. NaN count: {nan_count}/{len(signals_df)}")
                    else:
                        self.logger.error(f"Cannot calculate Z-score for {suffix}: Smoothed column '{smoothed_col_name}' not found.")
                        signals_df[zscore_col_name] = np.nan
                else:
                    self.logger.info(f"Skipping Z-score normalization for OBI {suffix} as 'obi_zscore_window' is {zscore_window}.")
                    signals_df[zscore_col_name] = np.nan # Ensure column exists as NaN if not calculated

            self.logger.info("Finished new OBI signal processing.")
            # --- END NEW OBI Processing ---
            # 2. Rolling Spread Statistics - Preserve existing high-quality data from feature files
            spread_roll_win = cfg_micro.spread_rolling_window
            spread_metric = cfg_micro.spread_metric_to_roll
            # Determine the correct raw spread column name based on config
            raw_spread_col = (
                "raw_spread_rel"
                if cfg_micro.spread_metric_to_roll == "relative"
                else "raw_spread_abs"
            )

            # Check if high-quality spread statistics already exist (e.g., from feature files)
            if ("spread_mean" in signals_df.columns
                and signals_df["spread_mean"].isna().mean() <= 0.02):
                # Keep existing high-quality spread_mean column
                self.logger.info(f"Preserving existing high-quality 'spread_mean' column (NaN ratio: {signals_df['spread_mean'].isna().mean():.3f})")
                preserve_spread_mean = True
            else:
                preserve_spread_mean = False

            if ("spread_std" in signals_df.columns
                and signals_df["spread_std"].isna().mean() <= 0.02):
                # Keep existing high-quality spread_std column
                self.logger.info(f"Preserving existing high-quality 'spread_std' column (NaN ratio: {signals_df['spread_std'].isna().mean():.3f})")
                preserve_spread_std = True
            else:
                preserve_spread_std = False

            # Only calculate rolling spread stats if we don't have high-quality existing data
            if (not preserve_spread_mean or not preserve_spread_std) and raw_spread_col in signals_df.columns and spread_roll_win > 1:
                self.logger.debug(
                    f"Calculating Rolling Spread Stats: Window={spread_roll_win}, Metric={spread_metric}"
                )
                rolling_spread = signals_df[raw_spread_col].rolling(
                    window=spread_roll_win, min_periods=max(1, spread_roll_win // 2)
                )

                if not preserve_spread_mean:
                    signals_df["spread_mean"] = rolling_spread.mean()
                    self.logger.debug(f"Calculated spread_mean. NaNs: {signals_df['spread_mean'].isna().sum()}")

                if not preserve_spread_std:
                    signals_df["spread_std"] = rolling_spread.std()
                    self.logger.debug(f"Calculated spread_std. NaNs: {signals_df['spread_std'].isna().sum()}")

            elif not preserve_spread_mean or not preserve_spread_std:
                self.logger.warning(
                    f"Cannot calculate spread stats: Column '{raw_spread_col}' not found or window <= 1."
                )
                if not preserve_spread_mean:
                    signals_df["spread_mean"] = np.nan
                if not preserve_spread_std:
                    signals_df["spread_std"] = np.nan
            # --- Conditionally Calculate Primary Spread Percentiles for GMS ---
            spread_mean_mode = getattr(
                cfg_regime, "gms_spread_mean_thresh_mode", "fixed"
            )
            spread_std_mode = getattr(cfg_regime, "gms_spread_std_thresh_mode", "fixed")
            # Removed duplicate getattr calls
            calc_mean_pct = spread_mean_mode == "percentile"
            calc_std_pct = spread_std_mode == "percentile"

            # Initialize columns to ensure they exist
            if "spread_mean_primary_pctile" not in signals_df.columns:
                signals_df["spread_mean_primary_pctile"] = np.nan
            if "spread_std_primary_pctile" not in signals_df.columns:
                signals_df["spread_std_primary_pctile"] = np.nan

            if calc_mean_pct or calc_std_pct:
                window = getattr(
                    cfg_regime, "gms_spread_percentile_window", None
                )  # Get specific config, default None
                if (
                    window is None
                ):  # Fallback to general spread window if specific one not set
                    window = getattr(
                        cfg_micro, "spread_rolling_window", 24
                    )  # Use a reasonable default if micro setting also missing

                if window > 1:
                    min_p = max(
                        1, int(window * 0.5)
                    )  # Ensure min_periods is at least 1 and reasonable

                    if (
                        calc_mean_pct
                        and "spread_mean" in signals_df.columns
                        and not signals_df["spread_mean"].isnull().all()
                    ):
                        try:
                            # self.logger.debug(f"Calculating Primary Spread Mean Percentile (Window={window}, MinP={min_p})...") # Removed log
                            signals_df["spread_mean_primary_pctile"] = rolling_percentile_rank(
                                signals_df["spread_mean"], window, min_p)
                        except Exception as e:
                            self.logger.error(
                                f"Error calculating primary spread mean percentile: {e}",
                                exc_info=True,
                            )  # Added exc_info
                            signals_df["spread_mean_primary_pctile"] = (
                                np.nan
                            )  # Fallback to NaN on error

                    if (
                        calc_std_pct
                        and "spread_std" in signals_df.columns
                        and not signals_df["spread_std"].isnull().all()
                    ):
                        try:
                            # self.logger.debug(f"Calculating Primary Spread Std Percentile (Window={window}, MinP={min_p})...") # Removed log
                            signals_df["spread_std_primary_pctile"] = rolling_percentile_rank(
                                signals_df["spread_std"], window, min_p)
                        except Exception as e:
                            self.logger.error(
                                f"Error calculating primary spread std percentile: {e}",
                                exc_info=True,
                            )  # Added exc_info
                            signals_df["spread_std_primary_pctile"] = (
                                np.nan
                            )  # Fallback to NaN on error
                else:
                    # self.logger.warning(f"GMS Primary Spread Percentile window ({window}) <= 1, skipping calculation.") # Removed log
                    pass  # Keep else block structure if needed
            # --- End Primary Spread Percentile Calc ---

            # Ensure any existing fallback percentile calculations (e.g., for 'Uncertain' refinement)
            # remain separate and use distinct column names (e.g., 'spread_mean_fallback_pctile').
            # The code above uses 'spread_mean_primary_pctile' and 'spread_std_primary_pctile'.
            # Removed exit log block
            # --- Conditionally Calculate Spread Mean Percentile ---
            tight_spread_pct_thresh = getattr(
                cfg_regime, "gms_tight_spread_fallback_percentile", None
            )
            # Calculate only if the percentile threshold is set (not None) and > 0
            if tight_spread_pct_thresh is not None and tight_spread_pct_thresh > 0:
                window = getattr(cfg_regime, "gms_tight_spread_percentile_window", 24)
                min_p = max(1, window // 2)
                # Ensure spread_mean exists and window is valid
                if "spread_mean" in signals_df.columns and window > 1:
                    try:
                        self.logger.debug(
                            f"Calculating Spread Mean Percentile (Window={window}, MinP={min_p})..."
                        )
                        # Calculate rolling percentile rank of spread_mean
                        signals_df["spread_mean_pctile"] = rolling_percentile_rank(
                            signals_df["spread_mean"], window, min_p)
                        self.logger.debug(
                            f"Spread Mean Percentile calculated. NaN count: {signals_df['spread_mean_pctile'].isna().sum()}"
                        )
                    except Exception as e:
                        self.logger.error(
                            f"Error calculating Spread Mean Percentile: {e}",
                            exc_info=True,
                        )
                        signals_df["spread_mean_pctile"] = (
                            np.nan
                        )  # Add NaN column on error
                else:
                    self.logger.warning(
                        "Could not calculate Spread Mean Percentile: 'spread_mean' missing or window <= 1."
                    )
                    signals_df["spread_mean_pctile"] = np.nan
            else:
                # Ensure column doesn't exist if feature is disabled, or add it as NaN?
                # Let's add as NaN to prevent potential KeyErrors later if code expects it
                if "spread_mean_pctile" not in signals_df.columns:
                    signals_df["spread_mean_pctile"] = np.nan
            # --- End Spread Mean Percentile Calculation ---

            # 3. Funding Rate Proxy
            self.logger.debug("Adding default funding rate proxy signal 'funding_rate'")
            signals_df["funding_rate"] = self.config.costs.funding_rate

            # --- DEBUG: State Before Advanced Calcs ---
            self.logger.info(
                f"[DEBUG SetB Pre] Shape BEFORE advanced calcs: {signals_df.shape}"
            )
            if raw_obi_col in signals_df.columns:
                self.logger.info(
                    f"[DEBUG SetB Pre] Input '{raw_obi_col}' NaNs: {signals_df[raw_obi_col].isna().sum()}"
                )
            else:
                self.logger.warning(f"[DEBUG SetB Pre] Input '{raw_obi_col}' MISSING!")
            if raw_spread_col in signals_df.columns:
                self.logger.info(
                    f"[DEBUG SetB Pre] Input '{raw_spread_col}' NaNs: {signals_df[raw_spread_col].isna().sum()}"
                )
            else:
                self.logger.warning(
                    f"[DEBUG SetB Pre] Input '{raw_spread_col}' MISSING!"
                )
            # --- END DEBUG ---

            # --- NEW: Advanced Microstructure Signal Calculations (Conditional) ---
            self.logger.debug(
                "Calculating advanced microstructure signals (conditional)..."
            )
            cfg_regime = self.config.regime
            cfg_micro = self.config.microstructure
            # Ensure raw column names are defined earlier in the method, e.g.:
            # raw_obi_col = f'raw_obi_{cfg_micro.obi_levels}'
            # raw_spread_col = 'raw_spread_rel' if cfg_micro.spread_metric_to_roll == 'relative' else 'raw_spread_abs'


            # --- B. Rolling Percentile of Raw Spread ---
            # Conditional on a gate value being set in config (0-100)
            spread_pct_gate_cfg = getattr(
                cfg_regime, "gms_spread_percentile_gate", None
            )  # Get the gate value (0-100)
            self.logger.info(
                f"Checking GMS Spread Percentile Gate: {spread_pct_gate_cfg}"
            )

            # Activate if the gate is configured (not None)
            if spread_pct_gate_cfg is not None:
                # Robustly get pct_window, defaulting to spread_rolling_window, then 50
                pct_window = getattr(
                    cfg_micro, "spread_percentile_window", None
                )  # Check specific first
                if pct_window is None:
                    pct_window = getattr(
                        cfg_micro, "spread_rolling_window", 50
                    )  # Fallback to rolling, then 50
                    self.logger.debug(
                        f"Using spread_rolling_window ({pct_window}) for spread percentile rank calculation."
                    )
                else:
                    self.logger.debug(
                        f"Using dedicated spread_percentile_window ({pct_window}) for spread percentile rank calculation."
                    )

                # Ensure it's a valid integer > 1
                if not isinstance(pct_window, int) or pct_window <= 1:
                    self.logger.error(
                        f"Invalid pct_window value ({pct_window}) derived for spread percentile rank. Check 'spread_percentile_window' or 'spread_rolling_window'. Defaulting to 50."
                    )
                    pct_window = 50  # Safe default
                min_p_pct = max(1, pct_window // 2)

                # Determine the raw spread column to use (consistent with spread_mean/std)
                raw_spread_col = (
                    "raw_spread_rel"
                    if cfg_micro.spread_metric_to_roll == "relative"
                    else "raw_spread_abs"
                )
                # raw_spread_col = getattr(cfg_micro, 'raw_spread_col', 'raw_spread_rel') # Old way, less consistent

                if raw_spread_col in signals_df.columns:
                    try:
                        self.logger.debug(
                            f"Calculating Raw Spread Percentile Rank for '{raw_spread_col}' (Window={pct_window}, MinP={min_p_pct})..."
                        )
                        # Calculate rolling percentile rank of the current spread
                        signals_df["raw_spread_percentile"] = rolling_percentile_rank(
                            signals_df[raw_spread_col],
                            window=pct_window,
                            min_periods=min_p_pct,
                        )
                        # NaNs will exist for initial periods, which is expected.
                        self.logger.debug(
                            f"Raw Spread Percentile Rank calculated. Initial NaN count: {signals_df['raw_spread_percentile'].isna().sum()}"
                        )
                        # --- DEBUG: After Spread Percentile Calc ---
                        if "raw_spread_percentile" in signals_df.columns:
                            spread_pct_nan_count = (
                                signals_df["raw_spread_percentile"].isna().sum()
                            )
                            self.logger.info(
                                f"[DEBUG SetB Pct] 'raw_spread_percentile' (rank) calculated. Shape: {signals_df.shape}, NaNs: {spread_pct_nan_count}"
                            )
                            # self.logger.info(f"[DEBUG SetB Pct] Head:\n{signals_df['raw_spread_percentile'].dropna().head()}") # Maybe too verbose
                        else:
                            self.logger.warning(
                                "[DEBUG SetB Pct] 'raw_spread_percentile' column MISSING after calculation attempt."
                            )
                        # --- END DEBUG ---
                    except Exception as e:
                        self.logger.error(
                            f"Error calculating Raw Spread Percentile Rank (Window={pct_window}, MinP={min_p_pct}): {e}",
                            exc_info=True,
                        )  # Add context and exc_info
                        signals_df["raw_spread_percentile"] = np.nan  # Fallback
                else:
                    self.logger.warning(
                        f"Skipping Spread Percentile Rank: Required column '{raw_spread_col}' not found."
                    )
                    signals_df["raw_spread_percentile"] = np.nan  # Fallback value
            else:
                # If gate is not set, ensure column exists as NaN
                self.logger.info(
                    "GMS Spread Percentile Gate not configured. Setting 'raw_spread_percentile' to NaN."
                )
                signals_df["raw_spread_percentile"] = np.nan

            # --- C. Spread Trend ---
            # Conditional on a lookback period > 1 in config
            spread_trend_lookback_cfg = getattr(
                cfg_regime, "gms_spread_trend_lookback", None
            )  # Get lookback period
            if spread_trend_lookback_cfg is not None and spread_trend_lookback_cfg > 1:
                self.logger.debug(
                    f"Calculating Spread Trend using 'spread_mean' over lookback {spread_trend_lookback_cfg} periods..."
                )
                # Requires 'spread_mean' to be calculated beforehand
                if (
                    "spread_mean" in signals_df.columns
                    and not signals_df["spread_mean"].isnull().all()
                ):
                    try:
                        # Option 1: Simple difference over the lookback period
                        # signals_df['spread_trend'] = signals_df['spread_mean'].diff(spread_trend_lookback_cfg)

                        # Option 2: Slope of a short MA (current implementation) - Keep for now
                        # Use the lookback config directly for the MA window? Or keep it shorter?
                        # Let's make the MA window related to the lookback for smoothing trend noise
                        short_ma_window = max(
                            2, int(spread_trend_lookback_cfg * 0.5)
                        )  # e.g., half the lookback
                        self.logger.debug(
                            f"  Using short MA window {short_ma_window} for smoothing spread trend"
                        )
                        spread_mean_ma_short = (
                            signals_df["spread_mean"]
                            .rolling(window=short_ma_window, min_periods=1)
                            .mean()
                        )
                        # Trend is the difference (slope) of this short MA over 1 period
                        signals_df["spread_trend"] = spread_mean_ma_short.diff(
                            1
                        )  # Rate of change of smoothed spread mean

                        # Fill initial NaNs from diff() and rolling()
                        signals_df["spread_trend"].fillna(
                            0.0, inplace=True
                        )  # Fill initial NaNs
                        self.logger.debug(
                            f"Spread Trend calculated. Final non-NaN count: {signals_df['spread_trend'].notna().sum()}"
                        )
                    except Exception as e:
                        self.logger.error(
                            f"Error calculating Spread Trend (Lookback={spread_trend_lookback_cfg}): {e}",
                            exc_info=True,
                        )
                        signals_df["spread_trend"] = np.nan  # Fallback value
                else:
                    self.logger.warning(
                        f"Skipping Spread Trend: Required column 'spread_mean' not available or all NaN (Lookback={spread_trend_lookback_cfg})."
                    )
                    signals_df["spread_trend"] = np.nan  # Fallback value
            else:
                self.logger.info(
                    f"Spread Trend calculation skipped (gms_spread_trend_lookback={spread_trend_lookback_cfg})."
                )
                signals_df["spread_trend"] = np.nan  # Ensure column exists

            # --- D/E. Depth Slope & Skew (Using dedicated calculator) ---
            depth_slope_limit_cfg = getattr(
                cfg_regime, "gms_depth_slope_thin_limit", None
            )  # Expects float or None
            depth_skew_thresh_cfg = getattr(
                cfg_regime, "gms_depth_skew_thresh", None
            )  # Expects float or None

            calc_slope = depth_slope_limit_cfg is not None
            calc_skew = depth_skew_thresh_cfg is not None

            # Define required input columns for the dedicated calculator
            required_depth_cols = ["bid_slope", "ask_slope", "book_asymmetry"]

            # Check if we should allow NaN depth metrics
            allow_nan_micro_depth = getattr(cfg_micro, "allow_nan_micro_depth", True)

            if calc_slope or calc_skew:
                self.logger.debug(
                    f"Attempting Depth Slope/Skew calculation using dedicated function (Slope Active: {calc_slope}, Skew Active: {calc_skew})..."
                )

                # Check if all required input columns are present
                missing_depth_cols = [
                    col for col in required_depth_cols if col not in signals_df.columns
                ]

                if not missing_depth_cols:
                    # Check for excessive NaNs in input columns before calculating
                    nan_counts = {
                        col: signals_df[col].isna().sum() for col in required_depth_cols
                    }
                    total_rows = len(signals_df)
                    if any(count == total_rows for count in nan_counts.values()):
                        if allow_nan_micro_depth:
                            self.logger.warning(
                                f"Depth Metrics: One or more required input columns are entirely NaN. Setting depth metrics to NaN but continuing. NaN counts: {nan_counts}"
                            )
                            signals_df["depth_slope"] = np.nan
                            signals_df["depth_skew"] = np.nan
                        else:
                            self.logger.warning(
                                f"Skipping Depth Metrics calculation: One or more required input columns are entirely NaN. NaN counts: {nan_counts}"
                            )
                            signals_df["depth_slope"] = np.nan
                            signals_df["depth_skew"] = np.nan
                    else:
                        try:
                            self.logger.info(
                                "Calculating 'depth_slope' and 'depth_skew' using calculate_depth_metrics..."
                            )
                            # Call the dedicated function from depth_metrics_calculator.py
                            depth_slope, depth_skew = calculate_depth_metrics(
                                signals_df
                            )

                            # Assign results to the DataFrame
                            signals_df["depth_slope"] = depth_slope
                            signals_df["depth_skew"] = depth_skew

                            # Log success and NaN counts of results
                            slope_nan_count = signals_df["depth_slope"].isna().sum()
                            skew_nan_count = signals_df["depth_skew"].isna().sum()
                            self.logger.info(
                                f"Depth Metrics calculated. Slope NaNs: {slope_nan_count}, Skew NaNs: {skew_nan_count}"
                            )
                            # Optional: Log stats if needed for debugging
                            # self.logger.debug(f"  Depth Slope Stats: Mean={signals_df['depth_slope'].mean():.4f}, Std={signals_df['depth_slope'].std():.4f}")
                            # self.logger.debug(f"  Depth Skew Stats: Mean={signals_df['depth_skew'].mean():.4f}, Std={signals_df['depth_skew'].std():.4f}")

                        except Exception as e:
                            if allow_nan_micro_depth:
                                self.logger.warning(
                                    f"Error calculating Depth Metrics: {e}. Setting to NaN but continuing.",
                                    exc_info=True,
                                )
                                signals_df["depth_slope"] = np.nan  # Fallback on error
                                signals_df["depth_skew"] = np.nan
                            else:
                                self.logger.error(
                                    f"Error calculating Depth Metrics: {e}",
                                    exc_info=True,
                                )
                                signals_df["depth_slope"] = np.nan  # Fallback on error
                                signals_df["depth_skew"] = np.nan
                else:
                    if allow_nan_micro_depth:
                        self.logger.warning(
                            f"Depth Metrics: Missing required input columns {missing_depth_cols}. Setting depth metrics to NaN but continuing."
                        )
                        signals_df["depth_slope"] = np.nan
                        signals_df["depth_skew"] = np.nan
                    else:
                        self.logger.warning(
                            f"Skipping Depth Metrics calculation: Missing required input columns {missing_depth_cols}. Ensure DataHandler provides them."
                        )
                        signals_df["depth_slope"] = np.nan
                        signals_df["depth_skew"] = np.nan
            else:
                self.logger.info(
                    "Depth Slope and Skew calculations skipped (parameters not configured)."
                )
                signals_df["depth_slope"] = (
                    np.nan
                )  # Ensure columns exist even if skipped
                signals_df["depth_skew"] = np.nan

            # --- post‑check: alert if depth features are unusable -----------------------
            if 'depth_slope' in signals_df.columns and signals_df['depth_slope'].isna().all():
                if allow_nan_micro_depth:
                    self.logger.warning("Depth Metrics: 'depth_slope' is entirely NaN – micro‑depth features missing or degenerate. Continuing due to allow_nan_micro_depth=True.")
                else:
                    self.logger.warning("Depth Metrics: 'depth_slope' is entirely NaN – micro‑depth features missing or degenerate.")

            if 'depth_skew' in signals_df.columns and signals_df['depth_skew'].isna().all():
                if allow_nan_micro_depth:
                    self.logger.warning("Depth Metrics: 'depth_skew' is entirely NaN – micro‑depth features missing or degenerate. Continuing due to allow_nan_micro_depth=True.")
                else:
                    self.logger.warning("Depth Metrics: 'depth_skew' is entirely NaN – micro‑depth features missing or degenerate.")
            # ---------------------------------------------------------------------------

            # --- F. Adaptive OBI Threshold Components ---
            # Conditional on a base value > 0 in config
            adaptive_obi_base_cfg = getattr(
                cfg_regime, "adaptive_obi_base", 0
            )  # Example config key
            if adaptive_obi_base_cfg is not None and adaptive_obi_base_cfg > 0:
                self.logger.debug(
                    "Calculating components for Adaptive OBI Threshold..."
                )
                # Requires 'log_ret' for volatility calculation. 'realised_vol' might be used if available.
                if "log_ret" in signals_df.columns:
                    try:
                        # Define windows (consider making these configurable)
                        # Example: Long-term = 30 days, Short-term = 1 day (adjust based on data frequency)
                        tf_multiplier = {
                            "1m": 60 * 24,
                            "5m": 12 * 24,
                            "1h": 24,
                            "4h": 6,
                        }.get(
                            self.config.timeframe, 6
                        )  # Bars per day approx (Added 4h)
                        long_term_vol_window = (
                            getattr(cfg_micro, "adaptive_vol_long_window_days", 30)
                            * tf_multiplier
                        )
                        short_term_vol_window = (
                            getattr(cfg_micro, "adaptive_vol_short_window_days", 1)
                            * tf_multiplier
                        )

                        min_p_long = max(1, long_term_vol_window // 2)
                        min_p_short = max(1, short_term_vol_window // 2)

                        # Calculate long-term baseline volatility
                        signals_df["vol_long_term"] = signals_df["log_ret"].rolling(
                            window=long_term_vol_window, min_periods=min_p_long
                        ).std() * np.sqrt(
                            tf_multiplier
                        )  # Annualize/Day-ize? Check units. Assuming std of log returns.

                        # Use pre-calculated short-term vol if available and reliable, otherwise calculate it
                        if (
                            "realised_vol" in signals_df.columns
                            and not signals_df["realised_vol"].isnull().all()
                        ):
                            self.logger.debug(
                                "Using existing 'realised_vol' as 'vol_short_term'."
                            )
                            signals_df["vol_short_term"] = signals_df[
                                "realised_vol"
                            ]  # Ensure units match vol_long_term if used together
                        else:
                            self.logger.debug(
                                f"Calculating 'vol_short_term' (window={short_term_vol_window}, min_periods={min_p_short})..."
                            )
                            signals_df["vol_short_term"] = signals_df[
                                "log_ret"
                            ].rolling(
                                window=short_term_vol_window, min_periods=min_p_short
                            ).std() * np.sqrt(
                                tf_multiplier
                            )  # Match units

                        # Handle potential NaNs from rolling calculations
                        signals_df["vol_long_term"].fillna(
                            method="ffill", inplace=True
                        )  # avoid look-ahead bias: forward-fill instead of back-fill
                        signals_df["vol_short_term"].fillna(
                            method="ffill", inplace=True
                        )

                        self.logger.debug(
                            f"Adaptive OBI components calculated. Long Vol NaNs: {signals_df['vol_long_term'].isna().sum()}, Short Vol NaNs: {signals_df['vol_short_term'].isna().sum()}"
                        )

                    except Exception as e:
                        self.logger.error(
                            f"Error calculating Adaptive OBI components: {e}",
                            exc_info=True,
                        )
                        signals_df["vol_long_term"] = np.nan
                        signals_df["vol_short_term"] = np.nan
                else:
                    self.logger.warning(
                        "Skipping Adaptive OBI components: Required column 'log_ret' not found."
                    )
                    signals_df["vol_long_term"] = np.nan
                    signals_df["vol_short_term"] = np.nan

            # --- Percentile Ranks for Adaptive Thresholds are now calculated using the rolling_percentile_rank function ---
            # This duplicate block has been removed to avoid overwriting the values with a different scale

            # --- DEBUG: State After Advanced Calcs ---
            self.logger.info(
                f"[DEBUG SetB Post] Shape AFTER advanced calcs: {signals_df.shape}"
            )
            critical_cols_check = [
                "open",
                "high",
                "low",
                "close",
                "raw_obi_zscore",
                "raw_spread_percentile",
            ]  # Add others if needed
            missing_critical = [
                c
                for c in critical_cols_check
                if c in signals_df.columns and signals_df[c].isna().all()
            ]
            if any(
                c not in signals_df.columns for c in ["open", "high", "low", "close"]
            ):
                self.logger.error(
                    "[DEBUG SetB Post] CRITICAL: OHLC columns lost after advanced calcs!"
                )
            if missing_critical:
                self.logger.warning(
                    f"[DEBUG SetB Post] Columns present but all NaN after advanced calcs: {missing_critical}"
                )
            # --- END DEBUG ---

            # --- End Advanced Calculations ---

            # --- Standard TA Indicators (using pandas-ta) ---

            # Trend Following Indicators
            if (
                cfg_strategies.use_tf_v2
                or cfg_regime.detector_type == "rule_based"
            ):  # Calculate if TF used OR rule-based detector needs them
                # ADX (Potentially used by Granular detector too)
                adx_p = cfg_indicators.adx_period
                self.logger.debug(f"Calculating ADX with period {adx_p}")
                signals_df.ta.adx(length=adx_p, append=True)
                signals_df.rename(
                    columns={
                        f"ADX_{adx_p}": "adx",
                        f"DMP_{adx_p}": "dmp",
                        f"DMN_{adx_p}": "dmn",
                    },
                    inplace=True,
                    errors="ignore",
                )

                # EWMAs & Forecast (Needed for TF strategy and Rule-Based detector)
                fast_p, slow_p = (
                    cfg_indicators.tf_ewma_fast,
                    cfg_indicators.tf_ewma_slow,
                )
                self.logger.debug(f"Calculating EWMAs: Fast={fast_p}, Slow={slow_p}")
                signals_df.ta.ema(length=fast_p, append=True)
                signals_df.rename(
                    columns={f"EMA_{fast_p}": "tf_ewma_fast"},
                    inplace=True,
                    errors="ignore",
                )
                signals_df.ta.ema(length=slow_p, append=True)
                signals_df.rename(
                    columns={f"EMA_{slow_p}": "tf_ewma_slow"},
                    inplace=True,
                    errors="ignore",
                )

                if cfg_indicators.use_tf_medium_ewma:
                    med_p = cfg_indicators.tf_ewma_medium
                    self.logger.debug(f"Calculating EWMA Medium: {med_p}")
                    signals_df.ta.ema(length=med_p, append=True)
                    signals_df.rename(
                        columns={f"EMA_{med_p}": "tf_ewma_medium"},
                        inplace=True,
                        errors="ignore",
                    )

                if (
                    "tf_ewma_fast" in signals_df.columns
                    and "tf_ewma_slow" in signals_df.columns
                ):
                    signals_df["forecast"] = (
                        signals_df["tf_ewma_fast"] - signals_df["tf_ewma_slow"]
                    )
                else:
                    self.logger.warning(
                        "Could not calculate Forecast: Missing required EWMA columns."
                    )
                    signals_df["forecast"] = np.nan

                # Assign TF ATR
                if tf_atr_period == atr_period and "atr" in signals_df.columns:
                    signals_df["atr_tf"] = signals_df["atr"]
                elif tf_atr_period > 0:
                    self.logger.warning(
                        f"Recalculating ATR for TF with period {tf_atr_period}"
                    )
                    _add_atr(signals_df, tf_atr_period, "atr_tf")

            # Mean Reversion Indicators (If strategy enabled)
            if cfg_strategies.use_mean_reversion:
                # RSI
                rsi_p = cfg_indicators.mr_rsi_period
                self.logger.debug(f"Calculating RSI with period {rsi_p}")
                signals_df.ta.rsi(length=rsi_p, append=True)
                signals_df.rename(
                    columns={f"RSI_{rsi_p}": "rsi"}, inplace=True, errors="ignore"
                )

                # Keltner Channels
                ema_p = cfg_indicators.mr_ema_period
                mult = cfg_indicators.mr_keltner_mult
                atr_len_for_kc = (
                    mr_atr_period if mr_atr_period > 0 else None
                )  # Let kc use its default if 0

                if atr_len_for_kc is not None and atr_len_for_kc <= 0:
                    self.logger.error(
                        "Cannot calculate Keltner Channels: Invalid mr_atr_period."
                    )
                else:
                    self.logger.debug(
                        f"Calculating Keltner Channels: EMA={ema_p}, Mult={mult}, ATR Period={atr_len_for_kc if atr_len_for_kc else 'KC default'}"
                    )
                    signals_df.ta.kc(
                        length=ema_p,
                        scalar=mult,
                        atr_length=atr_len_for_kc,
                        mamode="ema",
                        append=True,
                        col_names=("kc_lower", "kc_basis", "kc_upper"),
                    )
                    if not all(
                        k in signals_df.columns
                        for k in ["kc_lower", "kc_basis", "kc_upper"]
                    ):
                        self.logger.error(
                            "Keltner Channel columns not found after calculation!"
                        )

                # Assign MR ATR
                if mr_atr_period == atr_period and "atr" in signals_df.columns:
                    signals_df["atr_mr"] = signals_df["atr"]
                elif mr_atr_period > 0:
                    # Check if KC created the ATR column we need
                    kc_atr_col = f"ATR_{mr_atr_period}"      # pandas-ta's default column name
                    if kc_atr_col in signals_df.columns:
                        signals_df.rename(
                            columns={kc_atr_col: "atr_mr"},
                            inplace=True,
                            errors="ignore",
                        )
                    else:
                        self.logger.warning(
                            f"Recalculating ATR for MR with period {mr_atr_period}"
                        )
                        _add_atr(signals_df, mr_atr_period, "atr_mr")

            # Mean Variance Indicators (If strategy enabled)
            if cfg_strategies.use_mean_variance:
                # Rolling Log Return Std Dev
                vol_period = cfg_indicators.mv_volatility_period
                if vol_period > 1:
                    self.logger.debug(
                        f"Calculating Log Return Std Dev with period {vol_period}"
                    )
                    log_ret = np.log(
                        signals_df["close"].replace(0, np.nan)
                        / signals_df["close"].shift(1).replace(0, np.nan)
                    )
                    log_ret.replace([np.inf, -np.inf], np.nan, inplace=True)
                    signals_df["log_return_std_rolling"] = log_ret.rolling(
                        window=vol_period, min_periods=max(1, vol_period // 2)
                    ).std()
                else:
                    signals_df["log_return_std_rolling"] = np.nan

                # Short-Term EMA for Edge Proxy
                edge_ema_period = cfg_indicators.mv_edge_ema_period
                if edge_ema_period > 1:
                    self.logger.debug(
                        f"Calculating MV Edge EMA with period {edge_ema_period}"
                    )
                    signals_df.ta.ema(length=edge_ema_period, append=True)
                    signals_df.rename(
                        columns={f"EMA_{edge_ema_period}": "mv_edge_ema"},
                        inplace=True,
                        errors="ignore",
                    )
                else:
                    signals_df["mv_edge_ema"] = np.nan

                # MV Edge Proxy
                if (
                    "mv_edge_ema" in signals_df.columns
                    and "close" in signals_df.columns
                ):
                    self.logger.debug("Calculating MV Edge Proxy")
                    signals_df["mv_edge_proxy"] = (
                        signals_df["mv_edge_ema"] - signals_df["close"]
                    ) / signals_df["close"].replace(0, np.nan)
                    signals_df["mv_edge_proxy"].replace(
                        [np.inf, -np.inf], np.nan, inplace=True
                    )
                else:
                    signals_df["mv_edge_proxy"] = np.nan

                # Assign MV ATR
                if mv_atr_period == atr_period and "atr" in signals_df.columns:
                    signals_df["atr_mv"] = signals_df["atr"]
                elif mv_atr_period > 0:
                    self.logger.warning(
                        f"Recalculating ATR for MV with period {mv_atr_period}"
                    )
                    _add_atr(signals_df, mv_atr_period, "atr_mv")

            # --- Indicators for Granular Microstructure Detector ---
            if cfg_regime.detector_type in ["granular_microstructure", "continuous_gms"]:
                self.logger.debug(
                    "Calculating indicators for Granular Microstructure Detector..."
                )

                # Rate of Change (ROC)
                roc_p = cfg_indicators.gms_roc_period
                if roc_p > 0:
                    self.logger.debug(f"Calculating ROC with period {roc_p}")
                    try:
                        # Try using pandas-ta to calculate ROC
                        signals_df.ta.roc(length=roc_p, append=True)
                        signals_df.rename(
                            columns={f"ROC_{roc_p}": "roc"}, inplace=True, errors="ignore"
                        )

                        # Check if ROC column exists and has data
                        if "roc" not in signals_df.columns or signals_df["roc"].isna().all():
                            # Fallback: Calculate ROC manually using pct_change
                            self.logger.warning(f"ROC column missing or all NaN. Calculating manually using pct_change...")
                            signals_df["roc"] = signals_df["close"].pct_change(periods=roc_p) * 100
                            self.logger.info(f"Manually calculated ROC. NaN count: {signals_df['roc'].isna().sum()}")
                    except Exception as e:
                        # Fallback: Calculate ROC manually using pct_change
                        self.logger.warning(f"Error calculating ROC with pandas-ta: {e}. Calculating manually...")
                        signals_df["roc"] = signals_df["close"].pct_change(periods=roc_p) * 100
                        self.logger.info(f"Manually calculated ROC. NaN count: {signals_df['roc'].isna().sum()}")

                    self.logger.debug(
                        f"ROC calculated. NaN count: {signals_df['roc'].isna().sum()}"
                    )
                else:
                    signals_df["roc"] = np.nan

                # Moving Average Slope
                ma_slope_p = cfg_indicators.gms_ma_slope_period
                if ma_slope_p > 1:
                    self.logger.debug(f"Calculating MA Slope with period {ma_slope_p}")
                    # Calculate MA first (e.g., SMA)
                    ma_col = f"SMA_{ma_slope_p}"
                    try:
                        signals_df.ta.sma(length=ma_slope_p, append=True)

                        # Check if the MA column was created successfully
                        if ma_col in signals_df.columns:
                            # Calculate slope
                            signals_df["ma_slope"] = signals_df[ma_col] - signals_df[ma_col].shift(1)
                            # Optional: Normalize slope? e.g., divide by MA value or price? Keep simple for now.
                            # signals_df.drop(columns=[ma_col], inplace=True) # Clean up intermediate MA column
                            self.logger.debug(
                                f"MA Slope calculated. NaN count: {signals_df['ma_slope'].isna().sum()}"
                            )
                        else:
                            # Fallback: Calculate MA slope using close price directly
                            self.logger.warning(f"MA column '{ma_col}' not found. Using fallback calculation for ma_slope.")
                            signals_df["ma_slope"] = signals_df["close"].diff(ma_slope_p)
                            self.logger.debug(
                                f"MA Slope (fallback) calculated. NaN count: {signals_df['ma_slope'].isna().sum()}"
                            )
                    except Exception as e:
                        # Fallback: Calculate MA slope using close price directly
                        self.logger.warning(f"Error calculating MA with pandas-ta: {e}. Using fallback calculation for ma_slope.")
                        signals_df["ma_slope"] = signals_df["close"].diff(ma_slope_p)
                        self.logger.debug(
                            f"MA Slope (fallback) calculated. NaN count: {signals_df['ma_slope'].isna().sum()}"
                        )
                else:
                    signals_df["ma_slope"] = np.nan

                # Fill NaN values in ma_slope with 0 for testing purposes
                # This is a temporary fix to allow backtesting with limited data
                if signals_df["ma_slope"].isna().any():
                    self.logger.warning(f"Filling {signals_df['ma_slope'].isna().sum()} NaN values in ma_slope with 0 for testing")
                    signals_df["ma_slope"] = signals_df["ma_slope"].fillna(0)

                # ATR Percent - preserve atr_percent_sec from feature files if it exists
                if preserve_atr_percent_sec:
                    # Use existing atr_percent_sec from feature files (already set up above)
                    self.logger.info("Using preserved 'atr_percent_sec' from feature files for GMS detector")
                    # atr_percent was already set up in the preservation logic above
                elif 'atr_percent_sec' in signals_df.columns and not preserve_atr_percent_sec:
                    # Use existing atr_percent_sec but it wasn't high quality enough to preserve
                    self.logger.info("Using existing 'atr_percent_sec' from feature files for GMS detector (lower quality)")
                    # Also create atr_percent for backward compatibility (same units as atr_percent_sec)
                    signals_df["atr_percent"] = signals_df["atr_percent_sec"]  # Keep decimal units
                else:
                    # Calculate ATR percent from scratch
                    atr_perc_p = cfg_indicators.gms_atr_percent_period
                    atr_col_for_perc = "atr"  # Use the main ATR calculated earlier
                    # Check if main ATR period matches the required period
                    if atr_perc_p != atr_period:
                        # Recalculate ATR specifically for ATR% if periods differ
                        self.logger.warning(
                            f"Recalculating ATR for ATR% with period {atr_perc_p}"
                        )
                        atr_col_for_perc = f"atr_{atr_perc_p}"
                        _add_atr(signals_df, atr_perc_p, atr_col_for_perc)

                    if (
                        atr_col_for_perc in signals_df.columns
                        and "close" in signals_df.columns
                    ):
                        self.logger.debug(
                            f"Calculating ATR Percent using '{atr_col_for_perc}'"
                        )
                        # Check for duplicate indices before division
                        if not signals_df.index.is_unique:
                            self.logger.warning("Found duplicate indices in signals_df. Fixing by keeping the last occurrence.")
                            signals_df = signals_df[~signals_df.index.duplicated(keep='last')]

                        # Calculate ATR percent (as decimal, not percentage)
                        signals_df["atr_percent"] = (
                            signals_df[atr_col_for_perc]
                            / signals_df["close"].replace(0, np.nan)
                        )
                        signals_df["atr_percent"] = signals_df["atr_percent"].replace(
                            [np.inf, -np.inf], np.nan
                        )
                        self.logger.debug(
                            f"ATR Percent calculated. NaN count: {signals_df['atr_percent'].isna().sum()}"
                        )
                    else:
                        self.logger.warning(
                            f"Skipping ATR Percent calculation: Missing '{atr_col_for_perc}' or 'close' column."
                        )
                        signals_df["atr_percent"] = np.nan
                        signals_df["atr_percent_pctile"] = np.nan

                # Calculate percentile for volatility (ATR%) - Moved here after atr_percent is calculated
                rolling_window = (
                    cfg_regime.gms_tight_spread_percentile_window
                    if hasattr(cfg_regime, "gms_tight_spread_percentile_window")
                    else 24
                )
                if rolling_window > 0 and "atr_percent" in signals_df.columns:
                    signals_df['atr_percent_pctile'] = (
                        signals_df['atr_percent']
                        .rolling(window=rolling_window, min_periods=max(1, rolling_window // 2))
                        .apply(lambda arr: rolling_percentile_rank(pd.Series(arr), rolling_window, 1).iloc[-1], raw=False)
                    )
                    self.logger.debug(
                        f"ATR Percent Percentile calculated. NaN count: {signals_df['atr_percent_pctile'].isna().sum()}"
                    )

            # --- Calculate SMA for GMS Slope Visualization ---
            sma_col_name = "sma_gms_slope"  # Define the column name outside the check
            if "close" in signals_df.columns and hasattr(
                self.config.indicators, "gms_ma_slope_period"
            ):
                slope_period = self.config.indicators.gms_ma_slope_period

                if slope_period > 0:
                    if (
                        sma_col_name not in signals_df.columns
                    ):  # Avoid recalculating if already present
                        try:
                            self.logger.info(
                                f"Calculating {sma_col_name} with period {slope_period}..."
                            )
                            # Use pandas_ta to calculate SMA
                            signals_df[sma_col_name] = ta.sma(
                                signals_df["close"], length=slope_period
                            )
                            nan_count = signals_df[sma_col_name].isna().sum()
                            self.logger.info(
                                f"Successfully calculated and added '{sma_col_name}' column. NaN count: {nan_count}"
                            )
                        except Exception as e:
                            self.logger.error(
                                f"Failed to calculate {sma_col_name}: {e}",
                                exc_info=True,
                            )
                            signals_df[sma_col_name] = (
                                np.nan
                            )  # Add NaN column on failure
                    else:
                        self.logger.debug(
                            f"Column '{sma_col_name}' already exists in signals_df."
                        )
                else:
                    self.logger.warning(
                        f"Skipping {sma_col_name} calculation: gms_ma_slope_period is not positive ({slope_period})."
                    )
                    signals_df[sma_col_name] = (
                        np.nan
                    )  # Add NaN column if period is invalid
            else:
                self.logger.warning(
                    f"Could not calculate {sma_col_name}: Missing 'close' column or 'gms_ma_slope_period' config."
                )
                signals_df[sma_col_name] = (
                    np.nan
                )  # Add NaN column if config/data missing
            # --- End SMA Calculation ---

            # --- Calculate EMAs for TF-v3 Strategy ---
            if "close" in signals_df.columns and hasattr(self.config, 'tf_v3'):
                try:
                    # Get EMA periods from tf_v3 config
                    ema_fast_period = self.config.tf_v3.ema_fast
                    ema_slow_period = self.config.tf_v3.ema_slow

                    # Add EMAs using configured periods
                    self.logger.info(f"Calculating EMAs for TF-v3 strategy using periods: fast={ema_fast_period}, slow={ema_slow_period}...")
                    signals_df[f"ema_{ema_fast_period}"] = ta.ema(signals_df["close"], length=ema_fast_period)
                    signals_df[f"ema_{ema_slow_period}"] = ta.ema(signals_df["close"], length=ema_slow_period)
                    self.logger.info(f"Added EMAs for TF-v3: ema_{ema_fast_period} (NaN: {signals_df[f'ema_{ema_fast_period}'].isna().sum()}) and ema_{ema_slow_period} (NaN: {signals_df[f'ema_{ema_slow_period}'].isna().sum()})")
                except Exception as e:
                    self.logger.error(f"Failed to calculate EMAs for TF-v3: {e}", exc_info=True)
                    # Add default EMAs as fallback
                    signals_df["ema_20"] = np.nan
                    signals_df["ema_50"] = np.nan
            # --- End EMA Calculation ---

        except Exception as e:
            self.logger.error(f"Error calculating signals: {e}", exc_info=True)
            return pd.DataFrame()  # Return empty on major error

        # Add timestamp column from index for GMS detector compatibility
        if 'timestamp' not in signals_df.columns:
            signals_df['timestamp'] = signals_df.index
            self.logger.info("Added 'timestamp' column from index for GMS detector compatibility")

        # Add unrealised_pnl column (placeholder for backtesting)
        if 'unrealised_pnl' not in signals_df.columns:
            signals_df['unrealised_pnl'] = 0.0  # Default to 0 for backtesting
            self.logger.info("Added 'unrealised_pnl' column (placeholder for backtesting)")

        # Check if DataFrame is empty after all calculations
        if signals_df.empty:
            missing_cols = [col for col in required_base_cols if col not in signals_df.columns]
            self.logger.error(f"CRITICAL: DataFrame is empty after signal calculation. Missing essential columns: {missing_cols}")
            raise ValueError(f"Empty DataFrame after signal calculation. Missing essential columns: {missing_cols}")

        self.logger.info(
            f"Signal calculation complete. DataFrame shape: {signals_df.shape}"
        )
        final_cols = signals_df.columns.tolist()
        self.logger.debug(
            f"Final Signal DataFrame columns ({len(final_cols)}): {final_cols}"
        )
        # Log NaN counts for key final signals
        key_signals_for_nan_check = [
            "atr",
            "adx",
            "forecast",
            "rsi",
            "kc_basis",
            "obi_smoothed",
            "spread_mean",
            "spread_std",
            "roc",
            "ma_slope",
            "atr_percent",
        ]
        for sig in key_signals_for_nan_check:
            if sig in signals_df.columns:
                nan_count = signals_df[sig].isna().sum()
                if nan_count > 0:
                    self.logger.debug(
                        f"NaN count for '{sig}': {nan_count} / {len(signals_df)}"
                    )

        # --- DEBUG: Check columns at SignalEngine END ---
        self.logger.info(
            f"[DEBUG SE End] Output DataFrame shape: {signals_df.shape}. Columns: {signals_df.columns.tolist()}"
        )
        self.logger.info(
            f"Memory Usage: {signals_df.memory_usage(deep=True).sum() / (1024*1024):.2f} MB"
        )
        if "fear_greed_idx" not in signals_df.columns:
            self.logger.error(
                "[DEBUG SE End] CRITICAL: 'fear_greed_idx' is MISSING from the output DataFrame!"
            )
        else:
            self.logger.info(
                "[DEBUG SE End] 'fear_greed_idx' is PRESENT in the output DataFrame."
            )

        # Validation for spread percentiles
        _debug_cols = ['spread_mean_primary_pctile', 'spread_std_primary_pctile']
        for _c in _debug_cols:
            if _c in signals_df.columns:
                assert (signals_df[_c].between(-1e-9, 1+1e-9) | signals_df[_c].isna()).all(), f"{_c} out of 0–1 bounds"

        # Sanity assertions for the fixes
        assert 'atr_mr' not in signals_df or signals_df['atr_mr'].notna().any() or mr_atr_period == 0, "atr_mr still all NaN!"
        if 'spread_mean_pctile' in signals_df.columns:
            assert (signals_df['spread_mean_pctile'].between(0, 1) | signals_df['spread_mean_pctile'].isna()).all()

        return signals_df

    def calculate_required_lookback(self) -> int:
        """Calculates the maximum lookback period needed by any enabled indicator or calculation."""
        cfg_ind = self.config.indicators
        cfg_strat = self.config.strategies
        cfg_micro = self.config.microstructure
        cfg_regime = self.config.regime
        lookbacks = [1]

        # Base ATR lookback
        tf_atr_period = cfg_ind.tf_atr_period if cfg_strat.use_tf_v2 else 0
        mr_atr_period = cfg_ind.mr_atr_period if cfg_strat.use_mean_reversion else 0
        mv_atr_period = cfg_ind.mv_atr_period if cfg_strat.use_mean_variance else 0
        gms_atr_period = (
            cfg_ind.gms_atr_percent_period
            if cfg_regime.detector_type in ["granular_microstructure", "continuous_gms"]
            else 0
        )
        base_atr_lookback = max(
            tf_atr_period, mr_atr_period, mv_atr_period, gms_atr_period, 1
        )
        lookbacks.append(base_atr_lookback)

        # Microstructure lookbacks
        lookbacks.append(cfg_micro.obi_smoothing_window)
        lookbacks.append(cfg_micro.spread_rolling_window)

        # Strategy Indicator lookbacks
        if cfg_strat.use_tf_v2 or cfg_regime.detector_type == "rule_based":
            lookbacks.extend(
                [
                    cfg_ind.tf_ewma_fast,
                    cfg_ind.tf_ewma_medium if cfg_ind.use_tf_medium_ewma else 0,
                    cfg_ind.tf_ewma_slow,
                    cfg_ind.adx_period,  # ADX needs period + internal smoothing lookback
                    cfg_ind.tf_atr_period,  # Already included in base_atr_lookback
                ]
            )
        if cfg_strat.use_mean_reversion:
            lookbacks.extend(
                [
                    cfg_ind.mr_ema_period,  # Keltner EMA
                    cfg_ind.mr_rsi_period,
                    cfg_ind.mr_atr_period,  # Keltner ATR (already included)
                ]
            )
        if cfg_strat.use_mean_variance:
            lookbacks.extend(
                [
                    cfg_ind.mv_volatility_period,  # Log return std dev
                    cfg_ind.mv_atr_period,  # MV ATR (already included)
                    cfg_ind.mv_edge_ema_period,
                ]
            )

        # Granular Microstructure Detector lookbacks (including continuous_gms)
        if cfg_regime.detector_type in ["granular_microstructure", "continuous_gms"]:
            lookbacks.extend(
                [
                    cfg_ind.gms_roc_period,
                    cfg_ind.gms_ma_slope_period,  # MA period for slope calc
                    cfg_ind.gms_atr_percent_period,  # ATR period (already included)
                ]
            )

        # pandas-ta specific lookbacks (some indicators need more than just period)
        # ADX often needs ~ 2*period + buffer
        adx_lookback = cfg_ind.adx_period * 2 + 10 if cfg_ind.adx_period > 0 else 0
        lookbacks.append(adx_lookback)

        # General buffer for stability
        buffer_factor = 1.5
        min_buffer = 50  # Increased buffer slightly

        valid_lookbacks = [
            lb for lb in lookbacks if isinstance(lb, (int, float)) and lb > 0
        ]
        max_raw_lookback = max(valid_lookbacks) if valid_lookbacks else 1

        # Apply buffer to the maximum raw lookback found
        buffered_max_lookback = int(max_raw_lookback * buffer_factor) + min_buffer

        self.logger.info(
            f"Calculated required indicator lookback: Max Raw={max_raw_lookback}, Buffered={buffered_max_lookback} periods."
        )
        return buffered_max_lookback
