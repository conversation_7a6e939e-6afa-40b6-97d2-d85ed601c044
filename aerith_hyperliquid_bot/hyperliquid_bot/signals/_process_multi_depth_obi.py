"""
Temporary file containing the _process_multi_depth_obi method implementation.
This will be integrated into the SignalEngine class.
"""

import numpy as np
import pandas as pd
from hyperliquid_bot.features.microstructure import calc_obi
from hyperliquid_bot.signals.obi_zscore_calculator import calculate_obi_zscore

def _process_multi_depth_obi(self, signals_df: pd.DataFrame) -> pd.DataFrame:
    """
    Process Order Book Imbalance (OBI) for multiple depth variants.
    Calculates raw, smoothed, and z-score normalized OBI for each depth variant.
    
    Args:
        signals_df: DataFrame containing raw orderbook data
        
    Returns:
        DataFrame with added OBI signals for each depth variant
    """
    cfg_micro = self.config.microstructure
    
    # Get configuration parameters
    depth_variants = cfg_micro.obi_depth_variants
    smoothing_window = cfg_micro.obi_smoothing_window
    smoothing_type = cfg_micro.obi_smoothing_type
    zscore_window = cfg_micro.obi_zscore_window
    weight_scheme = cfg_micro.obi_weight_scheme
    
    # Validate depth variants configuration
    if not isinstance(depth_variants, list) or not depth_variants:
        self.logger.warning("Invalid obi_depth_variants config. Using default [5]")
        depth_variants = [5]
        
    # Check if we have the required orderbook data
    if 'bid_qty' not in signals_df.columns or 'ask_qty' not in signals_df.columns:
        self.logger.warning("Missing orderbook data columns (bid_qty, ask_qty). Cannot calculate multi-depth OBI.")
        return signals_df
        
    # Process each depth variant
    for depth in depth_variants:
        # Create column names
        raw_col_name = f"obi_raw_{depth}"
        smoothed_col_name = f"obi_smoothed_{depth}"
        zscore_col_name = f"obi_zscore_{depth}"
        
        # Calculate raw OBI for this depth variant
        try:
            # Determine weights based on the configured scheme
            weights = None
            if weight_scheme == 'linear':
                # Linear decay weights (e.g., [1.0, 0.8, 0.6, 0.4, 0.2] for depth=5)
                weights = [1.0 - (i / depth) for i in range(depth)]
            elif weight_scheme == 'exponential':
                # Exponential decay weights (e.g., [1.0, 0.5, 0.25, 0.125, 0.0625] for depth=5)
                weights = [1.0 / (2**i) for i in range(depth)]
            
            # Extract orderbook data for each row
            def process_row(row):
                # Create orderbook dictionary with numpy arrays
                order_book = {
                    'bid_qty': np.array(row['bid_qty'][:depth] if isinstance(row['bid_qty'], list) else []),
                    'ask_qty': np.array(row['ask_qty'][:depth] if isinstance(row['ask_qty'], list) else [])
                }
                
                # Calculate OBI using the calc_obi function
                try:
                    return calc_obi(order_book, levels=depth, weights=weights)
                except Exception as e:
                    self.logger.debug(f"Error calculating OBI for depth {depth}: {e}")
                    return np.nan
            
            # Apply the function to each row
            signals_df[raw_col_name] = signals_df.apply(process_row, axis=1)
            self.logger.debug(f"Calculated raw OBI for depth {depth}")
            
            # Apply smoothing (SMA or EMA)
            if smoothing_type.lower() == 'ema':
                signals_df[smoothed_col_name] = signals_df[raw_col_name].ewm(
                    span=smoothing_window, min_periods=max(2, smoothing_window//2)
                ).mean()
            else:  # Default to SMA
                signals_df[smoothed_col_name] = signals_df[raw_col_name].rolling(
                    window=smoothing_window, min_periods=max(2, smoothing_window//2)
                ).mean()
            
            # Calculate z-score normalized OBI
            if zscore_window > 0:
                min_periods = max(3, zscore_window // 3)  # Require at least 1/3 of the window
                signals_df[zscore_col_name] = calculate_obi_zscore(
                    signals_df, 
                    raw_obi_col=smoothed_col_name,
                    window=zscore_window,
                    min_periods=min_periods
                )
                self.logger.debug(f"Calculated OBI z-score for depth {depth} with window {zscore_window}")
        except Exception as e:
            self.logger.error(f"Error processing OBI for depth {depth}: {e}", exc_info=True)
            # Create empty columns to maintain DataFrame structure
            signals_df[raw_col_name] = np.nan
            signals_df[smoothed_col_name] = np.nan
            signals_df[zscore_col_name] = np.nan
    
    return signals_df
