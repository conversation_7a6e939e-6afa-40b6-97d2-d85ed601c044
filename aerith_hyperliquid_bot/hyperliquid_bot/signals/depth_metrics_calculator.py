import pandas as pd
import numpy as np
import logging
from typing import Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_depth_metrics(data: pd.DataFrame) -> <PERSON>ple[pd.Series, pd.Series]:
    """
    Calculates depth_slope and depth_skew metrics from order book data.
    
    Args:
        data (pd.DataFrame): DataFrame containing order book data. Must have bid_slope, ask_slope,
                            and book_asymmetry columns, or the necessary price/size columns to calculate them.
    
    Returns:
        Tuple[pd.Series, pd.Series]: A tuple containing two Series:
            - depth_slope: A measure of liquidity depth/thinness
            - depth_skew: A measure of order book asymmetry
    """
    logger.info(f"Calculating depth metrics from data with shape {data.shape}")
    
    # Check if we already have the necessary columns
    has_slopes = 'bid_slope' in data.columns and 'ask_slope' in data.columns
    has_asymmetry = 'book_asymmetry' in data.columns
    
    # Initialize result series with NaN values
    depth_slope = pd.Series(np.nan, index=data.index, name='depth_slope')
    depth_skew = pd.Series(np.nan, index=data.index, name='depth_skew')
    
    if has_slopes:
        logger.info("Using existing bid_slope and ask_slope columns for depth_slope calculation")
        # Calculate depth_slope as the average of bid and ask slopes
        # Higher values indicate thinner liquidity (steeper price curves)
        depth_slope = (data['bid_slope'] + data['ask_slope']) / 2
        
        # Log statistics for debugging
        logger.info(f"Depth slope stats - Mean: {depth_slope.mean():.6f}, Median: {depth_slope.median():.6f}, "
                   f"Min: {depth_slope.min():.6f}, Max: {depth_slope.max():.6f}")
    else:
        logger.warning("No bid_slope and ask_slope columns found. Calculating from price/size data is not implemented yet.")
        # TODO: Implement calculation from raw price/size data if needed
    
    if has_asymmetry:
        logger.info("Using existing book_asymmetry column for depth_skew calculation")
        # Use book_asymmetry directly as depth_skew
        # This measures the imbalance between bid and ask sides of the book
        depth_skew = data['book_asymmetry']
        
        # Log statistics for debugging
        logger.info(f"Depth skew stats - Mean: {depth_skew.mean():.6f}, Median: {depth_skew.median():.6f}, "
                   f"Min: {depth_skew.min():.6f}, Max: {depth_skew.max():.6f}")
    else:
        logger.warning("No book_asymmetry column found. Calculating from price/size data is not implemented yet.")
        # TODO: Implement calculation from raw price/size data if needed
    
    # Return both metrics
    return depth_slope, depth_skew

def calculate_depth_slope(data: pd.DataFrame) -> pd.Series:
    """
    Calculates only the depth_slope metric from order book data.
    
    Args:
        data (pd.DataFrame): DataFrame containing order book data.
    
    Returns:
        pd.Series: Series containing depth_slope values
    """
    depth_slope, _ = calculate_depth_metrics(data)
    return depth_slope

def calculate_depth_skew(data: pd.DataFrame) -> pd.Series:
    """
    Calculates only the depth_skew metric from order book data.
    
    Args:
        data (pd.DataFrame): DataFrame containing order book data.
    
    Returns:
        pd.Series: Series containing depth_skew values
    """
    _, depth_skew = calculate_depth_metrics(data)
    return depth_skew

def resample_depth_metrics(raw_data: pd.DataFrame, timeframe: str = '1h') -> pd.DataFrame:
    """
    Calculates depth metrics from raw order book data and resamples to the specified timeframe.
    
    Args:
        raw_data (pd.DataFrame): Raw order book data with timestamp index
        timeframe (str): Timeframe for resampling (e.g., '1h', '15min')
    
    Returns:
        pd.DataFrame: Resampled DataFrame with depth_slope and depth_skew columns
    """
    logger.info(f"Resampling depth metrics to {timeframe} timeframe")
    
    # Ensure data is sorted by timestamp
    if not isinstance(raw_data.index, pd.DatetimeIndex):
        if 'timestamp' in raw_data.columns:
            raw_data = raw_data.set_index('timestamp').sort_index()
        else:
            raise ValueError("Data must have a timestamp column or datetime index")
    
    # Calculate depth metrics on raw data
    depth_slope, depth_skew = calculate_depth_metrics(raw_data)
    
    # Create a DataFrame with the metrics
    metrics_df = pd.DataFrame({
        'depth_slope': depth_slope,
        'depth_skew': depth_skew
    }, index=raw_data.index)
    
    # Resample to the specified timeframe
    # For depth_slope (higher = thinner liquidity), we use mean
    # For depth_skew (measure of asymmetry), we use mean
    resampled = metrics_df.resample(timeframe).agg({
        'depth_slope': 'mean',
        'depth_skew': 'mean'
    })
    
    logger.info(f"Resampled data shape: {resampled.shape}")
    return resampled

if __name__ == '__main__':
    # Example Usage (for testing purposes)
    import sys
    
    if len(sys.argv) < 2:
        logger.error("Usage: python depth_metrics_calculator.py <path_to_raw_parquet_file> [timeframe]")
        sys.exit(1)
    
    file_path = sys.argv[1]
    timeframe = sys.argv[2] if len(sys.argv) > 2 else '1h'
    
    try:
        logger.info(f"Reading raw data from {file_path}")
        raw_data = pd.read_parquet(file_path)
        
        # Calculate and display depth metrics
        depth_slope, depth_skew = calculate_depth_metrics(raw_data)
        
        print("\nRaw Data Sample:")
        print(raw_data.head())
        
        print("\nDepth Slope Sample:")
        print(depth_slope.head())
        print(f"Depth Slope Stats: Mean={depth_slope.mean():.6f}, Std={depth_slope.std():.6f}")
        
        print("\nDepth Skew Sample:")
        print(depth_skew.head())
        print(f"Depth Skew Stats: Mean={depth_skew.mean():.6f}, Std={depth_skew.std():.6f}")
        
        # Resample and display
        resampled = resample_depth_metrics(raw_data, timeframe)
        print(f"\nResampled to {timeframe}:")
        print(resampled)
        
    except Exception as e:
        logger.error(f"Error processing file: {e}")
        sys.exit(1)
