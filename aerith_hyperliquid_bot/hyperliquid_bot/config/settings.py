# hyperliquid_bot/config/settings.py

from pydantic import BaseModel, Field, DirectoryPath, field_validator, model_validator, validator
from typing import List, Literal, Optional, ClassVar, Union, Dict
from datetime import datetime
import yaml
from pathlib import Path
import logging # Import logging
import re # Import re for regex

# Import scheduler settings
from hyperliquid_bot.config.scheduler_settings import SchedulerSettings

logger = logging.getLogger(__name__) # Logger for this module

# --- Pydantic Models for Nested Structures ---

class DataPaths(BaseModel):
    l2_data_root: DirectoryPath = Field(..., description="Root directory containing raw L2 data parquet files.")
    raw_l2_dir: DirectoryPath = Field(..., description="Directory containing raw L2 data files (JSON, Arrow, etc.)")
    feature_1s_dir: DirectoryPath = Field(..., description="Directory for 1-second feature parquet files")
    ohlcv_base_path: DirectoryPath = Field(..., description="Base path for L2 resampled OHLC data (contains 1h/, 4h/ subdirs)")
    log_dir: Path = Field(..., description="Directory to store log files.")
    require_ohlcv_volume: bool = Field(default=False, description="If true, requires OHLCV files to contain a 'volume' column.")

    @validator('l2_data_root', 'ohlcv_base_path', 'raw_l2_dir', 'feature_1s_dir')
    def dir_must_exist(cls, v):
        if not v.exists():
            raise ValueError(f"Directory does not exist: {v}")
        return v

class CacheSettings(BaseModel):
    l2_cache_max_size: int = Field(..., gt=0)

class BacktestSettings(BaseModel):
    period_preset: str = Field(default='full', description="Backtest period: 'full', 'custom', a 4-digit year (e.g., '2024'), or quarterly (e.g., '2024Q1')")
    custom_start_date: Optional[datetime] = Field(default=None, description="Custom start date (inclusive) if period_preset is 'custom'")
    custom_end_date: Optional[datetime] = Field(default=None, description="Custom end date (inclusive) if period_preset is 'custom'")
    use_execution_refinement: bool = Field(default=False, description="Enable execution refinement using 1-minute data")

    @model_validator(mode='before')
    def validate_period_preset(cls, values):
        preset = values.get('period_preset')
        # Updated regex to include YYYYQX format
        if not re.match(r"^(full|custom|\d{4}|\d{4}Q[1-4])$", preset):
            raise ValueError(f"Invalid period_preset: '{preset}'. Must be 'full', 'custom', a 4-digit year (e.g., '2024'), or quarterly (e.g., '2024Q1').")

        if preset == 'custom':
            if not values.get('custom_start_date') or not values.get('custom_end_date'):
                raise ValueError("Custom start/end date required when period_preset is 'custom'")
        return values

    @field_validator('custom_start_date', 'custom_end_date', mode='before')
    @classmethod
    def parse_dates(cls, v, info):
        if info.data.get('period_preset') == 'custom':
            if v is None:
                raise ValueError("Custom start/end date required when period_preset is 'custom'")
            try:
                if isinstance(v, datetime): return v
                if isinstance(v, str):
                    try: return datetime.strptime(v, '%Y-%m-%d')
                    except ValueError: pass
                    try: return datetime.fromisoformat(v.split('T')[0])
                    except ValueError: pass
                    raise ValueError(f"Invalid date format for custom date: {v}")
            except Exception as e:
                 raise ValueError(f"Error parsing custom date '{v}': {e}")
        return v

class SimulationSettings(BaseModel):
    latency_seconds: float = Field(default=0.5, ge=0)
    max_impact_levels: int = Field(default=5, gt=0)
    force_taker_execution: bool = Field(default=False, description="If True, all fills are simulated as taker orders crossing the spread.")
    attempt_maker_orders: bool = Field(
        default=False,
        description="If True and force_taker_execution is False, attempt probabilistic maker simulation."
    )
    maker_placement_type: Literal['mid', 'best_passive'] = Field(
        default='best_passive',
        description="Where to place the simulated maker order relative to BBO ('mid' or 'best_passive')."
    )
    maker_time_buckets_seconds: List[int] = Field(
        default=[5, 30, 120],
        description="Cumulative time buckets (seconds) for checking maker fills."
    )
    maker_fill_probabilities: List[float] = Field(
        default=[0.15, 0.25, 0.20],
        description="Marginal probability of maker fill within each time bucket IF price touches."
    )

    @validator('maker_fill_probabilities')
    def check_probabilities_range(cls, v):
        if not all(0.0 <= p <= 1.0 for p in v):
            raise ValueError('Maker fill probabilities must be between 0.0 and 1.0')
        return v

    @validator('maker_fill_probabilities')
    def check_list_lengths_match(cls, v, values, **kwargs):
        if 'maker_time_buckets_seconds' in values and len(v) != len(values['maker_time_buckets_seconds']):
            raise ValueError('Length of maker_fill_probabilities must match maker_time_buckets_seconds')
        return v

class OBIScalperStrategySettings(BaseModel):
    """Settings for the OBIScalperStrategy."""
    enabled: bool = Field(default=True, description="Enable OBI Scalper strategy")
    defaults: 'OBIScalperDefaults' = Field(default_factory=lambda: OBIScalperDefaults())

class OBIScalperDefaults(BaseModel):
    """Default settings for the OBIScalperStrategy."""
    vol_veto_threshold: float = Field(default=0.0006, description="Volatility veto threshold (6 bps)")
    spread_veto_threshold: float = Field(default=0.00004, description="Spread veto threshold")
    obi_l1_3_trigger: float = Field(default=0.80, description="OBI L1_3 trigger threshold")
    tp_ticks: int = Field(default=7, description="Take profit in ticks")
    sl_ticks: int = Field(default=5, description="Stop loss in ticks")
    timeout_seconds: int = Field(default=30, description="Timeout in seconds")
    allowed_gms_states: List[str] = Field(default=["CHOP", "TIGHT_SPREAD"], description="Allowed GMS states")
    zero_sign_eps: float = Field(default=0.001, description="Epsilon threshold for zero sign determination")
    tick_size: float = Field(default=0.01, gt=0, description="Tick size for price increments")

class StrategySettings(BaseModel):
    use_tf_v2: bool = True
    use_mean_reversion: bool = False
    use_mean_variance: bool = False
    use_obi_scalper: bool = Field(default=False, description="Enable OBI Scalper strategy")
    use_tf_v3: bool = Field(default=True, description="Enable TF-v3 strategy")
    # TF-v2 (Trend Following) Configuration
    tf_warmup_bars: Union[int, Literal["auto"]] = Field(default="auto", description="Warm-up period: 'auto' or integer (auto = max(ema_slow, atr_period, regime_lookback))")
    # TF Filter Toggles
    tf_use_obi_filter: bool = Field(default=False, description="Enable OBI filter for Trend Following entries")
    tf_use_funding_filter: bool = Field(default=False, description="Enable Funding Rate filter for Trend Following entries")
    # OBI Scalper Settings
    obi_scalper_active_in_all_regimes: bool = Field(default=False, description="If true, OBI Scalper is active regardless of regime")
    gms_activate_obi_scalper_in_chop: bool = Field(default=False, description="If true, activate OBI Scalper in CHOP regime with GMS detector")
    # OBI Scalper Risk Parameters
    scalper_risk_pct: float = Field(default=0.5, ge=0.0, lt=100.0, description="Risk percentage for OBI Scalper (0.5 = 0.5%)")
    scalper_stop_ticks: int = Field(default=5, gt=0, description="Number of ticks for stop loss distance")

    @model_validator(mode='after')
    def validate_tf_warmup_bars(self):
        if isinstance(self.tf_warmup_bars, int) and self.tf_warmup_bars <= 0:
            raise ValueError("tf_warmup_bars must be positive integer or 'auto'")
        return self
    # OBI Scalper Strategy Configuration
    OBIScalperStrategy: OBIScalperStrategySettings = Field(default_factory=OBIScalperStrategySettings)



class PortfolioSettings(BaseModel):
    """Portfolio-level settings for risk management and position sizing."""
    # --- existing risk controls (required fields) ---
    initial_balance: float = Field(..., gt=0, description="Initial balance for backtests (in USD)")
    risk_per_trade: float = Field(..., gt=0, lt=1, description="Risk % of balance per trade")
    max_leverage: float = Field(..., ge=1, description="Global maximum leverage cap")
    asset_max_leverage: float = Field(..., gt=1, description="Asset-specific max leverage")
    margin_mode: Literal['cross', 'isolated'] = Field(default='cross', description="Margin mode: 'cross' or 'isolated'")
    max_hold_time_hours: int = Field(..., gt=0, description="Maximum time to hold a position in hours")

    # --- optional guards (neutral defaults) ---
    max_notional: float = Field(default=0, ge=0, description="Maximum notional value for all positions (0 = no explicit ceiling)")
    min_trade_size: float = Field(default=0.001, ge=0, description="Minimum trade size in BTC (0.001 BTC default)")
    leverage_guard_pct: float = Field(default=0, ge=0, description="Leverage guard percentage (0 = disabled)")
    margin_buffer_pct: float = Field(default=0.05, ge=0, le=0.20, description="Margin buffer percentage (0.05 = 5%)")

class CostSettings(BaseModel):
    funding_rate: float = Field(default=0.0001, ge=-1.0, le=1.0)
    funding_hours: int = Field(default=8, gt=0)
    taker_fee: float = Field(..., ge=0)
    maker_fee: float = Field(..., ge=0)
    l2_penalty_factor: float = Field(..., gt=1)

# --- Detector-Specific Settings Models ---
class GranularMicrostructureSettings(BaseModel):
    """Settings specific to the granular_microstructure detector."""
    # Threshold values (IMPORTANT: These are decimal values, not percentages)
    gms_vol_high_thresh: float = Field(default=0.0092, ge=0, description="ATR% threshold defining High Volatility (0.92% as decimal)")
    gms_vol_low_thresh: float = Field(default=0.0055, ge=0, description="ATR% threshold defining Low Volatility (0.55% as decimal)")
    gms_mom_strong_thresh: float = Field(default=100.0, ge=0, description="MA Slope absolute threshold defining Strong Momentum")
    gms_mom_weak_thresh: float = Field(default=50.0, ge=0, description="MA Slope absolute threshold defining Weak Momentum")
    gms_spread_std_high_thresh: float = Field(default=0.000050, ge=0, description="Spread Std Dev threshold indicating high uncertainty/chop")
    gms_spread_mean_low_thresh: float = Field(default=0.000045, ge=0, description="Spread Mean threshold indicating tight spreads/low vol range")

    # Operational settings
    cadence_sec: int = Field(default=3600, description="Recompute every N seconds (granular uses hourly)")
    output_states: int = Field(default=8, description="8 = raw, 4 or 3 after collapse")
    state_collapse_map_file: str = Field(default='configs/gms_state_mapping.yaml', description="Path to state mapping file")
    use_four_state_mapping: bool = Field(default=False, description="Adds TIGHT_SPREAD → TSP")
    risk_suppressed_notional_frac: float = Field(default=0.25, description="Large-trade threshold")
    risk_suppressed_pnl_atr_mult: float = Field(default=1.5, description="PnL threshold multiplier")

    # Performance optimization for Legacy System
    skip_l2_raw_processing_if_1h_features_exist: bool = Field(
        default=True,
        description="Skip expensive L2 raw processing when using granular_microstructure detector. "
                   "The Legacy System already has required features in 1h OHLCV files. "
                   "This optimization reduces processing time by ~99% for Legacy System backtests. "
                   "Set to False to maintain full legacy behavior with complete L2 processing."
    )



    @model_validator(mode='after')
    def check_granular_thresholds(self):
        # Volatility threshold validation
        if self.gms_vol_low_thresh >= self.gms_vol_high_thresh:
            raise ValueError("Granular Microstructure: Low Vol threshold must be less than High Vol threshold")

        # Enhanced volatility range validation (0 < vol_low < vol_high < 1)
        if not (0 < self.gms_vol_low_thresh < self.gms_vol_high_thresh < 1):
            raise ValueError(f"Granular Microstructure: Volatility thresholds must satisfy 0 < vol_low ({self.gms_vol_low_thresh}) < vol_high ({self.gms_vol_high_thresh}) < 1")

        # Momentum threshold validation
        if self.gms_mom_weak_thresh >= self.gms_mom_strong_thresh:
            raise ValueError("Granular Microstructure: Weak Mom threshold must be less than Strong Mom threshold")

        # Enhanced momentum range validation (0 < mom_low < mom_high < 200)
        if not (0 < self.gms_mom_weak_thresh < self.gms_mom_strong_thresh < 200):
            raise ValueError(f"Granular Microstructure: Momentum thresholds must satisfy 0 < mom_weak ({self.gms_mom_weak_thresh}) < mom_strong ({self.gms_mom_strong_thresh}) < 200")

        return self

class ContinuousGMSSettings(BaseModel):
    """Settings specific to the continuous_gms detector."""
    # Threshold values (IMPORTANT: These are decimal values, not percentages)
    gms_vol_high_thresh: float = Field(default=0.03, ge=0, description="ATR% threshold defining High Volatility (3% as decimal)")
    gms_vol_low_thresh: float = Field(default=0.01, ge=0, description="ATR% threshold defining Low Volatility (1% as decimal)")
    gms_mom_strong_thresh: float = Field(default=5.0, ge=0, description="MA Slope absolute threshold defining Strong Momentum")
    gms_mom_weak_thresh: float = Field(default=1.0, ge=0, description="MA Slope absolute threshold defining Weak Momentum")
    gms_spread_std_high_thresh: float = Field(default=0.0005, ge=0, description="Spread Std Dev threshold indicating high uncertainty/chop")
    gms_spread_mean_low_thresh: float = Field(default=0.0001, ge=0, description="Spread Mean threshold indicating tight spreads/low vol range")

    # Operational settings
    cadence_sec: int = Field(default=60, description="Recompute every N seconds (continuous uses minute)")
    output_states: int = Field(default=8, description="8 = raw, 4 or 3 after collapse")
    state_collapse_map_file: str = Field(default='configs/gms_state_mapping.yaml', description="Path to state mapping file")
    use_four_state_mapping: bool = Field(default=False, description="Adds TIGHT_SPREAD → TSP")
    risk_suppressed_notional_frac: float = Field(default=0.25, description="Large-trade threshold")
    risk_suppressed_pnl_atr_mult: float = Field(default=1.5, description="PnL threshold multiplier")

    @model_validator(mode='after')
    def check_continuous_thresholds(self):
        # Volatility threshold validation
        if self.gms_vol_low_thresh >= self.gms_vol_high_thresh:
            raise ValueError("Continuous GMS: Low Vol threshold must be less than High Vol threshold")

        # Enhanced volatility range validation (0 < vol_low < vol_high < 1)
        if not (0 < self.gms_vol_low_thresh < self.gms_vol_high_thresh < 1):
            raise ValueError(f"Continuous GMS: Volatility thresholds must satisfy 0 < vol_low ({self.gms_vol_low_thresh}) < vol_high ({self.gms_vol_high_thresh}) < 1")

        # Momentum threshold validation
        if self.gms_mom_weak_thresh >= self.gms_mom_strong_thresh:
            raise ValueError("Continuous GMS: Weak Mom threshold must be less than Strong Mom threshold")

        # Enhanced momentum range validation (0 < mom_low < mom_high < 200)
        if not (0 < self.gms_mom_weak_thresh < self.gms_mom_strong_thresh < 200):
            raise ValueError(f"Continuous GMS: Momentum thresholds must satisfy 0 < mom_weak ({self.gms_mom_weak_thresh}) < mom_strong ({self.gms_mom_strong_thresh}) < 200")

        return self

# --- Updated RegimeSettings ---
class RegimeSettings(BaseModel):
    # General Settings
    use_filter: bool = True
    detector_type: Literal['rule_based', 'granular_microstructure', 'continuous_gms', 'continuous_modern_v2', 'enhanced'] = Field(default='rule_based', description="Which regime detector implementation to use")

    # Rule-Based Settings
    use_enhanced_detection: bool = True
    use_strict_strategy_filtering: bool = True
    use_chop_filter: bool = True
    use_dynamic_risk: bool = False
    use_chop_index_for_chop: bool = False
    use_bbw_for_chop_detection: bool = False
    chop_leverage_factor: float = Field(default=0.5, gt=0, le=1)
    chop_risk_factor: float = Field(default=0.5, gt=0, le=1)
    pause_in_chop: bool = False

    # Hurst Settings
    hurst_lookback_periods: int = Field(default=100, gt=10)
    hurst_trending_threshold: float = Field(default=0.55, gt=0, lt=1)
    hurst_ranging_threshold: float = Field(default=0.45, gt=0, lt=1)
    hurst_min_series_length: int = Field(default=50, gt=10)

    # Granular Microstructure Settings
    gms_use_adx_confirmation: bool = Field(default=False, description="Require ADX confirmation for strong trends")
    gms_use_funding_confirmation: bool = Field(default=False, description="Require non-extreme funding rate for strong trends")
    gms_vol_high_thresh: float = Field(default=0.0092, ge=0, description="ATR% threshold defining High Volatility (0.92% as decimal)")
    gms_vol_low_thresh: float = Field(default=0.0055, ge=0, description="ATR% threshold defining Low Volatility (0.55% as decimal)")
    gms_mom_strong_thresh: float = Field(default=5.0, ge=0, description="MA Slope absolute threshold defining Strong Momentum")
    gms_mom_weak_thresh: float = Field(default=1.0, ge=0, description="MA Slope absolute threshold defining Weak Momentum")
    gms_spread_std_high_thresh: float = Field(default=0.0005, ge=0, description="Spread Std Dev threshold indicating high uncertainty/chop")
    gms_spread_mean_low_thresh: float = Field(default=0.0001, ge=0, description="Spread Mean threshold indicating tight spreads/low vol range")
    gms_funding_extreme_positive_thresh: float = Field(default=0.001, description="Funding rate above this dampens Strong Bull Trend")
    gms_funding_extreme_negative_thresh: float = Field(default=-0.001, description="Funding rate below this dampens Strong Bear Trend")
    gms_vol_high_percentile: Optional[int] = Field(default=75, ge=0, le=100, description="Percentile for High Vol (future use)")
    gms_vol_low_percentile: Optional[int] = Field(default=25, ge=0, le=100, description="Percentile for Low Vol (future use)")
    gms_vol_thresh_mode: Literal['fixed', 'percentile'] = Field(
        default='fixed',
        description="Method for volatility thresholds ('fixed' or 'percentile')."
    )
    gms_filter_allow_weak_bull_trend: bool = True
    gms_filter_allow_weak_bear_trend: bool = True
    # --- Weak Trend Risk Scaling (for soft gating) ---
    weak_bull_risk_scale: float = Field(default=0.2, ge=0, le=1, description="Scale factor for risk_per_trade during Weak Bull trends when disabled")
    weak_bear_risk_scale: float = Field(default=0.2, ge=0, le=1, description="Scale factor for risk_per_trade during Weak Bear trends when disabled")

    # --- State Mapping Behavior ---
    map_weak_bear_to_bear: bool = Field(default=False, description="If True, maps Weak_Bear_Trend to BEAR instead of CHOP (default).")

    # --- Dynamic Risk Adjustment Settings ---
    dynamic_risk_adjustment: bool = Field(default=False, description="Apply dynamic risk adjustments based on market regime")
    chop_risk_factor: float = Field(default=0.5, gt=0, le=1, description="Risk multiplier for choppy markets")
    chop_leverage_factor: float = Field(default=0.5, gt=0, le=1, description="Leverage multiplier for choppy markets")
    strong_trend_risk_factor: float = Field(default=0.7, gt=0, le=1, description="Risk multiplier for strong trend markets")
    strong_trend_leverage_factor: float = Field(default=0.8, gt=0, le=1, description="Leverage multiplier for strong trend markets")
    weak_trend_risk_scale: float = Field(default=0.8, gt=0, le=1, description="Risk scale factor for weak trend markets")

    # --- NEW: Primary GMS Adaptive Spread Thresholds ---
    gms_spread_mean_thresh_mode: Literal['fixed', 'percentile'] = Field(
        default='fixed',
        description="Method for gms_spread_mean_low threshold ('fixed' or 'percentile')."
    )
    gms_spread_std_thresh_mode: Literal['fixed', 'percentile'] = Field(
        default='fixed',
        description="Method for gms_spread_std_high threshold ('fixed' or 'percentile')."
    )
    gms_spread_mean_low_percentile: float = Field(
        default=0.25,
        description="Low percentile threshold for spread_mean if using percentile mode.",
        ge=0.0,
        le=1.0
    )
    gms_spread_std_high_percentile: float = Field(
        default=0.75,
        description="High percentile threshold for spread_std if using percentile mode.",
        ge=0.0,
        le=1.0
    )
    gms_spread_percentile_window: Optional[int] = Field(
        default=None, # If None, uses microstructure.spread_rolling_window
        description="Optional: Rolling window for primary GMS spread percentile calcs. Uses micro.spread_rolling_window if None.",
        gt=1
    )
        # --- End Primary GMS Adaptive Spread Thresholds ---
    # --- NEW: Adaptive Tight Spread Fallback ---
    gms_tight_spread_fallback_percentile: Optional[float] = Field(
        default=None,
        ge=0,
        le=1,
        description="[Optional] If set (e.g., 0.25), enables fallback to TIGHT_SPREAD regime when current spread is below this rolling percentile. Requires spread percentile signal calculation upstream."
    )
    gms_tight_spread_percentile_window: Optional[int] = Field(
        default=24,
        gt=1,
        description="[Optional] Window size (periods) for calculating the rolling spread percentile used in the adaptive fallback. Only used if fallback percentile is set."
    )
    # --- End Adaptive Tight Spread Fallback ---

    # --- GMS 3-State Mapping ---
    gms_use_three_state_mapping: bool = Field(default=False, description="If true, map the 7 raw GMS states to 3 consolidated states (BULL/BEAR/CHOP) using the specified mapping file.")
    gms_state_mapping_file: str = Field(default="gms_state_mapping.yaml", description="Path to the YAML file containing the mapping from 7 GMS states to 3 states. Path relative to project root or config directory.")

    # --- Optional Override Parameters ---
    gms_spread_trend_lookback: Optional[int] = Field(default=None, gt=0, description="[Override] Lookback for spread trend calculation")
    gms_adaptive_obi_base: Optional[float] = Field(default=None, gt=0, description="[Override] Base value for adaptive OBI")
    gms_confirmation_bars: Optional[int] = Field(default=None, gt=0, description="Number of bars for confirmation")
    gms_depth_slope_thin_limit: Optional[float] = Field(default=None, ge=0, description="[Override] Depth slope threshold to suppress trades (Set B, D)")
    gms_depth_skew_thresh: Optional[float] = Field(default=None, description="[Override] Depth skew threshold (Set C)")
    gms_obi_zscore_threshold: Optional[float] = Field(default=None, ge=0, description="Minimum absolute OBI Z-Score to consider significant")

    gms_spread_percentile_gate: Optional[float] = Field(default=None, ge=0, le=100, description="[Override] Spread Percentile Gate threshold (0-100). Trades suppressed if raw_spread_percentile > gate.")
    # --- End Override Parameters ---

    # --- 3-State Market Bias Settings ---
    market_bias: 'MarketBiasSettings' = Field(default_factory=lambda: MarketBiasSettings(), description="Market bias risk adjustment settings")

    # --- Detector-Specific Settings ---
    granular_microstructure: Optional[GranularMicrostructureSettings] = Field(default=None, description="Settings specific to granular_microstructure detector")
    continuous_gms: Optional[ContinuousGMSSettings] = Field(default=None, description="Settings specific to continuous_gms detector")

    def get_detector_settings(self, detector_type: str) -> dict:
        """Get settings for specific detector type with fallback to flat structure for backward compatibility."""
        # Try detector-specific section first
        if detector_type == 'granular_microstructure' and self.granular_microstructure is not None:
            return self.granular_microstructure.model_dump()
        elif detector_type == 'continuous_gms' and self.continuous_gms is not None:
            return self.continuous_gms.model_dump()
        elif detector_type == 'enhanced':
            # Enhanced detector uses granular_microstructure settings
            if self.granular_microstructure is not None:
                return self.granular_microstructure.model_dump()

        # Fall back to flat structure for backward compatibility (threshold values only)
        return {
            'gms_vol_high_thresh': self.gms_vol_high_thresh,
            'gms_vol_low_thresh': self.gms_vol_low_thresh,
            'gms_mom_strong_thresh': self.gms_mom_strong_thresh,
            'gms_mom_weak_thresh': self.gms_mom_weak_thresh,
            'gms_spread_std_high_thresh': self.gms_spread_std_high_thresh,
            'gms_spread_mean_low_thresh': self.gms_spread_mean_low_thresh,
        }

    def get_detector_operational_settings(self, detector_type: str, gms_config=None) -> dict:
        """Get operational settings for specific detector type with fallback to gms section."""
        # Try detector-specific section first
        if detector_type == 'granular_microstructure' and self.granular_microstructure is not None:
            settings = self.granular_microstructure.model_dump()
            return {k: v for k, v in settings.items() if k in ['cadence_sec', 'output_states', 'state_collapse_map_file', 'use_four_state_mapping', 'risk_suppressed_notional_frac', 'risk_suppressed_pnl_atr_mult']}
        elif detector_type == 'continuous_gms' and self.continuous_gms is not None:
            settings = self.continuous_gms.model_dump()
            return {k: v for k, v in settings.items() if k in ['cadence_sec', 'output_states', 'state_collapse_map_file', 'use_four_state_mapping', 'risk_suppressed_notional_frac', 'risk_suppressed_pnl_atr_mult']}

        # Fall back to gms section for backward compatibility
        if gms_config is not None:
            return {
                'cadence_sec': getattr(gms_config, 'cadence_sec', 60),
                'output_states': getattr(gms_config, 'output_states', 8),
                'state_collapse_map_file': getattr(gms_config, 'state_collapse_map_file', 'configs/gms_state_mapping.yaml'),
                'use_four_state_mapping': getattr(gms_config, 'use_four_state_mapping', False),
                'risk_suppressed_notional_frac': getattr(gms_config, 'risk_suppressed_notional_frac', 0.25),
                'risk_suppressed_pnl_atr_mult': getattr(gms_config, 'risk_suppressed_pnl_atr_mult', 1.5),
            }

        # Default fallback values
        return {
            'cadence_sec': 60 if detector_type == 'continuous_gms' else 3600,
            'output_states': 8,
            'state_collapse_map_file': 'configs/gms_state_mapping.yaml',
            'use_four_state_mapping': False,
            'risk_suppressed_notional_frac': 0.25,
            'risk_suppressed_pnl_atr_mult': 1.5,
        }

    @model_validator(mode='after')
    def check_hurst_thresholds(self):
        if self.hurst_ranging_threshold >= self.hurst_trending_threshold:
            raise ValueError(f"Hurst ranging threshold ({self.hurst_ranging_threshold}) must be less than trending threshold ({self.hurst_trending_threshold})")
        return self

    @model_validator(mode='after')
    def check_hurst_lookback(self):
        if self.hurst_lookback_periods < self.hurst_min_series_length:
             logger.warning(f"Hurst lookback period ({self.hurst_lookback_periods}) is less than the minimum series length required for calculation ({self.hurst_min_series_length}).")
        return self

    @model_validator(mode='after')
    def check_gms_thresholds(self):
        # Volatility threshold validation
        if self.gms_vol_low_thresh >= self.gms_vol_high_thresh:
            raise ValueError("GMS Detector: Low Vol threshold must be less than High Vol threshold")

        # Enhanced volatility range validation (0 < vol_low < vol_high < 1)
        if not (0 < self.gms_vol_low_thresh < self.gms_vol_high_thresh < 1):
            raise ValueError(f"GMS Detector: Volatility thresholds must satisfy 0 < vol_low ({self.gms_vol_low_thresh}) < vol_high ({self.gms_vol_high_thresh}) < 1")

        # Momentum threshold validation
        if self.gms_mom_weak_thresh >= self.gms_mom_strong_thresh:
            raise ValueError("GMS Detector: Weak Mom threshold must be less than Strong Mom threshold")

        # Enhanced momentum range validation (0 < mom_low < mom_high < 200) - Relaxed to allow original baseline values
        if not (0 < self.gms_mom_weak_thresh < self.gms_mom_strong_thresh < 200):
            raise ValueError(f"GMS Detector: Momentum thresholds must satisfy 0 < mom_weak ({self.gms_mom_weak_thresh}) < mom_strong ({self.gms_mom_strong_thresh}) < 200")

        return self

# --- MarketBiasSettings ---
class MarketBiasSettings(BaseModel):
    """Settings for the 3-State Market Bias risk adjustment system."""
    enabled: bool = Field(default=False, description="Enable market bias risk adjustments")
    use_three_state_mapping: bool = Field(default=True, description="Use simplified 3-state market model")

    # Leverage factors
    bull_leverage_factor: float = Field(default=1.5, ge=0, le=5.0, description="Leverage multiplier in bull markets")
    bear_leverage_factor: float = Field(default=0.7, ge=0, le=5.0, description="Leverage multiplier in bear markets")
    chop_leverage_factor: float = Field(default=0.5, ge=0, le=5.0, description="Leverage multiplier in choppy markets")

    # Risk factors
    bull_risk_factor: float = Field(default=1.2, ge=0, le=5.0, description="Risk multiplier in bull markets")
    bear_risk_factor: float = Field(default=0.8, ge=0, le=5.0, description="Risk multiplier in bear markets")
    chop_risk_factor: float = Field(default=0.5, ge=0, le=5.0, description="Risk multiplier in choppy markets")

    # Optional long-short bias
    bull_long_bias: float = Field(default=1.0, ge=0, le=5.0, description="Adjustment for longs in bull markets")
    bull_short_bias: float = Field(default=0.5, ge=0, le=5.0, description="Adjustment for shorts in bull markets")
    bear_long_bias: float = Field(default=0.5, ge=0, le=5.0, description="Adjustment for longs in bear markets")
    bear_short_bias: float = Field(default=1.0, ge=0, le=5.0, description="Adjustment for shorts in bear markets")

# --- MicrostructureSettings ---
class MicrostructureSettings(BaseModel):
    # Calculation settings
    depth_levels: int = Field(default=5, gt=0, description="Unified depth levels for OBI and depth metrics calculations")
    obi_levels: Optional[int] = Field(default=None, gt=0, description="DEPRECATED: Use depth_levels instead. If None, defaults to depth_levels.")
    obi_smoothing_window: int = Field(default=8, gt=1, description="Rolling window for OBI smoothing.")
    obi_smoothing_type: Literal['sma', 'ema'] = Field(default='sma', description="Type of smoothing for OBI ('sma' or 'ema').")
    obi_zscore_window: Optional[int] = Field(default=None, gt=1, description="[Optional] Rolling window for OBI Z-score calculation. If None, Z-score is not calculated.")
    depth_levels_for_calc: Optional[int] = Field(default=None, gt=0, description="DEPRECATED: Use depth_levels instead. If None, defaults to depth_levels.")
    spread_rolling_window: int = Field(default=24, gt=1)
    spread_metric_to_roll: Literal['absolute', 'relative'] = Field(default='relative')

    # --- NEW Optional Calculation Windows ---
    obi_zscore_window: Optional[int] = Field(default=None, gt=1, description="[Optional] Window size for OBI Z-score calculation. Falls back to obi_smoothing_window if None.")
    spread_percentile_window: Optional[int] = Field(default=None, gt=1, description="[Optional] Window size for Spread Percentile calculation. Falls back to spread_rolling_window if None.")
    adaptive_vol_long_window_days: Optional[int] = Field(default=30, gt=0, description="[Optional] Long-term window (days) for adaptive volatility calculation.")
    adaptive_vol_short_window_days: Optional[int] = Field(default=1, gt=0, description="[Optional] Short-term window (days) for adaptive volatility calculation.")
    # --- End Optional Calculation Windows ---

    # Granular Detector Thresholds
    gms_obi_strong_confirm_thresh: float = Field(default=0.2, ge=0, le=1.0, description="Absolute OBI threshold for Strong Trend confirmation")
    gms_obi_weak_confirm_thresh: float = Field(default=0.05, ge=0, le=1.0, description="Absolute OBI threshold for Weak Trend confirmation")

    # TF Filter Thresholds
    tf_filter_obi_threshold_long: float = Field(default=0.1, ge=-1.0, le=1.0)
    tf_filter_obi_threshold_short: float = Field(default=-0.1, ge=-1.0, le=1.0)
    tf_filter_funding_threshold_long: float = Field(default=-0.0005, ge=-1.0, le=1.0)
    tf_filter_funding_threshold_short: float = Field(default=0.0005, ge=-1.0, le=1.0)

    @model_validator(mode='after')
    def check_tf_obi_thresholds(self):
        if self.tf_filter_obi_threshold_long < self.tf_filter_obi_threshold_short:
             raise ValueError("TF OBI Filter: Long threshold must be >= Short threshold")
        return self

    @model_validator(mode='after')
    def check_gms_obi_thresholds(self):
        if self.gms_obi_weak_confirm_thresh > self.gms_obi_strong_confirm_thresh:
             raise ValueError("GMS Detector OBI: Weak confirmation threshold cannot be greater than Strong confirmation threshold")
        return self

    # Define constant list of legacy depth keys
    LEGACY_DEPTH_KEYS: ClassVar[list[str]] = ["obi_levels", "depth_levels_for_calc"]

    @model_validator(mode='after')
    def validate_depth_levels(self):
        """
        Ensures consistency between depth_levels, obi_levels, and depth_levels_for_calc.
        If legacy parameters are None, they are set to depth_levels.
        If they are set but disagree with depth_levels, raises ValueError.
        """
        # Map None values to depth_levels
        if self.obi_levels is None:
            self.obi_levels = self.depth_levels
        if self.depth_levels_for_calc is None:
            self.depth_levels_for_calc = self.depth_levels

        # Check for consistency
        if self.obi_levels != self.depth_levels:
            raise ValueError(f"obi_levels ({self.obi_levels}) must match depth_levels ({self.depth_levels}) or be set to None")
        if self.depth_levels_for_calc != self.depth_levels:
            raise ValueError(f"depth_levels_for_calc ({self.depth_levels_for_calc}) must match depth_levels ({self.depth_levels}) or be set to None")

        return self

# --- Updated IndicatorSettings ---
class IndicatorSettings(BaseModel):
    # General Configuration for Signal Engine
    require_volume_for_signals: bool = Field(default=False, description="If true, SignalEngine requires the 'volume' column in its input data.")

    # General Indicators
    adx_period: int = Field(default=14, gt=1)
    adx_threshold: float = Field(default=30.0, ge=0, description="ADX level for RuleBased 'Trending' & GMS optional confirmation")
    high_volatility_adx_threshold: float = Field(default=30.0, ge=0)
    low_forecast_threshold: float = Field(default=1.0, ge=0)
    volatility_thresh_percent: float = Field(default=0.005, ge=0)
    bbw_thresh: float = Field(default=0.03, ge=0)
    chop_index_period: int = Field(default=14, gt=1)
    chop_index_high_thresh: float = Field(default=61.8, ge=0)
    chop_index_low_thresh: float = Field(default=38.2, ge=0)
    min_leverage: float = Field(default=1.0, ge=1)

    # Trend Following
    tf_ewma_fast: int = Field(default=8, gt=1)
    tf_ewma_medium: int = Field(default=32, gt=1)
    use_tf_medium_ewma: bool = False
    tf_ewma_slow: int = Field(default=128, gt=1)
    tf_atr_period: int = Field(default=20, gt=1)
    tf_atr_stop_mult: float = Field(default=2.0, gt=0)
    tf_atr_target_mult: float = Field(default=4.0, gt=0)
    tf_leverage_base: float = Field(default=5.0, ge=1)
    tf_max_entry_volatility_pct: float = Field(default=0.01, gt=0, lt=0.1)

    # Mean Reversion (Inactive)
    mr_ema_period: int = Field(default=20, gt=1)
    mr_keltner_mult: float = Field(default=2.0, gt=0)
    mr_rsi_period: int = Field(default=14, gt=1)
    mr_rsi_oversold: float = Field(default=30.0, ge=0, lt=100)
    mr_rsi_overbought: float = Field(default=70.0, ge=0, lt=100)
    mr_atr_period: int = Field(default=20, gt=1)
    mr_atr_stop_mult: float = Field(default=1.5, gt=0)
    mr_leverage_base: float = Field(default=5.0, ge=1)
    mr_obi_long_threshold: float = Field(default=-0.2, ge=-1.0, le=1.0)
    mr_obi_short_threshold: float = Field(default=0.2, ge=-1.0, le=1.0)
    mr_require_obi_filter: bool = True

    # Mean Variance (Inactive)
    mv_edge_ema_period: int = Field(default=10, gt=1)
    mv_volatility_period: int = Field(default=20, gt=1)
    mv_min_edge_threshold: float = Field(default=0.0005, ge=0)
    mv_min_exit_edge_threshold: float = Field(default=0.0, ge=-1.0)
    mv_min_kelly: float = Field(default=0.05, gt=0, lt=1)
    mv_max_kelly: float = Field(default=0.25, gt=0, le=2.0)
    mv_leverage_base: float = Field(default=3.0, ge=1)
    mv_atr_stop_mult: float = Field(default=2.5, gt=0)
    mv_atr_target_mult: float = Field(default=3.0, gt=0)
    mv_atr_period: int = Field(default=20, gt=1)
    mv_obi_long_threshold: float = Field(default=-0.3, ge=-1.0, le=1.0)
    mv_obi_short_threshold: float = Field(default=0.3, ge=-1.0, le=1.0)
    mv_require_obi_filter: bool = True

    # Indicators for Granular Microstructure Detector
    gms_roc_period: int = Field(default=5, gt=1, description="Period for Rate of Change (Momentum)")
    gms_ma_slope_period: int = Field(default=10, gt=1, description="Period for Moving Average used in Slope (Momentum)")
    gms_atr_percent_period: int = Field(default=14, gt=1, description="Period for ATR used in ATR% (Volatility)")

    # Validators
    @model_validator(mode='after')
    def check_ewma_order_logic(self):
        if self.tf_ewma_fast >= self.tf_ewma_slow:
            raise ValueError("TF EWMA: fast period must be less than slow period")
        if self.use_tf_medium_ewma:
            if not (self.tf_ewma_fast < self.tf_ewma_medium < self.tf_ewma_slow):
                raise ValueError("TF EWMA: periods must be fast < medium < slow when medium is used")
        return self

    @model_validator(mode='after')
    def check_rsi_levels_logic(self):
         if self.mr_rsi_oversold >= self.mr_rsi_overbought:
             raise ValueError("MR RSI: Oversold threshold must be less than Overbought threshold")
         return self

class AnalysisSettings(BaseModel):
    analyze_trades_after_backtest: bool = True

# --- Add these new classes before the Config class definition

class FearGreedProviderSettings(BaseModel):
    enabled: bool = False
    # cache_path: Optional[str] = None # Reserved for future caching implementation

class DataProviderSettings(BaseModel):
    fear_greed: FearGreedProviderSettings = Field(default_factory=FearGreedProviderSettings)
    # Add other providers here later (e.g., crypto_sentiment, whale_tracker)

# TF-v3 Strategy Settings Model
class TFV3Settings(BaseModel):
    enabled: bool = Field(default=True, description="Enable TF-v3 strategy")
    ema_fast: int = Field(default=20, gt=0, description="Fast EMA period")
    ema_slow: int = Field(default=50, gt=0, description="Slow EMA period")
    atr_period: int = Field(default=14, gt=0, description="ATR period for stop calculation")
    atr_trail_k: float = Field(default=3.0, gt=0, description="ATR multiplier for trailing stop")
    max_trade_life_h: int = Field(default=24, gt=0, description="Maximum trade life in hours")
    risk_frac: float = Field(default=0.25, gt=0, lt=1, description="Fraction of available notional to allocate")
    max_notional: int = Field(default=25000, gt=0, description="Maximum notional value per trade")
    gms_max_age_sec: int = Field(default=120, gt=0, description="Maximum age of GMS snapshot in seconds")
    atr_fallback_pct: float = Field(default=0.01, gt=0, lt=0.20, description="ATR fallback percentage when ATR column is NaN (1% = 0.01)")
    trail_eps: float = Field(default=0.01, gt=0, description="Trailing-stop epsilon in quote units")

    @model_validator(mode='after')
    def check_ema_order(self):
        if self.ema_fast >= self.ema_slow:
            raise ValueError("TF-v3: Fast EMA period must be less than Slow EMA period")
        return self

# Mean Reversion Microstructure Strategy Settings Model
class MeanReversionMicrostructureSettings(BaseModel):
    enabled: bool = Field(default=False, description="Enable Mean Reversion Microstructure strategy")
    active_regimes: List[str] = Field(
        default_factory=lambda: ["Choppy_Market", "Low_Vol_Range", "High_Vol_Range"],
        description="Regimes where mean reversion is active (8-state names)"
    )
    
    # Entry thresholds by regime
    regime_thresholds: Dict[str, float] = Field(
        default_factory=lambda: {
            "Choppy_Market": 1.5,
            "Low_Vol_Range": 1.8,
            "High_Vol_Range": 2.5,
            "Weak_Bear_Trend": 2.0,
            "Weak_Bull_Trend": 2.0,
            "Strong_Bull_Trend": 3.0,
            "Strong_Bear_Trend": 3.0,
            "Uncertain": 2.2,
            "default": 2.5
        },
        description="OBI z-score entry thresholds by regime"
    )
    
    # Direction restrictions by regime (optional)
    regime_directions: Dict[str, str] = Field(
        default_factory=lambda: {
            "Weak_Bear_Trend": "short_only",
            "Weak_Bull_Trend": "long_only"
        },
        description="Direction restrictions by regime (long_only, short_only, or both)"
    )
    
    # Exit parameters
    exit_threshold: float = Field(default=0.5, gt=0, lt=3.0, description="OBI z-score exit threshold")
    max_hold_seconds: int = Field(default=300, gt=0, description="Maximum position hold time in seconds")
    
    # Risk management
    risk_frac: float = Field(default=0.15, gt=0, lt=1, description="Fraction of available notional to allocate")
    max_notional: int = Field(default=15000, gt=0, description="Maximum notional value per trade")
    atr_stop_mult: float = Field(default=2.0, gt=0, description="ATR multiplier for stop loss")
    
    # Feature toggles
    use_orderbook_flow: bool = Field(default=True, description="Use order book flow imbalance as confirmation")
    use_spread_velocity: bool = Field(default=True, description="Use spread velocity as risk filter")
    max_spread_velocity: float = Field(default=0.02, gt=0, description="Maximum spread velocity (2% = 0.02)")
    use_obi_divergence: bool = Field(default=False, description="Use multi-level OBI divergence")
    
    # Execution refinement
    use_execution_refinement: bool = Field(default=True, description="Use 1-minute execution refinement")
    
    # OBI configuration
    obi_zscore_window: int = Field(default=300, gt=0, description="Window for OBI z-score calculation")
    obi_levels: Literal[5, 20] = Field(default=5, description="OBI depth levels to use (5 or 20)")

    @model_validator(mode='after')
    def validate_thresholds(self):
        """Ensure exit threshold is less than all entry thresholds."""
        for regime, threshold in self.regime_thresholds.items():
            if regime != "default" and self.exit_threshold >= threshold:
                raise ValueError(f"Exit threshold ({self.exit_threshold}) must be less than entry threshold for {regime} ({threshold})")
        return self

# NEW GMS Settings Model
class GMSSettings(BaseModel):
    detector_type: Literal['continuous_gms', 'granular_microstructure'] = Field(default='continuous_gms', description="GMS detector type")
    cadence_sec: int = Field(default=60, description="Recompute every N seconds")
    output_states: int = Field(default=8, description="8 = raw, 4 or 3 after collapse")
    state_collapse_map_file: str = Field(default='configs/gms_state_mapping.yaml', description="Path to state mapping file")
    use_four_state_mapping: bool = Field(default=False, description="Adds TIGHT_SPREAD → TSP")
    risk_suppressed_notional_frac: float = Field(default=0.25, description="Large-trade threshold")
    risk_suppressed_pnl_atr_mult: float = Field(default=1.5, description="PnL threshold multiplier")

    # Priming configuration for adaptive thresholds
    priming_hours: int = Field(default=0, ge=0, le=72, description="Hours of historical data to prime adaptive thresholds (0 = disabled)")

    # Adaptive Threshold Configuration (Task R-112k)
    auto_thresholds: bool = Field(default=False, description="Enable adaptive, causal thresholds")
    percentile_window_sec: int = Field(default=86400, gt=0, description="24-hour rolling window (past only)")
    vol_low_pct: float = Field(default=0.15, ge=0.0, le=1.0, description="15th percentile (robust to outliers)")
    vol_high_pct: float = Field(default=0.50, ge=0.0, le=1.0, description="50th percentile (median)")
    mom_low_pct: float = Field(default=0.15, ge=0.0, le=1.0, description="15th percentile for momentum")
    mom_high_pct: float = Field(default=0.50, ge=0.0, le=1.0, description="50th percentile for momentum")
    min_history_rows: int = Field(default=10000, gt=0, description="Fallback to static if fewer past rows")

    @model_validator(mode='after')
    def validate_percentiles(self):
        """Ensure percentile values are properly ordered."""
        if self.vol_low_pct >= self.vol_high_pct:
            raise ValueError(f"vol_low_pct ({self.vol_low_pct}) must be less than vol_high_pct ({self.vol_high_pct})")
        if self.mom_low_pct >= self.mom_high_pct:
            raise ValueError(f"mom_low_pct ({self.mom_low_pct}) must be less than mom_high_pct ({self.mom_high_pct})")
        return self

# ETL Pipeline Settings
class ETL_L20_to_1s_Settings(BaseModel):
    chunk_sec: int = Field(default=3600, gt=0, description="Chunk size in seconds for processing (default: 3600 = hourly)")
    rollup_method: Literal['median', 'mean', 'first'] = Field(default='median', description="Method for resampling to 1-second intervals")

class ETLSettings(BaseModel):
    l20_to_1s: ETL_L20_to_1s_Settings = Field(default_factory=ETL_L20_to_1s_Settings, description="Settings for L20 to 1s ETL pipeline")

# NEW Visualization Settings Model
class VisualizationSettings(BaseModel):
    plot_enabled: bool = Field(default=True, description="Enable generation of equity curve plots")
    save_plots: bool = Field(default=True, description="Save generated plots to disk")
    plot_dir: str = Field(default="results/plots", description="Directory to save plots")
    plot_trades_on_chart: bool = Field(default=True, description="Plot entry/exit markers on the equity chart")
    show_plots: bool = Field(default=False, description="Show plots interactively (useful for debugging, might pause execution)")

# --- Main Config Class using Pydantic ---
class Config(BaseModel):
    """ Main configuration model, loading from config.yaml """
    is_backtest: bool = Field(default=False, description="Global flag indicating if running in backtest mode")
    system_mode: Optional[str] = Field(default=None, description="Trading system mode: 'legacy' or 'modern'")
    data_paths: DataPaths
    cache: CacheSettings
    backtest: BacktestSettings
    simulation: SimulationSettings
    strategies: StrategySettings
    portfolio: PortfolioSettings = Field(..., description="Portfolio settings for risk management and position sizing")
    costs: CostSettings
    regime: RegimeSettings
    microstructure: MicrostructureSettings # Added
    indicators: IndicatorSettings
    analysis: AnalysisSettings
    visualization: VisualizationSettings # ADDED VisualizationSettings field
    data_providers: DataProviderSettings = Field(default_factory=DataProviderSettings)
    timeframe: Literal['1h', '4h'] = Field(default='1h', description="Timeframe for OHLC data (1h or 4h)")
    gms: GMSSettings = Field(default_factory=GMSSettings, description="GMS detector settings")
    tf_v3: TFV3Settings = Field(default_factory=TFV3Settings, description="TF-v3 strategy settings")
    mean_reversion: MeanReversionMicrostructureSettings = Field(
        default_factory=MeanReversionMicrostructureSettings, 
        description="Mean Reversion Microstructure strategy settings"
    )
    etl: ETLSettings = Field(default_factory=ETLSettings, description="ETL pipeline settings")
    scheduler: SchedulerSettings = Field(default_factory=SchedulerSettings, description="Scheduler settings")

    # Derived fields
    @property
    def MAX_HOLD_TIME_SECONDS(self) -> int:
        return self.portfolio.max_hold_time_hours * 3600

    @property
    def FUNDING_INTERVAL_SECONDS(self) -> int:
        return self.costs.funding_hours * 3600

    @model_validator(mode='after')
    def validate_strategy_and_detector_support(self):
        """Validate that strategy names and detector types are supported as per Task R-103."""
        # Import here to avoid circular imports
        from hyperliquid_bot.strategies.strategy_factory import SUPPORTED_STRATEGIES, SUPPORTED_DETECTORS

        # Check detector type
        if self.regime.detector_type not in SUPPORTED_DETECTORS:
            raise ValueError(f"Unsupported detector type '{self.regime.detector_type}'. Supported detectors: {sorted(SUPPORTED_DETECTORS)}")

        # Note: Strategy validation is done at runtime when strategies are created,
        # since the config uses boolean flags (use_trend_following, etc.) rather than strategy names

        return self

# --- Function to Load Configuration ---
def load_config(config_path: str = 'config.yaml') -> Config:
    """Loads configuration from a YAML file and validates it using Pydantic."""
    full_path = Path(config_path)
    if not full_path.is_file():
        project_root_dir = Path(__file__).parent.parent.parent.resolve()
        alt_path = project_root_dir / config_path
        if alt_path.is_file(): full_path = alt_path
        else:
             common_run_path = Path.cwd() / config_path
             if common_run_path.is_file(): full_path = common_run_path
             else: raise FileNotFoundError(f"Config file not found: {config_path}, {alt_path}, {common_run_path}")

    logger.info(f"Loading configuration from: {full_path}")
    try:
        with open(full_path, 'r') as f: config_data = yaml.safe_load(f)
        if not config_data: raise ValueError(f"Config file empty/invalid: {full_path}")
        config = Config(**config_data)
        log_dir_path = config.data_paths.log_dir
        if not log_dir_path.exists():
            logger.info(f"Log directory not found, creating: {log_dir_path}")
            log_dir_path.mkdir(parents=True, exist_ok=True)
        logger.info("Configuration loaded and validated successfully.")
        return config
    except yaml.YAMLError as e: logger.error(f"Error parsing YAML: {full_path}\n{e}"); raise
    except Exception as e: logger.error(f"Error validating config from {full_path}: {e}"); raise

# --- Example Usage ---
if __name__ == "__main__":
    try:
        project_root = Path(__file__).parent.parent.parent.resolve()
        default_config_path = project_root / 'config.yaml'
        logger.info(f"Attempting to load config from: {default_config_path}")
        if default_config_path.exists():
            loaded_config = load_config(str(default_config_path))
            logger.info("\n--- Example Detector-Specific Config Values ---")
            if hasattr(loaded_config, 'microstructure'): # Check if section exists
                 logger.info(f"GMS OBI Strong Confirm: {loaded_config.microstructure.gms_obi_strong_confirm_thresh}")

            # Test detector-specific settings
            detector_type = loaded_config.regime.detector_type
            logger.info(f"Current detector type: {detector_type}")

            detector_settings = loaded_config.regime.get_detector_settings(detector_type)
            logger.info(f"Detector-specific settings for {detector_type}:")
            logger.info(f"  - Vol High Thresh: {detector_settings.get('gms_vol_high_thresh')}")
            logger.info(f"  - Vol Low Thresh: {detector_settings.get('gms_vol_low_thresh')}")
            logger.info(f"  - Mom Strong Thresh: {detector_settings.get('gms_mom_strong_thresh')}")
            logger.info(f"  - Mom Weak Thresh: {detector_settings.get('gms_mom_weak_thresh')}")

            # Test both detector types
            for test_detector in ['granular_microstructure', 'continuous_gms']:
                test_settings = loaded_config.regime.get_detector_settings(test_detector)
                logger.info(f"\nSettings for {test_detector}:")
                logger.info(f"  - Vol High: {test_settings.get('gms_vol_high_thresh')}")
                logger.info(f"  - Mom Strong: {test_settings.get('gms_mom_strong_thresh')}")
        else:
            logger.warning(f"{default_config_path} not found.")
    except Exception as e:
        logger.error(f"\nError during example usage: {e}")
