"""
System-specific configuration router for clean separation between legacy and modern systems.

This module ensures that legacy and modern trading systems can't accidentally
contaminate each other by forcing specific configuration settings based on
the selected system mode.
"""

import logging
from typing import Dict, Any, Optional
from copy import deepcopy


logger = logging.getLogger(__name__)


def route_system_config(base_config: Dict[str, Any], system_mode: str = 'legacy') -> Dict[str, Any]:
    """
    Route configuration based on selected system mode.
    
    This is the central point of system separation. It ensures that:
    - Legacy system ALWAYS uses granular_microstructure + TF-v2
    - Modern system ALWAYS uses continuous_gms + TF-v3
    - No accidental mixing is possible
    
    Args:
        base_config: The base configuration dictionary
        system_mode: Either 'legacy' or 'modern'
        
    Returns:
        Modified configuration dictionary with system-specific settings
    """
    # Deep copy to avoid modifying the original
    config = deepcopy(base_config)
    
    if system_mode == 'legacy':
        # Force legacy system settings
        logger.info("Configuring LEGACY system (granular_microstructure + TF-v2)")
        
        # Detector settings
        config.setdefault('regime', {})
        config['regime']['detector_type'] = 'granular_microstructure'
        
        # GMS settings (ensure legacy mode)
        config.setdefault('gms', {})
        config['gms']['detector_type'] = 'granular_microstructure'
        config['gms']['auto_thresholds'] = False  # Critical: no adaptive thresholds
        
        # Strategy settings
        config.setdefault('strategies', {})
        config['strategies']['use_tf_v2'] = True
        config['strategies']['use_tf_v3'] = False
        config['strategies']['use_mean_reversion'] = False
        config['strategies']['use_mean_reversion_microstructure'] = False
        
        # Ensure strict filtering for legacy
        config['regime']['use_strict_strategy_filtering'] = True
        
        # Log critical settings
        logger.info("Legacy config enforced: detector=granular_microstructure, strategy=TF-v2, auto_thresholds=False")
        
    elif system_mode == 'modern':
        # Force modern system settings
        logger.info("Configuring MODERN system (continuous_gms + TF-v3)")
        
        # Detector settings
        config.setdefault('regime', {})
        config['regime']['detector_type'] = 'continuous_gms'
        
        # GMS settings (ensure continuous mode)
        config.setdefault('gms', {})
        config['gms']['detector_type'] = 'continuous_gms'
        # Modern system can experiment with adaptive thresholds
        # Don't force auto_thresholds here - let override configs control it
        
        # Strategy settings
        config.setdefault('strategies', {})
        config['strategies']['use_tf_v2'] = False
        config['strategies']['use_tf_v3'] = True
        config['strategies']['use_mean_reversion'] = False
        config['strategies']['use_mean_reversion_microstructure'] = False
        
        # Modern system can have different filtering
        # Don't force strict filtering - let override configs control it
        
        # TF-v3 specific settings
        config.setdefault('tf_v3', {})
        config['tf_v3']['enabled'] = True
        
        # Log critical settings
        logger.info("Modern config enforced: detector=continuous_gms, strategy=TF-v3")
        
    else:
        raise ValueError(f"Invalid system mode: {system_mode}. Must be 'legacy' or 'modern'")
    
    # Add system mode to config for downstream components
    config['system_mode'] = system_mode
    
    return config


def validate_system_separation(config: Dict[str, Any]) -> bool:
    """
    Validate that system configurations are properly separated.
    
    This ensures that incompatible combinations can't occur:
    - Legacy detector with modern strategy
    - Modern detector with legacy strategy
    
    Args:
        config: Configuration dictionary to validate
        
    Returns:
        True if configuration is valid, raises ValueError otherwise
    """
    detector = config.get('regime', {}).get('detector_type')
    use_tf_v2 = config.get('strategies', {}).get('use_tf_v2', False)
    use_tf_v3 = config.get('strategies', {}).get('use_tf_v3', False)
    system_mode = config.get('system_mode', 'unknown')
    
    # Check for invalid combinations
    if detector == 'granular_microstructure' and use_tf_v3:
        raise ValueError(
            f"Invalid configuration: Legacy detector (granular_microstructure) "
            f"cannot be used with modern strategy (TF-v3). System mode: {system_mode}"
        )
    
    if detector == 'continuous_gms' and use_tf_v2:
        raise ValueError(
            f"Invalid configuration: Modern detector (continuous_gms) "
            f"cannot be used with legacy strategy (TF-v2). System mode: {system_mode}"
        )
    
    # Check that exactly one main strategy is enabled
    main_strategies = [use_tf_v2, use_tf_v3]
    if sum(main_strategies) != 1:
        raise ValueError(
            f"Exactly one main strategy must be enabled. "
            f"TF-v2={use_tf_v2}, TF-v3={use_tf_v3}. System mode: {system_mode}"
        )
    
    # Log successful validation
    logger.info(
        f"System configuration validated: mode={system_mode}, "
        f"detector={detector}, TF-v2={use_tf_v2}, TF-v3={use_tf_v3}"
    )
    
    return True