# hyperliquid_bot/data/feature_store.py

import logging
from pathlib import Path
from datetime import datetime, date
from typing import Optional, Union, List

import pandas as pd
import pyarrow as pa
import pyarrow.dataset as ds
import pyarrow.parquet as pq

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.utils.feature_naming import obi_col

logger = logging.getLogger(__name__)

class FeatureStore:
    """
    Feature store for accessing 1-second feature data.

    This class provides methods to load and query 1-second feature data
    generated by the ETL pipeline.
    """

    def __init__(self, config: Config):
        """
        Initialize the feature store.

        Args:
            config: Application configuration
        """
        self.config = config
        self.feature_1s_dir = Path(config.data_paths.feature_1s_dir)

        if not self.feature_1s_dir.exists():
            logger.warning(f"Feature 1s directory does not exist: {self.feature_1s_dir}")

    def load_1s(
        self,
        date_str: Union[str, datetime, date],
        depth: Optional[int] = None,
        columns: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        Load 1-second feature data for a specific date.

        Args:
            date_str: Date string in YYYY-MM-DD format, or datetime/date object
            depth: Depth level for OBI features (default: from config)
            columns: List of columns to load (default: all)

        Returns:
            DataFrame with 1-second feature data
        """
        # Convert date to string if needed
        if isinstance(date_str, (datetime, date)):
            date_str = date_str.strftime("%Y%m%d")
        elif isinstance(date_str, str) and "-" in date_str:
            # Convert YYYY-MM-DD to YYYYMMDD
            date_str = date_str.replace("-", "")

        # Use depth from config if not provided
        if depth is None:
            depth = self.config.microstructure.depth_levels

        # Build dataset path
        dataset_path = self.feature_1s_dir

        # Check if directory exists
        if not dataset_path.exists():
            logger.error(f"Feature dataset directory does not exist: {dataset_path}")
            return pd.DataFrame()

        try:
            # Try to find files with pattern features_{date_str}_*.parquet
            files = list(dataset_path.glob(f"features_{date_str}_*.parquet"))

            # If no files found, try to find files in subdirectory with pattern {date_str}/features_*.parquet
            if not files:
                # Convert YYYYMMDD to YYYY-MM-DD for feature path
                try:
                    date_obj = datetime.strptime(date_str, '%Y%m%d')
                    feature_date_str = date_obj.strftime('%Y-%m-%d')

                    # Check if feature directory exists for this date
                    feature_path = dataset_path / feature_date_str

                    if feature_path.exists():
                        files = list(feature_path.glob("features_*.parquet"))
                        logger.info(f"Found {len(files)} feature files in subdirectory {feature_date_str}")
                except Exception as e:
                    logger.debug(f"Error checking for feature files in subdirectory: {e}")

            if not files:
                logger.warning(f"No feature files found for date {date_str}")
                return pd.DataFrame()

            logger.info(f"Found {len(files)} feature files for date {date_str}")

            # Create dataset
            dataset = ds.dataset(files, format="parquet")

            # First, read schema to get available columns
            # Use schema property instead of to_table with limit (compatibility fix)
            available_columns = dataset.schema.names

            # Dynamically resolve OBI column name based on depth
            try:
                obi_column_name = obi_col(available_columns, depth)
            except KeyError as e:
                logger.error(f"Failed to resolve OBI column for depth {depth}: {e}")
                return pd.DataFrame()

            # Prepare columns to read
            if columns is None:
                # Default columns
                columns = [
                    "timestamp", "best_bid", "best_ask", "mid_price", "close",
                    "spread", "spread_relative", obi_column_name,
                    "realised_vol_1s", "atr", "atr_percent", "atr_14_sec", "atr_percent_sec", "unrealised_pnl"
                ]

            # Ensure OBI column is included
            if obi_column_name not in columns:
                columns.append(obi_column_name)

            # Read dataset
            table = dataset.to_table(columns=columns)

            # Convert to pandas
            df = table.to_pandas()

            # Sort by timestamp
            if "timestamp" in df.columns:
                df = df.sort_values("timestamp")

            logger.info(f"Loaded {len(df)} rows of 1-second feature data")
            return df

        except Exception as e:
            logger.error(f"Error loading 1-second feature data: {e}")
            return pd.DataFrame()

    def get_available_dates(self) -> List[str]:
        """
        Get list of available dates in the feature store.

        Returns:
            List of date strings in YYYYMMDD format
        """
        if not self.feature_1s_dir.exists():
            return []

        # Find all feature files
        files = list(self.feature_1s_dir.glob("features_*.parquet"))

        # Extract dates from filenames
        dates = set()
        for file in files:
            # Extract date from filename pattern: features_YYYYMMDD_HH.parquet
            parts = file.stem.split("_")
            if len(parts) >= 2:
                date_part = parts[1]
                if len(date_part) == 8 and date_part.isdigit():
                    dates.add(date_part)

        return sorted(list(dates))

    def get_available_hours(self, date_str: str) -> List[int]:
        """
        Get list of available hours for a specific date.

        Args:
            date_str: Date string in YYYYMMDD format

        Returns:
            List of available hours (0-23)
        """
        if not self.feature_1s_dir.exists():
            return []

        # Find files for the date
        files = list(self.feature_1s_dir.glob(f"features_{date_str}_*.parquet"))

        # Extract hours from filenames
        hours = []
        for file in files:
            # Extract hour from filename pattern: features_YYYYMMDD_HH.parquet
            parts = file.stem.split("_")
            if len(parts) >= 3:
                hour_part = parts[2]
                if hour_part.isdigit():
                    hour = int(hour_part)
                    if 0 <= hour <= 23:
                        hours.append(hour)

        return sorted(hours)
