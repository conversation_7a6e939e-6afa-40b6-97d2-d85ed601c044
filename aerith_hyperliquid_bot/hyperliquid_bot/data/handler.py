# hyperliquid_bot/data/handler.py

import logging
from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
from typing import Optional, Tuple, List
from datetime import datetime, timedelta
from pathlib import Path
import time # For timing operations

# Import Config for type hinting
from hyperliquid_bot.config.settings import Config
# Import microstructure calculation functions
from hyperliquid_bot.features import microstructure # Assuming it's in features dir
# Import Fear & Greed index provider
from ..providers.feargreed import fetch_fear_greed_index
# Import data utilities
from hyperliquid_bot.utils.data_utils import deduplicate
# Import feature store
from hyperliquid_bot.data.feature_store import FeatureStore
# Import UTC helper for timezone consistency
from hyperliquid_bot.utils.time import to_utc_naive, vectorized_to_utc_naive

# Module-level docstring
"""
Data Handling Component.

Defines interfaces and provides implementations for loading, managing,
and accessing market data (OHLCV, L2 Order Book, etc.) for backtesting
and potentially live trading. Integrates raw microstructure features.
"""

logger = logging.getLogger(__name__)

# Define a type alias for L2 snapshot data for clarity
# List of bids [(price, size), ...], List of asks [(price, size), ...]
L2SnapshotType = Optional[Tuple[List[Tuple[float, float]], List[Tuple[float, float]]]]

class DataHandlerInterface(ABC):
    """
    Abstract Base Class for data handlers.
    Defines the common interface for accessing market data.
    """
    def __init__(self, config: Config):
        self.config = config
        logger.info(f"Initializing {self.__class__.__name__}...")

    @abstractmethod
    def load_historical_data(self, start_date: datetime, end_date: datetime) -> None:
        """
        Load all necessary historical data for the specified period.
        This should populate internal data structures.

        Args:
            start_date: The start datetime of the required data range.
            end_date: The end datetime (exclusive) of the required data range.
        """
        pass

    @abstractmethod
    def get_ohlcv_data(self) -> pd.DataFrame:
        """
        Returns the entire loaded historical OHLCV data as a DataFrame,
        potentially including integrated microstructure features.

        Returns:
            pandas DataFrame with OHLCV data and features, indexed by timestamp.
        """
        pass


    @abstractmethod
    def get_funding_rate(self, timestamp: pd.Timestamp) -> float:
        """
        Retrieves the funding rate applicable at the given timestamp.
        """
        pass

    @abstractmethod
    def get_open_interest(self, timestamp: pd.Timestamp) -> Optional[float]:
        """
        Retrieves the Open Interest applicable at or just before the given timestamp.
        """
        pass

    @abstractmethod
    def get_recent_liquidations(self, timestamp: pd.Timestamp, lookback_period: timedelta) -> Tuple[Optional[float], Optional[float]]:
        """
        Retrieves the total liquidation volume within a recent lookback period.
        """
        pass

    @abstractmethod
    def get_recent_close_prices(self, timestamp: pd.Timestamp, lookback_periods: int) -> Optional[pd.Series]:
        """
        Retrieves a series of recent close prices ending at or just before the
        given timestamp from the loaded OHLCV data.
        """
        pass


# --- Concrete Implementation for Historical Data ---

class HistoricalDataHandler(DataHandlerInterface):
    """
    Data handler implementation for loading and accessing historical data
    from local files (CSV for OHLCV, Parquet for L2 snapshots).
    Integrates raw microstructure features into the main OHLCV DataFrame.
    """
    def __init__(self, config: Config, parent_logger: Optional[logging.Logger] = None):
        super().__init__(config)
        self.config = config
        # Use the parent logger if provided, otherwise use the module logger
        self.logger = parent_logger if parent_logger else logger
        self.logger.info("Initializing HistoricalDataHandler...")

        self.ohlcv_data: pd.DataFrame = pd.DataFrame() # Holds only OHLCV initially
        self.combined_data: pd.DataFrame = pd.DataFrame() # Holds final OHLCV + Microstructure
        # Removed current_l2_segment and current_l2_date_str as we process day by day
        self.l2_file_pattern = "{date_str}_raw2.parquet"
        self.l2_data_root = Path(self.config.data_paths.l2_data_root)
        self.funding_rates: Optional[pd.Series] = None
        self.default_funding_rate: float = self.config.costs.funding_rate
        self.open_interest: Optional[pd.Series] = None
        self.liquidations: Optional[pd.DataFrame] = None
        self._warned_missing_oi = False
        self._warned_missing_liq = False
        self._warned_missing_l2 = False # Flag for missing L2 files

        if not self.l2_data_root.exists():
             logger.error(f"L2 data root path does not exist: {self.l2_data_root}")
             # This should probably be fatal if microstructure features are needed
             # raise FileNotFoundError(f"L2 data root path not found: {self.l2_data_root}")

    def load_historical_data(self, start_date: datetime, end_date: datetime) -> None:
        """Loads OHLCV and integrates microstructure features."""
        logger.info(f"Loading historical data from {start_date} to {end_date}...")
        load_start_time = time.time()
        try:
            # 1. Load OHLCV
            self._load_ohlcv(start_date, end_date)

            # 2. Integrate Microstructure Features
            self._integrate_microstructure_features()

            # 3. Load other data (placeholders)
            # self._load_funding_rates(start_date, end_date)
            logger.info("Funding rate data not loaded (using default from config).")
            # self._load_open_interest(start_date, end_date)
            logger.info("Open Interest data not loaded (placeholders active).")
            # self._load_liquidations(start_date, end_date)
            logger.info("Liquidation data not loaded (placeholders active).")

        except (FileNotFoundError, ValueError) as e:
             logger.error(f"Failed to load mandatory data: {e}. Halting.")
             raise
        except Exception as e:
             logger.error(f"Unexpected error during historical data loading: {e}", exc_info=True)
             raise

        load_end_time = time.time()
        logger.info(f"Historical data loading complete. Final shape={self.combined_data.shape}. Took {load_end_time - load_start_time:.2f} seconds.")


    def _load_ohlcv(self, start_date: datetime, end_date: datetime) -> None:
        """
        Internal method to load and preprocess L2-resampled OHLCV data.
        Loads data based on the configured timeframe ('1h' or '4h')
        from the ohlcv_base_path. Expects parquet files (YYYY-MM-DD_<tf>.parquet).
        """
        cfg = self.config
        timeframe = cfg.timeframe # Get timeframe ('1h' or '4h')
        logger.info(f"Loading {timeframe} L2-resampled OHLCV data...")
        all_files = []
        current_date = start_date

        # --- MODIFIED: Use new base path and timeframe subdir ---
        base_path = Path(cfg.data_paths.ohlcv_base_path)
        timeframe_path = base_path / timeframe # e.g., ../resampled_l2/1h/
        # --- END MODIFIED ---

        if not timeframe_path.exists():
            # --- MODIFIED: Updated error message ---
            raise FileNotFoundError(f"OHLCV path for timeframe '{timeframe}' not found: {timeframe_path}")
            # --- END MODIFIED ---

        while current_date < end_date:
            # --- MODIFIED: Changed filename pattern and extension ---
            file_path = timeframe_path / f"{current_date.strftime('%Y-%m-%d')}_{timeframe}.parquet"
            # --- END MODIFIED ---
            if file_path.exists():
                all_files.append(file_path)
            else:
                # Try alternative format without hyphens
                alt_file_path = timeframe_path / f"{current_date.strftime('%Y%m%d')}_{timeframe}.parquet"
                if alt_file_path.exists():
                    all_files.append(alt_file_path)
                    logger.debug(f"Found alternative format OHLCV file: {alt_file_path}")
                else:
                    # Log only once per run potentially, or just debug level? Let's use debug.
                    logger.debug(f"OHLCV file not found for date {current_date.strftime('%Y-%m-%d')}, timeframe {timeframe}: {file_path}")
            current_date += timedelta(days=1)

        if not all_files:
            # --- MODIFIED: Updated error message --- # Date formatting added
            raise FileNotFoundError(f"No {timeframe} L2-resampled OHLCV Parquet data found for {start_date.date()} to {end_date.date()} in {timeframe_path}")
            # --- END MODIFIED ---

        logger.info(f"Found {len(all_files)} {timeframe} L2-resampled OHLCV data files.")

        try:
            df_list = []
            # Define required columns - conditionally include 'volume'
            req_cols = ['timestamp', 'open', 'high', 'low', 'close']
            if self.config.data_paths.require_ohlcv_volume:
                req_cols.append('volume')
                logger.debug("Volume column is required based on configuration.")
            else:
                logger.debug("Volume column is NOT required based on configuration.")

            for f in all_files:
                try:
                    # --- MODIFIED: Use pd.read_parquet ---
                    # Parquet usually loads all columns efficiently, no need for usecols usually
                    df_chunk = pd.read_parquet(f)
                    # --- END MODIFIED ---

                    # Convert timestamp to UTC-naive using vectorized helper (PERFORMANCE OPTIMIZATION)
                    if 'timestamp' in df_chunk.columns and not isinstance(df_chunk.index, pd.DatetimeIndex):
                        df_chunk['timestamp'] = vectorized_to_utc_naive(df_chunk['timestamp'])
                    df_chunk = df_chunk.dropna(subset=['timestamp'])

                    # Assert UTC-naive for fail-fast debugging
                    assert df_chunk['timestamp'].dt.tz is None, f"OHLCV chunk from {f} has tz-aware timestamps!"

                    # --- ADDED: Set timestamp as index --- #
                    if 'timestamp' in df_chunk.columns:
                        df_chunk = df_chunk.set_index('timestamp', drop=True)
                    else:
                        # This case shouldn't happen if parquet saves the timestamp column, but handle defensively
                        logger.error(f"File {f} does not contain a 'timestamp' column after loading and conversion attempts. Skipping.")
                        continue
                    # --- END ADDED --- #

                    # Check required columns *after* setting index
                    # Use the conditionally defined req_cols (excluding 'timestamp' as it's the index now)
                    current_req_cols = [c for c in req_cols if c != 'timestamp']
                    missing_cols = [c for c in current_req_cols if c not in df_chunk.columns]

                    if missing_cols:
                        # Log specific error, skip file
                        self.logger.error(f"File {f} missing required columns: {missing_cols}. Skipping file.")
                        continue # Skip this file

                    df_list.append(df_chunk)
                except Exception as e:
                    logger.error(f"Error loading or processing Parquet file {f}: {e}", exc_info=True)
                    continue

            if not df_list:
                raise ValueError("Failed to load any valid L2-resampled OHLCV data.")

            df = pd.concat(df_list) # concat handles indices correctly if they are DatetimeIndex
            # Use deduplicate utility to handle duplicate indices (keeping first occurrence)
            df = deduplicate(df)

            # Rename columns (if necessary, assuming standard names 'open', 'high', etc.)
            # No rename needed if parquet files already use these names.

            # Convert numeric columns (Parquet might handle types, but good to ensure)
            # Conditionally define numeric columns
            numeric_cols = ['open', 'high', 'low', 'close']
            if self.config.data_paths.require_ohlcv_volume and 'volume' in df.columns:
                # Only add 'volume' if required AND it actually exists in the final df
                numeric_cols.append('volume')
            elif 'volume' in df.columns and not self.config.data_paths.require_ohlcv_volume:
                # If volume exists but is not required, still try to convert it if present.
                numeric_cols.append('volume')

            for col in numeric_cols:
                if col in df.columns: # Check if column exists before trying conversion
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            df.dropna(subset=['open', 'high', 'low', 'close'], inplace=True) # Drop rows where essential prices are NaN

            # Filter by date range (already done by file selection, but good as safeguard)
            # Convert start/end dates to UTC-naive for comparison with our UTC-naive data
            start_ts_utc = to_utc_naive(pd.Timestamp(start_date))
            end_ts_utc = to_utc_naive(pd.Timestamp(end_date))
            df = df[(df.index >= start_ts_utc) & (df.index < end_ts_utc)]

            if df.empty:
                raise ValueError("L2-resampled OHLCV data empty after loading and filtering.")

            self.ohlcv_data = df # Store loaded data
            logger.info(f"L2-resampled {timeframe} OHLCV data loaded successfully. Shape={df.shape}")

            # Check if Fear & Greed provider is enabled in the config
            # --- Fear & Greed merge logic remains the same ---
            if self.config.data_providers.fear_greed.enabled:
                logger.info("Fetching and merging Fear & Greed Index data...")
                # Fetch all historical data (limit=0)
                fg_data = fetch_fear_greed_index(limit=0)

                if fg_data is not None and not fg_data.empty:
                    # --- Timezone Alignment with UTC-naive approach ---
                    # Ensure OHLCV index is UTC-naive (should already be from our processing above)
                    if df.index.tz is not None:
                        logger.warning("OHLCV index is timezone-aware. Converting to UTC-naive for F&G merge.")
                        # PERFORMANCE OPTIMIZATION: Use vectorized timezone conversion instead of .map()
                        df.index = df.index.tz_convert('UTC').tz_localize(None)

                    # Ensure F&G index is UTC-naive
                    if fg_data is not None:
                        if fg_data.index.tz is not None:
                            logger.warning("F&G index was timezone-aware. Converting to UTC-naive.")
                            # PERFORMANCE OPTIMIZATION: Use vectorized timezone conversion
                            fg_data.index = fg_data.index.tz_convert('UTC').tz_localize(None)

                        # Assert both indices are UTC-naive for fail-fast debugging
                        assert df.index.tz is None, "OHLCV index should be UTC-naive for F&G merge"
                        assert fg_data.index.tz is None, "F&G index should be UTC-naive for F&G merge"

                    # --- Merge Data ---
                    if fg_data is not None: # Proceed only if fg_data is valid
                        logger.info(f"Merging F&G data (shape: {fg_data.shape}) into OHLCV data (shape: {df.shape})")
                        # Use merge_asof: finds the nearest preceding F&G timestamp for each OHLCV timestamp.
                        df = df.sort_index()
                        fg_data = fg_data.sort_index()

                        df = pd.merge_asof(
                            df,
                            fg_data[['fear_greed_idx']],
                            left_index=True,
                            right_index=True,
                            direction='backward',
                            tolerance=pd.Timedelta(days=2) # Allow up to 2 days gap
                        )

                        # Check if merge added the column
                        if 'fear_greed_idx' not in df.columns:
                            logger.warning("Fear & Greed index column ('fear_greed_idx') not found after merge_asof.")
                        else:
                            logger.info(f"Fear & Greed data merged. {df['fear_greed_idx'].notna().sum()} values non-NaN.")
                            # Optional: Forward fill missing F&G values if desired
                            # df['fear_greed_idx'] = df['fear_greed_idx'].ffill()
                            # df['fear_greed_classification'] = df['fear_greed_classification'].ffill()
                    else: logger.warning("Fear & Greed data was None or empty after timezone checks, skipping merge.")

                else: logger.warning("No Fear & Greed data fetched or available.")
                self.ohlcv_data = df # Update self.ohlcv_data with the merged data
                logger.info(f"OHLCV data shape after F&G merge attempt: {self.ohlcv_data.shape}")
                if 'fear_greed_idx' in self.ohlcv_data.columns:
                    logger.info(f"Non-NaN F&G values in final df: {self.ohlcv_data['fear_greed_idx'].notna().sum()}")
                else:
                    logger.warning("'fear_greed_idx' column missing in final self.ohlcv_data")

        except FileNotFoundError as e:
            logger.error(f"Failed to load mandatory data: {e}. Halting.")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during OHLCV loading/preprocessing: {e}", exc_info=True)
            raise

    def _load_l2_segment(self, date_str: str) -> Optional[pd.DataFrame]:
        """Loads, preprocesses, and returns a daily L2 Parquet file segment."""
        l2_file_name = self.l2_file_pattern.format(date_str=date_str)
        l2_file_path = self.l2_data_root / l2_file_name

        if not l2_file_path.exists():
            # --- BEGIN PATCH ---
            # Check if 1-second feature files exist for this date
            # Convert YYYYMMDD to YYYY-MM-DD for feature path
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                feature_date_str = date_obj.strftime('%Y-%m-%d')

                # Check if feature directory exists for this date
                feature_path = Path(self.config.data_paths.feature_1s_dir) / feature_date_str

                if feature_path.exists() and any(feature_path.glob("features_*.parquet")):
                    if not self._warned_missing_l2:
                        logger.warning(f"L2 Parquet file not found for {date_str}: {l2_file_path}; "
                                      f"skipping because 1-s feature files are present in {feature_path}")
                        self._warned_missing_l2 = True
                    return None
            except Exception as e:
                logger.debug(f"Error checking for feature files: {e}")
            # --- END PATCH ---

            if not self._warned_missing_l2: # Log only once per run potentially
                 logger.warning(f"L2 Parquet file not found for {date_str}: {l2_file_path}")
                 # This should probably be fatal if microstructure features are needed
                 # raise FileNotFoundError(f"L2 data root path not found: {self.l2_data_root}")
            self._warned_missing_l2 = True
            return None

        logger.debug(f"Loading L2 segment from: {l2_file_path}")
        try:
            segment_df = pd.read_parquet(l2_file_path)
            logger.debug(f"L2 Parquet {date_str} read successful. Initial shape: {segment_df.shape}")

            # --- Data Validation and Preprocessing ---
            # Assume 'timestamp' column exists from previous checks or file format guarantee
            if 'timestamp' not in segment_df.columns:
                 logger.error(f"L2 file {l2_file_path} missing 'timestamp'. Skipping day.")
                 return None

            # Ensure UTC-naive timestamps using vectorized helper (PERFORMANCE OPTIMIZATION)
            segment_df['timestamp'] = vectorized_to_utc_naive(segment_df['timestamp'])
            segment_df = segment_df.dropna(subset=['timestamp'])

            # Assert UTC-naive for fail-fast debugging
            assert segment_df['timestamp'].dt.tz is None, f"L2 segment {date_str} has tz-aware timestamps!"

            # Check for essential L1 columns (adjust if format differs)
            # Assuming format is bid_price_1, bid_size_1, ask_price_1, ask_size_1 ...
            required_l1_cols = ['bid_price_1', 'bid_size_1', 'ask_price_1', 'ask_size_1']
            if not all(col in segment_df.columns for col in required_l1_cols):
                 logger.error(f"L2 file {l2_file_path} missing required L1 columns {required_l1_cols}. Skipping day.")
                 return None

            # Convert price/size columns to numeric (assuming up to 5 levels)
            for i in range(1, 6):
                for side in ['bid', 'ask']:
                    for col_type in ['price', 'size']:
                        col = f"{side}_{col_type}_{i}"
                        if col in segment_df.columns and not pd.api.types.is_numeric_dtype(segment_df[col]):
                            segment_df[col] = pd.to_numeric(segment_df[col], errors='coerce')
                            # Optionally fill NaNs created by coerce? e.g., with 0? Or handle in feature calc.

            segment_df = segment_df.set_index('timestamp').sort_index()

            if not segment_df.index.is_monotonic_increasing:
                 logger.warning(f"L2 segment index for {date_str} is not monotonic increasing after sorting! Check data.")
                 # Use deduplicate utility to handle duplicate indices (keeping first occurrence)
                 segment_df = deduplicate(segment_df)

            if segment_df.empty:
                logger.warning(f"L2 segment empty after loading/preprocessing: {l2_file_path}")
                return None

            logger.debug(f"Processed L2 segment for {date_str}. Shape: {segment_df.shape}")
            return segment_df

        except Exception as e:
            logger.error(f"EXCEPTION during L2 load/process for {date_str}: {e}", exc_info=True)
            return None

    def _calculate_features_from_row(self, row: pd.Series) -> pd.Series:
        """Helper function to calculate microstructure features from a merged row."""
        bids = []
        asks = []
        # Extract L2 levels from the row (assuming column names like bid_price_1, etc.)
        for i in range(1, 6): # Up to 5 levels
            try:
                bid_px = row.get(f'bid_price_{i}')
                bid_sz = row.get(f'bid_size_{i}')
                ask_px = row.get(f'ask_price_{i}')
                ask_sz = row.get(f'ask_size_{i}')

                # Add level only if price and size are valid numbers > 0
                if pd.notna(bid_px) and pd.notna(bid_sz) and bid_px > 0 and bid_sz > 0:
                    bids.append((float(bid_px), float(bid_sz)))
                if pd.notna(ask_px) and pd.notna(ask_sz) and ask_px > 0 and ask_sz > 0:
                    asks.append((float(ask_px), float(ask_sz)))
            except Exception as e:
                # Log error if conversion fails for a specific level
                logger.error(f"Error processing L2 level {i} in row {row.name}: {e}", exc_info=False) # Reduce log noise
                # Continue to next level

        # Ensure sorting (might be redundant if source columns are ordered, but safe)
        bids.sort(key=lambda x: x[0], reverse=True)
        asks.sort(key=lambda x: x[0])

        l2_snapshot: L2SnapshotType = (bids, asks) if (bids or asks) else None
        # Determine best bid and best ask for use in simulation
        best_bid = bids[0][0] if bids else float('nan')
        best_ask = asks[0][0] if asks else float('nan')

        # Calculate features using functions from microstructure.py
        # Use the unified depth_levels parameter for all calculations
        depth_levels = self.config.microstructure.depth_levels

        raw_obi = microstructure.calculate_order_book_imbalance(l2_snapshot, levels=depth_levels)
        raw_abs_spread, raw_rel_spread = microstructure.calculate_bid_ask_spread(l2_snapshot)
        raw_depth_ratio, raw_depth_pressure = microstructure.calculate_depth_metrics(l2_snapshot, levels=depth_levels)

        # Return results as a Series (column names match SignalCalculator expectations), including best bid/ask
        return pd.Series({
            'best_bid': best_bid,
            'best_ask': best_ask,
            f'raw_obi_{depth_levels}': raw_obi,
            f'raw_depth_ratio_{depth_levels}': raw_depth_ratio,
            f'raw_depth_pressure_{depth_levels}': raw_depth_pressure,
            'raw_spread_abs': raw_abs_spread,
            'raw_spread_rel': raw_rel_spread
        })


    def _integrate_microstructure_features(self) -> None:
        """Loads L2 data day-by-day, merges with OHLCV, calculates raw features."""
        if self.ohlcv_data.empty:
            logger.error("Cannot integrate microstructure features: OHLCV data is empty.")
            self.combined_data = pd.DataFrame()
            return

        logger.info("Starting microstructure feature integration...")
        integration_start_time = time.time()

        # Check if we should skip 1s feature file loading for Legacy System optimization
        should_skip_1s_features = self._should_skip_1s_feature_loading()
        if should_skip_1s_features:
            logger.info("PERFORMANCE OPTIMIZATION: Skipping 1s feature file loading for Legacy System")
            logger.info("Legacy System will use features from 1h OHLCV files and raw2/ microstructure processing")

            # For Legacy System, we still need to process raw2/ L2 data for microstructure features
            # but we skip the expensive 1s feature file loading

        # Get unique dates from OHLCV index
        unique_dates = self.ohlcv_data.index.normalize().unique()
        logger.info(f"Processing {len(unique_dates)} days for L2 integration.")

        all_features = []

        for current_date in unique_dates:
            date_str = current_date.strftime('%Y%m%d')
            day_start_time = time.time()
            logger.debug(f"Processing L2 for date: {date_str}")

            # Load the L2 segment for the current day
            l2_daily_df = self._load_l2_segment(date_str)

            # Filter OHLCV data for the current day
            ohlcv_daily_df = self.ohlcv_data[self.ohlcv_data.index.normalize() == current_date].copy()

            if ohlcv_daily_df.empty:
                logger.debug(f"No OHLCV data for {date_str}, skipping L2 merge.")
                continue

            if l2_daily_df is None or l2_daily_df.empty:
                logger.warning(f"No valid L2 data for {date_str}. Microstructure features will be NaN for this day.")
                # Create NaN columns for features for this day
                depth_levels = self.config.microstructure.depth_levels
                feature_cols = [
                    f'raw_obi_{depth_levels}',
                    f'raw_depth_ratio_{depth_levels}',
                    f'raw_depth_pressure_{depth_levels}',
                    'raw_spread_abs', 'raw_spread_rel'
                ]
                nan_features = pd.DataFrame(np.nan, index=ohlcv_daily_df.index, columns=feature_cols)
                all_features.append(nan_features)
                continue

            # Merge OHLCV with L2 using merge_asof
            # Ensure both DataFrames have UTC timestamp index
            logger.debug(f"Merging OHLCV ({ohlcv_daily_df.shape}) and L2 ({l2_daily_df.shape}) for {date_str} using merge_asof...")
            try:
                # Reset index temporarily for merge_asof compatibility if needed, or ensure index name is 'timestamp'
                # merge_asof works best if the merge key is a column, not the index.
                ohlcv_daily_df_reset = ohlcv_daily_df.reset_index()
                l2_daily_df_reset = l2_daily_df.reset_index()

                merged_daily = pd.merge_asof(
                    ohlcv_daily_df_reset, # Left DF (OHLCV)
                    l2_daily_df_reset,    # Right DF (L2)
                    on='timestamp',       # Merge key
                    direction='backward', # Find last L2 snapshot <= OHLCV time
                    tolerance=pd.Timedelta(minutes=5) # Optional: max time diff allowed (adjust as needed)
                )
                merged_daily.set_index('timestamp', inplace=True) # Restore index
                logger.debug(f"Merge successful for {date_str}. Shape: {merged_daily.shape}")

            except Exception as e:
                 logger.error(f"Error during merge_asof for {date_str}: {e}", exc_info=True)
                 # Handle error - skip day or fill with NaNs? Fill with NaNs.
                 depth_levels = self.config.microstructure.depth_levels
                 feature_cols = [
                     f'raw_obi_{depth_levels}',
                     f'raw_depth_ratio_{depth_levels}',
                     f'raw_depth_pressure_{depth_levels}',
                     'raw_spread_abs', 'raw_spread_rel'
                 ]
                 nan_features = pd.DataFrame(np.nan, index=ohlcv_daily_df.index, columns=feature_cols)
                 all_features.append(nan_features)
                 continue


            # Calculate features row-wise on the merged data
            logger.debug(f"Calculating microstructure features for {date_str}...")
            start_apply_time = time.time()
            # Use .progress_apply from tqdm if available and dataset is large
            # from tqdm.auto import tqdm
            # tqdm.pandas(desc=f"Calculating Features {date_str}")
            # daily_features = merged_daily.progress_apply(self._calculate_features_from_row, axis=1)
            daily_features = merged_daily.apply(self._calculate_features_from_row, axis=1)
            end_apply_time = time.time()
            logger.debug(f"Feature calculation for {date_str} took {end_apply_time - start_apply_time:.2f}s. Shape: {daily_features.shape}")

            # Ensure the index matches the original daily OHLCV index
            daily_features = daily_features.reindex(ohlcv_daily_df.index)
            all_features.append(daily_features)

            day_end_time = time.time()
            logger.debug(f"Finished processing day {date_str}. Took {day_end_time - day_start_time:.2f} seconds.")


        # Concatenate features from all days
        if not all_features:
             logger.error("No microstructure features were calculated.")
             self.combined_data = self.ohlcv_data.copy() # Fallback to just OHLCV
             # Add NaN columns so downstream doesn't break
             depth_levels = self.config.microstructure.depth_levels
             feature_cols = [
                 f'raw_obi_{depth_levels}',
                 f'raw_depth_ratio_{depth_levels}',
                 f'raw_depth_pressure_{depth_levels}',
                 'raw_spread_abs', 'raw_spread_rel'
             ]
             for col in feature_cols:
                  if col not in self.combined_data.columns:
                       self.combined_data[col] = np.nan
             return

        logger.info("Concatenating daily features...")
        features_df = pd.concat(all_features)
        logger.info(f"Concatenated features shape: {features_df.shape}")

        # --- START ADDED DEBUG LOGGING ---
        if not features_df.empty:
            # Check if columns exist before trying to access them
            raw_spread_abs_col = 'raw_spread_abs'
            raw_spread_rel_col = 'raw_spread_rel'
            logger.info(f"DEBUG: Checking NaN counts in concatenated features_df (Shape: {features_df.shape}) BEFORE merge:") # Added shape log

            if raw_spread_abs_col in features_df.columns:
                raw_spread_abs_nan_count = features_df[raw_spread_abs_col].isna().sum()
                logger.info(f"  - {raw_spread_abs_col}: {raw_spread_abs_nan_count} / {len(features_df)} ({raw_spread_abs_nan_count/len(features_df):.1%})")
            else:
                logger.warning(f"  - Column '{raw_spread_abs_col}' not found in features_df.")

            if raw_spread_rel_col in features_df.columns:
                raw_spread_rel_nan_count = features_df[raw_spread_rel_col].isna().sum()
                logger.info(f"  - {raw_spread_rel_col}: {raw_spread_rel_nan_count} / {len(features_df)} ({raw_spread_rel_nan_count/len(features_df):.1%})")
            else:
                 logger.warning(f"  - Column '{raw_spread_rel_col}' not found in features_df.")

            # Optionally log NaNs for other raw features too if needed
            # raw_obi_col = f'raw_obi_{self.config.microstructure.obi_levels}'
            # if raw_obi_col in features_df.columns:
            #     raw_obi_nan_count = features_df[raw_obi_col].isna().sum()
            #     logger.info(f"  - {raw_obi_col}: {raw_obi_nan_count} / {len(features_df)} ({raw_obi_nan_count/len(features_df):.1%})")
        else:
            logger.warning("DEBUG: features_df is empty before merge, cannot check NaN counts.")
        # --- END ADDED DEBUG LOGGING ---

        # --- DEBUG: Check self.ohlcv_data columns BEFORE JOIN ---
        logger.info(f"[DEBUG Pre-Join] Columns in self.ohlcv_data: {self.ohlcv_data.columns.tolist()}")
        if 'fear_greed_idx' not in self.ohlcv_data.columns:
             logger.warning("[DEBUG Pre-Join] 'fear_greed_idx' is MISSING from self.ohlcv_data before the join!")
        else:
             logger.info("[DEBUG Pre-Join] 'fear_greed_idx' is PRESENT in self.ohlcv_data before the join.")
        # --- END DEBUG ---
        self.combined_data = self.ohlcv_data.join(features_df, how='left')
        logger.info(f"Final combined data shape: {self.combined_data.shape}")

        # Only load 1s feature files for Modern System (continuous_gms detector)
        if not should_skip_1s_features:
            logger.info("Loading ATR and spread statistics from 1-second feature files for Modern System")
            self._integrate_feature_store_data()

        # Log NaN counts for the newly added raw features
        for col in features_df.columns:
            nan_count = self.combined_data[col].isna().sum()
            if nan_count > 0:
                logger.warning(f"Raw feature '{col}' has {nan_count} NaN values out of {len(self.combined_data)} ({nan_count/len(self.combined_data):.1%}). Check L2 data availability/merging.")

        integration_end_time = time.time()
        logger.info(f"Microstructure feature integration finished. Took {integration_end_time - integration_start_time:.2f} seconds.")


    def _integrate_feature_store_data(self) -> None:
        """
        Extract feature store integration logic into a separate method.
        This loads ATR and spread statistics from 1-second feature files.
        """
        # Check if we have ATR and spread statistics from feature files
        logger.info("Using ATR and spread statistics from 1-second feature files")

        # Try to load ATR and spread statistics from 1-second feature files
        try:
            # Get all unique dates from the OHLCV data
            start_date = self.ohlcv_data.index[0].date()
            end_date = self.ohlcv_data.index[-1].date()

            # Load 1-second feature data for all dates in the backtest period
            feature_store = FeatureStore(self.config)
            all_feature_data = []

            current_date = start_date
            while current_date <= end_date:
                date_str = current_date.strftime("%Y%m%d")
                try:
                    # Load critical features for continuous GMS detector and other strategies
                    # Based on actual schema from feature files
                    required_columns = [
                        "timestamp",
                        "atr_14_sec", "atr_percent_sec", "atr", "atr_percent",  # ATR features
                        "spread_mean", "spread_std",  # Spread statistics
                        "ma_slope_ema_30s",  # Momentum feature for continuous GMS
                        "ma_slope",  # Additional momentum feature
                        # OBI features (available in schema)
                        "raw_obi_5", "obi_smoothed_5", "obi_zscore_5",
                        "raw_obi_20", "obi_smoothed_20", "obi_zscore_20"
                    ]
                    daily_features = feature_store.load_1s(date_str, columns=required_columns)
                    if not daily_features.empty:
                        all_feature_data.append(daily_features)
                except Exception as e:
                    logger.warning(f"Could not load feature data for {date_str}: {e}")
                    # Try with minimal required columns if full load fails
                    try:
                        minimal_columns = ["timestamp", "atr_14_sec", "atr_percent_sec", "spread_mean", "spread_std", "ma_slope_ema_30s"]
                        logger.info(f"Attempting minimal column load: {minimal_columns}")
                        daily_features = feature_store.load_1s(date_str, columns=minimal_columns)
                        if not daily_features.empty:
                            all_feature_data.append(daily_features)
                    except Exception as e2:
                        logger.error(f"Even minimal column load failed for {date_str}: {e2}")

                # Move to next day
                current_date += timedelta(days=1)

            # Continue with the rest of the feature integration logic...
            self._process_feature_store_data(all_feature_data)

        except Exception as e:
            logger.warning(f"Error loading ATR from feature files: {e}")
            logger.warning("ATR column 'atr_14_sec' not found in feature data. GMS detector may not work properly.")

    def _process_feature_store_data(self, all_feature_data: list) -> None:
        """
        Process the loaded feature store data and integrate it with combined_data.
        This method handles the aggregation and merging of 1-second features.
        """
        # Combine all feature data
        if all_feature_data:
            feature_df = pd.concat(all_feature_data, ignore_index=True)
        else:
            feature_df = pd.DataFrame()

        if not feature_df.empty and "atr_14_sec" in feature_df.columns and "timestamp" in feature_df.columns:
            # Set timestamp as index and ensure it's timezone-naive
            feature_df['timestamp'] = pd.to_datetime(feature_df['timestamp'])
            if feature_df['timestamp'].dt.tz is not None:
                feature_df['timestamp'] = feature_df['timestamp'].dt.tz_localize(None)
            feature_df = feature_df.set_index('timestamp')

            # Define feature aggregation methods based on feature type
            # This fixes the hourly aggregation to preserve critical features
            FEATURE_AGGREGATION_METHODS = {
                # ATR features - use last value (final computed value of the hour)
                'atr_14_sec': 'last',
                'atr_percent_sec': 'last',
                'atr': 'last',           # Legacy
                'atr_percent': 'last',   # Legacy

                # Rolling statistics - preserve final computed values (CRITICAL FIX)
                'spread_mean': 'last',   # Use final value, not mean of means
                'spread_std': 'last',    # Use final value, not mean of stds

                # Momentum/trend features - use final computed values (CRITICAL FIX)
                'ma_slope_ema_30s': 'last',  # Critical for continuous GMS
                'ma_slope': 'last',

                # OBI features - use final computed values
                'raw_obi_5': 'last',
                'obi_smoothed_5': 'last',
                'obi_zscore_5': 'last',
                'raw_obi_20': 'last',
                'obi_smoothed_20': 'last',
                'obi_zscore_20': 'last',

                # Any other features that might be present
                'mid_price': 'last',
                'close': 'last',
                'volume': 'sum',  # Volume should be summed
            }

            # Build aggregation dictionary for available columns
            agg_dict = {}
            for col in feature_df.columns:
                if col in FEATURE_AGGREGATION_METHODS:
                    agg_dict[col] = FEATURE_AGGREGATION_METHODS[col]
                else:
                    # Default fallback for unknown features
                    agg_dict[col] = 'mean'
                    logger.debug(f"Using default 'mean' aggregation for unknown feature: {col}")

            # Resample 1-second data to 1-hour intervals using proper aggregation methods
            # CRITICAL: Use label='right' and closed='left' to prevent look-ahead bias
            # This ensures the 10:00:00 bar only contains data from 09:00:00-09:59:59
            logger.info(f"Aggregating {len(feature_df)} 1-second rows to hourly using feature-specific methods")
            resampled_features = feature_df.resample('1H', label='right', closed='left').agg(agg_dict).dropna()

            # Align with combined_data index (ensure timezone-naive)
            combined_index = self.combined_data.index
            if combined_index.tz is not None:
                combined_index = combined_index.tz_localize(None)

            # Merge all resampled features with combined_data
            logger.info(f"Merging {len(resampled_features.columns)} aggregated features into combined_data")
            for col in resampled_features.columns:
                # Use reindex to align with combined_data timestamps
                aligned_values = resampled_features[col].reindex(combined_index, method='ffill')
                self.combined_data[col] = aligned_values

                # Log feature statistics
                nan_count = aligned_values.isna().sum()
                if nan_count > 0:
                    logger.warning(f"Feature '{col}' has {nan_count} NaN values out of {len(aligned_values)} ({nan_count/len(aligned_values):.1%}) after aggregation")
                else:
                    logger.debug(f"Feature '{col}' successfully aggregated with 0 NaN values")

            # Add legacy ATR columns for backward compatibility
            if 'atr_14_sec' in self.combined_data.columns:
                self.combined_data['atr'] = self.combined_data['atr_14_sec']
            if 'atr_percent_sec' in self.combined_data.columns:
                self.combined_data['atr_percent'] = self.combined_data['atr_percent_sec']

            # Validate critical features for continuous GMS detector
            self._validate_aggregated_features()

            logger.info("Feature aggregation completed successfully")
        else:
            logger.warning("Required columns not found in feature data. GMS detector may not work properly.")

    def _should_skip_1s_feature_loading(self) -> bool:
        """
        Determine if 1s feature file loading should be skipped for Legacy System optimization.

        Returns True if:
        1. detector_type is 'granular_microstructure' (Legacy System)
        2. skip_l2_raw_processing_if_1h_features_exist flag is True

        The Legacy System doesn't need 1s feature files because it gets all required
        features from 1h OHLCV files and raw2/ microstructure processing.
        """
        # Only apply optimization to Legacy System
        if self.config.regime.detector_type != 'granular_microstructure':
            return False

        # Check if the optimization flag is enabled
        try:
            # Get the flag from detector-specific settings
            if (hasattr(self.config.regime, 'granular_microstructure') and
                self.config.regime.granular_microstructure is not None):
                skip_flag = self.config.regime.granular_microstructure.skip_l2_raw_processing_if_1h_features_exist
                logger.debug(f"Legacy System 1s feature optimization flag: {skip_flag}")
                return skip_flag
            else:
                # Fall back to default value (True for optimization)
                logger.debug("No detector-specific settings found, using default 1s feature optimization: True")
                return True

        except AttributeError as e:
            logger.warning(f"Could not access 1s feature optimization flag: {e}. Defaulting to True for performance.")
            return True

    # Removed _should_skip_l2_raw_processing - duplicate of _should_skip_1s_feature_loading
    # The 1s feature loading check serves the same purpose for optimization

    def _integrate_legacy_feature_store_data(self) -> None:
        """
        Integrate feature store data specifically for Legacy System optimization.
        This method ensures the Legacy System gets the features it needs without
        the expensive L2 processing.

        The Legacy System (GranularMicrostructureRegimeDetector) needs:
        - ATR features (atr_percent)
        - OBI features (obi_smoothed_5)
        - Spread statistics (spread_mean, spread_std)
        - Momentum features (ma_slope)

        These should come from the 1h OHLCV files or be calculated by the SignalEngine.
        """
        logger.info("Integrating features for Legacy System optimization")

        # Check what features are already available in the 1h OHLCV data
        available_features = self.combined_data.columns.tolist()
        logger.debug(f"Features available in 1h OHLCV data: {available_features}")

        # The key insight is that the Legacy System should work with the features
        # already present in the 1h OHLCV files. The SignalEngine will calculate
        # the derived signals (obi_smoothed_5, spread_mean, spread_std) from the
        # raw features that are already present.

        # For the Legacy System, we don't need to load 1s feature files
        # The 1h OHLCV files should contain all the necessary base features
        logger.info("Legacy System will use features from 1h OHLCV files")
        logger.info("SignalEngine will calculate derived signals (obi_smoothed_5, spread_mean, spread_std)")

    def get_ohlcv_data(self) -> pd.DataFrame:
        """Returns the combined OHLCV and raw microstructure feature data."""
        if self.combined_data.empty:
            logger.warning("Attempted to get combined data before loading or loading failed.")
            # Return raw OHLCV as fallback? Or empty? Let's return empty.
            return pd.DataFrame()
        # --- DEBUG: Check columns at DataHandler FINAL ---
        logger.info(f"[DEBUG DH Final] Columns in DataFrame right before return: {self.combined_data.columns.tolist()}")
        if 'fear_greed_idx' not in self.combined_data.columns:
            logger.error("[DEBUG DH Final] CRITICAL: 'fear_greed_idx' MISSING just before leaving DataHandler!")
        # --- END DEBUG ---
        return self.combined_data


    # --- Implement get_funding_rate ---
    def get_funding_rate(self, timestamp: pd.Timestamp) -> float:
        # Convert to UTC-naive for consistent comparison
        target_ts_utc = to_utc_naive(timestamp)
        assert target_ts_utc.tz is None, "Funding rate lookup requires UTC-naive timestamp"

        if self.funding_rates is not None and not self.funding_rates.empty:
            try:
                rate = self.funding_rates.asof(target_ts_utc)
                if pd.notna(rate): return float(rate)
                else: logger.warning(f"Funding rate lookup failed for {target_ts_utc} (asof). Using default.")
            except Exception as e: logger.error(f"Error looking up funding rate for {target_ts_utc}: {e}. Using default.")
        return self.default_funding_rate

    # --- Implement get_open_interest ---
    def get_open_interest(self, timestamp: pd.Timestamp) -> Optional[float]:
        # Convert to UTC-naive for consistent comparison
        target_ts_utc = to_utc_naive(timestamp)
        assert target_ts_utc.tz is None, "Open interest lookup requires UTC-naive timestamp"

        if self.open_interest is not None and not self.open_interest.empty:
            try:
                oi = self.open_interest.asof(target_ts_utc)
                if pd.notna(oi): return float(oi)
                else: logger.warning(f"OI lookup failed for {target_ts_utc} (asof)."); return None
            except Exception as e: logger.error(f"Error looking up OI for {target_ts_utc}: {e}"); return None
        else:
            if not self._warned_missing_oi: logger.warning("Historical OI data not available."); self._warned_missing_oi = True
            return None

    # --- Implement get_recent_liquidations ---
    def get_recent_liquidations(self, timestamp: pd.Timestamp, lookback_period: timedelta) -> Tuple[Optional[float], Optional[float]]:
        # Convert to UTC-naive for consistent comparison
        target_ts_utc = to_utc_naive(timestamp)
        assert target_ts_utc.tz is None, "Liquidations lookup requires UTC-naive timestamp"

        start_ts_utc = target_ts_utc - lookback_period
        if self.liquidations is not None and not self.liquidations.empty:
            try:
                # Assuming index is timestamp and columns 'side', 'size' exist
                mask = (self.liquidations.index >= start_ts_utc) & (self.liquidations.index < target_ts_utc)
                recent_liqs = self.liquidations.loc[mask]
                if recent_liqs.empty: return 0.0, 0.0
                # Adjust side logic based on actual data ('buy'/'sell' or 'long'/'short')
                long_liq_vol = recent_liqs[recent_liqs['side'].str.lower().isin(['buy', 'long'])]['size'].sum()
                short_liq_vol = recent_liqs[recent_liqs['side'].str.lower().isin(['sell', 'short'])]['size'].sum()
                return float(long_liq_vol), float(short_liq_vol)
            except Exception as e: logger.error(f"Error processing liquidations for {target_ts_utc}: {e}"); return None, None
        else:
            if not self._warned_missing_liq: logger.warning("Historical Liquidation data not available."); self._warned_missing_liq = True
            return None, None

    # --- Implement get_recent_close_prices ---
    def get_recent_close_prices(self, timestamp: pd.Timestamp, lookback_periods: int) -> Optional[pd.Series]:
        # Use self.combined_data now, but only need 'close' column
        data_source = self.combined_data if not self.combined_data.empty else self.ohlcv_data
        if data_source.empty: logger.error("Cannot get recent close prices: No data loaded."); return None
        if 'close' not in data_source.columns: logger.error("Cannot get recent close prices: 'close' column missing."); return None
        if lookback_periods <= 0: logger.error(f"Invalid lookback_periods: {lookback_periods}"); return None

        # Convert to UTC-naive for consistent comparison
        target_ts_utc = to_utc_naive(timestamp)
        assert target_ts_utc.tz is None, "Close prices lookup requires UTC-naive timestamp"

        try:
            if not data_source.index.is_monotonic_increasing:
                logger.warning("Data source index not monotonic increasing. Sorting.")
                data_source.sort_index(inplace=True)

            end_loc = data_source.index.get_indexer([target_ts_utc], method='ffill')[0]
            if end_loc == -1: # Handle case where timestamp is before the first index
                 logger.error(f"Timestamp {target_ts_utc} not found in index (or before first entry).")
                 return None

            start_loc = max(0, end_loc - lookback_periods + 1)
            recent_closes = data_source.iloc[start_loc : end_loc + 1]['close']

            if recent_closes.empty: logger.error(f"Extracted recent closes empty for {target_ts_utc}."); return None
            # if recent_closes.isna().any(): logger.warning(f"Recent closes for {target_ts_utc} contain NaNs.") # Allow NaNs

            logger.debug(f"Retrieved {len(recent_closes)} close prices ending at {recent_closes.index[-1]} for target {target_ts_utc}.")
            return recent_closes
        except IndexError: # Can happen if get_indexer returns -1 and slicing fails
             logger.error(f"IndexError getting recent prices for {target_ts_utc}. Timestamp likely out of bounds.")
             return None
        except Exception as e:
            logger.error(f"Error retrieving recent close prices for {target_ts_utc}: {e}", exc_info=True)
            return None

    def load_1m_candles(self, start: pd.Timestamp, end: pd.Timestamp) -> Optional[pd.DataFrame]:
        """
        Load 1-minute candles for execution refinement.
        
        Args:
            start: Start timestamp (inclusive)
            end: End timestamp (exclusive)
            
        Returns:
            DataFrame with 1-minute OHLCV candles or None if not available
        """
        # Convert to UTC-naive for consistent file naming
        start_utc = to_utc_naive(start)
        end_utc = to_utc_naive(end)
        
        # Path to 1-minute data
        minute_data_path = Path("/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled_l2/1m")
        
        if not minute_data_path.exists():
            logger.error(f"1-minute data path does not exist: {minute_data_path}")
            return None
            
        # Get date string for file lookup
        date_str = start_utc.strftime('%Y-%m-%d')
        file_path = minute_data_path / f"{date_str}_1m.parquet"
        
        if not file_path.exists():
            logger.warning(f"1-minute data file not found: {file_path}")
            return None
            
        try:
            # Load the parquet file
            df = pd.read_parquet(file_path)
            
            # Ensure timestamp column exists and is datetime
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                if df['timestamp'].dt.tz is not None:
                    df['timestamp'] = df['timestamp'].dt.tz_localize(None)
                df = df.set_index('timestamp')
            
            # Filter to requested time range
            df = df[(df.index >= start_utc) & (df.index < end_utc)]
            
            if df.empty:
                logger.warning(f"No 1-minute data found in range {start_utc} to {end_utc}")
                return None
                
            # Ensure required columns exist
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                logger.error(f"1-minute data missing required columns: {missing_cols}")
                return None
                
            logger.debug(f"Loaded {len(df)} 1-minute candles from {start_utc} to {end_utc}")
            return df
            
        except Exception as e:
            logger.error(f"Error loading 1-minute data from {file_path}: {e}", exc_info=True)
            return None

    def _validate_aggregated_features(self):
        """
        Validate that critical features are preserved after hourly aggregation.
        This ensures the continuous GMS detector can function properly.
        """
        # Define critical features for different detector types
        critical_features = {
            'continuous_gms': ['spread_mean', 'spread_std', 'ma_slope_ema_30s'],
            'atr_features': ['atr_14_sec', 'atr_percent_sec'],
            'legacy_atr': ['atr', 'atr_percent']
        }

        validation_results = {}

        for feature_group, features in critical_features.items():
            missing_features = []
            all_nan_features = []
            high_nan_features = []

            for feature in features:
                if feature not in self.combined_data.columns:
                    missing_features.append(feature)
                elif self.combined_data[feature].isna().all():
                    all_nan_features.append(feature)
                else:
                    nan_ratio = self.combined_data[feature].isna().sum() / len(self.combined_data)
                    if nan_ratio > 0.5:  # More than 50% NaN is concerning
                        high_nan_features.append((feature, f"{nan_ratio:.1%}"))

            validation_results[feature_group] = {
                'missing': missing_features,
                'all_nan': all_nan_features,
                'high_nan': high_nan_features
            }

        # Log validation results
        for feature_group, results in validation_results.items():
            if results['missing']:
                logger.warning(f"[VALIDATION] {feature_group}: Missing features: {results['missing']}")
            if results['all_nan']:
                logger.warning(f"[VALIDATION] {feature_group}: All-NaN features: {results['all_nan']}")
            if results['high_nan']:
                logger.warning(f"[VALIDATION] {feature_group}: High-NaN features: {results['high_nan']}")

            if not any([results['missing'], results['all_nan'], results['high_nan']]):
                logger.info(f"[VALIDATION] {feature_group}: All features validated successfully")

        # Special validation for continuous GMS critical features
        gms_critical = validation_results['continuous_gms']
        if gms_critical['missing'] or gms_critical['all_nan']:
            logger.error(f"[VALIDATION] CRITICAL: Continuous GMS detector will fail due to missing/NaN features!")
            logger.error(f"Missing: {gms_critical['missing']}, All-NaN: {gms_critical['all_nan']}")
        else:
            logger.info("[VALIDATION] Continuous GMS detector critical features are available")