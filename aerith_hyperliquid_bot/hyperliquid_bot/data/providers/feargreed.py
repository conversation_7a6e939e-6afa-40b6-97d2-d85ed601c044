# File: aerith_hyperliquid_bot/hyperliquid_bot/data/providers/feargreed.py

import httpx
import pandas as pd
import logging
from typing import Optional # Ensure Optional is imported

logger = logging.getLogger(__name__)

FEAR_GREED_API_URL = "https://api.alternative.me/fng/"

def fetch_fear_greed_index(limit: int = 0) -> Optional[pd.DataFrame]:
    """
    Fetches the Fear & Greed Index data from alternative.me API.

    Args:
        limit (int): Number of results to return. 0 means all available historical data.

    Returns:
        Optional[pd.DataFrame]: DataFrame with 'timestamp' (UTC, as index)
                                 and 'fear_greed_idx', or None if fetching fails.
    """
    try:
        # Use limit=0 to get all available data for backtesting consistency
        url = f"{FEAR_GREED_API_URL}?limit={limit}&format=json"
        logger.info(f"Fetching Fear & Greed data from {url}...")
        # Using httpx for potential future async use, but run synchronously here
        with httpx.Client() as client:
            # Increased timeout for potentially large dataset when limit=0
            response = client.get(url, timeout=60.0)
            response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
            data = response.json()

        if 'data' not in data or not isinstance(data['data'], list):
            logger.error("Received unexpected data format from Fear & Greed API: 'data' key missing or not a list.")
            return None

        df = pd.DataFrame(data['data'])
        if df.empty:
            logger.warning("Fear & Greed API returned no data.")
            # Return an empty DataFrame with the expected structure
            return pd.DataFrame(columns=['timestamp', 'fear_greed_idx']).set_index('timestamp')

        # Convert 'value' to numeric, coercing errors to NaN
        df['fear_greed_idx'] = pd.to_numeric(df['value'], errors='coerce')

        # Convert timestamp string (seconds since epoch) to datetime objects (UTC-naive)
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True).dt.tz_localize(None)

        # Set timestamp as index, sort by time, keep only necessary column
        df = df[['timestamp', 'fear_greed_idx']].set_index('timestamp').sort_index()

        # Drop rows where 'fear_greed_idx' conversion failed (became NaN)
        initial_len = len(df)
        df.dropna(subset=['fear_greed_idx'], inplace=True)
        if len(df) < initial_len:
            logger.warning(f"Dropped {initial_len - len(df)} rows with non-numeric Fear & Greed values.")

        # Convert the index to Date only AFTER sorting and cleaning, for potential daily merge logic clarity.
        # However, keeping it as DatetimeIndex is better for merge_asof.
        # df.index = df.index.date # Let's keep it as DatetimeIndex[tz=UTC] for merge_asof

        logger.info(f"Successfully fetched and processed {len(df)} Fear & Greed data points.")
        return df

    except httpx.RequestError as exc:
        logger.error(f"HTTP Request Error while fetching Fear & Greed data: {exc}")
        return None
    except httpx.HTTPStatusError as exc:
        logger.error(f"HTTP Status Error while fetching Fear & Greed data: {exc.response.status_code} - {exc}")
        return None
    except Exception as e:
        # Catching generic Exception to log unexpected errors during processing
        logger.exception(f"An unexpected error occurred during Fear & Greed data fetching/processing: {e}")
        return None

# --- Optional: Example usage for direct testing ---
# if __name__ == '__main__':
#     logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
#
#     print("Fetching last 10 Fear & Greed records...")
#     fg_data_limited = fetch_fear_greed_index(limit=10)
#     if fg_data_limited is not None:
#         print(fg_data_limited.head())
#         print(fg_data_limited.tail())
#         print(fg_data_limited.info())
#     else:
#         print("Failed to fetch limited data.")
#
#     print("\nFetching all Fear & Greed records (this might take a moment)...")
#     fg_data_all = fetch_fear_greed_index(limit=0)
#     if fg_data_all is not None:
#         print(f"\nFetched {len(fg_data_all)} total records.")
#         print(fg_data_all.head())
#         print(fg_data_all.tail())
#         print(fg_data_all.info())
#     else:
#         print("Failed to fetch all data.")
# --- End Optional Example ---