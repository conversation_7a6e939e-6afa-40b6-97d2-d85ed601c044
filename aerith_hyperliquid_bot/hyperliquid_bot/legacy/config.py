"""
Legacy System Configuration
===========================
FROZEN configuration for the legacy trading system.
These values produce 191 trades with 243.03% ROI.

DO NOT MODIFY these values unless you fully understand the implications.
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class LegacyMicrostructureConfig:
    """Legacy microstructure configuration - FROZEN."""
    depth_levels: int = 5
    obi_levels: int = 5
    obi_smoothing_window: int = 10
    obi_smoothing_alpha: float = 0.2
    gms_obi_strong_confirm_thresh: float = 0.2
    gms_obi_weak_confirm_thresh: float = 0.05
    
    # TF-v2 filters (not used in baseline but preserved)
    tf_filter_obi_threshold_long: float = 0.1
    tf_filter_obi_threshold_short: float = -0.1
    tf_filter_funding_threshold_long: float = 0.0005
    tf_filter_funding_threshold_short: float = -0.0005


@dataclass
class LegacyIndicatorConfig:
    """Legacy indicator configuration - FROZEN."""
    # Trend Following EMAs
    tf_fast_window: int = 20
    tf_slow_window: int = 50
    use_tf_medium_ewma: bool = False
    tf_medium_window: int = 35
    
    # Forecast and volatility
    forecast_window: int = 50
    atr_window: int = 14
    tf_max_entry_volatility_pct: float = 0.02
    low_forecast_threshold: float = 0.01
    
    # Other indicators
    sma_window: int = 30
    ma_slope_window: int = 30
    adx_window: int = 14
    adx_threshold: float = 30.0


@dataclass
class LegacyRegimeConfig:
    """Legacy regime detection configuration - FROZEN."""
    detector_type: str = "granular_microstructure"
    use_filter: bool = True
    use_enhanced_filter: bool = True
    use_chop_filter: bool = True
    
    # CRITICAL: These thresholds produce the baseline performance
    gms_vol_high_thresh: float = 0.0092
    gms_vol_low_thresh: float = 0.0055
    gms_mom_strong_thresh: float = 100.0
    gms_mom_weak_thresh: float = 50.0
    
    # Spread thresholds
    gms_spread_std_high_thresh: float = 0.0005
    gms_spread_mean_low_thresh: float = 0.0001
    gms_spread_mean_thresh_mode: str = "fixed"
    gms_spread_std_thresh_mode: str = "fixed"
    
    # State mapping
    use_strict_strategy_filtering: bool = True
    gms_use_three_state_mapping: bool = True
    map_weak_bear_to_bear: bool = False  # CRITICAL: Weak_Bear_Trend -> CHOP
    
    # Optional features (not used in baseline)
    gms_use_adx_confirmation: bool = False
    gms_use_funding_confirmation: bool = False


@dataclass  
class LegacyStrategyConfig:
    """Legacy strategy configuration - FROZEN."""
    use_tf_v2: bool = True
    use_tf_v3: bool = False
    use_mean_reversion: bool = False
    use_mean_reversion_microstructure: bool = False
    
    # Risk management
    tf_warmup_bars: int = 120
    
    # Optional filters (not used in baseline)
    tf_use_obi_filter: bool = False
    tf_use_funding_filter: bool = False


@dataclass
class LegacyTFV3Config:
    """Legacy TF-v3 config (used for risk only in legacy system)."""
    risk_frac: float = 0.25  # CRITICAL: 25% risk per trade
    max_notional: float = 0.0
    
    # Stop/target configuration
    atr_trail_k: float = 1.5
    stop_loss_atr_mult: float = 1.5
    take_profit_atr_mult: float = 3.0
    
    # Other parameters (not used by legacy)
    ema_fast: int = 8
    ema_slow: int = 21
    atr_period: int = 14
    gms_max_age_sec: int = 600


@dataclass
class LegacyDataConfig:
    """Legacy data configuration - FROZEN."""
    base_path: str = "/Volumes/M2Crypto/hyperliquid_data"
    
    @property
    def l2_data_root(self) -> str:
        """Legacy L2 data path (raw2/)."""
        return f"{self.base_path}/raw2"
    
    @property  
    def ohlcv_base_path(self) -> str:
        """OHLCV data path."""
        return f"{self.base_path}/resampled_l2"


@dataclass
class LegacyRiskConfig:
    """Legacy risk configuration - FROZEN."""
    base_leverage: float = 5.0
    max_leverage: float = 10.0
    min_leverage: float = 1.0
    asset_max_leverage: float = 50.0
    
    # Dynamic risk (not used in baseline)
    dynamic_risk_adjustment: bool = False
    bull_bear_risk_factor: float = 1.0
    
    # Position limits
    max_positions: int = 3
    max_position_size_pct: float = 1.0
    
    # Margin
    margin_mode: str = "cross"
    maintenance_margin_buffer: float = 0.02


@dataclass
class LegacyConfig:
    """Complete legacy system configuration - FROZEN."""
    # Sub-configurations
    microstructure: LegacyMicrostructureConfig = field(default_factory=LegacyMicrostructureConfig)
    indicators: LegacyIndicatorConfig = field(default_factory=LegacyIndicatorConfig)
    regime: LegacyRegimeConfig = field(default_factory=LegacyRegimeConfig)
    strategies: LegacyStrategyConfig = field(default_factory=LegacyStrategyConfig)
    tf_v3: LegacyTFV3Config = field(default_factory=LegacyTFV3Config)
    data_paths: LegacyDataConfig = field(default_factory=LegacyDataConfig)
    risk: LegacyRiskConfig = field(default_factory=LegacyRiskConfig)
    
    # System identification
    system_mode: str = "legacy"
    
    # Other configs (minimal needed for legacy)
    timezone: str = "UTC"
    log_level: str = "INFO"
    backtest_start: str = "2024-01-01"
    backtest_end: str = "2024-12-31"
    
    @classmethod
    def load_frozen(cls) -> "LegacyConfig":
        """
        Load the frozen legacy configuration.
        
        This method returns the exact configuration that produces:
        - 191 trades
        - 243.03% ROI
        """
        return cls()
    
    def validate(self) -> bool:
        """Validate critical configuration values."""
        checks = [
            # Detector
            self.regime.detector_type == "granular_microstructure",
            
            # Strategy
            self.strategies.use_tf_v2 == True,
            self.strategies.use_tf_v3 == False,
            
            # Risk
            self.tf_v3.risk_frac == 0.25,
            
            # Critical thresholds
            self.regime.gms_vol_high_thresh == 0.0092,
            self.regime.gms_vol_low_thresh == 0.0055,
            self.regime.gms_mom_strong_thresh == 100.0,
            self.regime.gms_mom_weak_thresh == 50.0,
            
            # State mapping
            self.regime.map_weak_bear_to_bear == False,
        ]
        
        return all(checks)