"""
Legacy Trend Following (TF-v2) Strategy.

This is the FROZEN implementation that produces 180 trades with 215% ROI.
DO NOT MODIFY unless you fully understand the implications.

This strategy uses EMAs and forecast to generate trend-following signals,
with optional OBI and funding rate filters.
"""

import logging
import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, List, Tuple, Literal
from datetime import datetime

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.interfaces import IStrategy
from .registry import legacy_strategy


@legacy_strategy("tf_v2", version="1.0", frozen=True)
class LegacyTFV2Strategy(IStrategy):
    """
    Legacy implementation of the Trend Following v2 Strategy.
    
    This is the exact implementation that produces the baseline performance:
    - 180 trades
    - 215% ROI  
    - 25% risk per trade (not 2% as documentation suggests)
    
    Strategy logic:
    - LONG: Fast EMA > Slow EMA AND Forecast > 0
    - SHORT: Fast EMA < Slow EMA AND Forecast < 0
    - Optional medium EMA for additional confirmation
    - Optional OBI and funding rate filters
    """
    
    def __init__(self, config: Config):
        """Initialize the legacy TF-v2 strategy with frozen configuration."""
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.strategy_name = "legacy_tf_v2"
        
        # Diagnostic counters
        self.eval_count = 0
        self.fail_missing_signal = 0
        self.success_entry_long = 0
        self.success_entry_short = 0
        self.fail_condition = 0
        self.fail_forecast_filter = 0
        self.fail_volatility_filter = 0
        self.fail_obi_filter_long = 0
        self.fail_obi_filter_short = 0
        self.fail_funding_filter_long = 0
        self.fail_funding_filter_short = 0
        
        # Log configuration
        self.logger.info("Legacy TF-v2 Strategy Initialized (FROZEN)")
        self.logger.info(f"  - Use Medium EMA: {self.config.indicators.use_tf_medium_ewma}")
        self.logger.info(f"  - OBI Filter Enabled: {self.config.strategies.tf_use_obi_filter}")
        self.logger.info(f"  - Funding Filter Enabled: {self.config.strategies.tf_use_funding_filter}")
        
    def evaluate_entry(self, signals: Dict[str, Any], regime: str) -> Optional[Dict[str, Any]]:
        """
        Evaluate whether to enter a position.
        
        This is the main entry point that implements the IStrategy interface.
        """
        direction, strategy_info = self.evaluate(signals)
        
        if direction:
            return {
                'direction': direction,
                'confidence': 1.0,  # Legacy system doesn't use confidence scores
                'strategy_info': strategy_info or {}
            }
        return None
        
    def check_exit(self, signals: Dict[str, Any], position: Dict[str, Any]) -> Optional[str]:
        """
        Check if current position should be exited.
        
        Legacy TF-v2 uses simple EMA crossover for exits.
        """
        # Legacy exit logic is handled in backtester
        return None
        
    def get_position_size(self, signals: Dict[str, Any], direction: str) -> float:
        """
        Calculate position size for entry.
        
        CRITICAL: Legacy system uses 25% risk per trade, not 2%!
        """
        # Position sizing is handled by risk manager using config.strategies.tf_v3.risk_frac
        # which should be set to 0.25 for legacy system
        return 0.25  # FROZEN at 25% risk
        
    def get_strategy_name(self) -> str:
        """Get strategy identifier."""
        return self.strategy_name
    
    @property
    def required_signals(self) -> List[str]:
        """Define signals needed for TF logic and optional filters."""
        signals_needed = ["forecast", "tf_ewma_fast", "tf_ewma_slow", "regime", "close", "atr_tf"]
        
        if self.config.indicators.use_tf_medium_ewma:
            signals_needed.append("tf_ewma_medium")
            
        # Add signals for optional filters
        if self.config.strategies.tf_use_obi_filter:
            obi_depth = self.config.microstructure.obi_levels
            signals_needed.append(f"obi_smoothed_{obi_depth}")
            signals_needed.append("obi_smoothed")  # Legacy fallback
            
        if self.config.strategies.tf_use_funding_filter:
            signals_needed.append("funding_rate")
            
        return list(set(signals_needed))
        
    def evaluate(self, signals: Dict) -> Tuple[Optional[Literal["long", "short"]], Optional[Dict]]:
        """
        Legacy TF-v2 evaluation logic - DO NOT MODIFY.
        
        This is the exact implementation that produces 180 trades.
        """
        self.eval_count += 1
        cfg_indicators = self.config.indicators
        cfg_regime = self.config.regime
        cfg_strategies = self.config.strategies
        cfg_micro = self.config.microstructure
        
        # Check for required signals
        current_req_signals = self.required_signals
        missing_or_nan_signals = [s for s in current_req_signals if pd.isna(signals.get(s))]
        
        if missing_or_nan_signals:
            self.fail_missing_signal += 1
            current_regime = signals.get('regime')
            self.logger.info(
                f"TF Evaluate @ {signals.get('timestamp')}: Regime={current_regime}, "
                f"Missing signals: {missing_or_nan_signals}"
            )
            return None, None
            
        # Extract core signals
        fc = signals["forecast"]
        fast = signals["tf_ewma_fast"]
        slow = signals["tf_ewma_slow"]
        current_regime = signals["regime"]
        price = signals["close"]
        atr = signals["atr_tf"]
        
        # Debug logging (important for legacy system)
        self.logger.info(
            f"TF DEBUG @ {signals.get('timestamp')}: Regime={current_regime}, "
            f"Fast={fast:.2f}, Slow={slow:.2f}, "
            f"Forecast={fc:.2f}, Fast-Slow={fast-slow:.2f}"
        )
        
        # Determine base entry signal
        base_signal: Optional[Literal["long", "short"]] = None
        
        if cfg_indicators.use_tf_medium_ewma:
            med = signals.get("tf_ewma_medium")
            self.logger.info(f"TF DEBUG Medium EMA: {med:.2f}, Fast-Med={fast-med:.2f}, Med-Slow={med-slow:.2f}")
            
            if fast > med > slow and fc > 0:
                base_signal = "long"
                self.logger.info("TF DEBUG: LONG signal generated - Fast > Med > Slow and FC > 0")
            elif fast < med < slow and fc < 0:
                base_signal = "short"
                self.logger.info("TF DEBUG: SHORT signal generated - Fast < Med < Slow and FC < 0")
            else:
                self.logger.info(
                    f"TF DEBUG: NO signal - Medium EMA conditions not met: "
                    f"Fast={fast:.2f}, Med={med:.2f}, Slow={slow:.2f}, FC={fc:.2f}"
                )
        else:
            if fast > slow and fc > 0:
                base_signal = "long"
                self.logger.info("TF DEBUG: LONG signal generated - Fast > Slow and FC > 0")
            elif fast < slow and fc < 0:
                base_signal = "short"
                self.logger.info("TF DEBUG: SHORT signal generated - Fast < Slow and FC < 0")
            else:
                self.logger.info(
                    f"TF DEBUG: NO signal - Basic EMA conditions not met: "
                    f"Fast={fast:.2f}, Slow={slow:.2f}, FC={fc:.2f}"
                )
                
        # If no base signal, exit
        if base_signal is None:
            self.fail_condition += 1
            return None, None
            
        # Apply Hurst-specific filters (legacy system doesn't use Hurst)
        apply_hurst_filters = (cfg_regime.detector_type == 'hurst' and current_regime == 'Persistent')
        if apply_hurst_filters:
            # Forecast filter
            forecast_threshold = cfg_indicators.low_forecast_threshold
            if abs(fc) < forecast_threshold:
                self.fail_forecast_filter += 1
                return None, None
                
            # Volatility filter
            max_vol_pct = cfg_indicators.tf_max_entry_volatility_pct
            current_vol_pct = atr / price if price > 1e-9 else np.inf
            if current_vol_pct >= max_vol_pct:
                self.fail_volatility_filter += 1
                return None, None
                
        # Apply OBI and Funding filters
        final_signal = base_signal
        
        # OBI Filter
        if cfg_strategies.tf_use_obi_filter:
            obi_depth = cfg_micro.obi_levels
            obi_signal_name = f"obi_smoothed_{obi_depth}"
            obi = signals.get(obi_signal_name)
            
            # Fallback to legacy name
            if pd.isna(obi):
                self.logger.warning(f"Dynamic OBI signal '{obi_signal_name}' not found, falling back to 'obi_smoothed'")
                obi = signals.get("obi_smoothed")
                
            obi_long_thresh = cfg_micro.tf_filter_obi_threshold_long
            obi_short_thresh = cfg_micro.tf_filter_obi_threshold_short
            obi_passed = False
            
            if final_signal == "long":
                if obi >= obi_long_thresh:
                    obi_passed = True
                else:
                    self.fail_obi_filter_long += 1
            elif final_signal == "short":
                if obi <= obi_short_thresh:
                    obi_passed = True
                else:
                    self.fail_obi_filter_short += 1
                    
            if not obi_passed:
                final_signal = None
                
        # Funding Filter
        if final_signal is not None and cfg_strategies.tf_use_funding_filter:
            funding = signals.get("funding_rate")
            funding_long_thresh = cfg_micro.tf_filter_funding_threshold_long
            funding_short_thresh = cfg_micro.tf_filter_funding_threshold_short
            funding_passed = False
            
            if final_signal == "long":
                # Allow long if funding is LESS THAN OR EQUAL TO threshold
                if funding <= funding_long_thresh:
                    funding_passed = True
                else:
                    self.fail_funding_filter_long += 1
            elif final_signal == "short":
                # Allow short if funding is GREATER THAN OR EQUAL TO threshold
                if funding >= funding_short_thresh:
                    funding_passed = True
                else:
                    self.fail_funding_filter_short += 1
                    
            if not funding_passed:
                final_signal = None
                
        # Return final signal
        if final_signal == "long":
            self.success_entry_long += 1
            self.logger.info("TF DEBUG: Final LONG signal returned after all filters")
            return "long", None
        elif final_signal == "short":
            self.success_entry_short += 1
            self.logger.info("TF DEBUG: Final SHORT signal returned after all filters")
            return "short", None
        else:
            self.logger.info(f"TF DEBUG: Signal was blocked by filters. Base signal was {base_signal}")
            return None, None
            
    def log_evaluation_summary(self):
        """Logs a summary for the Trend Following strategy."""
        if self.eval_count == 0: 
            return
            
        total_success = self.success_entry_long + self.success_entry_short
        total_hurst_fails = self.fail_forecast_filter + self.fail_volatility_filter
        total_obi_fails = self.fail_obi_filter_long + self.fail_obi_filter_short
        total_funding_fails = self.fail_funding_filter_long + self.fail_funding_filter_short
        total_fails = self.fail_missing_signal + self.fail_condition + total_hurst_fails + total_obi_fails + total_funding_fails
        
        self.logger.info(f"  • Strategy:                 {self.strategy_name}")
        self.logger.info(f"  • Total Evaluations:        {self.eval_count}")
        self.logger.info(f"  • Entry Signals:            {total_success} (Long: {self.success_entry_long}, Short: {self.success_entry_short})")
        
        signal_rate = total_success / self.eval_count if self.eval_count > 0 else 0
        self.logger.info(f"  • Signal Rate:              {signal_rate:.1%}")
        
        if total_fails > 0:
            fail_rate = total_fails / self.eval_count if self.eval_count > 0 else 0
            self.logger.info(f"  • Failures/Blocks:          {total_fails} ({fail_rate:.1%})")
            
            if self.fail_missing_signal > 0:
                fail_perc = self.fail_missing_signal / self.eval_count
                self.logger.info(f"    - Missing Signal:         {self.fail_missing_signal} ({fail_perc:.1%})")
                
            if self.fail_condition > 0:
                fail_perc = self.fail_condition / self.eval_count
                self.logger.info(f"    - Base Condition:         {self.fail_condition} ({fail_perc:.1%})")
                
            if total_hurst_fails > 0:
                fail_perc = total_hurst_fails / self.eval_count
                self.logger.info(f"    - Hurst Filters:          {total_hurst_fails} ({fail_perc:.1%})")
                
            if self.config.strategies.tf_use_obi_filter and total_obi_fails > 0:
                fail_perc = total_obi_fails / self.eval_count
                self.logger.info(f"    - OBI Filter:             {total_obi_fails} ({fail_perc:.1%})")
                
            if self.config.strategies.tf_use_funding_filter and total_funding_fails > 0:
                fail_perc = total_funding_fails / self.eval_count
                self.logger.info(f"    - Funding Filter:         {total_funding_fails} ({fail_perc:.1%})")