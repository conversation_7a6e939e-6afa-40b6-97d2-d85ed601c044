# hyperliquid_bot/utils/data_utils.py
# Utility functions for data manipulation and processing

import logging
import pandas as pd
from typing import Optional

# Setup logging
logger = logging.getLogger(__name__)

def deduplicate(df: pd.DataFrame) -> pd.DataFrame:
    """
    Drop duplicate index rows, keeping the first, then sort.
    
    Args:
        df: DataFrame to deduplicate
        
    Returns:
        Deduplicated and sorted DataFrame
    """
    if df.index.has_duplicates:
        logger.warning(f"Found {df.index.duplicated().sum()} duplicate indices in DataFrame. Keeping first occurrence.")
        df = df[~df.index.duplicated(keep='first')]
    return df.sort_index()

def verify_atr_availability(df: pd.DataFrame, atr_column: str = 'atr_14', min_required: int = 14) -> bool:
    """
    Verify that ATR values are available in the DataFrame.
    
    Args:
        df: DataFrame containing ATR values
        atr_column: Name of the ATR column
        min_required: Minimum number of non-NaN values required
        
    Returns:
        True if ATR is available, False otherwise
    """
    if atr_column not in df.columns:
        logger.warning(f"ATR column '{atr_column}' not found in DataFrame")
        return False
    
    missing_atr = df[atr_column].isna().sum()
    if missing_atr >= min_required:
        logger.warning(f"ATR never warmed up ({missing_atr} NaNs)")
        return False
    
    logger.info(f"ATR verification passed: {missing_atr} NaNs out of {len(df)} rows")
    return True
