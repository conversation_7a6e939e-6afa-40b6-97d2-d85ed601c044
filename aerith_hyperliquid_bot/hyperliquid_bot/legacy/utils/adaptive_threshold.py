"""
Adaptive threshold calculation for GMS detector.

This module provides causal, rolling percentile-based thresholds that adapt
to recent market conditions without look-ahead bias.
"""

from collections import deque
import numpy as np
from typing import Tuple, Optional
import logging


class AdaptiveThreshold:
    """
    Maintains a rolling window of values and computes percentile-based thresholds
    in a causal manner (no look-ahead bias).
    
    The percentile calculation uses only past values, excluding the current sample
    to ensure no look-ahead bias.
    """
    
    def __init__(self, low_pct: float, high_pct: float, window_len: int):
        """
        Initialize adaptive threshold calculator.
        
        Args:
            low_pct: Low percentile threshold (0.0 to 1.0)
            high_pct: High percentile threshold (0.0 to 1.0) 
            window_len: Maximum number of samples to keep in rolling window
        """
        if not (0.0 <= low_pct <= 1.0):
            raise ValueError(f"low_pct must be between 0.0 and 1.0, got {low_pct}")
        if not (0.0 <= high_pct <= 1.0):
            raise ValueError(f"high_pct must be between 0.0 and 1.0, got {high_pct}")
        if low_pct >= high_pct:
            raise ValueError(f"low_pct ({low_pct}) must be less than high_pct ({high_pct})")
        if window_len <= 0:
            raise ValueError(f"window_len must be positive, got {window_len}")
            
        self.low_pct = low_pct
        self.high_pct = high_pct
        self.window_len = window_len
        self.buffer = deque(maxlen=window_len)
        
        self.logger = logging.getLogger(f"{__name__}.AdaptiveThreshold")
        
    def update(self, value: float) -> Tuple[Optional[float], Optional[float]]:
        """
        Update the rolling window with a new value and compute thresholds.
        
        The percentile calculation is causal - it uses only the values in the buffer
        BEFORE adding the current value, ensuring no look-ahead bias.
        
        Args:
            value: New value to add to the rolling window
            
        Returns:
            Tuple of (low_threshold, high_threshold). Returns (None, None) if
            insufficient data for reliable percentile calculation.
        """
        # Compute thresholds using current buffer (before adding new value)
        # This ensures causal computation with no look-ahead bias
        if len(self.buffer) == 0:
            # No historical data yet
            self.buffer.append(value)
            return None, None
            
        # Convert buffer to numpy array for percentile calculation
        arr = np.fromiter(self.buffer, dtype=float)
        
        # Filter out NaN values
        valid_arr = arr[~np.isnan(arr)]
        
        if len(valid_arr) == 0:
            # No valid historical data
            self.buffer.append(value)
            return None, None
            
        # Compute percentiles using only historical data (causal)
        try:
            low_thresh = np.percentile(valid_arr, self.low_pct * 100)
            high_thresh = np.percentile(valid_arr, self.high_pct * 100)
        except Exception as e:
            self.logger.warning(f"Error computing percentiles: {e}")
            self.buffer.append(value)
            return None, None
            
        # Add new value to buffer after computing thresholds
        self.buffer.append(value)
        
        return low_thresh, high_thresh
        
    def get_buffer_size(self) -> int:
        """Get current number of values in the buffer."""
        return len(self.buffer)
        
    def get_buffer_stats(self) -> dict:
        """Get statistics about the current buffer for debugging."""
        if len(self.buffer) == 0:
            return {"size": 0, "min": None, "max": None, "mean": None}
            
        arr = np.fromiter(self.buffer, dtype=float)
        valid_arr = arr[~np.isnan(arr)]
        
        if len(valid_arr) == 0:
            return {"size": len(self.buffer), "valid_size": 0, "min": None, "max": None, "mean": None}
            
        return {
            "size": len(self.buffer),
            "valid_size": len(valid_arr),
            "min": float(np.min(valid_arr)),
            "max": float(np.max(valid_arr)),
            "mean": float(np.mean(valid_arr))
        }
        
    def reset(self):
        """Clear the rolling window buffer."""
        self.buffer.clear()
