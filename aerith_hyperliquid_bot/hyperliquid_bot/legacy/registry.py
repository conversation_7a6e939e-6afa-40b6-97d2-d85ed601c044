"""
Legacy Component Registry
=========================
Isolated registry for legacy system components.
This ensures legacy components can't accidentally be mixed with modern ones.
"""

import logging
from typing import Type, Any, Optional

from ..core.registry import ComponentRegistry, get_registry
from ..core.interfaces import IRegimeDetector, IStrategy, IDataLoader


logger = logging.getLogger(__name__)


# Create isolated legacy registry
_legacy_registry = ComponentRegistry()


def get_legacy_registry() -> ComponentRegistry:
    """Get the legacy component registry."""
    return _legacy_registry


# Legacy-specific decorators
def legacy_detector(name: str, version: str = "1.0", frozen: bool = True):
    """
    Decorator to register a legacy regime detector.
    
    Args:
        name: Unique name for the detector
        version: Version string
        frozen: Whether this component is frozen (should always be True for legacy)
    """
    def decorator(cls: Type[IRegimeDetector]) -> Type[IRegimeDetector]:
        metadata = {
            'version': version,
            'frozen': frozen,
            'system': 'legacy'
        }
        _legacy_registry.register(IRegimeDetector, name, cls, metadata)
        
        # Also register in global registry with legacy_ prefix
        global_name = f"legacy_{name}"
        get_registry().register(IRegimeDetector, global_name, cls, metadata)
        
        return cls
    return decorator


def legacy_strategy(name: str, version: str = "1.0", frozen: bool = True):
    """
    Decorator to register a legacy strategy.
    
    Args:
        name: Unique name for the strategy
        version: Version string
        frozen: Whether this component is frozen (should always be True for legacy)
    """
    def decorator(cls: Type[IStrategy]) -> Type[IStrategy]:
        metadata = {
            'version': version,
            'frozen': frozen,
            'system': 'legacy'
        }
        _legacy_registry.register(IStrategy, name, cls, metadata)
        
        # Also register in global registry with legacy_ prefix
        global_name = f"legacy_{name}"
        get_registry().register(IStrategy, global_name, cls, metadata)
        
        return cls
    return decorator


def legacy_data_loader(name: str, version: str = "1.0", frozen: bool = True):
    """
    Decorator to register a legacy data loader.
    
    Args:
        name: Unique name for the data loader
        version: Version string
        frozen: Whether this component is frozen (should always be True for legacy)
    """
    def decorator(cls: Type[IDataLoader]) -> Type[IDataLoader]:
        metadata = {
            'version': version,
            'frozen': frozen,
            'system': 'legacy'
        }
        _legacy_registry.register(IDataLoader, name, cls, metadata)
        
        # Also register in global registry with legacy_ prefix
        global_name = f"legacy_{name}"
        get_registry().register(IDataLoader, global_name, cls, metadata)
        
        return cls
    return decorator


def get_legacy_component(interface_type: Type, name: str) -> Optional[Type]:
    """
    Get a component from the legacy registry.
    
    Args:
        interface_type: The interface type
        name: The component name
        
    Returns:
        The component class or None if not found
    """
    try:
        return _legacy_registry.get(interface_type, name)
    except KeyError:
        logger.warning(f"Legacy component not found: {interface_type.__name__}.{name}")
        return None