"""
Legacy Trading System Components.

This module contains the FROZEN legacy implementations that produce
the baseline performance of 180 trades with 215% ROI.

DO NOT MODIFY these components unless you fully understand the implications.
"""

from .detector import LegacyGranularMicrostructureDetector
from .strategy import LegacyTFV2Strategy
from .data_loader import LegacyDataLoader

__all__ = [
    'LegacyGranularMicrostructureDetector',
    'LegacyTFV2Strategy',
    'LegacyDataLoader'
]