"""
Legacy Granular Microstructure Regime Detector.

This is the FROZEN implementation that produces 180 trades with 215% ROI.
DO NOT MODIFY unless you fully understand the implications.

This detector uses microstructure features (OBI, Spread), momentum (MA Slope),
and volatility (ATR%) to classify market regimes.
"""

import logging
import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, List
from datetime import datetime

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.interfaces import IRegimeDetector
from .registry import legacy_detector
from .utils.state_mapping import (
    GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND,
    GMS_STATE_HIGH_VOL_RANGE, GMS_STATE_LOW_VOL_RANGE,
    GMS_STATE_UNCERTAIN, GMS_STATE_STRONG_BEAR_TREND, 
    GMS_STATE_UNKNOWN, get_valid_gms_states
)


@legacy_detector("granular_microstructure", version="1.0", frozen=True)
class LegacyGranularMicrostructureDetector(IRegimeDetector):
    """
    Legacy implementation of the Granular Microstructure Regime Detector.
    
    This is the exact implementation that produces the baseline performance:
    - 180 trades
    - 215% ROI
    - Stable, proven logic
    
    Output states:
    - Strong_Bull_Trend: Strong momentum up with confirmation
    - Weak_Bull_Trend: Weaker momentum up or less confirmation
    - High_Vol_Range: Choppy/ranging market with high volatility
    - Low_Vol_Range: Choppy/ranging market with low volatility
    - Uncertain: Conflicting signals or transition state
    - Weak_Bear_Trend: Weaker momentum down or less confirmation
    - Strong_Bear_Trend: Strong momentum down with confirmation
    - TIGHT_SPREAD: Special case when spread is too tight for reliable signals
    """
    
    def __init__(self, config: Config):
        """Initialize the legacy detector with frozen configuration."""
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Store config sections for quick access
        self.cfg_regime = config.regime
        self.cfg_micro = config.microstructure
        self.cfg_indicators = config.indicators
        self.depth_levels = self.cfg_micro.depth_levels
        
        # CRITICAL: These are the exact thresholds that produce 180 trades
        # DO NOT CHANGE THESE VALUES
        self.vol_high_thresh = getattr(self.cfg_regime, 'gms_vol_high_thresh', 0.0092)  # FROZEN
        self.vol_low_thresh = getattr(self.cfg_regime, 'gms_vol_low_thresh', 0.0055)   # FROZEN
        self.mom_strong_thresh = getattr(self.cfg_regime, 'gms_mom_strong_thresh', 100.0)  # FROZEN
        self.mom_weak_thresh = getattr(self.cfg_regime, 'gms_mom_weak_thresh', 50.0)     # FROZEN
        self.spread_std_high_thresh = getattr(self.cfg_regime, 'gms_spread_std_high_thresh', 0.0005)
        self.spread_mean_low_thresh = getattr(self.cfg_regime, 'gms_spread_mean_low_thresh', 0.0001)
        
        # Spread configuration
        self.spread_mean_mode = getattr(self.cfg_regime, 'gms_spread_mean_thresh_mode', 'fixed')
        self.spread_std_mode = getattr(self.cfg_regime, 'gms_spread_std_thresh_mode', 'fixed')
        self.spread_mean_low_pctile = getattr(self.cfg_regime, 'gms_spread_mean_low_percentile', 0.25)
        self.spread_std_high_pctile = getattr(self.cfg_regime, 'gms_spread_std_high_percentile', 0.75)
        
        # Volatility threshold mode
        self.vol_thresh_mode = getattr(self.cfg_regime, 'gms_vol_thresh_mode', 'fixed')
        
        # OBI thresholds
        self.obi_strong_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_strong_confirm_thresh', 0.2)
        self.obi_weak_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_weak_confirm_thresh', 0.05)
        
        # Optional confirmations
        self.use_adx_confirmation = getattr(self.cfg_regime, 'gms_use_adx_confirmation', False)
        self.adx_threshold = getattr(self.cfg_indicators, 'adx_threshold', 30.0)
        self.use_funding_confirmation = getattr(self.cfg_regime, 'gms_use_funding_confirmation', False)
        self.funding_extreme_pos_thresh = getattr(self.cfg_regime, 'gms_funding_extreme_positive_thresh', 0.001)
        self.funding_extreme_neg_thresh = getattr(self.cfg_regime, 'gms_funding_extreme_negative_thresh', -0.001)
        
        # Advanced features
        self.obi_zscore_threshold = getattr(self.cfg_regime, 'gms_obi_zscore_threshold', 0)
        spread_pct_gate_cfg = getattr(self.cfg_regime, 'gms_spread_percentile_gate', None)
        self.spread_percentile_gate = spread_pct_gate_cfg if isinstance(spread_pct_gate_cfg, (int, float)) else 0
        
        depth_slope_cfg = getattr(self.cfg_regime, 'gms_depth_slope_thin_limit', None)
        self.depth_slope_thin_limit = depth_slope_cfg if isinstance(depth_slope_cfg, (int, float)) else -np.inf
        
        depth_skew_cfg = getattr(self.cfg_regime, 'gms_depth_skew_thresh', None)
        self.depth_skew_thresh = depth_skew_cfg if isinstance(depth_skew_cfg, (int, float)) else -np.inf
        
        spread_trend_cfg = getattr(self.cfg_regime, 'gms_spread_trend_lookback', None)
        self.spread_trend_lookback = spread_trend_cfg if isinstance(spread_trend_cfg, (int, float)) else 0
        
        adaptive_obi_cfg = getattr(self.cfg_regime, 'gms_adaptive_obi_base', None)
        self.adaptive_obi_base = adaptive_obi_cfg if isinstance(adaptive_obi_cfg, (int, float)) else 0
        
        conf_bars_cfg = getattr(self.cfg_regime, 'gms_confirmation_bars', None)
        if isinstance(conf_bars_cfg, (int, float)) and conf_bars_cfg > 0:
            self.confirmation_bars = int(conf_bars_cfg)
        else:
            self.confirmation_bars = 0
            
        self.tight_spread_fallback_percentile = getattr(self.cfg_regime, 'gms_tight_spread_fallback_percentile', None)
        
        # State for confirmation logic
        self.previous_regime = None
        self.confirmation_counter = 0
        
        # Log configuration
        self.logger.info("Legacy Granular Microstructure Detector Initialized (FROZEN)")
        self.logger.info(f"  - Vol Thresh (Low/High ATR%): {self.vol_low_thresh:.4f} / {self.vol_high_thresh:.4f}")
        self.logger.info(f"  - Mom Thresh (Weak/Strong MA Slope): {self.mom_weak_thresh:.2f} / {self.mom_strong_thresh:.2f}")
        self.logger.info(f"  - OBI Confirm Thresh (Weak/Strong): {self.obi_weak_confirm_thresh:.3f} / {self.obi_strong_confirm_thresh:.3f}")
        self.logger.info(f"  - Depth Levels: {self.depth_levels}")
        
    def detect_regime(self, signals: Dict[str, Any], timestamp: Optional[datetime] = None) -> str:
        """
        Detect market regime from signals.
        
        This is the main entry point that implements the IRegimeDetector interface.
        """
        return self.get_regime(signals)
        
    def get_allowed_states(self, strategy_type: str) -> List[str]:
        """
        Get list of regime states that allow trading for a strategy.
        
        For legacy trend following (TF-v2):
        - Strong_Bull_Trend → LONG allowed
        - Strong_Bear_Trend → SHORT allowed
        """
        if strategy_type == 'trend_following':
            return [GMS_STATE_STRONG_BULL_TREND, GMS_STATE_STRONG_BEAR_TREND]
        else:
            # Other strategies might have different allowed states
            return []
            
    def get_confidence(self) -> float:
        """
        Get confidence level of current regime detection.
        
        Legacy detector uses confirmation bars as confidence proxy.
        """
        if self.confirmation_bars > 0:
            return min(self.confirmation_counter / self.confirmation_bars, 1.0)
        return 1.0  # Full confidence if no confirmation required
        
    def get_raw_state(self, features: Dict[str, Any], timestamp: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Get raw regime state with all details.
        
        This provides the full regime information including risk suppression.
        """
        regime = self.detect_regime(features, timestamp)
        
        # Risk is suppressed in ranging/uncertain states
        risk_suppressed = regime in [
            GMS_STATE_HIGH_VOL_RANGE,
            GMS_STATE_LOW_VOL_RANGE,
            GMS_STATE_UNCERTAIN,
            "TIGHT_SPREAD"
        ]
        
        return {
            'state': regime,
            'risk_suppressed': risk_suppressed,
            'confidence': self.get_confidence(),
            'timestamp': timestamp or features.get('timestamp')
        }
    
    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None) -> str:
        """
        Legacy regime detection logic - DO NOT MODIFY.
        
        This is the exact implementation that produces 180 trades.
        """
        if not self.cfg_regime.use_filter:
            return "Filter_Off"
            
        # Retrieve core signals
        atr_pct = signals.get('atr_percent', np.nan)
        ma_slope = signals.get('ma_slope', np.nan)
        obi_smooth = signals.get(f'obi_smoothed_{self.depth_levels}', np.nan)
        spread_mean = signals.get('spread_mean', np.nan)
        spread_std = signals.get('spread_std', np.nan)
        
        # Check for NaN in core signals
        if pd.isna(atr_pct) or pd.isna(ma_slope) or pd.isna(obi_smooth) or pd.isna(spread_mean) or pd.isna(spread_std):
            self.logger.warning(f"NaN in core signals - returning Unknown")
            return GMS_STATE_UNKNOWN
            
        # Optional signals
        adx = signals.get('adx', np.nan) if self.use_adx_confirmation else np.nan
        funding_rate = signals.get('funding_rate', np.nan) if self.use_funding_confirmation else np.nan
        
        # Determine OBI condition
        obi_condition = 'NONE'
        
        # Check OBI thresholds
        if obi_smooth > self.obi_strong_confirm_thresh:
            obi_condition = 'STRONG'
        elif obi_smooth > self.obi_weak_confirm_thresh:
            obi_condition = 'WEAK'
        elif obi_smooth < -self.obi_strong_confirm_thresh:
            obi_condition = 'STRONG'
        elif obi_smooth < -self.obi_weak_confirm_thresh:
            obi_condition = 'WEAK'
            
        # Determine market condition
        market_condition = 'NORMAL'
        
        # Check spread conditions
        if spread_std >= self.spread_std_high_thresh:
            market_condition = 'CHOPPY'
            
        # Determine potential regime
        potential_regime = GMS_STATE_UNCERTAIN
        
        # Check volatility
        is_vol_high = atr_pct >= self.vol_high_thresh
        is_vol_low = atr_pct <= self.vol_low_thresh
        
        # High volatility regime
        if is_vol_high:
            if abs(ma_slope) < self.mom_weak_thresh or market_condition == 'CHOPPY':
                potential_regime = GMS_STATE_HIGH_VOL_RANGE
                
        # Low volatility regime
        elif is_vol_low:
            if abs(ma_slope) < self.mom_weak_thresh and spread_mean <= self.spread_mean_low_thresh:
                potential_regime = GMS_STATE_LOW_VOL_RANGE
                
        # Check momentum-based regimes
        if potential_regime == GMS_STATE_UNCERTAIN:
            is_bullish = ma_slope > 0
            is_strong_mom = abs(ma_slope) >= self.mom_strong_thresh
            is_weak_mom = abs(ma_slope) >= self.mom_weak_thresh
            
            # Optional confirmations
            adx_confirms = (not self.use_adx_confirmation) or (not pd.isna(adx) and adx >= self.adx_threshold)
            funding_confirms = (not self.use_funding_confirmation) or \
                             (is_bullish and not pd.isna(funding_rate) and funding_rate < self.funding_extreme_pos_thresh) or \
                             (not is_bullish and not pd.isna(funding_rate) and funding_rate > self.funding_extreme_neg_thresh)
            
            if is_strong_mom:
                # Strong momentum requires strong OBI and confirmations
                if is_bullish and obi_condition == 'STRONG' and adx_confirms and funding_confirms:
                    potential_regime = GMS_STATE_STRONG_BULL_TREND
                elif not is_bullish and obi_condition == 'STRONG' and adx_confirms and funding_confirms:
                    potential_regime = GMS_STATE_STRONG_BEAR_TREND
                # Downgrade to weak trend if confirmations don't align
                elif is_bullish and obi_condition in ['STRONG', 'WEAK']:
                    potential_regime = GMS_STATE_WEAK_BULL_TREND
                elif not is_bullish and obi_condition in ['STRONG', 'WEAK']:
                    potential_regime = "Weak_Bear_Trend"
                    
            elif is_weak_mom:
                # Weak momentum requires at least weak OBI
                if is_bullish and obi_condition in ['STRONG', 'WEAK']:
                    potential_regime = "Weak_Bull_Trend"
                elif not is_bullish and obi_condition in ['STRONG', 'WEAK']:
                    potential_regime = "Weak_Bear_Trend"
                    
        # Apply confirmation bars logic if enabled
        final_regime = potential_regime
        
        if self.confirmation_bars > 0:
            if potential_regime == self.previous_regime:
                self.confirmation_counter += 1
            else:
                self.previous_regime = potential_regime
                self.confirmation_counter = 1
                
                if self.confirmation_bars == 1:
                    final_regime = potential_regime
                else:
                    final_regime = GMS_STATE_UNCERTAIN
                    
            if self.confirmation_counter >= self.confirmation_bars:
                final_regime = potential_regime
            else:
                final_regime = GMS_STATE_UNCERTAIN
                
        # Validate final output state
        valid_states = get_valid_gms_states()
        if final_regime not in valid_states:
            self.logger.error(f"Invalid regime state: {final_regime}")
            return GMS_STATE_UNKNOWN
            
        return final_regime
        
    @property
    def required_signals(self) -> List[str]:
        """Returns the list of signals needed by the legacy detector."""
        signals_needed = [
            'timestamp',
            'atr_percent',
            'ma_slope',
            f'obi_smoothed_{self.depth_levels}',
            'spread_mean',
            'spread_std'
        ]
        
        if self.use_adx_confirmation:
            signals_needed.append('adx')
        if self.use_funding_confirmation:
            signals_needed.append('funding_rate')
            
        return signals_needed