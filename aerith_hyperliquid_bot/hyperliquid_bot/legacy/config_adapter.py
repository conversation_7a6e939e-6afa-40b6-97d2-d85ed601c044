"""
Configuration Adapter for Legacy System
======================================
Bridges between the isolated LegacyConfig and the shared Config interface.
"""

from typing import Any, Dict, Optional
from ..config.settings import Config
from .config import LegacyConfig


class LegacyConfigAdapter:
    """
    Adapts LegacyConfig to the Config interface expected by components.
    
    This allows legacy components to work with the isolated configuration
    while maintaining compatibility with the existing codebase.
    """
    
    def __init__(self, legacy_config: Optional[LegacyConfig] = None):
        """Initialize with a legacy configuration."""
        self._legacy = legacy_config or LegacyConfig.load_frozen()
        self._adapted = None
        
    def to_config(self) -> Config:
        """
        Convert LegacyConfig to the shared Config format.
        
        Returns:
            Config object with legacy values
        """
        if self._adapted is not None:
            return self._adapted
            
        # Create a config dict matching the expected structure
        config_dict = {
            # System identification
            'system_mode': 'legacy',
            
            # Microstructure section
            'microstructure': {
                'depth_levels': self._legacy.microstructure.depth_levels,
                'obi_levels': self._legacy.microstructure.obi_levels,
                'obi_smoothing_window': self._legacy.microstructure.obi_smoothing_window,
                'obi_smoothing_alpha': self._legacy.microstructure.obi_smoothing_alpha,
                'gms_obi_strong_confirm_thresh': self._legacy.microstructure.gms_obi_strong_confirm_thresh,
                'gms_obi_weak_confirm_thresh': self._legacy.microstructure.gms_obi_weak_confirm_thresh,
                'tf_filter_obi_threshold_long': self._legacy.microstructure.tf_filter_obi_threshold_long,
                'tf_filter_obi_threshold_short': self._legacy.microstructure.tf_filter_obi_threshold_short,
                'tf_filter_funding_threshold_long': self._legacy.microstructure.tf_filter_funding_threshold_long,
                'tf_filter_funding_threshold_short': self._legacy.microstructure.tf_filter_funding_threshold_short,
            },
            
            # Indicators section
            'indicators': {
                'tf_fast_window': self._legacy.indicators.tf_fast_window,
                'tf_slow_window': self._legacy.indicators.tf_slow_window,
                'use_tf_medium_ewma': self._legacy.indicators.use_tf_medium_ewma,
                'tf_medium_window': self._legacy.indicators.tf_medium_window,
                'forecast_window': self._legacy.indicators.forecast_window,
                'atr_window': self._legacy.indicators.atr_window,
                'tf_max_entry_volatility_pct': self._legacy.indicators.tf_max_entry_volatility_pct,
                'low_forecast_threshold': self._legacy.indicators.low_forecast_threshold,
                'sma_window': self._legacy.indicators.sma_window,
                'ma_slope_window': self._legacy.indicators.ma_slope_window,
                'adx_window': self._legacy.indicators.adx_window,
                'adx_threshold': self._legacy.indicators.adx_threshold,
            },
            
            # Regime section
            'regime': {
                'detector_type': self._legacy.regime.detector_type,
                'use_filter': self._legacy.regime.use_filter,
                'use_enhanced_filter': self._legacy.regime.use_enhanced_filter,
                'use_chop_filter': self._legacy.regime.use_chop_filter,
                'gms_vol_high_thresh': self._legacy.regime.gms_vol_high_thresh,
                'gms_vol_low_thresh': self._legacy.regime.gms_vol_low_thresh,
                'gms_mom_strong_thresh': self._legacy.regime.gms_mom_strong_thresh,
                'gms_mom_weak_thresh': self._legacy.regime.gms_mom_weak_thresh,
                'gms_spread_std_high_thresh': self._legacy.regime.gms_spread_std_high_thresh,
                'gms_spread_mean_low_thresh': self._legacy.regime.gms_spread_mean_low_thresh,
                'gms_spread_mean_thresh_mode': self._legacy.regime.gms_spread_mean_thresh_mode,
                'gms_spread_std_thresh_mode': self._legacy.regime.gms_spread_std_thresh_mode,
                'use_strict_strategy_filtering': self._legacy.regime.use_strict_strategy_filtering,
                'gms_use_three_state_mapping': self._legacy.regime.gms_use_three_state_mapping,
                'map_weak_bear_to_bear': self._legacy.regime.map_weak_bear_to_bear,
                'gms_use_adx_confirmation': self._legacy.regime.gms_use_adx_confirmation,
                'gms_use_funding_confirmation': self._legacy.regime.gms_use_funding_confirmation,
            },
            
            # Strategies section
            'strategies': {
                'use_tf_v2': self._legacy.strategies.use_tf_v2,
                'use_tf_v3': self._legacy.strategies.use_tf_v3,
                'use_mean_reversion': self._legacy.strategies.use_mean_reversion,
                'use_mean_reversion_microstructure': self._legacy.strategies.use_mean_reversion_microstructure,
                'tf_warmup_bars': self._legacy.strategies.tf_warmup_bars,
                'tf_use_obi_filter': self._legacy.strategies.tf_use_obi_filter,
                'tf_use_funding_filter': self._legacy.strategies.tf_use_funding_filter,
            },
            
            # TF-v3 section (risk parameters)
            'tf_v3': {
                'risk_frac': self._legacy.tf_v3.risk_frac,
                'max_notional': self._legacy.tf_v3.max_notional,
                'atr_trail_k': self._legacy.tf_v3.atr_trail_k,
                'stop_loss_atr_mult': self._legacy.tf_v3.stop_loss_atr_mult,
                'take_profit_atr_mult': self._legacy.tf_v3.take_profit_atr_mult,
                'ema_fast': self._legacy.tf_v3.ema_fast,
                'ema_slow': self._legacy.tf_v3.ema_slow,
                'atr_period': self._legacy.tf_v3.atr_period,
                'gms_max_age_sec': self._legacy.tf_v3.gms_max_age_sec,
            },
            
            # Data paths
            'data_paths': {
                'base_path': self._legacy.data_paths.base_path,
                'l2_data_root': self._legacy.data_paths.l2_data_root,
                'ohlcv_base_path': self._legacy.data_paths.ohlcv_base_path,
            },
            
            # Risk section
            'risk': {
                'base_leverage': self._legacy.risk.base_leverage,
                'max_leverage': self._legacy.risk.max_leverage,
                'min_leverage': self._legacy.risk.min_leverage,
                'asset_max_leverage': self._legacy.risk.asset_max_leverage,
                'dynamic_risk_adjustment': self._legacy.risk.dynamic_risk_adjustment,
                'bull_bear_risk_factor': self._legacy.risk.bull_bear_risk_factor,
                'max_positions': self._legacy.risk.max_positions,
                'max_position_size_pct': self._legacy.risk.max_position_size_pct,
                'margin_mode': self._legacy.risk.margin_mode,
                'maintenance_margin_buffer': self._legacy.risk.maintenance_margin_buffer,
            },
            
            # Other required fields
            'timezone': self._legacy.timezone,
            'log_level': self._legacy.log_level,
            'backtest': {
                'start': self._legacy.backtest_start,
                'end': self._legacy.backtest_end,
            },
            
            # GMS section (for compatibility)
            'gms': {
                'detector_type': 'granular_microstructure',
                'auto_thresholds': False,
            },
            
            # State mapping file (empty to trigger default behavior)
            'gms_state_collapse_map_file': '',
        }
        
        # Create Config instance from dict
        # Note: This assumes Config can be created from a dict
        # If not, we'll need to use the actual Config constructor
        self._adapted = Config(**self._flatten_dict(config_dict))
        return self._adapted
    
    def _flatten_dict(self, d: Dict[str, Any], parent_key: str = '') -> Dict[str, Any]:
        """
        Flatten nested dictionary for Config initialization.
        
        Some Config implementations expect flat key structure.
        """
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}.{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key).items())
            else:
                items.append((new_key, v))
        return dict(items)
    
    @staticmethod
    def create_from_yaml(yaml_path: str) -> 'LegacyConfigAdapter':
        """
        Create adapter from a YAML file (for compatibility).
        
        In the isolated system, we ignore the YAML and use frozen values.
        """
        # Always return frozen config for legacy system
        return LegacyConfigAdapter()
    
    def validate(self) -> bool:
        """Validate the configuration."""
        return self._legacy.validate()