"""
Legacy Data Loader for raw2/ files.

This is the FROZEN implementation that loads data exactly as the working system expects.
DO NOT MODIFY unless you fully understand the implications.

This loader reads from:
- raw2/ directory for L2 microstructure data (37 features)
- resampled_l2/1h/ for OHLCV data
"""

import logging
import pandas as pd
import numpy as np
from typing import List, Optional, Any, Tuple
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.interfaces import IDataLoader
from .registry import legacy_data_loader
from .features import microstructure
from .utils.time import to_utc_naive, vectorized_to_utc_naive
from .utils.data_utils import deduplicate


@legacy_data_loader("raw2", version="1.0", frozen=True)
class LegacyDataLoader(IDataLoader):
    """
    Legacy data loader that reads from raw2/ and resampled_l2/1h/ directories.
    
    This loader produces the exact data format expected by the legacy system:
    - OHLCV data from 1h resampled files
    - L2 microstructure features from raw2/ parquet files
    - 37 total features as expected by granular_microstructure detector
    
    CRITICAL: The field mapping 'imbalance' → 'volume_imbalance' happens here!
    """
    
    def __init__(self, config: Config):
        """Initialize the legacy data loader."""
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Data directories
        # Use data_paths from config instead of directories
        self.l2_dir = Path(config.data_paths.l2_data_root)  # Already points to raw2
        self.ohlcv_dir = Path(config.data_paths.ohlcv_base_path) / "1h"
        self.data_dir = self.l2_dir.parent  # Parent of raw2
        
        # File patterns
        self.l2_file_pattern = "{date_str}_raw2.parquet"
        self.ohlcv_file_pattern = "{date_str}_1h.parquet"
        
        # Data storage
        self.ohlcv_data = pd.DataFrame()
        self.combined_data = pd.DataFrame()
        
        self.logger.info("Legacy Data Loader Initialized (FROZEN)")
        self.logger.info(f"  - L2 Directory: {self.l2_dir}")
        self.logger.info(f"  - OHLCV Directory: {self.ohlcv_dir}")
        
    def load_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """
        Load market data for specified date range.
        
        This is the main entry point that implements the IDataLoader interface.
        """
        self.logger.info(f"Loading legacy data from {start_date} to {end_date}")
        
        # Load OHLCV data
        self._load_ohlcv_data(start_date, end_date)
        
        # Load and integrate L2 microstructure features
        self._integrate_microstructure_features()
        
        # CRITICAL: Map 'imbalance' field to 'volume_imbalance' for compatibility
        if 'imbalance' in self.combined_data.columns:
            self.logger.info("Mapping 'imbalance' field to 'volume_imbalance' for compatibility")
            self.combined_data['volume_imbalance'] = self.combined_data['imbalance']
        
        return self.combined_data
        
    def get_required_columns(self) -> List[str]:
        """
        Get list of required columns for legacy system.
        
        These are the columns that must exist in the data for the system to work.
        """
        return [
            # OHLCV columns
            'open', 'high', 'low', 'close', 'volume',
            # Microstructure features from raw2
            f'raw_obi_{self.config.microstructure.depth_levels}',
            f'raw_depth_ratio_{self.config.microstructure.depth_levels}',
            f'raw_depth_pressure_{self.config.microstructure.depth_levels}',
            'raw_spread_abs', 'raw_spread_rel',
            # Field mapping
            'imbalance',  # Will be mapped to volume_imbalance
        ]
        
    def validate_data(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        Validate loaded data meets legacy requirements.
        """
        issues = []
        
        # Check for required columns
        required_cols = self.get_required_columns()
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            issues.append(f"Missing required columns: {missing_cols}")
            
        # Check for data
        if df.empty:
            issues.append("DataFrame is empty")
            
        # Check for NaNs in critical columns
        critical_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in critical_cols:
            if col in df.columns:
                nan_count = df[col].isna().sum()
                if nan_count > 0:
                    issues.append(f"Column '{col}' has {nan_count} NaN values")
                    
        is_valid = len(issues) == 0
        return is_valid, issues
        
    def _load_ohlcv_data(self, start_date: datetime, end_date: datetime) -> None:
        """Load OHLCV data from resampled_l2/1h/ files."""
        self.logger.info(f"Loading OHLCV data from {self.ohlcv_dir}")
        
        all_dfs = []
        current_date = start_date
        
        while current_date < end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            file_path = self.ohlcv_dir / self.ohlcv_file_pattern.format(date_str=date_str)
            
            if file_path.exists():
                try:
                    df = pd.read_parquet(file_path)
                    
                    # Ensure UTC timezone-naive timestamps
                    if 'timestamp' in df.columns and df['timestamp'].dtype == 'object':
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                    if 'timestamp' in df.columns:
                        df['timestamp'] = vectorized_to_utc_naive(df['timestamp'])
                        df.set_index('timestamp', inplace=True)
                    elif df.index.name != 'timestamp':
                        df.index.name = 'timestamp'
                        
                    # Ensure UTC timezone-naive index
                    df.index = vectorized_to_utc_naive(df.index)
                    
                    all_dfs.append(df)
                    self.logger.debug(f"Loaded OHLCV for {date_str}: {len(df)} rows")
                    
                except Exception as e:
                    self.logger.error(f"Error loading OHLCV file {file_path}: {e}")
                    
            else:
                self.logger.warning(f"OHLCV file not found: {file_path}")
                
            current_date += timedelta(days=1)
            
        # Concatenate all dataframes
        if all_dfs:
            self.ohlcv_data = pd.concat(all_dfs)
            self.ohlcv_data = deduplicate(self.ohlcv_data, keep='last')
            self.ohlcv_data.sort_index(inplace=True)
            self.logger.info(f"Loaded {len(self.ohlcv_data)} total OHLCV rows")
        else:
            self.logger.error("No OHLCV data loaded!")
            self.ohlcv_data = pd.DataFrame()
            
    def _integrate_microstructure_features(self) -> None:
        """Load L2 data from raw2/ and calculate microstructure features."""
        if self.ohlcv_data.empty:
            self.logger.error("Cannot integrate microstructure features: OHLCV data is empty")
            self.combined_data = pd.DataFrame()
            return
            
        self.logger.info("Starting microstructure feature integration from raw2/")
        
        # Get unique dates from OHLCV index
        unique_dates = self.ohlcv_data.index.normalize().unique()
        self.logger.info(f"Processing {len(unique_dates)} days for L2 integration")
        
        all_features = []
        
        for current_date in unique_dates:
            date_str = current_date.strftime('%Y%m%d')
            
            # Load L2 data for the day
            l2_daily_df = self._load_l2_segment(date_str)
            
            # Filter OHLCV data for the current day
            ohlcv_daily_df = self.ohlcv_data[self.ohlcv_data.index.normalize() == current_date].copy()
            
            if ohlcv_daily_df.empty:
                continue
                
            if l2_daily_df is None or l2_daily_df.empty:
                self.logger.warning(f"No L2 data for {date_str}")
                # Create NaN features
                nan_features = self._create_nan_features(ohlcv_daily_df.index)
                all_features.append(nan_features)
                continue
                
            # Merge OHLCV with L2 using merge_asof
            try:
                ohlcv_reset = ohlcv_daily_df.reset_index()
                l2_reset = l2_daily_df.reset_index()
                
                merged_daily = pd.merge_asof(
                    ohlcv_reset,
                    l2_reset,
                    on='timestamp',
                    direction='backward',
                    tolerance=pd.Timedelta(minutes=5)
                )
                merged_daily.set_index('timestamp', inplace=True)
                
                # Calculate microstructure features
                daily_features = merged_daily.apply(self._calculate_features_from_row, axis=1)
                daily_features = daily_features.reindex(ohlcv_daily_df.index)
                all_features.append(daily_features)
                
            except Exception as e:
                self.logger.error(f"Error processing {date_str}: {e}")
                nan_features = self._create_nan_features(ohlcv_daily_df.index)
                all_features.append(nan_features)
                
        # Concatenate all features
        if all_features:
            features_df = pd.concat(all_features)
            self.combined_data = self.ohlcv_data.join(features_df, how='left')
            self.logger.info(f"Final combined data shape: {self.combined_data.shape}")
        else:
            self.logger.error("No microstructure features calculated")
            self.combined_data = self.ohlcv_data.copy()
            
    def _load_l2_segment(self, date_str: str) -> Optional[pd.DataFrame]:
        """Load L2 data for a specific date from raw2/."""
        file_path = self.l2_dir / self.l2_file_pattern.format(date_str=date_str)
        
        if not file_path.exists():
            self.logger.warning(f"L2 file not found: {file_path}")
            return None
            
        try:
            df = pd.read_parquet(file_path)
            
            # Ensure timestamp column exists and is properly formatted
            if 'timestamp' not in df.columns:
                self.logger.error(f"No timestamp column in {file_path}")
                return None
                
            # Convert to datetime if needed
            if df['timestamp'].dtype == 'object':
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                
            # Ensure UTC timezone-naive
            df['timestamp'] = vectorized_to_utc_naive(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            
            # Sort by timestamp
            df.sort_index(inplace=True)
            
            self.logger.debug(f"Loaded L2 data for {date_str}: {len(df)} rows")
            return df
            
        except Exception as e:
            self.logger.error(f"Error loading L2 file {file_path}: {e}")
            return None
            
    def _calculate_features_from_row(self, row: pd.Series) -> pd.Series:
        """
        Calculate microstructure features from a single row.
        
        This replicates the exact feature calculation used by the legacy system.
        """
        depth_levels = self.config.microstructure.depth_levels
        
        # Check if we have bid/ask columns
        has_l2_data = all(f'{side}s_{i}' in row for side in ['bid', 'ask'] for i in range(depth_levels))
        
        if not has_l2_data:
            return self._create_nan_features_series()
            
        try:
            # Extract bid/ask data
            bids = [(row[f'bids_{i}'], row[f'bids_{i}_size']) for i in range(depth_levels) 
                    if pd.notna(row[f'bids_{i}']) and pd.notna(row[f'bids_{i}_size'])]
            asks = [(row[f'asks_{i}'], row[f'asks_{i}_size']) for i in range(depth_levels)
                    if pd.notna(row[f'asks_{i}']) and pd.notna(row[f'asks_{i}_size'])]
            
            if not bids or not asks:
                return self._create_nan_features_series()
                
            # Calculate features using microstructure module
            features = microstructure.calculate_orderbook_features(
                bids, asks, 
                depth_levels=depth_levels,
                config=self.config
            )
            
            # Create series with specific feature names
            return pd.Series({
                f'raw_obi_{depth_levels}': features.get('obi', np.nan),
                f'raw_depth_ratio_{depth_levels}': features.get('depth_ratio', np.nan),
                f'raw_depth_pressure_{depth_levels}': features.get('depth_pressure', np.nan),
                'raw_spread_abs': features.get('spread_abs', np.nan),
                'raw_spread_rel': features.get('spread_rel', np.nan),
                'imbalance': features.get('imbalance', np.nan),  # CRITICAL: Legacy field name
            })
            
        except Exception as e:
            self.logger.debug(f"Error calculating features: {e}")
            return self._create_nan_features_series()
            
    def _create_nan_features(self, index: pd.Index) -> pd.DataFrame:
        """Create DataFrame with NaN features for missing data."""
        depth_levels = self.config.microstructure.depth_levels
        
        feature_cols = [
            f'raw_obi_{depth_levels}',
            f'raw_depth_ratio_{depth_levels}',
            f'raw_depth_pressure_{depth_levels}',
            'raw_spread_abs',
            'raw_spread_rel',
            'imbalance'  # Legacy field name
        ]
        
        return pd.DataFrame(np.nan, index=index, columns=feature_cols)
        
    def _create_nan_features_series(self) -> pd.Series:
        """Create Series with NaN features for a single row."""
        depth_levels = self.config.microstructure.depth_levels
        
        return pd.Series({
            f'raw_obi_{depth_levels}': np.nan,
            f'raw_depth_ratio_{depth_levels}': np.nan,
            f'raw_depth_pressure_{depth_levels}': np.nan,
            'raw_spread_abs': np.nan,
            'raw_spread_rel': np.nan,
            'imbalance': np.nan  # Legacy field name
        })