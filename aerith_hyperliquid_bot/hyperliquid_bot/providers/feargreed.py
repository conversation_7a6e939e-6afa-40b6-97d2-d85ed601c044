import logging
import pandas as pd
import httpx
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

def fetch_fear_greed_index(limit: int = 0) -> pd.DataFrame:
    """
    Fetch historical Fear & Greed Index data from Alternative.me API.
    
    Args:
        limit (int, optional): Number of historical data points to retrieve. 
                               0 means fetch all available historical data. 
                               Defaults to 0.
    
    Returns:
        pd.DataFrame: DataFrame with columns ['timestamp', 'fear_greed_idx'], 
                      indexed by timestamp in UTC.
    """
    try:
        # Alternative.me Fear & Greed Index API endpoint
        url = "https://api.alternative.me/fng/"
        
        # Parameters for the API request
        params = {
            "limit": limit if limit > 0 else 0,  # 0 means all historical data
            "format": "json"
        }
        
        # Make the API request
        with httpx.Client() as client:
            response = client.get(url, params=params)
            response.raise_for_status()  # Raise an exception for bad responses
        
        # Parse the JSON response
        data = response.json()
        
        # Check if data is present
        if not data or 'data' not in data or not data['data']:
            logger.warning("No Fear & Greed Index data retrieved.")
            return pd.DataFrame()
        
        # Extract data points
        fear_greed_data = []
        for entry in data['data']:
            try:
                # Convert timestamp to datetime in UTC
                timestamp = datetime.fromtimestamp(int(entry['timestamp']), tz=timezone.utc)
                fear_greed_value = float(entry['value'])
                fear_greed_data.append({
                    'timestamp': timestamp,
                    'fear_greed_idx': fear_greed_value
                })
            except (KeyError, ValueError, TypeError) as e:
                logger.warning(f"Error parsing Fear & Greed data point: {e}")
        
        # Create DataFrame
        if not fear_greed_data:
            logger.warning("No valid Fear & Greed data points after parsing.")
            return pd.DataFrame()
        
        df = pd.DataFrame(fear_greed_data)
        df.set_index('timestamp', inplace=True)
        df.sort_index(inplace=True)
        
        logger.info(f"Successfully retrieved {len(df)} Fear & Greed data points.")
        return df
    
    except httpx.RequestError as e:
        logger.error(f"API request failed: {e}")
        return pd.DataFrame()
    except Exception as e:
        logger.error(f"Unexpected error fetching Fear & Greed data: {e}")
        return pd.DataFrame()

# Optional: Add a main block for testing/demonstration
if __name__ == "__main__":
    # Configure logging to see output
    logging.basicConfig(level=logging.INFO)
    
    # Fetch and display Fear & Greed data
    fg_data = fetch_fear_greed_index(limit=10)
    print(fg_data) 