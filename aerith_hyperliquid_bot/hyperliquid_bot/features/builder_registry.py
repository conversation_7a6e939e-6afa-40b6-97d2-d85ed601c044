# hyperliquid_bot/features/builder_registry.py

"""
FeatureBuilder Registry for Task R-112o

This module contains builder functions for all canonical features required by the trading system.
Each function takes a DataFrame and returns a pandas Series with the same length, ensuring
causal calculations (no look-ahead bias).

All functions are designed to be called from the ETL pipeline to generate the complete
canonical feature set as defined in docs/feature_catalog.md.
"""

import logging
import numpy as np
import pandas as pd
from typing import Optional, Union
from .ta_utils import calculate_atr, calculate_atr_percent, calculate_realized_volatility
from .microstructure import calc_obi

logger = logging.getLogger(__name__)

# ============================================================================
# Core Features
# ============================================================================

def build_best_bid(df: pd.DataFrame) -> pd.Series:
    """
    Extract best bid price from L2 data.

    Args:
        df: DataFrame with bid_price_1 column

    Returns:
        Series with best bid prices
    """
    if 'bid_price_1' in df.columns:
        result = df['bid_price_1'].copy()
        result.name = 'best_bid'
        return result
    elif 'best_bid' in df.columns:
        return df['best_bid'].copy()
    else:
        logger.warning("No bid price column found, returning NaN series")
        return pd.Series(np.nan, index=df.index, name='best_bid')

def build_best_ask(df: pd.DataFrame) -> pd.Series:
    """
    Extract best ask price from L2 data.

    Args:
        df: DataFrame with ask_price_1 column

    Returns:
        Series with best ask prices
    """
    if 'ask_price_1' in df.columns:
        result = df['ask_price_1'].copy()
        result.name = 'best_ask'
        return result
    elif 'best_ask' in df.columns:
        return df['best_ask'].copy()
    else:
        logger.warning("No ask price column found, returning NaN series")
        return pd.Series(np.nan, index=df.index, name='best_ask')

def build_volume(df: pd.DataFrame) -> pd.Series:
    """
    Build volume feature. For L2 data without trades, use a placeholder.

    Args:
        df: DataFrame

    Returns:
        Series with volume data (placeholder 0.0 for L2-only data)
    """
    if 'volume' in df.columns:
        return df['volume'].copy()
    else:
        # For L2-only data, volume is not available, use placeholder
        logger.info("No volume data available, using placeholder 0.0")
        return pd.Series(0.0, index=df.index, name='volume')

# ============================================================================
# ATR Features
# ============================================================================

# NOTE: ATR variants (atr_tf, atr_mr, atr_mv) are now runtime aliases for atr_percent_sec
# They should NOT be stored as separate columns in parquet files
# Instead, they are mapped at runtime in loaders/detectors/strategies

# def build_atr_tf(df: pd.DataFrame, length: int = 14) -> pd.Series:
#     """REMOVED: ATR variants are now runtime aliases for atr_percent_sec"""
#     pass

# def build_atr_mr(df: pd.DataFrame, length: int = 14) -> pd.Series:
#     """REMOVED: ATR variants are now runtime aliases for atr_percent_sec"""
#     pass

# def build_atr_mv(df: pd.DataFrame, length: int = 14) -> pd.Series:
#     """REMOVED: ATR variants are now runtime aliases for atr_percent_sec"""
#     pass

# ============================================================================
# OBI Features
# ============================================================================

def build_raw_obi_20(df: pd.DataFrame) -> pd.Series:
    """
    Build raw OBI using 20 levels.

    Args:
        df: DataFrame with bid_size_* and ask_size_* columns

    Returns:
        Series with raw OBI values for 20 levels
    """
    depth = 20
    bid_cols = [f'bid_size_{i}' for i in range(1, depth + 1)]
    ask_cols = [f'ask_size_{i}' for i in range(1, depth + 1)]

    # Check if all required columns exist
    missing_cols = [col for col in bid_cols + ask_cols if col not in df.columns]
    if missing_cols:
        logger.warning(f"Missing columns for OBI calculation: {missing_cols[:5]}... (showing first 5)")
        return pd.Series(np.nan, index=df.index, name='raw_obi_20')

    # Calculate OBI for each row
    obi_values = []
    for idx, row in df.iterrows():
        bid_sum = sum(row[col] for col in bid_cols if not pd.isna(row[col]))
        ask_sum = sum(row[col] for col in ask_cols if not pd.isna(row[col]))

        denominator = bid_sum + ask_sum
        if denominator > 1e-9:
            obi = (bid_sum - ask_sum) / denominator
        else:
            obi = 0.0
        obi_values.append(obi)

    return pd.Series(obi_values, index=df.index, name='raw_obi_20')

def build_obi_smoothed_5(df: pd.DataFrame, window: int = 30) -> pd.Series:
    """
    Build smoothed OBI using 5 levels with rolling mean.

    Args:
        df: DataFrame with raw_obi_5 column or bid/ask size columns
        window: Smoothing window in seconds

    Returns:
        Series with smoothed OBI values for 5 levels
    """
    if 'raw_obi_5' in df.columns:
        raw_obi = df['raw_obi_5']
    else:
        # Calculate raw OBI first
        raw_obi = build_raw_obi_5(df)

    # Apply causal rolling mean (no look-ahead)
    smoothed = raw_obi.rolling(window=window, min_periods=1).mean()
    return smoothed.rename('obi_smoothed_5')

def build_raw_obi_5(df: pd.DataFrame) -> pd.Series:
    """
    Build raw OBI using 5 levels (helper function).

    Args:
        df: DataFrame with bid_size_* and ask_size_* columns

    Returns:
        Series with raw OBI values for 5 levels
    """
    depth = 5
    bid_cols = [f'bid_size_{i}' for i in range(1, depth + 1)]
    ask_cols = [f'ask_size_{i}' for i in range(1, depth + 1)]

    # Check if all required columns exist
    missing_cols = [col for col in bid_cols + ask_cols if col not in df.columns]
    if missing_cols:
        logger.warning(f"Missing columns for OBI calculation: {missing_cols}")
        return pd.Series(np.nan, index=df.index, name='raw_obi_5')

    # Calculate OBI for each row
    obi_values = []
    for idx, row in df.iterrows():
        bid_sum = sum(row[col] for col in bid_cols if not pd.isna(row[col]))
        ask_sum = sum(row[col] for col in ask_cols if not pd.isna(row[col]))

        denominator = bid_sum + ask_sum
        if denominator > 1e-9:
            obi = (bid_sum - ask_sum) / denominator
        else:
            obi = 0.0
        obi_values.append(obi)

    return pd.Series(obi_values, index=df.index, name='raw_obi_5')

def build_obi_smoothed_20(df: pd.DataFrame, window: int = 30) -> pd.Series:
    """
    Build smoothed OBI using 20 levels with rolling mean.

    Args:
        df: DataFrame with raw_obi_20 column or bid/ask size columns
        window: Smoothing window in seconds

    Returns:
        Series with smoothed OBI values for 20 levels
    """
    if 'raw_obi_20' in df.columns:
        raw_obi = df['raw_obi_20']
    else:
        # Calculate raw OBI first
        raw_obi = build_raw_obi_20(df)

    # Apply causal rolling mean (no look-ahead)
    smoothed = raw_obi.rolling(window=window, min_periods=1).mean()
    return smoothed.rename('obi_smoothed_20')

def build_obi_smoothed(df: pd.DataFrame, window: int = 30) -> pd.Series:
    """
    Build legacy smoothed OBI (maps to obi_smoothed_5).

    Args:
        df: DataFrame with raw_obi_5 column or bid/ask size columns
        window: Smoothing window in seconds

    Returns:
        Series with smoothed OBI values (legacy mapping to 5 levels)
    """
    return build_obi_smoothed_5(df, window).rename('obi_smoothed')

def build_obi_zscore_5(df: pd.DataFrame, window: int = 300) -> pd.Series:
    """
    Build Z-score normalized OBI using 5 levels.

    Args:
        df: DataFrame with raw_obi_5 column or bid/ask size columns
        window: Rolling window for Z-score calculation

    Returns:
        Series with Z-score normalized OBI values for 5 levels
    """
    if 'raw_obi_5' in df.columns:
        raw_obi = df['raw_obi_5']
    else:
        raw_obi = build_raw_obi_5(df)

    # Calculate rolling mean and std (causal)
    rolling_mean = raw_obi.rolling(window=window, min_periods=1).mean()
    rolling_std = raw_obi.rolling(window=window, min_periods=1).std()

    # Calculate Z-score
    zscore = (raw_obi - rolling_mean) / (rolling_std + 1e-9)  # Add epsilon for stability
    return zscore.fillna(0.0).rename('obi_zscore_5')

def build_obi_zscore_20(df: pd.DataFrame, window: int = 300) -> pd.Series:
    """
    Build Z-score normalized OBI using 20 levels.

    Args:
        df: DataFrame with raw_obi_20 column or bid/ask size columns
        window: Rolling window for Z-score calculation

    Returns:
        Series with Z-score normalized OBI values for 20 levels
    """
    if 'raw_obi_20' in df.columns:
        raw_obi = df['raw_obi_20']
    else:
        raw_obi = build_raw_obi_20(df)

    # Calculate rolling mean and std (causal)
    rolling_mean = raw_obi.rolling(window=window, min_periods=1).mean()
    rolling_std = raw_obi.rolling(window=window, min_periods=1).std()

    # Calculate Z-score
    zscore = (raw_obi - rolling_mean) / (rolling_std + 1e-9)  # Add epsilon for stability
    return zscore.fillna(0.0).rename('obi_zscore_20')

def build_raw_obi_L1_3(df: pd.DataFrame) -> pd.Series:
    """
    Build raw OBI for levels 1-3 (OBI Scalper specific).

    Args:
        df: DataFrame with bid_size_* and ask_size_* columns

    Returns:
        Series with raw OBI values for levels 1-3
    """
    bid_cols = ['bid_size_1', 'bid_size_2', 'bid_size_3']
    ask_cols = ['ask_size_1', 'ask_size_2', 'ask_size_3']

    # Check if all required columns exist
    missing_cols = [col for col in bid_cols + ask_cols if col not in df.columns]
    if missing_cols:
        logger.warning(f"Missing columns for L1_3 OBI calculation: {missing_cols}")
        return pd.Series(np.nan, index=df.index, name='raw_obi_L1_3')

    # Calculate OBI for each row
    obi_values = []
    for idx, row in df.iterrows():
        bid_sum = sum(row[col] for col in bid_cols if not pd.isna(row[col]))
        ask_sum = sum(row[col] for col in ask_cols if not pd.isna(row[col]))

        denominator = bid_sum + ask_sum
        if denominator > 1e-9:
            obi = (bid_sum - ask_sum) / denominator
        else:
            obi = 0.0
        obi_values.append(obi)

    return pd.Series(obi_values, index=df.index, name='raw_obi_L1_3')

def build_raw_obi_L1_10(df: pd.DataFrame) -> pd.Series:
    """
    Build raw OBI for levels 1-10 (OBI Scalper specific).

    Args:
        df: DataFrame with bid_size_* and ask_size_* columns

    Returns:
        Series with raw OBI values for levels 1-10
    """
    bid_cols = [f'bid_size_{i}' for i in range(1, 11)]
    ask_cols = [f'ask_size_{i}' for i in range(1, 11)]

    # Check if all required columns exist
    missing_cols = [col for col in bid_cols + ask_cols if col not in df.columns]
    if missing_cols:
        logger.warning(f"Missing columns for L1_10 OBI calculation: {missing_cols[:5]}... (showing first 5)")
        return pd.Series(np.nan, index=df.index, name='raw_obi_L1_10')

    # Calculate OBI for each row
    obi_values = []
    for idx, row in df.iterrows():
        bid_sum = sum(row[col] for col in bid_cols if not pd.isna(row[col]))
        ask_sum = sum(row[col] for col in ask_cols if not pd.isna(row[col]))

        denominator = bid_sum + ask_sum
        if denominator > 1e-9:
            obi = (bid_sum - ask_sum) / denominator
        else:
            obi = 0.0
        obi_values.append(obi)

    return pd.Series(obi_values, index=df.index, name='raw_obi_L1_10')

# ============================================================================
# Momentum Features
# ============================================================================

# Removed build_ma_slope - dead code that was not used by any system
# Legacy system uses calculator.py for ma_slope, modern system uses ma_slope_ema_30s


def build_ma_slope_ema_30s(df: pd.DataFrame, span: int = 30, price_col: str = 'mid_price') -> pd.Series:
    """
    Build EMA-based moving average slope (percentage change per second).

    This is the corrected momentum indicator for continuous GMS detector.
    Formula: (ema.diff() / ema.shift(1)) * 100

    Args:
        df: DataFrame with price column
        span: EMA span in seconds (default: 30)
        price_col: Column to use for price (default: 'mid_price')

    Returns:
        Series with EMA slope values as percentage change per second
    """
    if price_col not in df.columns:
        logger.warning(f"Price column '{price_col}' not found, returning NaN series")
        return pd.Series(np.nan, index=df.index, name='ma_slope_ema_30s')

    prices = df[price_col]

    # Calculate EMA (causal)
    ema = prices.ewm(span=span, adjust=False).mean()

    # Calculate slope as percentage change per second
    # No look-ahead: uses .diff() (t vs t-1)
    ma_slope_ema_30s = (ema.diff() / ema.shift(1)) * 100

    # Ensure dtype float64, NaN only first row
    ma_slope_ema_30s = ma_slope_ema_30s.astype('float64')

    return ma_slope_ema_30s.rename('ma_slope_ema_30s')


def build_atr_rolling_14h(df: pd.DataFrame, price_col: str = 'mid_price', length: int = 14) -> pd.Series:
    """
    Build ATR (14-hour) from 1-second mid_price data using causal hourly resampling.

    This eliminates the need for external hourly OHLCV files by computing ATR
    directly from 1-second data with proper 14-hour rolling window.

    Args:
        df: DataFrame with price column and timestamp
        price_col: Column to use for price (default: 'mid_price')
        length: ATR period in hours (default: 14)

    Returns:
        Series with ATR values forward-filled to 1-second resolution
    """
    if price_col not in df.columns:
        logger.warning(f"Price column '{price_col}' not found, returning NaN series")
        return pd.Series(np.nan, index=df.index, name='atr_14_sec')

    if 'timestamp' not in df.columns:
        logger.warning("Timestamp column not found, returning NaN series")
        return pd.Series(np.nan, index=df.index, name='atr_14_sec')

    # Work with a copy to avoid modifying original
    df_work = df[['timestamp', price_col]].copy()

    # Ensure timestamp is datetime and set as index
    if not pd.api.types.is_datetime64_dtype(df_work['timestamp']):
        df_work['timestamp'] = pd.to_datetime(df_work['timestamp'])

    df_work = df_work.set_index('timestamp')

    # Resample to hourly OHLC causally
    # label='right', closed='right' ensures causal behavior
    ohlc = df_work[price_col].resample('1H', label='right', closed='right').ohlc()

    # Calculate True Range components
    ohlc['prev_close'] = ohlc['close'].shift(1)

    # True Range = max(high-low, |high-prev_close|, |low-prev_close|)
    true_range = np.maximum.reduce([
        ohlc['high'] - ohlc['low'],
        (ohlc['high'] - ohlc['prev_close']).abs(),
        (ohlc['low'] - ohlc['prev_close']).abs()
    ])

    # Calculate ATR using Wilder's smoothing (EWM with alpha=1/length)
    atr_series = pd.Series(true_range, index=ohlc.index)
    atr_14 = atr_series.ewm(alpha=1/length, adjust=False).mean()

    # Forward-fill ATR values down to 1-second resolution
    # Create a combined index with all timestamps
    combined_index = df_work.index.union(atr_14.index).sort_values()

    # Reindex ATR to combined index and forward fill
    atr_reindexed = atr_14.reindex(combined_index).ffill()

    # Extract values for original 1-second timestamps
    atr_1s = atr_reindexed.reindex(df_work.index)

    # Reset index to match original DataFrame structure
    atr_1s.index = df.index

    return atr_1s.rename('atr_14_sec')


def build_atr_percent_sec(df: pd.DataFrame, atr_col: str = 'atr_14_sec', price_col: str = 'mid_price') -> pd.Series:
    """
    Build ATR percentage from ATR and price columns.

    Args:
        df: DataFrame with ATR and price columns
        atr_col: Column name for ATR values (default: 'atr_14_sec')
        price_col: Column name for price values (default: 'mid_price')

    Returns:
        Series with ATR percentage values
    """
    if atr_col not in df.columns or price_col not in df.columns:
        logger.warning(f"Required columns '{atr_col}' or '{price_col}' not found, returning NaN series")
        return pd.Series(np.nan, index=df.index, name='atr_percent_sec')

    # Calculate ATR percentage
    atr_percent = df[atr_col] / df[price_col]

    return atr_percent.rename('atr_percent_sec')


# ============================================================================
# Legacy ATR Aliases for Backward Compatibility
# ============================================================================

def build_atr_legacy(df: pd.DataFrame) -> pd.Series:
    """
    Build legacy 'atr' column as alias for 'atr_14_sec'.

    Required for backward compatibility with continuous GMS detector.

    Args:
        df: DataFrame with atr_14_sec column

    Returns:
        Series with legacy ATR values
    """
    if 'atr_14_sec' in df.columns:
        return df['atr_14_sec'].rename('atr')
    else:
        logger.warning("atr_14_sec column not found, returning NaN series for legacy atr")
        return pd.Series(np.nan, index=df.index, name='atr')


def build_atr_percent_legacy(df: pd.DataFrame) -> pd.Series:
    """
    Build legacy 'atr_percent' column as alias for 'atr_percent_sec'.

    Required for backward compatibility with continuous GMS detector.

    Args:
        df: DataFrame with atr_percent_sec column

    Returns:
        Series with legacy ATR percentage values
    """
    if 'atr_percent_sec' in df.columns:
        return df['atr_percent_sec'].rename('atr_percent')
    else:
        logger.warning("atr_percent_sec column not found, returning NaN series for legacy atr_percent")
        return pd.Series(np.nan, index=df.index, name='atr_percent')

# ============================================================================
# Registry Dictionary
# ============================================================================

FEATURE_BUILDERS = {
    # Core features
    'best_bid': build_best_bid,
    'best_ask': build_best_ask,
    'volume': build_volume,

    # ATR features - REMOVED: atr_tf, atr_mr, atr_mv are now runtime aliases
    # These are mapped at runtime to atr_percent_sec in loaders/detectors/strategies

    # OBI features
    'raw_obi_20': build_raw_obi_20,
    'obi_smoothed': build_obi_smoothed,
    'obi_smoothed_5': build_obi_smoothed_5,
    'obi_smoothed_20': build_obi_smoothed_20,
    'obi_zscore_5': build_obi_zscore_5,
    'obi_zscore_20': build_obi_zscore_20,
    'raw_obi_L1_3': build_raw_obi_L1_3,
    'raw_obi_L1_10': build_raw_obi_L1_10,

    # Momentum features
    # 'ma_slope': removed - dead code, legacy uses calculator.py
    'ma_slope_ema_30s': build_ma_slope_ema_30s,

    # ATR features
    'atr_14_sec': build_atr_rolling_14h,
    'atr_percent_sec': build_atr_percent_sec,

    # Legacy ATR aliases for backward compatibility
    'atr': build_atr_legacy,
    'atr_percent': build_atr_percent_legacy,
}

def get_canonical_columns(depth_levels: int = 5) -> set:
    """
    Get the complete set of canonical column names with depth placeholders resolved.

    Args:
        depth_levels: Depth level configuration (5 or 20)

    Returns:
        Set of canonical column names
    """
    canonical_columns = {
        # Core features
        'timestamp', 'mid_price', 'close', 'high', 'low', 'best_bid', 'best_ask', 'spread', 'volume',

        # ATR features (atr_tf, atr_mr, atr_mv are runtime aliases)
        'atr_14_sec', 'atr_percent_sec', 'atr', 'atr_percent',

        # OBI features (depth-aware)
        'raw_obi_5', 'raw_obi_20', 'obi_smoothed_5', 'obi_smoothed_20',
        'obi_zscore_5', 'obi_zscore_20', 'obi_smoothed',

        # OBI Scalper features
        'raw_obi_L1_3', 'raw_obi_L1_10',

        # Spread features
        'spread_mean', 'spread_std',

        # Other features
        'ma_slope', 'ma_slope_ema_30s', 'realised_vol_1s', 'unrealised_pnl'
    }

    return canonical_columns

def validate_schema(df: pd.DataFrame, depth_levels: int = 5) -> tuple[bool, list[str]]:
    """
    Validate that DataFrame contains all canonical columns.

    Args:
        df: DataFrame to validate
        depth_levels: Depth level configuration

    Returns:
        Tuple of (is_valid, missing_columns)
    """
    canonical_columns = get_canonical_columns(depth_levels)
    actual_columns = set(df.columns)
    missing_columns = list(canonical_columns - actual_columns)

    is_valid = len(missing_columns) == 0
    return is_valid, missing_columns

def validate_nan_ratios(df: pd.DataFrame, warmup_rows: int = 100, depth_levels: int = 5) -> tuple[bool, dict]:
    """
    Two-tier NaN validation system:
    - Tier-1 (≤ 1% NaN): canonical analytic features
    - Tier-2 (≤ 5% NaN): raw L2 columns that can gap

    Args:
        df: DataFrame to validate
        warmup_rows: Number of rows to skip for warmup
        depth_levels: Depth level configuration for canonical columns

    Returns:
        Tuple of (is_valid, nan_ratios_dict)
    """
    if len(df) <= warmup_rows:
        return True, {}  # Skip validation for small datasets

    # Define tier-1 columns (≤ 1% NaN) - canonical analytic features that should be stable
    # Note: ATR columns have special handling for first-day warm-up period
    tier1_columns = {
        'atr_14_sec', 'atr', 'spread_mean', 'spread_std', 'ma_slope', 'ma_slope_ema_30s', 'unrealised_pnl', 'volume'
    }

    # Define tier-2 columns (≤ 5% NaN) - features that depend on raw L2 data and can have gaps
    tier2_patterns = ['bid_price_', 'ask_price_', 'bid_size_', 'ask_size_', 'raw_obi_']
    tier2_exact_columns = {'best_bid', 'best_ask', 'atr_percent_sec', 'atr_percent', 'mid_price', 'close', 'high', 'low', 'spread', 'realised_vol_1s'}

    # ATR percentage gets special treatment - same as atr_14_sec
    atr_dependent_columns = {'atr_percent_sec', 'atr_percent'}

    # Check if this is a single-day dataset for ATR warm-up allowance
    is_single_day = False
    if 'timestamp' in df.columns:
        unique_days = df['timestamp'].dt.normalize().nunique()
        is_single_day = (unique_days == 1)

    # Check NaN ratios after warmup
    df_after_warmup = df.iloc[warmup_rows:]
    nan_ratios = {}
    failed_columns = []

    # Validate Tier-1 columns (≤ 1% NaN, except ATR on first day ≤ 15%)
    for col in tier1_columns:
        if col == 'timestamp' or col not in df_after_warmup.columns:
            continue

        nan_count = df_after_warmup[col].isna().sum()
        total_count = len(df_after_warmup)
        nan_ratio = nan_count / total_count if total_count > 0 else 0.0
        nan_ratios[col] = nan_ratio

        # Special handling for ATR columns (allow up to 15% NaN for warm-up period)
        if col in {'atr_14_sec', 'atr'}:
            threshold = 0.15  # 15% for ATR warm-up (any dataset)
        else:
            threshold = 0.01  # 1% for other tier-1 columns

        if nan_ratio > threshold:
            failed_columns.append(col)

    # Validate Tier-2 columns (≤ 5% NaN, except ATR-dependent columns on first day ≤ 15%)
    tier2_columns = []
    for col in df_after_warmup.columns:
        if any(pattern in col for pattern in tier2_patterns) or col in tier2_exact_columns:
            tier2_columns.append(col)

    for col in tier2_columns:
        if col == 'timestamp':
            continue

        nan_count = df_after_warmup[col].isna().sum()
        total_count = len(df_after_warmup)
        nan_ratio = nan_count / total_count if total_count > 0 else 0.0
        nan_ratios[col] = nan_ratio

        # Special handling for ATR-dependent columns (allow up to 15% for warm-up)
        if col in atr_dependent_columns:
            threshold = 0.15  # 15% for ATR-dependent columns (any dataset)
        else:
            threshold = 0.05  # 5% for other tier-2 columns

        if nan_ratio > threshold:
            failed_columns.append(col)

    # Validate remaining canonical columns with 1% threshold
    canonical_columns = get_canonical_columns(depth_levels)
    remaining_columns = canonical_columns - tier1_columns - set(tier2_columns)

    for col in remaining_columns:
        if col == 'timestamp' or col not in df_after_warmup.columns:
            continue

        nan_count = df_after_warmup[col].isna().sum()
        total_count = len(df_after_warmup)
        nan_ratio = nan_count / total_count if total_count > 0 else 0.0
        nan_ratios[col] = nan_ratio

        if nan_ratio > 0.01:  # 1% threshold for remaining canonical
            failed_columns.append(col)

    is_valid = len(failed_columns) == 0
    return is_valid, nan_ratios
