"""
Indicators Module

This module provides functions for calculating technical indicators with look-ahead safety.
All calculations are performed on historical data up to the prior candle to avoid look-ahead bias.
"""

import logging
from typing import Dict, Any, Optional, Union, Tuple

import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

def calculate_ema(
    data: pd.DataFrame, 
    period: int, 
    price_col: str = 'close',
    shift: int = 1,
    min_periods: Optional[int] = None
) -> pd.Series:
    """
    Calculate EMA with look-ahead safety by shifting data.
    
    Args:
        data: DataFrame containing price data
        period: EMA period
        price_col: Column name for price data (default: 'close')
        shift: Number of periods to shift data to avoid look-ahead bias (default: 1)
        min_periods: Minimum number of observations required (default: period//2)
        
    Returns:
        Series containing EMA values
    """
    if period <= 0:
        logger.warning(f"Invalid EMA period: {period}. Must be > 0.")
        return pd.Series(index=data.index, dtype=float)
        
    # Set default min_periods if not provided
    if min_periods is None:
        min_periods = max(1, period // 2)
        
    # Shift data to avoid look-ahead bias
    shifted_data = data[price_col].shift(shift)
    
    # Calculate EMA
    ema = shifted_data.ewm(span=period, adjust=False, min_periods=min_periods).mean()
    
    return ema

def calculate_atr(
    data: pd.DataFrame,
    period: int,
    shift: int = 1,
    high_col: str = 'high',
    low_col: str = 'low',
    close_col: str = 'close'
) -> pd.Series:
    """
    Calculate ATR with look-ahead safety by shifting data.
    
    Args:
        data: DataFrame containing price data
        period: ATR period
        shift: Number of periods to shift data to avoid look-ahead bias (default: 1)
        high_col: Column name for high prices (default: 'high')
        low_col: Column name for low prices (default: 'low')
        close_col: Column name for close prices (default: 'close')
        
    Returns:
        Series containing ATR values
    """
    if period <= 0:
        logger.warning(f"Invalid ATR period: {period}. Must be > 0.")
        return pd.Series(index=data.index, dtype=float)
        
    # Shift data to avoid look-ahead bias
    shifted_high = data[high_col].shift(shift)
    shifted_low = data[low_col].shift(shift)
    shifted_close = data[close_col].shift(shift)
    prev_close = shifted_close.shift(1)
    
    # Calculate True Range
    tr1 = shifted_high - shifted_low  # Current high - current low
    tr2 = (shifted_high - prev_close).abs()  # Current high - previous close
    tr3 = (shifted_low - prev_close).abs()  # Current low - previous close
    
    # True Range is the maximum of the three
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # Calculate ATR using Wilder's smoothing method
    atr = tr.rolling(window=period, min_periods=period).mean()
    
    # Apply Wilder's smoothing for subsequent values
    for i in range(period, len(tr)):
        atr.iloc[i] = ((period - 1) * atr.iloc[i-1] + tr.iloc[i]) / period
        
    return atr

def prepare_ohlcv_for_indicators(
    data: pd.DataFrame,
    shift: int = 1
) -> pd.DataFrame:
    """
    Prepare OHLCV data for indicator calculation with look-ahead safety.
    
    Args:
        data: DataFrame containing OHLCV data
        shift: Number of periods to shift data to avoid look-ahead bias (default: 1)
        
    Returns:
        DataFrame with shifted OHLCV data
    """
    # Create a copy to avoid modifying the original
    result = data.copy()
    
    # Shift OHLCV columns
    ohlcv_cols = ['open', 'high', 'low', 'close', 'volume']
    for col in ohlcv_cols:
        if col in result.columns:
            result[f'shifted_{col}'] = result[col].shift(shift)
            
    return result

def calculate_indicators_for_tf_v3(
    data: pd.DataFrame,
    ema_fast_period: int,
    ema_slow_period: int,
    atr_period: int,
    shift: int = 1
) -> pd.DataFrame:
    """
    Calculate all indicators needed for TF-v3 strategy with look-ahead safety.
    
    Args:
        data: DataFrame containing OHLCV data
        ema_fast_period: Fast EMA period
        ema_slow_period: Slow EMA period
        atr_period: ATR period
        shift: Number of periods to shift data to avoid look-ahead bias (default: 1)
        
    Returns:
        DataFrame with original data plus calculated indicators
    """
    # Create a copy to avoid modifying the original
    result = data.copy()
    
    # Calculate EMAs
    result[f'ema_{ema_fast_period}'] = calculate_ema(
        data, ema_fast_period, price_col='close', shift=shift
    )
    result[f'ema_{ema_slow_period}'] = calculate_ema(
        data, ema_slow_period, price_col='close', shift=shift
    )
    
    # Calculate ATR
    result[f'atr_{atr_period}'] = calculate_atr(
        data, atr_period, shift=shift
    )
    
    return result
