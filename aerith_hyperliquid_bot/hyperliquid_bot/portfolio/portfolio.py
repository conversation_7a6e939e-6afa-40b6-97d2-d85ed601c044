# hyperliquid_bot/portfolio/portfolio.py

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timezone
from typing import Optional, Dict, List, Any

# Import Config for type hinting and settings access
from hyperliquid_bot.config.settings import Config
# Import DataHandlerInterface for type hinting (used for mark price source)
from hyperliquid_bot.data import DataHandlerInterface

logger = logging.getLogger(__name__)

class Portfolio:
    """
    Manages the trading account state, including balance, positions, trades,
    fees, funding, and equity curve tracking. Calculates P/L and handles
    margin/liquidation checks.
    """
    def __init__(self, config: Config, data_handler: Optional[DataHandlerInterface] = None):
        self.config = config
        self.data_handler = data_handler # Needed for mark price source
        self.logger = logging.getLogger(self.__class__.__name__)
        self.initial_balance = config.portfolio.initial_balance
        self.balance = self.initial_balance
        self.position: Optional[Dict[str, Any]] = None # Holds current position details
        self.trades: List[Dict[str, Any]] = [] # History of closed trades
        self.equity_curve: List[float] = [self.initial_balance] # Start with initial balance
        self.equity_timestamps: List[float] = [] # List of corresponding timestamps (unix float)
        self.total_fees_paid: float = 0.0
        self.total_funding_pnl: float = 0.0
        self.total_slippage_pnl: float = 0.0 # Track cumulative slippage PnL
        self.reserved_margin: float = 0.0    # IM currently locked for the open position
        self.maintenance_margin: float = 0.0  # MM required to keep position open

        # Margin mode configuration
        # - 'cross': All positions share the same margin pool (default)
        # - 'isolated': Each position has its own isolated margin
        self.margin_mode = getattr(config.portfolio, 'margin_mode', 'cross')

        # Debug: Print the actual config value to verify it's being read correctly
        if hasattr(config.portfolio, 'margin_mode'):
            logger.warning(f"CONFIG DEBUG: Read margin_mode={config.portfolio.margin_mode} from config")
        else:
            logger.warning("CONFIG DEBUG: margin_mode not found in config, using default: 'cross'")

        logger.info(f"Using margin mode: {self.margin_mode}")

        # Calculate Maintenance Margin Fraction based on asset max leverage
        # MM Fraction = 1 / Max Leverage (e.g., 50x -> 0.02 or 2%)
        # IM Fraction = 1 / Position Leverage (calculated at entry)
        # We need MM Fraction for liquidation checks.
        self.maintenance_margin_fraction = 1.0 / config.portfolio.asset_max_leverage
        logger.info(f"Calculated Maintenance Margin Fraction: {self.maintenance_margin_fraction:.4f} (based on asset max leverage {config.portfolio.asset_max_leverage}x)")

        # Margin health thresholds (configurable)
        self.margin_health_warning = 1.5  # Margin ratio below this triggers warning
        self.margin_health_danger = 1.2   # Margin ratio below this triggers danger alert

        # Diagnostics (can be updated by ExecutionSimulator)
        self.diagnostics: Dict[str, int] = {}

        logger.info(f"Initialized with Balance: ${self.balance:,.2f}")

    def update_diagnostics(self, deltas: Dict[str, int]):
        """Updates diagnostic counters from simulation results."""
        for key, delta in deltas.items():
            self.diagnostics[key] = self.diagnostics.get(key, 0) + delta

    def get_mark_price(self, signals: dict) -> Optional[float]:
        """Gets the mark price based on the configured source."""
        source = getattr(self.config, 'mark_price_source', 'close')  # Default to 'close' if not configured
        price = signals.get(source) # Assumes signal names match config options ('close', 'bbm', etc.)

        # Fallback logic if primary source is unavailable
        if price is None or pd.isna(price):
            fallback_sources = ['close', 'open', 'high', 'low'] # Order of preference
            for fb_source in fallback_sources:
                price = signals.get(fb_source)
                if price is not None and not pd.isna(price):
                    logger.warning(f"Mark price source '{source}' unavailable. Using fallback '{fb_source}'.")
                    break
            else: # If all fallbacks fail
                logger.error(f"Could not determine a valid mark price from signals. Available: {list(signals.keys())}")
                return None
        return float(price)

    def calculate_account_value(self, signals: dict) -> float:
        """
        Calculates the total account value including unrealized PnL for the current position.

        Args:
            signals: Dictionary containing current market data

        Returns:
            Total account value (balance + unrealized PnL)
        """
        account_value = self.balance

        # Add unrealized PnL if there is an open position
        if self.position:
            mark_price = self.get_mark_price(signals)
            if mark_price is not None:
                entry_price = self.position['entry']
                position_size = self.position['size']
                position_type = self.position['type']

                # Calculate unrealized PnL
                if position_type == 'long':
                    unrealized_pnl = (mark_price - entry_price) * position_size
                else:  # short
                    unrealized_pnl = (entry_price - mark_price) * position_size

                account_value += unrealized_pnl
                logger.debug(f"Account value calculation: Balance=${self.balance:.2f} + Unrealized PnL=${unrealized_pnl:.2f} = ${account_value:.2f}")
            else:
                logger.warning("Could not calculate unrealized PnL: Failed to get mark price")

        return account_value

    def check_liquidation(self, signals: dict) -> bool:
        """Checks if the current position should be liquidated based on Maintenance Margin."""
        if not self.position:
            return False

        # Get current mark price
        current_price = self.get_mark_price(signals)
        if current_price is None:
            logger.warning("Cannot check liquidation: Unable to get mark price")
            return False

        # Calculate position value and unrealized PnL
        position_type = self.position['type']
        entry_price = self.position['entry']
        size = self.position['size']
        leverage = self.position.get('leverage', 1.0)

        # Calculate margin ratio using our new function
        margin_ratio = self.calculate_margin_ratio(current_price)

        # Calculate maintenance margin for logging
        notional_value = size * current_price
        self.maintenance_margin = notional_value * self.maintenance_margin_fraction

        # Calculate unrealized PnL for logging
        if position_type == 'long':
            unrealized_pnl = (current_price - entry_price) * size
        else: # short
            unrealized_pnl = (entry_price - current_price) * size

        # Calculate equity (balance + unrealized PnL)
        equity = self.balance + self.reserved_margin + unrealized_pnl

        # Get margin health status
        health_status = self.get_margin_health_status(current_price)

        # Log margin health information
        if health_status == 'danger':
            logger.warning(f"MARGIN HEALTH DANGER: Ratio={margin_ratio:.2f}x, Equity=${equity:.2f}, MM=${self.maintenance_margin:.2f}")
        elif health_status == 'warning':
            logger.info(f"MARGIN HEALTH WARNING: Ratio={margin_ratio:.2f}x, Equity=${equity:.2f}, MM=${self.maintenance_margin:.2f}")

        # Check if margin ratio is at or below 1.0 (liquidation threshold)
        if margin_ratio <= 1.0:
            # Calculate liquidation price for logging
            liq_price = self.calculate_liquidation_price()

            logger.warning(f"LIQUIDATION TRIGGERED: Margin Ratio ({margin_ratio:.2f}x) <= 1.0")
            logger.warning(f"  Position: {position_type.upper()} {size:.4f} @ ${entry_price:.2f}, Current Price: ${current_price:.2f}")
            logger.warning(f"  Liquidation Price: ${liq_price:.2f}, Unrealized PnL: ${unrealized_pnl:.2f}")
            logger.warning(f"  Account Value: ${equity:.2f}, Maintenance Margin: ${self.maintenance_margin:.2f}")

            return True

        # Not liquidated
        return False

    def apply_funding(
        self,
        current_timestamp_unix: float,
        current_price: float,
        last_funding_time_unix: Optional[float],
        symbol: str = "BTC-PERP"
    ) -> Optional[float]:
        """Applies funding payment/receipt if a funding interval has passed."""
        if not self.position:
            return last_funding_time_unix # No funding if no position

        funding_interval = self.config.FUNDING_INTERVAL_SECONDS
        # Get the funding rate dynamically if possible, otherwise from config
        funding_rate = self.config.costs.funding_rate  # default fallback
        if self.data_handler and hasattr(self.data_handler, "get_funding_rate"):
            try:
                # Try with 3 args (self, symbol, timestamp)
                funding_rate = self.data_handler.get_funding_rate(symbol, current_timestamp_unix)
            except TypeError:
                try:
                    # Try with 2 args (self, timestamp) - original interface
                    funding_rate = self.data_handler.get_funding_rate(current_timestamp_unix)
                except Exception as e:
                    logger.warning(f"Could not get dynamic funding rate: {e}. Using fallback.")
        logger.debug(f"Using funding rate: {funding_rate:.6f}")


        logger.debug(f"apply_funding: ENTRY - current_ts={current_timestamp_unix}, last_funding_ts={last_funding_time_unix}, entry_time={self.position.get('entry_time')}, interval={funding_interval}")

        # Initialize last funding time if this is the first check for this position
        if last_funding_time_unix is None:
            last_funding_time_unix = self.position['entry_time']
            logger.debug(f"Initializing last funding time for position to entry time: {datetime.utcfromtimestamp(last_funding_time_unix)}")

        next_funding_time_unix = last_funding_time_unix + funding_interval

        if current_timestamp_unix >= next_funding_time_unix:
            logger.debug(f"apply_funding: Funding condition met. next_funding_time_unix={next_funding_time_unix}")
            position_value = self.position['size'] * current_price # Value at current price
            funding_payment = position_value * funding_rate

            # Long pays if rate is positive, receives if negative
            # Short receives if rate is positive, pays if negative
            if self.position['type'] == 'long':
                funding_pnl = -funding_payment
            else: # short
                funding_pnl = funding_payment

            # Update balance and total funding PnL tracker
            self.balance += funding_pnl
            self.total_funding_pnl += funding_pnl
            self.position["cum_funding_pnl"] = self.position.get("cum_funding_pnl", 0.0) + funding_pnl
            logger.info(f"FUNDING Applied at {datetime.utcfromtimestamp(current_timestamp_unix)}: "
                        f"Rate={funding_rate:.6f}, PosVal=${position_value:.2f}, PnL=${funding_pnl:.4f}, New Balance=${self.balance:.2f}")

            # Update last funding time to the time it just occurred
            # To handle multiple funding events within one bar, loop might be needed,
            # but for hourly bars and 8h funding, one check is usually sufficient.
            # We set the last funding time to the *scheduled* time it occurred.
            new_last_funding_time = next_funding_time_unix
            logger.debug(f"apply_funding: Before while loop - new_last_funding_time={new_last_funding_time}")
            # Advance funding time until it's past the current time
            while new_last_funding_time + funding_interval <= current_timestamp_unix:
                 logger.warning("Multiple funding intervals passed within one step. Applying funding again.")
                 # Recalculate based on potentially changed price? Or use same rate/value? Use same for simplicity.
                 self.balance += funding_pnl
                 self.total_funding_pnl += funding_pnl
                 logger.info(f"FUNDING Applied (catch-up): PnL=${funding_pnl:.4f}, New Balance=${self.balance:.2f}")
                 new_last_funding_time += funding_interval

            logger.debug(f"apply_funding: RETURN (funding applied) - new_last_funding_time={new_last_funding_time}")
            return new_last_funding_time # Return the timestamp of the last applied funding
        else:
            logger.debug(f"apply_funding: RETURN (no funding) - last_funding_time_unix={last_funding_time_unix}")
            return last_funding_time_unix # No funding applied, return previous time


    def handle_entry(self, position_dict: Dict[str, Any], fill_price: float, filled_size: float, slippage_pnl: float, timestamp_unix: float):
        """Updates portfolio state upon entering a new position."""
        if self.position:
            logger.error("Cannot handle entry: Already in a position.")
            return

        fee_mode = position_dict.get("fee_mode", "taker") # Default to taker
        fee_rate = self.config.costs.taker_fee if fee_mode == "taker" else self.config.costs.maker_fee
        # Safely get leverage, default to 1.0 if not specified (though it should be)
        leverage = position_dict.get("leverage", 1.0)
        # 1) Notional & initial‑margin
        notional        = filled_size * fill_price
        entry_fee       = notional * fee_rate
        initial_margin  = notional / leverage

        # 2) Check & lock collateral based on margin mode
        if self.balance < entry_fee:
            self.logger.warning(f"FEE CHECK FAILED: Balance ${self.balance:.2f} < Required Fee ${entry_fee:.2f}")
            raise ValueError("Insufficient free balance for required fee")

        # Additional checks for isolated margin mode
        if self.margin_mode == 'isolated':
            # In isolated mode, we need to ensure we have enough balance to cover the initial margin
            if self.balance < initial_margin + entry_fee:
                self.logger.warning(f"ISOLATED MARGIN CHECK FAILED: Balance ${self.balance:.2f} < Required ${(initial_margin + entry_fee):.2f}")
                self.logger.warning(f"ISOLATED MARGIN DETAILS: Notional=${notional:.2f}, Leverage={leverage:.1f}, IM=${initial_margin:.2f}, Fee=${entry_fee:.2f}")
                raise ValueError("Insufficient balance for isolated margin position")
            self.logger.info(f"MARGIN INFO: Mode=isolated, Balance ${self.balance:.2f}, IM: ${initial_margin:.2f}, Fee: ${entry_fee:.2f}")
        else:  # cross margin
            # In cross margin mode, we only need to check if we can cover the fee
            # The margin is shared across positions
            self.logger.info(f"MARGIN INFO: Mode=cross, Balance ${self.balance:.2f}, IM: ${initial_margin:.2f}, Fee: ${entry_fee:.2f}")

        # Track the margin for accounting purposes
        # This simulates how Hyperliquid actually works with sequential trading
        self.balance -= initial_margin  # Reserve the margin from balance
        self.reserved_margin = initial_margin  # Track the reserved margin

        self.logger.debug(f"handle_entry: fee_mode={fee_mode}, fee_rate={fee_rate:.6f}, leverage={leverage:.1f}, entry_fee={entry_fee:.6f}, initial_margin={initial_margin:.6f}")

        # Log received slippage from execution simulator
        self.logger.debug(f"HandleEntry: Received entry_slippage_pnl = {slippage_pnl:.4f}")

        self.balance -= entry_fee
        self.total_fees_paid += entry_fee
        self.total_slippage_pnl += slippage_pnl # ADD P&L impact

        # Update position dictionary with actual fill details
        position_dict['entry'] = fill_price
        position_dict['size'] = filled_size
        position_dict['entry_fee'] = entry_fee
        # Store raw P&L impact
        position_dict['entry_slippage_pnl'] = slippage_pnl
        position_dict['entry_time'] = timestamp_unix # Ensure entry time is set correctly
        self.logger.debug(f"HandleEntry: Stored entry_slippage_pnl={position_dict.get('entry_slippage_pnl', slippage_pnl):.4f} in position dict.")

        self.position = position_dict
        # Format SL/TP safely in log message
        sl_val = self.position.get('stop')
        tp_val = self.position.get('profit')
        sl_str = f"{sl_val:.2f}" if isinstance(sl_val, (int, float)) else str(sl_val or 'N/A')
        tp_str = f"{tp_val:.2f}" if isinstance(tp_val, (int, float)) else str(tp_val or 'N/A')
        lev_val = self.position.get('leverage')
        lev_str = f"{lev_val:.1f}" if isinstance(lev_val, (int, float)) else str(lev_val or 'N/A')
        slip_pnl_val = self.position.get('entry_slippage_pnl', 0.0)

        logger.info(f"ENTRY: {self.position['type']} {self.position['size']:.4f} at ${self.position['entry']:.2f} "
                    f"(Lev={lev_str}x, SL={sl_str}, TP={tp_str}, "
                    f"Fee=${entry_fee:.4f}, SlipPnL=${slip_pnl_val:.4f}, Bal=${self.balance:.2f})")

        # Record equity after balance update
        self._record_equity(timestamp_unix)

    def handle_exit(self, fill_price: float, exit_reason: str, slippage_pnl: float, current_timestamp_unix: float, fee_mode: str = "taker", is_liquidation: bool = False):
        """Updates portfolio state upon exiting a position."""
        if not self.position:
            logger.error("Cannot handle exit: No position currently held.")
            return

        try:
            entry_price = self.position['entry']
            exit_size = self.position['size'] # Assume full exit
            position_type = self.position['type']
            entry_fee = self.position['entry_fee']

            # Calculate gross profit (before fees and slippage)
            if position_type == 'long':
                gross_profit = (fill_price - entry_price) * exit_size
            else: # short
                gross_profit = (entry_price - fill_price) * exit_size

            # Calculate exit fee based on provided mode
            fee_rate = self.config.costs.taker_fee if fee_mode == "taker" else self.config.costs.maker_fee
            leverage = self.position.get("leverage", 1.0)
            self.logger.debug(f"handle_exit: fee_mode={fee_mode}, fee_rate={fee_rate:.6f}, exit_size={exit_size}, fill_price={fill_price:.2f}, leverage={leverage:.1f}")
            # Calculate fee based on notional value (Size * Price), excluding leverage
            exit_fee = exit_size * fill_price * fee_rate

            # Calculate net profit/loss for the trade
            entry_slippage_pnl = self.position.get('entry_slippage_pnl', 0.0)
            # Net PnL = (Exit Price - Entry Price) * Size * Direction - Total Fees + Total Slippage PnL
            # Direction factor: 1 for long, -1 for short
            direction_multiplier = 1 if position_type == 'long' else -1
            price_diff_pnl = (fill_price - entry_price) * exit_size * direction_multiplier
            total_fees = entry_fee + exit_fee
            total_slippage = entry_slippage_pnl + slippage_pnl
            # Calculate net profit in base currency units, excluding leverage multiplier here
            trade_net_profit = price_diff_pnl - total_fees + total_slippage
            self.logger.debug(f"HandleExit Calc: Final trade_net_profit = {trade_net_profit:.4f} (PriceDiffPnL={price_diff_pnl:.4f}, TotalFees={total_fees:.4f}, TotalSlippage={total_slippage:.4f}, EntryFee={entry_fee:.4f}, ExitFee={exit_fee:.4f}, EntrySlip={entry_slippage_pnl:.4f}, ExitSlip={slippage_pnl:.4f})")

            balance_before_exit = self.balance
            # Optional: Log balance change details
            # logger.info(f"Updating balance: Before={self.balance:.2f}, TradeNetPnL={trade_net_profit:.2f}, After={(self.balance + trade_net_profit):.2f}")
            # 1) Refund the reserved margin **before** PnL
            self.balance += self.reserved_margin
            self.reserved_margin = 0.0

            # 2) Apply trade PnL
            self.balance += trade_net_profit
            bankrupted = False
            original_exit_reason = exit_reason # Store original reason before potential modification

            # Check for bankruptcy AFTER applying the net_pnl
            if self.balance <= 0 and not np.isclose(self.balance, 0):
                self.logger.error(f"BANKRUPTCY on exit! Bal Before=${balance_before_exit:.4f}, Net PnL=${trade_net_profit:.4f}, Bal After=${self.balance:.4f}")
                actual_loss = -balance_before_exit
                trade_net_profit = actual_loss # Update trade profit to reflect actual loss
                self.balance = 0
                exit_reason += "_BANKRUPTCY"
                bankrupted = True
            else:
                # Note: 'gross_profit' here is pre-direction adjustment. 'price_diff_pnl' is the directional PnL before fees/slippage.
                self.logger.debug(f"EXIT_TRACE: ts={current_timestamp_unix}, gross(non-dir)={gross_profit:.4f}, price_diff_pnl={price_diff_pnl:.4f}, entry_fee={entry_fee:.4f}, exit_fee={exit_fee:.4f}, slip_entry={self.position.get('entry_slippage_pnl', 0):.4f}, slip_exit={slippage_pnl:.4f}, net={trade_net_profit:.4f}, new_bal={self.balance:.4f}")

            # Create trade record (uses potentially adjusted profit/reason)
            trade = {
                "entry_time": self.position['entry_time'],
                "exit_time": current_timestamp_unix,
                "type": position_type,
                "entry": entry_price,
                "exit": fill_price,
                "size": exit_size,
                "leverage": self.position['leverage'],
                "stop_loss": self.position.get('stop'),
                "take_profit": self.position.get('profit'),
                "entry_fee": entry_fee,
                "exit_fee": exit_fee,
                "entry_slippage_pnl": self.position.get('entry_slippage_pnl', 0),
                "exit_slippage_pnl": slippage_pnl,
                "funding_pnl": self.position.get("cum_funding_pnl", 0.0),
                "profit": trade_net_profit, # Use the potentially adjusted net profit
                "exit_reason": exit_reason, # Use potentially adjusted exit_reason
                "strategy": self.position['strategy'],
                "entry_regime": self.position.get('entry_regime', 'N/A')
            }
            self.trades.append(trade)

            # Update global counters
            self.total_fees_paid += exit_fee
            self.total_slippage_pnl += slippage_pnl

            # Log the exit
            exit_outcome = "LOSS" if trade_net_profit < 0 else "WIN"
            if is_liquidation: exit_outcome = "LIQUIDATION"
            log_reason = exit_reason if bankrupted else original_exit_reason # Use modified reason only if bankrupt
            logger.info(f"EXIT: {exit_outcome} ({log_reason}) Closed {position_type} {exit_size:.4f} at ${fill_price:.2f}. "
                      f"Net PnL: ${trade_net_profit:.4f}. New Balance: ${self.balance:.2f}")

            self._record_equity(current_timestamp_unix)

            # Check for bankruptcy again for critical logging and raise exception
            if bankrupted:
                raise ValueError("Account liquidated on exit") # Raise exception AFTER state update

        finally:
            # Ensure position is always cleared after an exit attempt
            self.position = None
            logger.debug("Position cleared in handle_exit finally block.")

    def get_free_balance(self) -> float:
        """Returns the available balance for new trades (total balance minus reserved margin)."""
        if self.margin_mode == 'isolated':
            # In isolated mode, each position needs its own separate margin
            return self.balance
        else:  # cross margin mode
            # In cross margin mode, the entire account value is available for new positions
            # We still need to ensure there's enough to cover fees, but margin is shared
            return self.balance + self.reserved_margin

    def calculate_liquidation_price(self) -> float:
        """Calculate the liquidation price for the current position.

        Returns:
            float: The price at which the position would be liquidated, or 0 if no position exists.
        """
        if not self.position:
            return 0.0

        entry_price = self.position['entry']
        leverage = self.position.get('leverage', 1.0)
        position_type = self.position['type']
        size = self.position['size']

        # Calculate liquidation price based on margin mode
        if self.margin_mode == 'isolated':
            # In isolated mode, only the reserved margin is considered
            available_margin = self.reserved_margin
        else:  # cross margin
            # In cross margin mode, the entire account balance + reserved margin is available
            available_margin = self.balance + self.reserved_margin

        # Calculate the bankruptcy price (where all margin is depleted)
        if position_type == 'long':
            # For longs: liquidation occurs when price falls to a level where equity = maintenance margin
            # Standard formula: entry_price * (1 - (1/leverage - maintenance_margin_fraction))
            # Adjusted formula for available margin: entry_price - (available_margin / size)
            bankruptcy_price = entry_price - (available_margin / size)
            # Liquidation happens slightly above bankruptcy price
            liquidation_price = max(bankruptcy_price * 1.01, entry_price * (1 - (1 / leverage - self.maintenance_margin_fraction)))
        else:  # short
            # For shorts: liquidation occurs when price rises to a level where equity = maintenance margin
            # Standard formula: entry_price * (1 + (1/leverage - maintenance_margin_fraction))
            # Adjusted formula for available margin: entry_price + (available_margin / size)
            bankruptcy_price = entry_price + (available_margin / size)
            # Liquidation happens slightly below bankruptcy price
            liquidation_price = min(bankruptcy_price * 0.99, entry_price * (1 + (1 / leverage - self.maintenance_margin_fraction)))

        self.logger.debug(f"MARGIN_DEBUG: Calculated liquidation price: ${liquidation_price:.2f} for {position_type} position with {self.margin_mode} margin")
        return liquidation_price

    def calculate_dynamic_leverage(self, current_price: float = None, volatility: float = None, timestamp: Optional[float] = None) -> float:
        """
        Calculate dynamic leverage based on market volatility.
        Lower leverage during high volatility, higher leverage during low volatility.

        Args:
            current_price: The current price of the asset (optional, used only for logging)
            volatility: Volatility metric (e.g., ATR % of price). If None, not used.
            timestamp: Optional timestamp for logging purposes

        Returns:
            float: Adjusted leverage value based on volatility
        """
        # Check if dynamic leverage is enabled in config
        use_dynamic_leverage = getattr(self.config.portfolio, 'use_dynamic_leverage', False)
        if not use_dynamic_leverage:
            # Return the base leverage unchanged if not enabled
            base_leverage = self.config.portfolio.max_leverage
            self.logger.debug(f"Dynamic leverage disabled, using base leverage: {base_leverage:.2f}x")
            return base_leverage

        # Get base leverage from config
        base_leverage = self.config.portfolio.max_leverage

        # If volatility is not provided, we can't adjust leverage
        if volatility is None or pd.isna(volatility) or volatility <= 0:
            self.logger.warning(f"Cannot calculate dynamic leverage: Invalid volatility ({volatility}). Using base leverage: {base_leverage:.2f}x")
            return base_leverage

        # Scale volatility to a more sensitive range (0.4 to 1.6)
        # Higher volatility = Lower multiplier (0.4), Lower volatility = Higher multiplier (1.6)
        vol_multiplier = max(0.4, min(1.6, 1.0 / (volatility * 20 + 0.5)))

        # Apply multiplier to base leverage
        adjusted_leverage = base_leverage * vol_multiplier

        # Cap at asset max leverage and minimum of 1.0
        max_allowed = self.config.portfolio.asset_max_leverage
        min_allowed = getattr(self.config.indicators, 'min_leverage', 1.0)  # Fallback to 1.0 if not configured

        adjusted_leverage = max(min_allowed, min(adjusted_leverage, max_allowed))

        # Add comprehensive logging
        time_str = ""
        if timestamp is not None:
            try:
                # Handle both Unix timestamps (float/int) and pandas Timestamp objects
                if isinstance(timestamp, (pd.Timestamp, datetime)):
                    time_str = f" at {timestamp}"
                else:
                    # Assume it's a Unix timestamp (float/int)
                    time_str = f" at {datetime.fromtimestamp(timestamp, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S')}"
            except Exception as e:
                self.logger.warning(f"Error formatting timestamp for logging: {e}")

        price_str = f" price ${current_price:.2f}" if current_price else ""

        self.logger.info(f"Dynamic leverage calculation{time_str}{price_str}: volatility={volatility:.5f}, "
                        f"multiplier={vol_multiplier:.2f}, adjusted leverage={adjusted_leverage:.2f}x")

        return adjusted_leverage

    def calculate_margin_ratio(self, current_price: float = None) -> float:
        """Calculate the current margin ratio (account value / maintenance margin).

        Args:
            current_price: The current price of the asset. If None, uses the entry price.

        Returns:
            float: The margin ratio, or float('inf') if no position exists.
        """
        if not self.position:
            return float('inf')  # No position means no margin requirement

        # Use provided price or fall back to entry price
        price = current_price if current_price is not None else self.position['entry']

        # Calculate notional value
        size = self.position['size']
        notional = size * price

        # Calculate maintenance margin requirement
        self.maintenance_margin = notional * self.maintenance_margin_fraction

        # Calculate account value based on margin mode
        if self.margin_mode == 'isolated':
            # In isolated mode, only the reserved margin for this position is considered
            account_value = self.reserved_margin
            self.logger.debug(f"MARGIN_DEBUG: Using isolated margin. Reserved margin: ${self.reserved_margin:.2f}")
        else:  # cross margin
            # In cross margin mode, the entire account balance + reserved margin is available
            account_value = self.balance + self.reserved_margin
            self.logger.debug(f"MARGIN_DEBUG: Using cross margin. Total account value: ${account_value:.2f}")

        # Calculate margin ratio
        margin_ratio = account_value / self.maintenance_margin if self.maintenance_margin > 0 else float('inf')

        return margin_ratio

    def get_margin_health_status(self, current_price: float = None) -> str:
        """Get the current margin health status based on the margin ratio.

        Args:
            current_price: The current price of the asset. If None, uses the entry price.

        Returns:
            str: The margin health status ('safe', 'warning', 'danger', or 'none' if no position).
        """
        if not self.position:
            return 'none'

        margin_ratio = self.calculate_margin_ratio(current_price)

        if margin_ratio < self.margin_health_danger:
            return 'danger'
        elif margin_ratio < self.margin_health_warning:
            return 'warning'
        else:
            return 'safe'

    def _record_equity(self, timestamp_unix: float):
        """Records the current balance and timestamp for the equity curve."""
        # Equity = free balance + reserved margin (unrealized PnL is added by backtester)
        equity_now = self.balance + self.reserved_margin

        # DEBUG: Log equity recording
        self.logger.debug(f"EQUITY_DEBUG: Recording equity=${equity_now:.2f} (balance=${self.balance:.2f} + reserved=${self.reserved_margin:.2f}) at {datetime.fromtimestamp(timestamp_unix, tz=timezone.utc)}")

        # Note: We don't calculate unrealized PnL here as we don't have access to current market data
        # The backtester should handle this by passing signals to calculate_account_value() instead

        # Avoid duplicate timestamps if multiple events happen at the same time
        if not self.equity_timestamps or timestamp_unix > self.equity_timestamps[-1]:
            self.equity_curve.append(equity_now)
            self.equity_timestamps.append(timestamp_unix)
            self.logger.debug(f"EQUITY_DEBUG: Added new equity point: ${equity_now:.2f} at index {len(self.equity_curve)-1}")
        elif timestamp_unix == self.equity_timestamps[-1]:
            # Update the last equity value if timestamp is the same
            old_value = self.equity_curve[-1]
            self.equity_curve[-1] = equity_now
            self.logger.debug(f"EQUITY_DEBUG: Updated last equity point from ${old_value:.2f} to ${equity_now:.2f} at index {len(self.equity_curve)-1}")

        # Sanity check to ensure arrays are in sync
        if len(self.equity_curve) != len(self.equity_timestamps):
            # Log the issue
            logging.getLogger(__name__).warning(
                f"Equity curve and timestamps out of sync: curve={len(self.equity_curve)}, timestamps={len(self.equity_timestamps)}. Fixing..."
            )
            # Fix by truncating to the shorter length
            min_len = min(len(self.equity_curve), len(self.equity_timestamps))
            self.equity_curve = self.equity_curve[:min_len]
            self.equity_timestamps = self.equity_timestamps[:min_len]