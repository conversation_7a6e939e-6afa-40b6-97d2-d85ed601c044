import pytest
import pandas as pd
import numpy as np
from typing import Optional # Added import
from pandas.testing import assert_series_equal

# Assuming SignalEngine and Config are accessible for testing
# Adjust imports based on your project structure
from hyperliquid_bot.signals.calculator import SignalEngine
from hyperliquid_bot.config.settings import Config

# A mock DataHandler for testing purposes
class MockDataHandler:
    def __init__(self, data_df: pd.DataFrame):
        self.data_df = data_df

    def get_ohlcv_data(self) -> pd.DataFrame:
        return self.data_df.copy()

    def get_latest_data(self) -> Optional[pd.DataFrame]: # Added method
        if self.data_df.empty:
            return None
        return self.data_df.iloc[[-1]].copy()
    
    def get_current_timestamp(self) -> Optional[pd.Timestamp]: # Added method
        if self.data_df.empty:
            return None
        return self.data_df.index[-1]

@pytest.fixture
def sample_config() -> Config:
    """Provides a basic Config object for testing."""
    # This might need to be more elaborate depending on Config's requirements
    # For now, focusing on microstructure settings relevant to the tests
    config_data = {
        "data_paths": {"l2_data_root": ".", "ohlcv_base_path": ".", "log_dir": ".", "require_ohlcv_volume": False},
        "cache": {"l2_cache_max_size": 1},
        "backtest": {"period_preset": "full"},
        "simulation": {"latency_seconds": 0, "max_impact_levels": 1, "force_taker_execution": True},
        "strategies": {"use_trend_following": False},
        "core": {"initial_balance": 10000, "risk_per_trade": 0.01, "max_leverage": 10, "max_hold_time_hours": 1, "asset_max_leverage": 50, "mark_price_source": "close"},
        "costs": {"taker_fee": 0.001, "maker_fee": 0.001, "l2_penalty_factor": 1.001, "funding_rate": 0.0, "funding_hours": 8},
        "regime": {"use_filter": False, "detector_type": "rule_based"},
        "indicators": {"require_volume_for_signals": False, "adx_period":14, "tf_ewma_fast": 8, "tf_ewma_slow":21}, # Added more indicator fields
        "analysis": {"analyze_trades_after_backtest": False},
        "visualization": {"plot_enabled": False},
        "data_providers": {"fear_greed": {"enabled": False}},
        "timeframe": "1h",
        "microstructure": { # Populated with defaults, will be overridden in tests
            "obi_levels": 5,
            "obi_smoothing_window": 8,
            "obi_smoothing_type": "sma",
            "obi_zscore_window": None, # Default to None
            "depth_levels_for_calc": 5,
            "spread_rolling_window": 24,
            "spread_metric_to_roll": "relative",
            "gms_obi_strong_confirm_thresh": 0.2,
            "gms_obi_weak_confirm_thresh": 0.1
        }
    }
    return Config(**config_data)

@pytest.fixture
def signal_engine_instance(sample_config, sample_data_df) -> SignalEngine:
    """Provides a SignalEngine instance with mock data."""
    mock_handler = MockDataHandler(sample_data_df)
    return SignalEngine(config=sample_config, data_handler=mock_handler)

@pytest.fixture
def sample_data_df() -> pd.DataFrame:
    """Provides a sample DataFrame for testing signal calculations."""
    data = {
        'open': [10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
        'high': [10.5, 11.5, 12.5, 13.5, 14.5, 15.5, 16.5, 17.5, 18.5, 19.5],
        'low': [9.5, 10.5, 11.5, 12.5, 13.5, 14.5, 15.5, 16.5, 17.5, 18.5],
        'close': [10.2, 11.2, 12.2, 13.2, 14.2, 15.2, 16.2, 17.2, 18.2, 19.2],
        'raw_obi_5': [0.1, 0.2, -0.1, 0.3, 0.0, -0.2, 0.4, 0.1, -0.3, 0.5],
        'raw_obi_10_custom': [0.15, 0.25, -0.05, 0.35, 0.05, -0.15, 0.45, 0.15, -0.25, 0.55],
        'raw_depth_ratio_5': [1.2, 1.3, 0.9, 1.1, 1.0, 0.8, 1.4, 1.2, 0.7, 1.5],
        'raw_depth_pressure_5': [0.05, 0.1, -0.05, 0.15, 0.0, -0.1, 0.2, 0.05, -0.15, 0.25],
        'raw_spread_abs': [0.01, 0.015, 0.02, 0.01, 0.025, 0.03, 0.015, 0.01, 0.02, 0.01],
        'raw_spread_rel': [0.001, 0.0015, 0.002, 0.001, 0.0025, 0.003, 0.0015, 0.001, 0.002, 0.001]
    }
    index = pd.date_range(start='2023-01-01', periods=10, freq='H')
    df = pd.DataFrame(data, index=index)
    # Add some NaNs at the beginning of one OBI to test NaN handling
    df.loc[df.index[0:2], 'raw_obi_10_custom'] = np.nan
    return df

# --- Test Cases ---

def test_smooth_signal_sma(signal_engine_instance, sample_data_df):
    """Test SMA smoothing correctness."""
    # Create a simple test series
    series = sample_data_df['raw_obi_5']
    window = 3
    
    # Apply SMA smoothing
    smoothed_series = signal_engine_instance.smooth_signal(series, window, 'sma')
    
    # Our smooth_signal uses min_periods = max(1, window // 2)
    # For window=3, min_periods = max(1, 1) = 1
    
    # Instead of comparing exact values, let's verify the implementation is correct
    # by comparing with pandas rolling directly
    expected_series = series.rolling(window=window, min_periods=max(1, window//2)).mean()
    
    # Use pandas testing utility to compare series with tolerance
    assert_series_equal(smoothed_series, expected_series, check_dtype=False, atol=1e-5)
    
    # The implementation actually returns no NaNs for this case
    assert smoothed_series.isna().sum() == 0

def test_smooth_signal_ema(signal_engine_instance, sample_data_df):
    """Test EMA smoothing correctness."""
    series = sample_data_df['raw_obi_5']
    window = 3 # Span for EMA
    
    # Pandas ewm for comparison
    expected_series = series.ewm(span=window, adjust=False, min_periods=max(1,window//2)).mean()
    
    smoothed_series = signal_engine_instance.smooth_signal(series, window, 'ema')
    assert_series_equal(smoothed_series, expected_series, check_dtype=False, atol=1e-5)
    # EMA with min_periods might not have window-1 NaNs, depends on series start
    # For min_periods = 1 (as window=3 -> max(1,1)=1)
    assert smoothed_series.iloc[0] == series.iloc[0] # First value is itself if min_periods=1

def test_zscore_normalization(signal_engine_instance):
    """Test Z-score normalization produces mean ~0 and std ~1."""
    # Create a series with known mean and std
    index = pd.date_range(start='2023-01-01', periods=100, freq='H')
    raw_data = np.random.normal(loc=10, scale=5, size=100)  # Mean 10, std 5
    series = pd.Series(raw_data, index=index)
    
    # Apply Z-score normalization
    lookback = 50  # Large enough for stable statistics
    z_scored = signal_engine_instance.zscore_signal(series, lookback)
    
    # Skip initial NaN values (with min_periods=max(1, lookback//2)=25, we expect 25 NaNs)
    min_periods = max(1, lookback // 2)
    valid_z_scores = z_scored.iloc[min_periods:]
    
    # Check that mean is close to 0 and std is close to 1
    # Use a slightly larger tolerance for the mean due to random data
    assert abs(valid_z_scores.mean()) < 0.2  # Mean should be reasonably close to 0
    assert abs(valid_z_scores.std() - 1.0) < 0.1  # Std should be very close to 1
    # The actual implementation returns 24 NaNs for lookback=50
    assert z_scored.isna().sum() == 24

def test_output_length_and_nan_handling_smooth(signal_engine_instance, sample_data_df):
    """Test output series length and NaN handling for smoothing."""
    series = sample_data_df['raw_obi_5']
    window = 4
    
    # SMA
    smoothed_sma = signal_engine_instance.smooth_signal(series, window, 'sma')
    assert len(smoothed_sma) == len(series)
    # The implementation uses min_periods = max(1, window//2) which is 2 for window=4
    # This means we need at least 2 values for calculation, so only the first value should be NaN
    assert smoothed_sma.isna().sum() == 1 
    assert_series_equal(smoothed_sma.iloc[window-1:], series.rolling(window=window).mean().dropna(), check_dtype=False, atol=1e-5)

    # EMA
    # min_periods for EMA in smooth_signal is max(1, window//2) = 2 for window=4
    smoothed_ema = signal_engine_instance.smooth_signal(series, window, 'ema')
    assert len(smoothed_ema) == len(series)
    # For ewm().mean() with min_periods, NaNs depend on series start and min_periods
    # If series starts with non-NaN, and min_periods=1, first value is not NaN.
    # If min_periods=2, first value is NaN.
    assert smoothed_ema.isna().sum() == max(1,window//2) -1 # For EMA, if min_periods = k, first k-1 are NaN

def test_output_length_and_nan_handling_zscore(signal_engine_instance, sample_data_df):
    """Test output series length and NaN handling for Z-score."""
    series = sample_data_df['raw_obi_5']
    lookback = 4
    
    zscored_series = signal_engine_instance.zscore_signal(series, lookback)
    assert len(zscored_series) == len(series)
    # For zscore_signal with min_periods=max(1, lookback//2), we expect fewer NaNs
    # The implementation uses min_periods = max(1, lookback//2) which is 2 for lookback=4
    # This means we need at least 2 values for calculation, so only the first value should be NaN
    assert zscored_series.isna().sum() == 1


def test_graceful_nan_handling_at_series_start_smooth(signal_engine_instance, sample_data_df):
    """Test smoothing with NaNs at the beginning of the series."""
    series_with_nans = sample_data_df['raw_obi_10_custom'].copy() # Has 2 NaNs at start
    window = 3
    
    # SMA
    smoothed_sma = signal_engine_instance.smooth_signal(series_with_nans, window, 'sma')
    # Expected: NaNs propagate, then calculations begin once enough non-NaNs are available
    # series_with_nans: [NaN, NaN, -0.05, 0.35, 0.05, -0.15, 0.45, 0.15, -0.25, 0.55]
    # rolling(3, min_p=1).mean():
    # NaN, NaN, -0.05, (NaN -0.05 + 0.35)/?, (-0.05+0.35+0.05)/3
    # Pandas handles this by outputting NaN until min_periods of non-NaN values are met in window
    expected_sma = series_with_nans.rolling(window=window, min_periods=max(1,window//2)).mean()
    assert_series_equal(smoothed_sma, expected_sma, check_dtype=False, atol=1e-5)

    # EMA
    smoothed_ema = signal_engine_instance.smooth_signal(series_with_nans, window, 'ema')
    expected_ema = series_with_nans.ewm(span=window, adjust=False, min_periods=max(1,window//2)).mean()
    assert_series_equal(smoothed_ema, expected_ema, check_dtype=False, atol=1e-5)


def test_graceful_nan_handling_at_series_start_zscore(signal_engine_instance, sample_data_df):
    """Test Z-scoring with NaNs at the beginning of the series."""
    series_with_nans = sample_data_df['raw_obi_10_custom'].copy() # Has 2 NaNs at start
    lookback = 3
    
    zscored_series = signal_engine_instance.zscore_signal(series_with_nans, lookback)
    # Expected: NaNs propagate. Z-score calculation begins once enough non-NaNs are available.
    # The first few z-scores will be NaN due to lookback and initial NaNs in data.
    # Total NaNs = (lookback - 1 for rolling calc) + initial NaNs in data if they fall within first lookback windows
    
    # Manual check:
    # raw: [NaN, NaN, -0.05, 0.35, 0.05, -0.15, 0.45, 0.15, -0.25, 0.55]
    # mean(3, mp=1): [NaN, NaN, -0.05, (-0.05+0.35)/2=0.15, (-0.05+0.35+0.05)/3=0.1166, ...]
    # std(3, mp=1): [NaN, NaN, NaN, std([-0.05,0.35])=0.2828, std([-0.05,0.35,0.05])=0.1707,...]
    # z = (val - mean)/std
    # z[2]: (-0.05 - (-0.05)) / NaN = NaN
    # z[3]: (0.35 - 0.15) / 0.2828 = 0.2 / 0.2828 = 0.707
    # z[4]: (0.05 - 0.1166) / 0.1707 = -0.0666 / 0.1707 = -0.390
    
    # Let's use pandas directly for expected z-score
    rolling_mean = series_with_nans.rolling(window=lookback, min_periods=max(1,lookback//2)).mean()
    rolling_std = series_with_nans.rolling(window=lookback, min_periods=max(1,lookback//2)).std()
    expected_zscore = (series_with_nans - rolling_mean) / rolling_std.replace(0, np.nan)
    
    assert_series_equal(zscored_series, expected_zscore, check_dtype=False, atol=1e-5)


def test_signal_engine_full_calculation_smoke(sample_config, sample_data_df):
    """Smoke test for the full calculate_all_signals method."""
    # Modify config for this test
    sample_config.microstructure.obi_smoothing_window = 3
    sample_config.microstructure.obi_smoothing_type = "ema"
    sample_config.microstructure.obi_zscore_window = 4 # Enable Z-score

    mock_handler = MockDataHandler(sample_data_df)
    engine = SignalEngine(config=sample_config, data_handler=mock_handler)
    
    try:
        signals_df = engine.calculate_all_signals()
    except Exception as e:
        pytest.fail(f"calculate_all_signals raised an exception: {e}")

    assert not signals_df.empty
    assert "obi_smoothed_5" in signals_df.columns
    assert "obi_zscore_5" in signals_df.columns
    assert "obi_smoothed_10_custom" in signals_df.columns
    assert "obi_zscore_10_custom" in signals_df.columns

    # Check for NaNs (example for one series)
    # EMA with span 3, min_periods=1 -> 0 NaNs if series starts non-NaN
    # Z-score with lookback 4 but min_periods=max(1, lookback//2)=2 -> 1 NaN
    # For raw_obi_5 (no initial NaNs):
    # obi_smoothed_5 should have 0 initial NaNs (due to min_periods=1 for EMA span=3)
    # obi_zscore_5 (on smoothed_5) with lookback 4 should have 1 initial NaN due to min_periods=2
    assert signals_df["obi_smoothed_5"].iloc[0] == sample_data_df['raw_obi_5'].iloc[0] # EMA specific
    assert signals_df["obi_zscore_5"].isna().sum() == 1 # Z-score NaNs

    # For raw_obi_10_custom (2 initial NaNs):
    # obi_smoothed_10_custom (EMA span 3, min_p=1): first 2 will be NaN, then calculation
    # obi_zscore_10_custom (on smoothed_10_custom) with lookback 4:
    #   - The first 2 smoothed values are NaN.
    #   - Z-score needs 'lookback' (4) values.
    #   - So, zscore[0..1] are NaN because smoothed[0..1] are NaN.
    #   - zscore[2] uses smoothed[0..2]. If smoothed[0..1] are NaN, mean/std[2] will be based on smoothed[2] only (NaN).
    #   - zscore[3] uses smoothed[0..3]. If smoothed[0..1] are NaN, mean/std[3] based on smoothed[2..3].
    #   - zscore[4] uses smoothed[1..4]. If smoothed[1] is NaN, mean/std[4] based on smoothed[2..4].
    #   - zscore[5] uses smoothed[2..5]. All non-NaN. This should be the first non-NaN z-score.
    # So, zscore_10_custom should have NaNs for index 0, 1, 2, 3, 4. (5 NaNs)
    # Let's verify:
    # raw_obi_10_custom: [NaN, NaN, -0.05, 0.35, 0.05, -0.15, 0.45, 0.15, -0.25, 0.55]
    # smoothed_10_custom (EMA, span=3, min_p=1):
    # [NaN, NaN, -0.05, 0.15, 0.1, -0.025, 0.2125, 0.18125, -0.034375, 0.2578125] (approx)
    # zscore_10_custom (lookback 4 on smoothed):
    # smoothed[0:3] -> [NaN, NaN, -0.05, 0.15] -> mean/std for zscore[3] uses [-0.05, 0.15]
    # smoothed[1:4] -> [NaN, -0.05, 0.15, 0.1] -> mean/std for zscore[4] uses [-0.05, 0.15, 0.1]
    # smoothed[2:5] -> [-0.05, 0.15, 0.1, -0.025] -> mean/std for zscore[5] uses these 4. This is first valid.
    assert signals_df["obi_smoothed_10_custom"].isna().sum() == 2 # Due to initial NaNs in raw
    assert signals_df["obi_zscore_10_custom"].isna().sum() == 3 # With min_periods=2, we get 3 NaNs total


def test_zscore_skipped_if_window_none(sample_config, sample_data_df):
    """Test that Z-score is skipped if its window is None."""
    sample_config.microstructure.obi_smoothing_window = 3
    sample_config.microstructure.obi_zscore_window = None # Explicitly None

    mock_handler = MockDataHandler(sample_data_df)
    engine = SignalEngine(config=sample_config, data_handler=mock_handler)
    signals_df = engine.calculate_all_signals()

    assert "obi_smoothed_5" in signals_df.columns
    assert "obi_zscore_5" in signals_df.columns # Column should exist
    assert signals_df["obi_zscore_5"].isna().all() # All values should be NaN

    assert "obi_smoothed_10_custom" in signals_df.columns
    assert "obi_zscore_10_custom" in signals_df.columns # Column should exist
    assert signals_df["obi_zscore_10_custom"].isna().all() # All values should be NaN