import numpy as np
import pytest
from hyperliquid_bot.features.microstructure import calc_obi

# Test data from PRD
fake_book_np_prd = {
    'bid_qty': np.array([100, 80, 60, 40, 20]),
    'ask_qty': np.array([ 90, 70, 50, 30, 10]),
}

def test_calc_obi_prd_cases():
    # Case 1: levels = 1 (int)
    assert np.isclose(calc_obi(fake_book_np_prd, levels=1), 10.0/190.0)

    # Case 2: levels = [1, 2] (list, 1-based) -> indices 0, 1
    assert np.isclose(calc_obi(fake_book_np_prd, levels=[1, 2]), 20.0/340.0)

def test_calc_obi_int_levels():
    book = {'bid_qty': np.array([10,20,30]), 'ask_qty': np.array([10,20,30])}
    assert np.isclose(calc_obi(book, levels=2), 0.0)
    
    book_asym = {'bid_qty': np.array([10,20]), 'ask_qty': np.array([5,5])}
    assert np.isclose(calc_obi(book_asym, levels=2), 0.5)

def test_calc_obi_list_levels():
    book = {'bid_qty': np.array([10,20,30,40]), 'ask_qty': np.array([10,20,30,40])}
    assert np.isclose(calc_obi(book, levels=[1, 3]), 0.0)

    book_asym = {'bid_qty': np.array([10,5,30,5]), 'ask_qty': np.array([5,2,10,2])}
    assert np.isclose(calc_obi(book_asym, levels=[1, 3]), 25.0/55.0)

def test_calc_obi_weights():
    book = {'bid_qty': np.array([10,20]), 'ask_qty': np.array([10,20])}
    assert np.isclose(calc_obi(book, levels=2, weights=[0.7, 0.3]), 0.0)

    book_asym = {'bid_qty': np.array([10,20]), 'ask_qty': np.array([5,5])}
    assert np.isclose(calc_obi(book_asym, levels=2, weights=[0.7, 0.3]), 8.0/18.0)
    assert np.isclose(calc_obi(book_asym, levels=[1,2], weights=[0.7,0.3]), 8.0/18.0)

def test_calc_obi_edge_cases():
    empty_book = {'bid_qty': np.array([]), 'ask_qty': np.array([])}
    assert calc_obi(empty_book, levels=1) == 0.0
    assert calc_obi(empty_book, levels=[1]) == 0.0

    bids_only_book = {'bid_qty': np.array([10, 20]), 'ask_qty': np.array([])}
    assert calc_obi(bids_only_book, levels=1) == 1.0
    assert calc_obi(bids_only_book, levels=[1]) == 1.0
    assert calc_obi(bids_only_book, levels=2) == 1.0

    asks_only_book = {'bid_qty': np.array([]), 'ask_qty': np.array([10, 20])}
    assert calc_obi(asks_only_book, levels=1) == -1.0
    assert calc_obi(asks_only_book, levels=[1]) == -1.0

    book_shallow = {'bid_qty': np.array([10]), 'ask_qty': np.array([10])}
    assert np.isclose(calc_obi(book_shallow, levels=5), 0.0)
    assert np.isclose(calc_obi(book_shallow, levels=[1,2,3], weights=[0.5, 0.3, 0.2]), 0.0)

def test_calc_obi_error_handling():
    book = {'bid_qty': np.array([10]), 'ask_qty': np.array([10])}
    with pytest.raises(TypeError):
        calc_obi(book, levels="invalid")

    assert calc_obi(book, levels=0) == 0.0
    assert calc_obi(book, levels=-1) == 0.0

    assert calc_obi(book, levels=[]) == 0.0
    assert calc_obi(book, levels=[0]) == 0.0
    assert calc_obi(book, levels=[-1]) == 0.0
    assert calc_obi(book, levels=[1,0]) == 0.0

def test_calc_obi_weights_len_mismatch():
    book = {'bid_qty': np.array([10, 20]), 'ask_qty': np.array([10, 20])}
    with pytest.raises(ValueError, match=r"Length of `weights` .* must match the number of selected levels"):
        calc_obi(book, levels=2, weights=[0.5])
    with pytest.raises(ValueError, match=r"Length of `weights` .* must match the number of selected levels"):
        calc_obi(book, levels=[1,2], weights=[0.5])
    with pytest.raises(ValueError, match=r"Length of `weights` .* must match the number of selected levels"):
        calc_obi(book, levels=1, weights=[0.5, 0.5])
    with pytest.raises(ValueError, match=r"Length of `weights` .* must match the number of selected levels"):
        calc_obi(book, levels=[1], weights=[0.5, 0.5])

    book_shallow = {'bid_qty': np.array([10]), 'ask_qty': np.array([10])}
    assert np.isclose(calc_obi(book_shallow, levels=2, weights=[0.7, 0.3]), 0.0)
    assert np.isclose(calc_obi(book_shallow, levels=[1,2], weights=[0.7,0.3]), 0.0)
