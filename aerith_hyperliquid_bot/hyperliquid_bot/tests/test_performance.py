import pytest
import pandas as pd
import numpy as np
import time

from hyperliquid_bot.signals.calculator import SignalEngine
from hyperliquid_bot.config.settings import Config

# A mock DataHandler for testing purposes
class MockDataHandler:
    def __init__(self, data_df: pd.DataFrame):
        self.data_df = data_df

    def get_ohlcv_data(self) -> pd.DataFrame:
        return self.data_df.copy()

    def get_latest_data(self):
        if self.data_df.empty:
            return None
        return self.data_df.iloc[[-1]].copy()
    
    def get_current_timestamp(self):
        if self.data_df.empty:
            return None
        return self.data_df.index[-1]

def create_sample_data(size=1000):
    """Create sample data for performance testing."""
    index = pd.date_range(start='2023-01-01', periods=size, freq='H')
    data = {
        'open': np.random.normal(100, 10, size),
        'high': np.random.normal(105, 10, size),
        'low': np.random.normal(95, 10, size),
        'close': np.random.normal(100, 10, size),
        'raw_obi_5': np.random.normal(0, 0.2, size),
        'raw_obi_10_custom': np.random.normal(0, 0.2, size),
        'raw_depth_ratio_5': np.random.normal(1, 0.2, size),
        'raw_depth_pressure_5': np.random.normal(0, 0.1, size),
        'raw_spread_abs': np.random.normal(0.01, 0.005, size),
        'raw_spread_rel': np.random.normal(0.001, 0.0005, size)
    }
    return pd.DataFrame(data, index=index)

def test_calculate_all_signals_performance():
    """Test that calculate_all_signals completes in less than 100ms for 1000 rows."""
    # Create sample data
    sample_data = create_sample_data(1000)
    
    # Create config
    config_data = {
        "data_paths": {"l2_data_root": ".", "ohlcv_base_path": ".", "log_dir": ".", "require_ohlcv_volume": False},
        "cache": {"l2_cache_max_size": 1},
        "backtest": {"period_preset": "full"},
        "simulation": {"latency_seconds": 0, "max_impact_levels": 1, "force_taker_execution": True},
        "strategies": {"use_trend_following": False},
        "core": {"initial_balance": 10000, "risk_per_trade": 0.01, "max_leverage": 10, "max_hold_time_hours": 1, "asset_max_leverage": 50, "mark_price_source": "close"},
        "costs": {"taker_fee": 0.001, "maker_fee": 0.001, "l2_penalty_factor": 1.001, "funding_rate": 0.0, "funding_hours": 8},
        "regime": {"use_filter": False, "detector_type": "rule_based"},
        "indicators": {"require_volume_for_signals": False, "adx_period":14, "tf_ewma_fast": 8, "tf_ewma_slow":21},
        "analysis": {"analyze_trades_after_backtest": False},
        "visualization": {"plot_enabled": False},
        "data_providers": {"fear_greed": {"enabled": False}},
        "timeframe": "1h",
        "microstructure": {
            "obi_levels": 5,
            "obi_smoothing_window": 8,
            "obi_smoothing_type": "sma",
            "obi_zscore_window": 24,
            "depth_levels_for_calc": 5,
            "spread_rolling_window": 24,
            "spread_metric_to_roll": "relative",
            "gms_obi_strong_confirm_thresh": 0.2,
            "gms_obi_weak_confirm_thresh": 0.1
        }
    }
    config = Config(**config_data)
    
    # Create SignalEngine
    mock_handler = MockDataHandler(sample_data)
    engine = SignalEngine(config=config, data_handler=mock_handler)
    
    # Measure execution time
    start_time = time.time()
    signals_df = engine.calculate_all_signals()
    end_time = time.time()
    
    execution_time_ms = (end_time - start_time) * 1000
    print(f"Execution time: {execution_time_ms:.2f} ms")
    
    # Check that execution time is less than 100ms
    assert execution_time_ms < 100, f"Execution time ({execution_time_ms:.2f} ms) exceeds 100 ms"
    
    # Verify that signals were calculated correctly
    assert not signals_df.empty
    assert "obi_smoothed_5" in signals_df.columns
    assert "obi_zscore_5" in signals_df.columns

if __name__ == "__main__":
    test_calculate_all_signals_performance()
