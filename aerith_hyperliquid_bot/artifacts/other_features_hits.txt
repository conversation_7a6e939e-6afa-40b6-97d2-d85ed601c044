./tools/etl_l20_to_1s.py:571:    # Calculate rolling spread statistics (spread_mean and spread_std)
./tools/etl_l20_to_1s.py:581:        df['spread_mean'] = rolling_spread.mean()
./tools/etl_l20_to_1s.py:582:        df['spread_std'] = rolling_spread.std()
./tools/etl_l20_to_1s.py:585:        spread_mean_nans = df['spread_mean'].isna().sum()
./tools/etl_l20_to_1s.py:586:        spread_std_nans = df['spread_std'].isna().sum()
./tools/etl_l20_to_1s.py:587:        logger.info(f"Rolling spread stats calculated. spread_mean NaNs: {spread_mean_nans}, spread_std NaNs: {spread_std_nans}")
./tools/etl_l20_to_1s.py:590:        nan_ratio = df['spread_mean'].isna().mean()
./tools/etl_l20_to_1s.py:594:        logger.warning("'spread' column not found, cannot calculate spread_mean and spread_std")
./tools/etl_l20_to_1s.py:595:        df['spread_mean'] = np.nan
./tools/etl_l20_to_1s.py:596:        df['spread_std'] = np.nan
./tests/test_spread_features.py:5:This test verifies that spread_mean and spread_std columns are properly
./tests/test_spread_features.py:28:        """Test that spread_mean and spread_std are calculated correctly."""
./tests/test_spread_features.py:43:        # Calculate post-resample features (this should add spread_mean and spread_std)
./tests/test_spread_features.py:46:        # Verify that spread_mean and spread_std columns exist
./tests/test_spread_features.py:47:        assert 'spread_mean' in result_df.columns, "spread_mean column should be present"
./tests/test_spread_features.py:48:        assert 'spread_std' in result_df.columns, "spread_std column should be present"
./tests/test_spread_features.py:51:        spread_mean_valid = result_df['spread_mean'].notna().sum()
./tests/test_spread_features.py:52:        spread_std_valid = result_df['spread_std'].notna().sum()
./tests/test_spread_features.py:54:        assert spread_mean_valid > 0, "spread_mean should have some non-NaN values"
./tests/test_spread_features.py:55:        assert spread_std_valid > 0, "spread_std should have some non-NaN values"
./tests/test_spread_features.py:62:        spread_mean_after_warmup = after_warmup['spread_mean'].notna().sum()
./tests/test_spread_features.py:63:        spread_std_after_warmup = after_warmup['spread_std'].notna().sum()
./tests/test_spread_features.py:67:        assert spread_mean_after_warmup >= min_valid_count, f"spread_mean should have at least {min_valid_count} valid values after warmup, got {spread_mean_after_warmup}"
./tests/test_spread_features.py:68:        assert spread_std_after_warmup >= min_valid_count, f"spread_std should have at least {min_valid_count} valid values after warmup, got {spread_std_after_warmup}"
./tests/test_spread_features.py:84:        # Verify that spread_mean and spread_std columns exist but are NaN
./tests/test_spread_features.py:85:        assert 'spread_mean' in result_df.columns, "spread_mean column should be present"
./tests/test_spread_features.py:86:        assert 'spread_std' in result_df.columns, "spread_std column should be present"
./tests/test_spread_features.py:89:        assert result_df['spread_mean'].isna().all(), "spread_mean should be all NaN when spread column is missing"
./tests/test_spread_features.py:90:        assert result_df['spread_std'].isna().all(), "spread_std should be all NaN when spread column is missing"
./tests/test_spread_features.py:112:        final_values = result_df['spread_mean'].iloc[-10:]  # Last 10 values
./tests/test_spread_features.py:139:        spread_mean_nan_ratio = result_df['spread_mean'].isna().mean()
./tests/test_spread_features.py:140:        spread_std_nan_ratio = result_df['spread_std'].isna().mean()
./tests/test_spread_features.py:144:        assert spread_mean_nan_ratio < max_nan_ratio, f"spread_mean NaN ratio {spread_mean_nan_ratio:.3f} should be < {max_nan_ratio}"
./tests/test_spread_features.py:145:        assert spread_std_nan_ratio < max_nan_ratio, f"spread_std NaN ratio {spread_std_nan_ratio:.3f} should be < {max_nan_ratio}"
./tests/test_priming_integration.py:59:                    'ma_slope': np.random.uniform(-10, 10, n_samples),
./tests/test_priming_integration.py:60:                    'spread_mean': np.random.uniform(0.0001, 0.001, n_samples)
./tests/test_continuous_gms.py:40:        self.config.regime.gms_spread_std_high_thresh = 0.0005
./tests/test_continuous_gms.py:41:        self.config.regime.gms_spread_mean_low_thresh = 0.0001
./tests/test_continuous_gms.py:42:        self.config.regime.gms_spread_mean_thresh_mode = 'fixed'
./tests/test_continuous_gms.py:43:        self.config.regime.gms_spread_std_thresh_mode = 'fixed'
./tests/test_continuous_gms.py:80:            'ma_slope': 2.0,
./tests/test_continuous_gms.py:85:            'spread_mean': 0.0002,
./tests/test_continuous_gms.py:86:            'spread_std': 0.0003,
./tests/test_continuous_gms.py:87:            'spread_mean_pctile': 0.3,
./tests/test_continuous_gms.py:88:            'spread_mean_primary_pctile': 0.3,
./tests/test_continuous_gms.py:89:            'spread_std_primary_pctile': 0.3,
./tests/test_continuous_gms.py:108:        signals['ma_slope'] = 10.0  # Strong momentum
./tests/test_continuous_gms.py:119:        signals['ma_slope'] = 2.0  # Weak momentum
./tests/test_continuous_gms.py:131:        signals['ma_slope'] = 0.5  # Very weak momentum
./tests/test_continuous_gms.py:132:        signals['spread_std'] = 0.0006  # High spread std
./tests/test_continuous_gms.py:143:        signals['ma_slope'] = 0.5  # Very weak momentum
./tests/test_continuous_gms.py:144:        signals['spread_mean'] = 0.00005  # Low spread mean
./tests/test_continuous_gms.py:154:        signals['ma_slope'] = 0.5  # Very weak momentum
./tests/test_continuous_gms.py:165:        signals['ma_slope'] = -10.0  # Strong negative momentum
./tests/test_continuous_gms.py:176:        signals['ma_slope'] = -2.0  # Weak negative momentum
./tests/test_continuous_gms.py:187:        signals['ma_slope'] = 0.5  # Very weak momentum
./tests/test_continuous_gms.py:191:        signals['spread_mean_pctile'] = 0.1  # Below threshold
./tests/test_continuous_gms.py:206:        signals['ma_slope'] = 10.0  # Strong momentum
./tests/test_continuous_gms.py:217:        signals['ma_slope'] = 2.0  # Weak momentum
./tests/test_continuous_gms.py:230:        signals['ma_slope'] = 10.0  # Strong momentum
./tests/test_continuous_gms.py:316:        signals['ma_slope'] = 10.0
./tests/test_continuous_gms.py:326:        signals['ma_slope'] = -10.0
./tests/integration/test_gms_with_etl.py:304:                    'ma_slope': 0.01,       # Positive slope (uptrend)
./tests/integration/test_gms_with_etl.py:305:                    'spread_mean': 0.5,     # Average spread
./tests/integration/test_gms_with_etl.py:306:                    'spread_std': 0.1,      # Spread standard deviation
./tests/integration/test_gms_with_etl.py:323:                    signals['ma_slope'] = 0.03       # Steeper slope (stronger uptrend)
./tests/test_signalengine_fallback.py:29:    config.indicators.gms_ma_slope_period = 10
./tests/test_spread_causal.py:30:        spread_mean[t] equals np.mean(series[max(0,t-59):t+1]).
./tests/test_spread_causal.py:63:            actual_mean = result_df['spread_mean'].iloc[t]
./tests/test_spread_causal.py:76:        Test that NaN ratio in spread_mean is less than 1% for real data.
./tests/test_spread_causal.py:105:        # Check that spread_mean column exists
./tests/test_spread_causal.py:106:        assert 'spread_mean' in df.columns, "spread_mean column not found in processed data"
./tests/test_spread_causal.py:109:        nan_ratio = df['spread_mean'].isna().mean()
./tests/test_spread_causal.py:114:            f"({df['spread_mean'].isna().sum()} NaNs out of {len(df)} rows)"
./tests/test_integration_continuous_gms.py:52:        self.config.regime.gms_spread_std_high_thresh = 0.0005
./tests/test_integration_continuous_gms.py:53:        self.config.regime.gms_spread_mean_low_thresh = 0.0001
./tests/test_integration_continuous_gms.py:54:        self.config.regime.gms_spread_mean_thresh_mode = 'fixed'
./tests/test_integration_continuous_gms.py:55:        self.config.regime.gms_spread_std_thresh_mode = 'fixed'
./tests/test_integration_continuous_gms.py:85:        self.config.indicators.gms_ma_slope_period = 20
./tests/test_gms_adaptive_thresholds.py:31:            'gms_spread_std_high_thresh': 0.0005,
./tests/test_gms_adaptive_thresholds.py:32:            'gms_spread_mean_low_thresh': 0.0001,
./tests/test_gms_adaptive_thresholds.py:81:            'gms_spread_std_high_thresh': 0.0005,
./tests/test_gms_adaptive_thresholds.py:82:            'gms_spread_mean_low_thresh': 0.0001,
./tests/test_gms_adaptive_thresholds.py:143:            'ma_slope': 2.0,
./tests/test_gms_adaptive_thresholds.py:145:            'spread_mean': 0.0001,
./tests/test_gms_adaptive_thresholds.py:146:            'spread_std': 0.0003,
./tests/test_gms_adaptive_thresholds.py:171:            'ma_slope': 2.0,
./tests/test_gms_adaptive_thresholds.py:173:            'spread_mean': 0.0001,
./tests/test_gms_adaptive_thresholds.py:174:            'spread_std': 0.0003,
./tests/test_gms_adaptive_thresholds.py:186:            signals['ma_slope'] = 1.0 + (i * 0.1)  # Increasing momentum
./tests/test_gms_adaptive_thresholds.py:239:                'ma_slope': 2.0,
./tests/test_gms_adaptive_thresholds.py:241:                'spread_mean': 0.0001,
./tests/test_gms_adaptive_thresholds.py:242:                'spread_std': 0.0003,
./tests/test_gms_adaptive_thresholds.py:262:            'ma_slope': np.nan,  # NaN momentum
./tests/test_gms_adaptive_thresholds.py:264:            'spread_mean': 0.0001,
./tests/test_gms_adaptive_thresholds.py:265:            'spread_std': 0.0003,
./tests/test_priming_causal.py:55:                    'ma_slope': np.random.uniform(-10, 10, n_samples),
./tests/test_priming_causal.py:56:                    'spread_mean': np.random.uniform(0.0001, 0.001, n_samples)
./tests/test_priming_causal.py:82:            'gms_spread_std_high_thresh': 0.0005,
./tests/test_priming_causal.py:83:            'gms_spread_mean_low_thresh': 0.0001,
./tests/test_priming_causal.py:270:            'ma_slope': [5.0]
./tests/test_priming_causal.py:277:            'ma_slope': [7.0]
./hyperliquid_bot/core/detector.py:336:        self.spread_std_high_thresh = getattr(self.cfg_regime, 'gms_spread_std_high_thresh', 0.0005)
./hyperliquid_bot/core/detector.py:337:        self.spread_mean_low_thresh = getattr(self.cfg_regime, 'gms_spread_mean_low_thresh', 0.0001)
./hyperliquid_bot/core/detector.py:339:        self.spread_mean_mode = getattr(self.cfg_regime, 'gms_spread_mean_thresh_mode', 'fixed')
./hyperliquid_bot/core/detector.py:340:        self.spread_std_mode = getattr(self.cfg_regime, 'gms_spread_std_thresh_mode', 'fixed')
./hyperliquid_bot/core/detector.py:341:        self.spread_mean_low_pctile = getattr(self.cfg_regime, 'gms_spread_mean_low_percentile', 0.25)
./hyperliquid_bot/core/detector.py:342:        self.spread_std_high_pctile = getattr(self.cfg_regime, 'gms_spread_std_high_percentile', 0.75)
./hyperliquid_bot/core/detector.py:395:        self.logger.info(f"  - Spread Thresh (Mean Low / Std High): {self.spread_mean_low_thresh:.6f} / {self.spread_std_high_thresh:.6f}")
./hyperliquid_bot/core/detector.py:419:            'ma_slope',         # Momentum
./hyperliquid_bot/core/detector.py:421:            'spread_mean',      # Base Spread Context (Range)
./hyperliquid_bot/core/detector.py:422:            'spread_std'        # Base Spread Context (Chop)
./hyperliquid_bot/core/detector.py:446:        # Fallback spread uses 'spread_mean' and 'spread_std', already included in base
./hyperliquid_bot/core/detector.py:448:        # --- Add spread_mean_pctile if adaptive tight spread fallback is enabled ---
./hyperliquid_bot/core/detector.py:451:            signals_needed.append('spread_mean_pctile') # This is the FALLBACK percentile
./hyperliquid_bot/core/detector.py:455:        if self.spread_mean_mode == 'percentile':
./hyperliquid_bot/core/detector.py:456:            signals_needed.append('spread_mean_primary_pctile')
./hyperliquid_bot/core/detector.py:457:        if self.spread_std_mode == 'percentile':
./hyperliquid_bot/core/detector.py:458:            signals_needed.append('spread_std_primary_pctile')
./hyperliquid_bot/core/detector.py:489:        required_now = ['timestamp', 'atr_percent', 'ma_slope', f'obi_smoothed_{self.depth_levels}', 'spread_mean', 'spread_std'] # Base requirements
./hyperliquid_bot/core/detector.py:508:        if self.spread_mean_mode == 'percentile':
./hyperliquid_bot/core/detector.py:509:            required_now.append('spread_mean_primary_pctile')
./hyperliquid_bot/core/detector.py:510:        if self.spread_std_mode == 'percentile':
./hyperliquid_bot/core/detector.py:511:            required_now.append('spread_std_primary_pctile')
./hyperliquid_bot/core/detector.py:514:            required_now.append('spread_mean_pctile')
./hyperliquid_bot/core/detector.py:527:            # Allow NaN for spread_mean_pctile if the feature is disabled (it shouldn't be in required_signals then, but double-check)
./hyperliquid_bot/core/detector.py:528:            if sig == 'spread_mean_pctile' and (self.tight_spread_fallback_percentile is None or self.tight_spread_fallback_percentile <= 0):
./hyperliquid_bot/core/detector.py:542:        ma_slope = signals.get('ma_slope', np.nan)
./hyperliquid_bot/core/detector.py:544:        spread_std = signals.get('spread_std', np.nan)
./hyperliquid_bot/core/detector.py:545:        spread_mean = signals.get('spread_mean', np.nan)
./hyperliquid_bot/core/detector.py:557:        # spread_mean_primary_pctile = signals.get('spread_mean_primary_pctile', np.nan) # F841 - Already commented
./hyperliquid_bot/core/detector.py:558:        # spread_std_primary_pctile = signals.get('spread_std_primary_pctile', np.nan) # F841 - Already commented
./hyperliquid_bot/core/detector.py:560:        # spread_mean_fallback_pctile = signals.get('spread_mean_pctile', np.nan) # F841 - Already commented
./hyperliquid_bot/core/detector.py:680:                 # NaN checks already done for spread_std and spread_mean
./hyperliquid_bot/core/detector.py:681:                 if spread_std >= self.spread_std_high_thresh:
./hyperliquid_bot/core/detector.py:683:                      # self.logger.debug(f"Market Condition: CHOPPY (Spread Std {spread_std:.6f} >= {self.spread_std_high_thresh:.6f})")
./hyperliquid_bot/core/detector.py:684:                 elif spread_mean <= self.spread_mean_low_thresh:
./hyperliquid_bot/core/detector.py:696:        # NaN checks for core signals (atr_pct, ma_slope) already performed
./hyperliquid_bot/core/detector.py:701:        is_spread_std_high = False
./hyperliquid_bot/core/detector.py:702:        if self.spread_std_mode == 'percentile':
./hyperliquid_bot/core/detector.py:703:            spread_std_pctile = signals.get('spread_std_primary_pctile', np.nan)
./hyperliquid_bot/core/detector.py:704:            if not pd.isna(spread_std_pctile): # Ensure pandas (pd) is imported
./hyperliquid_bot/core/detector.py:705:                is_spread_std_high = spread_std_pctile >= self.spread_std_high_pctile
./hyperliquid_bot/core/detector.py:707:            # spread_std already retrieved and NaN checked earlier
./hyperliquid_bot/core/detector.py:708:            if not pd.isna(spread_std):
./hyperliquid_bot/core/detector.py:709:                is_spread_std_high = spread_std >= self.spread_std_high_thresh
./hyperliquid_bot/core/detector.py:733:            if abs(ma_slope) < self.mom_weak_thresh or is_spread_std_high:
./hyperliquid_bot/core/detector.py:741:            is_spread_mean_low = False
./hyperliquid_bot/core/detector.py:742:            if self.spread_mean_mode == 'percentile':
./hyperliquid_bot/core/detector.py:743:                spread_mean_pctile = signals.get('spread_mean_primary_pctile', np.nan)
./hyperliquid_bot/core/detector.py:744:                if not pd.isna(spread_mean_pctile): # Ensure pandas (pd) is imported
./hyperliquid_bot/core/detector.py:745:                    is_spread_mean_low = spread_mean_pctile < self.spread_mean_low_pctile
./hyperliquid_bot/core/detector.py:747:                # spread_mean already retrieved and NaN checked earlier
./hyperliquid_bot/core/detector.py:748:                if not pd.isna(spread_mean):
./hyperliquid_bot/core/detector.py:749:                    is_spread_mean_low = spread_mean <= self.spread_mean_low_thresh
./hyperliquid_bot/core/detector.py:752:            if abs(ma_slope) < self.mom_weak_thresh and is_spread_mean_low and market_condition != 'WIDE_SPREAD':
./hyperliquid_bot/core/detector.py:757:            is_bullish = ma_slope > 0
./hyperliquid_bot/core/detector.py:758:            is_strong_mom = abs(ma_slope) >= self.mom_strong_thresh
./hyperliquid_bot/core/detector.py:759:            is_weak_mom = abs(ma_slope) >= self.mom_weak_thresh # Includes strong
./hyperliquid_bot/core/detector.py:788:            # else: remains Uncertain if momentum is negligible (abs(ma_slope) < self.mom_weak_thresh)
./hyperliquid_bot/core/detector.py:837:                spread_pctile = signals.get('spread_mean_pctile') # Signal was added conditionally
./hyperliquid_bot/core/detector.py:846:                    self.logger.warning(f"Tight Spread Fallback check failed: spread_mean_pctile signal is missing or NaN (Value: {spread_pctile}). Keeping 'Uncertain'.")
./hyperliquid_bot/core/detector.py:853:            f"Mom={ma_slope:.2f} (W:{self.mom_weak_thresh:.2f}/S:{self.mom_strong_thresh:.2f}), "
./hyperliquid_bot/core/detector.py:940:        required_gms_attrs_regime = ['gms_vol_high_thresh', 'gms_vol_low_thresh', 'gms_mom_strong_thresh', 'gms_mom_weak_thresh', 'gms_spread_std_high_thresh', 'gms_spread_mean_low_thresh']
./hyperliquid_bot/core/gms_detector.py:102:        self.spread_std_high_thresh = detector_settings.get('gms_spread_std_high_thresh', getattr(self.cfg_regime, 'gms_spread_std_high_thresh', 0.0005))
./hyperliquid_bot/core/gms_detector.py:103:        self.spread_mean_low_thresh = detector_settings.get('gms_spread_mean_low_thresh', getattr(self.cfg_regime, 'gms_spread_mean_low_thresh', 0.0001))
./hyperliquid_bot/core/gms_detector.py:106:        self.spread_mean_mode = getattr(self.cfg_regime, 'gms_spread_mean_thresh_mode', 'fixed')
./hyperliquid_bot/core/gms_detector.py:107:        self.spread_std_mode = getattr(self.cfg_regime, 'gms_spread_std_thresh_mode', 'fixed')
./hyperliquid_bot/core/gms_detector.py:108:        self.spread_mean_low_pctile = getattr(self.cfg_regime, 'gms_spread_mean_low_percentile', 0.25)
./hyperliquid_bot/core/gms_detector.py:109:        self.spread_std_high_pctile = getattr(self.cfg_regime, 'gms_spread_std_high_percentile', 0.75)
./hyperliquid_bot/core/gms_detector.py:230:        self.logger.info(f"  - Spread Thresh (Mean Low / Std High): {self.spread_mean_low_thresh:.6f} / {self.spread_std_high_thresh:.6f}")
./hyperliquid_bot/core/gms_detector.py:300:            'ma_slope',         # Momentum
./hyperliquid_bot/core/gms_detector.py:304:            'spread_mean',      # Base Spread Context (Range)
./hyperliquid_bot/core/gms_detector.py:305:            'spread_std'        # Base Spread Context (Chop)
./hyperliquid_bot/core/gms_detector.py:338:        # Add spread_mean_pctile if adaptive tight spread fallback is enabled
./hyperliquid_bot/core/gms_detector.py:340:            signals_needed.append('spread_mean_pctile') # This is the FALLBACK percentile
./hyperliquid_bot/core/gms_detector.py:343:        if self.spread_mean_mode == 'percentile':
./hyperliquid_bot/core/gms_detector.py:344:            signals_needed.append('spread_mean_primary_pctile')
./hyperliquid_bot/core/gms_detector.py:345:        if self.spread_std_mode == 'percentile':
./hyperliquid_bot/core/gms_detector.py:346:            signals_needed.append('spread_std_primary_pctile')
./hyperliquid_bot/core/gms_detector.py:452:        required_now = ['timestamp', 'atr_percent', 'ma_slope', f'obi_smoothed_{self.depth_levels}', 'spread_mean', 'spread_std'] # Base requirements
./hyperliquid_bot/core/gms_detector.py:473:        if self.spread_mean_mode == 'percentile':
./hyperliquid_bot/core/gms_detector.py:474:            required_now.append('spread_mean_primary_pctile')
./hyperliquid_bot/core/gms_detector.py:475:        if self.spread_std_mode == 'percentile':
./hyperliquid_bot/core/gms_detector.py:476:            required_now.append('spread_std_primary_pctile')
./hyperliquid_bot/core/gms_detector.py:480:            required_now.append('spread_mean_pctile')
./hyperliquid_bot/core/gms_detector.py:495:            # Allow NaN for spread_mean_pctile if the feature is disabled (it shouldn't be in required_signals then, but double-check)
./hyperliquid_bot/core/gms_detector.py:496:            if sig == 'spread_mean_pctile' and (self.tight_spread_fallback_percentile is None or self.tight_spread_fallback_percentile <= 0):
./hyperliquid_bot/core/gms_detector.py:525:        ma_slope = signals.get('ma_slope', np.nan)
./hyperliquid_bot/core/gms_detector.py:535:        spread_std = signals.get('spread_std', np.nan)
./hyperliquid_bot/core/gms_detector.py:536:        spread_mean = signals.get('spread_mean', np.nan)
./hyperliquid_bot/core/gms_detector.py:667:                 # NaN checks already done for spread_std and spread_mean
./hyperliquid_bot/core/gms_detector.py:668:                 if spread_std >= self.spread_std_high_thresh:
./hyperliquid_bot/core/gms_detector.py:670:                 elif spread_mean <= self.spread_mean_low_thresh:
./hyperliquid_bot/core/gms_detector.py:684:        is_spread_std_high = False
./hyperliquid_bot/core/gms_detector.py:685:        if self.spread_std_mode == 'percentile':
./hyperliquid_bot/core/gms_detector.py:686:            spread_std_pctile = signals.get('spread_std_primary_pctile', np.nan)
./hyperliquid_bot/core/gms_detector.py:687:            if not pd.isna(spread_std_pctile): # Ensure pandas (pd) is imported
./hyperliquid_bot/core/gms_detector.py:688:                is_spread_std_high = spread_std_pctile >= self.spread_std_high_pctile
./hyperliquid_bot/core/gms_detector.py:690:            # spread_std already retrieved and NaN checked earlier
./hyperliquid_bot/core/gms_detector.py:691:            if not pd.isna(spread_std):
./hyperliquid_bot/core/gms_detector.py:692:                is_spread_std_high = spread_std >= self.spread_std_high_thresh
./hyperliquid_bot/core/gms_detector.py:741:            mom_low_thresh, mom_high_thresh = self.adaptive_mom_threshold.update(abs(ma_slope))
./hyperliquid_bot/core/gms_detector.py:758:            if abs(ma_slope) < mom_weak_thresh_current or is_spread_std_high:
./hyperliquid_bot/core/gms_detector.py:766:            is_spread_mean_low = False
./hyperliquid_bot/core/gms_detector.py:767:            if self.spread_mean_mode == 'percentile':
./hyperliquid_bot/core/gms_detector.py:768:                spread_mean_pctile = signals.get('spread_mean_primary_pctile', np.nan)
./hyperliquid_bot/core/gms_detector.py:769:                if not pd.isna(spread_mean_pctile): # Ensure pandas (pd) is imported
./hyperliquid_bot/core/gms_detector.py:770:                    is_spread_mean_low = spread_mean_pctile < self.spread_mean_low_pctile
./hyperliquid_bot/core/gms_detector.py:772:                # spread_mean already retrieved and NaN checked earlier
./hyperliquid_bot/core/gms_detector.py:773:                if not pd.isna(spread_mean):
./hyperliquid_bot/core/gms_detector.py:774:                    is_spread_mean_low = spread_mean <= self.spread_mean_low_thresh
./hyperliquid_bot/core/gms_detector.py:777:            if abs(ma_slope) < mom_weak_thresh_current and is_spread_mean_low and market_condition != 'WIDE_SPREAD':
./hyperliquid_bot/core/gms_detector.py:783:            is_bullish = ma_slope > 0
./hyperliquid_bot/core/gms_detector.py:784:            is_strong_mom = abs(ma_slope) >= mom_strong_thresh_current
./hyperliquid_bot/core/gms_detector.py:785:            is_weak_mom = abs(ma_slope) >= mom_weak_thresh_current # Includes strong
./hyperliquid_bot/core/gms_detector.py:814:            # else: remains Uncertain if momentum is negligible (abs(ma_slope) < self.mom_weak_thresh)
./hyperliquid_bot/core/gms_detector.py:824:                spread_pctile = signals.get('spread_mean_pctile') # Signal was added conditionally
./hyperliquid_bot/core/gms_detector.py:833:                    self.logger.warning(f"Tight Spread Fallback check failed: spread_mean_pctile signal is missing or NaN (Value: {spread_pctile}). Keeping 'Uncertain'.")
./hyperliquid_bot/core/gms_detector.py:851:            f"Mom={ma_slope:.2f} (W:{mom_weak_thresh_current:.2f}/S:{mom_strong_thresh_current:.2f}), "
./hyperliquid_bot/core/gms_detector.py:1170:                mom_metric = row.get('ma_slope')
./hyperliquid_bot/core/gms_detector.py:1219:                            columns_needed = ['timestamp', self.ATR_PCT_COL, 'ma_slope']
./hyperliquid_bot/config/settings.py:179:    gms_spread_std_high_thresh: float = Field(default=0.000050, ge=0, description="Spread Std Dev threshold indicating high uncertainty/chop")
./hyperliquid_bot/config/settings.py:180:    gms_spread_mean_low_thresh: float = Field(default=0.000045, ge=0, description="Spread Mean threshold indicating tight spreads/low vol range")
./hyperliquid_bot/config/settings.py:217:    gms_spread_std_high_thresh: float = Field(default=0.0005, ge=0, description="Spread Std Dev threshold indicating high uncertainty/chop")
./hyperliquid_bot/config/settings.py:218:    gms_spread_mean_low_thresh: float = Field(default=0.0001, ge=0, description="Spread Mean threshold indicating tight spreads/low vol range")
./hyperliquid_bot/config/settings.py:278:    gms_spread_std_high_thresh: float = Field(default=0.0005, ge=0, description="Spread Std Dev threshold indicating high uncertainty/chop")
./hyperliquid_bot/config/settings.py:279:    gms_spread_mean_low_thresh: float = Field(default=0.0001, ge=0, description="Spread Mean threshold indicating tight spreads/low vol range")
./hyperliquid_bot/config/settings.py:306:    gms_spread_mean_thresh_mode: Literal['fixed', 'percentile'] = Field(
./hyperliquid_bot/config/settings.py:308:        description="Method for gms_spread_mean_low threshold ('fixed' or 'percentile')."
./hyperliquid_bot/config/settings.py:310:    gms_spread_std_thresh_mode: Literal['fixed', 'percentile'] = Field(
./hyperliquid_bot/config/settings.py:312:        description="Method for gms_spread_std_high threshold ('fixed' or 'percentile')."
./hyperliquid_bot/config/settings.py:314:    gms_spread_mean_low_percentile: float = Field(
./hyperliquid_bot/config/settings.py:316:        description="Low percentile threshold for spread_mean if using percentile mode.",
./hyperliquid_bot/config/settings.py:320:    gms_spread_std_high_percentile: float = Field(
./hyperliquid_bot/config/settings.py:322:        description="High percentile threshold for spread_std if using percentile mode.",
./hyperliquid_bot/config/settings.py:382:            'gms_spread_std_high_thresh': self.gms_spread_std_high_thresh,
./hyperliquid_bot/config/settings.py:383:            'gms_spread_mean_low_thresh': self.gms_spread_mean_low_thresh,
./hyperliquid_bot/config/settings.py:594:    gms_ma_slope_period: int = Field(default=10, gt=1, description="Period for Moving Average used in Slope (Momentum)")
./hyperliquid_bot/signals/calculator.py:438:                signals_df["spread_mean"] = rolling_spread.mean()
./hyperliquid_bot/signals/calculator.py:439:                signals_df["spread_std"] = rolling_spread.std()
./hyperliquid_bot/signals/calculator.py:441:                    f"Rolling Spread Stats calculated. Mean NaNs: {signals_df['spread_mean'].isna().sum()}, Std NaNs: {signals_df['spread_std'].isna().sum()}"
./hyperliquid_bot/signals/calculator.py:447:                signals_df["spread_mean"] = np.nan
./hyperliquid_bot/signals/calculator.py:448:                signals_df["spread_std"] = np.nan
./hyperliquid_bot/signals/calculator.py:450:                # Fill NaN values in spread_mean and spread_std with default values for testing
./hyperliquid_bot/signals/calculator.py:452:                self.logger.warning("Filling NaN values in spread_mean with 0.0001 and spread_std with 0.00005 for testing")
./hyperliquid_bot/signals/calculator.py:453:                signals_df["spread_mean"] = signals_df["spread_mean"].fillna(0.0001)
./hyperliquid_bot/signals/calculator.py:454:                signals_df["spread_std"] = signals_df["spread_std"].fillna(0.00005)
./hyperliquid_bot/signals/calculator.py:456:            spread_mean_mode = getattr(
./hyperliquid_bot/signals/calculator.py:457:                cfg_regime, "gms_spread_mean_thresh_mode", "fixed"
./hyperliquid_bot/signals/calculator.py:459:            spread_std_mode = getattr(cfg_regime, "gms_spread_std_thresh_mode", "fixed")
./hyperliquid_bot/signals/calculator.py:461:            calc_mean_pct = spread_mean_mode == "percentile"
./hyperliquid_bot/signals/calculator.py:462:            calc_std_pct = spread_std_mode == "percentile"
./hyperliquid_bot/signals/calculator.py:465:            if "spread_mean_primary_pctile" not in signals_df.columns:
./hyperliquid_bot/signals/calculator.py:466:                signals_df["spread_mean_primary_pctile"] = np.nan
./hyperliquid_bot/signals/calculator.py:467:            if "spread_std_primary_pctile" not in signals_df.columns:
./hyperliquid_bot/signals/calculator.py:468:                signals_df["spread_std_primary_pctile"] = np.nan
./hyperliquid_bot/signals/calculator.py:488:                        and "spread_mean" in signals_df.columns
./hyperliquid_bot/signals/calculator.py:489:                        and not signals_df["spread_mean"].isnull().all()
./hyperliquid_bot/signals/calculator.py:493:                            signals_df["spread_mean_primary_pctile"] = rolling_percentile_rank(
./hyperliquid_bot/signals/calculator.py:494:                                signals_df["spread_mean"], window, min_p)
./hyperliquid_bot/signals/calculator.py:500:                            signals_df["spread_mean_primary_pctile"] = (
./hyperliquid_bot/signals/calculator.py:506:                        and "spread_std" in signals_df.columns
./hyperliquid_bot/signals/calculator.py:507:                        and not signals_df["spread_std"].isnull().all()
./hyperliquid_bot/signals/calculator.py:511:                            signals_df["spread_std_primary_pctile"] = rolling_percentile_rank(
./hyperliquid_bot/signals/calculator.py:512:                                signals_df["spread_std"], window, min_p)
./hyperliquid_bot/signals/calculator.py:518:                            signals_df["spread_std_primary_pctile"] = (
./hyperliquid_bot/signals/calculator.py:527:            # remain separate and use distinct column names (e.g., 'spread_mean_fallback_pctile').
./hyperliquid_bot/signals/calculator.py:528:            # The code above uses 'spread_mean_primary_pctile' and 'spread_std_primary_pctile'.
./hyperliquid_bot/signals/calculator.py:538:                # Ensure spread_mean exists and window is valid
./hyperliquid_bot/signals/calculator.py:539:                if "spread_mean" in signals_df.columns and window > 1:
./hyperliquid_bot/signals/calculator.py:544:                        # Calculate rolling percentile rank of spread_mean
./hyperliquid_bot/signals/calculator.py:545:                        signals_df["spread_mean_pctile"] = rolling_percentile_rank(
./hyperliquid_bot/signals/calculator.py:546:                            signals_df["spread_mean"], window, min_p)
./hyperliquid_bot/signals/calculator.py:548:                            f"Spread Mean Percentile calculated. NaN count: {signals_df['spread_mean_pctile'].isna().sum()}"
./hyperliquid_bot/signals/calculator.py:555:                        signals_df["spread_mean_pctile"] = (
./hyperliquid_bot/signals/calculator.py:560:                        "Could not calculate Spread Mean Percentile: 'spread_mean' missing or window <= 1."
./hyperliquid_bot/signals/calculator.py:562:                    signals_df["spread_mean_pctile"] = np.nan
./hyperliquid_bot/signals/calculator.py:566:                if "spread_mean_pctile" not in signals_df.columns:
./hyperliquid_bot/signals/calculator.py:567:                    signals_df["spread_mean_pctile"] = np.nan
./hyperliquid_bot/signals/calculator.py:640:                # Determine the raw spread column to use (consistent with spread_mean/std)
./hyperliquid_bot/signals/calculator.py:702:                    f"Calculating Spread Trend using 'spread_mean' over lookback {spread_trend_lookback_cfg} periods..."
./hyperliquid_bot/signals/calculator.py:704:                # Requires 'spread_mean' to be calculated beforehand
./hyperliquid_bot/signals/calculator.py:706:                    "spread_mean" in signals_df.columns
./hyperliquid_bot/signals/calculator.py:707:                    and not signals_df["spread_mean"].isnull().all()
./hyperliquid_bot/signals/calculator.py:711:                        # signals_df['spread_trend'] = signals_df['spread_mean'].diff(spread_trend_lookback_cfg)
./hyperliquid_bot/signals/calculator.py:722:                        spread_mean_ma_short = (
./hyperliquid_bot/signals/calculator.py:723:                            signals_df["spread_mean"]
./hyperliquid_bot/signals/calculator.py:728:                        signals_df["spread_trend"] = spread_mean_ma_short.diff(
./hyperliquid_bot/signals/calculator.py:747:                        f"Skipping Spread Trend: Required column 'spread_mean' not available or all NaN (Lookback={spread_trend_lookback_cfg})."
./hyperliquid_bot/signals/calculator.py:1226:                ma_slope_p = cfg_indicators.gms_ma_slope_period
./hyperliquid_bot/signals/calculator.py:1227:                if ma_slope_p > 1:
./hyperliquid_bot/signals/calculator.py:1228:                    self.logger.debug(f"Calculating MA Slope with period {ma_slope_p}")
./hyperliquid_bot/signals/calculator.py:1230:                    ma_col = f"SMA_{ma_slope_p}"
./hyperliquid_bot/signals/calculator.py:1232:                        signals_df.ta.sma(length=ma_slope_p, append=True)
./hyperliquid_bot/signals/calculator.py:1237:                            signals_df["ma_slope"] = signals_df[ma_col] - signals_df[ma_col].shift(1)
./hyperliquid_bot/signals/calculator.py:1241:                                f"MA Slope calculated. NaN count: {signals_df['ma_slope'].isna().sum()}"
./hyperliquid_bot/signals/calculator.py:1245:                            self.logger.warning(f"MA column '{ma_col}' not found. Using fallback calculation for ma_slope.")
./hyperliquid_bot/signals/calculator.py:1246:                            signals_df["ma_slope"] = signals_df["close"].diff(ma_slope_p)
./hyperliquid_bot/signals/calculator.py:1248:                                f"MA Slope (fallback) calculated. NaN count: {signals_df['ma_slope'].isna().sum()}"
./hyperliquid_bot/signals/calculator.py:1252:                        self.logger.warning(f"Error calculating MA with pandas-ta: {e}. Using fallback calculation for ma_slope.")
./hyperliquid_bot/signals/calculator.py:1253:                        signals_df["ma_slope"] = signals_df["close"].diff(ma_slope_p)
./hyperliquid_bot/signals/calculator.py:1255:                            f"MA Slope (fallback) calculated. NaN count: {signals_df['ma_slope'].isna().sum()}"
./hyperliquid_bot/signals/calculator.py:1258:                    signals_df["ma_slope"] = np.nan
./hyperliquid_bot/signals/calculator.py:1260:                # Fill NaN values in ma_slope with 0 for testing purposes
./hyperliquid_bot/signals/calculator.py:1262:                if signals_df["ma_slope"].isna().any():
./hyperliquid_bot/signals/calculator.py:1263:                    self.logger.warning(f"Filling {signals_df['ma_slope'].isna().sum()} NaN values in ma_slope with 0 for testing")
./hyperliquid_bot/signals/calculator.py:1264:                    signals_df["ma_slope"] = signals_df["ma_slope"].fillna(0)
./hyperliquid_bot/signals/calculator.py:1334:                self.config.indicators, "gms_ma_slope_period"
./hyperliquid_bot/signals/calculator.py:1336:                slope_period = self.config.indicators.gms_ma_slope_period
./hyperliquid_bot/signals/calculator.py:1368:                        f"Skipping {sma_col_name} calculation: gms_ma_slope_period is not positive ({slope_period})."
./hyperliquid_bot/signals/calculator.py:1375:                    f"Could not calculate {sma_col_name}: Missing 'close' column or 'gms_ma_slope_period' config."
./hyperliquid_bot/signals/calculator.py:1436:            "spread_mean",
./hyperliquid_bot/signals/calculator.py:1437:            "spread_std",
./hyperliquid_bot/signals/calculator.py:1439:            "ma_slope",
./hyperliquid_bot/signals/calculator.py:1467:        _debug_cols = ['spread_mean_primary_pctile', 'spread_std_primary_pctile']
./hyperliquid_bot/signals/calculator.py:1474:        if 'spread_mean_pctile' in signals_df.columns:
./hyperliquid_bot/signals/calculator.py:1475:            assert (signals_df['spread_mean_pctile'].between(0, 1) | signals_df['spread_mean_pctile'].isna()).all()
./hyperliquid_bot/signals/calculator.py:1538:                    cfg_ind.gms_ma_slope_period,  # MA period for slope calc
./hyperliquid_bot/tests/test_obi_filter.py:45:            'ma_slope': 100.0,
./hyperliquid_bot/tests/test_obi_filter.py:46:            'spread_mean': 0.0001,
./hyperliquid_bot/tests/test_obi_filter.py:47:            'spread_std': 0.00005
./hyperliquid_bot/tests/test_obi_filter.py:64:            'ma_slope': 100.0,
./hyperliquid_bot/tests/test_obi_filter.py:65:            'spread_mean': 0.0001,
./hyperliquid_bot/tests/test_obi_filter.py:66:            'spread_std': 0.00005
./scripts/run_latin_square_grid.py:29:    "gms_spread_std_high_thresh": [0.000026, 0.000044],  # Low, High
./scripts/run_latin_square_grid.py:47:            "gms_spread_std_high_thresh": params["gms_spread_std_high_thresh"],
./scripts/run_latin_square_grid.py:179:                 len(PARAM_GRID["gms_spread_std_high_thresh"]) * \
./scripts/run_latin_square_grid.py:191:            for spd_thr in PARAM_GRID["gms_spread_std_high_thresh"]:
./scripts/run_latin_square_grid.py:200:                        "gms_spread_std_high_thresh": spd_thr,
./scripts/test_spread_fix.py:6:to verify that spread_mean and spread_std columns are properly generated.
./scripts/test_spread_fix.py:101:    Test the ETL pipeline to verify spread_mean and spread_std are generated.
./scripts/test_spread_fix.py:141:        # Calculate post-resample features (this should add spread_mean and spread_std)
./scripts/test_spread_fix.py:145:        print(f"\n=== Step 5: Verifying spread_mean and spread_std ===")
./scripts/test_spread_fix.py:146:        # Check if spread_mean and spread_std exist
./scripts/test_spread_fix.py:147:        required_columns = ['spread_mean', 'spread_std']
./scripts/test_spread_fix.py:154:        print("✅ Required columns found: spread_mean, spread_std")
./scripts/test_spread_fix.py:170:        sample_data = df_final[['timestamp', 'spread', 'spread_mean', 'spread_std']].tail(10)
./scripts/test_spread_fix.py:174:        spread_mean_valid = df_final['spread_mean'].dropna()
./scripts/test_spread_fix.py:175:        spread_std_valid = df_final['spread_std'].dropna()
./scripts/test_spread_fix.py:177:        if len(spread_mean_valid) > 0:
./scripts/test_spread_fix.py:178:            print(f"\nspread_mean stats: min={spread_mean_valid.min():.6f}, max={spread_mean_valid.max():.6f}, mean={spread_mean_valid.mean():.6f}")
./scripts/test_spread_fix.py:180:        if len(spread_std_valid) > 0:
./scripts/test_spread_fix.py:181:            print(f"spread_std stats: min={spread_std_valid.min():.6f}, max={spread_std_valid.max():.6f}, mean={spread_std_valid.mean():.6f}")
./scripts/test_spread_fix.py:184:        if len(spread_mean_valid) > 0 and len(spread_std_valid) > 0:
./scripts/test_spread_fix.py:185:            print("✅ SUCCESS: spread_mean and spread_std are properly calculated!")
./scripts/test_spread_fix.py:188:            print("❌ FAILURE: spread_mean and/or spread_std have no valid values!")
./scripts/diagnose_gms_inputs.py:37:        ma_slope = signals_row.get('ma_slope', np.nan)
./scripts/diagnose_gms_inputs.py:44:            'ma_slope': ma_slope,
./scripts/diagnose_gms_inputs.py:101:    df_clean = df.dropna(subset=['atr_percent', 'ma_slope'])
./scripts/diagnose_gms_inputs.py:134:    print(f"\n--- MOMENTUM (ma_slope) ---")
./scripts/diagnose_gms_inputs.py:135:    mom_stats = df_clean['ma_slope'].describe()
./scripts/diagnose_gms_inputs.py:145:    weak_mom_count = (np.abs(df_clean['ma_slope']) < mom_weak_thresh).sum()
./scripts/diagnose_gms_inputs.py:146:    mid_mom_count = ((np.abs(df_clean['ma_slope']) >= mom_weak_thresh) &
./scripts/diagnose_gms_inputs.py:147:                     (np.abs(df_clean['ma_slope']) < mom_strong_thresh)).sum()
./scripts/diagnose_gms_inputs.py:148:    strong_mom_count = (np.abs(df_clean['ma_slope']) >= mom_strong_thresh).sum()
./scripts/diagnose_gms_inputs.py:157:                        (np.abs(df_clean['ma_slope']) < mom_weak_thresh)).sum()
./scripts/diagnose_gms_inputs.py:184:    ax2.hist(df_clean['ma_slope'], bins=50, alpha=0.7, color='green', edgecolor='black')
./scripts/diagnose_gms_inputs.py:193:    ax2.set_title('Momentum Distribution (ma_slope)')
./scripts/diagnose_gms_inputs.py:226:    weak_mom_count = (np.abs(df_clean['ma_slope']) < mom_weak_thresh).sum()
./scripts/diagnose_gms_inputs.py:228:                        (np.abs(df_clean['ma_slope']) < mom_weak_thresh)).sum()
./scripts/diagnose_gms_inputs.py:249:        f.write("### Momentum (ma_slope)\n")
./scripts/diagnose_gms_inputs.py:250:        f.write(f"- Mean: {df_clean['ma_slope'].mean():.4f}\n")
./scripts/diagnose_gms_inputs.py:251:        f.write(f"- Median: {df_clean['ma_slope'].median():.4f}\n")
./scripts/diagnose_gms_inputs.py:252:        f.write(f"- 5th percentile: {df_clean['ma_slope'].quantile(0.05):.4f}\n")
./scripts/diagnose_gms_inputs.py:253:        f.write(f"- 95th percentile: {df_clean['ma_slope'].quantile(0.95):.4f}\n\n")
./scripts/verify_gms_state_mapping.py:100:        'ma_slope': 0.0001,  # Near zero slope
./scripts/verify_gms_state_mapping.py:102:        'spread_mean': 0.1,
./scripts/verify_gms_state_mapping.py:103:        'spread_std': 0.01
./scripts/fix_grid_search_overrides.py:11:    "gms_spread_std_high_thresh": [0.000026, 0.000044],  # Low, High
./scripts/fix_grid_search_overrides.py:22:  gms_spread_std_high_thresh: {spd_thr}
./scripts/fix_grid_search_overrides.py:37:        for spd_thr in PARAM_GRID["gms_spread_std_high_thresh"]:
./scripts/fix_grid_search_overrides.py:72:  gms_spread_std_high_thresh: 0.000035
./scripts/test_backtester.py:102:        'ma_slope': 100.0,  # Strong upward slope
./scripts/test_backtester.py:103:        'spread_mean': 0.0001,  # 0.01% spread
./scripts/test_backtester.py:104:        'spread_std': 0.00005,  # Low spread volatility
./scripts/visualize_backtest.py:234:            elif indicator_name == 'ma_slope':
./scripts/visualize_backtest.py:915:    if config.get('visualization', {}).get('panels', {}).get('show_ma_slope', False): # Default to False if key missing
./scripts/visualize_backtest.py:916:        indicators_to_plot.append('ma_slope')
./scripts/test_single_run.py:24:            "gms_spread_std_high_thresh": 0.000035,
./scripts/sanity_check_thresholds.py:74:            for col in ['ma_slope', 'ema_slope', 'momentum']:
./scripts/regenerate_features_with_atr.py:158:        if 'spread_mean' not in df.columns:
./scripts/regenerate_features_with_atr.py:159:            logger.error(f"spread_mean column missing in {sample_file}")
./scripts/regenerate_features_with_atr.py:162:        if 'spread_std' not in df.columns:
./scripts/regenerate_features_with_atr.py:163:            logger.error(f"spread_std column missing in {sample_file}")
./scripts/regenerate_features_with_atr.py:175:        spread_mean_nan_count = df['spread_mean'].isna().sum()
./scripts/regenerate_features_with_atr.py:176:        spread_std_nan_count = df['spread_std'].isna().sum()
./scripts/regenerate_features_with_atr.py:181:        if spread_mean_nan_count > max_allowed_nan:
./scripts/regenerate_features_with_atr.py:182:            logger.error(f"Too many NaN values in spread_mean: {spread_mean_nan_count}/{total_rows} (max allowed: {max_allowed_nan})")
./scripts/regenerate_features_with_atr.py:185:        if spread_std_nan_count > max_allowed_nan:
./scripts/regenerate_features_with_atr.py:186:            logger.error(f"Too many NaN values in spread_std: {spread_std_nan_count}/{total_rows} (max allowed: {max_allowed_nan})")
./scripts/regenerate_features_with_atr.py:190:        logger.info(f"Spread features verification passed for {date_str}: spread_mean {total_rows - spread_mean_nan_count}/{total_rows}, spread_std {total_rows - spread_std_nan_count}/{total_rows}")
./scripts/generate_latin_square_overrides.py:11:    "gms_spread_std_high_thresh": [0.000026, 0.000044],  # Low, High
./scripts/generate_latin_square_overrides.py:22:  gms_spread_std_high_thresh: {spd_thr}
./scripts/generate_latin_square_overrides.py:37:        for spd_thr in PARAM_GRID["gms_spread_std_high_thresh"]:
./scripts/compare_feature_distributions.py:21:    'spread_mean',
./scripts/compare_feature_distributions.py:22:    'spread_std',
./scripts/compare_feature_distributions.py:24:    'ma_slope',     # gms_ma_slope_period
./scripts/verify_gms_inline.py:80:            'ma_slope': 0.0001,  # Near zero slope
./scripts/verify_gms_inline.py:82:            'spread_mean': 0.1,
./scripts/verify_gms_inline.py:83:            'spread_std': 0.01,
./scripts/test_tf_v3_strategy.py:78:    signals[f'ma_slope'] = 100.0  # Strong upward slope
./scripts/test_tf_v3_strategy.py:79:    signals[f'spread_mean'] = 0.0001  # 0.01% spread
./scripts/test_tf_v3_strategy.py:80:    signals[f'spread_std'] = 0.00005  # Low spread volatility
./scripts/run_latin_square.py:28:    "gms_spread_std_high_thresh": [0.000026, 0.000044],
./scripts/run_latin_square.py:39:        "gms_spread_std_high_thresh": None,     # Will be filled in
./scripts/run_latin_square.py:53:            "gms_spread_std_high_thresh": params["gms_spread_std_high_thresh"],
./scripts/run_latin_square.py:104:                 len(PARAM_GRID["gms_spread_std_high_thresh"]) * \
./scripts/run_latin_square.py:112:            for spd_thr in PARAM_GRID["gms_spread_std_high_thresh"]:
./scripts/run_latin_square.py:120:                        "gms_spread_std_high_thresh": spd_thr,
./scripts/analyze_features.py:44:        'ma_slope',         # Momentum
./scripts/analyze_features.py:46:        'spread_mean',      # Microstructure Context (Range)
./scripts/analyze_features.py:47:        'spread_std',       # Microstructure Context (Chop)
./scripts/analyze_features.py:174:            'ma_slope',
./scripts/analyze_features.py:286:             # if 'ma_slope' in analysis_results:
./scripts/analyze_features.py:288:             #     abs_ma_slope = all_signals_df['ma_slope'].dropna().abs()
./scripts/analyze_features.py:289:             #     print(f"gms_mom_weak_thresh: {abs_ma_slope.quantile(0.30):.6f} # 30th percentile of abs(ma_slope)")
./scripts/analyze_features.py:290:             #     print(f"gms_mom_strong_thresh: {abs_ma_slope.quantile(0.85):.6f} # 85th percentile of abs(ma_slope)")
./scripts/smoke_test_gms.py:112:    df['ma_slope'] = df['close'].diff(10) / df['close'].shift(10) * 100
./scripts/smoke_test_gms.py:115:    df['spread_mean'] = df['spread'].rolling(window=60).mean()
./scripts/smoke_test_gms.py:116:    df['spread_std'] = df['spread'].rolling(window=60).std()
./scripts/test_single_run_with_metrics.py:24:  gms_spread_std_high_thresh: 2.6e-05
./scripts/test_tf_v3_direct.py:81:    signals[f'ma_slope'] = 100.0  # Strong upward slope
./scripts/test_tf_v3_direct.py:82:    signals[f'spread_mean'] = 0.0001  # 0.01% spread
./scripts/test_tf_v3_direct.py:83:    signals[f'spread_std'] = 0.00005  # Low spread volatility
