# Feature Schema Diff

This document compares canonical feature names against actual parquet schema for Task R-112n.

## Summary

| Category | Canonical Features | Code References | Status |
|----------|-------------------|-----------------|--------|
| Core | 7 | ✓ | Unknown |
| ATR | 8 | ✓ | Unknown |
| OBI | 8 | ✓ | Unknown |
| Spread | 2 | ✓ | Unknown |
| Momentum | 1 | ✓ | Unknown |
| Volatility | 1 | ✓ | Unknown |
| **Total** | **27** | **✓** | **Unknown** |

## Detailed Feature List

| Feature | Canonical Name | In Parquet? | Alternate Names Found |
|---------|----------------|-------------|----------------------|
| atr | atr_14_sec | Unknown | atr, atr_14 |
| atr_14_sec | atr_14_sec | Unknown | None |
| atr_mr | atr_mr | Unknown | None |
| atr_mv | atr_mv | Unknown | None |
| atr_percent | atr_percent_sec | Unknown | atr_percent |
| atr_percent_sec | atr_percent_sec | Unknown | None |
| atr_tf | atr_tf | Unknown | None |
| best_ask | best_ask | Unknown | None |
| best_bid | best_bid | Unknown | None |
| close | close | Unknown | None |
| high | high | Unknown | None |
| low | low | Unknown | None |
| ma_slope | ma_slope | Unknown | None |
| mid_price | mid_price | Unknown | None |
| obi_smoothed | obi_smoothed_5 | Unknown | obi_smoothed |
| obi_smoothed_5 | obi_smoothed_5 | Unknown | None |
| obi_smoothed_20 | obi_smoothed_20 | Unknown | None |
| obi_zscore_5 | obi_zscore_5 | Unknown | None |
| obi_zscore_20 | obi_zscore_20 | Unknown | None |
| raw_obi_5 | raw_obi_5 | Unknown | None |
| raw_obi_20 | raw_obi_20 | Unknown | None |
| raw_obi_L1_3 | raw_obi_L1_3 | Unknown | None |
| raw_obi_L1_10 | raw_obi_L1_10 | Unknown | None |
| realised_vol_1s | realised_vol_1s | Unknown | None |
| spread | spread | Unknown | None |
| spread_mean | spread_mean | Unknown | None |
| spread_std | spread_std | Unknown | None |
| timestamp | timestamp | Unknown | None |
| unrealised_pnl | unrealised_pnl | Unknown | None |
| volume | volume | Unknown | None |

## Canonical Feature Mapping

### ATR Features
- **atr_14_sec**: Primary ATR calculated from hourly OHLC data (14-period)
  - Alternate names: `atr`, `atr_14`
- **atr_percent_sec**: ATR as percentage of mid_price
  - Alternate names: `atr_percent`
- **atr_tf**: ATR for trend following strategy
- **atr_mr**: ATR for mean reversion strategy  
- **atr_mv**: ATR for mean variance strategy

### OBI Features
- **raw_obi_5**: Raw Order Book Imbalance using 5 levels
- **raw_obi_20**: Raw Order Book Imbalance using 20 levels
- **raw_obi_L1_3**: Raw OBI for levels 1-3 (OBI Scalper)
- **raw_obi_L1_10**: Raw OBI for levels 1-10 (OBI Scalper)
- **obi_smoothed_5**: Smoothed OBI (5 levels)
- **obi_smoothed_20**: Smoothed OBI (20 levels)
- **obi_zscore_5**: Z-score normalized OBI (5 levels)
- **obi_zscore_20**: Z-score normalized OBI (20 levels)

### Spread Features
- **spread_mean**: Rolling mean of spread (60-second window, causal)
- **spread_std**: Rolling standard deviation of spread (60-second window, causal)

### Core Features
- **timestamp**: Timestamp of observation (naive UTC)
- **mid_price**: Mid price ((best_bid + best_ask) / 2)
- **close**: Close price (set to mid_price for continuity)
- **high**: High price (set to close for 1-second data)
- **low**: Low price (set to close for 1-second data)
- **best_bid**: Best bid price
- **best_ask**: Best ask price
- **spread**: Absolute spread (best_ask - best_bid)

### Other Features
- **ma_slope**: Moving average slope (momentum indicator)
- **realised_vol_1s**: Realized volatility at 1-second level
- **unrealised_pnl**: Placeholder for unrealized P&L (set to 0.0)
- **volume**: Trading volume

## Notes

- **In Parquet?** column shows "Unknown" because no parquet file was available for comparison
- All features listed are referenced in the codebase across detectors, strategies, risk management, and signals
- Depth-aware features (OBI) use `{depth}` placeholder resolved via `cfg.gms.depth_levels`
- Legacy feature names are mapped to canonical names for consistency

## Component Usage

| Component | Primary Features Used |
|-----------|----------------------|
| **continuous_gms** | atr_percent_sec, ma_slope, obi_smoothed_{depth}, spread_mean, spread_std |
| **granular_microstructure** | atr_percent, ma_slope, obi_smoothed_{depth}, spread_mean, spread_std |
| **tf_v3** | atr_14_sec, close, atr_tf |
| **tf_v2** | atr_tf, close |
| **obi_scalper** | raw_obi_L1_3, raw_obi_L1_10, obi_smoothed_{levels}, obi_zscore_{levels} |
| **risk** | atr_14_sec, atr_tf, atr_mr, atr_mv |
| **ETL** | All core features, ATR features, spread features, OBI features |

## Next Steps

1. **Load actual parquet schema** from `/ext_ssd/hyperliquid/features_1s/YYYY-MM-DD/*.parquet`
2. **Update "In Parquet?" column** with actual comparison results
3. **Identify missing features** that need to be added to ETL pipeline
4. **Identify unused features** that can be deprecated
5. **Enforce schema consistency** via runtime validation

## Validation Requirements

Based on this audit, the ETL pipeline should generate parquet files with these canonical columns:

### Required Core Columns
```
timestamp, mid_price, close, high, low, best_bid, best_ask, spread
```

### Required ATR Columns  
```
atr_14_sec, atr_percent_sec, atr, atr_percent, atr_tf, atr_mr, atr_mv
```

### Required OBI Columns (depth-aware)
```
raw_obi_5, raw_obi_20, obi_smoothed_5, obi_smoothed_20, obi_zscore_5, obi_zscore_20
raw_obi_L1_3, raw_obi_L1_10  # For OBI Scalper
```

### Required Spread Columns
```
spread_mean, spread_std
```

### Required Other Columns
```
ma_slope, realised_vol_1s, unrealised_pnl, volume
```

This schema ensures all strategies and detectors have access to their required features.
