file,line,context,recommended_section,notes
hyperliquid_bot/core/risk.py.bak2,290,"final_leverage = max(cfg.indicators.min_leverage, min(final_leverage, cfg.core.max_leverage))",portfolio,Backup file - max_leverage should be portfolio.max_leverage
hyperliquid_bot/core/risk.py.bak2,328,"risk_amount_per_trade = balance * cfg.core.risk_per_trade * risk_factor * risk_scale",portfolio,Backup file - risk_per_trade should be portfolio.risk_per_trade
hyperliquid_bot/core/risk.py.bak2,399,"# final_leverage = max(cfg.indicators.min_leverage, min(final_leverage, cfg.core.max_leverage)) # Already capped",portfolio,Backup file - commented out max_leverage reference
hyperliquid_bot/core/risk.py.bak2,681,"max_leverage = cfg.core.max_leverage",portfolio,Backup file - max_leverage should be portfolio.max_leverage
hyperliquid_bot/core/risk.py.bak,245,"final_leverage = max(cfg.indicators.min_leverage, min(final_leverage, cfg.core.max_leverage))",portfolio,Backup file - max_leverage should be portfolio.max_leverage
hyperliquid_bot/core/risk.py.bak,283,"risk_amount_per_trade = balance * cfg.core.risk_per_trade * risk_factor * risk_scale",portfolio,Backup file - risk_per_trade should be portfolio.risk_per_trade
hyperliquid_bot/core/risk.py.bak,354,"final_leverage = max(cfg.indicators.min_leverage, min(final_leverage, cfg.core.max_leverage))",portfolio,Backup file - max_leverage should be portfolio.max_leverage
hyperliquid_bot/core/risk.py.bak,406,"final_leverage = max(cfg.indicators.min_leverage, min(final_leverage, cfg.core.max_leverage))",portfolio,Backup file - max_leverage should be portfolio.max_leverage
hyperliquid_bot/core/risk.py.bak,444,"risk_amount_per_trade = balance * cfg.core.risk_per_trade * risk_factor * risk_scale",portfolio,Backup file - risk_per_trade should be portfolio.risk_per_trade
hyperliquid_bot/core/risk.py.bak,633,"base_leverage = getattr(cfg.indicators, 'tf_leverage_base', cfg.core.max_leverage)",portfolio,Backup file - max_leverage fallback should be portfolio.max_leverage
hyperliquid_bot/core/risk.py.bak,675,"max_leverage = cfg.core.max_leverage",portfolio,Backup file - max_leverage should be portfolio.max_leverage
hyperliquid_bot/core/risk.py.bak,716,"base_leverage = getattr(cfg.indicators, 'tf_leverage_base', cfg.core.max_leverage)",portfolio,Backup file - max_leverage fallback should be portfolio.max_leverage
hyperliquid_bot/core/risk.py.bak,758,"max_leverage = cfg.core.max_leverage",portfolio,Backup file - max_leverage should be portfolio.max_leverage
hyperliquid_bot/core/risk.py,412,"# final_leverage = max(cfg.indicators.min_leverage, min(final_leverage, cfg.core.max_leverage)) # Already capped",portfolio,Active file - commented out max_leverage reference
hyperliquid_bot/backtester/__pycache__/run_backtest.cpython-311.pyc,N/A,Binary file matches,TBD,Compiled Python file - need to check source
tests/test_continuous_gms.py,61,"self.config.core = MagicMock()",portfolio,Test file - mock config.core creation
tests/test_continuous_gms.py,62,"self.config.core.initial_balance = 10000",portfolio,Test file - initial_balance should be portfolio.initial_balance
tests/test_continuous_gms.py,63,"self.config.core.max_leverage = 10.0",portfolio,Test file - max_leverage should be portfolio.max_leverage
tests/test_continuous_gms.py,270,"threshold = self.config.core.initial_balance * self.config.core.max_leverage * self.detector.risk_suppressed_notional_frac",portfolio,Test file - both initial_balance and max_leverage should be portfolio.*
tests/test_portfolio.py,36,"# default_config.core.initial_balance = 1000.0 # Example override",portfolio,Test file - commented out initial_balance reference
tests/test_runtime_tz.py,39,"self.config.core = Mock()",portfolio,Test file - mock config.core creation
tests/test_runtime_tz.py,40,"self.config.core.initial_balance = 10000.0",portfolio,Test file - initial_balance should be portfolio.initial_balance
tests/test_runtime_tz.py,41,"self.config.core.max_leverage = 10.0",portfolio,Test file - max_leverage should be portfolio.max_leverage
tests/test_portfolio_margin.py,29,"assert pytest.approx(p.get_free_balance()) == cfg.core.initial_balance - im - (100*cfg.costs.taker_fee)",portfolio,Test file - initial_balance should be portfolio.initial_balance
tests/test_portfolio_margin.py,38,"assert p.get_free_balance() > cfg.core.initial_balance",portfolio,Test file - initial_balance should be portfolio.initial_balance
tests/test_integration_continuous_gms.py,89,"self.config.core = MagicMock()",portfolio,Test file - mock config.core creation
tests/test_integration_continuous_gms.py,90,"self.config.core.initial_balance = 10000",portfolio,Test file - initial_balance should be portfolio.initial_balance
tests/test_integration_continuous_gms.py,91,"self.config.core.max_leverage = 10.0",portfolio,Test file - max_leverage should be portfolio.max_leverage
