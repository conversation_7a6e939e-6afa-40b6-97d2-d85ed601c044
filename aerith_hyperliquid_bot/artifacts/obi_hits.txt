./tests/test_obi_scalper.py:44:        self.strategy = OBIScalperStrategy(self.config, "obi_scalper")
./tests/test_obi_scalper.py:226:        self.strategy = OBIScalperStrategy(self.config, "obi_scalper")
./tests/test_obi_scalper.py:269:            'obi_threshold': 4,
./tests/test_continuous_gms.py:81:            'obi_smoothed_5': 0.15,
./tests/test_continuous_gms.py:82:            'obi_smoothed_20': 0.15,  # Add for 20 level test
./tests/test_continuous_gms.py:83:            'obi_zscore_5': 1.0,      # Add for zscore test
./tests/test_continuous_gms.py:84:            'obi_zscore_20': 1.0,     # Add for zscore test
./tests/test_continuous_gms.py:109:        signals['obi_smoothed_5'] = 0.25  # Strong OBI
./tests/test_continuous_gms.py:120:        signals['obi_smoothed_5'] = 0.15  # Weak OBI
./tests/test_continuous_gms.py:155:        signals['obi_smoothed_5'] = 0.01  # Very weak OBI
./tests/test_continuous_gms.py:166:        signals['obi_smoothed_5'] = -0.25  # Strong negative OBI
./tests/test_continuous_gms.py:177:        signals['obi_smoothed_5'] = -0.15  # Weak negative OBI
./tests/test_continuous_gms.py:188:        signals['obi_smoothed_5'] = 0.01  # Very weak OBI
./tests/test_continuous_gms.py:207:        signals['obi_smoothed_20'] = 0.25  # Strong OBI
./tests/test_continuous_gms.py:218:        signals['obi_smoothed_20'] = 0.15  # Weak OBI
./tests/test_continuous_gms.py:231:        signals['obi_smoothed_5'] = 0.25  # Strong OBI
./tests/test_continuous_gms.py:317:        signals['obi_smoothed_5'] = 0.25
./tests/test_continuous_gms.py:327:        signals['obi_smoothed_5'] = -0.25
./tests/test_obi_scalper_stub.py:38:            "obi_smoothed_5",
./tests/test_obi_scalper_stub.py:39:            "obi_zscore_5",
./tests/test_obi_scalper_stub.py:76:            "obi_smoothed_5": 0.15,
./tests/test_obi_scalper_stub.py:77:            "obi_zscore_5": 1.2,
./tests/test_obi_scalper_stub.py:99:            "obi_smoothed_5": 0.15
./tests/integration/test_gms_with_etl.py:293:                    'obi_smoothed_5': 0.2,  # Positive OBI (bullish)
./tests/integration/test_gms_with_etl.py:294:                    'obi_zscore_5': 1.5,    # Significant deviation
./tests/integration/test_gms_with_etl.py:321:                    signals['obi_smoothed_5'] = 0.5  # Strong positive OBI (very bullish)
./tests/integration/test_gms_with_etl.py:322:                    signals['obi_zscore_5'] = 2.5    # Very significant deviation
./tests/test_strategy_factory.py:85:            "obi_scalper": OBIScalperStrategy,
./tests/test_strategy_factory.py:127:        strategy = factory.create_strategy("obi_scalper")
./tests/test_obi_helper.py:23:        columns = ['timestamp', 'obi_10', 'price', 'volume']
./tests/test_obi_helper.py:25:        assert result == 'obi_10'
./tests/test_obi_helper.py:35:        columns = ['timestamp', 'raw_obi_5', 'obi_5', 'price', 'volume']
./tests/test_obi_helper.py:53:        assert "obi_5" in str(exc_info.value)
./tests/test_obi_helper.py:65:        columns = ['timestamp', 'raw_obi_10', 'obi_20', 'price']
./tests/test_obi_helper.py:70:        assert "Available OBI columns: ['raw_obi_10', 'obi_20']" in error_msg
./tests/test_obi_helper.py:75:        assert result == ['raw_obi_5', 'obi_5']
./tests/test_obi_helper.py:80:        assert result == ['obi_10', 'raw_obi_10']
./tests/test_obi_helper.py:85:        assert result == ['raw_obi_20', 'obi_20']
./tests/test_obi_helper.py:94:        columns_10 = ['timestamp', 'obi_10', 'price']
./tests/test_obi_helper.py:95:        assert obi_col(columns_10, 10) == 'obi_10'
./tests/test_obi_helper.py:109:        columns = ['timestamp', 'raw_obi_5_extra', 'obi_5_suffix', 'price']
./tests/test_gms_adaptive_thresholds.py:144:            'obi_smoothed_5': 0.1,
./tests/test_gms_adaptive_thresholds.py:172:            'obi_smoothed_5': 0.1,
./tests/test_gms_adaptive_thresholds.py:240:                'obi_smoothed_5': 0.1,
./tests/test_gms_adaptive_thresholds.py:263:            'obi_smoothed_5': 0.1,
./tests/test_obi_scalper_tick_size.py:54:                        "obi_l1_3_trigger": 0.50,
./hyperliquid_bot/core/gms_detector.py:284:        available_obi_cols = [c for c in available_columns if 'obi' in c.lower()]
./hyperliquid_bot/config/settings.py:513:    LEGACY_DEPTH_KEYS: ClassVar[list[str]] = ["obi_levels", "depth_levels_for_calc"]
./hyperliquid_bot/strategies/strategy_factory.py:22:    "obi_scalper": OBIScalperStrategy,
./hyperliquid_bot/strategies/obi_scalper_strategy.py:288:                    self.diag['obi_threshold'] += 1  # T-106A: Increment OBI threshold failure counter
./hyperliquid_bot/strategies/evaluator.py:102:            signals_needed.append("obi_smoothed")
./hyperliquid_bot/strategies/evaluator.py:215:                self.logger.warning(f"Dynamic OBI signal '{obi_signal_name}' not found, falling back to 'obi_smoothed'")
./hyperliquid_bot/strategies/evaluator.py:216:                obi = signals.get("obi_smoothed")
./hyperliquid_bot/strategies/evaluator.py:811:            self.strategies["obi_scalper"] = OBIScalperStrategy(self.config, "obi_scalper")
./hyperliquid_bot/strategies/evaluator.py:855:        if "obi_scalper" in self.strategies and cfg.strategies.obi_scalper_active_in_all_regimes:
./hyperliquid_bot/strategies/evaluator.py:856:            active_names.append("obi_scalper")
./hyperliquid_bot/strategies/evaluator.py:869:                        return ["obi_scalper"] if "obi_scalper" in self.strategies else []
./hyperliquid_bot/strategies/evaluator.py:927:                         if "obi_scalper" in self.strategies and cfg.strategies.gms_activate_obi_scalper_in_chop:
./hyperliquid_bot/strategies/evaluator.py:928:                             active_names.append("obi_scalper")
./hyperliquid_bot/signals/calculator.py:417:                    self.logger.info(f"Skipping Z-score normalization for OBI {suffix} as 'obi_zscore_window' is {zscore_window}.")
./hyperliquid_bot/signals/calculator.py:1435:            "obi_smoothed",
./hyperliquid_bot/tests/test_signal_engine.py:50:            "obi_levels": 5,
./hyperliquid_bot/tests/test_signal_engine.py:51:            "obi_smoothing_window": 8,
./hyperliquid_bot/tests/test_signal_engine.py:52:            "obi_smoothing_type": "sma",
./hyperliquid_bot/tests/test_signal_engine.py:53:            "obi_zscore_window": None, # Default to None
./hyperliquid_bot/tests/test_signal_engine.py:249:    assert "obi_smoothed_5" in signals_df.columns
./hyperliquid_bot/tests/test_signal_engine.py:250:    assert "obi_zscore_5" in signals_df.columns
./hyperliquid_bot/tests/test_signal_engine.py:251:    assert "obi_smoothed_10_custom" in signals_df.columns
./hyperliquid_bot/tests/test_signal_engine.py:252:    assert "obi_zscore_10_custom" in signals_df.columns
./hyperliquid_bot/tests/test_signal_engine.py:260:    assert signals_df["obi_smoothed_5"].iloc[0] == sample_data_df['raw_obi_5'].iloc[0] # EMA specific
./hyperliquid_bot/tests/test_signal_engine.py:261:    assert signals_df["obi_zscore_5"].isna().sum() == 1 # Z-score NaNs
./hyperliquid_bot/tests/test_signal_engine.py:282:    assert signals_df["obi_smoothed_10_custom"].isna().sum() == 2 # Due to initial NaNs in raw
./hyperliquid_bot/tests/test_signal_engine.py:283:    assert signals_df["obi_zscore_10_custom"].isna().sum() == 3 # With min_periods=2, we get 3 NaNs total
./hyperliquid_bot/tests/test_signal_engine.py:295:    assert "obi_smoothed_5" in signals_df.columns
./hyperliquid_bot/tests/test_signal_engine.py:296:    assert "obi_zscore_5" in signals_df.columns # Column should exist
./hyperliquid_bot/tests/test_signal_engine.py:297:    assert signals_df["obi_zscore_5"].isna().all() # All values should be NaN
./hyperliquid_bot/tests/test_signal_engine.py:299:    assert "obi_smoothed_10_custom" in signals_df.columns
./hyperliquid_bot/tests/test_signal_engine.py:300:    assert "obi_zscore_10_custom" in signals_df.columns # Column should exist
./hyperliquid_bot/tests/test_signal_engine.py:301:    assert signals_df["obi_zscore_10_custom"].isna().all() # All values should be NaN
./hyperliquid_bot/tests/test_performance.py:65:            "obi_levels": 5,
./hyperliquid_bot/tests/test_performance.py:66:            "obi_smoothing_window": 8,
./hyperliquid_bot/tests/test_performance.py:67:            "obi_smoothing_type": "sma",
./hyperliquid_bot/tests/test_performance.py:68:            "obi_zscore_window": 24,
./hyperliquid_bot/tests/test_performance.py:95:    assert "obi_smoothed_5" in signals_df.columns
./hyperliquid_bot/tests/test_performance.py:96:    assert "obi_zscore_5" in signals_df.columns
./hyperliquid_bot/tests/test_obi_filter.py:40:            'obi_smoothed': 0.15,  # Legacy name
./hyperliquid_bot/tests/test_obi_filter.py:41:            'obi_smoothed_5': 0.15,  # New dynamic name
./hyperliquid_bot/tests/test_obi_filter.py:42:            'obi_zscore_5': 1.5,
./hyperliquid_bot/tests/test_obi_filter.py:59:            'obi_smoothed': 0.15,  # Legacy name
./hyperliquid_bot/tests/test_obi_filter.py:60:            'obi_smoothed_5': 0.15,  # New dynamic name
./hyperliquid_bot/tests/test_obi_filter.py:61:            'obi_zscore_5': 1.5,
./hyperliquid_bot/tests/test_obi_filter.py:95:                    signals_needed.append("obi_smoothed")
./hyperliquid_bot/tests/test_obi_filter.py:104:            self.assertIn("obi_smoothed_5", required_signals)
./hyperliquid_bot/tests/test_obi_filter.py:105:            self.assertIn("obi_smoothed", required_signals)
./hyperliquid_bot/tests/test_obi_filter.py:145:                obi = signals.get("obi_smoothed")
./hyperliquid_bot/tests/test_obi_filter.py:151:            'obi_smoothed': 0.1,
./hyperliquid_bot/tests/test_obi_filter.py:152:            'obi_smoothed_5': 0.15
./hyperliquid_bot/tests/test_obi_filter.py:157:        signals = {'obi_smoothed': 0.1}
./hyperliquid_bot/utils/feature_naming.py:31:        >>> cols = ['timestamp', 'obi_10', 'price']
./hyperliquid_bot/utils/feature_naming.py:33:        'obi_10'
./hyperliquid_bot/utils/feature_naming.py:47:    available_obi_cols = [c for c in columns if 'obi' in c.lower()]
./scripts/run_latin_square_grid.py:27:    "obi_smoothing_window": [8, 14],  # Low, High
./scripts/run_latin_square_grid.py:41:            "obi_smoothing_window": params["obi_smoothing_window"],
./scripts/run_latin_square_grid.py:177:    total_runs = len(PARAM_GRID["obi_smoothing_window"]) * \
./scripts/run_latin_square_grid.py:189:    for obi_win in PARAM_GRID["obi_smoothing_window"]:
./scripts/run_latin_square_grid.py:198:                        "obi_smoothing_window": obi_win,
./scripts/verify_gms_state_mapping.py:101:        'obi_ratio': 0.0,    # Neutral OBI
./scripts/fix_grid_search_overrides.py:9:    "obi_smoothing_window": [8, 14],  # Low, High
./scripts/fix_grid_search_overrides.py:36:    for obi_win in PARAM_GRID["obi_smoothing_window"]:
./scripts/test_backtester.py:101:        'obi_smoothed_5': 0.15,  # Positive OBI (buy pressure)
./scripts/visualize_backtest.py:232:            if indicator_name == 'obi_smoothed':
./scripts/visualize_backtest.py:248:            if 'obi' in indicator_name.lower() or 'slope' in indicator_name.lower():
./scripts/visualize_backtest.py:395:         if 'obi' in name.lower() or 'slope' in name.lower():
./scripts/visualize_backtest.py:918:        indicators_to_plot.append('obi_smoothed')
./scripts/test_single_run.py:20:            "obi_smoothing_window": 10
./scripts/analyze_obi_filtered.py:22:        required_cols = ['raw_obi_zscore', 'obi_zscore_filtered']
./scripts/analyze_obi_filtered.py:38:        print(f"\nOBI ZSCORE FILTERED TYPE: {df['obi_zscore_filtered'].dtype}")
./scripts/analyze_obi_filtered.py:41:        true_count = df['obi_zscore_filtered'].sum()
./scripts/analyze_obi_filtered.py:48:            filtered_true = df[df['obi_zscore_filtered']]
./scripts/analyze_obi_filtered.py:77:            filtered_false = df[~df['obi_zscore_filtered']]
./scripts/analyze_obi_filtered.py:99:            pos_filtered = (df['obi_zscore_filtered'] & (df['raw_obi_zscore'] > 0))
./scripts/analyze_obi_filtered.py:104:            neg_filtered = (df['obi_zscore_filtered'] & (df['raw_obi_zscore'] < 0))
./scripts/analyze_obi_filtered.py:109:            abs_filtered = df['obi_zscore_filtered']
./scripts/generate_latin_square_overrides.py:9:    "obi_smoothing_window": [8, 14],  # Low, High
./scripts/generate_latin_square_overrides.py:36:    for obi_win in PARAM_GRID["obi_smoothing_window"]:
./scripts/compare_feature_distributions.py:20:    'obi_smoothed',
./scripts/verify_gms_inline.py:81:            'obi_ratio': 0.0,    # Neutral OBI
./scripts/test_tf_v3_strategy.py:77:    signals[f'obi_smoothed_5'] = 0.15  # Positive OBI (buy pressure)
./scripts/process_l2_data_slice.py:445:    output_directory = os.path.join(project_root, "analysis_outputs", "obi_eda")
./scripts/run_latin_square.py:26:    "obi_smoothing_window": [6, 14],
./scripts/run_latin_square.py:35:        "obi_smoothing_window": None  # Will be filled in
./scripts/run_latin_square.py:49:            "obi_smoothing_window": params["obi_smoothing_window"]
./scripts/run_latin_square.py:102:    total_runs = len(PARAM_GRID["obi_smoothing_window"]) * \
./scripts/run_latin_square.py:110:    for obi_win in PARAM_GRID["obi_smoothing_window"]:
./scripts/run_latin_square.py:118:                        "obi_smoothing_window": obi_win,
./scripts/analyze_features.py:45:        'obi_smoothed',     # Microstructure Confirmation
./scripts/analyze_features.py:175:            'obi_smoothed',
./scripts/test_tf_v3_direct.py:80:    signals[f'obi_smoothed_5'] = 0.15  # Positive OBI (buy pressure)
