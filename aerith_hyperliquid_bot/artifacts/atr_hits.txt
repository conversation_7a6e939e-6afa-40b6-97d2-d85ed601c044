./tools/etl_l20_to_1s.py:317:    ohlc_1h['atr_14'] = tr_series.rolling(window=length, min_periods=length).mean()
./tools/etl_l20_to_1s.py:341:    atr_columns_to_remove = ['atr_14_sec', 'atr_percent_sec']
./tools/etl_l20_to_1s.py:355:    atr_series = ohlc_1h_copy['atr_14']
./tools/etl_l20_to_1s.py:365:        atr_reindexed.to_frame('atr_14_sec'),
./tools/etl_l20_to_1s.py:373:        df_merged['atr_percent_sec'] = df_merged['atr_14_sec'] / df_merged['mid_price']
./tools/etl_l20_to_1s.py:485:    df['atr_14'] = atr_values
./tools/etl_l20_to_1s.py:560:    df['atr'] = atr_values
./tools/etl_l20_to_1s.py:563:    df['atr_percent'] = df['atr'] / df['close']
./tools/etl_l20_to_1s.py:566:    df['atr_percent_sec'] = df['atr_14_sec'] / df['close']
./tools/etl_l20_to_1s.py:764:        nan_count = resampled['atr_14_sec'].isna().sum()
./tests/test_config_fallbacks.py:171:                'atr_period': 14,
./tests/test_config_fallbacks.py:172:                'atr_trail_k': 3.0,
./tests/test_config_fallbacks.py:177:                'atr_fallback_pct': 0.05  # Custom value different from default
./tests/test_config_fallbacks.py:189:        assert getattr(config.tf_v3, 'atr_fallback_pct', 0.01) == 0.05
./tests/test_priming_integration.py:58:                    'atr_percent_sec': np.random.uniform(0.001, 0.05, n_samples),
./tests/test_continuous_gms.py:79:            'atr_percent': 0.03,
./tests/test_continuous_gms.py:90:            'atr_percent_pctile': 0.5,
./tests/test_continuous_gms.py:101:            'atr': 500.0
./tests/test_continuous_gms.py:130:        signals['atr_percent'] = 0.07  # High volatility
./tests/test_continuous_gms.py:142:        signals['atr_percent'] = 0.01  # Low volatility
./tests/test_continuous_gms.py:291:        signals['atr'] = 500.0
./tests/test_continuous_gms.py:294:        threshold = signals['atr'] * self.detector.risk_suppressed_pnl_atr_mult
./tests/test_etl_l20_to_1s.py:177:        self.assertIn("atr", df_post.columns)
./tests/test_etl_l20_to_1s.py:178:        self.assertIn("atr_percent", df_post.columns)
./tests/test_etl_l20_to_1s.py:209:        self.assertIn("atr", df.columns)
./tests/test_etl_l20_to_1s.py:210:        self.assertIn("atr_percent", df.columns)
./tests/integration/test_gms_with_etl.py:299:                    'atr_percent': 0.02,    # 2% ATR (moderate volatility)
./tests/test_etl_atr.py:129:        self.assertIn('atr_14', df_with_atr.columns)
./tests/test_etl_atr.py:132:        atr_values = df_with_atr['atr_14'].dropna()
./tests/test_etl_atr.py:137:        first_14_atr = df_with_atr['atr_14'].head(14)
./tests/test_etl_atr.py:162:        self.assertIn('atr_14_sec', df_result.columns)
./tests/test_etl_atr.py:163:        self.assertIn('atr_percent_sec', df_result.columns)
./tests/test_etl_atr.py:164:        self.assertEqual(df_result['atr_14_sec'].dtype, np.float64)
./tests/test_etl_atr.py:165:        self.assertEqual(df_result['atr_percent_sec'].dtype, np.float64)
./tests/test_etl_atr.py:168:        nan_count = df_result['atr_14_sec'].isna().sum()
./tests/test_etl_atr.py:180:            'atr_14': np.random.uniform(100, 500, 24)
./tests/test_etl_atr.py:194:        self.assertIn('atr_14_sec', df_merged.columns)
./tests/test_etl_atr.py:197:        first_hour_atr = df_merged['atr_14_sec'].head(3600)
./tests/test_tf_v3.py:161:        "atr_14": atr_14.iloc[-1],
./tests/test_tf_v3.py:346:    strategy.state['trail_price'] = position['entry'] - mock_signals['atr_14'] * strategy.tf_v3_config.atr_trail_k
./tests/test_tf_v3.py:377:    strategy.state['trail_price'] = position['entry'] - mock_signals['atr_14'] * strategy.tf_v3_config.atr_trail_k
./tests/test_tf_v3.py:414:    signals_copy["atr_14"] = 1000.0
./tests/test_spread_causal.py:45:            'atr_14_sec': 10.0,
./tests/test_spread_causal.py:46:            'atr': 10.0,
./tests/test_spread_causal.py:47:            'atr_percent': 0.0002,
./tests/test_spread_causal.py:48:            'atr_percent_sec': 0.0002,
./tests/test_gms_adaptive_thresholds.py:141:            'atr_percent_sec': 0.03,
./tests/test_gms_adaptive_thresholds.py:142:            'atr_percent': 0.03,
./tests/test_gms_adaptive_thresholds.py:148:            'atr_14_sec': 500.0,
./tests/test_gms_adaptive_thresholds.py:149:            'atr': 500.0,
./tests/test_gms_adaptive_thresholds.py:169:            'atr_percent_sec': 0.03,
./tests/test_gms_adaptive_thresholds.py:170:            'atr_percent': 0.03,
./tests/test_gms_adaptive_thresholds.py:176:            'atr_14_sec': 500.0,
./tests/test_gms_adaptive_thresholds.py:177:            'atr': 500.0,
./tests/test_gms_adaptive_thresholds.py:184:            signals['atr_percent_sec'] = 0.01 + (i * 0.001)  # Increasing volatility
./tests/test_gms_adaptive_thresholds.py:185:            signals['atr_percent'] = signals['atr_percent_sec']
./tests/test_gms_adaptive_thresholds.py:237:                'atr_percent_sec': 0.03,
./tests/test_gms_adaptive_thresholds.py:238:                'atr_percent': 0.03,
./tests/test_gms_adaptive_thresholds.py:244:                'atr_14_sec': 500.0,
./tests/test_gms_adaptive_thresholds.py:245:                'atr': 500.0,
./tests/test_gms_adaptive_thresholds.py:260:            'atr_percent_sec': np.nan,  # NaN volatility
./tests/test_gms_adaptive_thresholds.py:261:            'atr_percent': np.nan,
./tests/test_gms_adaptive_thresholds.py:267:            'atr_14_sec': 500.0,
./tests/test_gms_adaptive_thresholds.py:268:            'atr': 500.0,
./tests/test_priming_causal.py:54:                    'atr_percent_sec': np.random.uniform(0.001, 0.05, n_samples),
./tests/test_priming_causal.py:269:            'atr_percent_sec': [0.02],
./tests/test_priming_causal.py:276:            'atr_percent_sec': [0.03],
./hyperliquid_bot/core/detector.py:135:                # Assume SignalCalculator provides 'atr_tf' if TF strategy is enabled
./hyperliquid_bot/core/detector.py:137:                     signals_needed.append("atr_tf")
./hyperliquid_bot/core/detector.py:139:                # else: signals_needed.append("atr") # Assuming a generic 'atr' exists
./hyperliquid_bot/core/detector.py:191:        atr_tf = signals.get("atr_tf")
./hyperliquid_bot/core/detector.py:418:            'atr_percent',      # Volatility
./hyperliquid_bot/core/detector.py:463:            signals_needed.append('atr_percent_pctile')
./hyperliquid_bot/core/detector.py:489:        required_now = ['timestamp', 'atr_percent', 'ma_slope', f'obi_smoothed_{self.depth_levels}', 'spread_mean', 'spread_std'] # Base requirements
./hyperliquid_bot/core/detector.py:541:        atr_pct = signals.get('atr_percent', np.nan)
./hyperliquid_bot/core/detector.py:718:            atr_pct_pctile = signals.get('atr_percent_pctile', np.nan)
./hyperliquid_bot/core/risk.py:97:            atr = signals.get("atr_tf")
./hyperliquid_bot/core/risk.py:101:            atr = signals.get("atr_mr")
./hyperliquid_bot/core/risk.py:105:            atr = signals.get("atr_mv") # Need atr_mv signal
./hyperliquid_bot/core/risk.py:110:            atr = signals.get("atr_14_sec") or signals.get("atr")
./hyperliquid_bot/core/risk.py:117:                if hasattr(cfg.tf_v3, 'atr_trail_k'):
./hyperliquid_bot/core/gms_detector.py:188:        self.ATR_COL = 'atr_14_sec'  # Default ATR column name
./hyperliquid_bot/core/gms_detector.py:189:        self.ATR_PCT_COL = 'atr_percent_sec'  # Default ATR percent column name
./hyperliquid_bot/core/gms_detector.py:313:        signals_needed.append('atr_percent')
./hyperliquid_bot/core/gms_detector.py:350:            signals_needed.append('atr_percent_pctile')
./hyperliquid_bot/core/gms_detector.py:355:        signals_needed.append('atr')  # Keep legacy ATR for backward compatibility
./hyperliquid_bot/core/gms_detector.py:452:        required_now = ['timestamp', 'atr_percent', 'ma_slope', f'obi_smoothed_{self.depth_levels}', 'spread_mean', 'spread_std'] # Base requirements
./hyperliquid_bot/core/gms_detector.py:500:            if sig in ['atr', 'atr_percent', self.ATR_COL, self.ATR_PCT_COL] and pd.isna(value):
./hyperliquid_bot/core/gms_detector.py:522:            atr_pct = signals.get('atr_percent', np.nan)
./hyperliquid_bot/core/gms_detector.py:723:            atr_pct_pctile = signals.get('atr_percent_pctile', np.nan)
./hyperliquid_bot/core/gms_detector.py:947:                atr = float(signals.get('atr', 0.0))
./hyperliquid_bot/core/gms_detector.py:1026:                  ('atr' in signals and not pd.isna(signals.get('atr')))
./hyperliquid_bot/core/gms_detector.py:1028:                      ('atr_percent' in signals and not pd.isna(signals.get('atr_percent')))
./hyperliquid_bot/core/gms_detector.py:1032:            required_for_state = [s for s in required_for_state if s != 'atr' and s != self.ATR_COL]
./hyperliquid_bot/core/gms_detector.py:1034:            required_for_state = [s for s in required_for_state if s != 'atr_percent' and s != self.ATR_PCT_COL]
./hyperliquid_bot/strategies/tf_v3.py:397:                    atr_sec = signals.get('atr_14_sec')
./hyperliquid_bot/strategies/evaluator.py:92:        signals_needed = ["forecast", "tf_ewma_fast", "tf_ewma_slow", "regime", "close", "atr_tf"]
./hyperliquid_bot/strategies/evaluator.py:138:        atr = signals["atr_tf"]
./hyperliquid_bot/signals/calculator.py:354:            _add_atr(signals_df, atr_period, "atr")
./hyperliquid_bot/signals/calculator.py:355:            if "atr" in signals_df.columns:
./hyperliquid_bot/signals/calculator.py:357:                    f"Main ATR calculated. NaN count: {signals_df['atr'].isna().sum()}"
./hyperliquid_bot/signals/calculator.py:1065:                if tf_atr_period == atr_period and "atr" in signals_df.columns:
./hyperliquid_bot/signals/calculator.py:1066:                    signals_df["atr_tf"] = signals_df["atr"]
./hyperliquid_bot/signals/calculator.py:1071:                    _add_atr(signals_df, tf_atr_period, "atr_tf")
./hyperliquid_bot/signals/calculator.py:1115:                if mr_atr_period == atr_period and "atr" in signals_df.columns:
./hyperliquid_bot/signals/calculator.py:1116:                    signals_df["atr_mr"] = signals_df["atr"]
./hyperliquid_bot/signals/calculator.py:1122:                            columns={kc_atr_col: "atr_mr"},
./hyperliquid_bot/signals/calculator.py:1130:                        _add_atr(signals_df, mr_atr_period, "atr_mr")
./hyperliquid_bot/signals/calculator.py:1182:                if mv_atr_period == atr_period and "atr" in signals_df.columns:
./hyperliquid_bot/signals/calculator.py:1183:                    signals_df["atr_mv"] = signals_df["atr"]
./hyperliquid_bot/signals/calculator.py:1188:                    _add_atr(signals_df, mv_atr_period, "atr_mv")
./hyperliquid_bot/signals/calculator.py:1267:                if 'atr_percent_sec' in signals_df.columns:
./hyperliquid_bot/signals/calculator.py:1269:                    self.logger.info("Using existing 'atr_percent_sec' from feature files for GMS detector")
./hyperliquid_bot/signals/calculator.py:1271:                    signals_df["atr_percent"] = signals_df["atr_percent_sec"] * 100  # Convert to percentage
./hyperliquid_bot/signals/calculator.py:1275:                    atr_col_for_perc = "atr"  # Use the main ATR calculated earlier
./hyperliquid_bot/signals/calculator.py:1298:                        signals_df["atr_percent"] = (
./hyperliquid_bot/signals/calculator.py:1302:                        signals_df["atr_percent"] = signals_df["atr_percent"].replace(
./hyperliquid_bot/signals/calculator.py:1306:                            f"ATR Percent calculated. NaN count: {signals_df['atr_percent'].isna().sum()}"
./hyperliquid_bot/signals/calculator.py:1312:                        signals_df["atr_percent"] = np.nan
./hyperliquid_bot/signals/calculator.py:1313:                        signals_df["atr_percent_pctile"] = np.nan
./hyperliquid_bot/signals/calculator.py:1321:                if rolling_window > 0 and "atr_percent" in signals_df.columns:
./hyperliquid_bot/signals/calculator.py:1322:                    signals_df['atr_percent_pctile'] = (
./hyperliquid_bot/signals/calculator.py:1323:                        signals_df['atr_percent']
./hyperliquid_bot/signals/calculator.py:1328:                        f"ATR Percent Percentile calculated. NaN count: {signals_df['atr_percent_pctile'].isna().sum()}"
./hyperliquid_bot/signals/calculator.py:1430:            "atr",
./hyperliquid_bot/signals/calculator.py:1440:            "atr_percent",
./hyperliquid_bot/signals/calculator.py:1473:        assert 'atr_mr' not in signals_df or signals_df['atr_mr'].notna().any() or mr_atr_period == 0, "atr_mr still all NaN!"
./hyperliquid_bot/tests/test_obi_filter.py:39:            'atr_tf': 500.0,
./hyperliquid_bot/tests/test_obi_filter.py:44:            'atr_percent': 0.01,
./hyperliquid_bot/tests/test_obi_filter.py:58:            'atr_tf': 500.0,
./hyperliquid_bot/tests/test_obi_filter.py:63:            'atr_percent': 0.01,
./hyperliquid_bot/tests/test_obi_filter.py:87:                signals_needed = ["forecast", "tf_ewma_fast", "tf_ewma_slow", "regime", "close", "atr_tf"]
./hyperliquid_bot/utils/data_utils.py:26:def verify_atr_availability(df: pd.DataFrame, atr_column: str = 'atr_14', min_required: int = 14) -> bool:
./hyperliquid_bot/data/feature_store.py:121:                    "realised_vol_1s", "atr", "atr_percent", "atr_14_sec", "atr_percent_sec", "unrealised_pnl"
./hyperliquid_bot/data/handler.py:675:            feature_df = feature_store.load_1s(date_str, columns=["timestamp", "atr_14_sec", "atr_percent_sec", "atr", "atr_percent"])
./hyperliquid_bot/data/handler.py:677:            if not feature_df.empty and "atr_14_sec" in feature_df.columns and "timestamp" in feature_df.columns:
./hyperliquid_bot/data/handler.py:679:                if "atr_14_sec" not in self.combined_data.columns:
./hyperliquid_bot/data/handler.py:680:                    self.combined_data["atr_14_sec"] = np.nan
./hyperliquid_bot/data/handler.py:681:                if "atr_percent_sec" not in self.combined_data.columns:
./hyperliquid_bot/data/handler.py:682:                    self.combined_data["atr_percent_sec"] = np.nan
./hyperliquid_bot/data/handler.py:692:                atr_mean = feature_df['atr_14_sec'].mean()
./hyperliquid_bot/data/handler.py:693:                atr_percent_mean = feature_df['atr_percent_sec'].mean()
./hyperliquid_bot/data/handler.py:696:                self.combined_data['atr_14_sec'] = atr_mean
./hyperliquid_bot/data/handler.py:697:                self.combined_data['atr_percent_sec'] = atr_percent_mean
./hyperliquid_bot/data/handler.py:700:                self.combined_data['atr_14_sec'] = self.combined_data['atr_14_sec'].fillna(method='ffill')
./hyperliquid_bot/data/handler.py:701:                self.combined_data['atr_percent_sec'] = self.combined_data['atr_percent_sec'].fillna(method='ffill')
./hyperliquid_bot/data/handler.py:704:                self.combined_data['atr'] = self.combined_data['atr_14_sec']
./hyperliquid_bot/data/handler.py:705:                self.combined_data['atr_percent'] = self.combined_data['atr_percent_sec']
./hyperliquid_bot/data/handler.py:708:                atr_nan_count = self.combined_data['atr_14_sec'].isna().sum()
./hyperliquid_bot/data/handler.py:713:                logger.warning("ATR column 'atr_14_sec' not found in feature data. GMS detector may not work properly.")
./hyperliquid_bot/data/handler.py:716:            logger.warning("ATR column 'atr_14_sec' not found in feature data. GMS detector may not work properly.")
./hyperliquid_bot/backtester/backtester_patch.py:59:        if 'atr_14_sec' in self.data_handler.combined_data.columns:
./hyperliquid_bot/backtester/backtester_patch.py:60:            missing_atr = self.data_handler.combined_data['atr_14_sec'].isna().sum()
./hyperliquid_bot/backtester/backtester_patch.py:64:            if 'atr' not in self.data_handler.combined_data.columns:
./hyperliquid_bot/backtester/backtester_patch.py:65:                self.data_handler.combined_data['atr'] = self.data_handler.combined_data['atr_14_sec']
./hyperliquid_bot/backtester/backtester_patch.py:67:            if 'atr_percent' not in self.data_handler.combined_data.columns:
./hyperliquid_bot/backtester/backtester_patch.py:68:                self.data_handler.combined_data['atr_percent'] = self.data_handler.combined_data['atr_14_sec'] / self.data_handler.combined_data['close']
./hyperliquid_bot/backtester/backtester_patch.py:70:            self.logger.warning("ATR column 'atr_14_sec' not found in feature data. GMS detector may not work properly.")
./hyperliquid_bot/backtester/backtester.py:139:            if 'atr_14_sec' in self.data_handler.combined_data.columns:
./hyperliquid_bot/backtester/backtester.py:140:                missing_atr = self.data_handler.combined_data['atr_14_sec'].isna().sum()
./hyperliquid_bot/backtester/backtester.py:144:                if 'atr' not in self.data_handler.combined_data.columns:
./hyperliquid_bot/backtester/backtester.py:145:                    self.data_handler.combined_data['atr'] = self.data_handler.combined_data['atr_14_sec']
./hyperliquid_bot/backtester/backtester.py:147:                if 'atr_percent' not in self.data_handler.combined_data.columns:
./hyperliquid_bot/backtester/backtester.py:148:                    self.data_handler.combined_data['atr_percent'] = self.data_handler.combined_data['atr_14_sec'] / self.data_handler.combined_data['close']
./hyperliquid_bot/backtester/backtester.py:150:                self.logger.warning("ATR column 'atr_14_sec' not found in feature data. GMS detector may not work properly.")
./hyperliquid_bot/backtester/backtester.py:257:            atr_signal_name = "atr_tf"
./hyperliquid_bot/backtester/backtester.py:270:            atr_signal_name = "atr_mr"
./hyperliquid_bot/backtester/backtester.py:301:            atr_signal_names = ["atr", "atr_14", "atr_14_sec"]
./hyperliquid_bot/backtester/backtester.py:315:                stop_mult = getattr(self.config.tf_v3, 'atr_trail_k', 3.0)
./hyperliquid_bot/backtester/backtester.py:316:                target_mult = getattr(self.config.tf_v3, 'atr_target_k', 6.0)
./hyperliquid_bot/backtester/backtester.py:328:                fallback_pct = getattr(self.config.tf_v3, 'atr_fallback_pct', 0.01)
./hyperliquid_bot/backtester/backtester.py:346:            atr_signal_name = "atr_mv" # Use MV-specific ATR signal
./hyperliquid_bot/backtester/backtester.py:987:            current_atr = current_signals.get('atr')
./scripts/diagnose_gms_inputs.py:36:        atr_pct = signals_row.get('atr_percent', np.nan)
./scripts/diagnose_gms_inputs.py:43:            'atr_percent': atr_pct,
./scripts/diagnose_gms_inputs.py:101:    df_clean = df.dropna(subset=['atr_percent', 'ma_slope'])
./scripts/diagnose_gms_inputs.py:113:    atr_stats = df_clean['atr_percent'].describe()
./scripts/diagnose_gms_inputs.py:123:    low_vol_count = (df_clean['atr_percent'] <= vol_low_thresh).sum()
./scripts/diagnose_gms_inputs.py:124:    mid_vol_count = ((df_clean['atr_percent'] > vol_low_thresh) &
./scripts/diagnose_gms_inputs.py:125:                     (df_clean['atr_percent'] < vol_high_thresh)).sum()
./scripts/diagnose_gms_inputs.py:126:    high_vol_count = (df_clean['atr_percent'] >= vol_high_thresh).sum()
./scripts/diagnose_gms_inputs.py:156:    low_vol_weak_mom = ((df_clean['atr_percent'] <= vol_low_thresh) &
./scripts/diagnose_gms_inputs.py:172:    ax1.hist(df_clean['atr_percent'], bins=50, alpha=0.7, color='blue', edgecolor='black')
./scripts/diagnose_gms_inputs.py:225:    low_vol_count = (df_clean['atr_percent'] <= vol_low_thresh).sum()
./scripts/diagnose_gms_inputs.py:227:    low_vol_weak_mom = ((df_clean['atr_percent'] <= vol_low_thresh) &
./scripts/diagnose_gms_inputs.py:244:        f.write(f"- Mean: {df_clean['atr_percent'].mean():.6f}\n")
./scripts/diagnose_gms_inputs.py:245:        f.write(f"- Median: {df_clean['atr_percent'].median():.6f}\n")
./scripts/diagnose_gms_inputs.py:246:        f.write(f"- 5th percentile: {df_clean['atr_percent'].quantile(0.05):.6f}\n")
./scripts/diagnose_gms_inputs.py:247:        f.write(f"- 95th percentile: {df_clean['atr_percent'].quantile(0.95):.6f}\n\n")
./scripts/diagnose_gms_inputs.py:266:            f.write(f"- Typical ATR values: {df_clean['atr_percent'].median():.6f} ({df_clean['atr_percent'].median()*100:.3f}%)\n\n")
./scripts/test_market_bias.py:64:        'atr_tf': atr,
./scripts/test_market_bias.py:65:        'atr_mr': atr * 0.8,  # Slightly different ATR for MR strategy
./scripts/test_market_bias.py:66:        'atr_mv': atr * 0.7,  # Another ATR variant
./scripts/verify_gms_state_mapping.py:99:        'atr_pct': 0.005,
./scripts/check_atr_variation.py:37:            atr_val = df['atr_14_sec'].iloc[0]  # Get first ATR value (should be same for whole hour)
./scripts/check_atr_variation.py:44:                'atr_14_sec': atr_val,
./scripts/check_atr_variation.py:46:                'atr_percent': atr_pct
./scripts/check_atr_variation.py:58:        atr_series = pd.Series([x['atr_14_sec'] for x in atr_values])
./scripts/check_atr_variation.py:77:                prev_atr = atr_values[i-1]['atr_14_sec']
./scripts/check_atr_variation.py:78:                change = data['atr_14_sec'] - prev_atr
./scripts/update_features_with_atr.py:141:    ohlc_1h['atr_14'] = tr.rolling(window=length, min_periods=length).mean()
./scripts/update_features_with_atr.py:145:        ohlc_1h['atr_14'],
./scripts/update_features_with_atr.py:152:    df_merged['atr_14_sec'] = df_merged['atr_14'].ffill()
./scripts/update_features_with_atr.py:155:    df_merged = df_merged.drop(columns=['atr_14'])
./scripts/update_features_with_atr.py:158:    df_merged['atr_percent_sec'] = df_merged['atr_14_sec'] / df_merged['mid_price']
./scripts/update_features_with_atr.py:194:    if 'atr_14_sec' in df.columns and 'atr_percent_sec' in df.columns and not force:
./scripts/compare_signals.py:113:    signals_to_compare = ['open', 'high', 'low', 'close', 'volume', 'forecast', 'adx', 'atr_tf']
./scripts/test_backtester.py:94:        'atr_14': 1000.0,
./scripts/test_backtester.py:95:        'atr_14_sec': 1000.0,
./scripts/test_backtester.py:96:        'atr': 1000.0,
./scripts/test_backtester.py:97:        'atr_percent': 0.02,  # 2% of price
./scripts/test_backtester.py:98:        'atr_percent_sec': 0.02,
./scripts/test_market_bias_mapping.py:71:            "atr_tf": 50.0,  # Required for risk calculation
./scripts/sanity_check_thresholds.py:63:            for col in ['atr_percent', 'atr_percent_sec', 'atr_14_sec']:
./scripts/regenerate_features_with_atr.py:149:        if 'atr_14_sec' not in df.columns:
./scripts/regenerate_features_with_atr.py:153:        if 'atr_percent_sec' not in df.columns:
./scripts/regenerate_features_with_atr.py:167:        atr_nan_count = df['atr_14_sec'].isna().sum()
./scripts/test_tf_v3.py:58:        'atr_14': 1000.0,
./scripts/test_tf_v3.py:59:        'atr_14_sec': 1000.0,
./scripts/add_atr_to_features.py:61:    df['atr_14_sec'] = df['true_range'].rolling(window=period).mean()
./scripts/add_atr_to_features.py:64:    df['atr_percent_sec'] = df['atr_14_sec'] / df['close'] * 100
./scripts/add_atr_to_features.py:66:    return df[['atr_14_sec', 'atr_percent_sec']]
./scripts/add_atr_to_features.py:113:    if 'atr_14_sec' in combined_df.columns:
./scripts/add_atr_to_features.py:115:        combined_df = combined_df.drop(columns=['atr_14_sec', 'atr_percent_sec'], errors='ignore')
./scripts/add_atr_to_features.py:125:    combined_df['atr'] = combined_df['atr_14_sec']
./scripts/add_atr_to_features.py:126:    combined_df['atr_percent'] = combined_df['atr_percent_sec']
./scripts/add_atr_to_features.py:129:    nan_count = combined_df['atr_14_sec'].isna().sum()
./scripts/compare_feature_distributions.py:25:    'atr_percent'   # gms_atr_percent_period
./scripts/smoke_test_tf_v3.py:163:                'atr_14': 1000.0,
./scripts/smoke_test_tf_v3.py:164:                'atr_14_sec': 1000.0,
./scripts/verify_gms_inline.py:79:            'atr_pct': 0.005,
./scripts/test_tf_v3_strategy.py:71:    signals[f'atr'] = 1000.0
./scripts/test_tf_v3_strategy.py:72:    signals[f'atr_percent'] = 0.02  # 2% of price
./scripts/test_tf_v3_strategy.py:73:    signals[f'atr_14_sec'] = 1000.0
./scripts/test_tf_v3_strategy.py:74:    signals[f'atr_percent_sec'] = 0.02
./scripts/verify_atr_quality.py:39:            atr_cols = [col for col in df.columns if 'atr' in col]
./scripts/verify_atr_quality.py:44:            if 'atr_14_sec' in df.columns:
./scripts/verify_atr_quality.py:45:                atr_series = df['atr_14_sec']
./scripts/verify_atr_quality.py:75:            if 'atr_percent_sec' in df.columns:
./scripts/verify_atr_quality.py:76:                atr_pct = df['atr_percent_sec']
./scripts/verify_atr_quality.py:94:                'ATR column exists': 'atr_14_sec' in df.columns,
./scripts/verify_atr_quality.py:95:                'ATR has values': df['atr_14_sec'].count() > 0 if 'atr_14_sec' in df.columns else False,
./scripts/verify_atr_quality.py:96:                'ATR is numeric': df['atr_14_sec'].dtype in ['float64', 'float32'] if 'atr_14_sec' in df.columns else False,
./scripts/verify_atr_quality.py:97:                'Minimal NaN values': df['atr_14_sec'].isna().sum() < 14 if 'atr_14_sec' in df.columns else False,
./scripts/verify_atr_quality.py:98:                'ATR is dynamic': atr_series.diff().abs().sum() > 0 if 'atr_14_sec' in df.columns else False,
./scripts/analyze_features.py:43:        'atr_percent',      # Volatility
./scripts/analyze_features.py:176:            'atr_percent',
./scripts/analyze_features.py:283:             # if 'atr_percent' in analysis_results:
./scripts/analyze_features.py:284:             #     print(f"gms_vol_low_thresh: {analysis_results['atr_percent']['percentiles'][25]:.6f} # 25th percentile")
./scripts/analyze_features.py:285:             #     print(f"gms_vol_high_thresh: {analysis_results['atr_percent']['percentiles'][75]:.6f} # 75th percentile")
./scripts/smoke_test_gms.py:186:    if 'atr_14_sec' not in df.columns:
./scripts/smoke_test_gms.py:187:        logger.error("ATR column 'atr_14_sec' not found in feature data")
./scripts/smoke_test_gms.py:191:    logger.info(f"ATR column 'atr_14_sec' exists: {df['atr_14_sec'].notna().sum()} non-NaN values")
./scripts/test_tf_v3_direct.py:74:    signals[f'atr'] = 1000.0
./scripts/test_tf_v3_direct.py:75:    signals[f'atr_percent'] = 0.02  # 2% of price
./scripts/test_tf_v3_direct.py:76:    signals[f'atr_14_sec'] = 1000.0
./scripts/test_tf_v3_direct.py:77:    signals[f'atr_percent_sec'] = 0.02
