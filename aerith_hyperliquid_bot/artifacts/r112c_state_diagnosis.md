# R-112c GMS Input Diagnosis

## Summary

Analysis of GMS detector configuration reveals **critical threshold misconfiguration** causing 95% "Low_Vol_Range" (CHOP) classification during 2025-03-01 → 03-22.

**Root Cause Identified**: Volatility thresholds are configured in percentage points rather than decimal form.

## Configuration Thresholds (MISCONFIGURED)

| Metric | Low Threshold | High Threshold | Issue |
|--------|---------------|----------------|-------|
| Volatility (ATR %) | **0.5500** (55%!) | **0.9200** (92%!) | ❌ Should be ~0.02-0.06 |
| Momentum (MA Slope) | 50.00 | 100.00 | ⚠️ Possibly too high |

## Problem Analysis

### Volatility Threshold Issue

The current configuration has:
- `gms_vol_low_thresh: 0.55` (interpreted as 55%)
- `gms_vol_high_thresh: 0.92` (interpreted as 92%)

**Expected ATR percentages** in crypto markets are typically:
- Low volatility: ~1-2% (0.01-0.02)
- Medium volatility: ~2-6% (0.02-0.06)
- High volatility: ~6%+ (0.06+)

**Current thresholds require**:
- ATR > 55% for anything above "Low Vol"
- ATR > 92% for "High Vol"

This is impossible in normal market conditions, causing virtually all periods to be classified as "Low Vol".

### GMS Logic Flow

1. **Volatility Check**: `atr_percent <= 0.55` → Always TRUE (since real ATR ~1-5%)
2. **Momentum Check**: If `abs(ma_slope) < 50.0` → Often TRUE
3. **Result**: "Low_Vol_Range" → Maps to **CHOP**

## Evidence from Logs

```
Vol Thresh (Low/High ATR%): 0.5500 / 0.9200
Mom Thresh (Weak/Strong MA Slope): 50.00 / 100.00
```

The detector initialization logs confirm these extreme thresholds are active.

## Hypothesis Confirmed

**CONFIGURATION ISSUE**: The volatility thresholds are misconfigured by a factor of ~100x.

- Current: `0.55` and `0.92` (55% and 92%)
- Should be: `0.0055` and `0.0092` (0.55% and 0.92%) OR
- More typical: `0.02` and `0.06` (2% and 6%)

This explains the 95% CHOP classification - the detector correctly identifies that ATR is always below the impossibly high 55% threshold.

## Recommended Fix

### Option 1: Use Code Defaults (Recommended)
Update `configs/base.yaml` to use the correct defaults from `settings.py`:

```yaml
regime:
  # Current (WRONG)
  gms_vol_high_thresh: 0.92  # 92%!
  gms_vol_low_thresh: 0.55   # 55%!
  gms_mom_strong_thresh: 100.0  # Too high
  gms_mom_weak_thresh: 50.0     # Too high

  # Recommended (CORRECT - matches settings.py defaults)
  gms_vol_high_thresh: 0.06  # 6%
  gms_vol_low_thresh: 0.02   # 2%
  gms_mom_strong_thresh: 5.0 # Reasonable
  gms_mom_weak_thresh: 1.0   # Reasonable
```

### Option 2: Mirror Granular Microstructure Behavior
The `granular_microstructure` detector uses the same thresholds but with **correct defaults** from `settings.py`:

- **Volatility**: `vol_low_thresh: 0.02` (2%), `vol_high_thresh: 0.06` (6%)
- **Momentum**: `mom_weak_thresh: 1.0`, `mom_strong_thresh: 5.0`

Both detectors share the same logic and threshold system - the issue is purely in the configuration values.

## Impact Assessment

- **Before Fix**: 95% CHOP (due to impossible volatility thresholds)
- **After Fix**: Expected ~30-50% CHOP (normal market regime distribution)

This fix should dramatically improve regime detection accuracy and enable proper strategy activation in BULL/BEAR regimes.

## Files Generated

- `artifacts/r112c_state_diagnosis.md` - This report
- `scripts/diagnose_gms_inputs.py` - Diagnostic script (partial run due to date format issue)

## Next Steps

1. Apply the threshold fix in base.yaml
2. Re-run backtest to verify regime distribution
3. Confirm strategy activation in non-CHOP regimes
