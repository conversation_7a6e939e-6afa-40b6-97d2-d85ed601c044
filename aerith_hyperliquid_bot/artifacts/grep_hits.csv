file,line,column,component,context
./tools/etl_l20_to_1s.py,317,atr_14,ETL,ohlc_1h['atr_14'] = tr_series.rolling(window=length min_periods=length).mean()
./tools/etl_l20_to_1s.py,341,atr_14_sec,ETL,atr_columns_to_remove = ['atr_14_sec' 'atr_percent_sec']
./tools/etl_l20_to_1s.py,341,atr_percent_sec,ETL,atr_columns_to_remove = ['atr_14_sec' 'atr_percent_sec']
./tools/etl_l20_to_1s.py,365,atr_14_sec,ETL,atr_reindexed.to_frame('atr_14_sec')
./tools/etl_l20_to_1s.py,373,atr_percent_sec,ETL,df_merged['atr_percent_sec'] = df_merged['atr_14_sec'] / df_merged['mid_price']
./tools/etl_l20_to_1s.py,485,atr_14,ETL,df['atr_14'] = atr_values
./tools/etl_l20_to_1s.py,560,atr,ETL,df['atr'] = atr_values
./tools/etl_l20_to_1s.py,563,atr_percent,ETL,df['atr_percent'] = df['atr'] / df['close']
./tools/etl_l20_to_1s.py,566,atr_percent_sec,ETL,df['atr_percent_sec'] = df['atr_14_sec'] / df['close']
./tools/etl_l20_to_1s.py,210,mid_price,ETL,df['mid_price'] = (df['bid_price_1'] + df['ask_price_1']) / 2
./tools/etl_l20_to_1s.py,228,raw_obi_5,ETL,df[f'raw_obi_{depth}'] = (bid_sum - ask_sum) / (bid_sum + ask_sum + 1e-9)
./tools/etl_l20_to_1s.py,228,raw_obi_20,ETL,df[f'raw_obi_{depth}'] = (bid_sum - ask_sum) / (bid_sum + ask_sum + 1e-9)
./tools/etl_l20_to_1s.py,571,spread_mean,ETL,# Calculate rolling spread statistics (spread_mean and spread_std)
./tools/etl_l20_to_1s.py,571,spread_std,ETL,# Calculate rolling spread statistics (spread_mean and spread_std)
./tools/etl_l20_to_1s.py,581,spread_mean,ETL,df['spread_mean'] = rolling_spread.mean()
./tools/etl_l20_to_1s.py,582,spread_std,ETL,df['spread_std'] = rolling_spread.std()
./hyperliquid_bot/core/detector.py,418,atr_percent,detector,# Volatility
./hyperliquid_bot/core/detector.py,419,ma_slope,detector,# Momentum
./hyperliquid_bot/core/detector.py,420,obi_smoothed_5,detector,# Base OBI Confirmation
./hyperliquid_bot/core/detector.py,420,obi_smoothed_20,detector,# Base OBI Confirmation
./hyperliquid_bot/core/detector.py,421,spread_mean,detector,# Base Spread Context (Range)
./hyperliquid_bot/core/detector.py,422,spread_std,detector,# Base Spread Context (Chop)
./hyperliquid_bot/core/detector.py,489,timestamp,detector,required_now = ['timestamp' 'atr_percent' 'ma_slope' f'obi_smoothed_{self.depth_levels}' 'spread_mean' 'spread_std']
./hyperliquid_bot/core/gms_detector.py,188,atr_14_sec,gms_detector,self.ATR_COL = 'atr_14_sec'  # Default ATR column name
./hyperliquid_bot/core/gms_detector.py,189,atr_percent_sec,gms_detector,self.ATR_PCT_COL = 'atr_percent_sec'  # Default ATR percent column name
./hyperliquid_bot/core/gms_detector.py,303,obi_smoothed_5,gms_detector,f'obi_smoothed_{self.depth_levels}'
./hyperliquid_bot/core/gms_detector.py,303,obi_smoothed_20,gms_detector,f'obi_smoothed_{self.depth_levels}'
./hyperliquid_bot/core/gms_detector.py,304,spread_mean,gms_detector,# Base Spread Context (Range)
./hyperliquid_bot/core/gms_detector.py,305,spread_std,gms_detector,# Base Spread Context (Chop)
./hyperliquid_bot/core/gms_detector.py,310,obi_zscore_5,gms_detector,signals_needed.append(f'obi_zscore_{self.depth_levels}')
./hyperliquid_bot/core/gms_detector.py,310,obi_zscore_20,gms_detector,signals_needed.append(f'obi_zscore_{self.depth_levels}')
./hyperliquid_bot/core/risk.py,97,atr_tf,risk,atr = signals.get("atr_tf")
./hyperliquid_bot/core/risk.py,101,atr_mr,risk,atr = signals.get("atr_mr")
./hyperliquid_bot/core/risk.py,105,atr_mv,risk,atr = signals.get("atr_mv")
./hyperliquid_bot/core/risk.py,110,atr_14_sec,risk,atr = signals.get("atr_14_sec") or signals.get("atr")
./hyperliquid_bot/core/risk.py,110,atr,risk,atr = signals.get("atr_14_sec") or signals.get("atr")
./hyperliquid_bot/strategies/tf_v3.py,397,atr_14_sec,tf_v3,atr_sec = signals.get('atr_14_sec')
./hyperliquid_bot/strategies/evaluator.py,92,atr_tf,evaluator,signals_needed = ["forecast" "tf_ewma_fast" "tf_ewma_slow" "regime" "close" "atr_tf"]
./hyperliquid_bot/strategies/evaluator.py,92,close,evaluator,signals_needed = ["forecast" "tf_ewma_fast" "tf_ewma_slow" "regime" "close" "atr_tf"]
./hyperliquid_bot/strategies/evaluator.py,100,obi_smoothed_5,evaluator,signals_needed.append(f"obi_smoothed_{obi_depth}")
./hyperliquid_bot/strategies/evaluator.py,100,obi_smoothed_20,evaluator,signals_needed.append(f"obi_smoothed_{obi_depth}")
./hyperliquid_bot/strategies/evaluator.py,102,obi_smoothed,evaluator,signals_needed.append("obi_smoothed")
./hyperliquid_bot/strategies/obi_scalper.py,56,obi_smoothed_5,obi_scalper,f"obi_smoothed_{obi_levels}"   # Smoothed OBI
./hyperliquid_bot/strategies/obi_scalper.py,57,obi_zscore_5,obi_scalper,f"obi_zscore_{obi_levels}"     # OBI Z-Score for filtering extremes
./hyperliquid_bot/strategies/obi_scalper_strategy.py,112,raw_obi_L1_3,obi_scalper,"raw_obi_L1_3"       # Raw OBI for levels 1-3
./hyperliquid_bot/strategies/obi_scalper_strategy.py,113,raw_obi_L1_10,obi_scalper,"raw_obi_L1_10"      # Raw OBI for levels 1-10
./hyperliquid_bot/signals/calculator.py,354,atr,signal_calculator,_add_atr(signals_df atr_period "atr")
./hyperliquid_bot/signals/calculator.py,1066,atr_tf,signal_calculator,signals_df["atr_tf"] = signals_df["atr"]
./hyperliquid_bot/signals/calculator.py,1071,atr_tf,signal_calculator,_add_atr(signals_df tf_atr_period "atr_tf")
./hyperliquid_bot/signals/calculator.py,1116,atr_mr,signal_calculator,signals_df["atr_mr"] = signals_df["atr"]
./hyperliquid_bot/signals/calculator.py,1130,atr_mr,signal_calculator,_add_atr(signals_df mr_atr_period "atr_mr")
./hyperliquid_bot/signals/calculator.py,1183,atr_mv,signal_calculator,signals_df["atr_mv"] = signals_df["atr"]
./hyperliquid_bot/signals/calculator.py,1188,atr_mv,signal_calculator,_add_atr(signals_df mv_atr_period "atr_mv")
./hyperliquid_bot/signals/calculator.py,1267,atr_percent_sec,signal_calculator,if 'atr_percent_sec' in signals_df.columns:
./hyperliquid_bot/signals/calculator.py,1271,atr_percent,signal_calculator,signals_df["atr_percent"] = signals_df["atr_percent_sec"] * 100
./hyperliquid_bot/signals/calculator.py,1226,ma_slope,signal_calculator,ma_slope_p = cfg_indicators.gms_ma_slope_period
./hyperliquid_bot/signals/calculator.py,1237,ma_slope,signal_calculator,signals_df["ma_slope"] = signals_df[ma_col] - signals_df[ma_col].shift(1)
./hyperliquid_bot/signals/calculator.py,370,raw_obi_5,signal_calculator,obi_columns = [col for col in signals_df.columns if col.startswith("raw_obi_")]
./hyperliquid_bot/signals/calculator.py,370,raw_obi_20,signal_calculator,obi_columns = [col for col in signals_df.columns if col.startswith("raw_obi_")]
./hyperliquid_bot/signals/calculator.py,380,obi_smoothed_5,signal_calculator,smoothed_col_name = f"obi_smoothed_{suffix}"
./hyperliquid_bot/signals/calculator.py,380,obi_smoothed_20,signal_calculator,smoothed_col_name = f"obi_smoothed_{suffix}"
./hyperliquid_bot/signals/calculator.py,381,obi_zscore_5,signal_calculator,zscore_col_name = f"obi_zscore_{suffix}"
./hyperliquid_bot/signals/calculator.py,381,obi_zscore_20,signal_calculator,zscore_col_name = f"obi_zscore_{suffix}"
./hyperliquid_bot/signals/calculator.py,438,spread_mean,signal_calculator,signals_df["spread_mean"] = rolling_spread.mean()
./hyperliquid_bot/signals/calculator.py,439,spread_std,signal_calculator,signals_df["spread_std"] = rolling_spread.std()
./hyperliquid_bot/data/feature_store.py,121,timestamp,feature_store,"timestamp" "mid_price" "best_bid" "best_ask" "spread" "spread_relative"
./hyperliquid_bot/data/feature_store.py,121,mid_price,feature_store,"timestamp" "mid_price" "best_bid" "best_ask" "spread" "spread_relative"
./hyperliquid_bot/data/feature_store.py,121,best_bid,feature_store,"timestamp" "mid_price" "best_bid" "best_ask" "spread" "spread_relative"
./hyperliquid_bot/data/feature_store.py,121,best_ask,feature_store,"timestamp" "mid_price" "best_bid" "best_ask" "spread" "spread_relative"
./hyperliquid_bot/data/feature_store.py,121,spread,feature_store,"timestamp" "mid_price" "best_bid" "best_ask" "spread" "spread_relative"
./hyperliquid_bot/data/feature_store.py,121,realised_vol_1s,feature_store,"realised_vol_1s" "atr" "atr_percent" "atr_14_sec" "atr_percent_sec" "unrealised_pnl"
./hyperliquid_bot/data/feature_store.py,121,atr,feature_store,"realised_vol_1s" "atr" "atr_percent" "atr_14_sec" "atr_percent_sec" "unrealised_pnl"
./hyperliquid_bot/data/feature_store.py,121,atr_percent,feature_store,"realised_vol_1s" "atr" "atr_percent" "atr_14_sec" "atr_percent_sec" "unrealised_pnl"
./hyperliquid_bot/data/feature_store.py,121,atr_14_sec,feature_store,"realised_vol_1s" "atr" "atr_percent" "atr_14_sec" "atr_percent_sec" "unrealised_pnl"
./hyperliquid_bot/data/feature_store.py,121,atr_percent_sec,feature_store,"realised_vol_1s" "atr" "atr_percent" "atr_14_sec" "atr_percent_sec" "unrealised_pnl"
./hyperliquid_bot/data/feature_store.py,121,unrealised_pnl,feature_store,"realised_vol_1s" "atr" "atr_percent" "atr_14_sec" "atr_percent_sec" "unrealised_pnl"
./hyperliquid_bot/data/handler.py,675,timestamp,data_handler,feature_df = feature_store.load_1s(date_str columns=["timestamp" "atr_14_sec" "atr_percent_sec" "atr" "atr_percent"])
./hyperliquid_bot/data/handler.py,491,raw_obi_5,data_handler,raw_obi = microstructure.calculate_order_book_imbalance(l2_snapshot levels=depth_levels)
./hyperliquid_bot/data/handler.py,491,raw_obi_20,data_handler,raw_obi = microstructure.calculate_order_book_imbalance(l2_snapshot levels=depth_levels)
./hyperliquid_bot/data/handler.py,499,raw_obi_5,data_handler,f'raw_obi_{depth_levels}': raw_obi
./hyperliquid_bot/data/handler.py,499,raw_obi_20,data_handler,f'raw_obi_{depth_levels}': raw_obi
