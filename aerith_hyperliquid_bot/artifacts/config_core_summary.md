# Config.core Audit Summary - Task R-110a

## Overview
Comprehensive scan of the codebase for stale `config.core` and `cfg.core` references that need to be migrated to the new configuration schema.

## Total Hits: 28 occurrences

### Breakdown by <PERSON><PERSON><PERSON>

| Module | Hit Count | Status |
|--------|-----------|--------|
| **Backup Files (.bak, .bak2)** | 13 | Non-critical (backup files) |
| **Test Files** | 14 | **Critical** - Active test files |
| **Core Runtime** | 1 | **Critical** - Active runtime code |
| **Binary Files** | 1 | Needs source investigation |

### Critical Files Requiring Immediate Attention

#### 1. Test Files (14 hits) - **HIGH PRIORITY**
- `tests/test_continuous_gms.py` (4 hits)
- `tests/test_integration_continuous_gms.py` (3 hits)
- `tests/test_runtime_tz.py` (3 hits)
- `tests/test_portfolio_margin.py` (2 hits)
- `tests/test_portfolio.py` (1 hit - commented)

**Impact**: These test files will fail when executed because `config.core` no longer exists in the schema.

#### 2. Core Runtime Files (1 hit) - **MEDIUM PRIORITY**
- `hyperliquid_bot/core/risk.py` (1 hit - commented out)

**Impact**: Currently commented out, but represents potential runtime issue if uncommented.

#### 3. Binary Files (1 hit) - **RESOLVED**
- `hyperliquid_bot/backtester/__pycache__/run_backtest.cpython-311.pyc`

**Impact**: Investigated source file `run_backtest.py` - no config.core references found. Binary match is false positive from compiled bytecode.

### Configuration Migration Pattern

All identified references follow the pattern:
- `cfg.core.max_leverage` → `cfg.portfolio.max_leverage`
- `cfg.core.initial_balance` → `cfg.portfolio.initial_balance`
- `cfg.core.risk_per_trade` → `cfg.portfolio.risk_per_trade`

### Backup Files Analysis (13 hits) - **LOW PRIORITY**
- `hyperliquid_bot/core/risk.py.bak` (8 hits)
- `hyperliquid_bot/core/risk.py.bak2` (5 hits)

These are backup files and don't affect runtime, but should be cleaned up for consistency.

### Obvious Runtime Issues

**Current Broken Paths**: All test files using `config.core` will raise `AttributeError` when executed, as the `core` section no longer exists in the configuration schema.

**Risk Assessment**:
- **HIGH**: Test suite failures preventing CI/CD
- **MEDIUM**: Potential runtime errors if commented code is activated
- **LOW**: Backup file inconsistency

## Recommendations for R-110b

1. **Priority 1**: Fix all test files (14 hits)
2. **Priority 2**: Clean up commented runtime code (1 hit)
3. **Priority 3**: Remove or update backup files (13 hits)
4. **Priority 4**: Clean up compiled bytecode (optional)

## Files Generated
- `artifacts/config_core_hits.txt` - Raw grep output
- `artifacts/config_core_analysis.csv` - Detailed categorization
- `artifacts/config_core_summary.md` - This summary report
