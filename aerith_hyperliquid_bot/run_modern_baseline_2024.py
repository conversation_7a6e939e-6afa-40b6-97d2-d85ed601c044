#!/usr/bin/env python3
"""
Run Modern System Baseline - Full 2024
======================================
This establishes a stable baseline for the modern system after fixing data loading issues.
"""

import subprocess
import sys
from pathlib import Path
from datetime import datetime
import json

def main():
    print("=" * 80)
    print("MODERN SYSTEM BASELINE - FULL 2024")
    print("=" * 80)
    print()
    print("Changes implemented:")
    print("1. Dynamic warmup period calculation (89 hours)")
    print("2. Graceful handling of missing data")
    print("3. Fixed threshold (spread_mean_low_thresh: 0.0008)")
    print("4. Proper timing - no trading during warmup")
    print()
    
    # Create log directory
    log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    log_dir.mkdir(exist_ok=True)
    
    # Timestamp for files
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Run command
    cmd = [
        sys.executable,
        "scripts/run_modern_backtest.py",
        "--start-date", "2024-01-01",
        "--end-date", "2024-12-31",
        "--override", "configs/overrides/modern_system_v2_complete.yaml"
    ]
    
    log_file = log_dir / f"modern_baseline_2024_{timestamp}.log"
    
    print(f"Starting backtest...")
    print(f"Log file: {log_file}")
    print("-" * 80)
    
    start_time = datetime.now()
    
    # Run with output capture
    with open(log_file, 'w') as f:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        trade_count = 0
        for line in process.stdout:
            f.write(line)
            f.flush()
            
            # Show progress
            if "warmup period:" in line:
                print(line.rstrip())
            elif "Trading will begin" in line:
                print(line.rstrip())
            elif "Trade #" in line:
                trade_count += 1
                if trade_count % 50 == 0:
                    print(f"Progress: {trade_count} trades...")
            elif "Backtest Complete" in line:
                print(line.rstrip())
            elif "Total Trades:" in line:
                print(line.rstrip())
            elif "Total Return:" in line:
                print(line.rstrip())
            elif "ERROR" in line:
                print(line.rstrip())
        
        process.wait()
    
    elapsed = (datetime.now() - start_time).total_seconds()
    
    if process.returncode != 0:
        print(f"\nBacktest failed! Check {log_file}")
        return 1
    
    print(f"\nBacktest completed in {elapsed:.1f} seconds")
    
    # Find the output file
    import glob
    output_files = sorted(
        glob.glob(str(log_dir / "modern_backtest_*.json")),
        key=lambda x: Path(x).stat().st_mtime,
        reverse=True
    )
    
    if output_files:
        latest_output = output_files[0]
        print(f"\n📊 Results file: {latest_output}")
        
        # Load and display results
        with open(latest_output) as f:
            results = json.load(f)
        
        perf = results['performance']
        print(f"\n{'=' * 80}")
        print("RESULTS - MODERN SYSTEM BASELINE 2024")
        print("=" * 80)
        print(f"\nPerformance:")
        print(f"  Total Trades: {perf.get('total_trades', 0)}")
        print(f"  Total Return: {perf.get('total_return', 0):.2%}")
        print(f"  Win Rate: {perf.get('win_rate', 0):.2%}")
        print(f"  Execution Stats: {perf.get('execution_stats', {})}")
        
        print(f"\nComparison:")
        print(f"  Legacy System: 189 trades, +215.0% ROI")
        print(f"  Modern System: {perf.get('total_trades', 0)} trades, {perf.get('total_return', 0):.1%} ROI")
        
        # Save baseline analysis
        analysis = {
            "test": "modern_baseline_2024_fixed",
            "timestamp": timestamp,
            "fixes_applied": [
                "Dynamic warmup period calculation",
                "Graceful data loading with fallbacks",
                "Fixed spread_mean_low_thresh: 0.0008",
                "Skip trading during warmup period"
            ],
            "warmup_hours": 89,
            "performance": perf,
            "comparison": {
                "legacy_trades": 189,
                "legacy_roi": 2.15,
                "modern_trades": perf.get('total_trades', 0),
                "modern_roi": perf.get('total_return', 0)
            }
        }
        
        analysis_file = log_dir / f"modern_baseline_analysis_{timestamp}.json"
        with open(analysis_file, 'w') as f:
            json.dump(analysis, f, indent=2)
        
        print(f"\n📄 Analysis saved to: {analysis_file}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())