#!/usr/bin/env python3
"""Debug regime detection to understand why no tradeable regimes are detected."""

import json
import numpy as np

# Load results
with open('modern_backtest_results.json') as f:
    data = json.load(f)

print("=== REGIME DETECTION DEBUG ===\n")

# Analyze momentum values against thresholds
momentum_values = [r['momentum'] for r in data['regime_history']]
abs_momentum = [abs(m) for m in momentum_values]

# Current thresholds from config
mom_weak_thresh = 0.0005
mom_strong_thresh = 0.002

print(f"Momentum Threshold Analysis:")
print(f"  Weak threshold: {mom_weak_thresh}")
print(f"  Strong threshold: {mom_strong_thresh}")
print(f"\nMomentum statistics:")
print(f"  Min: {min(momentum_values):.6f}")
print(f"  Max: {max(momentum_values):.6f}")
print(f"  Mean: {np.mean(momentum_values):.6f}")
print(f"\nAbsolute momentum statistics:")
print(f"  Min: {min(abs_momentum):.6f}")
print(f"  Max: {max(abs_momentum):.6f}")
print(f"  Mean: {np.mean(abs_momentum):.6f}")
print(f"  Median: {np.median(abs_momentum):.6f}")

# Check how many pass thresholds
weak_count = sum(1 for m in abs_momentum if m >= mom_weak_thresh)
strong_count = sum(1 for m in abs_momentum if m >= mom_strong_thresh)

print(f"\nThreshold results:")
print(f"  Hours with momentum >= weak ({mom_weak_thresh}): {weak_count}/{len(abs_momentum)} ({weak_count/len(abs_momentum)*100:.1f}%)")
print(f"  Hours with momentum >= strong ({mom_strong_thresh}): {strong_count}/{len(abs_momentum)} ({strong_count/len(abs_momentum)*100:.1f}%)")

# Analyze volatility
volatility_values = [r['volatility'] for r in data['regime_history']]
vol_low_thresh = 0.006
vol_high_thresh = 0.009

print(f"\n\nVolatility Threshold Analysis:")
print(f"  Low threshold: {vol_low_thresh}")
print(f"  High threshold: {vol_high_thresh}")
print(f"\nVolatility statistics:")
print(f"  Min: {min(volatility_values):.6f}")
print(f"  Max: {max(volatility_values):.6f}")
print(f"  Mean: {np.mean(volatility_values):.6f}")

low_vol_count = sum(1 for v in volatility_values if v <= vol_low_thresh)
high_vol_count = sum(1 for v in volatility_values if v >= vol_high_thresh)
normal_vol_count = len(volatility_values) - low_vol_count - high_vol_count

print(f"\nVolatility distribution:")
print(f"  Low volatility (<= {vol_low_thresh}): {low_vol_count} hours ({low_vol_count/len(volatility_values)*100:.1f}%)")
print(f"  Normal volatility: {normal_vol_count} hours ({normal_vol_count/len(volatility_values)*100:.1f}%)")
print(f"  High volatility (>= {vol_high_thresh}): {high_vol_count} hours ({high_vol_count/len(volatility_values)*100:.1f}%)")

# Check OBI values
obi_values = [r['volume_imbalance'] for r in data['regime_history']]
abs_obi = [abs(o) for o in obi_values]
obi_weak_thresh = 0.36  # From config
obi_strong_thresh = 0.52

print(f"\n\nOBI/Volume Imbalance Analysis:")
print(f"  Weak confirmation threshold: {obi_weak_thresh}")
print(f"  Strong confirmation threshold: {obi_strong_thresh}")
print(f"\nOBI statistics:")
print(f"  Min: {min(obi_values):.3f}")
print(f"  Max: {max(obi_values):.3f}")
print(f"  Mean abs: {np.mean(abs_obi):.3f}")

obi_weak_count = sum(1 for o in abs_obi if o >= obi_weak_thresh)
obi_strong_count = sum(1 for o in abs_obi if o >= obi_strong_thresh)

print(f"\nOBI confirmation results:")
print(f"  Hours with |OBI| >= weak ({obi_weak_thresh}): {obi_weak_count}/{len(abs_obi)} ({obi_weak_count/len(abs_obi)*100:.1f}%)")
print(f"  Hours with |OBI| >= strong ({obi_strong_thresh}): {obi_strong_count}/{len(abs_obi)} ({obi_strong_count/len(abs_obi)*100:.1f}%)")

# Simulate regime detection logic
print(f"\n\n=== SIMULATED REGIME DETECTION ===")
tradeable_hours = 0
for i, hour in enumerate(data['regime_history']):
    mom = abs(hour['momentum'])
    vol = hour['volatility']
    obi = abs(hour['volume_imbalance'])
    
    # Check if this would be a tradeable regime
    has_momentum = mom >= mom_weak_thresh
    has_obi_confirm = obi >= obi_weak_thresh
    
    if has_momentum:
        tradeable_hours += 1
        if i < 5:  # Show first 5 potential trades
            direction = "BULL" if hour['momentum'] > 0 else "BEAR"
            strength = "STRONG" if mom >= mom_strong_thresh else "WEAK"
            print(f"\nHour {i+1} ({hour['timestamp']}) - Potential {strength} {direction}:")
            print(f"  Momentum: {hour['momentum']:.6f} (abs: {mom:.6f})")
            print(f"  OBI: {hour['volume_imbalance']:.3f} (abs: {obi:.3f})")
            print(f"  OBI aligned: {'YES' if has_obi_confirm else 'NO'}")

print(f"\n\nTotal potentially tradeable hours: {tradeable_hours}/{len(data['regime_history'])} ({tradeable_hours/len(data['regime_history'])*100:.1f}%)")

# Recommendations
print(f"\n\n=== RECOMMENDATIONS ===")
print(f"Current thresholds are preventing trades because:")
if weak_count < 5:
    print(f"1. Momentum threshold ({mom_weak_thresh}) is too high - only {weak_count} hours qualify")
    suggested_weak = np.percentile(abs_momentum, 30)  # 30th percentile
    suggested_strong = np.percentile(abs_momentum, 70)  # 70th percentile
    print(f"   Suggested: weak={suggested_weak:.6f}, strong={suggested_strong:.6f}")

if obi_weak_count < 10:
    print(f"2. OBI confirmation thresholds are too high - only {obi_weak_count} hours have strong enough OBI")
    suggested_obi_weak = np.percentile(abs_obi, 50)
    suggested_obi_strong = np.percentile(abs_obi, 75)
    print(f"   Suggested: weak={suggested_obi_weak:.3f}, strong={suggested_obi_strong:.3f}")