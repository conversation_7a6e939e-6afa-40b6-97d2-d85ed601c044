# ==============================================================================
# Data & Cache Configuration
# ==============================================================================
data_paths:
  l2_data_root: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw2" # Contains YYYYMMDD_raw2.parquet
  raw_l2_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/l2_raw" # Raw L2 data directory
  feature_1s_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s" # 1-second feature data
  ohlcv_base_path: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled_l2" # Base path for OHLC bars
  log_dir: "/Users/<USER>/Desktop/trading_bot_/logs"
  require_ohlcv_volume: false # Set to false for now
data_providers:
  fear_greed:
    enabled: false # Set to false for testing

cache:
  l2_cache_max_size: 24 # Max daily L2 files to keep in memory (if applicable)

# ==============================================================================
# Timeframe Setting
# ==============================================================================
timeframe: "1h" # Or "4h" - Set the desired timeframe for the run

# ==============================================================================
# Backtest & Simulation Configuration
# ==============================================================================
backtest:
  period_preset: '2024'              # Options: 'full', 'YYYYQX', 'YYYY', 'custom'
  custom_start_date: "2025-05-25"    # Using available data
  custom_end_date: "2025-05-25"      # Using available data

simulation:
  latency_seconds: 0.5               # Simulated order latency
  max_impact_levels: 5               # L2 levels assumed fillable without extra penalty
  force_taker_execution: True        # Set to False to enable maker attempt
  attempt_maker_orders: False        # Set to True to attempt maker fills
  maker_placement_type: 'best_passive'
  maker_time_buckets_seconds: [5, 30, 120]
  maker_fill_probabilities: [0.07, 0.13, 0.10]

# ==============================================================================
# Portfolio Configuration
# ==============================================================================
portfolio:
  initial_balance: 10000       # Initial balance for backtests (in USD)
  risk_per_trade: 0.02         # Risk % of balance per trade (e.g., 0.01 = 1%)
  max_leverage: 10.0           # Global maximum leverage cap
  asset_max_leverage: 50.0     # Asset-specific max leverage (e.g., for BTC on Hyperliquid)
  margin_mode: cross           # Margin mode: 'cross' or 'isolated'
  max_hold_time_hours: 24      # Maximum time to hold a position in hours
  max_notional: 0              # 0 = no explicit ceiling
  min_trade_size: 0.001        # Minimum trade size in BTC
  leverage_guard_pct: 0        # 0 = disabled
  margin_buffer_pct: 0.05      # Margin buffer percentage

# ==============================================================================
# Costs Configuration
# ==============================================================================
costs:
  funding_rate: 0.0001        # Default funding rate (per period)
  funding_hours: 8            # Funding interval
  taker_fee: 0.000315         # Taker fee rate
  maker_fee: 0.00009          # Maker fee rate
  l2_penalty_factor: 1.005    # Slippage penalty factor

# ==============================================================================
# Strategy Selection & Configuration
# ==============================================================================
strategies:
  use_tf_v2: True
  use_mean_reversion: False
  use_mean_variance: False
  use_obi_scalper: True       # OBI Scalper strategy activation flag

  tf_use_obi_filter: False
  tf_use_funding_filter: False

  obi_scalper_active_in_all_regimes: False
  gms_activate_obi_scalper_in_chop: True

  scalper_risk_pct: 0.5
  scalper_stop_ticks: 5

  OBIScalperStrategy:
    enabled: true
    defaults:
      vol_veto_threshold:      0.002
      spread_veto_threshold:   0.0001
      obi_l1_3_trigger:        0.50
      tp_ticks:                7
      sl_ticks:                5
      timeout_seconds:         30
      allowed_gms_states:      ["CHOP", "BULL", "BEAR"]
      zero_sign_eps:           0.001

# ==============================================================================
# Market Regime Detection Configuration
# ==============================================================================
regime:
  use_filter: True
  detector_type: 'granular_microstructure'
  use_strict_strategy_filtering: True

  use_enhanced_detection: True
  use_chop_filter: True
  use_chop_index_for_chop: False
  use_bbw_for_chop_detection: False
  pause_in_chop: False

  hurst_lookback_periods: 100
  hurst_trending_threshold: 0.55
  hurst_ranging_threshold: 0.45
  hurst_min_series_length: 50

  gms_use_adx_confirmation: False
  gms_use_funding_confirmation: False

  gms_vol_thresh_mode: 'fixed'
  gms_vol_high_thresh: 0.92
  gms_vol_low_thresh: 0.55
  gms_vol_high_percentile: 75
  gms_vol_low_percentile: 25

  gms_mom_strong_thresh: 2.5
  gms_mom_weak_thresh: 0.5

  gms_spread_std_high_thresh: 0.000050
  gms_spread_mean_low_thresh: 0.000045

  gms_funding_extreme_positive_thresh: 0.001
  gms_funding_extreme_negative_thresh: -0.001

  gms_filter_allow_weak_bull_trend: True
  gms_filter_allow_weak_bear_trend: True
  gms_weak_bull_risk_scale: 0.25
  gms_weak_bear_risk_scale: 0.25

  gms_obi_zscore_threshold: null
  gms_spread_percentile_gate: null
  gms_depth_slope_thin_limit: null
  gms_depth_skew_thresh: null
  gms_spread_trend_lookback: null
  gms_adaptive_obi_base: null
  gms_confirmation_bars: null

  gms_tight_spread_fallback_percentile: null
  gms_tight_spread_percentile_window: 24
  gms_spread_mean_thresh_mode: 'fixed'
  gms_spread_std_thresh_mode: 'fixed'
  gms_spread_mean_low_percentile: 0.25
  gms_spread_std_high_percentile: 0.75
  gms_spread_percentile_window: null

  gms_use_three_state_mapping: True
  gms_state_mapping_file: 'configs/gms_state_mapping.yaml'
  map_weak_bear_to_bear: false

  dynamic_risk_adjustment: false

  chop_risk_factor: 0.5
  chop_leverage_factor: 0.5
  strong_trend_risk_factor: 0.8
  strong_trend_leverage_factor: 0.7
  weak_trend_risk_scale: 0.8

# ==============================================================================
# Continuous GMS Detector Configuration
# ==============================================================================
gms:
  detector_type: 'continuous_gms'
  cadence_sec: 60
  output_states: 8
  state_collapse_map_file: 'configs/gms_state_mapping.yaml'
  use_four_state_mapping: false
  risk_suppressed_notional_frac: 0.25
  risk_suppressed_pnl_atr_mult: 1.5

  market_bias:
    enabled: false
    use_three_state_mapping: true
    bull_leverage_factor: 1.0
    bear_leverage_factor: 1.0
    chop_leverage_factor: 1.0
    bull_risk_factor: 1.2
    bear_risk_factor: 0.8
    chop_risk_factor: 0.5
    bull_long_bias: 1.0
    bull_short_bias: 1.0
    bear_long_bias: 1.0
    bear_short_bias: 1.0

# ==============================================================================
# Microstructure Feature Calculation & Filter Settings
# ==============================================================================
microstructure:
  depth_levels: 5
  obi_levels: 5  # Must match depth_levels or be null
  obi_smoothing_window: 8
  obi_smoothing_type: 'sma'
  obi_zscore_window: null
  depth_levels_for_calc: 5  # Must match depth_levels or be null
  spread_rolling_window: 24
  spread_metric_to_roll: 'relative'

  gms_obi_strong_confirm_thresh: 0.20
  gms_obi_weak_confirm_thresh: 0.11

  tf_filter_obi_threshold_long: 0.1
  tf_filter_obi_threshold_short: -0.1
  tf_filter_funding_threshold_long: -0.0005
  tf_filter_funding_threshold_short: 0.0005

# ==============================================================================
# Indicator Parameters
# ==============================================================================
indicators:
  require_volume_for_signals: false

  adx_period: 14
  adx_threshold: 30.0
  high_volatility_adx_threshold: 30.0
  low_forecast_threshold: 1.0
  volatility_thresh_percent: 0.005
  bbw_thresh: 0.03
  chop_index_period: 14
  chop_index_high_thresh: 61.8
  chop_index_low_thresh: 38.2
  min_leverage: 1.0

  tf_ewma_fast: 20
  tf_ewma_medium: 32
  use_tf_medium_ewma: False
  tf_ewma_slow: 50
  tf_atr_period: 20
  tf_atr_stop_mult: 2.0
  tf_atr_target_mult: 4.0
  tf_leverage_base: 5.0
  tf_max_entry_volatility_pct: 0.01

  mr_ema_period: 20
  mr_keltner_mult: 2.0
  mr_rsi_period: 14
  mr_rsi_oversold: 30.0
  mr_rsi_overbought: 70.0
  mr_atr_period: 20
  mr_atr_stop_mult: 1.5
  mr_leverage_base: 5.0
  mr_obi_long_threshold: -0.2
  mr_obi_short_threshold: 0.2
  mr_require_obi_filter: True

  mv_edge_ema_period: 10
  mv_volatility_period: 20
  mv_min_edge_threshold: 0.0005
  mv_min_exit_edge_threshold: 0.0
  mv_min_kelly: 0.05
  mv_max_kelly: 0.25
  mv_leverage_base: 3.0
  mv_atr_stop_mult: 2.5
  mv_atr_target_mult: 3.0
  mv_atr_period: 20
  mv_obi_long_threshold: -0.3
  mv_obi_short_threshold: 0.3
  mv_require_obi_filter: True

  gms_roc_period: 5
  gms_ma_slope_period: 30
  gms_atr_percent_period: 14

# ==============================================================================
# Analysis Configuration
# ==============================================================================
analysis:
  analyze_trades_after_backtest: True

# ==============================================================================
# ETL Pipeline Configuration
# ==============================================================================
etl:
  l20_to_1s:
    chunk_sec: 3600
    rollup_method: "median"

# ==============================================================================
# Scheduler Configuration
# ==============================================================================
scheduler:
  etl_enabled: true
  etl_poll_sec: 300

# ==============================================================================
# Visualization Configuration
# ==============================================================================
visualization:
  require_volume: false
  panels:
    show_ma_slope: true
    show_obi: true
    show_fear_greed: true
  appearance:
    price_panel_ratio: 4
    indicator_panel_ratio: 1
    max_points: 5000
