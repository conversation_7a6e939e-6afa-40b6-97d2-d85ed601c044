# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Python virtual environments
venv/
env/
.env/
.venv/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Local development settings
.env
.env.local

# Cache files
.cache/

# Mac OS specific
.DS_Store

# Jupyter Notebook
.ipynb_checkpoints

# Custom for this project
hyperliquid_data/

logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/ 
