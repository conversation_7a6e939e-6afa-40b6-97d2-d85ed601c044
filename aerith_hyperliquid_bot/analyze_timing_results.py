#!/usr/bin/env python3
"""
Analyze the detailed timing results from the 2024 backtest
"""

import pstats
import io
import json
from datetime import datetime, timed<PERSON><PERSON>

def analyze_timing_results():
    """Analyze the profiling results and create detailed timing breakdown."""
    
    print("=== DETAILED TIMING ANALYSIS: 2024 FULL BACKTEST RESULTS ===")
    
    # Load the profile data
    profile_file = "detailed_timing_2024_profile.prof"
    stats = pstats.Stats(profile_file)
    
    # Calculate total execution time from log timestamps
    log_start = "2025-05-29 02:49:23"
    log_end = "2025-05-29 03:04:10"
    
    start_time = datetime.strptime(log_start, "%Y-%m-%d %H:%M:%S")
    end_time = datetime.strptime(log_end, "%Y-%m-%d %H:%M:%S")
    total_time = (end_time - start_time).total_seconds()
    
    print(f"Total Execution Time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")
    print(f"Start: {log_start}")
    print(f"End: {log_end}")
    print()
    
    # Define operation categories and their patterns
    operation_categories = {
        "Time Conversion": [
            "to_utc_naive",
            "tz_convert",
            "tz_localize", 
            "timezone",
            "apply.*time"
        ],
        "Data Loading - OHLCV": [
            "_load_ohlcv",
            "read_parquet.*ohlcv",
            "load_historical_data"
        ],
        "Data Loading - L2": [
            "_load_l2_segment",
            "_integrate_microstructure",
            "read_parquet.*l2",
            "raw2"
        ],
        "Feature Calculation": [
            "_calculate_features_from_row",
            "calculate_order_book_imbalance",
            "calculate_bid_ask_spread",
            "calculate_depth_metrics",
            "microstructure"
        ],
        "Technical Analysis": [
            "pandas_ta",
            "talib",
            "ema",
            "atr",
            "sma",
            "SignalCalculator"
        ],
        "Regime Detection": [
            "GranularMicrostructure",
            "detect_regime",
            "RegimeDetector",
            "_classify_regime"
        ],
        "Strategy Evaluation": [
            "TrendFollowing",
            "evaluate_strategy",
            "StrategyEvaluator",
            "_generate_signal"
        ],
        "Portfolio Management": [
            "Portfolio",
            "RiskManager",
            "calculate_position",
            "_update_portfolio"
        ],
        "Execution Simulation": [
            "ExecutionSimulator",
            "_simulate_execution",
            "_process_order"
        ]
    }
    
    # Analyze each category
    category_times = {}
    
    for category, patterns in operation_categories.items():
        category_time = get_category_time(stats, patterns)
        category_times[category] = category_time
        percentage = (category_time / total_time) * 100
        print(f"{category:25}: {category_time:8.1f}s ({percentage:5.1f}%)")
    
    print()
    print("=== TOP 15 INDIVIDUAL FUNCTIONS BY CUMULATIVE TIME ===")
    s = io.StringIO()
    ps = pstats.Stats(stats.stats, stream=s)
    ps.sort_stats('cumulative')
    ps.print_stats(15)
    output = s.getvalue()
    print(output)
    
    print("=== OPTIMIZATION PRIORITIES ===")
    sorted_categories = sorted(category_times.items(), key=lambda x: x[1], reverse=True)
    
    for i, (category, time_spent) in enumerate(sorted_categories[:5], 1):
        percentage = (time_spent / total_time) * 100
        print(f"Priority {i}: {category}")
        print(f"  Time: {time_spent:.1f}s ({percentage:.1f}% of total)")
        print(f"  Potential savings: {estimate_savings(category, time_spent)}")
        print()
    
    # Generate summary report
    report = {
        "timestamp": datetime.now().isoformat(),
        "backtest_period": "2024 (full year)",
        "total_execution_time_seconds": total_time,
        "total_execution_time_minutes": total_time / 60,
        "category_breakdown": {
            category: {
                "time_seconds": time_spent,
                "percentage": (time_spent / total_time) * 100
            }
            for category, time_spent in category_times.items()
        },
        "optimization_priorities": [
            {
                "rank": i,
                "category": category,
                "time_seconds": time_spent,
                "percentage": (time_spent / total_time) * 100,
                "potential_savings": estimate_savings(category, time_spent)
            }
            for i, (category, time_spent) in enumerate(sorted_categories[:5], 1)
        ]
    }
    
    # Save report
    with open("detailed_timing_2024_report.json", 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"Detailed report saved to: detailed_timing_2024_report.json")

def get_category_time(stats, patterns):
    """Get total time for functions matching any pattern in the category."""
    total_time = 0.0
    
    for func_key, func_stats in stats.stats.items():
        filename, line_num, func_name = func_key
        full_func_path = f"{filename}:{line_num}({func_name})"
        
        # Check if this function matches any pattern in the category
        for pattern in patterns:
            if pattern.lower() in full_func_path.lower() or pattern.lower() in func_name.lower():
                # func_stats = (cc, nc, tt, ct, callers)
                # tt = total time, ct = cumulative time
                total_time += func_stats[2]  # Add total time
                break
    
    return total_time

def estimate_savings(category, time_spent):
    """Estimate potential time savings for each category."""
    savings_estimates = {
        "Time Conversion": (0.90, "Replace apply() with vectorized operations"),
        "Data Loading - L2": (0.70, "Implement caching and memory mapping"),
        "Data Loading - OHLCV": (0.50, "Optimize file reading and preprocessing"),
        "Feature Calculation": (0.60, "Vectorize calculations and cache results"),
        "Technical Analysis": (0.40, "Cache indicators and use faster libraries"),
        "Regime Detection": (0.30, "Optimize threshold calculations"),
        "Strategy Evaluation": (0.20, "Streamline signal generation"),
        "Portfolio Management": (0.15, "Optimize position calculations"),
        "Execution Simulation": (0.10, "Minor optimizations possible")
    }
    
    if category in savings_estimates:
        reduction_pct, description = savings_estimates[category]
        potential_savings = time_spent * reduction_pct
        return f"{potential_savings:.1f}s ({reduction_pct*100:.0f}% reduction) - {description}"
    else:
        return "Unknown optimization potential"

if __name__ == "__main__":
    analyze_timing_results()