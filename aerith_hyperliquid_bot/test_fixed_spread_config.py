#!/usr/bin/env python3
"""
Test the modern system with fixed spread thresholds and reduced quality threshold.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import logging
from datetime import datetime

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtest_engine import RobustBacktestEngine

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """Run test with fixed spread thresholds."""
    print("\n" + "="*80)
    print("TEST: MODERN SYSTEM WITH FIXED SPREAD & QUALITY THRESHOLDS")
    print("="*80)
    
    # Load config with fixed thresholds
    config_path = Path(__file__).parent / "configs/overrides/modern_system_v2_adjusted_thresholds.yaml"
    config = load_config(str(config_path))
    
    # Show configuration
    print("\nConfiguration:")
    print(f"  Detector type: {config.regime.detector_type}")
    enhanced_settings = config.regime.get_detector_settings('enhanced')
    print(f"  Quality threshold: {enhanced_settings.get('quality_threshold', 0.7)}")
    print(f"  Spread thresholds: {config.regime.gms_spread_mean_low_thresh} / {config.regime.gms_spread_std_high_thresh}")
    print(f"  Momentum thresholds: {config.regime.gms_mom_weak_thresh} / {config.regime.gms_mom_strong_thresh}")
    
    # Test period - one week for quick test
    start_date = datetime(2024, 2, 1)
    end_date = datetime(2024, 2, 7, 23, 59, 59)
    
    print(f"\nTest Period: {start_date.date()} to {end_date.date()}")
    print("-" * 80)
    
    try:
        # Create engine
        engine = RobustBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date,
            use_regime_cache=False,
            strict=False
        )
        
        print("\n✅ Engine initialized successfully")
        
        # Run the backtest
        print("\nRunning backtest...")
        results = engine.run_backtest()
        
        print("\n✅ Backtest completed")
        
        # Analyze results
        print("\n" + "="*80)
        print("RESULTS:")
        print("="*80)
        
        print(f"\nTrade Statistics:")
        print(f"  Total Trades: {results.get('total_trades', 0)}")
        print(f"  Winning Trades: {results.get('winning_trades', 0)}")
        print(f"  Losing Trades: {results.get('losing_trades', 0)}")
        print(f"  Win Rate: {results.get('win_rate', 0):.1%}")
        
        print(f"\nPerformance:")
        print(f"  Total Return: {results.get('total_return', 0):.2%}")
        print(f"  Average Return per Trade: {results.get('average_return', 0):.2%}")
        print(f"  Sharpe Ratio: {results.get('sharpe_ratio', 0):.2f}")
        print(f"  Max Drawdown: {results.get('max_drawdown', 0):.2%}")
        
        # Summary
        print("\n" + "="*80)
        print("SUMMARY:")
        print("="*80)
        
        if results.get('total_trades', 0) == 0:
            print("❌ Still no trades generated")
            print("   Need to investigate further:")
            print("   - Check if regimes are being detected properly")
            print("   - Verify strategy entry logic")
            print("   - Check position management")
        else:
            print(f"✅ Generated {results.get('total_trades', 0)} trades!")
            print(f"✅ Total return: {results.get('total_return', 0):.2%}")
            
            if results.get('total_return', 0) > 0:
                print("✅ System is profitable with fixed thresholds!")
            else:
                print("⚠️ System generates trades but needs optimization")
        
    except Exception as e:
        print(f"\n❌ ERROR: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()