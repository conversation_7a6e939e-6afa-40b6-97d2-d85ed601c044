#!/usr/bin/env python3
"""
Run Modern System 2024 - With Fixed Thresholds and Regenerated Cache
"""

import subprocess
import sys
import json
from pathlib import Path
from datetime import datetime

def main():
    print("=" * 80)
    print("MODERN SYSTEM - FIXED THRESHOLDS TEST")
    print("=" * 80)
    print()
    print("Changes Made:")
    print("1. Fixed spread_mean_low_thresh: 1.2 → 0.0008 (1500x error)")
    print("2. Regenerated regime cache with corrected thresholds")
    print()
    print("Testing full 2024...")
    print()
    
    # Create log directory if needed
    log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    log_dir.mkdir(exist_ok=True)
    
    # Run full 2024
    cmd = [
        sys.executable,
        "scripts/run_modern_backtest.py",
        "--start-date", "2024-01-01",
        "--end-date", "2024-12-31",
        "--override", "configs/overrides/modern_system_v2_complete.yaml",
        "--output", "modern_fixed_thresholds_2024.json"
    ]
    
    # Log file path
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"modern_fixed_thresholds_{timestamp}.log"
    
    print(f"Starting backtest...")
    print(f"Log file: {log_file}")
    print("-" * 80)
    
    start_time = datetime.now()
    
    # Run with output to console
    with open(log_file, 'w') as f:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        trade_count = 0
        
        for line in process.stdout:
            # Write to log
            f.write(line)
            f.flush()
            
            # Show progress
            if "Trade #" in line:
                trade_count += 1
                if trade_count % 10 == 0:
                    print(f"Progress: {trade_count} trades...")
            elif "Backtest Complete" in line:
                print(line.rstrip())
            elif "Total Trades:" in line:
                print(line.rstrip())
            elif "Final ROI:" in line:
                print(line.rstrip())
            elif "ERROR" in line:
                print(line.rstrip())
        
        process.wait()
    
    elapsed = (datetime.now() - start_time).total_seconds()
    
    if process.returncode != 0:
        print("\nBacktest failed!")
        return 1
    
    print(f"\nBacktest completed in {elapsed:.1f} seconds")
    
    # Load and analyze results
    results_file = Path("modern_fixed_thresholds_2024.json")
    if results_file.exists():
        with open(results_file) as f:
            data = json.load(f)
        
        perf = data['performance']
        trades = data['trades']
        completed = [t for t in trades if 'return' in t]
        
        print("\n" + "=" * 80)
        print("RESULTS - FIXED THRESHOLDS")
        print("=" * 80)
        
        print(f"\nTrades:")
        print(f"  Total: {perf.get('total_trades', 0)}")
        print(f"  Completed: {len(completed)}")
        print(f"  Open: {perf.get('total_trades', 0) - len(completed)}")
        
        print(f"\nPerformance:")
        print(f"  Total Return: {perf.get('total_return', 0):.2%}")
        print(f"  Win Rate: {perf.get('win_rate', 0):.2%}")
        print(f"  Avg Return: {perf.get('avg_trade_return', 0):.2%}")
        print(f"  Best Trade: {perf.get('best_trade', 0):.2%}")
        print(f"  Worst Trade: {perf.get('worst_trade', 0):.2%}")
        
        print(f"\nComparison:")
        print(f"  Modern (Fixed): {perf.get('total_trades', 0)} trades, {perf.get('total_return', 0):.1%} ROI")
        print(f"  Modern (Before): 277 trades, -2.1% ROI (Jan only)")  
        print(f"  Legacy: 189 trades, 215.0% ROI")
        
        # Calculate monthly breakdown
        monthly_returns = {}
        monthly_trades = {}
        
        for trade in completed:
            if 'exit_time' in trade:
                month = trade['exit_time'].strftime('%Y-%m') if hasattr(trade['exit_time'], 'strftime') else str(trade['exit_time'])[:7]
                if month not in monthly_returns:
                    monthly_returns[month] = 0
                    monthly_trades[month] = 0
                monthly_returns[month] += trade['return']
                monthly_trades[month] += 1
        
        if monthly_returns:
            print(f"\nMonthly Breakdown:")
            for month in sorted(monthly_returns.keys()):
                print(f"  {month}: {monthly_trades[month]} trades, {monthly_returns[month]:.2%} return")
        
        # Save detailed analysis
        analysis = {
            "test": "fixed_thresholds_full_2024",
            "changes": {
                "gms_spread_mean_low_thresh": {
                    "from": 1.2,
                    "to": 0.0008,
                    "ratio": "1500x error fixed"
                },
                "regime_cache": "regenerated with fixed thresholds"
            },
            "results": {
                "total_trades": perf.get('total_trades', 0),
                "completed_trades": len(completed),
                "total_return": perf.get('total_return', 0),
                "win_rate": perf.get('win_rate', 0),
                "avg_trade_return": perf.get('avg_trade_return', 0),
                "best_trade": perf.get('best_trade', 0),
                "worst_trade": perf.get('worst_trade', 0)
            },
            "comparison": {
                "legacy_trades": 189,
                "legacy_roi": 2.15,
                "modern_fixed_trades": perf.get('total_trades', 0),
                "modern_fixed_roi": perf.get('total_return', 0),
                "improvement_vs_broken": perf.get('total_return', 0) - (-0.021)
            },
            "monthly_breakdown": monthly_returns
        }
        
        with open("modern_fixed_thresholds_analysis.json", 'w') as f:
            json.dump(analysis, f, indent=2, default=str)
        
        print(f"\n📊 Full analysis saved to: modern_fixed_thresholds_analysis.json")
        print(f"📁 Detailed results saved to: {results_file}")
        print(f"📄 Complete log saved to: {log_file}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())