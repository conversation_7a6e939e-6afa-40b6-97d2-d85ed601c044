#!/usr/bin/env python3
"""Analyze why modern system is generating too many trades."""

import json
import numpy as np
from datetime import datetime

# Load results
with open('modern_backtest_results.json') as f:
    data = json.load(f)

print("=== TRADE FREQUENCY ANALYSIS ===\n")

# Analyze trades
trades = data['trades']
print(f"Total trades: {len(trades)}")
print(f"Test period: 24 hours (2024-01-01)")
print(f"Extrapolated annual rate: {len(trades) * 365} trades/year")
print(f"Target: ~180 trades/year\n")

# Analyze trade timing
print("Trade timing:")
for i, trade in enumerate(trades):
    print(f"{i+1}. {trade['timestamp']} - {trade['direction']} in {trade['regime']} (confidence: {trade['confidence']:.2f})")
    if i > 0:
        prev_time = datetime.strptime(trades[i-1]['timestamp'], "%Y-%m-%d %H:%M:%S")
        curr_time = datetime.strptime(trade['timestamp'], "%Y-%m-%d %H:%M:%S")
        hours_between = (curr_time - prev_time).total_seconds() / 3600
        print(f"   -> {hours_between:.1f} hours since last trade")

# Analyze regime states during test period
print("\n\nRegime distribution:")
regime_counts = {}
tradeable_hours = 0
for hour in data['regime_history']:
    regime = hour['regime']
    regime_counts[regime] = regime_counts.get(regime, 0) + 1
    
    # Check if this is a tradeable regime
    if regime in ['Weak_Bull_Trend', 'Strong_Bull_Trend', 'Weak_Bear_Trend', 'Strong_Bear_Trend']:
        tradeable_hours += 1

print(f"Total hours: {len(data['regime_history'])}")
print(f"Tradeable regime hours: {tradeable_hours} ({tradeable_hours/len(data['regime_history'])*100:.1f}%)")
print("\nRegime breakdown:")
for regime, count in sorted(regime_counts.items(), key=lambda x: x[1], reverse=True):
    print(f"  {regime}: {count} hours ({count/len(data['regime_history'])*100:.1f}%)")

# Analyze why trades cluster
print("\n\nTrade clustering analysis:")
print("All trades occurred during Weak_Bull_Trend regimes")
print("Looking at consecutive bull regimes...")

# Find bull regime periods
bull_periods = []
current_period = None
for i, hour in enumerate(data['regime_history']):
    if hour['regime'] in ['Weak_Bull_Trend', 'Strong_Bull_Trend']:
        if current_period is None:
            current_period = {'start': i, 'end': i, 'hours': [hour]}
        else:
            current_period['end'] = i
            current_period['hours'].append(hour)
    else:
        if current_period is not None:
            bull_periods.append(current_period)
            current_period = None

if current_period is not None:
    bull_periods.append(current_period)

print(f"\nFound {len(bull_periods)} bull trend periods:")
for i, period in enumerate(bull_periods):
    duration = period['end'] - period['start'] + 1
    start_time = data['regime_history'][period['start']]['timestamp']
    end_time = data['regime_history'][period['end']]['timestamp']
    print(f"\nPeriod {i+1}: {start_time} to {end_time} ({duration} hours)")
    
    # Check if trades occurred in this period
    period_trades = [t for t in trades if start_time <= t['timestamp'] <= end_time]
    if period_trades:
        print(f"  -> {len(period_trades)} trades in this period")
        print(f"  -> Trade frequency: 1 trade every {duration/len(period_trades):.1f} hours")

# Recommendations
print("\n\n=== RECOMMENDATIONS ===")
print("The system is trading too frequently because:")
print("1. It enters a new trade almost every hour when in a bull trend")
print("2. There's no minimum time between trades")
print("3. The forecast confirmation might be too easy to satisfy")
print("\nPossible solutions:")
print("1. Add minimum time between trades (e.g., 4-6 hours)")
print("2. Require stronger forecast values (not just > 0)")
print("3. Require regime to be stable for longer before entry")
print("4. Add position management - don't enter if already in position")
print("5. Make EMA crossover detection less sensitive")

# Calculate what trade frequency we need
target_trades_year = 180
hours_per_year = 365 * 24
avg_hours_between_trades = hours_per_year / target_trades_year
print(f"\nTarget frequency: 1 trade every {avg_hours_between_trades:.1f} hours")
print(f"Current frequency: 1 trade every {24/len(trades):.1f} hours")
print(f"Reduction needed: {len(trades)*365/target_trades_year:.1f}x fewer trades")