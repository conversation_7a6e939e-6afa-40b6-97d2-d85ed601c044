#!/usr/bin/env python3
"""
Debug regime cache generation to see what's happening.
"""

import logging
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.core.detector import GranularMicrostructureRegimeDetector
from hyperliquid_bot.modern.data_loader import ModernDataLoader

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Load configuration
config = load_config('configs/overrides/modern_system_v2_complete.yaml')

# Initialize components
data_loader = ModernDataLoader(config)
detector = GranularMicrostructureRegimeDetector(config)

# Test date
test_date = datetime(2024, 10, 1)
lookback_start = test_date - timedelta(hours=24)
day_end = test_date + timedelta(days=1) - timedelta(seconds=1)

# Load data
print(f"Loading data from {lookback_start} to {day_end}")
features_1s = data_loader.load_data(
    start_date=lookback_start,
    end_date=day_end
)

if features_1s is None or features_1s.empty:
    print("No data loaded!")
else:
    print(f"Loaded {len(features_1s)} rows")
    print(f"Columns: {list(features_1s.columns[:20])}...")  # Show first 20 columns
    
    # Check for required fields
    required_fields = ['atr_percent', 'ma_slope', 'obi_smoothed_5', 'spread_mean', 'spread_std']
    for field in required_fields:
        if field in features_1s.columns:
            print(f"✓ {field} found")
        else:
            print(f"✗ {field} MISSING")
            # Check for similar fields
            similar = [col for col in features_1s.columns if field.split('_')[0] in col]
            if similar:
                print(f"  Similar fields: {similar[:5]}")
    
    # Test with a single timestamp
    test_time = test_date + timedelta(hours=12)
    data_slice = features_1s[features_1s.index <= test_time]
    
    if len(data_slice) >= 100:
        latest_row = data_slice.iloc[-1]
        features_dict = latest_row.to_dict()
        
        print(f"\nTesting regime detection at {test_time}")
        print(f"Features dict keys (first 20): {list(features_dict.keys())[:20]}")
        
        try:
            regime_state = detector.get_regime(features_dict)
            print(f"✓ Regime detected: {regime_state}")
        except Exception as e:
            print(f"✗ Error detecting regime: {e}")
            import traceback
            traceback.print_exc()