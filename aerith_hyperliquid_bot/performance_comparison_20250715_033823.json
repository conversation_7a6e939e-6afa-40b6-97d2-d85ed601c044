{"timestamp": "2025-07-15T03:38:23.649288", "legacy_result": {"system_mode": "legacy", "performance": {"initial_balance": 10000.0, "winning_trades": 0.0, "losing_trades": 0.0, "avg_win": 413.73, "avg_loss": 413.73, "profit_factor": 92.0, "sharpe": 92.0, "max_drawdown": 8.98, "max_leverage": 50.0}, "trades": {"total_trades": 180, "winning_trades": 102, "losing_trades": 78, "long_trades": 134, "short_trades": 46, "all_pnls": [544.0116, -53.0065, -15.4387, 189.9053, 253.0855, 61.4621, 54.3593, 635.3862, -84.1629, 341.2182, 375.4538, -210.1987, 658.3439, 405.2899, 308.3559, 672.9156, -164.4589, 133.0797, 484.8494, -94.6526, 456.9437, 286.2506, -133.9257, 698.8012, -99.8654, -164.5239, -111.2701, 397.6888, -253.6621, -230.2281, 516.286, -229.1629, -166.937, 20.7353, 478.341, -42.5674, -88.1434, 1068.564, -190.471, -28.808, 543.0007, -317.2289, 241.0365, -195.4726, -65.6244, 235.9733, 53.0647, 339.9742, -298.7241, -0.3495, 436.0417, -6.2357, 199.5851, -8.1848, -277.615, -270.3664, 230.2098, 26.4828, 237.627, 26.874, 174.797, 955.5663, -291.3997, -7.7732, 727.2732, 543.879, -387.6612, 136.6948, 30.6153, -24.2771, 352.9538, 91.6922, 328.717, -264.4431, -521.9843, -304.0068, 366.2694, 676.5012, 496.4067, -312.6118, 230.0287, -210.9605, 1021.9001, 457.6819, 383.9251, -692.2295, -334.9984, 381.2686, -223.2887, 302.8992, 34.8921, 301.2888, 306.244, 229.8013, 129.7657, -182.7372, -387.706, 260.6415, -125.204, -7.7834, 23.6498, 82.2884, 116.0028, -283.0581, 55.0243, 988.608, 133.4603, -423.8095, 358.3494, 2.8778, 718.9898, -158.8648, -60.628, 731.6093, -166.2781, 132.1899, 121.9619, 738.972, -222.4541, 891.4081, 6.823, -87.3096, -725.5346, 679.3681, 513.6867, 667.6007, -8.2026, 8.4559, -167.0341, -476.241, 495.7945, 501.9995, 747.1414, -783.7138, -474.5798, 873.0635, 728.2496, 204.1472, -420.0568, 1775.0558, 42.6645, 1116.9469, -98.9411, -220.7256, -115.3631, -139.4088, 1.9163, 146.0043, 1166.9452, -382.8678, -424.7006, -180.2416, -151.0343, -262.2517, -240.0241, -321.7436, -242.9097, 529.4108, 66.5126, -299.199, -649.1728, 589.4315, -460.0932, 325.0944, -309.4887, -18.3644, 265.8184, -207.6833, 665.5625, -0.7842, 1579.6696, 299.9028, 330.1457, 1008.1466, -440.5552, -944.3678, 157.332, 364.6327, 274.5684, 439.7366], "winning_pnls": [544.0116, 189.9053, 253.0855, 61.4621, 54.3593, 635.3862, 341.2182, 375.4538, 658.3439, 405.2899, 308.3559, 672.9156, 133.0797, 484.8494, 456.9437, 286.2506, 698.8012, 397.6888, 516.286, 20.7353, 478.341, 1068.564, 543.0007, 241.0365, 235.9733, 53.0647, 339.9742, 436.0417, 199.5851, 230.2098, 26.4828, 237.627, 26.874, 174.797, 955.5663, 727.2732, 543.879, 136.6948, 30.6153, 352.9538, 91.6922, 328.717, 366.2694, 676.5012, 496.4067, 230.0287, 1021.9001, 457.6819, 383.9251, 381.2686, 302.8992, 34.8921, 301.2888, 306.244, 229.8013, 129.7657, 260.6415, 23.6498, 82.2884, 116.0028, 55.0243, 988.608, 133.4603, 358.3494, 2.8778, 718.9898, 731.6093, 132.1899, 121.9619, 738.972, 891.4081, 6.823, 679.3681, 513.6867, 667.6007, 8.4559, 495.7945, 501.9995, 747.1414, 873.0635, 728.2496, 204.1472, 1775.0558, 42.6645, 1116.9469, 1.9163, 146.0043, 1166.9452, 529.4108, 66.5126, 589.4315, 325.0944, 265.8184, 665.5625, 1579.6696, 299.9028, 330.1457, 1008.1466, 157.332, 364.6327, 274.5684, 439.7366], "losing_pnls": [-53.0065, -15.4387, -84.1629, -210.1987, -164.4589, -94.6526, -133.9257, -99.8654, -164.5239, -111.2701, -253.6621, -230.2281, -229.1629, -166.937, -42.5674, -88.1434, -190.471, -28.808, -317.2289, -195.4726, -65.6244, -298.7241, -0.3495, -6.2357, -8.1848, -277.615, -270.3664, -291.3997, -7.7732, -387.6612, -24.2771, -264.4431, -521.9843, -304.0068, -312.6118, -210.9605, -692.2295, -334.9984, -223.2887, -182.7372, -387.706, -125.204, -7.7834, -283.0581, -423.8095, -158.8648, -60.628, -166.2781, -222.4541, -87.3096, -725.5346, -8.2026, -167.0341, -476.241, -783.7138, -474.5798, -420.0568, -98.9411, -220.7256, -115.3631, -139.4088, -382.8678, -424.7006, -180.2416, -151.0343, -262.2517, -240.0241, -321.7436, -242.9097, -299.199, -649.1728, -460.0932, -309.4887, -18.3644, -207.6833, -0.7842, -440.5552, -944.3678], "trade_sizes": [0.2713, 0.2421, 0.2493, 0.1993, 0.3728, 0.4145, 0.44, 0.3789, 0.3251, 0.325, 0.283, 0.3124, 0.4066, 0.358, 0.2723, 0.2531, 0.1514, 0.1401, 0.2911, 0.1766, 0.104, 0.26, 0.3646, 0.2552, 0.2647, 0.2432, 0.2456, 0.1571, 0.1941, 0.17, 0.16, 0.1528, 0.1556, 0.1965, 0.2741, 0.2541, 0.2611, 0.2562, 0.2419, 0.3178, 0.388, 0.4299, 0.3394, 0.3489, 0.2693, 0.2401, 0.2875, 0.1834, 0.1631, 0.201, 0.2709, 0.3601, 0.298, 0.258, 0.2304, 0.3031, 0.2831, 0.3352, 0.415, 0.4052, 0.4605, 0.381, 0.4007, 0.417, 0.445, 0.3681, 0.3213, 0.3598, 0.524, 0.4275, 0.5496, 0.4314, 0.4323, 0.4204, 0.4575, 0.3337, 0.3771, 0.6557, 0.3802, 0.3836, 0.4016, 0.3765, 0.519, 0.5048, 0.4715, 0.4175, 0.3552, 0.4034, 0.5727, 0.3891, 0.5285, 0.3828, 0.4807, 0.4631, 0.3079, 0.2965, 0.1602, 0.2162, 0.259, 0.2955, 0.3781, 0.4122, 0.5231, 0.418, 0.4175, 0.4292, 0.5692, 0.4346, 0.4546, 0.6022, 0.5108, 0.5176, 0.559, 0.533, 0.8162, 0.5144, 0.4743, 0.4369, 0.5346, 0.5842, 0.5257, 0.5423, 0.8977, 0.7344, 0.6393, 0.6755, 0.6596, 0.5069, 0.6278, 0.6733, 0.9345, 0.7394, 0.6312, 0.5607, 0.5584, 0.4466, 0.3049, 0.4409, 0.5115, 0.5729, 0.4812, 0.403, 0.3178, 0.2273, 0.2517, 0.2763, 0.463, 0.361, 0.4023, 0.3897, 0.3732, 0.3875, 0.3239, 0.2928, 0.3585, 0.4767, 0.3556, 0.2604, 0.2565, 0.4056, 0.4767, 0.3197, 0.3334, 0.3146, 0.3663, 0.4003, 0.5284, 0.4136, 0.4077, 0.3466, 0.3091, 0.3166, 0.3173, 0.3959, 0.3336, 0.3641, 0.346, 0.4278, 0.555, 0.4338], "trades": [{"outcome": "WIN", "direction": "long", "size": 0.2713, "exit_price": 47044.0, "pnl": 544.0116}, {"outcome": "LOSS", "direction": "long", "size": 0.2421, "exit_price": 46661.0, "pnl": -53.0065}, {"outcome": "LOSS", "direction": "long", "size": 0.2493, "exit_price": 46679.0, "pnl": -15.4387}, {"outcome": "WIN", "direction": "short", "size": 0.1993, "exit_price": 43496.0, "pnl": 189.9053}, {"outcome": "WIN", "direction": "long", "size": 0.3728, "exit_price": 41752.0, "pnl": 253.0855}, {"outcome": "WIN", "direction": "long", "size": 0.4145, "exit_price": 43301.0, "pnl": 61.4621}, {"outcome": "WIN", "direction": "long", "size": 0.44, "exit_price": 45302.0, "pnl": 54.3593}, {"outcome": "WIN", "direction": "long", "size": 0.3789, "exit_price": 47244.0, "pnl": 635.3862}, {"outcome": "LOSS", "direction": "long", "size": 0.3251, "exit_price": 47292.0, "pnl": -84.1629}, {"outcome": "WIN", "direction": "long", "size": 0.325, "exit_price": 49921.0, "pnl": 341.2182}, {"outcome": "WIN", "direction": "long", "size": 0.283, "exit_price": 52224.0, "pnl": 375.4538}, {"outcome": "LOSS", "direction": "long", "size": 0.3124, "exit_price": 51714.0, "pnl": -210.1987}, {"outcome": "WIN", "direction": "long", "size": 0.4066, "exit_price": 54552.0, "pnl": 658.3439}, {"outcome": "WIN", "direction": "long", "size": 0.358, "exit_price": 55875.0, "pnl": 405.2899}, {"outcome": "WIN", "direction": "long", "size": 0.2723, "exit_price": 57301.0, "pnl": 308.3559}, {"outcome": "WIN", "direction": "long", "size": 0.2531, "exit_price": 61067.0, "pnl": 672.9156}, {"outcome": "LOSS", "direction": "long", "size": 0.1514, "exit_price": 61516.0, "pnl": -164.4589}, {"outcome": "WIN", "direction": "long", "size": 0.1401, "exit_price": 61470.0, "pnl": 133.0797}, {"outcome": "WIN", "direction": "long", "size": 0.2911, "exit_price": 65322.0, "pnl": 484.8494}, {"outcome": "LOSS", "direction": "long", "size": 0.1766, "exit_price": 67005.0, "pnl": -94.6526}, {"outcome": "WIN", "direction": "long", "size": 0.104, "exit_price": 66546.0, "pnl": 456.9437}, {"outcome": "WIN", "direction": "long", "size": 0.26, "exit_price": 68563.0, "pnl": 286.2506}, {"outcome": "LOSS", "direction": "long", "size": 0.3646, "exit_price": 69401.0, "pnl": -133.9257}, {"outcome": "WIN", "direction": "long", "size": 0.2552, "exit_price": 72203.0, "pnl": 698.8012}, {"outcome": "LOSS", "direction": "long", "size": 0.2647, "exit_price": 71785.0, "pnl": -99.8654}, {"outcome": "LOSS", "direction": "long", "size": 0.2432, "exit_price": 72904.0, "pnl": -164.5239}, {"outcome": "LOSS", "direction": "long", "size": 0.2456, "exit_price": 72897.0, "pnl": -111.2701}, {"outcome": "WIN", "direction": "short", "size": 0.1571, "exit_price": 69117.0, "pnl": 397.6888}, {"outcome": "LOSS", "direction": "short", "size": 0.1941, "exit_price": 67669.0, "pnl": -253.6621}, {"outcome": "LOSS", "direction": "short", "size": 0.17, "exit_price": 65012.0, "pnl": -230.2281}, {"outcome": "WIN", "direction": "short", "size": 0.16, "exit_price": 61655.0, "pnl": 516.286}, {"outcome": "LOSS", "direction": "short", "size": 0.1528, "exit_price": 62994.0, "pnl": -229.1629}, {"outcome": "LOSS", "direction": "long", "size": 0.1556, "exit_price": 65714.0, "pnl": -166.937}, {"outcome": "WIN", "direction": "short", "size": 0.1965, "exit_price": 64167.0, "pnl": 20.7353}, {"outcome": "WIN", "direction": "long", "size": 0.2741, "exit_price": 67156.0, "pnl": 478.341}, {"outcome": "LOSS", "direction": "long", "size": 0.2541, "exit_price": 70570.0, "pnl": -42.5674}, {"outcome": "LOSS", "direction": "long", "size": 0.2611, "exit_price": 70703.0, "pnl": -88.1434}, {"outcome": "WIN", "direction": "short", "size": 0.2562, "exit_price": 64907.99, "pnl": 1068.564}, {"outcome": "LOSS", "direction": "short", "size": 0.2419, "exit_price": 66174.0, "pnl": -190.471}, {"outcome": "LOSS", "direction": "long", "size": 0.3178, "exit_price": 67663.0, "pnl": -28.808}, {"outcome": "WIN", "direction": "long", "size": 0.388, "exit_price": 69617.0, "pnl": 543.0007}, {"outcome": "LOSS", "direction": "long", "size": 0.4299, "exit_price": 69438.0, "pnl": -317.2289}, {"outcome": "WIN", "direction": "long", "size": 0.3394, "exit_price": 71487.0, "pnl": 241.0365}, {"outcome": "LOSS", "direction": "long", "size": 0.3489, "exit_price": 70819.0, "pnl": -195.4726}, {"outcome": "LOSS", "direction": "short", "size": 0.2693, "exit_price": 70221.0, "pnl": -65.6244}, {"outcome": "WIN", "direction": "short", "size": 0.2401, "exit_price": 67207.0, "pnl": 235.9733}, {"outcome": "WIN", "direction": "short", "size": 0.2875, "exit_price": 67189.0, "pnl": 53.0647}, {"outcome": "WIN", "direction": "short", "size": 0.1834, "exit_price": 62993.0, "pnl": 339.9742}, {"outcome": "LOSS", "direction": "short", "size": 0.1631, "exit_price": 65859.66, "pnl": -298.7241}, {"outcome": "LOSS", "direction": "short", "size": 0.201, "exit_price": 63058.0, "pnl": -0.3495}, {"outcome": "WIN", "direction": "long", "size": 0.2709, "exit_price": 65204.0, "pnl": 436.0417}, {"outcome": "LOSS", "direction": "long", "size": 0.3601, "exit_price": 66391.0, "pnl": -6.2357}, {"outcome": "WIN", "direction": "short", "size": 0.298, "exit_price": 59512.0, "pnl": 199.5851}, {"outcome": "LOSS", "direction": "short", "size": 0.258, "exit_price": 57491.0, "pnl": -8.1848}, {"outcome": "LOSS", "direction": "short", "size": 0.2304, "exit_price": 58607.0, "pnl": -277.615}, {"outcome": "LOSS", "direction": "short", "size": 0.3031, "exit_price": 60592.0, "pnl": -270.3664}, {"outcome": "WIN", "direction": "long", "size": 0.2831, "exit_price": 63585.0, "pnl": 230.2098}, {"outcome": "WIN", "direction": "long", "size": 0.3352, "exit_price": 64222.0, "pnl": 26.4828}, {"outcome": "WIN", "direction": "long", "size": 0.415, "exit_price": 65136.0, "pnl": 237.627}, {"outcome": "WIN", "direction": "long", "size": 0.4052, "exit_price": 63041.0, "pnl": 26.874}, {"outcome": "WIN", "direction": "long", "size": 0.4605, "exit_price": 62736.0, "pnl": 174.797}, {"outcome": "WIN", "direction": "long", "size": 0.381, "exit_price": 65028.0, "pnl": 955.5663}, {"outcome": "LOSS", "direction": "long", "size": 0.4007, "exit_price": 65106.0, "pnl": -291.3997}, {"outcome": "LOSS", "direction": "long", "size": 0.417, "exit_price": 66904.0, "pnl": -7.7732}, {"outcome": "WIN", "direction": "long", "size": 0.445, "exit_price": 69960.0, "pnl": 727.2732}, {"outcome": "WIN", "direction": "long", "size": 0.3681, "exit_price": 71394.49, "pnl": 543.879}, {"outcome": "LOSS", "direction": "long", "size": 0.3213, "exit_price": 70163.0, "pnl": -387.6612}, {"outcome": "WIN", "direction": "long", "size": 0.3598, "exit_price": 70253.0, "pnl": 136.6948}, {"outcome": "WIN", "direction": "long", "size": 0.524, "exit_price": 69216.0, "pnl": 30.6153}, {"outcome": "LOSS", "direction": "long", "size": 0.4275, "exit_price": 68714.0, "pnl": -24.2771}, {"outcome": "WIN", "direction": "long", "size": 0.5496, "exit_price": 69859.0, "pnl": 352.9538}, {"outcome": "WIN", "direction": "long", "size": 0.4314, "exit_price": 69093.0, "pnl": 91.6922}, {"outcome": "WIN", "direction": "long", "size": 0.4323, "exit_price": 71299.0, "pnl": 328.717}, {"outcome": "LOSS", "direction": "long", "size": 0.4204, "exit_price": 71096.0, "pnl": -264.4431}, {"outcome": "LOSS", "direction": "short", "size": 0.4575, "exit_price": 68910.0, "pnl": -521.9843}, {"outcome": "LOSS", "direction": "long", "size": 0.3337, "exit_price": 67735.0, "pnl": -304.0068}, {"outcome": "WIN", "direction": "short", "size": 0.3771, "exit_price": 60917.0, "pnl": 366.2694}, {"outcome": "WIN", "direction": "long", "size": 0.6557, "exit_price": 62977.0, "pnl": 676.5012}, {"outcome": "WIN", "direction": "short", "size": 0.3802, "exit_price": 57215.0, "pnl": 496.4067}, {"outcome": "LOSS", "direction": "short", "size": 0.3836, "exit_price": 57306.0, "pnl": -312.6118}, {"outcome": "WIN", "direction": "short", "size": 0.4016, "exit_price": 57207.0, "pnl": 230.0287}, {"outcome": "LOSS", "direction": "long", "size": 0.3765, "exit_price": 57403.0, "pnl": -210.9605}, {"outcome": "WIN", "direction": "long", "size": 0.519, "exit_price": 59949.0, "pnl": 1021.9001}, {"outcome": "WIN", "direction": "long", "size": 0.5048, "exit_price": 61233.0, "pnl": 457.6819}, {"outcome": "WIN", "direction": "long", "size": 0.4715, "exit_price": 63526.0, "pnl": 383.9251}, {"outcome": "LOSS", "direction": "long", "size": 0.4175, "exit_price": 62911.0, "pnl": -692.2295}, {"outcome": "LOSS", "direction": "long", "size": 0.3552, "exit_price": 64741.0, "pnl": -334.9984}, {"outcome": "WIN", "direction": "long", "size": 0.4034, "exit_price": 66859.0, "pnl": 381.2686}, {"outcome": "LOSS", "direction": "long", "size": 0.5727, "exit_price": 67098.0, "pnl": -223.2887}, {"outcome": "WIN", "direction": "long", "size": 0.3891, "exit_price": 67958.0, "pnl": 302.8992}, {"outcome": "WIN", "direction": "long", "size": 0.5285, "exit_price": 68273.0, "pnl": 34.8921}, {"outcome": "WIN", "direction": "long", "size": 0.3828, "exit_price": 68685.0, "pnl": 301.2888}, {"outcome": "WIN", "direction": "long", "size": 0.4807, "exit_price": 69310.0, "pnl": 306.244}, {"outcome": "WIN", "direction": "short", "size": 0.4631, "exit_price": 66076.0, "pnl": 229.8013}, {"outcome": "WIN", "direction": "short", "size": 0.3079, "exit_price": 60906.0, "pnl": 129.7657}, {"outcome": "LOSS", "direction": "short", "size": 0.2965, "exit_price": 56191.0, "pnl": -182.7372}, {"outcome": "LOSS", "direction": "short", "size": 0.1602, "exit_price": 56434.0, "pnl": -387.706}, {"outcome": "WIN", "direction": "short", "size": 0.2162, "exit_price": 55548.0, "pnl": 260.6415}, {"outcome": "LOSS", "direction": "long", "size": 0.259, "exit_price": 60683.0, "pnl": -125.204}, {"outcome": "LOSS", "direction": "long", "size": 0.2955, "exit_price": 60865.0, "pnl": -7.7834}, {"outcome": "WIN", "direction": "long", "size": 0.3781, "exit_price": 61076.0, "pnl": 23.6498}, {"outcome": "WIN", "direction": "long", "size": 0.4122, "exit_price": 59413.0, "pnl": 82.2884}, {"outcome": "WIN", "direction": "long", "size": 0.5231, "exit_price": 60861.0, "pnl": 116.0028}, {"outcome": "LOSS", "direction": "long", "size": 0.418, "exit_price": 60965.0, "pnl": -283.0581}, {"outcome": "WIN", "direction": "long", "size": 0.4175, "exit_price": 60727.0, "pnl": 55.0243}, {"outcome": "WIN", "direction": "long", "size": 0.4292, "exit_price": 64089.0, "pnl": 988.608}, {"outcome": "WIN", "direction": "long", "size": 0.5692, "exit_price": 64465.0, "pnl": 133.4603}, {"outcome": "LOSS", "direction": "short", "size": 0.4346, "exit_price": 59541.0, "pnl": -423.8095}, {"outcome": "WIN", "direction": "short", "size": 0.4546, "exit_price": 59487.0, "pnl": 358.3494}, {"outcome": "WIN", "direction": "long", "size": 0.6022, "exit_price": 59103.0, "pnl": 2.8778}, {"outcome": "WIN", "direction": "long", "size": 0.5108, "exit_price": 56800.0, "pnl": 718.9898}, {"outcome": "LOSS", "direction": "long", "size": 0.5176, "exit_price": 56885.0, "pnl": -158.8648}, {"outcome": "LOSS", "direction": "long", "size": 0.559, "exit_price": 57832.0, "pnl": -60.628}, {"outcome": "WIN", "direction": "long", "size": 0.533, "exit_price": 59849.0, "pnl": 731.6093}, {"outcome": "LOSS", "direction": "long", "size": 0.8162, "exit_price": 59807.0, "pnl": -166.2781}, {"outcome": "WIN", "direction": "long", "size": 0.5144, "exit_price": 60509.0, "pnl": 132.1899}, {"outcome": "WIN", "direction": "long", "size": 0.4743, "exit_price": 60244.0, "pnl": 121.9619}, {"outcome": "WIN", "direction": "long", "size": 0.4369, "exit_price": 63877.0, "pnl": 738.972}, {"outcome": "LOSS", "direction": "long", "size": 0.5346, "exit_price": 63459.0, "pnl": -222.4541}, {"outcome": "WIN", "direction": "long", "size": 0.5842, "exit_price": 66032.0, "pnl": 891.4081}, {"outcome": "WIN", "direction": "short", "size": 0.5257, "exit_price": 61116.0, "pnl": 6.823}, {"outcome": "LOSS", "direction": "long", "size": 0.5423, "exit_price": 61850.0, "pnl": -87.3096}, {"outcome": "LOSS", "direction": "long", "size": 0.8977, "exit_price": 63570.0, "pnl": -725.5346}, {"outcome": "WIN", "direction": "long", "size": 0.7344, "exit_price": 63848.0, "pnl": 679.3681}, {"outcome": "WIN", "direction": "long", "size": 0.6393, "exit_price": 63086.0, "pnl": 513.6867}, {"outcome": "WIN", "direction": "long", "size": 0.6755, "exit_price": 65116.0, "pnl": 667.6007}, {"outcome": "LOSS", "direction": "long", "size": 0.6596, "exit_price": 65785.0, "pnl": -8.2026}, {"outcome": "WIN", "direction": "long", "size": 0.5069, "exit_price": 67200.0, "pnl": 8.4559}, {"outcome": "LOSS", "direction": "long", "size": 0.6278, "exit_price": 68229.0, "pnl": -167.0341}, {"outcome": "LOSS", "direction": "long", "size": 0.6733, "exit_price": 67619.0, "pnl": -476.241}, {"outcome": "WIN", "direction": "long", "size": 0.9345, "exit_price": 69219.0, "pnl": 495.7945}, {"outcome": "WIN", "direction": "long", "size": 0.7394, "exit_price": 70334.0, "pnl": 501.9995}, {"outcome": "WIN", "direction": "long", "size": 0.6312, "exit_price": 72458.0, "pnl": 747.1414}, {"outcome": "LOSS", "direction": "long", "size": 0.5607, "exit_price": 71890.0, "pnl": -783.7138}, {"outcome": "LOSS", "direction": "short", "size": 0.5584, "exit_price": 70044.0, "pnl": -474.5798}, {"outcome": "WIN", "direction": "long", "size": 0.4466, "exit_price": 71190.0, "pnl": 873.0635}, {"outcome": "WIN", "direction": "long", "size": 0.3049, "exit_price": 76107.0, "pnl": 728.2496}, {"outcome": "WIN", "direction": "long", "size": 0.4409, "exit_price": 76470.0, "pnl": 204.1472}, {"outcome": "LOSS", "direction": "long", "size": 0.5115, "exit_price": 76106.0, "pnl": -420.0568}, {"outcome": "WIN", "direction": "long", "size": 0.5729, "exit_price": 80440.0, "pnl": 1775.0558}, {"outcome": "WIN", "direction": "long", "size": 0.4812, "exit_price": 79970.0, "pnl": 42.6645}, {"outcome": "WIN", "direction": "long", "size": 0.403, "exit_price": 84119.0, "pnl": 1116.9469}, {"outcome": "LOSS", "direction": "long", "size": 0.3178, "exit_price": 87765.0, "pnl": -98.9411}, {"outcome": "LOSS", "direction": "long", "size": 0.2273, "exit_price": 88586.0, "pnl": -220.7256}, {"outcome": "LOSS", "direction": "long", "size": 0.2517, "exit_price": 90780.0, "pnl": -115.3631}, {"outcome": "LOSS", "direction": "long", "size": 0.2763, "exit_price": 91074.0, "pnl": -139.4088}, {"outcome": "WIN", "direction": "long", "size": 0.463, "exit_price": 91874.0, "pnl": 1.9163}, {"outcome": "WIN", "direction": "long", "size": 0.361, "exit_price": 92453.0, "pnl": 146.0043}, {"outcome": "WIN", "direction": "long", "size": 0.4023, "exit_price": 95881.0, "pnl": 1166.9452}, {"outcome": "LOSS", "direction": "long", "size": 0.3897, "exit_price": 97121.0, "pnl": -382.8678}, {"outcome": "LOSS", "direction": "long", "size": 0.3732, "exit_price": 97867.0, "pnl": -424.7006}, {"outcome": "LOSS", "direction": "long", "size": 0.3875, "exit_price": 98487.0, "pnl": -180.2416}, {"outcome": "LOSS", "direction": "short", "size": 0.3239, "exit_price": 92843.0, "pnl": -151.0343}, {"outcome": "LOSS", "direction": "short", "size": 0.2928, "exit_price": 94924.0, "pnl": -262.2517}, {"outcome": "LOSS", "direction": "long", "size": 0.3585, "exit_price": 95712.0, "pnl": -240.0241}, {"outcome": "LOSS", "direction": "long", "size": 0.4767, "exit_price": 96338.0, "pnl": -321.7436}, {"outcome": "LOSS", "direction": "long", "size": 0.3556, "exit_price": 98238.0, "pnl": -242.9097}, {"outcome": "WIN", "direction": "long", "size": 0.2604, "exit_price": 103699.0, "pnl": 529.4108}, {"outcome": "WIN", "direction": "long", "size": 0.2565, "exit_price": 99706.0, "pnl": 66.5126}, {"outcome": "LOSS", "direction": "long", "size": 0.4056, "exit_price": 99046.0, "pnl": -299.199}, {"outcome": "LOSS", "direction": "long", "size": 0.4767, "exit_price": 99546.5, "pnl": -649.1728}, {"outcome": "WIN", "direction": "short", "size": 0.3197, "exit_price": 95961.0, "pnl": 589.4315}, {"outcome": "LOSS", "direction": "short", "size": 0.3334, "exit_price": 99613.0, "pnl": -460.0932}, {"outcome": "WIN", "direction": "long", "size": 0.3146, "exit_price": 101569.0, "pnl": 325.0944}, {"outcome": "LOSS", "direction": "long", "size": 0.3663, "exit_price": 100920.0, "pnl": -309.4887}, {"outcome": "LOSS", "direction": "long", "size": 0.4003, "exit_price": 101409.0, "pnl": -18.3644}, {"outcome": "WIN", "direction": "long", "size": 0.5284, "exit_price": 103360.0, "pnl": 265.8184}, {"outcome": "LOSS", "direction": "long", "size": 0.4136, "exit_price": 104766.0, "pnl": -207.6833}, {"outcome": "WIN", "direction": "long", "size": 0.4077, "exit_price": 105511.0, "pnl": 665.5625}, {"outcome": "LOSS", "direction": "long", "size": 0.3466, "exit_price": 106799.0, "pnl": -0.7842}, {"outcome": "WIN", "direction": "short", "size": 0.3091, "exit_price": 99721.0, "pnl": 1579.6696}, {"outcome": "WIN", "direction": "short", "size": 0.3166, "exit_price": 100700.0, "pnl": 299.9028}, {"outcome": "WIN", "direction": "short", "size": 0.3173, "exit_price": 97070.0, "pnl": 330.1457}, {"outcome": "WIN", "direction": "short", "size": 0.3959, "exit_price": 94370.0, "pnl": 1008.1466}, {"outcome": "LOSS", "direction": "short", "size": 0.3336, "exit_price": 95250.0, "pnl": -440.5552}, {"outcome": "LOSS", "direction": "short", "size": 0.3641, "exit_price": 98708.0, "pnl": -944.3678}, {"outcome": "WIN", "direction": "long", "size": 0.346, "exit_price": 99075.0, "pnl": 157.332}, {"outcome": "WIN", "direction": "short", "size": 0.4278, "exit_price": 95740.0, "pnl": 364.6327}, {"outcome": "WIN", "direction": "short", "size": 0.555, "exit_price": 92166.0, "pnl": 274.5684}, {"outcome": "WIN", "direction": "short", "size": 0.4338, "exit_price": 93527.0, "pnl": 439.7366}], "total_pnl": 23552.08409999999, "avg_pnl": 130.84491166666666, "median_pnl": 53.712, "pnl_std": 431.5116323149085, "avg_win": 413.7266588235294, "largest_win": 1775.0558, "avg_loss": -239.07737307692307, "largest_loss": -944.3678, "avg_size": 0.3877938888888889, "median_size": 0.3785, "max_size": 0.9345}, "portfolio": {"avg_leverage": 5.0, "max_leverage": 5.0, "min_leverage": 5.0, "leverage_std": 0.0, "initial_balance": 10.0, "final_balance": 31541.32, "balance_progression": [23083.82, 31769.91, 31769.91, 31319.48, 31319.48, 30364.08, 30364.08, 23530.24, 23526.86, 23523.43, 30500.47, 30500.47, 30852.08, 30852.08, 31110.44, 31110.44, 22890.37, 22894.44, 22894.44, 31541.32]}, "raw_output": "y):    \u001b[92m           4.36\u001b[0m\n[INFO   ] Backtester          :   • Profit Factor:           \u001b[92m           2.26\u001b[0m\n[INFO   ] Backtester          :   • Max Drawdown:            \u001b[91m          8.98%\u001b[0m\n[INFO   ] Backtester          :   • Return on Initial (ROI): \u001b[92m         215.41%\u001b[0m\n[INFO   ] Backtester          :   • Trade Count:                         180\u001b[0m\n[INFO   ] Backtester          : \n\u001b[1m════════════════════════════════════════════════════════════════════════════════\u001b[0m\n\u001b[1m################################################################################\u001b[0m\n[INFO   ] Backtester          : Enhanced equity curve plot saved to /Users/<USER>/Desktop/trading_bot_/logs/equity_tf_det_granular_microstructure_Sfilt_20250715_033718.png\n[INFO   ] Backtester          : \n\u001b[1m\u001b[96m                             WINNING TRADE ANALYSIS                             \u001b[0m\n[INFO   ] Backtester          : \u001b[1m────────────────────────────────────────────────────────────────────────────────\u001b[0m\n[INFO   ] Backtester          : \n\u001b[1mWinning Trades by Entry Regime:\u001b[0m\n[INFO   ] Backtester          :   • Weak_Bull_Trend        72\n[INFO   ] Backtester          :   • Strong_Bear_Trend      21\n[INFO   ] Backtester          :   • Strong_Bull_Trend       9\n[INFO   ] Backtester          : Losing trades analysis saved to /Users/<USER>/Desktop/trading_bot_/logs/losing_trades_analysis_20250715_033718.csv\n[INFO   ] Backtester          : \n\u001b[1m\u001b[96m                             LOSING TRADE ANALYSIS                              \u001b[0m\n[INFO   ] Backtester          : \u001b[1m────────────────────────────────────────────────────────────────────────────────\u001b[0m\n[INFO   ] Backtester          : \n\u001b[1mLosing Trades by Entry Regime:\u001b[0m\n[INFO   ] Backtester          :   • Weak_Bull_Trend        48\n[INFO   ] Backtester          :   • Strong_Bull_Trend      15\n[INFO   ] Backtester          :   • Strong_Bear_Trend      15\n[INFO   ] Backtester          : \n\u001b[1mLosing Trades by Strategy:\u001b[0m\n[INFO   ] Backtester          :   • trend_following        78\n[INFO   ] Backtester          : ✅ No configuration fallbacks detected - using intended settings\n[INFO   ] __main__            : Backtest run completed.\n--- Backtest Script Finished ---\n2025-07-15 03:36:39,741 - INFO - Configuring LEGACY system (granular_microstructure + TF-v2)\n2025-07-15 03:36:39,741 - INFO - Legacy config enforced: detector=granular_microstructure, strategy=TF-v2, auto_thresholds=False\n2025-07-15 03:36:39,741 - INFO - System configuration validated: mode=legacy, detector=granular_microstructure, TF-v2=True, TF-v3=False\n/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/features/builder_registry.py:605: FutureWarning: The default fill_method='pad' in Series.pct_change is deprecated and will be removed in a future version. Either fill in any non-leading NA values prior to calling pct_change or specify 'fill_method=None' to not fill NA values.\n  spread_velocity = spread.pct_change()\n"}, "modern_result": {"system_mode": "modern", "performance": {"initial_balance": 10000.0, "winning_trades": 0.0, "losing_trades": 0.0, "avg_win": 201.91, "avg_loss": 201.91, "profit_factor": 92.0, "sharpe": 92.0, "max_drawdown": 3.68, "max_leverage": 50.0}, "trades": {"total_trades": 160, "winning_trades": 90, "losing_trades": 70, "long_trades": 131, "short_trades": 29, "all_pnls": [-27.8134, 428.343, -95.0952, 86.3929, 198.2802, 145.0881, -74.5969, 87.408, 427.6815, -16.1648, 220.4653, 360.635, 13.5361, 396.3692, 162.5868, 267.8941, -27.6301, 301.8761, -55.636, -166.1627, 44.8056, 170.8482, -72.2254, -98.4108, -15.8399, 174.5605, -143.8671, 3.4862, -181.4765, -38.352, 267.9607, -21.0452, -64.3417, 306.7356, -14.2739, 283.1965, 111.2803, -135.5221, -96.8347, 143.5011, 27.3169, 246.905, -121.7766, -92.9144, -141.8349, 123.4579, -2.8899, -83.1393, 248.1929, 29.2804, 97.9515, 14.4366, 65.2518, 612.1667, -160.5663, -2.7951, 176.5876, -281.5258, 12.5802, -50.1941, -32.2566, -91.8314, -25.021, 139.1061, -82.1813, 227.2309, -87.6643, -161.4716, 289.9099, 55.7279, 202.5148, 571.0745, 335.7786, 101.5307, 12.9149, 127.5176, 36.5163, 156.3133, 80.9488, -285.5054, -154.0788, 154.4257, 60.5424, 66.5311, 164.3606, -69.6172, -119.569, -30.1042, 251.9764, 29.2858, 36.4917, -142.0308, 417.5102, -14.9285, -167.1674, 34.6473, 1.4288, -51.1157, 249.4328, -83.0936, -17.9995, 321.3743, -79.7686, 59.6142, 42.87, 26.395, -26.9561, 310.5202, -137.3639, -23.6106, -96.4163, 113.2475, 140.7215, 338.218, 31.2891, 259.5029, -149.4609, -99.0984, -145.289, 394.3916, 456.4167, -208.9552, 534.9529, 30.0565, 137.1172, 89.1792, 702.5188, 258.4016, -66.5878, -46.2171, -44.4208, 114.3589, 394.9074, 79.4119, -140.6645, -115.8679, 132.8318, 475.0785, -188.707, 311.0545, -34.2362, -147.2983, -322.7682, 199.8164, 324.5176, -35.4653, 106.6664, 578.1067, -0.2828, 349.0426, -18.2179, -99.9567, -344.88, -0.2923, 437.0316, -145.8316, 239.2632, 75.5596, 46.4214, -62.2126], "winning_pnls": [428.343, 86.3929, 198.2802, 145.0881, 87.408, 427.6815, 220.4653, 360.635, 13.5361, 396.3692, 162.5868, 267.8941, 301.8761, 44.8056, 170.8482, 174.5605, 3.4862, 267.9607, 306.7356, 283.1965, 111.2803, 143.5011, 27.3169, 246.905, 123.4579, 248.1929, 29.2804, 97.9515, 14.4366, 65.2518, 612.1667, 176.5876, 12.5802, 139.1061, 227.2309, 289.9099, 55.7279, 202.5148, 571.0745, 335.7786, 101.5307, 12.9149, 127.5176, 36.5163, 156.3133, 80.9488, 154.4257, 60.5424, 66.5311, 164.3606, 251.9764, 29.2858, 36.4917, 417.5102, 34.6473, 1.4288, 249.4328, 321.3743, 59.6142, 42.87, 26.395, 310.5202, 113.2475, 140.7215, 338.218, 31.2891, 259.5029, 394.3916, 456.4167, 534.9529, 30.0565, 137.1172, 89.1792, 702.5188, 258.4016, 114.3589, 394.9074, 79.4119, 132.8318, 475.0785, 311.0545, 199.8164, 324.5176, 106.6664, 578.1067, 349.0426, 437.0316, 239.2632, 75.5596, 46.4214], "losing_pnls": [-27.8134, -95.0952, -74.5969, -16.1648, -27.6301, -55.636, -166.1627, -72.2254, -98.4108, -15.8399, -143.8671, -181.4765, -38.352, -21.0452, -64.3417, -14.2739, -135.5221, -96.8347, -121.7766, -92.9144, -141.8349, -2.8899, -83.1393, -160.5663, -2.7951, -281.5258, -50.1941, -32.2566, -91.8314, -25.021, -82.1813, -87.6643, -161.4716, -285.5054, -154.0788, -69.6172, -119.569, -30.1042, -142.0308, -14.9285, -167.1674, -51.1157, -83.0936, -17.9995, -79.7686, -26.9561, -137.3639, -23.6106, -96.4163, -149.4609, -99.0984, -145.289, -208.9552, -66.5878, -46.2171, -44.4208, -140.6645, -115.8679, -188.707, -34.2362, -147.2983, -322.7682, -35.4653, -0.2828, -18.2179, -99.9567, -344.88, -0.2923, -145.8316, -62.2126], "trade_sizes": [0.0883, 0.2136, 0.2227, 0.2205, 0.1307, 0.2137, 0.1846, 0.2172, 0.2014, 0.1388, 0.21, 0.2719, 0.1237, 0.2448, 0.1436, 0.0672, 0.0585, 0.1913, 0.078, 0.0519, 0.0669, 0.1552, 0.1966, 0.0619, 0.1007, 0.069, 0.0597, 0.051, 0.0873, 0.1016, 0.1535, 0.1256, 0.123, 0.09, 0.1575, 0.2023, 0.201, 0.1487, 0.1032, 0.146, 0.148, 0.1332, 0.0665, 0.1148, 0.0818, 0.1134, 0.1669, 0.1001, 0.2064, 0.1206, 0.171, 0.2177, 0.1719, 0.1916, 0.1946, 0.15, 0.1455, 0.1172, 0.1356, 0.3788, 0.2922, 0.1519, 0.1145, 0.183, 0.1307, 0.2162, 0.1124, 0.1534, 0.281, 0.0782, 0.1803, 0.3774, 0.235, 0.1308, 0.1371, 0.1349, 0.2485, 0.2008, 0.3598, 0.3476, 0.2038, 0.2424, 0.122, 0.0908, 0.1169, 0.0288, 0.0934, 0.0739, 0.1609, 0.1467, 0.1646, 0.153, 0.1813, 0.251, 0.1714, 0.2057, 0.299, 0.129, 0.1772, 0.2068, 0.166, 0.2341, 0.3915, 0.232, 0.1667, 0.1532, 0.1617, 0.2035, 0.1815, 0.1466, 0.337, 0.2429, 0.1751, 0.2208, 0.308, 0.1439, 0.1403, 0.1325, 0.2093, 0.3552, 0.2406, 0.1503, 0.1354, 0.1005, 0.1386, 0.2018, 0.2673, 0.0933, 0.0686, 0.1008, 0.088, 0.1601, 0.1843, 0.1249, 0.1344, 0.1048, 0.1283, 0.158, 0.1581, 0.1162, 0.0881, 0.1997, 0.1915, 0.1084, 0.123, 0.1447, 0.1377, 0.16, 0.125, 0.0937, 0.0723, 0.2163, 0.1631, 0.1201, 0.1762, 0.1521, 0.1541, 0.1527, 0.1908, 0.1329], "trades": [{"outcome": "LOSS", "direction": "long", "size": 0.0883, "exit_price": 45251.0, "pnl": -27.8134}, {"outcome": "WIN", "direction": "long", "size": 0.2136, "exit_price": 47044.0, "pnl": 428.343}, {"outcome": "LOSS", "direction": "long", "size": 0.2227, "exit_price": 46661.0, "pnl": -95.0952}, {"outcome": "WIN", "direction": "long", "size": 0.2205, "exit_price": 46679.0, "pnl": 86.3929}, {"outcome": "WIN", "direction": "short", "size": 0.1307, "exit_price": 42934.0, "pnl": 198.2802}, {"outcome": "WIN", "direction": "long", "size": 0.2137, "exit_price": 41752.0, "pnl": 145.0881}, {"outcome": "LOSS", "direction": "long", "size": 0.1846, "exit_price": 42748.0, "pnl": -74.5969}, {"outcome": "WIN", "direction": "long", "size": 0.2172, "exit_price": 45302.0, "pnl": 87.408}, {"outcome": "WIN", "direction": "long", "size": 0.2014, "exit_price": 47691.0, "pnl": 427.6815}, {"outcome": "LOSS", "direction": "long", "size": 0.1388, "exit_price": 47548.0, "pnl": -16.1648}, {"outcome": "WIN", "direction": "long", "size": 0.21, "exit_price": 49921.0, "pnl": 220.4653}, {"outcome": "WIN", "direction": "long", "size": 0.2719, "exit_price": 52224.0, "pnl": 360.635}, {"outcome": "WIN", "direction": "long", "size": 0.1237, "exit_price": 52507.0, "pnl": 13.5361}, {"outcome": "WIN", "direction": "long", "size": 0.2448, "exit_price": 54552.0, "pnl": 396.3692}, {"outcome": "WIN", "direction": "long", "size": 0.1436, "exit_price": 55875.0, "pnl": 162.5868}, {"outcome": "WIN", "direction": "long", "size": 0.0672, "exit_price": 61067.0, "pnl": 267.8941}, {"outcome": "LOSS", "direction": "long", "size": 0.0585, "exit_price": 61262.19, "pnl": -27.6301}, {"outcome": "WIN", "direction": "long", "size": 0.1913, "exit_price": 65235.0, "pnl": 301.8761}, {"outcome": "LOSS", "direction": "long", "size": 0.078, "exit_price": 65713.0, "pnl": -55.636}, {"outcome": "LOSS", "direction": "short", "size": 0.0519, "exit_price": 64795.0, "pnl": -166.1627}, {"outcome": "WIN", "direction": "long", "size": 0.0669, "exit_price": 67016.0, "pnl": 44.8056}, {"outcome": "WIN", "direction": "long", "size": 0.1552, "exit_price": 68563.0, "pnl": 170.8482}, {"outcome": "LOSS", "direction": "long", "size": 0.1966, "exit_price": 69401.0, "pnl": -72.2254}, {"outcome": "LOSS", "direction": "long", "size": 0.0619, "exit_price": 70593.0, "pnl": -98.4108}, {"outcome": "LOSS", "direction": "long", "size": 0.1007, "exit_price": 73422.0, "pnl": -15.8399}, {"outcome": "WIN", "direction": "short", "size": 0.069, "exit_price": 69117.0, "pnl": 174.5605}, {"outcome": "LOSS", "direction": "short", "size": 0.0597, "exit_price": 68771.0, "pnl": -143.8671}, {"outcome": "WIN", "direction": "short", "size": 0.051, "exit_price": 63590.0, "pnl": 3.4862}, {"outcome": "LOSS", "direction": "long", "size": 0.0873, "exit_price": 65263.0, "pnl": -181.4765}, {"outcome": "LOSS", "direction": "short", "size": 0.1016, "exit_price": 64651.0, "pnl": -38.352}, {"outcome": "WIN", "direction": "long", "size": 0.1535, "exit_price": 67156.0, "pnl": 267.9607}, {"outcome": "LOSS", "direction": "long", "size": 0.1256, "exit_price": 70570.0, "pnl": -21.0452}, {"outcome": "LOSS", "direction": "long", "size": 0.123, "exit_price": 70515.0, "pnl": -64.3417}, {"outcome": "WIN", "direction": "short", "size": 0.09, "exit_price": 65992.0, "pnl": 306.7356}, {"outcome": "LOSS", "direction": "long", "size": 0.1575, "exit_price": 67663.0, "pnl": -14.2739}, {"outcome": "WIN", "direction": "long", "size": 0.2023, "exit_price": 69617.0, "pnl": 283.1965}, {"outcome": "WIN", "direction": "long", "size": 0.201, "exit_price": 70730.0, "pnl": 111.2803}, {"outcome": "LOSS", "direction": "long", "size": 0.1487, "exit_price": 71148.0, "pnl": -135.5221}, {"outcome": "LOSS", "direction": "long", "size": 0.1032, "exit_price": 69990.55, "pnl": -96.8347}, {"outcome": "WIN", "direction": "short", "size": 0.146, "exit_price": 67207.0, "pnl": 143.5011}, {"outcome": "WIN", "direction": "short", "size": 0.148, "exit_price": 67189.0, "pnl": 27.3169}, {"outcome": "WIN", "direction": "short", "size": 0.1332, "exit_price": 62993.0, "pnl": 246.905}, {"outcome": "LOSS", "direction": "short", "size": 0.0665, "exit_price": 65859.66, "pnl": -121.7766}, {"outcome": "LOSS", "direction": "long", "size": 0.1148, "exit_price": 64175.0, "pnl": -92.9144}, {"outcome": "LOSS", "direction": "short", "size": 0.0818, "exit_price": 62725.0, "pnl": -141.8349}, {"outcome": "WIN", "direction": "long", "size": 0.1134, "exit_price": 64853.0, "pnl": 123.4579}, {"outcome": "LOSS", "direction": "long", "size": 0.1669, "exit_price": 66391.0, "pnl": -2.8899}, {"outcome": "LOSS", "direction": "short", "size": 0.1001, "exit_price": 58607.0, "pnl": -83.1393}, {"outcome": "WIN", "direction": "long", "size": 0.2064, "exit_price": 60591.0, "pnl": 248.1929}, {"outcome": "WIN", "direction": "long", "size": 0.1206, "exit_price": 63145.0, "pnl": 29.2804}, {"outcome": "WIN", "direction": "long", "size": 0.171, "exit_price": 65136.0, "pnl": 97.9515}, {"outcome": "WIN", "direction": "long", "size": 0.2177, "exit_price": 63041.0, "pnl": 14.4366}, {"outcome": "WIN", "direction": "long", "size": 0.1719, "exit_price": 62736.0, "pnl": 65.2518}, {"outcome": "WIN", "direction": "long", "size": 0.1916, "exit_price": 65717.0, "pnl": 612.1667}, {"outcome": "LOSS", "direction": "long", "size": 0.1946, "exit_price": 65106.0, "pnl": -160.5663}, {"outcome": "LOSS", "direction": "long", "size": 0.15, "exit_price": 66904.0, "pnl": -2.7951}, {"outcome": "WIN", "direction": "long", "size": 0.1455, "exit_price": 69539.0, "pnl": 176.5876}, {"outcome": "LOSS", "direction": "long", "size": 0.1172, "exit_price": 69856.0, "pnl": -281.5258}, {"outcome": "WIN", "direction": "long", "size": 0.1356, "exit_price": 69450.0, "pnl": 12.5802}, {"outcome": "LOSS", "direction": "long", "size": 0.3788, "exit_price": 69025.0, "pnl": -50.1941}, {"outcome": "LOSS", "direction": "long", "size": 0.2922, "exit_price": 68867.0, "pnl": -32.2566}, {"outcome": "LOSS", "direction": "long", "size": 0.1519, "exit_price": 68166.0, "pnl": -91.8314}, {"outcome": "LOSS", "direction": "long", "size": 0.1145, "exit_price": 68998.0, "pnl": -25.021}, {"outcome": "WIN", "direction": "long", "size": 0.183, "exit_price": 71299.0, "pnl": 139.1061}, {"outcome": "LOSS", "direction": "long", "size": 0.1307, "exit_price": 71096.0, "pnl": -82.1813}, {"outcome": "WIN", "direction": "long", "size": 0.2162, "exit_price": 68909.0, "pnl": 227.2309}, {"outcome": "LOSS", "direction": "long", "size": 0.1124, "exit_price": 67431.0, "pnl": -87.6643}, {"outcome": "LOSS", "direction": "long", "size": 0.1534, "exit_price": 60916.0, "pnl": -161.4716}, {"outcome": "WIN", "direction": "long", "size": 0.281, "exit_price": 62977.0, "pnl": 289.9099}, {"outcome": "WIN", "direction": "short", "size": 0.0782, "exit_price": 56387.0, "pnl": 55.7279}, {"outcome": "WIN", "direction": "long", "size": 0.1803, "exit_price": 57689.0, "pnl": 202.5148}, {"outcome": "WIN", "direction": "long", "size": 0.3774, "exit_price": 59493.0, "pnl": 571.0745}, {"outcome": "WIN", "direction": "long", "size": 0.235, "exit_price": 61233.0, "pnl": 335.7786}, {"outcome": "WIN", "direction": "long", "size": 0.1308, "exit_price": 63488.0, "pnl": 101.5307}, {"outcome": "WIN", "direction": "long", "size": 0.1371, "exit_price": 64876.0, "pnl": 12.9149}, {"outcome": "WIN", "direction": "long", "size": 0.1349, "exit_price": 66859.0, "pnl": 127.5176}, {"outcome": "WIN", "direction": "long", "size": 0.2485, "exit_price": 67635.0, "pnl": 36.5163}, {"outcome": "WIN", "direction": "long", "size": 0.2008, "exit_price": 67958.0, "pnl": 156.3133}, {"outcome": "WIN", "direction": "long", "size": 0.3598, "exit_price": 68432.0, "pnl": 80.9488}, {"outcome": "LOSS", "direction": "long", "size": 0.3476, "exit_price": 68526.0, "pnl": -285.5054}, {"outcome": "LOSS", "direction": "long", "size": 0.2038, "exit_price": 67478.49, "pnl": -154.0788}, {"outcome": "WIN", "direction": "long", "size": 0.2424, "exit_price": 69310.0, "pnl": 154.4257}, {"outcome": "WIN", "direction": "short", "size": 0.122, "exit_price": 66076.0, "pnl": 60.5424}, {"outcome": "WIN", "direction": "short", "size": 0.0908, "exit_price": 60818.0, "pnl": 66.5311}, {"outcome": "WIN", "direction": "short", "size": 0.1169, "exit_price": 56191.0, "pnl": 164.3606}, {"outcome": "LOSS", "direction": "short", "size": 0.0288, "exit_price": 56434.0, "pnl": -69.6172}, {"outcome": "LOSS", "direction": "long", "size": 0.0934, "exit_price": 55547.0, "pnl": -119.569}, {"outcome": "LOSS", "direction": "long", "size": 0.0739, "exit_price": 60759.0, "pnl": -30.1042}, {"outcome": "WIN", "direction": "long", "size": 0.1609, "exit_price": 61076.0, "pnl": 251.9764}, {"outcome": "WIN", "direction": "long", "size": 0.1467, "exit_price": 59413.0, "pnl": 29.2858}, {"outcome": "WIN", "direction": "long", "size": 0.1646, "exit_price": 60861.0, "pnl": 36.4917}, {"outcome": "LOSS", "direction": "long", "size": 0.153, "exit_price": 60714.0, "pnl": -142.0308}, {"outcome": "WIN", "direction": "long", "size": 0.1813, "exit_price": 64089.0, "pnl": 417.5102}, {"outcome": "LOSS", "direction": "long", "size": 0.251, "exit_price": 64254.0, "pnl": -14.9285}, {"outcome": "LOSS", "direction": "short", "size": 0.1714, "exit_price": 59541.0, "pnl": -167.1674}, {"outcome": "WIN", "direction": "long", "size": 0.2057, "exit_price": 60524.0, "pnl": 34.6473}, {"outcome": "WIN", "direction": "long", "size": 0.299, "exit_price": 59103.0, "pnl": 1.4288}, {"outcome": "LOSS", "direction": "short", "size": 0.129, "exit_price": 54297.0, "pnl": -51.1157}, {"outcome": "WIN", "direction": "long", "size": 0.1772, "exit_price": 56800.0, "pnl": 249.4328}, {"outcome": "LOSS", "direction": "long", "size": 0.2068, "exit_price": 56790.0, "pnl": -83.0936}, {"outcome": "LOSS", "direction": "long", "size": 0.166, "exit_price": 57832.0, "pnl": -17.9995}, {"outcome": "WIN", "direction": "long", "size": 0.2341, "exit_price": 59849.0, "pnl": 321.3743}, {"outcome": "LOSS", "direction": "long", "size": 0.3915, "exit_price": 59807.0, "pnl": -79.7686}, {"outcome": "WIN", "direction": "long", "size": 0.232, "exit_price": 60509.0, "pnl": 59.6142}, {"outcome": "WIN", "direction": "long", "size": 0.1667, "exit_price": 60244.0, "pnl": 42.87}, {"outcome": "WIN", "direction": "long", "size": 0.1532, "exit_price": 62768.0, "pnl": 26.395}, {"outcome": "LOSS", "direction": "long", "size": 0.1617, "exit_price": 62930.0, "pnl": -26.9561}, {"outcome": "WIN", "direction": "long", "size": 0.2035, "exit_price": 66032.0, "pnl": 310.5202}, {"outcome": "LOSS", "direction": "short", "size": 0.1815, "exit_price": 61886.0, "pnl": -137.3639}, {"outcome": "LOSS", "direction": "long", "size": 0.1466, "exit_price": 61850.0, "pnl": -23.6106}, {"outcome": "LOSS", "direction": "long", "size": 0.337, "exit_price": 63209.0, "pnl": -96.4163}, {"outcome": "WIN", "direction": "long", "size": 0.2429, "exit_price": 63389.0, "pnl": 113.2475}, {"outcome": "WIN", "direction": "long", "size": 0.1751, "exit_price": 63086.0, "pnl": 140.7215}, {"outcome": "WIN", "direction": "long", "size": 0.2208, "exit_price": 65660.0, "pnl": 338.218}, {"outcome": "WIN", "direction": "long", "size": 0.308, "exit_price": 65785.0, "pnl": 31.2891}, {"outcome": "WIN", "direction": "long", "size": 0.1439, "exit_price": 67839.0, "pnl": 259.5029}, {"outcome": "LOSS", "direction": "long", "size": 0.1403, "exit_price": 66943.0, "pnl": -149.4609}, {"outcome": "LOSS", "direction": "long", "size": 0.1325, "exit_price": 68113.0, "pnl": -99.0984}, {"outcome": "LOSS", "direction": "long", "size": 0.2093, "exit_price": 67632.0, "pnl": -145.289}, {"outcome": "WIN", "direction": "long", "size": 0.3552, "exit_price": 69799.0, "pnl": 394.3916}, {"outcome": "WIN", "direction": "long", "size": 0.2406, "exit_price": 71919.0, "pnl": 456.4167}, {"outcome": "LOSS", "direction": "long", "size": 0.1503, "exit_price": 72194.0, "pnl": -208.9552}, {"outcome": "WIN", "direction": "long", "size": 0.1354, "exit_price": 73191.0, "pnl": 534.9529}, {"outcome": "WIN", "direction": "long", "size": 0.1005, "exit_price": 74757.0, "pnl": 30.0565}, {"outcome": "WIN", "direction": "long", "size": 0.1386, "exit_price": 75860.0, "pnl": 137.1172}, {"outcome": "WIN", "direction": "long", "size": 0.2018, "exit_price": 76499.0, "pnl": 89.1792}, {"outcome": "WIN", "direction": "long", "size": 0.2673, "exit_price": 79970.0, "pnl": 702.5188}, {"outcome": "WIN", "direction": "long", "size": 0.0933, "exit_price": 88782.0, "pnl": 258.4016}, {"outcome": "LOSS", "direction": "long", "size": 0.0686, "exit_price": 88586.0, "pnl": -66.5878}, {"outcome": "LOSS", "direction": "long", "size": 0.1008, "exit_price": 90780.0, "pnl": -46.2171}, {"outcome": "LOSS", "direction": "long", "size": 0.088, "exit_price": 91074.0, "pnl": -44.4208}, {"outcome": "WIN", "direction": "long", "size": 0.1601, "exit_price": 92119.0, "pnl": 114.3589}, {"outcome": "WIN", "direction": "long", "size": 0.1843, "exit_price": 94424.0, "pnl": 394.9074}, {"outcome": "WIN", "direction": "long", "size": 0.1249, "exit_price": 98740.0, "pnl": 79.4119}, {"outcome": "LOSS", "direction": "long", "size": 0.1344, "exit_price": 97905.0, "pnl": -140.6645}, {"outcome": "LOSS", "direction": "short", "size": 0.1048, "exit_price": 93417.0, "pnl": -115.8679}, {"outcome": "WIN", "direction": "long", "size": 0.1283, "exit_price": 95431.0, "pnl": 132.8318}, {"outcome": "WIN", "direction": "long", "size": 0.158, "exit_price": 98080.0, "pnl": 475.0785}, {"outcome": "LOSS", "direction": "long", "size": 0.1581, "exit_price": 96942.0, "pnl": -188.707}, {"outcome": "WIN", "direction": "long", "size": 0.1162, "exit_price": 101599.0, "pnl": 311.0545}, {"outcome": "LOSS", "direction": "short", "size": 0.0881, "exit_price": 99707.0, "pnl": -34.2362}, {"outcome": "LOSS", "direction": "long", "size": 0.1997, "exit_price": 99046.0, "pnl": -147.2983}, {"outcome": "LOSS", "direction": "long", "size": 0.1915, "exit_price": 99221.34, "pnl": -322.7682}, {"outcome": "WIN", "direction": "short", "size": 0.1084, "exit_price": 95961.0, "pnl": 199.8164}, {"outcome": "WIN", "direction": "long", "size": 0.123, "exit_price": 101003.0, "pnl": 324.5176}, {"outcome": "LOSS", "direction": "long", "size": 0.1447, "exit_price": 101520.0, "pnl": -35.4653}, {"outcome": "WIN", "direction": "long", "size": 0.1377, "exit_price": 102230.0, "pnl": 106.6664}, {"outcome": "WIN", "direction": "long", "size": 0.16, "exit_price": 106470.0, "pnl": 578.1067}, {"outcome": "LOSS", "direction": "long", "size": 0.125, "exit_price": 106799.0, "pnl": -0.2828}, {"outcome": "WIN", "direction": "short", "size": 0.0937, "exit_price": 100700.0, "pnl": 349.0426}, {"outcome": "LOSS", "direction": "short", "size": 0.0723, "exit_price": 96670.0, "pnl": -18.2179}, {"outcome": "LOSS", "direction": "long", "size": 0.2163, "exit_price": 98353.0, "pnl": -99.9567}, {"outcome": "LOSS", "direction": "long", "size": 0.1631, "exit_price": 95342.0, "pnl": -344.88}, {"outcome": "LOSS", "direction": "short", "size": 0.1201, "exit_price": 94311.0, "pnl": -0.2923}, {"outcome": "WIN", "direction": "long", "size": 0.1762, "exit_price": 98721.0, "pnl": 437.0316}, {"outcome": "LOSS", "direction": "long", "size": 0.1521, "exit_price": 97660.0, "pnl": -145.8316}, {"outcome": "WIN", "direction": "short", "size": 0.1541, "exit_price": 94688.0, "pnl": 239.2632}, {"outcome": "WIN", "direction": "short", "size": 0.1527, "exit_price": 92166.0, "pnl": 75.5596}, {"outcome": "WIN", "direction": "long", "size": 0.1908, "exit_price": 94906.0, "pnl": 46.4214}, {"outcome": "LOSS", "direction": "long", "size": 0.1329, "exit_price": 93526.0, "pnl": -62.2126}], "total_pnl": 11492.244199999996, "avg_pnl": 71.82652625, "median_pnl": 29.671149999999997, "pnl_std": 197.14388290643072, "avg_win": 201.90701444444446, "largest_win": 702.5188, "avg_loss": -95.41981571428572, "largest_loss": -344.88, "avg_size": 0.163384375, "median_size": 0.15284999999999999, "max_size": 0.3915}, "portfolio": {"avg_leverage": 3.0, "max_leverage": 3.0, "min_leverage": 3.0, "leverage_std": 0.0, "initial_balance": 10.0, "final_balance": 20674.26, "balance_progression": [14459.62, 14457.88, 20543.1, 20543.1, 15540.29, 15538.83, 20389.58, 20389.58, 15440.29, 15441.74, 15443.2, 20628.58, 20628.58, 20699.67, 20699.67, 20699.67, 20740.41, 20740.41, 16574.61, 20674.26]}, "raw_output": "2: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  hour_start = pd.Timestamp(timestamp).floor('H')\n/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:432: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  hour_start = pd.Timestamp(timestamp).floor('H')\n/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:432: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  hour_start = pd.Timestamp(timestamp).floor('H')\n/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:432: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  hour_start = pd.Timestamp(timestamp).floor('H')\n/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:432: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  hour_start = pd.Timestamp(timestamp).floor('H')\n/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:432: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  hour_start = pd.Timestamp(timestamp).floor('H')\n/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:432: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  hour_start = pd.Timestamp(timestamp).floor('H')\n/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:432: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  hour_start = pd.Timestamp(timestamp).floor('H')\n/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:432: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  hour_start = pd.Timestamp(timestamp).floor('H')\n/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:432: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  hour_start = pd.Timestamp(timestamp).floor('H')\n/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:432: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  hour_start = pd.Timestamp(timestamp).floor('H')\n/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:432: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  hour_start = pd.Timestamp(timestamp).floor('H')\n"}, "comparison": {"roi_gap": 0, "legacy_performance": {"initial_balance": 10000.0, "winning_trades": 0.0, "losing_trades": 0.0, "avg_win": 413.73, "avg_loss": 413.73, "profit_factor": 92.0, "sharpe": 92.0, "max_drawdown": 8.98, "max_leverage": 50.0}, "modern_performance": {"initial_balance": 10000.0, "winning_trades": 0.0, "losing_trades": 0.0, "avg_win": 201.91, "avg_loss": 201.91, "profit_factor": 92.0, "sharpe": 92.0, "max_drawdown": 3.68, "max_leverage": 50.0}, "legacy_trades": {"total_trades": 180, "winning_trades": 102, "losing_trades": 78, "long_trades": 134, "short_trades": 46, "all_pnls": [544.0116, -53.0065, -15.4387, 189.9053, 253.0855, 61.4621, 54.3593, 635.3862, -84.1629, 341.2182, 375.4538, -210.1987, 658.3439, 405.2899, 308.3559, 672.9156, -164.4589, 133.0797, 484.8494, -94.6526, 456.9437, 286.2506, -133.9257, 698.8012, -99.8654, -164.5239, -111.2701, 397.6888, -253.6621, -230.2281, 516.286, -229.1629, -166.937, 20.7353, 478.341, -42.5674, -88.1434, 1068.564, -190.471, -28.808, 543.0007, -317.2289, 241.0365, -195.4726, -65.6244, 235.9733, 53.0647, 339.9742, -298.7241, -0.3495, 436.0417, -6.2357, 199.5851, -8.1848, -277.615, -270.3664, 230.2098, 26.4828, 237.627, 26.874, 174.797, 955.5663, -291.3997, -7.7732, 727.2732, 543.879, -387.6612, 136.6948, 30.6153, -24.2771, 352.9538, 91.6922, 328.717, -264.4431, -521.9843, -304.0068, 366.2694, 676.5012, 496.4067, -312.6118, 230.0287, -210.9605, 1021.9001, 457.6819, 383.9251, -692.2295, -334.9984, 381.2686, -223.2887, 302.8992, 34.8921, 301.2888, 306.244, 229.8013, 129.7657, -182.7372, -387.706, 260.6415, -125.204, -7.7834, 23.6498, 82.2884, 116.0028, -283.0581, 55.0243, 988.608, 133.4603, -423.8095, 358.3494, 2.8778, 718.9898, -158.8648, -60.628, 731.6093, -166.2781, 132.1899, 121.9619, 738.972, -222.4541, 891.4081, 6.823, -87.3096, -725.5346, 679.3681, 513.6867, 667.6007, -8.2026, 8.4559, -167.0341, -476.241, 495.7945, 501.9995, 747.1414, -783.7138, -474.5798, 873.0635, 728.2496, 204.1472, -420.0568, 1775.0558, 42.6645, 1116.9469, -98.9411, -220.7256, -115.3631, -139.4088, 1.9163, 146.0043, 1166.9452, -382.8678, -424.7006, -180.2416, -151.0343, -262.2517, -240.0241, -321.7436, -242.9097, 529.4108, 66.5126, -299.199, -649.1728, 589.4315, -460.0932, 325.0944, -309.4887, -18.3644, 265.8184, -207.6833, 665.5625, -0.7842, 1579.6696, 299.9028, 330.1457, 1008.1466, -440.5552, -944.3678, 157.332, 364.6327, 274.5684, 439.7366], "winning_pnls": [544.0116, 189.9053, 253.0855, 61.4621, 54.3593, 635.3862, 341.2182, 375.4538, 658.3439, 405.2899, 308.3559, 672.9156, 133.0797, 484.8494, 456.9437, 286.2506, 698.8012, 397.6888, 516.286, 20.7353, 478.341, 1068.564, 543.0007, 241.0365, 235.9733, 53.0647, 339.9742, 436.0417, 199.5851, 230.2098, 26.4828, 237.627, 26.874, 174.797, 955.5663, 727.2732, 543.879, 136.6948, 30.6153, 352.9538, 91.6922, 328.717, 366.2694, 676.5012, 496.4067, 230.0287, 1021.9001, 457.6819, 383.9251, 381.2686, 302.8992, 34.8921, 301.2888, 306.244, 229.8013, 129.7657, 260.6415, 23.6498, 82.2884, 116.0028, 55.0243, 988.608, 133.4603, 358.3494, 2.8778, 718.9898, 731.6093, 132.1899, 121.9619, 738.972, 891.4081, 6.823, 679.3681, 513.6867, 667.6007, 8.4559, 495.7945, 501.9995, 747.1414, 873.0635, 728.2496, 204.1472, 1775.0558, 42.6645, 1116.9469, 1.9163, 146.0043, 1166.9452, 529.4108, 66.5126, 589.4315, 325.0944, 265.8184, 665.5625, 1579.6696, 299.9028, 330.1457, 1008.1466, 157.332, 364.6327, 274.5684, 439.7366], "losing_pnls": [-53.0065, -15.4387, -84.1629, -210.1987, -164.4589, -94.6526, -133.9257, -99.8654, -164.5239, -111.2701, -253.6621, -230.2281, -229.1629, -166.937, -42.5674, -88.1434, -190.471, -28.808, -317.2289, -195.4726, -65.6244, -298.7241, -0.3495, -6.2357, -8.1848, -277.615, -270.3664, -291.3997, -7.7732, -387.6612, -24.2771, -264.4431, -521.9843, -304.0068, -312.6118, -210.9605, -692.2295, -334.9984, -223.2887, -182.7372, -387.706, -125.204, -7.7834, -283.0581, -423.8095, -158.8648, -60.628, -166.2781, -222.4541, -87.3096, -725.5346, -8.2026, -167.0341, -476.241, -783.7138, -474.5798, -420.0568, -98.9411, -220.7256, -115.3631, -139.4088, -382.8678, -424.7006, -180.2416, -151.0343, -262.2517, -240.0241, -321.7436, -242.9097, -299.199, -649.1728, -460.0932, -309.4887, -18.3644, -207.6833, -0.7842, -440.5552, -944.3678], "trade_sizes": [0.2713, 0.2421, 0.2493, 0.1993, 0.3728, 0.4145, 0.44, 0.3789, 0.3251, 0.325, 0.283, 0.3124, 0.4066, 0.358, 0.2723, 0.2531, 0.1514, 0.1401, 0.2911, 0.1766, 0.104, 0.26, 0.3646, 0.2552, 0.2647, 0.2432, 0.2456, 0.1571, 0.1941, 0.17, 0.16, 0.1528, 0.1556, 0.1965, 0.2741, 0.2541, 0.2611, 0.2562, 0.2419, 0.3178, 0.388, 0.4299, 0.3394, 0.3489, 0.2693, 0.2401, 0.2875, 0.1834, 0.1631, 0.201, 0.2709, 0.3601, 0.298, 0.258, 0.2304, 0.3031, 0.2831, 0.3352, 0.415, 0.4052, 0.4605, 0.381, 0.4007, 0.417, 0.445, 0.3681, 0.3213, 0.3598, 0.524, 0.4275, 0.5496, 0.4314, 0.4323, 0.4204, 0.4575, 0.3337, 0.3771, 0.6557, 0.3802, 0.3836, 0.4016, 0.3765, 0.519, 0.5048, 0.4715, 0.4175, 0.3552, 0.4034, 0.5727, 0.3891, 0.5285, 0.3828, 0.4807, 0.4631, 0.3079, 0.2965, 0.1602, 0.2162, 0.259, 0.2955, 0.3781, 0.4122, 0.5231, 0.418, 0.4175, 0.4292, 0.5692, 0.4346, 0.4546, 0.6022, 0.5108, 0.5176, 0.559, 0.533, 0.8162, 0.5144, 0.4743, 0.4369, 0.5346, 0.5842, 0.5257, 0.5423, 0.8977, 0.7344, 0.6393, 0.6755, 0.6596, 0.5069, 0.6278, 0.6733, 0.9345, 0.7394, 0.6312, 0.5607, 0.5584, 0.4466, 0.3049, 0.4409, 0.5115, 0.5729, 0.4812, 0.403, 0.3178, 0.2273, 0.2517, 0.2763, 0.463, 0.361, 0.4023, 0.3897, 0.3732, 0.3875, 0.3239, 0.2928, 0.3585, 0.4767, 0.3556, 0.2604, 0.2565, 0.4056, 0.4767, 0.3197, 0.3334, 0.3146, 0.3663, 0.4003, 0.5284, 0.4136, 0.4077, 0.3466, 0.3091, 0.3166, 0.3173, 0.3959, 0.3336, 0.3641, 0.346, 0.4278, 0.555, 0.4338], "trades": [{"outcome": "WIN", "direction": "long", "size": 0.2713, "exit_price": 47044.0, "pnl": 544.0116}, {"outcome": "LOSS", "direction": "long", "size": 0.2421, "exit_price": 46661.0, "pnl": -53.0065}, {"outcome": "LOSS", "direction": "long", "size": 0.2493, "exit_price": 46679.0, "pnl": -15.4387}, {"outcome": "WIN", "direction": "short", "size": 0.1993, "exit_price": 43496.0, "pnl": 189.9053}, {"outcome": "WIN", "direction": "long", "size": 0.3728, "exit_price": 41752.0, "pnl": 253.0855}, {"outcome": "WIN", "direction": "long", "size": 0.4145, "exit_price": 43301.0, "pnl": 61.4621}, {"outcome": "WIN", "direction": "long", "size": 0.44, "exit_price": 45302.0, "pnl": 54.3593}, {"outcome": "WIN", "direction": "long", "size": 0.3789, "exit_price": 47244.0, "pnl": 635.3862}, {"outcome": "LOSS", "direction": "long", "size": 0.3251, "exit_price": 47292.0, "pnl": -84.1629}, {"outcome": "WIN", "direction": "long", "size": 0.325, "exit_price": 49921.0, "pnl": 341.2182}, {"outcome": "WIN", "direction": "long", "size": 0.283, "exit_price": 52224.0, "pnl": 375.4538}, {"outcome": "LOSS", "direction": "long", "size": 0.3124, "exit_price": 51714.0, "pnl": -210.1987}, {"outcome": "WIN", "direction": "long", "size": 0.4066, "exit_price": 54552.0, "pnl": 658.3439}, {"outcome": "WIN", "direction": "long", "size": 0.358, "exit_price": 55875.0, "pnl": 405.2899}, {"outcome": "WIN", "direction": "long", "size": 0.2723, "exit_price": 57301.0, "pnl": 308.3559}, {"outcome": "WIN", "direction": "long", "size": 0.2531, "exit_price": 61067.0, "pnl": 672.9156}, {"outcome": "LOSS", "direction": "long", "size": 0.1514, "exit_price": 61516.0, "pnl": -164.4589}, {"outcome": "WIN", "direction": "long", "size": 0.1401, "exit_price": 61470.0, "pnl": 133.0797}, {"outcome": "WIN", "direction": "long", "size": 0.2911, "exit_price": 65322.0, "pnl": 484.8494}, {"outcome": "LOSS", "direction": "long", "size": 0.1766, "exit_price": 67005.0, "pnl": -94.6526}, {"outcome": "WIN", "direction": "long", "size": 0.104, "exit_price": 66546.0, "pnl": 456.9437}, {"outcome": "WIN", "direction": "long", "size": 0.26, "exit_price": 68563.0, "pnl": 286.2506}, {"outcome": "LOSS", "direction": "long", "size": 0.3646, "exit_price": 69401.0, "pnl": -133.9257}, {"outcome": "WIN", "direction": "long", "size": 0.2552, "exit_price": 72203.0, "pnl": 698.8012}, {"outcome": "LOSS", "direction": "long", "size": 0.2647, "exit_price": 71785.0, "pnl": -99.8654}, {"outcome": "LOSS", "direction": "long", "size": 0.2432, "exit_price": 72904.0, "pnl": -164.5239}, {"outcome": "LOSS", "direction": "long", "size": 0.2456, "exit_price": 72897.0, "pnl": -111.2701}, {"outcome": "WIN", "direction": "short", "size": 0.1571, "exit_price": 69117.0, "pnl": 397.6888}, {"outcome": "LOSS", "direction": "short", "size": 0.1941, "exit_price": 67669.0, "pnl": -253.6621}, {"outcome": "LOSS", "direction": "short", "size": 0.17, "exit_price": 65012.0, "pnl": -230.2281}, {"outcome": "WIN", "direction": "short", "size": 0.16, "exit_price": 61655.0, "pnl": 516.286}, {"outcome": "LOSS", "direction": "short", "size": 0.1528, "exit_price": 62994.0, "pnl": -229.1629}, {"outcome": "LOSS", "direction": "long", "size": 0.1556, "exit_price": 65714.0, "pnl": -166.937}, {"outcome": "WIN", "direction": "short", "size": 0.1965, "exit_price": 64167.0, "pnl": 20.7353}, {"outcome": "WIN", "direction": "long", "size": 0.2741, "exit_price": 67156.0, "pnl": 478.341}, {"outcome": "LOSS", "direction": "long", "size": 0.2541, "exit_price": 70570.0, "pnl": -42.5674}, {"outcome": "LOSS", "direction": "long", "size": 0.2611, "exit_price": 70703.0, "pnl": -88.1434}, {"outcome": "WIN", "direction": "short", "size": 0.2562, "exit_price": 64907.99, "pnl": 1068.564}, {"outcome": "LOSS", "direction": "short", "size": 0.2419, "exit_price": 66174.0, "pnl": -190.471}, {"outcome": "LOSS", "direction": "long", "size": 0.3178, "exit_price": 67663.0, "pnl": -28.808}, {"outcome": "WIN", "direction": "long", "size": 0.388, "exit_price": 69617.0, "pnl": 543.0007}, {"outcome": "LOSS", "direction": "long", "size": 0.4299, "exit_price": 69438.0, "pnl": -317.2289}, {"outcome": "WIN", "direction": "long", "size": 0.3394, "exit_price": 71487.0, "pnl": 241.0365}, {"outcome": "LOSS", "direction": "long", "size": 0.3489, "exit_price": 70819.0, "pnl": -195.4726}, {"outcome": "LOSS", "direction": "short", "size": 0.2693, "exit_price": 70221.0, "pnl": -65.6244}, {"outcome": "WIN", "direction": "short", "size": 0.2401, "exit_price": 67207.0, "pnl": 235.9733}, {"outcome": "WIN", "direction": "short", "size": 0.2875, "exit_price": 67189.0, "pnl": 53.0647}, {"outcome": "WIN", "direction": "short", "size": 0.1834, "exit_price": 62993.0, "pnl": 339.9742}, {"outcome": "LOSS", "direction": "short", "size": 0.1631, "exit_price": 65859.66, "pnl": -298.7241}, {"outcome": "LOSS", "direction": "short", "size": 0.201, "exit_price": 63058.0, "pnl": -0.3495}, {"outcome": "WIN", "direction": "long", "size": 0.2709, "exit_price": 65204.0, "pnl": 436.0417}, {"outcome": "LOSS", "direction": "long", "size": 0.3601, "exit_price": 66391.0, "pnl": -6.2357}, {"outcome": "WIN", "direction": "short", "size": 0.298, "exit_price": 59512.0, "pnl": 199.5851}, {"outcome": "LOSS", "direction": "short", "size": 0.258, "exit_price": 57491.0, "pnl": -8.1848}, {"outcome": "LOSS", "direction": "short", "size": 0.2304, "exit_price": 58607.0, "pnl": -277.615}, {"outcome": "LOSS", "direction": "short", "size": 0.3031, "exit_price": 60592.0, "pnl": -270.3664}, {"outcome": "WIN", "direction": "long", "size": 0.2831, "exit_price": 63585.0, "pnl": 230.2098}, {"outcome": "WIN", "direction": "long", "size": 0.3352, "exit_price": 64222.0, "pnl": 26.4828}, {"outcome": "WIN", "direction": "long", "size": 0.415, "exit_price": 65136.0, "pnl": 237.627}, {"outcome": "WIN", "direction": "long", "size": 0.4052, "exit_price": 63041.0, "pnl": 26.874}, {"outcome": "WIN", "direction": "long", "size": 0.4605, "exit_price": 62736.0, "pnl": 174.797}, {"outcome": "WIN", "direction": "long", "size": 0.381, "exit_price": 65028.0, "pnl": 955.5663}, {"outcome": "LOSS", "direction": "long", "size": 0.4007, "exit_price": 65106.0, "pnl": -291.3997}, {"outcome": "LOSS", "direction": "long", "size": 0.417, "exit_price": 66904.0, "pnl": -7.7732}, {"outcome": "WIN", "direction": "long", "size": 0.445, "exit_price": 69960.0, "pnl": 727.2732}, {"outcome": "WIN", "direction": "long", "size": 0.3681, "exit_price": 71394.49, "pnl": 543.879}, {"outcome": "LOSS", "direction": "long", "size": 0.3213, "exit_price": 70163.0, "pnl": -387.6612}, {"outcome": "WIN", "direction": "long", "size": 0.3598, "exit_price": 70253.0, "pnl": 136.6948}, {"outcome": "WIN", "direction": "long", "size": 0.524, "exit_price": 69216.0, "pnl": 30.6153}, {"outcome": "LOSS", "direction": "long", "size": 0.4275, "exit_price": 68714.0, "pnl": -24.2771}, {"outcome": "WIN", "direction": "long", "size": 0.5496, "exit_price": 69859.0, "pnl": 352.9538}, {"outcome": "WIN", "direction": "long", "size": 0.4314, "exit_price": 69093.0, "pnl": 91.6922}, {"outcome": "WIN", "direction": "long", "size": 0.4323, "exit_price": 71299.0, "pnl": 328.717}, {"outcome": "LOSS", "direction": "long", "size": 0.4204, "exit_price": 71096.0, "pnl": -264.4431}, {"outcome": "LOSS", "direction": "short", "size": 0.4575, "exit_price": 68910.0, "pnl": -521.9843}, {"outcome": "LOSS", "direction": "long", "size": 0.3337, "exit_price": 67735.0, "pnl": -304.0068}, {"outcome": "WIN", "direction": "short", "size": 0.3771, "exit_price": 60917.0, "pnl": 366.2694}, {"outcome": "WIN", "direction": "long", "size": 0.6557, "exit_price": 62977.0, "pnl": 676.5012}, {"outcome": "WIN", "direction": "short", "size": 0.3802, "exit_price": 57215.0, "pnl": 496.4067}, {"outcome": "LOSS", "direction": "short", "size": 0.3836, "exit_price": 57306.0, "pnl": -312.6118}, {"outcome": "WIN", "direction": "short", "size": 0.4016, "exit_price": 57207.0, "pnl": 230.0287}, {"outcome": "LOSS", "direction": "long", "size": 0.3765, "exit_price": 57403.0, "pnl": -210.9605}, {"outcome": "WIN", "direction": "long", "size": 0.519, "exit_price": 59949.0, "pnl": 1021.9001}, {"outcome": "WIN", "direction": "long", "size": 0.5048, "exit_price": 61233.0, "pnl": 457.6819}, {"outcome": "WIN", "direction": "long", "size": 0.4715, "exit_price": 63526.0, "pnl": 383.9251}, {"outcome": "LOSS", "direction": "long", "size": 0.4175, "exit_price": 62911.0, "pnl": -692.2295}, {"outcome": "LOSS", "direction": "long", "size": 0.3552, "exit_price": 64741.0, "pnl": -334.9984}, {"outcome": "WIN", "direction": "long", "size": 0.4034, "exit_price": 66859.0, "pnl": 381.2686}, {"outcome": "LOSS", "direction": "long", "size": 0.5727, "exit_price": 67098.0, "pnl": -223.2887}, {"outcome": "WIN", "direction": "long", "size": 0.3891, "exit_price": 67958.0, "pnl": 302.8992}, {"outcome": "WIN", "direction": "long", "size": 0.5285, "exit_price": 68273.0, "pnl": 34.8921}, {"outcome": "WIN", "direction": "long", "size": 0.3828, "exit_price": 68685.0, "pnl": 301.2888}, {"outcome": "WIN", "direction": "long", "size": 0.4807, "exit_price": 69310.0, "pnl": 306.244}, {"outcome": "WIN", "direction": "short", "size": 0.4631, "exit_price": 66076.0, "pnl": 229.8013}, {"outcome": "WIN", "direction": "short", "size": 0.3079, "exit_price": 60906.0, "pnl": 129.7657}, {"outcome": "LOSS", "direction": "short", "size": 0.2965, "exit_price": 56191.0, "pnl": -182.7372}, {"outcome": "LOSS", "direction": "short", "size": 0.1602, "exit_price": 56434.0, "pnl": -387.706}, {"outcome": "WIN", "direction": "short", "size": 0.2162, "exit_price": 55548.0, "pnl": 260.6415}, {"outcome": "LOSS", "direction": "long", "size": 0.259, "exit_price": 60683.0, "pnl": -125.204}, {"outcome": "LOSS", "direction": "long", "size": 0.2955, "exit_price": 60865.0, "pnl": -7.7834}, {"outcome": "WIN", "direction": "long", "size": 0.3781, "exit_price": 61076.0, "pnl": 23.6498}, {"outcome": "WIN", "direction": "long", "size": 0.4122, "exit_price": 59413.0, "pnl": 82.2884}, {"outcome": "WIN", "direction": "long", "size": 0.5231, "exit_price": 60861.0, "pnl": 116.0028}, {"outcome": "LOSS", "direction": "long", "size": 0.418, "exit_price": 60965.0, "pnl": -283.0581}, {"outcome": "WIN", "direction": "long", "size": 0.4175, "exit_price": 60727.0, "pnl": 55.0243}, {"outcome": "WIN", "direction": "long", "size": 0.4292, "exit_price": 64089.0, "pnl": 988.608}, {"outcome": "WIN", "direction": "long", "size": 0.5692, "exit_price": 64465.0, "pnl": 133.4603}, {"outcome": "LOSS", "direction": "short", "size": 0.4346, "exit_price": 59541.0, "pnl": -423.8095}, {"outcome": "WIN", "direction": "short", "size": 0.4546, "exit_price": 59487.0, "pnl": 358.3494}, {"outcome": "WIN", "direction": "long", "size": 0.6022, "exit_price": 59103.0, "pnl": 2.8778}, {"outcome": "WIN", "direction": "long", "size": 0.5108, "exit_price": 56800.0, "pnl": 718.9898}, {"outcome": "LOSS", "direction": "long", "size": 0.5176, "exit_price": 56885.0, "pnl": -158.8648}, {"outcome": "LOSS", "direction": "long", "size": 0.559, "exit_price": 57832.0, "pnl": -60.628}, {"outcome": "WIN", "direction": "long", "size": 0.533, "exit_price": 59849.0, "pnl": 731.6093}, {"outcome": "LOSS", "direction": "long", "size": 0.8162, "exit_price": 59807.0, "pnl": -166.2781}, {"outcome": "WIN", "direction": "long", "size": 0.5144, "exit_price": 60509.0, "pnl": 132.1899}, {"outcome": "WIN", "direction": "long", "size": 0.4743, "exit_price": 60244.0, "pnl": 121.9619}, {"outcome": "WIN", "direction": "long", "size": 0.4369, "exit_price": 63877.0, "pnl": 738.972}, {"outcome": "LOSS", "direction": "long", "size": 0.5346, "exit_price": 63459.0, "pnl": -222.4541}, {"outcome": "WIN", "direction": "long", "size": 0.5842, "exit_price": 66032.0, "pnl": 891.4081}, {"outcome": "WIN", "direction": "short", "size": 0.5257, "exit_price": 61116.0, "pnl": 6.823}, {"outcome": "LOSS", "direction": "long", "size": 0.5423, "exit_price": 61850.0, "pnl": -87.3096}, {"outcome": "LOSS", "direction": "long", "size": 0.8977, "exit_price": 63570.0, "pnl": -725.5346}, {"outcome": "WIN", "direction": "long", "size": 0.7344, "exit_price": 63848.0, "pnl": 679.3681}, {"outcome": "WIN", "direction": "long", "size": 0.6393, "exit_price": 63086.0, "pnl": 513.6867}, {"outcome": "WIN", "direction": "long", "size": 0.6755, "exit_price": 65116.0, "pnl": 667.6007}, {"outcome": "LOSS", "direction": "long", "size": 0.6596, "exit_price": 65785.0, "pnl": -8.2026}, {"outcome": "WIN", "direction": "long", "size": 0.5069, "exit_price": 67200.0, "pnl": 8.4559}, {"outcome": "LOSS", "direction": "long", "size": 0.6278, "exit_price": 68229.0, "pnl": -167.0341}, {"outcome": "LOSS", "direction": "long", "size": 0.6733, "exit_price": 67619.0, "pnl": -476.241}, {"outcome": "WIN", "direction": "long", "size": 0.9345, "exit_price": 69219.0, "pnl": 495.7945}, {"outcome": "WIN", "direction": "long", "size": 0.7394, "exit_price": 70334.0, "pnl": 501.9995}, {"outcome": "WIN", "direction": "long", "size": 0.6312, "exit_price": 72458.0, "pnl": 747.1414}, {"outcome": "LOSS", "direction": "long", "size": 0.5607, "exit_price": 71890.0, "pnl": -783.7138}, {"outcome": "LOSS", "direction": "short", "size": 0.5584, "exit_price": 70044.0, "pnl": -474.5798}, {"outcome": "WIN", "direction": "long", "size": 0.4466, "exit_price": 71190.0, "pnl": 873.0635}, {"outcome": "WIN", "direction": "long", "size": 0.3049, "exit_price": 76107.0, "pnl": 728.2496}, {"outcome": "WIN", "direction": "long", "size": 0.4409, "exit_price": 76470.0, "pnl": 204.1472}, {"outcome": "LOSS", "direction": "long", "size": 0.5115, "exit_price": 76106.0, "pnl": -420.0568}, {"outcome": "WIN", "direction": "long", "size": 0.5729, "exit_price": 80440.0, "pnl": 1775.0558}, {"outcome": "WIN", "direction": "long", "size": 0.4812, "exit_price": 79970.0, "pnl": 42.6645}, {"outcome": "WIN", "direction": "long", "size": 0.403, "exit_price": 84119.0, "pnl": 1116.9469}, {"outcome": "LOSS", "direction": "long", "size": 0.3178, "exit_price": 87765.0, "pnl": -98.9411}, {"outcome": "LOSS", "direction": "long", "size": 0.2273, "exit_price": 88586.0, "pnl": -220.7256}, {"outcome": "LOSS", "direction": "long", "size": 0.2517, "exit_price": 90780.0, "pnl": -115.3631}, {"outcome": "LOSS", "direction": "long", "size": 0.2763, "exit_price": 91074.0, "pnl": -139.4088}, {"outcome": "WIN", "direction": "long", "size": 0.463, "exit_price": 91874.0, "pnl": 1.9163}, {"outcome": "WIN", "direction": "long", "size": 0.361, "exit_price": 92453.0, "pnl": 146.0043}, {"outcome": "WIN", "direction": "long", "size": 0.4023, "exit_price": 95881.0, "pnl": 1166.9452}, {"outcome": "LOSS", "direction": "long", "size": 0.3897, "exit_price": 97121.0, "pnl": -382.8678}, {"outcome": "LOSS", "direction": "long", "size": 0.3732, "exit_price": 97867.0, "pnl": -424.7006}, {"outcome": "LOSS", "direction": "long", "size": 0.3875, "exit_price": 98487.0, "pnl": -180.2416}, {"outcome": "LOSS", "direction": "short", "size": 0.3239, "exit_price": 92843.0, "pnl": -151.0343}, {"outcome": "LOSS", "direction": "short", "size": 0.2928, "exit_price": 94924.0, "pnl": -262.2517}, {"outcome": "LOSS", "direction": "long", "size": 0.3585, "exit_price": 95712.0, "pnl": -240.0241}, {"outcome": "LOSS", "direction": "long", "size": 0.4767, "exit_price": 96338.0, "pnl": -321.7436}, {"outcome": "LOSS", "direction": "long", "size": 0.3556, "exit_price": 98238.0, "pnl": -242.9097}, {"outcome": "WIN", "direction": "long", "size": 0.2604, "exit_price": 103699.0, "pnl": 529.4108}, {"outcome": "WIN", "direction": "long", "size": 0.2565, "exit_price": 99706.0, "pnl": 66.5126}, {"outcome": "LOSS", "direction": "long", "size": 0.4056, "exit_price": 99046.0, "pnl": -299.199}, {"outcome": "LOSS", "direction": "long", "size": 0.4767, "exit_price": 99546.5, "pnl": -649.1728}, {"outcome": "WIN", "direction": "short", "size": 0.3197, "exit_price": 95961.0, "pnl": 589.4315}, {"outcome": "LOSS", "direction": "short", "size": 0.3334, "exit_price": 99613.0, "pnl": -460.0932}, {"outcome": "WIN", "direction": "long", "size": 0.3146, "exit_price": 101569.0, "pnl": 325.0944}, {"outcome": "LOSS", "direction": "long", "size": 0.3663, "exit_price": 100920.0, "pnl": -309.4887}, {"outcome": "LOSS", "direction": "long", "size": 0.4003, "exit_price": 101409.0, "pnl": -18.3644}, {"outcome": "WIN", "direction": "long", "size": 0.5284, "exit_price": 103360.0, "pnl": 265.8184}, {"outcome": "LOSS", "direction": "long", "size": 0.4136, "exit_price": 104766.0, "pnl": -207.6833}, {"outcome": "WIN", "direction": "long", "size": 0.4077, "exit_price": 105511.0, "pnl": 665.5625}, {"outcome": "LOSS", "direction": "long", "size": 0.3466, "exit_price": 106799.0, "pnl": -0.7842}, {"outcome": "WIN", "direction": "short", "size": 0.3091, "exit_price": 99721.0, "pnl": 1579.6696}, {"outcome": "WIN", "direction": "short", "size": 0.3166, "exit_price": 100700.0, "pnl": 299.9028}, {"outcome": "WIN", "direction": "short", "size": 0.3173, "exit_price": 97070.0, "pnl": 330.1457}, {"outcome": "WIN", "direction": "short", "size": 0.3959, "exit_price": 94370.0, "pnl": 1008.1466}, {"outcome": "LOSS", "direction": "short", "size": 0.3336, "exit_price": 95250.0, "pnl": -440.5552}, {"outcome": "LOSS", "direction": "short", "size": 0.3641, "exit_price": 98708.0, "pnl": -944.3678}, {"outcome": "WIN", "direction": "long", "size": 0.346, "exit_price": 99075.0, "pnl": 157.332}, {"outcome": "WIN", "direction": "short", "size": 0.4278, "exit_price": 95740.0, "pnl": 364.6327}, {"outcome": "WIN", "direction": "short", "size": 0.555, "exit_price": 92166.0, "pnl": 274.5684}, {"outcome": "WIN", "direction": "short", "size": 0.4338, "exit_price": 93527.0, "pnl": 439.7366}], "total_pnl": 23552.08409999999, "avg_pnl": 130.84491166666666, "median_pnl": 53.712, "pnl_std": 431.5116323149085, "avg_win": 413.7266588235294, "largest_win": 1775.0558, "avg_loss": -239.07737307692307, "largest_loss": -944.3678, "avg_size": 0.3877938888888889, "median_size": 0.3785, "max_size": 0.9345}, "modern_trades": {"total_trades": 160, "winning_trades": 90, "losing_trades": 70, "long_trades": 131, "short_trades": 29, "all_pnls": [-27.8134, 428.343, -95.0952, 86.3929, 198.2802, 145.0881, -74.5969, 87.408, 427.6815, -16.1648, 220.4653, 360.635, 13.5361, 396.3692, 162.5868, 267.8941, -27.6301, 301.8761, -55.636, -166.1627, 44.8056, 170.8482, -72.2254, -98.4108, -15.8399, 174.5605, -143.8671, 3.4862, -181.4765, -38.352, 267.9607, -21.0452, -64.3417, 306.7356, -14.2739, 283.1965, 111.2803, -135.5221, -96.8347, 143.5011, 27.3169, 246.905, -121.7766, -92.9144, -141.8349, 123.4579, -2.8899, -83.1393, 248.1929, 29.2804, 97.9515, 14.4366, 65.2518, 612.1667, -160.5663, -2.7951, 176.5876, -281.5258, 12.5802, -50.1941, -32.2566, -91.8314, -25.021, 139.1061, -82.1813, 227.2309, -87.6643, -161.4716, 289.9099, 55.7279, 202.5148, 571.0745, 335.7786, 101.5307, 12.9149, 127.5176, 36.5163, 156.3133, 80.9488, -285.5054, -154.0788, 154.4257, 60.5424, 66.5311, 164.3606, -69.6172, -119.569, -30.1042, 251.9764, 29.2858, 36.4917, -142.0308, 417.5102, -14.9285, -167.1674, 34.6473, 1.4288, -51.1157, 249.4328, -83.0936, -17.9995, 321.3743, -79.7686, 59.6142, 42.87, 26.395, -26.9561, 310.5202, -137.3639, -23.6106, -96.4163, 113.2475, 140.7215, 338.218, 31.2891, 259.5029, -149.4609, -99.0984, -145.289, 394.3916, 456.4167, -208.9552, 534.9529, 30.0565, 137.1172, 89.1792, 702.5188, 258.4016, -66.5878, -46.2171, -44.4208, 114.3589, 394.9074, 79.4119, -140.6645, -115.8679, 132.8318, 475.0785, -188.707, 311.0545, -34.2362, -147.2983, -322.7682, 199.8164, 324.5176, -35.4653, 106.6664, 578.1067, -0.2828, 349.0426, -18.2179, -99.9567, -344.88, -0.2923, 437.0316, -145.8316, 239.2632, 75.5596, 46.4214, -62.2126], "winning_pnls": [428.343, 86.3929, 198.2802, 145.0881, 87.408, 427.6815, 220.4653, 360.635, 13.5361, 396.3692, 162.5868, 267.8941, 301.8761, 44.8056, 170.8482, 174.5605, 3.4862, 267.9607, 306.7356, 283.1965, 111.2803, 143.5011, 27.3169, 246.905, 123.4579, 248.1929, 29.2804, 97.9515, 14.4366, 65.2518, 612.1667, 176.5876, 12.5802, 139.1061, 227.2309, 289.9099, 55.7279, 202.5148, 571.0745, 335.7786, 101.5307, 12.9149, 127.5176, 36.5163, 156.3133, 80.9488, 154.4257, 60.5424, 66.5311, 164.3606, 251.9764, 29.2858, 36.4917, 417.5102, 34.6473, 1.4288, 249.4328, 321.3743, 59.6142, 42.87, 26.395, 310.5202, 113.2475, 140.7215, 338.218, 31.2891, 259.5029, 394.3916, 456.4167, 534.9529, 30.0565, 137.1172, 89.1792, 702.5188, 258.4016, 114.3589, 394.9074, 79.4119, 132.8318, 475.0785, 311.0545, 199.8164, 324.5176, 106.6664, 578.1067, 349.0426, 437.0316, 239.2632, 75.5596, 46.4214], "losing_pnls": [-27.8134, -95.0952, -74.5969, -16.1648, -27.6301, -55.636, -166.1627, -72.2254, -98.4108, -15.8399, -143.8671, -181.4765, -38.352, -21.0452, -64.3417, -14.2739, -135.5221, -96.8347, -121.7766, -92.9144, -141.8349, -2.8899, -83.1393, -160.5663, -2.7951, -281.5258, -50.1941, -32.2566, -91.8314, -25.021, -82.1813, -87.6643, -161.4716, -285.5054, -154.0788, -69.6172, -119.569, -30.1042, -142.0308, -14.9285, -167.1674, -51.1157, -83.0936, -17.9995, -79.7686, -26.9561, -137.3639, -23.6106, -96.4163, -149.4609, -99.0984, -145.289, -208.9552, -66.5878, -46.2171, -44.4208, -140.6645, -115.8679, -188.707, -34.2362, -147.2983, -322.7682, -35.4653, -0.2828, -18.2179, -99.9567, -344.88, -0.2923, -145.8316, -62.2126], "trade_sizes": [0.0883, 0.2136, 0.2227, 0.2205, 0.1307, 0.2137, 0.1846, 0.2172, 0.2014, 0.1388, 0.21, 0.2719, 0.1237, 0.2448, 0.1436, 0.0672, 0.0585, 0.1913, 0.078, 0.0519, 0.0669, 0.1552, 0.1966, 0.0619, 0.1007, 0.069, 0.0597, 0.051, 0.0873, 0.1016, 0.1535, 0.1256, 0.123, 0.09, 0.1575, 0.2023, 0.201, 0.1487, 0.1032, 0.146, 0.148, 0.1332, 0.0665, 0.1148, 0.0818, 0.1134, 0.1669, 0.1001, 0.2064, 0.1206, 0.171, 0.2177, 0.1719, 0.1916, 0.1946, 0.15, 0.1455, 0.1172, 0.1356, 0.3788, 0.2922, 0.1519, 0.1145, 0.183, 0.1307, 0.2162, 0.1124, 0.1534, 0.281, 0.0782, 0.1803, 0.3774, 0.235, 0.1308, 0.1371, 0.1349, 0.2485, 0.2008, 0.3598, 0.3476, 0.2038, 0.2424, 0.122, 0.0908, 0.1169, 0.0288, 0.0934, 0.0739, 0.1609, 0.1467, 0.1646, 0.153, 0.1813, 0.251, 0.1714, 0.2057, 0.299, 0.129, 0.1772, 0.2068, 0.166, 0.2341, 0.3915, 0.232, 0.1667, 0.1532, 0.1617, 0.2035, 0.1815, 0.1466, 0.337, 0.2429, 0.1751, 0.2208, 0.308, 0.1439, 0.1403, 0.1325, 0.2093, 0.3552, 0.2406, 0.1503, 0.1354, 0.1005, 0.1386, 0.2018, 0.2673, 0.0933, 0.0686, 0.1008, 0.088, 0.1601, 0.1843, 0.1249, 0.1344, 0.1048, 0.1283, 0.158, 0.1581, 0.1162, 0.0881, 0.1997, 0.1915, 0.1084, 0.123, 0.1447, 0.1377, 0.16, 0.125, 0.0937, 0.0723, 0.2163, 0.1631, 0.1201, 0.1762, 0.1521, 0.1541, 0.1527, 0.1908, 0.1329], "trades": [{"outcome": "LOSS", "direction": "long", "size": 0.0883, "exit_price": 45251.0, "pnl": -27.8134}, {"outcome": "WIN", "direction": "long", "size": 0.2136, "exit_price": 47044.0, "pnl": 428.343}, {"outcome": "LOSS", "direction": "long", "size": 0.2227, "exit_price": 46661.0, "pnl": -95.0952}, {"outcome": "WIN", "direction": "long", "size": 0.2205, "exit_price": 46679.0, "pnl": 86.3929}, {"outcome": "WIN", "direction": "short", "size": 0.1307, "exit_price": 42934.0, "pnl": 198.2802}, {"outcome": "WIN", "direction": "long", "size": 0.2137, "exit_price": 41752.0, "pnl": 145.0881}, {"outcome": "LOSS", "direction": "long", "size": 0.1846, "exit_price": 42748.0, "pnl": -74.5969}, {"outcome": "WIN", "direction": "long", "size": 0.2172, "exit_price": 45302.0, "pnl": 87.408}, {"outcome": "WIN", "direction": "long", "size": 0.2014, "exit_price": 47691.0, "pnl": 427.6815}, {"outcome": "LOSS", "direction": "long", "size": 0.1388, "exit_price": 47548.0, "pnl": -16.1648}, {"outcome": "WIN", "direction": "long", "size": 0.21, "exit_price": 49921.0, "pnl": 220.4653}, {"outcome": "WIN", "direction": "long", "size": 0.2719, "exit_price": 52224.0, "pnl": 360.635}, {"outcome": "WIN", "direction": "long", "size": 0.1237, "exit_price": 52507.0, "pnl": 13.5361}, {"outcome": "WIN", "direction": "long", "size": 0.2448, "exit_price": 54552.0, "pnl": 396.3692}, {"outcome": "WIN", "direction": "long", "size": 0.1436, "exit_price": 55875.0, "pnl": 162.5868}, {"outcome": "WIN", "direction": "long", "size": 0.0672, "exit_price": 61067.0, "pnl": 267.8941}, {"outcome": "LOSS", "direction": "long", "size": 0.0585, "exit_price": 61262.19, "pnl": -27.6301}, {"outcome": "WIN", "direction": "long", "size": 0.1913, "exit_price": 65235.0, "pnl": 301.8761}, {"outcome": "LOSS", "direction": "long", "size": 0.078, "exit_price": 65713.0, "pnl": -55.636}, {"outcome": "LOSS", "direction": "short", "size": 0.0519, "exit_price": 64795.0, "pnl": -166.1627}, {"outcome": "WIN", "direction": "long", "size": 0.0669, "exit_price": 67016.0, "pnl": 44.8056}, {"outcome": "WIN", "direction": "long", "size": 0.1552, "exit_price": 68563.0, "pnl": 170.8482}, {"outcome": "LOSS", "direction": "long", "size": 0.1966, "exit_price": 69401.0, "pnl": -72.2254}, {"outcome": "LOSS", "direction": "long", "size": 0.0619, "exit_price": 70593.0, "pnl": -98.4108}, {"outcome": "LOSS", "direction": "long", "size": 0.1007, "exit_price": 73422.0, "pnl": -15.8399}, {"outcome": "WIN", "direction": "short", "size": 0.069, "exit_price": 69117.0, "pnl": 174.5605}, {"outcome": "LOSS", "direction": "short", "size": 0.0597, "exit_price": 68771.0, "pnl": -143.8671}, {"outcome": "WIN", "direction": "short", "size": 0.051, "exit_price": 63590.0, "pnl": 3.4862}, {"outcome": "LOSS", "direction": "long", "size": 0.0873, "exit_price": 65263.0, "pnl": -181.4765}, {"outcome": "LOSS", "direction": "short", "size": 0.1016, "exit_price": 64651.0, "pnl": -38.352}, {"outcome": "WIN", "direction": "long", "size": 0.1535, "exit_price": 67156.0, "pnl": 267.9607}, {"outcome": "LOSS", "direction": "long", "size": 0.1256, "exit_price": 70570.0, "pnl": -21.0452}, {"outcome": "LOSS", "direction": "long", "size": 0.123, "exit_price": 70515.0, "pnl": -64.3417}, {"outcome": "WIN", "direction": "short", "size": 0.09, "exit_price": 65992.0, "pnl": 306.7356}, {"outcome": "LOSS", "direction": "long", "size": 0.1575, "exit_price": 67663.0, "pnl": -14.2739}, {"outcome": "WIN", "direction": "long", "size": 0.2023, "exit_price": 69617.0, "pnl": 283.1965}, {"outcome": "WIN", "direction": "long", "size": 0.201, "exit_price": 70730.0, "pnl": 111.2803}, {"outcome": "LOSS", "direction": "long", "size": 0.1487, "exit_price": 71148.0, "pnl": -135.5221}, {"outcome": "LOSS", "direction": "long", "size": 0.1032, "exit_price": 69990.55, "pnl": -96.8347}, {"outcome": "WIN", "direction": "short", "size": 0.146, "exit_price": 67207.0, "pnl": 143.5011}, {"outcome": "WIN", "direction": "short", "size": 0.148, "exit_price": 67189.0, "pnl": 27.3169}, {"outcome": "WIN", "direction": "short", "size": 0.1332, "exit_price": 62993.0, "pnl": 246.905}, {"outcome": "LOSS", "direction": "short", "size": 0.0665, "exit_price": 65859.66, "pnl": -121.7766}, {"outcome": "LOSS", "direction": "long", "size": 0.1148, "exit_price": 64175.0, "pnl": -92.9144}, {"outcome": "LOSS", "direction": "short", "size": 0.0818, "exit_price": 62725.0, "pnl": -141.8349}, {"outcome": "WIN", "direction": "long", "size": 0.1134, "exit_price": 64853.0, "pnl": 123.4579}, {"outcome": "LOSS", "direction": "long", "size": 0.1669, "exit_price": 66391.0, "pnl": -2.8899}, {"outcome": "LOSS", "direction": "short", "size": 0.1001, "exit_price": 58607.0, "pnl": -83.1393}, {"outcome": "WIN", "direction": "long", "size": 0.2064, "exit_price": 60591.0, "pnl": 248.1929}, {"outcome": "WIN", "direction": "long", "size": 0.1206, "exit_price": 63145.0, "pnl": 29.2804}, {"outcome": "WIN", "direction": "long", "size": 0.171, "exit_price": 65136.0, "pnl": 97.9515}, {"outcome": "WIN", "direction": "long", "size": 0.2177, "exit_price": 63041.0, "pnl": 14.4366}, {"outcome": "WIN", "direction": "long", "size": 0.1719, "exit_price": 62736.0, "pnl": 65.2518}, {"outcome": "WIN", "direction": "long", "size": 0.1916, "exit_price": 65717.0, "pnl": 612.1667}, {"outcome": "LOSS", "direction": "long", "size": 0.1946, "exit_price": 65106.0, "pnl": -160.5663}, {"outcome": "LOSS", "direction": "long", "size": 0.15, "exit_price": 66904.0, "pnl": -2.7951}, {"outcome": "WIN", "direction": "long", "size": 0.1455, "exit_price": 69539.0, "pnl": 176.5876}, {"outcome": "LOSS", "direction": "long", "size": 0.1172, "exit_price": 69856.0, "pnl": -281.5258}, {"outcome": "WIN", "direction": "long", "size": 0.1356, "exit_price": 69450.0, "pnl": 12.5802}, {"outcome": "LOSS", "direction": "long", "size": 0.3788, "exit_price": 69025.0, "pnl": -50.1941}, {"outcome": "LOSS", "direction": "long", "size": 0.2922, "exit_price": 68867.0, "pnl": -32.2566}, {"outcome": "LOSS", "direction": "long", "size": 0.1519, "exit_price": 68166.0, "pnl": -91.8314}, {"outcome": "LOSS", "direction": "long", "size": 0.1145, "exit_price": 68998.0, "pnl": -25.021}, {"outcome": "WIN", "direction": "long", "size": 0.183, "exit_price": 71299.0, "pnl": 139.1061}, {"outcome": "LOSS", "direction": "long", "size": 0.1307, "exit_price": 71096.0, "pnl": -82.1813}, {"outcome": "WIN", "direction": "long", "size": 0.2162, "exit_price": 68909.0, "pnl": 227.2309}, {"outcome": "LOSS", "direction": "long", "size": 0.1124, "exit_price": 67431.0, "pnl": -87.6643}, {"outcome": "LOSS", "direction": "long", "size": 0.1534, "exit_price": 60916.0, "pnl": -161.4716}, {"outcome": "WIN", "direction": "long", "size": 0.281, "exit_price": 62977.0, "pnl": 289.9099}, {"outcome": "WIN", "direction": "short", "size": 0.0782, "exit_price": 56387.0, "pnl": 55.7279}, {"outcome": "WIN", "direction": "long", "size": 0.1803, "exit_price": 57689.0, "pnl": 202.5148}, {"outcome": "WIN", "direction": "long", "size": 0.3774, "exit_price": 59493.0, "pnl": 571.0745}, {"outcome": "WIN", "direction": "long", "size": 0.235, "exit_price": 61233.0, "pnl": 335.7786}, {"outcome": "WIN", "direction": "long", "size": 0.1308, "exit_price": 63488.0, "pnl": 101.5307}, {"outcome": "WIN", "direction": "long", "size": 0.1371, "exit_price": 64876.0, "pnl": 12.9149}, {"outcome": "WIN", "direction": "long", "size": 0.1349, "exit_price": 66859.0, "pnl": 127.5176}, {"outcome": "WIN", "direction": "long", "size": 0.2485, "exit_price": 67635.0, "pnl": 36.5163}, {"outcome": "WIN", "direction": "long", "size": 0.2008, "exit_price": 67958.0, "pnl": 156.3133}, {"outcome": "WIN", "direction": "long", "size": 0.3598, "exit_price": 68432.0, "pnl": 80.9488}, {"outcome": "LOSS", "direction": "long", "size": 0.3476, "exit_price": 68526.0, "pnl": -285.5054}, {"outcome": "LOSS", "direction": "long", "size": 0.2038, "exit_price": 67478.49, "pnl": -154.0788}, {"outcome": "WIN", "direction": "long", "size": 0.2424, "exit_price": 69310.0, "pnl": 154.4257}, {"outcome": "WIN", "direction": "short", "size": 0.122, "exit_price": 66076.0, "pnl": 60.5424}, {"outcome": "WIN", "direction": "short", "size": 0.0908, "exit_price": 60818.0, "pnl": 66.5311}, {"outcome": "WIN", "direction": "short", "size": 0.1169, "exit_price": 56191.0, "pnl": 164.3606}, {"outcome": "LOSS", "direction": "short", "size": 0.0288, "exit_price": 56434.0, "pnl": -69.6172}, {"outcome": "LOSS", "direction": "long", "size": 0.0934, "exit_price": 55547.0, "pnl": -119.569}, {"outcome": "LOSS", "direction": "long", "size": 0.0739, "exit_price": 60759.0, "pnl": -30.1042}, {"outcome": "WIN", "direction": "long", "size": 0.1609, "exit_price": 61076.0, "pnl": 251.9764}, {"outcome": "WIN", "direction": "long", "size": 0.1467, "exit_price": 59413.0, "pnl": 29.2858}, {"outcome": "WIN", "direction": "long", "size": 0.1646, "exit_price": 60861.0, "pnl": 36.4917}, {"outcome": "LOSS", "direction": "long", "size": 0.153, "exit_price": 60714.0, "pnl": -142.0308}, {"outcome": "WIN", "direction": "long", "size": 0.1813, "exit_price": 64089.0, "pnl": 417.5102}, {"outcome": "LOSS", "direction": "long", "size": 0.251, "exit_price": 64254.0, "pnl": -14.9285}, {"outcome": "LOSS", "direction": "short", "size": 0.1714, "exit_price": 59541.0, "pnl": -167.1674}, {"outcome": "WIN", "direction": "long", "size": 0.2057, "exit_price": 60524.0, "pnl": 34.6473}, {"outcome": "WIN", "direction": "long", "size": 0.299, "exit_price": 59103.0, "pnl": 1.4288}, {"outcome": "LOSS", "direction": "short", "size": 0.129, "exit_price": 54297.0, "pnl": -51.1157}, {"outcome": "WIN", "direction": "long", "size": 0.1772, "exit_price": 56800.0, "pnl": 249.4328}, {"outcome": "LOSS", "direction": "long", "size": 0.2068, "exit_price": 56790.0, "pnl": -83.0936}, {"outcome": "LOSS", "direction": "long", "size": 0.166, "exit_price": 57832.0, "pnl": -17.9995}, {"outcome": "WIN", "direction": "long", "size": 0.2341, "exit_price": 59849.0, "pnl": 321.3743}, {"outcome": "LOSS", "direction": "long", "size": 0.3915, "exit_price": 59807.0, "pnl": -79.7686}, {"outcome": "WIN", "direction": "long", "size": 0.232, "exit_price": 60509.0, "pnl": 59.6142}, {"outcome": "WIN", "direction": "long", "size": 0.1667, "exit_price": 60244.0, "pnl": 42.87}, {"outcome": "WIN", "direction": "long", "size": 0.1532, "exit_price": 62768.0, "pnl": 26.395}, {"outcome": "LOSS", "direction": "long", "size": 0.1617, "exit_price": 62930.0, "pnl": -26.9561}, {"outcome": "WIN", "direction": "long", "size": 0.2035, "exit_price": 66032.0, "pnl": 310.5202}, {"outcome": "LOSS", "direction": "short", "size": 0.1815, "exit_price": 61886.0, "pnl": -137.3639}, {"outcome": "LOSS", "direction": "long", "size": 0.1466, "exit_price": 61850.0, "pnl": -23.6106}, {"outcome": "LOSS", "direction": "long", "size": 0.337, "exit_price": 63209.0, "pnl": -96.4163}, {"outcome": "WIN", "direction": "long", "size": 0.2429, "exit_price": 63389.0, "pnl": 113.2475}, {"outcome": "WIN", "direction": "long", "size": 0.1751, "exit_price": 63086.0, "pnl": 140.7215}, {"outcome": "WIN", "direction": "long", "size": 0.2208, "exit_price": 65660.0, "pnl": 338.218}, {"outcome": "WIN", "direction": "long", "size": 0.308, "exit_price": 65785.0, "pnl": 31.2891}, {"outcome": "WIN", "direction": "long", "size": 0.1439, "exit_price": 67839.0, "pnl": 259.5029}, {"outcome": "LOSS", "direction": "long", "size": 0.1403, "exit_price": 66943.0, "pnl": -149.4609}, {"outcome": "LOSS", "direction": "long", "size": 0.1325, "exit_price": 68113.0, "pnl": -99.0984}, {"outcome": "LOSS", "direction": "long", "size": 0.2093, "exit_price": 67632.0, "pnl": -145.289}, {"outcome": "WIN", "direction": "long", "size": 0.3552, "exit_price": 69799.0, "pnl": 394.3916}, {"outcome": "WIN", "direction": "long", "size": 0.2406, "exit_price": 71919.0, "pnl": 456.4167}, {"outcome": "LOSS", "direction": "long", "size": 0.1503, "exit_price": 72194.0, "pnl": -208.9552}, {"outcome": "WIN", "direction": "long", "size": 0.1354, "exit_price": 73191.0, "pnl": 534.9529}, {"outcome": "WIN", "direction": "long", "size": 0.1005, "exit_price": 74757.0, "pnl": 30.0565}, {"outcome": "WIN", "direction": "long", "size": 0.1386, "exit_price": 75860.0, "pnl": 137.1172}, {"outcome": "WIN", "direction": "long", "size": 0.2018, "exit_price": 76499.0, "pnl": 89.1792}, {"outcome": "WIN", "direction": "long", "size": 0.2673, "exit_price": 79970.0, "pnl": 702.5188}, {"outcome": "WIN", "direction": "long", "size": 0.0933, "exit_price": 88782.0, "pnl": 258.4016}, {"outcome": "LOSS", "direction": "long", "size": 0.0686, "exit_price": 88586.0, "pnl": -66.5878}, {"outcome": "LOSS", "direction": "long", "size": 0.1008, "exit_price": 90780.0, "pnl": -46.2171}, {"outcome": "LOSS", "direction": "long", "size": 0.088, "exit_price": 91074.0, "pnl": -44.4208}, {"outcome": "WIN", "direction": "long", "size": 0.1601, "exit_price": 92119.0, "pnl": 114.3589}, {"outcome": "WIN", "direction": "long", "size": 0.1843, "exit_price": 94424.0, "pnl": 394.9074}, {"outcome": "WIN", "direction": "long", "size": 0.1249, "exit_price": 98740.0, "pnl": 79.4119}, {"outcome": "LOSS", "direction": "long", "size": 0.1344, "exit_price": 97905.0, "pnl": -140.6645}, {"outcome": "LOSS", "direction": "short", "size": 0.1048, "exit_price": 93417.0, "pnl": -115.8679}, {"outcome": "WIN", "direction": "long", "size": 0.1283, "exit_price": 95431.0, "pnl": 132.8318}, {"outcome": "WIN", "direction": "long", "size": 0.158, "exit_price": 98080.0, "pnl": 475.0785}, {"outcome": "LOSS", "direction": "long", "size": 0.1581, "exit_price": 96942.0, "pnl": -188.707}, {"outcome": "WIN", "direction": "long", "size": 0.1162, "exit_price": 101599.0, "pnl": 311.0545}, {"outcome": "LOSS", "direction": "short", "size": 0.0881, "exit_price": 99707.0, "pnl": -34.2362}, {"outcome": "LOSS", "direction": "long", "size": 0.1997, "exit_price": 99046.0, "pnl": -147.2983}, {"outcome": "LOSS", "direction": "long", "size": 0.1915, "exit_price": 99221.34, "pnl": -322.7682}, {"outcome": "WIN", "direction": "short", "size": 0.1084, "exit_price": 95961.0, "pnl": 199.8164}, {"outcome": "WIN", "direction": "long", "size": 0.123, "exit_price": 101003.0, "pnl": 324.5176}, {"outcome": "LOSS", "direction": "long", "size": 0.1447, "exit_price": 101520.0, "pnl": -35.4653}, {"outcome": "WIN", "direction": "long", "size": 0.1377, "exit_price": 102230.0, "pnl": 106.6664}, {"outcome": "WIN", "direction": "long", "size": 0.16, "exit_price": 106470.0, "pnl": 578.1067}, {"outcome": "LOSS", "direction": "long", "size": 0.125, "exit_price": 106799.0, "pnl": -0.2828}, {"outcome": "WIN", "direction": "short", "size": 0.0937, "exit_price": 100700.0, "pnl": 349.0426}, {"outcome": "LOSS", "direction": "short", "size": 0.0723, "exit_price": 96670.0, "pnl": -18.2179}, {"outcome": "LOSS", "direction": "long", "size": 0.2163, "exit_price": 98353.0, "pnl": -99.9567}, {"outcome": "LOSS", "direction": "long", "size": 0.1631, "exit_price": 95342.0, "pnl": -344.88}, {"outcome": "LOSS", "direction": "short", "size": 0.1201, "exit_price": 94311.0, "pnl": -0.2923}, {"outcome": "WIN", "direction": "long", "size": 0.1762, "exit_price": 98721.0, "pnl": 437.0316}, {"outcome": "LOSS", "direction": "long", "size": 0.1521, "exit_price": 97660.0, "pnl": -145.8316}, {"outcome": "WIN", "direction": "short", "size": 0.1541, "exit_price": 94688.0, "pnl": 239.2632}, {"outcome": "WIN", "direction": "short", "size": 0.1527, "exit_price": 92166.0, "pnl": 75.5596}, {"outcome": "WIN", "direction": "long", "size": 0.1908, "exit_price": 94906.0, "pnl": 46.4214}, {"outcome": "LOSS", "direction": "long", "size": 0.1329, "exit_price": 93526.0, "pnl": -62.2126}], "total_pnl": 11492.244199999996, "avg_pnl": 71.82652625, "median_pnl": 29.671149999999997, "pnl_std": 197.14388290643072, "avg_win": 201.90701444444446, "largest_win": 702.5188, "avg_loss": -95.41981571428572, "largest_loss": -344.88, "avg_size": 0.163384375, "median_size": 0.15284999999999999, "max_size": 0.3915}}}