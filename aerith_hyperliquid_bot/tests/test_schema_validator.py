# tests/test_schema_validator.py

"""
Unit tests for schema validation functionality.

Tests the schema validation logic to ensure it correctly identifies
missing canonical columns and validates NaN ratios.
"""

import unittest
import numpy as np
import pandas as pd
from datetime import datetime
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.features.builder_registry import (
    get_canonical_columns, validate_schema, validate_nan_ratios
)


class TestSchemaValidator(unittest.TestCase):
    """Test suite for schema validation functions."""

    def test_get_canonical_columns(self):
        """Test canonical columns retrieval."""
        # Test with depth_levels=5
        columns_5 = get_canonical_columns(depth_levels=5)
        self.assertIsInstance(columns_5, set)
        self.assertGreater(len(columns_5), 25)  # Should have at least 25 columns
        
        # Test with depth_levels=20
        columns_20 = get_canonical_columns(depth_levels=20)
        self.assertIsInstance(columns_20, set)
        self.assertEqual(columns_5, columns_20)  # Should be the same set
        
        # Check for required core columns
        required_core = {
            'timestamp', 'mid_price', 'close', 'high', 'low', 
            'best_bid', 'best_ask', 'spread', 'volume'
        }
        self.assertTrue(required_core.issubset(columns_5))
        
        # Check for required ATR columns
        required_atr = {
            'atr_14_sec', 'atr_percent_sec', 'atr', 'atr_percent', 
            'atr_tf', 'atr_mr', 'atr_mv'
        }
        self.assertTrue(required_atr.issubset(columns_5))
        
        # Check for required OBI columns
        required_obi = {
            'raw_obi_5', 'raw_obi_20', 'obi_smoothed_5', 'obi_smoothed_20',
            'obi_zscore_5', 'obi_zscore_20', 'obi_smoothed'
        }
        self.assertTrue(required_obi.issubset(columns_5))

    def test_validate_schema_complete(self):
        """Test schema validation with complete schema."""
        # Create DataFrame with all canonical columns
        canonical_columns = get_canonical_columns(depth_levels=5)
        
        df_complete = pd.DataFrame({
            col: np.random.uniform(0, 1, 100) if col != 'timestamp' 
            else pd.date_range('2025-01-01', periods=100, freq='1s')
            for col in canonical_columns
        })
        
        is_valid, missing_columns = validate_schema(df_complete, depth_levels=5)
        
        self.assertTrue(is_valid)
        self.assertEqual(len(missing_columns), 0)
        self.assertIsInstance(missing_columns, list)

    def test_validate_schema_missing_core(self):
        """Test schema validation with missing core columns."""
        # Create DataFrame missing some core columns
        df_incomplete = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=100, freq='1s'),
            'mid_price': np.random.uniform(50000, 51000, 100),
            'close': np.random.uniform(50000, 51000, 100),
            # Missing: best_bid, best_ask, spread, volume, etc.
        })
        
        is_valid, missing_columns = validate_schema(df_incomplete, depth_levels=5)
        
        self.assertFalse(is_valid)
        self.assertGreater(len(missing_columns), 0)
        
        # Should include expected missing columns
        expected_missing = {'best_bid', 'best_ask', 'spread', 'volume'}
        actual_missing = set(missing_columns)
        self.assertTrue(expected_missing.issubset(actual_missing))

    def test_validate_schema_missing_atr(self):
        """Test schema validation with missing ATR columns."""
        # Create DataFrame with core columns but missing ATR
        canonical_columns = get_canonical_columns(depth_levels=5)
        atr_columns = {col for col in canonical_columns if 'atr' in col}
        
        non_atr_columns = canonical_columns - atr_columns
        df_no_atr = pd.DataFrame({
            col: np.random.uniform(0, 1, 100) if col != 'timestamp' 
            else pd.date_range('2025-01-01', periods=100, freq='1s')
            for col in non_atr_columns
        })
        
        is_valid, missing_columns = validate_schema(df_no_atr, depth_levels=5)
        
        self.assertFalse(is_valid)
        self.assertGreater(len(missing_columns), 0)
        
        # All missing columns should be ATR-related
        missing_set = set(missing_columns)
        self.assertTrue(missing_set.issubset(atr_columns))

    def test_validate_schema_missing_obi(self):
        """Test schema validation with missing OBI columns."""
        # Create DataFrame with core columns but missing OBI
        canonical_columns = get_canonical_columns(depth_levels=5)
        obi_columns = {col for col in canonical_columns if 'obi' in col}
        
        non_obi_columns = canonical_columns - obi_columns
        df_no_obi = pd.DataFrame({
            col: np.random.uniform(0, 1, 100) if col != 'timestamp' 
            else pd.date_range('2025-01-01', periods=100, freq='1s')
            for col in non_obi_columns
        })
        
        is_valid, missing_columns = validate_schema(df_no_obi, depth_levels=5)
        
        self.assertFalse(is_valid)
        self.assertGreater(len(missing_columns), 0)
        
        # All missing columns should be OBI-related
        missing_set = set(missing_columns)
        self.assertTrue(missing_set.issubset(obi_columns))

    def test_validate_nan_ratios_good_data(self):
        """Test NaN ratio validation with good data."""
        # Create DataFrame with minimal NaNs
        df_good = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=200, freq='1s'),
            'feature_1': np.random.uniform(0, 1, 200),
            'feature_2': np.random.uniform(0, 1, 200),
            'feature_3': np.random.uniform(0, 1, 200),
        })
        
        # Add a few NaNs in warmup period only
        df_good.iloc[:5, 1] = np.nan  # Only in warmup
        
        is_valid, nan_ratios = validate_nan_ratios(
            df_good, max_nan_ratio=0.01, warmup_rows=50
        )
        
        self.assertTrue(is_valid)
        self.assertIsInstance(nan_ratios, dict)
        
        # All features should have low NaN ratios after warmup
        for col, ratio in nan_ratios.items():
            if col != 'timestamp':
                self.assertLessEqual(ratio, 0.01)

    def test_validate_nan_ratios_bad_data(self):
        """Test NaN ratio validation with bad data."""
        # Create DataFrame with too many NaNs after warmup
        df_bad = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=200, freq='1s'),
            'feature_1': np.random.uniform(0, 1, 200),
            'feature_2': np.random.uniform(0, 1, 200),
            'feature_3': np.random.uniform(0, 1, 200),
        })
        
        # Add many NaNs after warmup period (should fail validation)
        df_bad.iloc[60:80, 1] = np.nan  # 20 out of 150 = 13.3% NaN ratio
        df_bad.iloc[70:90, 2] = np.nan  # 20 out of 150 = 13.3% NaN ratio
        
        is_valid, nan_ratios = validate_nan_ratios(
            df_bad, max_nan_ratio=0.01, warmup_rows=50
        )
        
        self.assertFalse(is_valid)
        self.assertIsInstance(nan_ratios, dict)
        
        # Should have features with high NaN ratios
        high_nan_features = [col for col, ratio in nan_ratios.items() if ratio > 0.01]
        self.assertGreater(len(high_nan_features), 0)
        self.assertIn('feature_1', high_nan_features)
        self.assertIn('feature_2', high_nan_features)

    def test_validate_nan_ratios_warmup_period(self):
        """Test that NaN validation ignores warmup period."""
        # Create DataFrame with NaNs only in warmup period
        df_warmup_nans = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=200, freq='1s'),
            'feature_1': np.random.uniform(0, 1, 200),
            'feature_2': np.random.uniform(0, 1, 200),
        })
        
        # Fill entire warmup period with NaNs
        df_warmup_nans.iloc[:50, 1:] = np.nan
        
        is_valid, nan_ratios = validate_nan_ratios(
            df_warmup_nans, max_nan_ratio=0.01, warmup_rows=50
        )
        
        # Should be valid because NaNs are only in warmup period
        self.assertTrue(is_valid)
        
        # NaN ratios should be 0 for post-warmup period
        for col, ratio in nan_ratios.items():
            if col != 'timestamp':
                self.assertEqual(ratio, 0.0)

    def test_validate_nan_ratios_small_dataset(self):
        """Test NaN validation with dataset smaller than warmup."""
        # Create small DataFrame (smaller than warmup period)
        df_small = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=30, freq='1s'),
            'feature_1': np.random.uniform(0, 1, 30),
        })
        
        is_valid, nan_ratios = validate_nan_ratios(
            df_small, max_nan_ratio=0.01, warmup_rows=50
        )
        
        # Should be valid (validation skipped for small datasets)
        self.assertTrue(is_valid)
        self.assertEqual(len(nan_ratios), 0)

    def test_validate_nan_ratios_edge_cases(self):
        """Test NaN validation edge cases."""
        # Test with all NaN column
        df_all_nan = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=200, freq='1s'),
            'all_nan_feature': [np.nan] * 200,
            'good_feature': np.random.uniform(0, 1, 200),
        })
        
        is_valid, nan_ratios = validate_nan_ratios(
            df_all_nan, max_nan_ratio=0.01, warmup_rows=50
        )
        
        self.assertFalse(is_valid)
        self.assertEqual(nan_ratios['all_nan_feature'], 1.0)  # 100% NaN
        self.assertEqual(nan_ratios['good_feature'], 0.0)     # 0% NaN
        
        # Test with no NaN columns
        df_no_nan = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=200, freq='1s'),
            'feature_1': np.random.uniform(0, 1, 200),
            'feature_2': np.random.uniform(0, 1, 200),
        })
        
        is_valid, nan_ratios = validate_nan_ratios(
            df_no_nan, max_nan_ratio=0.01, warmup_rows=50
        )
        
        self.assertTrue(is_valid)
        for col, ratio in nan_ratios.items():
            if col != 'timestamp':
                self.assertEqual(ratio, 0.0)

    def test_validate_nan_ratios_timestamp_ignored(self):
        """Test that timestamp column is ignored in NaN validation."""
        # Create DataFrame with NaN timestamp (should be ignored)
        df_nan_timestamp = pd.DataFrame({
            'timestamp': [np.nan] * 200,  # All NaN timestamps
            'feature_1': np.random.uniform(0, 1, 200),
        })
        
        is_valid, nan_ratios = validate_nan_ratios(
            df_nan_timestamp, max_nan_ratio=0.01, warmup_rows=50
        )
        
        # Should be valid because timestamp is ignored
        self.assertTrue(is_valid)
        self.assertNotIn('timestamp', nan_ratios)
        self.assertEqual(nan_ratios['feature_1'], 0.0)

    def test_integration_schema_and_nan_validation(self):
        """Test integration of schema and NaN validation."""
        # Create a realistic DataFrame that should pass both validations
        canonical_columns = get_canonical_columns(depth_levels=5)
        
        df_realistic = pd.DataFrame({
            col: np.random.uniform(0, 1, 1000) if col != 'timestamp' 
            else pd.date_range('2025-01-01', periods=1000, freq='1s')
            for col in canonical_columns
        })
        
        # Add some NaNs in warmup period
        df_realistic.iloc[:50, 1:5] = np.nan
        
        # Schema validation should pass
        schema_valid, missing_cols = validate_schema(df_realistic, depth_levels=5)
        self.assertTrue(schema_valid)
        self.assertEqual(len(missing_cols), 0)
        
        # NaN validation should pass
        nan_valid, nan_ratios = validate_nan_ratios(
            df_realistic, max_nan_ratio=0.01, warmup_rows=100
        )
        self.assertTrue(nan_valid)
        
        # All features should have acceptable NaN ratios
        for col, ratio in nan_ratios.items():
            self.assertLessEqual(ratio, 0.01)


if __name__ == '__main__':
    unittest.main()
