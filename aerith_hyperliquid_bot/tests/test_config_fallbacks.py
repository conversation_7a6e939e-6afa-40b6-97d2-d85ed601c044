# tests/test_config_fallbacks.py

import pytest
from pydantic import ValidationError
from pathlib import Path
import yaml

from hyperliquid_bot.config.settings import Config, TFV3Settings, PortfolioSettings


class TestConfigFallbacks:
    """Test configuration fallback values for Task R-105a."""

    def test_load_base_yaml_fallback_values(self):
        """Test that base.yaml loads with correct fallback values."""
        # Load the base.yaml configuration
        project_root = Path(__file__).parent.parent.parent
        config_path = project_root / "aerith_hyperliquid_bot" / "configs" / "base.yaml"

        assert config_path.exists(), f"Config file not found: {config_path}"

        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f)

        # Validate the configuration loads successfully
        config = Config(**config_data)

        # Check ATR fallback percentage
        assert config.tf_v3.atr_fallback_pct == 0.01, f"Expected atr_fallback_pct=0.01, got {config.tf_v3.atr_fallback_pct}"

        # Check minimum trade size
        assert config.portfolio.min_trade_size == 0.001, f"Expected min_trade_size=0.001, got {config.portfolio.min_trade_size}"

    def test_tf_v3_atr_fallback_validation(self):
        """Test TFV3Settings atr_fallback_pct validation."""
        # Test valid values
        valid_settings = TFV3Settings(atr_fallback_pct=0.01)
        assert valid_settings.atr_fallback_pct == 0.01

        valid_settings = TFV3Settings(atr_fallback_pct=0.05)
        assert valid_settings.atr_fallback_pct == 0.05

        valid_settings = TFV3Settings(atr_fallback_pct=0.19)
        assert valid_settings.atr_fallback_pct == 0.19

        # Test invalid values - too high
        with pytest.raises(ValidationError) as exc_info:
            TFV3Settings(atr_fallback_pct=0.25)
        assert "less than 0.2" in str(exc_info.value)

        # Test invalid values - zero or negative
        with pytest.raises(ValidationError) as exc_info:
            TFV3Settings(atr_fallback_pct=0.0)
        assert "greater than 0" in str(exc_info.value)

        with pytest.raises(ValidationError) as exc_info:
            TFV3Settings(atr_fallback_pct=-0.01)
        assert "greater than 0" in str(exc_info.value)

    def test_portfolio_min_trade_size_validation(self):
        """Test PortfolioSettings min_trade_size validation."""
        # Test valid values
        valid_settings = PortfolioSettings(
            initial_balance=10000,
            risk_per_trade=0.02,
            max_leverage=10.0,
            asset_max_leverage=50.0,
            max_hold_time_hours=24,
            min_trade_size=0.001
        )
        assert valid_settings.min_trade_size == 0.001

        # Test zero (should be valid as it means no guard)
        valid_settings = PortfolioSettings(
            initial_balance=10000,
            risk_per_trade=0.02,
            max_leverage=10.0,
            asset_max_leverage=50.0,
            max_hold_time_hours=24,
            min_trade_size=0.0
        )
        assert valid_settings.min_trade_size == 0.0

        # Test negative values should fail
        with pytest.raises(ValidationError) as exc_info:
            PortfolioSettings(
                initial_balance=10000,
                risk_per_trade=0.02,
                max_leverage=10.0,
                asset_max_leverage=50.0,
                max_hold_time_hours=24,
                min_trade_size=-0.001
            )
        assert "greater than or equal to 0" in str(exc_info.value)

    def test_config_defaults_match_yaml(self):
        """Test that pydantic defaults match the YAML values."""
        # Create TFV3Settings with defaults
        tf_v3_defaults = TFV3Settings()
        assert tf_v3_defaults.atr_fallback_pct == 0.01

        # Create PortfolioSettings with defaults
        portfolio_defaults = PortfolioSettings(
            initial_balance=10000,
            risk_per_trade=0.02,
            max_leverage=10.0,
            asset_max_leverage=50.0,
            max_hold_time_hours=24
        )
        assert portfolio_defaults.min_trade_size == 0.001

    def test_atr_fallback_range_validation(self):
        """Test that atr_fallback_pct is properly constrained to reasonable range."""
        # Test boundary values
        # Just above minimum
        valid_settings = TFV3Settings(atr_fallback_pct=0.001)
        assert valid_settings.atr_fallback_pct == 0.001

        # Just below maximum
        valid_settings = TFV3Settings(atr_fallback_pct=0.199)
        assert valid_settings.atr_fallback_pct == 0.199

        # Test exactly at maximum should fail
        with pytest.raises(ValidationError) as exc_info:
            TFV3Settings(atr_fallback_pct=0.20)
        assert "less than 0.2" in str(exc_info.value)

        # Test way above maximum should fail
        with pytest.raises(ValidationError) as exc_info:
            TFV3Settings(atr_fallback_pct=0.50)
        assert "less than 0.2" in str(exc_info.value)

    def test_runtime_config_usage(self):
        """Test that runtime code actually uses config values instead of hard-coded ones."""

        # Create a temporary config with custom values
        custom_config_data = {
            'is_backtest': True,
            'data_paths': {
                'l2_data_root': '/tmp',
                'raw_l2_dir': '/tmp',
                'feature_1s_dir': '/tmp',
                'ohlcv_base_path': '/tmp',
                'log_dir': '/tmp'
            },
            'cache': {'l2_cache_max_size': 24},
            'backtest': {'period_preset': 'custom', 'custom_start_date': '2025-01-01', 'custom_end_date': '2025-01-02'},
            'simulation': {'latency_seconds': 0.5, 'max_impact_levels': 5},
            'strategies': {'use_trend_following': False, 'use_tf_v3': True},
            'portfolio': {
                'initial_balance': 10000,
                'risk_per_trade': 0.02,
                'max_leverage': 10.0,
                'asset_max_leverage': 50.0,
                'max_hold_time_hours': 24,
                'min_trade_size': 0.005  # Custom value different from default
            },
            'costs': {'taker_fee': 0.001, 'maker_fee': 0.001, 'l2_penalty_factor': 1.001},
            'regime': {'detector_type': 'continuous_gms'},
            'microstructure': {'depth_levels': 5},
            'indicators': {'adx_period': 14},
            'analysis': {'analyze_trades_after_backtest': True},
            'visualization': {'plot_enabled': False},
            'data_providers': {'fear_greed': {'enabled': False}},
            'timeframe': '1h',
            'gms': {'detector_type': 'continuous_gms'},
            'tf_v3': {
                'enabled': True,
                'ema_fast': 20,
                'ema_slow': 50,
                'atr_period': 14,
                'atr_trail_k': 3.0,
                'max_trade_life_h': 24,
                'risk_frac': 0.25,
                'max_notional': 25000,
                'gms_max_age_sec': 300,
                'atr_fallback_pct': 0.05  # Custom value different from default
            },
            'etl': {'l20_to_1s': {'chunk_sec': 3600}},
            'scheduler': {'etl_enabled': False}
        }

        # Test that config loads with custom values
        config = Config(**custom_config_data)
        assert config.tf_v3.atr_fallback_pct == 0.05
        assert config.portfolio.min_trade_size == 0.005

        # Test that the values are accessible via getattr (as used in runtime code)
        assert getattr(config.tf_v3, 'atr_fallback_pct', 0.01) == 0.05
        assert getattr(config.portfolio, 'min_trade_size', 0.001) == 0.005


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
