#!/usr/bin/env python3
"""
Unit tests for ATR injection in ETL pipeline.

Tests the new ATR calculation functionality that loads existing hourly OHLCV data
and injects ATR(14) values into 1-second feature parquet files.
"""

import os
import sys
import unittest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from tools.etl_l20_to_1s import (
    load_existing_hourly_ohlcv,
    calculate_atr_from_hourly_data,
    calculate_post_resample_features,
    merge_hourly_atr_to_seconds
)
from hyperliquid_bot.config.settings import Config


class MockConfig:
    """Mock configuration for testing."""
    def __init__(self, temp_dir):
        self.data_paths = MockDataPaths(temp_dir)


class MockDataPaths:
    """Mock data paths for testing."""
    def __init__(self, temp_dir):
        self.ohlcv_base_path = temp_dir


class TestETLATR(unittest.TestCase):
    """Test cases for ATR injection in ETL pipeline."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.config = MockConfig(self.temp_dir)
        
        # Create test directory structure
        self.hourly_dir = Path(self.temp_dir) / "1h"
        self.hourly_dir.mkdir(parents=True, exist_ok=True)
        
        # Create sample hourly OHLCV data
        self.create_sample_hourly_data()

    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)

    def create_sample_hourly_data(self):
        """Create sample hourly OHLCV data for testing."""
        # Create data for multiple days to test ATR calculation
        base_date = datetime(2025, 3, 1)
        
        for day_offset in range(25):  # 25 days of data
            current_date = base_date + timedelta(days=day_offset)
            
            # Create 24 hours of data for each day
            hourly_data = []
            base_price = 90000 + day_offset * 100  # Varying base price
            
            for hour in range(24):
                timestamp = current_date.replace(hour=hour, minute=0, second=0)
                
                # Create realistic OHLC data with some volatility
                open_price = base_price + np.random.normal(0, 100)
                high_price = open_price + abs(np.random.normal(50, 25))
                low_price = open_price - abs(np.random.normal(50, 25))
                close_price = open_price + np.random.normal(0, 50)
                
                hourly_data.append({
                    'timestamp': timestamp,
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'log_ret': np.random.normal(0, 0.01),
                    'realised_vol': abs(np.random.normal(0.02, 0.005))
                })
            
            # Save to parquet file
            df = pd.DataFrame(hourly_data)
            file_path = self.hourly_dir / f"{current_date.strftime('%Y-%m-%d')}_1h.parquet"
            df.to_parquet(file_path, index=False)

    def test_load_existing_hourly_ohlcv(self):
        """Test loading existing hourly OHLCV data."""
        date_str = "2025-03-03"
        
        # Load hourly data
        df = load_existing_hourly_ohlcv(date_str, self.config)
        
        # Verify data was loaded
        self.assertGreater(len(df), 0)
        self.assertIn('timestamp', df.columns)
        self.assertIn('open', df.columns)
        self.assertIn('high', df.columns)
        self.assertIn('low', df.columns)
        self.assertIn('close', df.columns)
        
        # Verify we have data from multiple days (for ATR calculation)
        unique_dates = df['timestamp'].dt.date.nunique()
        self.assertGreater(unique_dates, 1)

    def test_calculate_atr_from_hourly_data(self):
        """Test ATR calculation from hourly OHLCV data."""
        date_str = "2025-03-03"
        
        # Load hourly data
        df = load_existing_hourly_ohlcv(date_str, self.config)
        
        # Calculate ATR
        df_with_atr = calculate_atr_from_hourly_data(df)
        
        # Verify ATR column was added
        self.assertIn('atr_14', df_with_atr.columns)
        
        # Verify ATR values are reasonable
        atr_values = df_with_atr['atr_14'].dropna()
        self.assertGreater(len(atr_values), 0)
        self.assertTrue((atr_values > 0).all())  # ATR should be positive
        
        # Verify ATR has proper warmup period (first 14 values should be NaN)
        first_14_atr = df_with_atr['atr_14'].head(14)
        nan_count = first_14_atr.isna().sum()
        self.assertGreater(nan_count, 0)  # Should have some NaN values in warmup

    def test_atr_quality_gates(self):
        """Test quality gates for ATR calculation."""
        date_str = "2025-03-03"
        
        # Create sample 1-second data
        target_date = datetime.strptime(date_str, "%Y-%m-%d")
        timestamps = pd.date_range(
            start=target_date,
            end=target_date + timedelta(hours=23, minutes=59, seconds=59),
            freq='1S'
        )
        
        df_1s = pd.DataFrame({
            'timestamp': timestamps,
            'mid_price': 90000 + np.random.normal(0, 100, len(timestamps))
        })
        
        # Calculate post-resample features with ATR
        df_result = calculate_post_resample_features(df_1s, date_str=date_str, config=self.config)
        
        # Quality Gate 1: Columns exist and are float64
        self.assertIn('atr_14_sec', df_result.columns)
        self.assertIn('atr_percent_sec', df_result.columns)
        self.assertEqual(df_result['atr_14_sec'].dtype, np.float64)
        self.assertEqual(df_result['atr_percent_sec'].dtype, np.float64)
        
        # Quality Gate 2: NaN count < 14
        nan_count = df_result['atr_14_sec'].isna().sum()
        self.assertLess(nan_count, 14, f"ATR still NaN after warm-up: {nan_count} NaN values")
        
        # Quality Gate 3: Timestamp monotonic and UTC naive
        self.assertTrue(df_result['timestamp'].is_monotonic_increasing)
        self.assertIsNone(df_result['timestamp'].dt.tz)  # Should be timezone-naive

    def test_merge_hourly_atr_to_seconds(self):
        """Test merging hourly ATR to 1-second data."""
        # Create sample hourly data with ATR
        hourly_data = pd.DataFrame({
            'timestamp': pd.date_range('2025-03-03 00:00:00', periods=24, freq='1H'),
            'atr_14': np.random.uniform(100, 500, 24)
        })
        
        # Create sample 1-second data
        timestamps_1s = pd.date_range('2025-03-03 00:00:00', periods=3600, freq='1S')  # 1 hour
        df_1s = pd.DataFrame({
            'timestamp': timestamps_1s,
            'mid_price': 90000 + np.random.normal(0, 100, len(timestamps_1s))
        })
        
        # Merge ATR
        df_merged = merge_hourly_atr_to_seconds(df_1s, hourly_data)
        
        # Verify ATR was merged
        self.assertIn('atr_14_sec', df_merged.columns)
        
        # Verify forward-fill worked (all values in first hour should be the same)
        first_hour_atr = df_merged['atr_14_sec'].head(3600)
        unique_values = first_hour_atr.dropna().nunique()
        self.assertEqual(unique_values, 1)  # Should be forward-filled with same value


if __name__ == "__main__":
    unittest.main()
