#!/usr/bin/env python3
"""
Unit tests for OBI Scalper tick size configuration (Task R-105d).

Tests that the tick_size parameter is properly configurable and used
by both OBI scalper strategy implementations.
"""

import unittest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import MagicMock

from hyperliquid_bot.config.settings import Config, OBIScalperDefaults
from hyperliquid_bot.strategies.obi_scalper import OBIScalperStrategy as OBIScalperEvaluator
from hyperliquid_bot.strategies.obi_scalper_strategy import OBIScalperStrategy


class TestOBIScalperTickSizeConfig(unittest.TestCase):
    """Test cases for OBI Scalper tick size configuration."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a minimal config for testing
        self.base_config_data = {
            "is_backtest": True,
            "data_paths": {
                "l2_data_root": "/tmp",
                "ohlcv_base_path": "/tmp",
                "log_dir": "/tmp",
                "raw_l2_dir": "/tmp",
                "feature_1s_dir": "/tmp",
                "require_ohlcv_volume": False
            },
            "cache": {"l2_cache_max_size": 1},
            "backtest": {"period_preset": "full"},
            "simulation": {
                "latency_seconds": 0,
                "max_impact_levels": 1,
                "force_taker_execution": True
            },
            "strategies": {
                "use_trend_following": False,
                "use_mean_reversion": False,
                "use_mean_variance": False,
                "use_obi_scalper": True,
                "use_tf_v3": False,
                "OBIScalperStrategy": {
                    "enabled": True,
                    "defaults": {
                        "vol_veto_threshold": 0.002,
                        "spread_veto_threshold": 0.0001,
                        "obi_l1_3_trigger": 0.50,
                        "tp_ticks": 7,
                        "sl_ticks": 5,
                        "timeout_seconds": 30,
                        "allowed_gms_states": ["CHOP", "BULL", "BEAR"],
                        "zero_sign_eps": 0.001,
                        "tick_size": 0.01  # Default value
                    }
                }
            },
            "portfolio": {
                "initial_balance": 10000,
                "risk_per_trade": 0.01,
                "max_leverage": 10,
                "asset_max_leverage": 50,
                "max_hold_time_hours": 24
            },
            "costs": {
                "taker_fee": 0.0005,
                "maker_fee": 0.0002,
                "l2_penalty_factor": 1.1
            },
            "regime": {"detector_type": "continuous_gms"},
            "microstructure": {"depth_levels": 5},
            "indicators": {},
            "analysis": {},
            "visualization": {},
            "gms": {},
            "tf_v3": {},
            "etl": {},
            "scheduler": {}
        }

    def test_default_tick_size_config(self):
        """Test that default tick size (0.01) is properly loaded from config."""
        config = Config(**self.base_config_data)

        # Verify the default tick size is set correctly
        self.assertEqual(config.strategies.OBIScalperStrategy.defaults.tick_size, 0.01)

    def test_custom_tick_size_config(self):
        """Test that custom tick size values are properly loaded from config."""
        # Test with a different tick size
        custom_config_data = self.base_config_data.copy()
        custom_config_data["strategies"]["OBIScalperStrategy"]["defaults"]["tick_size"] = 0.05

        config = Config(**custom_config_data)

        # Verify the custom tick size is set correctly
        self.assertEqual(config.strategies.OBIScalperStrategy.defaults.tick_size, 0.05)

    def test_tick_size_validation(self):
        """Test that tick size validation works correctly."""
        # Test with invalid tick size (zero)
        invalid_config_data = self.base_config_data.copy()
        invalid_config_data["strategies"]["OBIScalperStrategy"]["defaults"]["tick_size"] = 0.0

        with self.assertRaises(ValueError):
            Config(**invalid_config_data)

        # Test with invalid tick size (negative)
        invalid_config_data["strategies"]["OBIScalperStrategy"]["defaults"]["tick_size"] = -0.01

        with self.assertRaises(ValueError):
            Config(**invalid_config_data)

    def test_obi_scalper_evaluator_uses_config_tick_size(self):
        """Test that OBIScalperStrategy (evaluator) uses configured tick size."""
        # Test with custom tick size
        custom_config_data = self.base_config_data.copy()
        custom_config_data["strategies"]["OBIScalperStrategy"]["defaults"]["tick_size"] = 0.05

        config = Config(**custom_config_data)
        strategy = OBIScalperEvaluator(config, "test_obi_scalper")

        # Create test signals with tick_size to verify fallback behavior
        test_signals_with_tick_size = {
            'close': 50000.0,
            'account_equity': 10000.0,
            'tick_size': 0.02  # This should be used instead of config
        }

        test_signals_without_tick_size = {
            'close': 50000.0,
            'account_equity': 10000.0
            # No tick_size - should use config default
        }

        # Mock the _get_direction method to return a direction
        strategy._get_direction = MagicMock(return_value="long")

        # Test 1: When tick_size is provided in signals, it should be used
        direction, trade_info = strategy.evaluate(test_signals_with_tick_size)
        if trade_info is not None:  # Only test if evaluation succeeded
            expected_stop_distance_with_signal = config.strategies.scalper_stop_ticks * 0.02
            self.assertEqual(trade_info['stop_distance'], expected_stop_distance_with_signal)

        # Test 2: When tick_size is not provided, config default should be used
        direction, trade_info = strategy.evaluate(test_signals_without_tick_size)
        if trade_info is not None:  # Only test if evaluation succeeded
            expected_stop_distance_config = config.strategies.scalper_stop_ticks * 0.05
            self.assertEqual(trade_info['stop_distance'], expected_stop_distance_config)

    def test_obi_scalper_strategy_uses_config_tick_size(self):
        """Test that OBIScalperStrategy (main strategy) uses configured tick size."""
        # Test with custom tick size
        custom_config_data = self.base_config_data.copy()
        custom_config_data["strategies"]["OBIScalperStrategy"]["defaults"]["tick_size"] = 0.025

        config = Config(**custom_config_data)
        strategy = OBIScalperStrategy(config, "test_obi_scalper")

        # Verify the strategy instance has the correct tick size
        self.assertEqual(strategy.tick_size, 0.025)

    def test_yaml_config_loading(self):
        """Test loading tick size configuration from YAML file."""
        # Create a temporary YAML config file
        yaml_config = {
            **self.base_config_data,
            "strategies": {
                **self.base_config_data["strategies"],
                "OBIScalperStrategy": {
                    "enabled": True,
                    "defaults": {
                        **self.base_config_data["strategies"]["OBIScalperStrategy"]["defaults"],
                        "tick_size": 0.005  # Custom tick size in YAML
                    }
                }
            }
        }

        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(yaml_config, f)
            temp_yaml_path = f.name

        try:
            # Load config from YAML file
            with open(temp_yaml_path, 'r') as f:
                config_data = yaml.safe_load(f)

            config = Config(**config_data)

            # Verify the tick size was loaded correctly from YAML
            self.assertEqual(config.strategies.OBIScalperStrategy.defaults.tick_size, 0.005)

        finally:
            # Clean up temporary file
            Path(temp_yaml_path).unlink()

    def test_obi_scalper_defaults_model(self):
        """Test the OBIScalperDefaults model directly."""
        # Test with default values
        defaults = OBIScalperDefaults()
        self.assertEqual(defaults.tick_size, 0.01)

        # Test with custom values
        custom_defaults = OBIScalperDefaults(tick_size=0.1)
        self.assertEqual(custom_defaults.tick_size, 0.1)

        # Test validation
        with self.assertRaises(ValueError):
            OBIScalperDefaults(tick_size=0.0)

        with self.assertRaises(ValueError):
            OBIScalperDefaults(tick_size=-0.01)


if __name__ == '__main__':
    unittest.main()
