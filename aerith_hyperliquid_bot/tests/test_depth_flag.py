"""
Unit tests for the depth_levels flag in the microstructure settings.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock

from hyperliquid_bot.config.settings import Config, MicrostructureSettings
from hyperliquid_bot.features.microstructure import calculate_order_book_imbalance, calculate_depth_metrics
from hyperliquid_bot.data.handler import HistoricalDataHandler


def create_mock_l2_snapshot(levels=20):
    """Create a mock L2 snapshot with the specified number of levels."""
    bids = [(100.0 - i, 10.0 + i) for i in range(levels)]
    asks = [(100.0 + i, 10.0 + i) for i in range(levels)]
    return (bids, asks)


def create_mock_l2_df(levels=20):
    """Create a mock L2 DataFrame with the specified number of levels."""
    df = pd.DataFrame()
    df['timestamp'] = [pd.Timestamp('2023-01-01 00:00:00')]
    
    # Create bid and ask columns
    for i in range(1, levels + 1):
        df[f'bid_price_{i}'] = [100.0 - i + 1]
        df[f'bid_size_{i}'] = [10.0 + i - 1]
        df[f'ask_price_{i}'] = [100.0 + i - 1]
        df[f'ask_size_{i}'] = [10.0 + i - 1]
    
    return df


class TestDepthFlag:
    """Test the depth_levels flag in the microstructure settings."""

    def test_depth_5(self):
        """Test that depth_levels=5 produces raw_obi_5 column."""
        # Create a mock config with depth_levels=5
        config = MagicMock()
        config.microstructure = MagicMock()
        config.microstructure.depth_levels = 5
        config.microstructure.obi_levels = 5
        config.microstructure.depth_levels_for_calc = 5
        
        # Create a mock L2 snapshot
        l2_snapshot = create_mock_l2_snapshot(levels=20)
        
        # Calculate OBI with depth_levels=5
        obi = calculate_order_book_imbalance(l2_snapshot, levels=config.microstructure.depth_levels)
        
        # Calculate depth metrics with depth_levels=5
        depth_ratio, depth_pressure = calculate_depth_metrics(l2_snapshot, levels=config.microstructure.depth_levels)
        
        # Check that the calculations used only 5 levels
        assert obi is not None
        assert depth_ratio is not None
        assert depth_pressure is not None
        
        # Create a mock data handler
        with patch('hyperliquid_bot.data.handler.HistoricalDataHandler._load_l2_segment') as mock_load:
            # Mock the L2 data loading
            mock_load.return_value = create_mock_l2_df(levels=20)
            
            # Mock the OHLCV data
            ohlcv_data = pd.DataFrame({
                'open': [100.0],
                'high': [101.0],
                'low': [99.0],
                'close': [100.5],
                'volume': [1000.0]
            }, index=[pd.Timestamp('2023-01-01 00:00:00')])
            
            # Create a data handler with the mock config
            handler = HistoricalDataHandler(config)
            
            # Mock the _integrate_microstructure_features method
            with patch.object(handler, '_integrate_microstructure_features') as mock_integrate:
                # Set the ohlcv_data attribute
                handler.ohlcv_data = ohlcv_data
                
                # Call the _calculate_features_from_row method
                row = create_mock_l2_df(levels=20).iloc[0]
                features = handler._calculate_features_from_row(row)
                
                # Check that the features include raw_obi_5
                assert f'raw_obi_{config.microstructure.depth_levels}' in features.index
                assert f'raw_depth_ratio_{config.microstructure.depth_levels}' in features.index
                assert f'raw_depth_pressure_{config.microstructure.depth_levels}' in features.index

    def test_depth_20(self):
        """Test that depth_levels=20 produces raw_obi_20 column."""
        # Create a mock config with depth_levels=20
        config = MagicMock()
        config.microstructure = MagicMock()
        config.microstructure.depth_levels = 20
        config.microstructure.obi_levels = 20
        config.microstructure.depth_levels_for_calc = 20
        
        # Create a mock L2 snapshot
        l2_snapshot = create_mock_l2_snapshot(levels=20)
        
        # Calculate OBI with depth_levels=20
        obi = calculate_order_book_imbalance(l2_snapshot, levels=config.microstructure.depth_levels)
        
        # Calculate depth metrics with depth_levels=20
        depth_ratio, depth_pressure = calculate_depth_metrics(l2_snapshot, levels=config.microstructure.depth_levels)
        
        # Check that the calculations used 20 levels
        assert obi is not None
        assert depth_ratio is not None
        assert depth_pressure is not None
        
        # Create a mock data handler
        with patch('hyperliquid_bot.data.handler.HistoricalDataHandler._load_l2_segment') as mock_load:
            # Mock the L2 data loading
            mock_load.return_value = create_mock_l2_df(levels=20)
            
            # Mock the OHLCV data
            ohlcv_data = pd.DataFrame({
                'open': [100.0],
                'high': [101.0],
                'low': [99.0],
                'close': [100.5],
                'volume': [1000.0]
            }, index=[pd.Timestamp('2023-01-01 00:00:00')])
            
            # Create a data handler with the mock config
            handler = HistoricalDataHandler(config)
            
            # Mock the _integrate_microstructure_features method
            with patch.object(handler, '_integrate_microstructure_features') as mock_integrate:
                # Set the ohlcv_data attribute
                handler.ohlcv_data = ohlcv_data
                
                # Call the _calculate_features_from_row method
                row = create_mock_l2_df(levels=20).iloc[0]
                features = handler._calculate_features_from_row(row)
                
                # Check that the features include raw_obi_20
                assert f'raw_obi_{config.microstructure.depth_levels}' in features.index
                assert f'raw_depth_ratio_{config.microstructure.depth_levels}' in features.index
                assert f'raw_depth_pressure_{config.microstructure.depth_levels}' in features.index

    def test_settings_validation_conflict(self):
        """Test that validation fails when depth_levels, obi_levels, and depth_levels_for_calc conflict."""
        # Create settings with conflicting values
        with pytest.raises(ValueError):
            settings = MicrostructureSettings(
                depth_levels=5,
                obi_levels=20,
                depth_levels_for_calc=5
            )
            
        # Create settings with conflicting values
        with pytest.raises(ValueError):
            settings = MicrostructureSettings(
                depth_levels=5,
                obi_levels=5,
                depth_levels_for_calc=20
            )
            
        # Create settings with matching values (should not raise)
        settings = MicrostructureSettings(
            depth_levels=5,
            obi_levels=5,
            depth_levels_for_calc=5
        )
        assert settings.depth_levels == 5
        assert settings.obi_levels == 5
        assert settings.depth_levels_for_calc == 5
        
        # Create settings with None values (should map to depth_levels)
        settings = MicrostructureSettings(
            depth_levels=5,
            obi_levels=None,
            depth_levels_for_calc=None
        )
        assert settings.depth_levels == 5
        assert settings.obi_levels == 5
        assert settings.depth_levels_for_calc == 5
