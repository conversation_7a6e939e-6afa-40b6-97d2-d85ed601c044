"""
Unit tests for SignalEngine fallback functionality.

Tests the robustness improvements in SignalEngine:
- ROC fallback calculation
- NaN-tolerant depth metrics
- Empty DataFrame handling
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.signals.calculator import SignalEngine


@pytest.fixture
def mock_config():
    """Create a mock Config object with necessary settings."""
    config = MagicMock(spec=Config)

    # Set up nested attributes
    config.indicators = MagicMock()
    config.indicators.gms_roc_period = 5
    config.indicators.require_volume_for_signals = False
    config.indicators.gms_ma_slope_period = 10
    config.indicators.gms_atr_percent_period = 14

    config.strategies = MagicMock()
    config.strategies.use_trend_following = False
    config.strategies.use_mean_reversion = False
    config.strategies.use_mean_variance = False

    config.regime = MagicMock()
    config.regime.detector_type = 'granular_microstructure'

    config.microstructure = MagicMock()
    config.microstructure.depth_levels = 5
    config.microstructure.obi_smoothing_window = 8
    config.microstructure.obi_smoothing_type = 'sma'
    config.microstructure.spread_rolling_window = 24
    config.microstructure.spread_metric_to_roll = 'relative'
    config.microstructure.allow_nan_micro_depth = True

    return config


@pytest.fixture
def mock_data_handler():
    """Create a mock DataHandler that returns test data."""
    data_handler = MagicMock()

    # Create test DataFrame with OHLCV data
    dates = pd.date_range(start='2025-01-01', periods=100, freq='h')
    df = pd.DataFrame({
        'open': np.random.normal(100, 5, 100),
        'high': np.random.normal(105, 5, 100),
        'low': np.random.normal(95, 5, 100),
        'close': np.random.normal(100, 5, 100),
        'volume': np.random.normal(1000, 100, 100),
        'raw_obi_5': np.random.normal(0, 0.2, 100),
        'raw_spread_rel': np.random.normal(0.001, 0.0002, 100),
        'raw_spread_abs': np.random.normal(0.1, 0.02, 100),
        'fear_greed_idx': np.random.normal(50, 10, 100),
        # Add required depth columns
        'raw_depth_ratio_5': np.random.normal(1.0, 0.1, 100),
        'raw_depth_pressure_5': np.random.normal(0.5, 0.1, 100),
        'bid_slope': np.random.normal(0.01, 0.005, 100),
        'ask_slope': np.random.normal(0.01, 0.005, 100),
        'book_asymmetry': np.random.normal(0, 0.1, 100),
    }, index=dates)

    data_handler.get_ohlcv_data.return_value = df
    return data_handler


def test_roc_fallback_calculation(mock_config, mock_data_handler):
    """Test that ROC is calculated as fallback when pandas-ta fails."""
    signal_engine = SignalEngine(mock_config, mock_data_handler)

    # Mock pandas-ta.roc to simulate failure
    with patch('pandas.DataFrame.ta.roc', side_effect=Exception("Simulated pandas-ta failure")):
        signals_df = signal_engine.calculate_all_signals()

    # Verify ROC was calculated as fallback
    assert 'roc' in signals_df.columns
    assert not signals_df['roc'].isna().all()

    # Verify the values are reasonable (pct_change * 100)
    expected_roc = signals_df['close'].pct_change(periods=mock_config.indicators.gms_roc_period) * 100
    pd.testing.assert_series_equal(signals_df['roc'], expected_roc, check_names=False)


def test_depth_metrics_nan_tolerance(mock_config, mock_data_handler):
    """Test that SignalEngine continues when depth metrics are NaN."""
    signal_engine = SignalEngine(mock_config, mock_data_handler)

    # Get the original data
    df = mock_data_handler.get_ohlcv_data()

    # Add required columns for depth metrics but with all NaN values
    df['bid_slope'] = np.nan
    df['ask_slope'] = np.nan
    df['book_asymmetry'] = np.nan

    # Update the mock to return this modified DataFrame
    mock_data_handler.get_ohlcv_data.return_value = df

    # Set up the regime config to request depth metrics
    mock_config.regime.gms_depth_slope_thin_limit = 0.5
    mock_config.regime.gms_depth_skew_thresh = 0.3

    # Calculate signals - this should not raise an exception
    signals_df = signal_engine.calculate_all_signals()

    # Verify depth metrics columns exist but contain NaN
    assert 'depth_slope' in signals_df.columns
    assert 'depth_skew' in signals_df.columns
    assert signals_df['depth_slope'].isna().all()
    assert signals_df['depth_skew'].isna().all()

    # Verify other calculations proceeded normally
    assert not signals_df['close'].isna().any()
    assert 'roc' in signals_df.columns
    assert not signals_df['roc'].isna().all()


def test_allow_nan_micro_depth_setting(mock_config, mock_data_handler):
    """Test that allow_nan_micro_depth setting controls behavior."""
    # First test with allow_nan_micro_depth = True (default)
    signal_engine = SignalEngine(mock_config, mock_data_handler)

    # Get the original data
    df = mock_data_handler.get_ohlcv_data()

    # Add required columns for depth metrics but with all NaN values
    df['bid_slope'] = np.nan
    df['ask_slope'] = np.nan
    df['book_asymmetry'] = np.nan

    # Update the mock to return this modified DataFrame
    mock_data_handler.get_ohlcv_data.return_value = df

    # Set up the regime config to request depth metrics
    mock_config.regime.gms_depth_slope_thin_limit = 0.5
    mock_config.regime.gms_depth_skew_thresh = 0.3

    # Calculate signals - this should not raise an exception
    signals_df = signal_engine.calculate_all_signals()

    # Verify depth metrics columns exist but contain NaN
    assert 'depth_slope' in signals_df.columns
    assert 'depth_skew' in signals_df.columns

    # Now test with allow_nan_micro_depth = False
    mock_config.microstructure.allow_nan_micro_depth = False

    # Calculate signals - this should still not raise an exception
    signals_df = signal_engine.calculate_all_signals()

    # Verify depth metrics columns exist but contain NaN
    assert 'depth_slope' in signals_df.columns
    assert 'depth_skew' in signals_df.columns
    assert signals_df['depth_slope'].isna().all()
    assert signals_df['depth_skew'].isna().all()


def test_empty_dataframe_handling(mock_config, mock_data_handler):
    """Test that empty DataFrame after calculation raises a descriptive error."""
    signal_engine = SignalEngine(mock_config, mock_data_handler)

    # Create a DataFrame with required columns but no data
    empty_df = pd.DataFrame(columns=[
        'open', 'high', 'low', 'close', 'volume',
        'raw_obi_5', 'raw_spread_rel', 'raw_spread_abs',
        'raw_depth_ratio_5', 'raw_depth_pressure_5',
        'bid_slope', 'ask_slope', 'book_asymmetry',
        'fear_greed_idx'
    ])

    # Make the data handler return this DataFrame
    mock_data_handler.get_ohlcv_data.return_value = empty_df

    # Calculate signals - this should raise a ValueError
    with pytest.raises(ValueError) as excinfo:
        signal_engine.calculate_all_signals()

    # Verify the error message is descriptive
    assert "Empty DataFrame" in str(excinfo.value)
