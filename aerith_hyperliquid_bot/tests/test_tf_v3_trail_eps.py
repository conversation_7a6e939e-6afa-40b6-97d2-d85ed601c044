"""
Unit tests for TF-v3 trailing-stop epsilon configuration (Task R-105c).

Tests that the trail_eps configuration parameter is properly used
instead of hardcoded 0.01 values in the trailing stop logic.
"""

import pytest
from datetime import datetime
from unittest.mock import MagicMock

from hyperliquid_bot.config.settings import TFV3Settings
from hyperliquid_bot.strategies.tf_v3 import TFV3Strategy


def test_default_trail_eps_config():
    """Test that the default trail_eps value (0.01) is loaded correctly."""
    tf_v3_config = TFV3Settings()
    assert tf_v3_config.trail_eps == 0.01


def test_custom_trail_eps_config():
    """Test that a custom trail_eps value (0.05) is loaded correctly."""
    tf_v3_config = TFV3Settings(trail_eps=0.05)
    assert tf_v3_config.trail_eps == 0.05


def test_trail_eps_validation():
    """Test that trail_eps validation works correctly."""
    # Test valid values
    valid_config = TFV3Settings(trail_eps=0.001)
    assert valid_config.trail_eps == 0.001

    valid_config = TFV3Settings(trail_eps=0.1)
    assert valid_config.trail_eps == 0.1

    # Test invalid values (should raise ValidationError)
    from pydantic import ValidationError

    with pytest.raises(ValidationError) as exc_info:
        TFV3Settings(trail_eps=0.0)
    assert "greater than 0" in str(exc_info.value)

    with pytest.raises(ValidationError) as exc_info:
        TFV3Settings(trail_eps=-0.01)
    assert "greater than 0" in str(exc_info.value)


@pytest.fixture
def mock_config():
    """Create a mock config for testing."""
    config = MagicMock()
    config.tf_v3 = TFV3Settings(trail_eps=0.05)  # Custom value for testing
    config.strategies = MagicMock()
    config.strategies.use_tf_v3 = True
    config.regime = MagicMock()
    config.regime.detector_type = 'rule_based'
    config.microstructure = MagicMock()
    config.gms = MagicMock()
    return config


def test_strategy_uses_config_trail_eps(mock_config):
    """Test that the strategy uses the trail_eps value from config."""
    # Create strategy instance
    strategy = TFV3Strategy(mock_config, "tf_v3", None)

    # Mock the GMSProvider
    strategy.gms_provider = MagicMock()
    strategy.gms_provider.latest.return_value = {
        'state': 'BULL',
        'risk_suppressed': False,
        'timestamp': datetime.now(),
        'regime_timestamp': datetime.now()
    }

    # Verify the strategy has the correct trail_eps value
    assert strategy.tf_v3_config.trail_eps == 0.05


def test_trailing_stop_epsilon_integration():
    """Test that the trailing stop logic uses the configured epsilon value."""
    # Create a mock config with custom trail_eps
    config = MagicMock()
    config.tf_v3 = TFV3Settings(trail_eps=0.05)  # Custom value
    config.strategies = MagicMock()
    config.strategies.use_tf_v3 = True
    config.regime = MagicMock()
    config.regime.detector_type = 'rule_based'
    config.microstructure = MagicMock()
    config.gms = MagicMock()

    # Create strategy instance
    strategy = TFV3Strategy(config, "tf_v3", None)

    # Mock the GMSProvider
    strategy.gms_provider = MagicMock()

    # Verify the strategy has the correct trail_eps value
    assert strategy.tf_v3_config.trail_eps == 0.05

    # Test that the epsilon value is accessible for use in trailing stop logic
    # This confirms the configuration is properly loaded and available
    epsilon = strategy.tf_v3_config.trail_eps
    assert epsilon == 0.05

    # Test basic arithmetic operations that would be used in trailing stop logic
    trail_price = 100.0
    new_trail_price_long = max(trail_price + 1.0, trail_price + epsilon)
    new_trail_price_short = min(trail_price - 1.0, trail_price - epsilon)

    assert new_trail_price_long == trail_price + 1.0  # Should use the larger value
    assert new_trail_price_short == trail_price - 1.0  # Should use the smaller value


def test_yaml_config_loads_trail_eps():
    """Test that trail_eps can be loaded from YAML configuration."""
    from pathlib import Path
    import yaml

    # Load the actual base.yaml to verify trail_eps is present
    project_root = Path(__file__).parent.parent
    config_path = project_root / "configs" / "base.yaml"

    if config_path.exists():
        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f)

        # Check that trail_eps is present in the tf_v3 section
        assert 'tf_v3' in config_data
        assert 'trail_eps' in config_data['tf_v3']
        assert config_data['tf_v3']['trail_eps'] == 0.01  # Default value
    else:
        # If config file doesn't exist, just test the TFV3Settings default
        tf_v3_config = TFV3Settings()
        assert tf_v3_config.trail_eps == 0.01
