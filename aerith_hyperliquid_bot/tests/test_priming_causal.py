#!/usr/bin/env python
"""
Unit tests for adaptive threshold priming functionality (Task R-112m).

Tests that the priming hook correctly loads historical data and pre-populates
adaptive threshold buffers without look-ahead bias.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import tempfile
import shutil

from hyperliquid_bot.core.gms_detector import ContinuousGMSDetector
from hyperliquid_bot.config.settings import Config


class TestAdaptiveThresholdPriming:
    """Test suite for adaptive threshold priming functionality."""

    @pytest.fixture
    def temp_feature_dir(self):
        """Create a temporary directory with synthetic feature files."""
        temp_dir = tempfile.mkdtemp()
        feature_dir = Path(temp_dir) / "features_1s"
        feature_dir.mkdir(parents=True)

        # Create synthetic feature data for 2 days
        base_date = datetime(2025, 3, 1)

        for day_offset in [-1, 0]:  # Day before and start day
            date = base_date + timedelta(days=day_offset)
            date_str = date.strftime('%Y-%m-%d')
            date_dir = feature_dir / date_str
            date_dir.mkdir()

            # Create hourly files
            for hour in range(24):
                # Generate synthetic data for this hour
                timestamps = pd.date_range(
                    start=f"{date_str} {hour:02d}:00:00",
                    end=f"{date_str} {hour:02d}:59:59",
                    freq="1s"
                )

                # Create realistic synthetic data
                n_samples = len(timestamps)
                data = {
                    'timestamp': timestamps,
                    'atr_percent_sec': np.random.uniform(0.001, 0.05, n_samples),
                    'ma_slope': np.random.uniform(-10, 10, n_samples),
                    'spread_mean': np.random.uniform(0.0001, 0.001, n_samples)
                }

                df = pd.DataFrame(data)
                hour_file = date_dir / f"features_{hour:02d}.parquet"
                df.to_parquet(hour_file)

        yield feature_dir

        # Cleanup
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def mock_config_with_priming(self, temp_feature_dir):
        """Create a mock config with priming enabled."""
        config = Mock(spec=Config)

        # Basic config sections
        config.regime = Mock()
        config.regime.use_filter = True
        config.regime.detector_type = 'continuous_gms'
        config.regime.get_detector_settings = Mock(return_value={
            'gms_vol_high_thresh': 0.06,
            'gms_vol_low_thresh': 0.02,
            'gms_mom_strong_thresh': 5.0,
            'gms_mom_weak_thresh': 1.0,
            'gms_spread_std_high_thresh': 0.0005,
            'gms_spread_mean_low_thresh': 0.0001,
        })
        config.regime.get_detector_operational_settings = Mock(return_value={
            'cadence_sec': 60,
            'output_states': 8,
            'risk_suppressed_notional_frac': 0.25,
            'risk_suppressed_pnl_atr_mult': 1.5,
        })

        config.microstructure = Mock()
        config.microstructure.depth_levels = 5
        config.microstructure.gms_obi_strong_confirm_thresh = 0.2
        config.microstructure.gms_obi_weak_confirm_thresh = 0.05

        config.indicators = Mock()
        config.indicators.adx_threshold = 30.0

        config.portfolio = Mock()

        # GMS config with priming enabled
        config.gms = Mock()
        config.gms.auto_thresholds = True
        config.gms.percentile_window_sec = 3600  # 1 hour for testing
        config.gms.vol_low_pct = 0.15
        config.gms.vol_high_pct = 0.50
        config.gms.mom_low_pct = 0.15
        config.gms.mom_high_pct = 0.50
        config.gms.min_history_rows = 10
        config.gms.priming_hours = 1  # Prime with 1 hour of data

        # Data paths
        config.data_paths = Mock()
        config.data_paths.feature_1s_dir = str(temp_feature_dir)

        # Start date for backtest
        config.start_date = datetime(2025, 3, 1, 0, 0, 0)

        return config

    def test_priming_disabled_by_default(self):
        """Test that priming is disabled when priming_hours is 0."""
        config = Mock(spec=Config)
        config.regime = Mock()
        config.regime.use_filter = True
        config.regime.detector_type = 'continuous_gms'
        config.regime.get_detector_settings = Mock(return_value={})
        config.regime.get_detector_operational_settings = Mock(return_value={
            'cadence_sec': 60,
            'output_states': 8,
            'risk_suppressed_notional_frac': 0.25,
            'risk_suppressed_pnl_atr_mult': 1.5,
        })

        config.microstructure = Mock()
        config.microstructure.depth_levels = 5
        config.microstructure.gms_obi_strong_confirm_thresh = 0.2
        config.microstructure.gms_obi_weak_confirm_thresh = 0.05
        config.indicators = Mock()
        config.indicators.adx_threshold = 30.0
        config.portfolio = Mock()

        # GMS config with priming disabled
        config.gms = Mock()
        config.gms.auto_thresholds = True
        config.gms.percentile_window_sec = 3600
        config.gms.vol_low_pct = 0.15
        config.gms.vol_high_pct = 0.50
        config.gms.mom_low_pct = 0.15
        config.gms.mom_high_pct = 0.50
        config.gms.min_history_rows = 10
        config.gms.priming_hours = 0  # Disabled

        # Should not call priming method
        with patch.object(ContinuousGMSDetector, '_prime_adaptive_thresholds') as mock_prime:
            detector = ContinuousGMSDetector(config)
            mock_prime.assert_not_called()

    def test_priming_with_historical_data(self, mock_config_with_priming):
        """Test that priming correctly loads historical data and populates buffers."""
        detector = ContinuousGMSDetector(mock_config_with_priming)

        # Check that adaptive thresholds were initialized
        assert detector.adaptive_vol_threshold is not None
        assert detector.adaptive_mom_threshold is not None

        # Check that buffers have been populated
        vol_stats = detector.adaptive_vol_threshold.get_buffer_stats()
        mom_stats = detector.adaptive_mom_threshold.get_buffer_stats()

        # Should have some data in buffers (1 hour = 3600 seconds)
        assert vol_stats['size'] > 0
        assert mom_stats['size'] > 0

        # Buffers should not exceed the window length
        assert vol_stats['size'] <= 3600
        assert mom_stats['size'] <= 3600

        # Should have valid statistics
        assert vol_stats['min'] is not None
        assert vol_stats['max'] is not None
        assert mom_stats['min'] is not None
        assert mom_stats['max'] is not None

    def test_priming_with_missing_data(self, temp_feature_dir):
        """Test priming behavior when historical data is missing."""
        config = Mock(spec=Config)
        config.regime = Mock()
        config.regime.use_filter = True
        config.regime.detector_type = 'continuous_gms'
        config.regime.get_detector_settings = Mock(return_value={})
        config.regime.get_detector_operational_settings = Mock(return_value={
            'cadence_sec': 60,
            'output_states': 8,
            'risk_suppressed_notional_frac': 0.25,
            'risk_suppressed_pnl_atr_mult': 1.5,
        })

        config.microstructure = Mock()
        config.microstructure.depth_levels = 5
        config.microstructure.gms_obi_strong_confirm_thresh = 0.2
        config.microstructure.gms_obi_weak_confirm_thresh = 0.05
        config.indicators = Mock()
        config.indicators.adx_threshold = 30.0
        config.portfolio = Mock()

        # GMS config with priming enabled
        config.gms = Mock()
        config.gms.auto_thresholds = True
        config.gms.percentile_window_sec = 3600
        config.gms.vol_low_pct = 0.15
        config.gms.vol_high_pct = 0.50
        config.gms.mom_low_pct = 0.15
        config.gms.mom_high_pct = 0.50
        config.gms.min_history_rows = 10
        config.gms.priming_hours = 24  # Try to prime with 24 hours

        # Point to non-existent directory
        config.data_paths = Mock()
        config.data_paths.feature_1s_dir = "/non/existent/path"

        # Start date for backtest
        config.start_date = datetime(2025, 3, 1, 0, 0, 0)

        # Should not crash, just log warning and continue
        detector = ContinuousGMSDetector(config)

        # Adaptive thresholds should still be initialized but empty
        assert detector.adaptive_vol_threshold is not None
        assert detector.adaptive_mom_threshold is not None

        # Buffers should be empty
        vol_stats = detector.adaptive_vol_threshold.get_buffer_stats()
        mom_stats = detector.adaptive_mom_threshold.get_buffer_stats()

        assert vol_stats['size'] == 0
        assert mom_stats['size'] == 0

    def test_priming_time_range_calculation(self, mock_config_with_priming):
        """Test that priming calculates correct time ranges."""
        # Mock the _load_historical_features method to check the time range
        with patch.object(ContinuousGMSDetector, '_load_historical_features') as mock_load:
            mock_load.return_value = pd.DataFrame()  # Return empty DataFrame

            detector = ContinuousGMSDetector(mock_config_with_priming)

            # Check that _load_historical_features was called with correct time range
            mock_load.assert_called_once()
            start_time, end_time = mock_load.call_args[0]

            # Should be 1 hour before start_date
            expected_start = datetime(2025, 2, 28, 23, 0, 0)  # 1 hour before 2025-03-01 00:00:00
            expected_end = datetime(2025, 3, 1, 0, 0, 0)

            assert start_time == expected_start
            assert end_time == expected_end

    def test_priming_causal_property(self, mock_config_with_priming):
        """Test that priming only uses data chronologically before start time."""
        detector = ContinuousGMSDetector(mock_config_with_priming)

        # Create test data that spans across the start time
        start_time = datetime(2025, 3, 1, 0, 0, 0)

        # Data before start time (should be included)
        before_data = pd.DataFrame({
            'timestamp': [start_time - timedelta(minutes=30)],
            'atr_percent_sec': [0.02],
            'ma_slope': [5.0]
        })

        # Data after start time (should be excluded)
        after_data = pd.DataFrame({
            'timestamp': [start_time + timedelta(minutes=30)],
            'atr_percent_sec': [0.03],
            'ma_slope': [7.0]
        })

        combined_data = pd.concat([before_data, after_data], ignore_index=True)

        # Test the filtering logic in _load_historical_features
        priming_start = start_time - timedelta(hours=1)
        priming_end = start_time

        # Filter data as the method would
        mask = (combined_data['timestamp'] >= priming_start) & (combined_data['timestamp'] < priming_end)
        filtered_data = combined_data[mask]

        # Should only include data before start time
        assert len(filtered_data) == 1
        assert filtered_data.iloc[0]['timestamp'] == start_time - timedelta(minutes=30)
