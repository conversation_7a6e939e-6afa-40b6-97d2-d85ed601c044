"""
Unit tests for the is_backtest flag functionality.

Tests that the new is_backtest flag correctly controls backtest-specific behavior
in the risk manager and TF-v3 strategy, replacing the legacy backtest detection.
"""

import pytest


class TestIsBacktestFlag:
    """Test the is_backtest flag functionality."""

    def test_config_is_backtest_flag_exists(self):
        """Test that the is_backtest flag exists in the Config model."""
        # This test ensures the flag is properly defined in the Config class
        from hyperliquid_bot.config.settings import Config

        # Check that is_backtest is a field in the Config model
        assert hasattr(Config, 'model_fields')
        assert 'is_backtest' in Config.model_fields

        # Check default value
        field_info = Config.model_fields['is_backtest']
        assert field_info.default is False

    def test_risk_manager_uses_is_backtest_flag(self):
        """Test that RiskManager code uses config.is_backtest instead of legacy detection."""
        # Read the risk.py file and verify it uses the new flag
        with open('hyperliquid_bot/core/risk.py', 'r') as f:
            risk_code = f.read()

        # Should contain the new flag usage
        assert 'self.config.is_backtest' in risk_code

        # Should NOT contain the old pattern
        assert 'hasattr(self.config, "backtest") and self.config.backtest' not in risk_code

    def test_tf_v3_strategy_uses_is_backtest_flag(self):
        """Test that TF-v3 strategy code uses config.is_backtest instead of legacy detection."""
        # Read the tf_v3.py file and verify it uses the new flag
        with open('hyperliquid_bot/strategies/tf_v3.py', 'r') as f:
            tf_v3_code = f.read()

        # Should contain the new flag usage
        assert 'self.config.is_backtest' in tf_v3_code

        # Should NOT contain the old pattern
        assert 'hasattr(self.config, "backtest") and self.config.backtest' not in tf_v3_code

    def test_legacy_backtest_detection_removed_from_core_code(self):
        """Test that legacy backtest detection patterns are removed from core runtime code."""
        import os
        import glob

        # Check all Python files in core runtime directories
        core_dirs = [
            'hyperliquid_bot/core/',
            'hyperliquid_bot/strategies/',
            'hyperliquid_bot/backtester/'
        ]

        legacy_pattern = 'hasattr(self.config, "backtest") and self.config.backtest'

        for core_dir in core_dirs:
            if os.path.exists(core_dir):
                py_files = glob.glob(f'{core_dir}**/*.py', recursive=True)
                for py_file in py_files:
                    with open(py_file, 'r') as f:
                        content = f.read()
                        # Should not contain the legacy pattern
                        assert legacy_pattern not in content, f"Legacy pattern found in {py_file}"

    def test_backtest_flag_integration(self):
        """Test that the is_backtest flag is properly integrated into the codebase."""
        # This test verifies that the migration from legacy detection to is_backtest flag is complete

        # Verify that the new flag is used in the right places
        with open('hyperliquid_bot/core/risk.py', 'r') as f:
            risk_code = f.read()
            # Should use the new flag in the backtest-specific logic
            assert 'if self.config.is_backtest:' in risk_code

        with open('hyperliquid_bot/strategies/tf_v3.py', 'r') as f:
            tf_v3_code = f.read()
            # Should use the new flag in state loading logic
            assert 'if self.config.is_backtest:' in tf_v3_code


if __name__ == "__main__":
    pytest.main([__file__])
