"""
Test Regime State Manager
=========================

This test suite verifies that the regime state manager correctly
maintains state history and prevents look-ahead bias.
"""

import unittest
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.modern.regime_state_manager import (
    RegimeStateManager, RegimeSnapshot, RegimeState
)


class TestRegimeStateManager(unittest.TestCase):
    """Test suite for regime state manager."""
    
    def setUp(self):
        """Set up test instance."""
        self.manager = RegimeStateManager(
            history_hours=24,
            state_update_seconds=60,
            mode="backtest"
        )
        self.base_time = datetime(2024, 1, 1, 12, 0, 0)
    
    def test_initialization(self):
        """Test manager initializes correctly."""
        self.assertEqual(self.manager.history_hours, 24)
        self.assertEqual(self.manager.state_update_seconds, 60)
        self.assertEqual(self.manager.mode, "backtest")
        self.assertIsNone(self.manager.get_current_state())
    
    def test_state_update(self):
        """Test basic state update."""
        # Update state
        result = self.manager.update_state(
            timestamp=self.base_time,
            state="BULL",
            confidence=0.85,
            features={'momentum': 0.5, 'volatility': 0.1, 'volume_imbalance': 0.3}
        )
        
        self.assertTrue(result)
        
        # Check current state
        current = self.manager.get_current_state()
        self.assertIsNotNone(current)
        self.assertEqual(current.state, "BULL")
        self.assertEqual(current.confidence, 0.85)
        self.assertEqual(current.momentum, 0.5)
    
    def test_no_look_ahead(self):
        """Test that future states are not visible in the past."""
        # Add states at different times
        for i in range(5):
            timestamp = self.base_time + timedelta(minutes=i)
            state = "BULL" if i < 3 else "BEAR"
            self.manager.update_state(
                timestamp=timestamp,
                state=state,
                confidence=0.8,
                features={'momentum': i * 0.1, 'volatility': 0.1, 'volume_imbalance': 0.0}
            )
        
        # Query at minute 2 - should not see BEAR states
        query_time = self.base_time + timedelta(minutes=2, seconds=30)
        state_at_time = self.manager.get_state_at_time(query_time)
        
        self.assertIsNotNone(state_at_time)
        self.assertEqual(state_at_time.state, "BULL")
        
        # Get history up to minute 2
        history = self.manager.get_state_history(query_time)
        
        # Should only have 3 states (minutes 0, 1, 2)
        self.assertEqual(len(history), 3)
        
        # All should be BULL
        for snapshot in history:
            self.assertEqual(snapshot.state, "BULL")
    
    def test_out_of_order_rejection(self):
        """Test that out-of-order updates are rejected."""
        # Add initial state
        self.manager.update_state(
            timestamp=self.base_time,
            state="BULL",
            confidence=0.8,
            features={'momentum': 0.5, 'volatility': 0.1, 'volume_imbalance': 0.0}
        )
        
        # Try to add earlier state
        result = self.manager.update_state(
            timestamp=self.base_time - timedelta(minutes=1),
            state="BEAR",
            confidence=0.9,
            features={'momentum': -0.5, 'volatility': 0.1, 'volume_imbalance': 0.0}
        )
        
        # Should be rejected
        self.assertFalse(result)
        
        # Current state should still be BULL
        current = self.manager.get_current_state()
        self.assertEqual(current.state, "BULL")
    
    def test_state_transitions(self):
        """Test state transition tracking."""
        states = ["BULL", "BULL", "BEAR", "BEAR", "CHOP", "BULL"]
        
        for i, state in enumerate(states):
            self.manager.update_state(
                timestamp=self.base_time + timedelta(minutes=i),
                state=state,
                confidence=0.8,
                features={'momentum': 0.0, 'volatility': 0.1, 'volume_imbalance': 0.0}
            )
        
        # Check statistics
        stats = self.manager.get_state_statistics(
            end_time=self.base_time + timedelta(minutes=10),
            hours=1
        )
        
        self.assertEqual(stats['total_states'], 6)
        self.assertEqual(stats['transitions'], 3)  # BULL->BEAR, BEAR->CHOP, CHOP->BULL
        self.assertEqual(stats['state_counts']['BULL'], 3)
        self.assertEqual(stats['state_counts']['BEAR'], 2)
        self.assertEqual(stats['state_counts']['CHOP'], 1)
    
    def test_regime_features_for_strategy(self):
        """Test regime feature extraction for strategy."""
        # Create a trending scenario
        for i in range(20):
            state = "BULL" if i < 15 else "BEAR"
            momentum = 0.5 if i < 15 else -0.3
            
            self.manager.update_state(
                timestamp=self.base_time + timedelta(minutes=i),
                state=state,
                confidence=0.8 + i * 0.01,
                features={
                    'momentum': momentum,
                    'volatility': 0.1 + i * 0.005,
                    'volume_imbalance': 0.2
                }
            )
        
        # Get features at minute 19
        features = self.manager.get_regime_features_for_strategy(
            timestamp=self.base_time + timedelta(minutes=19),
            lookback_hours=1
        )
        
        # Check features
        self.assertEqual(features['current_state'], "BEAR")
        self.assertGreater(features['recent_bullish_pct'], 50)  # Was mostly bullish
        self.assertLess(features['momentum_trend'], 0)  # Momentum declining
        # Note: is_trending requires > 75% in one direction in last 20 states
        # Our test has 15 BULL + 5 BEAR, so last 20 contains both, not trending
        self.assertFalse(features['is_trending'])  # Mixed states, not trending
        self.assertGreater(features['recent_transitions'], 0)  # Had transitions
    
    def test_state_persistence_calculation(self):
        """Test state persistence metric."""
        # Highly persistent scenario
        for i in range(10):
            self.manager.update_state(
                timestamp=self.base_time + timedelta(minutes=i),
                state="BULL",
                confidence=0.9,
                features={'momentum': 0.5, 'volatility': 0.1, 'volume_imbalance': 0.0}
            )
        
        features = self.manager.get_regime_features_for_strategy(
            timestamp=self.base_time + timedelta(minutes=10),
            lookback_hours=1
        )
        
        # Should have high persistence
        self.assertEqual(features['state_persistence'], 1.0)
        
        # Now add alternating states
        self.manager.clear_history()
        
        for i in range(10):
            state = "BULL" if i % 2 == 0 else "BEAR"
            self.manager.update_state(
                timestamp=self.base_time + timedelta(minutes=i),
                state=state,
                confidence=0.9,
                features={'momentum': 0.5, 'volatility': 0.1, 'volume_imbalance': 0.0}
            )
        
        features = self.manager.get_regime_features_for_strategy(
            timestamp=self.base_time + timedelta(minutes=10),
            lookback_hours=1
        )
        
        # Should have low persistence
        self.assertEqual(features['state_persistence'], 0.0)
    
    def test_history_window_limit(self):
        """Test that history window is properly limited."""
        # Add 25 hours of data
        for i in range(25 * 60):  # 25 hours * 60 minutes
            self.manager.update_state(
                timestamp=self.base_time + timedelta(minutes=i),
                state="BULL",
                confidence=0.8,
                features={'momentum': 0.0, 'volatility': 0.1, 'volume_imbalance': 0.0}
            )
        
        # Check that we only keep 24 hours
        max_states = 24 * 60  # 24 hours * 60 minutes
        history = self.manager.get_state_history(
            end_time=self.base_time + timedelta(hours=26),
            hours=None  # Get all available
        )
        
        # Should be limited by deque maxlen
        self.assertLessEqual(len(history), max_states)
    
    def test_regime_state_enum_helpers(self):
        """Test regime state classification helpers."""
        # Test bullish states
        self.assertTrue(RegimeState.is_bullish("BULL"))
        self.assertTrue(RegimeState.is_bullish("WEAK_BULL"))
        self.assertTrue(RegimeState.is_bullish("BULL_VOLATILE"))
        self.assertFalse(RegimeState.is_bullish("BEAR"))
        self.assertFalse(RegimeState.is_bullish("CHOP"))
        
        # Test bearish states
        self.assertTrue(RegimeState.is_bearish("BEAR"))
        self.assertTrue(RegimeState.is_bearish("WEAK_BEAR"))
        self.assertTrue(RegimeState.is_bearish("BEAR_VOLATILE"))
        self.assertFalse(RegimeState.is_bearish("BULL"))
        self.assertFalse(RegimeState.is_bearish("CHOP"))
        
        # Test neutral states
        self.assertTrue(RegimeState.is_neutral("CHOP"))
        self.assertTrue(RegimeState.is_neutral("NEUTRAL"))
        self.assertFalse(RegimeState.is_neutral("BULL"))
        self.assertFalse(RegimeState.is_neutral("BEAR"))
    
    def test_clear_history(self):
        """Test history clearing."""
        # Add some states
        for i in range(5):
            self.manager.update_state(
                timestamp=self.base_time + timedelta(minutes=i),
                state="BULL",
                confidence=0.8,
                features={'momentum': 0.0, 'volatility': 0.1, 'volume_imbalance': 0.0}
            )
        
        # Verify data exists
        self.assertIsNotNone(self.manager.get_current_state())
        
        # Clear history
        self.manager.clear_history()
        
        # Verify cleared
        self.assertIsNone(self.manager.get_current_state())
        history = self.manager.get_state_history(
            end_time=self.base_time + timedelta(hours=1)
        )
        self.assertEqual(len(history), 0)


if __name__ == '__main__':
    unittest.main()