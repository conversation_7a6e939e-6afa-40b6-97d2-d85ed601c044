"""
Test Modern TF-v3 Strategy
==========================

This test suite verifies:
- Strategy initialization
- Entry evaluation with regime history
- Exit condition checking
- Regime stability validation
"""

import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock
import sys
from pathlib import Path
import pandas as pd

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.modern.tf_v3_modern import ModernTFV3Strategy
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.utils.state_mapping import (
    GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BEAR_TREND,
    GMS_STATE_HIGH_VOL_RANGE, GMS_STATE_UNCERTAIN
)


class TestModernTFV3Strategy(unittest.TestCase):
    """Test suite for modern TF-v3 strategy."""
    
    def setUp(self):
        """Set up test environment."""
        # Load config
        self.config = load_config()
        
        # Create strategy
        self.strategy = ModernTFV3Strategy(self.config)
        
        # Base time for tests
        self.base_time = datetime(2024, 1, 1, 10, 0, 0)
        
        # Sample signals
        self.base_signals = {
            'timestamp': self.base_time,
            'close': 100.0,
            'high': 101.0,
            'low': 99.0,
            'volume': 10000.0,
            'ema_fast': 100.5,
            'ema_slow': 99.5,
            'ema_baseline': 99.0,
            'atr_14': 1.0,
            'atr_percent': 0.01,
            'rsi': 55.0,
            'bb_upper': 102.0,
            'bb_lower': 98.0,
            'bb_middle': 100.0
        }
        
        # Sample regime features
        self.regime_features = {
            'current_state': GMS_STATE_STRONG_BULL_TREND,
            'current_confidence': 0.8,
            'state_duration_minutes': 30,
            'dominant_state_1h': GMS_STATE_STRONG_BULL_TREND,
            'state_changes_1h': 2,
            'avg_momentum_1h': 50.0,
            'avg_volatility_1h': 0.015,
            'risk_suppressed': False
        }
    
    def test_initialization(self):
        """Test strategy initializes correctly."""
        self.assertEqual(self.strategy.strategy_name, "tf_v3_modern")
        self.assertIsNotNone(self.strategy.required_signals)
        self.assertIn('ema_fast', self.strategy.required_signals)
        self.assertIn('atr_14', self.strategy.required_signals)
    
    def test_evaluate_entry_bullish(self):
        """Test bullish entry evaluation."""
        # Setup bullish signals
        signals = self.base_signals.copy()
        signals['ema_fast'] = 101.0   # Fast > Slow
        signals['ema_slow'] = 100.0
        signals['close'] = 101.5      # Price > Baseline
        signals['ema_baseline'] = 100.0
        signals['regime_features'] = self.regime_features
        
        # Evaluate
        result = self.strategy.evaluate_entry(
            signals, 
            GMS_STATE_STRONG_BULL_TREND
        )
        
        # Verify
        self.assertIsNotNone(result)
        self.assertEqual(result['direction'], 'long')
        self.assertGreater(result['confidence'], 0.5)
        self.assertEqual(result['regime'], GMS_STATE_STRONG_BULL_TREND)
    
    def test_evaluate_entry_bearish(self):
        """Test bearish entry evaluation."""
        # Setup bearish signals
        signals = self.base_signals.copy()
        signals['ema_fast'] = 99.0    # Fast < Slow
        signals['ema_slow'] = 100.0
        signals['close'] = 98.5       # Price < Baseline
        signals['ema_baseline'] = 100.0
        
        # Bearish regime features
        regime_features = self.regime_features.copy()
        regime_features['current_state'] = GMS_STATE_WEAK_BEAR_TREND
        regime_features['avg_momentum_1h'] = -50.0
        signals['regime_features'] = regime_features
        
        # Evaluate
        result = self.strategy.evaluate_entry(
            signals,
            GMS_STATE_WEAK_BEAR_TREND
        )
        
        # Verify
        self.assertIsNotNone(result)
        self.assertEqual(result['direction'], 'short')
        self.assertGreater(result['confidence'], 0.5)
    
    def test_no_entry_wrong_regime(self):
        """Test no entry when regime doesn't allow."""
        signals = self.base_signals.copy()
        signals['regime_features'] = self.regime_features
        
        # Try with range regime
        result = self.strategy.evaluate_entry(
            signals,
            GMS_STATE_HIGH_VOL_RANGE
        )
        
        self.assertIsNone(result)
    
    def test_no_entry_unstable_regime(self):
        """Test no entry when regime is unstable."""
        signals = self.base_signals.copy()
        
        # Unstable regime features
        regime_features = self.regime_features.copy()
        regime_features['state_duration_minutes'] = 5  # Too short
        regime_features['state_changes_1h'] = 10      # Too many changes
        signals['regime_features'] = regime_features
        
        result = self.strategy.evaluate_entry(
            signals,
            GMS_STATE_STRONG_BULL_TREND
        )
        
        self.assertIsNone(result)
    
    def test_no_entry_risk_suppressed(self):
        """Test no entry when risk suppressed."""
        signals = self.base_signals.copy()
        
        # Risk suppressed
        regime_features = self.regime_features.copy()
        regime_features['risk_suppressed'] = True
        signals['regime_features'] = regime_features
        
        result = self.strategy.evaluate_entry(
            signals,
            GMS_STATE_STRONG_BULL_TREND
        )
        
        self.assertIsNone(result)
    
    def test_check_exit_stop_loss(self):
        """Test stop loss exit."""
        # Long position
        position = {
            'direction': 'long',
            'entry_price': 100.0,
            'stop_loss': 98.0,
            'entry_time': self.base_time
        }
        
        # Price hits stop
        signals = self.base_signals.copy()
        signals['close'] = 97.5
        
        exit_reason = self.strategy.check_exit(signals, position)
        self.assertEqual(exit_reason, 'stop_loss_hit')
    
    def test_check_exit_trailing_stop(self):
        """Test trailing stop exit."""
        # Set up trailing stop tracking
        self.strategy.trail_price = 99.0
        
        # Long position
        position = {
            'direction': 'long',
            'entry_price': 100.0,
            'entry_time': self.base_time
        }
        
        # Price hits trailing stop
        signals = self.base_signals.copy()
        signals['close'] = 98.5
        signals['atr_14'] = 1.0
        
        exit_reason = self.strategy.check_exit(signals, position)
        self.assertEqual(exit_reason, 'trailing_stop_hit')
    
    def test_check_exit_time_decay(self):
        """Test time decay exit."""
        # Old position
        position = {
            'direction': 'long',
            'entry_price': 100.0,
            'entry_time': self.base_time - timedelta(hours=25)
        }
        
        signals = self.base_signals.copy()
        signals['timestamp'] = self.base_time
        
        exit_reason = self.strategy.check_exit(signals, position)
        self.assertEqual(exit_reason, 'max_hold_time_exceeded')
    
    def test_check_exit_regime_change(self):
        """Test regime change exit."""
        position = {
            'direction': 'long',
            'entry_price': 100.0,
            'entry_time': self.base_time
        }
        
        # Regime changed to range
        signals = self.base_signals.copy()
        signals['regime_state'] = GMS_STATE_HIGH_VOL_RANGE
        
        exit_reason = self.strategy.check_exit(signals, position)
        self.assertEqual(exit_reason, 'regime_change_exit')
    
    def test_check_exit_ema_reversal(self):
        """Test EMA reversal exit."""
        # Long position
        position = {
            'direction': 'long',
            'entry_price': 100.0,
            'entry_time': self.base_time
        }
        
        # EMAs reversed
        signals = self.base_signals.copy()
        signals['ema_fast'] = 99.0   # Now below slow
        signals['ema_slow'] = 100.0
        
        exit_reason = self.strategy.check_exit(signals, position)
        self.assertEqual(exit_reason, 'ema_reversal_exit')
    
    def test_regime_stability_check(self):
        """Test regime stability validation."""
        # Stable regime
        stable_features = {
            'state_duration_minutes': 45,
            'state_changes_1h': 2,
            'current_confidence': 0.8
        }
        self.assertTrue(self.strategy._is_regime_stable(stable_features))
        
        # Unstable - too short
        unstable_features = stable_features.copy()
        unstable_features['state_duration_minutes'] = 5
        self.assertFalse(self.strategy._is_regime_stable(unstable_features))
        
        # Unstable - too many changes
        unstable_features = stable_features.copy()
        unstable_features['state_changes_1h'] = 10
        self.assertFalse(self.strategy._is_regime_stable(unstable_features))
        
        # Unstable - low confidence
        unstable_features = stable_features.copy()
        unstable_features['current_confidence'] = 0.3
        self.assertFalse(self.strategy._is_regime_stable(unstable_features))
    
    def test_confidence_calculation(self):
        """Test entry confidence calculation."""
        signals = self.base_signals.copy()
        signals['ema_fast'] = 102.0
        signals['ema_slow'] = 100.0
        
        regime_features = {
            'current_confidence': 0.8,
            'state_duration_minutes': 45,
            'avg_momentum_1h': 75.0
        }
        
        confidence = self.strategy._calculate_entry_confidence(
            signals,
            GMS_STATE_STRONG_BULL_TREND,
            regime_features,
            'long'
        )
        
        # Should be elevated due to good conditions
        self.assertGreater(confidence, 0.7)
        self.assertLessEqual(confidence, 1.0)
    
    def test_get_allowed_states(self):
        """Test allowed states for trend following."""
        states = self.strategy.get_allowed_states('trend_following')
        
        self.assertIn(GMS_STATE_STRONG_BULL_TREND, states)
        self.assertIn(GMS_STATE_WEAK_BEAR_TREND, states)
        self.assertNotIn(GMS_STATE_HIGH_VOL_RANGE, states)


if __name__ == '__main__':
    unittest.main()