

# Sample successful API response
MOCK_API_RESPONSE_SUCCESS = {
    "name": "Fear and Greed Index",
    "data": [
        {
            "value": "30",
            "value_classification": "Fear",
            "timestamp": "1713571200", # 2024-04-20 00:00:00 UTC
        },
        {
            "value": "35",
            "value_classification": "Fear",
            "timestamp": "1713484800", # 2024-04-19 00:00:00 UTC
        }
    ],
    "metadata": {"error": None}
}

# Sample API response missing 'data' field
MOCK_API_RESPONSE_MISSING_DATA = {
    "name": "Fear and Greed Index",
    "metadata": {"error": None}
}

# async def test_fetch_fear_greed_success():
#     """Test successful fetching and processing."""
#     def custom_response(request: httpx.Request):
#         return httpx.Response(200, json=MOCK_API_RESPONSE_SUCCESS)

#     transport = httpx.MockTransport(custom_response)
#     async with httpx.AsyncClient(transport=transport) as client:
#         original_client = httpx.AsyncClient
#         httpx.AsyncClient = lambda: client
#         try:
#             df = await fetch_fear_greed_index(limit=2)
#         finally:
#             httpx.AsyncClient = original_client

#     assert df is not None
#     assert isinstance(df, pd.DataFrame)
#     assert len(df) == 2
#     assert list(df.columns) == ['fear_greed_value', 'fear_greed_classification']
#     assert df.index.name == 'timestamp'
#     assert pd.api.types.is_datetime64_any_dtype(df.index)
#     assert df.index.tz.zone == 'UTC'
#     assert pd.api.types.is_integer_dtype(df['fear_greed_value'])
#     assert df.index[0] == pd.Timestamp('2024-04-19 00:00:00+00:00', tz='UTC')
#     assert df['fear_greed_value'].iloc[0] == 35
#     pass

# async def test_fetch_fear_greed_http_error():
#     """Test handling of HTTP status errors."""
#     def custom_response(request: httpx.Request):
#         return httpx.Response(500)

#     transport = httpx.MockTransport(custom_response)
#     async with httpx.AsyncClient(transport=transport) as client:
#         original_client = httpx.AsyncClient
#         httpx.AsyncClient = lambda: client
#         try:
#             df = await fetch_fear_greed_index(limit=1)
#         finally:
#             httpx.AsyncClient = original_client
#     assert df is None
#     pass

# async def test_fetch_fear_greed_request_error():
#     """Test handling of request errors (e.g., timeout)."""
#     def custom_response(request: httpx.Request):
#         raise httpx.ReadTimeout("Timeout")

#     transport = httpx.MockTransport(custom_response)
#     async with httpx.AsyncClient(transport=transport) as client:
#         original_client = httpx.AsyncClient
#         httpx.AsyncClient = lambda: client
#         try:
#             df = await fetch_fear_greed_index(limit=1, timeout=0.1)
#         finally:
#             httpx.AsyncClient = original_client
#     assert df is None
#     pass

# async def test_fetch_fear_greed_missing_data_field():
#     """Test handling of response missing 'data' field."""
#     def custom_response(request: httpx.Request):
#         return httpx.Response(200, json=MOCK_API_RESPONSE_MISSING_DATA)

#     transport = httpx.MockTransport(custom_response)
#     async with httpx.AsyncClient(transport=transport) as client:
#         original_client = httpx.AsyncClient
#         httpx.AsyncClient = lambda: client
#         try:
#             df = await fetch_fear_greed_index(limit=1)
#         finally:
#             httpx.AsyncClient = original_client
#     assert df is None
#     pass
