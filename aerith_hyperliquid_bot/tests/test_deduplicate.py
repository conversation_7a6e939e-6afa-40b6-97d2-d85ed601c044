# tests/test_deduplicate.py
# Unit tests for the deduplicate function

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from hyperliquid_bot.utils.data_utils import deduplicate

def test_deduplicate_no_duplicates():
    """Test that deduplicate returns the original DataFrame when there are no duplicates."""
    # Create a DataFrame with no duplicate indices
    index = pd.date_range(start='2025-01-01', periods=5, freq='H')
    df = pd.DataFrame({'value': range(5)}, index=index)
    
    # Apply deduplicate
    result = deduplicate(df)
    
    # Check that the result is the same as the input
    pd.testing.assert_frame_equal(result, df)
    assert result.index.is_unique
    assert result.index.is_monotonic_increasing

def test_deduplicate_with_duplicates():
    """Test that deduplicate removes duplicate indices, keeping the first occurrence."""
    # Create a DataFrame with duplicate indices
    index = pd.DatetimeIndex([
        '2025-01-01 00:00:00',
        '2025-01-01 01:00:00',
        '2025-01-01 01:00:00',  # Duplicate
        '2025-01-01 02:00:00',
        '2025-01-01 03:00:00',
        '2025-01-01 03:00:00',  # Duplicate
    ])
    df = pd.DataFrame({'value': [1, 2, 3, 4, 5, 6]}, index=index)
    
    # Apply deduplicate
    result = deduplicate(df)
    
    # Check that duplicates are removed and the first occurrence is kept
    assert result.index.is_unique
    assert result.index.is_monotonic_increasing
    assert len(result) == 4  # 6 rows - 2 duplicates = 4 rows
    assert result.loc['2025-01-01 01:00:00', 'value'] == 2  # First occurrence value
    assert result.loc['2025-01-01 03:00:00', 'value'] == 5  # First occurrence value

def test_deduplicate_unsorted_with_duplicates():
    """Test that deduplicate sorts the index and removes duplicates."""
    # Create an unsorted DataFrame with duplicate indices
    index = pd.DatetimeIndex([
        '2025-01-01 03:00:00',
        '2025-01-01 01:00:00',
        '2025-01-01 02:00:00',
        '2025-01-01 01:00:00',  # Duplicate
        '2025-01-01 00:00:00',
    ])
    df = pd.DataFrame({'value': [5, 2, 4, 3, 1]}, index=index)
    
    # Apply deduplicate
    result = deduplicate(df)
    
    # Check that the result is sorted and duplicates are removed
    assert result.index.is_unique
    assert result.index.is_monotonic_increasing
    assert len(result) == 4  # 5 rows - 1 duplicate = 4 rows
    assert result.iloc[0].name == pd.Timestamp('2025-01-01 00:00:00')
    assert result.iloc[-1].name == pd.Timestamp('2025-01-01 03:00:00')
    assert result.loc['2025-01-01 01:00:00', 'value'] == 2  # First occurrence value
