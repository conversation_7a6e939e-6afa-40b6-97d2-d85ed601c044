# tests/test_time_utils.py
# Tests for time utilities

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timezone
import tempfile
import os
from pathlib import Path

from hyperliquid_bot.utils.time import to_utc_naive


class TestTimeUtils(unittest.TestCase):
    """Test cases for time utility functions."""

    def test_to_utc_naive_with_timestamp_tz_aware(self):
        """Test conversion of timezone-aware pandas Timestamp."""
        # Create a timezone-aware timestamp
        ts_aware = pd.Timestamp("2025-03-01 12:00:00", tz="UTC")
        result = to_utc_naive(ts_aware)

        # Should be naive (no timezone) but same time
        self.assertIsNone(result.tz)
        self.assertEqual(result, pd.Timestamp("2025-03-01 12:00:00"))

    def test_to_utc_naive_with_timestamp_naive(self):
        """Test conversion of timezone-naive pandas Timestamp."""
        # Create a naive timestamp (assume UTC)
        ts_naive = pd.Timestamp("2025-03-01 12:00:00")
        result = to_utc_naive(ts_naive)

        # Should remain naive and unchanged
        self.assertIsNone(result.tz)
        self.assertEqual(result, ts_naive)

    def test_to_utc_naive_with_iso_string_z(self):
        """Test conversion of ISO string with Z suffix."""
        iso_string = "2025-03-01T12:00:00Z"
        result = to_utc_naive(iso_string)

        # Should be naive UTC
        self.assertIsNone(result.tz)
        self.assertEqual(result, pd.Timestamp("2025-03-01 12:00:00"))

    def test_to_utc_naive_with_iso_string_offset(self):
        """Test conversion of ISO string with timezone offset."""
        iso_string = "2025-03-01T12:00:00+00:00"
        result = to_utc_naive(iso_string)

        # Should be naive UTC
        self.assertIsNone(result.tz)
        self.assertEqual(result, pd.Timestamp("2025-03-01 12:00:00"))

    def test_to_utc_naive_with_epoch_milliseconds(self):
        """Test conversion of epoch milliseconds."""
        # March 1, 2025 12:00:00 UTC in milliseconds
        epoch_ms = 1740830400000
        result = to_utc_naive(epoch_ms)

        # Should be naive UTC
        self.assertIsNone(result.tz)
        expected = pd.Timestamp("2025-03-01 12:00:00")
        self.assertEqual(result, expected)

    def test_to_utc_naive_with_epoch_seconds(self):
        """Test conversion of epoch seconds."""
        # March 1, 2025 12:00:00 UTC in seconds
        epoch_s = 1740830400
        result = to_utc_naive(epoch_s)

        # Should be naive UTC
        self.assertIsNone(result.tz)
        expected = pd.Timestamp("2025-03-01 12:00:00")
        self.assertEqual(result, expected)

    def test_to_utc_naive_with_float_epoch(self):
        """Test conversion of float epoch timestamp."""
        # March 1, 2025 12:00:00.500 UTC in seconds
        epoch_float = 1740830400.5
        result = to_utc_naive(epoch_float)

        # Should be naive UTC with microseconds
        self.assertIsNone(result.tz)
        expected = pd.Timestamp("2025-03-01 12:00:00.500")
        self.assertEqual(result, expected)

    def test_to_utc_naive_with_invalid_type(self):
        """Test that invalid types raise TypeError."""
        with self.assertRaises(TypeError):
            to_utc_naive([1, 2, 3])

        with self.assertRaises(TypeError):
            to_utc_naive({"time": "2025-03-01"})

    def test_to_utc_naive_preserves_precision(self):
        """Test that microsecond precision is preserved."""
        # Create timestamp with microseconds
        ts_with_microseconds = pd.Timestamp("2025-03-01 12:00:00.123456", tz="UTC")
        result = to_utc_naive(ts_with_microseconds)

        self.assertIsNone(result.tz)
        self.assertEqual(result.microsecond, 123456)

    def test_to_utc_naive_with_different_timezones(self):
        """Test conversion from different timezones to UTC naive."""
        # Create timestamp in EST (UTC-5)
        ts_est = pd.Timestamp("2025-03-01 07:00:00", tz="US/Eastern")
        result = to_utc_naive(ts_est)

        # Should convert to UTC (12:00) and be naive
        self.assertIsNone(result.tz)
        expected = pd.Timestamp("2025-03-01 12:00:00")
        self.assertEqual(result, expected)


class TestETLIntegration(unittest.TestCase):
    """Integration tests for ETL with UTC helper."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.raw_dir = Path(self.temp_dir) / "raw"
        self.out_dir = Path(self.temp_dir) / "features"
        self.raw_dir.mkdir(parents=True)
        self.out_dir.mkdir(parents=True)

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_etl_produces_naive_timestamps(self):
        """Test that ETL processing produces UTC-naive timestamps."""
        # Create a minimal test data file
        test_data = []
        base_time = 1740830400000  # March 1, 2025 12:00:00 UTC in ms

        for i in range(10):
            timestamp = base_time + (i * 100)  # 100ms intervals
            record = {
                "raw": {
                    "data": {
                        "time": timestamp,
                        "levels": [
                            [{"px": "50000.0", "sz": "1.0"}],  # bids
                            [{"px": "50001.0", "sz": "1.0"}]   # asks
                        ]
                    }
                }
            }
            test_data.append(record)

        # Write test data to file
        import json
        test_file = self.raw_dir / "test_data.txt"
        with open(test_file, 'w') as f:
            for record in test_data:
                f.write(json.dumps(record) + '\n')

        # Import ETL functions
        from tools.etl_l20_to_1s import load_raw_l2_data, calculate_features, resample_to_1s

        # Process the test data
        df = load_raw_l2_data(str(test_file))
        self.assertGreater(len(df), 0, "Should load test data")

        # Verify timestamps are properly handled
        df_features = calculate_features(df, depth=5)
        resampled = resample_to_1s(df_features, method="median")

        # Apply our UTC helper
        resampled['timestamp'] = resampled['timestamp'].apply(to_utc_naive)

        # Verify all timestamps are UTC-naive
        self.assertIsNone(resampled['timestamp'].dt.tz,
                         "All timestamps should be timezone-naive after processing")

        # Verify timestamps are reasonable (around our test time)
        expected_time = pd.Timestamp("2025-03-01 12:00:00")
        time_diff = abs((resampled['timestamp'].iloc[0] - expected_time).total_seconds())
        self.assertLess(time_diff, 60, "Timestamps should be close to expected time")


if __name__ == '__main__':
    unittest.main()
