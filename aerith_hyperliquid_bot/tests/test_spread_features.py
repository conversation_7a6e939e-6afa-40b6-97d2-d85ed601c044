#!/usr/bin/env python3
"""
Test spread feature calculation in ETL pipeline.

This test verifies that spread_mean and spread_std columns are properly
calculated in the 1-second feature files generated by the ETL pipeline.
"""

import pytest
import pandas as pd
import numpy as np
from pathlib import Path
import tempfile
import os
import sys

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from tools.etl_l20_to_1s import calculate_post_resample_features


class TestSpreadFeatures:
    """Test spread feature calculations."""

    def test_spread_features_calculation(self):
        """Test that spread_mean and spread_std are calculated correctly."""
        # Create sample data with spread column
        timestamps = pd.date_range('2025-03-05 10:00:00', periods=120, freq='1s')
        
        # Create synthetic spread data with some variation
        np.random.seed(42)  # For reproducible tests
        spread_values = 0.01 + 0.005 * np.sin(np.arange(120) * 0.1) + 0.001 * np.random.randn(120)
        
        df = pd.DataFrame({
            'timestamp': timestamps,
            'mid_price': 50000.0 + 100 * np.random.randn(120),
            'spread': spread_values,
            'close': 50000.0 + 100 * np.random.randn(120),
        })
        
        # Calculate post-resample features (this should add spread_mean and spread_std)
        result_df = calculate_post_resample_features(df)
        
        # Verify that spread_mean and spread_std columns exist
        assert 'spread_mean' in result_df.columns, "spread_mean column should be present"
        assert 'spread_std' in result_df.columns, "spread_std column should be present"
        
        # Verify that the columns are not all NaN
        spread_mean_valid = result_df['spread_mean'].notna().sum()
        spread_std_valid = result_df['spread_std'].notna().sum()
        
        assert spread_mean_valid > 0, "spread_mean should have some non-NaN values"
        assert spread_std_valid > 0, "spread_std should have some non-NaN values"
        
        # Verify that after warmup period (60 seconds), most values should be valid
        # Allow for some NaN values at the beginning due to rolling window
        warmup_period = 60
        after_warmup = result_df.iloc[warmup_period:]
        
        spread_mean_after_warmup = after_warmup['spread_mean'].notna().sum()
        spread_std_after_warmup = after_warmup['spread_std'].notna().sum()
        
        # At least 80% of values after warmup should be valid
        min_valid_count = int(0.8 * len(after_warmup))
        assert spread_mean_after_warmup >= min_valid_count, f"spread_mean should have at least {min_valid_count} valid values after warmup, got {spread_mean_after_warmup}"
        assert spread_std_after_warmup >= min_valid_count, f"spread_std should have at least {min_valid_count} valid values after warmup, got {spread_std_after_warmup}"

    def test_spread_features_with_missing_spread_column(self):
        """Test behavior when spread column is missing."""
        # Create sample data without spread column
        timestamps = pd.date_range('2025-03-05 10:00:00', periods=60, freq='1s')
        
        df = pd.DataFrame({
            'timestamp': timestamps,
            'mid_price': 50000.0,
            'close': 50000.0,
        })
        
        # Calculate post-resample features
        result_df = calculate_post_resample_features(df)
        
        # Verify that spread_mean and spread_std columns exist but are NaN
        assert 'spread_mean' in result_df.columns, "spread_mean column should be present"
        assert 'spread_std' in result_df.columns, "spread_std column should be present"
        
        # All values should be NaN when spread column is missing
        assert result_df['spread_mean'].isna().all(), "spread_mean should be all NaN when spread column is missing"
        assert result_df['spread_std'].isna().all(), "spread_std should be all NaN when spread column is missing"

    def test_spread_rolling_window_calculation(self):
        """Test that rolling window calculation is correct."""
        # Create simple test data
        timestamps = pd.date_range('2025-03-05 10:00:00', periods=100, freq='1s')
        
        # Create constant spread for easy verification
        spread_values = [0.01] * 50 + [0.02] * 50  # Step change in spread
        
        df = pd.DataFrame({
            'timestamp': timestamps,
            'mid_price': 50000.0,
            'spread': spread_values,
            'close': 50000.0,
        })
        
        # Calculate post-resample features
        result_df = calculate_post_resample_features(df)
        
        # Check that rolling mean converges to expected values
        # After the step change, the rolling mean should be between 0.01 and 0.02
        final_values = result_df['spread_mean'].iloc[-10:]  # Last 10 values
        valid_final_values = final_values.dropna()
        
        if len(valid_final_values) > 0:
            # The rolling mean should be between the two constant values
            assert valid_final_values.min() >= 0.01, "Rolling mean should be at least 0.01"
            assert valid_final_values.max() <= 0.02, "Rolling mean should be at most 0.02"

    def test_nan_ratio_threshold(self):
        """Test that NaN ratio is below acceptable threshold."""
        # Create sample data
        timestamps = pd.date_range('2025-03-05 10:00:00', periods=3600, freq='1s')  # 1 hour of data
        
        np.random.seed(42)
        spread_values = 0.01 + 0.001 * np.random.randn(3600)
        
        df = pd.DataFrame({
            'timestamp': timestamps,
            'mid_price': 50000.0,
            'spread': spread_values,
            'close': 50000.0,
        })
        
        # Calculate post-resample features
        result_df = calculate_post_resample_features(df)
        
        # Calculate NaN ratios
        spread_mean_nan_ratio = result_df['spread_mean'].isna().mean()
        spread_std_nan_ratio = result_df['spread_std'].isna().mean()
        
        # NaN ratio should be less than 2% (allowing for warmup period)
        max_nan_ratio = 0.02
        assert spread_mean_nan_ratio < max_nan_ratio, f"spread_mean NaN ratio {spread_mean_nan_ratio:.3f} should be < {max_nan_ratio}"
        assert spread_std_nan_ratio < max_nan_ratio, f"spread_std NaN ratio {spread_std_nan_ratio:.3f} should be < {max_nan_ratio}"


if __name__ == "__main__":
    pytest.main([__file__])
