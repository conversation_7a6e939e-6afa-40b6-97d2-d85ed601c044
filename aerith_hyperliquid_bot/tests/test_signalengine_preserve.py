#!/usr/bin/env python3
"""
Unit tests for SignalEngine preservation of existing spread and ATR statistics.

Tests that the SignalEngine preserves high-quality spread_mean, spread_std,
atr_14_sec, and atr_percent_sec columns from feature files instead of
overwriting them with rolling calculations.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hyperliquid_bot.signals.calculator import SignalEngine


class TestSignalEnginePreserve:
    """Test SignalEngine preservation of existing high-quality statistics."""

    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock()

        # Basic config sections
        config.indicators = Mock()
        config.strategies = Mock()
        config.microstructure = Mock()
        config.regime = Mock()
        config.costs = Mock()
        config.tf_v3 = Mock()

        # Indicators config
        config.indicators.tf_atr_period = 14
        config.indicators.mr_atr_period = 14
        config.indicators.mv_atr_period = 14
        config.indicators.gms_atr_percent_period = 14
        config.indicators.require_volume_for_signals = False
        config.indicators.gms_roc_period = 5
        config.indicators.gms_ma_slope_period = 10

        # Strategies config
        config.strategies.use_tf_v2 = False
        config.strategies.use_mean_reversion = False
        config.strategies.use_mean_variance = False
        config.strategies.use_obi_scalper = False
        config.strategies.use_tf_v3 = True

        # Microstructure config
        config.microstructure.depth_levels = 5
        config.microstructure.obi_smoothing_window = 3
        config.microstructure.obi_smoothing_type = "ema"
        config.microstructure.obi_zscore_window = 24
        config.microstructure.spread_rolling_window = 24
        config.microstructure.spread_metric_to_roll = "relative"

        # Regime config
        config.regime.detector_type = "continuous_gms"
        config.regime.gms_spread_mean_thresh_mode = "fixed"
        config.regime.gms_spread_std_thresh_mode = "fixed"
        config.regime.gms_tight_spread_fallback_percentile = None  # Disable to avoid Mock comparison error
        config.regime.gms_spread_percentile_gate = None  # Disable to avoid Mock comparison error
        config.regime.gms_spread_trend_lookback = None  # Disable to avoid Mock comparison error
        config.regime.gms_depth_slope_thin_limit = None  # Disable to avoid Mock comparison error
        config.regime.gms_depth_skew_thresh = None  # Disable to avoid Mock comparison error
        config.regime.adaptive_obi_base = 0  # Disable to avoid Mock comparison error
        config.regime.gms_tight_spread_percentile_window = 24  # Set specific value to avoid Mock comparison

        # Costs config
        config.costs.funding_rate = 0.0001

        # TF-v3 config
        config.tf_v3.ema_fast = 20
        config.tf_v3.ema_slow = 50

        return config

    @pytest.fixture
    def mock_data_handler(self):
        """Create a mock data handler."""
        handler = Mock()
        return handler

    @pytest.fixture
    def sample_ohlcv_data(self):
        """Create sample OHLCV data with microstructure columns."""
        dates = pd.date_range('2025-01-01', periods=100, freq='1H')
        np.random.seed(42)  # For reproducible tests

        data = pd.DataFrame({
            'open': 50000 + np.random.randn(100) * 100,
            'high': 50100 + np.random.randn(100) * 100,
            'low': 49900 + np.random.randn(100) * 100,
            'close': 50000 + np.random.randn(100) * 100,
            'volume': 1000 + np.random.randn(100) * 100,
            'raw_obi_5': np.random.randn(100) * 0.1,
            'raw_depth_ratio_5': np.random.randn(100) * 0.05,
            'raw_depth_pressure_5': np.random.randn(100) * 0.02,
            'raw_spread_abs': np.random.rand(100) * 0.5 + 0.1,
            'raw_spread_rel': np.random.rand(100) * 0.001 + 0.0001,
        }, index=dates)

        return data

    def test_preserve_high_quality_spread_stats(self, mock_config, mock_data_handler, sample_ohlcv_data):
        """Test that high-quality spread statistics (1% NaN) are preserved."""
        # Add high-quality spread statistics (1% NaN)
        data_with_spread = sample_ohlcv_data.copy()
        data_with_spread['spread_mean'] = np.random.rand(100) * 0.001 + 0.0005
        data_with_spread['spread_std'] = np.random.rand(100) * 0.0005 + 0.0001

        # Introduce 1% NaN values (1 out of 100)
        data_with_spread.loc[data_with_spread.index[0], 'spread_mean'] = np.nan

        # Mock the data handler to return our test data
        mock_data_handler.get_ohlcv_data.return_value = data_with_spread

        # Create SignalEngine and calculate signals
        engine = SignalEngine(mock_config, mock_data_handler)

        with patch('hyperliquid_bot.signals.calculator._add_atr'):
            result = engine.calculate_all_signals()

        # Verify that spread statistics were preserved
        assert 'spread_mean' in result.columns
        assert 'spread_std' in result.columns

        # Check that the original values were preserved (not recalculated)
        # The NaN ratio should still be 1% (1 out of 100)
        assert result['spread_mean'].isna().sum() == 1
        assert result['spread_std'].isna().sum() == 0

        # Verify that the preserved values match the original (non-NaN values)
        original_non_nan = data_with_spread['spread_mean'].dropna()
        result_non_nan = result['spread_mean'].dropna()
        pd.testing.assert_series_equal(original_non_nan, result_non_nan, check_names=False)

    def test_recalculate_poor_quality_spread_stats(self, mock_config, mock_data_handler, sample_ohlcv_data):
        """Test that poor-quality spread statistics (50% NaN) are recalculated."""
        # Add poor-quality spread statistics (50% NaN)
        data_with_spread = sample_ohlcv_data.copy()
        data_with_spread['spread_mean'] = np.random.rand(100) * 0.001 + 0.0005
        data_with_spread['spread_std'] = np.random.rand(100) * 0.0005 + 0.0001

        # Introduce 50% NaN values
        nan_indices = np.random.choice(100, 50, replace=False)
        data_with_spread.loc[data_with_spread.index[nan_indices], 'spread_mean'] = np.nan
        data_with_spread.loc[data_with_spread.index[nan_indices], 'spread_std'] = np.nan

        # Mock the data handler to return our test data
        mock_data_handler.get_ohlcv_data.return_value = data_with_spread

        # Create SignalEngine and calculate signals
        engine = SignalEngine(mock_config, mock_data_handler)

        with patch('hyperliquid_bot.signals.calculator._add_atr'):
            result = engine.calculate_all_signals()

        # Verify that spread statistics were recalculated (not preserved)
        assert 'spread_mean' in result.columns
        assert 'spread_std' in result.columns

        # The values should be different from the original poor-quality data
        # (rolling calculation should produce different results)
        original_values = data_with_spread['spread_mean'].fillna(0)
        result_values = result['spread_mean'].fillna(0)

        # They should not be identical (recalculation occurred)
        assert not np.allclose(original_values, result_values, rtol=1e-10)

    def test_preserve_high_quality_atr_stats(self, mock_config, mock_data_handler, sample_ohlcv_data):
        """Test that high-quality ATR statistics (1% NaN) are preserved."""
        # Add high-quality ATR statistics (1% NaN)
        data_with_atr = sample_ohlcv_data.copy()
        data_with_atr['atr_14_sec'] = np.random.rand(100) * 100 + 50
        data_with_atr['atr_percent_sec'] = np.random.rand(100) * 0.02 + 0.01

        # Introduce 1% NaN values (1 out of 100)
        data_with_atr.loc[data_with_atr.index[0], 'atr_14_sec'] = np.nan

        # Mock the data handler to return our test data
        mock_data_handler.get_ohlcv_data.return_value = data_with_atr

        # Create SignalEngine and calculate signals
        engine = SignalEngine(mock_config, mock_data_handler)

        with patch('hyperliquid_bot.signals.calculator._add_atr'):
            result = engine.calculate_all_signals()

        # Verify that ATR statistics were preserved
        assert 'atr_14_sec' in result.columns
        assert 'atr_percent_sec' in result.columns
        assert 'atr' in result.columns
        assert 'atr_percent' in result.columns

        # Check that the original values were preserved
        assert result['atr_14_sec'].isna().sum() == 1
        assert result['atr_percent_sec'].isna().sum() == 0

        # Verify that atr column uses the preserved atr_14_sec
        pd.testing.assert_series_equal(result['atr'], result['atr_14_sec'], check_names=False)
        pd.testing.assert_series_equal(result['atr_percent'], result['atr_percent_sec'], check_names=False)

    def test_no_fallback_fillna_for_preserved_stats(self, mock_config, mock_data_handler, sample_ohlcv_data):
        """Test that preserved statistics don't get fallback fillna values."""
        # Add high-quality spread statistics
        data_with_spread = sample_ohlcv_data.copy()
        data_with_spread['spread_mean'] = np.random.rand(100) * 0.001 + 0.0005
        data_with_spread['spread_std'] = np.random.rand(100) * 0.0005 + 0.0001

        # Mock the data handler to return our test data
        mock_data_handler.get_ohlcv_data.return_value = data_with_spread

        # Create SignalEngine and calculate signals
        engine = SignalEngine(mock_config, mock_data_handler)

        with patch('hyperliquid_bot.signals.calculator._add_atr'):
            result = engine.calculate_all_signals()

        # Verify that no fallback values (0.0001, 0.00005) were used
        # All values should be from the original high-quality data
        assert not (result['spread_mean'] == 0.0001).any()
        assert not (result['spread_std'] == 0.00005).any()

        # Values should be in the expected range from our test data
        assert result['spread_mean'].min() >= 0.0005
        assert result['spread_mean'].max() <= 0.0015
        assert result['spread_std'].min() >= 0.0001
        assert result['spread_std'].max() <= 0.0006


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
