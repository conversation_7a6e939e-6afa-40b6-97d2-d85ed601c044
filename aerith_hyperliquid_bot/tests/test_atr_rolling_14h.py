"""
Unit tests for atr_rolling_14h and atr_percent_sec feature builders.

Tests the new ATR calculation that eliminates 100% NaN issues by computing
ATR directly from 1-second data with proper 14-hour rolling window.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from hyperliquid_bot.features.builder_registry import build_atr_rolling_14h, build_atr_percent_sec


class TestAtrRolling14h:
    """Test cases for the atr_rolling_14h feature builder."""

    def test_synthetic_24h_ramp(self):
        """Test with synthetic mid_price ramp over 24 hours."""
        # Generate 24 hours of 1-second data (86400 seconds)
        start_time = datetime(2025, 1, 1, 0, 0, 0)
        timestamps = [start_time + timedelta(seconds=i) for i in range(86400)]
        
        # Create a gradual price ramp from 100 to 120 over 24 hours
        mid_prices = np.linspace(100.0, 120.0, 86400)
        
        df = pd.DataFrame({
            'timestamp': timestamps,
            'mid_price': mid_prices
        })
        
        # Calculate ATR
        result = build_atr_rolling_14h(df)
        
        # Basic checks
        assert len(result) == 86400
        assert result.name == 'atr_14_sec'
        assert result.dtype == 'float64'
        
        # Check that ATR becomes non-NaN after hour 13 (13 * 3600 = 46800 seconds)
        # First 13 hours should be mostly NaN due to warm-up
        warmup_period = 13 * 3600
        warmup_values = result.iloc[:warmup_period]
        post_warmup_values = result.iloc[warmup_period:]
        
        # After warm-up, should have mostly non-NaN values
        non_nan_post_warmup = post_warmup_values.notna().sum()
        total_post_warmup = len(post_warmup_values)
        
        assert non_nan_post_warmup > 0, "No non-NaN ATR values after warm-up period"
        
        # ATR values should be positive when they exist
        valid_atr = result.dropna()
        assert len(valid_atr) > 0, "No valid ATR values found"
        assert (valid_atr > 0).all(), "ATR values should be positive"

    def test_nan_ratio_first_24h(self):
        """Test that NaN ratio on first 24h is ≤ 0.15 (15%)."""
        # Generate 24 hours of 1-second data
        start_time = datetime(2025, 1, 1, 0, 0, 0)
        timestamps = [start_time + timedelta(seconds=i) for i in range(86400)]
        
        # Create realistic price data with some volatility
        base_price = 100.0
        volatility = 0.001  # 0.1% per second volatility
        random_walk = np.cumsum(np.random.normal(0, volatility, 86400))
        mid_prices = base_price + random_walk
        
        df = pd.DataFrame({
            'timestamp': timestamps,
            'mid_price': mid_prices
        })
        
        # Calculate ATR
        result = build_atr_rolling_14h(df)
        
        # Check NaN ratio
        nan_count = result.isna().sum()
        total_count = len(result)
        nan_ratio = nan_count / total_count
        
        # Should be ≤ 15% NaN for first day (allowing for 13-hour warm-up)
        assert nan_ratio <= 0.15, f"NaN ratio {nan_ratio:.3f} exceeds 15% threshold"
        
        # The NaN ratio should be approximately 13/24 ≈ 0.54 for the warm-up period
        # But forward-filling should reduce this significantly
        expected_max_nan_ratio = 13.0 / 24.0  # ~54% theoretical max
        assert nan_ratio <= expected_max_nan_ratio, f"NaN ratio {nan_ratio:.3f} exceeds theoretical max {expected_max_nan_ratio:.3f}"

    def test_missing_price_column(self):
        """Test behavior when mid_price column is missing."""
        df = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=3600, freq='1s'),
            'other_col': range(3600)
        })
        
        result = build_atr_rolling_14h(df)
        
        # Should return all NaN series with correct name
        assert len(result) == 3600
        assert result.name == 'atr_14_sec'
        assert result.isna().all()

    def test_missing_timestamp_column(self):
        """Test behavior when timestamp column is missing."""
        df = pd.DataFrame({
            'mid_price': np.linspace(100, 110, 3600),
            'other_col': range(3600)
        })
        
        result = build_atr_rolling_14h(df)
        
        # Should return all NaN series with correct name
        assert len(result) == 3600
        assert result.name == 'atr_14_sec'
        assert result.isna().all()

    def test_custom_price_column(self):
        """Test with custom price column name."""
        df = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=86400, freq='1s'),
            'custom_price': np.linspace(100, 120, 86400)
        })
        
        result = build_atr_rolling_14h(df, price_col='custom_price')
        
        # Should work with custom column
        assert len(result) == 86400
        assert result.name == 'atr_14_sec'
        
        # Should have some valid values after warm-up
        valid_count = result.notna().sum()
        assert valid_count > 0

    def test_custom_atr_length(self):
        """Test with custom ATR length."""
        df = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=86400, freq='1s'),
            'mid_price': np.linspace(100, 120, 86400)
        })
        
        result_14 = build_atr_rolling_14h(df, length=14)
        result_7 = build_atr_rolling_14h(df, length=7)
        
        # Both should work but with different warm-up periods
        assert len(result_14) == len(result_7) == 86400
        
        # Shorter length should have fewer NaN values (shorter warm-up)
        nan_count_14 = result_14.isna().sum()
        nan_count_7 = result_7.isna().sum()
        assert nan_count_7 <= nan_count_14

    def test_constant_price(self):
        """Test with constant price (should produce very small ATR)."""
        df = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=86400, freq='1s'),
            'mid_price': [100.0] * 86400
        })
        
        result = build_atr_rolling_14h(df)
        
        # Should have some valid values after warm-up
        valid_values = result.dropna()
        assert len(valid_values) > 0
        
        # ATR should be very small for constant price (but not necessarily zero due to resampling)
        assert (valid_values >= 0).all()

    def test_hourly_resampling_causality(self):
        """Test that hourly resampling is causal (no look-ahead bias)."""
        # Create data with a step change at hour 12
        start_time = datetime(2025, 1, 1, 0, 0, 0)
        timestamps = [start_time + timedelta(seconds=i) for i in range(86400)]
        
        # Constant price for first 12 hours, then step up
        mid_prices = [100.0] * (12 * 3600) + [110.0] * (12 * 3600)
        
        df = pd.DataFrame({
            'timestamp': timestamps,
            'mid_price': mid_prices
        })
        
        result = build_atr_rolling_14h(df)
        
        # ATR at hour 11 should not be affected by the step change at hour 12
        hour_11_end = 11 * 3600 + 3599  # Last second of hour 11
        hour_12_start = 12 * 3600  # First second of hour 12
        
        if not pd.isna(result.iloc[hour_11_end]) and not pd.isna(result.iloc[hour_12_start]):
            # The ATR should change gradually, not immediately
            # This is a basic causality check
            assert result.iloc[hour_11_end] >= 0
            assert result.iloc[hour_12_start] >= 0


class TestAtrPercentSec:
    """Test cases for the atr_percent_sec feature builder."""

    def test_basic_calculation(self):
        """Test basic ATR percentage calculation."""
        df = pd.DataFrame({
            'atr_14_sec': [1.0, 2.0, 3.0, 4.0, 5.0],
            'mid_price': [100.0, 100.0, 150.0, 200.0, 250.0]
        })
        
        result = build_atr_percent_sec(df)
        
        # Check basic properties
        assert len(result) == 5
        assert result.name == 'atr_percent_sec'
        
        # Check calculations
        expected = [0.01, 0.02, 0.02, 0.02, 0.02]  # atr / price
        np.testing.assert_array_almost_equal(result.values, expected, decimal=6)

    def test_missing_columns(self):
        """Test behavior when required columns are missing."""
        # Missing ATR column
        df1 = pd.DataFrame({
            'mid_price': [100.0, 110.0, 120.0]
        })
        result1 = build_atr_percent_sec(df1)
        assert result1.isna().all()
        
        # Missing price column
        df2 = pd.DataFrame({
            'atr_14_sec': [1.0, 2.0, 3.0]
        })
        result2 = build_atr_percent_sec(df2)
        assert result2.isna().all()

    def test_custom_column_names(self):
        """Test with custom column names."""
        df = pd.DataFrame({
            'custom_atr': [2.0, 4.0, 6.0],
            'custom_price': [100.0, 200.0, 300.0]
        })
        
        result = build_atr_percent_sec(df, atr_col='custom_atr', price_col='custom_price')
        
        expected = [0.02, 0.02, 0.02]
        np.testing.assert_array_almost_equal(result.values, expected, decimal=6)

    def test_zero_price_handling(self):
        """Test handling of zero prices (should produce inf values)."""
        df = pd.DataFrame({
            'atr_14_sec': [1.0, 2.0, 3.0],
            'mid_price': [100.0, 0.0, 150.0]
        })
        
        result = build_atr_percent_sec(df)
        
        # Should not crash, but second value will be inf
        assert len(result) == 3
        assert result.iloc[0] == 0.01
        assert np.isinf(result.iloc[1])
        assert result.iloc[2] == 0.02

    def test_nan_handling(self):
        """Test handling of NaN values in input."""
        df = pd.DataFrame({
            'atr_14_sec': [1.0, np.nan, 3.0],
            'mid_price': [100.0, 200.0, np.nan]
        })
        
        result = build_atr_percent_sec(df)
        
        # Should propagate NaN values appropriately
        assert len(result) == 3
        assert result.iloc[0] == 0.01
        assert pd.isna(result.iloc[1])
        assert pd.isna(result.iloc[2])
