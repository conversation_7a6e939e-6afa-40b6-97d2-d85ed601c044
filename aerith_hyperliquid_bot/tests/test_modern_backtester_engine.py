"""
Test Modern Backtesting Engine
==============================

Verifies the modern backtesting engine properly:
- Simulates 60-second regime updates
- Evaluates trades hourly
- Prevents look-ahead bias
- Tracks performance correctly
"""

import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch
import sys
from pathlib import Path
import pandas as pd
import numpy as np

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.utils.state_mapping import GMS_STATE_STRONG_BULL_TREND


class TestModernBacktestEngine(unittest.TestCase):
    """Test suite for modern backtesting engine."""
    
    def setUp(self):
        """Set up test environment."""
        # Load config
        self.config = load_config()
        
        # Test dates
        self.start_date = datetime(2024, 1, 1, 0, 0, 0)
        self.end_date = datetime(2024, 1, 2, 0, 0, 0)  # 24 hours
        
        # Mock data directory
        self.mock_data_dir = Path("/tmp/test_data")
    
    @patch('hyperliquid_bot.modern.backtester_engine.get_modern_detector')
    @patch('hyperliquid_bot.modern.backtester_engine.get_modern_strategy')
    def test_initialization(self, mock_get_strategy, mock_get_detector):
        """Test engine initializes correctly."""
        # Setup mocks
        mock_detector = Mock()
        mock_strategy = Mock()
        mock_get_detector.return_value = mock_detector
        mock_get_strategy.return_value = mock_strategy
        
        # Create engine
        engine = ModernBacktestEngine(
            self.config,
            self.start_date,
            self.end_date,
            self.mock_data_dir
        )
        
        # Verify initialization
        self.assertEqual(engine.start_date, self.start_date)
        self.assertEqual(engine.end_date, self.end_date)
        self.assertEqual(engine.mode, 'backtest')
        self.assertIsNotNone(engine.data_aggregator)
        self.assertIsNotNone(engine.regime_manager)
        self.assertIsNotNone(engine.hourly_evaluator)
        
        # Verify detector and strategy creation
        mock_get_detector.assert_called_once()
        mock_get_strategy.assert_called_once_with("tf_v3_modern", config=self.config)
    
    def test_generate_hourly_timestamps(self):
        """Test hourly timestamp generation."""
        # Create engine with mocked components
        with patch('hyperliquid_bot.modern.backtester_engine.get_modern_detector'):
            with patch('hyperliquid_bot.modern.backtester_engine.get_modern_strategy'):
                engine = ModernBacktestEngine(
                    self.config,
                    self.start_date,
                    self.end_date,
                    self.mock_data_dir
                )
        
        # Generate timestamps
        timestamps = engine._generate_hourly_timestamps()
        
        # Should have 24 timestamps for 24 hours
        self.assertEqual(len(timestamps), 24)
        
        # First should be start date at hour boundary
        self.assertEqual(timestamps[0], self.start_date)
        
        # Last should be 23:00 on start date
        expected_last = datetime(2024, 1, 1, 23, 0, 0)
        self.assertEqual(timestamps[-1], expected_last)
        
        # All should be hourly
        for i in range(1, len(timestamps)):
            diff = timestamps[i] - timestamps[i-1]
            self.assertEqual(diff, timedelta(hours=1))
    
    @patch('hyperliquid_bot.modern.backtester_engine.ModernDataAggregator')
    def test_simulate_hourly_regime_updates(self, mock_aggregator_class):
        """Test 60 regime updates per hour."""
        # Setup mock aggregator
        mock_aggregator = Mock()
        mock_aggregator_class.return_value = mock_aggregator
        
        # Create sample 1s data
        sample_data = pd.DataFrame({
            'close': np.random.randn(3600) + 100,  # 1 hour of 1s data
            'volume': np.random.rand(3600) * 1000,
            'momentum': np.random.randn(3600),
            'volatility': np.abs(np.random.randn(3600)) * 0.01
        }, index=pd.date_range(self.start_date, periods=3600, freq='1s'))
        
        # Mock data loading
        mock_aggregator.load_features_1s.return_value = sample_data
        
        # Create mock 60s aggregated data
        mock_60s_data = pd.DataFrame({
            'close': [100.5],
            'momentum': [50.0],
            'volatility': [0.015]
        }, index=[self.start_date])
        mock_aggregator.aggregate_to_60s.return_value = mock_60s_data
        
        # Setup mock detector
        mock_detector = Mock()
        mock_detector.update.return_value = {
            'state': GMS_STATE_STRONG_BULL_TREND,
            'confidence': 0.8,
            'features': {'momentum': 50.0}
        }
        
        with patch('hyperliquid_bot.modern.backtester_engine.get_modern_detector', return_value=mock_detector):
            with patch('hyperliquid_bot.modern.backtester_engine.get_modern_strategy'):
                engine = ModernBacktestEngine(
                    self.config,
                    self.start_date,
                    self.end_date,
                    self.mock_data_dir
                )
                engine.data_aggregator = mock_aggregator
                engine.regime_detector = mock_detector
                
                # Simulate one hour
                hour_start = self.start_date
                hour_end = hour_start + timedelta(hours=1)
                
                regime_states = engine._simulate_hourly_regime_updates(
                    hour_start, hour_end
                )
                
                # Should have up to 60 regime updates
                self.assertGreater(len(regime_states), 0)
                self.assertLessEqual(len(regime_states), 60)
                
                # Verify aggregation was called with proper look-ahead prevention
                for call in mock_aggregator.aggregate_to_60s.call_args_list:
                    data_arg, time_arg = call[0]
                    # Ensure we only used past data
                    self.assertTrue(all(data_arg.index <= time_arg))
    
    @patch('hyperliquid_bot.modern.backtester_engine.ModernDataAggregator')
    def test_evaluate_trading_opportunity(self, mock_aggregator_class):
        """Test hourly trading evaluation."""
        # Setup mock aggregator
        mock_aggregator = Mock()
        mock_aggregator_class.return_value = mock_aggregator
        
        # Create sample hourly data
        hourly_data = pd.DataFrame({
            'open': [100, 101],
            'high': [102, 103],
            'low': [99, 100],
            'close': [101, 102],
            'volume': [10000, 12000]
        }, index=[
            self.start_date - timedelta(hours=1),
            self.start_date
        ])
        mock_aggregator.load_hourly_bars.return_value = hourly_data
        
        # Setup mocks
        mock_detector = Mock()
        mock_strategy = Mock()
        mock_evaluator = Mock()
        mock_evaluator.should_evaluate.return_value = True
        mock_evaluator.evaluate.return_value = {
            'direction': 'long',
            'position_size': 0.25,
            'confidence': 0.8
        }
        
        with patch('hyperliquid_bot.modern.backtester_engine.get_modern_detector', return_value=mock_detector):
            with patch('hyperliquid_bot.modern.backtester_engine.get_modern_strategy', return_value=mock_strategy):
                with patch('hyperliquid_bot.modern.backtester_engine.calculate_indicators_for_tf_v3', return_value=hourly_data):
                    engine = ModernBacktestEngine(
                        self.config,
                        self.start_date,
                        self.end_date,
                        self.mock_data_dir
                    )
                    engine.data_aggregator = mock_aggregator
                    engine.hourly_evaluator = mock_evaluator
                    
                    # Evaluate at hour boundary
                    hour_boundary = self.start_date + timedelta(hours=1)
                    signal = engine._evaluate_trading_opportunity(hour_boundary)
                    
                    # Should return signal
                    self.assertIsNotNone(signal)
                    self.assertEqual(signal['direction'], 'long')
                    self.assertEqual(signal['position_size'], 0.25)
    
    def test_refine_execution(self):
        """Test execution refinement with 1-minute data."""
        # Create mock aggregator
        mock_aggregator = Mock()
        
        # Create 5 minutes of 1s data
        exec_data = pd.DataFrame({
            'close': np.linspace(100, 101, 300),  # 5 minutes
            'volume': np.ones(300) * 100
        }, index=pd.date_range(self.start_date, periods=300, freq='1s'))
        mock_aggregator.load_features_1s.return_value = exec_data
        
        with patch('hyperliquid_bot.modern.backtester_engine.get_modern_detector'):
            with patch('hyperliquid_bot.modern.backtester_engine.get_modern_strategy'):
                engine = ModernBacktestEngine(
                    self.config,
                    self.start_date,
                    self.end_date,
                    self.mock_data_dir
                )
                engine.data_aggregator = mock_aggregator
                
                # Create trade signal
                trade_signal = {
                    'direction': 'long',
                    'position_size': 0.25,
                    'signal_price': 100.0,
                    'regime': GMS_STATE_STRONG_BULL_TREND
                }
                
                # Refine execution
                executed = engine._refine_execution(
                    trade_signal,
                    self.start_date
                )
                
                # Should return executed trade
                self.assertIsNotNone(executed)
                self.assertEqual(executed['direction'], 'long')
                self.assertEqual(executed['position_size'], 0.25)
                
                # Entry price should be VWAP of first minute
                # For linear price increase, VWAP ≈ average
                expected_vwap = exec_data.iloc[:60]['close'].mean()
                self.assertAlmostEqual(executed['entry_price'], expected_vwap, places=2)
    
    def test_calculate_performance_metrics(self):
        """Test performance metrics calculation."""
        with patch('hyperliquid_bot.modern.backtester_engine.get_modern_detector'):
            with patch('hyperliquid_bot.modern.backtester_engine.get_modern_strategy'):
                engine = ModernBacktestEngine(
                    self.config,
                    self.start_date,
                    self.end_date,
                    self.mock_data_dir
                )
                
                # Add some test trades
                engine.trades = [
                    {
                        'direction': 'long',
                        'position_size': 0.25,
                        'entry_price': 100
                    },
                    {
                        'direction': 'short',
                        'position_size': 0.25,
                        'entry_price': 101
                    }
                ]
                
                # Calculate metrics
                metrics = engine._calculate_performance_metrics()
                
                # Verify metrics
                self.assertEqual(metrics['total_trades'], 2)
                self.assertIn('total_return', metrics)
                self.assertIn('win_rate', metrics)
                self.assertIn('sharpe_ratio', metrics)
                self.assertIn('max_drawdown', metrics)
    
    def test_no_look_ahead_in_regime_updates(self):
        """Verify no look-ahead bias in regime updates."""
        mock_aggregator = Mock()
        
        # Track aggregation calls
        aggregation_calls = []
        
        def track_aggregation(data, current_time):
            aggregation_calls.append((data.index[-1], current_time))
            return pd.DataFrame({'close': [100]}, index=[current_time])
        
        mock_aggregator.aggregate_to_60s.side_effect = track_aggregation
        
        # Create data
        sample_data = pd.DataFrame({
            'close': np.ones(7200) * 100  # 2 hours
        }, index=pd.date_range(self.start_date, periods=7200, freq='1s'))
        mock_aggregator.load_features_1s.return_value = sample_data
        
        with patch('hyperliquid_bot.modern.backtester_engine.get_modern_detector'):
            with patch('hyperliquid_bot.modern.backtester_engine.get_modern_strategy'):
                with patch('hyperliquid_bot.modern.backtester_engine.ModernDataAggregator', return_value=mock_aggregator):
                    engine = ModernBacktestEngine(
                        self.config,
                        self.start_date,
                        self.end_date,
                        self.mock_data_dir
                    )
                    
                    # Simulate regime updates
                    engine._simulate_hourly_regime_updates(
                        self.start_date,
                        self.start_date + timedelta(hours=1)
                    )
                    
                    # Verify no look-ahead
                    for data_end, current_time in aggregation_calls:
                        self.assertLessEqual(
                            data_end, current_time,
                            f"Look-ahead detected: data up to {data_end} used at {current_time}"
                        )


if __name__ == '__main__':
    unittest.main()