"""
Unit tests for the AdaptiveThreshold class.

Tests the causal, rolling percentile-based threshold calculation.
"""

import pytest
import numpy as np
from hyperliquid_bot.utils.adaptive_threshold import AdaptiveThreshold


class TestAdaptiveThreshold:
    """Test cases for AdaptiveThreshold class."""
    
    def test_initialization(self):
        """Test proper initialization of AdaptiveThreshold."""
        threshold = AdaptiveThreshold(0.15, 0.50, 100)
        
        assert threshold.low_pct == 0.15
        assert threshold.high_pct == 0.50
        assert threshold.window_len == 100
        assert len(threshold.buffer) == 0
        
    def test_initialization_validation(self):
        """Test validation of initialization parameters."""
        # Test invalid percentiles
        with pytest.raises(ValueError, match="low_pct must be between 0.0 and 1.0"):
            AdaptiveThreshold(-0.1, 0.5, 100)
            
        with pytest.raises(ValueError, match="high_pct must be between 0.0 and 1.0"):
            AdaptiveThreshold(0.1, 1.5, 100)
            
        with pytest.raises(ValueError, match="low_pct .* must be less than high_pct"):
            AdaptiveThreshold(0.6, 0.4, 100)
            
        with pytest.raises(ValueError, match="window_len must be positive"):
            AdaptiveThreshold(0.1, 0.5, 0)
            
    def test_empty_buffer_returns_none(self):
        """Test that empty buffer returns None thresholds."""
        threshold = AdaptiveThreshold(0.15, 0.50, 100)
        
        # First update should return None (no history)
        low, high = threshold.update(1.0)
        assert low is None
        assert high is None
        assert threshold.get_buffer_size() == 1
        
    def test_causal_computation(self):
        """Test that percentile calculation is causal (no look-ahead bias)."""
        threshold = AdaptiveThreshold(0.25, 0.75, 100)
        
        # Add some initial values
        values = [1.0, 2.0, 3.0, 4.0, 5.0]
        results = []
        
        for value in values:
            low, high = threshold.update(value)
            results.append((low, high, value))
            
        # First value should return None (no history)
        assert results[0] == (None, None, 1.0)
        
        # Second value should use only the first value for percentiles
        low, high, current = results[1]
        assert low == 1.0  # 25th percentile of [1.0] is 1.0
        assert high == 1.0  # 75th percentile of [1.0] is 1.0
        assert current == 2.0  # Current value not used in calculation
        
        # Third value should use [1.0, 2.0] for percentiles
        low, high, current = results[2]
        expected_low = np.percentile([1.0, 2.0], 25)
        expected_high = np.percentile([1.0, 2.0], 75)
        assert abs(low - expected_low) < 1e-10
        assert abs(high - expected_high) < 1e-10
        assert current == 3.0
        
    def test_rolling_window_behavior(self):
        """Test that the rolling window maintains correct size."""
        window_size = 5
        threshold = AdaptiveThreshold(0.2, 0.8, window_size)
        
        # Add more values than window size
        for i in range(10):
            threshold.update(float(i))
            
        # Buffer should not exceed window size
        assert threshold.get_buffer_size() == window_size
        
        # Buffer should contain the last 5 values: [5, 6, 7, 8, 9]
        buffer_values = list(threshold.buffer)
        expected = [5.0, 6.0, 7.0, 8.0, 9.0]
        assert buffer_values == expected
        
    def test_nan_handling(self):
        """Test handling of NaN values."""
        threshold = AdaptiveThreshold(0.25, 0.75, 100)
        
        # Add some valid values
        threshold.update(1.0)
        threshold.update(2.0)
        threshold.update(3.0)
        
        # Add NaN value
        low, high = threshold.update(np.nan)
        
        # Should still work with valid values only
        assert low is not None
        assert high is not None
        assert not np.isnan(low)
        assert not np.isnan(high)
        
        # Add more NaN values
        threshold.update(np.nan)
        threshold.update(np.nan)
        
        # Should still work as long as there are some valid values
        low, high = threshold.update(4.0)
        assert low is not None
        assert high is not None
        
    def test_all_nan_buffer(self):
        """Test behavior when buffer contains only NaN values."""
        threshold = AdaptiveThreshold(0.25, 0.75, 100)
        
        # Fill buffer with NaN values
        threshold.update(np.nan)
        low, high = threshold.update(np.nan)
        
        # Should return None when no valid data
        assert low is None
        assert high is None
        
    def test_linear_ramp_percentiles(self):
        """Test percentiles with a linear ramp to verify causal behavior."""
        threshold = AdaptiveThreshold(0.15, 0.50, 100)
        
        # Create linear ramp from 0 to 1
        n_points = 20
        values = np.linspace(0, 1, n_points)
        
        results = []
        for i, value in enumerate(values):
            low, high = threshold.update(value)
            if low is not None:
                # Verify that current sample is excluded from percentile calculation
                history = list(threshold.buffer)[:-1]  # Exclude current sample
                if len(history) > 0:
                    expected_low = np.percentile(history, 15)
                    expected_high = np.percentile(history, 50)
                    assert abs(low - expected_low) < 1e-10, f"Low percentile mismatch at step {i}"
                    assert abs(high - expected_high) < 1e-10, f"High percentile mismatch at step {i}"
            results.append((low, high))
            
        # Verify we got valid results for most of the sequence
        valid_results = [(l, h) for l, h in results if l is not None]
        assert len(valid_results) >= n_points - 1  # All except first should be valid
        
    def test_buffer_stats(self):
        """Test buffer statistics functionality."""
        threshold = AdaptiveThreshold(0.25, 0.75, 100)
        
        # Empty buffer
        stats = threshold.get_buffer_stats()
        assert stats["size"] == 0
        assert stats["min"] is None
        
        # Add some values
        values = [1.0, 3.0, 2.0, 5.0, 4.0]
        for value in values:
            threshold.update(value)
            
        stats = threshold.get_buffer_stats()
        assert stats["size"] == 5
        assert stats["valid_size"] == 5
        assert stats["min"] == 1.0
        assert stats["max"] == 5.0
        assert abs(stats["mean"] - 3.0) < 1e-10
        
        # Add NaN values
        threshold.update(np.nan)
        threshold.update(np.nan)
        
        stats = threshold.get_buffer_stats()
        assert stats["size"] == 7
        assert stats["valid_size"] == 5  # Still 5 valid values
        assert stats["min"] == 1.0
        assert stats["max"] == 5.0
        
    def test_reset_functionality(self):
        """Test buffer reset functionality."""
        threshold = AdaptiveThreshold(0.25, 0.75, 100)
        
        # Add some values
        for i in range(10):
            threshold.update(float(i))
            
        assert threshold.get_buffer_size() == 10
        
        # Reset buffer
        threshold.reset()
        assert threshold.get_buffer_size() == 0
        
        # Should return None after reset
        low, high = threshold.update(1.0)
        assert low is None
        assert high is None
        assert threshold.get_buffer_size() == 1
