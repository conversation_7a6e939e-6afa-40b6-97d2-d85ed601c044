# tests/test_scalper_risk.py

import unittest
from hyperliquid_bot.core.risk import RiskManager


class TestFixedFractionPositionSize(unittest.TestCase):
    """Test the fixed fraction position size calculation."""

    def test_normal_values(self):
        """Test with normal values."""
        equity = 10000.0
        risk_pct = 0.005  # 0.5%
        expected = 50.0  # 10000 * 0.005

        result = RiskManager.get_fixed_fraction_position_size(equity, risk_pct)
        self.assertEqual(result, expected)

    def test_zero_equity(self):
        """Test with zero equity."""
        result = RiskManager.get_fixed_fraction_position_size(0, 0.005)
        self.assertEqual(result, 0.0)

    def test_negative_equity(self):
        """Test with negative equity (should return 0)."""
        result = RiskManager.get_fixed_fraction_position_size(-1000, 0.005)
        self.assertEqual(result, 0.0)

    def test_negative_risk(self):
        """Test with negative risk (should be capped at 0)."""
        result = RiskManager.get_fixed_fraction_position_size(10000, -0.01)
        self.assertEqual(result, 0.0)

    def test_high_risk(self):
        """Test with risk > 1.0 (should be capped at 1.0)."""
        equity = 10000.0
        result = RiskManager.get_fixed_fraction_position_size(equity, 1.5)
        self.assertEqual(result, equity)


if __name__ == '__main__':
    unittest.main()
