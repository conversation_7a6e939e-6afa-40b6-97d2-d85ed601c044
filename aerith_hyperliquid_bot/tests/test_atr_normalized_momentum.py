#!/usr/bin/env python3
"""
Unit tests for ATR-normalized momentum signal calculation.

This module tests the ATR-normalized momentum signal implementation to ensure:
1. No look-ahead bias in the calculation
2. Proper handling of ATR normalization
3. Correct fallback behavior when ATR is not available
4. Appropriate threshold scaling for different market conditions

Critical for maintaining system integrity after momentum signal changes.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.signals.calculator import SignalEngine
from hyperliquid_bot.data.handler import HistoricalDataHandler


class TestATRNormalizedMomentum(unittest.TestCase):
    """Test ATR-normalized momentum signal calculation."""

    def setUp(self):
        """Set up test fixtures."""
        # Create sample OHLCV data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
        np.random.seed(42)  # For reproducible tests
        
        # Create realistic price data
        base_price = 50000
        price_changes = np.random.normal(0, 0.01, 100)  # 1% volatility
        prices = base_price * np.exp(np.cumsum(price_changes))
        
        self.test_data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': prices * (1 + np.abs(np.random.normal(0, 0.005, 100))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.005, 100))),
            'close': prices,
            'volume': np.random.randint(1000, 10000, 100)
        })
        self.test_data.set_index('timestamp', inplace=True)
        
        # Create minimal config
        config_dict = {
            'data_paths': {
                'l2_data_root': '/tmp',
                'ohlcv_base_path': '/tmp',
                'raw_l2_dir': '/tmp', 
                'feature_1s_dir': '/tmp',
                'log_dir': '/tmp'
            },
            'cache': {'l2_cache_max_size': 1000},
            'backtest': {'period_preset': 'custom'},
            'indicators': {
                'gms_ma_slope_period': 20,
                'gms_atr_period': 14,
                'gms_atr_percent_period': 14
            },
            'regime': {
                'detector_type': 'continuous_gms'
            }
        }
        
        self.config = Config(**config_dict)

    def test_atr_normalization_no_look_ahead(self):
        """Test that ATR normalization doesn't introduce look-ahead bias."""
        # Create a simplified signal engine for testing
        data_handler = type('MockDataHandler', (), {
            'get_combined_data': lambda: self.test_data,
            'config': self.config
        })()
        
        signal_engine = SignalEngine(self.config, data_handler)
        
        # Add ATR and SMA columns manually for testing
        self.test_data['atr'] = self.test_data['close'].rolling(14).apply(
            lambda x: (x.max() - x.min()) / len(x), raw=False
        )
        self.test_data['SMA_20'] = self.test_data['close'].rolling(20).mean()
        
        # Calculate momentum signals
        ma_slope_period = 20
        ma_col = f"SMA_{ma_slope_period}"
        
        # Calculate absolute price slope
        abs_ma_slope = self.test_data[ma_col] - self.test_data[ma_col].shift(1)
        
        # Calculate ATR-normalized slope
        atr_normalized_slope = abs_ma_slope / self.test_data['atr']
        
        # Test 1: No look-ahead bias - each value should only depend on past data
        for i in range(21, len(self.test_data)):  # Start after warmup period
            # ATR at time i should only use data up to time i
            atr_value = self.test_data['atr'].iloc[i]
            self.assertFalse(pd.isna(atr_value), f"ATR should not be NaN at index {i}")
            
            # MA slope should only use data up to time i
            slope_value = abs_ma_slope.iloc[i]
            if not pd.isna(slope_value):
                # Normalized slope should be finite
                normalized_value = atr_normalized_slope.iloc[i]
                self.assertFalse(np.isinf(normalized_value), 
                                f"Normalized slope should not be infinite at index {i}")

    def test_atr_normalization_scaling(self):
        """Test that ATR normalization provides appropriate scaling."""
        # Create test data with different volatility regimes
        high_vol_data = self.test_data.copy()
        high_vol_data['close'] *= (1 + np.random.normal(0, 0.05, len(high_vol_data)))  # High volatility
        
        low_vol_data = self.test_data.copy()
        low_vol_data['close'] *= (1 + np.random.normal(0, 0.001, len(low_vol_data)))  # Low volatility
        
        # Calculate ATR for both
        high_vol_data['atr'] = high_vol_data['close'].rolling(14).apply(
            lambda x: (x.max() - x.min()) / len(x), raw=False
        )
        low_vol_data['atr'] = low_vol_data['close'].rolling(14).apply(
            lambda x: (x.max() - x.min()) / len(x), raw=False
        )
        
        # Calculate slopes
        high_vol_data['SMA_20'] = high_vol_data['close'].rolling(20).mean()
        low_vol_data['SMA_20'] = low_vol_data['close'].rolling(20).mean()
        
        high_vol_slope = high_vol_data['SMA_20'] - high_vol_data['SMA_20'].shift(1)
        low_vol_slope = low_vol_data['SMA_20'] - low_vol_data['SMA_20'].shift(1)
        
        # Normalize by ATR
        high_vol_normalized = high_vol_slope / high_vol_data['atr']
        low_vol_normalized = low_vol_slope / low_vol_data['atr']
        
        # Test: ATR normalization should make momentum comparable across volatility regimes
        # High volatility absolute slopes should be larger but normalized slopes should be similar
        high_vol_abs_mean = np.abs(high_vol_slope).mean()
        low_vol_abs_mean = np.abs(low_vol_slope).mean()
        
        high_vol_norm_mean = np.abs(high_vol_normalized).mean()
        low_vol_norm_mean = np.abs(low_vol_normalized).mean()
        
        # Absolute slopes should be very different
        self.assertGreater(high_vol_abs_mean, low_vol_abs_mean * 2, 
                          "High volatility should have larger absolute slopes")
        
        # Normalized slopes should be more similar (within 2x ratio)
        ratio = high_vol_norm_mean / low_vol_norm_mean
        self.assertLess(ratio, 3, "ATR normalization should reduce volatility scaling differences")

    def test_fallback_behavior(self):
        """Test fallback behavior when ATR is not available."""
        # Create test data without ATR
        test_data = self.test_data.copy()
        test_data['SMA_20'] = test_data['close'].rolling(20).mean()
        
        # Calculate slope without ATR normalization (fallback)
        abs_ma_slope = test_data['SMA_20'] - test_data['SMA_20'].shift(1)
        
        # Test: Fallback should produce absolute price differences
        self.assertGreater(np.abs(abs_ma_slope).mean(), 1.0, 
                          "Fallback should produce absolute price differences")
        
        # Test: No infinite or NaN values in valid range
        valid_slopes = abs_ma_slope.dropna()
        self.assertEqual(len(valid_slopes[np.isinf(valid_slopes)]), 0, 
                        "No infinite values should be present")

    def test_threshold_compatibility(self):
        """Test that thresholds are appropriate for ATR-normalized values."""
        # Create test data with known characteristics
        test_data = self.test_data.copy()
        test_data['atr'] = test_data['close'].rolling(14).apply(
            lambda x: (x.max() - x.min()) / len(x), raw=False
        )
        test_data['SMA_20'] = test_data['close'].rolling(20).mean()
        
        # Calculate ATR-normalized momentum
        abs_ma_slope = test_data['SMA_20'] - test_data['SMA_20'].shift(1)
        atr_normalized_slope = abs_ma_slope / test_data['atr']
        
        # Test: Most values should be within reasonable threshold range
        valid_normalized = atr_normalized_slope.dropna()
        
        # Strong momentum threshold (2.5x ATR)
        strong_threshold = 2.5
        strong_signals = np.abs(valid_normalized) >= strong_threshold
        strong_percentage = strong_signals.sum() / len(valid_normalized)
        
        # Should have some strong signals but not too many (expect 5-20%)
        self.assertGreater(strong_percentage, 0.01, "Should have some strong momentum signals")
        self.assertLess(strong_percentage, 0.5, "Should not have too many strong signals")
        
        # Weak momentum threshold (0.5x ATR)
        weak_threshold = 0.5
        weak_signals = np.abs(valid_normalized) <= weak_threshold
        weak_percentage = weak_signals.sum() / len(valid_normalized)
        
        # Should have reasonable number of weak signals (expect 20-60%)
        self.assertGreater(weak_percentage, 0.1, "Should have some weak momentum signals")
        self.assertLess(weak_percentage, 0.8, "Should not have too many weak signals")

    def test_momentum_direction_preservation(self):
        """Test that momentum direction is preserved after normalization."""
        # Create test data with clear upward trend
        dates = pd.date_range(start='2024-01-01', periods=50, freq='1H')
        prices = 50000 + np.arange(50) * 100  # Clear upward trend
        
        trend_data = pd.DataFrame({
            'timestamp': dates,
            'close': prices
        })
        trend_data.set_index('timestamp', inplace=True)
        
        # Add ATR and SMA
        trend_data['atr'] = 500  # Constant ATR for simplicity
        trend_data['SMA_20'] = trend_data['close'].rolling(20).mean()
        
        # Calculate momentum
        abs_ma_slope = trend_data['SMA_20'] - trend_data['SMA_20'].shift(1)
        atr_normalized_slope = abs_ma_slope / trend_data['atr']
        
        # Test: Upward trend should produce positive momentum
        valid_momentum = atr_normalized_slope.dropna()
        positive_momentum = valid_momentum[valid_momentum > 0]
        
        self.assertGreater(len(positive_momentum), len(valid_momentum) * 0.8, 
                          "Upward trend should produce mostly positive momentum")
        
        # Test: Momentum magnitude should be reasonable
        self.assertGreater(valid_momentum.mean(), 0.1, 
                          "Mean momentum should be positive for upward trend")


if __name__ == '__main__':
    unittest.main()