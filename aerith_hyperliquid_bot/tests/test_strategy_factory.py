#!/usr/bin/env python
# tests/test_strategy_factory.py

"""
Unit tests for the Strategy Factory (Task R-103).
"""

import pytest
from unittest.mock import Mock

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.strategies.strategy_factory import (
    StrategyFactory,
    SUPPORTED_STRATEGIES,
    SUPPORTED_DETECTORS,
    STRATEGY_ALIASES
)
from hyperliquid_bot.strategies.evaluator import TrendFollowingStrategy, MeanReversionStrategy, MeanVarianceStrategy
from hyperliquid_bot.strategies.tf_v3 import TFV3Strategy
from hyperliquid_bot.strategies.obi_scalper_strategy import OBIScalperStrategy


class TestStrategyFactory:
    """Test cases for StrategyFactory."""

    @pytest.fixture
    def mock_config(self):
        """Create a mock config for testing."""
        config = Mock(spec=Config)

        # Mock strategies config
        strategies_mock = Mock()
        strategies_mock.tf_use_obi_filter = False
        strategies_mock.tf_use_funding_filter = False
        strategies_mock.OBIScalperStrategy = Mock()
        strategies_mock.OBIScalperStrategy.defaults = Mock()
        strategies_mock.OBIScalperStrategy.defaults.vol_veto_threshold = 0.002
        strategies_mock.OBIScalperStrategy.defaults.spread_veto_threshold = 0.0001
        strategies_mock.OBIScalperStrategy.defaults.obi_l1_3_trigger = 0.50
        strategies_mock.OBIScalperStrategy.defaults.tp_ticks = 7
        strategies_mock.OBIScalperStrategy.defaults.sl_ticks = 5
        strategies_mock.OBIScalperStrategy.defaults.timeout_seconds = 30
        strategies_mock.OBIScalperStrategy.defaults.allowed_gms_states = ["CHOP", "BULL", "BEAR"]
        strategies_mock.OBIScalperStrategy.defaults.zero_sign_eps = 0.001
        config.strategies = strategies_mock

        # Mock tf_v3 config
        tf_v3_mock = Mock()
        tf_v3_mock.enabled = True
        tf_v3_mock.ema_fast = 20
        tf_v3_mock.ema_slow = 50
        tf_v3_mock.atr_period = 14
        tf_v3_mock.atr_trail_k = 3.0
        tf_v3_mock.max_trade_life_h = 24
        tf_v3_mock.risk_frac = 0.25
        tf_v3_mock.max_notional = 25000
        tf_v3_mock.gms_max_age_sec = 300
        config.tf_v3 = tf_v3_mock

        # Mock indicators config
        indicators_mock = Mock()
        indicators_mock.mr_require_obi_filter = True
        indicators_mock.mv_require_obi_filter = True
        config.indicators = indicators_mock

        # Mock regime config
        regime_mock = Mock()
        regime_mock.detector_type = 'rule_based'
        config.regime = regime_mock

        return config

    @pytest.fixture
    def factory(self, mock_config):
        """Create a StrategyFactory instance for testing."""
        return StrategyFactory(mock_config)

    def test_supported_strategies_constant(self):
        """Test that SUPPORTED_STRATEGIES contains exactly the 5 expected strategies."""
        expected_strategies = {
            "tf_v2": TrendFollowingStrategy,
            "tf_v3": TFV3Strategy,
            "mean_reversion": MeanReversionStrategy,
            "mean_variance": MeanVarianceStrategy,
            "obi_scalper": OBIScalperStrategy,
        }
        assert SUPPORTED_STRATEGIES == expected_strategies

    def test_supported_detectors_constant(self):
        """Test that SUPPORTED_DETECTORS contains exactly the 3 expected detectors."""
        expected_detectors = {
            "granular_microstructure",
            "continuous_gms",
            "rule_based"
        }
        assert SUPPORTED_DETECTORS == expected_detectors

    def test_strategy_aliases_constant(self):
        """Test that STRATEGY_ALIASES contains the expected alias mapping."""
        expected_aliases = {
            "trend_following": "tf_v2",
        }
        assert STRATEGY_ALIASES == expected_aliases

    def test_create_strategy_tf_v2(self, factory):
        """Test creating tf_v2 strategy."""
        strategy = factory.create_strategy("tf_v2")
        assert isinstance(strategy, TrendFollowingStrategy)

    def test_create_strategy_tf_v3(self, factory):
        """Test creating tf_v3 strategy."""
        strategy = factory.create_strategy("tf_v3")
        assert isinstance(strategy, TFV3Strategy)

    def test_create_strategy_mean_reversion(self, factory):
        """Test creating mean_reversion strategy."""
        strategy = factory.create_strategy("mean_reversion")
        assert isinstance(strategy, MeanReversionStrategy)

    def test_create_strategy_mean_variance(self, factory):
        """Test creating mean_variance strategy."""
        strategy = factory.create_strategy("mean_variance")
        assert isinstance(strategy, MeanVarianceStrategy)

    def test_create_strategy_obi_scalper(self, factory):
        """Test creating obi_scalper strategy."""
        strategy = factory.create_strategy("obi_scalper")
        assert isinstance(strategy, OBIScalperStrategy)

    def test_create_strategy_with_alias(self, factory):
        """Test creating strategy using alias (trend_following -> tf_v2)."""
        strategy = factory.create_strategy("trend_following")
        assert isinstance(strategy, TrendFollowingStrategy)

    def test_create_strategy_unknown_raises_error(self, factory):
        """Test that creating unknown strategy raises ValueError."""
        with pytest.raises(ValueError) as exc_info:
            factory.create_strategy("unknown_strategy")

        assert "Unsupported strategy 'unknown_strategy'" in str(exc_info.value)
        assert "tf_v2" in str(exc_info.value)  # Should list supported strategies

    def test_create_strategy_empty_name_raises_error(self, factory):
        """Test that creating strategy with empty name raises ValueError."""
        with pytest.raises(ValueError) as exc_info:
            factory.create_strategy("")

        assert "Unsupported strategy ''" in str(exc_info.value)

    def test_create_strategy_none_raises_error(self, factory):
        """Test that creating strategy with None name raises ValueError."""
        with pytest.raises(ValueError) as exc_info:
            factory.create_strategy(None)

        assert "Unsupported strategy 'None'" in str(exc_info.value)


class TestConfigValidation:
    """Test cases for config validation."""

    def test_supported_detector_validation(self):
        """Test that config validation accepts supported detectors."""
        # This test would require a full config setup, so we'll test the constants
        for detector in SUPPORTED_DETECTORS:
            assert detector in ["granular_microstructure", "continuous_gms", "rule_based"]

    def test_unsupported_detector_not_in_set(self):
        """Test that unsupported detectors are not in SUPPORTED_DETECTORS."""
        unsupported_detectors = ["hurst", "invalid_detector", ""]
        for detector in unsupported_detectors:
            assert detector not in SUPPORTED_DETECTORS


if __name__ == "__main__":
    pytest.main([__file__])
