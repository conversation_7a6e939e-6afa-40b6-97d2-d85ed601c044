"""
Unit tests for OBI helper functions.

Tests the dynamic OBI column resolution functionality.
"""

import pytest
import pandas as pd
from hyperliquid_bot.utils.feature_naming import obi_col, get_obi_column_name


class TestOBIHelper:
    """Test cases for OBI column helper functions."""

    def test_obi_col_with_raw_obi_5(self):
        """Test that helper returns raw_obi_5 when available."""
        columns = ['timestamp', 'raw_obi_5', 'price', 'volume']
        result = obi_col(columns, 5)
        assert result == 'raw_obi_5'

    def test_obi_col_with_obi_10(self):
        """Test that helper returns obi_10 when raw_obi_10 not available."""
        columns = ['timestamp', 'obi_10', 'price', 'volume']
        result = obi_col(columns, 10)
        assert result == 'obi_10'

    def test_obi_col_with_raw_obi_20(self):
        """Test that helper returns raw_obi_20 when available."""
        columns = ['timestamp', 'raw_obi_20', 'price', 'volume']
        result = obi_col(columns, 20)
        assert result == 'raw_obi_20'

    def test_obi_col_prefers_raw_obi(self):
        """Test that helper prefers raw_obi_X over obi_X when both available."""
        columns = ['timestamp', 'raw_obi_5', 'obi_5', 'price', 'volume']
        result = obi_col(columns, 5)
        assert result == 'raw_obi_5'

    def test_obi_col_with_pandas_index(self):
        """Test that helper works with pandas Index."""
        df = pd.DataFrame({'timestamp': [1], 'raw_obi_5': [0.1], 'price': [100]})
        result = obi_col(df.columns, 5)
        assert result == 'raw_obi_5'

    def test_obi_col_missing_column_raises_keyerror(self):
        """Test that helper raises KeyError when OBI column not found."""
        columns = ['timestamp', 'price', 'volume']
        with pytest.raises(KeyError) as exc_info:
            obi_col(columns, 5)
        
        assert "OBI column for depth 5 not found" in str(exc_info.value)
        assert "raw_obi_5" in str(exc_info.value)
        assert "obi_5" in str(exc_info.value)

    def test_obi_col_empty_list_raises_keyerror(self):
        """Test that helper raises KeyError with empty column list."""
        columns = []
        with pytest.raises(KeyError) as exc_info:
            obi_col(columns, 5)
        
        assert "OBI column for depth 5 not found" in str(exc_info.value)

    def test_obi_col_shows_available_obi_columns_in_error(self):
        """Test that error message shows available OBI columns."""
        columns = ['timestamp', 'raw_obi_10', 'obi_20', 'price']
        with pytest.raises(KeyError) as exc_info:
            obi_col(columns, 5)
        
        error_msg = str(exc_info.value)
        assert "Available OBI columns: ['raw_obi_10', 'obi_20']" in error_msg

    def test_get_obi_column_name_prefer_raw(self):
        """Test get_obi_column_name with prefer_raw=True."""
        result = get_obi_column_name(5, prefer_raw=True)
        assert result == ['raw_obi_5', 'obi_5']

    def test_get_obi_column_name_prefer_non_raw(self):
        """Test get_obi_column_name with prefer_raw=False."""
        result = get_obi_column_name(10, prefer_raw=False)
        assert result == ['obi_10', 'raw_obi_10']

    def test_get_obi_column_name_default_prefer_raw(self):
        """Test get_obi_column_name default behavior."""
        result = get_obi_column_name(20)
        assert result == ['raw_obi_20', 'obi_20']

    def test_obi_col_with_different_depths(self):
        """Test helper with various depth levels."""
        # Test depth 5
        columns_5 = ['timestamp', 'raw_obi_5', 'price']
        assert obi_col(columns_5, 5) == 'raw_obi_5'
        
        # Test depth 10
        columns_10 = ['timestamp', 'obi_10', 'price']
        assert obi_col(columns_10, 10) == 'obi_10'
        
        # Test depth 20
        columns_20 = ['timestamp', 'raw_obi_20', 'price']
        assert obi_col(columns_20, 20) == 'raw_obi_20'

    def test_obi_col_case_sensitivity(self):
        """Test that helper is case sensitive."""
        columns = ['timestamp', 'RAW_OBI_5', 'price']  # Wrong case
        with pytest.raises(KeyError):
            obi_col(columns, 5)

    def test_obi_col_with_similar_but_wrong_columns(self):
        """Test that helper doesn't match similar but incorrect column names."""
        columns = ['timestamp', 'raw_obi_5_extra', 'obi_5_suffix', 'price']
        with pytest.raises(KeyError):
            obi_col(columns, 5)
