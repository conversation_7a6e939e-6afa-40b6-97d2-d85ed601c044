#!/usr/bin/env python
# tests/test_warmup_bars.py

"""
Unit tests for TF-v2 dynamic warmup bars configuration (Task R-111).
"""

import pytest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import Mock, patch

from hyperliquid_bot.config.settings import Config, StrategySettings
from hyperliquid_bot.backtester.backtester import Backtester


class TestTFV2WarmupBars:
    """Test TF-v2 warmup bars configuration and calculation."""

    def test_strategy_settings_default_auto(self):
        """Test that StrategySettings defaults to 'auto' warmup."""
        settings = StrategySettings()
        assert settings.tf_warmup_bars == "auto"

    def test_strategy_settings_manual_warmup(self):
        """Test that StrategySettings accepts manual integer warmup."""
        settings = StrategySettings(tf_warmup_bars=50)
        assert settings.tf_warmup_bars == 50

    def test_strategy_settings_validation_positive_integer(self):
        """Test that StrategySettings validates positive integers."""
        with pytest.raises(ValueError, match="tf_warmup_bars must be positive integer"):
            StrategySettings(tf_warmup_bars=0)

        with pytest.raises(ValueError, match="tf_warmup_bars must be positive integer"):
            StrategySettings(tf_warmup_bars=-10)

    def test_strategy_settings_validation_auto_literal(self):
        """Test that StrategySettings accepts 'auto' literal."""
        settings = StrategySettings(tf_warmup_bars="auto")
        assert settings.tf_warmup_bars == "auto"

    def test_config_includes_tf_warmup_bars_setting(self):
        """Test that Config includes tf_warmup_bars field with proper defaults."""
        # Create minimal config YAML
        config_data = {
            'is_backtest': True,
            'data_paths': {
                'l2_data_root': '/tmp',
                'ohlcv_base_path': '/tmp',
                'raw_l2_dir': '/tmp',
                'feature_1s_dir': '/tmp',
                'log_dir': '/tmp'
            },
            'cache': {'l2_cache_max_size': 100},
            'costs': {'taker_fee': 0.001, 'maker_fee': 0.001, 'l2_penalty_factor': 1.001},
            'regime': {'detector_type': 'continuous_gms'},
            'microstructure': {'depth_levels': 5},
            'indicators': {'adx_period': 14},
            'analysis': {'analyze_trades_after_backtest': True},
            'visualization': {'plot_enabled': False},
            'data_providers': {'fear_greed': {'enabled': False}},
            'timeframe': '1h',
            'gms': {'detector_type': 'continuous_gms'},
            'strategies': {'tf_warmup_bars': 'auto'},  # Test default
            'tf_v3': {'enabled': True},
            'etl': {'l20_to_1s': {'chunk_sec': 3600}},
            'scheduler': {'etl_enabled': False}
        }

        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name

        try:
            config = Config.from_yaml(config_path)
            assert hasattr(config.strategies, 'tf_warmup_bars')
            assert config.strategies.tf_warmup_bars == "auto"
        finally:
            Path(config_path).unlink()

    def test_config_tf_warmup_bars_manual_override(self):
        """Test that Config properly loads manual warmup override."""
        config_data = {
            'is_backtest': True,
            'data_paths': {
                'l2_data_root': '/tmp',
                'ohlcv_base_path': '/tmp',
                'raw_l2_dir': '/tmp',
                'feature_1s_dir': '/tmp',
                'log_dir': '/tmp'
            },
            'cache': {'l2_cache_max_size': 100},
            'costs': {'taker_fee': 0.001, 'maker_fee': 0.001, 'l2_penalty_factor': 1.001},
            'regime': {'detector_type': 'continuous_gms'},
            'microstructure': {'depth_levels': 5},
            'indicators': {'adx_period': 14},
            'analysis': {'analyze_trades_after_backtest': True},
            'visualization': {'plot_enabled': False},
            'data_providers': {'fear_greed': {'enabled': False}},
            'timeframe': '1h',
            'gms': {'detector_type': 'continuous_gms'},
            'strategies': {'tf_warmup_bars': 75},  # Manual override
            'tf_v3': {'enabled': True},
            'etl': {'l20_to_1s': {'chunk_sec': 3600}},
            'scheduler': {'etl_enabled': False}
        }

        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name

        try:
            config = Config.from_yaml(config_path)
            assert config.strategies.tf_warmup_bars == 75
        finally:
            Path(config_path).unlink()

    @patch('hyperliquid_bot.backtester.backtester.DataHandler')
    @patch('hyperliquid_bot.backtester.backtester.SignalEngine')
    @patch('hyperliquid_bot.backtester.backtester.StrategyEvaluator')
    @patch('hyperliquid_bot.backtester.backtester.RiskManager')
    @patch('hyperliquid_bot.backtester.backtester.ExecutionSimulator')
    @patch('hyperliquid_bot.backtester.backtester.Portfolio')
    @patch('hyperliquid_bot.backtester.backtester.create_regime_detector')
    def test_backtester_auto_warmup_calculation(self, mock_detector, mock_portfolio,
                                               mock_exec_sim, mock_risk, mock_strat_eval,
                                               mock_signal_engine, mock_data_handler):
        """Test that backtester calculates auto warmup correctly for TF-v2."""
        # Mock config with TF-v2 enabled and auto warmup
        mock_config = Mock()
        mock_config.strategies.use_tf_v2 = True
        mock_config.strategies.tf_warmup_bars = "auto"
        mock_config.indicators.tf_ewma_slow = 50
        mock_config.indicators.tf_atr_period = 20
        mock_config.regime.detector_type = 'granular_microstructure'
        mock_config.data_paths.log_dir = '/tmp'

        # Mock signal engine
        mock_signal_instance = Mock()
        mock_signal_instance.calculate_required_lookback.return_value = 100
        mock_signal_engine.return_value = mock_signal_instance

        # Create backtester
        backtester = Backtester(mock_config)

        # Test auto warmup calculation
        warmup = backtester._calculate_required_warmup()

        # Should be max(50, 20, 120) = 120
        expected_warmup = max(50, 20, 120)
        assert warmup == expected_warmup

    @patch('hyperliquid_bot.backtester.backtester.DataHandler')
    @patch('hyperliquid_bot.backtester.backtester.SignalEngine')
    @patch('hyperliquid_bot.backtester.backtester.StrategyEvaluator')
    @patch('hyperliquid_bot.backtester.backtester.RiskManager')
    @patch('hyperliquid_bot.backtester.backtester.ExecutionSimulator')
    @patch('hyperliquid_bot.backtester.backtester.Portfolio')
    @patch('hyperliquid_bot.backtester.backtester.create_regime_detector')
    def test_backtester_manual_warmup_override(self, mock_detector, mock_portfolio,
                                              mock_exec_sim, mock_risk, mock_strat_eval,
                                              mock_signal_engine, mock_data_handler):
        """Test that backtester uses manual warmup override for TF-v2."""
        # Mock config with TF-v2 enabled and manual warmup
        mock_config = Mock()
        mock_config.strategies.use_tf_v2 = True
        mock_config.strategies.tf_warmup_bars = 75
        mock_config.data_paths.log_dir = '/tmp'

        # Mock signal engine
        mock_signal_instance = Mock()
        mock_signal_instance.calculate_required_lookback.return_value = 100
        mock_signal_engine.return_value = mock_signal_instance

        # Create backtester
        backtester = Backtester(mock_config)

        # Test manual warmup
        warmup = backtester._calculate_required_warmup()

        # Should use manual value
        assert warmup == 75

    @patch('hyperliquid_bot.backtester.backtester.DataHandler')
    @patch('hyperliquid_bot.backtester.backtester.SignalEngine')
    @patch('hyperliquid_bot.backtester.backtester.StrategyEvaluator')
    @patch('hyperliquid_bot.backtester.backtester.RiskManager')
    @patch('hyperliquid_bot.backtester.backtester.ExecutionSimulator')
    @patch('hyperliquid_bot.backtester.backtester.Portfolio')
    @patch('hyperliquid_bot.backtester.backtester.create_regime_detector')
    def test_backtester_fallback_for_non_tf_strategies(self, mock_detector, mock_portfolio,
                                                      mock_exec_sim, mock_risk, mock_strat_eval,
                                                      mock_signal_engine, mock_data_handler):
        """Test that backtester falls back to original logic for non-TF strategies."""
        # Mock config with TF-v2 disabled
        mock_config = Mock()
        mock_config.strategies.use_tf_v2 = False
        mock_config.regime.detector_type = 'continuous_gms'
        mock_config.data_paths.log_dir = '/tmp'

        # Mock signal engine
        mock_signal_instance = Mock()
        mock_signal_instance.calculate_required_lookback.return_value = 100
        mock_signal_engine.return_value = mock_signal_instance

        # Create backtester
        backtester = Backtester(mock_config)

        # Test fallback warmup calculation
        warmup = backtester._calculate_required_warmup()

        # Should use original capped logic: min(100 + 5, 10) = 10
        assert warmup == 10
