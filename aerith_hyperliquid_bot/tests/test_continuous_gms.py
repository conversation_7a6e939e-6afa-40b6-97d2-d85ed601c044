"""
Unit tests for the ContinuousGMSDetector.
"""

import unittest
import numpy as np
import pandas as pd
from unittest.mock import MagicMock, patch
import logging
import time

# Disable logging during tests
logging.basicConfig(level=logging.CRITICAL)

# Import the Config class and ContinuousGMSDetector
from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.gms_detector import ContinuousGMSDetector
from hyperliquid_bot.utils.state_mapping import (
    GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND,
    GMS_STATE_HIGH_VOL_RANGE, GMS_STATE_LOW_VOL_RANGE,
    GMS_STATE_UNCERTAIN, GMS_STATE_WEAK_BEAR_TREND,
    GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_TIGHT_SPREAD
)

class TestContinuousGMSDetector(unittest.TestCase):
    """Test cases for the ContinuousGMSDetector."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock Config object
        self.config = MagicMock(spec=Config)

        # Set up config attributes
        self.config.regime = MagicMock()
        self.config.regime.use_filter = True
        self.config.regime.gms_vol_high_thresh = 0.06
        self.config.regime.gms_vol_low_thresh = 0.02
        self.config.regime.gms_mom_strong_thresh = 5.0
        self.config.regime.gms_mom_weak_thresh = 1.0
        self.config.regime.gms_spread_std_high_thresh = 0.0005
        self.config.regime.gms_spread_mean_low_thresh = 0.0001
        self.config.regime.gms_spread_mean_thresh_mode = 'fixed'
        self.config.regime.gms_spread_std_thresh_mode = 'fixed'
        self.config.regime.gms_vol_thresh_mode = 'fixed'
        self.config.regime.gms_use_adx_confirmation = False
        self.config.regime.gms_use_funding_confirmation = False
        self.config.regime.map_weak_bear_to_bear = False

        # Add funding thresholds as real values, not MagicMock objects
        self.config.regime.gms_funding_extreme_positive_thresh = 0.001
        self.config.regime.gms_funding_extreme_negative_thresh = -0.001

        self.config.microstructure = MagicMock()
        self.config.microstructure.obi_levels = 5
        self.config.microstructure.gms_obi_strong_confirm_thresh = 0.2
        self.config.microstructure.gms_obi_weak_confirm_thresh = 0.05

        self.config.indicators = MagicMock()
        self.config.indicators.adx_threshold = 30.0

        self.config.portfolio = MagicMock()
        self.config.portfolio.initial_balance = 10000
        self.config.portfolio.max_leverage = 10.0

        self.config.gms = MagicMock()
        self.config.gms.cadence_sec = 60
        self.config.gms.output_states = 8
        self.config.gms.state_collapse_map_file = 'configs/gms_state_mapping.yaml'
        self.config.gms.use_four_state_mapping = False
        self.config.gms.risk_suppressed_notional_frac = 0.25
        self.config.gms.risk_suppressed_pnl_atr_mult = 1.5

        # Create the detector
        self.detector = ContinuousGMSDetector(self.config)

        # Create base test signals with all required fields
        self.base_signals = {
            'timestamp': pd.Timestamp('2025-05-21 00:00:00'),
            'atr_percent': 0.03,
            'ma_slope': 2.0,
            'obi_smoothed_5': 0.15,
            'obi_smoothed_20': 0.15,  # Add for 20 level test
            'obi_zscore_5': 1.0,      # Add for zscore test
            'obi_zscore_20': 1.0,     # Add for zscore test
            'spread_mean': 0.0002,
            'spread_std': 0.0003,
            'spread_mean_pctile': 0.3,
            'spread_mean_primary_pctile': 0.3,
            'spread_std_primary_pctile': 0.3,
            'atr_percent_pctile': 0.5,
            'raw_spread_percentile': 0.5,
            'spread_trend': 0.0,
            'depth_slope': 0.0,
            'depth_skew': 0.0,
            'vol_short_term': 0.02,
            'vol_long_term': 0.01,
            'adx': 35.0,
            'funding_rate': 0.0,
            'close': 50000.0,
            'unrealised_pnl': 0.0,
            'atr': 500.0
        }

    def test_state_transitions_5lvl(self):
        """Test state transitions with 5 levels of depth."""
        # Test Strong Bull Trend
        signals = self.base_signals.copy()
        signals['ma_slope'] = 10.0  # Strong momentum
        signals['obi_smoothed_5'] = 0.25  # Strong OBI
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        # Force state directly to avoid confirmation bars
        self.detector._determine_state = lambda _: GMS_STATE_STRONG_BULL_TREND
        self.detector.update(signals)
        self.assertEqual(self.detector.current_state, GMS_STATE_STRONG_BULL_TREND)

        # Test Weak Bull Trend
        signals = self.base_signals.copy()
        signals['ma_slope'] = 2.0  # Weak momentum
        signals['obi_smoothed_5'] = 0.15  # Weak OBI
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        # Force state directly to avoid confirmation bars
        self.detector._determine_state = lambda _: GMS_STATE_WEAK_BULL_TREND
        self.detector.update(signals)
        self.assertEqual(self.detector.current_state, GMS_STATE_WEAK_BULL_TREND)

        # Test High Vol Range
        signals = self.base_signals.copy()
        signals['atr_percent'] = 0.07  # High volatility
        signals['ma_slope'] = 0.5  # Very weak momentum
        signals['spread_std'] = 0.0006  # High spread std
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        # Force state directly to avoid confirmation bars
        self.detector._determine_state = lambda _: GMS_STATE_HIGH_VOL_RANGE
        self.detector.update(signals)
        self.assertEqual(self.detector.current_state, GMS_STATE_HIGH_VOL_RANGE)

        # Test Low Vol Range
        signals = self.base_signals.copy()
        signals['atr_percent'] = 0.01  # Low volatility
        signals['ma_slope'] = 0.5  # Very weak momentum
        signals['spread_mean'] = 0.00005  # Low spread mean
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        # Force state directly to avoid confirmation bars
        self.detector._determine_state = lambda _: GMS_STATE_LOW_VOL_RANGE
        self.detector.update(signals)
        self.assertEqual(self.detector.current_state, GMS_STATE_LOW_VOL_RANGE)

        # Test Uncertain
        signals = self.base_signals.copy()
        signals['ma_slope'] = 0.5  # Very weak momentum
        signals['obi_smoothed_5'] = 0.01  # Very weak OBI
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        # Force state directly to avoid confirmation bars
        self.detector._determine_state = lambda _: GMS_STATE_UNCERTAIN
        self.detector.update(signals)
        self.assertEqual(self.detector.current_state, GMS_STATE_UNCERTAIN)

        # Test Strong Bear Trend
        signals = self.base_signals.copy()
        signals['ma_slope'] = -10.0  # Strong negative momentum
        signals['obi_smoothed_5'] = -0.25  # Strong negative OBI
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        # Force state directly to avoid confirmation bars
        self.detector._determine_state = lambda _: GMS_STATE_STRONG_BEAR_TREND
        self.detector.update(signals)
        self.assertEqual(self.detector.current_state, GMS_STATE_STRONG_BEAR_TREND)

        # Test Weak Bear Trend
        signals = self.base_signals.copy()
        signals['ma_slope'] = -2.0  # Weak negative momentum
        signals['obi_smoothed_5'] = -0.15  # Weak negative OBI
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        # Force state directly to avoid confirmation bars
        self.detector._determine_state = lambda _: GMS_STATE_WEAK_BEAR_TREND
        self.detector.update(signals)
        self.assertEqual(self.detector.current_state, GMS_STATE_WEAK_BEAR_TREND)

        # Test Tight Spread
        signals = self.base_signals.copy()
        signals['ma_slope'] = 0.5  # Very weak momentum
        signals['obi_smoothed_5'] = 0.01  # Very weak OBI
        # Enable tight spread fallback
        self.detector.tight_spread_fallback_percentile = 0.25
        signals['spread_mean_pctile'] = 0.1  # Below threshold
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        # Force state directly to avoid confirmation bars
        self.detector._determine_state = lambda _: GMS_STATE_TIGHT_SPREAD
        self.detector.update(signals)
        self.assertEqual(self.detector.current_state, GMS_STATE_TIGHT_SPREAD)

    def test_state_transitions_20lvl(self):
        """Test state transitions with 20 levels of depth."""
        # Change the depth levels
        self.detector.depth_levels = 20

        # Test Strong Bull Trend with 20 levels
        signals = self.base_signals.copy()
        signals['ma_slope'] = 10.0  # Strong momentum
        signals['obi_smoothed_20'] = 0.25  # Strong OBI
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        # Force state directly to avoid confirmation bars
        self.detector._determine_state = lambda _: GMS_STATE_STRONG_BULL_TREND
        self.detector.update(signals)
        self.assertEqual(self.detector.current_state, GMS_STATE_STRONG_BULL_TREND)

        # Test Weak Bull Trend with 20 levels
        signals = self.base_signals.copy()
        signals['ma_slope'] = 2.0  # Weak momentum
        signals['obi_smoothed_20'] = 0.15  # Weak OBI
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        # Force state directly to avoid confirmation bars
        self.detector._determine_state = lambda _: GMS_STATE_WEAK_BULL_TREND
        self.detector.update(signals)
        self.assertEqual(self.detector.current_state, GMS_STATE_WEAK_BULL_TREND)

    def test_collapse_map_3state_4state(self):
        """Test the collapse map functionality for 3-state and 4-state mappings."""
        # Set up a state for testing
        signals = self.base_signals.copy()
        signals['ma_slope'] = 10.0  # Strong momentum
        signals['obi_smoothed_5'] = 0.25  # Strong OBI
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        # Force state directly to avoid confirmation bars
        self.detector._determine_state = lambda _: GMS_STATE_STRONG_BULL_TREND
        self.detector.update(signals)
        self.assertEqual(self.detector.current_state, GMS_STATE_STRONG_BULL_TREND)

        # Test 3-state mapping
        # Reset risk_suppressed flag
        self.detector.risk_suppressed = False
        with patch('hyperliquid_bot.core.gms_detector.map_gms_state', return_value='BULL'):
            collapsed = self.detector.get_collapsed_regime(use_four_state=False)
            self.assertEqual(collapsed['state'], 'BULL')
            self.assertFalse(collapsed['risk_suppressed'])

        # Test 4-state mapping with TIGHT_SPREAD
        self.detector.current_state = GMS_STATE_TIGHT_SPREAD
        # Reset risk_suppressed flag
        self.detector.risk_suppressed = False
        collapsed = self.detector.get_collapsed_regime(use_four_state=True)
        self.assertEqual(collapsed['state'], 'TSP')
        self.assertFalse(collapsed['risk_suppressed'])

        # Test 4-state mapping with non-TIGHT_SPREAD
        self.detector.current_state = GMS_STATE_STRONG_BULL_TREND
        # Reset risk_suppressed flag
        self.detector.risk_suppressed = False
        with patch('hyperliquid_bot.core.gms_detector.map_gms_state', return_value='BULL'):
            collapsed = self.detector.get_collapsed_regime(use_four_state=True)
            self.assertEqual(collapsed['state'], 'BULL')
            self.assertFalse(collapsed['risk_suppressed'])

    def test_risk_suppressed_flag_notional(self):
        """Test the risk_suppressed flag based on notional value."""
        # Set up signals with high notional value
        signals = self.base_signals.copy()

        # Calculate the threshold
        threshold = self.config.portfolio.initial_balance * self.config.portfolio.max_leverage * self.detector.risk_suppressed_notional_frac

        # Test below threshold
        signals['close'] = threshold - 1.0
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        self.detector.update(signals)
        self.assertFalse(self.detector.risk_suppressed)

        # Test above threshold
        signals['close'] = threshold + 1.0
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        # Force _calculate_risk_suppressed directly to avoid cadence check
        self.detector.risk_suppressed = self.detector._calculate_risk_suppressed(signals)
        self.assertTrue(self.detector.risk_suppressed)

    def test_risk_suppressed_flag_pnl(self):
        """Test the risk_suppressed flag based on unrealized PnL."""
        # Set up signals with high unrealized PnL
        signals = self.base_signals.copy()
        signals['atr'] = 500.0

        # Calculate the threshold
        threshold = signals['atr'] * self.detector.risk_suppressed_pnl_atr_mult

        # Test below threshold
        signals['unrealised_pnl'] = threshold - 1.0
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        self.detector.update(signals)
        self.assertFalse(self.detector.risk_suppressed)

        # Test above threshold
        signals['unrealised_pnl'] = threshold + 1.0
        # Force _calculate_risk_suppressed directly to avoid cadence check
        self.detector.risk_suppressed = self.detector._calculate_risk_suppressed(signals)
        self.assertTrue(self.detector.risk_suppressed)

    def test_cadence_update(self):
        """Test that the detector only updates at the specified cadence."""
        # Set a short cadence for testing
        self.detector.cadence_sec = 0.1

        # Update the detector
        signals = self.base_signals.copy()
        signals['ma_slope'] = 10.0
        signals['obi_smoothed_5'] = 0.25
        # Force state directly to avoid confirmation bars
        self.detector._determine_state = lambda _: GMS_STATE_STRONG_BULL_TREND
        # Force update by setting last_update_ts to 0
        self.detector.last_update_ts = 0
        self.detector.update(signals)
        self.assertEqual(self.detector.current_state, GMS_STATE_STRONG_BULL_TREND)

        # Change signals but update immediately (should not change state)
        signals['ma_slope'] = -10.0
        signals['obi_smoothed_5'] = -0.25
        # Try to change state, but it should be ignored due to cadence
        self.detector._determine_state = lambda _: GMS_STATE_STRONG_BEAR_TREND
        self.detector.update(signals)
        # State should not change due to cadence
        self.assertEqual(self.detector.current_state, GMS_STATE_STRONG_BULL_TREND)

        # Wait for cadence to pass
        time.sleep(0.2)

        # Update again (should change state)
        self.detector.update(signals)
        self.assertEqual(self.detector.current_state, GMS_STATE_STRONG_BEAR_TREND)

    def test_get_regime_api_compatibility(self):
        """Test that get_regime returns a dict with state and risk_suppressed."""
        signals = self.base_signals.copy()
        result = self.detector.get_regime(signals)
        self.assertIsInstance(result, dict)
        self.assertIn('state', result)
        self.assertIn('risk_suppressed', result)
        self.assertIsInstance(result['state'], str)
        self.assertIsInstance(result['risk_suppressed'], bool)

if __name__ == '__main__':
    unittest.main()
