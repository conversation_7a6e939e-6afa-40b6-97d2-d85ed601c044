#!/usr/bin/env python
"""
Integration test for adaptive threshold priming functionality (Task R-112m).

Tests the priming functionality with a real configuration and synthetic data.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import shutil
import sys

# Add project root to path for imports
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.core.gms_detector import ContinuousGMSDetector
from hyperliquid_bot.backtester.run_backtest import determine_backtest_dates


class TestPrimingIntegration:
    """Integration test for priming functionality."""

    @pytest.fixture
    def temp_feature_dir(self):
        """Create a temporary directory with synthetic feature files."""
        temp_dir = tempfile.mkdtemp()
        feature_dir = Path(temp_dir) / "features_1s"
        feature_dir.mkdir(parents=True)

        # Create synthetic feature data for 2 days
        base_date = datetime(2025, 3, 1)

        for day_offset in [-1, 0]:  # Day before and start day
            date = base_date + timedelta(days=day_offset)
            date_str = date.strftime('%Y-%m-%d')
            date_dir = feature_dir / date_str
            date_dir.mkdir()

            # Create hourly files for first few hours only (to speed up test)
            for hour in range(3):  # Just first 3 hours
                # Generate synthetic data for this hour
                timestamps = pd.date_range(
                    start=f"{date_str} {hour:02d}:00:00",
                    end=f"{date_str} {hour:02d}:59:59",
                    freq="1s"
                )

                # Create realistic synthetic data
                n_samples = len(timestamps)
                data = {
                    'timestamp': timestamps,
                    'atr_percent_sec': np.random.uniform(0.001, 0.05, n_samples),
                    'ma_slope': np.random.uniform(-10, 10, n_samples),
                    'spread_mean': np.random.uniform(0.0001, 0.001, n_samples)
                }

                df = pd.DataFrame(data)
                hour_file = date_dir / f"features_{hour:02d}.parquet"
                df.to_parquet(hour_file)

        yield feature_dir

        # Cleanup
        shutil.rmtree(temp_dir)

    def test_priming_with_real_config(self, temp_feature_dir):
        """Test priming with a real configuration."""
        # Load the base config
        config_path = project_root / "configs" / "base.yaml"
        config = load_config(str(config_path))

        # Modify config for testing
        config.data_paths.feature_1s_dir = str(temp_feature_dir)

        # Set backtest dates
        config.backtest.period_preset = 'custom'
        config.backtest.custom_start_date = datetime(2025, 3, 1, 0, 0, 0)
        config.backtest.custom_end_date = datetime(2025, 3, 2, 0, 0, 0)

        # Enable adaptive thresholds and priming
        config.gms.auto_thresholds = True
        config.gms.priming_hours = 2  # Prime with 2 hours of data
        config.gms.percentile_window_sec = 3600  # 1 hour window for testing
        config.gms.min_history_rows = 10  # Lower threshold for testing

        # Debug: Check what files were created
        print(f"Feature directory: {temp_feature_dir}")
        for date_dir in temp_feature_dir.iterdir():
            if date_dir.is_dir():
                print(f"Date directory: {date_dir}")
                for file in date_dir.iterdir():
                    print(f"  File: {file}")

        # Create detector
        detector = ContinuousGMSDetector(config)

        # Check that adaptive thresholds were initialized
        assert detector.adaptive_vol_threshold is not None
        assert detector.adaptive_mom_threshold is not None

        # Check that buffers have been populated
        vol_stats = detector.adaptive_vol_threshold.get_buffer_stats()
        mom_stats = detector.adaptive_mom_threshold.get_buffer_stats()

        # Should have some data in buffers
        print(f"Vol buffer size: {vol_stats['size']}")
        print(f"Mom buffer size: {mom_stats['size']}")

        if vol_stats['size'] > 0:
            assert mom_stats['size'] > 0

            # Should have valid statistics
            assert vol_stats['min'] is not None
            assert vol_stats['max'] is not None
            assert mom_stats['min'] is not None
            assert mom_stats['max'] is not None

            print(f"Vol buffer: {vol_stats['size']} samples, range: {vol_stats['min']:.6f} - {vol_stats['max']:.6f}")
            print(f"Mom buffer: {mom_stats['size']} samples, range: {mom_stats['min']:.6f} - {mom_stats['max']:.6f}")
        else:
            print("No data found in buffers - this might be expected if no historical data exists")

    def test_priming_disabled(self, temp_feature_dir):
        """Test that priming is disabled when priming_hours is 0."""
        # Load the base config
        config_path = project_root / "configs" / "base.yaml"
        config = load_config(str(config_path))

        # Modify config for testing
        config.data_paths.feature_1s_dir = str(temp_feature_dir)

        # Set backtest dates
        config.backtest.period_preset = 'custom'
        config.backtest.custom_start_date = datetime(2025, 3, 1, 0, 0, 0)
        config.backtest.custom_end_date = datetime(2025, 3, 2, 0, 0, 0)

        # Enable adaptive thresholds but disable priming
        config.gms.auto_thresholds = True
        config.gms.priming_hours = 0  # Disabled
        config.gms.percentile_window_sec = 3600
        config.gms.min_history_rows = 10

        # Create detector
        detector = ContinuousGMSDetector(config)

        # Check that adaptive thresholds were initialized
        assert detector.adaptive_vol_threshold is not None
        assert detector.adaptive_mom_threshold is not None

        # Check that buffers are empty (no priming)
        vol_stats = detector.adaptive_vol_threshold.get_buffer_stats()
        mom_stats = detector.adaptive_mom_threshold.get_buffer_stats()

        # Should have no data in buffers
        assert vol_stats['size'] == 0
        assert mom_stats['size'] == 0

    def test_priming_with_missing_data(self):
        """Test priming behavior when historical data is missing."""
        # Load the base config
        config_path = project_root / "configs" / "base.yaml"
        config = load_config(str(config_path))

        # Point to non-existent directory
        config.data_paths.feature_1s_dir = "/non/existent/path"

        # Set backtest dates
        config.backtest.period_preset = 'custom'
        config.backtest.custom_start_date = datetime(2025, 3, 1, 0, 0, 0)
        config.backtest.custom_end_date = datetime(2025, 3, 2, 0, 0, 0)

        # Enable adaptive thresholds and priming
        config.gms.auto_thresholds = True
        config.gms.priming_hours = 24  # Try to prime with 24 hours
        config.gms.percentile_window_sec = 3600
        config.gms.min_history_rows = 10

        # Should not crash, just log warning and continue
        detector = ContinuousGMSDetector(config)

        # Adaptive thresholds should still be initialized but empty
        assert detector.adaptive_vol_threshold is not None
        assert detector.adaptive_mom_threshold is not None

        # Buffers should be empty
        vol_stats = detector.adaptive_vol_threshold.get_buffer_stats()
        mom_stats = detector.adaptive_mom_threshold.get_buffer_stats()

        assert vol_stats['size'] == 0
        assert mom_stats['size'] == 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
