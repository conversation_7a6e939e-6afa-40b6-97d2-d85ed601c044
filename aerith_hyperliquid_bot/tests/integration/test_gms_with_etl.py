#!/usr/bin/env python
# tests/integration/test_gms_with_etl.py

"""
Integration test for ContinuousGMSDetector with ETL pipeline.

This test verifies that the ContinuousGMSDetector can process 1-second feature data
produced by the ETL pipeline and output non-"Unknown" regimes.

The test:
1. Copies sample raw arrow chunk (13:00-15:59) into a temporary directory
2. Launches the ETL scheduler in a background thread
3. Instantiates DataHandler and ContinuousGMSDetector
4. Streams the produced 1-second parquet rows for 3 hours and feeds them into the detector
5. Asserts that the detector's current_state exits "Unknown" at least 5 times
"""

import os
import sys
import tempfile
import shutil
import threading
import asyncio
import time
from pathlib import Path
import pytest
import logging
import pandas as pd
import numpy as np
from typing import Optional

# Add project root to path for imports
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.core.gms_detector import ContinuousGMSDetector
from hyperliquid_bot.data.handler import DataHandlerInterface
from hyperliquid_bot.signals.calculator import SignalEngine
from hyperliquid_bot.utils.feature_naming import get_obi_column_name
from services.etl_scheduler import ETLScheduler
from tools.etl_l20_to_1s import process_hour

# A mock DataHandler for testing purposes
class MockDataHandler(DataHandlerInterface):
    def __init__(self, config, data_df=None):
        super().__init__(config)
        self.data_df = data_df if data_df is not None else pd.DataFrame()

    def load_historical_data(self, start_date, end_date) -> None:
        """Mock implementation"""
        pass

    def get_ohlcv_data(self) -> pd.DataFrame:
        """Returns the mock data"""
        return self.data_df.copy()

    def get_funding_rate(self, timestamp) -> float:
        """Mock implementation"""
        return 0.0

    def get_open_interest(self, timestamp) -> float:
        """Mock implementation"""
        return 1000.0

    def get_recent_close_prices(self, lookback_bars=10) -> pd.Series:
        """Mock implementation"""
        if self.data_df.empty:
            return pd.Series()
        if 'close' in self.data_df.columns:
            return self.data_df['close'].tail(lookback_bars)
        return pd.Series([50000.0] * min(lookback_bars, len(self.data_df)))

    def get_recent_liquidations(self, lookback_bars=10) -> pd.DataFrame:
        """Mock implementation"""
        return pd.DataFrame(columns=['timestamp', 'side', 'quantity', 'price'])

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_gms_with_etl")

# Mark this test as an integration test that might be skipped in CI
pytestmark = pytest.mark.integration

# Fixture for temporary test directories
@pytest.fixture
def temp_dirs():
    """Create temporary directories for test data."""
    temp_dir = tempfile.mkdtemp()
    raw_dir = os.path.join(temp_dir, "l2_raw")
    out_dir = os.path.join(temp_dir, "features_1s")
    os.makedirs(raw_dir, exist_ok=True)
    os.makedirs(out_dir, exist_ok=True)

    yield {
        "temp_dir": temp_dir,
        "raw_dir": raw_dir,
        "out_dir": out_dir
    }

    # Clean up
    shutil.rmtree(temp_dir)

# Fixture for sample L2 data
@pytest.fixture
def sample_l2_data(temp_dirs):
    """Create sample L2 data for testing."""
    # Create a test fixture with 100 snapshots, depth 20
    raw_dir = temp_dirs["raw_dir"]

    # Create sample data for 3 hours (13:00-15:59)
    hours = [13, 14, 15]
    date_str = "20250525"  # May 25, 2025

    for hour in hours:
        # Create a sample L2 data file for each hour
        file_path = os.path.join(raw_dir, f"lob_{date_str}_{hour:02d}.arrow")

        # Create sample data
        timestamps = pd.date_range(
            start=f"2025-05-25 {hour:02d}:00:00",
            end=f"2025-05-25 {hour:02d}:59:59",
            freq="1s"
        )

        # Create a DataFrame with L2 data
        records = []
        for ts in timestamps:
            # Create a record with timestamp and bid/ask data
            record = {'timestamp': ts}

            # Add bid/ask data for 20 levels
            for i in range(1, 21):
                # Randomize prices and sizes a bit
                base_price = 50000.0  # Base BTC price
                price_noise = np.random.normal(0, 10.0)
                size_noise = np.random.normal(0, 0.5)

                # Bids decrease from base price
                record[f'bid_price_{i}'] = base_price - i * 10.0 + price_noise
                record[f'bid_size_{i}'] = 1.0 + i * 0.1 + size_noise

                # Asks increase from base price
                record[f'ask_price_{i}'] = base_price + i * 10.0 + price_noise
                record[f'ask_size_{i}'] = 1.0 + i * 0.1 + size_noise

            records.append(record)

        # Create DataFrame
        df = pd.DataFrame(records)

        # Save to arrow file
        df.to_feather(file_path)

        logger.info(f"Created sample L2 data file: {file_path}")

    return {
        "date_str": "2025-05-25",
        "hours": hours
    }

# Fixture for modified config
@pytest.fixture
def modified_config(temp_dirs):
    """Create a modified config for testing."""
    # Load the base config
    config_path = Path(project_root) / "configs" / "base.yaml"
    config = load_config(str(config_path))

    # Modify paths
    config.data_paths.raw_l2_dir = temp_dirs["raw_dir"]
    config.data_paths.feature_1s_dir = temp_dirs["out_dir"]

    # Set depth to 5
    config.microstructure.depth_levels = 5

    # Set detector type to continuous_gms
    config.gms.detector_type = 'continuous_gms'

    # Enable ETL scheduler
    config.scheduler.etl_enabled = True
    config.scheduler.etl_poll_sec = 10  # Poll every 10 seconds for faster testing

    return config

# Helper function to run ETL scheduler in a background thread
def run_etl_scheduler(config, stop_event):
    """Run the ETL scheduler in a background thread."""
    async def _run():
        # Create a scheduler with the provided config
        scheduler = ETLScheduler(str(Path(project_root) / "configs" / "base.yaml"))
        # Override settings with our test config
        scheduler.config = config
        scheduler.raw_l2_dir = Path(config.data_paths.raw_l2_dir)
        scheduler.feature_1s_dir = Path(config.data_paths.feature_1s_dir)
        scheduler.depth = config.microstructure.depth_levels
        scheduler.poll_sec = 10  # Poll every 10 seconds for faster testing

        while not stop_event.is_set():
            await scheduler.check_and_process_files()
            await asyncio.sleep(10)

    try:
        asyncio.run(_run())
    except Exception as e:
        logger.error(f"Error in ETL scheduler thread: {e}")
        # Don't propagate the exception as it would kill the thread

def test_gms_with_etl(temp_dirs, sample_l2_data, modified_config):
    """
    Test that ContinuousGMSDetector works with ETL pipeline.

    Steps:
    1. Copy sample raw arrow chunk (13:00-15:59) into tmp dir
    2. Launch ETL scheduler in a background thread
    3. Instantiate DataHandler -> ContinuousGMSDetector (depth=5)
    4. Stream the produced 1-s parquet rows for 3 h; feed into detector
    5. Assert detector.current_state exits "Unknown" at least 5 times
    """
    # Process the sample data directly first to ensure it's available
    date_str = sample_l2_data["date_str"]
    date_pattern = date_str.replace("-", "")

    for hour in sample_l2_data["hours"]:
        raw_file = os.path.join(temp_dirs["raw_dir"], f"lob_{date_pattern}_{hour:02d}.arrow")
        output_file = os.path.join(temp_dirs["out_dir"], f"features_{date_pattern}_{hour:02d}.parquet")

        # Process the hour
        success = process_hour(
            raw_file=raw_file,
            output_file=output_file,
            depth=modified_config.microstructure.depth_levels,
            rollup_method=modified_config.etl.l20_to_1s.rollup_method,
            force=True
        )

        assert success, f"Failed to process hour {hour}"

    # Start the ETL scheduler in a background thread
    stop_event = threading.Event()
    scheduler_thread = threading.Thread(
        target=run_etl_scheduler,
        args=(modified_config, stop_event)
    )
    scheduler_thread.daemon = True
    scheduler_thread.start()

    try:
        # Create a mock data handler
        mock_data_handler = MockDataHandler(modified_config)

        # Create a SignalEngine with the mock data handler
        signal_engine = SignalEngine(modified_config, mock_data_handler)

        # Create a ContinuousGMSDetector
        detector = ContinuousGMSDetector(modified_config)

        # Track non-Unknown states
        non_unknown_states = []

        # Process each hour of data
        for hour in sample_l2_data["hours"]:
            # Load the feature file
            feature_file = os.path.join(temp_dirs["out_dir"], f"features_{date_pattern}_{hour:02d}.parquet")

            # Wait for the file to be created if needed
            max_wait = 30  # seconds
            wait_time = 0
            while not os.path.exists(feature_file) and wait_time < max_wait:
                time.sleep(1)
                wait_time += 1

            assert os.path.exists(feature_file), f"Feature file {feature_file} not created within {max_wait} seconds"

            # Load the data
            df = pd.read_parquet(feature_file)

            # Create timestamps for each row
            timestamps = pd.date_range(
                start=f"2025-05-25 {hour:02d}:00:00",
                end=f"2025-05-25 {hour:02d}:59:59",
                freq="1s"
            )

            # Create signals directly for the detector
            for i, timestamp in enumerate(timestamps):
                # Create a signals dictionary with all required signals
                signals = {
                    'timestamp': timestamp,
                    'obi_smoothed_5': 0.2,  # Positive OBI (bullish)
                    'obi_zscore_5': 1.5,    # Significant deviation
                    'spread_percentile': 0.3,  # Low spread (good for entry)
                    'depth_imbalance': 0.4,  # Some imbalance
                    'unrealised_pnl': 0.0,  # No PnL
                    'funding_rate': 0.0001,  # Small positive funding
                    'atr_percent': 0.02,    # 2% ATR (moderate volatility)
                    'close': 50000.0 + i * 10.0,  # Increasing price
                    'volume': 100.0 + i * 1.0,    # Increasing volume

                    # Add missing signals from the warning
                    'ma_slope': 0.01,       # Positive slope (uptrend)
                    'spread_mean': 0.5,     # Average spread
                    'spread_std': 0.1,      # Spread standard deviation

                    # Add other potentially required signals
                    'ema_fast': 50000.0 + i * 12.0,  # Fast EMA increasing faster
                    'ema_slow': 50000.0 + i * 8.0,   # Slow EMA increasing slower
                    'adx': 25.0,            # ADX above 25 (trend strength)
                    'rsi': 55.0,            # RSI slightly bullish
                }

                # Add dynamic OBI column based on config
                obi_candidates = get_obi_column_name(modified_config.microstructure.depth_levels)
                signals[obi_candidates[0]] = 0.15  # Raw OBI (positive)

                # Every 10 seconds, make the signals more bullish to trigger non-Unknown states
                if i % 10 == 0:
                    signals['obi_smoothed_5'] = 0.5  # Strong positive OBI (very bullish)
                    signals['obi_zscore_5'] = 2.5    # Very significant deviation
                    signals['ma_slope'] = 0.03       # Steeper slope (stronger uptrend)
                    signals['adx'] = 35.0            # Stronger trend

                # Update detector
                detector.update(signals)

                # Check if state is not Unknown
                if detector.current_state != "Unknown":
                    non_unknown_states.append(detector.current_state)

        # Assert that we have at least 5 non-Unknown states
        assert len(non_unknown_states) >= 5, f"Expected at least 5 non-Unknown states, got {len(non_unknown_states)}"

        # Log the states we found
        logger.info(f"Found {len(non_unknown_states)} non-Unknown states: {non_unknown_states[:5]}...")

    finally:
        # Stop the scheduler
        stop_event.set()
        scheduler_thread.join(timeout=5)

if __name__ == "__main__":
    pytest.main(["-v", "test_gms_with_etl.py", "--run-integration"])
