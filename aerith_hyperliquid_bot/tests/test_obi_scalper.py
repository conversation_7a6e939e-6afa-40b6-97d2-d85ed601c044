# tests/test_obi_scalper.py

import unittest
from unittest.mock import MagicMock, patch
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import collections

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.strategies.obi_scalper_strategy import OBIScalperStrategy


class MockConfig:
    """Mock configuration for testing."""
    def __init__(self):
        self.strategies = MagicMock()
        self.strategies.OBIScalperStrategy = MagicMock()
        self.strategies.OBIScalperStrategy.defaults = MagicMock()

        # Set default values for OBIScalperStrategy
        self.strategies.OBIScalperStrategy.defaults.vol_veto_threshold = 0.0006
        self.strategies.OBIScalperStrategy.defaults.spread_veto_threshold = 0.00004
        self.strategies.OBIScalperStrategy.defaults.obi_l1_3_trigger = 0.80
        self.strategies.OBIScalperStrategy.defaults.tp_ticks = 7
        self.strategies.OBIScalperStrategy.defaults.sl_ticks = 5
        self.strategies.OBIScalperStrategy.defaults.timeout_seconds = 30
        self.strategies.OBIScalperStrategy.defaults.allowed_gms_states = ["CHOP", "TIGHT_SPREAD"]
        self.strategies.OBIScalperStrategy.defaults.zero_sign_eps = 0.001


class TestOBIScalperStrategy(unittest.TestCase):
    """Test cases for OBIScalperStrategy."""

    def setUp(self):
        """Set up test fixtures."""
        self.config = MockConfig()

        # Create a mock risk manager
        self.risk_manager = MagicMock()
        self.risk_manager.calculate_scalper_position_size = MagicMock(return_value=0.1)

        # Create the strategy instance
        self.strategy = OBIScalperStrategy(self.config, "obi_scalper")
        self.strategy.risk_manager = self.risk_manager
        self.strategy.tick_size = 0.01  # Set tick size for testing

        # Base signal data for testing
        self.base_signal_data = {
            "close": 50000.0,
            "best_bid": 49999.0,
            "best_ask": 50001.0,
            "regime": "CHOP",
            "realised_vol_1s": 0.0003,
            "spread_relative": 0.00002,
            "raw_obi_L1_3": 0.0,
            "raw_obi_L1_10": 0.0,
            "tick_size": 0.01
        }

        # Current timestamp for testing
        self.current_timestamp = datetime.now().timestamp()

    def test_safe_sign(self):
        """Test the _safe_sign method."""
        # Test positive values
        self.assertEqual(self.strategy._safe_sign(0.5), 1)
        self.assertEqual(self.strategy._safe_sign(0.1), 1)

        # Test negative values
        self.assertEqual(self.strategy._safe_sign(-0.5), -1)
        self.assertEqual(self.strategy._safe_sign(-0.1), -1)

        # Test values within epsilon
        self.assertEqual(self.strategy._safe_sign(0.0005), 0)
        self.assertEqual(self.strategy._safe_sign(-0.0005), 0)
        self.assertEqual(self.strategy._safe_sign(0.0), 0)

        # Test with custom epsilon
        self.assertEqual(self.strategy._safe_sign(0.05, eps=0.1), 0)
        self.assertEqual(self.strategy._safe_sign(0.15, eps=0.1), 1)

    def test_veto_logic(self):
        """Test that veto conditions prevent signal generation."""
        # Test volatility veto
        signal_data = self.base_signal_data.copy()
        signal_data["realised_vol_1s"] = 0.001  # Above threshold
        signals = self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)
        self.assertEqual(len(signals), 0)
        self.assertEqual(self.strategy.fail_vol_veto, 1)

        # Test spread veto
        signal_data = self.base_signal_data.copy()
        signal_data["spread_relative"] = 0.0001  # Above threshold
        signals = self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)
        self.assertEqual(len(signals), 0)
        self.assertEqual(self.strategy.fail_spread_veto, 1)

        # Test regime gate
        signal_data = self.base_signal_data.copy()
        signal_data["regime"] = "VOLATILE_WICK"  # Not in allowed states
        signals = self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)
        self.assertEqual(len(signals), 0)
        self.assertEqual(self.strategy.fail_regime_gate, 1)

    def test_sign_confirmation(self):
        """Test that sign confirmation is required for entry."""
        # Test sign mismatch (L1_3 positive, L1_10 negative)
        signal_data = self.base_signal_data.copy()
        signal_data["raw_obi_L1_3"] = 0.85  # Above threshold
        signal_data["raw_obi_L1_10"] = -0.05  # Opposite sign
        signals = self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)
        self.assertEqual(len(signals), 0)
        self.assertEqual(self.strategy.fail_sign_mismatch, 1)

        # Test sign mismatch (L1_3 negative, L1_10 positive)
        signal_data = self.base_signal_data.copy()
        signal_data["raw_obi_L1_3"] = -0.85  # Above threshold
        signal_data["raw_obi_L1_10"] = 0.05  # Opposite sign
        signals = self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)
        self.assertEqual(len(signals), 0)
        self.assertEqual(self.strategy.fail_sign_mismatch, 2)

        # Test sign match but L1_3 below threshold
        signal_data = self.base_signal_data.copy()
        signal_data["raw_obi_L1_3"] = 0.75  # Below threshold
        signal_data["raw_obi_L1_10"] = 0.05  # Same sign
        signals = self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)
        self.assertEqual(len(signals), 0)
        self.assertEqual(self.strategy.fail_obi_threshold, 1)

    def test_entry_long_short(self):
        """Test entry signal generation for long and short positions."""
        # Test long entry
        signal_data = self.base_signal_data.copy()
        signal_data["raw_obi_L1_3"] = 0.85  # Above threshold
        signal_data["raw_obi_L1_10"] = 0.05  # Same sign
        signals = self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)
        self.assertEqual(len(signals), 1)
        self.assertEqual(signals[0]["type"], "entry")
        self.assertEqual(signals[0]["side"], "LONG")
        self.assertEqual(self.strategy.success_entry_long, 1)

        # Reset strategy state
        self.strategy.active_trade_id = None
        self.strategy.entry_timestamp = None
        self.strategy.entry_price = None

        # Test short entry
        signal_data = self.base_signal_data.copy()
        signal_data["raw_obi_L1_3"] = -0.85  # Above threshold
        signal_data["raw_obi_L1_10"] = -0.05  # Same sign
        signals = self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)
        self.assertEqual(len(signals), 1)
        self.assertEqual(signals[0]["type"], "entry")
        self.assertEqual(signals[0]["side"], "SHORT")
        self.assertEqual(self.strategy.success_entry_short, 1)

    def test_tp_sl_timeout(self):
        """Test take profit, stop loss, and timeout exit conditions."""
        # Set up an active trade
        signal_data = self.base_signal_data.copy()
        signal_data["raw_obi_L1_3"] = 0.85
        signal_data["raw_obi_L1_10"] = 0.05
        signals = self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)
        self.assertEqual(len(signals), 1)

        # Store the active trade ID
        active_trade_id = self.strategy.active_trade_id
        self.assertIsNotNone(active_trade_id)

        # Test take profit exit
        signal_data = self.base_signal_data.copy()
        signal_data["close"] = self.strategy.entry_price + (self.strategy.tp_ticks * self.strategy.tick_size)
        signals = self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)
        self.assertEqual(len(signals), 1)
        self.assertEqual(signals[0]["type"], "exit")
        self.assertEqual(signals[0]["reason"], "TP")
        self.assertEqual(signals[0]["trade_id"], active_trade_id)
        self.assertEqual(self.strategy.exit_tp_count, 1)

        # Reset strategy state and set up another active trade
        self.strategy.active_trade_id = None
        self.strategy.entry_timestamp = None
        self.strategy.entry_price = None
        signal_data = self.base_signal_data.copy()
        signal_data["raw_obi_L1_3"] = 0.85
        signal_data["raw_obi_L1_10"] = 0.05
        signals = self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)
        active_trade_id = self.strategy.active_trade_id

        # Test stop loss exit
        signal_data = self.base_signal_data.copy()
        signal_data["close"] = self.strategy.entry_price - (self.strategy.sl_ticks * self.strategy.tick_size)
        signals = self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)
        self.assertEqual(len(signals), 1)
        self.assertEqual(signals[0]["type"], "exit")
        self.assertEqual(signals[0]["reason"], "SL")
        self.assertEqual(signals[0]["trade_id"], active_trade_id)
        self.assertEqual(self.strategy.exit_sl_count, 1)

        # Reset strategy state and set up another active trade
        self.strategy.active_trade_id = None
        self.strategy.entry_timestamp = None
        self.strategy.entry_price = None
        signal_data = self.base_signal_data.copy()
        signal_data["raw_obi_L1_3"] = 0.85
        signal_data["raw_obi_L1_10"] = 0.05
        signals = self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)
        active_trade_id = self.strategy.active_trade_id

        # Test timeout exit
        future_timestamp = self.current_timestamp + self.strategy.timeout_seconds + 1
        # Make sure we're not triggering SL by setting close price to entry price
        signal_data["close"] = self.strategy.entry_price
        signals = self.strategy.evaluate(signal_data, current_timestamp=future_timestamp)
        self.assertEqual(len(signals), 1)
        self.assertEqual(signals[0]["type"], "exit")
        self.assertEqual(signals[0]["reason"], "TIMEOUT")
        self.assertEqual(signals[0]["trade_id"], active_trade_id)
        self.assertEqual(self.strategy.exit_timeout_count, 1)

    def test_diag_counts(self):
        """Test diagnostic counters for T-106A."""
        # Reset the strategy to start with clean counters
        self.strategy = OBIScalperStrategy(self.config, "obi_scalper")
        self.strategy.risk_manager = self.risk_manager
        self.strategy.tick_size = 0.01

        # Create 20 synthetic bars with different conditions

        # 1. Five bars with mismatching regime (regime gate)
        for i in range(5):
            signal_data = self.base_signal_data.copy()
            signal_data["regime"] = "VOLATILE_WICK"  # Not in allowed states
            self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)

        # 2. Five bars with high volatility (vol veto)
        for i in range(5):
            signal_data = self.base_signal_data.copy()
            signal_data["realised_vol_1s"] = 0.001  # Above threshold
            self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)

        # 3. Five bars with high spread (spread veto)
        for i in range(5):
            signal_data = self.base_signal_data.copy()
            signal_data["spread_relative"] = 0.0001  # Above threshold
            self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)

        # 4. Four bars with OBI below threshold
        for i in range(4):
            signal_data = self.base_signal_data.copy()
            signal_data["raw_obi_L1_3"] = 0.5  # Below threshold
            signal_data["raw_obi_L1_10"] = 0.05
            self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)

        # 5. One bar triggering a long entry
        signal_data = self.base_signal_data.copy()
        signal_data["raw_obi_L1_3"] = 0.85  # Above threshold
        signal_data["raw_obi_L1_10"] = 0.05  # Same sign
        self.strategy.evaluate(signal_data, current_timestamp=self.current_timestamp)

        # Check diagnostic counters
        expected_counts = {
            'bars_seen': 20,
            'regime_gate': 5,
            'vol_veto': 5,
            'spread_veto': 5,
            'obi_threshold': 4,
            'entry_long': 1
        }

        # Check each expected count
        for key, expected_value in expected_counts.items():
            self.assertEqual(self.strategy.diag[key], expected_value,
                            f"Counter '{key}' expected {expected_value}, got {self.strategy.diag[key]}")

        # Log the diagnostic counters
        self.strategy.log_diagnostic_counters()


if __name__ == "__main__":
    unittest.main()
