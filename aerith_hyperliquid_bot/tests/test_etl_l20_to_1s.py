# tests/test_etl_l20_to_1s.py

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path
import unittest
from unittest.mock import patch, MagicMock

import numpy as np
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq

# Add project root to path for imports
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

sys.path.insert(0, str(project_root.parent))

from aerith_hyperliquid_bot.tools.etl_l20_to_1s import (
    load_raw_l2_data,
    calculate_features,
    resample_to_1s,
    calculate_post_resample_features,
    process_hour
)
from aerith_hyperliquid_bot.utils.feature_naming import obi_col

class TestETL_L20_to_1s(unittest.TestCase):
    """Test the ETL pipeline for converting raw L2 data to 1-second features."""

    def setUp(self):
        """Set up test fixtures."""
        # Create temporary directories for test data
        self.temp_dir = tempfile.mkdtemp()
        self.raw_dir = os.path.join(self.temp_dir, "raw")
        self.out_dir = os.path.join(self.temp_dir, "out")
        os.makedirs(self.raw_dir, exist_ok=True)
        os.makedirs(self.out_dir, exist_ok=True)

        # Create a test fixture with 100 snapshots, depth 20
        self.create_test_fixture()

    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)

    def create_test_fixture(self):
        """Create a test fixture with 100 snapshots, depth 20."""
        # Create a test fixture with 100 snapshots
        records = []
        base_time = 1733385600000  # 2024-12-05T08:00:00.000Z
        base_price = 100000.0

        for i in range(100):
            # Create a record with timestamp and L2 data
            timestamp = base_time + i * 100  # 100ms between snapshots
            mid_price = base_price + np.sin(i / 10) * 100  # Sine wave price
            spread = 10.0 + np.random.random() * 5.0  # Random spread
            best_bid = mid_price - spread / 2
            best_ask = mid_price + spread / 2

            # Create bid and ask levels
            bids = []
            asks = []
            for j in range(1, 21):  # 20 levels
                bid_price = best_bid - (j - 1) * 10
                ask_price = best_ask + (j - 1) * 10
                bid_size = 1.0 + np.random.random() * 5.0
                ask_size = 1.0 + np.random.random() * 5.0
                bids.append({"px": str(bid_price), "sz": str(bid_size), "n": 1})
                asks.append({"px": str(ask_price), "sz": str(ask_size), "n": 1})

            # Create the record
            record = {
                "time": "2024-12-05T08:00:00.000Z",
                "ver_num": 1,
                "raw": {
                    "channel": "l2Book",
                    "data": {
                        "coin": "BTC",
                        "time": timestamp,
                        "levels": [bids, asks]
                    }
                }
            }
            records.append(record)

        # Write the records to a file
        fixture_path = os.path.join(self.raw_dir, "lob_fixture.txt")
        with open(fixture_path, "w") as f:
            for record in records:
                f.write(json.dumps(record) + "\n")

    def test_load_raw_l2_data(self):
        """Test loading raw L2 data from a file."""
        fixture_path = os.path.join(self.raw_dir, "lob_fixture.txt")
        df = load_raw_l2_data(fixture_path)

        # Check that the DataFrame has the expected columns and rows
        self.assertIsInstance(df, pd.DataFrame)
        self.assertGreater(len(df), 0)
        self.assertIn("timestamp", df.columns)
        self.assertIn("bid_price_1", df.columns)
        self.assertIn("ask_price_1", df.columns)
        self.assertIn("bid_size_1", df.columns)
        self.assertIn("ask_size_1", df.columns)

    def test_calculate_features_depth5(self):
        """Test calculating features with depth=5."""
        fixture_path = os.path.join(self.raw_dir, "lob_fixture.txt")
        df = load_raw_l2_data(fixture_path)
        df_features = calculate_features(df, depth=5)

        # Check that the features were calculated correctly
        self.assertIn("mid_price", df_features.columns)
        self.assertIn("spread", df_features.columns)
        self.assertIn("spread_relative", df_features.columns)

        # Use helper function to get OBI column name
        obi_column_name = obi_col(df_features.columns, 5)
        self.assertIn(obi_column_name, df_features.columns)

        # Check that OBI is between -1 and 1
        self.assertTrue((df_features[obi_column_name] >= -1).all())
        self.assertTrue((df_features[obi_column_name] <= 1).all())

    def test_calculate_features_depth20(self):
        """Test calculating features with depth=20."""
        fixture_path = os.path.join(self.raw_dir, "lob_fixture.txt")
        df = load_raw_l2_data(fixture_path)
        df_features = calculate_features(df, depth=20)

        # Check that the features were calculated correctly
        # Use helper function to get OBI column name
        obi_column_name = obi_col(df_features.columns, 20)
        self.assertIn(obi_column_name, df_features.columns)

        # Check that OBI is between -1 and 1
        self.assertTrue((df_features[obi_column_name] >= -1).all())
        self.assertTrue((df_features[obi_column_name] <= 1).all())

    def test_resample_to_1s(self):
        """Test resampling to 1-second intervals."""
        fixture_path = os.path.join(self.raw_dir, "lob_fixture.txt")
        df = load_raw_l2_data(fixture_path)
        df_features = calculate_features(df, depth=5)
        df_resampled = resample_to_1s(df_features, method="median")

        # Check that the resampled DataFrame has the expected columns and rows
        self.assertIsInstance(df_resampled, pd.DataFrame)
        self.assertIn("timestamp", df_resampled.columns)
        self.assertIn("mid_price", df_resampled.columns)

        # Use helper function to get OBI column name
        obi_column_name = obi_col(df_resampled.columns, 5)
        self.assertIn(obi_column_name, df_resampled.columns)

        # Check that the number of rows is less than or equal to the range in seconds
        time_range_seconds = (df["timestamp"].max() - df["timestamp"].min()).total_seconds()
        self.assertLessEqual(len(df_resampled), np.ceil(time_range_seconds) + 1)

    def test_calculate_post_resample_features(self):
        """Test calculating post-resample features."""
        fixture_path = os.path.join(self.raw_dir, "lob_fixture.txt")
        df = load_raw_l2_data(fixture_path)
        df_features = calculate_features(df, depth=5)
        df_resampled = resample_to_1s(df_features, method="median")
        df_post = calculate_post_resample_features(df_resampled)

        # Check that the post-resample features were calculated correctly
        self.assertIn("close", df_post.columns)
        self.assertIn("realised_vol_1s", df_post.columns)
        self.assertIn("atr", df_post.columns)
        self.assertIn("atr_percent", df_post.columns)
        self.assertIn("unrealised_pnl", df_post.columns)

    def test_process_hour(self):
        """Test processing a single hour of data."""
        fixture_path = os.path.join(self.raw_dir, "lob_fixture.txt")
        output_path = os.path.join(self.out_dir, "features_test.parquet")

        # Process the hour
        result = process_hour(
            raw_file=fixture_path,
            output_file=output_path,
            depth=5,
            rollup_method="median",
            force=True
        )

        # Check that the processing was successful
        self.assertTrue(result)
        self.assertTrue(os.path.exists(output_path))

        # Check that the output file has the expected columns
        df = pd.read_parquet(output_path)
        self.assertIn("timestamp", df.columns)
        self.assertIn("mid_price", df.columns)

        # Use helper function to get OBI column name
        obi_column_name = obi_col(df.columns, 5)
        self.assertIn(obi_column_name, df.columns)

        self.assertIn("realised_vol_1s", df.columns)
        self.assertIn("atr", df.columns)
        self.assertIn("atr_percent", df.columns)

        # Check that spread_relative is <= 0.001 for the fixture (sanity check)
        self.assertTrue((df["spread_relative"] <= 0.001).all())

if __name__ == "__main__":
    unittest.main()
