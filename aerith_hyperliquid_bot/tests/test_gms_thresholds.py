"""
Test GMS threshold validation in settings.py

This test ensures that the enhanced GMS threshold validators work correctly
and catch invalid threshold configurations.
"""

import pytest
from pydantic import ValidationError
from hyperliquid_bot.config.settings import RegimeSettings


class TestGMSThresholdValidation:
    """Test cases for GMS threshold validation"""

    def test_valid_thresholds(self):
        """Test that valid threshold configurations pass validation"""
        # Valid configuration (matches our fixed values)
        valid_config = {
            'gms_vol_low_thresh': 0.02,
            'gms_vol_high_thresh': 0.06,
            'gms_mom_weak_thresh': 1.0,
            'gms_mom_strong_thresh': 5.0
        }

        # Should not raise any exception
        regime_settings = RegimeSettings(**valid_config)
        assert regime_settings.gms_vol_low_thresh == 0.02
        assert regime_settings.gms_vol_high_thresh == 0.06
        assert regime_settings.gms_mom_weak_thresh == 1.0
        assert regime_settings.gms_mom_strong_thresh == 5.0

    def test_vol_low_greater_than_high_fails(self):
        """Test that vol_low >= vol_high raises ValidationError"""
        invalid_config = {
            'gms_vol_low_thresh': 0.06,  # Higher than high!
            'gms_vol_high_thresh': 0.02,
            'gms_mom_weak_thresh': 1.0,
            'gms_mom_strong_thresh': 5.0
        }

        with pytest.raises(ValidationError) as exc_info:
            RegimeSettings(**invalid_config)

        assert "Low Vol threshold must be less than High Vol threshold" in str(exc_info.value)

    def test_vol_equal_fails(self):
        """Test that vol_low == vol_high raises ValidationError"""
        invalid_config = {
            'gms_vol_low_thresh': 0.05,  # Equal to high
            'gms_vol_high_thresh': 0.05,
            'gms_mom_weak_thresh': 1.0,
            'gms_mom_strong_thresh': 5.0
        }

        with pytest.raises(ValidationError) as exc_info:
            RegimeSettings(**invalid_config)

        assert "Low Vol threshold must be less than High Vol threshold" in str(exc_info.value)

    def test_vol_out_of_range_fails(self):
        """Test that volatility thresholds outside (0, 1) range fail"""
        # Test negative values (caught by Field constraint)
        with pytest.raises(ValidationError) as exc_info:
            RegimeSettings(
                gms_vol_low_thresh=-0.01,  # Negative!
                gms_vol_high_thresh=0.06,
                gms_mom_weak_thresh=1.0,
                gms_mom_strong_thresh=5.0
            )
        assert "Input should be greater than or equal to 0" in str(exc_info.value)

        # Test values >= 1 (caught by our custom validator)
        with pytest.raises(ValidationError) as exc_info:
            RegimeSettings(
                gms_vol_low_thresh=0.02,
                gms_vol_high_thresh=1.0,  # Equal to 1!
                gms_mom_weak_thresh=1.0,
                gms_mom_strong_thresh=5.0
            )
        assert "must satisfy 0 < vol_low" in str(exc_info.value)

        # Test values > 1 (caught by our custom validator)
        with pytest.raises(ValidationError) as exc_info:
            RegimeSettings(
                gms_vol_low_thresh=0.02,
                gms_vol_high_thresh=1.5,  # Greater than 1!
                gms_mom_weak_thresh=1.0,
                gms_mom_strong_thresh=5.0
            )
        assert "must satisfy 0 < vol_low" in str(exc_info.value)

    def test_mom_weak_greater_than_strong_fails(self):
        """Test that mom_weak >= mom_strong raises ValidationError"""
        invalid_config = {
            'gms_vol_low_thresh': 0.02,
            'gms_vol_high_thresh': 0.06,
            'gms_mom_weak_thresh': 5.0,  # Higher than strong!
            'gms_mom_strong_thresh': 1.0
        }

        with pytest.raises(ValidationError) as exc_info:
            RegimeSettings(**invalid_config)

        assert "Weak Mom threshold must be less than Strong Mom threshold" in str(exc_info.value)

    def test_mom_equal_fails(self):
        """Test that mom_weak == mom_strong raises ValidationError"""
        invalid_config = {
            'gms_vol_low_thresh': 0.02,
            'gms_vol_high_thresh': 0.06,
            'gms_mom_weak_thresh': 3.0,  # Equal to strong
            'gms_mom_strong_thresh': 3.0
        }

        with pytest.raises(ValidationError) as exc_info:
            RegimeSettings(**invalid_config)

        assert "Weak Mom threshold must be less than Strong Mom threshold" in str(exc_info.value)

    def test_mom_out_of_range_fails(self):
        """Test that momentum thresholds outside (0, 10) range fail"""
        # Test negative values (caught by Field constraint)
        with pytest.raises(ValidationError) as exc_info:
            RegimeSettings(
                gms_vol_low_thresh=0.02,
                gms_vol_high_thresh=0.06,
                gms_mom_weak_thresh=-1.0,  # Negative!
                gms_mom_strong_thresh=5.0
            )
        assert "Input should be greater than or equal to 0" in str(exc_info.value)

        # Test values >= 10 (caught by our custom validator)
        with pytest.raises(ValidationError) as exc_info:
            RegimeSettings(
                gms_vol_low_thresh=0.02,
                gms_vol_high_thresh=0.06,
                gms_mom_weak_thresh=1.0,
                gms_mom_strong_thresh=10.0  # Equal to 10!
            )
        assert "must satisfy 0 < mom_weak" in str(exc_info.value)

        # Test values > 10 (caught by our custom validator)
        with pytest.raises(ValidationError) as exc_info:
            RegimeSettings(
                gms_vol_low_thresh=0.02,
                gms_vol_high_thresh=0.06,
                gms_mom_weak_thresh=1.0,
                gms_mom_strong_thresh=15.0  # Greater than 10!
            )
        assert "must satisfy 0 < mom_weak" in str(exc_info.value)

    def test_old_misconfigured_values_fail(self):
        """Test that the old misconfigured values from base.yaml would fail validation"""
        # These are the old wrong values that caused the 95% CHOP issue
        old_wrong_config = {
            'gms_vol_low_thresh': 0.55,   # 55%!
            'gms_vol_high_thresh': 0.92,  # 92%!
            'gms_mom_weak_thresh': 50.0,  # Way too high
            'gms_mom_strong_thresh': 100.0  # Way too high
        }

        with pytest.raises(ValidationError) as exc_info:
            RegimeSettings(**old_wrong_config)

        # Should fail on momentum thresholds being > 10
        assert "must satisfy 0 < mom_weak" in str(exc_info.value)

    def test_boundary_values(self):
        """Test boundary values for the ranges"""
        # Test very small but valid values
        boundary_config = {
            'gms_vol_low_thresh': 0.001,   # Very small but > 0
            'gms_vol_high_thresh': 0.999,  # Very large but < 1
            'gms_mom_weak_thresh': 0.001,  # Very small but > 0
            'gms_mom_strong_thresh': 9.999  # Very large but < 10
        }

        # Should not raise any exception
        regime_settings = RegimeSettings(**boundary_config)
        assert regime_settings.gms_vol_low_thresh == 0.001
        assert regime_settings.gms_vol_high_thresh == 0.999
        assert regime_settings.gms_mom_weak_thresh == 0.001
        assert regime_settings.gms_mom_strong_thresh == 9.999
