# tests/test_margin_buffer.py

import pytest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import Mock, patch

from hyperliquid_bot.config.settings import Config, PortfolioSettings, load_config
from hyperliquid_bot.core.risk import RiskManager


class TestMarginBufferConfig:
    """Test margin buffer configuration functionality."""

    def test_portfolio_settings_default_margin_buffer(self):
        """Test that PortfolioSettings has correct default margin buffer."""
        portfolio_data = {
            'initial_balance': 10000,
            'risk_per_trade': 0.02,
            'max_leverage': 10.0,
            'asset_max_leverage': 50.0,
            'max_hold_time_hours': 24
        }

        portfolio = PortfolioSettings(**portfolio_data)

        # Check default margin buffer
        assert portfolio.margin_buffer_pct == 0.05
        assert hasattr(portfolio, 'margin_buffer_pct')

    def test_portfolio_settings_custom_margin_buffer(self):
        """Test that PortfolioSettings accepts custom margin buffer values."""
        portfolio_data = {
            'initial_balance': 10000,
            'risk_per_trade': 0.02,
            'max_leverage': 10.0,
            'asset_max_leverage': 50.0,
            'max_hold_time_hours': 24,
            'margin_buffer_pct': 0.10  # 10% buffer
        }

        portfolio = PortfolioSettings(**portfolio_data)

        # Check custom margin buffer
        assert portfolio.margin_buffer_pct == 0.10

    def test_portfolio_settings_margin_buffer_validation(self):
        """Test margin buffer validation constraints."""
        portfolio_data = {
            'initial_balance': 10000,
            'risk_per_trade': 0.02,
            'max_leverage': 10.0,
            'asset_max_leverage': 50.0,
            'max_hold_time_hours': 24
        }

        # Test valid range (0 to 0.20)
        valid_values = [0.0, 0.05, 0.10, 0.15, 0.20]
        for value in valid_values:
            portfolio_data['margin_buffer_pct'] = value
            portfolio = PortfolioSettings(**portfolio_data)
            assert portfolio.margin_buffer_pct == value

        # Test invalid values (negative)
        with pytest.raises(ValueError):
            portfolio_data['margin_buffer_pct'] = -0.01
            PortfolioSettings(**portfolio_data)

        # Test invalid values (too high)
        with pytest.raises(ValueError):
            portfolio_data['margin_buffer_pct'] = 0.21
            PortfolioSettings(**portfolio_data)

    def test_yaml_config_default_margin_buffer(self):
        """Test that YAML config loads with default margin buffer."""
        yaml_content = """
is_backtest: true
data_paths:
  l2_data_root: "/tmp/test"
  raw_l2_dir: "/tmp/test"
  feature_1s_dir: "/tmp/test"
  ohlcv_base_path: "/tmp/test"
  log_dir: "/tmp/test"
cache:
  l2_cache_max_size: 24
backtest:
  period_preset: 'full'
simulation:
  latency_seconds: 0.5
  max_impact_levels: 5
  force_taker_execution: true
  attempt_maker_orders: false
strategies:
  use_trend_following: false
  use_tf_v3: true
  use_mean_reversion: false
  use_mean_variance: false
  use_obi_scalper: false
portfolio:
  initial_balance: 10000
  risk_per_trade: 0.02
  max_leverage: 10.0
  asset_max_leverage: 50.0
  margin_mode: cross
  max_hold_time_hours: 24
  margin_buffer_pct: 0.05
costs:
  taker_fee: 0.0003
  maker_fee: 0.0001
  l2_penalty_factor: 1.005
regime:
  use_filter: true
  detector_type: 'continuous_gms'
microstructure:
  depth_levels: 5
  obi_smoothing_window: 8
  spread_rolling_window: 24
indicators:
  adx_period: 14
  tf_ewma_fast: 20
  tf_ewma_slow: 50
  tf_leverage_base: 5.0
analysis:
  analyze_trades_after_backtest: true
visualization:
  plot_enabled: true
data_providers:
  fear_greed:
    enabled: false
gms:
  detector_type: 'continuous_gms'
tf_v3:
  enabled: true
etl:
  l20_to_1s:
    chunk_sec: 3600
scheduler:
  etl_enabled: true
"""

        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(yaml_content)
            f.flush()

            # Create temporary directories
            import os
            os.makedirs("/tmp/test", exist_ok=True)

            try:
                config = load_config(f.name)
                assert config.portfolio.margin_buffer_pct == 0.05
            finally:
                os.unlink(f.name)

    def test_yaml_config_custom_margin_buffer(self):
        """Test that YAML config loads with custom margin buffer."""
        yaml_content = """
is_backtest: true
data_paths:
  l2_data_root: "/tmp/test"
  raw_l2_dir: "/tmp/test"
  feature_1s_dir: "/tmp/test"
  ohlcv_base_path: "/tmp/test"
  log_dir: "/tmp/test"
cache:
  l2_cache_max_size: 24
backtest:
  period_preset: 'full'
simulation:
  latency_seconds: 0.5
  max_impact_levels: 5
  force_taker_execution: true
  attempt_maker_orders: false
strategies:
  use_trend_following: false
  use_tf_v3: true
  use_mean_reversion: false
  use_mean_variance: false
  use_obi_scalper: false
portfolio:
  initial_balance: 10000
  risk_per_trade: 0.02
  max_leverage: 10.0
  asset_max_leverage: 50.0
  margin_mode: cross
  max_hold_time_hours: 24
  margin_buffer_pct: 0.10  # Custom 10% buffer
costs:
  taker_fee: 0.0003
  maker_fee: 0.0001
  l2_penalty_factor: 1.005
regime:
  use_filter: true
  detector_type: 'continuous_gms'
microstructure:
  depth_levels: 5
  obi_smoothing_window: 8
  spread_rolling_window: 24
indicators:
  adx_period: 14
  tf_ewma_fast: 20
  tf_ewma_slow: 50
  tf_leverage_base: 5.0
analysis:
  analyze_trades_after_backtest: true
visualization:
  plot_enabled: true
data_providers:
  fear_greed:
    enabled: false
gms:
  detector_type: 'continuous_gms'
tf_v3:
  enabled: true
etl:
  l20_to_1s:
    chunk_sec: 3600
scheduler:
  etl_enabled: true
"""

        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(yaml_content)
            f.flush()

            # Create temporary directories
            import os
            os.makedirs("/tmp/test", exist_ok=True)

            try:
                config = load_config(f.name)
                assert config.portfolio.margin_buffer_pct == 0.10
            finally:
                os.unlink(f.name)


class TestRiskManagerMarginBuffer:
    """Test RiskManager uses configurable margin buffer."""

    def test_margin_buffer_config_access(self):
        """Test that RiskManager can access margin buffer from config."""
        # Create mock config with custom margin buffer
        mock_config = Mock()
        mock_config.portfolio.margin_buffer_pct = 0.10  # 10% buffer

        # Create RiskManager
        risk_manager = RiskManager(mock_config)

        # Verify that the config is stored and accessible
        assert risk_manager.config.portfolio.margin_buffer_pct == 0.10

    def test_margin_buffer_default_value(self):
        """Test that RiskManager can access default margin buffer from config."""
        # Create mock config with default margin buffer
        mock_config = Mock()
        mock_config.portfolio.margin_buffer_pct = 0.05  # Default 5% buffer

        # Create RiskManager
        risk_manager = RiskManager(mock_config)

        # Verify that the config is stored and accessible
        assert risk_manager.config.portfolio.margin_buffer_pct == 0.05
