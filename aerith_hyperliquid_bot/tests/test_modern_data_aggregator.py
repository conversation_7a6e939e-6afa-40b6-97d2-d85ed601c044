"""
Test Modern Data Aggregator Look-Ahead Prevention
================================================

This test suite verifies that the modern data aggregator correctly
prevents all forms of look-ahead bias.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.modern.data_aggregator import ModernDataAggregator


class TestModernDataAggregator(unittest.TestCase):
    """Test suite for modern data aggregator."""
    
    def setUp(self):
        """Set up test data."""
        self.aggregator = ModernDataAggregator()
        
        # Create 1-second test data for 2 hours
        self.start_time = datetime(2024, 1, 1, 10, 0, 0)
        n_seconds = 7200  # 2 hours
        
        timestamps = pd.date_range(self.start_time, periods=n_seconds, freq='1s')
        
        # Create synthetic price data
        prices = 100 + np.random.randn(n_seconds).cumsum() * 0.01
        
        self.test_data = pd.DataFrame({
            'timestamp': timestamps,
            'close': prices,
            'high': prices + np.random.rand(n_seconds) * 0.1,
            'low': prices - np.random.rand(n_seconds) * 0.1,
            'volume': np.random.rand(n_seconds) * 1000,
            'obi_smoothed': np.random.uniform(-1, 1, n_seconds),
            'spread_mean': np.random.rand(n_seconds) * 0.001,
            'spread_std': np.random.rand(n_seconds) * 0.0001,
            'atr_14_sec': np.ones(n_seconds) * 1.0,
            'atr_percent_sec': np.ones(n_seconds) * 0.01,
            'ma_slope': np.random.randn(n_seconds) * 0.1,
            'ma_slope_ema_30s': np.random.randn(n_seconds) * 0.05
        })
        self.test_data.set_index('timestamp', inplace=True)
    
    def test_60s_aggregation_no_look_ahead(self):
        """Test that 60s aggregation doesn't include future data."""
        # Set current time to 30 minutes into data
        current_time = self.start_time + timedelta(minutes=30)
        
        # Add a spike at 31 minutes (future)
        spike_time = self.start_time + timedelta(minutes=31)
        self.test_data.loc[spike_time, 'close'] = 200.0
        
        # Aggregate up to current time
        agg_data = self.aggregator.aggregate_to_60s(self.test_data, current_time)
        
        # Verify no data beyond current time
        self.assertTrue(agg_data.index.max() <= current_time)
        
        # Verify spike is not included
        self.assertTrue(all(agg_data['close'] < 150))
        
        # Verify aggregation used correct labeling
        # With < filtering, we should have data up to but not including current_time
        # The last bar should be at 29:00 or 30:00 depending on exact timing
        self.assertGreater(len(agg_data), 0)
    
    def test_hourly_bar_completion(self):
        """Test that hourly bars only include completed hours."""
        # Current time is 10:45:30
        current_time = self.start_time + timedelta(minutes=45, seconds=30)
        
        # Get hourly bar
        hourly_bar = self.aggregator.get_hourly_bar(self.test_data, current_time)
        
        # With our test data starting at 10:00, there's no completed hourly bar yet
        # (would need data before 10:00 to have a 10:00 bar)
        # So let's test with more data
        current_time = self.start_time + timedelta(hours=1, minutes=45)
        hourly_bar = self.aggregator.get_hourly_bar(self.test_data, current_time)
        
        # Should get the 11:00 bar (containing 10:00-11:00 data)
        self.assertFalse(hourly_bar.empty)
        self.assertEqual(hourly_bar.name.hour, 11)
        self.assertEqual(hourly_bar.name.minute, 0)
    
    def test_execution_window_timing(self):
        """Test execution window doesn't see beyond 5 minutes."""
        signal_time = self.start_time + timedelta(minutes=30)
        
        # Add price improvement at minute 36 (beyond 5-minute window)
        improvement_time = signal_time + timedelta(minutes=6)
        self.test_data.loc[improvement_time, 'spread_mean'] = 0.00001
        
        # Get execution window data
        window_data = self.aggregator.get_execution_window_data(
            self.test_data,
            signal_time,
            window_minutes=5
        )
        
        # Verify window bounds
        # With right labeling, first bar is labeled at signal_time + 1 minute
        self.assertEqual(window_data.index.min(), signal_time + timedelta(minutes=1))
        self.assertLessEqual(window_data.index.max(), signal_time + timedelta(minutes=5))
        
        # Verify improvement is not visible
        self.assertTrue(all(window_data['spread_mean'] > 0.00005))
    
    def test_validate_no_look_ahead(self):
        """Test look-ahead validation function."""
        current_time = self.start_time + timedelta(hours=1)
        
        # Test valid aggregation
        valid_agg = self.aggregator.aggregate_to_60s(self.test_data, current_time)
        is_valid = self.aggregator.validate_no_look_ahead(
            self.test_data,
            valid_agg,
            current_time
        )
        self.assertTrue(is_valid)
        
        # Test invalid aggregation (manually add future data)
        invalid_agg = valid_agg.copy()
        future_time = current_time + timedelta(minutes=10)
        invalid_agg.loc[future_time] = 100.0
        
        is_valid = self.aggregator.validate_no_look_ahead(
            self.test_data,
            invalid_agg,
            current_time
        )
        self.assertFalse(is_valid)
    
    def test_feature_preparation(self):
        """Test feature preparation for regime detection."""
        current_time = self.start_time + timedelta(minutes=30)
        
        # Aggregate data
        agg_data = self.aggregator.aggregate_to_60s(self.test_data, current_time)
        
        # Prepare features
        features = self.aggregator.prepare_features_for_regime(agg_data, current_time)
        
        # Verify required fields exist
        required_fields = [
            'timestamp', 'close', 'volume_imbalance', 
            'spread_mean', 'atr_percent_sec', 'ma_slope_ema_30s'
        ]
        for field in required_fields:
            self.assertIn(field, features)
            
        # Verify timestamp is current time
        self.assertEqual(features['timestamp'], current_time)
        
        # Verify volume_imbalance is mapped from obi_smoothed
        self.assertIsNotNone(features['volume_imbalance'])
        self.assertTrue(-1 <= features['volume_imbalance'] <= 1)
    
    def test_resampling_label_consistency(self):
        """Test that resampling labels are consistent."""
        # This ensures the 30:00 bar contains 29:00-30:00 data
        current_time = self.start_time + timedelta(minutes=30)
        
        # Mark specific seconds for testing
        marker_time_29_59 = current_time - timedelta(seconds=1)
        marker_time_30_00 = current_time
        
        # Set distinctive values
        self.test_data.loc[marker_time_29_59, 'close'] = 150.0
        self.test_data.loc[marker_time_30_00, 'close'] = 200.0
        
        # Aggregate
        agg_data = self.aggregator.aggregate_to_60s(self.test_data, current_time)
        
        # The 30:00 bar should include the 29:59 value but not the 30:00 value
        bar_30 = agg_data.loc[current_time]
        
        # The 'last' value in the 30:00 bar should be from 29:59
        self.assertAlmostEqual(bar_30['close'], 150.0, places=1)


if __name__ == '__main__':
    unittest.main()