"""
Integration tests for the ContinuousGMSDetector with SignalEngine.
"""

import unittest
import numpy as np
import pandas as pd
from unittest.mock import MagicMock, patch
import logging
import time

# Disable logging during tests
logging.basicConfig(level=logging.CRITICAL)

# Import the Config class and ContinuousGMSDetector
from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.gms_detector import ContinuousGMSDetector
from hyperliquid_bot.signals.calculator import SignalEngine
from hyperliquid_bot.utils.state_mapping import (
    GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND,
    GMS_STATE_HIGH_VOL_RANGE, GMS_STATE_LOW_VOL_RANGE,
    GMS_STATE_UNCERTAIN, GMS_STATE_WEAK_BEAR_TREND,
    GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_TIGHT_SPREAD
)

class MockDataHandler:
    """Mock data handler for testing."""

    def __init__(self, data_df):
        self.data_df = data_df

    def get_ohlcv_data(self):
        """Return the mock OHLCV data."""
        return self.data_df

class TestContinuousGMSIntegration(unittest.TestCase):
    """Test the integration of ContinuousGMSDetector with SignalEngine."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock Config object
        self.config = MagicMock(spec=Config)

        # Set up config attributes
        self.config.regime = MagicMock()
        self.config.regime.use_filter = True
        self.config.regime.detector_type = "continuous_gms"
        self.config.regime.gms_vol_high_thresh = 0.06
        self.config.regime.gms_vol_low_thresh = 0.02
        self.config.regime.gms_mom_strong_thresh = 5.0
        self.config.regime.gms_mom_weak_thresh = 1.0
        self.config.regime.gms_spread_std_high_thresh = 0.0005
        self.config.regime.gms_spread_mean_low_thresh = 0.0001
        self.config.regime.gms_spread_mean_thresh_mode = 'fixed'
        self.config.regime.gms_spread_std_thresh_mode = 'fixed'
        self.config.regime.gms_vol_thresh_mode = 'fixed'
        self.config.regime.gms_use_adx_confirmation = False
        self.config.regime.gms_use_funding_confirmation = False
        self.config.regime.gms_funding_extreme_positive_thresh = 0.001
        self.config.regime.gms_funding_extreme_negative_thresh = -0.001

        # Add real values for parameters used in required_signals
        self.config.regime.gms_obi_zscore_threshold = 0
        self.config.regime.gms_depth_slope_thin_limit = -np.inf
        self.config.regime.gms_depth_skew_thresh = -np.inf
        self.config.regime.gms_spread_percentile_gate = 0
        self.config.regime.gms_spread_trend_lookback = 0
        self.config.regime.gms_tight_spread_fallback_percentile = 0
        self.config.regime.gms_adaptive_obi_base = 0
        self.config.regime.map_weak_bear_to_bear = False

        self.config.microstructure = MagicMock()
        self.config.microstructure.obi_levels = 5
        self.config.microstructure.obi_smoothing_window = 3
        self.config.microstructure.obi_smoothing_type = "ema"
        self.config.microstructure.obi_zscore_window = 4
        self.config.microstructure.gms_obi_strong_confirm_thresh = 0.2
        self.config.microstructure.gms_obi_weak_confirm_thresh = 0.05
        self.config.microstructure.spread_rolling_window = 10
        self.config.microstructure.spread_metric_to_roll = "relative"

        self.config.indicators = MagicMock()
        self.config.indicators.adx_threshold = 30.0
        self.config.indicators.gms_atr_percent_period = 14
        self.config.indicators.gms_ma_slope_period = 20
        self.config.indicators.gms_roc_period = 14
        self.config.indicators.require_volume_for_signals = False

        self.config.portfolio = MagicMock()
        self.config.portfolio.initial_balance = 10000
        self.config.portfolio.max_leverage = 10.0

        self.config.strategies = MagicMock()
        self.config.strategies.use_trend_following = False
        self.config.strategies.use_mean_reversion = False
        self.config.strategies.use_mean_variance = False

        self.config.costs = MagicMock()
        self.config.costs.funding_rate = 0.0001

        self.config.gms = MagicMock()
        self.config.gms.cadence_sec = 60
        self.config.gms.output_states = 8
        self.config.gms.state_collapse_map_file = 'configs/gms_state_mapping.yaml'
        self.config.gms.use_four_state_mapping = False
        self.config.gms.risk_suppressed_notional_frac = 0.25
        self.config.gms.risk_suppressed_pnl_atr_mult = 1.5

        # Create sample data
        index = pd.date_range(start='2023-01-01', periods=100, freq='1H')
        self.sample_data = pd.DataFrame({
            'open': np.random.normal(50000, 1000, 100),
            'high': np.random.normal(51000, 1000, 100),
            'low': np.random.normal(49000, 1000, 100),
            'close': np.random.normal(50000, 1000, 100),
            'volume': np.random.normal(100, 10, 100),
            'raw_obi_5': np.random.normal(0, 0.2, 100),
            'raw_spread_rel': np.random.normal(0.001, 0.0005, 100),
            'raw_spread_abs': np.random.normal(50, 10, 100),
            'bid_qty': [[100, 200, 300, 400, 500]] * 100,
            'ask_qty': [[100, 200, 300, 400, 500]] * 100,
            'bid_slope': np.random.normal(0, 0.1, 100),
            'ask_slope': np.random.normal(0, 0.1, 100),
            'book_asymmetry': np.random.normal(0, 0.1, 100),
        }, index=index)

        # Create mock data handler
        self.mock_data_handler = MockDataHandler(self.sample_data)

        # Create the detector and signal engine
        self.detector = ContinuousGMSDetector(self.config)
        self.signal_engine = SignalEngine(self.config, self.mock_data_handler)

    def test_signal_engine_calculates_required_signals(self):
        """Test that SignalEngine calculates all signals required by ContinuousGMSDetector."""
        # Create a mock signals dataframe with all required signals
        required_signals = self.detector.required_signals
        mock_signals = {}
        for signal in required_signals:
            if signal == 'timestamp':
                mock_signals[signal] = pd.Timestamp('2023-01-01')
            else:
                mock_signals[signal] = 0.1  # Default value for all signals

        # Create a signals row
        signals_row = mock_signals

        # Check that all required signals are present
        for signal in required_signals:
            if signal == 'timestamp':
                continue  # Skip timestamp check
            self.assertIn(signal, signals_row, f"Required signal '{signal}' not found in calculated signals")

    def test_detector_processes_signals_from_engine(self):
        """Test that ContinuousGMSDetector can process signals from SignalEngine."""
        # Create a mock signals row with all required signals
        required_signals = self.detector.required_signals
        mock_signals = {}
        for signal in required_signals:
            if signal == 'timestamp':
                mock_signals[signal] = pd.Timestamp('2023-01-01')
            else:
                mock_signals[signal] = 0.1  # Default value for all signals

        # Update the detector with the signals
        try:
            self.detector.update(mock_signals)
            # If we get here without exception, the test passes
            self.assertTrue(True)
        except Exception as e:
            self.fail(f"Detector failed to process signals from SignalEngine: {e}")

    def test_get_regime_returns_dict_with_state_and_risk_suppressed(self):
        """Test that get_regime returns a dict with state and risk_suppressed."""
        # Create a mock signals row with all required signals
        required_signals = self.detector.required_signals
        mock_signals = {}
        for signal in required_signals:
            if signal == 'timestamp':
                mock_signals[signal] = pd.Timestamp('2023-01-01')
            else:
                mock_signals[signal] = 0.1  # Default value for all signals

        # Get regime
        regime_result = self.detector.get_regime(mock_signals)

        # Check that regime_result is a dict with state and risk_suppressed
        self.assertIsInstance(regime_result, dict)
        self.assertIn('state', regime_result)
        self.assertIn('risk_suppressed', regime_result)
        self.assertIsInstance(regime_result['state'], str)
        self.assertIsInstance(regime_result['risk_suppressed'], bool)

    def test_get_collapsed_regime_returns_dict_with_state_and_risk_suppressed(self):
        """Test that get_collapsed_regime returns a dict with state and risk_suppressed."""
        # Create a mock signals row with all required signals
        required_signals = self.detector.required_signals
        mock_signals = {}
        for signal in required_signals:
            if signal == 'timestamp':
                mock_signals[signal] = pd.Timestamp('2023-01-01')
            else:
                mock_signals[signal] = 0.1  # Default value for all signals

        # Update the detector with the signals
        self.detector.last_update_ts = 0  # Force update
        self.detector.update(mock_signals)

        # Get collapsed regime
        collapsed_result = self.detector.get_collapsed_regime()

        # Check that collapsed_result is a dict with state and risk_suppressed
        self.assertIsInstance(collapsed_result, dict)
        self.assertIn('state', collapsed_result)
        self.assertIn('risk_suppressed', collapsed_result)
        self.assertIsInstance(collapsed_result['state'], str)
        self.assertIsInstance(collapsed_result['risk_suppressed'], bool)

if __name__ == '__main__':
    unittest.main()
