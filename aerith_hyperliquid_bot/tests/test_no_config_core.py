"""
Test to ensure no stale config.core references exist in the codebase.

This test is part of Task R-110a (scan) and R-110b (fix).
Currently marked as xfail because config.core references still exist.
Will be updated to pass in R-110b after all references are fixed.
"""

import subprocess
import pytest
import os


class TestNoConfigCore:
    """Test suite to verify config.core references are eliminated."""

    def test_no_cfg_core_references_in_codebase(self):
        """
        Assert that no cfg.core references exist in the codebase.

        This test scans the hyperliquid_bot and tests directories for any
        remaining cfg.core or config.core references that need to be migrated
        to the new configuration schema.
        """
        # Run grep to find cfg.core references (excluding this test file and binary files)
        result = subprocess.run(
            ["grep", "-R", "cfg.core", "hyperliquid_bot", "tests",
             "--exclude=test_no_config_core.py", "--exclude-dir=__pycache__"],
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.dirname(__file__))  # aerith_hyperliquid_bot directory
        )

        # Count the number of lines (hits)
        hit_count = len([line for line in result.stdout.split('\n') if line.strip()])

        # This should be 0 after R-110b is complete
        assert hit_count == 0, f"Found {hit_count} cfg.core references that need to be fixed:\n{result.stdout}"

    def test_no_config_core_references_in_codebase(self):
        """
        Assert that no config.core references exist in the codebase.

        This test scans the hyperliquid_bot and tests directories for any
        remaining config.core references that need to be migrated
        to the new configuration schema.
        """
        # Run grep to find config.core references (excluding this test file and binary files)
        result = subprocess.run(
            ["grep", "-R", "config.core", "hyperliquid_bot", "tests",
             "--exclude=test_no_config_core.py", "--exclude-dir=__pycache__"],
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.dirname(__file__))  # aerith_hyperliquid_bot directory
        )

        # Count the number of lines (hits)
        hit_count = len([line for line in result.stdout.split('\n') if line.strip()])

        # This should be 0 after R-110b is complete
        assert hit_count == 0, f"Found {hit_count} config.core references that need to be fixed:\n{result.stdout}"

    def test_current_config_core_count_matches_audit(self):
        """
        Verify that the current count of config.core references matches our audit.

        This test ensures our audit is accurate and will help track progress
        during R-110b remediation.
        """
        # Run grep to find all config.core and cfg.core references (excluding this test file and binary files)
        result = subprocess.run(
            ["grep", "-Rn", "-e", "config\\.core", "-e", "cfg\\.core", "hyperliquid_bot", "tests",
             "--exclude=test_no_config_core.py", "--exclude-dir=__pycache__"],
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.dirname(__file__))  # aerith_hyperliquid_bot directory
        )

        # Count non-empty lines (excluding binary file matches)
        lines = [line for line in result.stdout.split('\n') if line.strip() and not line.startswith('Binary file')]
        hit_count = len(lines)

        # After R-110b, all references should be fixed
        expected_count = 0  # All config.core references should be eliminated

        assert hit_count == expected_count, (
            f"Config.core references still exist! Found {hit_count} references that need to be fixed. "
            f"R-110b should have eliminated all references.\n\nRemaining hits:\n{result.stdout}"
        )
