## tests/test_portfolio.py

# ADD THIS SIMPLE TEST AT THE VERY TOP
def test_discovery():
    assert True

# --- Original code below ---

import pytest
import numpy as np
from datetime import datetime

# Import the classes we need to test and configure
from hyperliquid_bot.config.settings import Config, load_config
from hyperliquid_bot.portfolio import Portfolio

# --- Test Fixtures ---
# Fixtures provide reusable setup for tests

@pytest.fixture(scope="module")
def default_config() -> Config:
    """Loads the default configuration once per module."""
    # Assumes config.yaml is in the project root directory
    # Adjust path finding if necessary, similar to settings.py __main__ block
    from pathlib import Path
    project_root = Path(__file__).parent.parent.resolve()
    config_path = project_root / 'config.yaml'
    if not config_path.exists():
        pytest.skip(f"Configuration file not found at {config_path}, skipping portfolio tests.")
    return load_config(str(config_path))

@pytest.fixture
def basic_portfolio(default_config: Config) -> Portfolio:
    """Creates a fresh Portfolio instance for each test function."""
    # Modify config slightly for predictable testing if needed
    # default_config.portfolio.initial_balance = 1000.0 # Example override
    return Portfolio(default_config)

# --- Test Cases ---

def test_portfolio_initialization(basic_portfolio: Portfolio, default_config: Config):
    """Test if the portfolio initializes with the correct balance."""
    assert basic_portfolio.balance == default_config.portfolio.initial_balance
    assert basic_portfolio.initial_balance == default_config.portfolio.initial_balance
    assert basic_portfolio.position is None
    assert len(basic_portfolio.trades) == 0
    assert basic_portfolio.equity_curve == [default_config.portfolio.initial_balance]
    assert len(basic_portfolio.equity_timestamps) == 0 # Timestamps added on first event

def test_handle_entry_long(basic_portfolio: Portfolio, default_config: Config):
    """Test handling a simple long entry."""
    initial_balance = basic_portfolio.balance
    entry_price = 50000.0
    filled_size = 0.1
    leverage = 10.0
    slippage_pnl = -5.0 # Example entry slippage
    timestamp = datetime.utcnow().timestamp()

    position_dict = {
        "type": "long",
        "leverage": leverage,
        "strategy": "test_strat",
        "entry_regime": "Trending",
        "entry_time": timestamp,
        # Other fields like stop/profit would be added by Backtester normally
    }

    basic_portfolio.handle_entry(position_dict, entry_price, filled_size, slippage_pnl, timestamp)

    # Check position state
    assert basic_portfolio.position is not None
    assert basic_portfolio.position["entry"] == entry_price
    assert basic_portfolio.position["size"] == filled_size
    assert basic_portfolio.position["type"] == "long"
    assert "entry_fee" in basic_portfolio.position

    # Check balance update
    expected_fee = filled_size * entry_price * default_config.costs.taker_fee * leverage
    expected_balance = initial_balance - expected_fee
    assert np.isclose(basic_portfolio.balance, expected_balance)
    assert np.isclose(basic_portfolio.total_fees_paid, expected_fee)
    assert np.isclose(basic_portfolio.total_slippage_pnl, slippage_pnl)

    # Check equity curve
    assert len(basic_portfolio.equity_curve) == 2 # Initial + entry
    assert basic_portfolio.equity_curve[-1] == basic_portfolio.balance
    assert basic_portfolio.equity_timestamps[-1] == timestamp

def test_handle_exit_long_profit(basic_portfolio: Portfolio, default_config: Config):
    """Test handling a profitable long exit after an entry."""
    # --- Setup: Enter a position first ---
    entry_price = 50000.0
    filled_size = 0.1
    leverage = 10.0
    entry_slippage = -5.0
    entry_timestamp = datetime.utcnow().timestamp() - 3600 # 1 hour ago

    position_dict = {
        "type": "long", "leverage": leverage, "strategy": "test_strat",
        "entry_regime": "Trending", "entry_time": entry_timestamp,
    }
    basic_portfolio.handle_entry(position_dict, entry_price, filled_size, entry_slippage, entry_timestamp)
    balance_after_entry = basic_portfolio.balance
    # Retrieve entry fee and slippage from the stored position, as handle_exit does
    entry_fee = basic_portfolio.position["entry_fee"]
    entry_slippage_pnl = basic_portfolio.position["entry_slippage_pnl"]

    # --- Test Exit ---
    exit_price = 51000.0
    exit_reason = "take_profit"
    exit_slippage = -2.0
    exit_timestamp = datetime.utcnow().timestamp()

    trade_record = basic_portfolio.handle_exit(exit_price, exit_reason, exit_slippage, exit_timestamp)

    # Check position closed
    assert basic_portfolio.position is None

    # Check PnL calculation (mirroring handle_exit logic)
    pnl_points = exit_price - entry_price
    gross_profit = pnl_points * filled_size # Base gross profit (no leverage)
    exit_fee = abs(filled_size * exit_price * default_config.costs.taker_fee * leverage) # Notional exit fee

    # Calculate the comprehensive net profit for the trade, like in handle_exit
    expected_trade_net_profit = (gross_profit * leverage) - entry_fee - exit_fee + entry_slippage_pnl + exit_slippage

    # Check balance update
    expected_balance = balance_after_entry + expected_trade_net_profit

    # Add debug prints to understand the discrepancy
    print(f"Expected balance: {expected_balance}")
    print(f"Actual balance: {basic_portfolio.balance}")
    print(f"Difference: {basic_portfolio.balance - expected_balance}")
    print(f"Entry fee already deducted: {entry_fee}")
    print(f"Gross profit: {gross_profit}")
    print(f"Exit fee: {exit_fee}")
    print(f"Exit slippage: {exit_slippage}")

    # Use relative tolerance for comparison due to floating point errors
    assert np.isclose(basic_portfolio.balance, expected_balance, rtol=1e-10)

def test_apply_funding_long(basic_portfolio: Portfolio, default_config: Config):
    """Test applying funding cost to a long position."""
    # --- Setup: Enter a position ---
    entry_price = 50000.0
    filled_size = 0.1
    leverage = 10.0
    entry_timestamp = datetime.utcnow().timestamp() - default_config.costs.funding_hours * 3600 * 1.5 # 1.5 funding periods ago
    position_dict = {
        "type": "long", "leverage": leverage, "strategy": "test_strat",
        "entry_regime": "Trending", "entry_time": entry_timestamp, "size": filled_size, "entry": entry_price, "entry_fee": 1.0 # Dummy fee
    }
    basic_portfolio.position = position_dict # Manually set position for this test
    balance_before_funding = basic_portfolio.balance
    last_funding_time = None # Force initialization

    # --- Apply Funding ---
    current_time = datetime.utcnow().timestamp()
    current_price = 50500.0 # Price for notional calculation
    funding_rate = default_config.costs.funding_rate

    # First call initializes timer
    last_funding_time = basic_portfolio.apply_funding(current_time, current_price, last_funding_time)

    # Calculate the expected time of the first funding event
    expected_funding_time = entry_timestamp + (default_config.costs.funding_hours * 3600)
    # Assert that the returned time matches the time the funding *should have been* applied
    assert last_funding_time == expected_funding_time

    # --- Check Balance Update ---
    expected_position_value = filled_size * current_price
    funding_payment = expected_position_value * funding_rate
    expected_funding_pnl = -funding_payment # For long position

    # The balance in basic_portfolio was updated by apply_funding.
    # We check it against the balance we recorded *before* calling apply_funding, plus the expected PnL.
    expected_balance_after_funding = balance_before_funding + expected_funding_pnl
    assert np.isclose(basic_portfolio.balance, expected_balance_after_funding)

    # --- Check scenario where no funding is applied ---
    # Reset time and call apply_funding again, but before the next interval
    last_funding_time_unchanged = basic_portfolio.apply_funding(current_time + 10, current_price, last_funding_time)
    assert last_funding_time_unchanged == last_funding_time # Should return the same time as no funding was applied
    assert np.isclose(basic_portfolio.balance, expected_balance_after_funding) # Balance should remain unchanged

def test_bankruptcy_on_exit(basic_portfolio: Portfolio, default_config: Config):
    """Test if bankruptcy is handled correctly on a large loss exit."""
    # --- Setup: Enter a position ---
    entry_price = 50000.0
    filled_size = 0.1 # Small size
    leverage = 50.0 # High leverage relative to balance
    entry_slippage = 0
    entry_timestamp = datetime.utcnow().timestamp() - 3600
    # Manually reduce balance to make bankruptcy likely
    basic_portfolio.balance = 100.0
    basic_portfolio.initial_balance = 100.0
    basic_portfolio.equity_curve = [100.0]

    position_dict = {
        "type": "long", "leverage": leverage, "strategy": "test_strat",
        "entry_regime": "Trending", "entry_time": entry_timestamp,
    }
    # Simulate entry fee deduction
    entry_fee = filled_size * entry_price * default_config.costs.taker_fee * leverage
    basic_portfolio.balance -= entry_fee
    position_dict["entry"] = entry_price
    position_dict["size"] = filled_size
    position_dict["entry_fee"] = entry_fee
    basic_portfolio.position = position_dict
    balance_after_entry = basic_portfolio.balance

    # --- Test Exit with large loss ---
    exit_price = 40000.0 # Large drop
    exit_reason = "stop_loss"
    exit_slippage = 0
    exit_timestamp = datetime.utcnow().timestamp()

    # Expect ValueError due to bankruptcy
    with pytest.raises(ValueError, match="Account liquidated on exit"):
         basic_portfolio.handle_exit(exit_price, exit_reason, exit_slippage, exit_timestamp)

    # Check state after bankruptcy exception
    assert basic_portfolio.balance == 0 # Balance should be exactly zero
    assert basic_portfolio.position is None # Position should be cleared
    assert len(basic_portfolio.trades) == 1 # Trade should still be recorded
    trade = basic_portfolio.trades[0]
    assert "BANKRUPTCY" in trade["exit_reason"]
    # Profit should be capped at the negative of balance before loss calc
    assert np.isclose(trade["profit"], -balance_after_entry)


# Add more tests: short entry, short exit loss, funding for short, edge cases...