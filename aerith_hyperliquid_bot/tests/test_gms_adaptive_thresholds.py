"""
Unit tests for GMS detector with adaptive thresholds.

Tests the integration of AdaptiveThreshold with the ContinuousGMSDetector.
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch
from hyperliquid_bot.core.gms_detector import ContinuousGMSDetector
from hyperliquid_bot.config.settings import Config


class TestGMSAdaptiveThresholds:
    """Test cases for GMS detector with adaptive thresholds."""
    
    @pytest.fixture
    def mock_config_adaptive(self):
        """Create a mock config with adaptive thresholds enabled."""
        config = Mock(spec=Config)
        
        # Basic config sections
        config.regime = Mock()
        config.regime.use_filter = True
        config.regime.detector_type = 'continuous_gms'
        config.regime.get_detector_settings = Mock(return_value={
            'gms_vol_high_thresh': 0.06,
            'gms_vol_low_thresh': 0.02,
            'gms_mom_strong_thresh': 5.0,
            'gms_mom_weak_thresh': 1.0,
            'gms_spread_std_high_thresh': 0.0005,
            'gms_spread_mean_low_thresh': 0.0001,
        })
        config.regime.get_detector_operational_settings = Mock(return_value={
            'cadence_sec': 60,
            'output_states': 8,
            'risk_suppressed_notional_frac': 0.25,
            'risk_suppressed_pnl_atr_mult': 1.5,
        })
        config.regime.gms_vol_thresh_mode = 'fixed'
        
        config.microstructure = Mock()
        config.microstructure.depth_levels = 5
        config.microstructure.gms_obi_strong_confirm_thresh = 0.2
        config.microstructure.gms_obi_weak_confirm_thresh = 0.05
        
        config.indicators = Mock()
        config.indicators.adx_threshold = 30.0
        
        config.portfolio = Mock()
        config.portfolio.initial_balance = 10000
        config.portfolio.max_leverage = 10
        
        # GMS section with adaptive thresholds enabled
        config.gms = Mock()
        config.gms.auto_thresholds = True
        config.gms.percentile_window_sec = 1000  # Smaller window for testing
        config.gms.vol_low_pct = 0.15
        config.gms.vol_high_pct = 0.50
        config.gms.mom_low_pct = 0.15
        config.gms.mom_high_pct = 0.50
        config.gms.min_history_rows = 10  # Small for testing
        config.gms.detector_type = 'continuous_gms'
        
        return config
        
    @pytest.fixture
    def mock_config_static(self):
        """Create a mock config with static thresholds (adaptive disabled)."""
        config = Mock(spec=Config)
        
        # Basic config sections
        config.regime = Mock()
        config.regime.use_filter = True
        config.regime.detector_type = 'continuous_gms'
        config.regime.get_detector_settings = Mock(return_value={
            'gms_vol_high_thresh': 0.06,
            'gms_vol_low_thresh': 0.02,
            'gms_mom_strong_thresh': 5.0,
            'gms_mom_weak_thresh': 1.0,
            'gms_spread_std_high_thresh': 0.0005,
            'gms_spread_mean_low_thresh': 0.0001,
        })
        config.regime.get_detector_operational_settings = Mock(return_value={
            'cadence_sec': 60,
            'output_states': 8,
            'risk_suppressed_notional_frac': 0.25,
            'risk_suppressed_pnl_atr_mult': 1.5,
        })
        config.regime.gms_vol_thresh_mode = 'fixed'
        
        config.microstructure = Mock()
        config.microstructure.depth_levels = 5
        config.microstructure.gms_obi_strong_confirm_thresh = 0.2
        config.microstructure.gms_obi_weak_confirm_thresh = 0.05
        
        config.indicators = Mock()
        config.indicators.adx_threshold = 30.0
        
        config.portfolio = Mock()
        config.portfolio.initial_balance = 10000
        config.portfolio.max_leverage = 10
        
        # GMS section with adaptive thresholds disabled
        config.gms = Mock()
        config.gms.auto_thresholds = False
        config.gms.detector_type = 'continuous_gms'
        
        return config
        
    def test_adaptive_threshold_initialization(self, mock_config_adaptive):
        """Test that adaptive thresholds are properly initialized when enabled."""
        detector = ContinuousGMSDetector(mock_config_adaptive)
        
        # Should have adaptive threshold instances
        assert detector.adaptive_vol_threshold is not None
        assert detector.adaptive_mom_threshold is not None
        
        # Check configuration
        assert detector.adaptive_vol_threshold.low_pct == 0.15
        assert detector.adaptive_vol_threshold.high_pct == 0.50
        assert detector.adaptive_mom_threshold.low_pct == 0.15
        assert detector.adaptive_mom_threshold.high_pct == 0.50
        assert detector.min_history_rows == 10
        
    def test_static_threshold_initialization(self, mock_config_static):
        """Test that adaptive thresholds are not initialized when disabled."""
        detector = ContinuousGMSDetector(mock_config_static)
        
        # Should not have adaptive threshold instances
        assert detector.adaptive_vol_threshold is None
        assert detector.adaptive_mom_threshold is None
        
    def test_adaptive_threshold_warm_up(self, mock_config_adaptive):
        """Test behavior during adaptive threshold warm-up period."""
        detector = ContinuousGMSDetector(mock_config_adaptive)
        
        # Create test signals
        signals = {
            'timestamp': 1234567890,
            'atr_percent_sec': 0.03,
            'atr_percent': 0.03,
            'ma_slope': 2.0,
            'obi_smoothed_5': 0.1,
            'spread_mean': 0.0001,
            'spread_std': 0.0003,
            'close': 50000.0,
            'atr_14_sec': 500.0,
            'atr': 500.0,
            'unrealised_pnl': 0.0
        }
        
        # During warm-up, should use static thresholds
        with patch.object(detector, '_determine_state') as mock_determine:
            mock_determine.return_value = 'Uncertain'
            detector.update(signals)
            
        # Verify adaptive thresholds were created but buffer is small
        assert detector.adaptive_vol_threshold.get_buffer_size() == 1
        assert detector.adaptive_mom_threshold.get_buffer_size() == 1
        
    def test_adaptive_threshold_after_warm_up(self, mock_config_adaptive):
        """Test adaptive threshold behavior after sufficient data."""
        detector = ContinuousGMSDetector(mock_config_adaptive)
        
        # Add enough data to exceed min_history_rows
        base_signals = {
            'timestamp': 1234567890,
            'atr_percent_sec': 0.03,
            'atr_percent': 0.03,
            'ma_slope': 2.0,
            'obi_smoothed_5': 0.1,
            'spread_mean': 0.0001,
            'spread_std': 0.0003,
            'close': 50000.0,
            'atr_14_sec': 500.0,
            'atr': 500.0,
            'unrealised_pnl': 0.0
        }
        
        # Add varying volatility and momentum data
        for i in range(15):  # More than min_history_rows (10)
            signals = base_signals.copy()
            signals['atr_percent_sec'] = 0.01 + (i * 0.001)  # Increasing volatility
            signals['atr_percent'] = signals['atr_percent_sec']
            signals['ma_slope'] = 1.0 + (i * 0.1)  # Increasing momentum
            signals['timestamp'] += i
            
            with patch.object(detector, '_determine_state') as mock_determine:
                mock_determine.return_value = 'Uncertain'
                detector.update(signals)
                
        # Verify buffers have sufficient data
        assert detector.adaptive_vol_threshold.get_buffer_size() >= detector.min_history_rows
        assert detector.adaptive_mom_threshold.get_buffer_size() >= detector.min_history_rows
        
        # Get current adaptive thresholds
        vol_low, vol_high = detector.adaptive_vol_threshold.update(0.02)
        mom_low, mom_high = detector.adaptive_mom_threshold.update(2.0)
        
        assert vol_low is not None
        assert vol_high is not None
        assert mom_low is not None
        assert mom_high is not None
        
        # Thresholds should be different from static defaults
        # (This depends on the data we fed in)
        
    def test_adaptive_supersedes_percentile_mode(self, mock_config_adaptive):
        """Test that adaptive mode supersedes percentile mode."""
        # Set percentile mode but adaptive should override
        mock_config_adaptive.regime.gms_vol_thresh_mode = 'percentile'
        
        detector = ContinuousGMSDetector(mock_config_adaptive)
        
        # Should still have adaptive thresholds (supersedes percentile)
        assert detector.adaptive_vol_threshold is not None
        assert detector.adaptive_mom_threshold is not None
        
    def test_logging_shows_adaptive_mode(self, mock_config_adaptive, caplog):
        """Test that logging indicates adaptive mode is active."""
        detector = ContinuousGMSDetector(mock_config_adaptive)
        
        # Check that initialization log shows adaptive mode
        assert "Threshold Mode: ADAPTIVE" in caplog.text
        assert "auto_thresholds=True" in caplog.text
        assert "Adaptive Window: 1000 seconds" in caplog.text
        
    def test_fallback_to_static_on_error(self, mock_config_adaptive):
        """Test fallback to static thresholds when adaptive calculation fails."""
        detector = ContinuousGMSDetector(mock_config_adaptive)
        
        # Mock adaptive threshold to return None (simulating error)
        with patch.object(detector.adaptive_vol_threshold, 'update', return_value=(None, None)):
            signals = {
                'timestamp': 1234567890,
                'atr_percent_sec': 0.03,
                'atr_percent': 0.03,
                'ma_slope': 2.0,
                'obi_smoothed_5': 0.1,
                'spread_mean': 0.0001,
                'spread_std': 0.0003,
                'close': 50000.0,
                'atr_14_sec': 500.0,
                'atr': 500.0,
                'unrealised_pnl': 0.0
            }
            
            # Should not raise exception and should fall back to static
            with patch.object(detector, '_determine_state') as mock_determine:
                mock_determine.return_value = 'Uncertain'
                detector.update(signals)  # Should not raise
                
    def test_nan_handling_in_adaptive_mode(self, mock_config_adaptive):
        """Test that NaN values are handled properly in adaptive mode."""
        detector = ContinuousGMSDetector(mock_config_adaptive)
        
        signals = {
            'timestamp': 1234567890,
            'atr_percent_sec': np.nan,  # NaN volatility
            'atr_percent': np.nan,
            'ma_slope': np.nan,  # NaN momentum
            'obi_smoothed_5': 0.1,
            'spread_mean': 0.0001,
            'spread_std': 0.0003,
            'close': 50000.0,
            'atr_14_sec': 500.0,
            'atr': 500.0,
            'unrealised_pnl': 0.0
        }
        
        # Should handle NaN gracefully
        with patch.object(detector, '_determine_state') as mock_determine:
            mock_determine.return_value = 'Unknown'
            detector.update(signals)  # Should not raise
