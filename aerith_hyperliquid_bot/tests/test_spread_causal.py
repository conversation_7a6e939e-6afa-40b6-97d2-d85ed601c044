"""
Unit tests for Task R-112i: Causal spread-stat fix (no look-ahead)

Tests to verify that spread statistics are calculated without look-ahead bias
and that NaN ratios are kept low.
"""

import numpy as np
import pandas as pd
import pytest
from pathlib import Path
import sys

# Add project root to path for imports
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from tools.etl_l20_to_1s import calculate_post_resample_features
from hyperliquid_bot.config.settings import load_config


class TestSpreadCausal:
    """Test suite for causal spread statistics calculation."""

    def test_no_look_ahead_bias(self):
        """
        Test that rolling spread mean calculation is truly causal (no look-ahead).

        Creates a synthetic spread series [1,2,3,...] and verifies that
        spread_mean[t] equals np.mean(series[max(0,t-59):t+1]).
        """
        # Create synthetic spread series
        n_points = 200
        spread_series = np.arange(1, n_points + 1, dtype=float)

        # Create DataFrame with required columns
        df = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=n_points, freq='1s'),
            'spread': spread_series,
            'mid_price': 50000.0,  # Constant mid price
            'close': 50000.0,
            'high': 50000.0,
            'low': 50000.0,
            'realised_vol_1s': 0.001,
            'atr_14_sec': 10.0,
            'atr': 10.0,
            'atr_percent': 0.0002,
            'atr_percent_sec': 0.0002,
            'unrealised_pnl': 0.0
        })

        # Calculate spread statistics using ETL function
        result_df = calculate_post_resample_features(df.copy())

        # Verify no look-ahead bias for each timestamp
        window = 60
        for t in range(len(result_df)):
            # Calculate expected causal mean: mean of series[max(0,t-59):t+1]
            start_idx = max(0, t - window + 1)
            end_idx = t + 1
            expected_mean = np.mean(spread_series[start_idx:end_idx])

            actual_mean = result_df['spread_mean'].iloc[t]

            # Allow small floating point differences
            assert abs(actual_mean - expected_mean) < 1e-10, (
                f"Look-ahead bias detected at t={t}: "
                f"expected={expected_mean}, actual={actual_mean}, "
                f"window=[{start_idx}:{end_idx}]"
            )

        print("✓ No look-ahead bias test passed")

    def test_nan_ratio_low(self):
        """
        Test that NaN ratio in spread_mean is less than 1% for real data.

        This test requires freshly processed 2025-03-05 hourly feature files.
        If the files don't exist, the test will be skipped.
        """
        # Load config to get data paths
        config = load_config('configs/base.yaml')

        # Path to 2025-03-05 feature directory (contains hourly files)
        feature_dir = Path(config.data_paths.feature_1s_dir) / "2025-03-05"

        if not feature_dir.exists():
            pytest.skip(f"Feature directory not found: {feature_dir}")

        # Find all hourly feature files
        feature_files = list(feature_dir.glob("features_*.parquet"))

        if not feature_files:
            pytest.skip(f"No feature files found in: {feature_dir}")

        # Load and combine all hourly files
        all_data = []
        for feature_file in sorted(feature_files):
            df_hour = pd.read_parquet(feature_file)
            all_data.append(df_hour)

        # Combine all hourly data
        df = pd.concat(all_data, ignore_index=True)

        # Check that spread_mean column exists
        assert 'spread_mean' in df.columns, "spread_mean column not found in processed data"

        # Calculate NaN ratio
        nan_ratio = df['spread_mean'].isna().mean()

        # Assert NaN ratio is less than 1%
        assert nan_ratio < 0.01, (
            f"NaN ratio too high: {nan_ratio:.3f} >= 0.01 "
            f"({df['spread_mean'].isna().sum()} NaNs out of {len(df)} rows)"
        )

        print(f"✓ NaN ratio test passed: {nan_ratio:.3f} < 0.01 ({len(feature_files)} hourly files, {len(df)} total rows)")

    def test_spread_calculation_with_ffill(self):
        """
        Test that forward-filling bid/ask prices eliminates NaN spreads.
        """
        # Create test data with some NaN bid/ask prices
        df = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=10, freq='1s'),
            'bid_price_1': [100.0, np.nan, np.nan, 101.0, 102.0, np.nan, 103.0, 104.0, np.nan, 105.0],
            'ask_price_1': [100.1, np.nan, np.nan, 101.1, 102.1, np.nan, 103.1, 104.1, np.nan, 105.1],
            'bid_size_1': 10.0,
            'ask_size_1': 10.0
        })

        # Forward-fill as done in ETL
        df[['bid_price_1', 'ask_price_1']] = df[['bid_price_1', 'ask_price_1']].ffill()

        # Calculate spread
        df['spread'] = df['ask_price_1'] - df['bid_price_1']

        # Check that no NaN spreads remain after ffill (except possibly the first row)
        # Skip first row in case it starts with NaN
        spread_nans = df['spread'].iloc[1:].isna().sum()
        assert spread_nans == 0, f"Found {spread_nans} NaN spreads after forward-fill"

        # Check that spread values are reasonable (0.1 in this case)
        valid_spreads = df['spread'].dropna()
        assert all(abs(s - 0.1) < 1e-10 for s in valid_spreads), "Spread values incorrect after ffill"

        print("✓ Forward-fill spread calculation test passed")

    def test_rolling_window_causal_properties(self):
        """
        Test that rolling window with min_periods=1 is truly causal.
        """
        # Create simple test series
        series = pd.Series([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])

        # Calculate rolling mean with window=3, min_periods=1 (causal)
        rolling_mean = series.rolling(window=3, min_periods=1).mean()

        # Verify expected values
        expected = [
            1.0,        # mean([1])
            1.5,        # mean([1, 2])
            2.0,        # mean([1, 2, 3])
            3.0,        # mean([2, 3, 4])
            4.0,        # mean([3, 4, 5])
            5.0,        # mean([4, 5, 6])
            6.0,        # mean([5, 6, 7])
            7.0,        # mean([6, 7, 8])
            8.0,        # mean([7, 8, 9])
            9.0         # mean([8, 9, 10])
        ]

        for i, (actual, exp) in enumerate(zip(rolling_mean, expected)):
            assert abs(actual - exp) < 1e-10, f"Mismatch at index {i}: {actual} != {exp}"

        print("✓ Rolling window causal properties test passed")


if __name__ == "__main__":
    # Run tests directly
    test_suite = TestSpreadCausal()

    print("Running spread causal tests...")
    test_suite.test_no_look_ahead_bias()
    test_suite.test_spread_calculation_with_ffill()
    test_suite.test_rolling_window_causal_properties()
    test_suite.test_nan_ratio_low()
    print("All tests passed!")
