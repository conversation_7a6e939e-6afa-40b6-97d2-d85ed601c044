"""
Test Modern Continuous Detector
================================

This test suite verifies the modern continuous detector with:
- 60-second update cadence
- Regime state manager integration
- State determination logic
- Look-ahead prevention
"""

import unittest
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.modern.continuous_detector import ModernContinuousDetector
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.utils.state_mapping import (
    GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BULL_TREND,
    GMS_STATE_HIGH_VOL_RANGE, GMS_STATE_LOW_VOL_RANGE,
    GMS_STATE_UNCERTAIN, GMS_STATE_WEAK_BEAR_TREND,
    GMS_STATE_STRONG_BEAR_TREND, GMS_STATE_TIGHT_SPREAD,
    GMS_STATE_UNKNOWN
)


class TestModernContinuousDetector(unittest.TestCase):
    """Test suite for modern continuous detector."""
    
    def setUp(self):
        """Set up test environment."""
        # Load config
        self.config = load_config()
        
        # Create detector in backtest mode
        self.detector = ModernContinuousDetector(self.config, mode="backtest")
        
        # Base time for tests
        self.base_time = datetime(2024, 1, 1, 10, 0, 0)
        
        # Create base signals
        self.base_signals = {
            'timestamp': self.base_time,
            'close': 100.0,
            'high': 101.0,
            'low': 99.0,
            'volume': 1000.0,
            'volume_imbalance': 0.0,
            'spread_mean': 0.0002,
            'spread_std': 0.0001,
            'atr_percent_sec': 0.03,
            'ma_slope_ema_30s': 0.0,
            'atr_14_sec': 1.0,
            'unrealised_pnl': 0.0
        }
    
    def test_initialization(self):
        """Test detector initializes correctly."""
        self.assertEqual(self.detector.mode, "backtest")
        self.assertEqual(self.detector.cadence_sec, 60)
        self.assertTrue(self.detector.disable_risk_suppression)
        self.assertIsNotNone(self.detector.state_manager)
    
    def test_60s_update_cadence(self):
        """Test that detector only updates every 60 seconds."""
        # First update should work
        result1 = self.detector.update(self.base_signals, self.base_time)
        self.assertIsNotNone(result1)
        
        # Update 30s later should be skipped
        time_30s = self.base_time + timedelta(seconds=30)
        signals_30s = self.base_signals.copy()
        signals_30s['timestamp'] = time_30s
        
        result2 = self.detector.update(signals_30s, time_30s)
        self.assertIsNone(result2)
        
        # Update 60s later should work
        time_60s = self.base_time + timedelta(seconds=60)
        signals_60s = self.base_signals.copy()
        signals_60s['timestamp'] = time_60s
        
        result3 = self.detector.update(signals_60s, time_60s)
        self.assertIsNotNone(result3)
    
    def test_strong_bull_detection(self):
        """Test detection of strong bull trend."""
        signals = self.base_signals.copy()
        signals.update({
            'ma_slope_ema_30s': 120.0,  # Strong positive momentum (> 100.0)
            'volume_imbalance': 0.25,   # Strong positive OBI (> 0.2)
            'atr_percent_sec': 0.7,     # Normal volatility (between 0.55 and 0.92)
            'spread_std': 0.00003       # Low spread volatility
        })
        
        state = self.detector._determine_state(signals)
        self.assertEqual(state, GMS_STATE_STRONG_BULL_TREND)
    
    def test_weak_bear_detection(self):
        """Test detection of weak bear trend."""
        signals = self.base_signals.copy()
        signals.update({
            'ma_slope_ema_30s': -60.0,  # Weak negative momentum (> 50.0 in abs)
            'volume_imbalance': -0.12,  # Weak negative OBI (> 0.11 in abs)
            'atr_percent_sec': 0.7,     # Normal volatility
            'spread_std': 0.00004
        })
        
        state = self.detector._determine_state(signals)
        self.assertEqual(state, GMS_STATE_WEAK_BEAR_TREND)
    
    def test_high_vol_range_detection(self):
        """Test detection of high volatility range."""
        signals = self.base_signals.copy()
        signals.update({
            'atr_percent_sec': 0.95,   # High volatility (> 0.92)
            'ma_slope_ema_30s': 30.0,  # Weak momentum (< 50.0)
            'spread_std': 0.00006,     # High spread volatility (> 0.00005)
            'volume_imbalance': 0.02
        })
        
        state = self.detector._determine_state(signals)
        self.assertEqual(state, GMS_STATE_HIGH_VOL_RANGE)
    
    def test_low_vol_range_detection(self):
        """Test detection of low volatility range."""
        signals = self.base_signals.copy()
        signals.update({
            'atr_percent_sec': 0.5,     # Low volatility (< 0.55)
            'ma_slope_ema_30s': 30.0,   # Very weak momentum (< 50.0)
            'spread_mean': 0.00004,     # Tight spread (< 0.000045)
            'volume_imbalance': 0.01
        })
        
        state = self.detector._determine_state(signals)
        self.assertEqual(state, GMS_STATE_LOW_VOL_RANGE)
    
    def test_tight_spread_detection(self):
        """Test detection of tight spread condition."""
        signals = self.base_signals.copy()
        signals.update({
            'spread_mean': 0.00004,     # Very tight spread (< 0.000045)
            'ma_slope_ema_30s': 30.0,   # Weak momentum (< 50.0)
            'atr_percent_sec': 0.7,     # Normal volatility
            'volume_imbalance': 0.03
        })
        
        state = self.detector._determine_state(signals)
        self.assertEqual(state, GMS_STATE_TIGHT_SPREAD)
    
    def test_confidence_calculation(self):
        """Test confidence calculation for different states."""
        # Strong trend should have high confidence with strong signals
        signals_strong = self.base_signals.copy()
        signals_strong.update({
            'ma_slope_ema_30s': 120.0,  # Strong momentum
            'volume_imbalance': 0.3,    # Strong OBI
            'atr_percent_sec': 0.7      # Normal volatility
        })
        
        confidence_strong = self.detector._calculate_confidence(
            signals_strong, GMS_STATE_STRONG_BULL_TREND
        )
        self.assertGreater(confidence_strong, 0.7)
        
        # Range state with matching volatility
        signals_range = self.base_signals.copy()
        signals_range.update({
            'atr_percent_sec': 0.95,    # High volatility
            'ma_slope_ema_30s': 30.0    # Low momentum
        })
        
        confidence_range = self.detector._calculate_confidence(
            signals_range, GMS_STATE_HIGH_VOL_RANGE
        )
        self.assertGreater(confidence_range, 0.6)
    
    def test_state_manager_integration(self):
        """Test integration with regime state manager."""
        # Update detector multiple times
        for i in range(5):
            time_i = self.base_time + timedelta(seconds=i * 60)
            signals_i = self.base_signals.copy()
            signals_i['timestamp'] = time_i
            signals_i['ma_slope_ema_30s'] = 50.0 + i * 10.0  # Increasing momentum
            signals_i['volume_imbalance'] = 0.1 + i * 0.02
            signals_i['atr_percent_sec'] = 0.7  # Normal volatility
            
            result = self.detector.update(signals_i, time_i)
            if result:  # Should update every 60s
                self.assertIn('state', result)
                self.assertIn('confidence', result)
                self.assertIn('features', result)
        
        # Check state history
        history = self.detector.get_state_history(
            self.base_time + timedelta(minutes=10),
            hours=1
        )
        
        self.assertGreater(len(history), 0)
        self.assertLessEqual(len(history), 5)
    
    def test_risk_suppression_disabled_in_backtest(self):
        """Test that risk suppression is disabled in backtest mode."""
        signals = self.base_signals.copy()
        signals['close'] = 1000000.0  # Very high notional
        signals['unrealised_pnl'] = 1000.0  # High PnL
        
        risk_suppressed = self.detector._calculate_risk_suppressed(signals)
        self.assertFalse(risk_suppressed)  # Should be disabled
    
    def test_missing_signal_handling(self):
        """Test handling of missing signals."""
        signals = self.base_signals.copy()
        # Remove critical signal
        del signals['volume_imbalance']
        
        state = self.detector._determine_state(signals)
        self.assertEqual(state, GMS_STATE_UNKNOWN)
    
    def test_reset_functionality(self):
        """Test detector reset."""
        # Add some state
        self.detector.update(self.base_signals, self.base_time)
        
        # Reset
        self.detector.reset()
        
        # Check state is cleared
        self.assertEqual(self.detector.current_state, GMS_STATE_UNCERTAIN)
        self.assertIsNone(self.detector.last_update_time)
        self.assertFalse(self.detector.risk_suppressed)
        
        # State manager should be cleared
        current = self.detector.state_manager.get_current_state()
        self.assertIsNone(current)
    
    def test_interface_methods(self):
        """Test interface methods for compatibility."""
        # Test detect_regime
        state = self.detector.detect_regime(self.base_signals)
        self.assertIsInstance(state, str)
        
        # Test get_allowed_states
        trend_states = self.detector.get_allowed_states('trend_following')
        self.assertIn(GMS_STATE_STRONG_BULL_TREND, trend_states)
        self.assertIn(GMS_STATE_WEAK_BEAR_TREND, trend_states)
        
        range_states = self.detector.get_allowed_states('mean_reversion')
        self.assertIn(GMS_STATE_HIGH_VOL_RANGE, range_states)
        self.assertIn(GMS_STATE_LOW_VOL_RANGE, range_states)
        
        # Test get_confidence
        confidence = self.detector.get_confidence()
        self.assertGreaterEqual(confidence, 0.0)
        self.assertLessEqual(confidence, 1.0)
        
        # Test get_raw_state
        raw_state = self.detector.get_raw_state(self.base_signals)
        self.assertIn('state', raw_state)
        self.assertIn('confidence', raw_state)
        self.assertIn('risk_suppressed', raw_state)


if __name__ == '__main__':
    unittest.main()