# tests/test_config_backtest_flag.py

import pytest
import tempfile
import yaml
from pathlib import Path
from pydantic import ValidationError

from hyperliquid_bot.config.settings import Config, PortfolioSettings


class TestConfigBacktestFlag:
    """Test the new is_backtest flag and portfolio configuration schema."""

    def test_load_base_yaml_is_backtest_false(self):
        """Test that loading base.yaml results in is_backtest being False."""
        # Load the actual base.yaml configuration
        config_path = Path(__file__).parent.parent / "configs" / "base.yaml"
        
        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f)
        
        config = Config(**config_data)
        assert config.is_backtest is False

    def test_toggle_is_backtest_to_true(self):
        """Test that we can toggle is_backtest to True in a temporary YAML."""
        # Create a minimal config with is_backtest set to True
        minimal_config = {
            'is_backtest': True,
            'data_paths': {
                'l2_data_root': '/tmp/test',
                'raw_l2_dir': '/tmp/test',
                'feature_1s_dir': '/tmp/test',
                'ohlcv_base_path': '/tmp/test',
                'log_dir': '/tmp/test'
            },
            'cache': {'l2_cache_max_size': 24},
            'backtest': {
                'period_preset': 'custom',
                'custom_start_date': '2025-03-01',
                'custom_end_date': '2025-03-02'
            },
            'simulation': {},
            'strategies': {},
            'portfolio': {
                'initial_balance': 10000,
                'risk_per_trade': 0.02,
                'max_leverage': 10.0,
                'asset_max_leverage': 50.0,
                'margin_mode': 'cross',
                'max_hold_time_hours': 24
            },
            'costs': {
                'taker_fee': 0.000315,
                'maker_fee': 0.00009,
                'l2_penalty_factor': 1.005
            },
            'regime': {},
            'microstructure': {},
            'indicators': {},
            'analysis': {},
            'visualization': {}
        }
        
        # Create temporary directories for the test
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            for path_key in ['l2_data_root', 'raw_l2_dir', 'feature_1s_dir', 'ohlcv_base_path', 'log_dir']:
                minimal_config['data_paths'][path_key] = str(temp_path)
                temp_path.mkdir(exist_ok=True)
            
            config = Config(**minimal_config)
            assert config.is_backtest is True

    def test_missing_required_portfolio_key_raises_validation_error(self):
        """Test that missing required portfolio keys raise ValidationError."""
        incomplete_portfolio = {
            'initial_balance': 10000,
            'risk_per_trade': 0.02,
            # Missing max_leverage, asset_max_leverage, max_hold_time_hours
        }
        
        with pytest.raises(ValidationError) as exc_info:
            PortfolioSettings(**incomplete_portfolio)
        
        # Check that the error mentions the missing required fields
        error_str = str(exc_info.value)
        assert 'max_leverage' in error_str or 'asset_max_leverage' in error_str or 'max_hold_time_hours' in error_str

    def test_portfolio_optional_guards_default_to_zero(self):
        """Test that optional guard fields default to 0."""
        minimal_portfolio = {
            'initial_balance': 10000,
            'risk_per_trade': 0.02,
            'max_leverage': 10.0,
            'asset_max_leverage': 50.0,
            'max_hold_time_hours': 24
        }
        
        portfolio = PortfolioSettings(**minimal_portfolio)
        
        # Check that optional guards default to 0
        assert portfolio.max_notional == 0
        assert portfolio.min_trade_size == 0
        assert portfolio.leverage_guard_pct == 0

    def test_portfolio_preserves_existing_values(self):
        """Test that existing portfolio values are preserved."""
        portfolio_data = {
            'initial_balance': 10000,
            'risk_per_trade': 0.02,
            'max_leverage': 10.0,
            'asset_max_leverage': 50.0,
            'margin_mode': 'cross',
            'max_hold_time_hours': 24,
            'max_notional': 50000,  # Non-zero value
            'min_trade_size': 100,   # Non-zero value
            'leverage_guard_pct': 5  # Non-zero value
        }
        
        portfolio = PortfolioSettings(**portfolio_data)
        
        # Check that all values are preserved
        assert portfolio.initial_balance == 10000
        assert portfolio.risk_per_trade == 0.02
        assert portfolio.max_leverage == 10.0
        assert portfolio.asset_max_leverage == 50.0
        assert portfolio.margin_mode == 'cross'
        assert portfolio.max_hold_time_hours == 24
        assert portfolio.max_notional == 50000
        assert portfolio.min_trade_size == 100
        assert portfolio.leverage_guard_pct == 5

    def test_config_derived_properties_work(self):
        """Test that derived properties still work with the new portfolio structure."""
        # Create a minimal config
        minimal_config = {
            'is_backtest': False,
            'data_paths': {
                'l2_data_root': '/tmp/test',
                'raw_l2_dir': '/tmp/test', 
                'feature_1s_dir': '/tmp/test',
                'ohlcv_base_path': '/tmp/test',
                'log_dir': '/tmp/test'
            },
            'cache': {'l2_cache_max_size': 24},
            'backtest': {
                'period_preset': 'custom',
                'custom_start_date': '2025-03-01',
                'custom_end_date': '2025-03-02'
            },
            'simulation': {},
            'strategies': {},
            'portfolio': {
                'initial_balance': 10000,
                'risk_per_trade': 0.02,
                'max_leverage': 10.0,
                'asset_max_leverage': 50.0,
                'margin_mode': 'cross',
                'max_hold_time_hours': 48  # 48 hours for testing
            },
            'costs': {
                'taker_fee': 0.000315,
                'maker_fee': 0.00009,
                'l2_penalty_factor': 1.005,
                'funding_hours': 8
            },
            'regime': {},
            'microstructure': {},
            'indicators': {},
            'analysis': {},
            'visualization': {}
        }
        
        # Create temporary directories for the test
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            for path_key in ['l2_data_root', 'raw_l2_dir', 'feature_1s_dir', 'ohlcv_base_path', 'log_dir']:
                minimal_config['data_paths'][path_key] = str(temp_path)
                temp_path.mkdir(exist_ok=True)
            
            config = Config(**minimal_config)
            
            # Test derived properties
            assert config.MAX_HOLD_TIME_SECONDS == 48 * 3600  # 48 hours * 3600 seconds/hour
            assert config.FUNDING_INTERVAL_SECONDS == 8 * 3600  # 8 hours * 3600 seconds/hour
