"""
Unit tests for ma_slope_ema_30s builder function.

Tests the corrected EMA-based momentum calculation for continuous GMS detector.
"""

import pytest
import pandas as pd
import numpy as np
from hyperliquid_bot.features.builder_registry import build_ma_slope_ema_30s


class TestMaSlopeEma30s:
    """Test cases for the ma_slope_ema_30s builder function."""

    def test_synthetic_ramp_data(self):
        """Test with synthetic mid_price ramp from 1 to 2 over 120 seconds."""
        # Generate synthetic data: linear ramp from 1 to 2 over 120 seconds
        timestamps = pd.date_range('2025-01-01', periods=120, freq='1s')
        mid_prices = np.linspace(1.0, 2.0, 120)  # Linear ramp
        
        df = pd.DataFrame({
            'timestamp': timestamps,
            'mid_price': mid_prices
        })
        
        # Calculate ma_slope_ema_30s
        result = build_ma_slope_ema_30s(df, span=30)
        
        # Basic checks
        assert len(result) == 120
        assert result.name == 'ma_slope_ema_30s'
        assert result.dtype == 'float64'
        
        # First value should be NaN (due to diff calculation)
        assert pd.isna(result.iloc[0])
        
        # After EMA warmup, should have positive slope values
        # For a linear ramp, the percentage change should be roughly consistent
        non_nan_values = result.dropna()
        assert len(non_nan_values) > 100  # Most values should be non-NaN
        
        # For an upward ramp, all non-NaN slopes should be positive
        assert (non_nan_values > 0).all()
        
        # Calculate expected median slope for validation
        # Linear ramp from 1 to 2 over 120s = ~0.83% per second
        # But EMA smoothing will affect this, so we check order of magnitude
        median_slope = non_nan_values.median()
        assert 0.01 < median_slope < 2.0  # Reasonable range for percentage change

    def test_causal_calculation(self):
        """Test that calculation is causal (no look-ahead bias)."""
        # Create test data with a step change at position 60
        timestamps = pd.date_range('2025-01-01', periods=120, freq='1s')
        mid_prices = np.ones(120) * 100.0
        mid_prices[60:] = 101.0  # Step up at position 60
        
        df = pd.DataFrame({
            'timestamp': timestamps,
            'mid_price': mid_prices
        })
        
        result = build_ma_slope_ema_30s(df, span=30)
        
        # Check that slope at position 30 only uses data from 0-30 window
        # This is verified by ensuring the calculation matches manual EMA calculation
        
        # Manual calculation for first 31 points (0-30)
        prices_subset = mid_prices[:31]
        ema_manual = pd.Series(prices_subset).ewm(span=30, adjust=False).mean()
        slope_manual = (ema_manual.diff() / ema_manual.shift(1)) * 100
        
        # Compare with our function result
        assert abs(result.iloc[30] - slope_manual.iloc[30]) < 1e-10

    def test_missing_price_column(self):
        """Test behavior when price column is missing."""
        df = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=10, freq='1s'),
            'other_col': range(10)
        })
        
        result = build_ma_slope_ema_30s(df)
        
        # Should return all NaN series with correct name
        assert len(result) == 10
        assert result.name == 'ma_slope_ema_30s'
        assert result.isna().all()

    def test_custom_price_column(self):
        """Test with custom price column name."""
        df = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=60, freq='1s'),
            'custom_price': np.linspace(100, 110, 60)
        })
        
        result = build_ma_slope_ema_30s(df, price_col='custom_price')
        
        # Should work with custom column
        assert len(result) == 60
        assert result.name == 'ma_slope_ema_30s'
        non_nan_count = result.notna().sum()
        assert non_nan_count > 50  # Most values should be valid

    def test_custom_span(self):
        """Test with custom EMA span."""
        df = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=100, freq='1s'),
            'mid_price': np.linspace(50, 60, 100)
        })
        
        result_30 = build_ma_slope_ema_30s(df, span=30)
        result_10 = build_ma_slope_ema_30s(df, span=10)
        
        # Different spans should produce different results
        # Shorter span should be more responsive (higher absolute values)
        non_nan_30 = result_30.dropna()
        non_nan_10 = result_10.dropna()
        
        # Both should have positive slopes for upward trend
        assert (non_nan_30 > 0).all()
        assert (non_nan_10 > 0).all()
        
        # Shorter span should generally have higher absolute values
        assert non_nan_10.median() > non_nan_30.median()

    def test_zero_price_handling(self):
        """Test handling of zero prices (should not cause division by zero)."""
        df = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=60, freq='1s'),
            'mid_price': [0.0] * 30 + list(np.linspace(1, 2, 30))
        })
        
        result = build_ma_slope_ema_30s(df)
        
        # Should not raise exception and should handle gracefully
        assert len(result) == 60
        # Some values might be inf or very large, but should not crash
        finite_values = result[np.isfinite(result)]
        assert len(finite_values) > 0

    def test_constant_price(self):
        """Test with constant price (should produce near-zero slopes)."""
        df = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=60, freq='1s'),
            'mid_price': [100.0] * 60
        })
        
        result = build_ma_slope_ema_30s(df)
        
        # After first NaN, slopes should be very close to zero
        non_nan_values = result.dropna()
        assert len(non_nan_values) > 50
        
        # All slopes should be very close to zero (within floating point precision)
        assert (abs(non_nan_values) < 1e-10).all()

    def test_dtype_consistency(self):
        """Test that output dtype is consistently float64."""
        df = pd.DataFrame({
            'timestamp': pd.date_range('2025-01-01', periods=30, freq='1s'),
            'mid_price': range(30)  # Integer input
        })
        
        result = build_ma_slope_ema_30s(df)
        
        # Should always be float64 regardless of input dtype
        assert result.dtype == 'float64'
