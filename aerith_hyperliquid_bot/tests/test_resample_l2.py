# File: tests/test_resample_l2.py

import pytest
import pandas as pd
import numpy as np
from pathlib import Path
import sys

# Add the script's directory to the Python path to allow importing
# Adjust the path depth ('..') if your tests folder is structured differently
SCRIPT_DIR = Path(__file__).resolve().parent.parent / "scripts"
sys.path.append(str(SCRIPT_DIR))

# Import the function we want to test
# Assuming the script is named resample_l2_to_ohlcv.py
try:
    from resample_l2_to_ohlcv import ohlc_from_snapshots
except ImportError:
    pytest.fail("Could not import ohlc_from_snapshots from scripts/resample_l2_to_ohlcv.py. "
                "Ensure the script exists and the path is correct.", pytrace=False)

# --- Test Fixture for Sample Input Data ---

@pytest.fixture(scope="module")
def sample_snapshot_data() -> pd.DataFrame:
    """Creates a sample DataFrame mimicking L2 snapshot data for testing."""
    # Create timestamps for roughly 2 days, every 10 seconds
    timestamps = pd.date_range(start="2024-01-01 00:00:00",
                               end="2024-01-02 23:59:59",
                               freq="10s", tz="UTC")
    data_len = len(timestamps)

    # Create somewhat realistic mid-price movements
    start_price = 42000
    price_changes = np.random.normal(loc=0, scale=5, size=data_len).cumsum()
    mid_prices = start_price + price_changes

    # Simulate small bid-ask spread
    spread = np.random.uniform(1, 5, size=data_len)
    bids = mid_prices - spread / 2
    asks = mid_prices + spread / 2

    data = {
        'timestamp': timestamps,
        'best_bid': bids,
        'best_ask': asks,
        # 'mid_price': mid_prices # Optionally test with mid_price directly
    }
    df = pd.DataFrame(data)
    df = df.set_index('timestamp') # Set index here as expected by the function
    return df

# --- Test Functions ---

@pytest.mark.parametrize("tf, expected_bars", [("1h", 48), ("4h", 12)]) # 2 days = 48h = 12*4h
def test_output_shape_and_frequency(sample_snapshot_data, tf, expected_bars):
    """Tests if the output has the expected number of bars for the timeframe."""
    ohlc_df = ohlc_from_snapshots(sample_snapshot_data.copy(), tf=tf) # Use copy

    # Check number of rows (bars) - should now match the full expected count
    assert len(ohlc_df) == expected_bars , \
           f"Expected {expected_bars} bars for {tf} over 2 days, got {len(ohlc_df)}"

    # Check frequency of the index
    if not ohlc_df.empty:
        # Compare lowercase to handle potential case differences from infer_freq
        inferred_freq = pd.infer_freq(ohlc_df.index)
        assert inferred_freq is not None, f"Could not infer frequency for {tf}"
        # Map input tf to expected pandas freq code for comparison
        # Example mapping, expand as needed
        tf_to_pandas_freq_map = {
            '1h': 'H',
            '4h': '4H',
            '1d': 'D' # Add other relevant mappings if used
        }
        expected_pandas_freq = tf_to_pandas_freq_map.get(tf.lower())
        assert expected_pandas_freq is not None, f"Test setup error: No pandas freq mapping for {tf}"
        # Compare inferred frequency case-insensitively with expected pandas freq code
        assert inferred_freq.lower() == expected_pandas_freq.lower()

@pytest.mark.parametrize("tf", ["1h", "4h"])
def test_ohlc_logic(sample_snapshot_data, tf):
    """Tests High >= Open/Close and Low <= Open/Close."""
    ohlc_df = ohlc_from_snapshots(sample_snapshot_data.copy(), tf=tf)
    if ohlc_df.empty:
        pytest.skip("Skipping OHLC logic test on empty DataFrame")

    assert (ohlc_df['high'] >= ohlc_df['open']).all()
    assert (ohlc_df['high'] >= ohlc_df['close']).all()
    assert (ohlc_df['low'] <= ohlc_df['open']).all()
    assert (ohlc_df['low'] <= ohlc_df['close']).all()

@pytest.mark.parametrize("tf", ["1h", "4h"])
def test_timestamp_integrity(sample_snapshot_data, tf):
    """Tests if the index is a monotonic UTC DatetimeIndex."""
    ohlc_df = ohlc_from_snapshots(sample_snapshot_data.copy(), tf=tf)

    assert isinstance(ohlc_df.index, pd.DatetimeIndex)
    assert str(ohlc_df.index.tz) == 'UTC'
    assert ohlc_df.index.is_monotonic_increasing

@pytest.mark.parametrize("tf", ["1h", "4h"])
def test_output_columns(sample_snapshot_data, tf):
    """Tests if the output DataFrame has the expected columns."""
    ohlc_df = ohlc_from_snapshots(sample_snapshot_data.copy(), tf=tf)
    expected_columns = ['open', 'high', 'low', 'close', 'log_ret', 'realised_vol']
    assert list(ohlc_df.columns) == expected_columns

@pytest.mark.parametrize("tf", ["1h", "4h"])
def test_log_return_nans(sample_snapshot_data, tf):
    """Tests if the first log_ret is NaN and others are calculated."""
    ohlc_df = ohlc_from_snapshots(sample_snapshot_data.copy(), tf=tf)
    if ohlc_df.empty:
        pytest.skip("Skipping log_ret test on empty DataFrame")

    # Removed the dropna step, so first should be NaN
    assert pd.isna(ohlc_df['log_ret'].iloc[0])
    # Ensure subsequent values are not all NaN (unless price didn't change)
    assert not pd.isna(ohlc_df['log_ret'].iloc[1:].dropna()).empty

@pytest.mark.parametrize("tf", ["1h", "4h"])
def test_volatility_nans(sample_snapshot_data, tf):
    """Tests if initial volatility values are NaN due to window size."""
    ohlc_df = ohlc_from_snapshots(sample_snapshot_data.copy(), tf=tf)
    if ohlc_df.empty:
        pytest.skip("Skipping volatility test on empty DataFrame")

    vol_window = 24 if tf == "1h" else 6
    # NaN period depends on min_periods used in rolling calculation
    # min_periods = max(1, window // 2)
    # We need min_periods log_ret values. Since log_ret starts at index 1,
    # the first valid vol is at index `min_periods`.
    min_p = max(1, vol_window // 2)
    expected_nan_count = min_p # Indices 0 to min_p-1 should be NaN

    assert pd.isna(ohlc_df['realised_vol'].iloc[:expected_nan_count]).all()
    if len(ohlc_df) > expected_nan_count:
         assert not pd.isna(ohlc_df['realised_vol'].iloc[expected_nan_count])

def test_empty_input():
    """Tests behavior with an empty input DataFrame."""
    empty_df = pd.DataFrame(columns=['timestamp', 'best_bid', 'best_ask'])
    empty_df['timestamp'] = pd.to_datetime(empty_df['timestamp'])
    empty_df = empty_df.set_index('timestamp').tz_localize('UTC') # Ensure correct structure

    ohlc_df = ohlc_from_snapshots(empty_df, tf="1h")
    assert ohlc_df.empty
    assert list(ohlc_df.columns) == ['open', 'high', 'low', 'close', 'log_ret', 'realised_vol']

def test_input_with_nans():
    """Tests handling of NaNs in input bid/ask columns."""
    timestamps = pd.date_range(start="2024-01-01 00:00:00", periods=100, freq="1min", tz="UTC")
    data = {
        'timestamp': timestamps,
        'best_bid': np.random.uniform(40000, 41000, 100),
        'best_ask': np.random.uniform(40000, 41000, 100),
    }
    df = pd.DataFrame(data)
    # Introduce NaNs
    df.loc[df.index[5], 'best_bid'] = np.nan
    df.loc[df.index[10], 'best_ask'] = np.nan
    df.loc[df.index[15], ['best_bid', 'best_ask']] = np.nan

    df = df.set_index('timestamp')
    ohlc_df = ohlc_from_snapshots(df.copy(), tf="1h")

    # Should still produce output, possibly with some bars missing if NaNs affected whole interval
    # Basic check: ensure it doesn't crash and output columns are correct
    assert not ohlc_df.empty # Assuming enough valid data remains
    assert list(ohlc_df.columns) == ['open', 'high', 'low', 'close', 'log_ret', 'realised_vol']