# tests/test_obi_scalper_stub.py

import unittest
from unittest.mock import MagicMock, patch
import pandas as pd

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.strategies.obi_scalper import OBIScalperStrategy

class TestOBIScalperStub(unittest.TestCase):
    """
    Test suite to verify the OBI Scalper Strategy stub implementation behaves correctly.
    These tests ensure that the skeleton implementation returns "no action" until logic is added.
    """

    def setUp(self):
        """Set up test environment before each test."""
        # Create a mock config that provides the necessary settings for OBIScalper
        self.mock_config = MagicMock(spec=Config)
        self.mock_config.microstructure = MagicMock()
        self.mock_config.microstructure.obi_levels = 5
        
        # Create the strategy instance
        self.strategy = OBIScalperStrategy(self.mock_config, "test_obi_scalper")
        
        # Reset counters
        self.strategy.eval_count = 0
        self.strategy.fail_condition = 0
        self.strategy.fail_missing_signal = 0
        self.strategy.success_entry_long = 0
        self.strategy.success_entry_short = 0

    def test_required_signals(self):
        """Test that the required signals match the expected list."""
        expected_signals = [
            "close",
            "regime",
            "obi_smoothed_5",
            "obi_zscore_5",
            "spread_relative"
        ]
        
        # Get required signals and check they match expected ones (regardless of order)
        required_signals = self.strategy.required_signals
        self.assertEqual(set(required_signals), set(expected_signals))

    def test_evaluate_with_missing_signals(self):
        """Test evaluation with missing signals returns None."""
        # Create signals dict with some missing required signals
        signals = {
            "close": 50000.0,
            "regime": "CHOP",
            # Missing OBI signals
        }
        
        # Call evaluate and check result
        direction, info = self.strategy.evaluate(signals)
        
        # Assert expected behavior
        self.assertIsNone(direction)
        self.assertIsNone(info)
        self.assertEqual(self.strategy.eval_count, 1)
        self.assertEqual(self.strategy.fail_missing_signal, 1)
        self.assertEqual(self.strategy.success_entry_long, 0)
        self.assertEqual(self.strategy.success_entry_short, 0)

    def test_evaluate_with_complete_signals(self):
        """
        Test evaluation with all required signals returns None
        (stub implementation should not generate entry signals yet).
        """
        # Create signals dict with all required signals
        signals = {
            "close": 50000.0,
            "regime": "CHOP",
            "obi_smoothed_5": 0.15,
            "obi_zscore_5": 1.2,
            "spread_relative": 0.0005
        }
        
        # Call evaluate and check result
        direction, info = self.strategy.evaluate(signals)
        
        # Assert expected behavior for stub implementation
        self.assertIsNone(direction)
        self.assertIsNone(info)
        self.assertEqual(self.strategy.eval_count, 1)
        self.assertEqual(self.strategy.fail_missing_signal, 0)
        self.assertEqual(self.strategy.fail_condition, 1)  # Should increment the condition counter
        self.assertEqual(self.strategy.success_entry_long, 0)
        self.assertEqual(self.strategy.success_entry_short, 0)

    def test_check_exit_returns_none(self):
        """Test that check_exit always returns None in stub implementation."""
        # Create mock signals and position
        signals = {
            "close": 50000.0,
            "regime": "CHOP",
            "obi_smoothed_5": 0.15
        }
        position = {
            "type": "long",
            "size": 1.0,
            "entry_price": 49000.0
        }
        
        # Call check_exit and verify result
        exit_reason = self.strategy.check_exit(signals, position)
        self.assertIsNone(exit_reason)

    def test_log_evaluation_summary(self):
        """Test that log_evaluation_summary runs without errors."""
        # Set up some counter values
        self.strategy.eval_count = 100
        self.strategy.fail_condition = 80
        self.strategy.fail_missing_signal = 10
        self.strategy.fail_obi_threshold = 5
        self.strategy.fail_spread_filter = 3
        self.strategy.fail_zscore_filter = 2
        self.strategy.success_entry_long = 5
        self.strategy.success_entry_short = 5
        
        # Mock logger to avoid actual logging during tests
        self.strategy.logger = MagicMock()
        
        # Call log_evaluation_summary and verify it runs without errors
        try:
            self.strategy.log_evaluation_summary()
            success = True
        except Exception as e:
            success = False
        
        self.assertTrue(success)
        # Verify logger was called at least once
        self.assertTrue(self.strategy.logger.info.called)


if __name__ == "__main__":
    unittest.main()
