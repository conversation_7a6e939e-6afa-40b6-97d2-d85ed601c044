import pytest
import time
from hyperliquid_bot.portfolio.portfolio import <PERSON><PERSON><PERSON>
from hyperliquid_bot.config.settings import load_config # Changed import
import os # Added for path joining

# Determine the correct path to base.yaml relative to the test file or project root
# Assuming tests are run from the 'aerith_hyperliquid_bot' directory
config_file_path = os.path.join(os.path.dirname(__file__), '..', 'configs', 'base.yaml')
# If tests are run from project root, it would be:
# config_file_path = "aerith_hyperliquid_bot/configs/base.yaml"
# For now, let's assume pytest runs from aerith_hyperliquid_bot, so the path is simpler:
config_file_path = "configs/base.yaml"

cfg = load_config(config_file_path) # Load config from base.yaml
p   = Portfolio(cfg)

def test_margin_lock_and_refund():
    ts = time.time()
    p.handle_entry(
        {"type":"long","leverage":10,"size":1},   # 1 coin
        fill_price      = 100.0,
        filled_size     = 1.0,
        slippage_pnl    = 0.0,
        timestamp_unix  = ts,
    )
    im = 100/10          # $10
    assert pytest.approx(p.reserved_margin) == im
    assert pytest.approx(p.get_free_balance()) == cfg.portfolio.initial_balance - im - (100*cfg.costs.taker_fee)

    p.handle_exit(
        fill_price      = 110.0,
        exit_reason     = "take_profit",
        slippage_pnl    = 0.0,
        timestamp_unix  = ts+60,
    )
    assert p.reserved_margin == 0.0
    assert p.get_free_balance() > cfg.portfolio.initial_balance      # trade made money
