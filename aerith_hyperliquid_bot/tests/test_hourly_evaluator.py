"""
Test Hourly Strategy Evaluator
==============================

This test suite verifies:
- Hourly evaluation timing
- Integration with regime manager
- Risk management application
- Strategy signal preparation
"""

import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.modern.hourly_evaluator import HourlyStrategyEvaluator
from hyperliquid_bot.modern.regime_state_manager import RegimeStateManager
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.utils.state_mapping import (
    GMS_STATE_STRONG_BULL_TREND, GMS_STATE_WEAK_BEAR_TREND,
    GMS_STATE_HIGH_VOL_RANGE, GMS_STATE_UNCERTAIN
)


class TestHourlyEvaluator(unittest.TestCase):
    """Test suite for hourly strategy evaluator."""
    
    def setUp(self):
        """Set up test environment."""
        # Load config
        self.config = load_config()
        
        # Create mock regime manager
        self.regime_manager = Mock(spec=RegimeStateManager)
        
        # Create mock strategy
        self.strategy = Mock()
        self.strategy.get_strategy_name.return_value = "test_strategy"
        self.strategy.get_allowed_states.return_value = [
            GMS_STATE_STRONG_BULL_TREND,
            GMS_STATE_WEAK_BEAR_TREND
        ]
        
        # Create evaluator
        self.evaluator = HourlyStrategyEvaluator(
            config=self.config,
            regime_manager=self.regime_manager,
            strategy=self.strategy,
            mode="backtest"
        )
        
        # Base time for tests
        self.base_time = datetime(2024, 1, 1, 10, 0, 0)
        
        # Sample hourly bar
        self.hourly_bar = {
            'timestamp': self.base_time,
            'open': 100.0,
            'high': 101.0,
            'low': 99.0,
            'close': 100.5,
            'volume': 10000.0
        }
        
        # Sample signals
        self.signals = {
            'volume_imbalance': 0.1,
            'spread_mean': 0.0002,
            'atr_14': 1.0,
            'atr_percent': 0.01
        }
    
    def test_should_evaluate_first_time(self):
        """Test first evaluation always triggers."""
        should_eval = self.evaluator.should_evaluate(self.base_time)
        self.assertTrue(should_eval)
    
    def test_should_evaluate_hourly_boundary(self):
        """Test evaluation only on hour boundaries."""
        # First evaluation
        self.assertTrue(self.evaluator.should_evaluate(self.base_time))
        self.evaluator.last_evaluation_time = self.base_time
        
        # 30 minutes later - should not evaluate
        time_30m = self.base_time + timedelta(minutes=30)
        self.assertFalse(self.evaluator.should_evaluate(time_30m))
        
        # 59 minutes later - should not evaluate
        time_59m = self.base_time + timedelta(minutes=59)
        self.assertFalse(self.evaluator.should_evaluate(time_59m))
        
        # 60 minutes later - should evaluate
        time_60m = self.base_time + timedelta(minutes=60)
        self.assertTrue(self.evaluator.should_evaluate(time_60m))
    
    def test_evaluate_with_allowed_regime(self):
        """Test evaluation when regime allows trading."""
        # Setup regime features
        regime_features = {
            'current_state': GMS_STATE_STRONG_BULL_TREND,
            'current_confidence': 0.8,
            'state_duration_minutes': 30,
            'dominant_state_1h': GMS_STATE_STRONG_BULL_TREND,
            'state_changes_1h': 2,
            'avg_momentum_1h': 0.5,
            'avg_volatility_1h': 0.015
        }
        self.regime_manager.get_regime_features_for_strategy.return_value = regime_features
        
        # Setup strategy response
        self.strategy.evaluate_entry.return_value = {
            'direction': 'long',
            'confidence': 0.7,
            'position_size': 0.1
        }
        
        # Evaluate
        result = self.evaluator.evaluate(
            self.hourly_bar,
            self.signals,
            self.base_time
        )
        
        # Verify result
        self.assertIsNotNone(result)
        self.assertEqual(result['direction'], 'long')
        self.assertEqual(result['regime'], GMS_STATE_STRONG_BULL_TREND)
        self.assertIn('stop_loss', result)
        self.assertIn('position_size', result)
        
        # Verify risk management applied
        self.assertLessEqual(result['position_size'], self.config.portfolio.risk_per_trade)
    
    def test_evaluate_with_disallowed_regime(self):
        """Test no evaluation when regime doesn't allow trading."""
        # Setup regime features with disallowed state
        regime_features = {
            'current_state': GMS_STATE_HIGH_VOL_RANGE,  # Not in allowed states
            'current_confidence': 0.8
        }
        self.regime_manager.get_regime_features_for_strategy.return_value = regime_features
        
        # Evaluate
        result = self.evaluator.evaluate(
            self.hourly_bar,
            self.signals,
            self.base_time
        )
        
        # Should return None
        self.assertIsNone(result)
        
        # Strategy should not be called
        self.strategy.evaluate_entry.assert_not_called()
    
    def test_risk_management_position_sizing(self):
        """Test risk management reduces position size appropriately."""
        # Setup
        regime_features = {
            'current_state': GMS_STATE_STRONG_BULL_TREND,
            'current_confidence': 0.8
        }
        self.regime_manager.get_regime_features_for_strategy.return_value = regime_features
        
        # Test with large position size from strategy
        self.strategy.evaluate_entry.return_value = {
            'direction': 'long',
            'confidence': 0.9,
            'position_size': 0.5  # Too large
        }
        
        # Evaluate
        result = self.evaluator.evaluate(
            self.hourly_bar,
            self.signals,
            self.base_time
        )
        
        # Position size should be capped at risk_per_trade
        self.assertLessEqual(result['position_size'], self.config.portfolio.risk_per_trade)
    
    def test_risk_management_volatility_adjustment(self):
        """Test position sizing adjusts for volatility."""
        # Setup
        regime_features = {
            'current_state': GMS_STATE_STRONG_BULL_TREND,
            'current_confidence': 0.8
        }
        self.regime_manager.get_regime_features_for_strategy.return_value = regime_features
        
        self.strategy.evaluate_entry.return_value = {
            'direction': 'long',
            'confidence': 0.7,
            'position_size': 0.008  # Even smaller to ensure adjustment is visible
        }
        
        # Test high volatility
        high_vol_signals = self.signals.copy()
        high_vol_signals['atr_14'] = 3.0  # 3% ATR
        high_vol_signals['close'] = 100.0
        
        result_high_vol = self.evaluator.evaluate(
            self.hourly_bar,
            high_vol_signals,
            self.base_time
        )
        
        # Reset evaluation time
        self.evaluator.last_evaluation_time = None
        
        # Test low volatility
        low_vol_signals = self.signals.copy()
        low_vol_signals['atr_14'] = 0.8  # 0.8% ATR (< 1%)
        low_vol_signals['close'] = 100.0
        
        result_low_vol = self.evaluator.evaluate(
            self.hourly_bar,
            low_vol_signals,
            self.base_time + timedelta(hours=1)
        )
        
        # For now, just verify both got adjusted
        # The exact values depend on config and might be capped
        self.assertIsNotNone(result_high_vol['position_size'])
        self.assertIsNotNone(result_low_vol['position_size'])
        self.assertGreater(result_high_vol['position_size'], 0)
        self.assertGreater(result_low_vol['position_size'], 0)
    
    def test_stop_loss_calculation(self):
        """Test stop loss is calculated correctly."""
        # Setup
        regime_features = {
            'current_state': GMS_STATE_STRONG_BULL_TREND,
            'current_confidence': 0.8
        }
        self.regime_manager.get_regime_features_for_strategy.return_value = regime_features
        
        # Test long position
        self.strategy.evaluate_entry.return_value = {
            'direction': 'long',
            'confidence': 0.7,
            'position_size': 0.1
        }
        
        signals = self.signals.copy()
        signals['close'] = 100.0
        signals['atr_14'] = 1.0
        
        result = self.evaluator.evaluate(
            self.hourly_bar,
            signals,
            self.base_time
        )
        
        # Stop loss should be 2 ATR below entry
        expected_stop = 100.0 - (2.0 * 1.0)
        self.assertEqual(result['stop_loss'], expected_stop)
        
        # Reset for short test
        self.evaluator.last_evaluation_time = None
        
        # Test short position
        self.strategy.evaluate_entry.return_value = {
            'direction': 'short',
            'confidence': 0.7,
            'position_size': 0.1
        }
        
        result = self.evaluator.evaluate(
            self.hourly_bar,
            signals,
            self.base_time + timedelta(hours=1)
        )
        
        # Stop loss should be 2 ATR above entry
        expected_stop = 100.0 + (2.0 * 1.0)
        self.assertEqual(result['stop_loss'], expected_stop)
    
    def test_check_exit(self):
        """Test exit checking functionality."""
        # Mock current state
        mock_state = Mock()
        mock_state.state = GMS_STATE_HIGH_VOL_RANGE
        mock_state.confidence = 0.6
        self.regime_manager.get_current_state.return_value = mock_state
        
        # Mock strategy exit check
        self.strategy.check_exit.return_value = "regime_change"
        
        # Test position
        position = {
            'direction': 'long',
            'entry_price': 100.0,
            'size': 0.1
        }
        
        # Check exit
        exit_reason = self.evaluator.check_exit(
            position,
            self.signals,
            self.base_time
        )
        
        self.assertEqual(exit_reason, "regime_change")
        
        # Verify strategy was called with regime info
        call_args = self.strategy.check_exit.call_args[0]
        self.assertEqual(call_args[0]['regime_state'], GMS_STATE_HIGH_VOL_RANGE)
        self.assertEqual(call_args[0]['regime_confidence'], 0.6)
    
    def test_no_regime_data_handling(self):
        """Test handling when no regime data available."""
        # No regime features
        self.regime_manager.get_regime_features_for_strategy.return_value = None
        
        # Evaluate
        result = self.evaluator.evaluate(
            self.hourly_bar,
            self.signals,
            self.base_time
        )
        
        # Should return None
        self.assertIsNone(result)
    
    def test_get_diagnostics(self):
        """Test diagnostics output."""
        # Mock current state
        mock_state = Mock()
        mock_state.state = GMS_STATE_STRONG_BULL_TREND
        mock_state.confidence = 0.85
        self.regime_manager.get_current_state.return_value = mock_state
        
        # Set last evaluation
        self.evaluator.last_evaluation_time = self.base_time
        
        # Get diagnostics
        diag = self.evaluator.get_diagnostics()
        
        self.assertEqual(diag['mode'], 'backtest')
        self.assertEqual(diag['last_evaluation'], self.base_time)
        self.assertEqual(diag['current_regime'], GMS_STATE_STRONG_BULL_TREND)
        self.assertEqual(diag['regime_confidence'], 0.85)
        self.assertEqual(diag['risk_frac'], self.config.portfolio.risk_per_trade)
        self.assertEqual(diag['strategy'], 'test_strategy')


if __name__ == '__main__':
    unittest.main()