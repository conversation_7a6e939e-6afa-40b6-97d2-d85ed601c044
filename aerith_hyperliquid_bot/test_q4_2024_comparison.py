#!/usr/bin/env python3
"""
Test Q4 2024 comparison since that's where we have cached data.
Compare cached (hourly) vs uncached (60s) performance.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import json
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
from hyperliquid_bot.config.settings import load_config

def main():
    print("="*60)
    print("Q4 2024 COMPARISON: Hourly Cache vs 60s Updates")
    print("="*60)
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Q4 2024 period (where cache exists)
    start_date = datetime(2024, 10, 1)
    end_date = datetime(2024, 10, 31)  # Just October for faster test
    
    print(f"\nTest Period: {start_date.date()} to {end_date.date()}")
    
    # Test 1: With hourly cache
    print("\n" + "-"*60)
    print("TEST 1: WITH HOURLY CACHE (Current Implementation)")
    print("-"*60)
    
    engine_cached = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=True
    )
    
    results_cached = engine_cached.run_backtest()
    
    cached_stats = {}
    if results_cached:
        cached_stats = {
            'return': results_cached.get('total_return', 0),
            'trades': results_cached.get('total_trades', 0),
            'win_rate': results_cached.get('win_rate', 0),
            'profit_per_trade': (results_cached.get('total_return', 0) / 
                               max(results_cached.get('total_trades', 1), 1) * 100)
        }
        
        print(f"\nResults (Hourly Cache):")
        print(f"  Return: {cached_stats['return']:.2%}")
        print(f"  Trades: {cached_stats['trades']}")
        print(f"  Win Rate: {cached_stats['win_rate']:.1%}")
        print(f"  Profit/Trade: {cached_stats['profit_per_trade']:.3%}")
    
    # Test 2: Without cache (60s updates)
    print("\n" + "-"*60)
    print("TEST 2: WITHOUT CACHE (60s Regime Updates)")
    print("-"*60)
    print("This will calculate regimes every 60 seconds...")
    print("Expected: Better trade timing and higher profit per trade")
    
    engine_60s = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=False  # Forces 60s calculations
    )
    
    results_60s = engine_60s.run_backtest()
    
    uncached_stats = {}
    if results_60s:
        uncached_stats = {
            'return': results_60s.get('total_return', 0),
            'trades': results_60s.get('total_trades', 0),
            'win_rate': results_60s.get('win_rate', 0),
            'profit_per_trade': (results_60s.get('total_return', 0) / 
                               max(results_60s.get('total_trades', 1), 1) * 100)
        }
        
        print(f"\nResults (60s Updates):")
        print(f"  Return: {uncached_stats['return']:.2%}")
        print(f"  Trades: {uncached_stats['trades']}")
        print(f"  Win Rate: {uncached_stats['win_rate']:.1%}")
        print(f"  Profit/Trade: {uncached_stats['profit_per_trade']:.3%}")
    
    # Comparison
    print("\n" + "="*60)
    print("COMPARISON RESULTS")
    print("="*60)
    
    if cached_stats and uncached_stats:
        # Calculate improvements
        return_diff = uncached_stats['return'] - cached_stats['return']
        profit_improvement = ((uncached_stats['profit_per_trade'] - cached_stats['profit_per_trade']) / 
                            max(abs(cached_stats['profit_per_trade']), 0.001) * 100)
        
        print(f"\nAbsolute Differences:")
        print(f"  Return: {return_diff:+.2%}")
        print(f"  Trades: {uncached_stats['trades'] - cached_stats['trades']:+d}")
        print(f"  Win Rate: {uncached_stats['win_rate'] - cached_stats['win_rate']:+.1%}")
        print(f"  Profit/Trade: {uncached_stats['profit_per_trade'] - cached_stats['profit_per_trade']:+.3%}")
        
        print(f"\nRelative Improvement:")
        print(f"  Profit per trade: {profit_improvement:+.1f}%")
        
        if profit_improvement > 50:
            print("\n✅ SUCCESS! 60-second updates significantly improve performance!")
            print("This confirms stale regime data was the root cause.")
        elif profit_improvement > 0:
            print("\n⚠️ Some improvement, but less than expected.")
            print("May need longer test period or additional optimizations.")
        else:
            print("\n❌ No improvement detected.")
            print("Need to investigate why 60s updates aren't helping.")
        
        # Save results
        comparison = {
            "test_period": f"{start_date.date()} to {end_date.date()}",
            "hourly_cache": cached_stats,
            "60s_updates": uncached_stats,
            "improvements": {
                "return_absolute": return_diff,
                "profit_per_trade_pct": profit_improvement
            }
        }
        
        with open('q4_2024_comparison_results.json', 'w') as f:
            json.dump(comparison, f, indent=2)
        
        print(f"\n📁 Results saved to: q4_2024_comparison_results.json")
        
        # Extrapolate to full year
        if profit_improvement > 0:
            print("\n📈 FULL YEAR PROJECTION:")
            annual_factor = 12  # October to full year
            projected_annual = ((1 + uncached_stats['return']) ** annual_factor) - 1
            print(f"If October's {uncached_stats['return']:.2%} scales to full year:")
            print(f"Projected annual return: {projected_annual:.1%}")
            print(f"vs Current Modern: +41.78%")
            print(f"vs Legacy Target: +215%")

if __name__ == "__main__":
    main()