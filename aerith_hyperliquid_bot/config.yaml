# ==============================================================================
# Global Configuration
# ==============================================================================
is_backtest: true                 # Global flag indicating if running in backtest mode

# ==============================================================================
# Data & Cache Configuration
# ==============================================================================
data_paths:
  l2_data_root: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw2" # Contains YYYYMMDD_raw2.parquet
  raw_l2_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/l2_raw" # Raw L2 data directory
  feature_1s_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s" # 1-second feature data
  ohlcv_base_path: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled_l2" # Base path for OHLC bars
  log_dir: "/Users/<USER>/Desktop/trading_bot_/logs"
  require_ohlcv_volume: false # Set to false for now
  # TODO: Add paths for funding, OI, liquidation data later if needed
  # Add this new section for providers at the same level as data_paths
data_providers:
  fear_greed:
    enabled: false # Re-enabled after fixing timezone issues
    # cache_path: null # Optional: Path to cache file (future use)
cache:
  l2_cache_max_size: 24 # Max daily L2 files to keep in memory (if applicable)

# ==============================================================================
# Timeframe Setting
# ==============================================================================
timeframe: "1h" # Or "4h" - Set the desired timeframe for the run

# ==============================================================================
# Backtest & Simulation Configuration
# ==============================================================================
backtest:
  period_preset: '2024'            # Options: 'full', 'YYYYQX', 'YYYY', 'custom'
  custom_start_date: "2025-03-01"    # 22-day test period for R-113
  custom_end_date: "2025-03-22"      # 22-day test period for R-113

simulation:
  latency_seconds: 0.5               # Simulated order latency
  max_impact_levels: 5               # L2 levels assumed fillable without extra penalty (used by old simulator)
  force_taker_execution: True        # Set to False to enable maker attempt
  attempt_maker_orders: False        # Set to True to attempt maker fills
  maker_placement_type: 'best_passive'
  maker_time_buckets_seconds: [5, 30, 120]
  maker_fill_probabilities: [0.07, 0.13, 0.10]
  # l2_penalty_factor is defined in 'costs'

# ==============================================================================
# Portfolio Configuration
# ==============================================================================
portfolio:
  # --- existing risk controls (values stay AS IS) ---
  initial_balance: 10000       # Initial balance for backtests (in USD)
  risk_per_trade: 0.02         # Risk % of balance per trade (e.g., 0.01 = 1%) - Set to 2% based on recent runs
  max_leverage: 10.0           # Global maximum leverage cap
  asset_max_leverage: 50.0     # Asset-specific max leverage (e.g., for BTC on Hyperliquid, used for MM calc)
  margin_mode: cross           # Margin mode: 'cross' (shared margin pool), 'isolated' (separate margin per position)
  max_hold_time_hours: 24      # Maximum time to hold a position in hours

  # --- optional guards (neutral defaults) ---
  max_notional: 0              # 0 = no explicit ceiling
  min_trade_size: 0.001        # Minimum trade size in BTC (0.001 BTC default)
  leverage_guard_pct: 0        # 0 = disabled
  margin_buffer_pct: 0.05      # Margin buffer percentage (0.05 = 5%)

# ==============================================================================
# Costs Configuration
# ==============================================================================
costs:
  funding_rate: 0.0001        # Default funding rate (per period)
  funding_hours: 8            # Funding interval
  taker_fee: 0.000315         # Taker fee rate (e.g., 0.00035 = 0.035%)
  maker_fee: 0.00009          # Maker fee rate
  l2_penalty_factor: 1.005    # Slippage penalty factor when spread data is unusable (e.g., 1.005 = 0.5% penalty)

# ==============================================================================
# Strategy Selection & Configuration
# ==============================================================================
strategies:
  # --- Strategy Activation ---
  use_tf_v2: True            # Disable legacy tf_v2 for R-113 continuous_gms + tf_v3 test
  use_mean_reversion: False   # Keep inactive based on PRD
  use_mean_variance: False    # Keep inactive based on PRD
  use_obi_scalper: False       # OBI Scalper strategy activation flag
  use_tf_v3: False             # Enable TF-v3 for R-113 continuous_gms + tf_v3 test

  # --- Trend Following (TF-v2) Configuration ---
  tf_warmup_bars: "auto"      # Warm-up period: "auto" or integer (auto = max(ema_slow, atr_period, regime_lookback))

  # --- Trend Following Filters (Optional - Enable for specific tests) ---
  tf_use_obi_filter: False
  tf_use_funding_filter: False

  # --- OBI Scalper Activation Flags ---
  obi_scalper_active_in_all_regimes: False  # If true, OBI Scalper is active regardless of regime
  gms_activate_obi_scalper_in_chop: True    # If true, activate OBI Scalper in CHOP regime with GMS detector

  # --- OBI Scalper Risk Parameters ---
  scalper_risk_pct: 0.5                     # Risk percentage for OBI Scalper (0.5 = 0.5%)
  scalper_stop_ticks: 5                     # Number of ticks for stop loss distance

  # --- OBIScalperStrategy Configuration ---
  OBIScalperStrategy:
    enabled: true
    defaults:
      vol_veto_threshold:      0.002           # Increased from 0.0006 to 0.002
      spread_veto_threshold:   0.0001          # Increased from 0.00004 to 0.0001
      obi_l1_3_trigger:        0.50            # Reduced from 0.80 to 0.50
      tp_ticks:                7
      sl_ticks:                5
      timeout_seconds:         30
      allowed_gms_states:      ["CHOP", "BULL", "BEAR"] # Using the 3-state GMS mapping
      zero_sign_eps:           0.001
      tick_size:               0.01            # Tick size for price increments

# ==============================================================================
# Market Regime Detection Configuration
# ==============================================================================
regime:
  # --- General Settings ---
  use_filter: True                     # Enable/disable regime filtering entirely
  detector_type: 'granular_microstructure'      # Options: 'rule_based', 'continuous_gms', 'granular_microstructure'
  use_strict_strategy_filtering: True  # Only evaluate strategies mapped to the current regime

  # --- Rule-Based Detector Specific Settings ---
  # (Ignored if detector_type != 'rule_based')
  use_enhanced_detection: True        # Use ADX + Forecast + Chop/Vol logic
  use_chop_filter: True               # Apply chop filter logic (if enhanced) - Check if benchmark used this
  use_chop_index_for_chop: False      # Method for chop detection (if enhanced)
  use_bbw_for_chop_detection: False   # Method for chop detection (if enhanced)
  pause_in_chop: False                # Pause trading entirely in Volatile_Chop (if strict filtering)


  # ==============================================================================
  # Shared GMS Detector Settings (apply to all detector types)
  # ==============================================================================
  # Core settings
  gms_use_adx_confirmation: False
  gms_use_funding_confirmation: False

  # Volatility threshold mode and values (for backward compatibility)
  gms_vol_thresh_mode: 'fixed'  # Options: 'fixed' or 'percentile'
  gms_vol_high_thresh: 0.92          # default granular 0.92 (fallback value)
  gms_vol_low_thresh: 0.55           # default granular 0.55 (fallback value)
  gms_vol_high_percentile: 75        # Used when mode = 'percentile'
  gms_vol_low_percentile: 25         # Used when mode = 'percentile'

  # Momentum thresholds (for backward compatibility)
  gms_mom_strong_thresh: 100.0           # default granular 100.0 (fallback value)
  gms_mom_weak_thresh: 50.0              # default granular 50.0 (fallback value)

  # Spread thresholds (for backward compatibility)
  gms_spread_std_high_thresh: 0.000050   # was 0.000035 - 50 (fallback value)
  gms_spread_mean_low_thresh: 0.000045   # was 0.000030 - 45 (fallback value)

  # ==============================================================================
  # Detector-Specific Configuration Sections
  # ==============================================================================

  # Granular Microstructure Detector Settings (Legacy - ~184 trades baseline)
  granular_microstructure:
    # Threshold values
    gms_vol_high_thresh: 0.92
    gms_vol_low_thresh: 0.55
    gms_mom_strong_thresh: 100.0
    gms_mom_weak_thresh: 50.0
    gms_spread_std_high_thresh: 0.000050
    gms_spread_mean_low_thresh: 0.000045
    # Operational settings
    cadence_sec: 3600                                    # Hourly recompute for granular
    output_states: 8
    state_collapse_map_file: 'configs/gms_state_mapping.yaml'
    use_four_state_mapping: false
    risk_suppressed_notional_frac: 0.25
    risk_suppressed_pnl_atr_mult: 1.5

  # Continuous GMS Detector Settings (Optimized for continuous operation)
  continuous_gms:
    # Threshold values - Phase 1 Conservative Tuning (halved from original)
    gms_vol_high_thresh: 0.03      # Reduced from 0.06 to 0.03 (Phase 1)
    gms_vol_low_thresh: 0.01       # Reduced from 0.02 to 0.01 (Phase 1)
    gms_mom_strong_thresh: 2.5     # Reduced from 5.0 to 2.5 (Phase 1)
    gms_mom_weak_thresh: 0.5       # Reduced from 1.0 to 0.5 (Phase 1)
    gms_spread_std_high_thresh: 0.0005
    gms_spread_mean_low_thresh: 0.0001
    # Operational settings
    cadence_sec: 60                                      # Minute recompute for continuous
    output_states: 8
    state_collapse_map_file: 'configs/gms_state_mapping.yaml'
    use_four_state_mapping: false
    risk_suppressed_notional_frac: 0.25
    risk_suppressed_pnl_atr_mult: 1.5

  # Funding thresholds
  gms_funding_extreme_positive_thresh: 0.001
  gms_funding_extreme_negative_thresh: -0.001

  # Trend filter settings
  gms_filter_allow_weak_bull_trend: True
  gms_filter_allow_weak_bear_trend: True # Set to True
  gms_weak_bull_risk_scale: 0.25         # Example value
  gms_weak_bear_risk_scale: 0.25         # Example value - test with 0.25 first?

  # Advanced filters (null = disabled)
  gms_obi_zscore_threshold: null         # Set > 0 to enable Z-Score filter
  gms_spread_percentile_gate: null       # Set 1-100 to filter out trades when spread is in the top X%
  gms_depth_slope_thin_limit: null       # Depth slope threshold (Set >= 0 to enable)
  gms_depth_skew_thresh: null            # Depth skew threshold (Set >= 0 to enable)
  gms_spread_trend_lookback: null        # Set > 0 (e.g., 12) to enable Spread Trend filter
  gms_adaptive_obi_base: null            # Set > 0 (e.g., 0.1) to enable Adaptive OBI filter
  gms_confirmation_bars: null            # Set > 0 (e.g., 1) to require confirmation bars

  # Adaptive spread settings
  gms_tight_spread_fallback_percentile: null # Set to e.g.,0.15, 0.25 to enable
  gms_tight_spread_percentile_window: 24     # Window for percentile calculation
  gms_spread_mean_thresh_mode: 'fixed'       # 'fixed' or 'percentile'
  gms_spread_std_thresh_mode: 'fixed'        # 'fixed' or 'percentile'
  gms_spread_mean_low_percentile: 0.25
  gms_spread_std_high_percentile: 0.75
  gms_spread_percentile_window: null         # Uses default from microstructure settings

  # State mapping
  gms_use_three_state_mapping: True           # Default: Use raw 7 GMS states
  gms_state_mapping_file: 'configs/gms_state_mapping.yaml' # Verify this relative path
  map_weak_bear_to_bear: false                 # Toggle whether to map Weak_Bear_Trend to BEAR (true) or CHOP (false, default)

  # Dynamic risk adjustment based on market regime
  dynamic_risk_adjustment: false        # Apply dynamic risk in volatile regimes

  # Risk factors for different market conditions
  chop_risk_factor: 0.5                # Multiplier for risk_per_trade in choppy markets
  chop_leverage_factor: 0.5            # Multiplier for leverage in choppy markets
  strong_trend_risk_factor: 0.8        # Multiplier for risk_per_trade in strong trends
  strong_trend_leverage_factor: 0.7    # Multiplier for leverage in strong trends
  weak_trend_risk_scale: 0.8           # Scale risk in weak trend regimes

# ==============================================================================
# Continuous GMS Detector Configuration
# ==============================================================================
gms:
  detector_type: 'continuous_gms'       # New default
  cadence_sec: 60                       # Recompute every 60 seconds
  output_states: 8                      # 8 = raw, 4 or 3 after collapse
  state_collapse_map_file: 'configs/gms_state_mapping.yaml'
  use_four_state_mapping: false         # Adds TIGHT_SPREAD → TSP
  risk_suppressed_notional_frac: 0.25   # Large-trade threshold
  risk_suppressed_pnl_atr_mult: 1.5     # PnL threshold multiplier

  # ==============================================================================
  # Adaptive Threshold Configuration (Task R-112k)
  # ==============================================================================
  auto_thresholds: false                # Enable adaptive, causal thresholds
  percentile_window_sec: 86400          # 24-hour rolling window (past only)
  vol_low_pct: 0.001                    # 0.1st percentile (lowered 100x from 0.10)
  vol_high_pct: 0.50                    # 50th percentile (median)
  mom_low_pct: 0.001                    # 0.1st percentile for momentum (lowered 100x from 0.10)
  mom_high_pct: 0.50                    # 50th percentile for momentum
  min_history_rows: 100                 # Fallback to static if fewer past rows (lowered for testing)

  # ==============================================================================
  # Priming Configuration (Task R-112m)
  # ==============================================================================
  priming_hours: 24                     # Hours of historical data to prime adaptive thresholds (0 = disabled)

  # ==============================================================================
  # Market Bias System - Adjusts position sizing based on market conditions
  # ==============================================================================
  market_bias:
    enabled: false                      # Master switch for all market bias adjustments
    use_three_state_mapping: true      # Maps GMS regimes to simplified BULL/BEAR/CHOP states

    # -----------------------------------------------------------------------
    # LEVERAGE FACTORS - AFFECT MARGIN REQUIREMENTS ONLY (NOT POSITION SIZE)
    # -----------------------------------------------------------------------
    # - Used in: RiskManager.calculate_position() → calculate_leverage()
    # - These determine how much capital is reserved as margin for positions
    # - Higher values = less margin required = more capital efficient
    # - For single-asset trading (current mode), these have minimal impact
    # - Neutral value: 1.0 (no adjustment to base leverage)
    # -----------------------------------------------------------------------
    bull_leverage_factor: 1.0          # Multiplier applied to base leverage in bull markets
    bear_leverage_factor: 1.0          # Multiplier applied to base leverage in bear markets
    chop_leverage_factor: 1.0          # Multiplier applied to base leverage in choppy markets

    # -----------------------------------------------------------------------
    # RISK FACTORS - DIRECTLY AFFECT POSITION SIZE (PRIMARY SIZING CONTROL)
    # -----------------------------------------------------------------------
    # - Used in: RiskManager.calculate_position() → calculate_risk_amount()
    # - These directly multiply the risk_amount used in position size calculation
    # - Higher values = larger position sizes = more risk exposure
    # - These are the primary control parameters for market bias
    # - Neutral value: 1.0 (no adjustment to position size)
    # -----------------------------------------------------------------------
    bull_risk_factor: 1.2              # Increases position size in bull markets (+20%)
    bear_risk_factor: 0.8              # Decreases position size in bear markets (-20%)
    chop_risk_factor: 0.5              # Significantly reduces position size in choppy markets (-50%)

    # -----------------------------------------------------------------------
    # DIRECTION BIAS - REQUIRES 'direction' FIELD IN strategy_info DICTIONARY
    # -----------------------------------------------------------------------
    # - Used in: RiskManager.calculate_position() → calculate_leverage()
    # - These adjust leverage based on BOTH market condition AND trade direction
    # - NOTE: Currently not active since direction info is not passed to risk manager
    # - To enable: Modify backtester to include direction in strategy_info dict
    # - Neutral value: 1.0 (no direction-based adjustment)
    # -----------------------------------------------------------------------
    bull_long_bias: 1.0                # Applied to long trades in bull markets
    bull_short_bias: 1.0               # Applied to short trades in bull markets
    bear_long_bias: 1.0                # Applied to long trades in bear markets
    bear_short_bias: 1.0               # Applied to short trades in bear markets

# ==============================================================================
# Microstructure Feature Calculation & Filter Settings
# ==============================================================================
microstructure:
  # --- Feature Calculation Parameters ---
  depth_levels: 5               # Temporarily use 5 to test with existing feature files (will regenerate with 20 later)
  obi_levels: null              # Levels for raw OBI calc (null = use depth_levels)
  obi_smoothing_window: 8      # Window for OBI smoothing (in SignalCalculator)
  obi_smoothing_type: 'sma'     # 'sma' or 'ema'
  obi_zscore_window: null       # Window for OBI Z-score (if null, Z-score not calculated)
  depth_levels_for_calc: null   # Levels for depth ratio/pressure calc (null = use depth_levels)
  spread_rolling_window: 24     # Window for spread stats (in SignalCalculator)
  spread_metric_to_roll: 'relative' # 'absolute' or 'relative'
  allow_nan_micro_depth: true   # Allow NaN values in depth metrics without aborting

  # --- Granular Detector OBI Thresholds ---
  # (Ignored if detector_type != 'granular_microstructure')
  gms_obi_strong_confirm_thresh: 0.20
  gms_obi_weak_confirm_thresh: 0.11

  # --- Trend Following Filter Thresholds ---
  # (Ignored if strategies.tf_use_..._filter is False)
  tf_filter_obi_threshold_long: 0.1
  tf_filter_obi_threshold_short: -0.1
  tf_filter_funding_threshold_long: -0.0005
  tf_filter_funding_threshold_short: 0.0005

# ==============================================================================
# Indicator Parameters
# ==============================================================================
indicators:
  # General Signal Engine Configuration
  require_volume_for_signals: false # If true, checks if 'volume' column is present in data fed to SignalEngine

  # --- General / Rule-Based Detector ---
  adx_period: 14
  adx_threshold: 30.0                 # Used by RuleBased and optionally by GMS
  high_volatility_adx_threshold: 30.0 # Used by RuleBased (enhanced)
  low_forecast_threshold: 1.0         # Used by RuleBased (enhanced) / TF Hurst Filter
  volatility_thresh_percent: 0.005    # Used by RuleBased (enhanced fallback)
  bbw_thresh: 0.03                    # Used by RuleBased (enhanced fallback - if BBW enabled)
  chop_index_period: 14               # Used by RuleBased (enhanced fallback - if ChopIndex enabled)
  chop_index_high_thresh: 61.8
  chop_index_low_thresh: 38.2
  min_leverage: 1.0                 # Global minimum leverage allowed

  # --- Trend Following Strategy ---
  tf_ewma_fast: 20
  tf_ewma_medium: 32
  use_tf_medium_ewma: False
  tf_ewma_slow: 50
  tf_atr_period: 20
  tf_atr_stop_mult: 2.0
  tf_atr_target_mult: 4.0
  tf_leverage_base: 5.0             # Base leverage reference for TF strategy
  tf_max_entry_volatility_pct: 0.01 # Used only by TF Hurst filter

  # --- Mean Reversion Strategy (Inactive) ---
  mr_ema_period: 20
  mr_keltner_mult: 2.0
  mr_rsi_period: 14
  mr_rsi_oversold: 30.0
  mr_rsi_overbought: 70.0
  mr_atr_period: 20
  mr_atr_stop_mult: 1.5
  mr_leverage_base: 5.0
  mr_obi_long_threshold: -0.2
  mr_obi_short_threshold: 0.2
  mr_require_obi_filter: True

  # --- Mean Variance Strategy (Inactive) ---
  mv_edge_ema_period: 10
  mv_volatility_period: 20
  mv_min_edge_threshold: 0.0005
  mv_min_exit_edge_threshold: 0.0
  mv_min_kelly: 0.05
  mv_max_kelly: 0.25
  mv_leverage_base: 3.0
  mv_atr_stop_mult: 2.5
  mv_atr_target_mult: 3.0
  mv_atr_period: 20
  mv_obi_long_threshold: -0.3
  mv_obi_short_threshold: 0.3
  mv_require_obi_filter: True

  # --- Granular Microstructure Detector Indicators ---
  # (Ignored if detector_type != 'granular_microstructure')
  gms_roc_period: 5
  gms_ma_slope_period: 30
  gms_atr_percent_period: 14

# ==============================================================================
# Analysis Configuration
# ==============================================================================
analysis:
  analyze_trades_after_backtest: True # Generate losing trade analysis CSV

# ==============================================================================
# ETL Pipeline Configuration
# ==============================================================================
etl:
  l20_to_1s:
    chunk_sec: 3600       # Process hourly
    rollup_method: "median"   # Median of each second for numeric cols

# ==============================================================================
# Scheduler Configuration
# ==============================================================================
scheduler:
  etl_enabled: true       # Enable ETL scheduler
  etl_poll_sec: 300       # Poll every 5 minutes (300 seconds)

# ==============================================================================
# TF-v3 Strategy Configuration
# ==============================================================================
tf_v3:
  enabled: true            # Enable TF-v3 strategy for R-112 test
  ema_fast: 20
  ema_slow: 50
  atr_period: 14          # on 1‑hour bars
  atr_trail_k: 3.0        # stop distance in ATRs
  max_trade_life_h: 24    # time‑decay exit
  risk_frac: 0.25         # fraction of free notional to allocate
  max_notional: 25000     # absolute cap per TF trade
  gms_max_age_sec: 300    # staleness threshold for regime snapshot (increased from 120s)
  atr_fallback_pct: 0.01  # used only if ATR column NaN (1% of entry price)
  trail_eps: 0.01         # trailing-stop epsilon in quote units

# ==============================================================================
# Visualization Configuration
# ==============================================================================
visualization:
  require_volume: false # Set to true if volume column is required for plots
  # Indicator panels to show in visualization
  panels:
    show_ma_slope: true    # Show MA slope panel
    show_obi: true         # Show OBI panel
    show_fear_greed: false # Disabled to match fear_greed data provider
  # Panel appearance settings
  appearance:
    price_panel_ratio: 4   # Relative size of price panel
    indicator_panel_ratio: 1  # Relative size of indicator panels
    max_points: 5000       # Maximum data points to plot (for performance)
