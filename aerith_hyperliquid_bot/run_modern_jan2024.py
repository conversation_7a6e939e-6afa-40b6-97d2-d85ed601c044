#!/usr/bin/env python3
"""
Run modern system for January 2024 with fixed P&L tracking.
"""

import subprocess
import sys
import json
from pathlib import Path

def main():
    print("Running Modern System - January 2024")
    print("=" * 80)
    
    # Run January 2024
    cmd = [
        sys.executable,
        "scripts/run_modern_backtest.py",
        "--start-date", "2024-01-01",
        "--end-date", "2024-01-31",
        "--override", "configs/overrides/modern_system_v2_complete.yaml",
        "--output", "modern_jan2024_fixed.json"
    ]
    
    print("Starting backtest with fixed P&L tracking...")
    print("-" * 80)
    
    result = subprocess.run(cmd)
    
    if result.returncode != 0:
        print("\nBacktest failed!")
        return 1
    
    # Load and display results
    results_file = Path("modern_jan2024_fixed.json")
    if results_file.exists():
        with open(results_file) as f:
            data = json.load(f)
        
        perf = data['performance']
        trades = data['trades']
        
        # Count completed trades
        completed = [t for t in trades if 'return' in t]
        
        print("\n" + "=" * 80)
        print("RESULTS SUMMARY")
        print("=" * 80)
        print(f"Total Trades: {perf.get('total_trades', 0)}")
        print(f"Completed Trades: {len(completed)}")
        print(f"Open Trades: {perf.get('total_trades', 0) - len(completed)}")
        print(f"Total Return: {perf.get('total_return', 0):.2%}")
        print(f"Win Rate: {perf.get('win_rate', 0):.2%}")
        print(f"Avg Trade Return: {perf.get('avg_trade_return', 0):.2%}")
        print(f"Best Trade: {perf.get('best_trade', 0):.2%}")
        print(f"Worst Trade: {perf.get('worst_trade', 0):.2%}")
        
        print("\n" + "=" * 80)
        print("COMPARISON")
        print("=" * 80)
        print(f"Modern (Jan): {perf.get('total_trades', 0)} trades, {perf.get('total_return', 0):.1%} ROI")
        print(f"Legacy (Full Year): 189 trades, 215.0% ROI")
        print(f"Expected Monthly: ~16 trades, ~18% ROI")
        
        # Show sample trades
        if completed:
            print("\n" + "=" * 80)
            print("SAMPLE COMPLETED TRADES")
            print("=" * 80)
            for i, trade in enumerate(completed[:5]):
                print(f"Trade {i+1}:")
                print(f"  Direction: {trade['direction']}")
                print(f"  Entry: ${trade['entry_price']:.2f}")
                print(f"  Exit: ${trade.get('exit_price', 'N/A')}")
                print(f"  Return: {trade['return']:.2%}")
                print(f"  Exit Reason: {trade.get('exit_reason', 'N/A')}")
                print()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())