#!/usr/bin/env python3
"""
Test the modern system (continuous_gms + TF-v3) health and trade generation.
"""

import sys
from pathlib import Path
import logging
import yaml

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.core.detector_factory import get_regime_detector


def test_modern_config():
    """Test modern system configuration."""
    print("=== Modern System Health Check ===\n")
    
    # Load base config with modern profile override
    print("1. Loading configuration...")
    base_config = load_config("configs/base.yaml")
    
    # Load modern profile
    with open("configs/overrides/profile_modern_system.yaml", 'r') as f:
        modern_override = yaml.safe_load(f)
    
    # Apply override (simplified merge for testing)
    base_dict = base_config.model_dump()
    for key, value in modern_override.items():
        if isinstance(value, dict) and key in base_dict:
            base_dict[key].update(value)
        else:
            base_dict[key] = value
    
    # Create config from merged dict
    from hyperliquid_bot.config.settings import Config
    config = Config(**base_dict)
    
    print(f"   Detector type: {config.regime.detector_type}")
    print(f"   TF-v3 enabled: {config.strategies.use_tf_v3}")
    print(f"   Adaptive thresholds: {config.gms.auto_thresholds}")
    
    # 2. Check detector initialization
    print("\n2. Initializing detector...")
    detector = get_regime_detector(config)
    print(f"   Detector: {type(detector).__name__}")
    print(f"   Mode: {getattr(detector, 'detector_mode', 'N/A')}")
    
    if hasattr(detector, 'thresholds'):
        print(f"\n   Thresholds:")
        print(f"     vol_high: {detector.thresholds['vol_high']}")
        print(f"     vol_low: {detector.thresholds['vol_low']}")
        print(f"     mom_strong: {detector.thresholds['mom_strong']}")
        print(f"     mom_weak: {detector.thresholds['mom_weak']}")
    
    # 3. Test regime detection with various scenarios
    print("\n3. Testing regime detection...")
    test_scenarios = [
        {
            "name": "Low Vol + Low Mom",
            "atr_percent": 0.004,
            "ma_slope": 0.5,
            "obi_smoothed_5": 0.05
        },
        {
            "name": "Medium Vol + Medium Mom",
            "atr_percent": 0.010,
            "ma_slope": 1.5,
            "obi_smoothed_5": 0.15
        },
        {
            "name": "High Vol + Strong Mom",
            "atr_percent": 0.020,
            "ma_slope": 3.0,
            "obi_smoothed_5": 0.25
        }
    ]
    
    regime_counts = {}
    for scenario in test_scenarios:
        signals = {
            'timestamp': 1640995200,
            'atr_percent': scenario['atr_percent'],
            'ma_slope': scenario['ma_slope'],
            'obi_smoothed_5': scenario['obi_smoothed_5'],
            'spread_mean': 0.0001,
            'spread_std': 0.00005,
            'close': 50000.0
        }
        
        regime = detector.get_regime(signals)
        # Handle both string and dict returns (continuous mode returns dict)
        if isinstance(regime, dict):
            regime_str = regime.get('state', str(regime))
        else:
            regime_str = regime
        regime_counts[regime_str] = regime_counts.get(regime_str, 0) + 1
        print(f"   {scenario['name']}: {regime_str}")
    
    # 4. Check state mapping
    print("\n4. Checking state mapping...")
    
    # Get state mapping file from config
    state_mapping_file = 'configs/gms_state_mapping_modern.yaml'  # This should be loaded from profile
    
    # Actually load the state mapping file that the detector will use
    if hasattr(detector, 'state_collapse_map') and detector.state_collapse_map:
        # Use the loaded state mapping from detector
        state_map = detector.state_collapse_map.get('state_map', {})
        print(f"   Using state mapping from detector (loaded from config)")
    else:
        # Fallback: load the modern state mapping file directly
        try:
            with open(state_mapping_file, 'r') as f:
                state_map_data = yaml.safe_load(f)
                state_map = state_map_data.get('state_map', {})
                print(f"   Loaded state mapping from: {state_mapping_file}")
        except Exception as e:
            print(f"   Error loading state mapping: {e}")
            state_map = {}
    
    # Count how many states map to trading vs non-trading
    trading_states = 0
    non_trading_states = 0
    
    for gms_state, strategy_state in state_map.items():
        if strategy_state in ['BULL', 'BEAR']:
            trading_states += 1
        else:
            non_trading_states += 1
    
    print(f"   Trading states: {trading_states}")
    print(f"   Non-trading states: {non_trading_states}")
    
    # Check critical mappings
    critical_states = ['High_Vol_Range', 'Low_Vol_Range', 'Weak_Bear_Trend']
    print(f"\n   Critical state mappings:")
    for state in critical_states:
        mapped = state_map.get(state, 'NOT FOUND')
        print(f"     {state} -> {mapped}")
    
    # 5. Analysis
    print("\n=== Analysis ===")
    
    # Check if modern system is properly configured
    if (state_map.get('High_Vol_Range') == 'BULL' and 
        state_map.get('Low_Vol_Range') == 'BEAR' and
        state_map.get('Weak_Bear_Trend') == 'BEAR'):
        print("✅ State mapping is properly configured for modern system!")
        print("   - High_Vol_Range -> BULL (volatility breakout trades)")
        print("   - Low_Vol_Range -> BEAR (mean reversion shorts)")
        print("   - Weak_Bear_Trend -> BEAR (trend following shorts)")
    elif state_map.get('High_Vol_Range') == 'CHOP' and state_map.get('Low_Vol_Range') == 'CHOP':
        print("⚠️  WARNING: State mapping might be too restrictive for modern system!")
        print("   High_Vol_Range and Low_Vol_Range both map to CHOP (no trading)")
        print("   The modern system was designed to trade in these conditions")
    else:
        print("🔍 State mapping has mixed configuration")
        print(f"   Current mappings:")
        for state in critical_states:
            print(f"     {state} -> {state_map.get(state, 'NOT FOUND')}")
    
    # Check threshold levels
    continuous_config = getattr(config.regime, 'continuous_gms', None)
    if continuous_config:
        vol_high = getattr(continuous_config, 'gms_vol_high_thresh', None)
        vol_low = getattr(continuous_config, 'gms_vol_low_thresh', None)
        
        if vol_high and vol_low:
            print(f"\n   Continuous mode thresholds from config:")
            print(f"     vol_high: {vol_high} (should be ~0.015 for modern)")
            print(f"     vol_low: {vol_low} (should be ~0.005 for modern)")
            
            if vol_high > 0.01:
                print("   ✅ Thresholds are appropriately set for modern system")
            else:
                print("   ⚠️  Thresholds might be using legacy values")


def create_modern_state_mapping():
    """Create a suggested state mapping for modern system."""
    print("\n\n=== Suggested Modern State Mapping ===")
    print("Create a new file: configs/gms_state_mapping_modern.yaml")
    print("```yaml")
    print("# GMS State Mapping for Modern System (Continuous GMS + TF-v3)")
    print("# Optimized for volatility-based trading and trend following")
    print("state_map:")
    print("  Strong_Bull_Trend: 'BULL'      # Strong bullish momentum")
    print("  Weak_Bull_Trend: 'BULL'        # Weaker bullish momentum")
    print("  High_Vol_Range: 'BULL'         # Trade volatility breakouts")
    print("  Low_Vol_Range: 'BEAR'          # Trade mean reversion")
    print("  Uncertain: 'CHOP'              # No trading in uncertain conditions")
    print("  Unknown: 'CHOP'                # No trading on errors")
    print("  Weak_Bear_Trend: 'BEAR'        # Trade weak bearish trends")
    print("  Strong_Bear_Trend: 'BEAR'      # Strong bearish momentum")
    print("  TIGHT_SPREAD: 'CHOP'           # No trading in tight spreads")
    print("```")


if __name__ == "__main__":
    # Set logging to WARNING to reduce noise
    logging.basicConfig(level=logging.WARNING)
    
    test_modern_config()
    create_modern_state_mapping()