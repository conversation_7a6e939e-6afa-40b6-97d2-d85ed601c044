#!/bin/bash

# Back-fill March 2025 data for Task R-112p
cd /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot

echo "Starting March 2025 back-fill for Task R-112p"
echo "Processing dates 2025-03-03 through 2025-03-22"

# Process remaining days
for d in 2025-03-{03..22}; do
    echo "Processing $d"
    python3 tools/etl_l20_to_1s.py --date "$d" --overwrite
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to process $d"
        exit 1
    fi
    echo "Successfully processed $d"
    echo "---"
done

echo "March 2025 back-fill completed successfully!"
