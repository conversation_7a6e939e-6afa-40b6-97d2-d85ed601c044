# How to Run Modern System Backtest

## Quick Start (Recommended)

### Option 1: Use the Easy Wrapper (NEW!)
```bash
# Test 1 day (default)
./run_modern.sh

# Test 7 days
./run_modern.sh --days 7

# Test specific date range
./run_modern.sh --start 2024-01-01 --end 2024-01-31
```

### Option 2: Direct Command
```bash
# Test with just 1 day first to see if it generates trades
python3 scripts/run_modern_backtest.py \
    --start-date "2024-01-01" \
    --end-date "2024-01-02" \
    --override configs/overrides/modern_system_v2_complete.yaml
```

## Full Commands

### 1. Test Modern Signal Pipeline First
```bash
# This will verify the signal engine is working
python3 scripts/test_modern_signal_pipeline.py
```

Expected output:
- Signal validation should show most signals as ✓
- Look-ahead bias prevention should pass
- Integration test should complete

### 2. Run Short Backtest (1 Week)
```bash
python3 scripts/run_modern_backtest.py \
    --start-date "2024-01-01" \
    --end-date "2024-01-07" \
    --override configs/overrides/modern_system_v2_complete.yaml
```

### 3. Run Full Month Backtest
```bash
python3 scripts/run_modern_backtest.py \
    --start-date "2024-01-01" \
    --end-date "2024-02-01" \
    --override configs/overrides/modern_system_v2_complete.yaml
```

## What to Look For

### During Execution
You should see logs like:
```
- ModernSignalEngine initialized
- HourlyStrategyEvaluator initialized  
- Processing X hours
- Signals prepared: close=XXX, volume=XXX, ema_fast=XXX...
- Entry signal generated: long/short
```

### Results Location
- Equity curve: `logs/equity_det_continuous_modern_v2_*.png`
- Trade log: Check console output for trade summaries
- Performance metrics: End of backtest output

## Common Issues & Solutions

### 1. "No trades generated"
Check for these log messages:
- "Regime not stable enough for entry"
- "Missing required indicators"
- "Risk suppressed"

### 2. Very Slow Performance
The modern system processes 60x more data (1-second vs 1-minute).
Try:
- Shorter date ranges first
- Add `--debug` flag to see what's taking time

### 3. Memory Issues
```bash
# Increase Python memory limit if needed
ulimit -v unlimited
```

## Debug Mode
```bash
# Add debug flag for verbose output
python3 scripts/run_modern_backtest.py \
    --start "2024-01-01" \
    --end "2024-01-02" \
    --override configs/overrides/modern_system_v2_complete.yaml \
    --debug
```

## ⚠️ CRITICAL Configuration Issue

The `modern_system_v2_complete.yaml` currently has:
```yaml
risk_per_trade: 0.02  # 2% - TOO LOW!
```

This needs to be changed to:
```yaml
risk_per_trade: 0.25  # 25% - matches legacy system
```

To fix this:
```bash
# Edit the config file
vim configs/overrides/modern_system_v2_complete.yaml
# Change line 62: risk_per_trade: 0.02 → risk_per_trade: 0.25
```

Or use sed:
```bash
sed -i '' 's/risk_per_trade: 0.02/risk_per_trade: 0.25/' configs/overrides/modern_system_v2_complete.yaml
```

## Check Configuration
Make sure `configs/overrides/modern_system_v2_complete.yaml` has:
```yaml
regime:
  detector_type: "continuous_modern_v2"
  
strategies:
  use_tf_v3: true
  use_tf_v2: false

portfolio:
  risk_per_trade: 0.25  # CRITICAL: Must be 25%, not 2%!
```

## Expected Results

Based on our fixes, you should see:
- Regime states: ~50% in trading states (Bull/Bear)
- Trade frequency: 1-5 trades per day (60+ per month)
- All required signals present (no NaN after warm-up)

## If Still 0 Trades

1. Check the detector is entering trading states:
```bash
grep -E "state.*BULL|state.*BEAR" <log_output>
```

2. Check strategy is receiving signals:
```bash
grep "Signals prepared" <log_output>
```

3. Check for entry attempts:
```bash
grep -E "Entry signal|evaluate_entry" <log_output>
```

## Alternative: Use Legacy System
If you need results now:
```bash
python3 -m hyperliquid_bot.backtester.run_backtest
```
This uses the working legacy system (180 trades, +215% ROI).