#!/usr/bin/env python3
"""
Check the distribution of ma_slope values in the enhanced hourly data
to understand why no strong trends are being detected.
"""

import pandas as pd
import numpy as np

# Load a month of data to get a good distribution
print("Loading enhanced hourly data for February 2024...")

dfs = []
for day in range(1, 29):  # Feb 2024
    try:
        file = f"/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/enhanced_hourly/1h/2024-02-{day:02d}_1h_enhanced.parquet"
        df = pd.read_parquet(file)
        dfs.append(df)
    except:
        pass

# Combine all data
data = pd.concat(dfs)
print(f"\nLoaded {len(data)} hours of data")

# Analyze ma_slope distribution
print("\n" + "="*80)
print("MA_SLOPE DISTRIBUTION ANALYSIS:")
print("="*80)

ma_slope = data['ma_slope'].dropna()
print(f"\nTotal non-NaN values: {len(ma_slope)}")
print(f"\nBasic statistics:")
print(f"  Min:     {ma_slope.min():.2f}")
print(f"  Max:     {ma_slope.max():.2f}")
print(f"  Mean:    {ma_slope.mean():.2f}")
print(f"  Std:     {ma_slope.std():.2f}")

# Percentiles
percentiles = [1, 5, 10, 25, 30, 50, 70, 75, 90, 95, 99]
print(f"\nPercentiles:")
for p in percentiles:
    val = np.percentile(ma_slope, p)
    print(f"  {p:3d}%: {val:7.2f}")

# Check how many exceed the thresholds
print("\n" + "="*80)
print("THRESHOLD ANALYSIS:")
print("="*80)

weak_thresh = 50.0
strong_thresh = 100.0

print(f"\nLegacy detector thresholds:")
print(f"  Weak momentum:   {weak_thresh}")
print(f"  Strong momentum: {strong_thresh}")

above_weak = (abs(ma_slope) >= weak_thresh).sum()
above_strong = (abs(ma_slope) >= strong_thresh).sum()

print(f"\nValues exceeding thresholds:")
print(f"  |ma_slope| >= {weak_thresh}: {above_weak} ({above_weak/len(ma_slope)*100:.2f}%)")
print(f"  |ma_slope| >= {strong_thresh}: {above_strong} ({above_strong/len(ma_slope)*100:.2f}%)")

# Suggested thresholds based on percentiles
print("\n" + "="*80)
print("SUGGESTED THRESHOLD ADJUSTMENTS:")
print("="*80)

# Use 70th and 90th percentiles of absolute values
abs_ma_slope = abs(ma_slope)
weak_suggested = np.percentile(abs_ma_slope, 70)
strong_suggested = np.percentile(abs_ma_slope, 90)

print(f"\nBased on data distribution:")
print(f"  Weak momentum threshold:   {weak_suggested:.2f} (70th percentile)")
print(f"  Strong momentum threshold: {strong_suggested:.2f} (90th percentile)")

# Show what percentage would qualify with new thresholds
new_weak_count = (abs(ma_slope) >= weak_suggested).sum()
new_strong_count = (abs(ma_slope) >= strong_suggested).sum()

print(f"\nWith suggested thresholds:")
print(f"  Weak momentum signals:   {new_weak_count} ({new_weak_count/len(ma_slope)*100:.1f}%)")
print(f"  Strong momentum signals: {new_strong_count} ({new_strong_count/len(ma_slope)*100:.1f}%)")

# Check other relevant fields
print("\n" + "="*80)
print("OTHER RELEVANT FIELDS:")
print("="*80)

# ATR percent
if 'atr_percent_sec' in data.columns:
    atr = data['atr_percent_sec'].dropna()
    print(f"\natr_percent_sec distribution:")
    print(f"  Min: {atr.min():.4f}, Max: {atr.max():.4f}")
    print(f"  Mean: {atr.mean():.4f}, Std: {atr.std():.4f}")
    print(f"  25th percentile: {np.percentile(atr, 25):.4f}")
    print(f"  75th percentile: {np.percentile(atr, 75):.4f}")

# Volume imbalance (OBI)
if 'volume_imbalance' in data.columns:
    obi = data['volume_imbalance'].dropna()
    print(f"\nvolume_imbalance (OBI) distribution:")
    print(f"  Min: {obi.min():.3f}, Max: {obi.max():.3f}")
    print(f"  Mean: {obi.mean():.3f}, Std: {obi.std():.3f}")
    print(f"  50th percentile: {np.percentile(obi, 50):.3f}")
    print(f"  75th percentile: {np.percentile(obi, 75):.3f}")

print("\n" + "="*80)
print("CONCLUSION:")
print("="*80)
print("The ma_slope values in the enhanced hourly data are on a completely")
print("different scale than what the legacy detector expects!")
print(f"Current thresholds ({weak_thresh}/{strong_thresh}) are ~10-20x too high.")
print(f"Suggested adjustment: {weak_suggested:.1f}/{strong_suggested:.1f}")