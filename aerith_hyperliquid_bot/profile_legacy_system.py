#!/usr/bin/env python3
"""
Profile the Legacy System (Granular Microstructure GMS + TF-v2) to identify performance bottlenecks.
"""

import cProfile
import pstats
import io
import sys
import os
from pathlib import Path
from datetime import datetime

# Add the project root to Python path
project_root = Path(__file__).parent.resolve()
sys.path.insert(0, str(project_root))

def run_legacy_backtest():
    """Run the legacy system backtest for profiling."""
    print("Starting Legacy System backtest for profiling...")
    
    # Import and run the backtest with legacy configuration
    from hyperliquid_bot.backtester.run_backtest import main
    
    # Set command line arguments for the legacy configuration
    sys.argv = [
        'profile_legacy_system.py',
        '--override', 'configs/legacy_profile.yaml',
        '--timeframe', '1h',
        '--run-id', 'legacy_profile',
        '--skip-validation-warnings'
    ]
    
    # Run the main backtest function
    main()

def profile_legacy_system():
    """Profile the legacy system and generate analysis."""
    print("=== Legacy System Performance Profiling ===")
    print(f"Start time: {datetime.now()}")
    
    # Create profiler
    profiler = cProfile.Profile()
    
    # Run the backtest with profiling
    print("Running backtest with profiling enabled...")
    profiler.enable()
    
    try:
        run_legacy_backtest()
    except Exception as e:
        print(f"Error during backtest: {e}")
        raise
    finally:
        profiler.disable()
    
    print(f"Profiling completed at: {datetime.now()}")
    
    # Save profile data
    profile_file = "legacy_system_profile.prof"
    profiler.dump_stats(profile_file)
    print(f"Profile data saved to: {profile_file}")
    
    # Analyze profile data
    print("\n=== PROFILE ANALYSIS ===")
    
    # Create string buffer for profile output
    s = io.StringIO()
    ps = pstats.Stats(profiler, stream=s)
    
    # Sort by cumulative time and show top functions
    print("\n--- Top 15 Functions by Cumulative Time ---")
    ps.sort_stats('cumulative')
    ps.print_stats(15)
    cumulative_output = s.getvalue()
    print(cumulative_output)
    
    # Reset buffer and sort by total time
    s = io.StringIO()
    ps = pstats.Stats(profiler, stream=s)
    print("\n--- Top 15 Functions by Total Time (excluding subcalls) ---")
    ps.sort_stats('tottime')
    ps.print_stats(15)
    tottime_output = s.getvalue()
    print(tottime_output)
    
    # Reset buffer and look for data loading functions
    s = io.StringIO()
    ps = pstats.Stats(profiler, stream=s)
    print("\n--- Data Loading Related Functions ---")
    ps.sort_stats('cumulative')
    ps.print_stats('.*load.*|.*read.*|.*parquet.*|.*arrow.*|.*features.*', 20)
    data_loading_output = s.getvalue()
    print(data_loading_output)
    
    # Look for microstructure related functions
    s = io.StringIO()
    ps = pstats.Stats(profiler, stream=s)
    print("\n--- Microstructure Related Functions ---")
    ps.sort_stats('cumulative')
    ps.print_stats('.*microstructure.*|.*gms.*|.*regime.*|.*detector.*', 20)
    microstructure_output = s.getvalue()
    print(microstructure_output)
    
    # Save detailed analysis to file
    analysis_file = "legacy_system_profile_analysis.txt"
    with open(analysis_file, 'w') as f:
        f.write("=== Legacy System Performance Profile Analysis ===\n")
        f.write(f"Generated at: {datetime.now()}\n\n")
        f.write("=== Top 15 Functions by Cumulative Time ===\n")
        f.write(cumulative_output)
        f.write("\n=== Top 15 Functions by Total Time ===\n")
        f.write(tottime_output)
        f.write("\n=== Data Loading Related Functions ===\n")
        f.write(data_loading_output)
        f.write("\n=== Microstructure Related Functions ===\n")
        f.write(microstructure_output)
    
    print(f"\nDetailed analysis saved to: {analysis_file}")
    
    # Check for unexpected modern system data access
    print("\n=== CHECKING FOR UNEXPECTED MODERN SYSTEM DATA ACCESS ===")
    
    # Look for functions that might be accessing features_1s or l2_raw data
    s = io.StringIO()
    ps = pstats.Stats(profiler, stream=s)
    ps.sort_stats('cumulative')
    ps.print_stats('.*features_1s.*|.*l2_raw.*|.*1s.*|.*arrow.*', 20)
    modern_data_output = s.getvalue()
    
    if "features_1s" in modern_data_output or "l2_raw" in modern_data_output:
        print("⚠️  WARNING: Found potential access to modern system data sources!")
        print(modern_data_output)
    else:
        print("✅ No unexpected access to modern system data sources detected.")
    
    print("\n=== PROFILING COMPLETE ===")
    print(f"Profile file: {profile_file}")
    print(f"Analysis file: {analysis_file}")

if __name__ == "__main__":
    profile_legacy_system() 