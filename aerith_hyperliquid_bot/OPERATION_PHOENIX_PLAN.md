# OPERATION PHOENIX: Modern System Recovery Plan

## Executive Summary
Modern system performs 5x worse due to threshold bugs and wrong detector. We'll fix thresholds first (quick win), then rebuild cleanly while preserving what works.

## UPDATE: January 2025 Test Results

### Threshold Fix Test Failed ❌
- Fixed the 100,000x momentum scale error (0.001 → 100.0)
- Fixed the spread unit confusion (2.5 bps → 0.000050 decimal)
- **Result**: Performance got WORSE! (-10.74% ROI vs original +41.78%)
- **Conclusion**: The `continuous_modern_v2` detector is fundamentally flawed

### Root Cause Identified
The issue is deeper than just thresholds. The modern system uses a completely different detector (`continuous_modern_v2`) that behaves differently from the legacy `granular_microstructure` detector. Even with correct thresholds, it produces poor results.

### Next Action: Switch to Legacy Detector
- Changed `detector_type` from "continuous_modern_v2" to "granular_microstructure" in config
- This should immediately improve performance as we'll use the proven detector logic
- Need to test with legacy detector + modern data pipeline

## Track A: Emergency Performance Recovery (Days 1-3)

### Day 1: Fix Critical Thresholds
```yaml
# File: configs/overrides/modern_system_v2_complete.yaml

# FIX MOMENTUM (currently 100,000x too small!)
gms_mom_strong_thresh: 100.0    # was 0.001
gms_mom_weak_thresh: 50.0       # was 0.0003

# FIX SPREAD (wrong units!)
gms_spread_std_high_thresh: 0.000050   # was 2.5
gms_spread_mean_low_thresh: 0.000045   # was 8.0

# FIX UPDATE FREQUENCY 
gms_cadence_seconds: 3600       # was 60 (match data frequency)
```

### Day 2: Use Legacy Detector
```python
# Option 1: Quick config change
regime:
  detector_type: "granular_microstructure"  # Replace continuous_modern_v2
  
# Option 2: Feature flag in code
DETECTOR_MODE = os.getenv("DETECTOR_MODE", "legacy")  # legacy/modern
```

### Day 3: Validate & Test
- Run backtest on 2024 data
- Target: 180+ trades, >150% ROI (approaching legacy's 215%)
- Monitor for position management issues

## Track B: Clean Architecture (Weeks 1-4)

### Week 1: Core Module
```
hyperliquid_bot/
  core/
    market_data.py      # Unified data interface
    features.py         # All feature calculations
    signals.py          # Signal generation interface
    position.py         # Single position manager
```

### Week 2: Delete Bloat
Remove these duplicate/experimental modules:
- continuous_detector_v2.py
- enhanced_regime_detector.py  
- robust_backtest_engine.py
- All test_*/debug_* scripts not actively used

### Week 3: Deep Book Features
Implement high-value microstructure features:
```python
def calculate_deep_book_pressure(book_data):
    """Whale detection using levels 10-20"""
    deep_bid_volume = sum(book_data.bid_sz_10_20)
    deep_ask_volume = sum(book_data.ask_sz_10_20)
    return (deep_bid_volume - deep_ask_volume) / (deep_bid_volume + deep_ask_volume)

def calculate_liquidity_shape_entropy(book_data):
    """Detect regime changes from order book shape"""
    # Implementation using all 20 levels
```

### Week 4: Production Hardening
- Add parameter validation (prevent 100,000x errors)
- Implement circuit breakers
- Add comprehensive logging
- Create monitoring dashboard

## Success Metrics

### Immediate (Day 3)
- [ ] Backtest ROI > 150% (vs current 41.78%)
- [ ] Trade count 150-200 (vs current 222)
- [ ] Zero duplicate positions

### Week 1
- [ ] Modern matches legacy performance
- [ ] Clean test suite passing
- [ ] Parameter validation in place

### Week 4  
- [ ] Code reduced by 50%
- [ ] Deep book features tested
- [ ] Ready for paper trading

## Risk Mitigation

1. **Rollback Plan**: Keep legacy system running
2. **Testing**: Every change validated against 2024 baseline
3. **Gradual Rollout**: Paper trade → small live → full deployment

## Implementation Order

1. **CRITICAL**: Fix thresholds in modern_system_v2_complete.yaml
2. Test with corrected thresholds
3. If still underperforming, switch to legacy detector
4. Begin architecture cleanup in parallel
5. Add deep book features once stable

## Configuration Validation

Add this to prevent future threshold disasters:
```python
def validate_thresholds(config):
    """Ensure thresholds are in correct units/scale"""
    assert 10 < config.gms_mom_weak_thresh < 200, "Momentum thresh out of range"
    assert 0.00001 < config.gms_spread_mean_low_thresh < 0.001, "Spread thresh wrong units"
    # etc...
```

## Next Actions

1. Create branch: `feature/operation-phoenix`
2. Fix thresholds in config
3. Run backtest with fixed thresholds
4. Report results
5. Proceed based on performance