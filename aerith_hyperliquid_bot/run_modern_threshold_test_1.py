#!/usr/bin/env python3
"""
Run Modern System 2024 - Threshold Fix Test 1
Testing with spread_mean_low_thresh fixed from 1.2 to 0.0008
"""

import subprocess
import sys
import json
from pathlib import Path
from datetime import datetime

def main():
    print("=" * 80)
    print("MODERN SYSTEM - THRESHOLD FIX TEST 1")
    print("=" * 80)
    print()
    print("Fixed: spread_mean_low_thresh from 1.2 → 0.0008 (1500x error)")
    print("Testing full 2024...")
    print()
    
    # Create log directory if needed
    log_dir = Path("/Users/<USER>/Desktop/trading_bot_/logs")
    log_dir.mkdir(exist_ok=True)
    
    # Run full 2024
    cmd = [
        sys.executable,
        "scripts/run_modern_backtest.py",
        "--start-date", "2024-01-01",
        "--end-date", "2024-12-31",
        "--override", "configs/overrides/modern_system_v2_complete.yaml",
        "--output", "threshold_test_1_results.json"
    ]
    
    # Log file path
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"threshold_test_1_{timestamp}.log"
    
    print(f"Log file: {log_file}")
    print("-" * 80)
    
    # Run with output to both console and log
    with open(log_file, 'w') as f:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        for line in process.stdout:
            print(line.rstrip())
            f.write(line)
            f.flush()
        
        process.wait()
    
    if process.returncode != 0:
        print("\nBacktest failed!")
        return 1
    
    # Load and analyze results
    results_file = Path("threshold_test_1_results.json")
    if results_file.exists():
        with open(results_file) as f:
            data = json.load(f)
        
        perf = data['performance']
        trades = data['trades']
        completed = [t for t in trades if 'return' in t]
        
        print("\n" + "=" * 80)
        print("RESULTS - THRESHOLD FIX TEST 1")
        print("=" * 80)
        
        print(f"\nTrades:")
        print(f"  Total: {perf.get('total_trades', 0)}")
        print(f"  Completed: {len(completed)}")
        print(f"  Open: {perf.get('total_trades', 0) - len(completed)}")
        
        print(f"\nPerformance:")
        print(f"  Total Return: {perf.get('total_return', 0):.2%}")
        print(f"  Win Rate: {perf.get('win_rate', 0):.2%}")
        print(f"  Avg Return: {perf.get('avg_trade_return', 0):.2%}")
        print(f"  Best Trade: {perf.get('best_trade', 0):.2%}")
        print(f"  Worst Trade: {perf.get('worst_trade', 0):.2%}")
        
        print(f"\nComparison:")
        print(f"  Modern (Fixed): {perf.get('total_trades', 0)} trades, {perf.get('total_return', 0):.1%} ROI")
        print(f"  Modern (Before): 277 trades, -2.1% ROI (Jan only)")
        print(f"  Legacy: 189 trades, 215.0% ROI")
        
        # Save analysis
        analysis = {
            "test": "threshold_fix_1",
            "changes": {
                "gms_spread_mean_low_thresh": {
                    "from": 1.2,
                    "to": 0.0008,
                    "ratio": "1500x error fixed"
                }
            },
            "results": {
                "total_trades": perf.get('total_trades', 0),
                "completed_trades": len(completed),
                "total_return": perf.get('total_return', 0),
                "win_rate": perf.get('win_rate', 0),
                "avg_trade_return": perf.get('avg_trade_return', 0)
            },
            "comparison": {
                "legacy_trades": 189,
                "legacy_roi": 2.15,
                "improvement": perf.get('total_return', 0) - (-0.021)
            }
        }
        
        with open("threshold_test_1_analysis.json", 'w') as f:
            json.dump(analysis, f, indent=2)
        
        print(f"\n📊 Analysis saved to: threshold_test_1_analysis.json")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())