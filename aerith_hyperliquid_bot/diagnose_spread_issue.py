#!/usr/bin/env python3
"""
Diagnose why spread scores are always 0.4 (poor) in the enhanced detector.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import pandas as pd
import numpy as np
from hyperliquid_bot.config.settings import load_config

def analyze_spread_values():
    """Analyze spread values in enhanced data vs thresholds."""
    
    # Load config
    config_path = Path(__file__).parent / "configs/overrides/modern_system_v2_adjusted_thresholds.yaml"
    config = load_config(str(config_path))
    
    # Load data
    file = "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/../hyperliquid_data/enhanced_hourly/1h/2024-02-02_1h_enhanced.parquet"
    data = pd.read_parquet(file)
    
    print("=" * 80)
    print("SPREAD VALUE ANALYSIS")
    print("=" * 80)
    
    # Check what spread fields exist
    spread_cols = [col for col in data.columns if 'spread' in col.lower()]
    print(f"\nSpread-related columns found: {spread_cols}")
    
    if 'spread_mean' in data.columns:
        spread_mean = data['spread_mean'].dropna()
        print(f"\nspread_mean statistics:")
        print(f"  Count: {len(spread_mean)}")
        print(f"  Min: {spread_mean.min():.6f}")
        print(f"  Max: {spread_mean.max():.6f}")
        print(f"  Mean: {spread_mean.mean():.6f}")
        print(f"  50th percentile: {np.percentile(spread_mean, 50):.6f}")
        print(f"  75th percentile: {np.percentile(spread_mean, 75):.6f}")
    
    if 'spread_std' in data.columns:
        spread_std = data['spread_std'].dropna()
        print(f"\nspread_std statistics:")
        print(f"  Count: {len(spread_std)}")
        print(f"  Min: {spread_std.min():.6f}")
        print(f"  Max: {spread_std.max():.6f}")
        print(f"  Mean: {spread_std.mean():.6f}")
        print(f"  50th percentile: {np.percentile(spread_std, 50):.6f}")
        print(f"  75th percentile: {np.percentile(spread_std, 75):.6f}")
    
    # Compare to thresholds
    print("\n" + "=" * 80)
    print("THRESHOLD COMPARISON")
    print("=" * 80)
    
    low_spread_thresh = config.regime.gms_spread_mean_low_thresh
    high_spread_std = config.regime.gms_spread_std_high_thresh
    
    print(f"\nConfig thresholds:")
    print(f"  gms_spread_mean_low_thresh: {low_spread_thresh}")
    print(f"  gms_spread_std_high_thresh: {high_spread_std}")
    
    # Enhanced detector scoring logic (from line 180-191):
    # if spread_mean <= low_spread_thresh and spread_std < high_spread_std:
    #     return 1.0  # Excellent spread
    # elif spread_mean <= low_spread_thresh * 1.5:
    #     return 0.8  # Good spread
    # elif spread_mean <= low_spread_thresh * 2:
    #     return 0.6  # Acceptable spread
    # else:
    #     return 0.4  # Poor spread
    
    if 'spread_mean' in data.columns:
        print(f"\nSpread scoring breakdown:")
        excellent = sum((spread_mean <= low_spread_thresh) & (data['spread_std'] < high_spread_std))
        good = sum((spread_mean > low_spread_thresh) & (spread_mean <= low_spread_thresh * 1.5))
        acceptable = sum((spread_mean > low_spread_thresh * 1.5) & (spread_mean <= low_spread_thresh * 2))
        poor = sum(spread_mean > low_spread_thresh * 2)
        
        total = len(spread_mean)
        print(f"  Excellent (1.0): {excellent}/{total} ({excellent/total*100:.1f}%)")
        print(f"  Good (0.8): {good}/{total} ({good/total*100:.1f}%)")
        print(f"  Acceptable (0.6): {acceptable}/{total} ({acceptable/total*100:.1f}%)")
        print(f"  Poor (0.4): {poor}/{total} ({poor/total*100:.1f}%)")
        
        print(f"\nThreshold multiples:")
        print(f"  1x threshold: {low_spread_thresh:.6f}")
        print(f"  1.5x threshold: {low_spread_thresh * 1.5:.6f}")
        print(f"  2x threshold: {low_spread_thresh * 2:.6f}")
        
    # Check actual spread values vs thresholds
    print("\n" + "=" * 80)
    print("SPREAD VALUE DISTRIBUTION")
    print("=" * 80)
    
    if 'spread_mean' in data.columns:
        # Show how many values fall into each bucket
        for i, (idx, row) in enumerate(data.iterrows()):
            if i < 5:  # Show first 5 rows
                sm = row.get('spread_mean', np.nan)
                ss = row.get('spread_std', np.nan)
                print(f"\nRow {i}:")
                print(f"  spread_mean: {sm:.6f}")
                print(f"  spread_std: {ss:.6f}")
                print(f"  vs low_thresh: {sm/low_spread_thresh:.1f}x")
                print(f"  vs std_thresh: {ss/high_spread_std:.1f}x")

if __name__ == "__main__":
    analyze_spread_values()