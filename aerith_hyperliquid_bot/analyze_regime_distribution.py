#!/usr/bin/env python3
"""Analyze regime distribution from backtest results."""

import json

# Load results
with open('modern_backtest_results.json') as f:
    data = json.load(f)

print(f"Total hours analyzed: {len(data['regime_history'])}")

# Count regime types
regimes = {}
for hour in data['regime_history']:
    regime = hour['regime']
    regimes[regime] = regimes.get(regime, 0) + 1

print("\nRegime distribution:")
for regime, count in sorted(regimes.items()):
    percentage = count / len(data['regime_history']) * 100
    print(f"  {regime}: {count} hours ({percentage:.1f}%)")

# Check if there are any bull/bear regimes
bull_regimes = ['STRONG_BULL_TREND', 'WEAK_BULL_TREND', 'Strong_Bull_Trend', 'Weak_Bull_Trend']
bear_regimes = ['STRONG_BEAR_TREND', 'WEAK_BEAR_TREND', 'Strong_Bear_Trend', 'Weak_Bear_Trend']

bull_count = sum(regimes.get(r, 0) for r in bull_regimes)
bear_count = sum(regimes.get(r, 0) for r in bear_regimes)

print(f"\nTradeable regimes:")
print(f"  Bull regimes: {bull_count} hours")
print(f"  Bear regimes: {bear_count} hours")
print(f"  Total tradeable: {bull_count + bear_count} hours ({(bull_count + bear_count) / len(data['regime_history']) * 100:.1f}%)")

# Show some sample regime details
print("\nFirst 5 regime details:")
for i, hour in enumerate(data['regime_history'][:5]):
    print(f"\n{i+1}. {hour['timestamp']}:")
    print(f"   Regime: {hour['regime']}")
    print(f"   Confidence: {hour['confidence']:.4f}")
    print(f"   Momentum: {hour['momentum']:.6f}")
    print(f"   Volatility: {hour['volatility']:.6f}")