#!/usr/bin/env python3
"""
Quick test comparing hourly vs 60s regime updates for 1 week.
This verifies our hypothesis before the full cache completes.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import json
from hyperliquid_bot.modern.backtester_engine import ModernBacktestEngine
from hyperliquid_bot.config.settings import load_config

def test_regime_frequency():
    print("="*60)
    print("QUICK TEST: Hourly Cache vs No Cache (60s)")
    print("="*60)
    
    # Load config
    config = load_config('configs/overrides/modern_system_v2_complete.yaml')
    
    # Just 1 week for quick results
    start_date = datetime(2024, 3, 1)
    end_date = datetime(2024, 3, 7)
    
    print(f"\nTest Period: {start_date.date()} to {end_date.date()} (1 week)")
    
    # Test 1: With hourly cache
    print("\n" + "-"*60)
    print("TEST 1: WITH HOURLY CACHE")
    print("-"*60)
    
    engine_cached = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=True  # Uses hourly cache
    )
    
    results_cached = engine_cached.run_backtest()
    
    if results_cached:
        print(f"\nHourly Cache Results:")
        print(f"  Return: {results_cached.get('total_return', 0):.2%}")
        print(f"  Trades: {results_cached.get('total_trades', 0)}")
        print(f"  Win Rate: {results_cached.get('win_rate', 0):.1%}")
        
        profit_per_trade_cached = (results_cached.get('total_return', 0) / max(results_cached.get('total_trades', 1), 1) * 100)
        print(f"  Profit/Trade: {profit_per_trade_cached:.3%}")
    
    # Test 2: Without cache (60s updates)
    print("\n" + "-"*60)
    print("TEST 2: WITHOUT CACHE (60s updates)")
    print("-"*60)
    print("Note: This will be slower but more accurate...")
    
    engine_60s = ModernBacktestEngine(
        config=config,
        start_date=start_date,
        end_date=end_date,
        use_regime_cache=False  # Calculates every 60s
    )
    
    results_60s = engine_60s.run_backtest()
    
    if results_60s:
        print(f"\n60s Update Results:")
        print(f"  Return: {results_60s.get('total_return', 0):.2%}")
        print(f"  Trades: {results_60s.get('total_trades', 0)}")
        print(f"  Win Rate: {results_60s.get('win_rate', 0):.1%}")
        
        profit_per_trade_60s = (results_60s.get('total_return', 0) / max(results_60s.get('total_trades', 1), 1) * 100)
        print(f"  Profit/Trade: {profit_per_trade_60s:.3%}")
    
    # Comparison
    print("\n" + "="*60)
    print("COMPARISON ANALYSIS")
    print("="*60)
    
    if results_cached and results_60s:
        # Calculate improvements
        return_improvement = ((results_60s.get('total_return', 0) - results_cached.get('total_return', 0)) / 
                            max(abs(results_cached.get('total_return', 0.001)), 0.001) * 100)
        
        profit_improvement = ((profit_per_trade_60s - profit_per_trade_cached) / 
                            max(abs(profit_per_trade_cached), 0.001) * 100)
        
        print(f"\nReturn Improvement: {return_improvement:+.1f}%")
        print(f"Profit/Trade Improvement: {profit_improvement:+.1f}%")
        
        if profit_improvement > 50:
            print("\n✅ HYPOTHESIS CONFIRMED!")
            print("60-second regime updates significantly improve performance!")
            print("The full year cache generation will likely show even better results.")
        elif profit_improvement > 0:
            print("\n⚠️ Some improvement seen, but less than expected.")
            print("May need longer test period for conclusive results.")
        else:
            print("\n❌ No improvement or worse performance.")
            print("Need to investigate further.")
        
        # Save comparison
        comparison = {
            "test_period": f"{start_date.date()} to {end_date.date()}",
            "hourly_cache": {
                "return": results_cached.get('total_return', 0),
                "trades": results_cached.get('total_trades', 0),
                "win_rate": results_cached.get('win_rate', 0),
                "profit_per_trade": profit_per_trade_cached
            },
            "60s_updates": {
                "return": results_60s.get('total_return', 0),
                "trades": results_60s.get('total_trades', 0),
                "win_rate": results_60s.get('win_rate', 0),
                "profit_per_trade": profit_per_trade_60s
            },
            "improvements": {
                "return_pct": return_improvement,
                "profit_per_trade_pct": profit_improvement
            }
        }
        
        with open('quick_60s_comparison.json', 'w') as f:
            json.dump(comparison, f, indent=2)
        print(f"\n📁 Detailed comparison saved to: quick_60s_comparison.json")

if __name__ == "__main__":
    test_regime_frequency()