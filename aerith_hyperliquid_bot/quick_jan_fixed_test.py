#!/usr/bin/env python3
"""Quick January test with fixed thresholds"""

import subprocess
import sys
import json

cmd = [
    sys.executable,
    "scripts/run_modern_backtest.py",
    "--start-date", "2024-01-01",
    "--end-date", "2024-01-31",
    "--override", "configs/overrides/modern_system_v2_complete.yaml",
    "--output", "jan_fixed.json"
]

print("Testing January with fixed thresholds...")
result = subprocess.run(cmd, capture_output=True, text=True)

if result.returncode == 0:
    with open("jan_fixed.json") as f:
        data = json.load(f)
    perf = data['performance']
    print(f"\nResults:")
    print(f"Trades: {perf.get('total_trades', 0)}")  
    print(f"Return: {perf.get('total_return', 0):.2%}")
    print(f"Win Rate: {perf.get('win_rate', 0):.2%}")
    print(f"\nBefore fix: 23 trades, -2.13%")
    print(f"After fix: {perf.get('total_trades', 0)} trades, {perf.get('total_return', 0):.2%}")
else:
    print("Failed!")
    print(result.stderr)