# Architectural Decision: Regime-Dominant vs EMA-Dominant Entry Logic

## Current Situation
Our modern trading system has a temporal misalignment issue that's causing 100% directional bias:

### The Problem Flow:
1. **Regime Detector** (60-second updates): Correctly identifies market regimes
   - 2024 distribution: 21.5% Bull, 21% Bear, 57.5% Neutral ✓
   
2. **Signal Generation** (hourly EMAs 20/50): Overrides regime signals
   - In 2024 bull market: EMAs stayed bullish even during bear regimes
   - Result: 216 trades, 100% long, 0% short (even took longs in bear regimes!)

### Current Entry Logic:
```python
# For SHORT entry, ALL conditions must be true:
if regime in ['Strong_Bear_Trend', 'Weak_Bear_Trend']:
    if ema_fast < ema_slow AND forecast < -threshold:
        enter_short()
```

### The Core Issue:
- Bear regime detected at 10:00 (60s update) ✓
- But EMAs (20/50 hourly) still show fast > slow from recent bull trend ✗
- Result: No short entry allowed despite correct regime detection

## Our Attempted Fix:
Added "regime-dominant" entry for strong regimes:
```python
# Trust regime more than EMAs in strong trends
if regime == 'Strong_Bear_Trend' AND confidence > 0.85:
    if forecast < 0:  # Just negative, not full threshold
        enter_short()  # Don't require EMA confirmation
```

## Questions for Architectural Guidance:

1. **Is regime-dominant entry philosophically sound?** 
   - Should we trust 60-second regime updates over hourly EMAs?
   - Or does this violate the principle of multiple confirmations?

2. **Alternative approaches to consider:**
   - Dynamic EMA periods based on regime (8/21 in trends, 20/50 in ranges)?
   - Weighted scoring system (regime 60%, EMAs 40%)?
   - Separate strategies for different regimes?

3. **Risk considerations:**
   - Will regime-dominant entries increase false signals?
   - How to balance responsiveness vs reliability?

## Context:
- Target: ~180 trades/year (legacy system baseline)
- Current: 216 trades but 100% directional bias
- Need: Balanced long/short distribution while maintaining quality