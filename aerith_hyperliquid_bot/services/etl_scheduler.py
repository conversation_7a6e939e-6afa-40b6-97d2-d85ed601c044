#!/usr/bin/env python
# services/etl_scheduler.py

"""
ETL Scheduler Service

This service monitors the raw L2 data directory and automatically processes
new data files using the ETL pipeline. It runs as a background service,
waking up periodically to check for new files and process them.

The scheduler detects if the previous hour's raw files are complete and
calls the ETL pipeline to process them. It logs success/failure and retries
once on error.

Usage:
    python -m services.etl_scheduler
"""

import os
import sys
import asyncio
import logging
import subprocess
from pathlib import Path
from datetime import datetime, timedelta
import time
from typing import Dict, List, Optional, Tuple, Union, Any

# Add project root to path for imports
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from hyperliquid_bot.config.settings import load_config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("etl_scheduler")

class ETLScheduler:
    """
    ETL Scheduler Service

    This class implements a lightweight scheduler that periodically checks for
    new raw L2 data files and processes them using the ETL pipeline.
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the ETL scheduler.

        Args:
            config_path: Path to the configuration file (optional)
        """
        # Load configuration
        if config_path is None:
            # Default to base.yaml in the configs directory
            config_path = str(Path(__file__).resolve().parent.parent / "configs" / "base.yaml")
        self.config = load_config(config_path)

        # Get ETL settings
        self.chunk_sec = self.config.etl.l20_to_1s.chunk_sec
        self.poll_sec = getattr(self.config.scheduler, 'etl_poll_sec', self.chunk_sec // 2)
        self.enabled = getattr(self.config.scheduler, 'etl_enabled', True)

        # Get paths
        self.raw_l2_dir = Path(self.config.data_paths.raw_l2_dir)
        self.feature_1s_dir = Path(self.config.data_paths.feature_1s_dir)

        # Get depth from config
        self.depth = self.config.microstructure.depth_levels

        # Initialize state
        self.processed_files = set()
        self.running = False

        logger.info(f"ETL Scheduler initialized with:")
        logger.info(f"  - Poll interval: {self.poll_sec} seconds")
        logger.info(f"  - Chunk size: {self.chunk_sec} seconds")
        logger.info(f"  - Raw L2 directory: {self.raw_l2_dir}")
        logger.info(f"  - Feature 1s directory: {self.feature_1s_dir}")
        logger.info(f"  - Depth levels: {self.depth}")
        logger.info(f"  - Enabled: {self.enabled}")

    async def start(self):
        """Start the ETL scheduler."""
        if not self.enabled:
            logger.info("ETL Scheduler is disabled in configuration. Exiting.")
            return

        logger.info("Starting ETL Scheduler...")
        self.running = True

        while self.running:
            try:
                await self.check_and_process_files()
            except Exception as e:
                logger.error(f"Error in ETL Scheduler: {e}", exc_info=True)

            # Sleep until next check
            await asyncio.sleep(self.poll_sec)

    def stop(self):
        """Stop the ETL scheduler."""
        logger.info("Stopping ETL Scheduler...")
        self.running = False

    async def check_and_process_files(self):
        """Check for new files and process them."""
        # Get current time
        now = datetime.now()

        # Check for files from the previous hour
        prev_hour = now - timedelta(hours=1)
        date_str = prev_hour.strftime("%Y-%m-%d")
        hour = prev_hour.hour

        # Check if files for the previous hour are complete
        if self.is_hour_complete(date_str, hour):
            # Process the hour
            await self.process_hour(date_str, hour)

    def is_hour_complete(self, date_str: str, hour: int) -> bool:
        """
        Check if the raw files for a specific hour are complete.

        Args:
            date_str: Date string in YYYY-MM-DD format
            hour: Hour (0-23)

        Returns:
            True if the hour's files are complete, False otherwise
        """
        # Convert date format for file matching
        date_pattern = date_str.replace("-", "")

        # Look for files with patterns like:
        # - lob_20250525_13.arrow (hourly files)
        raw_files = list(self.raw_l2_dir.glob(f"*{date_pattern}_{hour:02d}*.arrow")) + \
                    list(self.raw_l2_dir.glob(f"*{date_pattern}_{hour:02d}*.parquet"))

        # Check if any files were found
        if not raw_files:
            logger.debug(f"No raw files found for {date_str} hour {hour}")
            return False

        # Check if the output file already exists
        output_file = self.feature_1s_dir / f"features_{date_pattern}_{hour:02d}.parquet"
        if output_file.exists():
            # Skip if already processed
            if str(output_file) in self.processed_files:
                return False

            logger.info(f"Output file {output_file} already exists, marking as processed")
            self.processed_files.add(str(output_file))
            return False

        # Check if the newest file is at least 5 minutes old
        # This helps ensure the hour's data is complete
        newest_file = max(raw_files, key=lambda f: f.stat().st_mtime)
        file_age = time.time() - newest_file.stat().st_mtime

        if file_age < 300:  # 5 minutes in seconds
            logger.debug(f"Newest file for {date_str} hour {hour} is only {file_age:.0f} seconds old, waiting for more data")
            return False

        logger.info(f"Raw files for {date_str} hour {hour} are complete")
        return True

    async def process_hour(self, date_str: str, hour: int, retry_count: int = 1):
        """
        Process a single hour of data using the ETL pipeline.

        Args:
            date_str: Date string in YYYY-MM-DD format
            hour: Hour (0-23)
            retry_count: Number of retries on failure (default: 1)
        """
        logger.info(f"Processing {date_str} hour {hour}")

        # Build command
        cmd = [
            sys.executable,
            "-m", "tools.etl_l20_to_1s",
            "--date", date_str,
            "--depth", str(self.depth),
            "--force"  # Always force to ensure latest data is used
        ]

        # Run the command
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                logger.info(f"Successfully processed {date_str} hour {hour}")

                # Add to processed files
                date_pattern = date_str.replace("-", "")
                output_file = self.feature_1s_dir / f"features_{date_pattern}_{hour:02d}.parquet"
                self.processed_files.add(str(output_file))
            else:
                logger.error(f"Error processing {date_str} hour {hour}: {stderr.decode()}")

                # Retry if needed
                if retry_count > 0:
                    logger.info(f"Retrying processing of {date_str} hour {hour} ({retry_count} retries left)")
                    await asyncio.sleep(10)  # Wait a bit before retrying
                    await self.process_hour(date_str, hour, retry_count - 1)

        except Exception as e:
            logger.error(f"Exception processing {date_str} hour {hour}: {e}", exc_info=True)

            # Retry if needed
            if retry_count > 0:
                logger.info(f"Retrying processing of {date_str} hour {hour} ({retry_count} retries left)")
                await asyncio.sleep(10)  # Wait a bit before retrying
                await self.process_hour(date_str, hour, retry_count - 1)

async def main():
    """Main entry point."""
    # Create and start the scheduler
    scheduler = ETLScheduler()

    try:
        await scheduler.start()
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, stopping scheduler")
        scheduler.stop()
    except Exception as e:
        logger.error(f"Unhandled exception: {e}", exc_info=True)
        scheduler.stop()

if __name__ == "__main__":
    asyncio.run(main())
