# AI Collaboration Context (Aerith Hyperliquid Bot - PRD v2.1)

## Project Status

Starting **Phase 3.1** of a new 13-week roadmap focused initially on filter validation, followed by a likely pivot to microstructure strategies.

## Key Changes & Findings

*   The previous TF+GMS strategy (using a 7-state Granular Microstructure detector) has been **archived** due to unreliable regime classifications by the GMS detector, despite achieving high simulated ROI with an improved (but optimistic) probabilistic maker simulation model (~130%+). A forced-taker simulation yielded ~113% ROI. Reproducibility issues were fixed.
*   Visualization tools are implemented.
*   **Current Simulation Baseline:**
    *   "Probabilistic Maker" model is used for general exploration (acknowledged as optimistic - ignores price-touch checks and delay opportunity cost).
    *   "Taker-Only" model is used for critical decision gates requiring conservative assumptions.

## Current Roadmap (Summary)

*   **Phase 3.1 (Days 1-5 - CURRENT):** Integrate external data (Fear&Greed direct API, Sentiment, Whale Flow) as potential features/filters for the baseline TF logic. Identify candidate features (|ρ|>0.2, AUROC>0.55).
*   **Phase 3.2 (Days 6-10):** Test simple 3-state HMM as regime filter ('skip if chop') for baseline TF.
    *   **Decision Gate:** Keep Filtered TF if PF>=1.15 & DD<=20% & Trades<=~80 (Taker Sim). Otherwise, archive TF entirely.
*   **Phase 4+ (Likely Path):** Pivot to OBI Scalper if TF fails gate. Simplify config. Setup tick data. Implement OBI strat. Test OBI (Target Sharpe>=1.2 Taker Sim). Add OFI/Liq later.
*   **Phase 5/6:** Further microstructure strategies & hardening for live trading.

## Immediate Next Task

Implement fetching Fear & Greed data from Alternative.me API (direct call, not MCP wrapper initially) and add it as `fear_greed_idx` column to signals DataFrame, likely via `DataHandler` or `SignalCalculator`.

## AI Collaboration Guidance

*   Assist with implementing data fetching (Phase 3.1).
*   Assist with HMM implementation/validation (Phase 3.2).
*   Help evaluate results against decision gate criteria.
*   Assist with config simplification after Phase 3 decision.
*   Assist with OBI scalper design/implementation (Phase 4).
*   Use the correct simulation mode per task context (ProbMaker for exploration, Taker-Only for gates). Be mindful of simulation optimism.
*   Refer to PRD v2.1 for details on scope, roadmap, and technical decisions.
