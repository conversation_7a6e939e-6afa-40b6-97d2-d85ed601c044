#!/usr/bin/env python3
"""
MODERN SYSTEM 2024 BASELINE TEST
================================

Run full 2024 backtest with enhanced hourly data.
Expected: ~200 trades, ~100% ROI

Baseline (Legacy): 189 trades, 215% ROI
"""

import subprocess
import sys
import os
from pathlib import Path
from datetime import datetime
import json
import time

# Colors for terminal output
RED = '\033[91m'
GREEN = '\033[92m'
YELLOW = '\033[93m'
BLUE = '\033[94m'
RESET = '\033[0m'

def main():
    # Clear screen
    os.system('clear' if os.name == 'posix' else 'cls')
    
    print(f"{BLUE}{'=' * 80}{RESET}")
    print(f"{BLUE}MODERN SYSTEM 2024 BASELINE TEST{RESET}")
    print(f"{BLUE}{'=' * 80}{RESET}")
    print()
    
    # Full 2024 settings
    start_date = "2024-01-01"
    end_date = "2024-12-31"
    config_file = "configs/overrides/modern_system_v2_complete.yaml"
    
    print(f"{GREEN}Configuration:{RESET}")
    print(f"  • Config: {config_file}")
    print(f"  • Period: {start_date} to {end_date} (Full 2024)")
    print(f"  • Risk per trade: 25%")
    print(f"  • Data: Enhanced hourly (3,600x faster)")
    print()
    
    print(f"{GREEN}Expected Performance:{RESET}")
    print(f"  • Trades: ~200 (vs Legacy: 189)")
    print(f"  • ROI: ~100% (vs Legacy: 215%)")
    print()
    
    # Get script directory
    script_dir = Path(__file__).resolve().parent
    
    # Build command
    cmd = [
        sys.executable,
        str(script_dir / "scripts" / "run_modern_backtest.py"),
        "--start-date", start_date,
        "--end-date", end_date,
        "--override", config_file,
        "--output", "modern_2024_baseline.json"
    ]
    
    print(f"{BLUE}Starting 2024 baseline test...{RESET}")
    print("-" * 80)
    
    start_time = time.time()
    
    try:
        # Run the backtest
        process = subprocess.Popen(
            cmd,
            cwd=str(script_dir),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Track trades
        trade_count = 0
        
        # Stream output
        for line in process.stdout:
            # Count trades
            if "Trade #" in line:
                trade_count += 1
                if trade_count % 10 == 0:
                    print(f"{YELLOW}Progress: {trade_count} trades...{RESET}")
            # Highlight important lines
            elif "Total Trades:" in line:
                print(f"{GREEN}{line.rstrip()}{RESET}")
            elif "Total Return:" in line:
                print(f"{GREEN}{line.rstrip()}{RESET}")
            elif "ERROR" in line or "Error" in line:
                print(f"{RED}{line.rstrip()}{RESET}")
            elif "Loading enhanced hourly features" in line:
                print(f"{BLUE}{line.rstrip()}{RESET}")
            elif "Loaded" in line and "enhanced hourly bars" in line:
                print(f"{GREEN}{line.rstrip()}{RESET}")
        
        # Wait for completion
        process.wait()
        
        elapsed_time = time.time() - start_time
        
        if process.returncode == 0:
            print()
            print(f"{GREEN}{'=' * 80}{RESET}")
            print(f"{GREEN}✅ BASELINE TEST COMPLETED!{RESET}")
            print(f"{GREEN}{'=' * 80}{RESET}")
            
            print(f"\n⏱️  Total time: {elapsed_time:.1f} seconds")
            
            # Load and display results
            results_file = script_dir / "modern_2024_baseline.json"
            if results_file.exists():
                with open(results_file) as f:
                    results = json.load(f)
                
                perf = results.get('performance', {})
                trades = results.get('trades', [])
                
                print(f"\n{BLUE}📊 RESULTS SUMMARY:{RESET}")
                print(f"  • Total Trades: {perf.get('total_trades', 0)}")
                print(f"  • Total Return: {perf.get('total_return', 0):.2%}")
                print(f"  • Win Rate: {perf.get('win_rate', 0):.2%}")
                print(f"  • Avg Trade Return: {perf.get('avg_trade_return', 0):.2%}")
                print(f"  • Best Trade: {perf.get('best_trade', 0):.2%}")
                print(f"  • Worst Trade: {perf.get('worst_trade', 0):.2%}")
                print(f"  • Max Drawdown: {perf.get('max_drawdown', 0):.2%}")
                print(f"  • Sharpe Ratio: {perf.get('sharpe_ratio', 0):.2f}")
                
                # Compare with legacy
                print(f"\n{YELLOW}COMPARISON WITH LEGACY:{RESET}")
                print(f"  • Modern: {perf.get('total_trades', 0)} trades, {perf.get('total_return', 0):.1%} ROI")
                print(f"  • Legacy: 189 trades, 215.0% ROI")
                
                # Monthly breakdown
                if 'monthly_returns' in results:
                    print(f"\n{BLUE}Monthly Returns:{RESET}")
                    for month, ret in results['monthly_returns'].items():
                        print(f"  • {month}: {ret:.2%}")
                
                print(f"\n📁 Full results saved to: {results_file}")
                
        else:
            print()
            print(f"{RED}{'=' * 80}{RESET}")
            print(f"{RED}❌ BASELINE TEST FAILED{RESET}")
            print(f"{RED}{'=' * 80}{RESET}")
            
        return process.returncode
        
    except KeyboardInterrupt:
        print(f"\n\n{YELLOW}⚠️  Test interrupted by user{RESET}")
        return 1
    except Exception as e:
        print(f"\n{RED}Error: {e}{RESET}")
        return 1


if __name__ == "__main__":
    sys.exit(main())