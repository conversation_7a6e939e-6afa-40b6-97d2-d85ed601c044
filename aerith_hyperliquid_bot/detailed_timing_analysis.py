#!/usr/bin/env python3
"""
Detailed Timing Analysis for 2024 Full Backtest
Uses the main config.yaml settings and provides comprehensive timing breakdown.
"""

import cProfile
import pstats
import io
import sys
import os
import tracemalloc
import time
from pathlib import Path
from datetime import datetime
import pandas as pd
import json

# Add the project root to Python path
project_root = Path(__file__).parent.resolve()
sys.path.insert(0, str(project_root))

class DetailedTimingAnalyzer:
    def __init__(self):
        self.timing_data = {}
        self.memory_snapshots = []
        self.operation_times = {}
        self.start_time = None
        
    def run_full_2024_backtest(self):
        """Run full 2024 backtest with detailed timing analysis."""
        print("=== DETAILED TIMING ANALYSIS: 2024 FULL BACKTEST ===")
        print(f"Start time: {datetime.now()}")
        print("Using main config.yaml settings...")
        
        # Start memory tracking
        tracemalloc.start()
        self.start_time = time.time()
        
        # Create profiler
        profiler = cProfile.Profile()
        
        # Take initial memory snapshot
        snapshot_start = tracemalloc.take_snapshot()
        self.memory_snapshots.append(("start", snapshot_start))
        
        print("Running 2024 backtest with comprehensive profiling...")
        profiler.enable()
        
        try:
            self._run_backtest_with_config()
        except Exception as e:
            print(f"Error during backtest: {e}")
            raise
        finally:
            profiler.disable()
            
            # Take final memory snapshot
            snapshot_end = tracemalloc.take_snapshot()
            self.memory_snapshots.append(("end", snapshot_end))
            tracemalloc.stop()
        
        total_time = time.time() - self.start_time
        print(f"Backtest completed at: {datetime.now()}")
        print(f"Total execution time: {total_time:.2f} seconds")
        
        # Analyze results
        self._analyze_detailed_timing(profiler, total_time)
        self._analyze_memory_usage()
        self._generate_timing_report()
        
    def _run_backtest_with_config(self):
        """Run the backtest using main config.yaml."""
        from hyperliquid_bot.backtester.run_backtest import main
        
        # Set command line arguments to use main config.yaml
        sys.argv = [
            'detailed_timing_analysis.py',
            '--timeframe', '1h',
            '--run-id', 'detailed_timing_2024',
            '--skip-validation-warnings'
        ]
        
        # Run the main backtest function
        main()
    
    def _analyze_detailed_timing(self, profiler, total_time):
        """Analyze timing with detailed breakdown by operation category."""
        print("\n=== DETAILED TIMING BREAKDOWN ===")
        
        # Save profile data
        profile_file = "detailed_timing_2024_profile.prof"
        profiler.dump_stats(profile_file)
        
        # Create stats object
        stats = pstats.Stats(profiler)
        
        # Define operation categories and their patterns
        operation_categories = {
            "Time Conversion": [
                "to_utc_naive",
                "tz_convert",
                "tz_localize", 
                "timezone",
                "apply.*time"
            ],
            "Data Loading - OHLCV": [
                "_load_ohlcv",
                "read_parquet.*ohlcv",
                "load_historical_data"
            ],
            "Data Loading - L2": [
                "_load_l2_segment",
                "_integrate_microstructure",
                "read_parquet.*l2",
                "raw2"
            ],
            "Feature Calculation": [
                "_calculate_features_from_row",
                "calculate_order_book_imbalance",
                "calculate_bid_ask_spread",
                "calculate_depth_metrics",
                "microstructure"
            ],
            "Technical Analysis": [
                "pandas_ta",
                "talib",
                "ema",
                "atr",
                "sma",
                "SignalCalculator"
            ],
            "Regime Detection": [
                "GranularMicrostructure",
                "detect_regime",
                "RegimeDetector",
                "_classify_regime"
            ],
            "Strategy Evaluation": [
                "TrendFollowing",
                "evaluate_strategy",
                "StrategyEvaluator",
                "_generate_signal"
            ],
            "Portfolio Management": [
                "Portfolio",
                "RiskManager",
                "calculate_position",
                "_update_portfolio"
            ],
            "Execution Simulation": [
                "ExecutionSimulator",
                "_simulate_execution",
                "_process_order"
            ]
        }
        
        # Analyze each category
        category_times = {}
        
        for category, patterns in operation_categories.items():
            category_time = self._get_category_time(stats, patterns)
            category_times[category] = category_time
            percentage = (category_time / total_time) * 100
            print(f"{category:25}: {category_time:8.2f}s ({percentage:5.1f}%)")
        
        # Store timing data
        self.timing_data = {
            "total_time": total_time,
            "category_times": category_times,
            "category_percentages": {k: (v/total_time)*100 for k, v in category_times.items()}
        }
        
        # Show top individual functions
        print(f"\n=== TOP 10 INDIVIDUAL FUNCTIONS ===")
        s = io.StringIO()
        ps = pstats.Stats(stats.stats, stream=s)
        ps.sort_stats('cumulative')
        ps.print_stats(10)
        output = s.getvalue()
        print(output)
        
    def _get_category_time(self, stats, patterns):
        """Get total time for functions matching any pattern in the category."""
        total_time = 0.0
        
        for func_key, func_stats in stats.stats.items():
            filename, line_num, func_name = func_key
            full_func_path = f"{filename}:{line_num}({func_name})"
            
            # Check if this function matches any pattern in the category
            for pattern in patterns:
                if pattern.lower() in full_func_path.lower() or pattern.lower() in func_name.lower():
                    # func_stats = (cc, nc, tt, ct, callers)
                    # tt = total time, ct = cumulative time
                    total_time += func_stats[2]  # Add total time
                    break
        
        return total_time
    
    def _analyze_memory_usage(self):
        """Analyze memory usage patterns."""
        print("\n=== MEMORY USAGE ANALYSIS ===")
        
        if len(self.memory_snapshots) >= 2:
            start_snapshot = self.memory_snapshots[0][1]
            end_snapshot = self.memory_snapshots[1][1]
            
            # Calculate memory difference
            top_stats = end_snapshot.compare_to(start_snapshot, 'lineno')
            
            total_memory_mb = sum(stat.size_diff for stat in top_stats) / 1024 / 1024
            self.timing_data['memory_usage_mb'] = total_memory_mb
            
            print(f"Total memory usage: {total_memory_mb:.2f} MB")
            
            # Show top memory consumers
            print("Top 5 memory consumers:")
            for index, stat in enumerate(top_stats[:5]):
                print(f"  {index+1}. {stat.traceback.format()[-1].strip()}: {stat.size_diff/1024/1024:.2f} MB")
    
    def _generate_timing_report(self):
        """Generate comprehensive timing report."""
        print("\n=== COMPREHENSIVE TIMING REPORT ===")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "config_used": "config.yaml (main configuration)",
            "backtest_period": "2024 (full year)",
            "timing_analysis": self.timing_data,
            "recommendations": self._generate_recommendations()
        }
        
        # Save report to file
        report_file = "detailed_timing_2024_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"Detailed report saved to: {report_file}")
        
        # Print priority summary
        print(f"\n=== OPTIMIZATION PRIORITIES ===")
        category_times = self.timing_data.get('category_times', {})
        sorted_categories = sorted(category_times.items(), key=lambda x: x[1], reverse=True)
        
        for i, (category, time_spent) in enumerate(sorted_categories[:5], 1):
            percentage = (time_spent / self.timing_data['total_time']) * 100
            print(f"Priority {i}: {category}")
            print(f"  Time: {time_spent:.2f}s ({percentage:.1f}% of total)")
            print(f"  Potential savings: {self._estimate_savings(category, time_spent)}")
            print()
    
    def _estimate_savings(self, category, time_spent):
        """Estimate potential time savings for each category."""
        savings_estimates = {
            "Time Conversion": (0.90, "Replace apply() with vectorized operations"),
            "Data Loading - L2": (0.70, "Implement caching and memory mapping"),
            "Data Loading - OHLCV": (0.50, "Optimize file reading and preprocessing"),
            "Feature Calculation": (0.60, "Vectorize calculations and cache results"),
            "Technical Analysis": (0.40, "Cache indicators and use faster libraries"),
            "Regime Detection": (0.30, "Optimize threshold calculations"),
            "Strategy Evaluation": (0.20, "Streamline signal generation"),
            "Portfolio Management": (0.15, "Optimize position calculations"),
            "Execution Simulation": (0.10, "Minor optimizations possible")
        }
        
        if category in savings_estimates:
            reduction_pct, description = savings_estimates[category]
            potential_savings = time_spent * reduction_pct
            return f"{potential_savings:.2f}s ({reduction_pct*100:.0f}% reduction) - {description}"
        else:
            return "Unknown optimization potential"
    
    def _generate_recommendations(self):
        """Generate optimization recommendations based on timing analysis."""
        recommendations = []
        category_times = self.timing_data.get('category_times', {})
        total_time = self.timing_data.get('total_time', 1)
        
        # Sort by time spent
        sorted_categories = sorted(category_times.items(), key=lambda x: x[1], reverse=True)
        
        for category, time_spent in sorted_categories:
            percentage = (time_spent / total_time) * 100
            if percentage > 5:  # Focus on categories taking >5% of time
                if category == "Time Conversion":
                    recommendations.append(f"HIGH PRIORITY: Replace apply(to_utc_naive) with vectorized pd.to_datetime operations (saves ~{time_spent*0.9:.1f}s)")
                elif category == "Data Loading - L2":
                    recommendations.append(f"HIGH PRIORITY: Implement L2 segment caching and memory mapping (saves ~{time_spent*0.7:.1f}s)")
                elif category == "Feature Calculation":
                    recommendations.append(f"MEDIUM PRIORITY: Vectorize microstructure calculations (saves ~{time_spent*0.6:.1f}s)")
                elif category == "Technical Analysis":
                    recommendations.append(f"MEDIUM PRIORITY: Cache TA indicators and consider talib (saves ~{time_spent*0.4:.1f}s)")
        
        return recommendations

def main():
    """Main function to run detailed timing analysis."""
    analyzer = DetailedTimingAnalyzer()
    analyzer.run_full_2024_backtest()

if __name__ == "__main__":
    main()