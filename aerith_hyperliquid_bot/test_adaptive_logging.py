#!/usr/bin/env python3
"""
Test script to verify adaptive threshold logging
"""
import sys
import os
import logging
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.core.gms_detector import ContinuousGMSDetector

def test_adaptive_logging():
    """Test that the detector logs ADAPTIVE mode correctly"""
    
    # Set up logging to see the detector's log output
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)-7s] %(name)-25s: %(message)s'
    )
    
    print("=== Testing Adaptive Threshold Logging ===")
    
    # Load config
    config = load_config('configs/base.yaml')
    
    print(f"auto_thresholds setting: {getattr(config.gms, 'auto_thresholds', 'NOT_FOUND')}")
    
    # Create detector - this should log ADAPTIVE mode
    print("\nCreating detector (watch for log output):")
    detector = ContinuousGMSDetector(config)
    
    print(f"\nDetector created. Adaptive thresholds initialized: {detector.adaptive_vol_threshold is not None}")

if __name__ == "__main__":
    test_adaptive_logging()
