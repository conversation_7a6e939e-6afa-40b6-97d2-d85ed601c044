#!/usr/bin/env python3
"""
Test script to verify legacy configuration isolation.
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from hyperliquid_bot.legacy.config import LegacyConfig
from hyperliquid_bot.legacy.config_adapter import LegacyConfigAdapter


def test_legacy_config():
    """Test legacy configuration loading and validation."""
    print("Testing Legacy Configuration Isolation")
    print("=" * 50)
    
    # Load frozen legacy config
    legacy_config = LegacyConfig.load_frozen()
    print("\n✅ Legacy config loaded successfully")
    
    # Validate critical values
    print("\nValidating critical values:")
    print(f"  - Detector type: {legacy_config.regime.detector_type}")
    print(f"  - Risk fraction: {legacy_config.tf_v3.risk_frac}")
    print(f"  - Vol high threshold: {legacy_config.regime.gms_vol_high_thresh}")
    print(f"  - Vol low threshold: {legacy_config.regime.gms_vol_low_thresh}")
    print(f"  - Mom strong threshold: {legacy_config.regime.gms_mom_strong_thresh}")
    print(f"  - Mom weak threshold: {legacy_config.regime.gms_mom_weak_thresh}")
    print(f"  - Map weak bear to bear: {legacy_config.regime.map_weak_bear_to_bear}")
    
    # Validate config
    is_valid = legacy_config.validate()
    print(f"\n✅ Configuration validation: {'PASSED' if is_valid else 'FAILED'}")
    
    # Test adapter
    print("\nTesting configuration adapter:")
    adapter = LegacyConfigAdapter(legacy_config)
    
    try:
        # Try to convert to Config format
        # Note: This will fail until we update the Config class constructor
        # For now, just test the adapter creation
        print("✅ Adapter created successfully")
        
        # Test adapter validation
        adapter_valid = adapter.validate()
        print(f"✅ Adapter validation: {'PASSED' if adapter_valid else 'FAILED'}")
        
    except Exception as e:
        print(f"⚠️  Adapter conversion not yet implemented: {e}")
        print("   This is expected until we update the Config class")
    
    print("\n" + "=" * 50)
    print("Legacy configuration isolation test complete!")


if __name__ == "__main__":
    test_legacy_config()