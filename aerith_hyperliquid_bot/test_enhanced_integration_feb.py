#!/usr/bin/env python3
"""
Integration Test: Enhanced Detector + Robust Backtest Engine
=============================================================

This script tests the integration between:
- Enhanced detector (wraps legacy logic)
- Robust backtest engine (in strict mode)

Using February data to ensure warmup data is available.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import logging
from datetime import datetime

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtest_engine import RobustBacktestEngine

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """Run integration test in strict mode with February data."""
    print("\n" + "="*80)
    print("INTEGRATION TEST: ENHANCED DETECTOR + ROBUST ENGINE (FEBRUARY DATA)")
    print("="*80)
    
    # Load config with enhanced detector
    config_path = Path(__file__).parent / "configs/overrides/modern_system_v2_complete.yaml"
    config = load_config(str(config_path))
    
    # Verify config is using enhanced detector
    detector_type = config.regime.detector_type
    print(f"\nDetector Type: {detector_type}")
    
    if detector_type != "enhanced":
        print(f"ERROR: Expected 'enhanced' detector, but config has '{detector_type}'")
        sys.exit(1)
    
    # Set up test period (February to ensure warmup data exists)
    start_date = datetime(2024, 2, 1)
    end_date = datetime(2024, 2, 7, 23, 59, 59)
    
    print(f"Test Period: {start_date.date()} to {end_date.date()}")
    print("Using February data to ensure proper warmup period is available")
    print("\nRunning in STRICT mode - any fallback will raise an exception")
    print("-" * 80)
    
    try:
        # Create engine in strict mode
        engine = RobustBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date,
            use_regime_cache=False,  # Force detector usage
            strict=True  # Strict mode - no silent failures!
        )
        
        print("\n✅ Phase 1 PASSED: Engine initialization successful")
        print("   - Enhanced detector loaded correctly")
        print("   - No exceptions during setup")
        
        # Run the backtest
        print("\nPhase 2: Running backtest...")
        results = engine.run_backtest()
        
        print("\n✅ Phase 2 PASSED: Backtest completed without crashes")
        
        # Analyze results
        print("\nPhase 3: Analyzing results...")
        print(f"  Total Trades: {results.get('total_trades', 0)}")
        print(f"  Winning Trades: {results.get('winning_trades', 0)}")
        print(f"  Losing Trades: {results.get('losing_trades', 0)}")
        print(f"  Total Return: {results.get('total_return', 0):.2%}")
        print(f"  Win Rate: {results.get('win_rate', 0):.1%}")
        
        # Check regime detection sources
        if 'regime_sources' in results:
            sources = results['regime_sources']
            total = sources.get('total', 1)
            cache_pct = sources.get('cache_used', 0) / total * 100 if total > 0 else 0
            detector_pct = sources.get('detector_used', 0) / total * 100 if total > 0 else 0
            fallback_pct = sources.get('fallback_used', 0) / total * 100 if total > 0 else 0
            
            print(f"\nRegime Detection Sources:")
            print(f"  Cache: {cache_pct:.1f}%")
            print(f"  Detector: {detector_pct:.1f}%")
            print(f"  Fallback: {fallback_pct:.1f}%")
            
            if fallback_pct > 0:
                print(f"\n⚠️  WARNING: {fallback_pct:.1f}% fallback usage detected in strict mode!")
                print("This should have raised an exception!")
        
        # Check if trades look logical
        if results.get('total_trades', 0) > 0:
            print("\n✅ Phase 3 PASSED: Trades generated successfully")
            print("   - Engine correctly interprets GMS states")
            print("   - Position management appears functional")
            
            # Show some trade details
            trades = results.get('trades', [])
            if trades:
                print(f"\nFirst trade details:")
                first_trade = trades[0]
                print(f"  Entry: {first_trade.get('entry_time')} at ${first_trade.get('entry_price', 0):.2f}")
                print(f"  Direction: {first_trade.get('direction', 'unknown')}")
                print(f"  Entry Regime: {first_trade.get('entry_regime', 'unknown')}")
                if first_trade.get('status') == 'closed':
                    print(f"  Exit: {first_trade.get('exit_time')} at ${first_trade.get('exit_price', 0):.2f}")
                    print(f"  P&L: {first_trade.get('pnl_pct', 0):.2%}")
        else:
            print("\n⚠️  WARNING: No trades generated")
            print("   - This may indicate overly restrictive entry conditions")
            print("   - Or missing position/exit logic")
        
        print("\n" + "="*80)
        print("INTEGRATION TEST COMPLETE")
        print("="*80)
        
        print("\nSummary:")
        print("✅ Engine and detector are compatible")
        print("✅ No crashes or integration errors")
        
        if results.get('total_trades', 0) == 0:
            print("⚠️  No trades generated - need to investigate:")
            print("   1. Entry thresholds may be too restrictive")
            print("   2. Regime confidence requirements may be too high")
            print("   3. Check min_regime_duration_minutes setting")
        else:
            print("✅ System generates trades - ready for performance tuning")
            print(f"   Generated {results.get('total_trades', 0)} trades with {results.get('total_return', 0):.2%} return")
        
    except ValueError as e:
        print(f"\n❌ INITIALIZATION FAILED: {e}")
        print("This likely means the detector is not properly registered")
        sys.exit(1)
    
    except RuntimeError as e:
        print(f"\n❌ RUNTIME FAILURE: {e}")
        print("This indicates the detector is failing to provide valid regimes")
        print("Or there's an integration mismatch between detector and engine")
        sys.exit(1)
    
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()