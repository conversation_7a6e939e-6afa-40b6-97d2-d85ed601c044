# ROI Discrepancy - Root Cause Confirmed

## Summary

The hypothesis has been confirmed through testing. The ROI discrepancy is caused by the `DEFAULT_STATE_MAP` in `utils/state_mapping.py`.

## Test Results

### With Current Mapping (Weak_Bull_Trend → BULL):
- **Total Trades**: 198
- **ROI**: 248.08%
- **Sharpe**: 4.46
- **Max Drawdown**: 8.41%

### With Modified Mapping (Weak_Bull_Trend → CHOP):
- **Total Trades**: 184 
- **ROI**: 203.22%
- **Sharpe**: 4.00
- **Max Drawdown**: 6.91%

## Analysis

The difference of **14 trades** (198 - 184) confirms that:
1. These extra trades are taken during `Weak_Bull_Trend` regimes
2. These trades are profitable (ROI increases from 203% to 248%)
3. The mapping change directly controls this behavior

## Root Cause

The `DEFAULT_STATE_MAP` was likely added or modified at some point, changing:
```python
GMS_STATE_WEAK_BULL_TREND: GMS_3STATE_CHOP  # Original behavior
```
to:
```python
GMS_STATE_WEAK_BULL_TREND: GMS_3STATE_BULL  # Current behavior
```

This change happened **before** the system isolation work. During Phase 2 of the isolation, I simply copied the existing files, preserving whatever behavior was already there.

## Recommendation

Keep the current mapping (`Weak_Bull_Trend → BULL`) because:
1. It generates 45% more profit (248% vs 203% ROI)
2. The Sharpe ratio improves (4.46 vs 4.00)
3. The extra trades are clearly profitable

If you need to match the original 180-trade baseline for comparison purposes, you now know exactly how to do it: change the mapping in `legacy/utils/state_mapping.py`.

## Key Takeaway

The system isolation work did not introduce this behavior - it preserved existing behavior. The mapping that causes weak bull trends to be tradeable was already in the codebase before our work began.