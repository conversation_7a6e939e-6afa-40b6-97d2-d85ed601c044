# Hyperliquid Trading Bot: Data Architecture and GMS Detector Systems

**Version**: 1.0
**Date**: 2025-01-23
**Purpose**: Definitive reference for LLMs working on GMS detector unification

## Table of Contents

1. [Overview](#overview)
2. [Data Architecture](#data-architecture)
3. [File Schemas](#file-schemas)
4. [GMS Detector Systems](#gms-detector-systems)
5. [Data Pipeline Flow](#data-pipeline-flow)
6. [Configuration Architecture](#configuration-architecture)
7. [Unification Guidelines](#unification-guidelines)
8. [Data Integrity Rules](#data-integrity-rules)

## Overview

The Hyperliquid trading bot operates with two parallel data systems supporting different detector implementations:

- **Granular Microstructure System**: Uses 5-depth order book data from `raw2/` and `resampled_l2/` files
- **Continuous GMS System**: Uses 20-depth order book data from `l2_raw/` and `features_1s/` files

Both systems are critical for bot functionality and must be maintained during unification.

## Data Architecture

### Directory Structure

```
hyperliquid_data/
├── raw2/                           # Granular microstructure raw data (5-depth)
│   └── YYYYMMDD_raw2.parquet      # Daily files with 37 microstructure features
├── resampled_l2/                   # Hourly aggregated data for tf-v2
│   ├── 1h/
│   │   └── YYYY-MM-DD_1h.parquet  # Hourly OHLC + microstructure features
│   └── 4h/
│       └── YYYY-MM-DD_4h.parquet  # 4-hour OHLC + microstructure features
├── l2_raw/                         # Raw L2 snapshots (modern system)
│   └── YYYY-MM-DD/
│       └── BTC_HH_l2Book.arrow    # Hourly raw L2 snapshots
└── features_1s/                    # 1-second processed features (modern system)
    └── YYYY-MM-DD/
        └── features_HH.parquet    # Hourly 1-second feature files
```

### System Compatibility Matrix

| Component | Granular Microstructure | Continuous GMS |
|-----------|------------------------|----------------|
| **Detector** | `granular_microstructure` | `continuous_gms` |
| **Strategy** | tf-v2 | tf-v3 |
| **Data Source** | `raw2/`, `resampled_l2/` | `l2_raw/`, `features_1s/` |
| **Depth Levels** | 5 | 5 or 20 (configurable) |
| **Cadence** | 3600 seconds (hourly) | 60 seconds |
| **Output Format** | String regime | Dict with state + risk_suppressed |

## File Schemas

### Raw2 Files (Granular Microstructure)

**File**: `hyperliquid_data/raw2/YYYYMMDD_raw2.parquet`
**Shape**: ~391,273 rows × 37 columns
**Used by**: `microstructure.py`, granular_microstructure detector

#### Column Schema (37 columns):
```
1.  timestamp                | datetime64[ns, UTC] | Timezone-aware timestamps
2.  bid_price_1              | float64             | Best bid price
3.  bid_size_1               | float64             | Best bid size
4.  bid_price_2              | float64             | 2nd level bid price
5.  bid_size_2               | float64             | 2nd level bid size
6.  bid_price_3              | float64             | 3rd level bid price
7.  bid_size_3               | float64             | 3rd level bid size
8.  bid_price_4              | float64             | 4th level bid price
9.  bid_size_4               | float64             | 4th level bid size
10. bid_price_5              | float64             | 5th level bid price
11. bid_size_5               | float64             | 5th level bid size
12. ask_price_1              | float64             | Best ask price
13. ask_size_1               | float64             | Best ask size
14. ask_price_2              | float64             | 2nd level ask price
15. ask_size_2               | float64             | 2nd level ask size
16. ask_price_3              | float64             | 3rd level ask price
17. ask_size_3               | float64             | 3rd level ask size
18. ask_price_4              | float64             | 4th level ask price
19. ask_size_4               | float64             | 4th level ask size
20. ask_price_5              | float64             | 5th level ask price
21. ask_size_5               | float64             | 5th level ask size
22. mid_price                | float64             | (best_bid + best_ask) / 2
23. spread                   | float64             | best_ask - best_bid
24. volume_proxy             | float64             | Volume estimation
25. imbalance                | float64             | Order book imbalance
26. bid_ask_ratio            | float64             | Bid/ask size ratio
27. bid_ask_ratio_log        | float64             | Log of bid/ask ratio
28. weighted_mid             | float64             | Size-weighted mid price
29. price_impact_bid         | float64             | Bid side price impact
30. price_impact_ask         | float64             | Ask side price impact
31. bid_slope                | float64             | Bid side slope
32. ask_slope                | float64             | Ask side slope
33. bid_concentration        | float64             | Bid concentration metric
34. ask_concentration        | float64             | Ask concentration metric
35. book_volatility          | float64             | Order book volatility
36. market_efficiency        | float64             | Market efficiency metric
37. book_asymmetry           | float64             | Order book asymmetry
```

**NaN Handling**: 0% NaN values across all columns
**Timestamp Range**: Full trading day with microsecond precision

### Resampled L2 Files (TF-v2 Strategy)

**File**: `hyperliquid_data/resampled_l2/1h/YYYY-MM-DD_1h.parquet`
**Shape**: 24 rows × 10 columns
**Used by**: tf-v2 strategy

#### Column Schema (10 columns):
```
1.  timestamp                | datetime64[ns]      | Timezone-naive hourly timestamps
2.  open                     | float64             | Hourly open price
3.  high                     | float64             | Hourly high price
4.  low                      | float64             | Hourly low price
5.  close                    | float64             | Hourly close price
6.  log_ret                  | float64             | Log returns (4.2% NaN)
7.  realised_vol             | float64             | Realized volatility (50% NaN)
8.  bid_slope                | float64             | Average bid slope
9.  ask_slope                | float64             | Average ask slope
10. book_asymmetry           | float64             | Average book asymmetry
```

**NaN Thresholds**:
- Tier-1 features (OHLC, slopes): ≤1% NaN acceptable
- Tier-2 features (log_ret, realised_vol): ≤50% NaN acceptable

### Raw L2 Files (Modern System)

**File**: `hyperliquid_data/l2_raw/YYYY-MM-DD/BTC_HH_l2Book.arrow`
**Shape**: ~6,380 rows × 6 columns
**Used by**: ETL pipeline for features_1s generation

#### Column Schema (6 columns):
```
1.  timestamp                | int64               | Unix timestamp in milliseconds
2.  best_bid                 | float64             | Best bid price
3.  best_ask                 | float64             | Best ask price
4.  bids                     | object              | Array of [price, size] bid levels
5.  asks                     | object              | Array of [price, size] ask levels
6.  ts                       | datetime64[ns]      | Converted timestamp
```

**Data Format**: Arrow format with nested arrays for order book levels
**Frequency**: ~10Hz (100ms intervals)
**Depth**: Up to 20 levels per side

### Features 1s Files (Continuous GMS)

**File**: `hyperliquid_data/features_1s/YYYY-MM-DD/features_HH.parquet`
**Shape**: ~3,593 rows × 109 columns
**Used by**: continuous_gms detector, tf-v3 strategy

#### Key Column Categories:

**Order Book Data (80 columns)**:
```
bid_price_1 to bid_price_20    | float64  | Bid prices (levels 1-20)
bid_size_1 to bid_size_20      | float64  | Bid sizes (levels 1-20)
ask_price_1 to ask_price_20    | float64  | Ask prices (levels 1-20)
ask_size_1 to ask_size_20      | float64  | Ask sizes (levels 1-20)
```

**Core Features (29 columns)**:
```
timestamp                      | datetime64[ns]  | Timezone-naive timestamps
mid_price                      | float64         | (best_bid + best_ask) / 2
spread                         | float64         | best_ask - best_bid
spread_relative                | float64         | spread / mid_price
raw_obi_5                      | float64         | 5-level OBI
raw_obi_20                     | float64         | 20-level OBI
close                          | float64         | Close price (= mid_price)
high                           | float64         | High price
low                            | float64         | Low price
volume                         | float64         | Volume proxy
best_bid                       | float64         | Best bid price
best_ask                       | float64         | Best ask price
realised_vol_1s                | float64         | 1-second realized volatility
spread_mean                    | float64         | Rolling spread mean
spread_std                     | float64         | Rolling spread std
obi_smoothed                   | float64         | Smoothed OBI (legacy)
obi_smoothed_5                 | float64         | 5-level smoothed OBI
obi_smoothed_20                | float64         | 20-level smoothed OBI
obi_zscore_5                   | float64         | 5-level OBI z-score
obi_zscore_20                  | float64         | 20-level OBI z-score
raw_obi_L1_3                   | float64         | L1 3-level OBI
raw_obi_L1_10                  | float64         | L1 10-level OBI
ma_slope                       | float64         | Moving average slope
ma_slope_ema_30s               | float64         | 30-second EMA slope
unrealised_pnl                 | float64         | Unrealized PnL
atr_14_sec                     | float64         | 14-period ATR (100% NaN)
atr_percent_sec                | float64         | ATR percentage (100% NaN)
atr                            | float64         | ATR (100% NaN)
atr_percent                    | float64         | ATR percentage (100% NaN)
```

**Critical NaN Patterns**:
- Order book levels: 0.3% NaN (11/3593 rows)
- Core features: 0-0.6% NaN
- ATR columns: 100% NaN (requires historical data)
- spread_std: 0.03% NaN (1/3593 rows)

## GMS Detector Systems

### Granular Microstructure Detector

**Configuration Section**: `regime.granular_microstructure`
**Detector Type**: `'granular_microstructure'`
**Data Source**: `raw2/` files via `microstructure.py`

#### Key Parameters:
```yaml
granular_microstructure:
  gms_vol_high_thresh: 0.92      # ATR% high volatility threshold
  gms_vol_low_thresh: 0.55       # ATR% low volatility threshold
  gms_mom_strong_thresh: 100.0   # MA slope strong momentum threshold
  gms_mom_weak_thresh: 50.0      # MA slope weak momentum threshold
  gms_spread_std_high_thresh: 0.000050   # Spread std high threshold
  gms_spread_mean_low_thresh: 0.000045   # Spread mean low threshold
  cadence_sec: 3600              # Hourly recompute
  output_states: 8               # Raw 8-state output
```

#### Required Signals:
- `timestamp`
- `atr_percent` (from hourly data)
- `ma_slope`
- `obi_smoothed_5`
- `spread_mean`
- `spread_std`

#### Output Format:
```python
# Returns string regime state
regime = detector.get_regime(signals)  # "Strong_Bull_Trend"
```

### Continuous GMS Detector

**Configuration Section**: `regime.continuous_gms`
**Detector Type**: `'continuous_gms'`
**Data Source**: `features_1s/` files

#### Key Parameters:
```yaml
continuous_gms:
  gms_vol_high_thresh: 0.03      # Reduced from granular (Phase 1)
  gms_vol_low_thresh: 0.01       # Reduced from granular (Phase 1)
  gms_mom_strong_thresh: 2.5     # Reduced from granular (Phase 1)
  gms_mom_weak_thresh: 0.5       # Reduced from granular (Phase 1)
  gms_spread_std_high_thresh: 0.0005
  gms_spread_mean_low_thresh: 0.0001
  cadence_sec: 60                # Minute recompute
  output_states: 8               # Raw 8-state output
```

#### Required Signals:
- `timestamp`
- `atr_percent_sec` (from 1-second data)
- `ma_slope`
- `obi_smoothed_{depth_levels}` (dynamic depth)
- `spread_mean`
- `spread_std`

#### Output Format:
```python
# Returns dictionary with state and risk suppression
regime = detector.get_regime(signals)
# {"state": "Strong_Bull_Trend", "risk_suppressed": false}
```

#### Adaptive Thresholds (Optional):
```yaml
gms:
  auto_thresholds: false         # Enable adaptive thresholds
  percentile_window_sec: 86400   # 24-hour rolling window
  vol_low_pct: 0.001            # 0.1st percentile
  vol_high_pct: 0.50            # 50th percentile
  mom_low_pct: 0.001            # 0.1st percentile
  mom_high_pct: 0.50            # 50th percentile
```

### State Mapping System

Both detectors support state collapse mapping via `configs/gms_state_mapping.yaml`:

#### 8-State → 3-State Mapping:
```yaml
# Raw 8 states collapse to BULL/BEAR/CHOP
Strong_Bull_Trend: "BULL"
Weak_Bull_Trend: "BULL"
Strong_Bear_Trend: "BEAR"
Weak_Bear_Trend: "CHOP"     # Configurable: can map to "BEAR"
Bull_Tight_Spread: "BULL"
Bear_Tight_Spread: "BEAR"
Volatile_Chop: "CHOP"
Tight_Spread_Chop: "CHOP"
```

#### Configuration Control:
```yaml
regime:
  gms_use_three_state_mapping: true
  map_weak_bear_to_bear: false    # Controls Weak_Bear_Trend mapping
```

## Data Pipeline Flow

### Granular Microstructure Pipeline

```
Raw L2 JSON → raw2/ parquet → microstructure.py → resampled_l2/ → tf-v2 strategy
                    ↓
            granular_microstructure detector (hourly)
```

**Key Steps**:
1. Raw L2 snapshots processed into `raw2/YYYYMMDD_raw2.parquet`
2. 37 microstructure features calculated (5-depth order book)
3. Hourly aggregation to `resampled_l2/1h/YYYY-MM-DD_1h.parquet`
4. Granular detector processes hourly signals
5. tf-v2 strategy consumes hourly OHLC + microstructure features

### Continuous GMS Pipeline

```
Raw L2 Arrow → ETL (etl_l20_to_1s.py) → features_1s/ → continuous_gms detector → tf-v3 strategy
```

**Key Steps**:
1. Raw L2 snapshots in `l2_raw/YYYY-MM-DD/BTC_HH_l2Book.arrow`
2. ETL processes 10Hz data into 1-second features
3. 109 features calculated (configurable 5-20 depth)
4. Output to `features_1s/YYYY-MM-DD/features_HH.parquet`
5. Continuous detector processes 1-second signals
6. tf-v3 strategy consumes 1-second features

### ETL Processing Details

**Input Format**: Arrow files with nested bid/ask arrays
**Processing**:
- Expand arrays to individual price/size columns
- Calculate OBI, spread, and microstructure features
- Resample 10Hz → 1-second using median aggregation
- Forward-fill bid/ask prices to eliminate NaN without look-ahead bias

**Output Validation**:
- Timezone assertions (UTC-naive required)
- NaN ratio validation (Tier-1: ≤1%, Tier-2: ≤5%)
- Schema validation against canonical feature set

## Configuration Architecture

### Unified Configuration Structure

```yaml
regime:
  detector_type: 'granular_microstructure'  # or 'continuous_gms'

  # Shared settings (backward compatibility)
  gms_vol_high_thresh: 0.92      # Fallback values
  gms_vol_low_thresh: 0.55
  gms_mom_strong_thresh: 100.0
  gms_mom_weak_thresh: 50.0

  # Detector-specific sections
  granular_microstructure:
    gms_vol_high_thresh: 0.92
    gms_vol_low_thresh: 0.55
    gms_mom_strong_thresh: 100.0
    gms_mom_weak_thresh: 50.0
    cadence_sec: 3600

  continuous_gms:
    gms_vol_high_thresh: 0.03
    gms_vol_low_thresh: 0.01
    gms_mom_strong_thresh: 2.5
    gms_mom_weak_thresh: 0.5
    cadence_sec: 60
```

### Configuration Inheritance

**Priority Order**:
1. Detector-specific settings (`regime.{detector_type}.{parameter}`)
2. Shared regime settings (`regime.{parameter}`)
3. Default values in detector class

**Example**:
```python
# ContinuousGMSDetector parameter resolution
detector_settings = cfg.regime.continuous_gms
vol_high = detector_settings.get('gms_vol_high_thresh',
           getattr(cfg.regime, 'gms_vol_high_thresh', 0.06))
```

## Unification Guidelines

### Proposed Unified Architecture

#### Single Detector Class
```python
class UnifiedGMSDetector(RegimeDetectorInterface):
    def __init__(self, config):
        # Mode selection
        self.mode = config.gms.mode  # 'continuous' or 'static'
        self.cadence_sec = 60 if self.mode == 'continuous' else 3600

        # Threshold resolution with detector-specific fallback
        detector_type = config.regime.detector_type
        detector_settings = getattr(config.regime, detector_type, {})

        # Load thresholds with inheritance
        self.vol_high_thresh = self._resolve_threshold(
            'gms_vol_high_thresh', detector_settings, config.regime)
```

#### Configuration Migration
```yaml
gms:
  mode: 'continuous'              # 'continuous' or 'static'
  detector_type: 'continuous_gms' # For backward compatibility

  # Unified threshold sections
  thresholds:
    continuous:
      vol_high: 0.03
      vol_low: 0.01
      mom_strong: 2.5
      mom_weak: 0.5
    static:
      vol_high: 0.92
      vol_low: 0.55
      mom_strong: 100.0
      mom_weak: 50.0
```

### Backward Compatibility Requirements

1. **Preserve existing configuration paths**
2. **Maintain detector_type mapping**:
   - `'granular_microstructure'` → static mode, 3600s cadence
   - `'continuous_gms'` → continuous mode, 60s cadence
3. **Support both output formats**:
   - String for legacy mode
   - Dict for continuous mode
4. **Maintain exact threshold values** for baseline reproduction

### Feature Availability Matrix

| Feature | Raw2 | Features_1s | Required By |
|---------|------|-------------|-------------|
| `timestamp` | ✓ | ✓ | Both detectors |
| `atr_percent` | ✓ | ✗ | Granular only |
| `atr_percent_sec` | ✗ | ✓ | Continuous only |
| `ma_slope` | ✓ | ✓ | Both detectors |
| `obi_smoothed_5` | ✓ | ✓ | Both detectors |
| `obi_smoothed_20` | ✗ | ✓ | Continuous only |
| `spread_mean` | ✓ | ✓ | Both detectors |
| `spread_std` | ✓ | ✓ | Both detectors |

**Unification Strategy**:
- Implement runtime column resolution
- Support both `atr_percent` and `atr_percent_sec`
- Dynamic OBI column selection based on available depth

## Data Integrity Rules

### Validation Requirements

#### Schema Validation
```python
# Required columns per detector mode
GRANULAR_REQUIRED = [
    'timestamp', 'atr_percent', 'ma_slope',
    'obi_smoothed_5', 'spread_mean', 'spread_std'
]

CONTINUOUS_REQUIRED = [
    'timestamp', 'atr_percent_sec', 'ma_slope',
    'obi_smoothed_{depth}', 'spread_mean', 'spread_std'
]
```

#### NaN Ratio Thresholds
```python
NAN_THRESHOLDS = {
    'tier_1': 0.01,    # Core analytic features (≤1% NaN)
    'tier_2': 0.05,    # Raw L2 features (≤5% NaN)
    'tier_3': 1.00,    # ATR features (100% NaN acceptable initially)
}
```

#### Runtime Guards
```python
def validate_detector_data(df, detector_mode):
    """Validate data availability for detector mode."""
    required_cols = get_required_columns(detector_mode)

    for col in required_cols:
        if col not in df.columns:
            raise MissingColumnError(f"Required column '{col}' not found")

        nan_ratio = df[col].isnull().sum() / len(df)
        threshold = get_nan_threshold(col)

        if nan_ratio > threshold:
            raise DataQualityError(f"Column '{col}' has {nan_ratio:.1%} NaN, "
                                 f"exceeds threshold {threshold:.1%}")
```

### Acceptance Criteria

#### Detector Performance
- **CHOP regime**: ≤60% of total time
- **BULL + BEAR regimes**: ≥30% of total time
- **Trade generation**: ≥10 trades per test period
- **Baseline reproduction**: Exact metrics for granular_microstructure

#### Data Quality
- **Timestamp continuity**: No gaps > 5 seconds in features_1s
- **Order book integrity**: bid_price_1 < ask_price_1 always
- **Feature correlation**: OBI values within [-1, 1] range
- **ATR availability**: Must be computed from historical data

### Migration Validation

#### Legacy Regression Tests
```python
def test_granular_baseline():
    """Ensure granular_microstructure produces exact baseline metrics."""
    config = load_baseline_config()
    detector = UnifiedGMSDetector(config)

    results = run_backtest(detector, baseline_period)

    assert results.sharpe == 4.00
    assert results.roi_pct == 203.22
    assert results.max_drawdown_pct == 6.91
    assert results.trade_count == 184
```

#### Cross-System Validation
```python
def test_feature_consistency():
    """Validate feature calculations across both systems."""
    raw2_features = load_raw2_features(test_date)
    features_1s = load_features_1s(test_date)

    # Compare overlapping features
    for feature in ['spread_mean', 'spread_std', 'ma_slope']:
        correlation = np.corrcoef(raw2_features[feature],
                                features_1s[feature])[0,1]
        assert correlation > 0.95, f"Low correlation for {feature}: {correlation}"
```

---

**Document Version**: 1.0
**Last Updated**: 2025-01-23
**Maintainer**: Hyperliquid Trading Bot Team
