# Features 1s Data Schema Report

Generated: 2025-07-17T16:34:52.364527

## Executive Summary

- **Files Analyzed**: 5
- **Date Range**: 2024-01-01 to 2024-12-31
- **Total Columns**: 109

## Critical Field Mappings

| Expected Field | Status | Actual Field | Action Required |
|----------------|--------|--------------|----------------|
| timestamp | FOUND | timestamp | ✅ None |
| close | FOUND | close | ✅ None |
| high | FOUND | high | ✅ None |
| low | FOUND | low | ✅ None |
| volume | FOUND | volume | ✅ None |
| volume_imbalance | MAPPED | obi_smoothed | 🔧 Map field |
| spread_mean | FOUND | spread_mean | ✅ None |
| spread_std | FOUND | spread_std | ✅ None |
| atr_percent_sec | FOUND | atr_percent_sec | ✅ None |
| ma_slope_ema_30s | FOUND | ma_slope_ema_30s | ✅ None |

## Recommendations

### 1. [HIGH] Field name mismatches

**Action**: Create data adapter to transform field names

**Required Mappings**:
- `volume_imbalance` → `obi_smoothed`


## Available Columns

<details>
<summary>Click to expand full column list</summary>

| Column | Data Type | Null % | Sample Values |
|--------|-----------|--------|---------------|
| ask_price_1 | float64 | 0.6% | [42627.0, 42626.0, 42627.0] |
| ask_price_10 | float64 | 0.6% | [42670.0, 42684.0, 42715.0] |
| ask_price_11 | float64 | 0.6% | [42680.0, 42715.0, 42716.0] |
| ask_price_12 | float64 | 0.6% | [42684.0, 42716.0, 42757.0] |
| ask_price_13 | float64 | 0.6% | [42715.0, 42757.0, 42758.0] |
| ask_price_14 | float64 | 0.6% | [42716.0, 42758.0, 42801.0] |
| ask_price_15 | float64 | 0.6% | [42757.0, 42801.0, 42808.0] |
| ask_price_16 | float64 | 0.6% | [42758.0, 42808.0, 42817.0] |
| ask_price_17 | float64 | 0.6% | [42801.0, 42817.0, 42900.0] |
| ask_price_18 | float64 | 0.6% | [42808.0, 42900.0, 42950.0] |
| ask_price_19 | float64 | 0.6% | [42817.0, 42950.0, 43000.0] |
| ask_price_2 | float64 | 0.6% | [42628.0, 42627.0, 42629.0] |
| ask_price_20 | float64 | 0.6% | [42900.0, 43000.0, 43013.0] |
| ask_price_3 | float64 | 0.6% | [42629.0, 42629.0, 42631.0] |
| ask_price_4 | float64 | 0.6% | [42631.0, 42631.0, 42637.0] |
| ask_price_5 | float64 | 0.6% | [42634.0, 42637.0, 42641.0] |
| ask_price_6 | float64 | 0.6% | [42637.0, 42641.0, 42661.0] |
| ask_price_7 | float64 | 0.6% | [42641.0, 42661.0, 42669.0] |
| ask_price_8 | float64 | 0.6% | [42652.0, 42669.0, 42670.0] |
| ask_price_9 | float64 | 0.6% | [42661.0, 42670.0, 42684.0] |
| ask_size_1 | float64 | 0.6% | [0.00117, 0.02345, 0.00117] |
| ask_size_10 | float64 | 0.6% | [3.60889, 0.11744, 3.72277] |
| ask_size_11 | float64 | 0.6% | [3.96938, 3.72277, 4.35379] |
| ask_size_12 | float64 | 0.6% | [3.66773, 4.35379, 0.0877] |
| ask_size_13 | float64 | 0.6% | [3.72277, 0.0877, 0.02923] |
| ask_size_14 | float64 | 0.6% | [4.35379, 0.02923, 0.0292] |
| ask_size_15 | float64 | 0.6% | [0.0877, 0.0292, 4.18585] |
| ask_size_16 | float64 | 0.6% | [0.02923, 4.18585, 3.96821] |
| ask_size_17 | float64 | 0.6% | [0.0292, 3.96821, 0.01611] |
| ask_size_18 | float64 | 0.6% | [4.18585, 0.01611, 2.5] |
| ask_size_19 | float64 | 0.6% | [3.96821, 2.5, 0.0219] |
| ask_size_2 | float64 | 0.6% | [0.02345, 0.00117, 0.1125] |
| ask_size_20 | float64 | 0.6% | [0.01611, 0.0219, 38.20416] |
| ask_size_3 | float64 | 0.6% | [0.1125, 0.1125, 6.78819] |
| ask_size_4 | float64 | 0.6% | [6.78819, 6.78819, 0.0062] |
| ask_size_5 | float64 | 0.6% | [3.68525, 0.0062, 7.67746] |
| ask_size_6 | float64 | 0.6% | [0.0062, 7.67746, 0.0165] |
| ask_size_7 | float64 | 0.6% | [15.3717, 0.0165, 3.67905] |
| ask_size_8 | float64 | 0.6% | [7.4296, 3.67905, 3.60889] |
| ask_size_9 | float64 | 0.6% | [0.0165, 3.60889, 0.11744] |
| atr | float64 | 0.0% | [343.9642857142857, 343.9642857142857, 343.9642857142857] |
| atr_14_sec | float64 | 0.0% | [343.9642857142857, 343.9642857142857, 343.9642857142857] |
| atr_percent | float64 | 0.6% | [0.008069354049507009, 0.00806973267910768, 0.00806963801837642] |
| atr_percent_sec | float64 | 0.6% | [0.008069354049507009, 0.00806973267910768, 0.00806963801837642] |
| best_ask | float64 | 0.6% | [42627.0, 42626.0, 42627.0] |
| best_bid | float64 | 0.6% | [42625.0, 42622.0, 42622.0] |
| bid_price_1 | float64 | 0.6% | [42625.0, 42622.0, 42622.0] |
| bid_price_10 | float64 | 0.6% | [42527.0, 42533.0, 42533.0] |
| bid_price_11 | float64 | 0.6% | [42524.0, 42527.0, 42527.0] |
| bid_price_12 | float64 | 0.6% | [42515.0, 42524.0, 42524.0] |
| bid_price_13 | float64 | 0.6% | [42513.0, 42515.0, 42515.0] |
| bid_price_14 | float64 | 0.6% | [42481.0, 42513.0, 42513.0] |
| bid_price_15 | float64 | 0.6% | [42399.0, 42481.0, 42481.0] |
| bid_price_16 | float64 | 0.6% | [42398.0, 42399.0, 42399.0] |
| bid_price_17 | float64 | 0.6% | [42350.0, 42398.0, 42398.0] |
| bid_price_18 | float64 | 0.6% | [42201.0, 42350.0, 42350.0] |
| bid_price_19 | float64 | 0.6% | [42189.0, 42201.0, 42201.0] |
| bid_price_2 | float64 | 0.6% | [42622.0, 42614.0, 42614.0] |
| bid_price_20 | float64 | 0.6% | [42171.0, 42189.0, 42189.0] |
| bid_price_3 | float64 | 0.6% | [42614.0, 42613.0, 42613.0] |
| bid_price_4 | float64 | 0.6% | [42613.0, 42611.0, 42612.0] |
| bid_price_5 | float64 | 0.6% | [42605.0, 42606.0, 42611.0] |
| bid_price_6 | float64 | 0.6% | [42595.0, 42605.0, 42605.0] |
| bid_price_7 | float64 | 0.6% | [42572.0, 42595.0, 42595.0] |
| bid_price_8 | float64 | 0.6% | [42566.0, 42572.0, 42572.0] |
| bid_price_9 | float64 | 0.6% | [42533.0, 42566.0, 42566.0] |
| bid_size_1 | float64 | 0.6% | [0.20133, 0.075, 0.27633] |
| bid_size_10 | float64 | 0.6% | [3.89914, 0.08817, 0.08817] |
| bid_size_11 | float64 | 0.6% | [0.0294, 3.89914, 3.89914] |
| bid_size_12 | float64 | 0.6% | [0.0072, 0.0294, 0.0294] |
| bid_size_13 | float64 | 0.6% | [4.07705, 0.0072, 0.0072] |
| bid_size_14 | float64 | 0.6% | [0.02942, 4.07705, 4.07705] |
| bid_size_15 | float64 | 0.6% | [3.98626, 0.02942, 0.02942] |
| bid_size_16 | float64 | 0.6% | [3.65626, 3.98626, 3.98626] |
| bid_size_17 | float64 | 0.6% | [0.28391, 3.65626, 3.65626] |
| bid_size_18 | float64 | 0.6% | [0.04415, 0.28391, 0.28391] |
| bid_size_19 | float64 | 0.6% | [0.71108, 0.04415, 0.04415] |
| bid_size_2 | float64 | 0.6% | [0.075, 0.00791, 0.12363] |
| bid_size_20 | float64 | 0.6% | [42.10146, 0.71108, 0.71108] |
| bid_size_3 | float64 | 0.6% | [0.0895, 4.2699, 4.2699] |
| bid_size_4 | float64 | 0.6% | [4.29335, 0.02345, 0.12363] |
| bid_size_5 | float64 | 0.6% | [8.25021, 0.15, 0.02345] |
| bid_size_6 | float64 | 0.6% | [7.93477, 8.25021, 8.25021] |
| bid_size_7 | float64 | 0.6% | [8.00957, 7.93477, 7.93477] |
| bid_size_8 | float64 | 0.6% | [0.11759, 8.00957, 8.00957] |
| bid_size_9 | float64 | 0.6% | [0.08817, 0.11759, 0.11759] |
| close | float64 | 0.6% | [42626.0, 42624.0, 42624.5] |
| high | float64 | 0.6% | [42626.0, 42624.0, 42624.5] |
| low | float64 | 0.6% | [42626.0, 42624.0, 42624.5] |
| ma_slope | float64 | 0.0% | [-0.5583333333343035, -0.5775862068985589, -0.7672413793115993] |
| ma_slope_ema_30s | float64 | 0.0% | [-0.0004060961835578997, 0.0, -0.0007264308840287739] |
| mid_price | float64 | 0.6% | [42626.0, 42624.0, 42624.5] |
| obi_smoothed | float64 | 0.0% | [-0.11076559977084623, -0.1070526736473202, -0.10675896987082686] |
| obi_smoothed_20 | float64 | 0.0% | [-0.2647422796768637, -0.25426490146978764, -0.24197649762509113] |
| obi_smoothed_5 | float64 | 0.0% | [-0.11076559977084623, -0.1070526736473202, -0.10675896987082686] |
| obi_zscore_20 | float64 | 0.0% | [1.7580901388361176, 0.8802647056398024, 1.1653763439643965] |
| obi_zscore_5 | float64 | 0.0% | [0.8463535728614189, 0.0, -0.24831841014971945] |
| raw_obi_20 | float64 | 0.0% | [0.17999272816327594, 0.0, 0.05433076912861516] |
| raw_obi_5 | float64 | 0.6% | [0.09773957852385994, -0.2099230478347948, -0.503471209294931] |
| raw_obi_L1_10 | float64 | 0.0% | [-0.05835283751568525, 0.0, 0.13532317766480337] |
| raw_obi_L1_3 | float64 | 0.0% | [0.45473705139675913, 0.0, 0.9389210967654282] |
| realised_vol_1s | float64 | 1.16% | [0.0, 1.1730411678983722e-05, 0.0] |
| spread | float64 | 0.6% | [2.0, 4.0, 5.0] |
| spread_mean | float64 | 0.0% | [3.0416666666666665, 3.042372881355932, 3.059322033898305] |
| spread_relative | float64 | 0.6% | [4.6919720358466666e-05, 9.384384384384384e-05, 0.00011730342877922321] |
| spread_std | float64 | 0.0% | [0.8795784172490373, 0.8871114282593681, 0.895798361714784] |
| timestamp | datetime64[ns] | 0.0% | ['2024-01-01 03:00:00', '2024-01-01 03:00:01', '2024-01-01 03:00:02'] |
| unrealised_pnl | float64 | 0.0% | [0.0, 0.0, 0.0] |
| volume | float64 | 0.0% | [0.0, 0.0, 0.0] |

</details>
