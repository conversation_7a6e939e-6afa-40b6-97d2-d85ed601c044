# Modern System State Mapping Fix Guide

## Overview
This guide documents the critical fixes implemented to enable the modern system (continuous_gms + TF-v3) to generate trades. The issues stemmed from state mapping configuration and implementation problems that prevented proper regime detection and strategy activation.

## Problem Summary
The modern system was not generating any trades despite being properly configured. Investigation revealed multiple layers of state mapping issues:

1. **Wrong State Mapping File**: Modern system was using legacy state mappings
2. **Hardcoded Mapping Utility**: Global utility ignored configuration settings
3. **Double Mapping**: TF-v3 was re-mapping already mapped states
4. **Regime Propagation**: Backtester wasn't passing mapped regimes to strategies

## Root Cause Analysis

### 1. State Mapping Configuration Issue
The modern system requires different state mappings than the legacy system:

**Legacy Mappings** (Conservative):
```yaml
High_Vol_Range: 'CHOP'  # No trading in high volatility
Low_Vol_Range: 'CHOP'   # No trading in low volatility
Weak_Bear_Trend: 'CHOP' # No trading in weak trends
```

**Modern Mappings** (Aggressive):
```yaml
High_Vol_Range: 'BULL'  # Trade volatility breakouts
Low_Vol_Range: 'BEAR'   # Trade mean reversion
Weak_Bear_Trend: 'BEAR' # Trade weak trends
```

### 2. Implementation Issues

#### A. Hardcoded State Mapping Utility
```python
# hyperliquid_bot/utils/state_mapping.py - PROBLEMATIC CODE
potential_paths.append(os.path.join(configs_dir, "gms_state_mapping.yaml"))
# This always loaded the legacy mapping, ignoring config settings!
```

#### B. TF-v3 Double Mapping
```python
# hyperliquid_bot/strategies/tf_v3.py - PROBLEMATIC CODE
regime = map_gms_state(raw_regime, map_weak_bear_to_bear=map_weak_bear_to_bear)
# This re-mapped already mapped states, causing BEAR -> CHOP
```

#### C. Backtester Regime Passing
The backtester was passing raw regimes (e.g., 'Low_Vol_Range') instead of mapped regimes ('BEAR') to strategies.

## Implementation Fixes

### 1. Created Modern State Mapping File
**File**: `configs/gms_state_mapping_modern.yaml`
```yaml
# GMS State Mapping for Modern System (Continuous GMS + TF-v3)
# Maps 8 GMS states to 3 strategy states (BULL/BEAR/CHOP)
# OPTIMIZED: This mapping enables TF-v3 to trade in volatility conditions
state_map:
  Strong_Bull_Trend: 'BULL'      # Strong bullish momentum with confirmation
  Weak_Bull_Trend: 'BULL'        # Weaker bullish momentum
  High_Vol_Range: 'BULL'         # Trade volatility breakouts as bullish
  Low_Vol_Range: 'BEAR'          # Trade low volatility as bearish (mean reversion)
  Uncertain: 'CHOP'              # Conflicting signals - NO TRADING
  Unknown: 'CHOP'                # Missing data or error state - NO TRADING
  Weak_Bear_Trend: 'BEAR'        # Trade weak bearish trends
  Strong_Bear_Trend: 'BEAR'      # Strong bearish momentum with confirmation
  TIGHT_SPREAD: 'CHOP'           # Tight spread conditions - NO TRADING
```

### 2. Updated Modern System Profile
**File**: `configs/overrides/profile_modern_system.yaml`
```yaml
regime:
  gms_state_mapping_file: 'configs/gms_state_mapping_modern.yaml'
  
continuous_gms:
  state_collapse_map_file: 'configs/gms_state_mapping_modern.yaml'
  
gms:
  state_collapse_map_file: 'configs/gms_state_mapping_modern.yaml'
```

### 3. Fixed StrategyEvaluator to Use Detector's State Map
**File**: `hyperliquid_bot/strategies/evaluator.py`
```python
def __init__(self, config: Config, regime_detector=None):
    self.regime_detector = regime_detector  # Store reference to regime detector
    
# In get_active_strategies():
if (self.regime_detector and 
    hasattr(self.regime_detector, 'state_collapse_map') and 
    self.regime_detector.state_collapse_map and 
    'state_map' in self.regime_detector.state_collapse_map):
    # Use the detector's loaded state map
    state_map = self.regime_detector.state_collapse_map['state_map']
    mapped_regime = state_map.get(current_regime, 'CHOP')
```

### 4. Fixed TF-v3 to Use Pre-Mapped Regime
**File**: `hyperliquid_bot/strategies/tf_v3.py`
```python
# Use the regime from signals which has already been properly mapped
regime = signals.get('regime', 'Unknown')

# Get the raw regime state from GMS snapshot for logging
raw_regime = gms_snapshot.get('state')

# Log if there's a difference
if regime != raw_regime:
    self.logger.info(f"Using mapped regime '{regime}' (raw was '{raw_regime}')")
```

### 5. Fixed Backtester to Pass Mapped Regimes
**File**: `hyperliquid_bot/backtester/backtester.py`
```python
# Map the regime state to BULL/BEAR/CHOP before passing to strategies
if active_strategy_names and self.strategy_evaluator.gms_mapping_active:
    if (self.regime_detector and 
        hasattr(self.regime_detector, 'state_collapse_map') and 
        self.regime_detector.state_collapse_map):
        state_map = self.regime_detector.state_collapse_map['state_map']
        mapped_regime = state_map.get(regime_state, 'CHOP')
        if mapped_regime != regime_state:
            self.logger.info(f"Mapped regime for strategies: {regime_state} -> {mapped_regime}")
            current_signals['regime'] = mapped_regime
```

## Verification Process

### 1. Test Script
```python
# test_modern_system.py
# Verifies state mapping is loaded correctly
if hasattr(detector, 'state_collapse_map') and detector.state_collapse_map:
    state_map = detector.state_collapse_map.get('state_map', {})
    print(f"Using state mapping from detector (loaded from config)")
    
# Check critical mappings
critical_states = ['High_Vol_Range', 'Low_Vol_Range', 'Weak_Bear_Trend']
for state in critical_states:
    mapped = state_map.get(state, 'NOT FOUND')
    print(f"  {state} -> {mapped}")
```

### 2. Backtest Results
**Before Fix**: 0 trades (no strategies activated)
**After Fix**: 7 trades in 20-day test period

## Key Learnings

1. **Configuration Hierarchy**: State mapping files must be specified at multiple levels (regime, continuous_gms, gms sections)
2. **Implementation Coupling**: Changes to state mapping require updates in multiple components
3. **Testing Requirements**: Always verify state mappings are loaded correctly before running backtests
4. **Documentation**: Critical configuration differences between legacy and modern systems must be clearly documented

## Troubleshooting

### No Trades Generated
1. Check state mapping file is loaded: Look for "Loaded state collapse map from" in logs
2. Verify mapping content: Low_Vol_Range should map to BEAR, not CHOP
3. Check regime propagation: Look for "Mapped regime for strategies" in logs

### Wrong Mappings Used
1. Ensure all three config sections point to correct file
2. Check StrategyEvaluator is using detector's state map
3. Verify TF-v3 isn't re-mapping states

### Performance Issues
- The modern system is designed for more aggressive trading
- Expect higher trade frequency compared to legacy system
- Monitor risk metrics carefully in live trading

## Future Improvements

1. **Dynamic State Mapping**: Allow runtime switching between conservative/aggressive mappings
2. **Unified Configuration**: Single source of truth for state mappings
3. **Validation Tools**: Automated checks for state mapping consistency
4. **Performance Metrics**: Track performance by regime to optimize mappings