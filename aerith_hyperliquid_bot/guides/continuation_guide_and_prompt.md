# Aerith Hyperliquid Bot - Continuation Guide & AI Prompt

## 🤖 AI Assistant Prompt (Start Here)

You are continuing work on the Aerith Hyperliquid Bot project. This is a cryptocurrency trading bot with two parallel systems:

1. **LEGACY SYSTEM (WORKING)**: Generates ~180 trades with +215% ROI - DO NOT MODIFY
2. **MODER<PERSON> SYSTEM (BROKEN)**: 100% regime gate failures, generates 0 trades - NEEDS FIXING

### Critical Context
- We just completed a 3-phase architectural separation to isolate legacy and modern systems
- The legacy system uses specific thresholds that MUST be preserved (momentum: 100.0/50.0, volatility: 0.0092/0.0055, risk: 25%)
- The modern system uses different scales (momentum: 2.5/0.5, volatility: 0.015/0.005, risk: 2%)
- Both systems are now cleanly separated with no cross-contamination

### Your Mission
Fix the modern system's 100% regime gate failures while preserving the working legacy system exactly as is.

## 📁 Project Structure

```
aerith_hyperliquid_bot/
├── configs/
│   ├── base.yaml                      # Base configuration (shared)
│   └── overrides/
│       ├── legacy_system.yaml          # Legacy overrides (FROZEN - DO NOT MODIFY)
│       └── modern_system.yaml          # Modern overrides (can modify)
├── hyperliquid_bot/
│   ├── core/                           # Shared infrastructure
│   │   ├── interfaces.py               # IRegimeDetector, IStrategy, IDataLoader
│   │   ├── registry.py                 # Component registration system
│   │   └── config_validator.py         # ❌ emoji configuration validator
│   ├── legacy/                         # Legacy components (FROZEN)
│   │   ├── detector.py                 # LegacyGranularMicrostructureDetector
│   │   ├── strategy.py                 # LegacyTFV2Strategy  
│   │   └── data_loader.py              # LegacyDataLoader (raw2/ files)
│   ├── modern/                         # Modern components (can modify)
│   │   ├── detector.py                 # ModernContinuousGMSDetector
│   │   ├── strategy.py                 # ModernTFV3Strategy
│   │   └── data_loader.py              # ModernDataLoader (features_1s/ files)
│   ├── systems/                        # System compositions
│   │   ├── legacy_system.py            # LegacyTradingSystem
│   │   └── modern_system.py            # ModernTradingSystem
│   └── backtester/
│       └── run_backtest.py             # Main backtest runner
├── scripts/
│   ├── test_legacy_system.py           # Legacy system validator
│   └── test_modern_system.py           # Modern system validator
└── guides/
    ├── system_separation_plan.md       # Original separation plan
    ├── phase1_summary.md               # Configuration validation phase
    ├── phase2_summary.md               # Legacy extraction phase
    └── phase3_completion_summary.md    # Modern extraction phase
```

## 🔧 Current State

### Legacy System (WORKING ✅)
- **Detector**: `granular_microstructure` mode, 3600s cadence
- **Data**: `raw2/` directory, hourly files
- **Key Thresholds**:
  - Momentum: 100.0 (strong) / 50.0 (weak)
  - Volatility: 0.0092 (high) / 0.0055 (low) - SCALED for decimal ATR
  - Risk fraction: 0.25 (25% per trade)
- **Performance**: ~180 trades, +215% ROI

### Modern System (BROKEN ❌)
- **Detector**: `continuous_gms` mode, 60s cadence
- **Data**: `features_1s/` directory, 1-second features
- **Key Thresholds**:
  - Momentum: 2.5 (strong) / 0.5 (weak)
  - Volatility: 0.015 (high) / 0.005 (low)
  - Risk fraction: 0.02 (2% per trade)
- **Issue**: 100% regime gate failures (all regimes classified as CHOP)

## 🎯 Immediate Next Steps

### 1. Create Wrapper Scripts
Create two convenience scripts for easy backtesting without command line arguments:

**`scripts/run_legacy_backtest.py`**:
```python
#!/usr/bin/env python3
"""Run legacy system backtest with frozen configuration."""
import subprocess
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
config_path = project_root / "configs" / "overrides" / "legacy_system.yaml"
backtest_script = project_root / "hyperliquid_bot" / "backtester" / "run_backtest.py"

# Run with legacy override
subprocess.run([
    sys.executable, 
    str(backtest_script),
    "--override", str(config_path)
])
```

**`scripts/run_modern_backtest.py`**:
```python
#!/usr/bin/env python3
"""Run modern system backtest with experimental configuration."""
import subprocess
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
config_path = project_root / "configs" / "overrides" / "modern_system.yaml"
backtest_script = project_root / "hyperliquid_bot" / "backtester" / "run_backtest.py"

# Run with modern override
subprocess.run([
    sys.executable,
    str(backtest_script), 
    "--override", str(config_path)
])
```

### 2. Debug Modern System Regime Gates

The core issue is that the modern system classifies all market conditions as CHOP. Investigation points:

#### A. Data Quality Check
```python
# Create scripts/analyze_modern_features.py
# Load features_1s/ data and analyze distributions of:
# - atr_percent_sec values
# - ma_slope_ema_30s values
# - Compare with threshold values (0.015/0.005 and 2.5/0.5)
```

#### B. Threshold Calibration
The modern thresholds might be miscalibrated for the actual data:
- Volatility: Are 1.5%/0.5% ATR reasonable for crypto?
- Momentum: Is 2.5/0.5 slope appropriate for the timeframe?

#### C. Regime Mapping Investigation
Check the state mapping logic:
- Raw 8-state detection → 3-state mapping
- Verify `map_weak_bear_to_bear` setting
- Check `gms_state_mapping.yaml` configuration

### 3. Create Diagnostic Dashboard

Create a script to visualize what's happening:
```python
# scripts/diagnose_regime_failures.py
# For both systems side-by-side:
# 1. Load same date range
# 2. Show regime classifications
# 3. Display key metrics vs thresholds
# 4. Identify why modern system sees everything as CHOP
```

## 🔍 Investigation Checklist

- [ ] Verify modern data loader correctly loads features_1s/ files
- [ ] Check ATR/momentum calculations match expected scales
- [ ] Confirm regime detector receives proper signals
- [ ] Validate threshold comparisons (decimal vs percentage)
- [ ] Test regime mapping (8-state to 3-state)
- [ ] Check for timing/synchronization issues
- [ ] Verify warm-up period handling

## ⚡ Quick Commands

```bash
# Run legacy backtest (should show ~180 trades, +215% ROI)
python3 scripts/run_legacy_backtest.py

# Run modern backtest (currently shows 0 trades)
python3 scripts/run_modern_backtest.py

# Test legacy system components
python3 scripts/test_legacy_system.py

# Test modern system components  
python3 scripts/test_modern_system.py

# Run configuration validator
python3 -m hyperliquid_bot.core.config_validator
```

## 🚨 Critical Warnings

1. **NEVER MODIFY LEGACY COMPONENTS** - They are frozen and working
2. **PRESERVE EXACT THRESHOLDS** - Legacy: 100.0/50.0, Modern: 2.5/0.5
3. **MAINTAIN RISK FRACTIONS** - Legacy: 25%, Modern: 2%
4. **DON'T MIX DATA SOURCES** - Legacy uses raw2/, Modern uses features_1s/

## 💡 Debugging Strategy

1. **Start with data analysis** - Understand what values the modern system actually sees
2. **Compare with legacy** - Run same period through both systems
3. **Trace regime detection** - Add detailed logging to see classification logic
4. **Adjust thresholds carefully** - Based on statistical analysis, not guessing
5. **Test incrementally** - One change at a time with full backtests

## 📊 Success Metrics

The modern system is fixed when:
- Generates 100-200 trades (not 0 or 900+)
- Achieves >100% ROI
- Shows reasonable BULL/BEAR/CHOP distribution
- Maintains <15% drawdown
- Uses proper 2% risk management

## 🔗 Key Relationships

```
Legacy System:
raw2/ data → Legacy Detector (3600s) → BULL/BEAR/CHOP → TF-v2 Strategy → ~180 trades

Modern System:
features_1s/ → Modern Detector (60s) → BULL/BEAR/CHOP → TF-v3 Strategy → 0 trades ❌
                                              ↑
                                    Problem is here!
```

## 📝 Session Summary

Today we completed a comprehensive 3-phase separation:
1. **Phase 1**: Built configuration validator with ❌ emoji warnings
2. **Phase 2**: Extracted and validated legacy system (confirmed 191 trades, 243% ROI)
3. **Phase 3**: Extracted modern system (components work but regime gates fail)

The architecture is now clean and modular. The next session should focus on:
1. Creating the wrapper scripts
2. Analyzing why modern detector sees everything as CHOP
3. Calibrating thresholds based on actual data distributions
4. Running comparative backtests to validate fixes

Remember: The goal is to make the modern system work WITHOUT touching the legacy system!