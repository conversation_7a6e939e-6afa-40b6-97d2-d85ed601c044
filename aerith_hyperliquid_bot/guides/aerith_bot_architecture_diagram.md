# Aerith Hyperliquid Bot - Architecture Diagram

This document contains the simplified architecture diagram for the Aerith Hyperliquid Bot based on complete codebase exploration, optimized for LLM understanding.

## System Architecture Overview

The Aerith Hyperliquid Bot is a sophisticated cryptocurrency trading system designed for Hyperliquid DEX, featuring regime detection, multiple trading strategies, and comprehensive backtesting capabilities.

## Simplified Architecture Diagram

```mermaid
graph TB
    %% External Data Sources
    subgraph "Data Sources"
        L2[L2 Market Data<br/>Parquet Files]
        OHLCV[OHLCV Data<br/>Parquet Files]
        FG[Fear & Greed API]
    end

    %% Data Processing
    subgraph "Data Processing"
        DH[Data Handler<br/>handler.py]
        FS[Feature Store<br/>feature_store.py]
        FB_REG[Feature Builder<br/>builder_registry.py]
    end

    %% Feature Engineering
    subgraph "Feature Engineering"
        INDICATORS[Technical Indicators<br/>indicators.py]
        STATISTICAL[Statistical Features<br/>statistical.py]
        MICRO_FEAT[Microstructure Features<br/>microstructure.py]
    end

    %% Signal & Regime Detection
    subgraph "Signal & Regime Detection"
        SC[Signal Engine<br/>calculator.py]
        RD[Regime Detector<br/>detector.py]
        GMS[GMS Detector<br/>gms_detector.py]
        UGMS[Unified GMS<br/>unified_gms_detector.py]
    end

    %% Strategy & Risk
    subgraph "Strategy & Risk Management"
        SE[Strategy Evaluator<br/>evaluator.py]
        SF[Strategy Factory<br/>strategy_factory.py]
        TF[Trend Following<br/>TrendFollowingStrategy]
        TF3[TF-v3 Strategy<br/>tf_v3.py]
        OBI[OBI Scalper<br/>obi_scalper.py]
        RM[Risk Manager<br/>risk.py]
    end

    %% Execution & Portfolio
    subgraph "Execution & Portfolio"
        PORT[Portfolio Manager<br/>portfolio.py]
        SIM[Execution Simulation<br/>simulation.py]
        BT[Backtester<br/>backtester.py]
        RUN_BT[Backtest Runner<br/>run_backtest.py]
    end

    %% Configuration
    subgraph "Configuration"
        CFG[Config Settings<br/>settings.py]
        BASE_YAML[Main Config<br/>configs/base.yaml]
        GMS_MAP[GMS State Mapping<br/>configs/gms_state_mapping.yaml]
    end

    %% Testing & Debug
    subgraph "Testing & Debug Infrastructure"
        DEBUG_BT[Debug Backtester<br/>debug_backtester.py]
        MOCK_BT[Mock Backtester<br/>mock_backtester.py]
        BT_PATCH[Backtester Patch<br/>backtester_patch.py]
        TEST_SUITE[Test Suite<br/>tests/]
    end

    %% Utilities
    subgraph "Utilities & Support"
        STATE_MAP[State Mapping<br/>state_mapping.py]
        SYS_VAL[System Validation<br/>system_validation.py]
        DATA_UTILS[Data Utilities<br/>data_utils.py]
        LOG_ANALYZER[Log Analyzer<br/>log_analyzer.py]
        ADAPTIVE_THRESH[Adaptive Thresholds<br/>adaptive_threshold.py]
    end

    %% Data Flow Connections
    L2 --> DH
    OHLCV --> DH
    FG --> DH
    
    DH --> FS
    DH --> FB_REG
    
    FB_REG --> INDICATORS
    FB_REG --> STATISTICAL
    FB_REG --> MICRO_FEAT
    
    FS --> SC
    INDICATORS --> SC
    STATISTICAL --> SC
    MICRO_FEAT --> SC
    
    SC --> RD
    SC --> GMS
    SC --> UGMS
    
    RD --> SE
    GMS --> SE
    UGMS --> SE
    
    SE --> SF
    SF --> TF
    SF --> TF3
    SF --> OBI
    
    TF --> PORT
    TF3 --> PORT
    OBI --> PORT
    
    RM --> PORT
    PORT --> SIM
    SIM --> BT
    BT --> RUN_BT
    
    %% Debug/Test connections
    BT --> DEBUG_BT
    BT --> MOCK_BT
    BT_PATCH --> BT
    TEST_SUITE --> BT
    
    %% Configuration connections
    CFG --> DH
    CFG --> SC
    CFG --> SE
    CFG --> PORT
    CFG --> BT
    
    BASE_YAML --> CFG
    GMS_MAP --> GMS
    GMS_MAP --> UGMS
    
    %% Utilities connections
    STATE_MAP --> GMS
    STATE_MAP --> UGMS
    SYS_VAL --> RUN_BT
    DATA_UTILS --> DH
    LOG_ANALYZER --> RUN_BT
    ADAPTIVE_THRESH --> GMS
    ADAPTIVE_THRESH --> UGMS

    %% Styling
    classDef dataSource fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef dataLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef featureLayer fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef signalLayer fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef strategyLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef executionLayer fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef configLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef testLayer fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef utilsLayer fill:#fafafa,stroke:#424242,stroke-width:2px
    
    class L2,OHLCV,FG dataSource
    class DH,FS,FB_REG dataLayer
    class INDICATORS,STATISTICAL,MICRO_FEAT featureLayer
    class SC,RD,GMS,UGMS signalLayer
    class SE,SF,TF,TF3,OBI,RM strategyLayer
    class PORT,SIM,BT,RUN_BT executionLayer
    class CFG,BASE_YAML,GMS_MAP configLayer
    class DEBUG_BT,MOCK_BT,BT_PATCH,TEST_SUITE testLayer
    class STATE_MAP,SYS_VAL,DATA_UTILS,LOG_ANALYZER,ADAPTIVE_THRESH utilsLayer
```

## Simplified Components Description

### Data Sources
- **L2 Market Data**: Raw order book data in parquet format
- **OHLCV Data**: Resampled price and volume data  
- **Fear & Greed API**: External sentiment data provider

### Data Processing
- **Data Handler**: Main data loading and preprocessing with validation
- **Feature Store**: Manages computed features with 1-second resolution
- **Feature Builder**: Centralized registry for feature calculations

### Feature Engineering
- **Technical Indicators**: Standard TA indicators (RSI, EMA, ATR, etc.)
- **Statistical Features**: Advanced calculations (Hurst exponent, etc.)
- **Microstructure Features**: Order book analysis (OBI, depth metrics)

### Signal & Regime Detection
- **Signal Engine**: Central signal calculation with comprehensive pipeline
- **Regime Detector**: Market regime classification system
- **GMS Detector**: Granular Microstructure State detection
- **Unified GMS**: Consolidated GMS implementation (legacy + continuous modes)

### Strategy & Risk Management
- **Strategy Evaluator**: Manages multiple trading strategies
- **Strategy Factory**: Creates strategy instances
- **Trend Following**: Main TF strategy implementation
- **TF-v3 Strategy**: Advanced TF with regime awareness
- **OBI Scalper**: Order book imbalance scalping
- **Risk Manager**: Position sizing and risk management

### Execution & Portfolio
- **Portfolio Manager**: Position and balance management
- **Execution Simulation**: Trade execution with maker/taker logic
- **Backtester**: Main backtesting engine with metrics
- **Backtest Runner**: Entry point for running backtests

### Configuration
- **Config Settings**: Pydantic configuration models
- **Main Config**: Primary YAML configuration (`configs/base.yaml`)
- **GMS State Mapping**: Regime state mapping configuration

### Testing & Debug Infrastructure
- **Debug Backtester**: Enhanced debugging capabilities
- **Mock Backtester**: Lightweight testing implementation
- **Backtester Patch**: Runtime patches for debugging
- **Test Suite**: Comprehensive unit and integration tests

### Utilities & Support
- **State Mapping**: GMS state mapping utilities
- **System Validation**: Startup validation for configuration
- **Data Utilities**: Common data processing functions
- **Log Analyzer**: Comprehensive log analysis tools
- **Adaptive Thresholds**: Dynamic threshold calculations

## Key Simplifications Made

1. **Grouped Related Components**: Combined similar functionality into logical groups
2. **Reduced Visual Complexity**: Fewer boxes while maintaining all essential information
3. **Clearer Data Flow**: Simplified connections showing main data pathways
4. **Preserved Critical Details**: All important components and relationships maintained
5. **Enhanced Readability**: Better color coding and organization for LLM understanding

## Data Flow Summary

1. **Data Ingestion**: Raw market data flows through Data Handler
2. **Feature Engineering**: Feature Builder orchestrates calculations
3. **Signal Generation**: Signal Engine produces trading signals
4. **Regime Detection**: GMS detectors classify market conditions
5. **Strategy Evaluation**: Strategy Factory manages multiple strategies
6. **Risk Management**: Position sizing with dynamic adjustments
7. **Execution**: Trades executed through simulation engine
8. **Backtesting**: Performance analysis with comprehensive logging

## Architecture Benefits

- **Modular Design**: Clear separation of concerns
- **Extensible**: Easy to add new strategies and features
- **Configurable**: Comprehensive YAML-based configuration
- **Robust**: Extensive error handling and validation
- **Observable**: Comprehensive logging and monitoring
- **Testable**: Extensive debug and testing infrastructure

This simplified architecture maintains all critical context while being more digestible for LLM understanding and implementation guidance. 