{"tests_passed": 5, "tests_failed": 0, "errors": [], "warnings": [], "metrics": {"adapter_stats": {"total_rows_processed": 3600, "null_values_handled": 96, "fields_mapped": 1, "derived_fields_computed": 0, "warnings_generated": 0, "config": {"nan_handling": "interpolate", "compute_derived": true, "cache_enabled": true}}, "backtest": {"rows_processed": 3600, "updates_simulated": 60, "data_quality": {"nulls_in_volume_imbalance": 0, "nulls_in_momentum": 0}}}}