# ENHANCED PRD 1: Data Standardization, ATR Bug Fix & Continuous Mode Foundation

**Version**: 1.2 (Enhanced)  
**Author**: <PERSON> (Based on Codebase Analysis + Original PRD_1)  
**Date**: 2025-01-09  
**Target Implementation**: Incremental improvements + Critical new components  
**Priority**: HIGH  
**Risk Level**: LOW (preserves existing functionality)

## Executive Summary

This PRD addresses **critical data consistency issues** identified in the existing codebase while implementing **essential infrastructure** for continuous mode advancement. We combine targeted fixes to the existing sophisticated system with new components needed for the multi-timeframe roadmap.

## Key Issues Identified

### 1. ATR Calculation Bug (CRITICAL - Verify Status)
- **Problem**: Potential 100x unit conversion error in ATR calculations
- **Impact**: Incorrect volatility thresholds causing regime misclassification
- **Action**: Verify if already fixed, add validation regardless

### 2. Performance Bottlenecks
- **Problem**: Continuous mode 13x slower than legacy mode (663s vs 48s)
- **Impact**: Impractical for production use
- **Root Cause**: Inefficient 1-second feature loading and processing

### 3. Missing Performance Parity Validation
- **Problem**: No systematic comparison between legacy and continuous modes
- **Impact**: Cannot validate continuous mode improvements
- **Need**: Comprehensive parity testing framework

### 4. Missing 1-Minute OHLCV Infrastructure
- **Problem**: No way to generate 1-minute bars from features_1s
- **Impact**: Blocks multi-timeframe roadmap progression
- **Need**: Real-time OHLCV generation from mid_price data

## Phase 1: Critical ATR Validation & Schema Standardization (Days 1-2)

### Task 1.1: Verify and Validate ATR Calculations

**ATR Bug Check**:
```python
# hyperliquid_bot/features/calculator.py (verify existing implementation)
def validate_atr_calculations(df: pd.DataFrame) -> Dict[str, bool]:
    """
    Comprehensive ATR validation to check for unit conversion bugs.
    """
    validation_results = {}
    
    atr_columns = ['atr_percent', 'atr_percent_sec', 'atr_14_sec']
    
    for col in atr_columns:
        if col in df.columns:
            # Check for percentage format bug (values > 1.0)
            has_percentage_bug = (df[col] > 1.0).any()
            validation_results[f'{col}_percentage_bug'] = has_percentage_bug
            
            # Check reasonable ranges (0.01% to 10% as decimals)
            reasonable_range = (df[col] >= 0.0001) & (df[col] <= 0.1)
            validation_results[f'{col}_reasonable_range'] = reasonable_range.all()
            
            if has_percentage_bug:
                logger.error(f"ATR BUG DETECTED: Column '{col}' contains percentage values > 1.0")
                
    return validation_results

def fix_atr_percentage_calculation(atr_values: pd.Series, close_prices: pd.Series) -> pd.Series:
    """
    Ensure ATR percentage is in decimal format (0.0123 for 1.23%).
    """
    atr_percent = atr_values / close_prices
    
    # Validate result
    if (atr_percent > 1.0).any():
        logger.warning("ATR percentage values > 100% detected - check calculation")
    
    return atr_percent
```

### Task 1.2: Define Unified Feature Schema

**File**: `hyperliquid_bot/data/schemas.py` (new)

```python
# Unified schema compatible with existing architecture
UNIFIED_SCHEMA = {
    'timestamp': 'datetime64[ns]',
    'open': 'float32',
    'high': 'float32',
    'low': 'float32', 
    'close': 'float32',
    'volume': 'float32',
    'mid_price': 'float32',
    'atr_14_sec': 'float32',
    'atr_percent_sec': 'float32',  # Match existing column names
    'spread_mean': 'float32',
    'spread_std': 'float32',
    'ma_slope': 'float32',
    'obi_smoothed_5': 'float32'
}

CONTINUOUS_EXTENDED_SCHEMA = {
    'spread_bps': 'float32',
    'volume_imbalance': 'float32',
    'obi_5': 'float32',
    'ma_slope_ema_30s': 'float32'
}

class DataValidator:
    """Ensures data consistency across modes"""
    
    @staticmethod
    def validate_dataframe(df: pd.DataFrame, mode: str = 'legacy') -> Tuple[bool, List[str]]:
        """
        Validates dataframe against unified schema.
        Returns: (is_valid, list_of_issues)
        """
        issues = []
        
        # Check required columns
        required_cols = list(UNIFIED_SCHEMA.keys())
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            issues.append(f"Missing columns: {missing_cols}")
        
        # Check ATR format specifically
        atr_validation = validate_atr_calculations(df)
        for check, passed in atr_validation.items():
            if not passed:
                issues.append(f"ATR validation failed: {check}")
        
        # Check for excessive NaNs
        for col in required_cols:
            if col in df.columns:
                nan_ratio = df[col].isna().sum() / len(df)
                if nan_ratio > 0.1:  # More than 10% NaN
                    issues.append(f"Column {col}: {nan_ratio:.1%} NaN values")
        
        return len(issues) == 0, issues
```

### Task 1.3: Enhance Existing Data Loaders

**File**: `hyperliquid_bot/data/unified_loader.py` (new - integrates with existing handlers)

```python
class UnifiedDataLoader:
    """Single interface for loading data for both modes - integrates with existing architecture"""
    
    def __init__(self, config: Config):
        self.config = config
        self.validator = DataValidator()
        # Use existing handlers
        self.historical_handler = HistoricalDataHandler(config)
        self.feature_store = FeatureStore(config)
    
    def load_for_backtest(
        self, 
        start_date: datetime, 
        end_date: datetime,
        mode: str = 'legacy'
    ) -> pd.DataFrame:
        """
        Loads and validates data for backtesting using existing infrastructure.
        """
        if mode == 'legacy':
            df = self._load_legacy_format(start_date, end_date)
        else:  # continuous
            df = self._load_continuous_format(start_date, end_date)
        
        # Validate using unified schema
        is_valid, issues = self.validator.validate_dataframe(df, mode)
        if not is_valid:
            logger.warning(f"Data validation issues: {issues}")
        
        return df
    
    def _load_legacy_format(self, start, end) -> pd.DataFrame:
        """Load using existing HistoricalDataHandler"""
        self.historical_handler.load_historical_data(start, end)
        return self.historical_handler.get_ohlcv_data()
    
    def _load_continuous_format(self, start, end) -> pd.DataFrame:
        """Load from features_1s using existing FeatureStore"""
        return self.feature_store.load_features_for_period(start, end)
```

## Phase 2: Performance Parity Validation Framework (Days 3-4)

### Task 2.1: Create Comprehensive Parity Test Framework

**File**: `tests/test_mode_parity.py` (new)

```python
class TestModeParity:
    """Ensures continuous mode matches legacy mode performance"""
    
    def __init__(self):
        self.config = Config()
        self.tolerance = {
            'trades_per_year': 0.1,  # 10% tolerance
            'sharpe_ratio': 0.15,    # 15% tolerance
            'total_return': 0.20     # 20% tolerance
        }
    
    def test_parity(self, test_period: Tuple[datetime, datetime]):
        """
        Run both modes and compare results systematically.
        """
        logger.info(f"Starting parity test for period {test_period}")
        
        # 1. Run legacy mode (baseline)
        legacy_results = self._run_legacy_backtest(test_period)
        
        # 2. Configure continuous mode to match legacy exactly
        continuous_config = self._create_matching_config()
        continuous_results = self._run_continuous_backtest(test_period, continuous_config)
        
        # 3. Compare results with tolerance
        metrics_comparison = self._compare_metrics(legacy_results, continuous_results)
        
        # 4. Generate detailed report
        self._generate_parity_report(metrics_comparison)
        
        return metrics_comparison
    
    def _create_matching_config(self) -> Config:
        """Configure continuous mode to behave exactly like legacy"""
        config = deepcopy(self.config)
        
        # Use continuous_gms detector
        config.regime.detector_type = 'continuous_gms'
        
        # Match timing exactly
        config.gms.continuous_gms.cadence_sec = 3600  # 1-hour updates like legacy
        config.gms.continuous_gms.output_states = 8   # Full states like legacy
        
        # CRITICAL: Disable adaptive thresholds to match legacy
        config.gms.continuous_gms.auto_thresholds = False
        config.gms.continuous_gms.vol_thresh_mode = 'fixed'
        
        # Copy exact threshold values from legacy
        config.gms.continuous_gms.vol_low_thresh = config.regime.gms_vol_low_thresh
        config.gms.continuous_gms.vol_high_thresh = config.regime.gms_vol_high_thresh
        config.gms.continuous_gms.mom_weak_thresh = config.regime.gms_mom_weak_thresh
        config.gms.continuous_gms.mom_strong_thresh = config.regime.gms_mom_strong_thresh
        
        return config
    
    def _compare_metrics(self, legacy: dict, continuous: dict) -> dict:
        """Compare key metrics with tolerance checks"""
        comparison = {
            'period': legacy['period'],
            'legacy': legacy,
            'continuous': continuous,
            'differences': {},
            'within_tolerance': {}
        }
        
        # Calculate differences
        for metric in ['n_trades', 'sharpe_ratio', 'total_return', 'max_drawdown']:
            if metric in legacy and metric in continuous:
                diff_pct = (continuous[metric] - legacy[metric]) / legacy[metric] * 100
                comparison['differences'][f'{metric}_diff_%'] = diff_pct
                
                # Check tolerance
                tolerance_key = metric if metric in self.tolerance else 'total_return'
                within_tolerance = abs(diff_pct) <= (self.tolerance[tolerance_key] * 100)
                comparison['within_tolerance'][metric] = within_tolerance
        
        return comparison
```

### Task 2.2: Performance Comparison Report Generator

**File**: `reports/parity_validation_report.py` (new)

```python
def generate_parity_report(comparison_results: dict) -> None:
    """Generate detailed comparison report with visualizations"""
    
    report = {
        'summary': {
            'test_period': comparison_results['period'],
            'parity_status': 'PASS' if all(comparison_results['within_tolerance'].values()) else 'FAIL',
            'legacy_mode': comparison_results['legacy'],
            'continuous_mode': comparison_results['continuous'],
            'differences': comparison_results['differences'],
            'tolerance_checks': comparison_results['within_tolerance']
        },
        'detailed_analysis': {
            'performance_delta': {},
            'recommendations': []
        }
    }
    
    # Analyze significant differences
    for metric, diff_pct in comparison_results['differences'].items():
        if abs(diff_pct) > 5:  # > 5% difference
            report['detailed_analysis']['performance_delta'][metric] = {
                'difference_%': diff_pct,
                'significance': 'HIGH' if abs(diff_pct) > 15 else 'MEDIUM'
            }
    
    # Generate recommendations
    if not all(comparison_results['within_tolerance'].values()):
        report['detailed_analysis']['recommendations'].append(
            "Continuous mode not within tolerance - investigate threshold configuration"
        )
    
    # Save comprehensive report
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_path = f'reports/parity_validation_{timestamp}.json'
    
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    logger.info(f"Parity validation report saved to {report_path}")
    return report_path
```

## Phase 3: Critical Infrastructure Components (Days 5-6)

### Task 3.1: Selective Feature Loader for Performance

**File**: `hyperliquid_bot/data/selective_loader.py` (new)

```python
class SelectiveFeatureLoader:
    """Efficiently loads only required columns from features_1s - integrates with FeatureStore"""
    
    def __init__(self, feature_store: FeatureStore):
        self.feature_store = feature_store
        self.cache = {}  # Simple in-memory cache
        self.cache_max_size = 100
        
    def load_window(
        self, 
        timestamp: pd.Timestamp, 
        window_seconds: int = 60,
        columns: List[str] = None
    ) -> pd.DataFrame:
        """
        Load specific columns for a time window.
        Target: <10ms for 60s window.
        """
        if columns is None:
            # Default columns for continuous GMS
            columns = ['timestamp', 'mid_price', 'atr_percent_sec', 'spread_mean', 
                      'spread_std', 'ma_slope', 'obi_smoothed_5']
        
        # Create cache key
        cache_key = f"{timestamp}:{window_seconds}:{','.join(sorted(columns))}"
        
        if cache_key in self.cache:
            return self.cache[cache_key].copy()
        
        # Load data using existing FeatureStore with column selection
        start_time = timestamp - pd.Timedelta(seconds=window_seconds)
        
        try:
            # Use FeatureStore's existing infrastructure but with column selection
            df = self.feature_store.load_features_selective(
                start_time, timestamp, columns=columns
            )
            
            # Cache with size limit
            if len(self.cache) >= self.cache_max_size:
                # Remove oldest entry
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
            
            self.cache[cache_key] = df.copy()
            return df
            
        except Exception as e:
            logger.error(f"Failed to load features for window {timestamp}: {e}")
            return pd.DataFrame()
```

### Task 3.2: 1-Minute OHLCV Generator (Critical for Roadmap)

**File**: `hyperliquid_bot/data/resampler.py` (new)

```python
class MinuteBarGenerator:
    """
    Creates 1-minute OHLCV from features_1s mid_price.
    CRITICAL: Required for multi-timeframe roadmap progression.
    """
    
    def __init__(self, feature_loader: SelectiveFeatureLoader):
        self.loader = feature_loader
        self.cache = {}
        self.cache_max_size = 50
        
    def get_bars(
        self, 
        timestamp: pd.Timestamp, 
        lookback_minutes: int = 20
    ) -> pd.DataFrame:
        """
        Generate 1-minute OHLCV bars from mid_price data.
        
        Returns DataFrame with columns: ['open', 'high', 'low', 'close', 'volume']
        """
        # Check cache first
        cache_key = f"{timestamp}:{lookback_minutes}"
        if cache_key in self.cache:
            return self.cache[cache_key].copy()
        
        # Load raw mid_price data
        window_seconds = lookback_minutes * 60 + 60  # Extra minute for safety
        df = self.loader.load_window(
            timestamp, 
            window_seconds, 
            columns=['timestamp', 'mid_price']
        )
        
        if df.empty:
            logger.warning(f"No data available for 1-minute bar generation at {timestamp}")
            return pd.DataFrame()
        
        # Resample to 1-minute OHLCV
        df_indexed = df.set_index('timestamp')
        
        df_1m = df_indexed['mid_price'].resample('1T').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min', 
            'close': 'last'
        }).dropna()
        
        # Calculate volume proxy (tick count per minute)
        df_1m['volume'] = df_indexed['mid_price'].resample('1T').count()
        
        # Calculate additional useful metrics
        df_1m['typical_price'] = (df_1m['high'] + df_1m['low'] + df_1m['close']) / 3
        df_1m['price_change'] = df_1m['close'].pct_change()
        
        # Cache with size limit
        if len(self.cache) >= self.cache_max_size:
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
        
        self.cache[cache_key] = df_1m.copy()
        return df_1m
    
    def get_current_bar(self, timestamp: pd.Timestamp) -> Dict[str, float]:
        """Get current incomplete 1-minute bar for real-time use"""
        # Load last 2 minutes to ensure we have current minute data
        df = self.loader.load_window(timestamp, 120, columns=['timestamp', 'mid_price'])
        
        if df.empty:
            return {}
        
        # Filter to current minute
        current_minute = timestamp.floor('T')
        current_data = df[df['timestamp'] >= current_minute]
        
        if current_data.empty:
            return {}
        
        # Calculate OHLC for current incomplete bar
        prices = current_data['mid_price']
        return {
            'open': prices.iloc[0],
            'high': prices.max(),
            'low': prices.min(),
            'close': prices.iloc[-1],
            'volume': len(prices),
            'timestamp': current_minute
        }
```

### Task 3.3: Enhanced Performance Optimization

**File**: `hyperliquid_bot/core/unified_gms_detector.py` (enhance existing)

```python
# Add to existing UnifiedGMSDetector class
class UnifiedGMSDetector:
    def __init__(self, config: Config):
        # ... existing initialization ...
        
        # Add performance optimizations
        self.regime_cache = {}
        self.cache_max_size = 1000
        self.performance_stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'avg_calculation_time': 0
        }
        
    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None):
        """Enhanced with caching and performance monitoring."""
        start_time = time.time()
        
        # Create cache key from relevant signals
        cache_key = self._create_cache_key(signals)
        
        if cache_key in self.regime_cache:
            self.performance_stats['cache_hits'] += 1
            return self.regime_cache[cache_key]
        
        # Calculate regime
        self.performance_stats['cache_misses'] += 1
        result = self._compute_regime_internal(signals, price_history)
        
        # Cache result with size limit
        if len(self.regime_cache) >= self.cache_max_size:
            # Remove oldest entry (FIFO)
            oldest_key = next(iter(self.regime_cache))
            del self.regime_cache[oldest_key]
        
        self.regime_cache[cache_key] = result
        
        # Update performance stats
        calculation_time = time.time() - start_time
        self.performance_stats['avg_calculation_time'] = (
            self.performance_stats['avg_calculation_time'] * 0.9 + 
            calculation_time * 0.1
        )
        
        return result
    
    def _create_cache_key(self, signals: dict) -> str:
        """Create deterministic cache key from signals"""
        # Round values to avoid cache misses from tiny differences
        rounded_signals = {
            k: round(v, 6) if isinstance(v, (int, float)) else v 
            for k, v in signals.items()
        }
        return str(sorted(rounded_signals.items()))
    
    def get_performance_stats(self) -> dict:
        """Get performance statistics for monitoring"""
        total_requests = self.performance_stats['cache_hits'] + self.performance_stats['cache_misses']
        cache_hit_rate = self.performance_stats['cache_hits'] / max(total_requests, 1)
        
        return {
            'cache_hit_rate': cache_hit_rate,
            'total_requests': total_requests,
            'avg_calculation_time_ms': self.performance_stats['avg_calculation_time'] * 1000
        }
```

## Phase 4: Performance Benchmarking & Validation (Day 7)

### Task 4.1: Comprehensive Benchmarking Suite

**File**: `benchmarks/continuous_mode_perf.py` (new)

```python
class ContinuousModeBenchmark:
    """Measures performance of continuous mode operations with specific targets"""
    
    def __init__(self, config: Config):
        self.config = config
        self.targets = {
            'feature_loading_ms': 10,      # <10ms for 60s window
            'gms_detection_ms': 50,        # <50ms per detection
            'signal_generation_ms': 30,    # <30ms per signal
            'memory_usage_mb': 1000,       # <1GB for 24h operation
            'decision_cycle_ms': 100       # <100ms end-to-end
        }
    
    def run_all_benchmarks(self) -> dict:
        """Run comprehensive performance benchmarks"""
        results = {}
        
        logger.info("Starting comprehensive performance benchmarks...")
        
        # 1. Feature loading speed
        results['feature_loading'] = self._benchmark_feature_loading()
        
        # 2. GMS detection speed  
        results['gms_detection'] = self._benchmark_gms_detection()
        
        # 3. Signal generation speed
        results['signal_generation'] = self._benchmark_signal_generation()
        
        # 4. Memory usage monitoring
        results['memory_usage'] = self._measure_memory_usage()
        
        # 5. End-to-end decision cycle
        results['decision_cycle'] = self._benchmark_decision_cycle()
        
        # 6. 1-minute bar generation
        results['minute_bar_generation'] = self._benchmark_minute_bars()
        
        # Generate comprehensive report
        report_path = self._generate_benchmark_report(results)
        
        logger.info(f"Benchmark results saved to {report_path}")
        return results
    
    def _benchmark_feature_loading(self) -> dict:
        """Target: <10ms for 60s of features"""
        from hyperliquid_bot.data.selective_loader import SelectiveFeatureLoader
        
        feature_store = FeatureStore(self.config)
        loader = SelectiveFeatureLoader(feature_store)
        
        times = []
        test_timestamp = pd.Timestamp.now()
        
        for i in range(100):
            start = time.time()
            df = loader.load_window(
                test_timestamp, 
                window_seconds=60,
                columns=['mid_price', 'atr_percent_sec', 'spread_mean', 'obi_smoothed_5']
            )
            elapsed_ms = (time.time() - start) * 1000
            times.append(elapsed_ms)
            
            # Vary timestamp slightly for realistic test
            test_timestamp += pd.Timedelta(seconds=1)
            
        return {
            'mean_ms': np.mean(times),
            'median_ms': np.median(times),
            'p95_ms': np.percentile(times, 95),
            'p99_ms': np.percentile(times, 99),
            'target_ms': self.targets['feature_loading_ms'],
            'target_met': np.mean(times) < self.targets['feature_loading_ms'],
            'samples': len(times)
        }
    
    def _benchmark_gms_detection(self) -> dict:
        """Target: <50ms per GMS detection"""
        detector = UnifiedGMSDetector(self.config)
        
        # Create realistic test signals
        test_signals = [
            {
                'atr_percent_sec': 0.015,
                'ma_slope': 75.0,
                'obi_smoothed_5': 0.1,
                'spread_mean': 0.0001,
                'spread_std': 0.0003
            },
            {
                'atr_percent_sec': 0.008,
                'ma_slope': -45.0,
                'obi_smoothed_5': -0.2,
                'spread_mean': 0.0002,
                'spread_std': 0.0001
            }
            # Add more varied test cases
        ]
        
        times = []
        for _ in range(200):
            signals = random.choice(test_signals)
            # Add some noise for realistic variation
            for key in signals:
                signals[key] += random.uniform(-0.001, 0.001)
            
            start = time.time()
            regime = detector.get_regime(signals)
            elapsed_ms = (time.time() - start) * 1000
            times.append(elapsed_ms)
        
        # Get cache performance stats
        perf_stats = detector.get_performance_stats()
        
        return {
            'mean_ms': np.mean(times),
            'median_ms': np.median(times),
            'p95_ms': np.percentile(times, 95),
            'target_ms': self.targets['gms_detection_ms'],
            'target_met': np.mean(times) < self.targets['gms_detection_ms'],
            'cache_hit_rate': perf_stats['cache_hit_rate'],
            'samples': len(times)
        }
    
    def _benchmark_minute_bars(self) -> dict:
        """Benchmark 1-minute bar generation performance"""
        from hyperliquid_bot.data.selective_loader import SelectiveFeatureLoader
        from hyperliquid_bot.data.resampler import MinuteBarGenerator
        
        feature_store = FeatureStore(self.config)
        loader = SelectiveFeatureLoader(feature_store)
        bar_generator = MinuteBarGenerator(loader)
        
        times = []
        test_timestamp = pd.Timestamp.now()
        
        for i in range(50):
            start = time.time()
            bars = bar_generator.get_bars(test_timestamp, lookback_minutes=20)
            elapsed_ms = (time.time() - start) * 1000
            times.append(elapsed_ms)
            
            test_timestamp += pd.Timedelta(minutes=1)
        
        return {
            'mean_ms': np.mean(times),
            'p95_ms': np.percentile(times, 95),
            'target_ms': 100,  # Target: <100ms for 20-minute bars
            'target_met': np.mean(times) < 100,
            'samples': len(times)
        }
    
    def _generate_benchmark_report(self, results: dict) -> str:
        """Generate detailed benchmark report"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = f'reports/performance_benchmark_{timestamp}.json'
        
        # Calculate overall performance score
        targets_met = sum(1 for r in results.values() if r.get('target_met', False))
        total_targets = len([r for r in results.values() if 'target_met' in r])
        performance_score = targets_met / max(total_targets, 1) * 100
        
        report = {
            'summary': {
                'timestamp': timestamp,
                'performance_score': performance_score,
                'targets_met': f"{targets_met}/{total_targets}",
                'overall_status': 'PASS' if performance_score >= 80 else 'FAIL'
            },
            'detailed_results': results,
            'recommendations': self._generate_recommendations(results)
        }
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        return report_path
    
    def _generate_recommendations(self, results: dict) -> List[str]:
        """Generate performance improvement recommendations"""
        recommendations = []
        
        if not results['feature_loading']['target_met']:
            recommendations.append(
                "Feature loading exceeds target - consider implementing more aggressive caching"
            )
        
        if not results['gms_detection']['target_met']:
            recommendations.append(
                "GMS detection slow - optimize threshold calculations or increase cache size"
            )
        
        if results['gms_detection']['cache_hit_rate'] < 0.7:
            recommendations.append(
                "Low cache hit rate - review cache key generation strategy"
            )
        
        return recommendations
```

## Success Criteria & Validation

### Phase 1 Success (ATR Validation & Schema)
- [ ] ATR calculations verified (no values > 1.0 in percentage columns)
- [ ] Unified schema documented and implemented
- [ ] Data validation framework working
- [ ] Both modes load data through same interface

### Phase 2 Success (Parity Validation)
- [ ] Continuous mode matches legacy within tolerances:
  - Trades per year: ±10%
  - Sharpe ratio: ±15%
  - Total return: ±20%
- [ ] Detailed comparison report generated
- [ ] Any significant differences explained and justified

### Phase 3 Success (Critical Infrastructure)
- [ ] Selective feature loading <10ms for 60s window
- [ ] 1-minute bar generation working correctly
- [ ] Performance optimizations showing measurable improvement
- [ ] Memory usage optimized

### Phase 4 Success (Performance Benchmarking)
- [ ] All benchmarks passing targets (≥80% score)
- [ ] Decision cycle <100ms end-to-end
- [ ] No memory leaks in extended testing
- [ ] Comprehensive performance report generated

## Risk Mitigation

1. **Legacy Mode Protection**: All changes isolated to new components or enhancements
2. **Incremental Testing**: Each phase validated before proceeding
3. **Rollback Plan**: Git tags at each phase completion
4. **Performance Monitoring**: Continuous benchmarking during development
5. **Backward Compatibility**: All new components integrate with existing architecture

## Implementation Notes for Claude Code

1. **Start with ATR validation** - Verify current status and add comprehensive checks
2. **Build parity framework early** - Critical for validating all other improvements
3. **Implement 1-minute bars** - Required for roadmap progression
4. **Use existing architecture** - Enhance rather than replace
5. **Comprehensive testing** - Each component must pass benchmarks

## Next Steps After Enhanced PRD 1

Once all components are implemented and validated:
- **PRD 2**: Multi-timeframe signal hierarchy (enabled by 1-minute bars)
- **PRD 3**: Advanced continuous mode optimizations
- **PRD 4**: Real-time microstructure scoring

This enhanced PRD combines the **critical bug fixes and architectural compatibility** with the **essential infrastructure components** needed for the trading bot's evolution, ensuring both immediate stability and future capability.