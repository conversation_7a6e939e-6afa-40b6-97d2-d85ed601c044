# Threshold Investigation Findings

## Date: July 22, 2025

## Issue Discovered
The modern system had multiple threshold values that were off by factors of 1,500x to 50,000x compared to actual data ranges.

## Root Cause
Configuration values were set as absolute numbers when they should have been percentages/decimals, or vice versa. This is a common mistake when translating between different units (basis points, percentages, decimals).

## Specific Errors Found

### 1. Momentum Thresholds (Correct in Modern Config)
- **Legacy Values**: 50.0 and 100.0
- **Modern Values**: 0.0003 and 0.001 ✅ CORRECT
- **Actual Data Range**: -0.002 to 0.002
- **Error Magnitude**: Legacy was 100,000x too high

### 2. Spread Mean Threshold  
- **Legacy Value**: 0.000045 (4.5 basis points)
- **Modern Value**: 1.2 ❌ WRONG
- **Actual Data Range**: 0.0008 to 0.0015
- **Error Magnitude**: 1,500x too high
- **Fixed To**: 0.0008

### 3. Spread Std Threshold
- **Legacy Value**: 0.00005 (5 basis points)
- **Modern Value**: 2.5 ✅ CORRECT (matches data)
- **Actual Data Range**: 1.2 to 2.6
- **Note**: Legacy appears to be wrong here

## Data Analysis Results
From `analyze_data_ranges.py` on 2024 data:

```
ATR Percent (Volatility):
  Range: [0.00116498, 0.10454869]
  25th percentile: 0.00612861
  75th percentile: 0.00943034

MA Slope (Momentum):
  Range: [-0.05624879, 0.03907521]
  50th percentile (abs): 0.00053681
  90th percentile (abs): 0.00222747

OBI Smoothed (Volume Imbalance):
  Range: [-0.8486, 0.8623]
  75th percentile (abs): 0.3596
  90th percentile (abs): 0.5216

Spread Mean:
  Range: [0.00010061, 0.00509338]
  25th percentile: 0.00080078

Spread Std:
  Range: [0.00000000, 11.87079581]
  90th percentile: 2.65844614
```

## Actions Taken

### 1. Fixed Configuration
Updated `modern_system_v2_complete.yaml`:
```yaml
gms_spread_mean_low_thresh: 0.0008  # Was 1.2 (1500x too high)
```

### 2. Regenerated Regime Cache
**CRITICAL**: The modern system uses pre-computed regime states for performance. After changing thresholds, the cache MUST be regenerated:
```bash
python scripts/precompute_regimes.py --start 2024-01-01 --end 2024-12-31 --config configs/overrides/modern_system_v2_complete.yaml
```

## Expected Impact
- Regime detection should now properly identify market states
- Trade frequency may change significantly  
- Entry/exit timing should improve

## Lessons Learned
1. Always verify threshold units (%, basis points, decimals)
2. Use data analysis to validate configuration values
3. Remember to regenerate caches after config changes
4. Document units clearly in configuration files

## Test Results After Fix

### January 2024 Test
- **Before Fix**: 23 trades, -2.13% ROI, 27.27% win rate
- **After Fix**: 23 trades, -2.13% ROI, 27.27% win rate
- **Conclusion**: No change in performance

### Key Finding
Despite fixing a 1,500x threshold error and regenerating the regime cache, the results remained identical. This indicates that:

1. The `gms_spread_mean_low_thresh` threshold may not be actively used in trading decisions
2. The poor performance has other root causes beyond threshold calibration
3. We need to investigate the actual regime detection patterns and entry/exit logic

### Regime Distribution (January 2024)
After regenerating with fixed thresholds:
```
Weak_Bull_Trend      158 (25.3%)
Low_Vol_Range        157 (25.2%)
Weak_Bear_Trend      154 (24.7%)
Uncertain             92 (14.7%)
High_Vol_Range        44 (7.1%)
Strong_Bull_Trend     10 (1.6%)
Strong_Bear_Trend      9 (1.4%)
```

The system is detecting mostly weak/uncertain regimes, which may explain the poor performance.

## Next Steps
1. ✅ Complete regime cache regeneration
2. ✅ Run full 2024 backtest with fixed thresholds (no improvement)
3. Continue with regime detection comparison
4. Analyze entry/exit logic differences
5. Investigate why the system enters losing trades