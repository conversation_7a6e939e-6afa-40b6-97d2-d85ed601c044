# Trading Bot Data Schema Analysis

## Overview

This document provides the exact schema information for the trading bot's data sources, critical for understanding column names and data types that the GMS and TF strategy code will access.

## Data Sources Summary

Your trading bot uses **4 primary data sources**:

1. **1-Hour OHLCV Data** (Primary for 1-hour strategies)
2. **1-Second Features Data** (Used by continuous_gms and tf-v3)
3. **L2 Raw Order Book Data** (Used by legacy granular_microstructure)
4. **Fear & Greed Index** (Optional external data)

---

## 1. 1-Hour OHLCV Data Schema

**Location**: `hyperliquid_data/resampled_l2/1h/*.parquet`
**File Pattern**: `YYYY-MM-DD_1h.parquet`
**Sample File**: `2025-03-01_1h.parquet`

### Schema Details:
```
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 24 entries, 0 to 23
Data columns (total 10 columns):
 #   Column          Non-Null Count  Dtype         
---  ------          --------------  -----         
 0   timestamp       24 non-null     datetime64[ns]
 1   open            24 non-null     float64       
 2   high            24 non-null     float64       
 3   low             24 non-null     float64       
 4   close           24 non-null     float64       
 5   log_ret         23 non-null     float64       
 6   realised_vol    12 non-null     float64       
 7   bid_slope       24 non-null     float64       
 8   ask_slope       24 non-null     float64       
 9   book_asymmetry  24 non-null     float64       
```

### Column Names (Exact):
```python
['timestamp', 'open', 'high', 'low', 'close', 'log_ret', 'realised_vol', 'bid_slope', 'ask_slope', 'book_asymmetry']
```

### Sample Data:
```
            timestamp     open     high      low     close  log_ret  realised_vol  bid_slope  ask_slope  book_asymmetry
0 2025-03-01 00:00:00  84313.5  84558.0  83775.5   83802.0      NaN           NaN  13.573857   7.725374       -0.313559
1 2025-03-01 01:00:00  83802.0  84851.5  83785.5   84644.5      NaN           NaN   6.107119  23.901649       -0.013391
2 2025-03-01 02:00:00  84644.5  85661.5  84321.5   85661.5      NaN           NaN  11.863111  22.140024        0.205341
```

### Key Notes:
- **Standard OHLCV columns**: `open`, `high`, `low`, `close` (lowercase)
- **Additional microstructure features**: `bid_slope`, `ask_slope`, `book_asymmetry`
- **Volume column**: Not present in this dataset
- **Index**: RangeIndex (not timestamp-indexed by default)
- **Timestamp format**: UTC datetime64[ns]

---

## 2. 1-Second Features Data Schema

**Location**: `hyperliquid_data/features_1s/YYYY-MM-DD/features_HH.parquet`
**File Pattern**: `features_00.parquet` to `features_23.parquet` (hourly files)
**Sample File**: `features_1s/2025-03-01/features_12.parquet`

### Schema Details:
```
<class 'pandas.core.frame.DataFrame'>
Int64Index: 3600 entries, 43193 to 46792
Columns: 109 entries, timestamp to atr_percent
dtypes: datetime64[ns](1), float64(108)
```

### Key Column Categories:
1. **L2 Order Book Data** (40 columns):
   - `bid_price_1` to `bid_price_20`, `bid_size_1` to `bid_size_20`
   - `ask_price_1` to `ask_price_20`, `ask_size_1` to `ask_size_20`

2. **Basic Market Data** (7 columns):
   - `timestamp`, `mid_price`, `spread`, `spread_relative`, `close`, `high`, `low`, `volume`

3. **OBI Features** (8 columns):
   - `raw_obi_5`, `raw_obi_20`, `obi_smoothed`, `obi_smoothed_5`, `obi_smoothed_20`
   - `obi_zscore_5`, `obi_zscore_20`, `raw_obi_L1_3`, `raw_obi_L1_10`

4. **Volatility & ATR** (6 columns):
   - `realised_vol_1s`, `atr_14_sec`, `atr_percent_sec`, `atr`, `atr_percent`

5. **Spread Statistics** (4 columns):
   - `spread_mean`, `spread_std`, `best_bid`, `best_ask`

6. **Technical Indicators** (3 columns):
   - `ma_slope`, `ma_slope_ema_30s`, `unrealised_pnl`

### Sample Data:
```
                timestamp  bid_price_1  bid_size_1  ...  atr_percent_sec         atr  atr_percent
43193 2025-03-01 12:00:00      84561.0     6.14291  ...         0.010213  863.662097     0.010213
43194 2025-03-01 12:00:01          NaN         NaN  ...              NaN  863.662097          NaN
```

---

## 3. L2 Raw Order Book Data Schema

**Location**: `hyperliquid_data/l2_raw/YYYY-MM-DD/BTC_HH_l2Book.arrow`
**File Format**: Arrow IPC files
**Sample File**: `l2_raw/2025-03-01/BTC_12_l2Book.arrow`

### Schema Details:
```
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 6328 entries, 0 to 6327
Data columns (total 6 columns):
 #   Column     Non-Null Count  Dtype         
---  ------     --------------  -----         
 0   timestamp  6328 non-null   int64         
 1   best_bid   6328 non-null   float64       
 2   best_ask   6328 non-null   float64       
 3   bids       6328 non-null   object        
 4   asks       6328 non-null   object        
 5   ts         6328 non-null   datetime64[ns]
```

### Column Details:
- **timestamp**: Unix timestamp (int64)
- **best_bid/best_ask**: Top of book prices (float64)
- **bids/asks**: Nested arrays of [price, size] pairs (object)
- **ts**: Converted datetime timestamp

### Sample Data:
```
       timestamp  best_bid  best_ask                                               bids                                               asks                      ts
0  1740830400253   84561.0   84562.0  [[84561.0, 6.14291], [84560.0, 0.00015], ...]  [[84562.0, 0.00744], [84563.0, 0.00015], ...]  2025-03-01 12:00:00.253
1  1740830402891   84561.0   84562.0  [[84561.0, 6.14291], [84560.0, 0.02372], ...]  [[84562.0, 0.00703], [84563.0, 0.02372], ...]  2025-03-01 12:00:02.891
```

---

## Data Flow Analysis

### Current Implementation (from `handler.py`):

1. **Primary Data Loading**: 
   - Loads 1-hour OHLCV from `hyperliquid_data/resampled_l2/1h/`
   - Uses `pd.read_parquet()` to load files

2. **Microstructure Integration**:
   - Legacy system loads L2 raw data day-by-day
   - Calculates features using `microstructure.py` functions
   - Merges with OHLCV using `pd.merge_asof()`

3. **Feature Store Integration**:
   - Attempts to load ATR from 1-second feature files
   - Resamples 1-second data to 1-hour for ATR statistics

### Column Name Mapping Issues:

**Potential Mismatches to Watch For**:
- Your code expects: `df['close']`, `df['atr']`
- 1-hour files have: `'close'` ✅, but no `'atr'` column directly
- 1-second files have: `'atr'`, `'atr_percent'` ✅
- Volume: Not present in 1-hour files, present in 1-second files

---

## Answers to Your Questions

### 1. Granular Microstructure Data Requirements

**For 1-hour operation**: The legacy `granular_microstructure` can work with 1-hour OHLCV data because:
- The `HistoricalDataHandler` loads L2 raw data separately
- It calculates microstructure features (OBI, spread, depth metrics) from L2 data
- These features are merged with 1-hour OHLCV using `merge_asof`

**However**: If L2 raw data is missing for a day, microstructure features will be NaN.

### 2. Continuous GMS Data Requirements

**For 1-hour operation**: The `continuous_gms` detector requires:
- 1-second features files for detailed microstructure calculations
- It can resample these to 1-hour internally
- Key features needed: `atr_percent`, `ma_slope`, `obi_smoothed`, `spread_mean`, `spread_std`

**All these features are present in the 1-second feature files**.

### 3. Data Source Dependencies

**For PRD 0 (1-hour focus)**:
- **Primary**: 1-hour OHLCV data (always required)
- **Secondary**: 1-second features (for continuous_gms and ATR statistics)
- **Optional**: L2 raw data (only if using legacy granular_microstructure)
- **External**: Fear & Greed index (optional, fetched via API)

---

## Recommendations for PRD 0

1. **Start with 1-hour OHLCV**: This is your most reliable data source
2. **Use 1-second features for ATR**: The handler already does this
3. **Simplify microstructure**: Consider using pre-calculated features from 1-second files rather than raw L2 processing
4. **Column name consistency**: Ensure your strategy code uses the exact column names shown above

---

## File Locations Summary

```
hyperliquid_data/
├── resampled_l2/1h/          # 1-hour OHLCV (PRIMARY for 1h strategies)
├── features_1s/YYYY-MM-DD/   # 1-second features (for continuous_gms)
└── l2_raw/YYYY-MM-DD/        # Raw L2 data (for legacy granular_microstructure)
```

This schema analysis should provide the exact information needed for your PRD 0 documentation and help identify any column name mismatches in your strategy code. 