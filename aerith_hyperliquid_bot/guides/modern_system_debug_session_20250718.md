# Modern System Debug Session - July 18, 2025

## Session Summary

This document captures the findings from today's debugging session and provides a systematic plan for resolving the modern system's 0-trade issue.

### What We Accomplished Today

1. **Fixed Risk Configuration**
   - Changed `risk_per_trade` from 0.02 (2%) to 0.25 (25%) in `modern_system_v2_complete.yaml`
   - This matches the legacy system configuration

2. **Created Execution Scripts**
   - `RUN_MODERN_BACKTEST.py` - One-button solution
   - `run_modern_easy.py` - Python wrapper with options
   - `run_modern.sh` - Shell wrapper
   - Updated default config in `run_modern_backtest.py`

3. **Identified Core Issues**
   - Modern system generates **0 trades** (critical issue)
   - Processing 1-second data is extremely slow
   - Data validation errors: missing `volume_imbalance` field
   - `obi_smoothed` needs to be mapped to `volume_imbalance`

### Critical Discovery: Data Flow Mismatch

From `MODERN_SYSTEM_EXPECTED.md`:
```
# For Modern System Backtesting:
features_1s/ → Resample to 1h bars → Feed to TF-v3
             → Keep raw for GMS updates every 60s
             → Generate 1m bars for execution refinement
```

**Current Implementation**: Processing raw 1s data directly (86,400 rows/day)
**Expected Implementation**: Resample to 1h bars first (24 rows/day)

This is a **3,600x performance difference** and likely affects signal quality.

## Root Cause Analysis

### 1. Signal Pipeline Integration
From the conversation history:
- Created `ModernSignalEngine` to calculate technical indicators
- Integrated with `HourlyStrategyEvaluator`
- Added look-ahead bias prevention (shift=1)
- **Status**: Implemented but may not be working correctly

### 2. Data Schema Issues
```
Expected fields:
- volume_imbalance (missing - needs mapping from obi_smoothed)
- timestamp (missing from some data)
- Various ATR fields have high null rates
```

### 3. Regime Detection Problems
- Using `continuous_modern_v2` detector
- Thresholds may be too restrictive
- Need to verify regime states are actually changing

### 4. Strategy Signal Generation
- TF-v3 expects pre-calculated indicators
- Signal engine may not be providing all required fields
- Entry conditions might never be met

## Systematic Debug Plan for Tomorrow

### Phase 1: Data Pipeline Verification (Priority: CRITICAL)

1. **Verify Data Loading**
   ```python
   # Check what data is actually being loaded
   python3 -c "
   from hyperliquid_bot.modern.data_loader import ModernDataLoader
   from hyperliquid_bot.config.settings import load_config
   from datetime import datetime
   
   config = load_config('configs/overrides/modern_system_v2_complete.yaml')
   loader = ModernDataLoader(config)
   data = loader.load_data(datetime(2024,1,1), datetime(2024,1,2))
   print(f'Rows loaded: {len(data)}')
   print(f'Columns: {list(data.columns)[:10]}...')
   print(f'Has volume_imbalance: {"volume_imbalance" in data.columns}')
   "
   ```

2. **Implement Proper Resampling**
   - Create a data preprocessor that resamples features_1s to 1h
   - Ensure all required fields are present
   - Verify ATR calculations are working

### Phase 2: Regime Detection Analysis

1. **Check Regime States**
   ```python
   # Add logging to see regime state changes
   # In ModernBacktestEngine._simulate_hourly_regime_updates()
   # Log: timestamp, regime_state, confidence, thresholds
   ```

2. **Verify Thresholds**
   - Current: vol=[0.006, 0.009], mom=[0.0005, 0.002]
   - May need calibration based on actual 1s data distribution

### Phase 3: Signal Generation Debug

1. **Trace Signal Flow**
   ```
   HourlyStrategyEvaluator.evaluate()
   → ModernSignalEngine.calculate_signals()
   → TFV3Strategy.should_enter()
   → Entry signal generated?
   ```

2. **Log Every Decision Point**
   - What signals are calculated?
   - What values do they have?
   - Which conditions fail?

### Phase 4: Integration Testing

1. **Create Minimal Test Case**
   - Load 1 hour of data
   - Force favorable regime state
   - Verify signal generation
   - Check if trade is created

## File Organization Issues (Secondary)

1. **Output Files**
   - Move JSON results to `logs/` directory
   - Use timestamp in filename: `modern_backtest_results_YYYYMMDD_HHMMSS.json`

2. **Backtest Metrics**
   - Current output missing standard metrics
   - Need to align with legacy backtest output format

## Key Files to Review

1. **`/hyperliquid_bot/modern/backtester_engine.py`**
   - Line 117: `run_backtest()` - main entry point
   - Line 143: `_simulate_hourly_regime_updates()` - regime simulation
   - Line 149: `_evaluate_trading_opportunity()` - trade evaluation

2. **`/hyperliquid_bot/modern/data_loader.py`**
   - Line 70: `load_data()` - data loading logic
   - Line 282: Column mapping (obi_smoothed → volume_imbalance)

3. **`/hyperliquid_bot/modern/signal_engine.py`**
   - Signal calculation logic
   - Look-ahead bias prevention

4. **`/hyperliquid_bot/modern/hourly_evaluator.py`**
   - Strategy evaluation logic
   - Signal preparation

## Quick Commands for Tomorrow

```bash
# Run modern backtest with debug output
python3 RUN_MODERN_BACKTEST.py

# Check data schema
python3 scripts/check_features_schema.py

# Test signal pipeline
python3 scripts/test_modern_signal_pipeline.py

# Run with legacy engine for comparison
python3 scripts/run_legacy_backtest.py
```

## Success Criteria

1. **Immediate Goal**: Generate at least 1 trade
2. **Short-term Goal**: Generate 60+ trades matching legacy performance
3. **Long-term Goal**: Improve upon legacy with modern enhancements

## Remember

- **Don't apply duct-tape fixes** - find root causes
- **Think systematically** - one change at a time
- **Document everything** - for future debugging
- **Test incrementally** - verify each fix works

## Next Session Starting Point

1. Run data pipeline verification (Phase 1)
2. Check if `volume_imbalance` mapping is working
3. Verify hourly resampling implementation
4. Add comprehensive logging to trace execution flow
5. Focus on getting that first trade generated

---

**Last Working State**: Legacy system with 180 trades, +215% ROI
**Current State**: Modern system with 0 trades, need systematic debugging
**Goal**: Modern system with 60+ trades, matching or exceeding legacy performance