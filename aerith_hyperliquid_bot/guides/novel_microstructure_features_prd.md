# Novel Microstructure Features PRD
**Status**: POSTPONED until modern system achieves profitability

## Executive Summary

During the enhanced data architecture implementation, we discovered that Hyperliquid provides 20-level order book data that is completely untapped by both legacy and modern systems. This presents a significant opportunity to create novel microstructure features that could improve regime detection and trade timing.

## Discovery Details

### Available Data (Per Second)
- **Bid Side**: 20 levels
  - `bid_px_1` through `bid_px_20` (prices)
  - `bid_sz_1` through `bid_sz_20` (sizes)
- **Ask Side**: 20 levels
  - `ask_px_1` through `ask_px_20` (prices)
  - `ask_sz_1` through `ask_sz_20` (sizes)

Total: 80 data points per second of deep market microstructure

### Current Usage
- Legacy system: Uses only level 1 (best bid/ask)
- Modern system: Uses only level 1 (best bid/ask)
- **Utilization: 2.5% of available order book data**

## Proposed Novel Features

### 1. Deep Book Pressure Score (DBPS)
**Concept**: Weighted imbalance across all 20 levels to detect institutional order flow

**Formula**:
```python
def calculate_dbps(bid_prices, bid_sizes, ask_prices, ask_sizes):
    """
    Calculate pressure score with distance-weighted volumes
    """
    mid_price = (bid_prices[0] + ask_prices[0]) / 2
    
    bid_pressure = 0
    ask_pressure = 0
    
    for i in range(20):
        # Weight decreases with distance from mid
        bid_weight = 1 / (1 + abs(bid_prices[i] - mid_price) / mid_price)
        ask_weight = 1 / (1 + abs(ask_prices[i] - mid_price) / mid_price)
        
        bid_pressure += bid_sizes[i] * bid_weight
        ask_pressure += ask_sizes[i] * ask_weight
    
    return (bid_pressure - ask_pressure) / (bid_pressure + ask_pressure)
```

**Expected Value**: Early detection of large order positioning

### 2. Liquidity Shape Entropy (LSE)
**Concept**: Information entropy of order book shape to detect market maker behavior

**Implementation**:
- Calculate size distribution entropy across levels
- Compare bid vs ask entropy asymmetry
- Track entropy changes over time

**Expected Value**: Identify when MMs pull liquidity (pre-volatility signal)

### 3. Order Book Velocity Gradient (OBVG)
**Concept**: Rate of change in book structure depth

**Features**:
- Level-wise volume velocity (dV/dt for each level)
- Gradient steepness changes
- Acceleration/deceleration patterns

**Expected Value**: Predict aggressive vs passive market phases

### 4. Market Maker Footprint (MMF)
**Concept**: Pattern recognition for typical MM behaviors

**Patterns to Detect**:
- Symmetric quote updates (MM rebalancing)
- Liquidity vacuum patterns (pre-move withdrawal)
- Spoofing detection (large orders that vanish)
- Quote stuffing identification

**Expected Value**: Trade with/against MM positioning

### 5. Microstructure Regime Features
**Integration with existing regime detection**:
- `deep_book_bull_score`: DBPS > 0.7 sustained
- `liquidity_drought`: LSE sudden drops
- `mm_accumulation`: MMF patterns + price stability
- `aggressive_buyers`: OBVG + price momentum

## Implementation Plan

### Phase 1: Data Pipeline (1 week)
1. Extend enhanced hourly aggregator for deep book features
2. Create efficient storage format (parquet with compression)
3. Implement streaming calculations for live trading

### Phase 2: Feature Engineering (1 week)
1. Implement DBPS with backtesting
2. Add LSE calculations
3. Develop OBVG metrics
4. Create MMF pattern detector

### Phase 3: Integration (1 week)
1. Add features to regime detection
2. Enhance entry/exit signals
3. Create microstructure-aware position sizing
4. Backtest with A/B comparison

### Phase 4: Optimization (1 week)
1. Feature selection (which add value?)
2. Weight optimization
3. Threshold tuning
4. Performance analysis

## Expected Outcomes

### Quantitative Goals
- Improve win rate by 5-10%
- Reduce false entry signals by 20%
- Better exit timing (capture additional 0.5-1% per trade)
- Earlier regime change detection (1-2 bars faster)

### Risk Considerations
- Overfitting to market maker behavior
- Computational overhead in live trading
- Data quality/gaps handling
- Feature correlation with existing signals

## Success Metrics
1. Backtest improvement: +20% ROI vs current
2. Sharpe ratio increase: >0.3
3. Maximum drawdown reduction: >10%
4. Win rate improvement: >5%

## Prerequisites
- Modern system must achieve baseline profitability
- Full 2024 enhanced data must be processed
- Regime detection must be properly calibrated

## Next Steps
1. **POSTPONED** - Focus on threshold calibration first
2. Return to this PRD after modern system achieves >100% ROI
3. Start with DBPS as simplest high-value feature

---
*Note: This PRD is intentionally postponed. The immediate focus is on fixing threshold values and achieving profitability with existing features before adding complexity.*