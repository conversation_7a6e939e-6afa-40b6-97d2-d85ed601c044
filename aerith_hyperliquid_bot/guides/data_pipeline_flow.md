# Hyperliquid Trading Bot: Data Pipeline Flow Architecture

This diagram illustrates the complete data flow from raw L2 order book data to trading decisions, showing both the legacy system (granular_microstructure + tf-v2) and modern system (continuous_gms + tf-v3) pipelines.

## Data Pipeline Flow Diagram

```mermaid
flowchart TD
    %% Raw Data Sources
    RawL2["`**Raw L2 JSON Files**
    📁 /hyperliquid_data/l2_raw/YYYY-MM-DD/
    📄 BTC_HH_l2Book.txt
    ⚡ 10Hz snapshots
    📊 Nested bid/ask arrays`"]
    
    %% Legacy Pipeline Branch
    subgraph LegacyPipeline ["`🔄 **LEGACY SYSTEM PIPELINE**
    ⏱️ Performance: 48.95s (Optimized)
    📈 Trade Count: 184 (Baseline)`"]
        Raw2Process["`**Raw2 Processing**
        📁 hyperliquid_data/raw2/
        📄 YYYYMMDD_raw2.parquet
        🔧 microstructure.py
        📊 37 features (5-depth)`"]
        
        ResampledL2["`**Hourly Aggregation**
        📁 hyperliquid_data/resampled_l2/1h/
        📄 YYYY-MM-DD_1h.parquet
        ⏰ 3600s cadence
        📈 OHLCV + microstructure`"]
        
        GranularDetector["`**Granular Microstructure Detector**
        📄 hyperliquid_bot/core/detector.py
        🎯 GranularMicrostructureRegimeDetector
        🔧 Fixed thresholds (0.55/0.92)
        📤 String output`"]
        
        TFv2Strategy["`**TF-v2 Strategy**
        📄 hyperliquid_bot/strategies/
        🎯 Trend Following v2
        ⏰ Hourly execution
        📊 OHLCV-based signals`"]
    end
    
    %% Modern Pipeline Branch
    subgraph ModernPipeline ["`🚀 **MODERN SYSTEM PIPELINE**
    ⏱️ Performance: 663.45s (BOTTLENECK)
    🔴 Adaptive Threshold Issue: 655s (98.8%)`"]
        ETLProcess["`**ETL L20 to 1s**
        📄 tools/etl_l20_to_1s.py
        🔄 Arrow → 1-second features
        📊 109 features (5-20 depth)
        ⚡ Median aggregation`"]
        
        Features1s["`**1-Second Features**
        📁 hyperliquid_data/features_1s/YYYY-MM-DD/
        📄 features_HH.parquet
        ⏰ 1-second cadence
        📈 Comprehensive microstructure`"]
        
        ContinuousDetector["`**Continuous GMS Detector**
        📄 hyperliquid_bot/core/gms_detector.py
        🎯 ContinuousGMSDetector
        🔧 Adaptive thresholds (SLOW)
        📤 Dict output {state, risk_suppressed}`"]
        
        TFv3Strategy["`**TF-v3 Strategy**
        📄 hyperliquid_bot/strategies/tf_v3.py
        🎯 Trend Following v3
        ⏰ 60s execution
        📊 1-second feature-based`"]
    end
    
    %% Proposed Unified System
    subgraph UnifiedSystem ["`✨ **PROPOSED UNIFIED SYSTEM**
    🎯 Zero Code Duplication
    ⚡ Optimized Performance`"]
        UnifiedDetector["`**Unified GMS Detector**
        📄 hyperliquid_bot/core/unified_gms_detector.py
        🔄 Mode: legacy | continuous
        🔧 Fixed + Optimized Adaptive
        📤 Compatible outputs`"]
        
        OptimizedThresholds["`**Optimized Adaptive Thresholds**
        📄 hyperliquid_bot/utils/optimized_adaptive_threshold.py
        ⚡ Batch processing (655s → 5s)
        💾 Memory pools
        📊 Vectorized operations`"]
    end
    
    %% Configuration and Control
    subgraph ConfigSystem ["`⚙️ **CONFIGURATION SYSTEM**`"]
        BaseConfig["`**Base Configuration**
        📄 configs/base.yaml
        🔧 Unified GMS settings
        🔄 Mode selection`"]
        
        MigrationTools["`**Migration Tools**
        📄 hyperliquid_bot/utils/config_migration.py
        🔄 Legacy → Unified
        ✅ Validation utilities`"]
    end
    
    %% Trading Execution
    subgraph TradingExecution ["`💰 **TRADING EXECUTION**`"]
        Backtester["`**Backtester**
        📄 hyperliquid_bot/backtester/backtester.py
        📊 Performance metrics
        📈 Trade simulation`"]
        
        Results["`**Trading Results**
        📊 Performance metrics
        💹 Trade execution
        📈 P&L tracking`"]
    end
    
    %% Data Flow Connections
    RawL2 --> Raw2Process
    RawL2 --> ETLProcess
    
    %% Legacy Flow
    Raw2Process --> ResampledL2
    ResampledL2 --> GranularDetector
    GranularDetector --> TFv2Strategy
    
    %% Modern Flow
    ETLProcess --> Features1s
    Features1s --> ContinuousDetector
    ContinuousDetector --> TFv3Strategy
    
    %% Unified Flow
    Features1s -.-> UnifiedDetector
    ResampledL2 -.-> UnifiedDetector
    UnifiedDetector -.-> OptimizedThresholds
    UnifiedDetector -.-> TFv2Strategy
    UnifiedDetector -.-> TFv3Strategy
    
    %% Configuration Flow
    BaseConfig --> GranularDetector
    BaseConfig --> ContinuousDetector
    BaseConfig --> UnifiedDetector
    MigrationTools -.-> BaseConfig
    
    %% Execution Flow
    TFv2Strategy --> Backtester
    TFv3Strategy --> Backtester
    Backtester --> Results
    
    %% Performance Annotations
    classDef legacy fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef modern fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef unified fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef config fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef bottleneck fill:#ffebee,stroke:#c62828,stroke-width:3px
    
    class Raw2Process,ResampledL2,GranularDetector,TFv2Strategy legacy
    class ETLProcess,Features1s,ContinuousDetector,TFv3Strategy modern
    class UnifiedDetector,OptimizedThresholds unified
    class BaseConfig,MigrationTools config
    class ContinuousDetector bottleneck
```

## Performance Characteristics

### Legacy System (Optimized)
- **Total Runtime:** 48.95 seconds
- **Trade Count:** 184 trades (baseline)
- **Data Source:** raw2/ parquet files (37 features, 5-depth)
- **Processing:** Hourly aggregation (3600s cadence)
- **Optimization:** L2 processing skip flag active
- **Bottlenecks:** Signal calculation (60.57s, 64.5% of runtime)

### Modern System (Current Issues)
- **Total Runtime:** 663.45 seconds (**13.6x slower**)
- **Primary Bottleneck:** Adaptive threshold priming (655.35s, 98.8%)
- **Data Source:** features_1s/ parquet files (109 features, 5-20 depth)
- **Processing:** 1-second features (60s cadence)
- **Issue:** 328,926 individual threshold updates during initialization

### Proposed Unified System (Optimized)
- **Legacy Mode:** 48.95s (preserved performance)
- **Continuous Mode:** <30s target (97% improvement)
- **Emergency Fix:** 663s → 60s (disable adaptive thresholds)
- **Optimized Adaptive:** Batch processing, vectorized operations

## Data Format Details

### Raw L2 JSON Structure
```
📁 /hyperliquid_data/l2_raw/2025-03-15/
├── BTC_13_l2Book.txt (10Hz snapshots)
├── BTC_14_l2Book.txt
└── BTC_15_l2Book.txt
```

### Legacy Data Pipeline
```
Raw L2 JSON → raw2/ parquet → resampled_l2/ → granular detector → tf-v2
     10Hz         37 cols        OHLCV+micro      string        hourly
```

### Modern Data Pipeline
```
Raw L2 Arrow → ETL → features_1s/ → continuous detector → tf-v3
     10Hz       1s      109 cols       dict output        60s
```

## Key Optimization Opportunities

1. **Adaptive Threshold System:** Replace O(n²) individual updates with O(n) batch processing
2. **Memory Management:** Use pre-allocated arrays and memory pools
3. **Percentile Calculations:** Vectorized operations instead of individual scipy calls
4. **Data Pipeline:** Optimize feature calculation and reduce redundant processing
5. **Configuration:** Unified settings to eliminate duplication and complexity
