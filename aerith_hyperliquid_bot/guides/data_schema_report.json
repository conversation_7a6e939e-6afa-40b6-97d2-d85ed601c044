{"discovery_timestamp": "2025-07-17T16:34:52.364527", "data_directory": "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s", "files_analyzed": 5, "date_range": {"start": "2024-01-01", "end": "2024-12-31"}, "columns": {"timestamp": {"dtype": "datetime64[ns]", "always_present": true, "null_percentage": 0.0, "value_range": {"min": "2024-01-01 03:00:00", "max": "2024-12-31 03:59:59"}, "sample_values": ["2024-01-01 03:00:00", "2024-01-01 03:00:01", "2024-01-01 03:00:02", "2024-01-01 03:00:03", "2024-01-01 03:00:04"]}, "bid_price_1": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42289.0, "max": 92672.0}, "sample_values": [42625.0, 42622.0, 42622.0, 42622.0, 42622.0]}, "bid_size_1": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 1e-05, "max": 46.84055}, "sample_values": [0.20133, 0.075, 0.27633, 0.27633, 0.27633]}, "bid_price_2": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42280.0, "max": 92671.0}, "sample_values": [42622.0, 42614.0, 42614.0, 42614.0, 42614.0]}, "bid_size_2": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 30.71573}, "sample_values": [0.075, 0.00791, 0.12363, 0.0005, 0.14848]}, "bid_price_3": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42278.0, "max": 92670.0}, "sample_values": [42614.0, 42613.0, 42613.0, 42613.0, 42613.0]}, "bid_size_3": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 16.631459999999997}, "sample_values": [0.0895, 4.2699, 4.2699, 4.2699, 4.2699]}, "bid_price_4": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42274.0, "max": 92669.0}, "sample_values": [42613.0, 42611.0, 42612.0, 42612.0, 42612.0]}, "bid_size_4": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 17.81427}, "sample_values": [4.29335, 0.02345, 0.12363, 0.0005, 0.14848]}, "bid_price_5": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42273.0, "max": 92668.0}, "sample_values": [42605.0, 42606.0, 42611.0, 42611.0, 42611.0]}, "bid_size_5": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00042, "max": 23.783245}, "sample_values": [8.25021, 0.15, 0.02345, 0.02345, 0.02345]}, "bid_price_6": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42272.0, "max": 92666.0}, "sample_values": [42595.0, 42605.0, 42605.0, 42605.0, 42605.0]}, "bid_size_6": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00018, "max": 17.47584}, "sample_values": [7.93477, 8.25021, 8.25021, 8.25021, 8.25021]}, "bid_price_7": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42254.0, "max": 92664.0}, "sample_values": [42572.0, 42595.0, 42595.0, 42595.0, 42595.0]}, "bid_size_7": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00021, "max": 28.58961}, "sample_values": [8.00957, 7.93477, 7.93477, 7.93477, 7.93477]}, "bid_price_8": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42247.0, "max": 92663.0}, "sample_values": [42566.0, 42572.0, 42572.0, 42572.0, 42572.0]}, "bid_size_8": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00019, "max": 18.80769}, "sample_values": [0.11759, 8.00957, 8.00957, 8.00957, 8.00957]}, "bid_price_9": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42230.0, "max": 92660.0}, "sample_values": [42533.0, 42566.0, 42566.0, 42566.0, 42566.0]}, "bid_size_9": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00018, "max": 29.13092}, "sample_values": [0.08817, 0.11759, 0.11759, 0.11759, 0.11759]}, "bid_price_10": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42206.0, "max": 92659.0}, "sample_values": [42527.0, 42533.0, 42533.0, 42533.0, 42533.0]}, "bid_size_10": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00018, "max": 19.57724}, "sample_values": [3.89914, 0.08817, 0.08817, 0.08817, 0.08817]}, "bid_price_11": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42203.5, "max": 92658.0}, "sample_values": [42524.0, 42527.0, 42527.0, 42527.0, 42527.0]}, "bid_size_11": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00019, "max": 29.67557}, "sample_values": [0.0294, 3.89914, 3.89914, 3.89914, 3.89914]}, "bid_price_12": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42200.5, "max": 92657.0}, "sample_values": [42515.0, 42524.0, 42524.0, 42524.0, 42524.0]}, "bid_size_12": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00018, "max": 20.08275}, "sample_values": [0.0072, 0.0294, 0.0294, 0.0294, 0.0294]}, "bid_price_13": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42196.0, "max": 92655.0}, "sample_values": [42513.0, 42515.0, 42515.0, 42515.0, 42515.0]}, "bid_size_13": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00018, "max": 17.77798}, "sample_values": [4.07705, 0.0072, 0.0072, 0.0072, 0.0072]}, "bid_price_14": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42192.5, "max": 92653.0}, "sample_values": [42481.0, 42513.0, 42513.0, 42513.0, 42513.0]}, "bid_size_14": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00019, "max": 17.46802}, "sample_values": [0.02942, 4.07705, 4.07705, 4.07705, 4.07705]}, "bid_price_15": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42178.5, "max": 92652.0}, "sample_values": [42399.0, 42481.0, 42481.0, 42481.0, 42481.0]}, "bid_size_15": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00016, "max": 13.34756}, "sample_values": [3.98626, 0.02942, 0.02942, 0.02942, 0.02942]}, "bid_price_16": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42150.0, "max": 92651.0}, "sample_values": [42398.0, 42399.0, 42399.0, 42399.0, 42399.0]}, "bid_size_16": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 29.94639}, "sample_values": [3.65626, 3.98626, 3.98626, 3.98626, 3.98626]}, "bid_price_17": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42100.0, "max": 92650.0}, "sample_values": [42350.0, 42398.0, 42398.0, 42398.0, 42398.0]}, "bid_size_17": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 18.08192}, "sample_values": [0.28391, 3.65626, 3.65626, 3.65626, 3.65626]}, "bid_price_18": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42082.0, "max": 92649.0}, "sample_values": [42201.0, 42350.0, 42350.0, 42350.0, 42350.0]}, "bid_size_18": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00018, "max": 18.55887}, "sample_values": [0.04415, 0.28391, 0.28391, 0.28391, 0.28391]}, "bid_price_19": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42078.0, "max": 92648.0}, "sample_values": [42189.0, 42201.0, 42201.0, 42201.0, 42201.0]}, "bid_size_19": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 42.10146}, "sample_values": [0.71108, 0.04415, 0.04415, 0.04415, 0.04415]}, "bid_price_20": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42075.5, "max": 92647.0}, "sample_values": [42171.0, 42189.0, 42189.0, 42189.0, 42189.0]}, "bid_size_20": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 42.10146}, "sample_values": [42.10146, 0.71108, 0.71108, 0.71108, 0.71108]}, "ask_price_1": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42293.0, "max": 92673.0}, "sample_values": [42627.0, 42626.0, 42627.0, 42627.0, 42627.0]}, "ask_size_1": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 2e-05, "max": 44.04999}, "sample_values": [0.00117, 0.02345, 0.00117, 0.02462, 0.02462]}, "ask_price_2": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42294.0, "max": 92676.0}, "sample_values": [42628.0, 42627.0, 42629.0, 42629.0, 42629.0]}, "ask_size_2": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 6e-05, "max": 38.5354}, "sample_values": [0.02345, 0.00117, 0.1125, 0.1125, 0.1125]}, "ask_price_3": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42296.0, "max": 92678.0}, "sample_values": [42629.0, 42629.0, 42631.0, 42631.0, 42631.0]}, "ask_size_3": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 8e-05, "max": 38.99285}, "sample_values": [0.1125, 0.1125, 6.78819, 6.78819, 6.78819]}, "ask_price_4": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42297.0, "max": 92679.5}, "sample_values": [42631.0, 42631.0, 42637.0, 42637.0, 42637.0]}, "ask_size_4": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00018, "max": 22.41678}, "sample_values": [6.78819, 6.78819, 0.0062, 0.0062, 0.0062]}, "ask_price_5": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42301.0, "max": 92680.5}, "sample_values": [42634.0, 42637.0, 42641.0, 42641.0, 42641.0]}, "ask_size_5": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.0003, "max": 35.847615000000005}, "sample_values": [3.68525, 0.0062, 7.67746, 7.67746, 7.67746]}, "ask_price_6": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42309.0, "max": 92681.5}, "sample_values": [42637.0, 42641.0, 42661.0, 42661.0, 42661.0]}, "ask_size_6": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.0002, "max": 36.04936}, "sample_values": [0.0062, 7.67746, 0.0165, 0.0165, 0.0165]}, "ask_price_7": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42313.0, "max": 92682.5}, "sample_values": [42641.0, 42661.0, 42669.0, 42669.0, 42669.0]}, "ask_size_7": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00016, "max": 20.719005000000003}, "sample_values": [15.3717, 0.0165, 3.67905, 3.67905, 3.67905]}, "ask_price_8": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42315.0, "max": 92683.5}, "sample_values": [42652.0, 42669.0, 42670.0, 42670.0, 42670.0]}, "ask_size_8": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.00025, "max": 35.29208}, "sample_values": [7.4296, 3.67905, 3.60889, 3.60889, 3.60889]}, "ask_price_9": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42333.0, "max": 92686.0}, "sample_values": [42661.0, 42670.0, 42684.0, 42684.0, 42684.0]}, "ask_size_9": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 20.52506}, "sample_values": [0.0165, 3.60889, 0.11744, 0.11744, 0.11744]}, "ask_price_10": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42342.0, "max": 92687.5}, "sample_values": [42670.0, 42684.0, 42715.0, 42715.0, 42715.0]}, "ask_size_10": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.0002, "max": 38.06113}, "sample_values": [3.60889, 0.11744, 3.72277, 3.72277, 3.72277]}, "ask_price_11": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42347.0, "max": 92689.0}, "sample_values": [42680.0, 42715.0, 42716.0, 42716.0, 42716.0]}, "ask_size_11": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 18.76043}, "sample_values": [3.96938, 3.72277, 4.35379, 4.35379, 4.35379]}, "ask_price_12": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42350.0, "max": 92690.0}, "sample_values": [42684.0, 42716.0, 42757.0, 42757.0, 42757.0]}, "ask_size_12": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 25.0184}, "sample_values": [3.66773, 4.35379, 0.0877, 0.0877, 0.0877]}, "ask_price_13": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42375.0, "max": 92691.0}, "sample_values": [42715.0, 42757.0, 42758.0, 42758.0, 42758.0]}, "ask_size_13": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.0002, "max": 35.33748}, "sample_values": [3.72277, 0.0877, 0.02923, 0.02923, 0.02923]}, "ask_price_14": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42384.0, "max": 92692.0}, "sample_values": [42716.0, 42758.0, 42801.0, 42801.0, 42801.0]}, "ask_size_14": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 22.77353}, "sample_values": [4.35379, 0.02923, 0.0292, 0.0292, 0.0292]}, "ask_price_15": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42392.0, "max": 92693.0}, "sample_values": [42757.0, 42801.0, 42808.0, 42808.0, 42808.0]}, "ask_size_15": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 41.07887}, "sample_values": [0.0877, 0.0292, 4.18585, 4.18585, 4.18585]}, "ask_price_16": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42393.0, "max": 92694.5}, "sample_values": [42758.0, 42808.0, 42817.0, 42817.0, 42817.0]}, "ask_size_16": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 41.97151}, "sample_values": [0.02923, 4.18585, 3.96821, 3.96821, 3.96821]}, "ask_price_17": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42400.0, "max": 92696.5}, "sample_values": [42801.0, 42817.0, 42900.0, 42900.0, 42900.0]}, "ask_size_17": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 41.97151}, "sample_values": [0.0292, 3.96821, 0.01611, 0.01611, 0.01611]}, "ask_price_18": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42401.0, "max": 92698.0}, "sample_values": [42808.0, 42900.0, 42950.0, 42950.0, 42950.0]}, "ask_size_18": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 41.97151}, "sample_values": [4.18585, 0.01611, 2.5, 2.5, 2.5]}, "ask_price_19": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42434.0, "max": 92700.0}, "sample_values": [42817.0, 42950.0, 43000.0, 43000.0, 43000.0]}, "ask_size_19": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 41.97151}, "sample_values": [3.96821, 2.5, 0.0219, 0.0219, 0.0219]}, "ask_price_20": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42457.0, "max": 92701.0}, "sample_values": [42900.0, 43000.0, 43013.0, 43013.0, 43013.0]}, "ask_size_20": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 4e-05, "max": 41.97151}, "sample_values": [0.01611, 0.0219, 38.20416, 38.20416, 38.20416]}, "mid_price": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42291.0, "max": 92672.5}, "sample_values": [42626.0, 42624.0, 42624.5, 42624.5, 42624.5]}, "spread": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 1.0, "max": 28.0}, "sample_values": [2.0, 4.0, 5.0, 5.0, 5.0]}, "spread_relative": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 1.079068763656964e-05, "max": 0.0005672551939303694}, "sample_values": [4.6919720358466666e-05, 9.384384384384384e-05, 0.00011730342877922321, 0.00011730342877922321, 0.00011730342877922321]}, "raw_obi_5": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": -0.9988051228105128, "max": 0.9984076644188096}, "sample_values": [0.09773957852385994, -0.2099230478347948, -0.503471209294931, -0.5233823348954031, -0.5002323418624509]}, "close": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42291.0, "max": 92672.5}, "sample_values": [42626.0, 42624.0, 42624.5, 42624.5, 42624.5]}, "realised_vol_1s": {"dtype": "float64", "always_present": false, "null_percentage": 1.1555555555555557, "value_range": {"min": 0.0, "max": 0.0007139287802346345}, "sample_values": [0.0, 1.1730411678983722e-05, 0.0, 0.0, 4.692247235373111e-05]}, "high": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42291.0, "max": 92672.5}, "sample_values": [42626.0, 42624.0, 42624.5, 42624.5, 42624.5]}, "low": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42291.0, "max": 92672.5}, "sample_values": [42626.0, 42624.0, 42624.5, 42624.5, 42624.5]}, "unrealised_pnl": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": 0.0, "max": 0.0}, "sample_values": [0.0, 0.0, 0.0, 0.0, 0.0]}, "spread_mean": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": 1.0, "max": 5.567796610169491}, "sample_values": [3.0416666666666665, 3.042372881355932, 3.059322033898305, 3.093220338983051, 3.1271186440677967]}, "spread_std": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": 0.0, "max": 4.488300862550137}, "sample_values": [0.8795784172490373, 0.8871114282593681, 0.895798361714784, 0.9306772481740266, 0.963082427179985]}, "best_bid": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42289.0, "max": 92672.0}, "sample_values": [42625.0, 42622.0, 42622.0, 42622.0, 42622.0]}, "best_ask": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 42293.0, "max": 92673.0}, "sample_values": [42627.0, 42626.0, 42627.0, 42627.0, 42627.0]}, "volume": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": 0.0, "max": 0.0}, "sample_values": [0.0, 0.0, 0.0, 0.0, 0.0]}, "raw_obi_20": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": -0.8994281788590209, "max": 0.9222217645872285}, "sample_values": [0.17999272816327594, 0.0, 0.05433076912861516, -0.26533844460236794, -0.26797235892324706]}, "obi_smoothed": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": -0.9440965529526804, "max": 0.8214374145442138}, "sample_values": [-0.11076559977084623, -0.1070526736473202, -0.10675896987082686, -0.11667161426726308, -0.12728661622952261]}, "obi_smoothed_5": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": -0.9440965529526804, "max": 0.8214374145442138}, "sample_values": [-0.11076559977084623, -0.1070526736473202, -0.10675896987082686, -0.11667161426726308, -0.12728661622952261]}, "obi_smoothed_20": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": -0.7481677097144759, "max": 0.586051944668315}, "sample_values": [-0.2647422796768637, -0.25426490146978764, -0.24197649762509113, -0.24036482492468622, -0.23884491540525268]}, "obi_zscore_5": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": -4.663630232921959, "max": 4.618540955934257}, "sample_values": [0.8463535728614189, 0.0, -0.24831841014971945, -1.2891945781890246, -1.355126350570261]}, "obi_zscore_20": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": -3.999509853819434, "max": 5.789962058053172}, "sample_values": [1.7580901388361176, 0.8802647056398024, 1.1653763439643965, -0.4401467968389755, -0.44812362091479807]}, "raw_obi_L1_3": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": -0.9997485303893533, "max": 0.9997239754236648}, "sample_values": [0.45473705139675913, 0.0, 0.9389210967654282, -0.1928840310688472, -0.20733714317593038]}, "raw_obi_L1_10": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": -0.9871262109406257, "max": 0.9707323095195639}, "sample_values": [-0.05835283751568525, 0.0, 0.13532317766480337, 0.06346212433632012, 0.05879201331905339]}, "ma_slope": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": -132.41063218390627, "max": 91.73333333332266}, "sample_values": [-0.5583333333343035, -0.5775862068985589, -0.7672413793115993, -0.9396551724130404, -1.1120689655144815]}, "ma_slope_ema_30s": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": -0.010190523525914853, "max": 0.007522080200727472}, "sample_values": [-0.0004060961835578997, 0.0, -0.0007264308840287739, -0.0005600530687184304, -0.0005239235469126222]}, "atr_14_sec": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": 263.8392857142857, "max": 788.5714285714286}, "sample_values": [343.9642857142857, 343.9642857142857, 343.9642857142857, 343.9642857142857, 343.9642857142857]}, "atr_percent_sec": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.004292617337351204, "max": 0.011668882767892816}, "sample_values": [0.008069354049507009, 0.00806973267910768, 0.00806963801837642, 0.00806963801837642, 0.00806963801837642]}, "atr": {"dtype": "float64", "always_present": true, "null_percentage": 0.0, "value_range": {"min": 263.8392857142857, "max": 788.5714285714286}, "sample_values": [343.9642857142857, 343.9642857142857, 343.9642857142857, 343.9642857142857, 343.9642857142857]}, "atr_percent": {"dtype": "float64", "always_present": false, "null_percentage": 0.6, "value_range": {"min": 0.004292617337351204, "max": 0.011668882767892816}, "sample_values": [0.008069354049507009, 0.00806973267910768, 0.00806963801837642, 0.00806963801837642, 0.00806963801837642]}}, "field_mappings": {"timestamp": {"status": "FOUND", "actual_name": "timestamp"}, "close": {"status": "FOUND", "actual_name": "close"}, "high": {"status": "FOUND", "actual_name": "high"}, "low": {"status": "FOUND", "actual_name": "low"}, "volume": {"status": "FOUND", "actual_name": "volume"}, "volume_imbalance": {"status": "MAPPED", "actual_name": "obi_smoothed", "alternatives": ["obi_smoothed", "raw_obi_5", "raw_obi_20"]}, "spread_mean": {"status": "FOUND", "actual_name": "spread_mean"}, "spread_std": {"status": "FOUND", "actual_name": "spread_std"}, "atr_percent_sec": {"status": "FOUND", "actual_name": "atr_percent_sec"}, "ma_slope_ema_30s": {"status": "FOUND", "actual_name": "ma_slope_ema_30s"}}, "data_quality": {"issues": [], "severity": "GOOD"}, "recommendations": [{"priority": "HIGH", "issue": "Field name mismatches", "mappings": [{"expected": "volume_imbalance", "actual": "obi_smoothed"}], "action": "Create data adapter to transform field names"}]}