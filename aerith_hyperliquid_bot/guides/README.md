# Trading Bot Documentation Guides

This directory contains comprehensive documentation for understanding, debugging, and developing the Aerith Hyperliquid trading bot system.

## Quick Start

If you're new to the project, read these in order:
1. [Architectural Separation Plan](architecture/architectural_separation_plan.md) - Current system state and future direction
2. [Configuration Issues Guide](configuration/configuration_issues_guide.md) - Critical configuration discoveries
3. [Development Workflow Guide](development/development_workflow_guide.md) - How to make changes safely

## Documentation Structure

### 📁 architecture/
System architecture documentation and refactoring plans.

- **[architectural_separation_plan.md](architecture/architectural_separation_plan.md)**
  - Overview of legacy vs modern systems
  - Three-layer architecture design
  - Implementation phases and success criteria
  - Risk mitigation strategies

### 📁 configuration/
Configuration management and common issues.

- **[configuration_issues_guide.md](configuration/configuration_issues_guide.md)**
  - Critical configuration discoveries (25% vs 2% risk)
  - Silent fallback problems and solutions
  - Configuration validation with ❌ emoji logging
  - Best practices and checklists

### 📁 debugging/
Debugging techniques and systematic approaches.

- **[systematic_debugging_guide.md](debugging/systematic_debugging_guide.md)**
  - Backwards analysis method
  - Configuration debugging patterns
  - Trade generation debugging
  - Common mistakes and solutions

### 📁 development/
Development workflows and best practices.

- **[development_workflow_guide.md](development/development_workflow_guide.md)**
  - Core development principles
  - Incremental development workflow
  - Testing strategies
  - Git workflow and emergency procedures

## Key Discoveries

### 1. The 25% Risk Discovery
The working system uses 25% risk per trade, not 2% as assumed. This explains the massive profits and was discovered through systematic log analysis.

### 2. Configuration Fallback Issue
Missing values in override configs fall back to schema defaults, not base config values. This caused silent configuration errors.

### 3. Threshold Confusion
Different detector modes (continuous_gms vs granular_microstructure) use different threshold scales, causing mixing issues in UnifiedGMSDetector.

## Current System State

### Legacy System (WORKING) ✅
- 180 trades, +215% ROI for 2024
- Uses granular_microstructure detector
- Reads from raw2/ hourly files
- **FROZEN - DO NOT MODIFY**

### Modern System (BROKEN) ❌
- 0 trades due to 100% regime gate failures
- Uses continuous_gms detector
- Complex feature integration pipeline
- Needs architectural separation

## Next Steps

1. **Complete Architectural Separation**
   - Extract legacy components to isolated module
   - Create clean modern system implementation
   - Implement proper system routing

2. **Fix Configuration Management**
   - Use ConfigValidator for all runs
   - Ensure ✅ "No configuration fallbacks detected"
   - Document all configuration requirements

3. **Establish Testing Framework**
   - Regression tests for legacy system
   - Integration tests for modern system
   - Performance benchmarks

## Quick Commands

```bash
# Check for configuration issues
grep -E "(❌|⚠️|✅)" logs/latest_backtest.log

# Find current trade count
grep "Total Trades:" logs/latest_backtest.log

# Check regime failures
grep "Regime gate failures:" logs/latest_backtest.log

# Validate configuration
python3 -c "from hyperliquid_bot.core.config_validator import ConfigValidator; print('✅ Validator available')"
```

## Important Files

- `/configs/base.yaml` - Base configuration (2% risk)
- `/configs/overrides/execution_refinement_enabled.yaml` - Override with 25% risk
- `/hyperliquid_bot/core/config_validator.py` - Configuration validation
- `/hyperliquid_bot/core/unified_gms_detector.py` - Problematic unified detector

## Contact & Support

For questions about:
- Configuration issues → See [Configuration Guide](configuration/configuration_issues_guide.md)
- Debugging problems → See [Debugging Guide](debugging/systematic_debugging_guide.md)
- Development process → See [Development Guide](development/development_workflow_guide.md)
- Architecture questions → See [Architecture Plan](architecture/architectural_separation_plan.md)