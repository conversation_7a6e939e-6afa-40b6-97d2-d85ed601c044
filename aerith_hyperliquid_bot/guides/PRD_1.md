# GREEN LIGHT! PRD 1: Data Standardization & Continuous Mode Foundation
---

## PRD 1: Unified Data Pipeline & Continuous Mode Validation

**Version**: 1.0  
**Author**: <PERSON> (Quant Advisor)  
**Date**: 2025-01-09  
**Target Implementation**: <PERSON> Code  
**Priority**: HIGH  
**Risk Level**: LOW (preserves frozen baseline)

### Executive Summary

Establish a standardized data pipeline serving both legacy (frozen) and continuous (experimental) modes, validate parity between modes, then implement selective optimizations for the continuous path. This PRD prioritizes stability and validation before innovation.

### Objectives

1. **Standardize data schema** across both modes to eliminate confusion
2. **Achieve performance parity** between continuous and legacy modes
3. **Optimize data loading** for continuous mode without affecting legacy
4. **Deprecate adaptive thresholds** in continuous mode
5. **Prepare foundation** for multi-timeframe enhancements

---

## Phase 1: Data Schema Standardization (Days 1-2)

### Task 1.1: Define Unified Feature Schema

**File**: `docs/unified_feature_schema.md`

```markdown
# Unified Feature Schema v1.0

## Primary Features (Used by Both Modes)
| Column | Type | Description | Source | Required |
|--------|------|-------------|--------|----------|
| timestamp | datetime64[ns] | UTC-naive timestamp | All | Yes |
| open | float32 | OHLC open price | OHLCV | Yes |
| high | float32 | OHLC high price | OHLCV | Yes |
| low | float32 | OHLC low price | OHLCV | Yes |
| close | float32 | OHLC close price | OHLCV | Yes |
| volume | float32 | Trade volume | OHLCV | No |
| mid_price | float32 | (bid + ask) / 2 | L2 | Yes |
| atr_14_sec | float32 | 14-period ATR | Calculated | Yes |
| atr_percent_sec | float32 | ATR as % of price | Calculated | Yes |
| spread_mean | float32 | Rolling spread mean | Features_1s | Yes |
| spread_std | float32 | Rolling spread std | Features_1s | Yes |
| ma_slope | float32 | Price momentum | Calculated | Yes |
| obi_smoothed_5 | float32 | Order book imbalance | Features_1s | Yes |

## Extended Features (Continuous Mode Only)
| Column | Type | Description | Source | Required |
|--------|------|-------------|--------|----------|
| spread_bps | float32 | Current spread in bps | L2 | No |
| volume_imbalance | float32 | Buy - Sell volume | Trades | No |
| obi_5 | float32 | Raw OBI at depth 5 | L2 | No |
| ma_slope_ema_30s | float32 | EMA momentum | Calculated | No |
```

**Implementation**:
```python
# hyperliquid_bot/data/schemas.py
UNIFIED_SCHEMA = {
    'timestamp': 'datetime64[ns]',
    'open': 'float32',
    'high': 'float32',
    'low': 'float32', 
    'close': 'float32',
    'volume': 'float32',
    'mid_price': 'float32',
    'atr_14_sec': 'float32',
    'atr_percent_sec': 'float32',
    'spread_mean': 'float32',
    'spread_std': 'float32',
    'ma_slope': 'float32',
    'obi_smoothed_5': 'float32'
}

CONTINUOUS_EXTENDED_SCHEMA = {
    'spread_bps': 'float32',
    'volume_imbalance': 'float32',
    'obi_5': 'float32',
    'ma_slope_ema_30s': 'float32'
}
```

### Task 1.2: Create Schema Validator

**File**: `hyperliquid_bot/data/validators.py`

```python
class DataValidator:
    """Ensures data consistency across modes"""
    
    @staticmethod
    def validate_dataframe(df: pd.DataFrame, mode: str = 'legacy') -> Tuple[bool, List[str]]:
        """
        Validates dataframe against unified schema
        Returns: (is_valid, list_of_issues)
        """
        issues = []
        
        # Check required columns
        required_cols = list(UNIFIED_SCHEMA.keys())
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            issues.append(f"Missing columns: {missing_cols}")
        
        # Check data types
        for col, expected_dtype in UNIFIED_SCHEMA.items():
            if col in df.columns:
                actual_dtype = str(df[col].dtype)
                if not self._compatible_dtype(actual_dtype, expected_dtype):
                    issues.append(f"Column {col}: expected {expected_dtype}, got {actual_dtype}")
        
        # Check for excessive NaNs
        for col in required_cols:
            if col in df.columns:
                nan_ratio = df[col].isna().sum() / len(df)
                if nan_ratio > 0.1:  # More than 10% NaN
                    issues.append(f"Column {col}: {nan_ratio:.1%} NaN values")
        
        return len(issues) == 0, issues
```

### Task 1.3: Standardize Data Loaders

**File**: `hyperliquid_bot/data/unified_loader.py`

```python
class UnifiedDataLoader:
    """Single interface for loading data for both modes"""
    
    def __init__(self, config: Config):
        self.config = config
        self.validator = DataValidator()
    
    def load_for_backtest(
        self, 
        start_date: datetime, 
        end_date: datetime,
        mode: str = 'legacy'
    ) -> pd.DataFrame:
        """
        Loads and validates data for backtesting
        """
        if mode == 'legacy':
            df = self._load_legacy_format(start_date, end_date)
        else:  # continuous
            df = self._load_continuous_format(start_date, end_date)
        
        # Validate
        is_valid, issues = self.validator.validate_dataframe(df, mode)
        if not is_valid:
            logger.warning(f"Data validation issues: {issues}")
        
        # Ensure consistent timezone handling
        df = self._standardize_timestamps(df)
        
        return df
    
    def _load_legacy_format(self, start, end) -> pd.DataFrame:
        """Load 1h OHLCV + pre-computed features"""
        # Implementation based on current legacy loader
        pass
    
    def _load_continuous_format(self, start, end) -> pd.DataFrame:
        """Load from features_1s with proper resampling"""
        # Implementation for continuous mode
        pass
```

---

## Phase 2: Parity Validation (Days 3-4)

### Task 2.1: Create Parity Test Framework

**File**: `tests/test_mode_parity.py`

```python
class TestModeParity:
    """Ensures continuous mode matches legacy mode performance"""
    
    def __init__(self):
        self.config = Config()
        self.tolerance = {
            'trades_per_year': 0.1,  # 10% tolerance
            'sharpe_ratio': 0.15,    # 15% tolerance
            'total_return': 0.20     # 20% tolerance
        }
    
    def test_parity(self, test_period: Tuple[datetime, datetime]):
        """
        Run both modes and compare results
        """
        # 1. Run legacy mode
        legacy_results = self._run_legacy_backtest(test_period)
        
        # 2. Configure continuous mode to match legacy
        continuous_config = self._create_matching_config()
        continuous_results = self._run_continuous_backtest(test_period, continuous_config)
        
        # 3. Compare results
        metrics_comparison = self._compare_metrics(legacy_results, continuous_results)
        
        # 4. Generate report
        self._generate_parity_report(metrics_comparison)
        
        return metrics_comparison
    
    def _create_matching_config(self) -> dict:
        """Configure continuous mode to behave like legacy"""
        return {
            'gms': {
                'detector_type': 'continuous_gms',
                'continuous_gms': {
                    'cadence_sec': 3600,  # Match 1-hour updates
                    'output_states': 8,    # Full states like legacy
                    # Use fixed thresholds matching legacy
                    'auto_thresholds': False,
                    'vol_thresh_mode': 'fixed',
                    'vol_low_thresh': 0.02,
                    'vol_high_thresh': 0.06,
                    # ... other legacy-matching params
                }
            }
        }
```

### Task 2.2: Continuous Mode Configuration Matcher

**File**: `hyperliquid_bot/config/parity_configs.py`

```python
def create_continuous_config_matching_legacy(legacy_config: Config) -> Config:
    """
    Creates a continuous mode config that should produce similar results to legacy
    """
    # Copy base config
    continuous_config = deepcopy(legacy_config)
    
    # Override specific settings
    continuous_config.regime.detector_type = 'continuous_gms'
    
    # Match thresholds exactly
    continuous_config.gms = {
        'thresholds': {
            'vol_low_thresh': legacy_config.regime.gms_vol_low_thresh,
            'vol_high_thresh': legacy_config.regime.gms_vol_high_thresh,
            'mom_weak_thresh': legacy_config.regime.gms_mom_weak_thresh,
            'mom_strong_thresh': legacy_config.regime.gms_mom_strong_thresh,
        },
        'operational': {
            'cadence_sec': 3600,  # 1 hour to match legacy
            'output_states': 8,
            'auto_thresholds': False  # CRITICAL: No adaptive!
        }
    }
    
    # Use TF-v3 with similar parameters to TF-v2
    continuous_config.strategies.use_tf_v3 = True
    continuous_config.strategies.use_tf_v2 = False
    
    return continuous_config
```

### Task 2.3: Performance Comparison Report

**File**: `reports/parity_validation_report.py`

```python
def generate_parity_report(legacy_results: dict, continuous_results: dict) -> None:
    """
    Generate detailed comparison report
    """
    report = {
        'summary': {
            'test_period': legacy_results['period'],
            'legacy_mode': {
                'total_trades': legacy_results['n_trades'],
                'sharpe_ratio': legacy_results['sharpe'],
                'total_return': legacy_results['total_return'],
                'max_drawdown': legacy_results['max_dd']
            },
            'continuous_mode': {
                'total_trades': continuous_results['n_trades'],
                'sharpe_ratio': continuous_results['sharpe'],
                'total_return': continuous_results['total_return'],
                'max_drawdown': continuous_results['max_dd']
            },
            'differences': {
                'trades_diff_%': (continuous_results['n_trades'] - legacy_results['n_trades']) / legacy_results['n_trades'] * 100,
                'sharpe_diff_%': (continuous_results['sharpe'] - legacy_results['sharpe']) / legacy_results['sharpe'] * 100,
                'return_diff_%': (continuous_results['total_return'] - legacy_results['total_return']) / legacy_results['total_return'] * 100
            }
        }
    }
    
    # Save report
    with open('reports/parity_validation.json', 'w') as f:
        json.dump(report, f, indent=2)
```

---

## Phase 3: Continuous Mode Optimizations (Days 5-6)

### Task 3.1: Deprecate Adaptive Thresholds

**File**: `hyperliquid_bot/core/unified_gms_detector.py`

```python
# Modification to existing detector
class UnifiedGMSDetector:
    def __init__(self, config: Config):
        # ... existing init code ...
        
        # Add deprecation warning
        if hasattr(config.gms, 'auto_thresholds') and config.gms.auto_thresholds:
            logger.warning(
                "DEPRECATION: auto_thresholds will be removed in v2.0. "
                "Use fixed thresholds or online-only adaptation."
            )
        
        # Override for continuous mode
        if self.mode == 'continuous' and not config.allow_deprecated_features:
            self.adaptive_vol_threshold = None
            self.adaptive_mom_threshold = None
            logger.info("Adaptive thresholds disabled for continuous mode")
```

### Task 3.2: Selective Feature Loader

**File**: `hyperliquid_bot/data/selective_loader.py`

```python
class SelectiveFeatureLoader:
    """Efficiently loads only required columns from features_1s"""
    
    def __init__(self, base_path: str):
        self.base_path = Path(base_path)
        self.cache = {}  # Simple in-memory cache
        
    def load_window(
        self, 
        timestamp: pd.Timestamp, 
        window_seconds: int = 60,
        columns: List[str] = None
    ) -> pd.DataFrame:
        """
        Load specific columns for a time window
        """
        if columns is None:
            columns = ['timestamp', 'mid_price', 'spread_bps', 'obi_5']
        
        # Determine file path
        date_str = timestamp.strftime('%Y-%m-%d')
        hour = timestamp.hour
        file_path = self.base_path / date_str / f'features_{hour:02d}.parquet'
        
        # Check cache
        cache_key = f"{file_path}:{','.join(columns)}"
        if cache_key not in self.cache:
            # Load with column selection
            self.cache[cache_key] = pd.read_parquet(
                file_path, 
                columns=columns
            )
        
        # Filter to window
        df = self.cache[cache_key]
        start = timestamp - pd.Timedelta(seconds=window_seconds)
        mask = (df['timestamp'] >= start) & (df['timestamp'] <= timestamp)
        
        return df[mask].copy()
```

### Task 3.3: 1-Minute OHLCV Generator

**File**: `hyperliquid_bot/data/resampler.py`

```python
class MinuteBarGenerator:
    """Creates 1-minute OHLCV from features_1s mid_price"""
    
    def __init__(self, feature_loader: SelectiveFeatureLoader):
        self.loader = feature_loader
        self.cache = {}
        
    def get_bars(
        self, 
        timestamp: pd.Timestamp, 
        lookback_minutes: int = 20
    ) -> pd.DataFrame:
        """
        Generate 1-minute OHLCV bars
        """
        # Check cache first
        cache_key = f"{timestamp}:{lookback_minutes}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        # Load raw data
        window_seconds = lookback_minutes * 60 + 60  # Extra minute for safety
        df = self.loader.load_window(
            timestamp, 
            window_seconds, 
            columns=['timestamp', 'mid_price']
        )
        
        # Resample to 1-minute
        df_1m = df.set_index('timestamp')['mid_price'].resample('1T').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min', 
            'close': 'last'
        })
        
        # Calculate simple volume proxy (number of ticks)
        df_1m['volume'] = df.set_index('timestamp')['mid_price'].resample('1T').count()
        
        # Cache and return
        self.cache[cache_key] = df_1m
        return df_1m
```

---

## Phase 4: Performance Benchmarking (Day 7)

### Task 4.1: Benchmarking Suite

**File**: `benchmarks/continuous_mode_perf.py`

```python
class ContinuousModeBenchmark:
    """Measures performance of continuous mode operations"""
    
    def run_all_benchmarks(self):
        results = {}
        
        # 1. Feature loading speed
        results['feature_loading'] = self._benchmark_feature_loading()
        
        # 2. GMS detection speed
        results['gms_detection'] = self._benchmark_gms_detection()
        
        # 3. Signal generation speed
        results['signal_generation'] = self._benchmark_signal_generation()
        
        # 4. Memory usage
        results['memory_usage'] = self._measure_memory_usage()
        
        # Generate report
        self._generate_benchmark_report(results)
        
    def _benchmark_feature_loading(self):
        """Target: <10ms for 60s of features"""
        loader = SelectiveFeatureLoader('features_1s')
        
        times = []
        for _ in range(100):
            start = time.time()
            df = loader.load_window(
                pd.Timestamp.now(), 
                window_seconds=60,
                columns=['mid_price', 'spread_bps', 'obi_5']
            )
            times.append((time.time() - start) * 1000)  # ms
            
        return {
            'mean_ms': np.mean(times),
            'p99_ms': np.percentile(times, 99),
            'target_met': np.mean(times) < 10
        }
```

---

## Success Criteria & Validation

### Phase 1 Success (Data Standardization)
- [ ] Unified schema documented and implemented
- [ ] Both modes load data through same interface
- [ ] Validation catches schema mismatches
- [ ] No changes to legacy mode performance

### Phase 2 Success (Parity)
- [ ] Continuous mode matches legacy within tolerances:
  - Trades per year: ±10%
  - Sharpe ratio: ±15%
  - Total return: ±20%
- [ ] Detailed comparison report generated
- [ ] Any significant differences explained

### Phase 3 Success (Optimization)
- [ ] Adaptive thresholds deprecated (no future peeking)
- [ ] Feature loading <10ms for 60s window
- [ ] 1-minute bar generation working correctly
- [ ] Memory usage <1GB for 24h operation

### Phase 4 Success (Performance)
- [ ] All benchmarks passing targets
- [ ] Decision cycle <100ms end-to-end
- [ ] No memory leaks in 24h test

---

## Risk Mitigation

1. **Legacy Mode Protection**: All changes isolated to continuous mode path
2. **Incremental Testing**: Each phase validated before proceeding
3. **Rollback Plan**: Git tags at each phase completion
4. **Performance Monitoring**: Benchmarks run after each change

---

## Implementation Notes for Claude Code

1. **Start with Task 1.1** - Schema definition is pure documentation
2. **Use type hints** everywhere for clarity
3. **Add docstrings** with examples for each function
4. **Test each component** individually before integration
5. **Keep legacy code untouched** - only add new files/functions

---

## Next Steps After PRD 1

Once parity is achieved and optimizations complete:
- PRD 2: Multi-timeframe signal hierarchy
- PRD 3: Adaptive TF-v3 implementation
- PRD 4: Advanced microstructure scoring

---

This PRD provides a solid foundation while respecting your frozen baseline. Ready to feed this to Claude Code?