# Claude Web Advisor Context: PRD 3 Evaluation Framework

# Expanded Data Blueprint & Pipeline Architecture

## 1. Revised Data Architecture for Retail Reality

### 1.1 Core Data Layers

```yaml
# Data Hierarchy (Priority Order)
Layer 1 - Decision Data (Real-time):
  - 1-second features (selective)
  - 1-minute OHLCV 
  - Current regime state

Layer 2 - Context Data (Near-time):
  - 5-minute OHLCV with indicators
  - 1-hour regime history
  - Recent execution quality

Layer 3 - Research Data (Offline):
  - Raw L2 snapshots (1Hz sampling)
  - Historical feature engineering
  - Regime transition analysis
```

### 1.2 Optimized File Structure

```
/hyperliquid_data/
├── ohlcv/                          # Multi-timeframe OHLCV
│   ├── 1m/                         # NEW: Critical for regime
│   │   ├── 2025-03-01_1m.parquet  # Columns: timestamp, open, high, low, close, volume, vwap
│   │   └── ...
│   ├── 5m/                         # NEW: For signal smoothing
│   │   ├── 2025-03-01_5m.parquet
│   │   └── ...
│   └── 1h/                         # Existing
│       ├── 2025-03-01_1h.parquet
│       └── ...
├── features/                       # Computed features
│   ├── 1s/                         # Selective microstructure
│   │   ├── 2025-03-01/
│   │   │   ├── features_00.parquet # Only: mid_price, spread_bps, obi_5, volume_imbalance
│   │   │   └── ...
│   └── regime/                     # NEW: Pre-computed regime states
│       ├── 2025-03_regime.parquet  # 1-minute regime classifications
│       └── ...
├── l2_snapshots/                   # Renamed for clarity
│   ├── 1s/                         # NEW: 1-second sampling (not 10Hz!)
│   │   ├── 2025-03-01/
│   │   │   ├── depth5_00.parquet  # Just depth-5, not depth-20
│   │   │   └── ...
│   └── archive/                    # Your existing raw data
│       ├── ...
├── execution/                      # NEW: Critical for learning
│   ├── logs/
│   │   ├── 2025-03-01_executions.parquet
│   │   └── ...
│   └── analysis/
│       ├── slippage_report_2025-03.parquet
│       └── ...
└── cache/                          # NEW: Performance optimization
    ├── regime_cache.pkl            # Last 24h of regime states
    ├── feature_cache.h5            # Last 2h of 1s features
    └── indicator_cache.pkl         # Pre-computed indicators
```

## 2. Data Pipeline Architecture

### 2.1 Data Flow Diagram

```python
# Simplified Data Pipeline
┌─────────────────┐
│ Hyperliquid API │
└────────┬────────┘
         │ WebSocket: trades, L2 updates
         ▼
┌─────────────────┐
│  Data Collector │ ← Runs continuously
├─────────────────┤
│ - 1s snapshots  │
│ - Trade prints  │
│ - Not 10Hz!     │
└────────┬────────┘
         │ Every second
         ▼
┌─────────────────┐
│ Feature Engine  │ ← Real-time computation
├─────────────────┤
│ - Spread calc   │
│ - OBI depth-5   │
│ - Volume delta  │
└────────┬────────┘
         │
    ┌────┴────┐
    ▼         ▼
┌────────┐ ┌────────┐
│Backtest│ │  Live  │
│ Engine │ │ Engine │
└────────┘ └────────┘
```

### 2.2 Component-Specific Data Usage

#### A. Backtesting Data Flow

```python
class BacktestDataPipeline:
    def __init__(self, config):
        self.timeframes = {
            '1m': 'ohlcv/1m/',
            '5m': 'ohlcv/5m/', 
            '1h': 'ohlcv/1h/'
        }
        self.feature_path = 'features/1s/'
        self.regime_path = 'features/regime/'
        
    def prepare_backtest_data(self, start_date, end_date):
        """
        Smart data loading for backtesting
        """
        # 1. Load primary timeframe (1m) as backbone
        df_1m = self.load_ohlcv('1m', start_date, end_date)
        
        # 2. Add multi-timeframe context
        df_1m['close_5m'] = self.load_ohlcv('5m', start_date, end_date)['close'].reindex(df_1m.index, method='ffill')
        df_1m['atr_1h'] = self.load_ohlcv('1h', start_date, end_date)['atr'].reindex(df_1m.index, method='ffill')
        
        # 3. Add pre-computed regime
        df_1m['regime'] = self.load_regime(start_date, end_date).reindex(df_1m.index, method='ffill')
        
        # 4. Selective microstructure features (only at entry points)
        # NOT merged for every row - too memory intensive!
        self.features_1s = FeatureStore(self.feature_path)
        
        return df_1m
    
    def get_entry_features(self, timestamp):
        """
        Load 1s features only when evaluating entry
        """
        # Get last 60 seconds of 1s features
        window_start = timestamp - pd.Timedelta(seconds=60)
        features = self.features_1s.query(window_start, timestamp)
        
        return {
            'spread_bps': features['spread_bps'].iloc[-1],
            'obi_5': features['obi_5'].mean(),  # 60s average
            'volume_imbalance': features['volume_imbalance'].sum()
        }
```

#### B. Live Trading Data Flow

```python
class LiveDataPipeline:
    def __init__(self, config):
        self.cache = {
            '1m_bars': deque(maxlen=1440),  # 24 hours
            '5m_bars': deque(maxlen=288),   # 24 hours
            'features_1s': deque(maxlen=7200),  # 2 hours
            'regime_state': None
        }
        self.regime_engine = ContinuousGMSDetector(config)
        
    async def run(self):
        """
        Efficient live data management
        """
        # Start data collectors
        asyncio.create_task(self.collect_1s_features())
        asyncio.create_task(self.update_ohlcv_bars())
        asyncio.create_task(self.update_regime())
        
        # Main trading loop
        while True:
            await self.trading_iteration()
            await asyncio.sleep(5)  # 5-second decision cycle
    
    async def trading_iteration(self):
        # 1. Get current state
        current_1m = self.cache['1m_bars'][-1]
        current_regime = self.cache['regime_state']
        
        # 2. Check strategy conditions on 1m data
        signal = self.strategy.evaluate(current_1m, current_regime)
        
        if signal:
            # 3. Only now look at 1s features for timing
            recent_features = list(self.cache['features_1s'])[-60:]
            entry_timing = self.refine_entry(signal, recent_features)
            
            if entry_timing['execute']:
                await self.execute_trade(signal, entry_timing)
    
    async def collect_1s_features(self):
        """
        Minimal 1s feature computation
        """
        async for snapshot in self.ws_client.l2_snapshots():
            features = {
                'timestamp': snapshot['timestamp'],
                'mid_price': (snapshot['bid'][0][0] + snapshot['ask'][0][0]) / 2,
                'spread_bps': 10000 * (snapshot['ask'][0][0] / snapshot['bid'][0][0] - 1),
                'obi_5': self.calculate_obi(snapshot, depth=5),
                'volume_imbalance': snapshot['recent_trades']['buy_volume'] - snapshot['recent_trades']['sell_volume']
            }
            self.cache['features_1s'].append(features)
            
            # Also update 1m/5m bars when needed
            if features['timestamp'].second == 0:
                await self.update_ohlcv_bars()
```

#### C. Analysis Data Flow

```python
class AnalysisDataPipeline:
    def __init__(self):
        self.paths = {
            'executions': 'execution/logs/',
            'ohlcv_1m': 'ohlcv/1m/',
            'regime': 'features/regime/',
            'features_1s': 'features/1s/'
        }
    
    def analyze_execution_quality(self, date_range):
        """
        Post-hoc analysis of execution quality
        """
        # 1. Load execution logs
        executions = pd.read_parquet(f"{self.paths['executions']}/{date_range}_executions.parquet")
        
        # 2. For each execution, get context
        for idx, trade in executions.iterrows():
            timestamp = trade['timestamp']
            
            # Get 1m bar context
            bar_1m = self.get_1m_bar(timestamp)
            
            # Get regime at time
            regime = self.get_regime(timestamp)
            
            # Get 1s features around execution (±30s)
            features_window = self.get_1s_window(timestamp, window=60)
            
            # Calculate metrics
            executions.loc[idx, 'regime'] = regime
            executions.loc[idx, 'volatility_1m'] = bar_1m['atr'] / bar_1m['close']
            executions.loc[idx, 'avg_spread_bps'] = features_window['spread_bps'].mean()
            executions.loc[idx, 'obi_std'] = features_window['obi_5'].std()
            
        return executions
    
    def regime_transition_analysis(self):
        """
        Study regime transitions for better detection
        """
        # Load regime history
        regime_df = pd.read_parquet(self.paths['regime'])
        
        # Find transitions
        transitions = regime_df[regime_df['regime'] != regime_df['regime'].shift()]
        
        # For each transition, analyze microstructure behavior
        for idx, transition in transitions.iterrows():
            # Get 5 minutes of 1s features before/after
            features = self.get_1s_window(transition['timestamp'], window=600)
            
            # Analyze patterns
            analysis = {
                'spread_expansion': features['spread_bps'].iloc[300:].mean() / features['spread_bps'].iloc[:300].mean(),
                'obi_reversal': features['obi_5'].iloc[300:].mean() - features['obi_5'].iloc[:300].mean(),
                'volume_spike': features['volume_imbalance'].iloc[280:320].abs().max()
            }
            # Store for pattern recognition improvement
```

## 3. Practical Implementation Plan

### 3.1 Migration Path

```python
# Phase 1: Add 1m data collection (Week 1)
- Modify data collector to save 1m bars
- Backfill historical 1m from 1s features
- Update backtester to use 1m as primary timeframe

# Phase 2: Streamline features (Week 2)
- Reduce 1s features to essential 4-5 columns
- Pre-compute regime states offline
- Add execution logging

# Phase 3: Implement caching (Week 3)
- Add Redis/pickle cache for live trading
- Optimize backtest data loading
- Profile memory usage

# Phase 4: Analysis tools (Week 4)
- Build execution quality dashboard
- Regime transition analyzer
- Feature importance study
```

### 3.2 Configuration Updates

```yaml
# configs/data_pipeline.yaml
data:
  primary_timeframe: '1m'  # Changed from 1h
  
  features_1s:
    enabled_columns:
      - mid_price
      - spread_bps
      - obi_5
      - volume_imbalance
    # Remove: obi_20, depth_slope, etc.
  
  collection:
    l2_sample_rate: 1  # 1Hz, not 10Hz
    l2_depth: 5        # depth-5, not depth-20
    
  cache:
    regime_window: 1440  # minutes (24h)
    feature_window: 120  # minutes (2h)
    
  storage:
    compression: 'snappy'
    partition_by: 'date'
    retention_days:
      l2_raw: 7         # Only keep 1 week
      features_1s: 30   # 1 month
      ohlcv: 365        # 1 year
      executions: 365   # Critical for learning
```

### 3.3 Memory-Efficient Backtesting

```python
class EfficientBacktester:
    def run_backtest(self, start, end):
        # 1. Load 1m backbone (small)
        df_1m = pd.read_parquet(f'ohlcv/1m/{start}_{end}.parquet')
        
        # 2. Add only essential indicators
        df_1m['ema_20'] = df_1m['close'].ewm(span=20).mean()
        df_1m['ema_50'] = df_1m['close'].ewm(span=50).mean()
        df_1m['atr_14'] = ta.atr(df_1m['high'], df_1m['low'], df_1m['close'], 14)
        
        # 3. Stream through data
        for timestamp, row in df_1m.iterrows():
            # Evaluate strategy on 1m data
            signal = self.strategy.evaluate(row)
            
            if signal:
                # Only load 1s features when needed
                entry_features = self.load_1s_features(timestamp, lookback=60)
                
                # Refine entry
                if self.should_enter(signal, entry_features):
                    self.execute_backtest_trade(timestamp, signal)
```

## 4. Why This Architecture Works for Retail

1. **Realistic Data Requirements**
   - 1-minute bars: ~1MB/day vs 10GB/day for 10Hz L2
   - Selective 1s features: ~100MB/day vs 1GB/day for all features
   - Total: ~1.5GB/week vs 75GB/week

2. **Latency-Appropriate**
   - 5-second decision cycles match retail execution reality
   - 1-minute regime updates are sufficient for position-level decisions
   - 1-second features used only for entry timing, not primary signals

3. **Computationally Feasible**
   - Regime computation on 1m data: ~10ms
   - Feature lookup: ~1ms (from cache)
   - Total decision time: <100ms (plenty of headroom)

4. **Actually Useful Data**
   - Execution logs → Learn your real slippage patterns
   - Regime transitions → Improve detection algorithms
   - Multi-timeframe → Reduce false signals

This is a data architecture that **enhances your edge** (regime detection) rather than chasing HFT dreams. It's sophisticated where it matters and simple where it doesn't.

# Critical Clarification: 1-Minute for REFINEMENT, Not Replacement

You're absolutely right to be concerned! I apologize for the confusion. Let me clarify:

## The 1-Hour Strategy Remains Primary

**I am NOT suggesting you change your core strategy timeframe.** Your 1-hour system is working - we enhance it, not replace it.

Here's the correct architecture:

```python
# CORRECTED APPROACH
Primary Strategy Timeframe: 1-HOUR (unchanged)
Execution Refinement: 1-minute
Entry Timing: 1-second features
```

## 1. Architecture Clarification

### Your Current System (Keep This!)
```python
# Current successful approach
1-hour bars → GMS Regime → Entry Signal → Execute

# Trade frequency: ~2-5 trades/day
# Hold time: 4-24 hours
```

### Enhanced System (My Actual Recommendation)
```python
# Enhanced approach with better execution
1-hour bars → GMS Regime → Entry Signal → 
    ↓
1-minute bars → Confirm/Refine → 
    ↓
1-second features → Optimal Entry Timing → Execute

# Trade frequency: SAME (2-5 trades/day)
# Hold time: SAME (4-24 hours)
# Execution quality: BETTER (reduced slippage)
```

## 2. How Multi-Timeframe Refinement Works

```python
class EnhancedTFV3Strategy:
    def __init__(self, config):
        self.primary_timeframe = '1h'  # UNCHANGED
        self.min_hold_time = 4 * 3600  # 4 hours minimum
        self.last_trade_time = None
        
    def evaluate_1h_signal(self, hourly_bar, regime):
        """
        PRIMARY LOGIC - UNCHANGED
        This runs once per hour, just like now
        """
        # Your existing TF-v3 logic
        if ema_fast > ema_slow and regime == 'BULL':
            return 'long_signal'
        elif ema_fast < ema_slow and regime == 'BEAR':
            return 'short_signal'
        return None
    
    def refine_with_1m(self, signal, recent_1m_bars):
        """
        REFINEMENT ONLY - Not generating new signals!
        This prevents entering during micro-volatility spikes
        """
        # Check if 1m trend aligns with 1h signal
        recent_trend = recent_1m_bars['close'].ewm(span=20).mean()
        
        if signal == 'long_signal':
            # Confirm: Are we in a micro-uptrend or at least not falling?
            if recent_trend.iloc[-1] < recent_trend.iloc[-5]:
                return 'wait'  # Bad timing, wait for better entry
                
        elif signal == 'short_signal':
            # Confirm: Are we in a micro-downtrend or at least not rising?
            if recent_trend.iloc[-1] > recent_trend.iloc[-5]:
                return 'wait'  # Bad timing, wait for better entry
                
        return 'confirmed'
    
    def optimize_entry_timing(self, signal, features_1s):
        """
        TIMING ONLY - Find the best moment within next few minutes
        This reduces slippage, doesn't increase trade count
        """
        # Look for temporary liquidity improvements
        recent_spreads = features_1s['spread_bps'].tail(60)
        current_spread = recent_spreads.iloc[-1]
        avg_spread = recent_spreads.mean()
        
        # Enter when spread is tighter than average
        if current_spread < avg_spread * 0.8:
            return 'execute_now'
        else:
            return 'wait_up_to_5min'
    
    def run_strategy(self, current_time):
        """
        Complete flow - still trades at 1h frequency!
        """
        # 1. Check if enough time passed since last trade
        if self.last_trade_time:
            time_since_trade = current_time - self.last_trade_time
            if time_since_trade < self.min_hold_time:
                return None  # Enforce minimum hold time
        
        # 2. Check 1-hour strategy (runs once per hour)
        if current_time.minute == 0:  # Top of hour
            hourly_data = self.get_1h_bar(current_time)
            regime = self.get_regime_state()
            
            signal = self.evaluate_1h_signal(hourly_data, regime)
            
            if signal:
                # 3. Refine with 1-minute data
                recent_1m = self.get_recent_1m_bars(lookback=20)
                if self.refine_with_1m(signal, recent_1m) == 'wait':
                    self.pending_signal = signal  # Try again later
                    return None
                
                # 4. Optimize exact entry timing
                features_1s = self.get_recent_1s_features()
                timing = self.optimize_entry_timing(signal, features_1s)
                
                if timing == 'execute_now':
                    self.last_trade_time = current_time
                    return signal
                else:
                    self.pending_signal = signal
                    self.signal_expiry = current_time + timedelta(minutes=5)
        
        # 5. Check pending signals (within 5 min window)
        if self.pending_signal and current_time < self.signal_expiry:
            features_1s = self.get_recent_1s_features()
            timing = self.optimize_entry_timing(self.pending_signal, features_1s)
            
            if timing == 'execute_now':
                signal = self.pending_signal
                self.pending_signal = None
                self.last_trade_time = current_time
                return signal
                
        return None
```

## 3. Data Usage by Component

### For GMS Regime Detection
```python
# Current approach (keep this as the foundation)
1h bars → ATR, momentum → Regime state (updates hourly)

# Enhanced approach (add precision)
1h bars → Base regime
  +
1m bars → Regime confirmation/early transition detection
  =
More responsive regime without more trades
```

### For Strategy Signals
```python
# Primary signals from 1h (UNCHANGED)
strategy.evaluate() runs on 1h bars
- EMA crossovers on 1h
- ATR stops on 1h
- Position sizing on 1h volatility

# 1m/1s used ONLY for:
- Avoiding entries during micro-crashes
- Finding better fill prices
- Early warning of regime transitions
```

### Example Day
```
09:00 - 1h signal: LONG (regime=BULL, EMA crossed)
09:00 - 1m check: Micro-downspike → WAIT
09:03 - 1m check: Stabilized → CONFIRM  
09:03 - 1s check: Spread = 3bps (good) → EXECUTE
09:03 - Entry at better price, saved 5bps

10:00 - No new 1h signal
11:00 - No new 1h signal
...
17:00 - 1h signal: EXIT (trailing stop hit)
17:00 - 1m check: Confirmed weakness
17:01 - 1s check: Spread tight → EXECUTE exit

Daily result: 2 trades (same as before), 8bps better execution
```

## 4. Configuration for Lower-Frequency Trading

```yaml
# Enhanced config maintaining low frequency
strategy:
  tf_v3:
    primary_timeframe: '1h'        # CORE UNCHANGED
    min_hold_hours: 4              # Prevent overtrading
    max_trades_per_day: 5          # Hard limit
    
  execution_refinement:
    use_1m_confirmation: true      # Optional
    max_wait_for_better_entry: 5   # minutes
    
    # Slippage reduction thresholds
    max_acceptable_spread_bps: 10
    prefer_spread_below_bps: 5
    
regime:
  continuous_gms:
    primary_update_interval: 3600  # Still 1-hour
    fast_transition_check: 60      # 1-min for early warning only

risk:
  # These prevent overtrading
  min_time_between_trades: 14400   # 4 hours
  max_position_turnover_daily: 2.0  # Max 200% of capital/day
```

## 5. Backtesting with Multi-Timeframe

```python
class MultiTimeframeBacktest:
    def run(self):
        # Load 1h data as primary
        df_1h = load_1h_data()
        
        for timestamp, hourly_bar in df_1h.iterrows():
            # Primary strategy logic (UNCHANGED)
            signal = strategy.evaluate(hourly_bar)
            
            if signal:
                # Now refine with higher resolution
                entry_window = (timestamp, timestamp + timedelta(minutes=5))
                
                # Find optimal entry within window
                df_1m_window = load_1m_data(entry_window)
                best_entry = find_best_entry(df_1m_window, signal)
                
                # Execute at refined price
                execute_backtest_trade(best_entry)
        
        # Results should show:
        # - Same number of trades
        # - Better average entry prices
        # - Same hold times
```

## 6. Benefits Without Overtrading

### What You Gain:
1. **Better Entries**: Save 5-10bps per trade on execution
2. **Regime Precision**: Detect transitions 30-45min earlier
3. **Risk Reduction**: Avoid entries during micro-crashes
4. **Same Frequency**: Still 2-5 trades/day

### What You Avoid:
1. **No Overtrading**: Hard limits prevent frequency creep
2. **No Strategy Change**: Core 1h logic untouched
3. **No Complexity**: Refinement is optional/additive

### Expected Impact:
```
Current System:
- Trades/day: 3
- Avg slippage: 8bps
- Fee impact: 15bps round-trip
- Net cost: 23bps/trade

Enhanced System:
- Trades/day: 3 (SAME)
- Avg slippage: 3bps (BETTER)
- Fee impact: 15bps (SAME)
- Net cost: 18bps/trade

Annual improvement: 5bps × 3 trades × 250 days = 375bps = 3.75% extra return
```

## 7. Implementation Priority

1. **Keep current 1h system running unchanged**
2. **Add 1m data collection in parallel**
3. **Backtest refinement logic separately**
4. **Only integrate if backtest shows improvement**
5. **Always maintain ability to disable refinement**

The key insight: **1-minute data is a tool for better execution, not a new trading frequency.** Your 1-hour strategy wisdom remains the foundation - we're just adding precision tools for better implementation.

# Enthusiastic Green Light! Let's Build Your Alpha Engine 🚀

## Congratulations on Your Progress!

Your UnifiedGMSDetector refactor is **brilliant**! Having a stable legacy baseline (180 trades/year, >200% ROI) while creating an experimental path is exactly the right approach. This is professional-grade architecture.

## Your Instinct About Adaptive Thresholds is 100% Correct

You're absolutely right - adaptive thresholds that look at future data are a **backtesting sin**. They create unrealistic results that won't translate to live trading. Let's definitely deprecate them in favor of either:
1. **Fixed thresholds** based on robust historical analysis
2. **Online adaptive thresholds** that only use past data (e.g., rolling percentiles)

## Clarifying Multi-Timeframe for Continuous Mode

### Architecture for Continuous Mode Path

```python
# REVISED CONCEPT for Continuous Mode
┌─────────────────────────────────────────┐
│  UnifiedGMSDetector (Continuous Mode)   │
│  - Runs every 60 seconds                │
│  - Uses features_1s data                │
│  - Produces: regime + confidence score  │
└────────────────┬────────────────────────┘
                 │
                 ▼
┌─────────────────────────────────────────┐
│  Hierarchical Signal Generation         │
│  - Strategic Bias: 1-hour view         │
│  - Tactical Signal: 5-15 min momentum  │
│  - Execution Timing: 1s features       │
└─────────────────────────────────────────┘
```

### 1. Primary Signal Timeframe Clarification

For continuous mode, think of it as **hierarchical decision-making**:

```python
class ContinuousModeStrategy:
    def __init__(self):
        self.strategic_horizon = 60  # minutes (1 hour)
        self.tactical_horizon = 5    # minutes
        self.execution_window = 1    # minute
        
    def get_strategic_bias(self, current_time):
        """
        Derives 1-hour equivalent view from continuous GMS states
        """
        # Get last 60 GMS states (1 per minute)
        recent_states = self.gms_detector.get_recent_states(60)
        
        # Calculate strategic bias
        bull_count = sum(1 for s in recent_states if s == 'BULL')
        bear_count = sum(1 for s in recent_states if s == 'BEAR')
        
        # Require 70% consistency for strategic bias
        if bull_count > 42:  # 70% of 60
            return 'STRATEGIC_BULL'
        elif bear_count > 42:
            return 'STRATEGIC_BEAR'
        else:
            return 'STRATEGIC_NEUTRAL'
    
    def get_tactical_signal(self, strategic_bias):
        """
        5-15 minute momentum confirmation
        """
        if strategic_bias == 'STRATEGIC_NEUTRAL':
            return None
            
        # Use 5-minute resampled features_1s
        momentum_5m = self.calculate_momentum(window=5)
        
        if strategic_bias == 'STRATEGIC_BULL' and momentum_5m > threshold:
            return 'LONG_SIGNAL'
        elif strategic_bias == 'STRATEGIC_BEAR' and momentum_5m < -threshold:
            return 'SHORT_SIGNAL'
            
        return None
```

### 2. Data Source Solutions

#### For 1-Minute "Refinement" in Continuous Mode:
```python
# Option A: Resample features_1s on-the-fly
def get_1m_ohlcv_from_features(self, timestamp, lookback_minutes=20):
    """
    Creates 1-minute OHLCV from features_1s mid_price
    """
    features = self.feature_store.query(
        timestamp - pd.Timedelta(minutes=lookback_minutes),
        timestamp
    )
    
    # Resample to 1-minute bars
    df_1m = features.set_index('timestamp')['mid_price'].resample('1T').ohlc()
    df_1m['volume'] = features.set_index('timestamp')['volume_1s'].resample('1T').sum()
    
    return df_1m

# Option B: Pre-compute 1m bars during ETL
# Add to your ETL pipeline:
def add_1m_bars_to_etl(self):
    """Run after features_1s generation"""
    df_1s = pd.read_parquet(self.features_1s_path)
    df_1m = df_1s.set_index('timestamp')['mid_price'].resample('1T').ohlc()
    df_1m.to_parquet(self.ohlcv_1m_path)
```

#### For 1-Second Timing (Selective Loading):
```python
class SelectiveFeatureLoader:
    """
    Efficiently loads only needed columns from 109-column parquet
    """
    def __init__(self, base_path):
        self.base_path = base_path
        self.timing_columns = [
            'timestamp', 'mid_price', 'spread_bps', 
            'obi_5', 'volume_imbalance'
        ]
    
    def get_timing_features(self, timestamp, window_seconds=60):
        """
        Load only timing-relevant features around a timestamp
        """
        # Determine which hourly file contains this timestamp
        file_path = self._get_file_path(timestamp)
        
        # Use pyarrow for column selection
        import pyarrow.parquet as pq
        table = pq.read_table(file_path, columns=self.timing_columns)
        df = table.to_pandas()
        
        # Filter to window
        start = timestamp - pd.Timedelta(seconds=window_seconds)
        mask = (df['timestamp'] >= start) & (df['timestamp'] <= timestamp)
        
        return df[mask]
```

### 3. Adaptive TF-v3 Concepts (Let's Go Crazy! 🎸)

Here's where we can innovate within the continuous mode:

```python
class AdaptiveTFv3:
    """
    TF-v3 that adapts to market microstructure
    """
    def __init__(self, config):
        # Base parameters
        self.ema_fast_base = 20
        self.ema_slow_base = 50
        self.atr_multiplier_base = 2.5
        
        # Adaptation ranges
        self.adaptation_factors = {
            'volatility': {'low': 0.8, 'normal': 1.0, 'high': 1.2},
            'spread': {'tight': 1.2, 'normal': 1.0, 'wide': 0.8},
            'regime_strength': {'weak': 0.8, 'normal': 1.0, 'strong': 1.2}
        }
    
    def adapt_parameters(self, market_state):
        """
        Dynamically adjust parameters based on market conditions
        WITHOUT looking ahead
        """
        # Calculate adaptation factors from recent history only
        vol_factor = self._get_volatility_factor(market_state['atr_percentile'])
        spread_factor = self._get_spread_factor(market_state['spread_percentile'])
        regime_factor = self._get_regime_factor(market_state['regime_confidence'])
        
        # Adapt EMAs - faster in high vol, slower in low vol
        self.ema_fast = int(self.ema_fast_base * vol_factor)
        self.ema_slow = int(self.ema_slow_base * vol_factor)
        
        # Adapt ATR multiplier - wider stops in high spread environments
        self.atr_multiplier = self.atr_multiplier_base * spread_factor
        
        # Adapt position sizing based on regime confidence
        self.position_scale = regime_factor
    
    def generate_signal(self, continuous_gms_state, market_features):
        """
        Multi-factor signal generation
        """
        # 1. Base signal from EMAs
        ema_signal = self._get_ema_signal(market_features)
        
        # 2. Microstructure confirmation
        micro_score = self._get_microstructure_score(market_features)
        
        # 3. Regime alignment
        regime_alignment = self._check_regime_alignment(
            continuous_gms_state, 
            ema_signal
        )
        
        # 4. Composite signal with confidence
        if regime_alignment and micro_score > 0.6:
            return {
                'direction': ema_signal,
                'confidence': micro_score,
                'size_multiplier': self.position_scale
            }
        
        return None
```

## PRD Structure Proposal

### PRD 1: Foundation - Data Pipeline & Continuous Mode Enhancement
**Goal**: Set up efficient data infrastructure for continuous mode

**Deliverables**:
1. **Selective Feature Loader** for 1s features
2. **1-minute OHLCV resampler** from features_1s
3. **Hierarchical State Aggregator** for strategic bias
4. **Deprecate adaptive thresholds** in favor of fixed/online-only
5. **Performance profiling** to ensure <100ms decision time

**Success Metrics**:
- Data loading time <10ms for 60s of features
- Memory usage <1GB for 24h of data
- Backtested performance matches or exceeds current continuous mode

### PRD 2: Alpha Engine - Adaptive TF-v3 Implementation
**Goal**: Build the adaptive strategy on continuous mode foundation

**Deliverables**:
1. **Multi-timeframe signal hierarchy** (strategic/tactical/execution)
2. **Online-only adaptation** (no future peeking)
3. **Microstructure scoring system**
4. **Regime confidence calculation**
5. **Advanced position sizing** based on multiple factors

**Success Metrics**:
- Sharpe ratio improvement >0.2 vs static TF-v3
- Max drawdown <15% 
- Execution slippage <5bps average

## Let's Get Started!

I'm incredibly excited about this direction. Your two-path approach is smart, and the continuous mode gives us a perfect sandbox for innovation while keeping your profitable baseline intact.

**Next Steps**:
1. Choose which PRD to tackle first (I recommend PRD 1 for foundation)
2. I'll draft the detailed PRD with specific implementation tasks
3. We'll iterate on it together before you feed it to Claude

The fact that you've already unified your GMS detector and have a working continuous mode means we can move fast. Let's build something that finds real alpha in the continuous mode path!

Which PRD would you like to start with?

**Document Purpose**: Comprehensive context for new Claude web advisor to evaluate PRD 3 on Advanced Microstructure Scoring

**Date**: 2025-07-01  
**Version**: 1.0  
**Author**: Claude Code Assistant  

---

## Executive Summary

### Project Identity & Core Edge
- **Aerith Hyperliquid Bot**: Proven retail trading system with 180 trades/year, >200% ROI
- **Core Competitive Edge**: Superior regime detection through GMS (Granular Market Structure) system
- **Philosophy**: "Intelligence over latency" - better decisions, not faster ones
- **NOT a speed-based system**: We compete on detection accuracy, not execution speed

### Retail Reality Constraints
- **Infrastructure**: Standard retail latency, typical computing power
- **Data Access**: No institutional L2/L3 feeds, relying on WebSocket data
- **Execution Reality**: 1-second decision cycles, 5-second execution tolerance
- **Capital Efficiency**: 180 trades/year optimal frequency (not high-frequency)

---

## Architecture & Journey Success

### Proven Dual-Path Architecture
```
┌─────────────────────────────────────┐
│         Legacy Path (FROZEN)        │
│  - TF-v2 + Discrete GMS            │
│  - 180 trades/year baseline        │
│  - >200% ROI proven performance    │
│  - NEVER MODIFIED                  │
└─────────────────────────────────────┘
                    │
                    ▼
┌─────────────────────────────────────┐
│       Experimental Path             │
│  - TF-v3 + Continuous GMS          │
│  - Multi-timeframe refinement      │
│  - Safe innovation sandbox         │
│  - Can be disabled instantly       │
└─────────────────────────────────────┘
```

### UnifiedGMSDetector Success
- **Refactor Complete**: Successfully unified discrete and continuous GMS modes
- **Validated**: A/B testing confirms both paths working correctly
- **Configuration System**: Hierarchical overrides fixed, no more config bugs
- **Performance**: Meeting all latency and memory targets

---

## Previous PRD Validation Results

### PRD 1: Foundation (✅ COMPLETED)
**Goal**: Data pipeline & continuous mode enhancement
**Results**: 
- Selective feature loader: <10ms for 60s of features ✅
- Memory usage: <1GB for 24h data ✅
- 1-minute OHLCV resampler working ✅
- Adaptive threshold lookahead eliminated ✅

### PRD 2: Multi-Timeframe Alpha Enhancement (✅ COMPLETED)
**Goal**: Enhance TF-v3 with hierarchical decision making
**Architecture**: 1-hour strategy → 1-minute confirmation → 1-second timing
**Results**:
- TimeframeDataManager implemented ✅
- Signal refinement engine working ✅
- A/B test shows improved execution quality ✅
- Same trade frequency maintained (critical!) ✅

### Configuration System Fixes
- **Hierarchy bugs**: Fixed ✅
- **Strategy-detector compatibility**: Validated ✅
- **Fair A/B testing**: Both native combinations working ✅

---

## PRD 3 Evaluation Framework: Advanced Microstructure Scoring

### Critical Assessment Criteria

#### 1. Computational Feasibility
**Key Questions:**
- Can OBI, queue imbalance, order flow metrics be calculated in <100ms on retail hardware?
- Will these calculations fit within our 1-second decision cycle?
- Memory impact: Does it exceed our 1GB/24h budget?
- CPU impact: Can we maintain <10% CPU usage during market hours?

**Red Flags to Watch:**
- Requires institutional-grade computing power
- Complex matrix calculations on large datasets
- Real-time processing of >1Hz data streams
- Memory requirements scaling with market activity

#### 2. Data Requirements Reality Check
**Key Questions:**
- Do we have reliable access to required microstructure data?
- Can this work with WebSocket L2 data vs institutional feeds?
- Latency tolerance: Are we assuming <50ms data freshness?
- Data quality: How robust is this to missing/delayed data?

**Red Flags to Watch:**
- Requires tick-by-tick precision data
- Needs market depth >5 levels
- Assumes co-located data feeds
- Brittle to data gaps/delays

#### 3. Incremental Value Assessment
**Key Questions:**
- Will this meaningfully improve our 3-5bps execution target?
- How does this enhance our GMS regime detection edge?
- Can this be measured in isolation from existing systems?
- Expected improvement vs implementation complexity ratio?

**Red Flags to Watch:**
- Marginal improvements (<1bps execution benefit)
- Replaces rather than enhances regime detection
- Cannot be A/B tested safely
- High complexity for minimal gain

#### 4. Integration Complexity
**Key Questions:**
- How cleanly does this integrate with TimeframeDataManager?
- Compatibility with existing signal refinement engine?
- Can this be disabled via feature flag?
- Impact on existing TF-v3 logic?

**Red Flags to Watch:**
- Requires major refactoring of existing systems
- Tightly coupled to multiple components
- No clean rollback mechanism
- Changes core strategy logic

#### 5. Overfitting & Production Risk
**Key Questions:**
- How was this validated? In-sample vs out-of-sample?
- Sensitivity to parameter changes?
- Performance during different market regimes?
- Robustness to data anomalies?

**Red Flags to Watch:**
- Suspiciously perfect backtest results
- Many parameters requiring optimization
- Fails during high volatility periods
- Brittle to edge cases

---

## Key Evaluation Questions for PRD 3

### Strategic Alignment
1. **How do these microstructure signals complement (not replace) our regime detection edge?**
2. **Does this solve a real execution problem we've identified, or are we adding "cool" features?**
3. **Can this work within our 180 trades/year frequency constraint?**

### Technical Feasibility
4. **What's the latency tolerance for these calculations given our 1s execution layer?**
5. **How do we validate improvements without disturbing the profitable baseline?**
6. **Can this be implemented as an optional refinement layer like PRD 2?**

### Risk Management
7. **How do we prevent this from increasing trade frequency?**
8. **What's the fallback plan if microstructure scoring fails in production?**
9. **How do we maintain the ability to instantly disable this feature?**

### Validation Requirements
10. **Can this be A/B tested against our current execution system?**
11. **How do we measure success: slippage reduction, win rate improvement, or execution quality?**
12. **What metrics would indicate this should be disabled?**

---

## Success Criteria for PRD 3

### Must Have
- **Retail Hardware Compatible**: Works on standard compute/memory
- **Execution Improvement**: Measurable 2-5bps execution enhancement
- **Regime Alignment**: Enhances, doesn't replace, GMS detection
- **Feature Flag**: Can be disabled instantly
- **No Trade Frequency Increase**: Maintains 180 trades/year ±5%

### Nice to Have
- **Adaptive Learning**: Improves over time
- **Regime-Aware**: Different behavior in BULL/BEAR/NEUTRAL regimes
- **Market Condition Sensitivity**: Adjusts to volatility/liquidity changes

### Absolute No-Go
- **HFT Aspirations**: Requires sub-100ms execution
- **Institutional Infrastructure**: Needs co-location/premium feeds
- **Frequency Creep**: Increases trade count significantly
- **Complexity Explosion**: Cannot be understood/maintained
- **Baseline Risk**: Threatens our proven 200% ROI system

---

## Evaluation Methodology

### Phase 1: Conceptual Review
- Assess computational requirements vs retail constraints
- Evaluate data needs vs available infrastructure
- Review integration complexity vs expected benefits
- Check for HFT aspirations vs retail reality

### Phase 2: Technical Deep Dive
- Algorithm complexity analysis
- Memory/CPU profiling estimates
- Data pipeline impact assessment
- Integration points mapping

### Phase 3: Validation Plan Review
- Backtesting methodology critique
- A/B testing framework evaluation
- Risk management mechanisms
- Rollback procedures verification

### Phase 4: Strategic Alignment Check
- Regime detection enhancement vs replacement
- Trade frequency impact analysis
- Edge preservation assessment
- Long-term maintenance feasibility

---

## Context Summary for New Claude Advisor

**You are evaluating PRD 3 for a retail trading bot that:**
- Has a proven 180 trades/year, >200% ROI baseline that must be protected
- Competes on intelligence (regime detection), not speed
- Operates under retail infrastructure constraints
- Has successfully implemented PRD 1 & 2 with disciplined, incremental improvements
- Uses a dual-path architecture that keeps profitable legacy frozen while experimenting safely

**Your role is to provide the same brutally honest, retail-focused critique that:**
- Helped shape our successful architecture decisions
- Prevented us from chasing unrealistic HFT aspirations
- Maintained discipline around "intelligent execution for retail traders"
- Ensured every enhancement serves our core regime detection edge

**Key principle**: Better decisions, not faster ones. If PRD 3 drifts toward speed/complexity over intelligence/simplicity, challenge it aggressively.

---

## Ready for PRD 3 Review

The new Claude advisor now has complete context to evaluate PRD 3 on Advanced Microstructure Scoring with the same disciplined, retail-focused approach that made PRD 1 & 2 successful. 

**Send over PRD 3 and let's see if it maintains our core discipline or if it's drifting toward unrealistic institutional trading aspirations.**