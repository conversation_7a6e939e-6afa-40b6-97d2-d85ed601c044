# [IMPLEMENTATION GUIDE] Replicating Z-Score Mean-Reversion Strategy & Analytics Framework for Hyperliquid TF Bot

> **FOR AI AGENTS / CODE-GEN MODELS**
>
> This is NOT my current strategy. Use this as a *template* to:
> 1. Engineer a Z-score mean-reversion module compatible with my Hyperliquid TF environment.
> 2. Build a robust analytics system to evaluate ANY trading strategy with deep statistical rigor.
> 3. All code should be modular, parameterized, and easily extensible for future research.

---

## 1. Mean-Reversion Strategy Module

### a. Feature Calculations

- **Inputs:** OHLCV candles (1h BTC as baseline, but must support configurable assets/timeframes)
- **Rolling Window Calculations:**  
  - Rolling Mean: `window_mean = rolling_mean(close, N)`  
  - Rolling Stddev: `window_std = rolling_stddev(close, N)`
  - **Z-Score:**  
    `zscore = (close - window_mean) / window_std`

- **All functions must be vectorized and support batch operations for backtesting and live trading.**

### b. Trading Logic (Parameterize All Thresholds)

- **Entry Condition:**  
  - `if zscore > ENTRY_THRESHOLD: enter_long()`
- **Exit Condition:**  
  - `if zscore <= EXIT_THRESHOLD: exit_long()`
- **Risk Management:**  
  - `stop_loss_pct`, `trailing_stop_loss_pct`  
  - *Model these as dynamic strategy parameters.*

### c. Strategy Config Template

```python
STRATEGY_CONFIG = {
    "lookback": 120,
    "entry_threshold": 0.0,
    "exit_threshold": -1.5,
    "stop_loss_pct": 0.05,
    "trailing_stop_pct": 0.01
}


⸻

2. Robust Analytics Module

a. Metrics to Compute (some of these are already present in compute_metrics portfolio.py pipeline)
	•	Sharpe Ratio
	•	Sortino Ratio
	•	Calmar Ratio
	•	Profit Factor
	•	Win Rate
	•	Expectancy
	•	Max Drawdown & Drawdown Duration
	•	Ulcer Index
	•	Rolling Metrics (windowed Sharpe, rolling returns, etc.)
	•	PnL Distribution (histogram, mean/median, tail analysis)
	•	Equity Curve Visualization

All metrics should be callable as functions and support export (CSV/JSON/Plots) for reporting and AI review.

b. Trade Diagnostics
	•	Holding Time Distribution
	•	Monthly/Regime Return Breakdown
	•	Trade Outcome Clustering (outlier detection: are a few trades responsible for all profits?)

⸻

3. Backtesting & Validation Protocol
	•	Simulate All Frictions: Model realistic fills, slippage, fees, latency, and partial fills.
	•	Walk-Forward Analysis:
	•	Code should allow for rolling out-of-sample validation (not just static in-sample).
	•	Parameter Search/Robustness Grid:
	•	All thresholds and lookbacks should be grid-searchable, results stored for later ML analysis.

⸻

4. Best Practice Prompts for LLM Agents
	•	When extending strategy logic, require all new factors/indicators be switchable in config and logged in analytics.
	•	For any strategy, always plot and log distribution of returns/trades, not just mean/Sharpe.
	•	Before reporting “good results,” check for overfitting by examining trade outlier dependence and regime variance.

⸻

5. To-Do for Implementation Agents
	•	Build modular Z-score mean-reversion strategy class (with parameters).
	•	Integrate rolling window analytics and metrics computation.
	•	Add full-featured backtester (must model slippage, fees, etc).
	•	Create dashboard/reporting pipeline for live and historical metrics.
	•	Parameterize everything for future AI/ML-driven optimization.

⸻

END OF GUIDE
This template is for LLM/AIAgent-driven implementation, not a one-size-fits-all trading solution. Prioritize code quality, testability, and extensibility at every step.

---