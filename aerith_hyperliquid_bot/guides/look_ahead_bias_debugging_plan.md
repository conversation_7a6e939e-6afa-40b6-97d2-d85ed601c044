# Look-Ahead Bias Debugging Plan - Systematic Backwards Approach

## Executive Summary

After implementing look-ahead bias fixes, TF-v3 generates 0 trades instead of expected ~10% reduction from a working baseline. The regime detector works correctly and detects diverse regimes - this was verified earlier today. The issue is likely:

1. **Simple configuration/flag issue** (most likely)
2. **Subtle bug in bias prevention code**
3. **Legacy system conflicts**
4. **Architectural incompatibility** (least likely)

## Current State Analysis

### What We Know Works
- **Regime Detection**: continuous_gms correctly detects diverse regimes (verified today)
- **Signal Provision**: All signals including ohlcv_history are properly provided
- **Strategy Initialization**: TF-v3 initializes correctly with 14-candle warm-up
- **Backtester Flow**: Strategy evaluation is called (1 time in 2024)

### What's Broken
- **Trade Generation**: 0 trades instead of expected ~190 with ~10% reduction
- **Strategy Activation**: Only 1 evaluation in entire 2024 year
- **Expected vs Actual**: Should see meaningful trading activity with bias prevention

## Systematic Backwards Debugging Plan

### Phase 1: Revert to Working Baseline (Tomorrow Morning)

**Goal**: Restore the exact working system that produced 189 trades with 235% ROI

**Steps**:
1. **Document Current State**: 
   - Save all current bias prevention changes
   - Create diff of all modifications made today
   - Backup current tf_v3.py completely

2. **Revert Look-Ahead Bias Fixes**:
   - Restore original tf_v3.py that produced 208% ROI
   - Revert backtester signal checking changes
   - Restore any EMA fabrication logic that was removed
   - Remove warm-up period enforcement

3. **Verify Restoration**:
   - Run backtest and confirm ~189 trades, ~235% ROI
   - Document EXACT configuration used
   - Confirm regime activation pattern

### Phase 2: Document Working Configuration

**Create Complete Baseline Documentation**:
- Exact YAML configuration files used
- Complete tf_v3.py code that works
- Backtester configuration
- Signal provision logic
- Any environment variables or flags
- Data paths and versions used

### Phase 3: Incremental Bias Prevention (One Fix at a Time)

**Implement fixes individually and test after each**:

1. **Fix 1: Indicator Shift=1 Only**
   - Change EMA calculations to use shift=1
   - Test: Should see minimal trade reduction
   
2. **Fix 2: Remove EMA Fabrication**
   - Remove fallback logic for missing EMAs
   - Test: May see moderate trade reduction
   
3. **Fix 3: Add Warm-up Period**
   - Implement 14-candle warm-up requirement
   - Test: Should see ~10% trade reduction
   
4. **Fix 4: OHLCV History Requirement**
   - Make ohlcv_history mandatory for calculations
   - Test: Minimal additional impact expected

## Potential Simple Issues to Investigate

### Configuration Issues
- [ ] YAML flag conflicts between base.yaml and overrides
- [ ] use_execution_refinement flag interactions
- [ ] System mode settings (legacy vs modern)
- [ ] Timeframe configuration mismatches
- [ ] Strategy activation flags in config

### Code Issues  
- [ ] Import statement changes affecting dependencies
- [ ] Path resolution issues for state files or configs
- [ ] Exception handling that silently fails
- [ ] Type annotation conflicts
- [ ] Variable name conflicts with existing code

### Environment Issues
- [ ] PYTHONPATH conflicts
- [ ] Data file access permissions
- [ ] State file corruption or conflicts
- [ ] Logging level masking important errors
- [ ] Memory or performance issues

### Legacy System Conflicts
- [ ] Old strategy factory logic interfering
- [ ] Cached state files from previous runs
- [ ] Database/file locks from parallel processes
- [ ] Configuration inheritance issues

## Investigation Tools for Tomorrow

### Quick Diagnostics
```python
# Add to tf_v3.py for debugging
def debug_evaluation_flow(self, signals):
    self.logger.error(f"DEBUG: TF-v3 evaluate called at {signals.get('timestamp')}")
    self.logger.error(f"DEBUG: Available signals: {list(signals.keys())}")
    self.logger.error(f"DEBUG: Regime: {signals.get('regime')} -> {signals.get('regime_mapped')}")
    self.logger.error(f"DEBUG: Risk suppressed: {signals.get('risk_suppressed')}")
    self.logger.error(f"DEBUG: OHLCV history length: {len(signals.get('ohlcv_history', []))}")
```

### Configuration Verification
```bash
# Verify exact config being used
python -c "from hyperliquid_bot.config.settings import Config; c=Config('configs/base.yaml'); print(c.dict())"

# Check for file conflicts
find . -name "*.py" -exec grep -l "look.*ahead\|warm.*up\|shift.*1" {} \;
```

### Strategy Activation Debug
```python
# Add to strategy_evaluator.py
def debug_strategy_activation(self, regime_state):
    self.logger.error(f"DEBUG: get_active_strategies called with {regime_state}")
    active = self._determine_active_strategies(regime_state)
    self.logger.error(f"DEBUG: Active strategies determined: {active}")
    return active
```

## Expected Findings

**Most Likely Issues**:
1. **Configuration Flag**: Some YAML setting is preventing TF-v3 activation
2. **Exception Silencing**: Error being caught and logged as debug instead of error
3. **State File Conflict**: Old state interfering with new logic

**Less Likely But Possible**:
1. **Data Schema Change**: Bias fixes changed expected data format
2. **Timing Issue**: Warm-up period calculation is too restrictive
3. **Dependency Conflict**: Import or library version issue

## Success Criteria

### Phase 1 Success
- [ ] Restore exactly 189 trades, 235% ROI for 2024
- [ ] Document complete working configuration
- [ ] Confirm TF-v3 evaluates thousands of times, not just 1

### Phase 2 Success  
- [ ] Each incremental fix shows expected small reduction in trades
- [ ] Final result: ~170 trades (10% reduction), maintaining profitability
- [ ] All bias prevention measures working without breaking core functionality

### Phase 3 Success
- [ ] Complete documentation of working bias-free system
- [ ] Step-by-step guide for implementing bias prevention correctly
- [ ] Robust TF-v3 strategy ready for live trading

## Tomorrow's Action Plan

**Morning (9-11 AM)**:
- Revert all bias fixes to working baseline
- Verify restoration with full backtest
- Document exact working configuration

**Midday (11 AM-2 PM)**:
- Implement Fix 1 (shift=1) only
- Test and document results
- If broken, investigate simple issues list

**Afternoon (2-5 PM)**:
- Continue incremental fixes if Fix 1 successful
- OR deep dive into simple issues if Fix 1 breaks system
- Document findings and create final implementation plan

This systematic approach will definitively isolate what specific change breaks the TF-v3 system and allow us to implement bias prevention correctly.