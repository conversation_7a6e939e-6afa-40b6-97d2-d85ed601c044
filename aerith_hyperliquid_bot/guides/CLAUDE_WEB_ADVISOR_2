# Clear Architecture Instructions for Claude Code

## 1. CRITICAL MISUNDERSTANDING: Regime Updates ≠ Trading Frequency

```python
# WRONG Understanding:
"crypto regimes change every 60 seconds" → Trade every 60 seconds ❌

# CORRECT Understanding:
Regime UPDATES every 60s → But TRADE DECISIONS only hourly ✅
```

**Key Point**: The 60-second updates give you CURRENT regime state when making HOURLY decisions. You're not meant to trade on every regime change!

## 2. Backtesting Data Pipeline (STOP Loading Everything!)

### WRONG Approach:
```python
# DON'T load 30 days of 1-second data at once!
all_data = load_features_1s('2024-01-01', '2024-01-31')  # 2.6M rows ❌
```

### CORRECT Approach:
```python
# Process ONE DAY at a time:
for date in date_range(start, end):
    # 1. Load just one day
    daily_1s = load_features_1s(date)  # ~86K rows ✓
    
    # 2. Resample to hourly
    hourly = resample_to_hourly(daily_1s)  # 24 rows ✓
    
    # 3. Process hourly bars
    for hour in hourly:
        evaluate_strategy(hour)
    
    # 4. Garbage collect
    del daily_1s
```

## 3. Strategy Logic Clarification

### The HOURLY Decision Flow:
```python
# Every hour (not every minute!):
1. Get current regime state (from 60s updates)
2. Check EMA crossover on HOURLY bars
3. Confirm regime aligns with signal
4. Enter position if all conditions met

# State persistence is IRRELEVANT for entry!
# It's for regime detection, not trading
```

### Why Legacy Works:
- It's probably using proper HOURLY evaluation
- Not trying to trade every regime change
- Respecting the timeframe hierarchy

## 4. Duplicate Timestamp Fix

```python
# This error means you're concatenating data wrong
# When merging daily files:
all_hourly = []
for date in dates:
    hourly = process_day(date)
    all_hourly.append(hourly)

# Use concat with verify_integrity=False
df = pd.concat(all_hourly, ignore_index=True)
df = df.drop_duplicates(subset=['timestamp'])
```

## 5. Stop Optimizing! Fix the Basics First

### Order of Operations:
1. **Get trades working** with default parameters
2. **Match legacy trade frequency** (~24 trades/month)
3. **Then worry about profitability**

### Debug Checklist:
```python
# Print these EVERY backtest:
print(f"Hours evaluated: {hours_count}")  # Should be ~720/month
print(f"Trades generated: {trade_count}")  # Should be ~24/month
print(f"Regime at trade: {regime_states}")  # Should be mostly BULL/BEAR
print(f"EMA difference at entry: {ema_diff}")  # Should be significant
```

## 6. Live Trading (Future - Placeholder)

```python
# Live trading will be DIFFERENT:
class LiveTrader:
    def __init__(self):
        self.regime_updater = Timer(60, self.update_regime)
        self.trade_evaluator = Timer(3600, self.evaluate_trades)
    
    # But this is LATER - get backtesting working first!
```

## Key Messages for Claude Code:

1. **STOP trying to load all data at once** - Process day by day
2. **STOP confusing regime updates with trade frequency** - Updates≠Trades  
3. **STOP optimizing parameters** - Fix the architecture first
4. **START printing debug info** - Understand what's actually happening
5. **The 0 state persistence is FINE** - It's not for trading decisions

## The One Truth:

Your modern system should generate **THE SAME NUMBER OF TRADES** as legacy (~24/month), just with **BETTER TIMING** from regime awareness. If you're getting different trade counts, the architecture is wrong, not the parameters!