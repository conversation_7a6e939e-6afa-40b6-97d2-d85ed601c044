# Data Pipeline Usage Guide

This guide explains how to use the updated data pipeline with Task R-101 ATR improvements.

## Overview

The data pipeline consists of two main steps:

1. **TXT → Arrow Conversion**: Convert raw JSON/TXT L2 book files to Arrow format
2. **Arrow → 1s Features**: Generate 1-second feature parquet files with improved ATR calculation

## File Structure

```
/hyperliquid_data/
├── l2_raw/
│   ├── 20250301/                    # Raw TXT files (old format)
│   │   ├── BTC_0_l2Book.txt
│   │   ├── BTC_1_l2Book.txt
│   │   └── ...
│   ├── 2025-03-01/                  # Arrow files (new format)
│   │   ├── BTC_00_l2Book.arrow
│   │   ├── BTC_01_l2Book.arrow
│   │   └── ...
│   └── ...
├── features_1s/
│   ├── 2025-03-01/                  # 1-second feature files
│   │   ├── features_00.parquet
│   │   ├── features_01.parquet
│   │   └── ...
│   └── ...
└── ohlcv/
    └── 1h/                          # Hourly OHLCV for ATR calculation
        ├── 2025-03-01_1h.parquet
        └── ...
```

## Scripts

### 1. Individual Scripts

#### TXT to Arrow Conversion
```bash
python scripts/convert_txt_to_arrow.py --base-dir /path/to/l2_raw --overwrite
```

#### Arrow to 1s Features (Single Date)
```bash
python -m tools.etl_l20_to_1s --date 2025-03-01 --overwrite
```

### 2. Master Pipeline Script

#### Process Complete Date Range
```bash
python scripts/update_data_pipeline.py --start-date 2025-03-01 --end-date 2025-03-22 --overwrite
```

#### Convert TXT Files Only
```bash
python scripts/update_data_pipeline.py --convert-only --overwrite
```

#### Regenerate Features Only (Skip Conversion)
```bash
python scripts/update_data_pipeline.py --skip-conversion --overwrite
```

#### Verify ATR Injection Only
```bash
python scripts/update_data_pipeline.py --verify-only
```

## Key Features

### ATR Injection (Task R-101)
- Uses existing hourly OHLCV data for ATR calculation
- Implements Wilder's smoothing method for ATR(14)
- Forward-fills ATR values to 1-second resolution
- Adds both `atr_14_sec` and `atr_percent_sec` columns

### Robust File Handling
- Supports both old (YYYYMMDD) and new (YYYY-MM-DD) directory formats
- Handles missing files gracefully
- Overwrites existing files when `--overwrite` is specified
- Validates data quality with built-in checks

### Quality Gates
- ✅ ATR column exists and is float64
- ✅ NaN count < 14 (warmup period)
- ✅ Timestamps are monotonic
- ✅ Essential columns present (mid_price, OBI, etc.)

## Usage Examples

### Process New Raw Data
When you receive new raw TXT files:

```bash
# 1. Convert TXT to Arrow
python scripts/update_data_pipeline.py --convert-only --overwrite

# 2. Generate 1s features with ATR
python scripts/update_data_pipeline.py --skip-conversion --overwrite
```

### Regenerate Features with New ATR
To regenerate existing 1s features with improved ATR:

```bash
python scripts/update_data_pipeline.py --skip-conversion --overwrite
```

### Verify Pipeline Health
```bash
python scripts/update_data_pipeline.py --verify-only
```

## Output

### Successful Pipeline Run
```
=== UPDATING DATA PIPELINE FOR 2025-03-01 TO 2025-03-22 ===
✅ All 1-second feature files now have proper ATR values
✅ TF-v3 and Continuous GMS strategies can now use ATR features
✅ Raw TXT files converted to Arrow format
✅ Pipeline ready for future raw data processing
```

### Quality Verification
```
✅ Quality Gates:
✅ ATR column exists: True
✅ ATR is float64: True
✅ NaN count < 14: True
✅ Timestamps monotonic: True
✅ Has mid_price: True
✅ Has OBI: True
```

## Troubleshooting

### Common Issues

1. **Missing hourly OHLCV data**: Pipeline falls back to calculating OHLC from 1s data
2. **File format errors**: Check that TXT files contain valid Hyperliquid L2 JSON format
3. **Permission errors**: Ensure write access to output directories
4. **Memory issues**: Process smaller date ranges for large datasets

### Debug Mode
Add verbose logging by modifying the script to set log level to DEBUG.

## Integration

The updated pipeline is fully compatible with:
- TF-v3 strategy (requires ATR features)
- Continuous GMS detector
- Existing backtesting infrastructure
- Future high-frequency strategies

## Performance

- **TXT → Arrow**: ~250ms per hour file (6K rows)
- **Arrow → 1s Features**: ~50ms per hour file with ATR injection
- **Memory usage**: ~100MB per day of processing
- **Storage**: Arrow files are ~50% smaller than equivalent TXT files
