# Regime-Aware Execution Enhancement PRD

## Executive Summary

This PRD outlines the implementation of a sophisticated regime-aware trading system that enhances the modern path (continuous_gms + TF-v3) with multi-timeframe execution refinement. The goal is to maintain the same trade frequency (2-5/day) while improving execution quality and risk management.

## Problem Statement

Current issues:
1. **Calibration Problem**: continuous_gms with proper thresholds only detects CHOP regime
2. **Execution Quality**: No refinement of entry timing leading to unnecessary slippage
3. **Regime Blindness**: TF-v3 generates signals without considering current market regime
4. **Risk Management**: No dynamic adjustment based on regime confidence

## Solution Architecture

### High-Level Design
```
Continuous GMS (60s) → Updates regime_state every minute
                    ↓
TF-v3 (hourly eval) → Reads CURRENT regime + history
                    ↓
Execution Refinement → Uses regime confidence for timing
```

### Key Principles
1. **No Overtrading**: Maintain 2-5 trades/day by evaluating only on hourly boundaries
2. **Regime Context**: Use 60s regime updates for context without generating more signals
3. **Simple Filtering**: Start with basic opposing signal filters before complex logic
4. **Incremental Enhancement**: Each phase independently valuable and testable

## Implementation Phases

### Phase 0: Calibration Fix (PREREQUISITE)
**Goal**: Ensure continuous_gms properly detects BULL/BEAR/CHOP regimes

**Tasks**:
- [ ] Analyze current threshold scaling issues
- [ ] Create calibrated override config
- [ ] Validate proper regime distribution in backtest
- [ ] Ensure both long and short signals generated

**Success Criteria**:
- Regime distribution: ~30-40% BULL, ~20-30% BEAR, ~30-40% CHOP
- Both long and short trades present
- No single regime dominance

### Phase 1: Basic Regime-Aware TF-v3
**Goal**: Filter out signals that oppose current regime

**Implementation**:
```python
# Simple regime filtering
if signal == 'long' and current_regime == 'BEAR':
    skip_signal()
elif signal == 'short' and current_regime == 'BULL':
    skip_signal()
```

**Metrics**:
- Track filtered signals
- Measure win rate improvement
- Monitor trade count reduction

### Phase 2: Confidence-Based Adjustments
**Goal**: Scale positions based on regime confidence and duration

**Features**:
- Position sizing: `base_size * regime_confidence`
- Skip signals when confidence < 0.6
- Reduce size for regimes < 15 minutes old

### Phase 3: Execution Refinement Layer
**Goal**: Optimize entry timing using 1m/1s data

**Architecture**:
```python
# After hourly signal generated
1. Check last 5 minutes regime trend
2. If strengthening → execute now
3. If weakening → wait up to 5 minutes
4. Use 1s data for optimal entry price
```

### Phase 4: Advanced Features
**Goal**: Sophisticated regime-based risk management

**Features**:
- Early exit on regime transitions
- Dynamic stop adjustments
- Regime-specific take profit levels

## Technical Specifications

### Data Requirements
**1-hour data**: `/hyperliquid_data/resampled_l2/1h/`
- OHLCV standard fields
- Calculated indicators (EMA, ATR)

**1-second features**: `/hyperliquid_data/features_1s/`
- `raw_obi_5`, `spread_relative`, `atr_percent`
- For resampling to 1-minute bars

**Regime State Fields**:
- `state`: 'BULL'|'BEAR'|'CHOP'
- `confidence`: 0.0-1.0
- `minutes_in_regime`: integer
- `transition_probability`: 0.0-1.0

### Configuration Schema
```yaml
continuous_gms:
  cadence_sec: 60
  # Calibrated thresholds (TBD based on Phase 0)
  
tf_v3:
  evaluation_schedule: "hourly"
  regime_aware: true
  regime_filters:
    skip_opposing_signals: true
    min_confidence: 0.6
    min_duration_minutes: 15
    
execution_refinement:
  enabled: false  # Phase 3
  max_wait_minutes: 5
  momentum_lookback_bars: 5
```

## Risk Mitigation

1. **Feature Flags**: Each enhancement behind config flag
2. **Parallel Testing**: Run alongside baseline for comparison
3. **Incremental Rollout**: Validate each phase before next
4. **Clear Rollback**: Can disable with single config change

## Success Metrics

### Primary KPIs
- Win Rate: Target >5% improvement
- Average Entry Improvement: Target >5 bps
- Risk-Adjusted Returns: Higher Sharpe ratio
- Trade Quality: Fewer losing trades

### Secondary KPIs
- Regime Detection Accuracy
- Signal-to-Execution Ratio
- Average Holding Period (should remain 4-24h)
- Maximum Drawdown Reduction

## Timeline

**Week 0**: Calibration Fix (2-3 days)
**Week 1**: Regime-Aware TF-v3 (5 days)
**Week 2**: Execution Refinement (5 days)
**Week 3**: Testing & Optimization (5 days)

Total: ~3 weeks for full implementation

## Appendix A: Calibration Analysis

Current issues with continuous_gms calibration:
1. Momentum thresholds (0.5/2.5) too low for hourly MA slope
2. Volatility thresholds may need adjustment
3. OBI confirmation logic might be too strict

Proposed calibration approach:
1. Analyze legacy system thresholds
2. Scale appropriately for 60s updates
3. Test multiple threshold combinations
4. Validate regime distribution

## Appendix B: Example Day

```
09:00 - 1h eval: LONG signal (EMA cross)
      - Regime: BULL (confidence: 0.85)
      - Execute immediately
      
10:00 - 1h eval: No signal
      - Regime: BULL (confidence: 0.90)
      
11:00 - 1h eval: SHORT signal (trailing stop)
      - Regime: Transitioning to CHOP
      - Delay execution, monitor regime
      
11:03 - Regime confirmed CHOP
      - Execute SHORT with reduced size
      
Result: 2 trades, better entries, risk-adjusted sizing
```