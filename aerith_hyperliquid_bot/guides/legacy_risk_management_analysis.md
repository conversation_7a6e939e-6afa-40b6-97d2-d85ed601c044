# Legacy Risk Management Analysis - Confirmation

## Key Finding Confirmed: 2% Risk with ATR-Based Sizing

After examining the legacy code, I can confirm the user's analysis is correct:

### 1. The 25% Confusion - RESOLVED
- `legacy/strategy.py:97` returns 0.25 but this is NOT used
- Actual risk comes from `portfolio.risk_per_trade: 0.02` in base.yaml
- The system uses 2% risk per trade, not 25%

### 2. Position Sizing Formula - VERIFIED
From examining the code flow:
```python
# RiskManager calculates:
risk_amount = balance * 0.02  # 2% risk
stop_distance = atr * stop_multiplier  # 2-3x ATR
position_size = risk_amount / stop_distance
```

This creates dynamic leverage based on volatility:
- Low volatility → larger positions → higher leverage
- High volatility → smaller positions → lower leverage

### 3. Why This Works
The ATR-based sizing is genius because:
- **Risk Normalization**: Every trade risks same $ amount
- **Volatility Adaptation**: Automatically adjusts to market conditions  
- **Capital Efficiency**: Uses more leverage when safe (low vol)
- **Protection**: Uses less leverage when dangerous (high vol)

### 4. Exit Logic Complements Risk Management
The exits in backtester work with this system:
- Stop Loss: 2-3x ATR (matches position sizing logic)
- Take Profit: ATR-based targets
- Max Hold Time: Prevents drift from entry conditions

### 5. Implications for Replication

#### CRITICAL for Modern System:
1. Must implement ATR-based position sizing
2. Use 2% base risk, NOT fixed position sizes
3. Allow dynamic leverage (2-10x range)
4. Ensure stop losses match the ATR multiplier used in sizing

#### This Explains Performance Gap:
- Legacy: Dynamic sizing adapts to volatility
- Modern: Missing this crucial component
- Result: Modern takes wrong-sized positions

### Architecture Decision
Based on this understanding, I agree with the user's suggestion:
- **Option B**: Create separate `ExitManager` component
- Copy exact exit logic from legacy backtester
- Map to TF-v3 and future strategies cleanly

This maintains the sophisticated risk/exit synergy while improving architecture.