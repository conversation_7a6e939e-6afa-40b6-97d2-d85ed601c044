# Data Architecture Enhancement Plan

## Executive Summary

This document outlines a comprehensive plan to enhance the modern trading system's data architecture, addressing the critical performance bottleneck (10+ minute backtests) while maintaining flexibility for novel regime detection research using Hyperliquid-specific microstructure features.

## Current State Analysis

### Performance Bottleneck
- **Issue**: Modern system loads 2.27 billion data points for 2024 backtest
- **Root Cause**: Loading 72 hours of 1s data (259,200 points) to generate each hourly bar
- **Impact**: 10-15 minute backtests killing research velocity

### Architecture Comparison

#### Legacy System
- **Pros**: 
  - On-the-fly microstructure calculation from raw L2 snapshots
  - Dynamic depth level adaptation
  - Direct access to order book for novel features
- **Cons**: 
  - Potentially slower for repeated calculations
  - Higher memory usage during runtime

#### Modern System
- **Pros**: 
  - Clean isolation from legacy system
  - Pre-computed features reduce runtime calculations
- **Cons**: 
  - No access to raw L2 data
  - Fixed feature set limits experimentation
  - Massive data loading overhead

### Key Findings
1. TF-v3 strategy only uses: EMA crossover + forecast + regime confirmation
2. RSI and Bollinger Bands are calculated but not used for trading
3. Microstructure features (volume_imbalance) are critical for regime detection
4. System has access to unique Hyperliquid data: funding rates, OI, liquidations

## Implementation Plan

### Phase 1: Immediate Performance Fix (1-2 days)

#### 1.1 Create Enhanced Hourly Resampling Script

**File**: `scripts/create_enhanced_hourly_data.py`

**Purpose**: Convert 1s features to enhanced hourly format with all TF-v3 requirements

**Schema**:
```python
# Enhanced hourly schema (12 columns)
{
    # Core OHLCV
    'timestamp': 'datetime64[ns]',  # Index
    'open': 'float64',
    'high': 'float64',
    'low': 'float64',
    'close': 'float64',
    'volume': 'float64',
    
    # Microstructure (from features_1s)
    'volume_imbalance': 'float64',  # from obi_smoothed
    'spread_mean': 'float64',
    'spread_std': 'float64',
    'ma_slope': 'float64',
    'ma_slope_ema_30s': 'float64',
    
    # Volatility
    'atr_14_sec': 'float64',
    'atr_percent_sec': 'float64',
    'realised_vol_1s': 'float64'
}
```

**Aggregation Strategy**:
```python
agg_dict = {
    # OHLCV - standard
    'open': 'first',
    'high': 'max',
    'low': 'min',
    'close': 'last',
    'volume': 'sum',
    
    # Microstructure - statistical
    'obi_smoothed': 'mean',  # → volume_imbalance
    'spread_mean': 'mean',
    'spread_std': 'mean',
    'realised_vol_1s': 'mean',
    
    # Indicators - last value
    'ma_slope': 'last',
    'ma_slope_ema_30s': 'last',
    'atr_14_sec': 'last',
    'atr_percent_sec': 'last'
}
```

#### 1.2 Batch Processing Implementation

**File**: `scripts/batch_create_enhanced_hourly.py`

**Features**:
- Process full date range with progress tracking
- Error handling and resumption capability
- Validation against original data
- Performance metrics logging

**Usage**:
```bash
python scripts/batch_create_enhanced_hourly.py \
    --start 2024-01-01 \
    --end 2024-12-31 \
    --validate
```

#### 1.3 Update Modern Data Loader

**File**: `hyperliquid_bot/modern/data_loader.py`

**Changes**:
- Add method `load_enhanced_hourly_features()`
- Remove 1s resampling logic
- Maintain backward compatibility with config flag

**Performance Impact**:
- Before: 2.27B data points loaded
- After: 630K data points loaded
- Expected speedup: ~3,600x

### Phase 2: Add Microstructure Flexibility (1 week)

#### 2.1 Modern Microstructure Module

**File**: `hyperliquid_bot/modern/features/live_microstructure.py`

**Core Functions**:
```python
class ModernMicrostructureEngine:
    def calculate_dynamic_obi(self, l2_data, depth_levels=[5, 10, 20]):
        """Calculate OBI at multiple depths for regime detection"""
        
    def calculate_liquidation_pressure(self, liquidation_data, window='10s'):
        """
        Hyperliquid-specific: Detect liquidation cascades
        Returns: pressure score (-1 to 1)
        """
        
    def calculate_funding_momentum(self, funding_series):
        """
        Calculate funding rate acceleration (2nd derivative)
        Indicates crowded trade acceleration
        """
        
    def calculate_oi_velocity(self, oi_series, volume_series):
        """
        OI change rate vs volume change rate
        Detects positioning battles
        """
        
    def calculate_book_absorption_ratio(self, book_pressure, realized_volume):
        """
        Realized flow vs potential flow
        Detects absorption or spoofing
        """
```

#### 2.2 Dual Data Path Architecture

**Primary Path** (Fast - 95% of backtests):
```
enhanced_hourly/1h/*.parquet → DataLoader → SignalEngine → Strategy
```

**Secondary Path** (Flexible - 5% deep research):
```
features_1s/*.parquet → MicrostructureEngine → Custom Features → Strategy
```

**Configuration**:
```yaml
data:
  primary_source: "enhanced_hourly"
  enable_experimental_features: false
  experimental_features:
    - "dynamic_obi"
    - "liquidation_pressure"
    - "funding_momentum"
```

#### 2.3 Integration Points

1. **Data Adapter Enhancement**:
   - Add feature source routing
   - Maintain field mapping consistency
   - Cache experimental features

2. **Signal Engine Integration**:
   - Accept both pre-computed and on-demand features
   - Feature importance tracking
   - A/B testing framework

### Phase 3: Future Enhancements

#### 3.1 Polars Migration Strategy

**Evaluation Criteria**:
- 10x performance on rolling operations
- Better memory efficiency (2-4x vs 5-10x RAM usage)
- Multi-threaded by default

**Migration Path**:
1. Benchmark critical operations
2. Create polars branch for testing
3. Implement compatibility layer
4. Gradual rollout by module

#### 3.2 Novel Regime Definitions

**Hyperliquid-Specific Regimes**:

1. **Funding-Driven Regime**:
   - Trigger: `abs(funding_rate_acceleration) > threshold`
   - Characteristics: Crowded trades, mean reversion likely
   - Strategy adjustment: Reduce position size, tighter stops

2. **Liquidation Cascade Regime**:
   - Trigger: `liquidation_volume.rolling('10s').sum() > threshold`
   - Characteristics: Forced flows, momentum continuation
   - Strategy adjustment: Follow liquidation direction

3. **OI Divergence Regime**:
   - Trigger: `oi_velocity / volume_velocity > threshold`
   - Characteristics: Large positioning without price movement
   - Strategy adjustment: Prepare for breakout

4. **Book Absorption Regime**:
   - Trigger: `book_absorption_ratio < threshold`
   - Characteristics: Large orders being absorbed
   - Strategy adjustment: Fade aggressive orders

## Clarifications from Planning Discussion

### Data Requirements
1. **Depth Levels**: 5 levels is sufficient
   - Top 5 levels capture ~80-90% of order book activity
   - Standard in academic microstructure research
   - Can extend later if needed

2. **Live Trading Architecture**:
   ```
   Backtesting: features_1s files → resample to 1h → TF-v3
   Live: WebSocket → features_1s (real-time) → resample to 1h → TF-v3
   ```
   - Same resampling logic for both modes
   - Ensures consistency between backtest and live

3. **Validation Criteria**: 
   - Zero tolerance for differences between backtests
   - Deterministic aggregation required
   - No random operations allowed

4. **Testing Baseline**: Full 2024 data
   - Multiple market regimes included
   - Sufficient sample size
   - Recent, relevant data

5. **Data Constraints**:
   - No historical funding rates available from Hyperliquid
   - Must work with features that can be inferred from existing data
   - Need to verify OI (open interest) availability

### Feature Implementation Priority (Based on Data Discovery)

We discovered 20-level order book data (bid_px_1-20, bid_sz_1-20, ask_px_1-20, ask_sz_1-20) that's completely underutilized. This enables powerful novel features:

#### Priority 1: Deep Book Pressure Score (DBPS)
**Concept**: Detect hidden liquidity and whale orders in levels 6-20
```python
def calculate_deep_book_pressure(df):
    # Compare shallow (1-5) vs deep (6-20) liquidity
    shallow_bid_liq = sum(df[f'bid_sz_{i}'] for i in range(1, 6))
    deep_bid_liq = sum(df[f'bid_sz_{i}'] for i in range(6, 21))
    
    # Pressure score: deep liquidity relative to shallow
    bid_pressure = deep_bid_liq / (shallow_bid_liq + 1e-8)
    
    # Similar for asks
    # High ratio = large orders hiding in deep book
    return bid_pressure - ask_pressure  # Directional signal
```

#### Priority 2: Liquidity Shape Entropy (LSE)
**Concept**: Detect regime changes through order book shape changes
```python
def calculate_liquidity_entropy(df):
    # Normalize liquidity distribution
    bid_sizes = [df[f'bid_sz_{i}'] for i in range(1, 21)]
    total_bid = sum(bid_sizes)
    bid_probs = [sz/total_bid for sz in bid_sizes]
    
    # Shannon entropy of liquidity distribution
    entropy = -sum(p * log(p+1e-8) for p in bid_probs if p > 0)
    
    # Low entropy = concentrated liquidity (whale orders)
    # High entropy = distributed liquidity (normal market)
    return entropy
```

#### Priority 3: Order Book Velocity Gradient (OBVG)
**Concept**: Rate of change in liquidity at different levels
```python
def calculate_ob_velocity_gradient(df, lookback=60):
    # Track how fast liquidity changes at each level
    velocities = []
    for level in range(1, 21):
        current = df[f'bid_sz_{level}']
        previous = df[f'bid_sz_{level}'].shift(lookback)
        velocity = (current - previous) / lookback
        velocities.append(velocity)
    
    # Gradient shows if changes concentrate at top or deep
    gradient = np.gradient(velocities)
    return gradient.mean()  # Positive = deepening liquidity
```

#### Priority 4: Market Maker Footprint (MMF)
**Concept**: Detect algorithmic market making patterns
```python
def calculate_mm_footprint(df):
    # Detect regular spacing patterns in order book
    bid_prices = [df[f'bid_px_{i}'] for i in range(1, 11)]
    price_gaps = [bid_prices[i] - bid_prices[i+1] for i in range(9)]
    
    # Low variance in gaps = algorithmic placement
    gap_variance = np.var(price_gaps)
    
    # Size clustering detection
    bid_sizes = [df[f'bid_sz_{i}'] for i in range(1, 11)]
    size_clusters = len(set(bid_sizes)) / 10  # Ratio of unique sizes
    
    # Combined score (lower = more algorithmic)
    return gap_variance * size_clusters
```

### Implementation Complexity & Value Matrix

| Feature | Implementation | Value | Data Required |
|---------|---------------|-------|---------------|
| Deep Book Pressure | Easy (2 hrs) | High | 20-level book data ✓ |
| Liquidity Entropy | Easy (2 hrs) | High | 20-level book data ✓ |
| OB Velocity Gradient | Medium (4 hrs) | Medium | Historical book data ✓ |
| MM Footprint | Medium (4 hrs) | Medium | 10-level price/size ✓ |

### Why These Features Matter

1. **Early Detection**: Microstructure changes happen 5-30 seconds before price moves
2. **Unique to Hyperliquid**: Most traders only look at top 5 levels
3. **Regime Specific**: Each feature targets different regime types:
   - DBPS → Whale accumulation/distribution
   - LSE → Market structure transitions
   - OBVG → Momentum building in deep book
   - MMF → Algorithmic vs organic markets

## Implementation Timeline

### Week 1
- Day 1-2: Implement enhanced hourly resampling
- Day 3: Update data loader
- Day 4-5: Test and validate performance

### Week 2  
- Day 1-2: Create modern microstructure module
- Day 3-4: Implement dual data path
- Day 5: Integration testing

### Week 3
- Day 1-2: Implement novel regime features (based on available data)
- Day 3-4: Backtest validation
- Day 5: Documentation and training

## Risk Analysis & Mitigation

### Technical Risks

1. **Data Quality**:
   - Risk: Aggregation might lose important information
   - Mitigation: Validate against current results, keep raw data

2. **Integration Complexity**:
   - Risk: Breaking modern system isolation
   - Mitigation: Use clear interfaces, maintain separation

3. **Performance Regression**:
   - Risk: New features might slow system
   - Mitigation: Profile before/after, feature flags

### Operational Risks

1. **Migration Disruption**:
   - Risk: Breaking existing workflows
   - Mitigation: Parallel operation, gradual cutover

2. **Team Adaptation**:
   - Risk: Learning curve for new architecture
   - Mitigation: Clear documentation, pair programming

## Success Metrics

### Performance
- [ ] Backtest runtime: <30 seconds for 2024
- [ ] Memory usage: <4GB for full year
- [ ] Data loading: <5 seconds

### Functionality
- [ ] All existing features maintained
- [ ] 3+ novel regime features implemented
- [ ] Experimental feature framework operational

### Research Velocity
- [ ] Feature development cycle: <1 day
- [ ] Backtest iteration: <1 minute
- [ ] A/B testing capability enabled

## Validation Checklist

### Phase 1 Validation
- [ ] Enhanced hourly data matches current OHLCV
- [ ] Microstructure features correctly aggregated
- [ ] Backtest results match within 0.1%
- [ ] Performance improvement >1000x

### Phase 2 Validation
- [ ] Microstructure module produces same OBI values
- [ ] Novel features generate reasonable values
- [ ] Dual data path works seamlessly
- [ ] No memory leaks in experimental mode

### Phase 3 Validation
- [ ] Regime detection improves Sharpe ratio
- [ ] Novel features add predictive value
- [ ] System remains maintainable
- [ ] Documentation complete

## Code Examples

### Enhanced Data Loading
```python
# Before (slow)
def load_hourly_features(self, start_time, end_time):
    # Loads 259,200 points per hour
    features_1s = self._load_feature_data(start_time, end_time)
    return self._resample_to_hourly(features_1s)

# After (fast)
def load_hourly_features(self, start_time, end_time):
    # Loads 1 point per hour
    enhanced_path = self.data_dir / "enhanced_hourly" / "1h"
    return pd.read_parquet(enhanced_path, 
                          filters=[('timestamp', '>=', start_time),
                                  ('timestamp', '<', end_time)])
```

### Novel Feature Calculation
```python
def calculate_funding_momentum(self, df):
    """
    Detect acceleration in funding rates
    High values indicate crowded trades getting more crowded
    """
    funding_rate = df['funding_rate']
    
    # First derivative: rate of change
    funding_velocity = funding_rate.diff()
    
    # Second derivative: acceleration
    funding_acceleration = funding_velocity.diff()
    
    # Normalize by recent volatility
    recent_vol = funding_rate.rolling(window=24).std()
    funding_momentum = funding_acceleration / recent_vol
    
    return funding_momentum
```

## Conclusion

This plan addresses the immediate performance crisis while building toward a sophisticated regime detection system that leverages Hyperliquid's unique data. The phased approach minimizes risk while maximizing research velocity.

The key insight is that we don't need to choose between performance and flexibility - we can have both through intelligent data architecture design.