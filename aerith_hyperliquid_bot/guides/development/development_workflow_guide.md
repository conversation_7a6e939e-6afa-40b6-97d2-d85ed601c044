# Development Workflow Guide

## Core Development Principles

### 1. Simplicity First
- Start with minimal viable implementation
- Add complexity only when proven necessary
- One clear way to do things, not five

### 2. Incremental Progress
- Change ONE thing at a time
- Test after EVERY change
- Git commit working improvements immediately
- Revert if performance degrades

### 3. Explicit Over Implicit
- No silent fallbacks
- Clear error messages
- Obvious code flow
- Document WHY, not just what

## Development Workflow

### Phase 1: Planning
1. **Document Current State**
   ```bash
   # Record baseline metrics
   python3 scripts/run_backtest.py > baseline_metrics.txt
   # Metrics to track: trades, ROI, win rate, sharpe
   ```

2. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Write Test First**
   - Define success criteria
   - Create validation tests
   - Document expected behavior

### Phase 2: Implementation

#### Step 1: Minimal Implementation
```python
# Start with simplest possible version
class NewFeature:
    def process(self, data):
        # Basic implementation only
        return data
```

#### Step 2: Test Immediately
```bash
# Run quick test
python3 -m pytest tests/test_new_feature.py

# Run integration test
python3 scripts/run_backtest.py --override test_config.yaml
```

#### Step 3: Check Configuration
```
[INFO] Backtester: ✅ No configuration fallbacks detected
```

#### Step 4: Commit Working Version
```bash
git add -A
git commit -m "feat: Add basic NewFeature implementation

- Processes data without modifications
- Passes all baseline tests
- No performance impact: 180 trades, 215% ROI"
```

### Phase 3: Enhancement

Only after basic version works:

1. **Add ONE Enhancement**
   ```python
   class NewFeature:
       def process(self, data):
           # Add single enhancement
           enhanced_data = self._enhance(data)
           return enhanced_data
   ```

2. **Test Enhancement**
   ```bash
   # Compare metrics
   python3 scripts/compare_metrics.py baseline_metrics.txt new_metrics.txt
   ```

3. **Commit or Revert**
   - If improvement: commit
   - If degradation: `git checkout -- .`

## Configuration Management

### 1. Always Explicit Configs
```yaml
# BAD: Relying on defaults
tf_v3:
  max_leverage: 5.0
  
# GOOD: All critical values explicit
tf_v3:
  risk_frac: 0.25      # EXPLICIT
  max_leverage: 5.0    # EXPLICIT
  atr_trail_k: 2.0     # EXPLICIT
```

### 2. Test Configuration Changes
```python
# Add to test suite
def test_configuration_no_fallbacks():
    config = load_config("configs/your_config.yaml")
    validator = ConfigValidator()
    issues = validator.validate_config(config)
    assert len(issues) == 0, f"Config issues: {issues}"
```

### 3. Version Control Configs
```bash
# Tag working configuration
git tag -a "config-v1.0-working" -m "180 trades, 215% ROI"
```

## Testing Strategy

### 1. Unit Tests
```python
# Test individual components
def test_detector_thresholds():
    detector = ContinuousGMSDetector(config)
    assert detector.mom_strong_thresh == 100.0
    assert detector.vol_high_thresh == 0.0092
```

### 2. Integration Tests
```python
# Test component interactions
def test_strategy_with_detector():
    detector = get_regime_detector(config)
    strategy = TFV3Strategy(config)
    signals = generate_test_signals()
    
    regime = detector.detect_regime(signals)
    decision = strategy.evaluate(signals, regime)
    
    assert decision is not None
```

### 3. Regression Tests
```bash
# Ensure changes don't break working features
python3 -m pytest tests/regression/
```

### 4. Performance Tests
```python
# Track key metrics
BASELINE_METRICS = {
    "trades": 180,
    "roi": 215.0,
    "win_rate": 0.56,
    "sharpe": 1.8
}

def test_performance_metrics():
    results = run_backtest(config)
    assert abs(results["trades"] - BASELINE_METRICS["trades"]) < 10
    assert abs(results["roi"] - BASELINE_METRICS["roi"]) < 5.0
```

## Git Workflow

### 1. Atomic Commits
```bash
# One logical change per commit
git add specific_file.py
git commit -m "fix: Correct momentum threshold in detector"

git add another_file.py  
git commit -m "feat: Add validation for threshold values"
```

### 2. Descriptive Messages
```bash
# BAD
git commit -m "fix bug"

# GOOD
git commit -m "fix: Restore risk_frac to 0.25 in tf_v3 config

- Was falling back to schema default (0.02)
- Caused position sizes to be 12.5x smaller
- Fixes issue #123"
```

### 3. Branch Protection
```bash
# Never commit directly to main
git checkout main
git pull origin main
git checkout -b fix/configuration-fallback
# ... make changes ...
git push origin fix/configuration-fallback
# Create PR for review
```

## Debugging Workflow

### 1. Immediate Investigation
```bash
# When something breaks
git status                    # What changed?
git diff                      # How did it change?
grep "❌" logs/*.log         # Config issues?
git log --oneline -10        # Recent commits?
```

### 2. Systematic Isolation
```bash
# Binary search for breaking change
git bisect start
git bisect bad                    # Current broken
git bisect good 4e2af48          # Known working
# Test each commit git suggests
```

### 3. Document Findings
```markdown
## Debug Session 2024-01-15

### Issue
- 0 trades generated (expected 180)

### Investigation
1. Checked logs: 100% regime gate failures
2. Found config: risk_frac = 0.02 (should be 0.25)
3. Traced source: schema default fallback

### Solution
- Added explicit risk_frac to override config
- Validated with ConfigValidator

### Result
- ❌ Still 0 trades - deeper issue exists
```

## Code Review Checklist

Before submitting PR:

### Functionality
- [ ] Feature works as intended
- [ ] No performance degradation
- [ ] All tests pass

### Configuration  
- [ ] No configuration fallbacks (✅ in logs)
- [ ] All values explicitly set
- [ ] Thresholds match between sections

### Code Quality
- [ ] Clear, obvious implementation
- [ ] No clever tricks
- [ ] Proper error handling
- [ ] Meaningful variable names

### Testing
- [ ] Unit tests added
- [ ] Integration tests pass
- [ ] Regression tests pass
- [ ] Performance benchmarked

### Documentation
- [ ] Code comments explain WHY
- [ ] README updated if needed
- [ ] Configuration documented
- [ ] Breaking changes noted

## Common Pitfalls

### 1. Configuration Inheritance
```python
# Always log actual values
logger.info(f"Using risk_frac: {config.tf_v3.risk_frac}")
```

### 2. Silent Failures
```python
# BAD
try:
    process_data()
except:
    pass  # Silent failure!

# GOOD  
try:
    process_data()
except Exception as e:
    logger.error(f"Failed to process data: {e}")
    raise
```

### 3. Untested Changes
```bash
# ALWAYS test before committing
python3 scripts/run_backtest.py
# Check: trades, ROI, no errors
```

## Emergency Procedures

### When Everything Breaks
1. **Don't Panic**
2. **Revert to Known Good**
   ```bash
   git checkout 4e2af48 -- .
   ```
3. **Start Over with Smaller Changes**
4. **Document What Went Wrong**

### Recovery Checklist
- [ ] Identify last working commit
- [ ] Create new branch from there
- [ ] Apply changes incrementally
- [ ] Test after each change
- [ ] Find minimal breaking change
- [ ] Fix or abandon approach