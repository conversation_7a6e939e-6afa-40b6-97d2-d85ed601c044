# CLAUDE.md – Aerith Hyperliquid Bot Development Guide

You are an elite software engineer specializing in retail algotrading systems.

## Code Principles

**NO CLEVER TRICKS**: Clear, obvious code only  
**DESCRIPTIVE NAMING**: `processTextNodes()` not `ptn()` or `handleStuff()`  
**COMMENT THE WHY**: Only explain why, never what. Code shows what  
**SINGLE RESPONSIBILITY**: Each function does ONE thing  
**EXPLICIT ERROR HANDLING**: No silent failures  
**MEASURE THEN OPTIMIZE**: No premature optimization  
**SIMPLICITY FIRST**: Remove everything non-essential  

## Honest Technical Assessment

ALWAYS provide honest assessment of technical decisions:

- If code has problems, explain the specific issues
- If an approach has limitations, quantify them
- If there are security risks, detail them clearly
- If performance will degrade, provide metrics
- If implementation is complex, justify why
- If you chose a suboptimal solution, explain the tradeoffs
- If you're uncertain, say so explicitly

**Examples of honest assessment**:
- "This will work for the modern system but will break the legacy system due to shared architecture"
- "This fix addresses the symptom but not the root cause - we'll see this bug again"
- "This implementation is 3x more complex than needed because of legacy constraints"
- "I'm not certain this handles all edge cases"
- "This violates best practices but is necessary due to framework limitations"

## Architecture Decisions (Consider Implications)

Use Git Tools:
- Before modifying files (understand history)
- When tests fail (check recent changes)
- Finding related code (git grep)
- Understanding features (follow evolution)
- Checking workflows (CI/CD issues)

## The Ten Universal Commandments

1. Thou shalt ALWAYS use MCP tools before coding
2. Thou shalt NEVER assume; always question
3. Thou shalt write code that's clear and obvious
4. Thou shalt be BRUTALLY HONEST in assessments
5. Thou shalt PRESERVE CONTEXT, not delete it
6. Thou shalt make atomic, descriptive commits
7. Thou shalt document the WHY, not just the WHAT
8. Thou shalt test before declaring done
9. Thou shalt handle errors explicitly
10. Thou shalt treat user data as sacred

## Final Reminders

- Codebase > Documentation > Training data (in order of truth)
- Research current docs, don't trust outdated knowledge
- Ask questions early and often

---

# Project Context: Aerith Hyperliquid Trading Bot

## Critical System State (January 2025)

**LEGACY SYSTEM (FROZEN)**: 180 trades, +215% ROI for 2024 ✅  
**MODERN SYSTEM (BROKEN)**: 0-957 trades, massive losses ❌  

### The Two-Path Architecture

```
LEGACY PATH (IMMUTABLE)
├── granular_microstructure detector
├── raw2/ hourly files (37 features)
├── tf_v2 strategy
└── WORKS PERFECTLY - DO NOT TOUCH

MODERN PATH (EXPERIMENTAL)  
├── continuous_gms detector
├── features_1s/ files (1-second data)
├── tf_v3 strategy with enhancements
└── BROKEN - needs complete rebuild
```

## Core Files & Responsibilities

```
hyperliquid_bot/
├── core/
│   ├── detector.py              # RegimeDetectorInterface + factory
│   ├── gms_detector.py          # ContinuousGMSDetector (modern)
│   └── unified_gms_detector.py  # Unified implementation (complex)
├── data/
│   ├── handler.py               # HistoricalDataHandler
│   └── simple_loader.py         # SimpleDataLoader (compatibility layer)
├── strategies/
│   ├── tf_v2.py                 # Legacy strategy (FROZEN)
│   └── tf_v3.py                 # Modern strategy (needs work)
├── utils/
│   └── adaptive_threshold.py    # BOTTLENECK: 655s runtime (99% of backtest time)
└── backtester/
    └── run_backtest.py          # Entry point
```

## Data Architecture

```
hyperliquid_data/
├── raw2/                        # LEGACY: Hourly aggregated (WORKING)
│   └── YYYYMMDD_raw2.parquet    # 37 features, field: 'imbalance'
├── features_1s/                 # MODERN: 1-second features (BROKEN)
│   └── YYYY-MM-DD/features_HH.parquet  # Field: 'volume_imbalance'
└── resampled_l2/                # OHLCV data (shared)
    └── 1h/YYYY-MM-DD_1h.parquet
```

## Critical Configuration

```yaml
# Working legacy configuration
regime:
  detector_type: "granular_microstructure"  # LEGACY
strategies:
  use_tf_v2: true                           # LEGACY

# Broken modern configuration  
regime:
  detector_type: "continuous_gms"           # MODERN
strategies:
  use_tf_v3: true                           # MODERN
```

## Essential Commands

```bash
# Legacy system (WORKING)
python3 -m hyperliquid_bot.backtester.run_backtest

# Modern system (BROKEN)
python3 -m hyperliquid_bot.backtester.run_backtest --override configs/overrides/test_agressive_trades.yaml

# Data processing
python3 -m tools.etl_l20_to_1s --raw-dir /path/to/l2_raw/YYYY-MM-DD/ --out-dir /path/to/features_1s/
```

## Key Classes & Critical Methods

```python
# Regime Detection Factory
def get_regime_detector(config, feature_store=None) -> RegimeDetectorInterface:
    """Creates detector based on config.regime.detector_type"""

# Modern Detector (BROKEN)
class ContinuousGMSDetector:
    def detect_regime(self, data, timestamp) -> str:
        """Returns: BULL, BEAR, CHOP, etc."""
    def get_raw_state(self, features, timestamp) -> Dict:
        """8-state classification + risk_suppressed flag"""

# Data Loading (Compatibility Issues)
class HistoricalDataHandler:
    def load_data(self, start_date, end_date) -> pd.DataFrame:
        """Loads OHLCV + features - schema conflicts between legacy/modern"""

# Strategy Evaluation
class StrategyEvaluator:
    def evaluate(self, data, timestamp) -> Dict:
        """Orchestrates regime detection + strategy signals"""
```

## Critical Issues & Constraints

### Performance Bottleneck
- `adaptive_threshold.py::_prime_adaptive_thresholds()` = 655s (99% of runtime)
- Target: 100× speedup via vectorization or Cython
- Modern system is 13.6× slower than legacy

### Data Quality Issues
- Experimental 2024 data shows severe problems
- TF-v3 generates 0 trades or 957 losing trades
- Schema incompatibility between legacy/modern paths

### Architecture Problems
- Over-engineered factory patterns
- Complex compatibility layers
- Multiple ways to do the same thing
- UnifiedGMSDetector trying to handle too many modes

## Development Rules

### NEVER TOUCH LEGACY SYSTEM
- `granular_microstructure` detector is FROZEN
- `tf_v2` strategy is FROZEN  
- `raw2/` data files are IMMUTABLE
- Any change must preserve 180 trades, +215% ROI exactly

### Modern System Rebuild Strategy
1. **Start Simple**: Replicate legacy performance with modern stack
2. **One Change**: Add ONE enhancement at a time
3. **Validate Always**: Backtest after every change
4. **Revert Fast**: If performance degrades, revert immediately

### Data Handling
- Legacy uses `imbalance` field ([-1, 1] range)
- Modern uses `volume_imbalance` field (same range, different name)
- Never mutate files under `raw2/` or `resampled_l2/`
- Modern ETL outputs to `features_1s/`

## Regime States

```python
# 8-state raw classification
RAW_STATES = [
    "BULL", "BEAR", "CHOP", "BULL_VOLATILE", 
    "BEAR_VOLATILE", "WEAK_BULL", "WEAK_BEAR", "NEUTRAL"
]

# 3-state mapping (when gms_use_three_state_mapping: true)
THREE_STATE_MAPPING = {
    "BULL": "BULL", "WEAK_BULL": "BULL",
    "BEAR": "BEAR", "WEAK_BEAR": "BEAR", 
    "CHOP": "CHOP", "NEUTRAL": "CHOP",
    "BULL_VOLATILE": "BULL", "BEAR_VOLATILE": "BEAR"
}
```

## Immediate Priorities

1. **Fix Modern System**: Make continuous_gms + tf_v3 match legacy baseline
2. **Optimize Bottleneck**: Vectorize `adaptive_threshold.py` for 100× speedup
3. **Clean Architecture**: Remove factory patterns and compatibility layers
4. **Data Quality**: Investigate why experimental data generates 0 trades

## Warning Signs

- If backtest generates 0 trades → data loading issue
- If backtest generates 900+ trades → threshold/filtering issue  
- If runtime > 60s → adaptive_threshold bottleneck
- If ROI < 100% → strategy logic broken

Remember: **Better decisions, not faster ones**. This is a retail system optimized for intelligent execution under 600ms latency constraints.