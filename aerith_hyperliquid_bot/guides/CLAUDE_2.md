# CLAUDE.md – Aerith Hyperliquid Bot Development Guide

> **Purpose**  This document is the single source‑of‑truth for how we, as a *pair‑programming team*, build and maintain the Aerith Hyperliquid dual‑path trading bot.  It combines our general engineering playbook (hooks, workflow, communication) **and** all project‑specific guard‑rails (legacy/modern system boundaries, retail constraints, performance bottlenecks).
>
> **Read‑me cadence**  If you haven’t re‑opened this file in the last **30 minutes**, stop and skim it again.  Treat it as living documentation.

---

## 1  Project Snapshot

| Path       | Status            | Detector → Strategy                 | Data Feed                  | 2024/25 Perf                          |
| ---------- | ----------------- | ----------------------------------- | -------------------------- | ------------------------------------- |
| **Legacy** | **FROZEN** (Prod) | `granular_microstructure` → `tf_v2` | `raw2/` → hourly parquet   | **ROI 215 %**, Sharpe 4.36, DD 8.98 % |
| **Modern** | **EXPERIMENTAL**  | `continuous_gms` → `tf_v3`          | `l2_raw/` → `features_1s/` | 22‑day test only *(stats N/A)*        |

* The **Legacy path is immutable**.  All PRs **must** preserve bit‑for‑bit back‑test parity for 2024.
* The **Modern path is open** to optimization — goal is to close the 13.6× runtime gap and match‑plus legacy metrics.

---

## 2  Blocking Gate – Automated Hooks

Run **`make fmt && make lint && make test`** before every commit.  **All hooks must be ✅ GREEN.**  On any ❌ failure:

1. **Stop immediately**
2. **Fix every item**
3. Re‑run the failing command until green
4. Resume original work

Zero tolerance for TODOs, lint warnings, or formatting drift.

---

## 3  Retail‑Friendly Workflow

### Research → Plan → Implement

1. **Research**  Examine existing modules, patterns, and data.
2. **Plan**  Write a bulletproof plan *and show me* before touching code.
3. **Implement**  Code the plan with incremental commits + checkpoints.

> When starting a feature: *“Let me research the codebase and draft a plan before implementing.”*

### Parallel Agents

Use sub‑agents to break work:

* e.g. one agent profiles `adaptive_threshold.py` while another drafts vectorized refactor.
* Announce: *“I’ll spawn agents to tackle benchmark + refactor in parallel.”*

### Reality Checkpoints

Pause and validate:

* After finishing a feature
* Before large refactors
* When hooks fail
* When something feels off

---

## 4  Language & Code‑Style Rules (Python 3.11)

### Forbidden Patterns (autolint‑enforced)

* **`time.sleep()` in prod code** — use asyncio or scheduler
* Global mutable state
* Catch‑all `except:` blocks
* Implicit relative imports
* Magic numbers – promote to `ALL_CAPS` constants

### Required Practices

* *Early returns* to flatten nesting
* **Type hints everywhere** — checked with `mypy` strict
* Constructors return concrete types: `def new_risk_manager() -> RiskManager:`
* Table‑driven tests for branching logic
* Use `pytest‑bench` for hot‑path benchmarks

### Docstrings & Comments

* Google‑style docstrings on every public symbol
* Mark immutable code with `# LEGACY SYSTEM (FROZEN)`
* Mark hotspots with `# BOTTLENECK – profile before editing`

---

## 5  Legacy vs Modern Guard‑Rails

| Area            | Constraint                                                                                                  |
| --------------- | ----------------------------------------------------------------------------------------------------------- |
| **Legacy code** | **No edits** unless they 100 % preserve historical metrics & interface.  Add new functionality *around* it. |
| **Modern code** | Free to change, but **must not break** legacy import paths or config schema.                                |
| **Configs**     | `base.yaml` is canonical.  Override files must merge cleanly via `deepmerge`.                               |
| **Data**        | Do **not** mutate files under `raw2/` or `resampled_l2/`.  Modern ETL outputs to `features_1s/`.            |

---

## 6  Performance Charter

* ***Primary bottleneck***: `adaptive_threshold.py::_prime_adaptive_thresholds` → 99 % of modern back‑test runtime.  Target **100× speed‑up** (vectorize or Cython).
* Add a **`scripts/profile_.py`** when optimising; commit profiler JSON + before/after stats.
* Benchmark with representative 3‑day slice to shorten CI time.

---

## 7  Risk & Resilience (Retail Constraints)

* **Latency budget**: ≥ 500 ms round‑trip.  Strategies must remain profitable under that.
* **Compute budget**: single 8‑core VM, ≤ 4 GB RAM per process.
* **API rate limits**: 120 req/min burst‑safe.

Back‑tests and planning must assume the above.

---

## 8  Working‑Memory Aids

* Maintain **`PROGRESS.md`** when history > 1k lines.
* Keep **`TODO.md`** sections:

  ```
  ## Current Task
  - [ ] …

  ## Completed
  - [x] …

  ## Next Steps
  - [ ] …
  ```
* Summarize long Slack or PR threads here.

---

## 9  Implementation Definition of Done

1. Hooks pass cleanly<br>
2. All tests green and **≥ 80 % coverage** for new modules<br>
3. Feature works E2E (back‑test + at least one live‑paper trade)<br>
4. No dead code; legacy untouched<br>
5. Docs & example commands updated

---

## 10  Communication Cheatsheet

* **Progress pings** (short, present‑tense):
  *“✓ vectorized threshold calc (tests passing) / ✗ CI still 60 s slower – investigating.”*
* **Architectural forks**: *“I see two approaches: A vectorize; B memoize.  Preference?”*
* **Ultrathink moments**: *“Let me ultrathink the caching layer before coding.”*

---

## 11  Command Syntax Cheat‑Sheet

### Legacy Path (FROZEN)

```bash
python3 -m hyperliquid_bot.backtester.run_backtest \
  --override configs/overrides/legacy_regression_2024.yaml
```

### Modern Path (EXPERIMENTAL)

```bash
python3 -m hyperliquid_bot.backtester.run_backtest \
  --override configs/overrides/test_agressive_trades.yaml
```

### Common CLI Flags

| Flag                         | Purpose                                       | Example                                                    |
| ---------------------------- | --------------------------------------------- | ---------------------------------------------------------- |
| `--override PATH`            | YAML override file (modern **requires** this) | `--override configs/overrides/tf_v3_backtest_2025_03.yaml` |
| `--timeframe {1h,4h}`        | Timeframe override                            | `--timeframe 4h`                                           |
| `--run-id ID`                | Tag logs / results                            | `--run-id "profile_run"`                                   |
| `--skip-validation-warnings` | Ignore non‑fatal schema warnings              | `--skip-validation-warnings`                               |

> **Why it matters**  Running the right command saves tokens, CI minutes, and your time.

---

## 12  Directory Structure (Reference)

```text
/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/
├── raw2/                        # LEGACY SYSTEM (FROZEN)
│   ├── 20240101_raw2.parquet    # Daily aggregated files (37 features, 5-depth)
│   ├── 20240102_raw2.parquet    # Used by granular_microstructure detector
│   └── ...                      # Status: Production data, do not modify
│
├── resampled_l2/                # LEGACY SYSTEM (FROZEN)
│   ├── 1h/                      # Hourly OHLCV timeframe
│   │   ├── 2024-01-01_1h.parquet # Daily OHLCV files with microstructure features
│   │   ├── 2024-01-02_1h.parquet # Columns: open,high,low,close,log_ret,realised_vol,
│   │   └── ...                   #          bid_slope,ask_slope,book_asymmetry
│   └── 4h/                      # 4-hour timeframe
│       └── ...
│
├── l2_raw/                      # MODERN SYSTEM (Experimental)
│   ├── 2025-03-01/              # Date-based directory structure
│   │   ├── BTC_00_l2Book.arrow  # Hourly L2 data (00:00-00:59)
│   │   ├── BTC_01_l2Book.arrow  # Hourly L2 data (01:00-01:59)
│   │   └── ...                  # 24 files per day
│   └── ...                      # Status: 2025 data only, limited coverage
│
└── features_1s/                 # MODERN SYSTEM (Experimental)
    ├── 2025-03-01/              # Date-based directory structure
    │   ├── features_20250301_00.parquet # Hourly 1s features (109 features, 5-20 depth)
    │   ├── features_20250301_01.parquet # Used by continuous_gms detector
    │   └── ...                          # 24 files per day
    └── ...                              # Status: 2025 data only, performance bottleneck
```

---

## 13  System Architecture Diagrams

### Legacy System Flow (FROZEN)

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Legacy Data    │     │  Legacy Loading │     │ Legacy Features │
│  - raw2/        │────▶│  DataHandler    │────▶│ microstructure  │
│  - resampled_l2/│     │  (hourly)       │     │ (37 features)   │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Trade Execution│     │  Risk & Position│     │ Legacy Detection│
│  Backtester     │◀────│  RiskManager    │◀────│ granular_micro  │
│  Portfolio      │     │  PositionManager│     │ (FROZEN)        │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
        ▲                                                 │
        │                                                 ▼
        │               ┌─────────────────┐     ┌─────────────────┐
        │               │  Configuration  │     │ Legacy Strategy │
        └───────────────│  base.yaml      │◀────│ tf_v2 (FROZEN)  │
                        │  (FROZEN)       │     │ 202% ROI        │
                        └─────────────────┘     └─────────────────┘
```

### Modern System Flow (Under Optimization)

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Modern Data    │     │  Modern Loading │     │ Modern Features │
│  - l2_raw/      │────▶│  ETL Pipeline   │────▶│ features_1s     │
│  - features_1s/ │     │  (1-second)     │     │ (109 features)  │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Trade Execution│     │  Risk & Position│     │ Modern Detection│
│  Backtester     │◀────│  RiskManager    │◀────│ continuous_gms  │
│  Portfolio      │     │  PositionManager│     │ (BOTTLENECK)    │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
        ▲                                                 │
        │                                                 ▼
        │               ┌─────────────────┐     ┌─────────────────┐
        │               │  Configuration  │     │ Modern Strategy │
        └───────────────│  base.yaml      │◀────│ tf_v3 (EXPER.)  │
                        │  (EXPERIMENTAL) │     │ 22% ROI (21d)   │
                        └─────────────────┘     └─────────────────┘
```

---

## 14  Need Help?

If you’re blocked > 25 minutes:

1. Post a concise status + obstacle.
2. Suggest two fallback ideas.
3. Tag me; we decide live.

---

*Happy coding — and remember, green hooks or it doesn’t ship!*

If you’re blocked > 25 minutes:

1. Post a concise status + obstacle.
2. Suggest two fallback ideas.
3. Tag me; we decide live.

---

*Happy coding — and remember, green hooks or it doesn’t ship!*
