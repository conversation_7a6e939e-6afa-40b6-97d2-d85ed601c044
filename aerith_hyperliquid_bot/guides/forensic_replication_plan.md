# Forensic Replication Plan: Legacy to Modern

## Overview
The legacy system achieves 184 trades/year with +203% ROI. Our modern attempt failed with only +6.22% ROI. This plan outlines a systematic approach to replicate the legacy system exactly before enhancing it with 60-second regime updates.

## Phase 1: Forensic Analysis (3-4 days)

### 1.1 Build LegacySystemAuditor
Create minimal logging infrastructure to capture every decision point:

```python
# hyperliquid_bot/auditing/legacy_auditor.py
class LegacySystemAuditor:
    def log_event(self, event_type: str, data: dict):
        """Log structured events for analysis"""
        pass
```

### 1.2 Critical Logging Points
Add logging at these exact locations:

1. **Regime Detection** (`legacy/detector.py:detect_regime`)
   - Input signals
   - Calculated metrics (momentum, volatility, OBI)
   - Final regime decision
   - Why other regimes were rejected

2. **Strategy Evaluation** (`legacy/strategy.py:evaluate`)
   - EMA values and crossover status
   - Forecast value
   - Base signal (long/short/none)
   - Filter results (OBI, funding if enabled)
   - Final decision

3. **Position Check** (`portfolio/portfolio.py`)
   - Current position status
   - Why new positions were blocked

4. **Exit Decisions** (`backtester/backtester.py:_evaluate_exit`)
   - SL/TP levels
   - Current price vs levels
   - Time held
   - Exit trigger

### 1.3 Generate Golden Truth Log
Run legacy system for Q1 2024 (3 months) with full logging:
```bash
python3 -m hyperliquid_bot.backtester.run_backtest --start 2024-01-01 --end 2024-03-31
```

Expected output: ~45 trades with detailed decision logs

## Phase 2: Direct Replication (4-5 days)

### 2.1 Port Missing Components to Modern

#### Position Management
```python
# In RobustBacktestEngine
if len(self.positions) >= 1:
    logger.info("Skipping entry: Position limit reached")
    continue
```

#### Exit Logic
Port the exact exit evaluation from legacy backtester:
- Stop loss checks using low/high within bar
- Take profit checks using high/low within bar
- Max hold time checks
- Market exit at close price

#### Risk Sizing
```python
# In ModernTFV3Strategy
def get_position_size(self):
    return 0.25  # 25% risk per trade, NOT 2%
```

### 2.2 Fix Configuration Mismatches

#### Momentum Thresholds
```yaml
# Legacy values (DO NOT CHANGE)
gms_mom_weak_thresh: 50.0
gms_mom_strong_thresh: 100.0
```

#### Allowed Regimes
```python
# Only allow Strong trends
allowed_states = ['Strong_Bull_Trend', 'Strong_Bear_Trend']
```

#### Remove Quality Scoring
Disable the enhanced detector's quality filter entirely for pure replication.

### 2.3 Data Alignment
Ensure modern system loads the SAME data as legacy:
- Use raw2/ hourly files
- Calculate same indicators with same parameters
- No enhanced features yet

## Phase 3: Verification (2 days)

### 3.1 Side-by-Side Comparison
Run both systems on Q1 2024:
```bash
# Legacy
python3 -m hyperliquid_bot.backtester.run_backtest

# Modern Replica
python3 run_modern_replica_backtest.py
```

### 3.2 Trade-by-Trade Validation
Compare audit logs to verify:
- Same entry timestamps (±1 hour tolerance)
- Same direction (long/short)
- Same exit reasons
- Similar P&L (±5% tolerance)

### 3.3 Success Criteria
- Trade count: 45 ±5 trades
- ROI: ~50% (Q1 portion of annual 203%)
- Trade matching: >90% overlap

## Phase 4: Architecture for Enhancement (Future)

### 4.1 Regime State Manager
Once replication verified, add 60s regime tracking:
```python
class RegimeStateManager:
    def update_60s(self, microstructure_data):
        # Track regime at 60s intervals
        pass
    
    def get_current_regime(self):
        # Return latest 60s regime
        pass
```

### 4.2 Entry Refinement
Keep hourly decisions but use 60s state:
```python
# Every hour
if hourly_signal and regime_manager.get_current_regime() in allowed_states:
    enter_position()
```

## Key Success Factors

1. **No Enhancements Yet**: Pure replication first
2. **Exact Logic Match**: Don't "improve" anything
3. **25% Risk**: This is critical for matching returns
4. **One Position**: Never allow multiple positions
5. **Strong Regimes Only**: No Weak trends

## Hidden Rules to Discover

Through forensic analysis, we expect to find:
1. Exact confidence thresholds (if any)
2. Hidden time-based filters
3. Subtle regime persistence requirements
4. Edge cases in exit logic

## Timeline

- Week 1: Forensic analysis + auditor implementation
- Week 2: Port components + verification
- Week 3: Enhancement architecture (only after verification)

## Success Metrics

Replica matches legacy:
- Trades: 180-190 per year
- ROI: 200-220%
- Sharpe: ~4.0
- Same trade dates (>90% overlap)