# Aerith Hyperliquid Bot - Complete Project Overview

## Executive Summary

The Aerith Hyperliquid Bot is a **dual-path trading system** designed for Hyperliquid perpetual futures, featuring both a proven legacy system and an experimental modern system.

### 🏆 **Legacy System (FROZEN - Production Ready)**
- **Architecture**: `granular_microstructure` detector + `tf_v2` strategy
- **Data Pipeline**: `raw2/` hourly parquet files → microstructure features → 1h/4h OHLC
- **Performance (2024 Full Year)**:
  - **ROI**: 215.41% (3.15x initial capital)
  - **Sharpe Ratio**: 4.36 (exceptional risk-adjusted returns)
  - **Max Drawdown**: 8.98% (low risk profile)
  - **Trade Count**: 180 trades (selective execution)
  - **Profit Factor**: 2.26
- **Status**: ✅ **FROZEN** - No changes allowed, all development must maintain backward compatibility
- **Runtime**: ~49 seconds for full backtests
- **Command**: `python3 -m hyperliquid_bot.backtester.run_backtest`

### 🔬 **Modern System (Experimental - Under Development)**
- **Architecture**: `continuous_gms` detector + `tf_v3` strategy
- **Data Pipeline**: `l2_raw/` Arrow files → `features_1s/` parquet → 1-second features
- **Performance (2025-03-02 to 2025-03-22, 22 days)**:
  - **Strategy Configuration**: Intentionally loose/aggressive to generate trades for testing
  - **Sample Size**: Limited to 22 days (insufficient for statistical significance)
  - **Note**: Performance metrics not representative of optimized strategy
- **Status**: 🔬 **Under optimization** - Working but needs performance tuning
- **Runtime**: ~663 seconds (13.6x slower than legacy, major bottleneck identified)
- **Command**: `python3 -m hyperliquid_bot.backtester.run_backtest --override configs/overrides/test_agressive_trades.yaml`

### 📊 **Current Performance Metrics**

**Legacy System (2024 Full Year)**:
```
                               IMPORTANT METRICS
────────────────────────────────────────────────────────────────────────────────
  • Sharpe Ratio (Daily):               4.36
  • Profit Factor:                      2.26
  • Max Drawdown:                      8.98%
  • Return on Initial (ROI):          215.41%
  • Trade Count:                         180
```

**Modern System (22-day test period)**:
- Uses intentionally loose/aggressive strategy configuration
- Limited sample size insufficient for statistical significance
- Performance metrics not representative of optimized strategy
- Primary goal: Validate system functionality with sufficient trade volume

### 🎯 **Development Philosophy**
1. **Legacy System**: Absolutely frozen, no modifications allowed
2. **Modern System**: Experimental optimization target - goal is to match or exceed legacy performance
3. **Backward Compatibility**: All changes must preserve legacy system functionality
4. **Data Resampling Goal**: Extend modern system testing to full 2024 dataset

## 1. Core File Structure

```
/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/
├── configs/
│   ├── base.yaml                    # Primary configuration file
│   ├── gms_state_mapping.yaml       # Regime state mapping configuration
│   ├── gms_state_mapping_modern.yaml # Modern system state mapping
│   └── overrides/                   # Environment-specific overrides
│       ├── legacy_regression_2024.yaml # Legacy system full year test
│       ├── profile_modern_system.yaml  # Modern system profiling
│       └── tf_v3_backtest_2025_03.yaml # TF-v3 testing configuration
├── hyperliquid_bot/
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py              # Pydantic models for config validation
│   │   └── scheduler_settings.py    # ETL scheduler configuration
│   ├── core/
│   │   ├── __init__.py              # Exports key interfaces and factory functions
│   │   ├── detector.py              # GranularMicrostructureRegimeDetector (LEGACY)
│   │   ├── gms_detector.py          # ContinuousGMSDetector (MODERN)
│   │   ├── unified_gms_detector.py  # UnifiedGMSDetector (supports both modes)
│   │   ├── risk.py                  # RiskManager for position sizing
│   │   └── risk_interface.py        # Risk management interfaces
│   ├── data/
│   │   ├── __init__.py
│   │   ├── handler.py               # HistoricalDataHandler for data loading
│   │   ├── feature_store.py         # FeatureStore for standardized column access
│   │   └── providers/               # External data providers
│   │       └── feargreed.py         # Fear & Greed index provider
│   ├── execution/
│   │   ├── __init__.py
│   │   └── simulation.py            # Trade execution simulation
│   ├── features/
│   │   ├── __init__.py
│   │   ├── microstructure.py        # Legacy 37-feature calculation (5-depth)
│   │   ├── builder_registry.py      # Feature builder registry
│   │   ├── indicators.py            # Technical indicators
│   │   ├── statistical.py          # Statistical features
│   │   └── ta_utils.py              # Technical analysis utilities
│   ├── portfolio/
│   │   ├── __init__.py
│   │   └── portfolio.py             # Portfolio management
│   ├── providers/
│   │   ├── __init__.py
│   │   └── feargreed.py             # Fear & Greed data provider
│   ├── signals/
│   │   ├── __init__.py
│   │   ├── calculator.py            # SignalEngine for technical indicators
│   │   ├── depth_metrics_calculator.py # Depth metrics calculation
│   │   └── obi_zscore_calculator.py # OBI z-score calculation
│   ├── strategies/
│   │   ├── __init__.py
│   │   ├── evaluator.py             # StrategyEvaluator orchestration
│   │   ├── tf_v3.py                 # Modern trend following strategy (TF-v3)
│   │   ├── obi_scalper.py           # OBI-based scalping strategy
│   │   ├── obi_scalper_strategy.py  # OBI scalper implementation
│   │   ├── strategy_factory.py      # Strategy factory
│   │   └── exceptions.py            # Strategy exceptions
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── state_mapping.py         # Regime state standardization
│   │   ├── adaptive_threshold.py    # Adaptive threshold calculation (BOTTLENECK)
│   │   ├── feature_naming.py        # Depth-agnostic column naming
│   │   ├── data_utils.py            # Data utility functions
│   │   ├── time.py                  # Time utilities
│   │   ├── system_validation.py     # System validation utilities
│   │   └── regime_analytics.py      # Regime analysis tools
│   └── backtester/
│       ├── __init__.py
│       ├── backtester.py            # Main Backtester class
│       ├── run_backtest.py          # Entry point for backtesting
│       ├── debug_backtester.py      # Debug backtester
│       └── mock_backtester.py       # Mock backtester for testing
├── tools/
│   └── etl_l20_to_1s.py             # Modern 109-feature ETL (5-20 depth)
├── scripts/                         # Analysis and utility scripts
├── services/
│   └── etl_scheduler.py             # ETL scheduling service
├── tests/                           # Test suite
├── docs/                            # Documentation
├── guides/                          # User guides and overviews
└── logs/                            # Directory for log outputs
```

## 2. Key Class Definitions

### RegimeDetectorInterface

```python
class RegimeDetectorInterface(ABC):
    """Base interface for all regime detectors."""
    
    @abstractmethod
    def detect_regime(self, data: pd.DataFrame, timestamp: pd.Timestamp) -> str:
        """
        Detect the current market regime.
        
        Args:
            data: DataFrame containing market data
            timestamp: Current timestamp
            
        Returns:
            str: Regime state (e.g., 'BULL', 'BEAR', 'CHOP')
        """
        pass
```

### GranularMicrostructureRegimeDetector (Legacy - FROZEN)

```python
class GranularMicrostructureRegimeDetector(RegimeDetectorInterface):
    """
    Legacy regime detector using 37 microstructure features from hourly data.

    Key features:
    - Processes hourly aggregated data from raw2/ parquet files
    - Uses 5-depth order book features
    - Proven performance: 202% ROI, 3.99 Sharpe ratio (2024)
    - Status: FROZEN - no modifications allowed

    Args:
        config: Configuration object
    """

    def __init__(self, config: Config):
        # Legacy implementation - DO NOT MODIFY

    def detect_regime(self, data: pd.DataFrame, timestamp: pd.Timestamp) -> str:
        """Returns standardized regime state using legacy logic."""
```

### ContinuousGMSDetector (Modern - Under Optimization)

```python
class ContinuousGMSDetector(RegimeDetectorInterface):
    """
    Modern regime detector using 1-second microstructure features.

    Key features:
    - Processes 1-second feature data from features_1s/ parquet files
    - Recomputes every config.gms.cadence_sec seconds (default 60)
    - Accepts arbitrary depth (5 or 20) via config.microstructure.obi_levels
    - Outputs raw 8 states plus risk_suppressed boolean
    - Supports adaptive thresholds (major performance bottleneck - 655s/99% runtime)
    - Status: Under optimization

    Args:
        config: Configuration object
        feature_store: Optional FeatureStore for standardized column access
    """

    def __init__(self, config: Config, feature_store: Optional[FeatureStore] = None):
        # Modern implementation details

    def detect_regime(self, data: pd.DataFrame, timestamp: pd.Timestamp) -> str:
        """Returns standardized regime state."""

    def get_raw_state(self, features: pd.DataFrame, timestamp: pd.Timestamp) -> Dict[str, Any]:
        """Returns raw 8-state classification with risk_suppressed flag."""

    def is_risk_suppressed(self, features: pd.DataFrame, timestamp: pd.Timestamp) -> bool:
        """Determines if risk should be suppressed based on position and volatility."""
```

### UnifiedGMSDetector

```python
class UnifiedGMSDetector(RegimeDetectorInterface):
    """
    Unified detector supporting both legacy and modern modes.

    Modes:
    - 'legacy': Uses GranularMicrostructureRegimeDetector logic
    - 'continuous': Uses ContinuousGMSDetector logic

    Args:
        config: Configuration object
    """

    def __init__(self, config: Config):
        self.detector_mode = self._resolve_detector_mode(config)
        # Mode-specific initialization

    def detect_regime(self, data: pd.DataFrame, timestamp: pd.Timestamp) -> str:
        """Delegates to appropriate detector based on mode."""
```

### HistoricalDataHandler

```python
class HistoricalDataHandler:
    """
    Handles loading and processing of historical data.
    
    Args:
        config: Configuration object
        
    Methods:
        load_data(start_date, end_date) -> pd.DataFrame: Load OHLCV and feature data
        get_ohlcv(start_date, end_date) -> pd.DataFrame: Get OHLCV data only
        get_features(start_date, end_date) -> pd.DataFrame: Get microstructure features
    """
    
    def __init__(self, config: Config):
        # Implementation details
        
    def load_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Load OHLCV and feature data for the specified date range."""
        
    def get_ohlcv(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Get OHLCV data for the specified date range."""
        
    def get_features(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Get microstructure features for the specified date range."""
```

### SignalEngine

```python
class SignalEngine:
    """
    Calculates technical indicators and trading signals.
    
    Args:
        config: Configuration object
        
    Methods:
        calculate_all_signals(data) -> pd.DataFrame: Calculate all configured signals
        calculate_ema(data, period) -> pd.Series: Calculate EMA for specified period
        calculate_atr(data, period) -> pd.Series: Calculate ATR for specified period
        calculate_obi(data, smoothing) -> pd.Series: Calculate OBI with smoothing
    """
    
    def __init__(self, config: Config):
        # Implementation details
        
    def calculate_all_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate all configured signals and add them to the data."""
        
    def calculate_ema(self, data: pd.DataFrame, period: int) -> pd.Series:
        """Calculate EMA for the specified period."""
        
    def calculate_atr(self, data: pd.DataFrame, period: int) -> pd.Series:
        """Calculate ATR for the specified period."""
        
    def calculate_obi(self, data: pd.DataFrame, smoothing: int) -> pd.Series:
        """Calculate Order Book Imbalance with smoothing."""
```

### StrategyEvaluator

```python
class StrategyEvaluator:
    """
    Orchestrates strategy evaluation and signal generation.
    
    Args:
        config: Configuration object
        signal_engine: SignalEngine instance
        regime_detector: RegimeDetectorInterface instance
        
    Methods:
        evaluate(data, timestamp) -> Dict: Evaluate all active strategies
        get_position_signal(data, timestamp) -> int: Get position signal (-1, 0, 1)
        should_enter_trade(data, timestamp, direction) -> bool: Check entry conditions
        should_exit_trade(data, timestamp, position) -> bool: Check exit conditions
    """
    
    def __init__(self, config: Config, signal_engine: SignalEngine, 
                 regime_detector: RegimeDetectorInterface):
        # Implementation details
        
    def evaluate(self, data: pd.DataFrame, timestamp: pd.Timestamp) -> Dict[str, Any]:
        """Evaluate all active strategies and return combined signals."""
        
    def get_position_signal(self, data: pd.DataFrame, timestamp: pd.Timestamp) -> int:
        """Get position signal: -1 (short), 0 (neutral), 1 (long)."""
        
    def should_enter_trade(self, data: pd.DataFrame, timestamp: pd.Timestamp, 
                           direction: int) -> bool:
        """Check if entry conditions are met for the specified direction."""
        
    def should_exit_trade(self, data: pd.DataFrame, timestamp: pd.Timestamp, 
                          position: Dict[str, Any]) -> bool:
        """Check if exit conditions are met for the current position."""
```

### TrendFollowingStrategy (TF-v2 - Legacy - FROZEN)

```python
class TrendFollowingStrategy(StrategyInterface):
    """
    Legacy trend following strategy with proven performance.

    Key features:
    - Works with granular_microstructure detector
    - Uses hourly OHLC data with microstructure features
    - Proven performance: 202% ROI, 3.99 Sharpe (2024)
    - Status: FROZEN - no modifications allowed

    Args:
        config: Configuration object
        strategy_name: Strategy identifier
    """

    def __init__(self, config: Config, strategy_name: str):
        # Legacy implementation - DO NOT MODIFY

    def evaluate(self, signals: Dict) -> Tuple[Optional[Literal["long", "short"]], Optional[Dict]]:
        """Legacy trend following evaluation logic."""
```

### TFV3Strategy (Modern - Under Development)

```python
class TFV3Strategy:
    """
    Modern trend following strategy with regime awareness and advanced features.

    Key features:
    - Works with continuous_gms detector
    - Uses 1-second feature data
    - Regime-gated entries with ATR trailing stops
    - Strict no-look-ahead bias (using ohlcv.shift(1))
    - Time-decay exits and execution scoring
    - Status: Under development and optimization

    Args:
        config: Configuration object
        signal_engine: SignalEngine instance
    """

    def __init__(self, config: Config, signal_engine: SignalEngine):
        # Modern implementation details

    def generate_signal(self, data: pd.DataFrame, timestamp: pd.Timestamp,
                        regime: str) -> Dict[str, Any]:
        """Generate trading signal based on current data and regime."""

    def calculate_execution_score(self, data: pd.DataFrame, timestamp: pd.Timestamp,
                                 direction: int) -> float:
        """Calculate execution quality score (0-100+)."""

    def check_tactical_alignment(self, data: pd.DataFrame, timestamp: pd.Timestamp,
                                direction: int) -> bool:
        """Check if short-term momentum aligns with signal direction."""
```

### OBIScalperStrategy

```python
class OBIScalperStrategy(StrategyInterface):
    """
    Order Book Imbalance scalping strategy.

    Key features:
    - Full entry/exit lifecycle with regime gating
    - OBI thresholds and veto conditions
    - TP/SL/timeout exits with diagnostic instrumentation
    - Configurable tick size via strategies.obi_scalper.tick_size
    - Status: Implemented but inactive in current configurations

    Args:
        config: Configuration object
        strategy_name: Strategy identifier
    """

    def __init__(self, config: Config, strategy_name: str):
        # OBI scalper implementation

    def evaluate(self, signals: Dict) -> Tuple[Optional[Literal["long", "short"]], Optional[Dict]]:
        """OBI-based scalping evaluation logic."""
```

### Backtester

```python
class Backtester:
    """
    Main backtesting engine.
    
    Args:
        config: Configuration object
        data_handler: HistoricalDataHandler instance
        strategy_evaluator: StrategyEvaluator instance
        risk_manager: RiskManager instance
        
    Methods:
        run(start_date, end_date) -> Dict: Run backtest for specified period
        get_metrics() -> Dict: Get performance metrics
        save_results(filename) -> None: Save backtest results
    """
    
    def __init__(self, config: Config, data_handler: HistoricalDataHandler,
                 strategy_evaluator: StrategyEvaluator, risk_manager: RiskManager):
        # Implementation details
        
    def run(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Run backtest for the specified period."""
        
    def get_metrics(self) -> Dict[str, float]:
        """Get performance metrics for the backtest."""
        
    def save_results(self, filename: str) -> None:
        """Save backtest results to file."""
```

## 3. Essential Configuration Fields

```yaml
# ==============================================================================
# Global Configuration
# ==============================================================================
is_backtest: true                 # Global flag indicating if running in backtest mode

# ==============================================================================
# Data & Cache Configuration
# ==============================================================================
data_paths:
  l2_data_root: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw2" # Legacy hourly files
  raw_l2_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/l2_raw" # Modern raw L2 data
  feature_1s_dir: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/features_1s" # Modern 1s features
  ohlcv_base_path: "/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled_l2" # OHLCV files
  log_dir: "/Users/<USER>/Desktop/trading_bot_/logs"
  require_ohlcv_volume: false

# ==============================================================================
# Timeframe Setting
# ==============================================================================
timeframe: "1h"                   # Or "4h" - Set the desired timeframe

# ==============================================================================
# Backtest & Simulation Configuration
# ==============================================================================
backtest:
  period_preset: '2024'           # Options: 'full', 'YYYYQX', 'YYYY', 'custom'
  custom_start_date: "2025-03-02" # Custom period start
  custom_end_date: "2025-03-22"   # Custom period end

simulation:
  latency_seconds: 0.5            # Simulated order latency
  max_impact_levels: 5            # L2 levels assumed fillable
  force_taker_execution: True     # Set to False to enable maker attempt

# ==============================================================================
# Portfolio Configuration
# ==============================================================================
portfolio:
  initial_balance: 10000          # Initial balance for backtests (USD)
  risk_per_trade: 0.02            # Risk % of balance per trade (2%)
  max_leverage: 10.0              # Global maximum leverage cap
  margin_mode: cross              # Margin mode: 'cross' or 'isolated'
  max_hold_time_hours: 24         # Maximum time to hold a position

# ==============================================================================
# Strategy Selection & Configuration
# ==============================================================================
strategies:
  # LEGACY SYSTEM (FROZEN)
  use_tf_v2: True                 # Legacy trend following (granular_microstructure)

  # MODERN SYSTEM (EXPERIMENTAL)
  use_tf_v3: False                # Modern trend following (continuous_gms)

  # OTHER STRATEGIES
  use_mean_reversion: False       # Mean reversion strategy
  use_mean_variance: False        # Mean variance strategy
  use_obi_scalper: False          # OBI scalper strategy

# ==============================================================================
# Regime Detection Configuration
# ==============================================================================
regime:
  # CRITICAL: Determines which system is used
  detector_type: "granular_microstructure"  # Options: "granular_microstructure" (LEGACY), "continuous_gms" (MODERN)

  # Legacy system settings (granular_microstructure)
  use_filter: true                # Enable regime filtering
  gms_use_three_state_mapping: true # Use 3-state mapping

  # Modern system settings (continuous_gms)
  gms_cadence_sec: 60            # Update frequency in seconds
  output_states: 8               # Number of regime states
  auto_thresholds: false         # Use adaptive thresholds (BOTTLENECK when true)

# ==============================================================================
# Microstructure Settings
# ==============================================================================
microstructure:
  obi_smoothing_window: 8         # OBI smoothing window
  obi_levels: 5                   # OBI depth levels (5 or 20)
  gms_obi_strong_confirm_thresh: 0.20 # OBI threshold for confirmation

# ==============================================================================
# TF-v3 Strategy Settings (Modern System)
# ==============================================================================
tf_v3:
  fast_ema: 8                     # Fast EMA period
  slow_ema: 128                   # Slow EMA period
  atr_period: 14                  # ATR period for volatility
  min_execution_score: 35         # Minimum execution score (0-100)
  max_wait_minutes: 5             # Maximum wait time for execution
```

## 4. Data Directory Structure & Pipeline Flow

### Legacy System Data Flow (FROZEN)
```
Raw L2 JSON → raw2/ parquet → microstructure.py → resampled_l2/ → granular_microstructure detector → tf_v2 strategy
```

### Modern System Data Flow (Under Optimization)
```
Raw L2 Arrow → ETL (etl_l20_to_1s.py) → features_1s/ → continuous_gms detector → tf_v3 strategy
```

### Directory Structure

```
/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/
├── raw2/                        # LEGACY SYSTEM (FROZEN)
│   ├── 20240101_raw2.parquet    # Daily aggregated files (37 features, 5-depth)
│   ├── 20240102_raw2.parquet    # Used by granular_microstructure detector
│   └── ...                      # Status: Production data, do not modify
│
├── resampled_l2/                # LEGACY SYSTEM (FROZEN)
│   ├── 1h/                      # Hourly OHLCV timeframe
│   │   ├── 2024-01-01_1h.parquet # Daily OHLCV files with microstructure features
│   │   ├── 2024-01-02_1h.parquet # Columns: open,high,low,close,log_ret,realised_vol,
│   │   └── ...                   #          bid_slope,ask_slope,book_asymmetry
│   └── 4h/                      # 4-hour timeframe
│       └── ...
│
├── l2_raw/                      # MODERN SYSTEM (Experimental)
│   ├── 2025-03-01/              # Date-based directory structure
│   │   ├── BTC_00_l2Book.arrow  # Hourly L2 data (00:00-00:59)
│   │   ├── BTC_01_l2Book.arrow  # Hourly L2 data (01:00-01:59)
│   │   └── ...                  # 24 files per day
│   └── ...                      # Status: 2025 data only, limited coverage
│
└── features_1s/                 # MODERN SYSTEM (Experimental)
    ├── 2025-03-01/              # Date-based directory structure
    │   ├── features_20250301_00.parquet # Hourly 1s features (109 features, 5-20 depth)
    │   ├── features_20250301_01.parquet # Used by continuous_gms detector
    │   └── ...                          # 24 files per day
    └── ...                              # Status: 2025 data only, performance bottleneck
```

### Data Coverage Status
- **Legacy System**: Full 2024 data available (365 days) ✅
- **Modern System**: Limited 2025 data (22 days: 2025-03-01 to 2025-03-22) ⚠️
- **Goal**: Resample full 2024 data for modern system testing

## 5. Critical Function Signatures

### Regime Detection

```python
# Factory function to create regime detector
def get_regime_detector(config: Config, feature_store: Optional[FeatureStore] = None) -> RegimeDetectorInterface:
    """
    Create a regime detector based on configuration.
    
    Args:
        config: Configuration object
        feature_store: Optional feature store for standardized column access
        
    Returns:
        RegimeDetectorInterface: Configured regime detector instance
    """

# Core regime detection method
def detect_regime(self, data: pd.DataFrame, timestamp: pd.Timestamp) -> str:
    """
    Detect the current market regime.
    
    Args:
        data: DataFrame containing market data
        timestamp: Current timestamp
        
    Returns:
        str: Regime state (e.g., 'BULL', 'BEAR', 'CHOP')
    """
```

### Signal Generation

```python
# Signal calculation
def calculate_all_signals(self, data: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate all configured signals and add them to the data.
    
    Args:
        data: DataFrame containing market data
        
    Returns:
        pd.DataFrame: Data with added signal columns
    """

# Strategy evaluation
def evaluate(self, data: pd.DataFrame, timestamp: pd.Timestamp) -> Dict[str, Any]:
    """
    Evaluate all active strategies and return combined signals.
    
    Args:
        data: DataFrame containing market data
        timestamp: Current timestamp
        
    Returns:
        Dict[str, Any]: Combined signals from all active strategies
    """
```

### Trade Execution

```python
# Position sizing
def calculate_position_size(self, price: float, direction: int, 
                           atr: float, balance: float) -> float:
    """
    Calculate position size based on risk parameters.
    
    Args:
        price: Current price
        direction: Trade direction (-1, 1)
        atr: Average True Range
        balance: Account balance
        
    Returns:
        float: Position size in base currency
    """

# Trade execution
def execute_trade(self, timestamp: pd.Timestamp, price: float, 
                 size: float, direction: int) -> Dict[str, Any]:
    """
    Execute a trade with the specified parameters.
    
    Args:
        timestamp: Current timestamp
        price: Execution price
        size: Position size
        direction: Trade direction (-1, 1)
        
    Returns:
        Dict[str, Any]: Trade details
    """
```

## 6. Important Constants and Enums

```python
# Regime States
class RegimeState:
    BULL = "BULL"
    BEAR = "BEAR"
    CHOP = "CHOP"
    BULL_VOLATILE = "BULL_VOLATILE"
    BEAR_VOLATILE = "BEAR_VOLATILE"
    WEAK_BULL = "WEAK_BULL"
    WEAK_BEAR = "WEAK_BEAR"
    NEUTRAL = "NEUTRAL"

# Detector Types
class DetectorType:
    GRANULAR_MICROSTRUCTURE = "granular_microstructure"
    CONTINUOUS_GMS = "continuous_gms"
    RULE_BASED = "rule_based"
    HMM = "hmm"

# Position Directions
class Direction:
    LONG = 1
    SHORT = -1
    NEUTRAL = 0

# Execution Types
class ExecutionType:
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"

# Strategy Types
class StrategyType:
    TF_V2 = "tf_v2"
    TF_V3 = "tf_v3"
    OBI_SCALPER = "obi_scalper"
    MEAN_REVERSION = "mean_reversion"
```

## 7. System Architecture Diagrams

### Legacy System Flow (FROZEN)
```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Legacy Data    │     │  Legacy Loading │     │ Legacy Features │
│  - raw2/        │────▶│  DataHandler    │────▶│ microstructure  │
│  - resampled_l2/│     │  (hourly)       │     │ (37 features)   │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Trade Execution│     │  Risk & Position│     │ Legacy Detection│
│  Backtester     │◀────│  RiskManager    │◀────│ granular_micro  │
│  Portfolio      │     │  PositionManager│     │ (FROZEN)        │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
        ▲                                                 │
        │                                                 ▼
        │               ┌─────────────────┐     ┌─────────────────┐
        │               │  Configuration  │     │ Legacy Strategy │
        └───────────────│  base.yaml      │◀────│ tf_v2 (FROZEN)  │
                        │  (FROZEN)       │     │ 202% ROI        │
                        └─────────────────┘     └─────────────────┘
```

### Modern System Flow (Under Optimization)
```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Modern Data    │     │  Modern Loading │     │ Modern Features │
│  - l2_raw/      │────▶│  ETL Pipeline   │────▶│ features_1s     │
│  - features_1s/ │     │  (1-second)     │     │ (109 features)  │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Trade Execution│     │  Risk & Position│     │ Modern Detection│
│  Backtester     │◀────│  RiskManager    │◀────│ continuous_gms  │
│  Portfolio      │     │  PositionManager│     │ (BOTTLENECK)    │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
        ▲                                                 │
        │                                                 ▼
        │               ┌─────────────────┐     ┌─────────────────┐
        │               │  Configuration  │     │ Modern Strategy │
        └───────────────│  base.yaml      │◀────│ tf_v3 (EXPER.)  │
                        │  (EXPERIMENTAL) │     │ 22% ROI (21d)   │
                        └─────────────────┘     └─────────────────┘
```

### Performance Bottleneck (Modern System)
```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ adaptive_thresh │     │ 328,926 calls   │     │ 655s runtime   │
│ _prime_adaptive │────▶│ per backtest    │────▶│ (99% of total)  │
│ _thresholds()   │     │ (BOTTLENECK)    │     │ 13.6x slower    │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## 8. Common Command-Line Operations

### Running Legacy System Backtests (FROZEN)

```bash
# Legacy system default configuration (2024 full year baseline)
python3 -m hyperliquid_bot.backtester.run_backtest

# Legacy system with specific override file
python3 -m hyperliquid_bot.backtester.run_backtest \
  --override configs/overrides/legacy_regression_2024.yaml

# Legacy system with timeframe override
python3 -m hyperliquid_bot.backtester.run_backtest --timeframe 1h

# Legacy system with run ID for logging
python3 -m hyperliquid_bot.backtester.run_backtest --run-id "legacy_baseline_test"
```

### Running Modern System Backtests (Experimental)

```bash
# Modern system with aggressive trades configuration (22-day test period)
python3 -m hyperliquid_bot.backtester.run_backtest \
  --override configs/overrides/test_agressive_trades.yaml

# Modern system with TF-v3 configuration
python3 -m hyperliquid_bot.backtester.run_backtest \
  --override configs/overrides/tf_v3_backtest_2025_03.yaml

# Modern system with profiling
python3 -m hyperliquid_bot.backtester.run_backtest \
  --override configs/overrides/profile_modern_system.yaml

# Modern system with run ID and skip validation warnings
python3 -m hyperliquid_bot.backtester.run_backtest \
  --override configs/overrides/test_agressive_trades.yaml \
  --run-id "modern_system_test" \
  --skip-validation-warnings
```

### Command Syntax Reference

```bash
# Basic syntax
python3 -m hyperliquid_bot.backtester.run_backtest [OPTIONS]

# Available options:
--override PATH          # Path to YAML override configuration file
--timeframe {1h,4h}     # Timeframe override (overrides config file)
--run-id ID             # Unique identifier for log filename
--skip-validation-warnings  # Skip validation warnings and continue

# Examples of override usage:
python3 -m hyperliquid_bot.backtester.run_backtest --override configs/overrides/filename.yaml
python3 -m hyperliquid_bot.backtester.run_backtest --timeframe 4h --run-id "test_001"
```

### Data Processing (Modern System Only)

```bash
# Process Arrow files to 1-second features (Modern system)
python3 -m tools.etl_l20_to_1s \
  --raw-dir /path/to/l2_raw/YYYY-MM-DD/ \
  --out-dir /path/to/features_1s/ \
  --date YYYY-MM-DD

# Run ETL scheduler for continuous processing
python3 -m services.etl_scheduler --config configs/base.yaml
```

### Performance Analysis and Debugging

```bash
# Profile modern system performance
python3 scripts/profile_modern_system.py

# Compare legacy vs modern system
python3 scripts/regression_test_tf_v3.py

# Analyze adaptive threshold bottleneck
python3 scripts/adaptive_threshold_analysis.py

# Generate backtest performance plots
python3 scripts/visualize_backtest.py \
  --results-file logs/backtest_trades_20250322_123456.json
```

## 9. Critical Dependencies & Constraints

### System-Level Dependencies

1. **Legacy System (FROZEN)**:
   - `granular_microstructure` detector → `tf_v2` strategy (IMMUTABLE)
   - `raw2/` parquet files → `microstructure.py` → hourly features (FROZEN)
   - All legacy components must remain backward compatible

2. **Modern System (Experimental)**:
   - `continuous_gms` detector → `tf_v3` strategy (Under development)
   - `l2_raw/` Arrow files → `etl_l20_to_1s.py` → `features_1s/` (Performance bottleneck)
   - `adaptive_threshold.py` → 655s runtime bottleneck (99% of execution time)

### Component Dependencies

3. **Data Pipeline Dependencies**:
   - Legacy: `DataHandler` → `microstructure.py` → `resampled_l2/` files
   - Modern: `DataHandler` → `FeatureStore` → `features_1s/` files
   - Both systems require proper timezone handling (UTC-naive timestamps)

4. **Strategy Dependencies**:
   - `StrategyEvaluator` → Regime detector output (state mapping critical)
   - `RiskManager` → `SignalEngine` (ATR calculations for position sizing)
   - `tf_v3` → GMS snapshot integration with staleness checks

5. **Configuration Dependencies**:
   - `detector_type` setting determines entire system path (legacy vs modern)
   - All components depend on `base.yaml` configuration
   - Override files must maintain compatibility with base configuration

6. **Performance Dependencies**:
   - Modern system: 13.6x slower than legacy (663s vs 49s)
   - Adaptive thresholds: 328,926 calls per backtest (major bottleneck)
   - Data coverage: Legacy (365 days 2024) vs Modern (22 days 2025)

## 10. Naming Conventions & Standards

### Data Files

**Legacy System (FROZEN)**:
- **Legacy Raw Files**: `YYYYMMDD_raw2.parquet` (e.g., `20240101_raw2.parquet`)
- **Resampled OHLCV Files**: `YYYY-MM-DD_1h.parquet` (e.g., `2024-01-01_1h.parquet`)

**Modern System (Experimental)**:
- **L2 Raw Arrow Files**: `BTC_HH_l2Book.arrow` (HH = 2-digit hour, e.g., `BTC_09_l2Book.arrow`)
- **1-Second Feature Files**: `features_YYYYMMDD_HH.parquet` (e.g., `features_20250301_09.parquet`)

**Output Files**:
- **Backtest Results**: `backtest_trades_YYYYMMDD_HHMMSS.json`
- **Signal Logs**: `backtest_signals_YYYYMMDD_HHMMSS.parquet`
- **Profile Reports**: `profile_modern_system_YYYYMMDD_HHMMSS.json`

### Configuration Parameters

**System Selection**:
- **Detector Types**: `"granular_microstructure"` (legacy), `"continuous_gms"` (modern)
- **Strategy Flags**: `use_tf_v2` (legacy), `use_tf_v3` (modern)

**Parameter Naming**:
- **Boolean Flags**: Use `use_` prefix (e.g., `use_cache`, `use_tf_v3`)
- **Thresholds**: Use `_thresh` suffix (e.g., `gms_obi_strong_confirm_thresh`)
- **Periods**: Use `_period` suffix for time periods (e.g., `atr_period`)
- **Windows**: Use `_window` suffix for rolling windows (e.g., `obi_smoothing_window`)
- **Directories**: Use `_dir` suffix (e.g., `log_dir`, `feature_1s_dir`)
- **Timeframes**: Use ISO format (e.g., `"1h"`, `"4h"`, `"1d"`)
- **Dates**: Use ISO format (`"YYYY-MM-DD"`)

### Code Conventions

**Classes**:
- **PascalCase**: `ContinuousGMSDetector`, `TFV3Strategy`
- **Legacy Suffix**: Classes marked as legacy should be clearly identified
- **Interfaces**: End with `Interface` (e.g., `RegimeDetectorInterface`)

**Methods/Functions**:
- **snake_case**: `detect_regime()`, `calculate_signals()`
- **Factory Functions**: Start with `get_` or `create_` (e.g., `get_regime_detector()`)
- **Legacy Methods**: Must not be modified (FROZEN)

**Variables & Constants**:
- **Variables**: snake_case (e.g., `obi_value`, `regime_state`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_POSITION_SIZE`, `DEFAULT_TIMEOUT`)
- **File Paths**: Use absolute paths in configuration

### System Status Indicators

**Code Comments**:
- `# LEGACY SYSTEM (FROZEN)` - No modifications allowed
- `# MODERN SYSTEM (EXPERIMENTAL)` - Under development
- `# BOTTLENECK` - Performance issue identified
- `# TODO: OPTIMIZATION` - Needs performance improvement
