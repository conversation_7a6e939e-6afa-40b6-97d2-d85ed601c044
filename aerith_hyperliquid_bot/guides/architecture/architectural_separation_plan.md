# Architectural Separation Plan

## Overview
This document outlines the plan to separate the legacy and modern trading systems into isolated, independently functional components while preserving the working legacy system's exact behavior.

## Current Architecture Problems

### 1. Over-Engineered Shared Components
- **UnifiedGMSDetector**: Tries to handle multiple detector types with different configurations
- **Configuration Inheritance**: Silent fallbacks cause unexpected behavior
- **Data Pipeline Complexity**: Multiple data formats and compatibility layers

### 2. Cross-Contamination Issues
- Modern system changes break legacy system
- Configuration values get mixed between systems
- Shared detector logic causes threshold confusion

### 3. Hidden Dependencies
- Risk fraction falls back to schema defaults (25% vs 2%)
- Volatility thresholds use wrong values (0.015/0.005 vs 0.0092/0.0055)
- System mode detection issues

## Three-Layer Architecture

### Layer 1: Core Infrastructure (Shared)
```
core/
├── interfaces.py          # Abstract interfaces
├── config_validator.py    # Configuration validation
└── registry.py           # Component registry
```

### Layer 2: Isolated Systems
```
legacy/
├── detector.py           # GranularMicrostructureDetector
├── strategy.py           # TFV2Strategy
└── data_loader.py        # LegacyDataLoader (raw2/)

modern/
├── detector.py           # ContinuousGMSDetector
├── strategy.py           # TFV3Strategy
└── data_loader.py        # ModernDataLoader (features_1s/)
```

### Layer 3: System Composition
```
systems/
├── legacy_system.py      # Complete legacy trading system
└── modern_system.py      # Complete modern trading system
```

## Implementation Phases

### Phase 1: Infrastructure (CURRENT)
1. ✅ Checkout clean commit (3356bed)
2. ✅ Implement configuration validator
3. 🔄 Create documentation structure
4. Define core interfaces
5. Implement component registry

### Phase 2: Legacy System Extraction
1. Extract GranularMicrostructureDetector
2. Extract TFV2Strategy
3. Create LegacyDataLoader
4. Create legacy_system.py composition
5. Validate: 180 trades, 215% ROI

### Phase 3: Modern System Isolation
1. Extract ContinuousGMSDetector
2. Extract TFV3Strategy
3. Create ModernDataLoader
4. Create modern_system.py composition
5. Fix regime gate failures

### Phase 4: System Router
1. Implement system selection logic
2. Add system mode validation
3. Create unified entry point
4. Add performance monitoring

## Key Design Principles

### 1. Complete Isolation
- No shared state between systems
- Independent configuration paths
- Separate data loaders

### 2. Explicit Configuration
- No silent fallbacks
- ❌ emoji logging for issues
- Strict validation mode available

### 3. Incremental Development
- Each phase independently testable
- Git commits after each working phase
- Revert capability at each step

### 4. Context Preservation
- Detailed logging at each step
- Progress tracking with todos
- Documentation of decisions

## Success Criteria

### Legacy System
- Exact match: 180 trades
- Exact match: 215% ROI
- No code modifications to frozen components

### Modern System
- Fix 100% regime gate failures
- Generate reasonable trade count
- Clean, maintainable architecture

### Configuration
- ✅ No configuration fallbacks detected
- All values from intended sources
- Clear error messages for issues

## Risk Mitigation

### 1. Version Control
- Branch from clean commit
- Atomic commits for each phase
- Tag working milestones

### 2. Testing Strategy
- Test after each component extraction
- Compare metrics exactly
- Regression test suite

### 3. Rollback Plan
- Each phase reversible
- Clear rollback procedures
- Preserve working baseline

## Next Steps

1. Complete documentation structure ← CURRENT
2. Define core interfaces
3. Begin legacy system extraction
4. Validate each step thoroughly