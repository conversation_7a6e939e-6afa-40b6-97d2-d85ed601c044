# Modern System Architecture Overview

## 1. Expected Pipeline

```python
# MODERN SYSTEM FLOW:
Every 60 seconds:
    Continuous GMS reads features_1s → Updates regime state
    
Every 3600 seconds (hourly):
    TF-v3 reads current regime → Generates signal if conditions met
    If signal generated → Execution refinement checks 1m data
    Execute at optimal price within 5 minutes
```

## 2. Data Loading Pipeline

### Backtesting Data Flow:
```python
# For Modern System Backtesting:
features_1s/ → Resample to 1h bars → Feed to TF-v3
             → Keep raw for GMS updates every 60s
             → Generate 1m bars for execution refinement

# Critical: THREE different uses of same data!
1. Hourly bars: Strategy signals (resampled)
2. 60s updates: GMS regime (raw features)  
3. 1m bars: Execution timing (resampled)
```

### Live Trading Data Flow:
```python
# For Modern System Live:
WebSocket → features_1s (real-time) → GMS updates every 60s
                                   → Resample to 1h for TF-v3
                                   → Buffer 1m for execution
```

## 3. Detector Behavior Clarification

### The Cadence Confusion:
```python
# IMPORTANT: Cadence ≠ Signal Frequency!

continuous_gms:
    cadence_sec: 60  # Updates regime STATE every minute
                     # Does NOT generate trades every minute!

tf_v3:
    evaluation: hourly  # Only evaluates for TRADES hourly
                       # But READS the 60s regime state
```

### When you set cadence to 3600s:
```python
# This BREAKS the architecture because:
- GMS only updates once per hour
- No benefit over legacy mode
- Loses early regime detection advantage
- Should stay at 60s for state updates
```

## 4. Backtesting vs Live Critical Difference

### Backtesting:
```python
# Must simulate the different timeframes:
for each hour in backtest:
    # Simulate 60 GMS updates
    for minute in range(60):
        gms.update(features_1s[minute])  # Update state
    
    # Hour complete - check for trade
    if minute == 59:
        signal = tf_v3.evaluate(hourly_bar, gms.current_state)
```

### Live Trading:
```python
# Natural flow with timers:
every_60_seconds():
    gms.update(latest_features)
    
every_3600_seconds():  
    signal = tf_v3.evaluate(latest_hourly, gms.current_state)
```

## Issue Analysis & Solutions

### 1. Threshold Scaling ✅
```python
# This is EXPECTED - different scales!
Legacy: Works on hourly aggregated data
Modern: Works on 1-second features

# Solution: Keep different thresholds, they're correct
legacy_vol_threshold: 0.02  # On hourly data
modern_vol_threshold: 0.0003  # On 1s data (~100x smaller)
```

### 2. Risk Fraction ⚠️
```python
# 2% vs 25% is likely the trade blocker!
risk_fraction: 0.02  # Too conservative

# Fix: Align with legacy for testing
risk_fraction: 0.25  # Match legacy initially
```

### 3. Data Format Verification
```python
# Modern system needs these columns in features_1s:
REQUIRED = ['timestamp', 'close', 'volume', 'atr_14_sec', 
            'spread_mean', 'spread_std', 'ma_slope']

# Verify with:
df = pd.read_parquet('features_1s/2024-03-01/features_00.parquet')
assert all(col in df.columns for col in REQUIRED)
```

### 4. State Mapping ✅
```python
# Your 8→3 state mapping is correct
# Modern system should use same mapping:
BULL states → Generate long signals
BEAR states → Generate short signals  
CHOP states → No signals

# Verify mapping is applied in modern path
```

## Quick Diagnostic Commands

```python
# 1. Check GMS is updating:
print(f"GMS updates per hour: {gms.update_count}")  # Should be ~60

# 2. Check state distribution:
print(f"State counts: {gms.state_history.value_counts()}")

# 3. Check TF-v3 is reading regime:
print(f"TF-v3 sees regime: {tf_v3.last_regime_seen}")

# 4. Check risk blocking:
print(f"Risk allows trade: {risk_manager.can_trade(size)}")
```

## Most Likely Fix

```yaml
# In config:
continuous_gms:
    cadence_sec: 60  # NOT 3600!
    
tf_v3:
    risk_fraction: 0.25  # Match legacy for now
    min_regime_confidence: 0.5  # Not too high
```

The modern system is more complex because it maintains high-frequency state updates while preserving low-frequency trading. This is the correct architecture - just needs proper configuration!