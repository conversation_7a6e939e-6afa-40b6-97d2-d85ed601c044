# Systematic Debugging Guide

## Overview
This guide documents the systematic debugging approach that successfully identified critical issues in the trading bot system. Follow these methods when debugging complex system failures.

## The Backwards Analysis Method

### When to Use
- System previously worked but now broken
- Multiple changes made simultaneously  
- Root cause unclear

### Steps
1. **Identify Last Known Good State**
   ```bash
   # Find working commit
   git log --oneline --grep="working\|stable\|baseline"
   # Example: commit 4e2af48 "fix: Restore frozen baseline to 180 trades/215% ROI"
   ```

2. **Compare Logs Line-by-Line**
   - Get logs from working system
   - Get logs from broken system
   - Compare configuration values
   - Look for differences in:
     - Trade counts
     - Configuration values
     - Error patterns
     - Execution paths

3. **Systematic File Restoration**
   ```bash
   # Check which files changed
   git diff 4e2af48 HEAD --name-only
   
   # Restore files one by one
   git checkout 4e2af48 -- path/to/file.py
   
   # Test after each restoration
   python3 scripts/run_backtest.py
   ```

## Configuration Debugging

### 1. Log Analysis Pattern
Look for these key indicators:

**Risk Fraction Check**:
```
# WORKING:
TF-v3 Config: Risk Fraction=0.25

# BROKEN:
TF-v3 Config: Risk Fraction=0.02
```

**Threshold Values**:
```
# WORKING:
Thresholds: {'vol_high': 0.0092, 'vol_low': 0.0055, 'mom_strong': 100.0, 'mom_weak': 50.0}

# BROKEN:
Thresholds: {'vol_high': 0.015, 'vol_low': 0.005, 'mom_strong': 2.5, 'mom_weak': 0.5}
```

### 2. Configuration Inheritance Debugging
```python
# Add debug logging to see value sources
logger.warning(f"CONFIG DEBUG: tf_v3.risk_frac = {config.tf_v3.risk_frac}")
logger.warning(f"CONFIG DEBUG: Source = {determine_source(config, 'tf_v3.risk_frac')}")
```

### 3. Silent Fallback Detection
Common silent fallbacks:
- Schema defaults overriding base config
- Missing override values
- Type conversion issues
- Nested configuration problems

## Trade Generation Debugging

### 1. Evaluation Pipeline Tracking
```
Strategy Evaluations: 4893
├── Regime Gate Failures: 4893 (100%)  ← PROBLEM HERE
├── Signal Validity Checks: 0
├── Risk Checks: 0
└── Successful Entries: 0
```

### 2. Regime Detection Analysis
Add logging at each decision point:
```python
logger.info(f"Regime state: {regime_state}")
logger.info(f"Allowed states: {allowed_states}")
logger.info(f"Gate result: {regime_state in allowed_states}")
```

### 3. Signal Flow Visualization
```
Data → Signals → Regime Filter → Strategy → Risk Check → Entry
         ↓           ↓              ↓          ↓           ↓
      [Valid]    [BLOCKED]      [Never]    [Never]     [Never]
```

## Common Debugging Mistakes

### 1. Assumption-Based Debugging
❌ **Wrong**: "System uses 2% risk because that's in base config"
✅ **Right**: Check actual runtime values in logs

### 2. Multi-Change Debugging  
❌ **Wrong**: Fix multiple issues simultaneously
✅ **Right**: Change one thing, test, repeat

### 3. Incomplete Restoration
❌ **Wrong**: Restore only the obvious files
✅ **Right**: Check ALL modified files systematically

### 4. Ignoring Configuration  
❌ **Wrong**: Focus only on code changes
✅ **Right**: Configuration changes can break everything

## Debugging Checklist

### Initial Investigation
- [ ] Identify when system last worked
- [ ] Get logs from working system
- [ ] Get logs from broken system
- [ ] List all files changed between versions
- [ ] Check configuration differences

### Systematic Testing
- [ ] Create isolated test environment
- [ ] Restore files one at a time
- [ ] Test after each restoration
- [ ] Document what each change affects
- [ ] Identify minimal fix

### Configuration Validation
- [ ] Check for schema default fallbacks
- [ ] Verify threshold values match
- [ ] Confirm risk parameters
- [ ] Validate detector configuration
- [ ] Look for ❌ in logs

### Trade Generation  
- [ ] Count evaluations vs trades
- [ ] Check regime gate failures
- [ ] Verify signal generation
- [ ] Examine risk limits
- [ ] Test position sizing

## Debug Output Examples

### Successful Debug Session
```
1. Started with: 0 trades, 100% regime failures
2. Found: risk_frac using wrong value (0.02 vs 0.25)
3. Found: volatility thresholds wrong (0.015 vs 0.0092)
4. Found: momentum thresholds wrong (2.5 vs 100.0)
5. Result after fixes: Still 0 trades
6. Conclusion: Deeper architectural issue
```

### Key Discoveries Log
```
CRITICAL: Working system used 25% risk, not 2%
CRITICAL: Thresholds must match between regime and gms sections
CRITICAL: UnifiedGMSDetector mixing detector configurations
CRITICAL: Override configs can skip to schema defaults
```

## Emergency Debugging Commands

```bash
# Quick system check
grep -n "Risk Fraction" logs/backtest_*.log | tail -5

# Configuration validation
grep -E "(❌|⚠️|✅)" logs/backtest_*.log | tail -20

# Trade generation check
grep -E "(Total Trades:|trades generated)" logs/backtest_*.log

# Regime failures
grep "Regime gate failures:" logs/backtest_*.log | tail -5

# Find configuration source
grep -B5 -A5 "risk_frac" configs/base.yaml configs/overrides/*.yaml
```

## Lessons Learned

1. **Always verify runtime values, not configured values**
2. **Configuration inheritance is complex and error-prone**
3. **Silent fallbacks are the enemy of debugging**
4. **Systematic restoration beats clever fixes**
5. **Document everything during debugging**