# Modern System Systematic Debugging Plan

## Context Summary (Session: 2025-07-17)

### What We Built (Phases 1-4)
Successfully implemented the modern system architecture as specified in MODERN_SYSTEM_EXPECTED.md:
- ✅ ModernDataAggregator with look-ahead prevention
- ✅ RegimeStateManager for 60s state tracking  
- ✅ ModernContinuousDetector for 60s updates
- ✅ HourlyStrategyEvaluator for hourly trading
- ✅ ModernBacktestEngine orchestrating all components
- ✅ ExecutionRefiner for optimal entry timing
- ✅ Proper separation of 60s regime updates from hourly trading

### What Failed (Phase 5)
During testing, we discovered systematic integration issues:
- Data schema mismatches ("Missing core signals for modern GMS detection")
- Interface inconsistencies (method names, parameters)
- Configuration structure conflicts
- Missing adapter layers between components

## Systematic Debugging Action Plan

### Step 1: Understand the Actual Data (Day 1, Hours 1-2)

#### 1.1 Create Data Discovery Script
```python
# scripts/discover_features_1s_schema.py
"""
Analyzes features_1s directory to document:
1. All available columns with data types
2. Value ranges for each column
3. Missing vs present data
4. Timestamp formats and frequency
"""
```

**Actions:**
1. Load sample features_1s files from multiple dates
2. Document every column name and type
3. Compare with expected columns from ModernContinuousGMSDetector
4. Create mapping table: expected_name -> actual_name

#### 1.2 Validate Data Quality
```python
# scripts/validate_features_1s_quality.py
"""
Checks:
1. Data continuity (gaps in 1-second intervals)
2. Value sanity (NaN, outliers, impossible values)
3. Column consistency across dates
4. Timezone handling
"""
```

**Output:** `data_schema_report.json` with complete column mappings

### Step 2: Map All Interfaces (Day 1, Hours 3-4)

#### 2.1 Create Interface Documentation
```python
# scripts/document_modern_interfaces.py
"""
For each modern component, document:
1. Expected inputs (method signatures, data types)
2. Expected outputs
3. Side effects (state updates, logging)
4. Dependencies on other components
"""
```

**Components to map:**
- ModernDataLoader → ModernDataAggregator
- ModernDataAggregator → ModernContinuousDetector
- ModernContinuousDetector → RegimeStateManager
- RegimeStateManager → HourlyStrategyEvaluator
- HourlyStrategyEvaluator → ModernTFV3Strategy
- ModernTFV3Strategy → Portfolio

#### 2.2 Identify Integration Points
Create visual diagram showing:
```mermaid
graph TD
    A[features_1s files] -->|load| B[ModernDataLoader]
    B -->|DataFrame| C[ModernDataAggregator]
    C -->|60s aggregated| D[ModernContinuousDetector]
    D -->|regime state| E[RegimeStateManager]
    E -->|regime history| F[HourlyStrategyEvaluator]
    F -->|trade signal| G[ModernBacktestEngine]
```

**Output:** `interface_mapping.md` with all mismatches documented

### Step 3: Create Proper Adapters (Day 2, Hours 1-3)

#### 3.1 Data Schema Adapter
```python
# hyperliquid_bot/modern/adapters/data_adapter.py
class ModernDataSchemaAdapter:
    """
    Centralizes all data transformations between:
    - Raw features_1s data
    - Expected detector inputs
    - Strategy signal formats
    """
    
    COLUMN_MAPPINGS = {
        # Discovered from Step 1
        'volume_imbalance': 'imbalance',
        'obi_smoothed': 'volume_imbalance',
        # etc...
    }
    
    def adapt_features_for_detector(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform features_1s data to detector expected format"""
        
    def adapt_signals_for_strategy(self, signals: Dict) -> Dict:
        """Transform detector output to strategy expected format"""
```

#### 3.2 Configuration Adapter
```python
# hyperliquid_bot/modern/adapters/config_adapter.py
class ModernConfigAdapter:
    """
    Handles configuration inconsistencies between:
    - Legacy config structure
    - Modern expected structure
    - Runtime requirements
    """
    
    def get_indicator_params(self) -> Dict:
        """Returns consistent indicator parameters regardless of source"""
        
    def get_risk_params(self) -> Dict:
        """Returns consistent risk parameters"""
```

### Step 4: Write Tests First (Day 2, Hours 4-6)

#### 4.1 Component Tests
```python
# tests/modern/test_data_flow.py
def test_data_loader_output_schema():
    """Verify data loader produces expected columns"""

def test_aggregator_60s_output():
    """Verify 60s aggregation produces correct format"""

def test_detector_with_real_data():
    """Test detector with actual features_1s data"""
```

#### 4.2 Integration Tests
```python
# tests/modern/test_integration.py
def test_full_pipeline_data_flow():
    """
    Test data flows correctly through entire pipeline:
    1. Load features_1s
    2. Aggregate to 60s
    3. Detect regime
    4. Evaluate strategy
    5. Generate trade signal
    """

def test_look_ahead_bias_prevention():
    """Verify no future data leaks at any stage"""
```

### Step 5: Systematic Debugging Tools (Day 3, Hours 1-2)

#### 5.1 Data Flow Tracer
```python
# scripts/trace_modern_data_flow.py
"""
Traces a single data point through the entire system:
1. Shows transformations at each stage
2. Logs column names and values
3. Identifies where data is lost or corrupted
"""
```

#### 5.2 Regime Detection Monitor
```python
# scripts/monitor_regime_detection.py
"""
Real-time monitoring of regime detection:
1. Shows regime changes with timestamps
2. Displays confidence scores
3. Correlates with price movements
4. Identifies stuck states
"""
```

#### 5.3 Trade Generation Analyzer
```python
# scripts/analyze_trade_generation.py
"""
Analyzes why trades are/aren't generated:
1. Regime gate failures
2. Strategy filter rejections
3. Risk management blocks
4. Data quality issues
"""
```

### Step 6: Fix Root Causes (Day 3, Hours 3-4)

Based on discoveries from Steps 1-5:

1. **Update ModernDataLoader**
   - Use discovered column names
   - Add proper data validation
   - Handle missing columns gracefully

2. **Fix ModernContinuousDetector**
   - Accept actual data schema
   - Use adapters for transformations
   - Add comprehensive logging

3. **Align Configuration**
   - Create single source of truth
   - Remove conflicting parameters
   - Document all settings

## Validation Criteria

### Success Metrics
1. **Data Flow**: Every component receives data in expected format
2. **Regime Detection**: 60 updates per hour, reasonable state distribution
3. **Trade Generation**: 100-200 trades per year (matching target)
4. **Performance**: <60 seconds for 1-week backtest
5. **No Warnings**: Clean execution without schema warnings

### Test Scenarios
1. **1-Week Quick Test**: Basic functionality validation
2. **1-Month Full Test**: Performance and trade count validation
3. **Edge Cases**: Data gaps, extreme market conditions
4. **Comparison Test**: Modern vs Legacy on same period

## Critical Principles

1. **No Duct-Tape Fixes**: Every fix addresses root cause
2. **Test Before Fix**: Write failing test, then make it pass
3. **Document Everything**: Every assumption, every decision
4. **Clean Interfaces**: Clear contracts between components
5. **Data-Driven**: Let actual data guide implementation

## Next Session Checklist

### Pre-Session Preparation
- [ ] Review MODERN_SYSTEM_EXPECTED.md
- [ ] Review MODERN_SYSTEM_PHASE5_ASSESSMENT.md
- [ ] Have sample features_1s data ready

### Session Tasks
- [ ] Run data discovery scripts
- [ ] Document all schema mismatches
- [ ] Create adapter implementations
- [ ] Write comprehensive tests
- [ ] Fix root causes systematically

### Post-Session Validation
- [ ] All tests passing
- [ ] Clean backtest execution
- [ ] Reasonable trade generation
- [ ] Performance acceptable

## File Organization

```
aerith_hyperliquid_bot/
├── guides/
│   ├── MODERN_SYSTEM_EXPECTED.md          # Original design
│   ├── modern_system_systematic_debugging_plan.md  # This file
│   └── modern_system_implementation_log.md # Daily progress
├── hyperliquid_bot/
│   └── modern/
│       ├── adapters/                      # NEW: Clean adapters
│       │   ├── data_adapter.py
│       │   └── config_adapter.py
│       └── tests/                         # NEW: Comprehensive tests
│           ├── test_components.py
│           └── test_integration.py
├── scripts/
│   ├── discover_features_1s_schema.py     # Data discovery
│   ├── document_modern_interfaces.py      # Interface mapping
│   ├── trace_modern_data_flow.py         # Debugging tools
│   └── analyze_trade_generation.py       # Trade analysis
└── MODERN_SYSTEM_PHASE5_ASSESSMENT.md     # Current state

```

## Remember

The modern system architecture is sound. We built it correctly following the design. The issues are in the integration layer - where components meet. By systematically understanding the actual data and creating proper adapters, we can make the system work without compromising the clean architecture we've built.

The goal is not to make it work at any cost, but to make it work correctly with maintainable, understandable code that follows best practices.