# Aerith Hyperliquid Bot - Project Structure

This document outlines the directory structure and purpose of key modules within the project.

**Assumed Root Directory:** `/Users/<USER>/Desktop/trading_bot_/`

## Main Project Structure

```
aerith_hyperliquid_bot/
├── .gitignore
├── config.yaml           # Main configuration file
├── main.py               # Example entry point for running backtests (or run_backtest.py)
├── pyproject.toml        # Project metadata and dependencies (or requirements.txt)
├── README.md             # Project overview and setup
├── roadmap.md            # Detailed development roadmap
├── project_structure.md  # Project file structure overview (This file)
├── message_to_ai.md      # Context summary for AI collaborators
├── aerith_hyperliquid_bot/ # Main Python package source code
│   └── hyperliquid_bot/
│       ├── __init__.py
│       ├── backtester/
│       │   ├── __init__.py
│       │   └── backtester.py     # Core backtesting loop, orchestration
│       ├── config/
│       │   ├── __init__.py
│       │   └── settings.py       # Pydantic models for config validation
│       ├── core/
│       │   ├── __init__.py
│       │   ├── detector.py       # Regime detectors (GMS Archived, HMM TBD)
│       │   └── risk.py           # Risk management, position sizing (ATR-based)
│       ├── data/
│       │   ├── __init__.py
│       │   ├── handler.py        # Loads OHLCV, L2 features
│       │   └── providers/        # New: For external data fetching
│       │       ├── __init__.py
│       │       └── feargreed.py    # Example: Fetches Fear & Greed data
│       │       └── (Other providers...)
│       ├── execution/
│       │   ├── __init__.py
│       │   └── simulation.py     # Order execution simulation (Taker/ProbMaker modes)
│       ├── features/
│       │   ├── __init__.py
│       │   ├── microstructure.py # OBI, Depth, Spread calculations
│       │   ├── statistical.py    # (Inactive - Hurst)
│       │   └── hmm_features.py   # New: Feature generation for HMM
│       ├── models/               # New: For ML models
│       │   ├── __init__.py
│       │   └── regime_filters/   # New: Proposed location for HMM model files/logic
│       │       ├── __init__.py
│       │       └── hmm_model.py    # Example: HMM training/prediction logic
│       ├── portfolio/
│       │   ├── __init__.py
│       │   └── portfolio.py      # Manages balance, positions, trades, P/L, fees
│       ├── signals/
│       │   ├── __init__.py
│       │   └── calculator.py     # Calculates indicators (TA-Lib, custom), combines data (or SignalEngine)
│       ├── strategies/
│       │   ├── __init__.py
│       │   ├── evaluator.py      # Strategy evaluation logic (contains TF logic)
│       │   ├── obi_scalper.py    # TBD: OBI Scalper strategy
│       │   └── liq_strat.py      # TBD: Liquidation strategy
│       └── utils/                # Optional: Utility functions
│           ├── __init__.py
│           └── (Utility modules...)
├── logs/                   # Directory for log files, backtest outputs
│   ├── backtest_signals_*.parquet # Saved signals DataFrame per run
│   └── backtest_trades_*.json     # Saved trades list per run
├── plots/                  # Directory for saving visualization plots
├── scripts/                # Utility and analysis scripts
│   ├── __init__.py
│   └── visualize_backtest.py # Script to generate backtest plots
├── tests/                  # Unit and integration tests (pytest)
│   ├── __init__.py
│   └── (Test files...)
└── (Other config files like .env if used)

## External Data Directories

```
/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/
├── raw2/                   # Raw L2 Parquet snapshots (Input)
├── resampled/              # Resampled OHLCV CSV data (Input)
└── ticks/                  # TBD: Directory for downloaded tick data (Phase 4)
