# Configuration Issues Guide

## Critical Configuration Discoveries

### 1. Risk Fraction Fallback Issue

**Problem**: TF-v3 risk_frac falls back to schema default (0.25) instead of base config value (0.02)

**Root Cause**: Missing risk_frac in override configuration causes Pydantic to use schema default, not base config value.

```yaml
# base.yaml
tf_v3:
  risk_frac: 0.02  # 2% risk per trade

# execution_refinement_enabled.yaml (BROKEN)
tf_v3:
  atr_trail_k: 2.0
  max_leverage: 5.0
  # risk_frac: MISSING! Falls back to schema default (0.25)

# settings.py schema default
class TFV3Settings:
    risk_frac: float = 0.25  # 25% default!
```

**Solution**: Always explicitly set risk_frac in override configs:
```yaml
tf_v3:
  risk_frac: 0.25  # EXPLICIT: Use 25% risk per trade
```

### 2. Volatility Threshold Mismatch

**Problem**: Override config uses wrong volatility thresholds

**Working Values** (from July 15 04:09:50 log):
```yaml
gms_vol_high_thresh: 0.0092
gms_vol_low_thresh: 0.0055
```

**Broken Values** (in override):
```yaml
gms_vol_high_thresh: 0.015   # Too high!
gms_vol_low_thresh: 0.005    # Wrong!
```

**Impact**: Causes 100% regime gate failures

### 3. Momentum Threshold Confusion

**Problem**: Different detector modes use different threshold values

```yaml
# continuous_gms mode (WORKING)
gms_mom_strong_thresh: 100.0
gms_mom_weak_thresh: 50.0

# granular_microstructure mode (DIFFERENT)
gms_mom_strong_thresh: 2.5
gms_mom_weak_thresh: 0.5
```

**Impact**: UnifiedGMSDetector mixes these values incorrectly

### 4. Configuration Inheritance Layers

**Order of Precedence**:
1. Command-line arguments (highest)
2. Override configuration file
3. Base configuration file
4. Schema defaults (lowest)

**Problem**: Missing values skip to schema defaults, not base config!

## Configuration Validation

### Using the ConfigValidator

The system now includes automatic configuration validation that detects:
- Values falling back to schema defaults
- Mismatches between regime and gms sections
- Detector type inconsistencies
- Leverage limit violations

**Success Output**:
```
[INFO] Backtester: ✅ No configuration fallbacks detected - using intended settings
```

**Failure Output**:
```
[ERROR] Backtester: ❌ CONFIG FALLBACK: tf_v3.risk_frac using schema default=0.25 instead of base config=0.02
```

### Common Configuration Patterns

#### Pattern 1: Detector Configuration
Always set thresholds in BOTH locations:
```yaml
regime:
  continuous_gms:
    gms_mom_strong_thresh: 100.0
    gms_vol_high_thresh: 0.0092

gms:
  gms_mom_strong_thresh: 100.0  # Must match!
  gms_vol_high_thresh: 0.0092   # Must match!
```

#### Pattern 2: Strategy Configuration
Always include critical values in overrides:
```yaml
tf_v3:
  risk_frac: 0.25      # ALWAYS include
  max_leverage: 5.0    # ALWAYS include
  atr_trail_k: 2.0     # ALWAYS include
```

#### Pattern 3: System Mode
Ensure detector types match:
```yaml
regime:
  detector_type: 'continuous_gms'
  
gms:
  detector_type: 'continuous_gms'  # Must match!
```

## Debugging Configuration Issues

### 1. Check Log Output
Look for configuration values at startup:
```
CONFIG: Risk Fraction=0.25
CONFIG: Momentum thresholds: strong=100.0, weak=50.0
CONFIG: Volatility thresholds: high=0.0092, low=0.0055
```

### 2. Enable Validation Logging
The ConfigValidator logs all issues:
```
[ERROR] ❌ CONFIG FALLBACK: tf_v3.risk_frac using schema default
[WARNING] ⚠️ CONFIG MISMATCH: regime.vol_high=0.0092 but gms.vol_high=0.015
```

### 3. Use Strict Mode
For development, enable strict validation:
```python
validator = ConfigValidator(strict_mode=True)
# Raises exception on any configuration issue
```

## Best Practices

### 1. Complete Override Files
Always include ALL critical values in override configs, even if they match base config.

### 2. Validate After Changes
Run a quick backtest after any configuration change to check for:
- ✅ No configuration fallbacks detected
- Correct trade count
- Expected ROI

### 3. Document Configuration Intent
Add comments explaining why values are set:
```yaml
tf_v3:
  risk_frac: 0.25  # CRITICAL: Use 25% for aggressive trading (NOT 2%)
```

### 4. Version Control Configs
- Commit working configurations
- Tag known-good config sets
- Document config changes in commit messages

## Configuration Checklist

Before running a backtest, verify:

- [ ] risk_frac is explicitly set in tf_v3 section
- [ ] All threshold values match between regime and gms sections  
- [ ] detector_type matches in both locations
- [ ] Override file includes all critical values
- [ ] Log shows "✅ No configuration fallbacks detected"
- [ ] No ❌ or ⚠️ symbols in configuration logs