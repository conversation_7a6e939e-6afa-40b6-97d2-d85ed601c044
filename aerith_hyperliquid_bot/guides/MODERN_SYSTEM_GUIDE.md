# Hyperliquid Trading Bot - Modern System Guide

## Executive Summary

The Modern System is a regime-aware multi-timeframe trading bot that achieved **235% ROI** in 2024 backtests. It combines:
- **Continuous GMS Detector**: Updates regime state every 60 seconds
- **TF-v3 Strategy**: Enhanced trend following with regime gating
- **Execution Refinement**: 1-minute timing optimization for better entries
- **Confidence-Based Scaling**: Position sizing based on regime stability

### Quick Start
```bash
# Run modern system with execution refinement (235% ROI config)
python3 -m hyperliquid_bot.backtester.run_backtest --override configs/overrides/execution_refinement_enabled.yaml
```

## System Architecture

### Data Flow
```
Raw L2 Data (1s) → Feature Extraction → Regime Detection → Strategy Signals → Execution Refinement → Trades
     ↓                    ↓                    ↓                   ↓                    ↓
  Parquet Files      Indicators           GMS States         TF-v3 Logic         1m Candles
                   (EMA, ATR, OBI)      (BULL/BEAR/CHOP)    (Trend Following)   (Timing Opt)
```

### Key Components

1. **Data Handler** (`hyperliquid_bot/data/handler.py`)
   - Loads historical L2 data and resamples to desired timeframe
   - Provides 1-minute candles for execution refinement
   - Handles feature extraction and aggregation

2. **Continuous GMS Detector** (`hyperliquid_bot/core/gms_detector.py`)
   - Analyzes market microstructure every 60 seconds
   - Detects 8 raw states, maps to 3 trading regimes (BULL/BEAR/CHOP)
   - Calculates regime confidence (0.3-1.0) for position scaling

3. **TF-v3 Strategy** (`hyperliquid_bot/strategies/tf_v3.py`)
   - Enhanced trend following with regime gating
   - Only trades in BULL/BEAR regimes (skips CHOP)
   - Uses EMA crossover with ATR-based trailing stops

4. **Execution Filter** (`hyperliquid_bot/execution/execution_filter.py`)
   - Evaluates first 5 minutes of each hour for optimal entry
   - Scores based on spread (40%), momentum (30%), price (30%)
   - Immediate execution for high confidence regimes (≥0.8)

## Configuration Guide - The "Knobs"

### Regime Detection Knobs (`configs/base.yaml`)

#### Momentum Thresholds
```yaml
continuous_gms:
  gms_mom_strong_thresh: 100.0  # Strong momentum threshold (MA slope)
  gms_mom_weak_thresh: 50.0     # Weak momentum threshold
```
- **Effect**: Higher values = fewer trend detections, more CHOP
- **Calibrated**: Using legacy values that work with hourly data
- **Tuning**: Decrease for more sensitive trend detection

#### Volatility Thresholds
```yaml
continuous_gms:
  gms_vol_high_thresh: 0.03     # High volatility threshold (3% ATR)
  gms_vol_low_thresh: 0.01      # Low volatility threshold (1% ATR)
```
- **Effect**: Determines High_Vol_Range vs Low_Vol_Range states
- **Current**: More aggressive than legacy (0.92%/0.55%)
- **Tuning**: Increase for less volatility-based filtering

#### State Mapping
```yaml
regime:
  map_weak_bear_to_bear: true   # Map Weak_Bear_Trend to BEAR (not CHOP)
```
- **Effect**: Enables short signals in weak bear markets
- **Critical**: Without this, only Strong_Bear_Trend generates shorts
- **Default**: false (legacy behavior)

### Strategy Knobs (`configs/base.yaml`)

#### TF-v3 Parameters
```yaml
tf_v3:
  ema_fast: 20                  # Fast EMA period
  ema_slow: 50                  # Slow EMA period
  atr_period: 14                # ATR calculation period
  atr_trail_k: 3.0              # ATR multiplier for trailing stop
  max_trade_life_h: 24          # Maximum hours to hold position
  risk_frac: 0.25               # Risk fraction per trade (25%)
  max_notional: 25000           # Maximum position size ($25k)
```
- **EMA Periods**: Shorter = more signals, longer = fewer but stronger
- **ATR Trail**: Higher = wider stops, more room for positions
- **Risk Fraction**: Percentage of capital risked per trade
- **Trade Life**: Forces exit after N hours (time decay)

#### Confidence-Based Scaling
```yaml
tf_v3:
  confidence_position_scaling: true    # Enable position scaling
  min_confidence_for_scaling: 0.3     # Minimum confidence (30%)
  max_confidence_for_scaling: 1.0     # Maximum confidence (100%)
  confidence_scale_factor: 0.5        # Size at min confidence (50%)
```
- **Effect**: Reduces position size in uncertain regimes
- **Formula**: `size = base_size * (scale_factor + (1-scale_factor) * confidence)`
- **Example**: 0.3 confidence = 50% position, 1.0 confidence = 100% position

### Execution Refinement Knobs

```yaml
backtest:
  use_execution_refinement: true      # Enable 1-minute timing

execution:
  high_confidence_threshold: 0.8      # Execute immediately above this
  execution_window_minutes: 5         # Check first N minutes
  min_execution_score: 35            # Minimum score to execute early
```
- **High Confidence**: Skip timing optimization, execute at hour open
- **Window**: How many minutes to evaluate (max 5)
- **Min Score**: Lower = more aggressive execution

## Regime Detection Mechanics

### The 8 Raw States
1. **Strong_Bull_Trend**: High positive momentum + confirmation
2. **Weak_Bull_Trend**: Moderate positive momentum
3. **Strong_Bear_Trend**: High negative momentum + confirmation
4. **Weak_Bear_Trend**: Moderate negative momentum
5. **High_Vol_Range**: No clear trend + high volatility
6. **Low_Vol_Range**: No clear trend + low volatility
7. **Uncertain**: Conflicting signals
8. **TIGHT_SPREAD**: Spread too tight for reliable signals

### The 3 Trading Regimes
- **BULL**: Strong_Bull_Trend, Weak_Bull_Trend (if enabled)
- **BEAR**: Strong_Bear_Trend, Weak_Bear_Trend (if map_weak_bear_to_bear=true)
- **CHOP**: Everything else (no trading)

### Confidence Calculation
```python
# Natural progressions = high confidence
Weak_Bull → Strong_Bull: 0.9
Weak_Bear → Strong_Bear: 0.9

# Reversals = low confidence  
Bull → Bear: 0.5
Bear → Bull: 0.5

# Volatility adjustment
High volatility: confidence * 0.8
Low volatility: confidence * 0.9
```

## Execution Refinement Process

### When Signal Generated (Hourly)
1. **High Confidence (≥0.8)**: Execute immediately at hour open
2. **Lower Confidence**: Evaluate first 5 minutes

### Minute Scoring (0-100 points)
- **Spread Quality (40%)**: Tighter = better
- **Momentum Alignment (30%)**: Direction matches signal
- **Price Stability (30%)**: Lower volatility = better
- **Bonus Points**: 
  - Tightening spread (+10)
  - Growing OBI in our direction (+15)

### Example
```
Hour Open: $50,000, Confidence: 0.6
Minute 1: Score 25 (wide spread)
Minute 2: Score 45 (better spread, good momentum)
Minute 3: Score 38 (spread widened)
Minute 4: Score 52 (tight spread, aligned OBI)
Minute 5: Score 41 (default fallback)
→ Execute at Minute 4 price
```

## Performance Tuning

### For Trending Markets (2024-like)
```yaml
# Current production settings
continuous_gms:
  gms_mom_strong_thresh: 100.0
  gms_mom_weak_thresh: 50.0
  map_weak_bear_to_bear: true
```
- Results: 189 trades, 235% ROI
- Catches major trends, filters noise

### For Choppy Markets
```yaml
# More sensitive settings
continuous_gms:
  gms_mom_strong_thresh: 50.0    # Half the threshold
  gms_mom_weak_thresh: 25.0      # More sensitive
  
tf_v3:
  ema_fast: 10                   # Faster signals
  ema_slow: 25
  atr_trail_k: 2.0               # Tighter stops
```
- Generates more signals
- Smaller positions, quicker exits

### For High Volatility
```yaml
continuous_gms:
  gms_vol_high_thresh: 0.05      # Accept higher volatility
  
execution:
  min_execution_score: 25        # Lower bar for execution
  
tf_v3:
  confidence_scale_factor: 0.3   # Smaller positions (30% at min)
```

## Common Configurations

### Conservative (Lower Risk/Return)
```yaml
portfolio:
  risk_per_trade: 0.01           # 1% risk per trade
  max_leverage: 5.0              # Lower leverage

tf_v3:
  atr_trail_k: 4.0               # Wider stops
  risk_frac: 0.15                # Smaller positions
```

### Aggressive (Higher Risk/Return)
```yaml
portfolio:
  risk_per_trade: 0.03           # 3% risk per trade
  max_leverage: 15.0             # Higher leverage

tf_v3:
  atr_trail_k: 2.0               # Tighter stops
  risk_frac: 0.35                # Larger positions
  max_trade_life_h: 48           # Hold longer
```

### Scalping Mode
```yaml
tf_v3:
  ema_fast: 5
  ema_slow: 15
  max_trade_life_h: 4            # 4-hour max hold
  
execution:
  execution_window_minutes: 2     # Quick decisions
  min_execution_score: 20        # Take more entries
```

## Required Data

### 1-Minute Candles (for execution refinement)
```bash
# Generate from raw L2 data
python3 scripts/generate_1m_candles.py

# Location: /hyperliquid_data/resampled_l2/1m/
# Format: YYYY-MM-DD.parquet
```

### Hourly Features
```bash
# Location: /hyperliquid_data/raw2/
# Format: YYYYMMDD_raw2.parquet
```

## Troubleshooting

### No Trades Generated
1. Check regime detection:
   ```yaml
   # Too strict momentum thresholds
   gms_mom_strong_thresh: 1000.0  # Way too high
   ```
2. Verify data availability
3. Check if `use_tf_v3: true` is set

### Too Many Trades
1. Increase momentum thresholds
2. Enable strict regime filtering:
   ```yaml
   use_strict_strategy_filtering: true
   ```
3. Increase `min_execution_score`

### Poor Execution Prices
1. Verify 1-minute data exists
2. Check execution window settings
3. Consider increasing `high_confidence_threshold`

### Low Win Rate
1. Widen stops: increase `atr_trail_k`
2. Be more selective: increase momentum thresholds
3. Check if regime detection is too sensitive

## For AI Collaborators

### Key Files to Understand
1. `hyperliquid_bot/core/gms_detector.py` - Regime detection logic
2. `hyperliquid_bot/strategies/tf_v3.py` - Trading strategy
3. `hyperliquid_bot/execution/execution_filter.py` - Entry timing
4. `configs/overrides/execution_refinement_enabled.yaml` - Best config

### Critical Concepts
- **Look-ahead bias**: Already eliminated, see `test_look_ahead_bias.py`
- **Dual architecture**: Legacy (TF-v2 + discrete GMS) vs Modern (TF-v3 + continuous GMS)
- **State mapping**: 8 raw states → 3 trading regimes via YAML config
- **Confidence scoring**: Natural progressions (0.9) vs reversals (0.5)

### Performance Baselines
- Legacy System: 215% ROI (180 trades)
- Modern System: 235% ROI (189 trades)
- Improvement: +9.3% ROI, +5% trades

### Next Development Areas
1. **Multi-asset support**: Extend to multiple trading pairs
2. **Adaptive thresholds**: Dynamic regime detection parameters
3. **ML enhancement**: Use ML for regime prediction
4. **Risk parity**: Dynamic position sizing across strategies

## Conclusion

The modern system represents a significant advancement over the legacy system, achieving 235% ROI through:
1. More responsive regime detection (60s vs 3600s)
2. Sophisticated execution timing (+94% improvement)
3. Confidence-based position management
4. Robust look-ahead bias prevention

The system is production-ready with extensive configuration options for different market conditions and risk preferences.