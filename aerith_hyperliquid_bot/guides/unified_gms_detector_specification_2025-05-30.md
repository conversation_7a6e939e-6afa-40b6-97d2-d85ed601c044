# Unified GMS Detector: Technical Specification & Implementation Guide
**Date:** May 30, 2025
**Objective:** Merge GranularMicrostructureRegimeDetector and ContinuousGMSDetector into UnifiedGMSDetector
**Status:** 🔧 **IMPLEMENTATION READY**

## Executive Summary

This specification provides a comprehensive plan to unify the two existing GMS detector implementations into a single, highly configurable `UnifiedGMSDetector` class. The unified detector will support both legacy (granular_microstructure) and modern (continuous_gms) detection modes through configuration flags while eliminating code duplication and improving maintainability.

**Key Benefits:**
- ✅ **Zero Code Duplication**: Single implementation supporting both modes
- ✅ **Backward Compatibility**: 100% reproducible results for existing configurations
- ✅ **Performance Optimization**: Integrated adaptive threshold optimizations
- ✅ **Unified Configuration**: Consolidated GMS settings in single section
- ✅ **Clean Architecture**: Simplified factory pattern and dependency management

## 1. Current Architecture Analysis

### 1.1 Existing Detector Comparison

| Aspect | GranularMicrostructureRegimeDetector | ContinuousGMSDetector |
|--------|-------------------------------------|----------------------|
| **File Location** | `hyperliquid_bot/core/detector.py` | `hyperliquid_bot/core/gms_detector.py` |
| **Lines of Code** | ~600 lines | ~1,100 lines |
| **Data Source** | `raw2/`, `resampled_l2/` (hourly) | `l2_raw/`, `features_1s/` (1-second) |
| **Depth Levels** | Fixed 5 | Configurable 5-20 |
| **Cadence** | 3600 seconds (hourly) | 60 seconds (configurable) |
| **Output Format** | String regime state | Dict with state + risk_suppressed |
| **Threshold Management** | Fixed values only | Fixed + adaptive thresholds |
| **Performance** | 48.95s (optimized) | 663.45s (bottleneck identified) |

### 1.2 Code Duplication Analysis

**Shared Logic (~70% overlap):**
- Threshold-based regime classification logic
- Signal validation and NaN handling
- State mapping and output formatting
- Configuration parameter resolution
- Logging and error handling

**Unique Logic (~30% differences):**
- Data pipeline integration (raw2 vs features_1s)
- Adaptive threshold management (continuous only)
- Output format handling (string vs dict)
- Signal naming conventions (depth-specific)
- Cadence and timing logic

### 1.3 Configuration Complexity

**Current Configuration Sections:**
```yaml
# Scattered across multiple sections
regime:
  detector_type: 'granular_microstructure'  # or 'continuous_gms'
  gms_vol_high_thresh: 0.92                 # Fallback values
  granular_microstructure: {...}            # Detector-specific settings
  continuous_gms: {...}                     # Detector-specific settings

gms:
  detector_type: 'continuous_gms'           # Duplicate setting
  cadence_sec: 60                           # Operational settings
  auto_thresholds: false                    # Adaptive threshold toggle

microstructure:
  gms_obi_strong_confirm_thresh: 0.20       # OBI thresholds
  depth_levels: 5                           # Affects signal naming
```

## 2. Unified Architecture Design

### 2.1 Core Class Structure

```python
class UnifiedGMSDetector(RegimeDetectorInterface):
    """
    Unified GMS detector supporting both legacy and continuous modes.

    Modes:
    - 'legacy' or 'granular_microstructure': Replicates granular detector behavior
    - 'continuous' or 'continuous_gms': Provides modern continuous functionality
    """

    def __init__(self, config: Config):
        super().__init__(config)

        # Mode resolution with backward compatibility
        self.detector_mode = self._resolve_detector_mode(config)
        self.detector_type = self._resolve_detector_type(config)  # For compatibility

        # Configuration resolution
        self.cfg_gms = self._resolve_gms_config(config)
        self.cfg_regime = config.regime
        self.cfg_micro = config.microstructure

        # Mode-specific initialization
        if self.detector_mode == 'legacy':
            self._init_legacy_mode()
        else:
            self._init_continuous_mode()

        # Common initialization
        self._init_common_components()

    def _resolve_detector_mode(self, config: Config) -> str:
        """Resolve detector mode with multiple fallback paths for compatibility."""
        # Priority 1: New unified mode setting
        if hasattr(config, 'gms') and hasattr(config.gms, 'mode'):
            mode = config.gms.mode.lower()
            if mode in ['legacy', 'granular_microstructure', 'static']:
                return 'legacy'
            elif mode in ['continuous', 'continuous_gms', 'adaptive']:
                return 'continuous'

        # Priority 2: Legacy detector_type in gms section
        if hasattr(config, 'gms') and hasattr(config.gms, 'detector_type'):
            detector_type = config.gms.detector_type.lower()
            if detector_type == 'granular_microstructure':
                return 'legacy'
            elif detector_type == 'continuous_gms':
                return 'continuous'

        # Priority 3: Legacy detector_type in regime section
        if hasattr(config.regime, 'detector_type'):
            detector_type = config.regime.detector_type.lower()
            if detector_type == 'granular_microstructure':
                return 'legacy'
            elif detector_type == 'continuous_gms':
                return 'continuous'

        # Default to continuous mode
        return 'continuous'
```

### 2.2 Unified Configuration Schema

```yaml
# New unified configuration structure
gms:
  # Mode selection (primary configuration)
  mode: 'legacy'                    # 'legacy' or 'continuous'

  # Backward compatibility (deprecated but supported)
  detector_type: 'granular_microstructure'  # Maps to mode

  # Operational settings
  cadence_sec: null                 # Auto-resolved based on mode (3600 for legacy, 60 for continuous)
  output_states: 8                  # 8 = raw, 4 or 3 after collapse
  state_collapse_map_file: 'configs/gms_state_mapping.yaml'

  # Unified threshold configuration
  thresholds:
    legacy:
      vol_high: 0.92
      vol_low: 0.55
      mom_strong: 100.0
      mom_weak: 50.0
      spread_std_high: 0.000050
      spread_mean_low: 0.000045
    continuous:
      vol_high: 0.03
      vol_low: 0.01
      mom_strong: 2.5
      mom_weak: 0.5
      spread_std_high: 0.0005
      spread_mean_low: 0.0001

  # Adaptive threshold configuration (continuous mode only)
  adaptive_thresholds:
    enabled: false                  # Toggle adaptive vs fixed thresholds
    mode: 'optimized'               # 'optimized' or 'legacy'
    percentile_window_sec: 86400    # 24-hour rolling window
    priming_hours: 1                # Reduced from 24 for performance
    vol_low_pct: 0.15              # 15th percentile
    vol_high_pct: 0.50             # 50th percentile
    mom_low_pct: 0.15              # 15th percentile
    mom_high_pct: 0.50             # 50th percentile

  # Performance optimizations
  performance:
    skip_l2_raw_processing_if_1h_features_exist: true  # Legacy mode optimization
    use_vectorized_operations: true                    # Enable optimizations
    cache_percentiles: true                           # Cache adaptive threshold calculations

  # Risk suppression settings
  risk_suppression:
    enabled: true
    notional_frac: 0.25
    pnl_atr_mult: 1.5

# Backward compatibility: existing sections still supported
regime:
  detector_type: 'granular_microstructure'  # Maps to gms.mode
  # Legacy threshold fallbacks (used if gms.thresholds not specified)
  gms_vol_high_thresh: 0.92
  gms_vol_low_thresh: 0.55
  # ... other legacy settings

microstructure:
  # OBI thresholds (mode-independent)
  gms_obi_strong_confirm_thresh: 0.20
  gms_obi_weak_confirm_thresh: 0.11
  depth_levels: 5                           # Affects signal naming
```

### 2.3 Factory Function Integration

```python
def get_regime_detector(config: Config) -> RegimeDetectorInterface:
    """Updated factory function supporting unified detector."""
    detector_type = getattr(config.regime, 'detector_type', 'rule_based').lower()

    if detector_type in ['granular_microstructure', 'continuous_gms']:
        # Use unified detector for both GMS types
        logger.info(f"Instantiating UnifiedGMSDetector for type: {detector_type}")

        # Validation for required config attributes
        required_attrs = _validate_gms_config(config, detector_type)
        if required_attrs:
            raise AttributeError(f"Missing GMS config attrs: {required_attrs}")

        return UnifiedGMSDetector(config)

    # ... other detector types remain unchanged
```

## 3. Implementation Strategy

### 3.1 Phase 1: Core Unification (Week 1)

**Objective:** Create UnifiedGMSDetector with mode-based branching

**Tasks:**
1. **Create UnifiedGMSDetector class** in `hyperliquid_bot/core/unified_gms_detector.py`
2. **Implement mode resolution logic** with backward compatibility
3. **Merge threshold management** with unified configuration support
4. **Integrate signal processing** with depth-agnostic naming
5. **Implement output format handling** (string vs dict based on mode)

**Key Implementation Points:**
```python
class UnifiedGMSDetector(RegimeDetectorInterface):
    def __init__(self, config: Config):
        # Mode resolution with multiple fallback paths
        self.detector_mode = self._resolve_detector_mode(config)

        # Unified threshold resolution
        self.thresholds = self._resolve_thresholds(config)

        # Mode-specific data pipeline setup
        if self.detector_mode == 'legacy':
            self.data_source = 'raw2'
            self.cadence_sec = 3600
            self.output_format = 'string'
        else:
            self.data_source = 'features_1s'
            self.cadence_sec = 60
            self.output_format = 'dict'

    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None):
        """Unified regime detection with mode-specific output formatting."""
        # Common regime detection logic
        state = self._determine_state(signals)

        # Mode-specific output formatting
        if self.detector_mode == 'legacy':
            return state  # String output for legacy mode
        else:
            return {
                "state": state,
                "risk_suppressed": self._calculate_risk_suppression(signals)
            }
```

### 3.2 Phase 2: Adaptive Threshold Integration (Week 2)

**Objective:** Integrate optimized adaptive thresholds from previous analysis

**Tasks:**
1. **Import OptimizedAdaptiveThreshold** from adaptive threshold optimization
2. **Implement adaptive threshold toggle** in continuous mode
3. **Add performance optimizations** (vectorized operations, caching)
4. **Create emergency fallback** to fixed thresholds on errors
5. **Implement priming optimization** (batch processing vs individual updates)

**Key Implementation Points:**
```python
def _init_adaptive_thresholds(self):
    """Initialize adaptive thresholds for continuous mode."""
    if (self.detector_mode == 'continuous' and
        self.cfg_gms.adaptive_thresholds.enabled):

        from hyperliquid_bot.utils.optimized_adaptive_threshold import OptimizedAdaptiveThreshold

        # Use optimized implementation
        window_sec = self.cfg_gms.adaptive_thresholds.percentile_window_sec

        self.adaptive_vol_threshold = OptimizedAdaptiveThreshold(
            self.cfg_gms.adaptive_thresholds.vol_low_pct,
            self.cfg_gms.adaptive_thresholds.vol_high_pct,
            window_sec
        )

        # Batch priming for performance
        self._prime_adaptive_thresholds_optimized()
    else:
        # Use fixed thresholds
        self.adaptive_vol_threshold = None
```

### 3.3 Phase 3: Configuration Migration (Week 3)

**Objective:** Update configuration schema and provide migration tools

**Tasks:**
1. **Update settings.py** with unified GMSSettings model
2. **Create configuration migration utility** for existing configs
3. **Update base.yaml** with new unified structure
4. **Implement backward compatibility layer** for existing configurations
5. **Create validation tools** to ensure configuration correctness

**Migration Utility:**
```python
def migrate_gms_config(old_config_path: str, new_config_path: str):
    """Migrate existing GMS configuration to unified format."""
    with open(old_config_path, 'r') as f:
        config = yaml.safe_load(f)

    # Extract detector type
    detector_type = config.get('regime', {}).get('detector_type', 'continuous_gms')
    mode = 'legacy' if detector_type == 'granular_microstructure' else 'continuous'

    # Create unified GMS section
    unified_gms = {
        'mode': mode,
        'detector_type': detector_type,  # For backward compatibility
        'thresholds': {
            'legacy': extract_legacy_thresholds(config),
            'continuous': extract_continuous_thresholds(config)
        },
        'adaptive_thresholds': extract_adaptive_config(config),
        'performance': extract_performance_config(config)
    }

    # Update configuration
    config['gms'] = unified_gms

    with open(new_config_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
```

### 3.4 Phase 4: Testing & Validation (Week 4)

**Objective:** Comprehensive testing and performance validation

**Tasks:**
1. **Create unit tests** for both detector modes
2. **Implement integration tests** with data pipelines
3. **Performance regression testing** against baselines
4. **Result consistency validation** (legacy: 184 trades, continuous: optimized)
5. **Configuration validation testing** for all migration scenarios

**Test Structure:**
```python
class TestUnifiedGMSDetector:
    def test_legacy_mode_consistency(self):
        """Ensure legacy mode produces identical results to original detector."""
        # Load baseline configuration
        config = load_legacy_config()

        # Test with original detector
        original_detector = GranularMicrostructureRegimeDetector(config)
        original_results = run_backtest_with_detector(original_detector)

        # Test with unified detector in legacy mode
        unified_detector = UnifiedGMSDetector(config)
        unified_results = run_backtest_with_detector(unified_detector)

        # Assert identical results
        assert original_results.trade_count == unified_results.trade_count
        assert np.allclose(original_results.returns, unified_results.returns)

    def test_continuous_mode_performance(self):
        """Ensure continuous mode achieves performance targets."""
        config = load_continuous_config()
        detector = UnifiedGMSDetector(config)

        start_time = time.time()
        results = run_backtest_with_detector(detector)
        execution_time = time.time() - start_time

        # Performance assertions
        assert execution_time < 30  # Target: <30s
        assert results.trade_count > 0  # Should generate trades
```

## 4. Backward Compatibility Strategy

### 4.1 Configuration Compatibility Matrix

| Legacy Configuration | Unified Configuration | Compatibility |
|---------------------|----------------------|---------------|
| `regime.detector_type: 'granular_microstructure'` | `gms.mode: 'legacy'` | ✅ Auto-mapped |
| `regime.detector_type: 'continuous_gms'` | `gms.mode: 'continuous'` | ✅ Auto-mapped |
| `regime.gms_vol_high_thresh: 0.92` | `gms.thresholds.legacy.vol_high: 0.92` | ✅ Fallback supported |
| `gms.detector_type: 'continuous_gms'` | `gms.mode: 'continuous'` | ✅ Direct mapping |
| `gms.auto_thresholds: true` | `gms.adaptive_thresholds.enabled: true` | ✅ Auto-migrated |

### 4.2 Factory Function Compatibility

```python
def get_regime_detector(config: Config) -> RegimeDetectorInterface:
    """Backward compatible factory function."""
    detector_type = getattr(config.regime, 'detector_type', 'rule_based').lower()

    # Map legacy detector types to unified detector
    if detector_type in ['granular_microstructure', 'continuous_gms']:
        return UnifiedGMSDetector(config)

    # Legacy detectors remain unchanged for non-GMS types
    elif detector_type == 'rule_based':
        return RuleBasedRegimeDetector(config)
    elif detector_type == 'hurst':
        return HurstRegimeDetector(config)
    else:
        raise ValueError(f"Unknown detector type: {detector_type}")
```

### 4.3 Output Format Compatibility

```python
def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None):
    """Maintain output format compatibility based on mode."""
    state = self._determine_state(signals)

    # Legacy mode: return string (maintains compatibility with tf-v2)
    if self.detector_mode == 'legacy':
        return state

    # Continuous mode: return dict (maintains compatibility with tf-v3)
    else:
        return {
            "state": state,
            "risk_suppressed": self._calculate_risk_suppression(signals)
        }
```

## 5. Performance Optimization Integration

### 5.1 Adaptive Threshold Optimization

**Integration Points:**
- Import `OptimizedAdaptiveThreshold` from previous optimization work
- Use batch priming instead of individual updates (655s → 5s improvement)
- Implement percentile caching for repeated calculations
- Add emergency fallback to fixed thresholds on performance issues

### 5.2 Data Pipeline Optimization

**Legacy Mode Optimizations:**
- Maintain `skip_l2_raw_processing_if_1h_features_exist: true` flag
- Use raw2/resampled_l2 pipeline for optimal performance
- Preserve 48.95s baseline performance

**Continuous Mode Optimizations:**
- Integrate vectorized operations from adaptive threshold analysis
- Use memory pools for frequent allocations
- Implement incremental update algorithms

### 5.3 Performance Monitoring

```python
class PerformanceMonitor:
    """Monitor detector performance and trigger optimizations."""

    def __init__(self, detector: UnifiedGMSDetector):
        self.detector = detector
        self.performance_metrics = {}

    def monitor_priming_performance(self):
        """Monitor adaptive threshold priming performance."""
        if self.detector.detector_mode == 'continuous':
            start_time = time.time()
            self.detector._prime_adaptive_thresholds()
            priming_time = time.time() - start_time

            if priming_time > 10:  # Performance threshold
                self.logger.warning(f"Slow priming detected: {priming_time:.2f}s")
                # Trigger emergency fallback to fixed thresholds
                self.detector._fallback_to_fixed_thresholds()
```

## 6. Success Criteria & Validation

### 6.1 Functional Requirements

✅ **Legacy Mode Validation:**
- Produces identical results to GranularMicrostructureRegimeDetector
- Maintains 48.95s performance baseline
- Generates exactly 184 trades on baseline dataset
- Returns string regime states for tf-v2 compatibility

✅ **Continuous Mode Validation:**
- Achieves <30s performance with optimized adaptive thresholds
- Generates >0 trades on test dataset
- Returns dict format for tf-v3 compatibility
- Supports both fixed and adaptive threshold modes

✅ **Configuration Validation:**
- All existing configurations migrate successfully
- Backward compatibility maintained for all legacy paths
- New unified configuration schema works correctly
- Validation errors provide clear guidance

### 6.2 Performance Requirements

| Mode | Current Performance | Target Performance | Success Criteria |
|------|-------------------|-------------------|------------------|
| **Legacy** | 48.95s | 48.95s | ✅ Maintain baseline |
| **Continuous (Fixed)** | ~8s | <10s | ✅ Fast fixed thresholds |
| **Continuous (Adaptive)** | 663.45s | <30s | ✅ Optimized adaptive |

### 6.3 Code Quality Requirements

✅ **Architecture:**
- Zero code duplication between modes
- Clean separation of concerns
- Comprehensive error handling
- Extensive logging and monitoring

✅ **Testing:**
- 100% test coverage for core functionality
- Performance regression tests
- Configuration migration tests
- Integration tests with data pipelines

✅ **Documentation:**
- Complete API documentation
- Migration guide for existing users
- Performance optimization guide
- Troubleshooting documentation

## Conclusion

The unified GMS detector architecture provides a clean, maintainable solution that eliminates code duplication while preserving backward compatibility and enabling performance optimizations. The phased implementation approach ensures minimal risk while delivering significant architectural improvements.

**Expected Outcomes:**
- **50% reduction in GMS-related code complexity**
- **100% backward compatibility** with existing configurations
- **97% performance improvement** for continuous mode (663s → 20s)
- **Unified configuration interface** for all GMS functionality
- **Foundation for future enhancements** and optimizations

The implementation guide provides concrete steps, code examples, and validation criteria to ensure successful execution of this architectural unification.

## 7. Detailed Implementation Guide

### 7.1 Step 1: Create UnifiedGMSDetector Class

**File:** `hyperliquid_bot/core/unified_gms_detector.py`

```python
"""
Unified GMS Detector Implementation
Merges GranularMicrostructureRegimeDetector and ContinuousGMSDetector functionality.
"""

import logging
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime, timedelta

from hyperliquid_bot.config.settings import Config
from hyperliquid_bot.core.detector import RegimeDetectorInterface
from hyperliquid_bot.utils.state_mapping import standardize_state_name


class UnifiedGMSDetector(RegimeDetectorInterface):
    """
    Unified GMS detector supporting both legacy and continuous modes.

    Modes:
    - 'legacy': Replicates GranularMicrostructureRegimeDetector behavior (48.95s, 184 trades)
    - 'continuous': Provides ContinuousGMSDetector functionality with optimizations
    """

    def __init__(self, config: Config):
        super().__init__(config)

        # Core configuration resolution
        self.detector_mode = self._resolve_detector_mode(config)
        self.detector_type = self._resolve_detector_type(config)

        # Configuration objects
        self.cfg_gms = self._resolve_gms_config(config)
        self.cfg_regime = config.regime
        self.cfg_micro = config.microstructure

        # Threshold resolution
        self.thresholds = self._resolve_thresholds(config)

        # Mode-specific initialization
        if self.detector_mode == 'legacy':
            self._init_legacy_mode()
        else:
            self._init_continuous_mode()

        # Common initialization
        self._init_common_components()

        self.logger.info(f"Initialized UnifiedGMSDetector in {self.detector_mode} mode")

    def _resolve_detector_mode(self, config: Config) -> str:
        """Resolve detector mode with multiple fallback paths for compatibility."""
        # Priority 1: New unified mode setting
        if hasattr(config, 'gms') and hasattr(config.gms, 'mode'):
            mode = config.gms.mode.lower()
            if mode in ['legacy', 'granular_microstructure', 'static']:
                return 'legacy'
            elif mode in ['continuous', 'continuous_gms', 'adaptive']:
                return 'continuous'

        # Priority 2: Legacy detector_type in gms section
        if hasattr(config, 'gms') and hasattr(config.gms, 'detector_type'):
            detector_type = config.gms.detector_type.lower()
            if detector_type == 'granular_microstructure':
                return 'legacy'
            elif detector_type == 'continuous_gms':
                return 'continuous'

        # Priority 3: Legacy detector_type in regime section
        if hasattr(config.regime, 'detector_type'):
            detector_type = config.regime.detector_type.lower()
            if detector_type == 'granular_microstructure':
                return 'legacy'
            elif detector_type == 'continuous_gms':
                return 'continuous'

        # Default to continuous mode
        self.logger.warning("No detector mode specified, defaulting to continuous")
        return 'continuous'

    def _resolve_detector_type(self, config: Config) -> str:
        """Resolve detector type for backward compatibility."""
        if self.detector_mode == 'legacy':
            return 'granular_microstructure'
        else:
            return 'continuous_gms'

    def _resolve_gms_config(self, config: Config) -> Any:
        """Resolve GMS configuration with fallback hierarchy."""
        # Try new unified gms section first
        if hasattr(config, 'gms'):
            return config.gms

        # Fallback to detector-specific sections in regime
        if self.detector_mode == 'legacy':
            return getattr(config.regime, 'granular_microstructure', None)
        else:
            return getattr(config.regime, 'continuous_gms', None)

    def _resolve_thresholds(self, config: Config) -> Dict[str, float]:
        """Resolve threshold values with unified configuration support."""
        thresholds = {}

        # Try unified threshold configuration first
        if (hasattr(config, 'gms') and hasattr(config.gms, 'thresholds')):
            mode_thresholds = getattr(config.gms.thresholds, self.detector_mode, None)
            if mode_thresholds:
                return {
                    'vol_high': getattr(mode_thresholds, 'vol_high', 0.92),
                    'vol_low': getattr(mode_thresholds, 'vol_low', 0.55),
                    'mom_strong': getattr(mode_thresholds, 'mom_strong', 100.0),
                    'mom_weak': getattr(mode_thresholds, 'mom_weak', 50.0),
                    'spread_std_high': getattr(mode_thresholds, 'spread_std_high', 0.0005),
                    'spread_mean_low': getattr(mode_thresholds, 'spread_mean_low', 0.0001),
                }

        # Fallback to detector-specific configuration
        detector_config = None
        if self.detector_mode == 'legacy':
            detector_config = getattr(config.regime, 'granular_microstructure', None)
            defaults = {'vol_high': 0.92, 'vol_low': 0.55, 'mom_strong': 100.0, 'mom_weak': 50.0}
        else:
            detector_config = getattr(config.regime, 'continuous_gms', None)
            defaults = {'vol_high': 0.03, 'vol_low': 0.01, 'mom_strong': 2.5, 'mom_weak': 0.5}

        if detector_config:
            return {
                'vol_high': getattr(detector_config, 'gms_vol_high_thresh', defaults['vol_high']),
                'vol_low': getattr(detector_config, 'gms_vol_low_thresh', defaults['vol_low']),
                'mom_strong': getattr(detector_config, 'gms_mom_strong_thresh', defaults['mom_strong']),
                'mom_weak': getattr(detector_config, 'gms_mom_weak_thresh', defaults['mom_weak']),
                'spread_std_high': getattr(detector_config, 'gms_spread_std_high_thresh', 0.0005),
                'spread_mean_low': getattr(detector_config, 'gms_spread_mean_low_thresh', 0.0001),
            }

        # Final fallback to regime-level settings
        return {
            'vol_high': getattr(config.regime, 'gms_vol_high_thresh', defaults['vol_high']),
            'vol_low': getattr(config.regime, 'gms_vol_low_thresh', defaults['vol_low']),
            'mom_strong': getattr(config.regime, 'gms_mom_strong_thresh', defaults['mom_strong']),
            'mom_weak': getattr(config.regime, 'gms_mom_weak_thresh', defaults['mom_weak']),
            'spread_std_high': getattr(config.regime, 'gms_spread_std_high_thresh', 0.0005),
            'spread_mean_low': getattr(config.regime, 'gms_spread_mean_low_thresh', 0.0001),
        }

    def _init_legacy_mode(self):
        """Initialize legacy mode (granular_microstructure) settings."""
        self.cadence_sec = 3600  # Hourly updates
        self.output_format = 'string'
        self.data_source = 'raw2'
        self.depth_levels = 5  # Fixed depth for legacy

        # Legacy-specific settings
        self.use_adx_confirmation = getattr(self.cfg_regime, 'gms_use_adx_confirmation', False)
        self.use_funding_confirmation = getattr(self.cfg_regime, 'gms_use_funding_confirmation', False)

        # No adaptive thresholds in legacy mode
        self.adaptive_vol_threshold = None
        self.adaptive_mom_threshold = None

        # Performance optimization for legacy
        self.skip_l2_processing = getattr(self.cfg_gms, 'skip_l2_raw_processing_if_1h_features_exist', True)

        self.logger.info("Initialized legacy mode: 3600s cadence, fixed thresholds, raw2 data source")

    def _init_continuous_mode(self):
        """Initialize continuous mode (continuous_gms) settings."""
        self.cadence_sec = getattr(self.cfg_gms, 'cadence_sec', 60)
        self.output_format = 'dict'
        self.data_source = 'features_1s'
        self.depth_levels = getattr(self.cfg_micro, 'depth_levels', 5)

        # Continuous-specific settings
        self.output_states = getattr(self.cfg_gms, 'output_states', 8)
        self.risk_suppressed_notional_frac = getattr(self.cfg_gms, 'risk_suppressed_notional_frac', 0.25)
        self.risk_suppressed_pnl_atr_mult = getattr(self.cfg_gms, 'risk_suppressed_pnl_atr_mult', 1.5)

        # Initialize adaptive thresholds if enabled
        self._init_adaptive_thresholds()

        self.logger.info(f"Initialized continuous mode: {self.cadence_sec}s cadence, "
                        f"depth_levels={self.depth_levels}, adaptive_thresholds={self.adaptive_vol_threshold is not None}")

    def _init_adaptive_thresholds(self):
        """Initialize adaptive thresholds for continuous mode."""
        adaptive_config = getattr(self.cfg_gms, 'adaptive_thresholds', None)

        if (adaptive_config and getattr(adaptive_config, 'enabled', False)):
            try:
                from hyperliquid_bot.utils.optimized_adaptive_threshold import OptimizedAdaptiveThreshold

                # Use optimized implementation
                window_sec = getattr(adaptive_config, 'percentile_window_sec', 86400)

                self.adaptive_vol_threshold = OptimizedAdaptiveThreshold(
                    getattr(adaptive_config, 'vol_low_pct', 0.15),
                    getattr(adaptive_config, 'vol_high_pct', 0.50),
                    window_sec
                )

                self.adaptive_mom_threshold = OptimizedAdaptiveThreshold(
                    getattr(adaptive_config, 'mom_low_pct', 0.15),
                    getattr(adaptive_config, 'mom_high_pct', 0.50),
                    window_sec
                )

                # Batch priming for performance
                priming_hours = getattr(adaptive_config, 'priming_hours', 1)
                self._prime_adaptive_thresholds_optimized(priming_hours)

                self.logger.info("Initialized optimized adaptive thresholds")

            except ImportError:
                self.logger.warning("OptimizedAdaptiveThreshold not available, falling back to fixed thresholds")
                self.adaptive_vol_threshold = None
                self.adaptive_mom_threshold = None
            except Exception as e:
                self.logger.error(f"Error initializing adaptive thresholds: {e}, falling back to fixed")
                self.adaptive_vol_threshold = None
                self.adaptive_mom_threshold = None
        else:
            # Use fixed thresholds
            self.adaptive_vol_threshold = None
            self.adaptive_mom_threshold = None

    def _init_common_components(self):
        """Initialize components common to both modes."""
        # Signal column names (depth-agnostic)
        self.ATR_COL = 'atr_percent'
        self.ATR_PCT_COL = 'atr_percent_sec' if self.detector_mode == 'continuous' else 'atr_percent'

        # State tracking
        self.current_state = "Unknown"
        self.risk_suppressed = False
        self.last_update_time = 0

        # OBI thresholds from microstructure config
        self.obi_strong_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_strong_confirm_thresh', 0.20)
        self.obi_weak_confirm_thresh = getattr(self.cfg_micro, 'gms_obi_weak_confirm_thresh', 0.11)
```

### 7.2 Step 2: Implement Core Detection Logic

```python
    @property
    def required_signals(self) -> List[str]:
        """Returns the list of signals needed by the unified detector."""
        # Base signals always needed
        signals_needed = [
            'timestamp',
            self.ATR_PCT_COL,   # Volatility (mode-specific)
            'ma_slope',         # Momentum
            f'obi_smoothed_{self.depth_levels}',  # Depth-specific OBI
            'spread_mean',      # Base Spread Context
            'spread_std'        # Base Spread Context
        ]

        # Mode-specific additional signals
        if self.detector_mode == 'legacy':
            # Legacy mode may need additional signals
            if self.use_adx_confirmation:
                signals_needed.append('adx')
            if self.use_funding_confirmation:
                signals_needed.append('funding_rate')
        else:
            # Continuous mode additional signals
            if self.adaptive_mom_threshold is not None:
                signals_needed.append('ma_slope_ema_30s')  # Preferred momentum signal

        return signals_needed

    def get_regime(self, signals: dict, price_history: Optional[pd.Series] = None) -> Union[str, Dict[str, Any]]:
        """
        Unified regime detection with mode-specific output formatting.

        Returns:
            str: Regime state for legacy mode
            dict: {"state": str, "risk_suppressed": bool} for continuous mode
        """
        # Check for required signals and handle missing data
        if not self._validate_signals(signals):
            if self.detector_mode == 'legacy':
                return "Unknown"
            else:
                return {"state": "Unknown", "risk_suppressed": False}

        # Update adaptive thresholds if enabled
        if self.detector_mode == 'continuous' and self.adaptive_vol_threshold is not None:
            self._update_adaptive_thresholds(signals)

        # Core regime detection logic
        state = self._determine_state(signals)

        # Update internal state
        self.current_state = state
        self.last_update_time = signals.get('timestamp', 0)

        # Mode-specific output formatting
        if self.detector_mode == 'legacy':
            return state  # String output for legacy mode
        else:
            # Calculate risk suppression for continuous mode
            self.risk_suppressed = self._calculate_risk_suppression(signals)

            # Handle state collapse if configured
            if self.output_states < 8:
                state = self._collapse_state(state)

            return {
                "state": state,
                "risk_suppressed": self.risk_suppressed
            }

    def _validate_signals(self, signals: dict) -> bool:
        """Validate required signals are present and not NaN."""
        required = self.required_signals

        for signal in required:
            if signal == 'timestamp':
                continue  # Skip timestamp validation

            value = signals.get(signal, np.nan)
            if pd.isna(value):
                if self.detector_mode == 'legacy':
                    # Legacy mode is more lenient with missing signals
                    self.logger.debug(f"Missing signal in legacy mode: {signal}")
                    continue
                else:
                    # Continuous mode requires all signals
                    self.logger.warning(f"Missing required signal: {signal}")
                    return False

        return True

    def _determine_state(self, signals: dict) -> str:
        """
        Core regime detection logic unified for both modes.

        This implements the granular microstructure logic with mode-specific
        threshold resolution and signal handling.
        """
        if not getattr(self.cfg_regime, 'use_filter', True):
            return "Filter_Off"

        # Get current threshold values (fixed or adaptive)
        vol_thresholds = self._get_current_vol_thresholds()
        mom_thresholds = self._get_current_mom_thresholds()

        # Extract signals with fallbacks
        atr_pct = signals.get(self.ATR_PCT_COL, np.nan)
        ma_slope = signals.get('ma_slope', np.nan)

        # Use enhanced momentum signal if available (continuous mode)
        if self.detector_mode == 'continuous' and 'ma_slope_ema_30s' in signals:
            ma_slope = signals.get('ma_slope_ema_30s', ma_slope)

        obi_col = f'obi_smoothed_{self.depth_levels}'
        obi_value = signals.get(obi_col, np.nan)
        spread_mean = signals.get('spread_mean', np.nan)
        spread_std = signals.get('spread_std', np.nan)

        # Validate core signals
        if any(pd.isna(val) for val in [atr_pct, ma_slope, obi_value, spread_mean, spread_std]):
            return "Unknown"

        # Volatility classification
        if atr_pct >= vol_thresholds['high']:
            vol_regime = "High"
        elif atr_pct <= vol_thresholds['low']:
            vol_regime = "Low"
        else:
            vol_regime = "Medium"

        # Momentum classification
        abs_ma_slope = abs(ma_slope)
        if abs_ma_slope >= mom_thresholds['strong']:
            mom_regime = "Strong"
        elif abs_ma_slope <= mom_thresholds['weak']:
            mom_regime = "Weak"
        else:
            mom_regime = "Medium"

        # Direction classification
        direction = "Bull" if ma_slope > 0 else "Bear"

        # OBI confirmation (mode-specific thresholds)
        obi_confirms = self._check_obi_confirmation(obi_value, direction)

        # Spread-based regime detection
        spread_regime = self._classify_spread_regime(spread_mean, spread_std)

        # Combine classifications into final state
        final_state = self._combine_classifications(
            vol_regime, mom_regime, direction, obi_confirms, spread_regime
        )

        # Apply mode-specific filters
        if self.detector_mode == 'legacy':
            final_state = self._apply_legacy_filters(final_state, signals)
        else:
            final_state = self._apply_continuous_filters(final_state, signals)

        return standardize_state_name(final_state)

    def _get_current_vol_thresholds(self) -> Dict[str, float]:
        """Get current volatility thresholds (fixed or adaptive)."""
        if self.adaptive_vol_threshold is not None:
            low_thresh, high_thresh = self.adaptive_vol_threshold.get_current_thresholds()
            if low_thresh is not None and high_thresh is not None:
                return {'low': low_thresh, 'high': high_thresh}

        # Fallback to fixed thresholds
        return {
            'low': self.thresholds['vol_low'],
            'high': self.thresholds['vol_high']
        }

    def _get_current_mom_thresholds(self) -> Dict[str, float]:
        """Get current momentum thresholds (fixed or adaptive)."""
        if self.adaptive_mom_threshold is not None:
            weak_thresh, strong_thresh = self.adaptive_mom_threshold.get_current_thresholds()
            if weak_thresh is not None and strong_thresh is not None:
                return {'weak': weak_thresh, 'strong': strong_thresh}

        # Fallback to fixed thresholds
        return {
            'weak': self.thresholds['mom_weak'],
            'strong': self.thresholds['mom_strong']
        }
```

### 7.3 Step 3: Update Factory Function

**File:** `hyperliquid_bot/core/detector.py`

```python
def get_regime_detector(config: Config) -> RegimeDetectorInterface:
    """
    Updated factory function supporting unified detector.
    """
    detector_type = getattr(config.regime, 'detector_type', 'rule_based').lower()
    logger = logging.getLogger("RegimeFactory")

    if detector_type == 'hurst':
        logger.info("Instantiating HurstRegimeDetector.")
        required_hurst_attrs = ['hurst_lookback_periods', 'hurst_trending_threshold',
                               'hurst_ranging_threshold', 'hurst_min_series_length']
        missing_attrs = [attr for attr in required_hurst_attrs if not hasattr(config.regime, attr)]
        if missing_attrs:
            raise AttributeError(f"Missing Hurst config attrs: {missing_attrs}")
        return HurstRegimeDetector(config)

    elif detector_type == 'rule_based':
        logger.info("Instantiating RuleBasedRegimeDetector.")
        return RuleBasedRegimeDetector(config)

    elif detector_type in ['granular_microstructure', 'continuous_gms']:
        # Use unified detector for both GMS types
        logger.info(f"Instantiating UnifiedGMSDetector for type: {detector_type}")

        # Import unified detector
        from hyperliquid_bot.core.unified_gms_detector import UnifiedGMSDetector

        # Validation for required config attributes
        required_attrs = _validate_unified_gms_config(config, detector_type)
        if required_attrs:
            raise AttributeError(f"Missing GMS config attrs: {required_attrs}")

        return UnifiedGMSDetector(config)

    else:
        raise ValueError(f"Unknown regime detector type specified in config: '{detector_type}'")


def _validate_unified_gms_config(config: Config, detector_type: str) -> List[str]:
    """Validate configuration for unified GMS detector."""
    missing_attrs = []

    # Check for basic regime configuration
    required_regime_attrs = ['gms_vol_high_thresh', 'gms_vol_low_thresh',
                           'gms_mom_strong_thresh', 'gms_mom_weak_thresh']

    # Check regime section
    for attr in required_regime_attrs:
        if not hasattr(config.regime, attr):
            # Check if it's in detector-specific section
            detector_section = getattr(config.regime, detector_type, None)
            if not detector_section or not hasattr(detector_section, attr):
                # Check if it's in unified gms section
                if not (hasattr(config, 'gms') and hasattr(config.gms, 'thresholds')):
                    missing_attrs.append(attr)

    # Check microstructure configuration
    required_micro_attrs = ['gms_obi_strong_confirm_thresh', 'gms_obi_weak_confirm_thresh']
    for attr in required_micro_attrs:
        if not hasattr(config.microstructure, attr):
            missing_attrs.append(f"microstructure.{attr}")

    return missing_attrs
```

### 7.4 Step 4: Configuration Migration Tools

**File:** `hyperliquid_bot/utils/config_migration.py`

```python
"""
Configuration migration utilities for unified GMS detector.
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional


class GMSConfigMigrator:
    """Migrate existing GMS configurations to unified format."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def migrate_config_file(self, input_path: str, output_path: str) -> bool:
        """
        Migrate a configuration file to unified GMS format.

        Args:
            input_path: Path to existing configuration file
            output_path: Path for migrated configuration file

        Returns:
            bool: True if migration successful
        """
        try:
            # Load existing configuration
            with open(input_path, 'r') as f:
                config = yaml.safe_load(f)

            # Perform migration
            migrated_config = self.migrate_config_dict(config)

            # Save migrated configuration
            with open(output_path, 'w') as f:
                yaml.dump(migrated_config, f, default_flow_style=False, indent=2)

            self.logger.info(f"Successfully migrated configuration: {input_path} -> {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error migrating configuration: {e}")
            return False

    def migrate_config_dict(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate configuration dictionary to unified format.

        Args:
            config: Original configuration dictionary

        Returns:
            Dict: Migrated configuration dictionary
        """
        migrated = config.copy()

        # Extract detector type and mode
        detector_type = self._extract_detector_type(config)
        mode = 'legacy' if detector_type == 'granular_microstructure' else 'continuous'

        # Create unified GMS section
        unified_gms = self._create_unified_gms_section(config, mode, detector_type)

        # Update configuration
        migrated['gms'] = unified_gms

        # Add backward compatibility comments
        migrated['# Migration Info'] = {
            'migrated_from': detector_type,
            'unified_mode': mode,
            'migration_date': str(datetime.now().date()),
            'backward_compatible': True
        }

        return migrated

    def _extract_detector_type(self, config: Dict[str, Any]) -> str:
        """Extract detector type from existing configuration."""
        # Check regime section
        regime = config.get('regime', {})
        if 'detector_type' in regime:
            return regime['detector_type']

        # Check gms section
        gms = config.get('gms', {})
        if 'detector_type' in gms:
            return gms['detector_type']

        # Default to continuous_gms
        return 'continuous_gms'

    def _create_unified_gms_section(self, config: Dict[str, Any], mode: str, detector_type: str) -> Dict[str, Any]:
        """Create unified GMS configuration section."""
        unified = {
            'mode': mode,
            'detector_type': detector_type,  # For backward compatibility
        }

        # Extract operational settings
        unified.update(self._extract_operational_settings(config, mode))

        # Extract threshold configuration
        unified['thresholds'] = self._extract_threshold_configuration(config)

        # Extract adaptive threshold configuration
        unified['adaptive_thresholds'] = self._extract_adaptive_configuration(config)

        # Extract performance settings
        unified['performance'] = self._extract_performance_settings(config, mode)

        # Extract risk suppression settings
        unified['risk_suppression'] = self._extract_risk_suppression_settings(config)

        return unified

    def _extract_operational_settings(self, config: Dict[str, Any], mode: str) -> Dict[str, Any]:
        """Extract operational settings."""
        settings = {}

        # Cadence (auto-resolved based on mode if not specified)
        gms = config.get('gms', {})
        regime = config.get('regime', {})

        cadence = gms.get('cadence_sec')
        if cadence is None:
            # Auto-resolve based on mode
            cadence = 3600 if mode == 'legacy' else 60

        settings['cadence_sec'] = cadence
        settings['output_states'] = gms.get('output_states', 8)
        settings['state_collapse_map_file'] = gms.get('state_collapse_map_file', 'configs/gms_state_mapping.yaml')
        settings['use_four_state_mapping'] = gms.get('use_four_state_mapping', False)

        return settings

    def _extract_threshold_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Extract threshold configuration for both modes."""
        thresholds = {
            'legacy': self._extract_mode_thresholds(config, 'granular_microstructure'),
            'continuous': self._extract_mode_thresholds(config, 'continuous_gms')
        }

        return thresholds

    def _extract_mode_thresholds(self, config: Dict[str, Any], detector_type: str) -> Dict[str, Any]:
        """Extract thresholds for a specific detector mode."""
        regime = config.get('regime', {})
        detector_section = regime.get(detector_type, {})

        # Default values based on detector type
        if detector_type == 'granular_microstructure':
            defaults = {
                'vol_high': 0.92, 'vol_low': 0.55,
                'mom_strong': 100.0, 'mom_weak': 50.0,
                'spread_std_high': 0.000050, 'spread_mean_low': 0.000045
            }
        else:
            defaults = {
                'vol_high': 0.03, 'vol_low': 0.01,
                'mom_strong': 2.5, 'mom_weak': 0.5,
                'spread_std_high': 0.0005, 'spread_mean_low': 0.0001
            }

        # Extract values with fallbacks
        return {
            'vol_high': detector_section.get('gms_vol_high_thresh',
                       regime.get('gms_vol_high_thresh', defaults['vol_high'])),
            'vol_low': detector_section.get('gms_vol_low_thresh',
                      regime.get('gms_vol_low_thresh', defaults['vol_low'])),
            'mom_strong': detector_section.get('gms_mom_strong_thresh',
                         regime.get('gms_mom_strong_thresh', defaults['mom_strong'])),
            'mom_weak': detector_section.get('gms_mom_weak_thresh',
                       regime.get('gms_mom_weak_thresh', defaults['mom_weak'])),
            'spread_std_high': detector_section.get('gms_spread_std_high_thresh',
                              regime.get('gms_spread_std_high_thresh', defaults['spread_std_high'])),
            'spread_mean_low': detector_section.get('gms_spread_mean_low_thresh',
                              regime.get('gms_spread_mean_low_thresh', defaults['spread_mean_low']))
        }

    def _extract_adaptive_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Extract adaptive threshold configuration."""
        gms = config.get('gms', {})

        return {
            'enabled': gms.get('auto_thresholds', False),
            'mode': 'optimized',  # Always use optimized mode
            'percentile_window_sec': gms.get('percentile_window_sec', 86400),
            'priming_hours': 1,  # Reduced for performance
            'vol_low_pct': gms.get('vol_low_pct', 0.15),
            'vol_high_pct': gms.get('vol_high_pct', 0.50),
            'mom_low_pct': gms.get('mom_low_pct', 0.15),
            'mom_high_pct': gms.get('mom_high_pct', 0.50)
        }

    def _extract_performance_settings(self, config: Dict[str, Any], mode: str) -> Dict[str, Any]:
        """Extract performance optimization settings."""
        regime = config.get('regime', {})
        detector_section = regime.get('granular_microstructure' if mode == 'legacy' else 'continuous_gms', {})

        return {
            'skip_l2_raw_processing_if_1h_features_exist': detector_section.get(
                'skip_l2_raw_processing_if_1h_features_exist', mode == 'legacy'),
            'use_vectorized_operations': True,
            'cache_percentiles': True
        }

    def _extract_risk_suppression_settings(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Extract risk suppression settings."""
        gms = config.get('gms', {})

        return {
            'enabled': True,
            'notional_frac': gms.get('risk_suppressed_notional_frac', 0.25),
            'pnl_atr_mult': gms.get('risk_suppressed_pnl_atr_mult', 1.5)
        }


def migrate_gms_config(input_path: str, output_path: str) -> bool:
    """
    Convenience function to migrate GMS configuration.

    Args:
        input_path: Path to existing configuration file
        output_path: Path for migrated configuration file

    Returns:
        bool: True if migration successful
    """
    migrator = GMSConfigMigrator()
    return migrator.migrate_config_file(input_path, output_path)


def validate_migrated_config(config_path: str) -> Dict[str, Any]:
    """
    Validate a migrated configuration file.

    Args:
        config_path: Path to configuration file to validate

    Returns:
        Dict: Validation results with 'valid' boolean and 'issues' list
    """
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)

        issues = []

        # Check for unified GMS section
        if 'gms' not in config:
            issues.append("Missing unified 'gms' section")
        else:
            gms = config['gms']

            # Check required fields
            required_fields = ['mode', 'thresholds', 'adaptive_thresholds']
            for field in required_fields:
                if field not in gms:
                    issues.append(f"Missing required field: gms.{field}")

            # Check threshold structure
            if 'thresholds' in gms:
                for mode in ['legacy', 'continuous']:
                    if mode not in gms['thresholds']:
                        issues.append(f"Missing threshold section: gms.thresholds.{mode}")

        # Check backward compatibility
        regime = config.get('regime', {})
        if 'detector_type' not in regime:
            issues.append("Missing regime.detector_type for backward compatibility")

        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'config': config
        }

    except Exception as e:
        return {
            'valid': False,
            'issues': [f"Error loading configuration: {e}"],
            'config': None
        }
```

### 7.5 Step 5: Testing Framework

**File:** `tests/test_unified_gms_detector.py`

```python
"""
Comprehensive test suite for UnifiedGMSDetector.
"""

import pytest
import numpy as np
import pandas as pd
import time
from unittest.mock import Mock, patch
from typing import Dict, Any

from hyperliquid_bot.core.unified_gms_detector import UnifiedGMSDetector
from hyperliquid_bot.config.settings import Config


class TestUnifiedGMSDetector:
    """Test suite for UnifiedGMSDetector."""

    @pytest.fixture
    def legacy_config(self):
        """Create configuration for legacy mode."""
        config = Mock(spec=Config)

        # Regime configuration
        config.regime = Mock()
        config.regime.detector_type = 'granular_microstructure'
        config.regime.use_filter = True
        config.regime.gms_vol_high_thresh = 0.92
        config.regime.gms_vol_low_thresh = 0.55
        config.regime.gms_mom_strong_thresh = 100.0
        config.regime.gms_mom_weak_thresh = 50.0
        config.regime.gms_spread_std_high_thresh = 0.000050
        config.regime.gms_spread_mean_low_thresh = 0.000045
        config.regime.gms_use_adx_confirmation = False
        config.regime.gms_use_funding_confirmation = False

        # Microstructure configuration
        config.microstructure = Mock()
        config.microstructure.depth_levels = 5
        config.microstructure.gms_obi_strong_confirm_thresh = 0.20
        config.microstructure.gms_obi_weak_confirm_thresh = 0.11

        # GMS configuration (optional)
        config.gms = Mock()
        config.gms.detector_type = 'granular_microstructure'
        config.gms.skip_l2_raw_processing_if_1h_features_exist = True

        return config

    @pytest.fixture
    def continuous_config(self):
        """Create configuration for continuous mode."""
        config = Mock(spec=Config)

        # Regime configuration
        config.regime = Mock()
        config.regime.detector_type = 'continuous_gms'
        config.regime.use_filter = True
        config.regime.gms_vol_high_thresh = 0.03
        config.regime.gms_vol_low_thresh = 0.01
        config.regime.gms_mom_strong_thresh = 2.5
        config.regime.gms_mom_weak_thresh = 0.5
        config.regime.gms_spread_std_high_thresh = 0.0005
        config.regime.gms_spread_mean_low_thresh = 0.0001

        # Microstructure configuration
        config.microstructure = Mock()
        config.microstructure.depth_levels = 5
        config.microstructure.gms_obi_strong_confirm_thresh = 0.20
        config.microstructure.gms_obi_weak_confirm_thresh = 0.11

        # GMS configuration
        config.gms = Mock()
        config.gms.detector_type = 'continuous_gms'
        config.gms.cadence_sec = 60
        config.gms.output_states = 8
        config.gms.risk_suppressed_notional_frac = 0.25
        config.gms.risk_suppressed_pnl_atr_mult = 1.5

        # Adaptive thresholds (disabled by default)
        config.gms.adaptive_thresholds = Mock()
        config.gms.adaptive_thresholds.enabled = False

        return config

    @pytest.fixture
    def sample_signals(self):
        """Create sample signals for testing."""
        return {
            'timestamp': 1640995200,  # 2022-01-01 00:00:00
            'atr_percent': 0.02,
            'atr_percent_sec': 0.02,
            'ma_slope': 75.0,
            'obi_smoothed_5': 0.15,
            'spread_mean': 0.0001,
            'spread_std': 0.00005,
            'close': 50000.0
        }

    def test_legacy_mode_initialization(self, legacy_config):
        """Test initialization in legacy mode."""
        detector = UnifiedGMSDetector(legacy_config)

        assert detector.detector_mode == 'legacy'
        assert detector.detector_type == 'granular_microstructure'
        assert detector.cadence_sec == 3600
        assert detector.output_format == 'string'
        assert detector.data_source == 'raw2'
        assert detector.depth_levels == 5
        assert detector.adaptive_vol_threshold is None
        assert detector.adaptive_mom_threshold is None

    def test_continuous_mode_initialization(self, continuous_config):
        """Test initialization in continuous mode."""
        detector = UnifiedGMSDetector(continuous_config)

        assert detector.detector_mode == 'continuous'
        assert detector.detector_type == 'continuous_gms'
        assert detector.cadence_sec == 60
        assert detector.output_format == 'dict'
        assert detector.data_source == 'features_1s'
        assert detector.depth_levels == 5

    def test_threshold_resolution_legacy(self, legacy_config):
        """Test threshold resolution for legacy mode."""
        detector = UnifiedGMSDetector(legacy_config)

        assert detector.thresholds['vol_high'] == 0.92
        assert detector.thresholds['vol_low'] == 0.55
        assert detector.thresholds['mom_strong'] == 100.0
        assert detector.thresholds['mom_weak'] == 50.0

    def test_threshold_resolution_continuous(self, continuous_config):
        """Test threshold resolution for continuous mode."""
        detector = UnifiedGMSDetector(continuous_config)

        assert detector.thresholds['vol_high'] == 0.03
        assert detector.thresholds['vol_low'] == 0.01
        assert detector.thresholds['mom_strong'] == 2.5
        assert detector.thresholds['mom_weak'] == 0.5

    def test_required_signals_legacy(self, legacy_config):
        """Test required signals for legacy mode."""
        detector = UnifiedGMSDetector(legacy_config)
        required = detector.required_signals

        assert 'timestamp' in required
        assert 'atr_percent' in required  # Legacy ATR column
        assert 'ma_slope' in required
        assert 'obi_smoothed_5' in required
        assert 'spread_mean' in required
        assert 'spread_std' in required

    def test_required_signals_continuous(self, continuous_config):
        """Test required signals for continuous mode."""
        detector = UnifiedGMSDetector(continuous_config)
        required = detector.required_signals

        assert 'timestamp' in required
        assert 'atr_percent_sec' in required  # Continuous ATR column
        assert 'ma_slope' in required
        assert 'obi_smoothed_5' in required
        assert 'spread_mean' in required
        assert 'spread_std' in required

    def test_regime_detection_legacy_output(self, legacy_config, sample_signals):
        """Test regime detection returns string for legacy mode."""
        detector = UnifiedGMSDetector(legacy_config)

        # Adjust signals for legacy mode
        signals = sample_signals.copy()
        signals['atr_percent'] = 0.02  # Use legacy ATR column

        result = detector.get_regime(signals)

        assert isinstance(result, str)
        assert result != "Unknown"  # Should detect a valid regime

    def test_regime_detection_continuous_output(self, continuous_config, sample_signals):
        """Test regime detection returns dict for continuous mode."""
        detector = UnifiedGMSDetector(continuous_config)

        result = detector.get_regime(signals)

        assert isinstance(result, dict)
        assert 'state' in result
        assert 'risk_suppressed' in result
        assert isinstance(result['state'], str)
        assert isinstance(result['risk_suppressed'], bool)

    def test_signal_validation_legacy_lenient(self, legacy_config):
        """Test that legacy mode is lenient with missing signals."""
        detector = UnifiedGMSDetector(legacy_config)

        # Missing some signals
        incomplete_signals = {
            'timestamp': 1640995200,
            'atr_percent': 0.02,
            'ma_slope': 75.0,
            # Missing obi_smoothed_5, spread_mean, spread_std
        }

        result = detector.get_regime(incomplete_signals)
        assert isinstance(result, str)  # Should still return a result

    def test_signal_validation_continuous_strict(self, continuous_config):
        """Test that continuous mode is strict with missing signals."""
        detector = UnifiedGMSDetector(continuous_config)

        # Missing some signals
        incomplete_signals = {
            'timestamp': 1640995200,
            'atr_percent_sec': 0.02,
            'ma_slope': 75.0,
            # Missing obi_smoothed_5, spread_mean, spread_std
        }

        result = detector.get_regime(incomplete_signals)
        assert result == {"state": "Unknown", "risk_suppressed": False}

    def test_performance_legacy_mode(self, legacy_config, sample_signals):
        """Test performance of legacy mode."""
        detector = UnifiedGMSDetector(legacy_config)

        # Adjust signals for legacy mode
        signals = sample_signals.copy()
        signals['atr_percent'] = 0.02

        # Time multiple regime detections
        start_time = time.time()
        for _ in range(1000):
            detector.get_regime(signals)
        execution_time = time.time() - start_time

        # Should be very fast (< 1 second for 1000 calls)
        assert execution_time < 1.0

    def test_performance_continuous_mode(self, continuous_config, sample_signals):
        """Test performance of continuous mode."""
        detector = UnifiedGMSDetector(continuous_config)

        # Time multiple regime detections
        start_time = time.time()
        for _ in range(1000):
            detector.get_regime(sample_signals)
        execution_time = time.time() - start_time

        # Should be reasonably fast (< 2 seconds for 1000 calls)
        assert execution_time < 2.0

    def test_backward_compatibility_config_paths(self):
        """Test that various configuration paths work correctly."""
        # Test regime.detector_type path
        config1 = Mock(spec=Config)
        config1.regime = Mock()
        config1.regime.detector_type = 'granular_microstructure'
        config1.regime.gms_vol_high_thresh = 0.92
        config1.regime.gms_vol_low_thresh = 0.55
        config1.regime.gms_mom_strong_thresh = 100.0
        config1.regime.gms_mom_weak_thresh = 50.0
        config1.regime.gms_spread_std_high_thresh = 0.000050
        config1.regime.gms_spread_mean_low_thresh = 0.000045
        config1.microstructure = Mock()
        config1.microstructure.depth_levels = 5
        config1.microstructure.gms_obi_strong_confirm_thresh = 0.20
        config1.microstructure.gms_obi_weak_confirm_thresh = 0.11

        detector1 = UnifiedGMSDetector(config1)
        assert detector1.detector_mode == 'legacy'

        # Test gms.detector_type path
        config2 = Mock(spec=Config)
        config2.regime = Mock()
        config2.regime.gms_vol_high_thresh = 0.03
        config2.regime.gms_vol_low_thresh = 0.01
        config2.regime.gms_mom_strong_thresh = 2.5
        config2.regime.gms_mom_weak_thresh = 0.5
        config2.regime.gms_spread_std_high_thresh = 0.0005
        config2.regime.gms_spread_mean_low_thresh = 0.0001
        config2.microstructure = Mock()
        config2.microstructure.depth_levels = 5
        config2.microstructure.gms_obi_strong_confirm_thresh = 0.20
        config2.microstructure.gms_obi_weak_confirm_thresh = 0.11
        config2.gms = Mock()
        config2.gms.detector_type = 'continuous_gms'
        config2.gms.cadence_sec = 60
        config2.gms.output_states = 8
        config2.gms.adaptive_thresholds = Mock()
        config2.gms.adaptive_thresholds.enabled = False

        detector2 = UnifiedGMSDetector(config2)
        assert detector2.detector_mode == 'continuous'


class TestConfigMigration:
    """Test configuration migration functionality."""

    def test_migrate_legacy_config(self):
        """Test migration of legacy configuration."""
        from hyperliquid_bot.utils.config_migration import GMSConfigMigrator

        legacy_config = {
            'regime': {
                'detector_type': 'granular_microstructure',
                'gms_vol_high_thresh': 0.92,
                'gms_vol_low_thresh': 0.55,
                'gms_mom_strong_thresh': 100.0,
                'gms_mom_weak_thresh': 50.0,
                'granular_microstructure': {
                    'cadence_sec': 3600,
                    'skip_l2_raw_processing_if_1h_features_exist': True
                }
            },
            'microstructure': {
                'depth_levels': 5,
                'gms_obi_strong_confirm_thresh': 0.20,
                'gms_obi_weak_confirm_thresh': 0.11
            }
        }

        migrator = GMSConfigMigrator()
        migrated = migrator.migrate_config_dict(legacy_config)

        # Check unified GMS section
        assert 'gms' in migrated
        gms = migrated['gms']

        assert gms['mode'] == 'legacy'
        assert gms['detector_type'] == 'granular_microstructure'
        assert gms['cadence_sec'] == 3600

        # Check threshold migration
        assert 'thresholds' in gms
        legacy_thresholds = gms['thresholds']['legacy']
        assert legacy_thresholds['vol_high'] == 0.92
        assert legacy_thresholds['vol_low'] == 0.55

        # Check performance settings
        assert gms['performance']['skip_l2_raw_processing_if_1h_features_exist'] == True

    def test_migrate_continuous_config(self):
        """Test migration of continuous configuration."""
        from hyperliquid_bot.utils.config_migration import GMSConfigMigrator

        continuous_config = {
            'regime': {
                'detector_type': 'continuous_gms',
                'continuous_gms': {
                    'gms_vol_high_thresh': 0.03,
                    'gms_vol_low_thresh': 0.01,
                    'gms_mom_strong_thresh': 2.5,
                    'gms_mom_weak_thresh': 0.5,
                    'cadence_sec': 60
                }
            },
            'gms': {
                'auto_thresholds': True,
                'percentile_window_sec': 86400,
                'vol_low_pct': 0.15,
                'vol_high_pct': 0.50
            },
            'microstructure': {
                'depth_levels': 20,
                'gms_obi_strong_confirm_thresh': 0.20,
                'gms_obi_weak_confirm_thresh': 0.11
            }
        }

        migrator = GMSConfigMigrator()
        migrated = migrator.migrate_config_dict(continuous_config)

        # Check unified GMS section
        gms = migrated['gms']
        assert gms['mode'] == 'continuous'
        assert gms['detector_type'] == 'continuous_gms'
        assert gms['cadence_sec'] == 60

        # Check adaptive threshold migration
        adaptive = gms['adaptive_thresholds']
        assert adaptive['enabled'] == True
        assert adaptive['percentile_window_sec'] == 86400
        assert adaptive['vol_low_pct'] == 0.15
        assert adaptive['vol_high_pct'] == 0.50


if __name__ == "__main__":
    pytest.main([__file__])
```
