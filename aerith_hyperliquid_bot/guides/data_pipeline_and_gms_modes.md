# Data Pipeline and GMS Detector Modes - Complete Guide

## Executive Summary

The Aerith Hyperliquid Bot uses a multi-layered data pipeline with 4 distinct data sources feeding into 2 different GMS (Granular Microstructure) detector modes. Each mode has specific data requirements, processing frequencies, and output formats optimized for different trading scenarios.

## Data Sources Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                     RAW DATA SOURCES                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                   │
│  1. L2 Raw Order Book (/l2_raw/)                                │
│     - Tick-by-tick order book snapshots                         │
│     - Format: Arrow IPC files (BTC_HH_l2Book.arrow)            │
│     - Contains: timestamp, best_bid/ask, full depth arrays      │
│                                                                   │
│  2. Raw2 Processed L2 (/raw2/)                                  │
│     - Daily aggregated L2 snapshots                             │
│     - Format: Parquet files (YYYYMMDD_raw2.parquet)            │
│     - Contains: Hourly L2 snapshots with microstructure         │
│                                                                   │
│  3. 1-Second Features (/features_1s/)                           │
│     - Pre-computed features at 1-second intervals               │
│     - Format: Hourly Parquet files (features_HH.parquet)       │
│     - Contains: 109 features including OBI, ATR, spreads        │
│                                                                   │
│  4. OHLCV Resampled (/resampled_l2/)                           │
│     - Time-aggregated price bars (1h, 4h, etc.)                │
│     - Format: Daily Parquet files (YYYY-MM-DD_1h.parquet)      │
│     - Contains: OHLC, volume, basic microstructure              │
│                                                                   │
└─────────────────────────────────────────────────────────────────┘
```

## Data Processing Pipeline

```
┌──────────────┐      ┌──────────────┐      ┌──────────────┐
│   L2 Raw     │      │  ETL Tools   │      │   Features   │
│  (Tick Data) │ ───► │  Processing  │ ───► │  (1-second)  │
└──────────────┘      └──────────────┘      └──────────────┘
       │                                              │
       │                                              │
       ▼                                              ▼
┌──────────────┐                            ┌──────────────┐
│    Raw2      │                            │     GMS      │
│ (Hourly L2)  │ ──────────────────────────►│  Detectors   │
└──────────────┘                            └──────────────┘
       │                                              ▲
       │                                              │
       ▼                                              │
┌──────────────┐                                     │
│    OHLCV     │ ────────────────────────────────────┘
│   (1h/4h)    │
└──────────────┘
```

## GMS Detector Modes - Detailed Comparison

### 1. Granular Microstructure Mode (Legacy)

**Purpose**: Hourly regime detection optimized for longer-term trend following strategies

**Data Flow**:
```
Raw2 L2 Data (Hourly Snapshots)
         │
         ▼
┌─────────────────────────┐
│  Feature Calculation    │
│  - ATR (decimal %)      │
│  - MA Slope             │
│  - OBI (5 levels)       │
│  - Spread Stats         │
└─────────────────────────┘
         │
         ▼
┌─────────────────────────┐
│  Fixed Thresholds       │
│  - Vol: 0.92%/0.55%     │
│  - Mom: 100/50          │
│  - Spread: 5/4.5 bps    │
└─────────────────────────┘
         │
         ▼
┌─────────────────────────┐
│  State Classification   │
│  8 Granular States      │
│  (String Output)        │
└─────────────────────────┘
```

**Key Characteristics**:
- **Update Frequency**: 3600 seconds (1 hour)
- **Feature Count**: 37 features
- **Data Source**: `raw2` directory (hourly L2 snapshots)
- **Threshold Type**: FIXED ONLY (adaptive disabled to prevent overfitting)
- **Output Format**: Simple string state (e.g., "Strong_Bull_Trend")
- **Performance**: Can skip L2 processing if hourly features exist

### 2. Continuous GMS Mode (Modern)

**Purpose**: High-frequency regime detection for modern 60-second strategies

**Data Flow**:
```
Pre-computed 1s Features
         │
         ▼
┌─────────────────────────┐
│  Signal Extraction      │
│  - ATR (decimal %)      │
│  - MA Slope EMA 30s     │
│  - OBI (1-5 levels)     │
│  - Risk Metrics         │
└─────────────────────────┘
         │
         ▼
┌─────────────────────────┐
│  Adaptive Thresholds    │
│  - Vol: Dynamic         │
│  - Mom: Dynamic         │
│  - 24h percentiles      │
└─────────────────────────┘
         │
         ▼
┌─────────────────────────┐
│  State + Risk Output    │
│  Dict: {state, risk}    │
│  Optional collapse 8→3   │
└─────────────────────────┘
```

**Key Characteristics**:
- **Update Frequency**: 60 seconds (configurable)
- **Feature Count**: 109 features
- **Data Source**: `features_1s` directory (pre-computed)
- **Threshold Type**: Adaptive (adjusts to market conditions)
- **Output Format**: Dictionary with state and risk suppression
- **Performance**: Leverages pre-computed features for speed

## Feature Requirements by Mode

### Legacy Mode (Granular Microstructure)

**Required Signals**:
```python
required_signals = [
    'timestamp',
    'atr_percent',           # Volatility in decimal format
    'ma_slope',              # Momentum indicator
    'obi_smoothed_5',        # Order book imbalance (5 levels)
    'spread_mean',           # Average spread
    'spread_std',            # Spread volatility
    # Optional based on config:
    'adx',                   # If use_adx_confirmation
    'funding_rate'           # If use_funding_confirmation
]
```

### Modern Mode (Continuous GMS)

**Required Signals**:
```python
required_signals = [
    'timestamp',
    'atr_percent',           # Volatility in decimal format
    'ma_slope',              # Base momentum
    'ma_slope_ema_30s',      # Enhanced momentum (preferred)
    'obi_smoothed_N',        # OBI at configured depth (1-5)
    'spread_mean',           # Average spread
    'spread_std',            # Spread volatility
    'close',                 # Current price
    'position_size_usd',     # Risk management
    'realized_pnl_usd'       # Risk management
]
```

## Critical Configuration Notes

### Mode Selection Priority

The detector mode is resolved through a hierarchy:
1. `gms.mode` (new unified setting)
2. `gms.detector_type` 
3. `regime.detector_type`
4. Default: 'continuous'

**CRITICAL**: For proper legacy mode operation:
```yaml
regime:
  detector_type: 'granular_microstructure'
gms:
  detector_type: 'granular_microstructure'  # MUST match!
```

### Threshold Scaling (ATR Bug Fix)

Legacy thresholds were scaled down 100x to match corrected ATR units:
- **Before**: ATR reported as 92% (incorrect)
- **After**: ATR reported as 0.92% (correct decimal format)
- **Result**: Thresholds adjusted from 0.92 → 0.0092

## Data Quality Requirements

### Legacy Mode
- Tolerant of missing signals (logs warnings but continues)
- Can operate with partial data
- Fallback states for missing information

### Continuous Mode
- Requires core signals (ATR, MA slope, close price)
- Missing critical signals return "Unknown" state
- Adaptive thresholds need consistent data flow

## Performance Optimizations

### Legacy Mode
- `skip_l2_raw_processing_if_1h_features_exist`: true
- Avoids redundant L2 processing when features available
- Hourly updates reduce computational load

### Continuous Mode
- Pre-computed features eliminate real-time calculation
- Batch processing for adaptive threshold updates
- Vectorized operations for timestamp handling

## State Mapping and Output

### 8-State Granular Model
```
Strong_Bull_Trend    Weak_Bull_Trend
Strong_Bear_Trend    Weak_Bear_Trend
High_Vol_Range       Low_Vol_Range
Uncertain            Unknown/Filter_Off
```

### 3-State Collapsed Model
```
BULL (Strong_Bull + Weak_Bull)
BEAR (Strong_Bear + Weak_Bear)
CHOP (Everything else)
```

## Integration with Trading Strategies

### TF v2 (Legacy) + Granular Microstructure
- Hourly bar alignment
- Fixed threshold stability
- 37 features sufficient for trend detection

### TF v3 (Modern) + Continuous GMS
- 60-second bar alignment
- Adaptive market sensitivity
- 109 features for nuanced decisions
- Risk suppression for position management

## Summary for LLM Understanding

1. **Two Modes, Different Purposes**:
   - Legacy: Stable, hourly, fixed thresholds for long-term trends
   - Modern: Responsive, minute-level, adaptive for short-term trading

2. **Data Source Selection**:
   - Legacy uses raw L2 data processed hourly
   - Modern uses pre-computed 1-second features

3. **Key Trade-offs**:
   - Legacy: Stability and proven performance
   - Modern: Responsiveness and market adaptation

4. **Critical Implementation Details**:
   - ATR values in decimal format (0.01 = 1%)
   - Mode selection requires matching config entries
   - Adaptive thresholds only for continuous mode
   - State validation ensures valid GMS states

This architecture allows the same codebase to support both conservative long-term strategies and aggressive short-term trading while maintaining data consistency and computational efficiency.