# LLM Prompt: Salvage PRD for Continuous GMS & TF-v3 Features

## Context & Agreement with Evaluation

I **AGREE** with your critical assessment of the original roadmap being overly ambitious for HF trading. However, I need to clarify several key points:

### Development Context Clarifications
- **"New to coding" is irrelevant** - I'm using AI agents (like you) for implementation, not coding manually
- **Timeline concerns are moot** - AI agents can implement features rapidly with proper guidance
- **Focus should be on technical feasibility and profitability**, not development complexity

### Infrastructure Reality
- **VPS Budget Available**: I have budget for higher-grade VPS if the bot demonstrates profitability
- **Not running from PC**: This will be deployed on a proper VPS with adequate resources
- **Latency Reality**: Accept 500-700ms Hyperliquid latency as baseline (user reports confirm this)

## Current System Performance (CRITICAL BASELINE)

**Proven Production System:**
- **GMS Detector + TF-v2 Strategy** on 1h/4h timeframes
- **Performance**: Sharpe 3.99, 202% ROI, 6.91% max drawdown
- **Trade Frequency**: 185 trades/year (selective execution)
- **Infrastructure**: Standard VPS requirements
- **Status**: ✅ Production-ready and profitable

## Features to Salvage (Currently Untested)

### 1. Continuous GMS Detector
**Current Implementation Issues:**
- Requires 10Hz L2 data (2-5 GB/day storage)
- Sub-200ms latency requirements (unrealistic for retail)
- Complex ETL pipeline (L2 JSON → Arrow → 1s Features)
- 60-second processing cadence with microstructure analysis

**Core Value to Preserve:**
- Advanced 8-state regime classification
- Sophisticated order book imbalance (OBI) analysis
- Risk suppression signals
- Hierarchical decision logic

### 2. TF-v3 Strategy
**Current Implementation Issues:**
- Depends on Continuous GMS real-time feeds
- Requires <120-second regime staleness validation
- Complex GMS snapshot validation logic

**Core Value to Preserve:**
- Regime-aware trading (only trades in BULL/BEAR, skips CHOP)
- EMA crossover signals (20/50 EMA)
- ATR trailing stops with dynamic risk management
- 24-hour maximum position holding
- Risk suppression integration

## Technical Constraints & Requirements

### Hyperliquid Reality Check
- **Documented Latency**: ~200ms (Hyperliquid claims)
- **User Reality**: 500-700ms typical for VPS users
- **Top 5% Performance**: ~500ms even with premium VPS
- **Implication**: Sub-200ms requirements are unrealistic

### VPS Infrastructure Available
- **Budget**: Flexible for profitable bot (can scale up)
- **Target Specs**: 8-16 GB RAM, 4-8 CPU cores, 500GB+ SSD
- **Network**: Premium VPS with low-latency connection to exchanges
- **Cost Range**: $100-300/month acceptable if profitable

### Data Pipeline Simplification Needed
**Current (Unsustainable):**
```
Raw L2 JSON (10Hz) → Arrow Files → 1s Features → GMS Processing
     2-5 GB/day        Hourly        500MB/day      60s Cadence
```

**Target (Sustainable):**
```
OHLCV + Volume Profile → Simplified Features → Regime Detection
        ~10 MB/day           ~1 MB/day         Hourly Updates
```

## Your Task: Create Salvage PRD

Create a **Product Requirements Document** that:

### 1. Preserves Core Value
- Maintain regime-aware trading concept from TF-v3
- Keep sophisticated regime detection from Continuous GMS
- Preserve risk management and position sizing logic

### 2. Addresses Infrastructure Reality
- Accept 500-700ms Hyperliquid latency constraints
- Reduce data requirements by 90%+ (from GB to MB scale)
- Simplify from continuous processing to hourly updates
- Target standard VPS deployment (not HF infrastructure)

### 3. Maintains Performance Expectations
- Target 70-85% of original performance with 20% of infrastructure cost
- Keep selective trading approach (quality over quantity)
- Maintain regime-based risk management
- Preserve core TF-v3 entry/exit logic

### 4. Implementation Strategy
- **Phase 1**: Simplified GMS using OHLCV + volume data only
- **Phase 2**: TF-v3 adaptation with hourly regime updates
- **Phase 3**: Integration with existing proven GMS+TF-v2 system

### 5. Success Criteria
- **Performance**: Sharpe > 2.5, ROI > 120% (vs. current 3.99/202%)
- **Infrastructure**: <$200/month VPS cost
- **Storage**: <10 GB total data requirements
- **Latency**: Function properly with 500-700ms constraints
- **Reliability**: 95%+ uptime with standard VPS

### 6. Technical Specifications Required
- Detailed data pipeline architecture (simplified)
- Regime detection algorithm (OHLCV-based)
- TF-v3 strategy adaptation specifications
- Risk management integration approach
- Deployment and monitoring requirements

### 7. Risk Mitigation
- Fallback to proven GMS+TF-v2 if salvage fails
- Gradual rollout with paper trading validation
- Performance benchmarking against current system
- Clear success/failure criteria for each phase

## Key Constraints to Remember

1. **Must work with 500-700ms Hyperliquid latency**
2. **Must reduce infrastructure costs by 80%+**
3. **Must maintain regime-aware trading concept**
4. **Must be deployable on standard VPS**
5. **Must preserve core profitability drivers**

## Critical Success Factor

The salvaged system should be **"intelligent execution"** focused, not speed-focused. Make decisions every few seconds with high confidence rather than rapid decisions with marginal edge.

**Your goal**: Create a PRD that transforms sophisticated HF concepts into retail-viable implementations while preserving the core alpha-generating mechanisms. 