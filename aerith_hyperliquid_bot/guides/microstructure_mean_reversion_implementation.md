# Microstructure Mean Reversion Strategy Implementation Guide

## Overview

This guide provides a detailed, step-by-step implementation plan for a professional-grade microstructure mean reversion strategy that leverages Hyperliquid's transparent order book data. The strategy is designed to work alongside TF-v3 in a complementary manner, activating during specific market regimes where mean reversion is most profitable.

### Retail Setup Considerations

- **Decision Latency**: ~100ms (acceptable for mean reversion)
- **Roundtrip Latency**: ~700ms (focus on 30-300s holding periods)
- **Single Position**: Only one position at a time (BTC perps)
- **Data Available**: L2 order book snapshots, no trade data
- **Edge**: Full book transparency that most retail traders ignore

## Core Principles

1. **No Look-Ahead Bias**: All calculations must be strictly causal
2. **Regime-Aware**: Strategy activation controlled by GMS state mapping
3. **Microstructure-Based**: Uses order book dynamics, not price-based TA
4. **Test-Driven Development**: Write tests before implementation
5. **Verifiable**: Every component must be independently testable

## Strategy Architecture

### 1. Regime-Based Activation Logic

The strategy must support flexible regime-based activation:

```yaml
# Example configuration for 3-state mapping
mean_reversion:
  enabled: true
  active_regimes: ["CHOP"]  # Only active in CHOP
  
# Example configuration for 8-state mapping  
mean_reversion:
  enabled: true
  active_regimes: ["Low_Vol_Range", "Weak_Bear_Trend", "Choppy_Market"]
```

**Implementation Requirements**:
- Strategy checks current regime from GMS snapshot
- Only generates signals if regime is in `active_regimes` list
- Supports both 3-state and 8-state regime mappings
- Must handle regime transitions gracefully (exit positions on regime change)

### 2. Signal Generation Components

#### 2.1 OBI Z-Score Mean Reversion (Primary Signal)

**Formula**: 
```
obi_zscore = (obi_current - obi_mean) / obi_std
Entry: |obi_zscore| > entry_threshold
Exit: |obi_zscore| < exit_threshold
```

**Implementation Steps**:
1. Use existing `obi_zscore_5` from feature builder
2. Add configurable thresholds per regime
3. Direction based on regime and z-score sign:
   - CHOP: Both directions allowed
   - BULL-like regimes: Long-only (negative z-score)
   - BEAR-like regimes: Short-only (positive z-score)

#### 2.2 Order Book Flow Imbalance (Confirmation Signal)

**NEW FEATURE REQUIRED**

Since we don't have trade volume data, we'll use order book flow as a proxy:

**Formula**:
```
# Track changes in bid/ask sizes at top levels
bid_flow = sum(bid_size[t] - bid_size[t-1]) for levels 1-5
ask_flow = sum(ask_size[t] - ask_size[t-1]) for levels 1-5
flow_imbalance = (bid_flow - ask_flow) / (|bid_flow| + |ask_flow| + epsilon)
```

**Implementation**:
```python
def build_orderbook_flow_imbalance(df: pd.DataFrame, levels: int = 5) -> pd.Series:
    """
    Calculate order book flow imbalance from L2 changes.
    
    Positive = bid size increasing / ask size decreasing (bullish flow)
    Negative = ask size increasing / bid size decreasing (bearish flow)
    """
    flow_imbalance = pd.Series(0.0, index=df.index)
    
    for level in range(1, levels + 1):
        bid_col = f'bid_size_{level}'
        ask_col = f'ask_size_{level}'
        
        if bid_col in df.columns and ask_col in df.columns:
            # Calculate size changes (causal - no look ahead)
            bid_change = df[bid_col].diff()
            ask_change = df[ask_col].diff()
            
            # Aggregate flows
            bid_flow = bid_change.fillna(0)
            ask_flow = ask_change.fillna(0)
            
            # Add to total flow
            flow_imbalance += (bid_flow - ask_flow)
    
    # Normalize by total absolute flow
    total_flow = flow_imbalance.abs().rolling(60, min_periods=1).sum()
    normalized = flow_imbalance / (total_flow + 1e-9)
    
    return normalized.rename('orderbook_flow_imbalance')
```

#### 2.3 Spread Velocity (Risk Filter)

**NEW FEATURE REQUIRED**

**Formula**:
```
spread_velocity = (spread_t - spread_t-1) / spread_t-1
```

**Implementation**:
```python
def build_spread_velocity(df: pd.DataFrame, window: int = 5) -> pd.Series:
    """
    Calculate rate of spread change.
    Positive = widening, Negative = tightening
    """
    if 'spread_relative' not in df.columns:
        # Calculate from best_bid and best_ask
        spread = (df['best_ask'] - df['best_bid']) / df['mid_price']
    else:
        spread = df['spread_relative']
    
    # Causal calculation - no look ahead
    spread_velocity = spread.pct_change()
    
    # Smooth to reduce noise
    return spread_velocity.rolling(window, min_periods=1).mean()
```

#### 2.4 Multi-Level OBI Divergence

**Formula**:
```
obi_divergence = obi_L1_3 - obi_L1_10
```

**Implementation**:
- Use existing `raw_obi_L1_3` and `raw_obi_L1_10`
- Large positive divergence = surface bid pressure, deep ask pressure
- Large negative divergence = surface ask pressure, deep bid pressure
- Trade towards the deeper book side

### 3. Strategy Coordination (Single Position Constraint)

Since only ONE position can be open at a time:

```python
def can_enter_position(self, portfolio_state: Dict) -> bool:
    """
    Check if mean reversion can take a position.
    
    Strategy Priority Rules:
    1. If TF-v3 has position: MR cannot enter
    2. If MR has position: TF-v3 cannot enter
    3. First valid signal wins
    """
    # Check if any position exists
    if portfolio_state.get('position_size', 0) != 0:
        # Check if it's our position
        if portfolio_state.get('strategy_id') == self.strategy_name:
            return False  # We already have a position
        else:
            return False  # Another strategy has position
    
    # Check if we're in an allowed regime
    current_regime = portfolio_state.get('regime')
    return current_regime in self.active_regimes
```

**Important**: The backtester/live system must enforce this constraint at the portfolio level.

## How Both Strategies Work Together

### Simultaneous Active Operation

**Key Concept**: Both TF-v3 and Mean Reversion strategies are ALWAYS ACTIVE and monitoring the market simultaneously. The regime determines which strategy is allowed to ENTER new positions, not which strategy is "running".

#### System Architecture

```
Market Data → Feature Pipeline → GMS Detector → Both Strategies (simultaneously)
                                                    ↓
                                              TF-v3 (evaluating)
                                              Mean Reversion (evaluating)
                                                    ↓
                                              Portfolio Manager
                                              (enforces single position)
```

#### Example Scenario

```yaml
# Configuration
tf_v3:
  enabled: true
  active_regimes: ["Strong_Bull_Trend", "Weak_Bull_Trend", "Strong_Bear_Trend"]

mean_reversion:
  enabled: true
  active_regimes: ["Choppy_Market", "Low_Vol_Range", "High_Vol_Range"]
```

**What happens at each timestamp:**

1. **Both strategies receive the same signals** (prices, OBI, regime, etc.)
2. **Both evaluate their entry conditions** independently
3. **Regime gate determines who can act**:
   - If regime = "Strong_Bull_Trend" → Only TF-v3 can enter
   - If regime = "Choppy_Market" → Only Mean Reversion can enter
   - If regime = "Uncertain" → Neither can enter (unless configured)
4. **Single position constraint enforced**:
   - If a position exists, no new entries allowed
   - The strategy holding the position manages its exit

### Seamless Regime Transitions

When regime changes while a position is open:

```python
# Example: Mean Reversion has long position, regime shifts from CHOP to BULL

Time T:   Regime = "Choppy_Market"
          Mean Reversion: Long position (entered at $65,000)
          TF-v3: Watching but cannot enter (wrong regime)

Time T+1: Regime = "Strong_Bull_Trend" (confidence: 0.8)
          Mean Reversion: STILL HOLDS position, checks exit conditions
          TF-v3: Now in active regime but CANNOT enter (position exists)

Outcomes:
1. If aligned with new trend → MR may hold longer (ride the trend)
2. If OBI normalizes → MR exits based on its own logic
3. If regime confidence low → MR may tighten stops
4. Once MR exits → TF-v3 can now enter if signal present
```

**Key Points**:
- ✅ Positions are NOT forcibly closed on regime change
- ✅ Exit logic considers regime transitions intelligently
- ✅ No gaps or conflicts during handoff
- ✅ Strategies complement each other naturally

### 4. Entry Logic

```python
def evaluate(self, signals: Dict) -> Tuple[Optional[str], Optional[Dict]]:
    """
    Mean reversion entry logic with strict regime gating.
    """
    # 1. Check regime gate
    current_regime = signals.get('regime')
    if current_regime not in self.active_regimes:
        return None, None
    
    # 2. Get microstructure signals
    obi_zscore = signals.get('obi_zscore_5')
    spread_velocity = signals.get('spread_velocity')
    
    # 3. Check entry conditions
    if abs(obi_zscore) > self.get_threshold_for_regime(current_regime):
        # 4. Determine direction based on regime
        direction = self._get_direction(obi_zscore, current_regime)
        if direction is None:
            return None, None
            
        # 5. Risk filters
        if self._check_risk_filters(signals):
            # 6. Calculate position size
            position_info = self._calculate_position_size(signals)
            return direction, position_info
    
    return None, None
```

### 4. Exit Management

```python
def check_exit(self, signals: Dict, position: Dict) -> Optional[str]:
    """
    Exit conditions:
    1. OBI normalization (crosses exit threshold)
    2. Time decay (max hold time)
    3. Regime change
    4. Stop loss (adverse move)
    """
    # Check regime change first
    if signals.get('regime') not in self.active_regimes:
        return "Regime changed"
    
    # Check OBI normalization
    obi_zscore = signals.get('obi_zscore_5')
    if abs(obi_zscore) < self.exit_threshold:
        return "OBI normalized"
    
    # Time-based exit
    position_age = signals['timestamp'] - position['entry_time']
    if position_age > self.max_hold_seconds:
        return f"Time limit ({position_age}s)"
    
    # Stop loss check
    if self._check_stop_loss(signals, position):
        return "Stop loss"
    
    return None
```

## Implementation Steps

### Phase 1: Foundation (Week 1)

#### Step 1.1: Create Strategy Skeleton
1. Create `hyperliquid_bot/strategies/mean_reversion_microstructure.py`
2. Inherit from `StrategyInterface`
3. Implement required methods with placeholder logic
4. Write unit test: `tests/test_mean_reversion_skeleton.py`

#### Step 1.2: Add Configuration Schema
1. Update `hyperliquid_bot/config/settings.py`:
```python
@dataclass
class MeanReversionConfig:
    enabled: bool = False
    active_regimes: List[str] = field(default_factory=lambda: ["CHOP"])
    
    # Entry thresholds by regime type
    regime_thresholds: Dict[str, float] = field(default_factory=lambda: {
        "CHOP": 1.5,
        "Low_Vol_Range": 1.5,
        "Weak_Bear_Trend": 2.0,
        "Weak_Bull_Trend": 2.0,
        "default": 2.5
    })
    
    exit_threshold: float = 0.5
    max_hold_seconds: int = 300
    
    # Risk parameters
    use_volume_imbalance: bool = False  # Start simple
    use_spread_velocity: bool = True
    max_spread_velocity: float = 0.02  # 2% per second max
```

2. Update `configs/base.yaml` with mean reversion section
3. Write config validation test

#### Step 1.3: Implement Basic OBI Z-Score Logic
1. Implement `_get_direction()` method
2. Implement `evaluate()` with OBI z-score only
3. Write comprehensive unit tests:
   - Test regime gating
   - Test direction logic per regime
   - Test threshold application
   - Test edge cases (NaN, missing data)

### Phase 2: Microstructure Features (Week 2)

#### Step 2.1: Add Spread Velocity
1. Implement `build_spread_velocity()` in builder_registry
2. Add to feature pipeline
3. Write tests:
   - Verify causal calculation (no look-ahead)
   - Test with real spread data
   - Verify smoothing window behavior

#### Step 2.2: Add Volume Imbalance (if data available)
**STOP POINT**: Need to verify if we have trade volume data with side classification

If available:
1. Implement `build_volume_imbalance()`
2. Add as confirmation signal

If not available:
1. Implement `build_orderbook_flow_imbalance()` as proxy
2. Document limitations

#### Step 2.3: Multi-Level OBI Analysis
1. Implement `build_obi_divergence()`
2. Add divergence threshold parameters
3. Test divergence signal generation

### Phase 3: Risk Management (Week 3)

#### Step 3.1: Position Sizing
1. Implement regime-based position sizing
2. Add confidence scaling (like TF-v3)
3. Test position size calculations

#### Step 3.2: Stop Loss Implementation
1. Add ATR-based stops
2. Implement adverse excursion tracking
3. Test stop loss triggers

#### Step 3.3: Exit Logic
1. Implement all exit conditions
2. Add exit priority logic
3. Comprehensive exit testing

### Phase 4: Integration Testing (Week 4)

#### Step 4.1: Backtesting
1. Run isolated mean reversion backtest
2. Verify no look-ahead bias
3. Analyze regime-specific performance

#### Step 4.2: Combined Strategy Testing
1. Test with TF-v3 running simultaneously
2. Verify no strategy conflicts
3. Test regime transition handling

#### Step 4.3: Performance Analysis
1. Calculate strategy metrics by regime
2. Analyze win rate and profit factor
3. Identify optimization opportunities

## Testing Requirements

### Unit Tests

1. **Strategy Logic Tests** (`test_mean_reversion_logic.py`):
   - Regime gating
   - Direction determination
   - Entry/exit conditions
   - Risk filters

2. **Feature Builder Tests** (`test_mr_features.py`):
   - Spread velocity calculation
   - Volume imbalance (if applicable)
   - OBI divergence
   - No look-ahead verification

3. **Configuration Tests** (`test_mr_config.py`):
   - Config loading
   - Regime mapping validation
   - Threshold application

### Integration Tests

1. **Data Pipeline Tests**:
   - Feature availability
   - Data quality checks
   - NaN handling

2. **Regime Transition Tests**:
   - Position exit on regime change
   - State management
   - Clean transitions

### Backtesting Validation

1. **Regime-Specific Tests**:
   - CHOP-only activation
   - Multi-regime activation
   - Performance by regime

2. **Look-Ahead Bias Tests**:
   - Point-in-time data verification
   - Signal timing validation
   - Exit timing checks

## Risk Considerations

1. **Liquidity Risk**: 
   - Monitor book depth before entry
   - Adjust size for thin markets

2. **Regime Risk**:
   - Fast regime changes may trap positions
   - Consider regime stability metrics

3. **Correlation Risk**:
   - Mean reversion may correlate with TF-v3 losses
   - Monitor combined drawdown

## Performance Monitoring

### Key Metrics to Track

1. **By Regime**:
   - Win rate
   - Average win/loss
   - Profit factor
   - Maximum drawdown

2. **Microstructure Health**:
   - Average spread at entry
   - OBI reversion time
   - Slippage analysis

3. **Risk Metrics**:
   - Position overlap with TF-v3
   - Regime transition losses
   - Stop loss frequency

## Configuration Examples

### Conservative CHOP-Only Setup
```yaml
mean_reversion:
  enabled: true
  active_regimes: ["CHOP"]
  regime_thresholds:
    CHOP: 2.0
  exit_threshold: 0.5
  max_hold_seconds: 180
  use_spread_velocity: true
  max_spread_velocity: 0.01
```

### Aggressive Multi-Regime Setup (3-State Collapsed)
```yaml
mean_reversion:
  enabled: true
  active_regimes: ["CHOP", "Low_Vol_Range", "Weak_Bear_Trend"]
  regime_thresholds:
    CHOP: 1.5
    Low_Vol_Range: 1.5
    Weak_Bear_Trend: 2.5
  exit_threshold: 0.3
  max_hold_seconds: 300
  use_volume_imbalance: true
  use_spread_velocity: true
```

### Full 8-State Configuration Example
```yaml
mean_reversion:
  enabled: true
  # Choose which regimes activate mean reversion
  active_regimes: [
    "Choppy_Market",     # Classic mean reversion environment
    "Low_Vol_Range",     # Tight ranges, good for MR
    "High_Vol_Range",    # Volatile but ranging
    "Weak_Bear_Trend",   # Weak trends often revert
    "Weak_Bull_Trend"    # Weak trends often revert
  ]
  
  # Different thresholds for different regimes
  regime_thresholds:
    Choppy_Market: 1.5      # Tighter threshold in ideal conditions
    Low_Vol_Range: 1.8      # Slightly wider for low vol
    High_Vol_Range: 2.5     # Much wider for high vol
    Weak_Bear_Trend: 2.0    # Standard for weak trends
    Weak_Bull_Trend: 2.0    # Standard for weak trends
    Strong_Bull_Trend: 3.0  # Very wide (if enabled)
    Strong_Bear_Trend: 3.0  # Very wide (if enabled)
    Uncertain: 2.5          # Conservative for uncertain
    
  # Direction restrictions by regime (optional)
  regime_directions:
    Weak_Bear_Trend: "short_only"  # Only fade rallies in weak bear
    Weak_Bull_Trend: "long_only"   # Only fade dips in weak bull
    # Others allow both directions
    
  exit_threshold: 0.5
  max_hold_seconds: 300
  use_execution_refinement: true
```

### Complementary Strategy Configuration (Recommended)
```yaml
# Complete system configuration showing how strategies complement each other

# TF-v3 handles strong trends
tf_v3:
  enabled: true
  active_regimes: [
    "Strong_Bull_Trend",   # Clear trend following
    "Strong_Bear_Trend",   # Clear trend following
    "Weak_Bull_Trend"      # Can capture trend acceleration
  ]
  atr_period: 14
  confidence_scaling: true
  use_execution_refinement: true

# Mean Reversion handles ranging/uncertain markets
mean_reversion:
  enabled: true
  active_regimes: [
    "Choppy_Market",      # Perfect for mean reversion
    "Low_Vol_Range",      # Tight ranges = predictable reverts
    "High_Vol_Range",     # Volatile ranges = larger moves
    "Weak_Bear_Trend",    # Weak trends often fail/revert
    "Uncertain"           # When trend is unclear, fade extremes
  ]
  
  regime_thresholds:
    Choppy_Market: 1.5
    Low_Vol_Range: 1.8
    High_Vol_Range: 2.5
    Weak_Bear_Trend: 2.0
    Uncertain: 2.2
    
  exit_threshold: 0.5
  max_hold_seconds: 300
  use_execution_refinement: true

# GMS configuration
gms:
  mode: "continuous"
  verbose: true
  
# Example regime coverage:
# - Strong trends (2/8 states) → TF-v3 active
# - Weak/ranging/uncertain (5/8 states) → Mean Reversion active  
# - Weak_Bull_Trend (1/8 state) → Both can be active (first signal wins)
```

## Clarifications Resolved

1. **Trade Volume Data**: ❌ No trade data available
   - Only order book snapshots (L2 data)
   - Will use order book flow imbalance as proxy
   - Actually preferable - order book changes lead trades

2. **Regime Transition Handling**: ✅ Smart exit based on 8-state transitions
   
   ```python
   # Regime transition confidence thresholds
   TRANSITION_CONFIDENCE_THRESHOLDS = {
       # Transitions between similar regimes need lower confidence
       ('Weak_Bull_Trend', 'Weak_Bear_Trend'): 0.5,
       ('Weak_Bear_Trend', 'Weak_Bull_Trend'): 0.5,
       ('Low_Vol_Range', 'High_Vol_Range'): 0.4,
       ('High_Vol_Range', 'Low_Vol_Range'): 0.4,
       
       # Transitions to strong trends need higher confidence
       ('Choppy_Market', 'Strong_Bull_Trend'): 0.8,
       ('Choppy_Market', 'Strong_Bear_Trend'): 0.8,
       ('Weak_Bull_Trend', 'Strong_Bull_Trend'): 0.7,
       ('Weak_Bear_Trend', 'Strong_Bear_Trend'): 0.7,
       
       # Default threshold
       'default': 0.6
   }
   
   def handle_regime_transition(self, old_regime: str, new_regime: str, 
                               position_type: str, regime_confidence: float) -> str:
       """
       Smart exit logic for regime transitions with 8-state awareness.
       
       Returns: "hold", "tighten", or "exit_now"
       """
       # Get appropriate confidence threshold for this transition
       threshold = TRANSITION_CONFIDENCE_THRESHOLDS.get(
           (old_regime, new_regime), 
           TRANSITION_CONFIDENCE_THRESHOLDS['default']
       )
       
       # Define bullish and bearish regime sets
       bullish_regimes = {'Strong_Bull_Trend', 'Weak_Bull_Trend'}
       bearish_regimes = {'Strong_Bear_Trend', 'Weak_Bear_Trend'}
       neutral_regimes = {'Choppy_Market', 'Low_Vol_Range', 'High_Vol_Range', 'Uncertain'}
       
       # Position aligned with new trend direction
       if position_type == "long":
           if new_regime in bullish_regimes:
               if regime_confidence > threshold:
                   return "hold"  # Let winner ride
               else:
                   return "tighten"  # Low confidence - take profits
           elif new_regime in bearish_regimes:
               return "exit_now"  # Against trend
           else:  # Neutral regimes
               if old_regime in bullish_regimes:
                   return "tighten"  # Trend weakening
               else:
                   return "hold"  # Stay in mean reversion
                   
       elif position_type == "short":
           if new_regime in bearish_regimes:
               if regime_confidence > threshold:
                   return "hold"  # Let winner ride
               else:
                   return "tighten"  # Low confidence - take profits
           elif new_regime in bullish_regimes:
               return "exit_now"  # Against trend
           else:  # Neutral regimes
               if old_regime in bearish_regimes:
                   return "tighten"  # Trend weakening
               else:
                   return "hold"  # Stay in mean reversion
       
       return "hold"  # Default
   ```

3. **Position Limits**: ✅ ONE position only (BTC perps)
   - Simplifies strategy interaction
   - Forces quality over quantity
   - Perfect for retail setup

4. **Execution Integration**: ✅ Use execution refinement
   - Configurable via `use_execution_refinement`
   - Adapted for mean reversion context

## Success Criteria

1. **Functional Requirements**:
   - ✓ Regime-aware activation
   - ✓ No look-ahead bias
   - ✓ Configurable per regime
   - ✓ Clean integration with existing system

2. **Performance Targets**:
   - Win rate > 60% in CHOP regime
   - Sharpe ratio > 2.0
   - Maximum drawdown < 10%
   - Average holding time < 5 minutes

3. **Code Quality**:
   - 90%+ test coverage
   - No pylint errors
   - Comprehensive logging
   - Clear documentation

## TF-v3 Enhancement: Regime Transition Handling

Since this affects both strategies, here's the recommended enhancement for TF-v3:

```python
# In tf_v3.py check_exit method, add:
def check_regime_transition_exit(self, signals: Dict, position: Dict) -> Optional[str]:
    """
    Handle exits on regime transitions with 8-state awareness.
    TF-v3 uses same confidence thresholds as mean reversion.
    """
    current_regime = signals.get('regime')
    entry_regime = position.get('entry_regime')  # Store this on entry
    
    if current_regime != entry_regime:
        # Get regime confidence
        gms_snapshot = self.state.get('last_gms_snapshot', {})
        regime_confidence = gms_snapshot.get('regime_confidence', 0.5)
        
        # Get appropriate confidence threshold
        threshold = TRANSITION_CONFIDENCE_THRESHOLDS.get(
            (entry_regime, current_regime),
            TRANSITION_CONFIDENCE_THRESHOLDS['default']
        )
        
        position_type = position.get('type')
        
        # Define regime categories
        bullish_regimes = {'Strong_Bull_Trend', 'Weak_Bull_Trend'}
        bearish_regimes = {'Strong_Bear_Trend', 'Weak_Bear_Trend'}
        
        # TF-v3 specific: Exit if moving to opposite trend
        if position_type == 'long':
            if current_regime in bearish_regimes:
                return f"Regime changed: {entry_regime} → {current_regime}"
            elif current_regime not in bullish_regimes:
                # Moved to neutral - tighten stop if low confidence
                if regime_confidence < threshold:
                    self.state['trail_price'] = signals['close'] - signals[f'atr_{self.tf_v3_config.atr_period}'] * 1.5
                    self.logger.info(f"Tightened stop due to regime transition uncertainty")
                    
        elif position_type == 'short':
            if current_regime in bullish_regimes:
                return f"Regime changed: {entry_regime} → {current_regime}"
            elif current_regime not in bearish_regimes:
                # Moved to neutral - tighten stop if low confidence
                if regime_confidence < threshold:
                    self.state['trail_price'] = signals['close'] + signals[f'atr_{self.tf_v3_config.atr_period}'] * 1.5
                    self.logger.info(f"Tightened stop due to regime transition uncertainty")
            
    return None
```

## Next Steps

1. Review this implementation guide
2. Implement regime transition handling for TF-v3 first
3. Begin Phase 1 mean reversion implementation
4. Weekly progress reviews

---

**Note**: This is a living document. Update as implementation progresses and new insights emerge.