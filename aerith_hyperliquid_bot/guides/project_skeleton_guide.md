# Hyperliquid Trading Bot - Project Skeleton Guide

## Overview
This guide provides LLMs with essential project structure and naming conventions to avoid incorrect assumptions when implementing features or modifications. **Always ask for clarification when uncertain about specific implementations.**

## Critical Project Structure

### Main Package: `hyperliquid_bot/`
```
hyperliquid_bot/
├── config/                 # Configuration management
│   ├── settings.py         # Main Config class (Pydantic models)
│   └── scheduler_settings.py
├── core/                   # Core business logic
│   ├── detector.py         # GMS regime detection (GranularMicrostructureRegimeDetector)
│   └── risk.py            # RiskManager class
├── data/                   # Data handling and providers
│   ├── handler.py         # HistoricalDataHandler class
│   └── providers/
├── signals/                # Signal calculation engine
│   └── calculator.py      # SignalEngine class
├── strategies/             # Trading strategy implementations
│   ├── evaluator.py       # StrategyEvaluator, TrendFollowingStrategy
│   ├── obi_scalper.py     # OBIScalperStrategy (deprecated but kept)
│   └── tf_v3.py           # TFV3Strategy
├── portfolio/              # Portfolio and position management
│   └── portfolio.py       # Portfolio class
├── backtester/             # Backtesting engine
│   ├── backtester.py      # Main Backtester class
│   └── run_backtest.py    # Entry point script
└── utils/                  # Utility functions
```

## Critical Class Names (DO NOT RENAME)

### Core Classes
- **`Config`** - Main configuration class in `config/settings.py`
- **`Backtester`** - Main backtesting engine in `backtester/backtester.py`
- **`Portfolio`** - Position and balance management in `portfolio/portfolio.py`
- **`SignalEngine`** - Technical indicator calculator in `signals/calculator.py`
- **`StrategyEvaluator`** - Strategy management in `strategies/evaluator.py`
- **`RiskManager`** - Position sizing and risk in `core/risk.py`
- **`HistoricalDataHandler`** - Data loading in `data/handler.py`

### Strategy Classes
- **`TrendFollowingStrategy`** - Main trend following strategy
- **`TFV3Strategy`** - TF-v3 strategy implementation
- **`OBIScalperStrategy`** - OBI scalping strategy (currently skeleton)
- **`MeanReversionStrategy`** - Mean reversion strategy (inactive)
- **`MeanVarianceStrategy`** - Mean variance strategy (inactive)

### Detector Classes
- **`GranularMicrostructureRegimeDetector`** - Main GMS detector in `core/detector.py`

## Critical Method Names (DO NOT RENAME)

### Backtester Methods
- **`run(start_date, end_date)`** - Main backtest execution method
- **`_run_simulation_loop()`** - Core simulation loop
- **`_load_and_prepare_data()`** - Data loading and preparation

### Portfolio Methods
- **`handle_entry(position_dict, fill_price, filled_size, slippage_pnl, timestamp_unix)`**
- **`handle_exit(fill_price, exit_reason, slippage_pnl, timestamp_unix, fee_mode, is_liquidation)`**
- **`calculate_account_value(signals)`**
- **`check_liquidation(signals)`**

### SignalEngine Methods
- **`calculate_all_signals()`** - Main signal calculation method
- **`calculate_required_lookback()`** - Lookback period calculation

### Strategy Methods
- **`evaluate(signals)`** - Returns (direction, strategy_info) tuple
- **`check_exit(signals, position)`** - Exit condition checking
- **`log_evaluation_summary()`** - Strategy performance logging

## Configuration Structure

### Main Config Sections
```yaml
# Key configuration sections in base.yaml
data_paths:          # Data directory paths
cache:              # Caching settings
backtest:           # Backtest parameters
simulation:         # Execution simulation
strategies:         # Strategy enable/disable flags
portfolio:          # Risk and position sizing
costs:              # Fees and funding
regime:             # Regime detection settings
microstructure:     # Microstructure signal settings
indicators:         # Technical indicator parameters
```

### Critical Config Fields (might be named differently - ask for clarification)
- **`use_tf_v2`** - Enable trend following strategy
- **`use_tf_v3`** - Enable TF-v3 strategy  
- **`use_obi_scalper`** - Enable OBI scalper
- **`detector_type`** - Regime detector type ('granular_microstructure', 'rule_based', etc.)
- **`gms_use_three_state_mapping`** - Enable 3-state regime mapping
- **`map_weak_bear_to_bear`** - Weak bear trend mapping toggle

## Data Flow Architecture

### 1. Data Loading
```
HistoricalDataHandler → OHLCV + Raw Microstructure Features
```

### 2. Signal Calculation
```
SignalEngine.calculate_all_signals() → Technical Indicators + Smoothed Signals
```

### 3. Regime Detection
```
GranularMicrostructureRegimeDetector.get_regime() → Market Regime State
```

### 4. Strategy Evaluation
```
StrategyEvaluator.get_active_strategies() → Active Strategy List
Strategy.evaluate() → (direction, strategy_info)
```

### 5. Risk Management
```
RiskManager.calculate_position() → (size, leverage)
```

### 6. Execution
```
Portfolio.handle_entry() / Portfolio.handle_exit()
```

## Critical Signal Names (DO NOT RENAME)

### OHLCV Signals
- **`open`, `high`, `low`, `close`** - Standard OHLC data
- **`volume`** - Trading volume (optional based on config)

### Microstructure Signals
- **`raw_obi_5`** - Raw order book imbalance (5 levels)
- **`raw_spread_abs`, `raw_spread_rel`** - Raw spread metrics
- **`obi_smoothed`** - Smoothed OBI signal
- **`spread_mean`, `spread_std`** - Rolling spread statistics

### Technical Indicators
- **`atr`** - Average True Range
- **`adx`** - Average Directional Index
- **`forecast`** - EMA fast - EMA slow
- **`tf_ewma_fast`, `tf_ewma_slow`** - Trend following EMAs
- **`regime`** - Current market regime

### GMS-Specific Signals
- **`ma_slope`** - Moving average slope (momentum)
- **`atr_percent`** - ATR as percentage of price (volatility)
- **`roc`** - Rate of change (momentum)

## Entry Point Scripts

### Main Backtest Runner
```bash
python hyperliquid_bot/backtester/run_backtest.py [options]
```

### Key Command Line Arguments
- **`--override`** - Path to override config file
- **`--timeframe`** - Timeframe override (1h, 4h)
- **`--run-id`** - Unique run identifier
- **`--skip-validation-warnings`** - Skip validation prompts

## Important Implementation Notes

### 1. Strategy Naming Conventions
- Strategy names in code: `"trend_following"`, `"tf_v3"`, `"obi_scalper"`
- Config flags: `use_tf_v2`, `use_tf_v3`, `use_obi_scalper`
- **Note**: TF-v2 maps to `"trend_following"` strategy name

### 2. Regime Detection
- Raw GMS states: 7-state model (Strong_Bull_Trend, Weak_Bull_Trend, etc.)
- Mapped states: 3-state model (BULL, BEAR, CHOP)
- State mapping file: `configs/gms_state_mapping.yaml`

### 3. Data Requirements
- Timeframes: 1h (default), 4h
- Required columns: OHLCV + raw microstructure features
- Data format: Parquet files with timestamp index

### 4. Position Management
- Single position at a time (no portfolio of positions)
- Margin modes: 'cross' (default), 'isolated'
- Position dictionary keys: `type`, `entry`, `size`, `leverage`, `stop`, `profit`

### 5. Signal Evaluation Return Format
```python
# Strategy.evaluate() returns:
(direction, strategy_info)
# Where:
# - direction: "long" | "short" | None
# - strategy_info: Dict with additional info or None
```

## Common Pitfalls to Avoid

### 1. **DO NOT** rename core classes or methods without explicit confirmation
### 2. **DO NOT** assume signal names - they may have specific suffixes (e.g., `obi_smoothed_5`)
### 3. **DO NOT** modify the Portfolio.handle_entry/handle_exit signatures
### 4. **DO NOT** change the Config class structure without understanding dependencies
### 5. **DO NOT** assume strategy names match config flags exactly

## When to Ask for Clarification

1. **Signal naming**: If unsure about exact signal column names
2. **Config structure**: When adding new configuration options
3. **Strategy integration**: When implementing new strategies
4. **Data format**: When working with data loading/processing
5. **Method signatures**: When modifying existing method parameters
6. **File paths**: When uncertain about relative vs absolute paths

## Testing and Validation

### Key Test Files
- `tests/test_portfolio.py` - Portfolio functionality tests
- `tests/integration/` - Integration tests
- `scripts/test_*.py` - Various test scripts

### Validation Scripts
- `scripts/run_regression_test.py` - Regression testing
- `scripts/run_simple_backtest.py` - Simple backtest validation

## Performance Considerations

### Memory Usage
- Large datasets: Use chunked processing
- Signal DataFrames: Monitor memory usage in SignalEngine
- Parquet files: Efficient storage format used throughout

### Execution Speed
- Vectorized operations preferred in signal calculations
- Avoid loops in hot paths (simulation loop)
- Cache frequently accessed data

---

**Remember**: This is a complex financial trading system. When in doubt about any implementation detail, naming convention, or architectural decision, **always ask for clarification** rather than making assumptions that could break existing functionality. 