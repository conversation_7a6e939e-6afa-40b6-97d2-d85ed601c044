# Modern System Session Findings - 2025-07-17

## Technical Discoveries

### 1. Data Schema Issues

#### Missing Columns in features_1s
```
Warning: "Missing core signals for modern GMS detection"
```
This indicates the ModernContinuousGMSDetector expects columns that don't exist in features_1s data.

#### Column Name Mismatches
- Detector expects: `momentum`, `volatility`, `volume_imbalance`
- Data might have: `ma_slope`, `atr_percent_sec`, `obi_smoothed`

### 2. Configuration Misalignments

#### TF-v3 Indicator Parameters
```python
# Code expects:
self.config.indicators.ema_fast_period
self.config.indicators.ema_slow_period

# Actually exists:
self.config.tf_v3.ema_fast
self.config.tf_v3.ema_slow
```

#### Risk Configuration
Successfully fixed:
```yaml
tf_v3:
  risk_frac: 0.25  # Changed from 0.02 to match legacy 25%
```

### 3. Method Signature Issues

#### ModernContinuousGMSDetector
```python
# Has:
def detect_regime(self, signals: Dict[str, Any], timestamp: Optional[datetime] = None) -> str

# Code tried to call:
detector.update(features, timestamp)  # Wrong method name
```

#### HourlyStrategyEvaluator
```python
# Expects:
def evaluate(self, 
            hourly_bar: Dict[str, Any],
            current_signals: Dict[str, Any],
            timestamp: datetime) -> Optional[Dict[str, Any]]

# Code initially passed:
evaluate(latest_bar.to_dict(), hour_boundary)  # Missing current_signals
```

### 4. Pandas Aggregation Issues

#### Original (failing with pandas 2.x):
```python
agg_dict = {
    'close': 'last',  # Error: NDFrame.last() missing required argument
    'atr_14_sec': 'last'
}
```

#### Fixed:
```python
agg_dict = {
    'close': lambda x: x.iloc[-1] if len(x) > 0 else None,
    'atr_14_sec': lambda x: x.iloc[-1] if len(x) > 0 else None
}
```

### 5. Data Loader Evolution

The ModernDataLoader needs these methods that weren't initially present:
- `load_features_1s()` - Added as wrapper around `_load_feature_data()`
- `load_resampled_data()` - Added as wrapper around `_load_ohlcv_data()`

### 6. Initialization Parameter Mismatches

#### Portfolio
```python
# Wrong:
self.portfolio = Portfolio(config.portfolio)

# Correct:
self.portfolio = Portfolio(config)
```

#### ModernDataLoader
```python
# Code tried:
ModernDataLoader(data_dir=..., start_date=..., end_date=...)

# Actually expects:
ModernDataLoader(config)
```

## Key Code Patterns to Remember

### 1. Config Merging for Tests
```python
from deepmerge import always_merger

# Load base config
with open('configs/base.yaml', 'r') as f:
    base_config = yaml.safe_load(f)

# Load override
with open(override_path, 'r') as f:
    override_config = yaml.safe_load(f)

# Merge
merged_config = always_merger.merge(base_config, override_config)
config = Config(**merged_config)
```

### 2. Registry Pattern Issues
The registry expects exact parameter matches. Components registered with decorators must match the initialization parameters expected by the registry's get methods.

### 3. Data Flow Pattern
```
1-second features → 60s aggregation → regime detection → hourly evaluation
```

Each stage expects specific data formats that aren't well documented.

## Lessons Learned

### 1. Don't Assume Method Names
Even if a class implements an interface, the actual method names might differ from what calling code expects.

### 2. Config Structure Is Critical
The config structure has evolved differently between legacy and modern systems. We need a unified approach.

### 3. Data Schema Is Undocumented
The features_1s data schema is not documented anywhere. We need to discover it empirically.

### 4. Pandas Version Matters
Aggregation functions that worked in older pandas versions need updates for 2.x.

### 5. Integration Tests Are Essential
Unit tests wouldn't have caught these integration issues. We need end-to-end tests.

## Quick Fixes Applied (Need Proper Solutions)

1. **Pandas Aggregation**: Changed string aggregations to lambdas
2. **Method Names**: Added wrapper methods instead of fixing callers
3. **Config Access**: Changed attribute access patterns
4. **Parameters**: Adjusted initialization parameters

These are temporary fixes that need proper architectural solutions as outlined in the systematic debugging plan.

## Critical Questions for Next Session

1. What columns does features_1s actually contain?
2. What's the correct config structure for modern system?
3. Which detector method should be called: detect_regime or update?
4. How should data adapters transform between formats?
5. What's the expected trade count for validation?

## Command Reference

### Useful Commands from Session
```bash
# Run modern system test
python scripts/test_modern_system_phase5.py --quick --config configs/overrides/modern_system.yaml

# Check git history
git log --grep="modern" --since="2024-01-01"

# Find modern files
find . -path "*/modern/*" -name "*.py"

# Grep for specific patterns
grep -r "def detect_regime" hyperliquid_bot/modern/
```

### File Paths to Remember
- Modern system config: `configs/overrides/modern_system.yaml`
- Modern calibrated config: `configs/overrides/modern_calibrated.yaml`
- Test script: `scripts/test_modern_system_phase5.py`
- Implementation plan: `MODERN_SYSTEM_IMPLEMENTATION_PLAN.md`

This document preserves the specific technical details discovered during the session that will be needed for proper fixes in the next session.