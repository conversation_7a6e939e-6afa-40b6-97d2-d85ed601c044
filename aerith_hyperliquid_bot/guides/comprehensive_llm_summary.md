# Comprehensive Trading Bot Summary for LLMs

**Version**: 1.0  
**Date**: 2025-01-23  
**Purpose**: Complete reference for LLMs working on the Hyperliquid trading bot

## Table of Contents

1. [System Overview](#system-overview)
2. [Data Architecture](#data-architecture)
3. [GMS Detector Systems](#gms-detector-systems)
4. [Trading Strategies](#trading-strategies)
5. [Data Pipeline Flow](#data-pipeline-flow)
6. [Performance Metrics](#performance-metrics)
7. [Implementation Guidelines](#implementation-guidelines)
8. [Critical Constraints](#critical-constraints)

## System Overview

### Legacy Production System (Baseline)
- **Architecture**: Granular Microstructure + TF-v2 Strategy on 1h/4h timeframes
- **Performance**: Sharpe 3.99, 202% ROI, 6.91% max drawdown (2024 full year)
- **Trade Frequency**: 185 trades/year (selective execution)
- **Infrastructure**: Standard VPS requirements
- **Status**: ✅ Proven baseline performance

### Modern System (Tested & Working)
- **Architecture**: Continuous GMS + TF-v3 Strategy on 1h timeframes
- **Performance**: Sharpe 0.79, 5.87% ROI, 10.31% max drawdown (2025-03-02 to 2025-03-22)
- **Trade Frequency**: 32 trades in 21 days (more active than legacy)
- **Infrastructure**: Uses 1s features data pipeline
- **Status**: ✅ Tested and functional, higher trade frequency but lower Sharpe

### Performance Comparison (Same Period: 2025-03-02 to 2025-03-22)
| System | Sharpe | ROI | Max DD | Trades | Trade Freq |
|--------|--------|-----|--------|--------|------------|
| **Legacy (Granular + TF-v2)** | 1.29 | 1.58% | 6.41% | 12 | 0.57/day |
| **Modern (Continuous + TF-v3)** | 0.79 | 5.87% | 10.31% | 32 | 1.52/day |

**Key Insights**:
- Modern system generates 2.7x more trades but with lower risk-adjusted returns
- Modern system achieved higher absolute returns (5.87% vs 1.58%) in the test period
- Legacy system maintains better risk profile (lower drawdown and higher Sharpe)
- Both systems are functional and can be used depending on trading preferences

## Data Architecture

### Directory Structure
```
hyperliquid_data/
├── raw2/                           # Granular microstructure (5-depth)
│   └── YYYYMMDD_raw2.parquet      # 37 microstructure features
├── resampled_l2/                   # Hourly aggregated data
│   ├── 1h/YYYY-MM-DD_1h.parquet  # 10 columns: OHLC + microstructure
│   └── 4h/YYYY-MM-DD_4h.parquet  # 4-hour aggregated data
├── l2_raw/                         # Raw L2 snapshots (modern)
│   └── YYYY-MM-DD/BTC_HH_l2Book.arrow  # 10Hz L2 data
└── features_1s/                    # 1-second processed features
    └── YYYY-MM-DD/features_HH.parquet   # 109 columns
```

### Data Source Matrix

| Data Source | File Pattern | Columns | Used By | Status |
|-------------|--------------|---------|---------|---------|
| **1h OHLCV** | `resampled_l2/1h/YYYY-MM-DD_1h.parquet` | 10 | TF-v2, GMS | ✅ Production |
| **Raw2** | `raw2/YYYYMMDD_raw2.parquet` | 37 | Granular GMS | ✅ Production |
| **L2 Raw** | `l2_raw/YYYY-MM-DD/BTC_HH_l2Book.arrow` | 6 | ETL Pipeline | ⚠️ Experimental |
| **1s Features** | `features_1s/YYYY-MM-DD/features_HH.parquet` | 109 | Continuous GMS, TF-v3 | ✅ Tested & Working |

### Key Schema Details

#### 1-Hour OHLCV (Production Data)
```python
# File: resampled_l2/1h/YYYY-MM-DD_1h.parquet
columns = [
    'timestamp',      # datetime64[ns] - timezone-naive
    'open', 'high', 'low', 'close',  # float64 - OHLC prices
    'log_ret',        # float64 - log returns (4.2% NaN)
    'realised_vol',   # float64 - realized volatility (50% NaN)
    'bid_slope',      # float64 - bid side slope
    'ask_slope',      # float64 - ask side slope  
    'book_asymmetry'  # float64 - order book asymmetry
]
# Shape: 24 rows × 10 columns (24 hours per day)
# Missing: volume, atr (calculated separately)
```

#### 1-Second Features (Experimental Data)
```python
# File: features_1s/YYYY-MM-DD/features_HH.parquet
key_columns = [
    'timestamp',           # datetime64[ns] - timezone-naive
    'mid_price', 'spread', 'volume',  # Basic market data
    'raw_obi_5', 'raw_obi_20',       # Order book imbalance
    'obi_smoothed_5', 'obi_smoothed_20',  # Smoothed OBI
    'obi_zscore_5', 'obi_zscore_20',      # OBI z-scores
    'atr_14_sec', 'atr_percent_sec',      # ATR metrics (100% NaN initially)
    'spread_mean', 'spread_std',          # Spread statistics
    'ma_slope', 'ma_slope_ema_30s',       # Momentum indicators
    # Plus 80 order book columns (bid_price_1-20, ask_price_1-20, etc.)
]
# Shape: ~3,600 rows × 109 columns (1 second resolution)
```

## GMS Detector Systems

### System Compatibility Matrix

| Component | Granular Microstructure | Continuous GMS |
|-----------|------------------------|----------------|
| **Detector** | `granular_microstructure` | `continuous_gms` |
| **Strategy** | TF-v2 | TF-v3 |
| **Data Source** | `raw2/`, `resampled_l2/` | `l2_raw/`, `features_1s/` |
| **Depth Levels** | 5 | 5 or 20 (configurable) |
| **Cadence** | 3600 seconds (hourly) | 60 seconds |
| **Output Format** | String regime | Dict with state + risk_suppressed |
| **Status** | ✅ Production | ✅ Tested & Working |

### Granular Microstructure Detector (Production)

**Configuration**:
```yaml
regime:
  detector_type: 'granular_microstructure'
  gms_vol_high_thresh: 0.92      # ATR% high volatility threshold
  gms_vol_low_thresh: 0.55       # ATR% low volatility threshold
  gms_mom_strong_thresh: 100.0   # MA slope strong momentum threshold
  gms_mom_weak_thresh: 50.0      # MA slope weak momentum threshold
  cadence_sec: 3600              # Hourly recompute
```

**Required Signals**:
- `timestamp`, `atr_percent`, `ma_slope`
- `obi_smoothed_5`, `spread_mean`, `spread_std`

**Output**: String regime state (e.g., "Strong_Bull_Trend")

### Continuous GMS Detector (Tested & Working)

**Configuration**:
```yaml
regime:
  detector_type: 'continuous_gms'
  gms_vol_high_thresh: 0.03      # Reduced thresholds
  gms_vol_low_thresh: 0.01
  gms_mom_strong_thresh: 2.5
  gms_mom_weak_thresh: 0.5
  cadence_sec: 60                # Minute recompute
```

**Required Signals**:
- `timestamp`, `atr_percent_sec`, `ma_slope`
- `obi_smoothed_{depth_levels}`, `spread_mean`, `spread_std`

**Output**: Dict with state and risk suppression
```python
{"state": "Strong_Bull_Trend", "risk_suppressed": false}
```

### State Mapping System

**8-State → 3-State Mapping**:
```yaml
Strong_Bull_Trend: "BULL"
Weak_Bull_Trend: "BULL"
Strong_Bear_Trend: "BEAR"
Weak_Bear_Trend: "CHOP"     # Configurable: can map to "BEAR"
Bull_Tight_Spread: "BULL"
Bear_Tight_Spread: "BEAR"
Volatile_Chop: "CHOP"
Tight_Spread_Chop: "CHOP"
```

## Trading Strategies

### TF-v2 Strategy (Production)

**Architecture**: Works with Granular GMS on 1h/4h timeframes
**Core Logic**:
```python
# Entry conditions
forecast = ema_fast - ema_slow
adx_strength = adx > threshold
long_signal = forecast > 0 and adx_strength
short_signal = forecast < 0 and adx_strength

# Risk management
stop_loss = entry_price ± (atr * stop_multiplier)
take_profit = entry_price ± (atr * target_multiplier)
```

**Performance**: Sharpe 3.99, 202% ROI, 6.91% max drawdown

### TF-v3 Strategy (Tested & Working)

**Architecture**: Works with Continuous GMS with regime awareness
**Enhanced Features**:
- Regime gating (only trades in BULL/BEAR, skips CHOP)
- GMS snapshot validation (configurable staleness threshold)
- Risk suppression integration
- Configurable maximum position holding (24-72 hours)
- Dynamic ATR trailing stops

**Test Configuration (R-112q)**:
```yaml
tf_v3:
  ema_fast: 20
  ema_slow: 50
  atr_trail_k: 2.0          # Tighter stops
  max_trade_life_h: 72      # Longer hold time
  risk_frac: 0.10           # Smaller position size
  gms_max_age_sec: 1800     # 30 min staleness threshold
```

**Performance**: Sharpe 0.79, 5.87% ROI, 10.31% max drawdown (21-day test)

**Required Columns**:
- `timestamp`, `open`, `high`, `low`, `close`, `volume`
- `regime`, `regime_timestamp`, `risk_suppressed`

## Data Pipeline Flow

### Production Pipeline (Granular Microstructure)
```
Raw L2 JSON → raw2/ parquet → microstructure.py → resampled_l2/ → TF-v2 strategy
                    ↓
            granular_microstructure detector (hourly)
```

### Modern Pipeline (Continuous GMS - Tested & Working)
```
Raw L2 Arrow → ETL (etl_l20_to_1s.py) → features_1s/ → continuous_gms detector → TF-v3 strategy
```

### DataHandler Integration

**HistoricalDataHandler** in `hyperliquid_bot/data/handler.py`:

1. **Primary Data Loading** (`_load_ohlcv`):
   - Loads 1h OHLCV from `resampled_l2/1h/`
   - Conditionally requires volume based on config
   - Merges Fear & Greed Index if enabled

2. **Microstructure Integration** (`_integrate_microstructure_features`):
   - Loads L2 raw data from `raw2/` files
   - Calculates raw microstructure features
   - Merges with OHLCV using `pd.merge_asof`

3. **Feature Store Integration**:
   - Loads ATR and spread statistics from 1s features
   - Resamples to hourly using specific aggregation
   - Merges with combined OHLCV+microstructure data

4. **Final Output** (`get_ohlcv_data`):
   - Returns `self.combined_data` with all features

### SignalEngine Processing

**SignalEngine** in `signals/calculator.py`:
- Preserves high-quality ATR columns if available
- Calculates additional technical indicators
- Processes OBI signals for multiple depth levels
- Adds strategy-specific indicators

## Performance Metrics

### Legacy System Results (2024 Hyperliquid BTC - Full Year)

**Performance Metrics**:
- **Sharpe Ratio**: 3.99 ⭐ (Exceptional)
- **ROI**: 202.94% (Over 3x capital)
- **Profit Factor**: 2.08
- **Max Drawdown**: 6.91% (Low risk)
- **Total Trades**: 185 (Selective)

**Key Insights**:
- Risk-adjusted excellence (Sharpe > 3.0)
- Controlled risk profile (DD < 7%)
- Consistent profitability (PF > 2.0)
- High selectivity (0.5 trades/day)

### Modern System Results (2025-03-02 to 2025-03-22 Test Period)

**Continuous GMS + TF-v3 Performance**:
- **Sharpe Ratio**: 0.79 (Good for short period)
- **ROI**: 5.87% (21 days)
- **Profit Factor**: 1.33
- **Max Drawdown**: 10.31%
- **Total Trades**: 32 (More active)

**Legacy GMS + TF-v2 Performance (Same Period)**:
- **Sharpe Ratio**: 1.29 (Better risk-adjusted)
- **ROI**: 1.58% (21 days)
- **Profit Factor**: 1.26
- **Max Drawdown**: 6.41% (Lower risk)
- **Total Trades**: 12 (More selective)

**Comparative Analysis**:
- Modern system: Higher absolute returns, more trades, higher risk
- Legacy system: Better risk-adjusted returns, lower drawdown, more selective
- Both systems are functional and profitable
- Choice depends on risk tolerance and trading frequency preference

## Implementation Guidelines

### Critical Class Names (DO NOT RENAME)
- **`Config`** - Main configuration (`config/settings.py`)
- **`Backtester`** - Backtesting engine (`backtester/backtester.py`)
- **`Portfolio`** - Position management (`portfolio/portfolio.py`)
- **`SignalEngine`** - Signal calculation (`signals/calculator.py`)
- **`StrategyEvaluator`** - Strategy management (`strategies/evaluator.py`)
- **`HistoricalDataHandler`** - Data loading (`data/handler.py`)
- **`GranularMicrostructureRegimeDetector`** - GMS detector (`core/detector.py`)

### Critical Method Names (DO NOT RENAME)
- **`run(start_date, end_date)`** - Main backtest execution
- **`calculate_all_signals()`** - Signal calculation
- **`evaluate(signals)`** - Strategy evaluation (returns direction, info)
- **`handle_entry/handle_exit`** - Portfolio position management
- **`get_regime(signals)`** - Regime detection

### Configuration Structure
```yaml
data_paths:          # Data directory paths
backtest:           # Backtest parameters
strategies:         # Strategy enable/disable flags
portfolio:          # Risk and position sizing
regime:             # Regime detection settings
indicators:         # Technical indicator parameters
```

### Signal Naming Conventions
- **OHLCV**: `open`, `high`, `low`, `close`, `volume`
- **Microstructure**: `raw_obi_5`, `obi_smoothed`, `spread_mean`, `spread_std`
- **Technical**: `atr`, `adx`, `forecast`, `tf_ewma_fast`, `tf_ewma_slow`
- **GMS**: `ma_slope`, `atr_percent`, `regime`

## Critical Constraints

### Infrastructure Reality
- **Hyperliquid Latency**: 500-700ms typical for VPS users (not sub-200ms)
- **VPS Budget**: $100-300/month acceptable for profitable bot
- **Storage**: Current experimental system requires 150+ GB for 3-month history
- **Processing**: Continuous GMS requires 24/7 data pipeline management

### Retail Trading Limitations
- **High Infrastructure Costs**: Experimental system requires $200-500/month VPS
- **Operational Complexity**: 24/7 monitoring and maintenance
- **Data Requirements**: Continuous L2 feed subscription costs
- **Latency Dependencies**: Sub-200ms requirements unrealistic

### System Selection Considerations
Both systems are functional - choice depends on requirements:

**Choose Legacy (Granular + TF-v2) for**:
1. **Better risk-adjusted returns** (higher Sharpe ratio)
2. **Lower drawdown tolerance** (6-7% vs 10%+)
3. **Simpler infrastructure** (raw2 files vs 1s features pipeline)
4. **More selective trading** (fewer but higher quality trades)
5. **Proven long-term performance** (full year validation)

**Choose Modern (Continuous + TF-v3) for**:
1. **Higher trade frequency** (2.7x more trades)
2. **Higher absolute returns** (in test period)
3. **More responsive regime detection** (60s vs 3600s cadence)
4. **Advanced features** (risk suppression, adaptive thresholds)
5. **Modern data pipeline** (1s features with richer signals)

## Data Quality Rules

### Validation Requirements
```python
# NaN ratio thresholds
NAN_THRESHOLDS = {
    'tier_1': 0.01,    # Core features (≤1% NaN)
    'tier_2': 0.05,    # L2 features (≤5% NaN)
    'tier_3': 1.00,    # ATR features (100% NaN initially acceptable)
}

# Required columns per detector
GRANULAR_REQUIRED = [
    'timestamp', 'atr_percent', 'ma_slope',
    'obi_smoothed_5', 'spread_mean', 'spread_std'
]

CONTINUOUS_REQUIRED = [
    'timestamp', 'atr_percent_sec', 'ma_slope',
    'obi_smoothed_{depth}', 'spread_mean', 'spread_std'
]
```

### Runtime Guards
- Schema validation for required columns
- NaN ratio validation per tier
- Timestamp continuity checks
- Order book integrity validation

## Key Implementation Notes

### 1. Backward Compatibility
- Preserve existing configuration paths
- Maintain detector_type mapping
- Support both string and dict output formats
- Keep exact threshold values for baseline reproduction

### 2. Feature Availability
- Runtime column resolution based on data source
- Support both `atr_percent` and `atr_percent_sec`
- Dynamic OBI column selection based on depth

### 3. Strategy Integration
- Regime-aware strategy selection
- Dynamic risk adjustment based on market state
- Fallback mechanisms for missing data

### 4. Error Handling
- Graceful degradation for missing features
- Data quality validation at runtime
- Clear error messages for configuration issues

## Common Pitfalls to Avoid

1. **DO NOT** rename core classes/methods without confirmation
2. **DO NOT** assume signal names (check for specific suffixes)
3. **DO NOT** modify Portfolio method signatures
4. **DO NOT** change Config structure without understanding dependencies
5. **DO NOT** assume strategy names match config flags exactly

## When to Ask for Clarification

1. **Signal naming**: Exact column names in DataFrames
2. **Config structure**: Adding new configuration options
3. **Strategy integration**: Implementing new strategies
4. **Data format**: Working with data loading/processing
5. **Method signatures**: Modifying existing parameters
6. **File paths**: Relative vs absolute path handling

---

**Remember**: This is a complex financial system with two functional approaches:
1. **Legacy System**: Proven long-term performance with excellent risk-adjusted returns
2. **Modern System**: Tested and working with higher trade frequency and absolute returns

Both systems are production-ready. The choice depends on your risk tolerance, infrastructure preferences, and trading frequency requirements. The modern system offers more advanced features but requires a more complex data pipeline, while the legacy system provides proven stability with simpler infrastructure needs. 