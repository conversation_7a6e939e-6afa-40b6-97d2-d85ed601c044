# Quick Debugging Checklist - Simple Issues to Check First

## Immediate Quick Fixes to Try Tomorrow

### 1. Configuration Flags
```bash
# Check for conflicting flags in YAML
grep -r "use_tf_v3\|tf_warmup\|execution_refinement" configs/
grep -r "strict.*filter\|regime.*filter" configs/
```

### 2. Exception Silencing
```python
# Add to tf_v3.py evaluate() method at the very beginning:
try:
    self.logger.error(f"TF-v3 ENTRY: {signals.get('timestamp')}")
    # ... existing code ...
except Exception as e:
    self.logger.error(f"TF-v3 EXCEPTION: {e}")
    raise  # Don't silence any exceptions
```

### 3. Strategy Activation Debug
```python
# Add to strategy_evaluator.py get_active_strategies():
def get_active_strategies(self, regime_state):
    self.logger.error(f"REGIME DEBUG: {regime_state} -> checking TF-v3 activation")
    result = # ... existing logic ...
    self.logger.error(f"ACTIVATION RESULT: {result}")
    return result
```

### 4. State File Conflicts
```bash
# Clear any cached state
rm -f hyperliquid_bot/state/tf_v3_*.json
rm -f *.pkl *.cache
```

### 5. Import Issues
```python
# Check if my changes broke imports
python -c "
from hyperliquid_bot.strategies.tf_v3 import TFV3Strategy
from hyperliquid_bot.backtester.backtester import Backtester  
print('Imports OK')
"
```

### 6. Required Signals List
```python
# Check if ohlcv_history requirement is breaking things
# In tf_v3.py required_signals, temporarily remove:
# signals.append('ohlcv_history')  # TEMP DISABLE
```

### 7. Warm-up Logic Error
```python
# Maybe my warm-up calculation is wrong
# Check available_periods calculation:
ohlcv_history = signals.get('ohlcv_history')
if ohlcv_history is None:
    available_periods = 0
    self.logger.error(f"WARMUP DEBUG: ohlcv_history is None")
elif not isinstance(ohlcv_history, pd.DataFrame):
    available_periods = 0  
    self.logger.error(f"WARMUP DEBUG: ohlcv_history wrong type: {type(ohlcv_history)}")
elif ohlcv_history.empty:
    available_periods = 0
    self.logger.error(f"WARMUP DEBUG: ohlcv_history is empty")
else:
    available_periods = len(ohlcv_history)
    self.logger.error(f"WARMUP DEBUG: available_periods={available_periods}, required={self.min_warmup_periods}")
```

### 8. Signal Provision Regression
```python
# Maybe my signal checking fixes broke something
# Temporarily disable the new is_signal_missing logic
# Use original simple check:
missing_now = [s for s in required_now if s not in current_signals or pd.isna(current_signals.get(s))]
```

### 9. Regime Mapping Issues
```bash
# Check if regime mapping is working
grep -A5 -B5 "regime_mapped\|Strong_Bull_Trend\|Strong_Bear_Trend" logs/backtest_run_tf_v3_final_signal_fix_20250715_062204.log
```

### 10. Data Access Problems
```python
# Check if OHLCV data access is broken
try:
    all_ohlcv = self.data_handler.get_ohlcv_data()
    self.logger.error(f"OHLCV DEBUG: Shape={all_ohlcv.shape if all_ohlcv is not None else None}")
except Exception as e:
    self.logger.error(f"OHLCV ACCESS ERROR: {e}")
```

## Most Likely Culprits (Rank Ordered)

1. **Warm-up period too aggressive**: 14 candles might be eliminating all valid periods
2. **Exception being silently caught**: Error not bubbling up properly  
3. **Configuration flag conflict**: Some YAML setting preventing activation
4. **Signal checking regression**: My DataFrame fixes broke simple scalar checking
5. **Required signals change**: Adding ohlcv_history broke existing flow

## Quick Restoration Steps

If simple fixes don't work, immediately revert:

```bash
# 1. Restore original tf_v3.py
git checkout HEAD~5 -- hyperliquid_bot/strategies/tf_v3.py

# 2. Restore original backtester.py  
git checkout HEAD~5 -- hyperliquid_bot/backtester/backtester.py

# 3. Test immediately
python -m hyperliquid_bot.backtester.run_backtest --timeframe 1h --run-id baseline_restoration --system modern

# 4. If that works, we know my changes broke it
# 5. If that doesn't work, there's a deeper issue
```

## Verification Commands

```bash
# Quick sanity checks
python -c "from hyperliquid_bot.config.settings import Config; print('Config loads')"
python -c "from hyperliquid_bot.strategies.tf_v3 import TFV3Strategy; print('TF-v3 imports')" 
python -c "from hyperliquid_bot.backtester.backtester import Backtester; print('Backtester imports')"

# Check for obvious syntax errors
python -m py_compile hyperliquid_bot/strategies/tf_v3.py
python -m py_compile hyperliquid_bot/backtester/backtester.py
```

The goal is to find the smoking gun quickly and get back to a working state.