# Clean Restart Implementation Plan

## Objective
Replicate the baseline performance (180 trades, 215% ROI) using continuous_gms + TF-v3 while ensuring the system is production-ready for retail trading with no bugs, no look-ahead bias, and following industry-standard practices.

## Phase 1: Foundation (Day 1-2)

### 1.1 Codebase Understanding & Documentation
**Before writing any code, we must understand what we have:**

```markdown
Tasks:
□ Map the data flow from source to trade execution
□ Document each component's responsibility (single responsibility principle)
□ Identify and document all data dependencies
□ Create a visual architecture diagram
□ List all external dependencies and their versions
```

**Key Questions to Answer:**
- What exact fields does each strategy require?
- How is data timestamped and aligned?
- Where are potential look-ahead bias points?
- What are the critical execution paths?

### 1.2 Data Schema Documentation
**Create comprehensive data schema documentation:**

```python
# Expected Schema for Legacy Hourly Data
{
    'timestamp': datetime,      # Bar open time
    'open': float,             # First price in hour
    'high': float,             # Highest price in hour  
    'low': float,              # Lowest price in hour
    'close': float,            # Last price in hour
    'volume': float,           # Total volume in hour
    'imbalance': float,        # Order book imbalance [-1, 1]
    'spread': float,           # Bid-ask spread
    'funding_rate': float,     # Funding rate at hour end
    # ... document ALL fields
}
```

### 1.3 Testing Infrastructure Setup
**Before any implementation:**

```python
# tests/test_data_integrity.py
- Test for look-ahead bias
- Test for data alignment
- Test for missing data handling
- Test for timezone consistency

# tests/test_strategy_logic.py  
- Test entry/exit logic isolation
- Test signal generation
- Test position sizing
- Test risk management

# tests/test_backtester.py
- Test trade execution
- Test fee calculation
- Test slippage model
- Test portfolio tracking
```

## Phase 2: Baseline Replication (Day 3-4)

### 2.1 Minimal Viable Implementation
**Goal: Exact replication with simplest possible code**

```python
# Step 1: Direct data reader for hourly files
class LegacyHourlyReader:
    """Reads pre-aggregated hourly data directly - no processing"""
    def read_daterange(self, start, end):
        # Direct parquet read, no transformation
        pass

# Step 2: Basic trend following in TF-v3
class TFV3Strategy:
    """Start with exact TF-v2 logic, no enhancements"""
    def generate_signal(self, data):
        # Copy TF-v2 logic exactly first
        pass

# Step 3: Simple backtester
class CleanBacktester:
    """Minimal backtester - no abstractions"""
    def run(self, data, strategy):
        # Direct execution, clear logic
        pass
```

### 2.2 Validation Checklist
```markdown
□ Backtest produces ~180 trades (±10%)
□ ROI is ~215% (±10%)  
□ No look-ahead bias (verified by tests)
□ No data from future timestamps used
□ All trades can be manually verified
□ Performance matches legacy system
```

## Phase 3: Code Quality & Safety (Day 5)

### 3.1 Look-Ahead Bias Prevention
**Systematic checks at every data access point:**

```python
class DataValidator:
    def check_lookahead(self, current_time, data_timestamp):
        """Ensure we never access future data"""
        if data_timestamp > current_time:
            raise LookAheadBiasError(
                f"Attempting to access future data: {data_timestamp} > {current_time}"
            )
```

### 3.2 Industry Standard Practices
**Implement from the start:**

1. **Type Hints Everywhere**
   ```python
   def calculate_position_size(
       balance: float, 
       risk_pct: float, 
       stop_distance: float
   ) -> float:
   ```

2. **Comprehensive Logging**
   ```python
   logger.info(f"Signal generated: {signal}", extra={
       'timestamp': current_time,
       'price': current_price,
       'indicators': indicator_values
   })
   ```

3. **Error Handling**
   ```python
   try:
       position_size = calculate_position_size(...)
   except InsufficientBalanceError:
       logger.warning("Skipping trade: insufficient balance")
       return None
   ```

4. **Configuration Management**
   ```yaml
   # One source of truth: config.yaml
   data:
     path: /hyperliquid_data/raw2
     format: hourly_aggregated
   
   strategy:
     tf_v3:
       ema_fast: 20
       ema_slow: 50
   ```

### 3.3 Performance Profiling
```python
# Profile critical paths
import cProfile
profiler = cProfile.Profile()
profiler.enable()
# ... run backtest ...
profiler.disable()
profiler.dump_stats('backtest_profile.stats')
```

## Phase 4: Incremental Enhancement (Day 6-7)

### 4.1 Enhancement Protocol
**For each enhancement:**

1. **Document the hypothesis**
   ```markdown
   Enhancement: Tactical Alignment Check
   Hypothesis: 5-min momentum alignment will reduce false signals by 20%
   Expected Impact: -10% trades, +5% win rate
   ```

2. **Implement in isolation**
   ```python
   class TacticalAlignment:
       """Single responsibility: check momentum alignment"""
       def is_aligned(self, signal_direction, momentum_5m):
           # Clear, testable logic
           pass
   ```

3. **Test thoroughly**
   ```python
   def test_tactical_alignment():
       # Test all edge cases
       # Test with real data samples
       # Test performance impact
   ```

4. **Backtest comparison**
   ```markdown
   Baseline: 180 trades, 215% ROI
   With Enhancement: 162 trades, 224% ROI
   Decision: ACCEPT (meets criteria)
   ```

### 4.2 Git Workflow
```bash
# Feature branch for each enhancement
git checkout -b feature/tactical-alignment

# Commit after tests pass
git add -A
git commit -m "feat: add tactical alignment check

- Reduces false signals by checking 5m momentum
- Backtested: 162 trades, 224% ROI (+9% improvement)
- All tests passing, no look-ahead bias"

# Merge only if improvement confirmed
git checkout main
git merge feature/tactical-alignment
```

## Phase 5: Production Readiness (Day 8+)

### 5.1 Monitoring & Observability
```python
class TradingMetrics:
    """Real-time metrics collection"""
    def __init__(self):
        self.metrics = {
            'signals_generated': Counter(),
            'trades_executed': Counter(),
            'win_rate': Gauge(),
            'daily_pnl': Histogram()
        }
```

### 5.2 Data Quality Monitoring
```python
class DataQualityMonitor:
    def check_data_quality(self, df):
        checks = {
            'missing_values': df.isnull().sum(),
            'timestamp_gaps': self.find_gaps(df.index),
            'price_spikes': self.detect_spikes(df['close']),
            'spread_anomalies': self.check_spreads(df)
        }
        return checks
```

## Critical Success Factors

### 1. Data Integrity
- **Never trust, always verify** - validate data at every step
- **Immutable data** - never modify source data
- **Clear timestamps** - always use UTC, document timezone handling

### 2. Code Simplicity
- **No premature abstractions** - duplicate code is better than wrong abstraction
- **Explicit over implicit** - clear variable names, obvious logic
- **Single responsibility** - each class/function does ONE thing

### 3. Testing Discipline
- **Test-first development** - write test before implementation
- **Edge case coverage** - test the unusual scenarios
- **Performance benchmarks** - ensure changes don't degrade speed

### 4. Incremental Progress
- **One change at a time** - isolate variables
- **Measure everything** - data drives decisions
- **Revert quickly** - if degradation detected

## Areas We Previously Missed

### 1. **Data Validation Pipeline**
We jumped straight to processing without validating data quality:
- Missing values handling
- Outlier detection
- Timestamp consistency
- Schema validation

### 2. **Component Testing**
We tested the full system but not individual components:
- Strategy logic in isolation
- Indicator calculations
- Risk management rules
- Order execution logic

### 3. **Performance Benchmarking**
We never established performance baselines:
- Execution speed targets
- Memory usage limits
- Latency requirements
- Throughput goals

### 4. **Error Recovery**
No plan for handling failures:
- Network disconnections
- Data feed issues
- Partial fills
- System crashes

### 5. **Configuration Validation**
Too many config files with no validation:
- Parameter bounds checking
- Dependency validation
- Override precedence
- Type checking

### 6. **Audit Trail**
No record of decision-making:
- Why parameters were chosen
- What alternatives were tested
- Performance impact of changes
- Rationale for architecture

## Daily Checklist

### Morning
- [ ] Review previous day's changes
- [ ] Check all tests are passing
- [ ] Plan single enhancement for today
- [ ] Document hypothesis and success criteria

### During Development  
- [ ] Write tests first
- [ ] Implement minimal solution
- [ ] Check for look-ahead bias
- [ ] Profile performance
- [ ] Document decisions

### Evening
- [ ] Run full backtest comparison
- [ ] Document results
- [ ] Commit if improvement
- [ ] Revert if degradation
- [ ] Update progress log

## Success Metrics

### Week 1 Goals
- Baseline replicated: ~180 trades, ~215% ROI ✓
- All tests passing ✓
- No look-ahead bias verified ✓
- Clean, documented codebase ✓

### Week 2 Goals  
- 2-3 enhancements tested
- Performance improved by 10-20%
- Production monitoring in place
- Comprehensive documentation

### Red Flags to Watch For
- Sudden jump in trade count (possible look-ahead bias)
- Too-good-to-be-true results (data leakage)
- Increasing code complexity (over-engineering)
- Decreasing test coverage (cutting corners)
- Multiple changes at once (losing control)

## Final Reminders

1. **Simplicity is the ultimate sophistication**
2. **Trust but verify - especially your own code**
3. **When in doubt, choose the simpler solution**
4. **Measure twice, code once**
5. **The baseline already works - respect it**

---

*Ready to start tomorrow with discipline and focus.*