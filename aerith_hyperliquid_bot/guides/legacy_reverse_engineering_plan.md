# Legacy System Reverse Engineering Plan

## Date: January 24, 2025

## Problem Statement
Modern system achieves only +41.78% ROI vs Legacy's +215% ROI despite having:
- Fixed temporal alignment (dual EMA system)
- Correct threshold scales
- Proper trade distribution (94.6% long in bull year)
- More trades (222 vs 180)

**The 5x performance gap indicates we're missing CORE LOGIC, not just parameters.**

## Red Flags Identified

### 1. Identical Regime Distributions 🚨
- Legacy (hourly): 21.5% Bull, 21% Bear, 57.5% Neutral
- Modern (60s): 21.5% Bull, 21% Bear, 57.5% Neutral
- **This is IMPOSSIBLE if Modern updates 60x more frequently**

### 2. Profit Per Trade Gap
- Legacy: 1.19% per trade
- Modern: 0.19% per trade
- **6x difference suggests fundamental logic differences**

## Forensic Analysis Plan

### Phase 1: Verify Update Frequencies
**Script**: `verify_regime_updates.py`
- Count actual regime transitions in both systems
- Expected: Modern should have ~60x more transitions
- If not, Modern is using wrong data or update logic

### Phase 2: Deep Trade Analysis
**Script**: `legacy_system_analyzer.py`
- Analyze EVERY legacy trade to understand:
  - Exact signals used
  - Data sources (order book features?)
  - Entry conditions
  - Special filters or rules
  - The mysterious 'imbalance' field

### Phase 3: Configuration & Code Audit
**Script**: `check_hidden_features.py`
- Check for:
  - Position management limits (max 1 position?)
  - Time-based trading filters
  - Order book depth usage (beyond L5?)
  - Exit logic differences
  - Special data fields

## Key Hypotheses

### 1. The 'Imbalance' Field (Most Likely)
- Legacy data has 'imbalance' field in raw2/
- Modern data doesn't have this in features_1s/
- This could be pre-computed order flow imbalance
- **This might be the SECRET SAUCE**

### 2. Position Management
- Legacy might only allow 1 position at a time
- Modern allows multiple positions (diluting returns)
- Need to check `max_concurrent_positions` config

### 3. Hidden Order Book Features
- Legacy might use deeper book levels (L6-L20)
- Or calculate special features from order book
- Check for order book pressure, liquidity shape, etc.

### 4. Time/Market Filters
- Legacy might skip low liquidity hours
- Or have volatility-based filters
- Or funding rate considerations

### 5. Exit Logic Differences
- Legacy might have sophisticated exit evaluation
- Modern might be missing exit signal checks
- Could explain profit per trade difference

## Execution Plan

### Step 1: Run Analysis Scripts
```bash
# Check regime update frequencies
python scripts/verify_regime_updates.py

# Audit configuration and code
python scripts/check_hidden_features.py

# Deep forensic trade analysis
python scripts/legacy_system_analyzer.py
```

### Step 2: Analyze Results
- If regime updates are identical → Fix Modern's update logic
- If 'imbalance' is used → Recreate this feature for Modern
- If position limits exist → Implement in Modern
- If special filters found → Add to Modern

### Step 3: Implement Findings
- NO DUCT TAPE - fix at the root cause
- Maintain system isolation
- Test each change incrementally
- Verify performance improvements

## Expected Discoveries

1. **'Imbalance' field is critical** - Pre-computed order flow metric
2. **Position limits** - Legacy only takes 1 position at a time
3. **Exit timing** - Legacy has better exit evaluation
4. **Hidden filters** - Time-based or volatility-based trade filtering

## Success Criteria

1. Understand WHY Legacy performs 5x better
2. Identify the EXACT features/logic differences
3. Create actionable plan to implement in Modern
4. No parameter tuning - only fundamental fixes

## Next Session Focus

After running the analysis scripts, we'll have concrete evidence of what makes Legacy successful. Then we can implement the missing pieces properly, maintaining clean architecture and system isolation.