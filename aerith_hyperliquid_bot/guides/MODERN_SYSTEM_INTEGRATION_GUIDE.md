# Modern System Integration Guide
*Last Updated: 2025-07-17*

## Overview

This document tracks the integration of the overhauled modern system components with the existing backtesting infrastructure. It serves as a reference for understanding how the new clean architecture connects with the legacy codebase.

## Component Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Backtester Engine                          │
├─────────────────────────────────────────────────────────────────┤
│                                                                   │
│  ┌─────────────────┐     ┌──────────────────┐                   │
│  │ ModernDataLoader │────▶│ ModernDataAdapter│                   │
│  └─────────────────┘     └──────────────────┘                   │
│           │                        │                              │
│           ▼                        ▼                              │
│  ┌─────────────────┐     ┌──────────────────────┐               │
│  │  features_1s/   │     │ Transformed Signals  │               │
│  │  (raw data)     │     │ (expected schema)    │               │
│  └─────────────────┘     └──────────────────────┘               │
│                                   │                               │
│                                   ▼                               │
│                    ┌──────────────────────────────┐              │
│                    │ ModernContinuousDetectorV2   │              │
│                    │ (no hardcoded values)        │              │
│                    └──────────────────────────────┘              │
│                                   │                               │
│                                   ▼                               │
│                    ┌──────────────────────────────┐              │
│                    │    RegimeStateManager        │              │
│                    └──────────────────────────────┘              │
│                                   │                               │
│                                   ▼                               │
│                    ┌──────────────────────────────┐              │
│                    │    TF-v3 Strategy            │              │
│                    └──────────────────────────────┘              │
└─────────────────────────────────────────────────────────────────┘
```

## Key Integration Points

### 1. Data Loading Pipeline

**File**: `hyperliquid_bot/modern/data_loader.py`
- Must integrate `ModernDataAdapter` to transform raw features_1s data
- Key method to modify: `load_features_1s()`

```python
def load_features_1s(self, start_date, end_date):
    # Load raw data
    df_raw = self._load_feature_data(start_date, end_date)
    
    # NEW: Apply data adapter transformation
    df_adapted = self.data_adapter.adapt_features_dataframe(df_raw)
    
    return df_adapted
```

### 2. Detector Registration

**File**: `hyperliquid_bot/modern/registry.py`
- Register `ModernContinuousDetectorV2` with the modern registry
- Ensure it's accessible via `detector_type: "continuous_modern_v2"`

### 3. Configuration Loading

**File**: `hyperliquid_bot/config/settings.py`
- Must handle new detector settings without defaults
- Validate all required thresholds are present

### 4. Backtester Integration

**File**: `hyperliquid_bot/modern/backtester_engine.py`
- Must properly initialize detector with config
- Handle the 60-second update cadence
- Process signal quality metrics

## Migration Checklist

### Pre-Integration Validation
- [ ] Verify ModernDataContract handles all required fields
- [ ] Test ModernDataAdapter with sample data
- [ ] Validate ModernContinuousDetectorV2 initialization
- [ ] Check configuration loading without defaults

### Integration Steps
1. [ ] Update ModernDataLoader to use adapter
2. [ ] Register new detector in registry
3. [ ] Update config validation
4. [ ] Modify backtester to handle new detector
5. [ ] Test with small date range

### Post-Integration Validation
- [ ] No hardcoded values in logs
- [ ] Field mappings working (volume_imbalance)
- [ ] Signal quality metrics logged
- [ ] State transitions logged
- [ ] No crashes from missing fields

## Configuration Requirements

The new system REQUIRES these settings in the override YAML:

```yaml
regime:
  detector_type: "continuous_modern_v2"
  
  # ALL of these are required (no defaults!)
  gms_vol_high_thresh: <value>
  gms_vol_low_thresh: <value>
  gms_mom_strong_thresh: <value>
  gms_mom_weak_thresh: <value>
  gms_spread_std_high_thresh: <value>
  gms_spread_mean_low_thresh: <value>
  gms_obi_strong_confirm_thresh: <value>
  gms_obi_weak_confirm_thresh: <value>
```

## Error Handling

### Common Issues and Solutions

1. **"Missing required detector settings"**
   - Cause: Configuration missing required thresholds
   - Solution: Add all required settings to override YAML

2. **"Signal quality too low"**
   - Cause: Too many missing fields in data
   - Solution: Check data quality, enable partial signals

3. **"Critical signals missing: ['volume_imbalance']"**
   - Cause: Data adapter not applied
   - Solution: Ensure ModernDataLoader uses adapter

## Testing Strategy

### Unit Tests
1. Test data adapter field mapping
2. Test detector with perfect signals
3. Test detector with missing signals
4. Test configuration validation

### Integration Tests
1. Full pipeline with real data
2. Verify no look-ahead bias
3. Check state transition logic
4. Validate trade generation

### System Tests
1. Run 1-week backtest
2. Verify 60+ trades generated
3. Check performance metrics
4. Compare with legacy system

## Monitoring and Debugging

### Key Log Messages to Watch

```
INFO - Applied field mappings: {'obi_smoothed': 'volume_imbalance'}
INFO - State change: UNCERTAIN -> WEAK_BULL_TREND (confidence: 0.75, signal_quality: 0.95)
WARNING - Low signal quality: 0.45
WARNING - Critical signals missing: ['ma_slope_ema_30s']
```

### Metrics to Track
- Signal quality score (0-1)
- State confidence (0-1)
- Field mapping count
- Null values handled
- State transitions per hour

## Rollback Plan

If issues arise:
1. Change `detector_type` back to `continuous_gms`
2. Remove data adapter from pipeline
3. Revert to original ModernContinuousDetector

All new components are isolated and won't affect legacy system.

## Next Steps

1. Create integration test script
2. Run validation on 1-week sample
3. Tune thresholds based on results
4. Document performance metrics
5. Plan for full 2024 backtest