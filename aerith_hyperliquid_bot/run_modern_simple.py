#!/usr/bin/env python3
"""
Simple Modern Backtest Runner
============================
One-click solution to run modern backtest with dynamic period configuration.
"""

import subprocess
import sys
import yaml
from pathlib import Path

# Project root
project_root = Path(__file__).resolve().parent

def load_config(config_path):
    """Load YAML configuration file"""
    try:
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"Error loading config {config_path}: {e}")
        return None

def parse_period_preset(period_preset, custom_start=None, custom_end=None):
    """Parse period preset and return start/end dates"""
    if period_preset == 'full':
        # Full available data range (assume 2024 for now)
        return ['--start-date', '2024-01-01', '--end-date', '2024-12-31']
    elif period_preset == 'custom':
        if custom_start and custom_end:
            return ['--start-date', custom_start, '--end-date', custom_end]
        else:
            print("Error: custom period requires custom_start_date and custom_end_date")
            return None
    elif len(period_preset) == 4 and period_preset.isdigit():  # YYYY format
        year = period_preset
        return ['--start-date', f'{year}-01-01', '--end-date', f'{year}-12-31']
    elif len(period_preset) == 6 and period_preset[4:6] in ['Q1', 'Q2', 'Q3', 'Q4']:  # YYYYQX format
        year = period_preset[:4]
        quarter = period_preset[4:6]
        
        if quarter == 'Q1':
            return ['--start-date', f'{year}-01-01', '--end-date', f'{year}-03-31']
        elif quarter == 'Q2':
            return ['--start-date', f'{year}-04-01', '--end-date', f'{year}-06-30']
        elif quarter == 'Q3':
            return ['--start-date', f'{year}-07-01', '--end-date', f'{year}-09-30']
        elif quarter == 'Q4':
            return ['--start-date', f'{year}-10-01', '--end-date', f'{year}-12-31']
    else:
        print(f"Error: Unknown period preset '{period_preset}'")
        return None

def main():
    print("=" * 80)
    print("MODERN SYSTEM BACKTEST - SIMPLE RUNNER")
    print("=" * 80)
    print()
    
    # Load configuration
    config_path = project_root / "configs/overrides/modern_system_v2_complete.yaml"
    config = load_config(config_path)
    
    if not config:
        print("Failed to load configuration, exiting.")
        return 1
    
    # Extract backtest configuration
    backtest_config = config.get('backtest', {})
    period_preset = backtest_config.get('period_preset', '2024')
    custom_start = backtest_config.get('custom_start_date')
    custom_end = backtest_config.get('custom_end_date')
    
    print(f"Period preset: {period_preset}")
    if period_preset == 'custom':
        print(f"Custom period: {custom_start} to {custom_end}")
    print("Risk per trade: 25% (from config)")
    print()
    
    # Parse period arguments
    period_args = parse_period_preset(period_preset, custom_start, custom_end)
    if not period_args:
        return 1
    
    # Build command
    cmd = [
        sys.executable,
        str(project_root / "scripts" / "run_modern_backtest.py"),
        "--override", "configs/overrides/modern_system_v2_complete.yaml"
    ] + period_args
    
    print("Command:", " ".join(cmd))
    print("-" * 80)
    
    try:
        # Run the backtest
        result = subprocess.run(cmd, cwd=str(project_root))
        return result.returncode
    except KeyboardInterrupt:
        print("\n\nBacktest interrupted by user")
        return 1
    except Exception as e:
        print(f"\nError: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())