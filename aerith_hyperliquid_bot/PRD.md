PRD_v2.1_rev1_fullpath.txt

Product Requirements Document (PRD): Aerith Hyperliquid Bot Development (AI Collaboration)

Version: 2.1 rev1 (MCP/HMM Filter Test & Microstructure Pivot Plan)

Date: 2025-04-19

Author/User: jpegcollector

AI Collaborator: Gemini / Claude / Grok / GPT-4 (acting as Expert Quant Developer/Analyst)

Project Goal: Develop a near-institutional-grade trading bot framework for Hyperliquid. The immediate focus is executing a structured 13-week plan starting with Phase 3: Filter Validation. This phase involves integrating external sentiment/flow data (MCP sources) and testing a simple 3-state Hidden Markov Model (HMM) as potential filters for the existing Trend Following (TF) strategy logic. A clear decision gate based on quantitative metrics (Profit Factor ≥ 1.15, Max Drawdown ≤ 20%, Trade Count ≤ ~80/year under taker simulation) will determine if the filtered TF approach warrants further development. If not, the primary path pivots cleanly to Phase 4: Microstructure Alpha, focusing on developing and validating strategies (starting with OBI Scalper) that directly exploit Hyperliquid's market microstructure. The underlying goal remains achieving a risk-adjusted target ROI (e.g., 10-30%) with acceptable drawdowns.

1. Overview

Initial development established a baseline TF strategy and integrated L2 features for a Granular Microstructure regime detector (GMS). Subsequent simulation improvements fixed critical slippage calculation errors and introduced "Forced Taker" and "Probabilistic Maker" execution models. While the TF+GMS combination showed high simulated ROI, deeper investigation revealed the GMS detector's 7-state classifications were unreliable and counter-intuitive, even after tuning attempts. The GMS detector approach for direct strategy mapping is therefore archived. The high ROI figures are also considered optimistic due to simplifications in the maker simulation model.

The project now adopts a structured 13-week plan focusing first on validating potential filters (external data, HMM) for the archived baseline TF logic (Phase 3). Failure to meet objective performance gates triggers a definitive pivot to microstructure strategies (Phase 4 onwards).

2. Goals & Objectives

*   G1: Robust Framework & Simulation (Status: Built, Simulation Baseline Established)
    *   Solid framework components functional.
    *   Execution simulation improved (Taker/ProbMaker modes functional, slippage calc fixed). Known Limitation: ProbMaker mode is optimistic.
    *   GMS Detector archived for direct strategy mapping.
*   G2: Achieve Target ROI via Robust Strategy (Status: Path Refined)
    *   Current Objective (Phase 3): Validate if external data or a simple HMM filter can create a robustly profitable filtered TF strategy (PF >= 1.15, DD <= 20%, Trades <= ~80/yr in Taker Sim) by Day 10.
    *   Contingent Objective (Phase 4+): If Phase 3 fails, identify and validate microstructure-based strategies (OBI, OFI, Liqs) capable of achieving target ROI (e.g., Sharpe >= 1.2 for OBI) under the established simulation model.
*   G3: DEX Alpha Exploitation (Status: Phased Approach - Filter First, Then Microstructure)
    *   Phase 3.1: Integrate external sentiment/flow data. Identify candidate features using objective criteria (Promotion rule: |ρ| > 0.20 AND AUROC > 0.55 (lag 0 or +1 h)).
    *   Phase 3.2: Test HMM as a regime filter.
    *   Phase 4 onwards: Pivot to direct microstructure alpha (OBI, OFI, Liqs) if Phase 3 fails the decision gate.
*   G4: Modular & Testable Framework (Status: Cleanup Pending)
    *   Maintain modularity. Use DataHandler for raw I/O, SignalEngine (formerly SignalCalculator) for derived columns/features.
    *   Objective: Simplify configuration post-Phase 3 decision gate (remove GMS, MR, MV, Hurst parameters).
*   G5: Future AI/ML Integration (Status: HMM Near-Term, LSTM Deferred)
    *   Phase 3.2: Implement and test a simple 3-state Gaussian HMM for regime filtering. Freeze the bull/bear/chop mapping after first fit; no relabelling within 2024 data. HMM model files potentially stored under models/regime_filters/.
    *   LSTM model remains deferred.
*   G6: Live Trading Readiness (Status: Simulation Baseline Set)
    *   Simulation improved but acknowledged limitations remain. Further hardening deferred to Phase 6. Kelly Sizing also deferred to Phase 6.
*   G7: Critical AI Collaboration (Status: Ongoing)

3. Target User & AI Role

*   User: jpegcollector (Project Lead, Domain Expert, Coder)
*   AI Collaborator: Gemini / Claude / Grok / GPT-4 (Acting as Expert Quant Developer/Analyst)

4. Scope (Current Roadmap - 13-Week Plan)

*   In Scope (Phase 3.1 - MCP Overlay - Days 1-5):
    *   Integrate Fear & Greed, CryptoSentiment, WhaleTracker data (calling underlying APIs). Use descriptive names like fear_greed_idx.
    *   Append data to signals DataFrame.
    *   Visualize overlays.
    *   Correlate vs. TF trade P/L. Promotion rule for candidate features: |ρ| > 0.20 AND AUROC > 0.55 (lag 0 or +1 h).
*   In Scope (Phase 3.2 - HMM Filter Test - Days 6-10):
    *   Implement feature builder (log-return, vol, candidate MCP features).
    *   Train/Predict 3-state GaussianHMM.
    *   Label states (bull/bear/chop) - Freeze mapping after initial fit.
    *   Wire HMM state as a filter ('skip if chop') for the baseline TF strategy logic.
    *   Backtest Filtered TF (Taker-Only Sim).
    *   Decision Gate: Compare Filtered TF vs. Vanilla TF based on PF >= 1.15, DD <= 20%, and Trade Count <= ~80/year criteria.
*   In Scope (Phase 4 - OBI Scalper MVP - Weeks 3-6 - Contingent):
    *   (If Decision Gate -> Pivot) Simplify Config (remove GMS, MR, MV, Hurst).
    *   Setup Tick Data Ingestion.
    *   Calculate OBI/OFI/Spread features.
    *   Implement OBI_Scalper strategy.
    *   Backtest OBI Scalper (Taker Sim).
    *   Paper Trade OBI Scalper.
    *   Tune OBI Scalper OOS. Decision Gate: Sharpe >= 1.2 on 2024 taker sim.
*   In Scope (Phase 5 - Flow/Liq Alpha - Weeks 7-9 - Contingent):
    *   Refine OFI using external data.
    *   Integrate Liquidation data.
    *   Implement/Test Liquidation Reversion strategy.
    *   Combine strategies.
*   In Scope (Phase 6 - Polish - Weeks 10-13 - Contingent):
    *   Config Cleanup (if not done earlier).
    *   Refactor execution router (asyncio).
    *   Add latency/slippage monitoring.
    *   Implement VaR / kill-switch risk controls.
    *   Implement fractional Kelly overlay (α ≤ 0.5) once live Sharpe > 2 and VaR control is in place.
    *   Dry-run live simulation.
*   Out of Scope (Immediate): GMS detector usage/tuning, TF parameter optimization (beyond baseline), Kelly sizing (until Phase 6), advanced simulation enhancements, LSTM, HMM portfolio construction, strategies other than TF(filter)/OBI/Liq.

5. Current Development State (As of PRD v2.1 rev1)

*   Phase: Starting Phase 3.1: MCP Overlay.
*   Completed:
    *   TF benchmark established.
    *   GMS detector built & investigated; archived.
    *   Execution simulation fixed & enhanced (Taker/ProbMaker modes, reproducibility fixed).
    *   Backtest data saving implemented.
    *   Visualization script implemented.
    *   Adopted new 13-week roadmap.
*   Current Status: Ready to integrate first MCP data feed (Fear & Greed via direct API call).
*   Next Task: Implement data_providers/feargreed.py and wire it into DataHandler/SignalEngine to add fear_greed_idx column to signals data.

6. Agreed Development Roadmap (13-Week Plan)

*   Phase 3.1: MCP Overlay (Days 1-5) - Integrate F&G, Sentiment, Whale data; Visualize; Correlate. Exit: Candidate features identified (|ρ|>0.2, AUROC>0.55).
*   Phase 3.2: HMM Filter Test (Days 6-10) - Build features; Train/Predict HMM; Label states (freeze map); Filter TF logic; Validate (Taker Sim). Exit: Decision Gate on Filtered TF (PF≥1.15, DD≤20%, Trades≤~80).
*   Phase 4: OBI Scalper MVP (Wk 3-6 - Contingent) - Simplify Config; Tick Data; OBI/OFI Feats; OBI Strat; Backtest; Paper Trade; Tune. Exit: Viable OBI strategy (Sharpe≥1.2) or freeze.
*   Phase 5: Flow/Liq Alpha (Wk 7-9 - Contingent) - OFI Refine; Liq Data; Liq Strat; Combine.
*   Phase 6: Polish (Wk 10-13 - Contingent) - Config Cleanup; Async Router; Monitoring; Risk Controls; Implement Kelly Overlay (α≤0.5, if Sharpe>2 & VaR ok); Dry Run.

7. Key Technical Decisions & Data

*   Language/Libs: Python 3.11+, Pandas, NumPy, Pydantic v2, PyYAML, PyArrow, Matplotlib, mplfinance, Pytest, Pandas-TA, JSON, httpx, scikit-learn (for HMM).
*   Configuration: `config.yaml`. (To be simplified). Use descriptive key names (e.g., `fear_greed_idx`).
*   Data Sources:
    *   L2 Snapshots (Parquet): /Users/<USER>/Desktop/trading_bot_/hyperliquid_data/raw2/
    *   OHLCV (CSV): /Users/<USER>/Desktop/trading_bot_/hyperliquid_data/resampled/
    *   External APIs (Phase 3.1): Alternative.me (F&G), etc. (via direct calls)
    *   Tick Data (Phase 4): TBD Provider (Data stored likely in /Users/<USER>/Desktop/trading_bot_/hyperliquid_data/ticks/)
    *   External APIs (Phase 5): TBD MCP/API sources for OFI/Liq.
    *   External Cache: `/Users/<USER>/Desktop/trading_bot_/hyperliquid_data/external/`
    *   Backtest Outputs: /Users/<USER>/Desktop/trading_bot_/logs/ (contains backtest_signals_*.parquet, backtest_trades_*.json)
*   Regime Detection: GMS Archived. Simple 3-state HMM to be tested as a filter.
*   Strategy Logic: Baseline TF logic (8/128 EMA) used for filter test. OBI/Liq logic TBD.
*   Execution Simulation: Probabilistic Maker Model (optimistic) for general testing; Taker-Only Mode for critical decision gates.
*   Core Alpha Hypothesis (Revised v8):
    *   (Hypothesis A - Phase 3): External factors (sentiment/flow) or a simple HMM filter can improve the baseline TF logic to meet validation criteria (PF≥1.15, DD≤20%, Trades≤~80).
    *   (Hypothesis B - Phase 4+): If A fails, Hyperliquid's microstructure (OBI, OFI, Liqs) contains exploitable short-term alpha (e.g., OBI Sharpe ≥ 1.2).

8. Project File Structure & Key Data Formats

*   Root Project Directory: /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/
    ```
    aerith_hyperliquid_bot/
    ├── .gitignore
    ├── config.yaml           # Main configuration file
    ├── main.py               # Example entry point for running backtests (or run_backtest.py)
    ├── pyproject.toml        # Project metadata and dependencies (or requirements.txt)
    ├── README.md             # Project overview and setup
    ├── roadmap.md            # Detailed development roadmap
    ├── project_structure.md  # Project file structure overview
    ├── message_to_ai.md      # Context summary for AI collaborators
    ├── aerith_hyperliquid_bot/ # Main Python package source code
    │   └── hyperliquid_bot/
    │       ├── __init__.py
    │       ├── backtester/
    │       │   ├── __init__.py
    │       │   └── backtester.py     # Core backtesting loop, orchestration
    │       ├── config/
    │       │   ├── __init__.py
    │       │   └── settings.py       # Pydantic models for config validation
    │       ├── core/
    │       │   ├── __init__.py
    │       │   ├── detector.py       # Regime detectors (GMS Archived, HMM TBD)
    │       │   └── risk.py           # Risk management, position sizing (ATR-based)
    │       ├── data/
    │       │   ├── __init__.py
    │       │   ├── handler.py        # Loads OHLCV, L2 features
    │       │   └── providers/        # New: For external data fetching
    │       │       ├── __init__.py
    │       │       └── feargreed.py    # Example: Fetches Fear & Greed data
    │       │       └── (Other providers...)
    │       ├── execution/
    │       │   ├── __init__.py
    │       │   └── simulation.py     # Order execution simulation (Taker/ProbMaker modes)
    │       ├── features/
    │       │   ├── __init__.py
    │       │   ├── microstructure.py # OBI, Depth, Spread calculations
    │       │   ├── statistical.py    # (Inactive - Hurst)
    │       │   └── hmm_features.py   # New: Feature generation for HMM
    │       ├── models/               # New: For ML models
    │       │   ├── __init__.py
    │       │   └── regime_filters/   # New: Proposed location for HMM model files/logic
    │       │       ├── __init__.py
    │       │       └── hmm_model.py    # Example: HMM training/prediction logic
    │       ├── portfolio/
    │       │   ├── __init__.py
    │       │   └── portfolio.py      # Manages balance, positions, trades, P/L, fees
    │       ├── signals/              # Renamed from calculator -> engine conceptually
    │       │   ├── __init__.py
    │       │   └── signal_engine.py  # Calculates indicators, combines data (formerly calculator.py)
    │       ├── strategies/
    │       │   ├── __init__.py
    │       │   ├── evaluator.py      # Strategy evaluation logic (contains TF logic)
    │       │   ├── obi_scalper.py    # TBD: OBI Scalper strategy
    │       │   └── liq_strat.py      # TBD: Liquidation strategy
    │       └── utils/                # Optional: Utility functions
    │           ├── __init__.py
    │           └── (Utility modules...)
    ├── logs/                   # Directory for log files, backtest outputs
    │   ├── backtest_signals_*.parquet # Saved signals DataFrame per run
    │   └── backtest_trades_*.json     # Saved trades list per run
    ├── plots/                  # Directory for saving visualization plots
    ├── scripts/                # Utility and analysis scripts
    │   ├── __init__.py
    │   └── visualize_backtest.py # Script to generate backtest plots
    ├── tests/                  # Unit and integration tests (pytest)
    │   ├── __init__.py
    │   └── (Test files...)
    └── (Other config files like .env if used)
    ```
*   External Data Directories:
    ```
    /Users/<USER>/Desktop/trading_bot_/hyperliquid_data/
    ├── raw2/                   # Raw L2 Parquet snapshots (Input)
    ├── resampled/              # Resampled OHLCV CSV data (Input)
    └── ticks/                  # TBD: Directory for downloaded tick data (Phase 4)
    ```

9. How to Use This PRD (For AI Collaboration)

*   Reflects the current 13-week roadmap starting with Phase 3.1 (MCP Integration).
*   Note the archived status of GMS detector. Phase 3 tests filters for baseline TF logic.
*   Use roadmap (Section 6) and scope (Section 4) for current tasks & decision criteria (PF, DD, Trades, Sharpe thresholds).
*   Refer to Section 7 for simulation model status and data sources.

10. Message for Future AI Collaborator (Continuity Note - v2.1 rev1 Update)

*   Project Status: Starting Phase 3.1 of a new 13-week roadmap. GMS detector archived due to unreliable classifications. Focus is now on testing filters (external data, HMM) for the baseline TF logic. Simulation uses "Probabilistic Maker" (optimistic) for exploration and "Taker-Only" for decision gates. Reproducibility fixed. Visualization tools ready.
*   Current Roadmap:
    *   Phase 3.1 (Days 1-5 - CURRENT): Integrate external data (Fear&Greed direct API, Sentiment, Whale Flow) as potential features/filters. Identify candidate features (|ρ|>0.2, AUROC>0.55).
    *   Phase 3.2 (Days 6-10): Test simple 3-state HMM as regime filter ('skip if chop') for baseline TF. Decision Gate: Keep Filtered TF if PF>=1.15 & DD<=20% & Trades<=~80 (Taker Sim). Otherwise, archive TF entirely.
    *   Phase 4+ (Likely Path): Pivot to OBI Scalper if TF fails gate. Simplify config. Setup tick data. Implement OBI strat. Test OBI (Target Sharpe>=1.2 Taker Sim). Add OFI/Liq later.
    *   Phase 5/6: Further microstructure strategies & hardening for live trading. Kelly Sizing added to Phase 6.
*   Immediate Next Task: Implement fetching Fear & Greed data from Alternative.me API (direct call, not MCP wrapper initially) and add it as fear_greed_idx column to signals DataFrame, likely via DataHandler or SignalEngine.
*   AI Collaboration Guidance: Assist with data fetching (Phase 3.1), HMM implementation/validation (Phase 3.2), evaluating results vs gates, config simplification, OBI scalper design/implementation (Phase 4). Use correct simulation mode per task context (ProbMaker for exploration, Taker-Only for gates). Be mindful of simulation optimism. Refer to PRD v2.1 rev1 for details.