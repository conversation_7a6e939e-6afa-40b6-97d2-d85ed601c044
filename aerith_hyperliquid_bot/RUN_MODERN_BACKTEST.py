#!/usr/bin/env python3
"""
MODERN BACKTEST - ONE BUTTON SOLUTION
=====================================

This script runs the modern backtest with all the correct settings.
Just run: python3 RUN_MODERN_BACKTEST.py

What it does:
1. Uses modern_system_v2_complete.yaml with 25% risk (fixed)
2. Runs 1 day test by default
3. Shows clear progress and results
"""

import subprocess
import sys
import os
from pathlib import Path
from datetime import datetime

# Colors for terminal output
RED = '\033[91m'
GREEN = '\033[92m'
YELLOW = '\033[93m'
BLUE = '\033[94m'
RESET = '\033[0m'

def main():
    # Clear screen
    os.system('clear' if os.name == 'posix' else 'cls')
    
    print(f"{BLUE}{'=' * 80}{RESET}")
    print(f"{BLUE}AERITH MODERN SYSTEM BACKTEST{RESET}")
    print(f"{BLUE}{'=' * 80}{RESET}")
    print()
    
    # Default settings
    start_date = "2024-01-01"
    end_date = "2024-01-02"
    config_file = "configs/overrides/modern_system_v2_complete.yaml"
    
    print(f"{GREEN}Configuration:{RESET}")
    print(f"  • Config: {config_file}")
    print(f"  • Period: {start_date} to {end_date}")
    print(f"  • Risk per trade: 25% (fixed)")
    print(f"  • Detector: continuous_modern_v2")
    print(f"  • Strategy: TF-v3 modern")
    print()
    
    print(f"{YELLOW}⚠️  Note: This may take 2-5 minutes due to 1-second data processing{RESET}")
    print()
    
    # Get script directory
    script_dir = Path(__file__).resolve().parent
    
    # Build command
    cmd = [
        sys.executable,
        str(script_dir / "scripts" / "run_modern_backtest.py"),
        "--start-date", start_date,
        "--end-date", end_date,
        "--override", config_file,
        "--output", "modern_backtest_results.json"
    ]
    
    print(f"{BLUE}Starting backtest...{RESET}")
    print("-" * 80)
    
    try:
        # Run the backtest
        process = subprocess.Popen(
            cmd,
            cwd=str(script_dir),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Stream output
        for line in process.stdout:
            # Highlight important lines
            if "Total Trades:" in line:
                print(f"{GREEN}{line.rstrip()}{RESET}")
            elif "Total Return:" in line:
                print(f"{GREEN}{line.rstrip()}{RESET}")
            elif "ERROR" in line or "Error" in line:
                print(f"{RED}{line.rstrip()}{RESET}")
            elif "Trade #" in line:
                print(f"{YELLOW}{line.rstrip()}{RESET}")
            elif "Progress:" in line:
                print(f"{BLUE}{line.rstrip()}{RESET}")
            else:
                print(line.rstrip())
        
        # Wait for completion
        process.wait()
        
        if process.returncode == 0:
            print()
            print(f"{GREEN}{'=' * 80}{RESET}")
            print(f"{GREEN}✅ BACKTEST COMPLETED SUCCESSFULLY!{RESET}")
            print(f"{GREEN}{'=' * 80}{RESET}")
            
            # Check if results file exists
            results_file = script_dir / "modern_backtest_results.json"
            if results_file.exists():
                print(f"\n📊 Results saved to: {results_file}")
                
                # Try to show quick summary
                try:
                    import json
                    with open(results_file) as f:
                        results = json.load(f)
                    
                    perf = results.get('performance', {})
                    print(f"\n{BLUE}Summary:{RESET}")
                    print(f"  • Total Trades: {perf.get('total_trades', 0)}")
                    print(f"  • Total Return: {perf.get('total_return', 0):.2%}")
                    print(f"  • Win Rate: {perf.get('win_rate', 0):.2%}")
                except:
                    pass
        else:
            print()
            print(f"{RED}{'=' * 80}{RESET}")
            print(f"{RED}❌ BACKTEST FAILED{RESET}")
            print(f"{RED}{'=' * 80}{RESET}")
            
            print(f"\n{YELLOW}Troubleshooting:{RESET}")
            print("1. Check if data files exist in hyperliquid_data/features_1s/2024-01-01/")
            print("2. Check logs/ directory for detailed error messages")
            print("3. Try running with --verbose flag for more output")
            
        return process.returncode
        
    except KeyboardInterrupt:
        print(f"\n\n{YELLOW}⚠️  Backtest interrupted by user{RESET}")
        return 1
    except Exception as e:
        print(f"\n{RED}Error: {e}{RESET}")
        return 1


if __name__ == "__main__":
    sys.exit(main())