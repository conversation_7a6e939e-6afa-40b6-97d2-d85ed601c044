#!/usr/bin/env python3
"""
Test the modern system with adjusted momentum thresholds.

The original thresholds (50/100) were too high for the enhanced hourly data.
This test uses adjusted thresholds based on the actual data distribution.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import logging
from datetime import datetime

from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.backtest_engine import RobustBacktestEngine

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """Run test with adjusted thresholds."""
    print("\n" + "="*80)
    print("TEST: MODERN SYSTEM WITH ADJUSTED MOMENTUM THRESHOLDS")
    print("="*80)
    
    # Load config with adjusted thresholds
    config_path = Path(__file__).parent / "configs/overrides/modern_system_v2_adjusted_thresholds.yaml"
    config = load_config(str(config_path))
    
    # Show configuration
    print("\nConfiguration:")
    print(f"  Detector type: {config.regime.detector_type}")
    print(f"  Momentum thresholds: {config.regime.gms_mom_weak_thresh} / {config.regime.gms_mom_strong_thresh}")
    print(f"  OBI thresholds: {config.microstructure.gms_obi_weak_confirm_thresh} / {config.microstructure.gms_obi_strong_confirm_thresh}")
    
    # Test period - one month to get meaningful results
    start_date = datetime(2024, 2, 1)
    end_date = datetime(2024, 2, 29, 23, 59, 59)
    
    print(f"\nTest Period: {start_date.date()} to {end_date.date()}")
    print("-" * 80)
    
    try:
        # Create engine
        engine = RobustBacktestEngine(
            config=config,
            start_date=start_date,
            end_date=end_date,
            use_regime_cache=False,  # Calculate fresh
            strict=False  # Allow fallbacks to complete test
        )
        
        print("\n✅ Engine initialized successfully")
        
        # Run the backtest
        print("\nRunning backtest...")
        results = engine.run_backtest()
        
        print("\n✅ Backtest completed")
        
        # Analyze results
        print("\n" + "="*80)
        print("RESULTS:")
        print("="*80)
        
        print(f"\nTrade Statistics:")
        print(f"  Total Trades: {results.get('total_trades', 0)}")
        print(f"  Winning Trades: {results.get('winning_trades', 0)}")
        print(f"  Losing Trades: {results.get('losing_trades', 0)}")
        print(f"  Win Rate: {results.get('win_rate', 0):.1%}")
        
        print(f"\nPerformance:")
        print(f"  Total Return: {results.get('total_return', 0):.2%}")
        print(f"  Average Return per Trade: {results.get('average_return', 0):.2%}")
        print(f"  Sharpe Ratio: {results.get('sharpe_ratio', 0):.2f}")
        print(f"  Max Drawdown: {results.get('max_drawdown', 0):.2%}")
        
        # Check regime detection
        if 'regime_sources' in results:
            sources = results['regime_sources']
            total = sources.get('total', 1)
            if total > 0:
                print(f"\nRegime Detection Sources:")
                print(f"  Detector: {sources.get('detector_used', 0)/total*100:.1f}%")
                print(f"  Fallback: {sources.get('fallback_used', 0)/total*100:.1f}%")
        
        # Show some trades if any
        if results.get('total_trades', 0) > 0:
            trades = results.get('trades', [])
            print(f"\nSample Trades (first 3):")
            for i, trade in enumerate(trades[:3]):
                print(f"\n  Trade {i+1}:")
                print(f"    Entry: {trade.get('entry_time')} at ${trade.get('entry_price', 0):.2f}")
                print(f"    Direction: {trade.get('direction', 'unknown')}")
                print(f"    Regime: {trade.get('entry_regime', 'unknown')}")
                if trade.get('status') == 'closed':
                    print(f"    Exit: {trade.get('exit_time')} at ${trade.get('exit_price', 0):.2f}")
                    print(f"    P&L: {trade.get('pnl_pct', 0):.2%}")
                    print(f"    Reason: {trade.get('exit_reason', 'unknown')}")
        
        # Summary
        print("\n" + "="*80)
        print("SUMMARY:")
        print("="*80)
        
        if results.get('total_trades', 0) == 0:
            print("❌ Still no trades generated")
            print("   Possible issues:")
            print("   1. Entry conditions still too restrictive")
            print("   2. Need to check OBI confirmation thresholds")
            print("   3. Strategy logic may need adjustment")
        else:
            print(f"✅ Generated {results.get('total_trades', 0)} trades!")
            print(f"✅ Total return: {results.get('total_return', 0):.2%}")
            
            if results.get('total_return', 0) > 0:
                print("→ System is profitable with adjusted thresholds")
            else:
                print("→ System generates trades but needs optimization")
        
    except Exception as e:
        print(f"\n❌ ERROR: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()