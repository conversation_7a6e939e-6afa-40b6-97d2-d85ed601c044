#!/usr/bin/env python3
"""
Debug script to check configuration values for auto_thresholds
"""

import sys
import os

# Add the parent directory to the path to find the hyperliquid_bot module
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from hyperliquid_bot.config.settings import load_config

def main():
    # Load the base configuration
    config = load_config("configs/base.yaml")

    print("=== Configuration Debug ===")
    print(f"regime.detector_type: {getattr(config.regime, 'detector_type', 'NOT_SET')}")
    print(f"gms section exists: {hasattr(config, 'gms')}")

    if hasattr(config, 'gms'):
        print(f"gms.auto_thresholds: {getattr(config.gms, 'auto_thresholds', 'NOT_SET')}")
        print(f"gms.detector_type: {getattr(config.gms, 'detector_type', 'NOT_SET')}")
        print(f"gms.cadence_sec: {getattr(config.gms, 'cadence_sec', 'NOT_SET')}")

    # Test the detector factory
    from hyperliquid_bot.core.detector_factory import get_regime_detector

    print("\n=== Testing Detector Factory ===")
    detector = get_regime_detector(config)

    print(f"Detector type: {type(detector).__name__}")
    if hasattr(detector, 'adaptive_vol_threshold'):
        print(f"adaptive_vol_threshold: {detector.adaptive_vol_threshold}")
        print(f"adaptive_mom_threshold: {detector.adaptive_mom_threshold}")

    # Check the config after factory processing
    print(f"\nAfter factory - gms.auto_thresholds: {getattr(config.gms, 'auto_thresholds', 'NOT_SET')}")

if __name__ == "__main__":
    main()
