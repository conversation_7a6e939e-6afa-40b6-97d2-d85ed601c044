#!/usr/bin/env python3
"""
Analyze regime distribution in February 2024 with adjusted thresholds.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import pandas as pd
import numpy as np
from datetime import datetime
from hyperliquid_bot.config.settings import load_config
from hyperliquid_bot.modern.enhanced_regime_detector import EnhancedRegimeDetector

def main():
    """Analyze regime distribution with adjusted thresholds."""
    
    # Load config
    config_path = Path(__file__).parent / "configs/overrides/modern_system_v2_adjusted_thresholds.yaml"
    config = load_config(str(config_path))
    
    # Initialize detector
    detector = EnhancedRegimeDetector(config)
    
    print("="*80)
    print("REGIME DISTRIBUTION ANALYSIS - FEBRUARY 2024")
    print("="*80)
    
    # Track regimes
    regime_counts = {}
    strong_trend_hours = []
    
    # Load February data
    for day in range(2, 29):  # Feb 2-28, 2024
        try:
            file = f"/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/../hyperliquid_data/enhanced_hourly/1h/2024-02-{day:02d}_1h_enhanced.parquet"
            data = pd.read_parquet(file)
            
            for idx, row in data.iterrows():
                # Prepare signals
                signals = {
                    'atr_percent': row.get('atr_percent_sec', np.nan),
                    'ma_slope': row.get('ma_slope', np.nan),
                    f'obi_smoothed_{config.microstructure.depth_levels}': row.get('volume_imbalance', np.nan),
                    'spread_mean': row.get('spread_mean', np.nan),
                    'spread_std': row.get('spread_std', np.nan),
                    'volume': row.get('volume', 0),
                    'timestamp': row.name
                }
                
                # Skip if missing key signals
                if pd.isna(signals['atr_percent']) or pd.isna(signals['ma_slope']):
                    continue
                
                # Get regime
                regime = detector.detect_regime(signals)
                regime_counts[regime] = regime_counts.get(regime, 0) + 1
                
                # Track strong trends
                if regime in ['Strong_Bull_Trend', 'Strong_Bear_Trend']:
                    strong_trend_hours.append({
                        'timestamp': row.name,
                        'regime': regime,
                        'ma_slope': signals['ma_slope'],
                        'obi': signals[f'obi_smoothed_{config.microstructure.depth_levels}'],
                        'atr_percent': signals['atr_percent']
                    })
                    
        except Exception as e:
            pass
    
    # Show distribution
    total = sum(regime_counts.values())
    print(f"\nTotal hours analyzed: {total}")
    print("\nRegime distribution:")
    for regime, count in sorted(regime_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  {regime:20s}: {count:4d} ({count/total*100:5.1f}%)")
    
    # Analyze strong trends
    print("\n" + "="*80)
    print("STRONG TREND ANALYSIS")
    print("="*80)
    
    strong_count = len(strong_trend_hours)
    print(f"\nTotal Strong Trend hours: {strong_count} ({strong_count/total*100:.1f}%)")
    
    if strong_count > 0:
        print(f"\nFirst 5 Strong Trend periods:")
        for i, period in enumerate(strong_trend_hours[:5]):
            print(f"\n  {i+1}. {period['timestamp']} - {period['regime']}")
            print(f"     MA Slope: {period['ma_slope']:.2f}")
            print(f"     OBI: {period['obi']:.3f}")
            print(f"     ATR%: {period['atr_percent']:.4f}")
    else:
        print("\n❌ NO STRONG TRENDS DETECTED!")
        print("\nThis explains why no trades are generated.")
        print("The adjusted momentum thresholds (10/20) may still be too high.")
    
    # Check what would happen with lower thresholds
    print("\n" + "="*80)
    print("THRESHOLD SENSITIVITY ANALYSIS")
    print("="*80)
    
    # Count how many would be strong with different thresholds
    ma_slopes = []
    for day in range(2, 29):
        try:
            file = f"/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/../hyperliquid_data/enhanced_hourly/1h/2024-02-{day:02d}_1h_enhanced.parquet"
            data = pd.read_parquet(file)
            ma_slopes.extend(data['ma_slope'].dropna().abs().tolist())
        except:
            pass
    
    if ma_slopes:
        thresholds_to_test = [20, 15, 10, 8, 5, 3]
        print(f"\n|ma_slope| values that would exceed different thresholds:")
        for thresh in thresholds_to_test:
            exceeding = sum(1 for s in ma_slopes if s >= thresh)
            print(f"  >= {thresh:2d}: {exceeding:4d} ({exceeding/len(ma_slopes)*100:5.1f}%)")

if __name__ == "__main__":
    main()