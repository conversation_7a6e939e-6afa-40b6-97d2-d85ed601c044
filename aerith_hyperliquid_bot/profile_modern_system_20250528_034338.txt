=== MODERN SYSTEM PERFORMANCE PROFILE REPORT ===
Generated: 2025-05-28 03:43:38.223571
Date Range: 2025-03-02 to 2025-03-05 (4 days)
System: Continuous GMS + TF-v3 Strategy

         125339349 function calls (119269648 primitive calls) in 635.408 seconds

   Ordered by: cumulative time
   List reduced from 10062 to 50 due to restriction <50>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.000    0.000  633.161  633.161 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/run_backtest.py:179(main)
        1    0.000    0.000  629.456  629.456 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:39(__init__)
        2    0.000    0.000  629.357  314.679 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/detector.py:913(get_regime_detector)
        2    0.015    0.008  629.357  314.678 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py:74(__init__)
        2   10.146    5.073  629.340  314.670 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py:1134(_prime_adaptive_thresholds)
   328923   23.165    0.000  602.941    0.002 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/utils/adaptive_threshold.py:48(update)
   657838    0.461    0.000  372.031    0.001 <__array_function__ internals>:177(percentile)
5935558/667863    4.065    0.000  371.563    0.001 {built-in method numpy.core._multiarray_umath.implement_array_function}
   657838    1.510    0.000  371.031    0.001 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:3920(percentile)
   657838    0.487    0.000  361.471    0.001 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:4465(_quantile_unchecked)
   657838    1.496    0.000  360.985    0.001 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:3692(_ureduce)
   657838    0.724    0.000  359.346    0.001 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:4614(_quantile_ureduce_func)
   657838    5.101    0.000  350.327    0.001 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:4682(_quantile)
   657838  315.324    0.000  315.324    0.000 {method 'partition' of 'numpy.ndarray' objects}
        1    0.000    0.000  314.030  314.030 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/strategies/evaluator.py:760(__init__)
        1    0.000    0.000  314.030  314.030 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/strategies/evaluator.py:802(_initialize_strategies)
        1    0.000    0.000  313.935  313.935 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/strategies/tf_v3.py:70(__init__)
        1    0.000    0.000  313.934  313.934 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_provider.py:26(__init__)
   328923  207.609    0.001  207.609    0.001 {built-in method numpy.fromiter}
   172786    0.441    0.000   11.782    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/frame.py:1366(iterrows)
182376/182279    1.389    0.000   10.586    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/series.py:342(__init__)
  1315700    8.452    0.000    8.452    0.000 {method 'flatten' of 'numpy.ndarray' objects}
   657838    1.571    0.000    7.897    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:4483(_quantile_is_valid)
   657838    3.189    0.000    7.143    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:4647(_get_indexes)
  1975390    2.051    0.000    6.498    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/fromnumeric.py:69(_wrapreduction)
  1315864    0.550    0.000    6.328    0.000 <__array_function__ internals>:177(all)
  1315864    0.631    0.000    4.805    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/fromnumeric.py:2432(all)
  1973514    1.024    0.000    4.576    0.000 <__array_function__ internals>:177(take)
   657846    0.332    0.000    4.412    0.000 <__array_function__ internals>:177(unique)
  2633183    1.312    0.000    3.830    0.000 {method 'any' of 'numpy.generic' objects}
   657872    0.319    0.000    3.811    0.000 <__array_function__ internals>:177(any)
   657838    3.689    0.000    3.778    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py:4559(_lerp)
   657846    0.543    0.000    3.701    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/arraysetops.py:138(unique)
        1    0.000    0.000    3.650    3.650 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:1239(run)
   351318    0.759    0.000    3.309    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/series.py:966(__getitem__)
  3958549    3.285    0.000    3.285    0.000 {method 'reduce' of 'numpy.ufunc' objects}
   347490    0.142    0.000    3.241    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/generic.py:4263(get)
        1    0.000    0.000    3.230    3.230 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:114(_load_and_prepare_data)
  1981021    1.331    0.000    3.060    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/numerictypes.py:356(issubdtype)
   174145    0.706    0.000    2.965    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/construction.py:493(sanitize_array)
   657846    2.222    0.000    2.880    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/arraysetops.py:323(_unique1d)
  1973514    0.732    0.000    2.763    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/fromnumeric.py:93(take)
        1    0.004    0.004    2.685    2.685 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/data/handler.py:140(load_historical_data)
   657872    0.356    0.000    2.675    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/fromnumeric.py:2333(any)
        1    0.025    0.025    2.655    2.655 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/data/handler.py:507(_integrate_microstructure_features)
  2635615    0.525    0.000    2.523    0.000 /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/_methods.py:55(_any)
        4    0.017    0.004    2.350    0.587 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/data/handler.py:371(_load_l2_segment)
  1315680    0.735    0.000    2.301    0.000 {method 'all' of 'numpy.generic' objects}
 1728/117    0.005    0.000    2.291    0.020 <frozen importlib._bootstrap>:1167(_find_and_load)
  1602/12    0.006    0.000    2.290    0.191 <frozen importlib._bootstrap>:1122(_find_and_load_unlocked)




=== DETAILED FUNCTION ANALYSIS ===

1. <method 'partition' of 'numpy.ndarray' objects> (~:0)
   File: ~
   Calls: 657838
   Total Time: 315.324152s
   Cumulative Time: 315.324152s
   Per Call (Total): 0.000479s
   Per Call (Cumulative): 0.000479s

2. <built-in method numpy.fromiter> (~:0)
   File: ~
   Calls: 328923
   Total Time: 207.609000s
   Cumulative Time: 207.609000s
   Per Call (Total): 0.000631s
   Per Call (Cumulative): 0.000631s

3. update (adaptive_threshold.py:48)
   File: /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/utils/adaptive_threshold.py
   Calls: 328923
   Total Time: 23.165099s
   Cumulative Time: 602.941447s
   Per Call (Total): 0.000070s
   Per Call (Cumulative): 0.001833s

4. _prime_adaptive_thresholds (gms_detector.py:1134)
   File: /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py
   Calls: 2
   Total Time: 10.145926s
   Cumulative Time: 629.339769s
   Per Call (Total): 5.072963s
   Per Call (Cumulative): 314.669884s

5. <method 'flatten' of 'numpy.ndarray' objects> (~:0)
   File: ~
   Calls: 1315700
   Total Time: 8.451835s
   Cumulative Time: 8.451835s
   Per Call (Total): 0.000006s
   Per Call (Cumulative): 0.000006s

6. _quantile (function_base.py:4682)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657838
   Total Time: 5.101258s
   Cumulative Time: 350.327349s
   Per Call (Total): 0.000008s
   Per Call (Cumulative): 0.000533s

7. <built-in method numpy.core._multiarray_umath.implement_array_function> (~:0)
   File: ~
   Calls: 5935558
   Total Time: 4.064722s
   Cumulative Time: 371.563237s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000063s

8. _lerp (function_base.py:4559)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657838
   Total Time: 3.688834s
   Cumulative Time: 3.778250s
   Per Call (Total): 0.000006s
   Per Call (Cumulative): 0.000006s

9. <method 'reduce' of 'numpy.ufunc' objects> (~:0)
   File: ~
   Calls: 3958549
   Total Time: 3.284688s
   Cumulative Time: 3.284692s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000001s

10. _get_indexes (function_base.py:4647)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657838
   Total Time: 3.188911s
   Cumulative Time: 7.143276s
   Per Call (Total): 0.000005s
   Per Call (Cumulative): 0.000011s

11. _unique1d (arraysetops.py:323)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/arraysetops.py
   Calls: 657846
   Total Time: 2.222195s
   Cumulative Time: 2.879571s
   Per Call (Total): 0.000003s
   Per Call (Cumulative): 0.000004s

12. _wrapreduction (fromnumeric.py:69)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/fromnumeric.py
   Calls: 1975390
   Total Time: 2.051201s
   Cumulative Time: 6.497502s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000003s

13. _quantile_is_valid (function_base.py:4483)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657838
   Total Time: 1.570792s
   Cumulative Time: 7.897356s
   Per Call (Total): 0.000002s
   Per Call (Cumulative): 0.000012s

14. percentile (function_base.py:3920)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657838
   Total Time: 1.510086s
   Cumulative Time: 371.031190s
   Per Call (Total): 0.000002s
   Per Call (Cumulative): 0.000564s

15. _ureduce (function_base.py:3692)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657838
   Total Time: 1.496400s
   Cumulative Time: 360.984599s
   Per Call (Total): 0.000002s
   Per Call (Cumulative): 0.000549s

16. __init__ (series.py:342)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/series.py
   Calls: 182376
   Total Time: 1.388968s
   Cumulative Time: 10.586300s
   Per Call (Total): 0.000008s
   Per Call (Cumulative): 0.000058s

17. issubdtype (numerictypes.py:356)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/numerictypes.py
   Calls: 1981021
   Total Time: 1.330867s
   Cumulative Time: 3.059919s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000002s

18. <method 'any' of 'numpy.generic' objects> (~:0)
   File: ~
   Calls: 2633183
   Total Time: 1.311511s
   Cumulative Time: 3.829910s
   Per Call (Total): 0.000000s
   Per Call (Cumulative): 0.000001s

19. to_utc_naive (time.py:8)
   File: /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/utils/time.py
   Calls: 602450
   Total Time: 1.271785s
   Cumulative Time: 1.408457s
   Per Call (Total): 0.000002s
   Per Call (Cumulative): 0.000002s

20. issubclass_ (numerictypes.py:282)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/numerictypes.py
   Calls: 3962042
   Total Time: 1.173238s
   Cumulative Time: 1.607695s
   Per Call (Total): 0.000000s
   Per Call (Cumulative): 0.000000s

21. <method 'take' of 'numpy.ndarray' objects> (~:0)
   File: ~
   Calls: 1973673
   Total Time: 1.118129s
   Cumulative Time: 1.118129s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000001s

22. take (<__array_function__ internals>:177)
   File: <__array_function__ internals>
   Calls: 1973514
   Total Time: 1.023850s
   Cumulative Time: 4.576216s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000002s

23. _get_gamma (function_base.py:4537)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657838
   Total Time: 1.007954s
   Cumulative Time: 1.223534s
   Per Call (Total): 0.000002s
   Per Call (Cumulative): 0.000002s

24. <built-in method numpy.asanyarray> (~:0)
   File: ~
   Calls: 7242286
   Total Time: 0.877924s
   Cumulative Time: 0.877959s
   Per Call (Total): 0.000000s
   Per Call (Cumulative): 0.000000s

25. <built-in method builtins.isinstance> (~:0)
   File: ~
   Calls: 8174201
   Total Time: 0.815343s
   Cumulative Time: 1.249935s
   Per Call (Total): 0.000000s
   Per Call (Cumulative): 0.000000s

26. __getitem__ (series.py:966)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pandas/core/series.py
   Calls: 351318
   Total Time: 0.759149s
   Cumulative Time: 3.308973s
   Per Call (Total): 0.000002s
   Per Call (Cumulative): 0.000009s

27. <lambda> (function_base.py:110)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657838
   Total Time: 0.745651s
   Cumulative Time: 0.745651s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000001s

28. <method 'all' of 'numpy.generic' objects> (~:0)
   File: ~
   Calls: 1315680
   Total Time: 0.734705s
   Cumulative Time: 2.301096s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000002s

29. take (fromnumeric.py:93)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/core/fromnumeric.py
   Calls: 1973514
   Total Time: 0.732198s
   Cumulative Time: 2.763313s
   Per Call (Total): 0.000000s
   Per Call (Cumulative): 0.000001s

30. _quantile_ureduce_func (function_base.py:4614)
   File: /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/lib/function_base.py
   Calls: 657838
   Total Time: 0.724460s
   Cumulative Time: 359.345725s
   Per Call (Total): 0.000001s
   Per Call (Cumulative): 0.000546s
