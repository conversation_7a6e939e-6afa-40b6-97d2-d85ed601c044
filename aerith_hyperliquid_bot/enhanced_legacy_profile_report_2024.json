{"analysis_metadata": {"timestamp": "2025-05-29T19:44:08.542144", "analysis_period": "2024 Full Year", "configuration": "legacy_profile.yaml", "system_type": "Legacy System (Granular Microstructure + TF-v2)"}, "performance_metrics": {"total_runtime_seconds": 5.135361909866333, "total_runtime_minutes": 0.08558936516443888, "peak_memory_mb": 0, "estimated_days_processed": "~0.5 days (estimated)", "avg_time_per_day_seconds": 0.014069484684565295}, "bottleneck_analysis": {"data_loading": [{"function": "_find_and_load", "file": "<frozen importlib._bootstrap>", "cumulative_time": 4.961889291, "total_time": 0.020559766, "calls": 1688}, {"function": "_find_and_load_unlocked", "file": "<frozen importlib._bootstrap>", "cumulative_time": 4.961706792, "total_time": 0.011350685000000001, "calls": 1652}, {"function": "_load_unlocked", "file": "<frozen importlib._bootstrap>", "cumulative_time": 4.957977292000001, "total_time": 0.008245716, "calls": 1599}, {"function": "<built-in method marshal.loads>", "file": "~", "cumulative_time": 0.389886864, "total_time": 0.389886864, "calls": 1386}, {"function": "<method 'read' of '_io.BufferedReader' objects>", "file": "~", "cumulative_time": 0.281715623, "total_time": 0.281715623, "calls": 1394}, {"function": "load_module", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pkg_resources/extern/__init__.py", "cumulative_time": 0.06366020800000001, "total_time": 3.0666e-05, "calls": 5}, {"function": "_read_sections", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/_lib/_docscrape.py", "cumulative_time": 0.048273825000000006, "total_time": 0.0014080350000000002, "calls": 428}, {"function": "_read_to_next_section", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/_lib/_docscrape.py", "cumulative_time": 0.048238889, "total_time": 0.003886753, "calls": 423}, {"function": "load_config", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/config/settings.py", "cumulative_time": 0.045237584000000004, "total_time": 4.3043e-05, "calls": 1}, {"function": "safe_load", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/yaml/__init__.py", "cumulative_time": 0.040431792, "total_time": 6.459e-06, "calls": 1}], "microstructure_processing": [{"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py", "cumulative_time": 0.062932166, "total_time": 6.2915e-05, "calls": 1}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/features/microstructure.py", "cumulative_time": 0.00074025, "total_time": 5.5959000000000004e-05, "calls": 1}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_provider.py", "cumulative_time": 0.000309625, "total_time": 2.1083e-05, "calls": 1}, {"function": "GMSProvider", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_provider.py", "cumulative_time": 0.000251625, "total_time": 1.1543000000000001e-05, "calls": 1}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/strategies/obi_scalper_strategy.py", "cumulative_time": 0.00023925000000000001, "total_time": 2.6416000000000003e-05, "calls": 1}, {"function": "OBIScalperStrategy", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/strategies/obi_scalper_strategy.py", "cumulative_time": 0.000156167, "total_time": 2.1459000000000002e-05, "calls": 1}, {"function": "RegimeDetectorInterface", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py", "cumulative_time": 0.000152625, "total_time": 1.7374e-05, "calls": 1}, {"function": "ContinuousGMSDetector", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_detector.py", "cumulative_time": 0.00010725, "total_time": 2.4083000000000003e-05, "calls": 1}, {"function": "GMSValidator", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/core/gms_provider.py", "cumulative_time": 5.833000000000001e-06, "total_time": 5.541e-06, "calls": 1}], "time_conversion": [{"function": "_validate_timestamp_pyc", "file": "<frozen importlib._bootstrap_external>", "cumulative_time": 0.009270921000000001, "total_time": 0.0056443100000000005, "calls": 1386}, {"function": "reset_tzpath", "file": "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/zoneinfo/_tzpath.py", "cumulative_time": 0.004494292, "total_time": 6.125000000000001e-06, "calls": 1}, {"function": "DatetimeIndex", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/core/indexes/datetimes.py", "cumulative_time": 0.001226417, "total_time": 6.770700000000001e-05, "calls": 1}, {"function": "DatetimeTZDtype", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/core/dtypes/dtypes.py", "cumulative_time": 0.0006583330000000001, "total_time": 3.7875e-05, "calls": 1}, {"function": "<method 'strftime' of 'datetime.date' objects>", "file": "~", "cumulative_time": 0.0006404570000000001, "total_time": 0.0006404570000000001, "calls": 76}, {"function": "DatetimeIndexOpsMixin", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/core/indexes/datetimelike.py", "cumulative_time": 0.000517125, "total_time": 7.2624e-05, "calls": 1}, {"function": "DatetimeTimedeltaMixin", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/core/indexes/datetimelike.py", "cumulative_time": 0.0004907500000000001, "total_time": 5.6169000000000004e-05, "calls": 1}, {"function": "DatetimeLikeArrayMixin", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/core/arrays/datetimelike.py", "cumulative_time": 0.000439666, "total_time": 9.799900000000001e-05, "calls": 1}, {"function": "DatetimeArray", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/core/arrays/datetimes.py", "cumulative_time": 0.00013179200000000001, "total_time": 8.8458e-05, "calls": 1}, {"function": "_read_tzfile", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/dateutil/tz/tz.py", "cumulative_time": 8.820900000000001e-05, "total_time": 5.2919000000000006e-05, "calls": 1}], "parquet_operations": [{"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/__init__.py", "cumulative_time": 1.296923333, "total_time": 0.00011554100000000001, "calls": 1}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/core/api.py", "cumulative_time": 0.883252125, "total_time": 0.000119791, "calls": 1}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/_libs/__init__.py", "cumulative_time": 0.33285016700000003, "total_time": 6.0042000000000006e-05, "calls": 1}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas_ta/__init__.py", "cumulative_time": 0.320597209, "total_time": 8.7335e-05, "calls": 1}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/core/arrays/__init__.py", "cumulative_time": 0.230371542, "total_time": 7.1086e-05, "calls": 1}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/core/groupby/__init__.py", "cumulative_time": 0.22850662500000002, "total_time": 1.6291e-05, "calls": 1}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/core/groupby/generic.py", "cumulative_time": 0.22692525000000002, "total_time": 0.00010804500000000001, "calls": 1}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/core/arrays/arrow/__init__.py", "cumulative_time": 0.199629792, "total_time": 1.9125e-05, "calls": 1}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/core/frame.py", "cumulative_time": 0.19496554200000002, "total_time": 0.000186085, "calls": 1}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/pandas/core/generic.py", "cumulative_time": 0.15336212500000002, "total_time": 0.00023804500000000002, "calls": 1}], "modern_system_access": [{"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/_continuous_distns.py", "cumulative_time": 0.329025917, "total_time": 0.0009884610000000002, "calls": 1, "warning": "Unexpected modern system component access detected"}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/strategies/tf_v3.py", "cumulative_time": 0.258853917, "total_time": 4.596e-05, "calls": 1, "warning": "Unexpected modern system component access detected"}, {"function": "<module>", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/config/scheduler_settings.py", "cumulative_time": 0.079483458, "total_time": 6.042000000000001e-06, "calls": 1, "warning": "Unexpected modern system component access detected"}, {"function": "SchedulerSettings", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/config/scheduler_settings.py", "cumulative_time": 0.00023479200000000002, "total_time": 2.0166e-05, "calls": 1, "warning": "Unexpected modern system component access detected"}, {"function": "_call_super_mom", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/_continuous_distns.py", "cumulative_time": 0.00019974900000000001, "total_time": 7.2328e-05, "calls": 19, "warning": "Unexpected modern system component access detected"}, {"function": "TFV3Strategy", "file": "/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/strategies/tf_v3.py", "cumulative_time": 0.000141208, "total_time": 3.5915000000000004e-05, "calls": 1, "warning": "Unexpected modern system component access detected"}, {"function": "norm_gen", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/_continuous_distns.py", "cumulative_time": 7.1792e-05, "total_time": 3.0083000000000003e-05, "calls": 1, "warning": "Unexpected modern system component access detected"}, {"function": "vonmises_gen", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/_continuous_distns.py", "cumulative_time": 5.9625000000000006e-05, "total_time": 2.6334000000000002e-05, "calls": 1, "warning": "Unexpected modern system component access detected"}, {"function": "beta_gen", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/_continuous_distns.py", "cumulative_time": 5.0916000000000005e-05, "total_time": 2.2792e-05, "calls": 1, "warning": "Unexpected modern system component access detected"}, {"function": "expon_gen", "file": "/Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/_continuous_distns.py", "cumulative_time": 4.575e-05, "total_time": 1.8125e-05, "calls": 1, "warning": "Unexpected modern system component access detected"}], "memory_intensive": []}, "performance_breakdown": {"data_loading": {"total_time_seconds": 15.799018160000001, "percentage_of_total": 307.6515041646058, "top_function": "_find_and_load"}, "microstructure_processing": {"total_time_seconds": 0.064894791, "percentage_of_total": 1.2636848607557851, "top_function": "<module>"}, "time_conversion": {"total_time_seconds": 0.017957962, "percentage_of_total": 0.34969223815556605, "top_function": "_validate_timestamp_pyc"}, "parquet_operations": {"total_time_seconds": 4.06738371, "percentage_of_total": 79.20344819681596, "top_function": "<module>"}, "modern_system_access": {"total_time_seconds": 0.6681671239999998, "percentage_of_total": 13.01110098426133, "top_function": "<module>"}, "memory_intensive": {"total_time_seconds": 0, "percentage_of_total": 0.0, "top_function": null}}, "optimization_recommendations": [{"category": "Data Loading", "priority": "High", "issue": "Data loading consumes 307.7% of total runtime", "recommendations": ["Consider implementing data caching mechanisms", "Optimize parquet file reading with column selection", "Implement parallel data loading for multiple days", "Pre-process and cache frequently accessed data"]}, {"category": "Configuration Issue", "priority": "Critical", "issue": "Unexpected modern system component access detected", "recommendations": ["Review configuration to ensure complete Legacy System isolation", "Check for TF-v3 or continuous GMS detector access", "Verify ETL scheduler is properly disabled", "Audit code paths for modern system dependencies"]}, {"category": "Memory Optimization", "priority": "Low", "issue": "General memory usage optimization", "recommendations": ["Monitor memory usage patterns during long backtests", "Consider implementing data chunking for very large datasets", "Review pandas DataFrame memory usage", "Implement garbage collection at strategic points"]}], "configuration_integrity": {"status": "FAIL", "issues": ["Modern system components accessed during Legacy System run", "Unexpected access: <module> in /Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/_continuous_distns.py", "Unexpected access: <module> in /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/strategies/tf_v3.py", "Unexpected access: <module> in /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/config/scheduler_settings.py", "Unexpected access: SchedulerSettings in /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/config/scheduler_settings.py", "Unexpected access: _call_super_mom in /Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/_continuous_distns.py", "Unexpected access: TFV3Strategy in /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/strategies/tf_v3.py", "Unexpected access: norm_gen in /Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/_continuous_distns.py", "Unexpected access: vonmises_gen in /Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/_continuous_distns.py", "Unexpected access: beta_gen in /Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/_continuous_distns.py", "Unexpected access: expon_gen in /Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/_continuous_distns.py"], "warnings": []}, "backtest_results_summary": null, "raw_profile_summary": "         2687836 function calls (2630595 primitive calls) in 5.006 seconds\n\n   Ordered by: cumulative time\n   List reduced from 7960 to 50 due to restriction <50>\n\n   ncalls  tottime  percall  cumtime  percall filename:lineno(function)\n   1688/4    0.021    0.000    4.962    1.240 <frozen importlib._bootstrap>:1167(_find_and_load)\n   1652/4    0.011    0.000    4.962    1.240 <frozen importlib._bootstrap>:1122(_find_and_load_unlocked)\n   3834/7    0.002    0.000    4.960    0.709 <frozen importlib._bootstrap>:233(_call_with_frames_removed)\n   1599/5    0.008    0.000    4.958    0.992 <frozen importlib._bootstrap>:666(_load_unlocked)\n   1386/5    0.006    0.000    4.958    0.992 <frozen importlib._bootstrap_external>:934(exec_module)\n   1939/5    0.112    0.000    4.955    0.991 {built-in method builtins.exec}\n   735/15    0.002    0.000    4.625    0.308 {built-in method builtins.__import__}\n        1    0.000    0.000    4.490    4.490 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/__init__.py:1(<module>)\n        1    0.000    0.000    4.489    4.489 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/backtester/backtester.py:1(<module>)\n2042/1143    0.004    0.000    2.625    0.002 <frozen importlib._bootstrap>:1209(_handle_fromlist)\n        1    0.000    0.000    1.880    1.880 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/signals/__init__.py:1(<module>)\n        1    0.000    0.000    1.879    1.879 /Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot/hyperliquid_bot/signals/calculator.py:1(<module>)\n        1    0.000    0.000    1.546    1.546 /Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/__init__.py:1(<module>)\n        1    0.001    0.001    1.379    1.379 /Users/<USER>/Desktop/trading_bot_/.venv/lib/python3.11/site-packages/scipy/stats/_stats_py.py:1(<module>)\n        1    0.000    0.000   ..."}