#!/usr/bin/env python3
"""
Test state mapping for the modern system.
"""

import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Import after path is set
from hyperliquid_bot.utils.state_mapping import get_state_map, map_gms_state


def test_modern_state_mapping():
    """Test that the modern state mapping works correctly."""
    print("=== Testing Modern State Mapping ===\n")
    
    # Load the default state map (should load from gms_state_mapping.yaml)
    default_map = get_state_map()
    print("Default state map loaded:")
    for state, mapped in default_map.items():
        print(f"  {state} -> {mapped}")
    
    print("\n--- Testing Individual State Mappings ---")
    
    # Test critical states
    test_states = [
        'High_Vol_Range',
        'Low_Vol_Range', 
        'Weak_Bear_Trend',
        'Strong_Bull_Trend',
        'Strong_Bear_Trend',
        'Uncertain',
        'Unknown',
        'TIGHT_SPREAD'
    ]
    
    for state in test_states:
        mapped = map_gms_state(state)
        print(f"{state} -> {mapped}")
    
    print("\n--- Expected vs Actual for Modern System ---")
    print("Expected mappings for modern system:")
    print("  High_Vol_Range -> BULL (for volatility breakouts)")
    print("  Low_Vol_Range -> BEAR (for mean reversion)")
    print("  Weak_Bear_Trend -> BEAR (for trend following)")
    
    print("\nActual mappings from default loader:")
    print(f"  High_Vol_Range -> {default_map.get('High_Vol_Range', 'NOT FOUND')}")
    print(f"  Low_Vol_Range -> {default_map.get('Low_Vol_Range', 'NOT FOUND')}")
    print(f"  Weak_Bear_Trend -> {default_map.get('Weak_Bear_Trend', 'NOT FOUND')}")
    
    print("\n⚠️  Note: The state_mapping.py utility always loads from 'gms_state_mapping.yaml'")
    print("It does NOT respect the config file setting for the modern mapping file.")
    print("This is why the modern system isn't getting the correct mappings!")


if __name__ == "__main__":
    test_modern_state_mapping()