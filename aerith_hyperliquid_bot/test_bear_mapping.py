#!/usr/bin/env python3
"""
Test the impact of map_weak_bear_to_bear on trade direction distribution
"""

import sys
sys.path.append('/Users/<USER>/Desktop/trading_bot_/aerith_hyperliquid_bot')

from hyperliquid_bot.utils.state_mapping import map_gms_state, get_bear_states, get_bull_states

def test_bear_mapping():
    """Test how map_weak_bear_to_bear affects trade opportunities"""
    
    print("🔍 BEAR MAPPING ANALYSIS")
    print("=" * 50)
    
    # Test states that might occur in 2024
    test_states = [
        'Strong_Bull_Trend',
        'Weak_Bull_Trend', 
        'Strong_Bear_Trend',
        'Weak_Bear_Trend',  # KEY STATE
        'High_Vol_Range',
        'Low_Vol_Range',
        'Uncertain'
    ]
    
    print("\nState Mapping Comparison:")
    print(f"{'State':<18} {'Legacy (False)':<15} {'Modern (True)':<15} {'Impact'}")
    print("-" * 65)
    
    bear_trades_legacy = 0
    bear_trades_modern = 0
    total_states = len(test_states)
    
    for state in test_states:
        legacy_mapping = map_gms_state(state, map_weak_bear_to_bear=False)
        modern_mapping = map_gms_state(state, map_weak_bear_to_bear=True)
        
        # Count potential bear trades
        if legacy_mapping == 'BEAR':
            bear_trades_legacy += 1
        if modern_mapping == 'BEAR':
            bear_trades_modern += 1
            
        # Show impact
        if legacy_mapping != modern_mapping:
            impact = f"🔥 {legacy_mapping}→{modern_mapping}"
        else:
            impact = "No change"
            
        print(f"{state:<18} {legacy_mapping:<15} {modern_mapping:<15} {impact}")
    
    print(f"\n📊 BEAR TRADING OPPORTUNITIES:")
    print(f"Legacy (map_weak_bear_to_bear=False): {bear_trades_legacy}/{total_states} states = {bear_trades_legacy/total_states*100:.1f}%")
    print(f"Modern (map_weak_bear_to_bear=True):  {bear_trades_modern}/{total_states} states = {bear_trades_modern/total_states*100:.1f}%")
    
    improvement = ((bear_trades_modern - bear_trades_legacy) / total_states) * 100
    print(f"Improvement: +{improvement:.1f}% more bear trading opportunities")
    
    print(f"\n🎯 KEY INSIGHT:")
    if improvement > 0:
        print(f"   Setting map_weak_bear_to_bear=True increases bear trades by {improvement:.1f}%")
        print(f"   This could explain why modern system was long-only!")
    else:
        print("   No improvement from map_weak_bear_to_bear setting")
    
    # Show the actual bear states
    print(f"\n📚 BEAR STATES BY CONFIGURATION:")
    legacy_bear_states = get_bear_states(map_weak_bear_to_bear=False)
    modern_bear_states = get_bear_states(map_weak_bear_to_bear=True)
    
    print(f"Legacy bear states: {legacy_bear_states}")
    print(f"Modern bear states: {modern_bear_states}")
    
    return {
        'legacy_bear_pct': bear_trades_legacy/total_states*100,
        'modern_bear_pct': bear_trades_modern/total_states*100,
        'improvement': improvement
    }

if __name__ == "__main__":
    results = test_bear_mapping()
    
    print(f"\n🔧 RECOMMENDED ACTION:")
    if results['improvement'] > 0:
        print("1. Ensure override config is loading properly (fix path issue)")
        print("2. Verify map_weak_bear_to_bear=True is being applied")
        print("3. Re-run backtest to confirm more balanced long/short trades")
    else:
        print("Issue may be elsewhere - check regime detector threshold calibration")