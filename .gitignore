# 1. Ignore everything in this directory by default
*

# 2. BUT, DO NOT ignore the main application code folder AND ITS CONTENTS
!aerith_hyperliquid_bot/
!aerith_hyperliquid_bot/**
!aerith_hyperliquid_bot/configs/
!aerith_hyperliquid_bot/configs/**
!aerith_hyperliquid_bot/hyperliquid_bot/
!aerith_hyperliquid_bot/hyperliquid_bot/**
!aerith_hyperliquid_bot/scripts/
!aerith_hyperliquid_bot/scripts/**

# 3. DO NOT ignore this .gitignore file itself
!.gitignore

# Optional: If you have a README.md file in the root (trading_bot_)
# that you also want to commit, un-ignore it too:
# !README.md

# --- Standard ignores below will apply ANYWHERE not ignored above ---

# Python cache/compiled files
__pycache__/
*.pyc
*.pyo

# Python Virtual Environments
venv/
.venv/
env/
bot_env/ # Added this back from your previous example just in case

# IDE / Editor / Workspace files
.vscode/
.cursor/
.idea/
.project/
.settings/
*.sublime-project
*.sublime-workspace
# trading_bot_.code-workspace # Ignored by '*' unless un-ignored

# Log files (will be ignored even inside aerith_hyperliquid_bot)
*.log
logs/

# Add OS specific files if needed e.g. .DS_Store for macOS
.DS_Store

# Added by Claude Task Master
# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 